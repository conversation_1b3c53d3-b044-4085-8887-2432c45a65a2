const path = require('path');
const fs = require('fs');
const merge = require('webpack-merge');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const UglifyJsPlugin = require('uglifyjs-webpack-plugin');
const ManifestPlugin = require('webpack-manifest-plugin');
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const OptimizeCssAssetsPlugin = require("optimize-css-assets-webpack-plugin");
const webpackBaseConfig = require('./webpack.base.config.js');
const TerserPlugin = require('terser-webpack-plugin');
const utils = require('./util.js');
const {
    webpackConfig, prodDirectory = 'build',
    projectGroup, projectName, moduleSeparator
} = require('../project.config.js');
const TplConfigPlugin = require('webpack-tplconfig-plugin');
const {ConcatSource} = require("webpack-sources")

function recursiveIssuer(m, entry_name) {
    if (m.issuer) {
        return recursiveIssuer(m.issuer);
    } else {
        for (var chunk of m._chunks) {
            if (entry_name == chunk.name)
                return chunk.name;
        }
        return false;
    }
}

function getCacheGroups(entries) {
    let groups = {};
    for (let key in entries) {
        groups[`${key}Style`] = {
            name: key,
            test: (m, c, entry = key) => m.constructor.name === 'CssModule' && recursiveIssuer(m, entry) === entry,
            enforce: true,
            reuseExistingChunk: true
        };
    }
    // todo: 插件里有引用 detail/base 先不抽
    groups['detail-v3-base'] = {
        test: /v3\/base/,
        chunks: 'all',
        minChunks: 2,
        name: 'detail-v3-base',
        enforce: true,
        reuseExistingChunk: true,
        priority: 3,
    };
    // const BasePlugin  = ['plugin_public_methods', 'plugin_base'];
    // BasePlugin.forEach(n => {
    //     groups[n] = {
    //         test: (m, c, key=n) => {
    //             return m.request && m.request.indexOf(key) !== -1;
    //         },
    //         name: n,
    //         chunks: 'all',
    //         minChunks: 2,
    //         enforce: true,
    //         reuseExistingChunk: true,
    //         priority: 3,
    //     }
    // });
    return groups;
}

class EditSdk {
    apply(compiler) {
        compiler.hooks.thisCompilation.tap('editHash', (compilation, callback) => {
            compilation.hooks.chunkHash.tap('updateHash', (chunk, chunkHash) => {
                if (chunk.name === 'sdk') {
                    // sdk每次都会在构建后写入seajs map，必须更新hash，去掉文件缓存
                    chunkHash.update(new Date().getTime().toString());
                }
            });
        });

        compiler.hooks.afterEmit.tapAsync('done', (compilation, callback) => {
            try {
                let jsonConfig = require(path.resolve(__dirname, `../dist/manifest.json`));
                let dir = path.resolve(__dirname, `../dist/${jsonConfig.sdk}`);

                function getSeajsMap(json) {
                    var map = [];
                    var names = Object.keys(utils.getPluginEntry());

                    Object.keys(json).forEach(key => {
                        map.push([names.includes(key) ? `vcrm-dist/plugin/${key}.js` : `vcrm-dist/${key}.js`, `vcrm-dist/${json[key]}`])
                    })
                    return JSON.stringify(map);
                }

                function getSeajsConfig(json) {
                    return (`
              (function () {
                seajs.config({
                  map: ${getSeajsMap(json)}
                })
              })();
            `).replace(/\s*|\n/g, '');
                }

                fs.readFile(dir, 'utf8', (err, data) => {
                    fs.writeFile(dir, getSeajsConfig(jsonConfig) + data, (err) => {
                        err && console.log(err);
                        callback();
                    })
                })
            } catch (error) {
                console.log(error)
            }

        });
    }
}

module.exports = (env, argv) => {
    argv.mode = "production";
    let webpack_config = utils.result({webpackConfig}, 'webpackConfig', [env, argv]);
    let result = merge(webpackBaseConfig, {
        stats: 'errors-only',
        mode: 'production',
        devtool: false,
        output: {
            filename: (pathData) => {
                let names = Object.keys(utils.getPluginEntry());
                return names.includes(pathData.chunk.name) ? 'plugin/[name].[contenthash].js' : '[name].[contenthash].js';
            },
            chunkFilename: '[name].[contenthash].js',
            path: path.resolve(__dirname, `../${prodDirectory}`),
            crossOriginLoading: 'anonymous'
        },
        module: {
            rules: [
                {
                    test: /\.js$/,
                    use: [
                        {
                            loader: 'thread-loader',
                            options:{
                                workers: 2,
                                // 一个 worker 进程中并行执行工作的数量 默认为 20
                                workerParallelJobs: 50,
                                // 池分配给 worker 的工作数量
                                // 默认为 200
                                // 降低这个数值会降低总体的效率，但是会提升工作分布更均一
                                poolParallelJobs: 50,
                            }
                        },
                        {
                            loader: 'babel-loader',
                            options: {
                                presets: ["@babel/preset-env"],
                                plugins: ["transform-vue-jsx"],
                                comments: true
                            }
                        }
                    ],
                    exclude: /node_modules/
                },
                {
                    test: /plugin\/.*\.less$/,
                    use: [
                        'style-loader',
                        'css-loader',
                        'postcss-loader',
                        'less-loader'
                    ],
                    exclude: /node_modules/
                },
                {
                    test: /\.less$/,
                    use: [
                        MiniCssExtractPlugin.loader,
                        'css-loader',
                        'postcss-loader',
                        'less-loader'
                    ],
                    exclude: /node_modules|plugin/
                },
                {
                    test: /\.css$/,
                    use: [
                        MiniCssExtractPlugin.loader,
                        'css-loader',
                        'postcss-loader'
                    ],
                    // exclude: /node_modules/
                },
                {
                    test: /\.(gif|jpg|png|svg)\??.*$/,
                    use: [
                        {
                            loader: 'url-loader',
                            options: {
                                limit: 8192,
                                name: 'images/[name].[ext]'
                            }
                        },
                        // {
                        //   loader: 'image-webpack-loader',
                        //   options: {
                        //     mozjpeg: {
                        //       progressive: true,
                        //       quality: 65
                        //     },
                        //     optipng: {
                        //       enabled: false,
                        //     },
                        //     pngquant: {
                        //       quality: '65-90',
                        //       speed: 4
                        //     },
                        //     gifsicle: {
                        //       interlaced: false,
                        //     },
                        //     webp: {
                        //       quality: 75
                        //     }
                        //   }
                        // }
                    ]
                },
            ]
        },
        optimization: {
            minimizer: [
                new OptimizeCssAssetsPlugin(),
                new TerserPlugin({
                    parallel: true, // 启用并行压缩
                    terserOptions: {
                        compress: {
                            warnings: false, // 是否显示警告信息
                            drop_console: false, // 默认false，设置为true, 则会删除所有console.* 相关的代码。
                            pure_funcs: ["console.log"], // 单纯禁用console.log
                        },
                        output: {
                            comments: false // 删除所有的注释
                        }
                    },
                    extractComments: false // 不提取注释到单独文件
                }),
            ],
            splitChunks: {
                // chunks: 'all',
                // minChunks: 2,
                cacheGroups: getCacheGroups(webpack_config.entry)
            }
            // splitChunks: {
            //   cacheGroups: {
            //     vendor: {
            //       chunks: "all",
            //       minChunks: 2,
            //       name: 'vendor',
            //       enforce: true,
            //       reuseExistingChunk: true
            //     },
            //   },
            // }
            // runtimeChunk: 'single'
        },
        plugins: [
            new CleanWebpackPlugin(['dist'], {
                root: path.resolve(__dirname, '../')
            }),
            new MiniCssExtractPlugin({
                filename: "[name].[contenthash:8].css"
            }),
            new ManifestPlugin({
                generate(seed, files) {
                    return files.reduce(function (manifest, {name, path, isInitial}) {
                        if (isInitial) {
                            let key = name.replace(/\.[A-Za-z]+$/, '');
                            if (/\.css$/.test(name))     //处理css
                                key = `css-${key}`;
                            manifest[key] = path;
                        }
                        return manifest;
                    }, seed);
                }
            }),
            new TplConfigPlugin({
                prefix: (projectGroup ? projectGroup + moduleSeparator : '') + projectName + moduleSeparator
            }),
            new EditSdk()
        ],
    }, webpack_config);
    return result;
}
