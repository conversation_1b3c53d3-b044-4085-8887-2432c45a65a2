<div class="import-category-tree" v-loading="isLoading">
  <template v-if="!isLoading">
    <fx-tree
      ref="fxTree"
      :data="treeData"
      show-checkbox
      node-key="_id"
      :default-checked-keys="value"
      :check-strictly="true"
      :props="defaultProps">
    </fx-tree>
    <fx-checkbox 
      v-show="isShowBindProductCheckbox && treeData.length"
      :title="$t('dht.category.automatically_associated')"
      class="bind-product-checkbox"
      v-model="bindProduct" 
      size="micro">
      {{$t('dht.category.automatically_associated')}}
    </fx-checkbox>
  </template>
</div>