<script lang="ts">
import 'reflect-metadata';
import { Component, Prop, Vue, Ref } from 'vue-property-decorator';

@Component({
  name: 'ImportCategoryTree'
})
export default class ImportCategoryTree extends Vue {
  @Ref('fxTree') fxTree: any;

  @Prop({default: '0'}) readonly categoryType!: '0' | '1'; // 0: 产品分类，1: 商城分类 

  treeData: any[] = [];
  bindProduct = false;
  isLoading: boolean = false;
  isShowBindProductCheckbox: boolean = false;

  defaultProps = { // 分类树属性设置
    children: 'children',
    label: 'name'
  };

  created() {
    this.getTreeList().then(() => {
      const timer = setTimeout(() => {
        clearTimeout(timer);
        this.isShowBindProductCheckbox = true;
      }, 350);
    });
  }

  getTreeList() {
    return new Promise((resolve,reject) => {
      this.isLoading = true;
      CRM.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/product_category/service/list',
        data: this._getParams(),
        success: (res: any) => {
          this.isLoading = false;
          if (res.Result.StatusCode === 0) {
           this.treeData = this._buildTree(res.Value.result);
           resolve(this.treeData);
          } else {
            reject();
          }
        },
        error: (err: any) => {
          this.isLoading = false;
          console.error(err);
          reject(err);
        }
      }, {
        errorAlertModel: 1
      });
    });
  }

  _getParams() {
    let data: any;
    if (this.categoryType === '1') {
      data = {
        filterByShopCategory: true,
        filterCategory: 'Shop',
      };
    } else {
      data = {
        filterCategory: 'Product'
      };
    }
    return data;
  }

  /**
   * 构建分类树的方法
   * @param nodeList
   * @returns {*[]}
   * @private
   */
  _buildTree(nodeList: any[]) {
    // 创建 ID 到节点的映射表
    const nodeMap: Record<string, any> = {};
    nodeList.forEach(node => {
      // 复制节点数据，避免修改原始数据
      nodeMap[node._id] = { ...node, children: [] };
    });

    // 存储根节点
    const rootNodes: any[] = [];

    // 构建树结构
    nodeList.forEach(node => {
      const mappedNode = nodeMap[node._id];
      const parentId = node.pid;

      // 如果父节点不存在或 pid 为空，则视为根节点
      if (!parentId || !nodeMap[parentId]) {
        rootNodes.push(mappedNode);
      } else {
        // 将当前节点添加到父节点的 children 中
        nodeMap[parentId].children.push(mappedNode);
      }
    });

    return rootNodes;
  }

  save() {
    const nodes = (this.$refs.fxTree as any).getCheckedNodes();
    if (nodes.length) {
      return {
        categoryType: this.categoryType,
        bindProduct: this.bindProduct,
        nodes,
      };
    } else {
      (this as any).$message({
        duration: 3000,
        message: $t('请选择分类')
      });
      return null;
    }
  }

}
</script>

<template src="./import-category-tree.html"></template>
<style src="./import-category-tree.less" lang="less" scoped></style>