<script lang="ts">
import 'reflect-metadata';
import {Component, Prop, Ref, Vue} from 'vue-property-decorator';

@Component({
  name: 'ConnectProducts'
})
export default class ConnectProducts extends Vue {
  @Ref() readonly connectProductsContext!: any;

  @Prop({default: () => ({})}) categoryData!: any;

  // 1：手动添加，2：按添加添加
  connectType: '1' | '2' = '1';

  table: any;

  get objectApiName() {
    return CRM._cache.productOpenSpu ? 'SPUObj' : 'ProductObj';
  }

  mounted() {
    this.renderProductTable();
  }

  renderProductTable() {
    seajs.use('crm-modules/components/shopcategoryobj/connect_products_list', (Table: any) => {
      this.table = new Table({
        el: $(this.$refs.connectProductsContext),
        categoryData: this.categoryData,
      });
      this.table.render();
    });
  }

  showProductModal() {
    const filters: any[] = [{
      field_name: 'product_status',
      field_values: ['1'],
      operator: 'EQ',
    },{
      field_name: 'mall_category_id',
      field_values: [this.categoryData._id],
      operator: 'NHASANYOF',
    }];

    const pickerParams = {
      isMultiple: true,
      apiname: this.objectApiName,
      filters: filters,
      object_data: {},
    };

    this._renderObjectPicker2(pickerParams, (list: any[]) => {
      if (list.length) {
        const productIds: string[] = list.map(item => item._id);
        this.bindProducts(productIds);
      }
    });
  }

  _renderObjectPicker2(params: any, cb: Function) {
    seajs.use('crm-modules/components/pickselfobject/pickselfobject', (PickSelf: any) => {
      const pickObject = new PickSelf();

      pickObject.on('select', (obj: any) => {
        cb && cb(obj);
        pickObject.destroy();
      });

      pickObject.render(Object.assign({
        zIndex: 3000,
        isMultiple: false,
      }, params));
    });
  }

  bindProducts(productIds: string[]) {
    CRM.util.showLoading_tip($t('保存中...'));
    CRM.util.FHHApi({
      url: '/EM1HNCRM/API/v1/object/shop_category/service/bind_products',
      data: {
        storeId: this.categoryData.store_id,
        shopCategoryId: this.categoryData._id,
        productIds: productIds
      },
      success: (res: any) => {
        CRM.util.hideLoading_tip();
        if (res.Result.StatusCode === 0) {
          CRM.util.remindSuccess();
          const Value = res.Value || {};
          // 判断是否是异步绑定，当超过一定数量的绑定商品时，会异步绑定
          if (Value.async) {
            // 当前一次性绑定商品过多，需要异步绑定，请稍后手动刷新表格查看绑定结果！
            CRM.util.alert($t('dht.bindproduct.async.tip'));
          } else {
            this.table.refresh();
          }
          return;
        }
        CRM.util.alert(res.Result.FailureMessage || $t('保存失败，请重试！'));
      },
      error: (err: any) => {
        console.error(err);
        CRM.util.hideLoading_tip();
      }
    }, {
      errorAlertModel: 1
    });
  }
}
</script>

<template src="./connect-products.html"></template>
<style src="./connect-products.less" lang="less"></style>
