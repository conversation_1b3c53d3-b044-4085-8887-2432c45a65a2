<div :class="'category-level-' + level">
  <div :class="'category-row category-row-' + level">
    <div class="level-label">{{ getLevelLabel }}</div>
    <div class="category-content">
      <div class="items-container" :class="{ expanded: isExpanded }">
      <span
        v-for="(item, index) in items"
        :key="index"
        :class="['level-item', { active: item.active }]"
        @click="handleClick(item)">
          {{ item.name }}
      </span>
      </div>
      <div v-if="needExpand" class="expand-control" @click="toggleExpand">
        {{ isExpanded ? $t('收起') : $t('展开') }}
        <i :class="['el-icon-arrow-right', { 'is-expanded': isExpanded }]"></i>
      </div>
    </div>
  </div>
  <category-tree-item
    v-for="item in items"
    v-if="item.active && hasChildren(item)"
    :key="item.name"
    :items="item.children"
    :level="level + 1"
    :parent-node="item"
    @select="handleChildSelect"
  ></category-tree-item>
</div>

