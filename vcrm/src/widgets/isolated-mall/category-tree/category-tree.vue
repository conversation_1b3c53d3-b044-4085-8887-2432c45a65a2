<script lang="ts">
import 'reflect-metadata';
import {Vue, Prop, Component} from 'vue-property-decorator';
import CategoryTreeItem from './category-tree-item/category-tree-item.vue';

@Component({
  name: 'CategoryTree',
  components: {
    CategoryTreeItem
  }
})
export default class CategoryTree extends Vue {
  nodes: any[] = [];

  created() {
    this.getTreeList();
  }

  handleChildSelect(node: any, level: number) {
    this.$emit('select', node, level);
  }

  async getTreeList() {
    const storeId = await this.getStoreIdByApp();

    CRM.util.FHHApi({
      url: '/EM1HNCRM/API/v1/object/shop_category/service/query_sub_shop_category_list',
      data: {
        pid: '',
        storeId,
      },
      success: (res: any) => {
        if (res.Result.StatusCode === 0) {
          this.nodes = this._buildTree(res.Value.shopCategoryList);
        } else {
          CRM.util.alert(res.Result.FailureMessage || $t("暂时无法获取数据") + '!');
        }
      },
      error: (err: any) => {
        console.error(err);
      }
    }, {
      errorAlertModel: 1
    });
  }

  getStoreIdByApp() {
    return new Promise((resolve, reject) => {
      CRM.util.FHHApi({
        url: '/EM1HNCRM/API/v1/inner/object/online_store/service/get_by_link_app_id',
        data: {
          linkAppId: (window as any).Portal.appId
        },
        success: (res: any) => {
          if (res.Result.StatusCode === 0) {
            resolve(res.Value.result.storeId);
          } else {
            reject(null);
            CRM.util.alert(res.Result.FailureMessage || $t("暂时无法获取数据") + '!');
          }
        },
        error: (err: any) => {
          reject(err);
        },
      }, {
        errorAlertModel: 1
      });
    });
  }

  _buildTree(nodeList: any[]) {
    // 创建 ID 到节点的映射表
    const nodeMap: any = {};
    nodeList.forEach(node => {
      // 复制节点数据，避免修改原始数据
      nodeMap[node.id] = { ...node, children: [], active: false };
    });

    // 存储根节点
    const rootNodes: any = [];

    // 构建树结构
    nodeList.forEach(node => {
      const mappedNode = nodeMap[node.id];
      const parentId = node.pid;

      // 如果父节点不存在或 pid 为空，则视为根节点
      if (!parentId || !nodeMap[parentId]) {
        if (!rootNodes.length) {
          rootNodes.push({
            pid: '',
            name: '全部',
            storeId: mappedNode.storeId,
            id: '1',
            active: true,
          });
        }
        rootNodes.push(mappedNode);
      } else {
        const children = nodeMap[parentId].children;
        if (!children.length) {
          children.push({
            pid: '',
            name: '全部',
            storeId: mappedNode.storeId,
            id: '1',
            active: false,
          });
        }
        // 将当前节点添加到父节点的 children 中
        children.push(mappedNode);
      }
    });

    return rootNodes;
  }
}
</script>

<template src="./category-tree.html"></template>
<style src="./category-tree.less" scoped lang="less"></style>
