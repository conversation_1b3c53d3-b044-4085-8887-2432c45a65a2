<script lang="ts">
import 'reflect-metadata';
import { Component, Prop, Vue, Mixins } from 'vue-property-decorator';
import ProductPreview from '../product-preview/product-preview.vue';
import UnitSelector from '../unit-selector/unit-selector.vue';
import ProductDetailMeta from './product-detail-meta/product-detail-meta.vue';
import PricepolicyDisplay from './pricepolicy-display/pricepolicy-display.vue';
import PricepolicyDisplayjr from './pricepolicy-display-optimize/pricepolicy-display.vue';
import StockMixin from '../mixins/stock-mixin.vue';
import CommonUnitMixin from '../mixins/common-unit-mixin.vue';
import {Position} from '@/widgets/services/CartAnimationService';
import {CartService} from '../services/CartService';
import {getImageByPath} from '../utils/image-util';
import {UnitService} from '../unit-selector/UnitService';
import {collectionService} from '../services/CollectionService';
import {Unit} from '@/widgets/types';
import { getDefaultCurrencyFlag } from '../utils/product-util';
import {BomService} from "@/widgets/services/BomService";

@Component({
  name: 'ProductDetailMixin',
  components: {
    ProductPreview,
    UnitSelector,
    ProductDetailMeta,
    PricepolicyDisplay,
    PricepolicyDisplayjr,
  }
})
export default class ProductDetail extends Mixins(StockMixin, CommonUnitMixin) {
  @Prop({default: () => []}) readonly metaData!: any[];
  @Prop({default: true}) readonly isUseByModal!: boolean;
  @Prop({default: () => ({})}) readonly pageConfig!: Record<string, any>;
  @Prop({default: () => ({})}) readonly pwcHide!: Record<string, any>;

  quantity = '1';
  conversionRatio = '';
  isCollection = false;
  promotions = [];
  isShopMallMode = $dht.config.sail.isShopMallMode;
  isFixedCollocation = $dht.config.simpleCpq.isEnable;
  isSpuMode = $dht.config.sail.isSpuMode;
  defaultCurrencyFlag = getDefaultCurrencyFlag();

  get precision() {
    const value = $dht.config.meta.SalesOrderProductObj.quantityPrecision;
    return typeof value === 'number' ? value : null;
  }

  get isHidePrice() {
    return $dht.config.sail.isHidePrice;
  }

  // 是否显示主图
  get isShowProductPreview() {
    let show = true;

    if(this.pageConfig && this.pageConfig.img) {
      show = this.pageConfig.img === '1';
    }
    // 默认显示
    // console.log('isShowProductPreview show:', show);
    return show;
  }

  // 是否显示标签
  get isShowCommodityLabels() {
    let show = true;
    if(this.pageConfig && this.pageConfig.tag) {
      show = this.pageConfig.tag === '1';
    }
    // 默认显示
    return show;
  }

  // 是否显示库存
  get isShowConfigStock() {
    // 默认走原来的 stock-mixin.vue中的 isShowStock
    let show = this.isShowStock;
    if(this.pageConfig && this.pageConfig.stock) {
      show = this.pageConfig.stock === '1';
    }
    return show;
  }

  created() {}

  beforeDestroy() {}

  getCollectionStatus(params: any) {
    collectionService.getObjFavouriteStatus(params).then((res: any) => {
      this.isCollection = res.status === 1
    })
  }

  addProductToCart(products: any[] | any, animationPosition?: Position, ignoreValidationRule?: boolean) {
    return CartService.getInstance().addProductToCart(products, {animationPosition, ignoreValidationRule});
  }

  getImgList(picture: any[] = [], product: any) {
    const images: any[] = [];
    // 有可能是个空数组
    const isHasVideo = product.video || product.product_video

    if (Array.isArray(picture) && picture.length) {
      const length = Math.ceil(picture.length / 4);
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < length; i++) {
        images[i] = [];
      }
      // eslint-disable-next-line no-plusplus
      for (let j = 0; j < picture.length; j++) {
        const index = Math.floor(j / 4);
        if(picture[j].signedUrl){
          images[index].push({
            min: picture[j].signedUrl + '&size=350*0',
            max: picture[j].signedUrl + '&size=1000*0',
          });
        } else {
          images[index].push({
            min: getImageByPath(picture[j].path, '350*0', picture[j].ext),
            max: getImageByPath(picture[j].path, '1000*0', picture[j].ext),
          });
        }
      }
    }
    // 有图片的
    if(isHasVideo && isHasVideo.length > 0 && images.length > 0 ) {
      images[0].unshift({
        min: getImageByPath(null),
        max: getImageByPath(null),
        isVideo: true,
        videoUrl: product.productVideoUrl
      })
    }
    // 没有配置图片的
    if (images.length === 0) {
      images.push([{
        min: getImageByPath(null),
        max: getImageByPath(null),
        isVideo: (isHasVideo && isHasVideo.length > 0 ) ? true : false,
        videoUrl: (isHasVideo && isHasVideo.length > 0) ? product.productVideoUrl : undefined
      }]);
    }
    return images;
  }

  /**
   * 获取基本单位名称
   * @param product
   */
  getBaseUnitName(product: any) {
    const baseUnit = $dht.services.unit.getBaseUnit(product);
    return baseUnit ? baseUnit.name : '';
  }

  /**
   * 选择单位改变
   * @param unit: 选择的单位
   * @param selectSku: 当前产品
   */
  unitChange(unit: any, product: any) {
    product._selectUnit = unit;
  }

  collectionAction(product: any) {
    const me = this as any;
    let params = {
      apiName: product.object_describe_api_name,
      objId: product._id,
    }
    let isCollect = this.isCollection
    CRM.util.showLoading_tip((!isCollect ? $t('收藏中') : $t('取消收藏中')) + '...');
    let Action = !isCollect ? collectionService.addToCollectionList(params) : collectionService.removeFromCollection(params);
    Action.then(() => {
      this.isCollection = !this.isCollection;
      me.$message({
        message: !isCollect ? $t('收藏成功') : $t('已取消收藏'),
        type: 'success',
        center: true,
        duration: 1000
      });
      CRM.util.hideLoading_tip();
    });
  }

  showCpqAction(product: any) {
    let bomComp = new BomService();
    bomComp.initBom(product, {
      zIndex: FxUI.Utils.getPopupZIndex() * 1 + 1,
      // 是否显示立即下单按钮，分屏模式显示
      isShowImmediatelyBtn: !this.isUseByModal
    });
  }

  // onCollectionChange(action: 'add' | 'remove', product: any): void {
  //   product.is_in_collection = action === 'add';
  // },

  getProductStockText(product: any) {
    const stockInfo = product.virtual_available_stock ? product.virtual_available_stock : '--';
    return `${$t('库存')}: ${stockInfo}`;
  }

  getUnitConversionRatio(product: any) {
    if (!product.is_multiple_unit) return;
    UnitService.getInstance().getUnitsByProductId(product._id).then((units: Unit[]) => {
      const tips: string[] = [];
      const baseUnit = units.find(item => item.isBase);
      units.forEach((item: any) => {
        if (!item.isBase) {
          tips.push(`1${item.label}${$t('等于')}${item.conversion}${baseUnit!.label}`);
        }
      });
      this.conversionRatio = tips.join('，');
    });
  }
  /**
   * 转化图文详情
   * @param des
   */
  convertImgHtml(des: string) {
    let description = des;
    const isCustomDomain = Fx.util.isCustomDomain();
    if (description) {
      const regExp = /(<img.*?src=")(.*?)(".*?\/?>)/g;
      // https://www.ceshi115.com/FSC/EM/File/ViewTempImg?TempFileName=N_202412_05_172b278f124641fdb140482b93086633

      description = description.replace(regExp,
        (match: string, p1: string, p2: string, p3: string) => {
          let sep = '/';
          // 线上环境, 非112域名, 图片地址中的appId用 &appid= 拼接
          if(isCustomDomain && p2.includes('ViewTempImg?TempFileName=')) {
            sep = '&appid=';
          }
          const appId = (window as any).Portal.appId;
          const replaceStr = `${p2.replace('/i/', '/o/')}${sep}${appId}`;
          return `${p1}${replaceStr}${p3}`;
        });
    }
    return description || '';
  }
  /**
   * 获取价格政策信息
   */
  getPricePolicy (data: any) {
    if (!$dht.config.pricePolicy.isEnable) return;
    CRM.util.showPolicyInfo({
      masterApiName: 'SalesOrderObj',
      dataId: data.objId,
    }, data.apiName, null, true).then((res: any) => {
      this.promotions = res.policyInfo || [];
    })
  }


  /**********************以下为支持apaas增加的函数 **********************/


}
</script>
