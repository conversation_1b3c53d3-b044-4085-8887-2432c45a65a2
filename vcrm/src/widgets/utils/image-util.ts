import { Attach } from '../types/Attach';
const crmUtil = CRM.util;

function getImgHost() {
    const host = window.location.host;
    const isXJ = host.includes('xjgc.com');
    const defaultHost = host.replace(/^(dht|www|crm)/, 'img');
    const imgHost = isXJ ? crmUtil.getImgHost() : `//${defaultHost}`;

    return imgHost;
}


export function getImageByPath(
  npath:  Attach[] | string | null | undefined,
  widthAndHeight = '0*0',
  type = 'jpg',
  appId = 'FSAID_11490c84',
  defaultImage = 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png',
): string {
  const host = getImgHost();
  if (!npath) {
    if($dht.config.placeholderImg.path){
      const placeholderImg = $dht.config.placeholderImg.path;
      return `${host}/image/o/${placeholderImg}/${widthAndHeight}/${type}/${appId}`;
    }
    return 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png';
  }

  let strNPath = typeof npath === 'string' ? npath : '';
  if (Array.isArray(npath)) {
    const imageData = npath[0];
    if (imageData == null || !imageData.path) {
      return defaultImage;
    }
    strNPath = imageData.path;
  }

  return `${host}/image/o/${strNPath}/${widthAndHeight}/${type}/${appId}`;
}
