import { widgetService } from '../services/WidgetService';

// 组件
widgetService.registerWidget('cartQuantityInput', () => import(/* webpackChunkName: "input-widgets" */ './quantity-input/cart-quantity-input/cart-quantity-input.vue'));
widgetService.registerWidget('quantityInput', () => import(/* webpackChunkName: "input-widgets" */ './quantity-input/quantity-input.vue'));
widgetService.registerWidget('orderQuickluQuantityInput', () => import(/* webpackChunkName: "input-widgets" */ './order-quickly/order-quickly-quantity-input/order-quickly-quantity-input.vue'));
widgetService.registerWidget('productList', () => import(/* webpackChunkName: "productList-widgets" */ './product-list/product-list.vue'));
widgetService.registerWidget('sortSelect', () => import(/* webpackChunkName: "sortSelect-widgets" */ './sort-select/sort-select.vue'));
widgetService.registerWidget('recordType', () => import(/* webpackChunkName: "recordType-widgets" */ './record-type/record-type.vue'));
widgetService.registerWidget('cartRecordType', () => import(/* webpackChunkName: "cartRecordType-widgets" */ './cart-recordtype/cart-recordtype.vue'));
widgetService.registerWidget('cartFooter', () => import(/* webpackChunkName: "cartFooter-widgets" */ './cart-footer/cart-footer.vue'));
widgetService.registerWidget('shopCart', () => import(/* webpackChunkName: "shopCart-widgets" */ './shop-cart/shop-cart.vue'));
widgetService.registerWidget('goodDetail', () => import(/* webpackChunkName: "goodDetail-widgets" */ './product-detail/product-detail.vue'));
widgetService.registerWidget('spuDetail', () => import(/* webpackChunkName: "spuDetail-widgets" */ './product-detail/spu-detail/spu-detail.vue'));
widgetService.registerWidget('skuDetail', () => import(/* webpackChunkName: "skuDetail-widgets" */ './product-detail/sku-detail/sku-detail.vue'));
widgetService.registerWidget('productDetailMeta', () => import(/* webpackChunkName: "productDetailMeta-widgets" */ './product-detail/product-detail-meta/product-detail-meta.vue'));
widgetService.registerWidget('attachPreview', () => import(/* webpackChunkName: "attachPreview-widgets" */ './attach-preview/attach-preview.vue'));
widgetService.registerWidget('isolatedMall', () => import(/* webpackChunkName: "isolatedMall-widgets" */ './isolated-mall/isolated-mall.vue'));
widgetService.registerWidget('mallCategoryTree', () => import(/* webpackChunkName: "isolatedMall-widgets" */ './isolated-mall/category-tree/category-tree.vue'));
widgetService.registerWidget('mallShopList', () => import(/* webpackChunkName: "isolatedMall-widgets" */ './isolated-mall/shop-list/shop-list.vue'));

widgetService.registerWidget('splitScreen', () => import(/* webpackChunkName: "splitScreen-widgets" */ './split-screen/split-screen.vue'));

widgetService.registerWidget('singleSelect', () => import(/* webpackChunkName: "singleSelect-widgets" */ './modal-components/single-select/single-select.vue'));
widgetService.registerWidget('selectConfirm', () => import(/* webpackChunkName: "singleSelect-widgets" */ './modal-components/select-confirm/select-confirm.vue'));
widgetService.registerWidget('hotZoneEdit', () => import(/* webpackChunkName: "hotZoneEdit-widgets" */ './modal-components/hot-zone-edit/hot-zone-edit.vue'));
widgetService.registerWidget('connectProducts', () => import(/* webpackChunkName: "connectProducts-widgets" */ './modal-components/connect-products/connect-products.vue'));
widgetService.registerWidget('importCategoryTree', () => import(/* webpackChunkName: "importCategoryTree-widgets" */ './modal-components/import-category-tree/import-category-tree.vue'));
widgetService.registerWidget('dhtModules', () => import(/* webpackChunkName: "dhtModules-widgets" */ '../modules/components/dhtmodules/index.vue'));
widgetService.registerWidget('fullLifeCard', () => import(/* webpackChunkName: "fullLifeCard-widgets" */ '../modules/components/full-life-card/index.vue'));

// 服务
widgetService.registerWidgetService('imagePreviewService', () => import(/* webpackChunkName: "preview-widgets" */ './image-preview/imagePreviewService'));
widgetService.registerWidgetService('modalService', () => import(/* webpackChunkName: "modal-widgets" */ './d-modal/DModalService'));
widgetService.registerWidgetService('cartService', () => import(/* webpackChunkName: "cart-widgets" */ './services/CartService'));
widgetService.registerWidgetService('collectionService', () => import(/* webpackChunkName: "collection-widgets" */ './services/CollectionService'));
widgetService.registerWidgetService('productDetailService', () => import(/* webpackChunkName: "product-detail-widgets" */ './product-detail/product-detail-modal'));
widgetService.registerWidgetService('bomService', () => import(/* webpackChunkName: "bom-widgets" */ './services/BomService'));
widgetService.registerWidgetService('logService', () => import(/* webpackChunkName: "logService-widgets" */ './services/LogService'));
widgetService.registerWidgetService('unitService', () => import(/* webpackChunkName: "unitService-widgets" */ './unit-selector/UnitService'));
