
<template>
    <div class="channel">
        <div class="channel-top">
            <span>{{ $t("crm.channelguide.title") }}</span>
        </div>
        <div class="channel-switch">
            <span class="switch-span">{{ $t("crm.channelguide.switch") }}</span>
            <fx-switch v-model="switchValue" :disabled="switchValue" size="small" :before-change="beforeChange1"></fx-switch>
            <p>{{ $t("crm.setting.tradeconfigure.warn_cannot_closed") }}</p>
        </div>
        <div class="channel-middle" v-show="switchValue">
            <!-- 说明 -->
            <div class="middle-describe">
                <span>{{ $t("crm.channelguide.middle.describe") }}</span>
                <p>1.{{ $t("crm.channelguide.middle.describe1") }}</p>
                <p>2.{{ $t("crm.channelguide.middle.describe2") }}</p>
                <span>3.{{ $t("crm.channelguide.middle.span1") }}{{ labels }}{{ $t("crm.channelguide.middle.span2") }}</span>
                <p>4.{{ $t("crm.channelguide.middle.describe4") }}</p>
            </div>
            <!-- 渠道模式-->
            <div class="middle-model">
                <span class="span-title">{{ $t("crm.channelguide.middle.model") }}</span>
                <fx-radio-group v-model="modelradio" @change="changeModel">
                    <fx-radio v-for = "(item,index) in modelList" :key="index" :label="item.value" size="mini">{{ item.label }}</fx-radio>
                </fx-radio-group>
            </div>
            <!-- 适用应用 -->
            <div class="middle-app">
                <span class="span-title">{{ $t("crm.channelguide.middle.app") }}</span>
                <fx-radio-group v-model="appradio" @change="changeApp">
                    <fx-radio v-for = "(item,index) in applyList" :key="index" :label="item.value" size="mini">{{ item.label }}</fx-radio>
                </fx-radio-group>
            </div>
            <!-- 关联业务对象 -->
            <div class="middle-obj">
                <span>{{ $t("crm.channelguide.middle.obj") }}</span>
                <fx-tooltip  class="item" :effect="effectType" :content='$t("crm.dlt.notice.object")' placement="top" transition="">
                    <span class="fx-icon-question"></span>
                </fx-tooltip>
                <fx-select
                    ref="select"
                    v-model="value"
                    size="small"
                    collapse-tags
                    clearable
                    disabled
                    @change="changeSele"
                    :before-change="beforeChange"
                    :options="options"
                ></fx-select>
            </div>
            <!-- 准入相关节点 -->
            <div class="middle-node">
                <span class="node-title">{{ $t("crm.channelguide.middle.node") }}</span>
                <fx-checkbox-group v-model="checkedNodes">
                    <fx-popover
                        placement="top"
                        v-if="recruitval"
                        width="230"
                        trigger="hover">
                        <div class="poppver">
                            <span class="fx-icon-warning"></span><span class="poppver-title">{{ $t("sfa.business.tips") }}</span>
                            <p>{{ $t("crm.sfa.recruit.tips") }}</p>
                            <fx-button type="primary" size="mini" @click="ok('recruit')">{{ $t("crm_action_incentivepolicyruleobj_0") }}</fx-button>
                            <fx-button plain size="mini" @click="cancel('recruit')">{{ $t("fx.default.buttonCancel") }}</fx-button>
                        </div>
                        <fx-checkbox slot="reference" :class="{'custom-checkbox': recruitval == true}" label="recruit">{{ $t("crm.channelguide.middle.recruit") }}</fx-checkbox>
                    </fx-popover>
                    <fx-checkbox v-else label="recruit">{{ $t("crm.channelguide.middle.recruit") }}</fx-checkbox>
                    <fx-popover
                        placement="top"
                        v-if="examineval"
                        width="230"
                        trigger="hover">
                        <div class="poppver">
                            <span class="fx-icon-warning"></span><span class="poppver-title">{{ $t("sfa.business.tips") }}</span>
                            <p>{{ $t("crm.sfa.recruit.tips") }}</p>
                            <fx-button type="primary" size="mini" @click="ok('register')">{{ $t("crm_action_incentivepolicyruleobj_0") }}</fx-button>
                            <fx-button plain size="mini" @click="cancel('register')">{{ $t("fx.default.buttonCancel") }}</fx-button>
                        </div>
                        <fx-checkbox slot="reference" :class="{'custom-checkbox': examineval == true}" label="register">{{ $t("crm.channelguide.middle.examine") }}</fx-checkbox>
                    </fx-popover>
                    <fx-checkbox v-else label="register">{{ $t("crm.channelguide.middle.examine") }}</fx-checkbox>
                    <fx-popover
                        placement="top"
                        v-if="browseval"
                        width="230"
                        trigger="hover">
                        <div class="poppver">
                            <span class="fx-icon-warning"></span><span class="poppver-title">{{ $t("sfa.business.tips") }}</span>
                            <p>{{ $t("crm.sfa.recruit.tips") }}</p>
                            <fx-button type="primary" size="mini" @click="ok('browsing_rule')">{{ $t("crm_action_incentivepolicyruleobj_0") }}</fx-button>
                            <fx-button plain size="mini" @click="cancel('browsing_rule')">{{ $t("fx.default.buttonCancel") }}</fx-button>
                        </div>
                        <fx-checkbox slot="reference" :class="{'custom-checkbox': browseval == true}" label="browsing_rule">{{ $t("crm.channelguide.middle.browse") }}</fx-checkbox>
                    </fx-popover>
                    <fx-checkbox v-else label="browsing_rule">{{ $t("crm.channelguide.middle.browse") }}</fx-checkbox>
                    <fx-popover
                        placement="top"
                        v-if="qualificationval"
                        width="230"
                        trigger="hover">
                        <div class="poppver">
                            <span class="fx-icon-warning"></span><span class="poppver-title">{{ $t("sfa.business.tips") }}</span>
                            <p>{{ $t("crm.sfa.recruit.tips") }}</p>
                            <fx-button type="primary" size="mini" @click="ok('qualification')">{{ $t("crm_action_incentivepolicyruleobj_0") }}</fx-button>
                            <fx-button plain size="mini" @click="cancel('qualification')">{{ $t("fx.default.buttonCancel") }}</fx-button>
                        </div>
                        <fx-checkbox slot="reference" :class="{'custom-checkbox': qualificationval == true}" label="qualification">{{ $t("crm.channelguide.middle.qualification") }}</fx-checkbox>
                    </fx-popover>
                    <fx-checkbox v-else label="qualification">{{ $t("crm.channelguide.middle.qualification") }}</fx-checkbox>
                    <fx-popover
                        placement="top"
                        v-if="signval"
                        width="230"
                        trigger="hover">
                        <div class="poppver">
                            <span class="fx-icon-warning"></span><span class="poppver-title">{{ $t("sfa.business.tips") }}</span>
                            <p>{{ $t("crm.sfa.recruit.tips") }}</p>
                            <fx-button type="primary" size="mini" @click="ok('sign')">{{ $t("crm_action_incentivepolicyruleobj_0") }}</fx-button>
                            <fx-button plain size="mini" @click="cancel('sign')">{{ $t("fx.default.buttonCancel") }}</fx-button>
                        </div>
                        <fx-checkbox slot="reference" :class="{'custom-checkbox': signval == true}" label="sign">{{ $t("crm.channelguide.middle.sign") }}</fx-checkbox>
                    </fx-popover>
                    <fx-checkbox v-else label="sign">{{ $t("crm.channelguide.middle.sign") }}</fx-checkbox>
                </fx-checkbox-group>  
            </div>
        </div>
        <div class="channel-content" v-show="switchValue">
            <div class="channel-content-module" v-for="(item, index) in dataList" :key="index">
                <div class="top" :class="{'dashed': item.isSoon === false}">
                    <h1 class="title">{{ item.title }}</h1>
                    <span class="soon" v-show="item.isSoon"><span class="fx-icon-f-obj-app87"></span>{{ $t("crm.channelguide.soon") }}</span>
                    <span class="describe">{{ item.describe }}</span>
                </div>
                <div v-show="!item.isSoon" class="bottom">
                    <div class="bottom-template" v-for="(val,i) in item.telList" :key="i" :class="`background-`+ item.color">
                        <p :title="val.title" @click="toSkip(val.toSkip)">{{ val.title }}</p>
                        <span :title="val.template" style="width:196px" @click="toSkip(val.toSkip)">{{ val.template }}</span>
                        <span class="fx-icon-arrow-right" @click="toSkip(val.toSkip)"></span>
                        <img v-show="i !== item.telList.length -1"  src="../../../assets/images/channlejt.png" alt="">
                    </div>
                </div>
            </div>
        </div>
       <div class="channel-bottom" v-show="switchValue">
            <fx-button type="primary" size="small" @click="save">{{ $t("crm.form_save_btn") }}</fx-button>
       </div>
    </div>
</template>
<script>
import api from '@/modules/components/channelaccess/utils/api';
export default {
    name: "channelguide",
    props: {
    },
    computed: {
        radioValue() {
            return this.appradio;
        }
    },
    watch: {
        radioValue(val) {
          
            if (val == '1') {
                this.value = 'AccountObj';
            } else if (val == '2') {
                this.value = 'PartnerObj';
            } else {
                //todo 服务通
            }
        },
        checkedNodes(val) {
            this.changeAll(val);
        },
        appradio(val) {
            this.theApp = this.applyList.find(item => item.value == val).label;
        }
    },
    data() {
        return {
            labels:'',
            modelList:[], //模式集合
            applyList:[], //应用集合
            theApp: '', //当前应用
            oldValue:'',
            switchValue:false,
            recruitval:false,
            examineval:false,
            browseval:false,
            qualificationval:false,
            signval:false,
            dataList:[],
            modelradio:'channel_dealer',
            appradio:'',
            value:'',
            options:[
                {
                    label: $t("合作伙伴"),
                    value:'PartnerObj'
                },
                {
                    label: $t("客户"),
                    value:'AccountObj'
                }
            ],
            checkedNodes:[],
        }
    },
   async mounted() {
        let value =  await CRM.util.getConfigValue('open_channel_access');
        this.switchValue = value == 'open' ? true : false;
        let result = await api.fetchChannelConfig();
        this.$nextTick(() => {
            this.value = result.relatedObjectApiName;
        })
        let appList = await api.appConfigLayout();
        appList.appConfigLayouts.forEach(item => {
            if (item.apiName == "channelMode") {
                this.modelList = item.options;
            } else if (item.apiName == "applyToApp") {
                this.applyList = item.options;
                this.labels = this.applyList.map(({label}) => label).join('、');
            } else if (item.apiName == "relatedBusinessObject") {
                //todo
            }
        })
        this.appradio = result.applyToApp;
        this.theApp = this.applyList.find(item => item.value == this.appradio).label
        this.oldValue = this.appradio;
        this.modelradio = result.channelMode;
        this.checkedNodes = result.applyModules;
        const hash = new URL(window.location.href).hash;
        const key = hash.split('key-')[1];
        switch(key) {
            case 'recruit':
                this.recruitval = true;
                break;
            case 'examine':
                this.examineval = true;
                break;
            case 'browse':
                this.browseval = true;
                break;
            case 'qualification':
                this.qualificationval = true;
                break;
            case 'sign':
                this.signval = true;
                break;
        }
        if(this.checkedNodes.length === 0) {
            this.dataList = [];
        }
       
    },
    methods: {
        async setConfig(key,value,oldValue) {
            CRM.util.showLoading_tip();
            try {
                await Promise.resolve(CRM.util.setConfigValue({
                    key: key,
                    value: value,
                    oldValue: oldValue
                }));
                this.switchValue = true;
                CRM.util.remind(1, $t('开启成功'));
            } catch (error) {
                this.switchValue = false;
                CRM.util.remind(3, error || $t('操作失败!'))
            }
            CRM.util.hideLoading_tip();
        },
        beforeChange1() {
            this.setConfig('open_channel_access','open','');
        },
        cancel(val) {
            switch(val) {
                case 'recruit':
                    this.recruitval = false;
                    break;
                case 'register':
                    this.examineval = false;
                    break;
                case 'browsing_rule':
                    this.browseval = false;
                    break;
                case 'qualification':
                    this.qualificationval = false;
                    break;
                case 'sign':
                    this.signval = false;
                    break;
            }
        },
        ok(val) {
            switch(val) {
                case 'recruit':
                    this.recruitval = false;
                    break;
                case 'register':
                    this.examineval = false;
                    break;
                case 'browsing_rule':
                    this.browseval = false;
                    break;
                case 'qualification':
                    this.qualificationval = false;
                    break;
                case 'sign':
                    this.signval = false;
                    break;
            }
        },
        changeAll(val) {
            this.dataList = [];
            val.forEach(item => {
               switch(item) {
                case 'recruit':
                    this.dataList.push( {
                        title:$t("crm.channelguide.middle.recruit"),
                        isSoon:false,
                        describe:$t("crm.channelguide.newdescribe1") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.top.title1") + (this.modelradio == 'channel_dealer' ? $t("crm.channelguide.title.jx") : $t("crm.channelguide.title.dl")) + $t("crm.channelguide.end.title1"),
                        color:'green',
                        index:1,
                        telList:[
                            {
                                title:$t("crm.channelguide.title1"),
                                template:$t("crm.channelguide.newtemplate1") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.end1"),
                                toSkip:'recruit'
                            },
                            {
                                title:$t("crm.channelguide.title2"),
                                template:$t("crm.channelguide.newtemplate2") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) +  $t("crm.channelguide.end2"),
                                toSkip:'recruit'
                            }
                        ]
                    });
                    break;
                case 'register':
                    this.dataList.push({
                        title:$t("crm.channelguide.middle.examine"),
                        isSoon:false,
                        describe:$t("crm.channelguide.newdescribe2") + (this.theApp) + $t("crm.channelguide.top.title2") + (this.theApp) + $t("crm.channelguide.end.title2"),
                        color:'purple',
                        index:2,
                        telList:[
                            {
                                title:$t("crm.channelguide.title3"),
                                template:$t("crm.channelguide.newtitle3") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("审批流"),
                                toSkip:'register'
                            },
                            {
                                title:$t("crm.channelguide.title4"),
                                template:$t("crm.channelguide.template4"),
                                toSkip:'register'
                            },
                            {
                                title:$t("crm.channelguide.title5"),
                                template:$t("crm.channelguide.template5"),
                                toSkip:'register'
                            }
                        ]
                    },);
                    break;
                case 'browsing_rule':
                    this.dataList.push({
                        title:$t("crm.channelguide.middle.browse"),
                        isSoon:false,
                        describe:$t("crm.channelguide.newdescribe3") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.top.title3"),
                        color:'yellow',
                        index:3,
                        telList:[
                            {
                                title:$t("crm.maintenance.regulations"),
                                template:$t("crm.maintenance.newallfile") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.end3"),
                                toSkip:'browsing_rule'
                            },
                            {
                                title:$t("crm.configuration.regulations"),
                                template:$t("crm.configuration.newlook") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.end4"),
                                toSkip:'browsing_rule'
                            }
                        ]
                    },);
                    break;
                case 'qualification':
                    this.dataList.push({
                        title:$t("crm.channelguide.middle.qualification"),
                        isSoon:false,
                        describe:$t("crm.perfect.newplan") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.top.title4"),
                        color:'red',
                        index:4,
                        telList:[
                            {
                                title:$t("crm.perfect.set"),
                                template:$t("crm.perfect.newdiff") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.top.title4"),
                                toSkip:'qualification'
                            }
                        ]
                    });
                    break;
                case 'sign':
                    this.dataList.push({
                        title:$t("crm.channelguide.middle.sign"),
                        isSoon:false,
                        describe:$t("crm.maintenance.newcan")  + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.top.title5"),
                        color:'blue',
                        index:5,
                        telList:[
                            {
                                title:$t("crm.maintenance.content"),
                                template:$t("crm.maintenance.newall") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.end5"),
                                toSkip:'sign'
                            },
                            {
                                title:$t("crm.maintenance.plan"),
                                template:$t("crm.perfect.newdiff") + (this.value == 'AccountObj' ? $t("客户") : $t("合作伙伴")) + $t("crm.channelguide.end6"),
                                toSkip:'sign'
                            },
                        ]
                    });
                    break;  
               }
            });
            this.dataList.sort((a, b) => a.index - b.index);
        },
        changeApp(val) {
            let _this = this;
            let msg = $t("crm.channelguide.change.appmsg");
            _this.$confirm($t(msg), $t('提示'), {
                confirmButtonText: $t('确定'),
                cancelButtonText: $t('取消'),
                type: ''
            }).then(() => {
                _this.appradio = val;
                _this.oldValue = val;
                _this.value = val == 'prm' ? 'PartnerObj' : 'AccountObj';
                _this.modelradio = val == 'prm' ? 'channel_agent' : 'channel_dealer';
                setTimeout(() => {
                    this.changeAll(this.checkedNodes);
                }, 100);
            }).catch(() => {
                this.$nextTick(() => {
                    this.appradio = _this.oldValue;
                });
            });
        },
        beforeChange(a,b) {
            let _this = this;
            let msg = $t("crm.channelguide.change.msg");
            _this.$confirm($t(msg), $t('提示'), {
                confirmButtonText: $t('确定'),
                cancelButtonText: $t('取消'),
                type: ''
            }).then(() => {
                _this.value = a;
                _this.changeAll(this.checkedNodes);
            }).catch(() => {
                _this.value = b;
            });
        },
        changeSele(val) {
            console.log(val)
        },
        changeModel(val) {
            let _this = this;
            let msg = $t("crm.channelguide.change.qudao");
            if (val == 'channel_dealer') {
                _this.$confirm($t(msg), $t('提示'), {
                    confirmButtonText: $t('确定'),
                    cancelButtonText: $t('取消'),
                    type: ''
                }).then(() => {
                     _this.appradio = 'dht';
                    _this.modelradio = 'channel_dealer'
                    setTimeout(() => {
                        this.changeAll(this.checkedNodes);
                    }, 100);
                }).catch(() => {
                    _this.appradio = 'prm';
                    _this.modelradio = 'channel_agent'
                });
            } else {
                _this.$confirm($t(msg), $t('提示'), {
                    confirmButtonText: $t('确定'),
                    cancelButtonText: $t('取消'),
                    type: ''
                }).then(() => {
                      _this.appradio = 'prm';
                    _this.modelradio = 'channel_agent'
                    setTimeout(() => {
                        this.changeAll(this.checkedNodes);
                    }, 100);
                }).catch(() => {
                    _this.appradio = 'dht';
                    _this.modelradio = 'channel_dealer'
                });
            }
        },
        save() {   
            let data = {
                "channelMode": this.modelradio,
                "applyToApp": this.appradio,
                "relatedObjectApiName": this.value,
                "applyModules": this.checkedNodes
            };
            this.saveChannelConfig(data);
        },
        saveChannelConfig(data) {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/save_channel_admission_config",
                        data: data,
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                CRM.util.remind(1, $t("保存成功"));
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        toSkip(val) {
            const URL_MAP = {
                recruit:"channel-recruit",
                register:"channel-approval",
                browsing_rule:"channel-browsing-rules",
                qualification:"channel-qualifications-enhancement",
                sign:"channel-agreement-signing"
            }
            CRM.control.navigate(`#crmmanage/=/module-${URL_MAP[val]}`);
        }
    },
};
</script>
<style lang="less">
    .channel {
        background: #fff;
        height: 100%;
        .channel-top {
            height: 40px;
            line-height: 40px;
            border-bottom: 1px solid var(--color-neutrals05);
            background: var(--color-neutrals01);
            span {
                margin-left: 16px;
                font-size: 14px;
                font-style: normal;
                font-weight: 700;
                line-height: 20px;
                color: var(--color-neutrals19);
            }
        }
        .channel-switch {
            padding: 16px 16px 0 16px;
            .switch-span {
                font-size: 14px;
                font-weight: 400;
                line-height: 18px;
                color: var(--color-neutrals19);
                padding-right: 4px;
            }
            p {
                padding-top: 6px;
                color: var(--color-danger06);
            }
        }
        .channel-middle {
            background: var(--color-neutrals01);
            padding: 12px 16px 16px 16px;
            .middle-describe {
                background: var(--color-neutrals03);
                padding: 12px;
                font-size: 12px;
                line-height: 18px;
                color: var(--color-neutrals19);
            }
            .middle-model,.middle-app,.middle-node,.middle-obj {
                margin-top: 12px;
                margin-left: 6px;
                font-size: 14px;
                line-height: 20px;
                color: var(--color-neutrals19);
                .span-title {
                    margin-right: 6px;
                }
            }
            .middle-model,.middle-app {
                .el-radio {
                    .el-radio__label {
                        padding-left: 0px !important;
                    }
                }
            }
            .middle-obj {
               .el-select {
                  width: 216px;
               }
               .fx-icon-question {
                  margin-left: 2px;
                  margin-right: 5px;
                  cursor: pointer;
               }
            }
            .middle-node {
                display: flex;
                
                .custom-checkbox {
                    .el-checkbox__label {
                        color: var(--color-danger06) !important;
                    }
                    .el-checkbox__inner {
                        border-color: var(--color-danger06) !important;
                    }
                 }
                .node-title {
                    margin-right: 10px;
                }
                .el-checkbox__label {
                    padding-left: 0px !important;

                }
                .el-checkbox {
                    margin-right: 16px !important;
                }
            }
        }
        .channel-content {
            background: var(--color-neutrals03);
            margin: 0 16px;
            height: calc(100% - 438px);
            overflow-y: scroll;
            padding:0 12px;
            .channel-content-module {
                margin-bottom: 12px;
                margin-top: 12px;
                border-radius: 4px;
                padding: 16px;
                background: var(--color-neutrals01);
                .top {
                    .title {
                        display:inline-block;
                        font-size: 14px;
                        line-height: 20px;
                        font-style: normal;
                        font-weight: 700;
                        margin-bottom: 8px;
                        vertical-align: middle;
                        color: var(--color-neutrals19);
                    }
                    .soon {
                        display:inline-block;
                        margin-left: 8px;
                        border-radius: 2px;
                        border: 1px solid var(--color-warning03);
                        background: var(--color-warning01);
                        color: var(--color-warning06);
                        padding-right: 4px;
                        vertical-align: text-bottom;
                        .fx-icon-f-obj-app87 {
                            padding: 0 2px 0 4px;
                            &::before {
                                color: var(--color-warning06);
                            }
                        }
                    }
                    .describe{
                        display: block;
                        font-size: 12px;
                        line-height: 18px;
                        color: var(--color-neutrals11);
                    }
                }
                .bottom {
                    padding-top: 16px;
                    display: flex;
                    .bottom-template {
                        margin-right: 66px;
                        cursor: pointer;
                        height: 78px;
                        border: 1px solid var(--color-neutrals05);
                        border-radius: 4px;
                        width: 236px;
                        position: relative;
                        .fx-icon-arrow-right {
                            position: absolute;
                            right: 0px;
                            bottom: 32px;
                            width: 16px;
                            height: 16px;
                            &::before {
                                color: var(--color-neutrals07);
                            }
                        }
                        p {
                            padding: 12px 12px 4px 12px;
                            font-size: 14px;
                            font-weight: 400;
                            line-height: 20px;
                            width: 80%;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            overflow: hidden;
                            color: var(--color-neutrals19);
                        }
                        span {
                            font-size: 12px;
                            line-height: 16px;
                            padding: 0 12px;
                            text-overflow: ellipsis;
                            overflow: hidden;
                            -webkit-line-clamp: 2;
                            display: -webkit-box;
                            -webkit-box-orient: vertical;
                            color: var(--color-neutrals11);
                            height: 30px;
                        }
                        img {
                            position: absolute;
                            right: -54px;
                            bottom: 43%;
                            width: 41px;
                            height: 8px;
                            cursor: auto;
                        }
                    }
                    .background-green {
                        border-left: 3px solid var(--color-success05);
                    }
                    .background-purple {
                        border-left: 3px solid #976AE8;
                    }
                    .background-yellow {
                        border-left: 3px solid #FF9B29;
                    }
                    .background-red {
                        border-left: 3px solid #ff4a66;
                    }
                    .background-blue {
                        border-left: 3px solid #36c2b6;
                    }
                }
                .dashed {
                    padding-bottom: 16px;
                    border-bottom: 1px dashed var(--color-neutrals05);
                }
            }
        }
        .channel-bottom {
            position: absolute;
            bottom: 0;
            width: 100%;
            padding: 8px 16px;
            background: var(--color-neutrals01);
            .fx-button {
                cursor: pointer;
            }   
        }
    }
    .poppver {
        height: 82px;
        .fx-icon-warning {
            font-size: 16px;
            margin-right: 8px;
        }
        .poppver-title {
            font-size: 14px;
            line-height: 20px;
            color: var(--color-neutrals19);
        }
        p {
            font-size: 12px;
            margin-top: 4px;
            line-height: 18px;
            margin-left: 22px;
            color: var(--color-neutrals15);
        }
        .fx-button {
            margin-top: 12px;
            margin-left: 120px;
        }
        .el-button+.el-button {
            margin-left: 5px;
        }
    }
</style>
