/**
* @desc: 价目表详情页中，编辑价目表明细组件；
* @author: wang<PERSON><PERSON>
* @date: 2020-08-14
* @param:{
        isAdd: false, // 是否来自新建
        fromSavePd,   // 新建产品并快速维护到价目表及产品详情页直接编辑价目表明细
        pricebook_id: '',
        pricebook_id__r: '',
        apiname: '',
        _id: '',
        tenant_id: '',
        data: [],
        columns: me.table.get('columns'),
        oData:oData,
}
* @callback // 编辑成功回调
*/

<template>
    <div>
        <div class="tableBox" @click="tableClick" ref="tableBox"></div>
        <!-- 批量编辑浮动价格 -->
        <price-border
            v-if="isShowPriceBorder"
            @callback="priceBorderCallBack"
            @closed="priceBorderClosed"
        ></price-border>
        <add-com
            :param="addParam"
            v-if="isShowAdd"
            @callback="addCallBack"
            @closed="addClosed"
        ></add-com>
    </div>
</template>

<script>
    const util = CRM.util;
    import {requireTable, requireField} from '@common/require.js';
    import PriceBorder from './priceborder';
    import AddCom from '@detail/view/v3/pricebookobj/components/relation/Add';

    export default {
        name: "EditPricebookProduct",

        props: {
            param: Object,
        },

        data() {
            return {
                zIndex: util.getzIndex(),
                cdata: this.param,
                isShowPriceBorder: false,
                isShowAdd: false,
                filterIds: []
            }
        },

        computed: {
            addParam() {
                return {
                    pricebook_id: this.param.pricebook_id,
                    filterIds: this.filterIds,
                    filters: this.filterIds.length ? [{
                        // field_name: 'product_id',
                        field_name: '_id',
                        field_values: this.filterIds,
                        operator: 'NIN',
                    }] : [],
                    callback: this.addCallBack,
                    destroy: this.addClosed,
                    // columns: this.table.get('columns'),
                    describe: _.isEmpty(this.param.describe) ? this.objectDescribe : this.param.describe,
                    showChildren: this.param.showChildren,
                    ...(this.param.relationAddParam || {})
                }
            },
        },

        components: {
            PriceBorder,
            AddCom
        },

        methods: {
            render() {
                let me = this;
                CRM.util.waiting($t('数据加载中') + '...');
                this._fetchColumns(this.param, async function (obj) {
                    me.param.data = await me.parseParam(me.param.data);
                    if (me.param.apiname === 'PriceBookProductObj' && obj !== me.param) {
                        me.param.columns = obj.columns;
                    }
                    me.renderTable(me.param);
                    CRM.util.waiting(false);
                });
            },

            // 查多单位产品的单位信息；
            async fetchMultiInfo(data) {
                if (!CRM._cache.multi_unit_price_book) return data;
                let multiPro = this.getMultiProduct(data);
                if (!multiPro.length) return data;
                let res = await CRM.util.getProductMultiInfo({
                    productIds: multiPro
                });
                if (!res || (res && res.datas && !res.datas.length)) return data;
                this.setMultiInfo(data, res.datas || []);
                return data;
            },

            // 补充产品多单位信息
            setMultiInfo(data, multiInfo) {
                data.forEach(item => {
                    let f = multiInfo.find(d => d.productId === item.product_id || item._id);
                    if (f) item.multiUnitInfos = f.infos;
                })
            },

            // 获取多单位产品
            getMultiProduct(data) {
                let res = [];
                data.forEach(item => {
                    if (item.is_multiple_unit) res.push(item.product_id || item._id)
                });
                return res;
            },

            // 处理多单位定价逻辑；
            async parseParam(data) {
                this.initData(data);
                if (CRM._cache.multi_unit_price_book && this.param.isAdd) {
                    await this.fetchMultiInfo(data);
                    data = this.splitMultipleProduct(data, this.objectDescribe);
                }
                return data
            },

            // 处理多单位产品；
            splitMultipleProduct(data, describe) {
                let res = [];
                let options = describe.fields.actual_unit.options;

                function _findLabel(unit) {
                    let f = options.find(o => o.value === unit);
                    return f.label;
                }

                let dec = describe.fields.pricebook_sellingprice.decimal_places;
                data.forEach(item => {
                    if (item.multiUnitInfos && item.multiUnitInfos.length > 1) {
                        item.multiUnitInfos.forEach(m => {
                            let o = $.extend(true, {}, item, {
                                unit: m.unit_id,
                                actual_unit: m.unit_id,
                                is_pricing: m.is_pricing,
                                unit__tpd: _findLabel(m.unit_id),
                                _stand_price: CRM.util.formatDecimalPlace(m.price, dec)
                            });
                            res.push(o)
                        })
                    } else {
                        if(!item.actual_unit) item.actual_unit = item.unit__v ||item.unit;
                        res.push(item)
                    }
                })
                return res;
            },

            // 设置单位字段不可编辑；
            setColumnReadonly(columns) {
                if (!CRM._cache.multi_unit_price_book) return;
                let c = columns.find(item => item.api_name === 'actual_unit');
                if (c) c.isEdit = false;
            },

            async renderTable(obj) {
                var me = this;
                me.setColumnReadonly(obj.columns);
                if (obj.fromSavePd) {
                    obj.autoHeight = true;
                    obj.maxHeight = '400px';
                }
                let Table = await requireTable();
                var table = new Table({
                    autoHeight: obj.autoHeight,
                    [obj.maxHeight ? 'maxHeight':'']: obj.maxHeight, // 防止table中maxHeight的默认值修改
                    className: 'crm-table crm-table-noborder crm-table-open crm-table-add-price-book-product',
                    isMyObject: true,
                    api_name: obj.apiname,
                    $el: $(me.$refs.tableBox),
                    showMultiple: true,
                    showRequiredTip: true,
                    columns: obj.columns,
                    doStatic: true,
                    lineCheck: true,
                    showPage: false,
                    showSize: false,
                    openStart: true,
                    zIndex: util.getzIndex() * 1 + 10 || 900,
                    sizeType: 'md',
                    showBatchBtns: true,
                    isNewCalculate:true,
                    batchBtns: [
                        {
                            text: $t("删除"),
                            className: 'j-del',
                            isHidden: !obj.isAdd,
                            btnId: 'del',
                            isCommon: true
                        }, {
                            text: $t("批量编辑折扣"),
                            className: 'j-edit-discount',
                            isHidden: !_.findWhere(obj.columns, {data: 'discount'}),
                            btnId: 'editDiscount',
                            isCommon: true
                        }, {
                            text: $t("批量编辑价目表售价"),
                            className: 'j-edit-sellingprice',
                            isHidden: !_.findWhere(obj.columns, {data: 'pricebook_sellingprice'}),
                            btnId: 'editSellingPrice',
                            isCommon: true
                        }, {
                            text: $t("批量编辑售卖浮动上下限价格"),
                            className: 'j-edit-priceborder',
                            isHidden: !_.findWhere(obj.columns, {data: 'ceiling_price'}) && !_.findWhere(obj.columns, {data: 'floor_price'}),
                            btnId: 'editFloorPrice',
                            isCommon: true
                        }, {
                            // 批量编辑有效开始时间
                            text: $t("vcrm.batch_edit{{label}}", {
                                label: (_.findWhere(obj.columns, {data: 'start_date'}) || {}).label || ''
                            }),
                            className: 'j-edit-startdate',
                            isHidden: !CRM._cache.priceBookProductValidPeriod,
                            btnId: 'editStartDate',
                            isCommon: true
                        }, {
                            // 批量编辑有效结束时间
                            text: $t("vcrm.batch_edit{{label}}", {
                                label: (_.findWhere(obj.columns, {data: 'end_date'}) || {}).label || ''
                            }),
                            className: 'j-edit-enddate',
                            isHidden: !CRM._cache.priceBookProductValidPeriod,
                            btnId: 'editEndDate',
                            isCommon: true
                        }
                    ],
                    operate: {
                        btns: obj.isAdd && !obj.isHiddenAddBtn ? [
                            {
                                action: 'add',
                                text: obj.fromSavePd ? $t('选择价目表') : $t('添加产品'),
                                index: 1,
                                className: 'j-action-add',
                            }
                        ] : []},
                        initComplete:function (el) {
                            el.find('.dt-term-batch').hide();
                    }
                });
                me._columns = obj.columns;
                me.table = table;
                // 自定义操作列 复制 删除
                me.table.on('copy', me.copyHandle);
                me.table.on('del', me.delHandle);
                // batchBtns中只要有一个按钮配置isCommon:true, 则按钮触发方式为batchbtn.change
                me.table.on('batchbtn.change', function(item) {
                    me.batchBtnChange(item);
                });
                if (obj.fromSavePd) {
                    _.each(obj.data, function (a) {
                        a.pricebook_id = a.pricebook_id
                        a.pricebook_id__r = a.pricebook_id__r;
                        me.filterIds.push(a.product_id);
                    });
                } else {
                    _.each(obj.data, function (a) {
                        a.pricebook_id = obj.pricebook_id;
                        a.pricebook_id__r = obj.pricebook_id__r;
                        me.filterIds.push(a.product_id);
                    });
                }

                if (obj.isAdd) {
                    obj.data = _.map(obj.data, function (item) { // 补充数据
                        if (item.category === '#%$') {
                            item.category__r = '#%$';
                        }
                        return _.extend({}, me._getDefaultData(), item, me._getQuoteData(item._quoteData));
                    });

                    /* 保存的产品添加到多价目表，价目表明细中对应多价目表需要一条一条请求计算服务（限制选择价目表条数为10, 8.0.5堰塞湖解除选择条数限制，） */
                    /* 为啥一条一条计算？价目表明细上引用字段a,引用了主对象价目表上的字段b，计算出错（选择多条时，除和masterdata的id一致的明细数据） */
                    if (obj.fromSavePd) {
                        // 并发
                        let allData = table.getTableAllData();
                        let flatten = _.zip(obj.data);

                        me.oneByOneBatchCalculate(flatten, allData);
                    } else {
                        me._getServerDefaultData(obj.data, obj, async function () {
                            await me.$emit('beforeAddNewData', obj.data || []);
                            table.doStaticData(me.addCurrencyAndRate(obj.data || []));
                            me.setLockReadOnly(obj.data, table);
                        });
                    }
                    return;
                }

                table.doStaticData(me.addCurrencyAndRate(obj.data || []));
                this.setLockReadOnly(obj.data, table);
                
            },
            copyHandle($target, e) {
                let $tr = $target.closest('tr');
                let index = $tr.index();
                let rowData = this.table.getCurData()[index];
                var data = JSON.parse(JSON.stringify(rowData));
                this.table.insertRow(data, {
                    pos: index
                });
            },
            delHandle($target, e) {
                let $tr = $target.closest('tr');
                let index = $tr.index();
                let rowData = this.table.getCurData()[index];
                this.setFilterData([rowData]);
                this.table.delRow(index);
            },
            /**
             * @description: 一个产品维护到多个价目表中。见259行 “为啥一条一条计算”
             * @param {Array} selectedData [[],[],[],···]
             * @param {Array} originalData [{}, {},···]
             * @return {*}
             */
            oneByOneBatchCalculate(selectedData, originalData = []) {
                const me = this;
                function asyncWork(tsk) {
                    return new Promise((resolve, reject) => {
                        me._getServerDefaultData(tsk, me.param, function (data, error) {
                            me.addCurrencyAndRate(data, data[0]._quoteData.oData)
                            error ? reject('calc error!') : resolve(data);
                        }, () => { reject('calc error!') });
                    });
                }

                function finishCb(endResult = [], errResult = []) {
                    let data = _.flatten(endResult, true);

                    if (errResult.length) {
                        let err = _.flatten(errResult, true);
                        CRM.util.alert($t('价目表：{{errName}}明细计算出错，请重新选择', { errName: _.pluck(err, 'pricebook_id__r').join('，') }));
                    }

                    originalData = originalData.concat(data);
                    me.table.doStaticData(originalData);
                    me.setLockReadOnly(originalData, me.table);
                    me.$emit('calculateEnd');
                }
                
                me.$emit('calculateStart');
                CRM.util.pool(asyncWork, selectedData, finishCb, void(0), void(0), {
                    progressCb({total, progress}) {
                        me.$emit('calculateStart', `${ (Number.parseFloat(progress/total) * 100).toFixed()}%`);
                    }
                });
            },

            // 是否锁定
            isLocked(data){
               return data.lock_status == '1';
            },

            // 价目表明细是锁定状态的，不允许编辑；
            setLockReadOnly(data = [], table){
                if(this.param.isAdd) return;
                let columns = table.getAllColumns();
                data.forEach((item, index) => {
                    if(this.isLocked(item)){
                        columns.forEach(c => {
                            if(c.isEdit && c.api_name){
                                table.setCellsStatus(c.api_name, index, {
                                    readonly: true
                                })
                            }
                        })
                    }
                })
            },
            
            _fetchColumns: function (obj, callback) {
                var me = this;
                me._fetchAJax && me._fetchAJax.abort();
                me._fetchAJax = util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/' + obj.apiname + '/controller/DescribeLayout',
                    data: {
                        include_detail_describe: true,
                        include_layout: true,
                        apiname: obj.apiname,
                        layout_type: obj.isAdd
                            ? 'add'
                            : 'edit',
                        recordType_apiName: 'default__c'
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            me.afterFetchColumns(res.Value, obj, callback);
                            return;
                        }
                        callback && callback(obj);
                    },
                    complete: function () {
                        me._fetchAJax = null;
                    }
                }, {errorAlertModel: 1})
            },
            
            async afterFetchColumns(value, obj, callback) {
                this.orderMap = CRM.util.calculateFieds2OrderMap(value);
                this.objectDescribe = value.objectDescribe;
                let field = await requireField();
                this.fields = field;
                obj.columns = this.parse(value, field);
                // 包含本价目表已选产品开关开启&&添加产品时：选择数据时，增加复制,删除功能
                if (obj.isAdd && CRM._cache.priceBookSelectProduct) {
                    obj.columns.push({
                        dataType: 'operate',
                        data: null,
                        width: 120,
                        title: $t("操作"),
                        lastFixed: true,
                        render: function (data, type, full, helper, index) {
                            let id = full.product_id;
                            let btn = `<a data-id="${id}" tb-action-type="copy" href="javascript:;" class="j-copy" >${$t('复制')}</a>
                            <a data-id="${id}" tb-action-type="del" href="javascript:;" class="j-del" >${$t('删除')}</a>`;
                            return btn;
                        }
                    });
                }
                callback && callback(obj);
            },
            // 处理table列 columns
            parse: function (res, field) {
                this.objectDescribe = res.objectDescribe;

                var columns = field.components.md.prototype.formatcolumns(res.layout.components[0].field_section, res.objectDescribe.fields, '', true);
                _.each(columns, function (obj) {
                    var apiname = obj.api_name;

                    obj.noLock = true;    //不支持固定列
                    obj.noSupportBatchEdit = true;
                    if (apiname === 'pricebook_sellingprice') {
                        obj.__isEdit = true;
                    }
                    if (apiname === 'product_id') {
                        obj.isEdit = false;
                        obj.is_readonly = true;
                        return;
                    }
                    if (apiname === 'pricebook_price') {
                        obj.pre_expression_key = 'discount';
                        obj.pre_expression = '$pricebook_price$/$stand_price$';
                    }
                    // 取不到__r字段，无法展示
                    if (apiname === 'pricebook_id') {
                        obj.isHidden = true;
                    }
                    // 价目表明细币种字段只读
                    if (apiname === 'mc_currency') {
                        obj.isEdit = false;
                    }

                });

                columns = this.param.parseColumns ? this.param.parseColumns(columns) : columns;

                return columns;
            },

            isMultipleUnit(data){
                return data.hasOwnProperty('is_multiple_unit__v') ? data.is_multiple_unit__v : data.is_multiple_unit;
            },

            /**
             * @desc 请求server计算的默认值
             */
            _getServerDefaultData: function (data, obj, cb, errorCb) {
                var me = this,
                    apiname = this.cdata.apiname,
                    calculate_fields = [],
                    odl = {},
                    table = me.table,
                    columns = table.options.allColumns;
                _.each(table.options.columns, function (a) {
                    if ((a.expression && !a.placeHolder) || a.quote_field) {
                        calculate_fields.push(a.api_name)
                    }
                });
                _.each(this.objectDescribe.fields, function (a) {
                    if(a.default_is_expression && a.default_value && !calculate_fields.includes(a.api_name)){
                        calculate_fields.push(a.api_name);
                    }
                });
                if (!calculate_fields.length) {
                    cb && cb(data);
                    return;
                }
                if (CRM._cache.multi_unit_price_book) {
                    CRM.util.deleteArrChildren(calculate_fields, ['stand_price', 'pricebook_sellingprice'])
                }
                _.each(data, function (item, index) {
                    odl[index] = item;
                    // item._quoteData = item._origin = item._origin__tpd = void 0;
                    item._origin = item._origin__tpd = void 0;
                    if (!obj.fromSavePd) {
                        item._quoteData = void 0;
                    }
                    if(CRM._cache.multi_unit_price_book && !me.isMultipleUnit(item)){
                        item.pricebook_sellingprice = item.hasOwnProperty('price') ? item.price : item.stand_price;
                    }
                })

                !obj.fromSavePd && me._calculateAjax && me._calculateAjax.abort();
                me._calculateAjax = util.FHHApi({
                    // url: '/EM1HNCRM/API/v1/object/calculate/service/batchDataExpressionCalculate',
                    // data: {
                    //     calculate_data_list: CRM.util.stringify([{
                    //         api_name_list: api_name_list,
                    //         objectDataList: odl,
                    //         objectDescribeApiName: apiname
                    //     }])
                    // },
                    url: '/EM1HNCRM/API/v1/object/calculate/service/batchCalculate',
                    data: {
                        masterObjectApiName: "PriceBookObj",
                        masterData: obj.fromSavePd ? me._filterrCalculateMasterData(data[0]['_quoteData']) : me._filterrCalculateMasterData(obj),
                        detailDataMap: {
                            "PriceBookProductObj": odl
                        },
                        calculateFieldApiNames: {
                            "PriceBookProductObj": calculate_fields
                        },
                        modifiedObjectApiName: "PriceBookProductObj",
                        modifiedDataIndexList: _.keys(odl),
                        calculateFields: CRM.util.getCalculateFieldOrder(me.orderMap, {
                            "PriceBookProductObj": calculate_fields
                        })
                    },
                    success: function (req) {
                        const ERR = (req.Result.StatusCode !== 0);
                        if (req.Result.StatusCode == 0) {
                            req = req.Value.calculateResult[apiname] || {};
                            _.each(data, function (item, index) {
                                var multiunit = {};
                                //多单位产品价目表价格和价目表售价取定价单位价格和单位
                                let price = CRM._cache.multi_unit_price_book && item.is_multiple_unit ? item._stand_price : item.stand_price;
                                if (me.isMultipleUnit(item)) {
                                    multiunit = {
                                        pricebook_price: price,
                                        pricebook_sellingprice: price,
                                        unit: item.unit,
                                        unit__r: item.unit__r || item.unit,
                                        unit__v: item.unit__v || (item._quoteData && item._quoteData['unit'])
                                    }
                                    if (!CRM._cache.multi_unit_price_book) {
                                        delete multiunit.pricebook_price;
                                        delete multiunit.pricebook_sellingprice;
                                    }
                                }
                                _.extend(item, req[index] || {}, multiunit);
                            });
                        }
                        const plugin = (window.PAAS && PAAS.get_plugin && PAAS.get_plugin(me.param.oData.object_describe_api_name + '.detail.MultiTable')) || {};
                        if (plugin.mdDidCalculate) {
                            plugin.mdDidCalculate(data).then(data => {
                                cb && cb(data, ERR);
                            }).catch((data) => {
                                cb && cb(data, ERR);
                            });
                        } else {
                            cb && cb(data, ERR);
                        }
                    },
                    error: function() {
                        errorCb && errorCb();
                    },
                    complete: function () {
                        me._calculateAjax = null;
                    }
                }, {errorAlertModel: 1})
            },

            //过滤参与计算的数据，只给server提交必要的字段，减少数据量
            _filterrCalculateMasterData: function (obj) {
                if (this.objectDescribe) {
                    var fields = this._filterCalculateFields(this.objectDescribe.fields);
                    return _.pick(obj.oData, fields);
                }
                return obj.oData;
            },

            _filterCalculateFields: function (fields) {
                var list = ['_id', 'record_type', 'life_status', 'object_describe_api_name'];
                _.each(fields, function (a) {
                    if (a.calculate_relation || a.type === 'formula' || a.type === 'count' || a.default_value || a.quote_field) {
                        list.push(a.api_name);
                    }
                });

                return list;
            },
            // 取字段默认值
            _getDefaultData: function () {
                if (!this._defaultData) {
                    var data = {};
                    _.each(this.objectDescribe.fields, function (a) {
                        !a.default_is_expression && a.default_value && (data[a.api_name] = a.default_value);
                    });
                    this._defaultData = data;
                }

                return this._defaultData;
            },

            _getQuoteData: function (obj) {
                if (!this._quoteFields) {
                    var quote = [];
                    _.each(this._columns, function (a) {
                        if (a.quote_field) {
                            var arr = a.quote_field.split('__r.');
                            quote.push([
                                a.api_name, arr[1]
                            ]);
                            return;
                        }
                    });
                    this._quoteFields = quote;
                }

                var data = {};

                _.each(this._quoteFields, function (arr) {
                    data[arr[0]] = _.unescape(obj[arr[1] + '__tpd']) || obj[arr[1]];
                });
                if (obj.isChildren) data.isChildren = obj.isChildren;
                return data;
            },

            getValue: function () {
                var me = this;
                var table = this.table;
                if (!table?.curData)
                    return;
                var fields = ['version', '_id'];
                var regs = {};
                var arrtypes = {};
                var numtypes = {};
                _.each(table.options.columns, function (a) {
                    fields.push(a.data);
                    var art = a.type;
                    regs[a.data] = art !== 'formula' && a.isRequired;
                    if (art === 'select_one') {
                        fields.push(a.data + '__o');
                    }
                    var aoo = {
                        image: arrtypes,
                        file_attachment: arrtypes,
                        signature: arrtypes,
                        select_many: arrtypes,
                        employee: arrtypes,
                        number: numtypes,
                        currency: numtypes
                    }[art];
                    aoo && (aoo[a.data] = 1)
                });
                // 自定义字段也保存
                _.each(this.objectDescribe.fields, function (a) {
                    if(a.api_name.includes('__c') && !fields.includes(a.api_name)){
                        fields.push(a.api_name);
                    }
                });

                var data = [];
                var hasError;
                var floatPriceErrorIndex = null;
                var cdata = this.cdata;

                _.find(table.curData.data, function (a, index) {
                    var tobj = _.pick(a, fields);

                    tobj.object_describe_id = cdata._id;
                    tobj.object_describe_api_name = cdata.apiname;
                    tobj.tenant_id = cdata.tenant_id;
                    if(cdata.pricebook_id) tobj.pricebook_id = cdata.pricebook_id;

                    if (cdata.fromSavePd) {
                        tobj.pricebook_id = a.pricebook_id;
                        tobj.object_describe_id = me.objectDescribe._id;
                        tobj.tenant_id = me.objectDescribe.tenant_id;
                        a.pricebook_id__r && _.extend(tobj, { pricebook_id__r : a.pricebook_id__r });
                    }
                    if(!tobj.hasOwnProperty('discount')) tobj.discount = a.discount;

                    _.each(tobj, function (ta, tk) {
                        if (!_.isArray(ta) && arrtypes[tk]) {
                            tobj[tk] = [];
                            return;
                        }
                        if (ta === '' && numtypes[tk]) {
                            tobj[tk] = null;
                            return;
                        }
                    });
                    if (a.isChildren) tobj.isChildren = a.isChildren;
                    data.push(tobj);

                    _.find(fields, field => {
                        if (regs[field]) {
                            let v = a[field];
                            if (arrtypes[field]) {
                                hasError = !v || !v.length;
                            } else {
                                hasError = v === undefined ||v === null || v === void 0 || v === '';
                            }
                        }
                        return hasError;
                    });

                    //校验价目表售价是否在浮动范围价格之间
                    let floorFlag = +tobj.floor_price && +tobj.floor_price > +tobj.pricebook_sellingprice,
                        cellFlag = +tobj.ceiling_price && +tobj.ceiling_price < +tobj.pricebook_sellingprice;
                    if (floorFlag || cellFlag) {
                        floatPriceErrorIndex = index;
                        hasError = true;
                        return hasError;
                    }
                    return hasError;
                });
                if (hasError || !table.editValid()) {
                    if (floatPriceErrorIndex !== null) {
                        table.$el.find('.tr[data-index="' + floatPriceErrorIndex + '"]').addClass('tr-error');
                        let str = $t("价目表售价超出价目表销售浮动价格！浮动价格是："),
                            info1 = data[floatPriceErrorIndex].ceiling_price ? $t("上限：") + data[floatPriceErrorIndex].ceiling_price : "",
                            info2 = data[floatPriceErrorIndex].floor_price ? $t("下限：") + data[floatPriceErrorIndex].floor_price : "";

                        util.alert(str + info1 + (info1 && info2 ? "，" : "") + info2);
                        return
                    } else {
                        util.alert($t("请完善表格上的必填项"));
                        return
                    }
                }

                return data;
            },
            // 校验有效开始时间和有效结束时间
            validDate(data) {
                let res = data.filter(({start_date, end_date}) => {
                    return start_date && end_date && start_date >= end_date;
                });
                if (res.length) {
                    CRM.util.alert($t('有效开始时间必须小于有效结束时间'));
                }
                return !res.length;
            },
            // 校验产品
            validProduct(data) {
                let valid = true;
                if (!!this.param?.is_stratified_pricing) {
                    valid = this.validStratifiedPricing(data);
                }
                return valid;
            },
            validStratifiedPricing(data) {
                const product_map = new Map();
                for (let item of data) {
                    const { product_id, is_stratified_pricing } = item;
                    if (product_map.has(product_id)) {
                        if (!!is_stratified_pricing !== product_map.get(product_id)) {
                            // 同一价目表中，同一个产品，是否分层定价必须相同
                            CRM.util.alert($t('sfa.vcrm.detail.components.edit_pricebook_product.tiered_pricing_must_be_same'));
                            return false;
                        }
                    } else {
                        product_map.set(product_id, !!is_stratified_pricing);
                    }
                }
                return true;
            },
            submit: async function (e) {
                var me = this;
                var data = me.getValue();
                if (!data || !data.length || !this.validDate(data))
                    return;
                
                if (!this.validProduct(data)) return;

                let clone_data = CRM.util.deepClone(data);
                if (me.param.submitBefore) {
                    let status =  await me.param.submitBefore({dataList: clone_data});
                    if (!status) return;
                }
                let batchingTask = me.cdata.fromSavePd && (me.cdata.isAdd && (data.length > 300) || !me.cdata.isAdd && (data.length > 100));
                
                function submitData(data, silent) {
                    let ajaxInstance;
                    let promise = new Promise((resolve, reject) => {
                        ajaxInstance = util.FHHApi({
                            url: '/EM1HNCRM/API/v1/object/PriceBookProductObj/action/' + (
                                me.cdata.isAdd
                                    ? 'BulkCreate'
                                    : 'BulkEdit'),
                            data: {
                                data_list: data,
                                dataSource: me.cdata.fromSavePd ? 1 : 0
                            },
                            success: function (res) {
                                me.handed = true;
                                if (res.Result.StatusCode === 0) {
                                    if (!silent) {
                                        me.$emit('callback', me.handed);
                                        util.remind(1, $t("操作成功"));
                                    }
                                    resolve();
                                    return;
                                }
                                !silent && util.alert(res.Result.FailureMessage || $t("操作失败"));
                                reject();
                            },
                            error: function() {
                                reject();
                            },
                        }, {
                            errorAlertModel: 1,
                            submitSelector: $(e.currentTarget)
                        })
                    });

                    if(ajaxInstance) ajaxInstance.promisifyInstance = promise;
                    return ajaxInstance;
                }
                
                if (!batchingTask) {
                    return submitData(data);
                } else {
                    let asyncWork = function(item) {
                        let r = submitData(item, true);
                        return r?.promisifyInstance;
                    };
                    let toNDimension = function (arr = [], dimension = 1) {
                        if (dimension === 1) {
                            return arr.slice();
                        }

                        let len = arr.length;
                        let lineNum = (len % dimension) === 0 ? (len / dimension) : Math.ceil(len / dimension);
                        let rst = [];
                        for (let i = 0; i < lineNum; i++) {
                            rst.push(arr.slice(i * dimension, i * dimension + dimension));
                        }

                        return rst;
                    }
                    // 数据量太大300条以上，服务报错。开启一个worker, 实现串行提交。
                    CRM.util.waiting('0%');
                    CRM.util.pool(asyncWork, toNDimension(data, 100)/* m行n列数组，n代表提交的条数 */, function(resList, errList) {
                        CRM.util.waiting(false);

                        if (!errList.length) {
                            me.$emit('callback', true);
                            util.remind(1, $t("操作成功"));
                        } else {
                            let err = _.flatten(errList, true);
                            CRM.util.alert($t('价目表：{{errName}}明细错误，请重新提交', { errName: _.pluck(err, 'pricebook_id__r').join('，') }), () => {
                                me.$emit('callback', true);
                            });
                        }
                    }, 1, 2, {
                        progressCb({total, progress}) {
                            CRM.util.waiting(`${ (Number.parseFloat(progress/total) * 100).toFixed() }%`);
                        }
                    });
                    return;
                }

            },

            // 删除
            delItems: function () {
                let checkedData = this.table.getCheckedData() || [];
                if (CRM._cache.multi_unit_price_book) {
                    // 如果删除的是定价单位，需要把该产品的其他单位一起删除；
                    let data = this.table.getTableAllData();
                    let indexArr = [];
                    checkedData.forEach(item => {
                        if (item.is_pricing) {
                            data.forEach((d, index) => {
                                if (d.product_id === item.product_id && d.__tbIndex !== item.__tbIndex && d.pricebook_id === item.pricebook_id) indexArr.push(index);
                            })
                        }
                    })
                    if (indexArr.length) {
                        let confirm = CRM.util.confirm($t('删除定价单位提示'), $t('提示'), () => {
                            this.table && this.table.delRowByIndex(indexArr);
                            this.table && this.table.delCheckedRow();
                            confirm.hide();
                            // 如果数据中还有剩下的多单位产品，选产品时还是需要过滤；
                            let filterData = [];
                            checkedData.forEach(d => {
                                let f = _.find(data, item => item.product_id === d.product_id);
                                if (!f) filterData.push(d);
                            })
                            this.setFilterData(filterData);
                        })
                    } else {
                        this.table && this.table.delCheckedRow();
                        checkedData = _.filter(checkedData, item => {
                            let f = _.find(data, d => d.product_id === item.product_id);
                            return !f;
                        });
                        this.setFilterData(checkedData);
                    }
                    return;
                }
                this.setFilterData(checkedData);
                this.table && this.table.delCheckedRow();
            },

            // 删除了一些产品，需要去掉过滤条件中的filterId
            setFilterData: function (checkedData) {
                _.each(checkedData, (item) => {
                    this.filterIds = _.filter(this.filterIds, cItem => cItem !== item.product_id);
                });
            },

            batchEditCallBack(){
                let data = this.table.getTableAllData();
                this.setLockReadOnly(data, this.table);
            },
            blukSetDateTime(name) {
                const tableData = this.table.getCheckedData();
                if (!tableData) {
                    CRM.util.alert($t("请选择需要编辑的数据！"));
                    return
                }
                this.table && this.table.batchEditColumn(name, false, true, {
                    ignoreEdit: false,
                    upDown: true,
                    callBack: this.batchEditCallBack.bind(this),
                    success: this.removeTableSelect,
                });
            },
            
            // 批量编辑折扣
            blukSetDiscount: function () {
                this.table && this.table.batchEditColumn('discount', false, false, {
                    ignoreEdit: false,
                    upDown: true,
                    success: this.removeTableSelect,
                    callBack: this.batchEditCallBack.bind(this),
                });
            },

            // 批量编辑价目表售价
            blukSetSellingprice: function () {
                this.table && this.table.batchEditColumn('pricebook_sellingprice', false, false, {
                    ignoreEdit: false,
                    upDown: true,
                    success: this.removeTableSelect,
                    callBack: this.batchEditCallBack.bind(this),
                });
            },

            // 批量编辑浮动价格
            blukSetPriceBorder: function () {
                const me = this,
                    tableData = me.table.getCheckedData();
                if (!tableData) {
                    CRM.util.alert($t("请选择需要编辑的数据！"));
                    return
                }
                this.isShowPriceBorder = true;
            },

            removeTableSelect() {
                this.table && $('.j-all-checkbox', this.table.$el[0]).removeClass('tb-checkbox-half-selected tb-checkbox-selected');
            },

            priceBorderCallBack(res) {
                const me = this,
                    tableData = me.table.getCheckedData();

                const ceilingFiled = me.table.getColumnByField('ceiling_price'),
                    floorField = me.table.getColumnByField('floor_price'),
                    c_decimal_places = ceilingFiled.decimal_places || 2,
                    f_decimal_places = floorField.decimal_places || 2;

                let type = res.type,
                    valUp = res.celling,
                    valDown = res.floor,
                    countVal = function (key, val) {
                        if (type == '1') {
                            return key == 'up'
                                ? (val * 1 + valUp * 1)
                                : (val * 1 - valDown * 1)
                        } else {
                            return key == 'up'
                                ? val * 1 * (1 + valUp / 100)
                                : val * 1 * (1 - valDown / 100)
                        }
                    },
                    changeData = {};

                tableData.forEach(d => {
                    if (d.pricebook_sellingprice) {
                        d.ceiling_price = valUp ? countVal('up', d.pricebook_sellingprice).toFixed(c_decimal_places) : d.ceiling_price;
                        d.floor_price = valDown ? countVal('down', d.pricebook_sellingprice).toFixed(f_decimal_places) : d.floor_price;
                        changeData[d.__tbIndex] = {
                            cells: ['ceiling_price', 'floor_price'],
                            data: {
                                'ceiling_price': d.ceiling_price,
                                'floor_price': d.floor_price
                            }
                        }
                    }
                });
                me.table.setCellsVal(changeData);
            },

            priceBorderClosed() {
                this.isShowPriceBorder = false;
            },
            // 触发批量编辑按钮
            batchBtnChange(item) {
                if (!item.className) return;
                if (item.className.includes('j-del')) {
                    this.delItems();
                    return;
                }
                if (item.className.includes('j-edit-discount')) {
                    this.blukSetDiscount();
                    return;
                }
                if (item.className.includes('j-edit-sellingprice')) {
                    this.blukSetSellingprice();
                    return;
                }
                if (item.className.includes('j-edit-priceborder')) {
                    this.blukSetPriceBorder();
                    return
                }
                let nameObj = {
                    // 批量编辑有效开始时间
                    'j-edit-startdate': 'start_date',
                    // 批量编辑有效结束时间
                    'j-edit-enddate': 'end_date'
                }
                let className = Object.keys(nameObj).find(className => item.className.includes(className));
                if (className) {
                    let name = nameObj[className];
                    this.blukSetDateTime(name);
                    return;
                }
            },
            tableClick(e) {
                let tar = $(e.target);
                if (tar.hasClass('j-action-add')) {
                    if (this.param && this.param.fromSavePd) {
                        // 缓存计算过的数据，避免重复计算
                        this.cacheExists = this.table.curData.data;
                        this.$emit('selectPb', this.getValue())
                    } else {
                        this.addHandle();
                    }
                }
            },
            addHandle() {
                this.isShowAdd = true;
            },

            addClosed() {
                this.isShowAdd = false;
            },

            // 编辑时，添加产品
            addCallBack(arr) {
                let me = this;
                // 如果未开启包含本价目表已选产品，才需要过滤重复添加的产品
                if (!CRM._cache.priceBookSelectProduct) {
                    arr = me.filterData(arr, me.table.getTableAllData());
                }
                arr = _.map(arr, function (item) {
                    me.filterIds.push(item.product_id);
                    item.pricebook_id = me.param.pricebook_id;
                    item.pricebook_id__r = me.param.pricebook_id__r;
                    return item
                });
                this.addNewData(arr)
            },

            // 把_quoteData中的数据取出来一部分；
            initData(data) {
                data.forEach(item => {
                    if (item._quoteData) {
                        item.unit = item._quoteData.unit;
                        item.is_multiple_unit = item._quoteData.hasOwnProperty('is_multiple_unit__v') ? item._quoteData.is_multiple_unit__v : item._quoteData.is_multiple_unit;
                        item.unit__v = item._quoteData.unit__v;
                        // item.multiUnitInfos = item._quoteData.multiUnitInfos || item._quoteData.multi_unit_data;
                    }
                })
            },

            // 给数据添加币种和汇率，和当前价目表的一致；
            addCurrencyAndRate(data, oData) {
                if (CRM._cache.currencyStatus) {
                    oData = oData || this.cdata.oData || {};
                    _.each(data || [], function (item) {
                        item.mc_currency = oData.mc_currency;
                        item.mc_exchange_rate = oData.mc_exchange_rate;
                    });
                }
                return data
            },

            // 过滤掉当前编辑中已有的产品；
            filterData(data, tableData) {
                return _.filter(data, item => {
                    let f = _.find(tableData, d => d.product_id === item.product_id);
                    return !f;
                })
            },

            async addNewData(data) {
                let me = this;
                data = await this.parseParam(data);
                data = _.map(data, function (item) { // 补充数据
                    if (item.category === '#%$') {
                        item.category__r = '#%$';
                    }
                    return _.extend({}, me._getDefaultData(), item, me._getQuoteData(item._quoteData || {}));
                });
                me._getServerDefaultData(data, me.param, async function (d) {
                    // todo：添加自定义插件入口
                    //其他逻辑
                    await me.$emit('beforeAddNewData', d);
                    data = me.table.getTableAllData().concat(d);
                    me.addCurrencyAndRate(data);
                    me.table.doStaticData(data);
                    me.setLockReadOnly(data, me.table);
                });
            },

            monitoring() { // 监听事件
                let events = {
                    'addNewData': function(data) {
                        this.addNewData(data)
                    },
                    'addPbNewData': function(data) {
                        this.oneByOneBatchCalculate(_.zip(data), this.table?.getTableAllData() || []);
                    }
                }
                _.each(events, (listener, evt) => {
                    this.$on(evt, listener.bind(this));
                });
            },
        },

        async mounted() {
            this.render();
            this.monitoring();

            console.log('/EditPricebookProduct.vue', this.param)
        },

        destroyed(){
            this.table && this.table.destroy();
            this.table = null;
        }
    }
</script>

<style>
    .crm-table-add-price-book-product {
        border: 1px solid #d2d2d2;
    }
</style>

