<!--
 * @Author: <PERSON>
 * @LastEditors: LiAng
 * @LastEditTime: 2025-06-24 17:52:57
-->
<template>
  <fx-dialog
    :visible.sync="visible"
    noHeaderBorderBottom
    custom-class="activity-record-dialog"
    :title="$t('sfa.activity.corpus.audio_setting_dialog_title')"
    @close="handleClose"
    append-to-body
  >
    <fx-alert
      v-if="showAlert"
      type="warning"
      show-icon
      :closable="false"
      :title="showAlertText"
    ></fx-alert>
    <div class="activity-record-obj-wrap">
      <div class="title" style="margin-bottom: 0;">
        <div class="label">{{ $t('sfa.activity.corpus.audio_setting_item_title') }}</div>
        <div class="title-right">
          <span>{{ $t('sfa.activity.corpus.audio_setting_show_all_fields') }}</span>
          <fx-switch v-model="showAllFields" @change="handleShowAllFieldsChange" size="mini"></fx-switch>
        </div>
      </div>
      <fx-form 
        v-if="showRecordTypeSelector"
        :model="form" 
        size="small" 
        ref="form"
        label-width="144px"
        label-position="left"
        class="basic-form-wrapper"
      >
        <fx-select
          :label="$t('业务类型')"
          v-model="form.recordType"
          option-value-key="api_name"
          :options="recordList"
          @change="handleRecordTypeChange"
        ></fx-select>
      </fx-form>
      <div class="activity-record-obj-inner" ref="objWrap"></div>
    </div>
    <div class="activity-record-custom-wrap">
      <div class="title">{{ $t('sfa.activity.corpus.audio_setting_item_title1' )}}</div>
      <fx-form 
        :model="form" 
        size="small" 
        ref="form"
        label-width="144px"
        label-position="left"
      >
        <fx-select 
          :label="$t('sfa.activity.corpus.audio_setting_item_label')"
          v-model="form.sourceLanguage"
          :options="sourceLanguageOptions"
          @change="handleSourceLanguageChange"
        ></fx-select>
        <fx-select 
          v-if="!realTimeTranslationExceed"
          :label="$t('sfa.activity.corpus.audio_setting_item_label2')"
          v-model="form.translation"
          :options="translationOptions"
          :disabled="!translationOptions.length"
          :placeholder="form.sourceLanguage === 'yue' ? $t('sfa.activity.corpus.audio_setting_item_label_placeholder') : ''"
        ></fx-select>
        <fx-select 
          v-if="isDevDomain"
          :label="$t('sfa.activity.corpus.audio_setting_provider')"
          v-model="form.provider"
          :options="getProviderOptions()"
        ></fx-select>
        <fx-radio-group
          v-model="form.speakerEnabled"
          :label="$t('sfa.activity.corpus.audio_setting_item_label1')"
        >
          <fx-radio :label="0">{{ $t('sfa.activity.corpus.audio_setting_radio_label') }}</fx-radio>
          <fx-radio label="">{{ $t('sfa.activity.corpus.audio_setting_radio_label1') }}</fx-radio>
        </fx-radio-group>
      </fx-form>
    </div>
    <div class="ctivity-record-custom-prove">
      <ul class="prove-list">
        <li>{{$t('sfa.activity.corpus.audio_setting_prove_list_item1')}}</li>
        <li>{{$t('sfa.activity.corpus.audio_setting_prove_list_item2')}}</li>
        <li class="have-link">{{$t('sfa.activity.corpus.audio_setting_prove_list_item3')}}
          <span class="link" @click="toHrefLink">《{{$t('隐私政策')}}》</span>
        </li>
        <li class="weight-text">{{$t('sfa.activity.corpus.audio_setting_prove_list_item4')}}</li>
      </ul>
    </div>
    <div class="activity-record-dialog-footer" slot="footer">
      <fx-button :disabled="noInteractiveComponent" size="medium" type="primary2" @click="handleConfirm">{{ $t('sfa.activity.corpus.audio_setting_btn_confirm') }}</fx-button>
    </div>
  </fx-dialog>
</template>

<script>
  export default {
    props: {
      activityResourceUsage: {
        type: Object,
        default: () => ({})
      },
      apiName: {
        type: String,
        default: ''
      }
    },
    data() {
      return {
        visible: true,
        form: {
          translation: false, // 翻译 true:翻译 false:不翻译
          speakerEnabled: 0, // 区分发言人 0:智能区分  !0:暂不区分
          sourceLanguage: 'cn', // 录音语言
          recordType: '', // 业务类型
          provider: 'TingWu' // 语音识别平台
        },
        sourceLanguageOptions: [
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_cn'), // 中文
            value: 'cn'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_en'), // 英文
            value: 'en'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_yue'), // 粤语
            value: 'yue'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_ja'), // 日语
            value: 'ja'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_ko'), // 韩语
            value: 'ko'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_de'),
            value: 'de'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_fr'),
            value: 'fr'
          },
          // {
          //   label: $t('sfa.activity.corpus.audio_setting_lang_ru'),
          //   value: 'ru'
          // }
        ],
        recordList: [],
        noInteractiveComponent: false,
        showAllFields: false
      }
    },
    computed: {
      translationOptions() {
        const defaultOptions = [
          {
            label: $t('sfa.activity.corpus.audio_setting_opt2'),
            value: false
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_en'),
            value: 'en'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_ja'),
            value: 'ja'
          }
        ]
        const otherOptions = [
          {
            label: $t('sfa.activity.corpus.audio_setting_opt2'),
            value: false
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_lang_cn'),
            value: 'cn'
          }
        ]
        const isLangYue = this.form.sourceLanguage === 'yue'
        return isLangYue
          ? []
          : this.form.sourceLanguage === 'cn'
            ? defaultOptions
            : otherOptions
      },
      // 是否显示资源使用提示
      showResourceUsageTips() {
        return this.activityResourceUsage?.interactionRecordingInsufficient
          || (this.activityResourceUsage?.realTimeTranslationInsufficient && !this.activityResourceUsage?.realTimeTranslationExceed)
      },
      showResourceUsageTipsText() {
        // 录音时长和翻译时长都不足
        if (this.activityResourceUsage?.interactionRecordingInsufficient && this.activityResourceUsage?.realTimeTranslationInsufficient) {
          return $t('sfa.activity.corpus.audio_setting_resource_usage_tips2')
        } else {
          return this.activityResourceUsage?.interactionRecordingInsufficient
            // 录音时长不足
            ? $t('sfa.activity.corpus.audio_setting_resource_usage_tips')
            // 翻译时长不足
            : $t('sfa.activity.corpus.audio_setting_resource_usage_tips1')
        }
      },
      realTimeTranslationExceed() {
        return !!this.activityResourceUsage?.realTimeTranslationExceed
      },
      showRecordTypeSelector () {
        return this.recordList.length > 1
      },
      showAlert() {
        return this.noInteractiveComponent || this.showResourceUsageTips
      },
      showAlertText() {
        return this.showAlert
          ? this.showResourceUsageTips
            ? this.showResourceUsageTipsText
            : this.$t('sfa.activity.corpus.no_interactive_component_include_tips')
          : ''
      },
      isDevDomain () {
        return Fx.domain && ['ceshi112.com', 'localhost'].includes(Fx.domain);
      }
    },
    methods: {
      handleConfirm(e) {
        this.$emit('confirm', e);
      },
      handleClose () {
        this.visible = false
        this.resetFileds()
        // 刷新详情页
        // this.$context.$emit('root.refresh');
        this.$destroy()
        // this.$emit('close');
      },
      getAudioConfig () {
        return Object.assign({}, this.form, {
          translationEnabled: !this.form.translation ? false : true,
          targetLanguages: this.form.translation ? this.form.translation : '',
          provider: this.form.provider
        })
      },
      // 跳转链接
      toHrefLink () {
        const lang = FS.util.getCurLocale();
        let url = 'https://www.fxiaoke.com/secure/index.html';
        switch (lang) {
          case 'zh-CN': // 简体中文
            url = 'https://www.fxiaoke.com/secure/index.html';
            break;
          case 'zh-TW': // 繁体中文
            url = 'https://www.fxiaoke.com/secure/tw/index.html';
            break;
          default: // 其他语种及英文
            url = 'https://www.fxiaoke.com/secure/en/index.html';
            break;
        }
        CRM.util.openUrl(url, '_blank');
      },
      handleSourceLanguageChange (value) {
        if (value === 'yue') {
          this.form.translation = ''
          return
        }
        this.form.translation = false
      },
      setIBSLanguage() {
        // 艾比森单独增加西班牙语
        if (CRM.ea === '796580_sandbox') {
          this.sourceLanguageOptions.push({
            label: $t('sfa.activity.corpus.audio_setting_lang_es'),
            value: 'es'
          })
        }
      },
      fetchRecordType () {
        const _this = this;
        if (!_this.apiName) {
          return
        }

        // 优先拿缓存的业务类型
        const recordType = CRM.getLocal(`ACTIVITY_RECORD_TYPE_${CRM.ea}_${CRM.curEmpId}`)

        return new Promise(resolve => {
            CRM.util.FHHApi({
              url: "/EM1HNCRM/API/v1/object/record_type/service/findValidRecordTypeList",
              data: {
                  describeApiName: _this.apiName,
                  is_only_active: true
              },
              success: (res) => {
                _this.recordList = res.Value?.record_list || []
                _this.form.recordType = recordType ?? _this.recordList[0]?.api_name
                _this.$emit('changeRecordType', _this.form.recordType)
                resolve();
              }
            })
        });
      },
      checkInteractiveComponentInclude () {
        let _this = this;
        CRM.util.FHHApi({
          url: "/EM1HNCRM/API/v1/object/ActiveRecordObj/controller/DescribeLayout",
          data: {
            include_describe: false,
            include_layout: true,
            apiname: "ActiveRecordObj",
            layout_type: "detail",
            recordType_apiName: this.form.recordType
          },
          success: ({ Result, Value }) => {
            if (Result.StatusCode === 0) {
              const layout = Value.layout;
              const components = layout.components;
              const interactiveComponentInclude = components.find(item => item.api_name === 'sfa_activity_audio_record');
              _this.noInteractiveComponent = !interactiveComponentInclude
            }
          }
        })
      },
      handleShowAllFieldsChange (value) {
        this.$emit('changeShowAllFields', value)
      },
      handleRecordTypeChange (value) {
        this.$emit('changeRecordType', value)
        this.checkInteractiveComponentInclude()
      },
      resetFileds () {
        this.form = {
          translation: false, // 翻译 true:翻译 false:不翻译
          speakerEnabled: 0, // 区分发言人 0:智能区分  !0:暂不区分
          sourceLanguage: 'cn', // 录音语言
          recordType: '' // 业务类型
        }
        this.recordList = []
        this.noInteractiveComponent = false
      },
      getProviderOptions() {
        return [
          {
            label: $t('sfa.activity.corpus.audio_setting_provider_tingwu'),
            value: 'TingWu'
          },
          {
            label: $t('sfa.activity.corpus.audio_setting_provider_tencent'),
            value: 'Tencent'
          }
        ]
      }
    },
    async mounted () {
      const activityAudioConfig = CRM.getLocal('ACTIVITY_AUDIO_CONFIG')
      const recordType = CRM.getLocal(`ACTIVITY_RECORD_TYPE_${CRM.ea}_${CRM.curEmpId}`)
      await this.fetchRecordType(recordType)
      this.setIBSLanguage()
      this.checkInteractiveComponentInclude()
      if (activityAudioConfig) {
        if (activityAudioConfig.translation == undefined || this.realTimeTranslationExceed) {
          activityAudioConfig.translation = false
        }
        this.form = activityAudioConfig
      }
    }
  }
</script>

<style lang="less" scoped>

</style>

<style lang="less">
  .activity-record-dialog {
    width: 840px;
    background-image: url("~@assets/images/activity/activity-audio-dialog-bg.png"),
                     linear-gradient(21.78deg, transparent 80%, #5A6FE9 240%),
                     linear-gradient(230.93deg, rgba(255, 255, 255, 0.08) 68.22%, rgba(151, 106, 235, 0.08) 127.09%),
                     linear-gradient(231deg, rgba(255, 255, 255, 0.08) 68.22%, rgba(151, 106, 235, 0.08)),
                     linear-gradient(22deg, var(--color-neutrals01) 69.53%, transparent 240%);
    background-size: 403px 134px, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
    background-repeat: no-repeat;
    background-position: right 48px top 48px, 0 0, 0 0, 0 0, 0 0;
    border-radius: 8px;
    .el-dialog__header {
      padding: 48px 48px 20px;
      border-bottom: none;
      .el-dialog__headerbtn {
        top: 24px;
        right: 18px;
      }
    }
    .el-dialog__body {
      padding: 0 48px;
      max-height: 500px;
      overflow-y: auto;
    }
    .el-dialog__footer {
      padding-top: 34px;
      padding-bottom: 48px;
      border: none;
    }
    .fx-alert {
      margin-bottom: 16px;
      position: sticky;
      top: 0;
      z-index: 1;
    }
    .el-dialog__title {
      font-size: 32px;
      line-height: 46px;
      height: 46px;
    }
    .title {
      padding-left: 12px;
      // border-left: 4px solid var(--color-primary06);
      font-size: 14px;

      line-height: 20px;
      height: 20px;
      font-weight: 700;
      margin-bottom: 10px;
      color: var(--color-neutrals19);
      position: relative;
      display: flex;
      align-items: center;
      .title-right {
        margin-left: 10px;
        display: flex;
        align-items: center;
        font-size: 12px;
        color: var(--color-neutrals11);
        font-weight: 400;
        .fx-switch {
          margin-left: 5px;
        }
      }
      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 14px;
        background-color: var(--color-primary06);
      }
    }
    .fx-select {
      width: 480px;
      display: block;
    }
    .el-form-item--small.el-form-item {
      margin-bottom: 10px;
    }
    // .el-form-item__label {
    //   margin-right: 8px;
    // }
    .activity-record-custom-wrap {
      margin: 16px 0;
      /deep/ .el-form-item__label {
        font-size: 14px;
      }
    }
    .ctivity-record-custom-prove{
      display: flex;
      padding: 12px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      align-self: stretch;
      border-radius: 8px;
      background: var(--color-special01);
      color: var(--color-neutrals15);
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
      .have-link{
        .link{
          cursor: pointer;
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          color: var(--color-info06);
        }

      }
      .weight-text{
        color: var(--color-neutrals19);
        font-size: 12px;
        font-style: normal;
        font-weight: 700;
        line-height: 20px;
      }
    }
    .basic-form-wrapper {
      margin-top: 10px;
      .el-form-item--small.el-form-item {
        margin-bottom: 0;
      }
      .el-form-item__label {
        &:before {
          content: '*';
          font-size: 14px;
          margin: 0 4px 0 0;
          transform: translateY(3px);
          visibility: hidden;
        }
      }
    }
    .activity-record-obj-inner {
      // max-height: 400px;
      // overflow-y: auto;
      min-height: 74px;
      .crm-action-nfield {
        padding: 0;
        overflow: inherit;
        &.crm-scroll {
          overflow: inherit;
        }
        .view-box {
          min-width: auto;
          width: 622px;
          margin-left: 0;
        }
      }
      .crm-action-nfield .f-g-tit {
        display: none !important;
      }
      .crm-action-nfield .f-g-item {
        margin-top: 10px;
      }
      .crm-action-nfield .f-g-item-tit {
        width: 144px;
        margin: 0;
        padding: 0 8px 0 10px;
        box-sizing: border-box;
      }
    }
    .activity-record-dialog-footer {
      text-align: right;
      .fx-button {
        // margin-top: 34px;
        background-color: #5B70EA;
        border-color: #5B70EA;
        color: #fff;
        &:hover {
          background-color: #5B70EA !important;
          border-color: #5B70EA !important;
        }
      }
    }
  }
</style>
