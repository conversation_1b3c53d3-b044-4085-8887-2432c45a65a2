<template>
    <div class="quoter-designer-container">
        <!-- 预览 -->
        <div class="preview-container" v-if="!loading">
            <fx-button size="small" @click="preview">{{$t('预览')}}</fx-button>
            <fx-dialog 
                draggable
                :visible="previewVisible" 
                :title="$t('预览')"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :width="800"
                custom-class="add-new-attr-dialog"
                @close="previewVisible = false"
            >
                <price-quoter-render 
                    :showSubmitBtn="false"
                    :sortByGroup="false"
                    :style="{
                    }"
                    ref="previewRender"
                ></price-quoter-render>
            </fx-dialog>
        </div>
        <div :class="['quoter-designer']">
            <template v-if="!loading">
                <ul class="headerList">
                    <li v-for="(item, i) in headerList" :key="i" :class="['cell', i % 2 === 0 ? 'attr' : 'val']">{{ item.label }}</li>
                </ul>
                <div class="quoter-designer-panel">
                    <fx-form :disabled="disabled"
                        class="row" :key="rowIdx" 
                        v-for="(modules, rowIdx) in formData" 
                        :model="formData[rowIdx]"
                        :ref="'rootForm'">
                        <cas-select-group 
                            :formDisabled="disabled"
                            :key="rowIdx" 
                            v-model="formData[rowIdx]" 
                            :attrsMap="attrsMap"
                            :siblingLength="formData.length" 
                            :rowIdx="rowIdx" 
                            :rootIdx="rowIdx" 
                            :isRoot="true"
                            :maxLevel="modules.maxLevel" 
                            :tableMaxLevel="tableMaxLevel" 
                            @addNewRow1="addNewRow(rowIdx, formData)"
                            @delRow1="delRow(rowIdx, formData, rowIdx)" 
                            :parentdata="null"
                            :nodePath="[{rowIdx, nodeLevel: modules.nodeLevel}]"
                            ></cas-select-group>
                    </fx-form>
                </div>
                <fx-button size="small" class="add-attr-btn" type="text" @click="addNewRow(formData.length - 1, formData)"><i class="el-icon-circle-plus"></i>{{$t('添加属性')}}</fx-button>
                <!-- <fx-button class="submit-btn" @click="subForm">提交</fx-button> -->
                <fx-dialog :visible="visible" width="480px"
                    :title="$t('属性值必填配置')"
                    @close="close" 
                    :close-on-press-escape="false"
                    :close-on-click-modal="false"
                    @selection-change="selectionChange">
                    <p style="margin-bottom:10px">{{ $t('在报价器选择该属性的属性值时，以下选中的属性值为必选') }}</p>
                    <div style="position: relative;">
                        <fx-table max-height="530px" ref="table" :data="attrTable" highlight-current-row border
                            >
                            <fx-table-column :label="$t('属性值')" prop="name" min-width="300"></fx-table-column>
                            <fx-table-column :label="$t('是否必填')" prop="_id" min-width="120">
                                <template slot-scope="{row}">
                                    <fx-checkbox :hasFormItem="false" :name="dialogVals.attrId" :label="row._id" 
                                    v-model="dialogVals.selected"
                                    @change="(val) => dialogSelectedChange(val, row._id)"
                                    >{{ '' }}</fx-checkbox>
                                </template>
                            </fx-table-column>
                            <!-- <fx-table-column type="selection" width="55"></fx-table-column> -->
                        </fx-table>
                        <p class="el-form-item__error" v-if="showError">{{ showError }}</p>
                    </div>
                    <template #footer>
                        <fx-button type="primary" @click="submitDialog">{{$t('保存')}}</fx-button>
                        <fx-button @click="cancelDialog">{{ $t('取消') }}</fx-button>
                    </template>
                </fx-dialog>
                <fx-dialog ref="addNewAttrDialog" append-to-body :visible="addNewAttrVisible" :title="$t('添加属性')"
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    width="1020px"
                    custom-class="add-new-attr-dialog"
                    @close="cancelTabDialog"
                >
                    <fx-tabs2 v-if="!isSingleSelect" v-model="activeTab"
                   @tab-click="handleTabClick"
                    :tabs="[{
                        name: 'standard',
                        label: $t('标准属性')
                    }, {
                        name: 'nonStandard',
                        label: $t('非标属性')
                    }]" >
                    </fx-tabs2>
                    <div v-show="activeTab === 'standard'" ref="tableData1" :style="{height: '500px'}"></div>
                    <div v-show="activeTab === 'nonStandard'" ref="tableData2" :style="{height: '500px'}"></div>
                    <template #footer>
                        <fx-button type="primary" @click="submitTabDialog">{{$t('确定')}}</fx-button>
                        <fx-button @click="cancelTabDialog">{{ $t('取消') }}</fx-button>
                    </template>
                </fx-dialog>
            </template>
            
        </div>
    </div>
</template>
<script>
import { requirePickselfObject, requireObjecttable } from "@common/require.js";
import CasSelectGroup from "./casSelectGroup.vue";
import AttrItem from './item'
import PriceQuoterRender from '../priceQuoterRender/index.vue';
export default {
    components: {
        CasSelectGroup,
        AttrItem,
        PriceQuoterRender
    },
    props: {
        disabled: {
            type: Boolean,
            default: false
        },
        dataId: {},
        apiName: {},
        defaultData: {
            type: Array,
            default() {
                return []
            }
        }
    },
    provide() {
        return {
            _showDialog: this.showDialog,
            _addNewRow: this.addNewRow,
            _addNewCol: this.addNewCol,
            _delRow: this.delRow,
            _getUuId: this.getUuId,
            _createNode: this.createNode,
            _updateMaxLevel: this.updateMaxLevel,
            _triggerHighlight1: this.triggerHighlight1,
            _setValueRequired1: this.setValueRequired,
            _validRepeatNode: this.validRepeatNode
        }
    },
    data() {
        return {
            loading: true,
            formData: [{
                maxLevel: 1, // 2
                nodeType: 1,
                nodeLevel: 1,
                values: [],
                highlight: false,
                isMultiSelected: false,
                isStandardAttribute: true,
                isRequired: false,
                requiredSelected: [],
                // children: [{
                //     isStandardAttribute: true,
                //     nodeLevel: 2,
                //     nodeType: 2,
                //     values: [],
                //     isRequired: false,
                // }]
            }],
            // showType: 1,
            visible: false,
            dialogVals: {
                selected: [],
                attrId: '',
                isMultiSelected: false
            },
            showError: false,
            defaultProps: {
                children: 'children',
                label: 'values'
            },
            attrsMap: {},
            attrTable: [],
            addNewAttrVisible: false,
            // standardTable: null,
            // nonStandardTable: null,
            activeTab: 'standard',
            isSingleSelect: false,
            previewVisible: false
        };
    },
    computed: {
        headerList() {
            return new Array(this.tableMaxLevel).fill({}).map((item, i) => {
                return {
                    label: i % 2 === 0 ? (i === 0 ? $t('选择约束属性') : $t('被约束属性')) : $t('属性值')
                }
            })
        },
        tableMaxLevel() {
            let arr = this.formData.map(item => item.maxLevel);
            let count = Math.max(...arr);
            return count + 1;
        }
    },
    watch: {
        defaultData: {
            immediate: true,
            handler(val) {
                this.initData(val);
            }
        }
    },
    mounted() {

    },
    methods: {
        handleTabClick(tab) {
            this.renderTable();
        }, 
        setShowError() {
            let msg = '';
            let {selected, isMultiSelected} = this.dialogVals;
            if (!isMultiSelected && selected && selected.length > 1) {
                msg = $t('属性单选，只能设置一个必选项');
                return msg;
            }
            return msg;
        },
        setNodeProp(attrId, propName = 'requiredSelected', val) {
            this.attrsMap[attrId][propName]= val;
            this.formData.forEach(item => {
                CRM.util.getLeafNode(item, (data) => {
                    return data.nodeType === 1 
                    && data.values[0] === attrId;
                }, data => {
                    this.$set(data, propName, val);
                })
            })
        },
        dialogSelectedChange(val, id) {
            this.showError = this.setShowError();
        },
        selectionChange(data, row) {
            // debugger
        },
        close() {
            this.visible = false;
        },
        cancelDialog() {
            this.close();
        },
        submitDialog() {
            if (this.showError) {
                return;
            }
            this.close();
            this.setNodeProp(this.dialogVals.attrId, 'requiredSelected', this.dialogVals.selected)
        },
        setValueRequired(node, propName) {
            let attrId = node.values[0];
            if (propName === 'requiredSelected') {
                if (!attrId) {
                    this.$message({
                        type: 'warning',
                        message: $t('请选择属性')
                    });
                    return;
                }
                this.attrTable = this.attrsMap[attrId].children;
                this.dialogVals = {
                    selected: node.requiredSelected || [],
                    attrId,
                    isMultiSelected: node.isMultiSelected
                }
                this.showError = this.setShowError();
                this.visible = true;
                return;
            }
            if (attrId && (propName === 'isRequired' || propName == 'isMultiSelected')) {
                this.setNodeProp(attrId, propName, node[propName]);
            }
        },
        async initData(val) {
            // module, subModule, 
            CRM.util.sendLog('AttributeConstraintObj', 'priceQuoterDesigner', {
                operationId: 'init', //操作类型
                eventType: 'pv'
            })
            if (!val) {
                this.loading = false;
                return;
            }
            try {
                this.attrsMap = await CRM.util.getAttributeConstraintAttrsMap(val);
                this.loading = false;
                this.$nextTick(() => {
                    this.formData = val.map(item => {
                        CRM.util.getLeafNode(item, data => {
                            return true;
                        }, data => {
                            if (data.nodeType === 1) {
                                let {values, isStandardAttribute, isRequired, requiredSelected, isMultiSelected} = data;
                                ['isRequired', 'requiredSelected', 'isMultiSelected'].forEach(propName => {
                                    this.setNodeProp(values[0], propName, data[propName]);
                                })
                            }
                            data.highlight = false;
                        })
                        return item;
                    });
                })
            } catch(e) {
                this.loading = false;
            }
        },
        
        updateMaxLevel(rootIdx, nodeLevel) {
            this.formData[rootIdx].maxLevel = nodeLevel;
        },
        /**
         * 更新属性缓存数据
         * @param {string} key 属性id
         * @param {object} val 要修改的数据
         */
        updateAttrsMap(key, val) {
            this.attrsMap[key] = {
                ...(this.attrsMap[key] || {}),
                ...val
            };
        },
        getUuId() {
            const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let id = '';
            for (let i = 0; i < 16; i++) {
                const index = Math.floor(Math.random() * chars.length);
                id += chars[index];
            }
            return id;
        },
        /**
         * 选择指定类型的属性
         * @param {boolean} isStandardAttribute 要选择的属性类型 true 标准属性 false 非标属性
         * @param {object} node 当前节点
         * @returns 
         */
        async showDialog(isStandardAttribute, node) {
            let me = this;
            this.isSingleSelect = true; // 单选
            this.activeTab = isStandardAttribute ? 'standard' : 'nonStandard';
            let res = await this.showTabDialog();
            if (!res) {
                return;
            }
            let data = isStandardAttribute ? res.standardAttribute[0] : res.nonStandardAttribute[0];
            await me.addNodeBefore(isStandardAttribute, node, [data]);
            let {_id, name, name__r} = data;
            return {
                isStandardAttribute,
                _id,
                name,
                name__r
            }
            
        },
        /**
         * 
         * @param {number} nodeType // 节点类型 1属性 2属性值
         * @param {number} nodeLevel // 节点层级
         * @param {number} isStandardAttribute // 属性值类型 1 标准属性 2 非标属性
         * @param {number} lastLevel // 增加到叶子节点的层级
         */
        createNode({ nodeType, nodeLevel, isStandardAttribute }, lastLevel) {
            let node = {};
            let _curLevel = nodeLevel;

            node = {
                isStandardAttribute,
                nodeType,
                highlight: false,
                isMultiSelected: false,
                requiredSelected: [], // 必选项id
                // isMultiSelected: nodeType === 1 && isStandardAttribute ? false : null,
                nodeLevel: _curLevel,
                values: [],
                isRequired: false,
                children: null
            }
            if (lastLevel && _curLevel < lastLevel) {
                let _nodeType = node.nodeType === 1 ? 2 : 1
                node.children = [
                    this.createNode({
                        isMultiSelected: false,
                        // isMultiSelected: _nodeType === 1 && node.isStandardAttribute ? false : null,
                        isStandardAttribute: node.isStandardAttribute, // 如果父节点为属性，下一级属性值类型依赖属性类型(标准/非标)
                        nodeType: _nodeType, // 节点类型
                        nodeLevel: node.nodeLevel + 1
                    }, lastLevel)
                ];
            }
            return node;
        },
        async addNodeBefore(isStandardAttribute, {nodeType}, list) {
            let me = this;
            let addAttrOptions = [];
            list.forEach(attr => {
                let { _id, name, name__r } = attr;
                // 选择该属性为标准属性且非首次添加时
                if (isStandardAttribute && me.attrsMap[_id]) {
                    let count = 0;
                    me.formData.forEach(item => {
                        CRM.util.getLeafNode(item, (data) => {
                            return data.nodeType === 1 
                            && data.values[0] === _id;
                        }, data => {
                            count++;
                        })
                    })
                    // 若当前页面没有该属性，重置单选，非必选，清空必选项
                    if (!count) {
                        me.updateAttrsMap(_id, { 
                            isRequired: false,
                            isMultiSelected: false, 
                            requiredSelected: []
                        });
                    }
                }
                me.updateAttrsMap(_id, { _id, name, name__r })
                // 第一次选择该属性，则获取并缓存属性的选项值
                if (isStandardAttribute && nodeType === 1 && _id && !me.attrsMap[_id].children) {
                    addAttrOptions.push(_id);
                }
            })
            if (addAttrOptions.length) {
                let childrens = await CRM.util.fetchAttributeObjectDataList('AttributeValueObj', addAttrOptions);
                addAttrOptions.forEach(_id => {
                    let children = childrens.filter(item => item.attribute_id === _id);
                    me.updateAttrsMap(_id, { children });
                });
            }
        },
        async handleAddNode({nodeType, nodeLevel, isStandardAttribute}) {
            let newItems = [];
            let _rowData = this.createNode({
                isStandardAttribute,
                nodeLevel: nodeLevel,
                nodeType: nodeType,
            })
            
            if (nodeType === 1) {
                this.isSingleSelect = false; // 多选
                this.activeTab = 'standard';
                let res = await this.showTabDialog();
                if (!res) {
                    return;
                }
                let {standardAttribute, nonStandardAttribute} = res;
                if (standardAttribute.length) {
                    // 标准属性
                    await this.addNodeBefore(true, {nodeType}, standardAttribute);
                    standardAttribute.forEach(item => {
                        let attr = this.attrsMap[item._id];
                        newItems.push({
                            ..._rowData,
                            maxLevel: nodeLevel === 1 ? 1 : null,
                            isStandardAttribute: true,
                            values: [item._id],
                            isRequired: !!attr.isRequired,
                            isMultiSelected: !!attr.isMultiSelected,
                            requiredSelected: attr?.requiredSelected || []
                        })
                    })
                }
                if (nonStandardAttribute.length) {
                    // 非标属性
                    await this.addNodeBefore(false, {nodeType}, nonStandardAttribute);
                    nonStandardAttribute.forEach(item => {
                        let attr = this.attrsMap[item._id];
                        newItems.push({
                            ..._rowData,
                            maxLevel: nodeLevel === 1 ? 1 : null,
                            isStandardAttribute: false,
                            values: [item._id],
                            isRequired: !!attr.isRequired,
                            isMultiSelected: !!attr.isMultiSelected,
                            requiredSelected: attr?.requiredSelected || []
                        })
                    })
                }
            
            } else {
                newItems.push({
                    ..._rowData,
                    maxLevel: nodeLevel === 1 ? 1 : null
                })
            }
            return newItems;
        },
        async showTabDialog() {
            this.renderTable();
            this.addNewAttrVisible = true;
            return new Promise((resolve, reject) => {
                this.$on('submit-tab-dialog', (data, data2) => {
                    resolve({standardAttribute: data, nonStandardAttribute: data2});
                })
                this.$on('cancel-tab-dialog', () => {
                    reject(null);
                })
            })
        },
        async renderTable() {
            let me = this;
            if (this.standardTable && this.nonStandardTable) {
                return;
            }
            let ObjectTable = await requireObjecttable();
            const baseOptions = {
                showMoreBtn: false,
                showTerm: false,
                search: {
                    placeHolder:   '',
                    type: 'Keyword',
                    showFilterField: true,
                },
                cacheSearchField: true,
                searchTerm: '',
                checked: {
                    idKey: '_id',
                    data: []
                },
                parseParam_after(param) {
                    let _info = JSON.parse(param.search_query_info) || []
                    let seletedIds = me.formData.map(item => item.values[0] || '')
                    seletedIds = seletedIds.filter(item => !!item);
                    seletedIds.length && _info.filters.push({
                        field_name: '_id',
                        field_values: seletedIds,
                        operator: 'NIN'
                    })
                    if (this.apiname === 'NonstandardAttributeObj') {
                        // 只可选择数值类型的非标属性
                        _info.filters.push({
                            field_name: 'type',
                            field_values: [1],
                            operator: 'EQ'
                        })
                    }
                    param.search_query_info = JSON.stringify(_info)
                    return param;
                }
            }
            let tabOptions = this.activeTab === 'standard' ? {
                el: this.$refs.tableData1,
                apiname: 'AttributeObj',
            } : {
                el: this.$refs.tableData2,
                apiname: 'NonstandardAttributeObj',
            };
            let options = {
                ...tabOptions,
                ...baseOptions
            }
            // 禁止选择的属性
            // let seletedIds = me.formData.map(item => item.values[0] || '')
            // seletedIds = seletedIds.filter(item => !!item);
            // let disabledcfg = {
            //     idKey: '_id',
            //     data: seletedIds.map(item => ({_id: item}))
            // }

            let table = new ObjectTable(options);
            
            if (!this.standardTable && this.activeTab === 'standard') {
                this.standardTable = table;
                this.standardTable.getOptions = function() {
                    let res = this.super.getOptions.apply(this, arguments);
                    return {
                        ...res,
                        single: me.isSingleSelect,
                        zIndex: 10000,
                        // disabledcfg,
                        // maxHeight: '300px'
                    }
                }
                this.standardTable.render();
            }
            if (!this.nonStandardTable && this.activeTab === 'nonStandard') {
                this.nonStandardTable = table;
                this.nonStandardTable.getOptions = function() {
                    let res = this.super.getOptions.apply(this, arguments);
                    return {
                        ...res,
                        single: me.isSingleSelect,
                        zIndex: 10000,
                        // disabledcfg,
                        // maxHeight: '300px'
                    }
                }
                this.nonStandardTable.render();
            }
        },
        submitTabDialog: _.throttle(function() {
            let standardData = this.standardTable?.getRemberData() || [];
            let nonStandardData = this.nonStandardTable?.getRemberData() || [];
            if (!standardData.length && !nonStandardData.length) {
                this.$message.warning($t('请至少选择一条数据'));
                return;
            }
            // console.log('standardData', standardData)
            // console.log('nonStandardData', nonStandardData)
            // 若存在于上一级节点重复的属性提示
            this.$emit('submit-tab-dialog', standardData, nonStandardData);
            this.cancelTabDialog();
        }, 2000),
        cancelTabDialog() {
            this.$emit('cancel-tab-dialog');
            this.addNewAttrVisible = false;
            if (this.standardTable) {
                this.standardTable.destroy();
                this.standardTable = null;
            }
            if (this.nonStandardTable) {
                this.nonStandardTable.destroy();
                this.nonStandardTable = null;
            }

        },
        /**
         * 追加列
         * @param {*} parentData 父节点数据
         * @param {*} rootIdx 根节点索引
         */
         async addNewCol(parentData, rootIdx) {
            let { nodeLevel, nodeType, isStandardAttribute, values } = parentData;
            let newItems = await this.handleAddNode({
                nodeLevel: nodeLevel + 1,
                nodeType: nodeType === 1 ? 2 : 1,
                isStandardAttribute
            })
            this.$set(parentData, 'children', newItems);
            if (this.formData[rootIdx].maxLevel < nodeLevel + 1) {
                this.updateMaxLevel(rootIdx, nodeLevel + 1);
            }
        },
        /**
         * 追加行
         * @param {*} rowIdx 
         * @param {*} siblingData 兄弟节点
         */
        async addNewRow(rowIdx, siblingData) {
            console.log('追加属性', rowIdx, siblingData)
            let { nodeType, nodeLevel, isStandardAttribute } = siblingData[rowIdx];
            let newItems = await this.handleAddNode({nodeType, nodeLevel, isStandardAttribute})
            
            siblingData.splice(rowIdx + 1, 0, ...newItems);
            
        },
        delRow(rowIdx, siblingData, rootIdx) {
            this.$confirm($t('确定删除此节点吗?'), $t('提示'), {
                confirmButtonText: $t('确定'),
                cancelButtonText: $t('取消'),
                type: 'warning'
            }).then(() => {
                let { nodeLevel } = siblingData[rowIdx];
                siblingData.splice(rowIdx, 1);
                // 删除以后，有兄弟节点，则保持其他兄弟节点的叶子节点最大level,
                // 没有兄弟节点，删除自己后，level
                let _level = nodeLevel;
                CRM.util.getLeafNode(this.formData[rootIdx], (node) => {
                    return !node.children || !node.children.length;
                }, (data) => {
                    _level < data.nodeLevel && (_level = data.nodeLevel);
                })
                this.updateMaxLevel(rootIdx, _level);
                this.$message({
                    type: 'success',
                    message: $t('删除成功!')
                });
            }).catch(() => {
            });
        },
        validRepeatNode(nodePath = [], value) { // nodePath = [{nodeLevel: 1, rowIdx: 1}, {nodeLevel: 2, rowIdx: 0}, {nodeLevel: 3, rowIdx: 0}]
            // 根据nodePath，获取所有选中的属性id
            let rootIdx = nodePath[0].rowIdx;
            let curRootNode = this.formData[rootIdx];
            let curIdx = nodePath[nodePath.length - 1].rowIdx;
            let curLevel = nodePath[nodePath.length - 1].nodeLevel;
            let rootIds = this.formData.map(item => item.values[0]);
            rootIds = rootIds.filter(item => !!item);
            if (curLevel !== 1 && rootIds.includes(value[0])) {
                return $t('crm.price_quoter_designer_repeat_error1', null, '不能与第一列属性重复');
            }
            let p_selectedIds = this.getParentNode(nodePath);
            if (p_selectedIds.includes(value[0])) {
                return $t('crm.price_quoter_designer_repeat_error2', null, '不能与父级属性重复');
            }
            // console.log('%cc_selectedIds', 'color: red', c_selectedIds)
            return false;
        },
        getParentNode(nodePath) {
            let len = nodePath.length;
            let p_selectedIds = []
            let count = 0;
            let idx = nodePath[count].rowIdx;
            let node = this.formData[idx];
            if (nodePath.length > 1) {
                this.traverseNodeType1(node, (children) => {
                    count++;
                    if (count == len - 1) return;
                    idx = nodePath[count].rowIdx;
                    return children[idx];
                }, (node) => {
                    // console.log('count='+count, 'idx='+idx)
                    // console.log(node.nodeLevel, node.values)
                    node.values[0] && p_selectedIds.push(node.values[0])
                })
            }
            
            return p_selectedIds;
        },
        traverseNodeType1(node, cb, operateCb) {
            if (!node) return;
            if (node.nodeType === 1) {
                operateCb && operateCb(node)
            }
            if (node.children && node.children.length) {
                let _node = cb(node.children);
                node && this.traverseNodeType1(_node, cb, operateCb)
            }
        },
        async subForm() {
            let valid = await this.validateForm();
            let data = this.getParams();
            return { valid, data };
        },
        validateForm() {
            let me = this;
            return new Promise((resolve, reject) => {
                let flag = true;
                me.$refs['rootForm'].forEach((item, i) => {
                    me.$refs['rootForm'][i].validate((valid) => {
                        console.log('valid', valid)
                        if (!valid) {
                            flag = false;
                        }
                    });
                })
                resolve(flag);
            }) 
        },
        getParams() {
            let _data = CRM.util.deepClone(this.formData);
            _data.forEach(item => {
                CRM.util.getLeafNode(item,(data) => {
                    return true;
                }, (data) => {
                    if (data.nodeType === 1 && data.values.length) {
                        data.field_num = this.attrsMap[data.values[0]].field_num
                    }
                    delete data.highlight;
                })
            })
            return _data;
        },
        triggerHighlight1(val, nodePath, nodeLevel, rowIdx) {
            let childNodes = this.formData;
            nodePath.forEach((item) => {
                let curNode = childNodes[item.rowIdx];
                curNode.highlight = val;
                childNodes = curNode.children;
            })
            if (childNodes && childNodes.length) {
                childNodes[0].highlight = val;
                let _level = childNodes[0].nodeLevel;
                CRM.util.getLeafNode(childNodes[0], (data) => {
                    return data.children && data.children.length;
                }, (data) => {
                    let level = data.children[0].nodeLevel;
                    if (level > _level) {
                        data.children[0].highlight = val;
                        _level = level;
                    }
                })
            }
        },
        preview() {
            this.validateForm().then(valid => {
                if (!valid) {
                    return;
                }
                let _formData = CRM.util.deepClone(this.formData);
                _formData.forEach((item, i) => {
                    CRM.util.getLeafNode(item, (data) => {
                        return true;
                    }, (data) => {
                        // 添加id，用于预览
                        data.id = data.id || this.getUuId();
                    })
                })
                this.previewVisible = true;
                this.$nextTick(() => {
                    this.$refs.previewRender?.init(_formData);
                })
            })
        }
    }
}
</script>
<style lang="less" scoped>
@border: 1px solid #DEE1E8;
.quoter-designer-container {
    position: relative;
    overflow: hidden;
}
.preview-container {
    text-align: right;
    padding: 0 8px;
    margin-bottom: 8px;
}
.quoter-designer {
    position: relative;
    min-width: 300px;
    overflow-y: hidden;
    &.float {
        // float: left;
    }

    .headerList {
        display: flex;

        .cell {
            flex-shrink: 0;
            background: #F2F3F5;
            border: 1px solid #E6E7EA;
            border-right: none;
            padding: 0px 8px;
            color: #545861;
            font-size: 12px;
            height: 40px;
            line-height: 40px;
            box-sizing: border-box;

            &.attr {
                width: 190px;
            }

            &.val {
                width: 194px;
            }

            &:last-child {
                border-right: @border;
            }
        }
    }

    .quoter-designer-panel {
        .fx-form.row {
            display: flex;

            /deep/ .node-level {
                flex: 1;
                display: flex;

                .form-item {
                    display: flex;
                    height: 100%;
                    box-sizing: border-box;
                    padding: 6px 8px;
                    border-bottom: 2px solid #DEE1E8;
                    border-left: @border;

                    &.bg0,
                    &.bg2 {
                        background: #FAFAFA;
                    }

                    &.bg1,
                    &.bg3 {
                        background: #FFF;
                    }
                    &.highlight {
                        background: #E6F4FF;
                    }
                    &.input-comp {
                        width: 190px;

                        .fx-input {
                            max-width: 135px;
                        }
                    }

                    &.select-comp {
                        width: 194px;

                        .fx-select {
                            max-width: 138px;
                        }

                        .range-input {
                            max-width: 165px;
                        }
                    }

                }

                &.node-leaf {
                    .form-item {
                        border-right: @border;
                    }
                }
            }

            /deep/ .row-child {
                display: flex;
                flex-direction: column;
            }
        }
    }
    .add-attr-btn {
        font-weight: 500;
        line-height: 18px;
        font-family: "Source Han Sans CN";
        i {
            font-size: 16px;
            margin-right: 4px;
            line-height: 18px;
            vertical-align: bottom;
        }
    }
}
/deep/ .el-dialog.add-new-attr-dialog {
    .el-dialog__body {
        .fx-tabs2 {
            .el-tabs__header {
                margin-bottom: 1px;
            }
        }
    }
}
</style>