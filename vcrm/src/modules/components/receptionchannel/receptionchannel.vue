<template>
    <div class="reception-channel"></div>
</template>
<script>
export default {
    name: "receptionchannel",
    components: {
    },
    watch: {
    },
    data() {
        return {
            alertText:'',
            appid: ''
        }
    },
    async mounted() {
        let _this = this;
        if(window.Portal && window.Portal.appId) {
            const appIdMapping = {
                //订货通
                'FSAID_11491084': 'FSAID_11490c84',
                //代理通
                'FSAID_11490d9e': 'FSAID_11490d9e',
                'FSAID_11490f80': 'FSAID_11490f80',
                //服务通
                'FSAID_98979c': 'FSAID_98979c',
                'FSAID_989aa3': 'FSAID_989aa3',
                //经销商门户应用
                'FSAID_11491250': 'FSAID_11491250',
                'FSAID_1149135b': 'FSAID_1149135b',
                //渠道分销应用
                'FSAID_11491173': 'FSAID_11491173',
                'FSAID_114910bc': 'FSAID_114910bc',
                //配件商城
                'FSAID_11491512': 'FSAID_11491512',
                'FSAID_11491427': 'FSAID_11491427',
            };
            const currentAppId = window.Portal.appId;
            if (appIdMapping.hasOwnProperty(currentAppId)) {
                _this.appid = appIdMapping[currentAppId];
            }
        }
        let result = await _this.cycleStatus();
        _this.alertText = result.alertText;
        // 可续签并且未读 需要强制提醒
        if (result.renewable && !result.hasRead) {
        // if (!result.renewable && !result.hasRead) {
            _this.$confirm(result.alertText || $t('crm.dateline.need'), $t('crm.dateline.notice'), {
                confirmButtonText: $t('crm.to.reception'),
                cancelButtonText: $t('我知道了'),
                type:'warning',
            }).then(() => {
                window.location.href = `https://${window.location.host}/XV/Cross/Portal?fs_out_appid=${_this.appid}#/portal${_this.appid}/list/=/PartnerAgreementDetailObj`
            }).catch(() => {
                _this.alertRead();
                _this.$nextTick(() => {
                    _this.addToptitle();
                });
            });
        } else if (result.renewable && result.hasRead) {
            // 可续签 但是已读 只展示提示横幅
            setTimeout(() => {
                _this.addToptitle();
            }, 1500);
        }
    },
    methods: {
        addToptitle() {
            let _this = this;
            _this.addTitle && _this.addTitle.destroy && _this.addTitle.destroy();
            $('.portal-root').css('top','92px')
            _this.addTitle = FxUI.create({
                wrapper: $('.container')[0],
                template: `
                        <fx-alert
                            v-show="visible"
                            title=""
                            :style="titleStyle"
                            :show-icon="true"
                            :closable="true"
                            type="warning"
                            @close="close">
                            <template #title>
                                <span v-html="msg"></span>
                                <fx-button size="micro" type="primary" plain  @click="to">{{ $t("crm.to.reception") }}</fx-button>
                            </template>
                        </fx-alert>
                `,
                computed: {
                    msg() {
                        return _this.alertText || $t('crm.dateline.need')
                    },
                    titleStyle() {
                        return 'height: 52px;'
                    }
                },
                data() {
                    return {
                        visible: true
                    }
                },
                methods: {
                    to() {
                        window.location.href = `https://${window.location.host}/XV/Cross/Portal?fs_out_appid=${_this.appid}#/portal${_this.appid}/list/=/PartnerAgreementDetailObj`
                    },
                    close() {
                        this.visible = false;
                        $('.portal-root').css('top','40px')
                    },
                },
            })
        },
        cycleStatus() {
            return new Promise((resolve, reject) => {
                    CRM.util.FHHApi({
                        url:
                            "/EM1HNCRM/API/v1/object/prm_channel/service/renewal_cycle_status",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        alertRead() {
            return new Promise((resolve, reject) => {
                    CRM.util.FHHApi({
                        url:
                            "/EM1HNCRM/API/v1/object/prm_channel/service/alert_read",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        }
    },
    destroy() {
        this.addTitle && this.addTitle.destroy && this.addTitle.destroy();
    }
}
</script>
<style lang="less">
</style>