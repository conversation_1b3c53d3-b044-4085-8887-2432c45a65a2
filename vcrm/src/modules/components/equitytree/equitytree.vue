<script>
import BusinessTree from '@components/businesstree/businesstree';
import baseDirectAction from '@components/businesstree/action/action';
import parse from './common/parse';
import api from './common/api';
import directAction from './common/action';
import actionApi from './common/actionapi';
import { edgeConfig } from './common/registerx';
export default {
    extends: BusinessTree,
    props: {
        pluginTooltipConfig: {
            type: Object,
            default: {
                offsetX: 10,
                offsetY: -10,
            },
        },
    },
    data() {
        return {
            actionMap: {
                noRelated: 'newObj',
                node: '',
            },
        }
    },
    methods: {
        fetchTreeData() {
            return api.fetchTreeData({
                "dataId": this.relatedObjectDataId, // 招商局请求关联客户主数据，其他为当前详情数据 dataId
                "name": this.relatedObjectDataName, // 招商局请求关联客户主数据name，其他为当前详情数据 name
                "objectApiName": this.relatedObjectApiName, // 组件配置允许关联创建 对象 apiname, 招商局固定 AccountMainDataObj
                "showFieldList": this.relatedFieldsConfig.showFields || ['name'],  // 组件配置的显示字段
            })
        },
        parseData(data) {
            return parse.parseData(data, this);
        },
        parseCompactTreeOptions(options) {
            const me = this;
            options = this.parseDefaultTreeOptions(options);
            const getConfig = options.getConfig;
            options.getConfig = function(config) {
                config = getConfig(config);
                config.edgeConfig = edgeConfig;
                return config;
            }
            options.registerParam = {
                pw: 25,
                collapseJudge(cfg, group) {
                    return cfg.children && cfg.children.length && cfg.tree_path.length > 1 && !cfg.noCollapse;
                }
            }
            options.getGraphOptions = function(graphOptions) {
                graphOptions.layout = _.extend({}, graphOptions.layout, {
                    type: 'mindmap',
                    direction: 'H',
                    getSide: (node) => {
                        return node.data.direction;
                    },
                    getHGap: () => {
                      return 66;
                    },
                })
                return graphOptions;
            }
            return options;
        },
        getTreeAction() {
            const baseTreeAction = baseDirectAction(actionApi, parse);
            const treeAction = directAction(actionApi, parse);
            return Object.assign({}, baseTreeAction, treeAction);
        },
    }
}
</script>
