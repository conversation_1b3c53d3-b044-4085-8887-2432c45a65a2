<script>
import api from './common/api';
import BusinessTree from '../businesstree/businesstree.vue';
import parse from '../businesstree/common/parse';
import directAction from '../businesstree/action/action';
import actionApi from './common/actionapi';

export default {
    extends: BusinessTree,
    props: {
        relatedFieldsConfig: {
            type: Object,
            default: {
                "default": {
                    "showFieldName": true,
                    "showFields": [
                        "name",
                        // "partner_id",  //查找关联
                        // "field_ihmCk__c",  //网址
                        // "industry_level1",  //单选
                        // "field_22jRj__c"   //日期时间
                    ],
                    "isShow": true,
                },
                "levelTwoAll": {
                    "showFieldName": true,
                    "showFields": [
                        // "name",
                        // "field_zj61R__c",  //百分数
                    ],
                    "isShow": false,
                },
                "levelTwo": {
                    "showFieldName": true,
                    "showFields": [
                        // "name"
                        // "industry_level1",  //单选
                        // "field_KsH2Z__c"  //多选
                    ],
                    "isShow": false,
                }
            }
        },
        relatedObjectApiName: {
            type: String,
            default: 'AccountObj',
        },
        relatedObjectDisplayName: {
            type: String,
            default: $t('crm.客户'),
        },
        accountMainData: {
            type: Object,
            default: {
                _id: '62fa31255cf11c00016a59ff',
                name: 'test',
            }
        },
        curId: {
            type: String,
            default: '64b0bc25d86e220001fb81f2',
        },
        // noRelatedTip: {
        //     type: String,
        //     default: $t('crm.g6tree.noaccount'),
        // },
        pTipMap: {
            type: Object,
            default: {
                noRelated: $t('crm.g6tree.noaccount'),
            }
        },
        pActionMap: {
            type: Object,
            default: {
                node: '',
            }
        },
    },
    data() {
        return {
            // actionMap: {
            //     noRelated: CRM.util.isGrayScale('CRM_ACCOUNTTREE_NOCREATE') ? '' : 'newObj',
            //     node: '',
            // },
            lazy: false,
            source: 'orgTree',
        }
    },
    methods: {
        async getDescribeLayout() {
            const res = await CRM.util.getObjectDescribe(this.relatedObjectApiName);
            const relatedVisibleFields = _.map(['default', 'levelTwo', 'levelTwoAll'], (key) => {
                return this.relatedFieldsConfig[key].showFields;
            })
            this.describeLayout = parse.parseMultipleDescribeLayout(res.objectDescribe, relatedVisibleFields);
        },
        async parseData(data) {
            data = await parse.parseData(data, this);
            data.accountMainData = this.accountMainData;
            return data;
        },
        fetchTreeData() {
            return api.fetchTreeData({
                "account_main_data_id": this.accountMainData._id, // 客户主数据ID
                "with_account_fields": this.relatedFieldsConfig?.default?.showFields,
                "show_my_2nd_level_account": this.relatedFieldsConfig?.levelTwo?.isShow, // 当前用户所属二级组织下客户信息
                "with_my_2nd_level_account_fields": this.relatedFieldsConfig?.levelTwo?.showFields,
                "show_other_2nd_level_account": this.relatedFieldsConfig?.levelTwoAll?.isShow, // 跨组织其他二级组织下客户信息
                "with_other_2nd_level_account_fields": this.relatedFieldsConfig?.levelTwoAll?.showFields,
            })
        },
        getTreeAction() {
            return directAction(actionApi, parse);
        },
        parseCompactTreeOptions(options) {
            options.getConfig = function(config) {
                const drawShape = config.nodeConfig.draw;
                config.nodeConfig.draw = function (cfg, group) {
                    if (cfg.isRoot && cfg.accountMainData && cfg.accountMainData.name) {
                        const fontSize = 13;
                        const paddingX = 8;
                        const paddingY = 4;
                        const lineHeight = 20;
                        const pathWidth = 50;
                        const mainWidth = parse.getStringWidth(cfg.accountMainData.name, 13) + paddingX*2;
                        const mainHeight = lineHeight + paddingY*2;
                        const h = (cfg.text ? cfg.text.length : 0) * 20  + 8;
                        const r = 4;

                        group.addShape('rect', {
                            attrs: {
                                x: - mainWidth - pathWidth,
                                y: h/2 - mainHeight/2,
                                width: mainWidth, //200,
                                height: mainHeight, // 60
                                stroke: '#DEE1E8',
                                radius: r,
                                fill: '#F2F4FB',
                            },
                            // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
                            name: 'main-data',
                        });
                        group.addShape('text', {
                            attrs: {
                                textBaseline: 'middle',
                                x: - mainWidth - pathWidth + paddingX,
                                y: h/2 - mainHeight/2 + paddingY + lineHeight/2,
                                lineHeight: lineHeight,
                                text: cfg.accountMainData.name,
                                fill: '#181C25',
                                fontSize: fontSize,
                            },
                            // must be assigned in G6 3.3 and later versions. it can be any string you want, but should be unique in a custom item type
                            name: 'maindata-text',
                        });
                        group.addShape('path', {
                            attrs: {
                                path: [
                                    ['M', -pathWidth, h/2],
                                    ['L', 0, h/2],
                                ],
                                stroke: '#DEE1E8',
                                lineWidth: 1,
                            }
                        });
                    }
                    const mainBox = drawShape(cfg, group);
                    return mainBox;
                }
                config.pluginsConfig.Tooltip.config.offsetX = -200;
                config.pluginsConfig.Tooltip.config.offsetY = -10;
                return config;
            }
            return options;
        },
        parseSearchTreeOptions(options) {
            options.showSearch = false;
            return options;
        },
        parseBaseInfo(info) {
            info.accountMainData = this.accountMainData;
            return info;
        }
    }

}
</script>
