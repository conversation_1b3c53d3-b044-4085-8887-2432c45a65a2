import { VNode } from 'vue';
import { DirectiveBinding } from 'vue/types/options';
import { editableManager } from '../core/EditableManager';
import type { EditableInstance, EditableOptions } from '../types';
import '../styles/index.less';

interface EditableElement extends HTMLElement {
  _editable?: EditableInstance;
  _editableOptions?: EditableOptions;
}

// 合并指令的值和修饰符生成配置
function createOptions(binding: DirectiveBinding, vnode: VNode): EditableOptions {
  const { value, modifiers } = binding;
  
  // 基础配置
  const options: EditableOptions = {
    trigger: 'dblclick',
    history: true,
    buttons: true
  };

  // 处理修饰符
  if (modifiers.click) options.trigger = 'click';
  if (modifiers.custom) options.trigger = 'custom';
  if (modifiers.nobuttons) options.buttons = false;
  if (modifiers.nohistory) options.history = false;

  // 处理传入的配置
  if (typeof value === 'object') {
    Object.assign(options, value);
  }

  // 添加事件处理
  const vm = vnode.context;
  if (vm) {
    const originalOnSave = options.onSave;
    const originalOnCancel = options.onCancel;
    const originalOnEdit = options.onEdit;

    options.onSave = async (content: string, target?: HTMLElement) => {
      vm.$emit('editable:save', content, target);
      if (originalOnSave) {
        return originalOnSave(content, target);
      }
    };

    options.onCancel = () => {
      vm.$emit('editable:cancel');
      originalOnCancel?.();
    };

    options.onEdit = () => {
      vm.$emit('editable:edit');
      originalOnEdit?.();
    };
  }

  return options;
}

export const editable = {
  bind(el: EditableElement, binding: DirectiveBinding, vnode: VNode) {
    const options = createOptions(binding, vnode);
    el._editableOptions = options;
    el._editable = editableManager.makeEditable(el, options);
  },

  update(el: EditableElement, binding: DirectiveBinding, vnode: VNode) {
    if (binding.value.version === binding.oldValue.version) return;
    editableManager.handleContentUpdate(el);
    // 如果配置发生变化，重新创建编辑实例
    // const newOptions = createOptions(binding, vnode);
    // const oldOptions = el._editableOptions;

    // if (JSON.stringify(newOptions) !== JSON.stringify(oldOptions)) {
    //   el._editableOptions = newOptions;
    //   el._editable = editableManager.reinitialize(el, newOptions);
    // } else {
    // 内容更新但配置未变化时，调用 handleContentUpdate
    // editableManager.handleContentUpdate(el);
    // }
  },

  unbind(el: EditableElement) {
    editableManager.destroy(el);
    delete el._editable;
    delete el._editableOptions;
  }
}; 