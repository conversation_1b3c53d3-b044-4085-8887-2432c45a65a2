// 按钮位置类型
export type ButtonPosition = 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';

// 清理配置接口
export interface CleanOptions {
  // 需要保留的标签选择器列表
  preserveSelectors?: string[];
  // 自定义清理函数
  customClean?: (content: string) => string;
}

// 编辑选项接口
export interface EditableOptions {
  version?: string;                           // 内容版本，变更时即dom或者内容发生变更，需要触发组件状态更新
  trigger?: 'dblclick' | 'click' | 'custom';  // 触发方式
  history?: boolean;                          // 是否启用历史记录
  buttons?: boolean;                          // 是否显示按钮
  buttonPosition?: ButtonPosition | ((element: HTMLElement) => ButtonPosition);  // 按钮位置
  customClass?: string;                       // 自定义样式类
  editableSelector?: string;                  // 可编辑元素选择器
  onSave?: (content: string, target?: HTMLElement) => Promise<void>;  // 保存回调
  onCancel?: () => void;                     // 取消回调
  onEdit?: () => void;                       // 进入编辑态回调
  validate?: (content: string) => boolean;    // 内容验证函数
  cleanOptions?: CleanOptions;
}

// 编辑实例接口
export interface EditableInstance {
  destroy: () => void;      // 销毁实例
  triggerEdit: () => void;  // 触发编辑
  save: () => void;         // 保存
  cancel: () => void;       // 取消
}

// 历史记录接口
export interface HistoryManager {
  push: (content: string) => void;  // 添加新记录
  undo: () => string | undefined;   // 撤销
  redo: () => string | undefined;   // 重做
  canUndo: () => boolean;          // 是否可以撤销
  canRedo: () => boolean;          // 是否可以重做
  clear: () => void;               // 清空历史
}

// 编辑元素状态
export interface EditableState {
  options: EditableOptions;
  cleanup: () => void;
  rebind: () => void;  // 添加重新绑定事件的方法
  isEditing: boolean;
  originalContent?: string;
  currentTarget?: HTMLElement;
  history?: HistoryManager;
  buttonsInstance?: any; // Vue组件实例
}

// Vue指令扩展的HTMLElement
export interface EditableElement extends HTMLElement {
  _editable?: EditableInstance;
  _editableOptions?: EditableOptions;
} 