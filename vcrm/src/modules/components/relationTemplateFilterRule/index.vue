<template>
  <div class="relation-template-filter-rule">
    <div class="filter-rule-tip">
      <fx-alert
        :title="filterTip"
        :show-icon="true"
        :closable="false"
        type="info">
      </fx-alert>
    </div>
    <div class="fitler-rule-box" ref="filter-ref">
    </div>
  </div>
</template>

<script>
import CRMRequire from '@common/require'

export default {
  name: 'RelationTemplateFilterRule',
  props: {
      value: {
          type: Array,
          default: []
      },
      apiName: {
          type: String
      },
      defaultVal: {
          type: Object
      },
      filterTip: {
          type: String,
          default: $t('crm.relation.template.create.tip')
      },
      objectName: {
          type: String
      },
  },
  watch: {

  },
  mounted() {
      this.renderFilter();
  },
  methods: {
      getPropsValue() {
          if (!this.value.length) return null;
          return this.value;
      },
      async renderFilter() {
          const me = this;
          const Filter = await CRMRequire('crm-modules/common/filtergroup/filtergroup');
          // const {PriceruleFilterConfig: FieldsConfig} = await CRMRequire('crm-modules/action/pricepolicyobj/pricepolicyobj');
          // const mApiName = me.sourceObjectData.apiName.master;
          const objectDescribe = await CRM.util.getDescribeLayout({
              apiname: me.apiName,
              include_layout: false,
              include_detail_describe: false
          }).then((res) => {
              return res.objectDescribe;
          })
          const fields = objectDescribe?.fields || {};
          this.filter = new Filter({
              $wrapper: $(me.$refs['filter-ref']),
              optionType: 'cascader',
              width: 800,
				      // apiname: me.apiName,
              defaultValue: me.getPropsValue(),
              // filterType: FieldsConfig.filterConditionType(),
              // filterApiname: FieldsConfig.filterConditionApi('master', me.sourceObjectData.fieldMapping, me.amortize === 'amortize' ? 'detail' : 'no'),
              objects: [
                  {
                      api_name: me.apiName,
                      fields,
                      label: objectDescribe?.display_name_r || objectDescribe?.display_name
                  }
              ],
              // formatGetItem(item) {
              //     item.object_api_name = mApiName;
              //     item.object_api_name__s = $t('crm.' + mApiName);
              //     item.field_name_type = 'field';
              //     item.field_value_type = 'value';

              //     if (item.type === 'percentile') {
              //         item.field_values__s = item.field_values__s + '%';
              //     }
              //     return item;
              // },
              parseFields(fields) {
                console.log(fields, 'fields000')
                  var _fs = {};
                  _.each(fields, function (field) {
                      if (field.is_active && field.is_index) {
                          _fs[field.api_name] = field;
                      }
                  });
                  return _fs;
              },
              props: {
                  lazy: true,
                  checkStrictly: true, // 可选时，expandTrigger只能为click, hover方式会导致最开始默认选中项无法选中
                  expandTrigger: 'click'
              },
              parseCompare(compare, field) {
                // 过滤多选字段 属于/不属于
                // console.log(field.type, 'field.type');
                if(me.objectName == 'InteractionStrategyObj' && field.type === 'select_many') {
                  console.log(compare, field, 'parseCompare---');
                  compare = compare.filter(({ value }) => ![25, 26].includes(value));
                }
                return compare
              }
          });
          // TODO 这里组件数据回显时，获取数据会有问题；改为提交时手动获取该组件数据；
          // this.filter.on('change', () => {
          //     const value = this.getValue();
          //     if (!value.length) return;
          //     this.$emit('change', value);
          // });
      },
      getValue() {
          let value = this.getPropsValue();
          if(this?.filter?._getValue) {
            value = JSON.parse(this.filter._getValue());
          }
          // 订单条件为空时，返回的是一个 [{}]
          return JSON.stringify({type: "CONDITION", value: JSON.stringify(value || [])});
      },
  },
  destroyed() {
      this.filter?.destroy();
  },
}
</script>

<style lang="less">
.relation-template-filter-rule{
  max-width: 824px;
  .filter-rule-tip{
    margin-bottom: 12px;
  }
  .fitler-rule-box {


  }
}

</style>
