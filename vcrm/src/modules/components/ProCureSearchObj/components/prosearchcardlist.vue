<template>
  <div>
    <div class="prosearchobj-list-detail">
      <div v-show="!showBulkChoose">
        <p
          style="height: 9px; background-color: var(--color-neutrals03) width: 1000px; margin-left: -15px;"
        ></p>
        <p
          v-show="render"
          style="width: 98%; margin: 8px 0px 8px 15px"
          class="card-top"
        >
         {{ $t('相关') }} {{total}} {{$t('条')}} {{ $t('crm.procurementsearchobj.banner')}}
        </p>
      </div>
      <div v-show="showBulkChoose">
        <p v-show="render" class="all-select-icon">
         {{ $t('全选')}}<span class="selected-custom"></span>
        </p>
        <p v-show="render" class="card-top">
          <fx-button
            type="primary"
            :disabled="isHavecheckDate"
            @click="isGetAllAnnouncement()"
            >{{$t('批量领取')}}</fx-button
          >
          <span style="float: right"
            > {{ $t('相关') }} {{total}} {{$t('条')}} {{ $t('crm.procurementsearchobj.banner')}}</span
          >
        </p>
      </div>
      <div v-if="render" class="detail-page">
        <fx-table
          :row-key="getRowKeys"
          ref="multipleTable"
          :data="listDate"
          style="width: 100%"
          tooltip-effect="dark"
          row-class-name="multiple-row"
          :cell-class-name="cellStyle"
          @select="handleSelectionChange"
          @select-all="handleAllSelectionChange"
        >
          <fx-table-column
            v-if="showBulkChoose"
            label-class-name="multiple-selection"
            width="55"
            :reserve-selection="true"
            type="selection"
          >
          </fx-table-column>
          <fx-table-column>
            <template slot-scope="scope">
              <div class="detail" @click="showCrmDetail(scope)">
                <p
                  v-for="(title, index) in scope.row.infoTitle"
                  :key="index"
                  v-html="title"
                  class="title"
                  :title="deteleTage(title)"
                ></p>
                <div class="detail-tag">
                  <fx-tag
                    v-for="(tag, index) in scope.row.tages"
                    :key="index"
                    :type="tag.type"
                    size="small"
                    class="tag-title"
                  >
                    {{ tag.name }}
                  </fx-tag>
                </div>
                <ol class="info">
                  <li v-for="(dt, idx) in scope.row.details" :key="idx">
                    <span class="product_title">{{ dt.key }}</span>
                    <span
                      :title="deteleTage(dt.name)"
                      :class="dt.className"
                      v-html="dt.name"
                      ></span
                    >
                  </li>
                </ol>
              </div>
            </template>
          </fx-table-column>

          <fx-table-column width="200">
            <template slot-scope="scope">
              <div
                v-for="(title, index) in scope.row.buttons"
                :key="index"
                class="operate"
                v-show="title.label && scope.row.biz_status != 'chosen'"
              >
                <fx-image @click="getAnnouncement(scope.row)" :src="imagePath1" class="operate-icon"></fx-image>
                <span>{{ title.label }}</span>
              </div>
            </template>
          </fx-table-column>
        </fx-table>
      </div>
      <div class="null-data-img" v-else>
        <div style="text-align: center">
          <fx-image :src="imgSrc" class="operate-icon"> </fx-image>
          <div class="null-data-img-hint">{{ hint }}</div>
        </div>
      </div>
    </div>

    <fx-dialog
      :visible.sync="dialogVisible1"
      ref="dialog1_3"
      size="big"
      max-height="400px"
      :showFullscreenSwitch="true"
      :append-to-body="true"
      :closeOnClickModal="false"
      title="提示"
    >
      <template>
        <fx-table
          :row-key="getRowKeys"
          ref="multipleTable"
          :data="listDate"
          style="width: 100%"
          tooltip-effect="dark"
          row-class-name="multiple-row"
        >
          <fx-table-column>
            <template slot-scope="scope">
              <div class="detail">
                <p
                  v-for="(title, index) in scope.row.infoTitle"
                  v-html="title"
                  class="title"
                  :title="deteleTage(title)"
                ></p>
                <div class="detail-tag">
                  <fx-tag
                    v-for="(tag, index) in scope.row.tages"
                    :key="index"
                    :type="tag.type"
                    size="small"
                    class="tag-title"
                  >
                    {{ tag.name }}
                  </fx-tag>
                </div>
                <ol class="info">
                  <li v-for="(dt, idx) in scope.row.details" :key="idx">
                    <span class="product_title">{{ dt.key }}</span>
                    <span
                      :title="deteleTage(dt.name)"
                      :class="dt.className"
                      v-html="dt.name"
                      >{{ dt.name }}</span
                    >
                  </li>
                </ol>
              </div>
            </template>
          </fx-table-column>
        </fx-table>
      </template>
    </fx-dialog>
  </div>
</template>

<script>
export default {
  name: "ProSearchList",
  data() {
    let _thisDate = this;
    return {
      cellStyle({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0 && _thisDate.showBulkChoose) {
          return "cellTextStyle";
        }
      },
      getRowKeys(row) {
        return row._id;
      },
      multipleSelection: [],
      dialogVisible1: false,
      isHavecheckDate: false,
      ids: [],
      checkMaxNumber: 50,
      imgSrc: FS.CRM_MODULE.ASSETS_PATH + "/images/placeholder-nullpage.svg",
      imagePath1:
        FS.CRM_MODULE.ASSETS_PATH + "/images/crm-procurement-fenxi.svg",
    };
  },
  props: {
    quotasRelated: {
      type: Object,
      default: {},
    },
    showBulkChoose: {
      type: Boolean,
      default: false,
    },
    hint: {
      type: String,
      default: $t("crm.procurementsearchobj.pleaseSearchFirst"),
    },
    render: {
      type: Boolean,
      default: false,
    },
    listDate: {
      type: Array,
      default: [],
    },
    total: {
      type: String,
    },
    isRefreshPage :{
      type: Boolean,
    },
    nowShowPage: {
      type: String,
    },
  },
  mounted() {},
  watch: {
    listDate(newVal, oldVal) {
      console.log(this.render, this.hint, 'shfsf')
      // 数据发生变化拿到预存的领取值，筛选加勾
      this.$nextTick(() => {
        const _this = this;
        if (_this.multipleSelection.length) {
          const arr = _this.multipleSelection;
          _this.ids = arr.map((item) => item._id);
          //新的数据中有其他的已选择的数据的id过滤出来筛选加对号
          _this.listDate
            .filter((item) => _this.ids.includes(item._id))
            .forEach((row) => {
              row.checked = true;
              _this.$refs.multipleTable.toggleRowSelection(row);
            });
        }
        // 加多少条的文案
        if (_this.listDate.length && _this.showBulkChoose) {
          _this.setDetailTagStyle();
        }
      });
    },
    multipleSelection: {
      handler(newvalue) {
        if (newvalue.length === 0) {
          // 批量领取能点击
          this.isHavecheckDate = true;
        } else {
          // 批量领取不能点击
          this.isHavecheckDate = false;
        }
      },
      deep: true,
      immediate: true,
    },
    isRefreshPage : {
      handler(newvalue){
        // 清空以选择的数据
            this.multipleSelection = [];
        // 刷新页面
            this.$emit("announToSaveRefech",true);
        // 全部变成未选择状态
            this.listDate.forEach((item) => {
                    item.checked = false;
                    this.toggleRowSelection(item);
            });
      }
    }

  },
  methods: {
    // 正则去标签
    deteleTage(str) {
      return str.toString().replace(/<\/?.+?\/?>/g, "");
    },
    //全选框后加文字以及权限没开启的样式
    setDetailTagStyle() {
      if (this.multipleSelection.length !== 0) {
        $(".selected-custom").html(
          $t("已选择{{allSelectCustom}}条", {
            allSelectCustom: this.multipleSelection.length,
          })
        );
      } else {
        $(".selected-custom").html("");
      }
    },
    // 跳转详情页
    showCrmDetail(d) {
      const me = this;
      CRM.api.show_crm_detail({
        type: "ProcurementSearchObj",
        id: d.row._id,
        apiName: "ProcurementSearchObj",
        extendData: {
          bidType: d.row.bid_type,
          record_type : me.nowShowPage === 'zl' ? void 0 : 'record_qlm__c',
        },
      });
    },
    // 领取公告
    getAnnouncement(account) {
      const _this = this;
      CRM.util.waiting($t("执行中") + "...");
      CRM.util.FHHApi(
        {
          url: "/EM1HNCRM/API/v1/object/ProcurementSearchObj/action/AsyncAllocate",
          data: {
            id: account._id,
            bid_type: account.bid_type,
            record_type : _this.nowShowPage === 'zl' ? void 0 : 'record_qlm__c',
          },
          success(res) {
            if (res && res.Value) {
              let param = {
                jobId: res.Value.jobId,
                apiname: "ProcurementSearchObj",
                success: () => {
                  _this.$message({
                    message: $t(
                      "crm.procurementsearchobj.getAnnouncementSucces"
                    ),
                    type: "success",
                    offset: parseInt(screen.availHeight) / 2,
                  });
                },
              };
              _this.getAnnouncementToSave(param);
            } else {
              // 失败有重复的
              CRM.util.waiting(false);
              _this.$message({
                message: res.Result.FailureMessage,
                type: "error",
                offset: parseInt(screen.availHeight) / 2,
              });
            }
          },
          error() {
            CRM.util.waiting(false);
            CRM.util.alert($t("无法获取数据"));
          },
        },
        {
          errorAlertModel: 1,
        }
      );
    },
    // 异步接口频繁调用
    getAnnouncementToSave(param) {
      const _this = this;
      if (!param.__time) {
        param.__time = new Date().getTime();
      }
      CRM.util.FHHApi(
        {
          url: "/EM1HNCRM/API/v1/object/button/service/findButtonJobResult",
          data: {
            jobId: param.jobId,
            describeApiName: param.apiname,
          },
          success: function (res) {
            var rv = res.Value;
            if (rv && rv.jobInfo) {
              //查询到结果
              CRM.util.waiting(false);
              // 成功给提示
              param.success && param.success();
              // 刷新页面
              _this.$emit("announToSaveRefech");
              // me.fetchDate(10, (me.toPage.pageNumber - 1) * 10, "old");
            } else {
              if (new Date().getTime() - param.__time > 5000) {
                //超过5s没结果

                CRM.util.waiting(false);
                _this.$alert($t("crm.批量操作提示"), $t("提示"), {
                  confirmButtonText: $t("我知道了"),
                });
              } else {
                setTimeout(function () {
                  _this.getAnnouncementToSave(param);
                }, 1000);
              }
            }
          },
          error: function () {
            CRM.util.waiting(false);
          },
        },
        {
          errorAlertModel: 1,
        }
      );
    },
    // 公告领取弹窗提醒
    isGetAllAnnouncement() {
      this.$confirm($t("是否开启批量领取"), $t("提示"), {
        confirmButtonText: $t("确定"),
        cancelButtonText: $t("取消"),
        type: "warning",
      })
        .then(() => {
          this.getAllAnnouncement();
        })
        .catch(() => {});
    },
    // 公告批量领取
    getAllAnnouncement() {
      const _this = this;
      // 处理数据
      let multipleLists = [];
      this.multipleSelection.forEach((item) => {
        multipleLists.push({
          id: item._id,
          bid_type: item.bid_type,
          record_type: _this.nowShowPage === 'zl' ? void 0 : 'record_qlm__c',
        });
      });
      CRM.util.waiting($t("执行中") + "...");
      CRM.util.FHHApi(
        {
          url: "/EM1HNCRM/API/v1/object/ProcurementSearchObj/action/AsyncBulkChoose",
          data: {
            data_list: multipleLists,
          },
          success(res) {
            if (res && res.Value) {
              let param = {
                jobId: res.Value.jobId,
                apiname: "ProcurementSearchObj",
                success: function () {
                  // 预存的数据清空刷新页面
                  _this.multipleSelection = [];
                  _this.$emit("announToSaveRefech");
                  _this.listDate.forEach((item) => {
                    item.checked = false;
                    _this.toggleRowSelection(item);
                  });
                },
              };
              _this.getAllAnnouncementToSave(param);
            } else {
              // 失败有重复的
              CRM.util.waiting(false);
              _this.$message({
                message: res.Result.FailureMessage,
                type: "error",
                offset: parseInt(screen.availHeight) / 2,
              });
            }
          },
          error() {
            CRM.util.waiting(false);
            CRM.util.alert($t("无法获取数据"));
          },
        },
        {
          errorAlertModel: 1,
        }
      );
    },
    // 批量保存所有的数据
    getAllAnnouncementToSave(param) {
      CRM.api.list_batchbtn_operate_query(param);
    },
    /* 
    1.selection已经选中过的元素
    2.要选择的元素数据
    */
    async handleSelectionChange(selection, row) {
      const me = this;
      const isChecked = selection.length && selection.indexOf(row) !== -1;
      // 如果有选中的并且新选中的不在里面的,选中态变为true
      isChecked && (row.checked = true);
      !isChecked && (row.checked = false);
      const isEmit = await this.setMaxNumber(selection, isChecked, row);
      if (isEmit) {
        // 加入已经选择的列表数据中
        row.checked && me.multipleSelection.push(row);
        //删除掉之前选中又取消的元素
        !row.checked &&
          (me.multipleSelection = me.multipleSelection.filter(
            (select) => select._id !== row._id
          ));
        this.filterData();
        me.multipleSelection.length === 0
          ? (this.isHavecheckDate = true)
          : (this.isHavecheckDate = false);
      }
    },
    // 选中全部数据
    async handleAllSelectionChange(selection) {
      const me = this;
      selection.map((item) => {
        item.checked = !item.checked;
        return item;
      });
      const isEmit = this.setAllMaxNumber(selection);
      if (isEmit) {
        me.multipleSelection = me.multipleSelection.concat(selection);
        if (selection.length === 0) {
          // 如果当页取消勾选,那么把已选数据中包含的数据去除
          let selectData = this.listDate.map((item) => item._id);
          me.multipleSelection = me.multipleSelection.filter(
            (item) => !selectData.includes(item._id)
          );
        }
        this.filterData();
        me.multipleSelection.length === 0
          ? (this.isHavecheckDate = true)
          : (this.isHavecheckDate = false);
      }
    },
    //去重
    filterData() {
      const me = this;
      let hash = {}; //去重
      let arr = me.multipleSelection.reduce((preVal, curVal) => {
        hash[curVal._id] //id就是数组中的id字段
          ? ""
          : (hash[curVal._id] = true && preVal.push(curVal));
        return preVal;
      }, []);
      me.multipleSelection = arr;
      // 加已经选择多少条
      if (arr.length !== 0) {
        $(".selected-custom").html(
          $t("已选择{{allSelectCustom}}条", {
            allSelectCustom: me.multipleSelection.length,
          })
        );
      } else {
        $(".selected-custom").html("");
      }
    },
    // 最多不超过50条
    setMaxNumber(selection, isChecked, row) {
      const me = this;
      let checkEl = "";
      let isExceeLimit = "";
      // 被点击的数据被选中
      if (isChecked) {
        isExceeLimit = me.multipleSelection.length >= this.checkMaxNumber;
        checkEl = selection[selection.length - 1];
      } else {
        //被点击的数据被取消
        isExceeLimit = me.multipleSelection.length - 1 >= this.checkMaxNumber;
        checkEl = row;
      }
      return new Promise((resolve, reject) => {
        // 如果数据大于最大数据
        if (isExceeLimit) {
          this.listDate.forEach((item) => {
            // 如果这条数据在列表中
            if (item._id === checkEl._id) {
              // 给他提示
              CRM.util.remind(
                3,
                $t("最多可勾选{{checkMaxNumber}}条", {
                  checkMaxNumber: this.checkMaxNumber,
                })
              );
              // 把选择的这条数据取消勾选
              this.$refs["multipleTable"].toggleRowSelection(item, false);
            }
          });
          resolve(false);
        } else {
          resolve(true);
        }
      });
    },
    //全选设置最大限额
    setAllMaxNumber(selection) {
      const me = this;
      return new Promise((resolve, reject) => {
        if (selection.length) {
          if (me.multipleSelection.length + selection.length > this.checkMaxNumber) {
            CRM.util.remind(
              3,
              $t("最多可勾选{{checkMaxNumber}}条", {
                checkMaxNumber: this.checkMaxNumber,
              })
            );
            this.listDate.forEach((item) => {
              this.$refs["multipleTable"].toggleRowSelection(item, false);
            });
            resolve(false);
          } else {
            resolve(true);
          }
        }
      });
    },
    //取消选中元素
    toggleRowSelection(val) {
      this.$refs["multipleTable"].toggleRowSelection(val, false);
    },
  },
};
</script>

<style>
</style>