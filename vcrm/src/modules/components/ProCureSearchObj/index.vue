<template>
  <div class="crm-prosearch-main" v-loading="loading">
    <!-- 搜索区域 -->
    <div class="dt-term-batch">
      <searchTitle
        :searchOptions="searchOptions"
        :searchQueryCach="searchQueryCach"
        :saveConditionGroup="saveConditionGroup"
        :nowShowPage='nowShowPage'
        :isZlAndQlmPage='isZlAndQlmPage'
        :highSearch="highSearch"
        @changeSearchModel="changeSearchModel"
        @gotoFetch="gotoFetch"
        @resetRenderOutFilter="resetRenderOutFilter"
        @toSaveConditionGroup="toSaveConditionGroup"
        @changeRenderPage='changeRenderPage'
        ref="getSearchWords"
      >
      </searchTitle>
    </div>
    <!-- 筛选器区域 -->
    <div class="dt-out-filter"></div>
    <!-- 列表区域 -->
    <ProSearchCardList
      :quotasRelated="quotasRelated"
      :showBulkChoose="showBulkChoose"
      :hint="hint"
      :render="render"
      :listDate="listDate"
      :total="totalSum"
      :nowShowPage='nowShowPage'
      @announToSaveRefech="announToSaveRefech"
    />
    <!-- 翻页组件 -->
    <div v-show="showPageNumber" class="page-number">
      <fx-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-sizes="[10, 20, 30]"
        :page-size="10"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
      >
      </fx-pagination>
    </div>
  </div>
</template>

<script>
import searchTitle from "./components/search.vue";
import ProSearchCardList from "./components/prosearchcardlist.vue";
import Table from "crm-widget/table/table";
import { COMPARE, _parseConditions , fetchListHeader , getPermission, getQlmCity, getConidtionGroup} from "./parseFilter.js";
import Tools from './toolFunction'
import highSearchVue from './components/highSearch.vue';
export default {
  props: {
    nameVal: String,
    filterApiname: String,
  },
  data() {
    return {
      // 当前展示页面
      nowShowPage:'',
      // 知了和千里马都开启
      isZlAndQlmPage:false,
      highSearch:false,//是否为高级搜索
      loading: true,
      searchOptions: [], //关键词左侧搜索参数
      searchQueryCach: {}, //请求参数
      tableOutfilter: {}, //筛选器实例
      multipleSelection: [], //多选时缓存的数据
      oldSearchQueryCach: {}, //备份条件主要是防止领取时用户删除了关键词以至于打不上标签
      saveConditionGroup: true,
      //一下是翻页组件所需要的数据
      total: 0,
      pageSize: 10,
      currentPage: 1,
      pageNumber: 1,
      showPageNumber: false,
      //列表组建需要数据
      quotasRelated: {},
      showBulkChoose: false,
      hint: $t("crm.procurementsearchobj.pleaseSearchFirst"),
      render: false,
      listDate: [],
      totalSum: "100",
      // 市区
      // zlCityOption: [],
      // qlmCityOption:[],
      qlmProvinceOption:[],
      // 特殊渲染字段
      zlSpecialFields: ['match_type','keywords','exclusion_words'],
      // 测试用信息
      testOptionConfig: "{\"limit\":10,\"offset\":0,\"filters\":[{\"field_name\":\"match_mode\",\"field_values\":[\"2\"],\"operator\":\"EQ\"},{\"field_name\":\"publish_time\",\"field_values\":[1706716800000,1710518399000],\"operator\":\"BETWEEN\"},{\"field_name\":\"area_province\",\"field_values\":[\"110000\",\"120000\"],\"operator\":\"HASANYOF\"},{\"field_name\":\"area_city\",\"field_values\":[\"283\"],\"operator\":\"EQ\"},{\"field_name\":\"tender_end_date_time\",\"field_values\":[1708931268000],\"operator\":\"GT\"},{\"field_name\":\"biding_end_date_time\",\"field_values\":[1707408000000],\"operator\":\"GT\"},{\"field_name\":\"budget_interval\",\"field_values\":[\"1\",\"100000\"],\"operator\":\"BETWEEN\"},{\"field_name\":\"caller_type\",\"field_values\":[\"1\",\"2\"],\"operator\":\"HASANYOF\"},{\"field_name\":\"bid_processes\",\"field_values\":[\"1\"],\"operator\":\"EQ\"},{\"field_name\":\"match_type\",\"field_values\":\"1\",\"operator\":\"EQ\"},{\"field_name\":\"keywords\",\"field_values\":\"1 123 45 78\",\"operator\":\"EQ\"},{\"field_name\":\"exclusion_words\",\"field_values\":\"123\",\"operator\":\"EQ\"}]}",
    };
  },
  components: {
    searchTitle: searchTitle,
    ProSearchCardList: ProSearchCardList,
  },
  created() {
    const me = this;
    // Promise.all([CRM.util.getCountryAreaOptions() , getPermission(),getQlmCity()]).then(([obj, config, qlmcityObj]) => { 
    //       me.zlCityOption = obj.city.options.map((item) => {
    //           item["ItemName"] = item.label;
    //           item["ItemCode"] = item.value;
    //           return item;
    //       });
    //       me.qlmCityOption = qlmcityObj.city.map((item) => {
    //           item["ItemName"] = item.label;
    //           item["ItemCode"] = item.value;
    //           return item;
    //       });
    //       me.qlmProvinceOption = qlmcityObj.province.map((item) => {
    //           item["ItemName"] = item.label;
    //           item["ItemCode"] = item.value;
    //           return item;
    //       });
    //     me.beforeInit(config);
    // })
    getPermission().then((config) => {
       me.beforeInit(config);
    })
  },
  methods: {
    // 改变当前搜索模式
    changeSearchModel(val){
      this.highSearch = val
    },
    changeRenderPage(renderPage){
      this.render  = false
      this.showPageNumber = false
      this.init(renderPage)
    },
    beforeInit(config){
        const me = this
        let renderPage = 'zl'
        // 只有千里马的权限渲染千里马查询
        if(config.hasProcurementQlm  && !config.hasProcurementZl){
            this.nowShowPage = 'qlm'
            renderPage = 'qlm'
        }
        // 都有默认渲染知了可通过切换渲染相关
        if(config.hasProcurementZl && config.hasProcurementQlm){
          me.isZlAndQlmPage = true
          if(CRM.getLocal('getUserLastSearchProcurementPermission') === 'qlm'){
            this.nowShowPage = 'qlm'
            renderPage = 'qlm'
          }
        }
        me.init(renderPage)
    },
    init(renderPage) {
      const me = this;
      me.nowShowPage = renderPage
      fetchListHeader(renderPage).done((res) => {
        let filterOptions = [];
        CRM.util.fetchFilterColumns(
          res,
          {
            isEdit: false,
          },
          function (pData) {
            filterOptions = [];
            // 过滤对象数组,筛选条件里得数据
            let filterColumns = Tools.filterColumnsByFileds(pData.filterColumns,res,me.nowShowPage)
            filterColumns.some((item) => {
              switch (item.api_name) {
                case "winner_names":
                  item.filterCompare = [2, 8, 9, 10, 11, 12, 22, 23];
                  item.comparison = 1;
                  filterOptions.push(item);
                  break;
                case "caller_type":
                  item.filterCompare = [7, 8, 10, 9, 2, 11, 12, 14, 22, 23];
                  item.comparison = 13;
                  filterOptions.push(item);
                  break;
                // case "area_province":
                //   item.filterCompare = [7, 8, 14, 10, 9, 2];
                //   item.comparison = 13;
                //   item.options = me.nowShowPage === 'qlm' ? me.qlmProvinceOption : item.options;
                //   filterOptions.push(item);
                //   break;
                case "publish_time":
                  let now = new Date()
                  now.setMonth(now.getMonth() - 1);
                  item.filterCompare = [
                    18, 19, 1, 2, 3, 4, 5, 6, 9, 10, 20, 21, 25, 26, 27, 28, 29,
                    30, 31, 32, 33, 34, 35, 36,
                  ];
                  item.filterValue = [now.getTime(), new Date().getTime()];
                  item.comparison = 17;
                  filterOptions.push(item);
                  break;
                case "budget_interval":
                  item.filterCompare = [1, 2, 3, 4, 5, 6, 9, 10];
                  item.comparison = 17;
                  filterOptions.push(item);
                  break;
                case "bid_processes":
                  item.filterCompare = [2, 5, 6, 9, 10, 14];
                  item.comparison = 13;
                  filterOptions.push(item);
                  break;
                case "caller_names":
                  item.filterCompare = [2, 8, 9, 10, 11, 12, 22, 23];
                  item.comparison = 1;
                  item.filterValue = me.nameVal || ''
                  filterOptions.push(item);
                  break;
                case "match_mode":
                  item.filterCompare = [2, 5, 6, 9, 10, 13, 14];
                  item.comparison = 1;
                  item.filterValue = item.default_value;
                  filterOptions.push(item);
                  break;
                case "bid_method":
                  item.filterCompare = [2, 5, 6, 9, 10, 14];
                  item.comparison = 13;
                  filterOptions.push(item);
                  break;
                case "tender_end_date_time":
                  item.filterCompare = [
                    17, 18, 19, 1, 2, 4, 5, 6, 9, 10, 20, 21, 25, 26, 27, 28,
                    29, 30, 31, 32, 33, 34, 35, 36,
                  ];
                  item.comparison = 3;
                  filterOptions.push(item);
                  break;
                case "biding_end_date_time":
                  item.filterCompare = [
                    17, 18, 19, 1, 2, 4, 5, 6, 9, 10, 20, 21, 25, 26, 27, 28,
                    29, 30, 31, 32, 33, 34, 35, 36,
                  ];
                  item.comparison = 3;
                  filterOptions.push(item);
                  break;
                // case "area_city":
                //   item.filterCompare = [7, 8, 14, 10, 9, 2];
                //   item.comparison = 13;
                //   item.options = me.nowShowPage === 'qlm' ? me.qlmCityOption : me.zlCityOption;
                //   filterOptions.push(item);
                //   break;
                case "purchase_type":
                  item.filterCompare = [1, 7, 8, 14, 10, 9, 2];
                  item.comparison = 13;
                  filterOptions.push(item);
                  break;
                case "one_level_two":
                  item.filterCompare =  [7, 8, 14, 10, 9, 2];
                  item.comparison = 13;
                  item.type = 'casecade_select',
                  filterOptions.push(item);
                  break;
                case "province_and_city":
                  item.filterCompare =  [7, 8, 14, 10, 9, 2];
                  item.comparison = 13;
                  item.type = 'casecade_select',
                  filterOptions.push(item);
                  break;
                default:
                  item.filterCompare =  [2, 5, 6, 9, 10, 14, 13];
                  item.comparison = 1;
                  filterOptions.push(item);
              }
            });

            // 顺序固定
            let filterOptions = Tools.regularFilterOptions(filterOptions);
            filterOptions =  filterOptions.filter(item => item.api_name !== 'area_city' && item.api_name !== 'area_province');
            me.searchOptions = filterOptions.filter((res) => {
                return res.api_name == "match_type";
            })[0].options
            me.filterOptions = filterOptions.filter((res) => {
                return res.api_name != "match_type";
            });
            me.initOutFilter();
          }
        );
      });
    },
    initOutFilter(filterOptions) {
      this.renderOutFilter(filterOptions);
      this.loading = false;
    },
    // 获取条件组配置信息重新渲染相关区域
    resetRenderOutFilter(condition){
      this.initFilterRest(condition)
    },
    // 冲新渲染筛选区
    renderOutFilter(filterOptions) {
      this.tableOutfilter = new Table.OutFilter({
        el: $(".dt-out-filter", this.$el),
        filterItems: filterOptions || this.filterOptions,
      });
      this.tableOutfilter.render();
      // 改变按钮筛选组件上绑定的事件
      $(".crm-prosearch-main .j-out-filter").addClass("j-out-myfilter");
      $(".crm-prosearch-main .j-out-myfilter").removeClass("j-out-filter");
      $(".crm-prosearch-main .j-out-myfilter").click(() => {
        this.gotoFetch();
        this.isRefreshPage = !this.isRefreshPage;
      });
    },
    // 保存条件组
    toSaveConditionGroup(cb){
       cb && cb(JSON.stringify(this.getFilterCach())) 
    },
    gotoFetch() {
      this.saveConditionGroup = false;
      this.getFilterCach()
      this.keyword = this.$refs.getSearchWords.searchQuery.keyword.trim();
      this.excludeKeywords =
        this.$refs.getSearchWords.searchQuery.excludeKeywords.trim();
      this.beforFetchDate();
    },
    // 获取条件
    getFilterCach(){
      this.searchQueryCach.filterQueryCatch = _parseConditions(
        this.tableOutfilter.getValue()
      );
      // 插入match_type
      this.searchQueryCach.filterQueryCatch.push({
        field_name: "match_type",
        field_values: [this.$refs.getSearchWords.value],
        operator: "EQ",
      });
      // 插入关键词
      if(!this.highSearch){
        this.searchQueryCach.filterQueryCatch.push({
        field_name: "keywords",
        field_values: [this.$refs.getSearchWords.searchQuery.keyword.trim()],
        operator: "EQ",
        });
      }
      if(this.highSearch){
        this.searchQueryCach.filterQueryCatch.push({
        field_name: "keywords",
        field_values: this.$refs.getSearchWords.getHighKeywordsValue(),
        operator: "EQ",
        });
      }
      
      // 插入排除词
      this.searchQueryCach.filterQueryCatch.push({
        field_name: "exclusion_words",
        field_values:
          [this.$refs.getSearchWords?.searchQuery.excludeKeywords.trim() || ''],
        operator: "EQ",
      });
      return this.filterNullObj();
    },
    beforFetchDate() {
      // 请求前参数校验
      if(!this.highSearch){
          if (!this.keyword) {
            this.popTitle();
            return;
          } else {
            this.fetchDate(10, 0, "new");
            return
          }
      }
        this.fetchDate(10, 0, "new");
    },
    // 没有关键词的弹窗提示
    popTitle() {
      this.$message({
        message: $t("crm.procurementsearchobj.pleaseFillInKeyword"),
        type: "warning",
      });
    },
    // 筛选器组件相关方法
    handleSizeChange(val) {
      this.pageSize = val;
      this.fetchDate(
        this.pageSize,
        (this.pageNumber - 1) * this.pageSize,
        "old"
      );
    },
    handleCurrentChange(val) {
      this.pageNumber = val;
      this.fetchDate(this.pageSize, (val - 1) * this.pageSize, "old");
    },
    fetchDate(limit, filters, isRenderPage) {
      CRM.util.waiting();
      const me = this;
      let searchQueryInfo = {
        limit: limit,
        offset: filters,
        filters: [],
      };
      searchQueryInfo.filters = this.filterNullObj();
      CRM.util.FHHApi(
        {
          url: "/EM1HNCRM/API/v1/object/ProcurementSearchObj/controller/List",
          data: {
            object_describe_api_name: "ProcurementSearchObj",
            search_template_id: null,
            search_query_info: JSON.stringify(searchQueryInfo),
            record_type: me.nowShowPage === 'zl' ? void 0 : 'record_qlm__c',
          },
          success(res) {
            CRM.util.waiting(false);
            if (res.Result.StatusCode == 0) {
              // 渲染列表
              me.parseListParam(res);
              if (isRenderPage == "new") {
                //重新关键词搜索去掉已经缓存的数据
                me.multipleSelection = [];
                me.total = res.Value.total;
                me.showPageNumber = true;
              }
            } else {
              CRM.util.alert(res.Result.FailureMessage);
            }
          },
          error() {
            CRM.util.waiting(false);
            CRM.util.alert($t("无法获取数据"));
          },
        },
        {
          errorAlertModel: 1,
        }
      );
    },
    // 过滤空对象
    filterNullObj() {
      let filterConfig = []
      // 不再传空对象
      this.searchQueryCach.filterQueryCatch.map((item) => {
        if (item.field_values && item.field_values.constructor == Array) {
          if (item.field_values[0] && item.field_values[0] != "") {
            filterConfig.push(item);
          }
        } else {
          filterConfig.push(item);
        }
        return item;
      });
      return filterConfig;
    },
    // 整理列表数据
    parseListParam(res) {
      const me = this;
      let searchQuery = {
        value: this.$refs.getSearchWords.value,
        keyword: this.$refs.getSearchWords.searchQuery.keyword,
      };
      if (res && res.Result.StatusCode == 0) {
        this.quotasRelated = {
          quotaRemain: res.Value.quotaRemain,
          quotaTotal: res.Value.quotaTotal,
          quotaUsed: res.Value.quotaUsed,
        };
        this.showBulkChoose = res.Value.showBulkChoose;

        let data = res.Value.dataList || [];
        if (res.Value.buttonInfo) {
          var buttonMap = res.Value.buttonInfo.buttonMap;
          var buttons = res.Value.buttonInfo.buttons;
        }
        if (data.length == 0) {
          this.render = false;
          this.hint = $t("crm.procurementsearchobj.noSearchResults");
          this.showPageNumber = false;
        } else {
          this.render = true;
          this.showPageNumber = true;
        }
        let fields = res.Value.objectDescribe.fields;
        this.totalSum = CRM.util.toMoney(Number(res.Value.total));
        data.length &&
          data.map((item) => {
            item.checked = false;
            const detailData = [];
            const tages = [];
            const infoTitle = [];
            const resultButtons = [];
            const obj = Tools.extractField(item);
            for (const key in obj) {
              switch (key) {
                case "title":
                  if (obj[key] && typeof obj[key] == "string") {
                    if (searchQuery.value == "1" || searchQuery.value == "3") {
                      if(me.highSearch){
                       obj[key] =  Tools.highlightHighKeywords(
                        obj[key],
                        this.$refs.getSearchWords.getHighKeywordsValue().join(' ').split(' ')
                        )
                      }else{
                        obj[key] = Tools.highlightKeywords(
                        obj[key],
                        searchQuery.keyword.trim().split(" ")
                      );
                      }
                    }
                    infoTitle.push(obj[key]);
                  }
                  break;
                case "biz_status":
                  if (obj[key]) {
                    let bizStatus = fields[key].options.filter((item) => {
                      return item.value == obj[key];
                    });
                    if (bizStatus[0].value == "chosen") {
                      tages.push({
                        type: "danger",
                        name: bizStatus[0].label,
                      });
                    }
                  }
                  break;
                case "bid_type":
                  if (obj[key]) {
                    let bibTypeLabel = fields[key].options.filter((item) => {
                      return parseInt(item.value) == parseInt(obj[key]);
                    });
                    if(bibTypeLabel[0]){
                      tages.push({
                      type: "effect",
                      name: bibTypeLabel[0].label,
                    });
                    }
                  }
                  break;
                case "bid_sub_type":
                  if (obj[key]) {
                    let bibSubTypeLabel = fields[key].options.filter((item) => {
                      return parseInt(item.value) == parseInt(obj[key]);
                    });
                    if(bibSubTypeLabel[0]){
                      tages.push({
                      type: "effect",
                      name: bibSubTypeLabel[0].label,
                    });
                    }
                  }
                  break;
                case "tender_days":
                  if (obj[key]) {
                    obj[key] =
                      $t("crm.procurementsearchobj.obtainBidSurplus") +
                      obj[key] +
                      $t("天");
                    tages.push({
                      type: "effect",
                      name: obj[key],
                    });
                  }
                  break;
                case "biding_days":
                  if (obj[key]) {
                    obj[key] =
                      $t("crm.procurementsearchobj.tenderRemaining") +
                      obj[key] +
                      $t("天");
                    tages.push({
                      type: "effect",
                      name: obj[key],
                    });
                  }
                  break;
                case "caller_budget":
                  detailData.push({
                    key: fields[key] ? fields[key].label : "--",
                    name:
                      obj[key] || obj[key] == 0
                        ? `<em style='color:#f98517;'>${CRM.util.toMoney(
                            obj[key]
                          )}</em>`
                        : "--",
                    className: key,
                  });
                  break;
                case "winner_amount":
                  detailData.push({
                    key: fields[key] ? fields[key].label : "--",
                    name:
                      obj[key] || obj[key] == 0
                        ? `<em style='color:#f98517;'>${CRM.util.toMoney(
                            obj[key]
                          )}</em>`
                        : "--",
                    className: key,
                  });
                  break;
                case "product_names":
                  if (obj[key] && typeof obj[key] == "string") {
                    if (searchQuery.value == "3" || searchQuery.value == "2") {
                       if(me.highSearch){
                       obj[key] =  Tools.highlightHighKeywords(
                        obj[key],
                        this.$refs.getSearchWords.getHighKeywordsValue().join(' ').split(' ')
                        )
                      }else{
                        obj[key] = Tools.highlightKeywords(
                        obj[key],
                        searchQuery.keyword.trim().split(" ")
                      );
                      }
                    }
                  }
                  detailData.push({
                    key: fields[key] ? fields[key].label : "--",
                    name: obj[key] ? obj[key] : "--",
                    className: key,
                  });
                  break;
                case "caller_names":
                  if (obj[key] && typeof obj[key] == "string") {
                    if (searchQuery.value == "4" || searchQuery.value == "3") {
                      if(me.highSearch){
                       obj[key] =  Tools.highlightHighKeywords(
                        obj[key],
                        this.$refs.getSearchWords.getHighKeywordsValue().join(' ').split(' ')
                        )
                      }else{
                        obj[key] = Tools.highlightKeywords(
                        obj[key],
                        searchQuery.keyword.trim().split(" ")
                      );
                      }
                    }
                  }
                  detailData.push({
                    key: fields[key] ? fields[key].label : "--",
                    name: obj[key] ? obj[key] : "--",
                    className: key,
                  });
                  break;
                case "winner_names":
                  if (obj[key] && typeof obj[key] == "string") {
                    if (searchQuery.value == "5" || searchQuery.value == "3") {
                       if(me.highSearch){
                       obj[key] =  Tools.highlightHighKeywords(
                        obj[key],
                        this.$refs.getSearchWords.getHighKeywordsValue().join(' ').split(' ')
                        )
                      }else{
                        obj[key] = Tools.highlightKeywords(
                        obj[key],
                        searchQuery.keyword.trim().split(" ")
                      );
                      }
                    }
                  }
                  detailData.push({
                    key: fields[key] ? fields[key].label : "--",
                    name: obj[key] ? obj[key] : "--",
                    className: key,
                  });
                  break;
                default:
                  detailData.push({
                    key: fields[key] ? fields[key].label : "--",
                    name: obj[key] ? obj[key] : "--",
                    className: key,
                  });
                  break;
              }
            }
            // 权限取按钮
            if (Object.keys(buttonMap).length) {
              buttonMap[item._id].map((item1) => {
                buttons.map((item2) => {
                  if (item2.api_name == item1) {
                    resultButtons.push({
                      label: item2.label,
                    });
                  }
                  return item1;
                });
                return item;
              });
            }

            item.buttons = resultButtons;
            item.tages = tages;
            item.details = detailData;
            item.infoTitle = infoTitle;
            return item;
          });
        this.listDate = data;
      }
    },
    // 重新渲染筛选去
    initFilterRest(conditions) {
      const me = this;
      let searchValue = {};
      let testOptionConfig = JSON.parse(conditions);
      // 深拷贝
      let filterOptions = JSON.parse(JSON.stringify(me.filterOptions))
      filterOptions.forEach((item1) => {
        const matchedItem2 = testOptionConfig.find((item2) =>{ 
              return  item2.field_name === item1.field_name
          }
        );
        if (matchedItem2) {
          // 如果被保存条件组的比较符，在已经排除的比较符里则以自身为主
          if(item1.filterCompare && !item1.filterCompare.includes(parseInt(Tools.findKeyByValue(COMPARE, matchedItem2.operator)))){
              item1.comparison = parseInt(Tools.findKeyByValue(COMPARE, matchedItem2.operator));
            if (matchedItem2.operator == "EQ" || (matchedItem2.operator == "BETWEEN" && matchedItem2.field_values.length == 1)) {
              item1.filterValue = matchedItem2.field_values[0];
            } else {
              item1.filterValue = matchedItem2.field_values;
            }  
        }
        
        }
      });
      // 特殊字段处理
      testOptionConfig.map((item2) =>{ 
          if(me.zlSpecialFields.includes(item2.field_name)){
                searchValue[item2.field_name] = item2.field_values;
            }
          }
        );
      // 重新渲染筛区
      this.initOutFilter(filterOptions);
      // 重新渲染特殊字段区域
      this.$refs.getSearchWords.changeData(searchValue)
    },
   
    // (领取后和点击后)刷新页面
    announToSaveRefech(refPageNumber) {
      const me = this;
      if (refPageNumber) {
        //点击后筛选后分页组件和数据强制刷新
        // 更新底部组件
        this.pageSize = 10;
        this.currentPage = 1;
        this.pageNumber = 1;
        this.fetchDate(10, 0, "new");
      } else {
        this.fetchDate(
          this.pageSize,
          (this.pageNumber - 1) * this.pageSize,
          "old"
        );
      }
    },
  },
};
</script>
<style lang="less">
.crm-prosearch-main {
  height: 100%;
  overflow: auto;
  overflow-x: hidden !important;
  overflow-y: auto !important;
  /* 隐藏默认滚动条 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  /* 定义滚动条的样式 */
  &::-webkit-scrollbar {
    width: 5px; /* 设置滚动条的宽度 */
  }

  /* 定义滚动条轨道的样式 */
  &::-webkit-scrollbar-track {
    background-color: #f1f1f1; /* 设置滚动条轨道的背景颜色 */
  }

  /* 定义滚动条滑块的样式 */
  &::-webkit-scrollbar-thumb {
    background-color: #888; /* 设置滚动条滑块的背景颜色 */
    border-radius: 5px; /* 设置滚动条滑块的圆角 */
  }

  /* 定义滚动条滑块在hover状态下的样式 */
  &::-webkit-scrollbar-thumb:hover {
    background-color: #555; /* 设置滚动条滑块hover状态下的背景颜色 */
  }
  .dt-term-batch {
    width: 99%;
    margin: -10px 7px 0px 8px;
    background: var(--color-neutrals01);
    height: 95px;
  }
  .dt-out-filter {
    width: 99%;
    margin: 0px 7px 0px 8px;
    background: var(--color-neutrals01);
    padding: 10px 0px;
    .crm-tb-outfilter {
      .o-ts-label {
        width: 100px;
      }
      .more-btn {
        display: none !important;
      }
      .shortcuts-value {
        background-color: var(--color-neutrals01);
        line-height: 30px;
        cursor: pointer;
        position: absolute;
        inset: 0px 27px 5px 24px;
      }
    }
  }

  .prosearchobj-list-detail {
    position: relative;
    td {
      border-left: 0px;
      border-right: 0px;
    }
    .all-select-icon {
      position: absolute;
      top: 23px;
      left: 70px;
      z-index: 1;
      .selected-custom {
        padding-left: 15px;
      }
    }
    .card-top {
      position: absolute;
      width: 500px;
      z-index: 1;
      top: 12px;
      right: 17px;
      font-family: "Source Han Sans CN";
      font-size: 12px;
      color: var(--color-neutrals15);
      span {
        margin: 4px 10px;
      }
      .el-button--primary {
        float: right;
        height: 15px;
        padding: 12px 10px;
      }
      .el-button {
        line-height: 0;
      }
    }
    .detail-page {
      width: 99%;
      margin: 6px 7px 0px 8px;
      background: var(--color-neutrals01);
      padding: 10px 0px;
      .has-gutter {
        .is-leaf {
          border: none;
          background-color: var(--color-neutrals01);
          .multiple-selection {
            margin-left: 10px;
          }
        }
        .multiple-title {
          white-space: nowrap;
          margin-top: 15px;
          font-size: 12px;
          margin-left: 12px;
        }
      }
    }
    .detail {
      cursor: pointer;
      width: 100%;
      padding-left: 5px;
      .title {
        font-size: 16px;
        color: var(--color-neutrals19);
        font-weight: normal;
        margin-bottom: 5px;
        cursor: pointer;
      }

      .detail-tag {
        overflow: hidden;
        white-space: nowrap;

        .tag-title {
          height: 22px;
          margin-right: 4px;
          padding: 0 7px;
          max-width: 200px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      .info {
        font-size: 14px;
        color: var(--color-neutrals11);

        li {
          margin-right: 12px;
          width: 44%;
          float: left;
          line-height: 1.6;
          display: flex;
        }

        span {
          display: -webkit-box;
          text-overflow: -o-ellipsis-lastline;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          line-clamp: 2;
          -webkit-box-orient: vertical;

          &:first-child {
            width: 90px;
            color: var(--color-neutrals11);
            margin-right: 20px;
          }

          &:last-child {
            color: #2a304d;
            flex: 1;
            cursor: pointer;
            // &.email {
            //     color: var(--color-info06);
            // }
          }
        }
      }
    }

    .null-data-img {
      width: 99%;
      margin: 0px 7px 0px 8px;
      height: 500px;
      background-color: var(--color-neutrals01);
      display: flex;
      justify-content: center;
      align-items: center;
      .null-data-img-hint {
        color: #cccfd7;
        font-size: 16px;
        margin-top: 5px;
      }
    }

    .logo-icon {
      padding: 18px;
      box-sizing: content-box;
      background: var(--color-neutrals03);
      border-radius: 4px;

      &.active {
        padding: 0;
        text-align: center;
        line-height: 62px;

        .image-slot {
          white-space: nowrap;
          color: var(--color-info06);
          font-weight: 600;
          border: 1px solid var(--color-neutrals05);
        }
      }
    }

    .operate {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      margin-top: 60px;
      cursor: pointer;
      .operate-icon {
        width: 24px;
        height: 24px;
        margin-bottom: 6px;
      }
      span {
        color: #2a304d;
      }
    }

    .exist {
      .operate-icon {
        color: var(--color-neutrals07);
      }
    }
    .cellTextStyle {
      .cell {
        line-height: 150px;
        margin-left: 10px;
      }
    }
  }
  .page-number {
    background-color: white;
    position: sticky;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
