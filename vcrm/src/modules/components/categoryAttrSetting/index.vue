<template>
    <div class="category-attribute-wrapper">
        <div class="category-attribute-op">
            <div class="category-attribute-selected" v-show="filterConditions.length">
                <div class="title">{{$t("筛选条件")}}：</div>
                <div class="content">
                    <div v-for="(attrVal, i) in filterConditions" :key="i" class="select-attribute-value attribute-item">
                        <span>{{ attrVal.name }}</span>
                        <span class="el-icon-close j-delete-condition" @click="deleteVal(attrVal)"></span>
                    </div>
                </div>   
                <div class="total">
                    <label>{{$t('共')}}<span class="total-num">{{ filterConditions.length }}</span>{{$t('个')}}</label>
                    <label class="j-clean clean-all" @click="clearVal"><span class="el-icon-delete"></span>{{$t("清空筛选值")}}</label>
                </div>  
            </div>
            <div class="operators">
                <div v-if="!attribute_constraint_id" class="category-attribute-btn crm-btn j-setting" @click="showDialog">{{$t("设置属性筛选")}}</div>
                <div class="category-attribute-btn crm-btn j-fold" data-fold="false" @click="toggleFlod">{{flod ? $t("展开属性筛选") : $t("收起属性筛选") }}</div>    
            </div>
        </div>
        <div class="category-attribute-content" v-show="!flod">
            <!-- 属性级联约束 -->
            <price-quoter-render
                v-if="!!attribute_constraint_id"
                ref="priceQuoter"
                :style="{
                    padding: '8px',
                    border: '1px solid #DEE1E6'
                }"
                :noDataText="$t('属性级联约束')"
                :sortByGroup="true"
                :showSubmitBtn="false"
                @changeAfter="priceQuoterChangeAfter"
            ></price-quoter-render>
            <!-- 普通属性 -->
            <template v-else>
                <div class="box">
                    <div class="wrapper" v-if="selectedGroupList.length">
                        <div class="group-item" v-for="(group, i) in selectedGroupList" :key="group.groupId">
                            <p class="group-name" @click="toggleExpand(group, i)">
                                <i :class="['expand-icon', group.expand ? 'fx-icon-unfold-2' : 'fx-icon-fold-2']"></i>
                                {{group.groupName}}
                            </p>
                            <div class="attribute-item-box" v-show="group.expand">
                                <div class="attribute-item" v-for="(attr, j) in group.options" :key="attr._id">
                                    <label class="attribute-name">{{ attr.name }}:</label>
                                    <div class="attribute-values">
                                        <div v-for="(attrVal, idx) in attr.attributeValues" :key="attrVal._id" 
                                            :class="['attribute-val', 'j-attribute-val', {'active': modelData[attr._id].checked.includes(attrVal._id)}]" 
                                            :data-id="attrVal._id"
                                            @click="clickAttrVal(attrVal, attr)"
                                        >{{ attrVal.name }}</div>
                                    </div>
                                    <!-- <label class="j-more btn-more" @click="showMore(attr, j)">{{attr.show === -1 ? $t("更多") : attr.show === 1 ? $t("收起") : $t("展开")}}</label> -->
                                    <label class="btn-multi">{{$t("多选")}}<fx-switch size="mini" v-model="modelData[attr._id].isMultiple" @change="(val) => toggleMultiple(val, attr)"></fx-switch></label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="info-text">{{$t('暂无属性值，可点击')}}<span class="setting j-setting" @click="showDialog">{{$t('设置')}}</span></div>
                </div>
                <attr-dialog
                    ref="dialog"
                    :categoryId="categoryId" 
                    :defaultSelected="modelData"
                    @submit="handleSubmit"
                ></attr-dialog>
            </template>
        </div>
    </div>
</template>
<script>
/**
 * 设置属性筛选组件
 * @event categoryChange 分类改变
 * @event change 属性改变
 * @event attribute.render.after 页面渲染完成
 * @method setCategory 初始化分类数据
 * @method getParam 获取分类设置条件
 */
import PriceQuoterRender from '@components/priceQuoterRender'
import AttrDialog from './attrSetting';
export default {
    name: 'categoryAttrSetting',
    props: {
        unFlod: false
    },
    components: {
        PriceQuoterRender,
        AttrDialog,
    },
    data() {
        return {
            // 展示
            categoryId: '',
            attribute_constraint_id: '',
            loaded: false,
            flod: !this.unFlod && CRM.getLocal('category-attribute-setting-flod'),
            // 属性
            selectedGroupList: [],
            modelData: {
                // [attrId]: {checked: []}
            },
        };
    },
    watch: {
        // categoryId: {
        //     immediate: true,
        //     async handler(id) {
        //         let me = this;
                
        //     }
        // }
    },
    computed: {
        filterConditions() {
            let data = Object.values(this.modelData).reduce((arr, item) => {
                if (item.checked?.length) {
                    let res = item.attributeValues.filter(val => item.checked.includes(val._id));
                    arr = arr.concat(res)
                }
                return arr;
            }, []);
            return data;
        }
    },
    mounted() {
    },
    methods: {
        /**
         * 初始化分类
         * @param {String} id 分类id
         * @param {Object} data 当前分类数据
         * @param {Array} defalutSelectedList = [{
                        attribute_id: "66dec67584be090007ec932f",
                        attribute_value: [ "66dec67584be090007ec9331"]
                    }] 默认选中属性
         */
        async setCategory(id, data, defalutSelectedList) {
			this.categoryId = id || "999999"; //全部分类id为999999
            this.attribute_constraint_id = data?.attribute_constraint_id; // 属性级联约束id
            return await this.initView(defalutSelectedList);
		},
        async initView(defalutSelectedList) {
            if (defalutSelectedList) {
                this.defalutSelectedData = defalutSelectedList.reduce((res, item) => {
                    res[item.attribute_id] = item;
                    return res;
                }, {});
            }
            this.selectedGroupList = [];
            this.modelData = {};
            let allConditions = [];
            if (this.attribute_constraint_id) {
                allConditions = await this.initPriceQuoter();
            } else {
                allConditions = await this.initAttribute();
            }
            this.$nextTick(() => {
                this.$emit('attribute.render.after');
            })
            return allConditions;
        },
        // 初始化级联
        initPriceQuoter() {
            return new Promise(async (resolve) => {
                let constraintData = await CRM.util.queryAttributeConstraintById({id: this.attribute_constraint_id});
                await this.$refs['priceQuoter'].init(constraintData?.dataList, this.defalutSelectedData);
                // 级联渲染完毕
                this.$nextTick(() => {
                    let allConditions = this.getPriceQuoterConditions();
                    resolve(allConditions)
                })
            })
        },
        /**
         * 获取级联 筛选条件
         */
        getPriceQuoterConditions() {
            let res = this.$refs['priceQuoter'].getFilterParam();
            let {_attrParam, _nsAttrParam} = res;
            let conditions = [];
            _attrParam.forEach(({attribute_id, field_values, field_num}) => {
                conditions.push({
                    'field_name': `attribute${field_num}`,
                    'operator': 'HASANYOF', // 13-属于
                    'field_values': field_values,
                });
            })
            _nsAttrParam.forEach(({attribute_id, field_values}) => {
                conditions.push({
                    field_name: 'nonstandard_attribute_ids',
                    operator: 'CONTAINS', // 40
                    field_values: [attribute_id] // 非标属性只传id
                })
            })
            return conditions;
        },
        // 切换报价器条件查询
        priceQuoterChangeAfter: _.debounce(function() {
            console.log('priceQuoterChangeAfter');
            let allConditions = this.getPriceQuoterConditions();
            this.$emit('change', allConditions);
        }, 300),
        // 初始化 全部属性 或 属性范围
        initAttribute() {
            return new Promise(async (resolve) => {
                const attributes = await CRM.util.getAttributeByCategory(this.categoryId);
                let attrList = attributes.reduce((res, item) => {
                    let {attribute, attributeValues} = item;
                    attribute.name = attribute.name__r || attribute.name;
                    let defalut_checked = [];
                     // 默认选中的属性值 // 记录上次设置的默认选中值
                     if (this.defalutSelectedData) {
                        defalut_checked = this.defalutSelectedData[attribute._id]?.attribute_value || [];
                     } else {
                        defalut_checked = attributeValues.reduce((res, item) => {
                            item['selected_flag'] && res.push(item._id);
                            return res;
                        }, []);
                    }
                    attribute.attributeValues = attributeValues.map(item => {
                        item.name = item.name__r || item.name;
                        return item;
                    });
                    delete item.attributeValues;
                    res.push(attribute);
                    this.$set(this.modelData, attribute._id, {
                        ...(_.pick(attribute, ['_id', 'name', 'name__r', 'field_num', 'groupId', 'groupName', 'groupNo'])),
                        isMultiple: false,
                        checked: defalut_checked,
                        default_value: defalut_checked.length ? defalut_checked[0] : null,
                        attributeValues: attributeValues
                    })
                    return res;
                }, [])
                this.selectedGroupList = CRM.util.sortAttrByGroup(attrList, {attr_id: '_id'});
                this.selectedGroupList.forEach(item => {
                    this.$set(item, 'expand', true)
                })
                console.log('attrList', attrList)
                console.log('modelData',this.modelData)
                console.log('selectedGroupList', this.selectedGroupList)
                // 属性渲染完毕
                this.$nextTick(() => {
                    let allConditions = this.getAllConditions();
                    resolve(allConditions)
                })
            })
        },
        toggleExpand(group) {
            group.expand = !group.expand;
        },
        clickAttrVal({_id, name}, attr) {
            let attrId = attr._id;
            let idx = this.modelData[attrId].checked.indexOf(_id);
            let isChecked = idx > -1;
            if (isChecked) {
                this.modelData[attrId].checked.splice(idx, 1);
            } else {
                if (this.modelData[attrId].isMultiple) {
                    this.modelData[attrId].checked.push(_id);
                } else {
                    this.modelData[attrId].checked = [_id];
                }
            }
            let allConditions = this.getAllConditions();
            this.$emit('change', allConditions);
        },
        /**
         * 获取属性 或 属性范围 筛选条件
         */
        getAllConditions() {
            let conditions = [];
            Object.values(this.modelData).forEach(({checked, field_num}) => {
                checked.length && conditions.push({
                    field_name: `attribute${field_num}`,
                    field_values: checked,
                    operator: 'HASANYOF'
                })
            })
            return conditions;
        },
        // showMore(attr, j) {
        //     console.log(attr)
        //     attr.show = attr.show === 1 ? 0 : 1;
        // },
        toggleFlod() {
            this.flod = !this.flod;
            CRM.setLocal('category-attribute-setting-flod', this.flod);
        },
        toggleMultiple(is_multiple, attr) {
            this.modelData[attr._id].isMultiple = is_multiple;
            let checked = this.modelData[attr._id].checked;
            if (!is_multiple && checked.length > 1) {
                let valId = checked[0];
                this.$set(this.modelData[attr._id], 'checked', [valId]);
                let allConditions = this.getAllConditions();
                this.$emit('change', allConditions);
            }
        },
        deleteVal({attribute_id, _id, name}) {
            let idx = this.modelData[attribute_id].checked.indexOf(_id);
            if (idx > -1) {
                this.modelData[attribute_id].checked.splice(idx, 1);
            }
            let allConditions = this.getAllConditions();
            this.$emit('change', allConditions);
        },
        clearVal() {
            Object.values(this.modelData).forEach(attr => {
                attr.checked = [];
            })
            this.$emit('change', []);
        },
        showDialog() {
            this.$refs['dialog'].show();
        },
        handleSubmit(data, cb) {
            let me = this;
            this.submitSetting({
                "categoryId": this.categoryId,
                "userId": CRM.curEmpId,
                isRecover: false,
                ...data
            }).done(async result => {
                if (result.isSuccess) {
                    cb && cb();
                    CRM.util.remind("1", $t("设置成功"));
                    let allConditions = await this.initView();
                    this.$emit('change', allConditions);
                }
            })
        },
        submitSetting(data) {
			return new Promise((resolve, reject) => {
                CRM.util.showLoading_new();
				CRM.util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/attribute/service/attributeUserRelate',
					data: data,
					success: function (res) {
                        CRM.util.hideLoading_new();
						if (res.Result.StatusCode === 0) {
							resolve(res.Value)
							return
						}
						CRM.util.alert(res.Result.FailureMessage);
					},
                    error(res) {
                        CRM.util.hideLoading_new();
                        reject(res.Result && res.Result.FailureMessage);
                    }
				}, {
					errorAlertModel: 1,
				});
			})
		},
        /**
         * 获取筛选条件
         */
         getParam() {
            let param = null;
            if (this.attribute_constraint_id) {
                param = this.getPriceQuoterConditions();
            } else {
                param = this.getAllConditions()
            }
            return param;
        }
    },
    destroyed() {
        console.log('categoryAttributeSetting destroyed')
    },
}
</script>
<style lang="less" scoped>
.category-attribute-wrapper {
    .category-attribute-content {
        .attribute-item-box {
            
            .attribute-item {
                padding: 8px 0 0;
                border: none;
                &:not(:last-child) {
                    border-bottom: 1px dotted var(--color-neutrals05);
                }
                .btn-multi {
                    display: flex;
                    align-items: center;
                    .fx-switch {
                        margin-left: 4px;
                    }
                }
                .btn-more {
                    display: none;
                }
            }
        }
    }
}
    .group-item {
        &:not(:last-child) {
            margin-bottom: 8px;
        }
        .group-name {
            cursor: pointer;
            height: 20px;
            line-height: 20px;
            padding: 4px;
            color: var(--color-neutrals19, #181C25);
            font-size: 14px;
            border-radius: 4px;
            background: var(--color-special01, #F2F4FB);
            
        }
        .attribute-item-box {
            padding-left: 8px;
            padding-top: 8px;
        }
    }
    
</style>