export function formatScheduleLayout(data,opts={}) {
  const meetingFields = ['interactive_scene', 'meeting_target', 'related_object_data'];
  const defaultBlackFields = ['meeting_platform','subject','remind_setting', 'repeat_setting', 'is_all_day', 'is_private']
  const WHITEFields = opts.whiteList?.length ? [...opts.whiteList] : null
  const BLACKFields = opts.blackList?.length ? [...opts.blackList] : [...defaultBlackFields]
  // 创建新的 "会议设置" 分组
  const newGroup = {
    "field_section": [{
      "show_header": true,
      "form_fields": [],
      "api_name": "meeting_setting_section__c",
      "tab_index": "ltr",
      "column": 1,
      "header": $t('sfa.gf.activity.meeting_setting'),
      "is_show": true  // 确保设为 true
    }]
  };

  // 遍历所有分组
  data.layout.components.forEach(component => {
    if (component.field_section) {
      component.field_section.forEach(section => {
        // 处理白名单中的字段
        WHITEFields && (section.form_fields = section.form_fields.filter(field => WHITEFields.includes(field.field_name)));
        // 处理黑名单中的字段
        section.form_fields = section.form_fields.filter(field => !BLACKFields.includes(field.field_name));
        // 提取要移动的字段
        const movedFields = section.form_fields.filter(field => meetingFields.includes(field.field_name));
        
        // 过滤掉这些字段
        section.form_fields = section.form_fields.filter(field => !meetingFields.includes(field.field_name));

        // 如果当前分组为空，则标记为需要删除
        if (section.form_fields.length === 0) {
          section._toBeRemoved = true;
        }

        // 将提取的字段添加到新分组
        if (movedFields.length > 0) {
          newGroup.field_section[0].form_fields.push(...movedFields);
        }
      });

      // 添加新的 "会议设置" 分组
      component.field_section.unshift(newGroup.field_section[0]);

      // 移除空分组
      component.field_section = component.field_section.filter(section => !section._toBeRemoved);
    }
  });

  return data;
}

export default {
  formatScheduleLayout
};