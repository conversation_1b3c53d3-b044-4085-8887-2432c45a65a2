/**
 * 创建会议
 *
 * @param {Object} params - 请求参数
 * @param {string} params.objectId - ID
 * @param {string} params.objectApiName - 入口对象apiName
 * @param {string} params.meetingType - 会议类型，可选值为 "tencent_scheduled"（预定会议）或 "tencent_quick"（快速会议）
 * @param {string} params.createUserName - 创建人
 * @param {Object} [params.data] - 日程新建的数据格式，快速会议不需要此值
 * @returns {Promise<Object>} - 返回一个 Promise，解析为包含会议 URL 或授权 URL 的对象
 * @example
 * createMeeting({
 *   objectId: 'xxxId',
 *   objectApiName: 'activity_meeting',
 *   meetingType: 'tencent_scheduled',
 *   createUserName: '张三李四王五',
 *   data: {
 *     // 日程新建的数据格式
 *   }
 * }).then(response => {
 *   console.log(response.url);
 * }).catch(error => {
 *   console.error(error);
 * });
 */
export function createMeeting(params) {
    return CRM.util.ajax_base('/EM1HNCRM/API/v1/object/activity_meeting/service/creating_meeting', {...params});
}
export default {createMeeting}