<template>
  <div>
    <div class="fields-box" v-loading="!showButton">
      <div ref="fieldsWrapper"></div>
    </div>
    <div class="footer-box" v-show="showButton">
      <fx-button type="primary" size="small" @click="handleConfirm">{{ confirmText }}</fx-button>
      <fx-button @click="handleClose" size="small">{{ $t('取消') }}</fx-button>
    </div>
  </div>
</template>

<script>
// http://localhost/XV/UI/Home#objdetail/AccountObj/6858f94ded27860001147637
import { requireField } from '@common/require';
import { createMeeting } from '../apis/serverApis.js';
import scheduled from "../assets/yuyuehuiyi.svg";
import quick from "../assets/kuaisuhuiyi.svg";
import TopIcon from "../assets/topicon.svg";
import { formatScheduleLayout } from '../apis/localApis.js';
export default {
  components: {
    // 注册所有使用的组件
    TopIcon
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    },
    meetingType: {
      type: String,
      default: ''
    },
    confirmText: {
      type: String,
      default: $t('确定')
    }
  },
  data() {
    return {
      showButton: false,
    }
  },
  
  computed: {
    baseCommonData() {
      const { describe_api_name, id } = this.params?.data?.related_object_data?.[0] || {};
      return {
        objectId: id,
        objectApiName: describe_api_name,
        meetingType: this.meetingType === 'tencent_quick' ? 1 : 0,
        meetingCategory: 'tencent',
        createUserName: CRM.curEmpName,
        data: null
      };
    },
    whiteList() {
      if(this.meetingType === 'tencent_quick') {
        return ['interactive_scene', 'meeting_target', 'related_object_data'];
      }
      return null;
    }
  },
  mounted() {
    this.initBaseView();
  },
  methods: {
    initBaseView() {
      this.initScheduledForm().then(viewData => {
        this.bkViewData = viewData;
      });
    },
    handleClose() {
      this.$emit('close',null);
    },
    handleSelectType(item) {
      if(this.meetingType === item.id) return;
      this.meetingType = item.id;
      this.initScheduledForm().then(viewData => {
        this.bkViewData = viewData;
      });
    },
    handleConfirm() {
      const formSubmitData = this.bkViewData.view.collect();
      const hasValidate = this.bkViewData.view.validate()
      if (!hasValidate) {
        return;
      }
      const baseData = {
        subject: this.getDefaultSubject(),
        meetingPlatform: 'tencentMeeting',
      }
      const object_data = Object.assign(baseData, this.bkViewData.view.model.get('data'));
      const seriesId = this.bkViewData.view.model.get('requestId');
      console.log(this.bkViewData);
      const data = {
        object_data,
        seriesId,
        "maskFieldApiNames": {
          "ScheduleObj": []
        },
        "fillMultiLang": true,
        "optionInfo": {
          "supportValidationResult": true
        }
      }
      const { describe_api_name, id } = this.params.data.related_object_data[0]
      const params = {
        ...this.baseCommonData,
        data,
      }
      createMeeting(params).then(res => {
        if (res.url) {
          window.open(res.url, '_blank');
        } else {
          this.params.methods.success && this.params.methods.success();
        }
        this.handleClose();
      });
    },
    // 重构后的 initScheduledForm 方法
    async initScheduledForm(params = this.params) {
      try {
        const field = await requireField();
        const meetingView = this.createMeetingView(field, params);
        const instance = new meetingView(params);
        return instance;
      } catch (error) {
        console.error(error);
        throw error;
      }
    },

    // 创建会议视图类的工厂方法
    createMeetingView(field, params) {
      const $vm = this;
      const $wrapper = this.$refs.fieldsWrapper;
      
      return Backbone.View.extend({
        initialize(options) {
          this.model = new field.Model(options);
          this.initMeetingForm(options.data);
        },

        initMeetingForm(data) {
          const viewOptions = this.buildViewOptions(data);
          const View = field.View.extend({});
          this.view = new View(viewOptions);
          this.view.render();
        },

        buildViewOptions(data) {
          const apiName = this.model.get('apiName');
          const baseOptions = this.getBaseOptions(data, apiName);
          const modelOptions = this.getModelOptions();
          
          const options = Object.assign(
            {},
            this.model.attributes,
            modelOptions,
            baseOptions
          );

          if (data.isEdit) {
            options.dataId = data._id;
            options.version = data.version;
          }

          return options;
        },

        getBaseOptions(data, apiName) {
          return {
            el: $wrapper,
            apiname: apiName,
            record_type: data.record_type,
            is_all_day: false,
            is_private: false,
            subject: $vm.getDefaultSubject(),
            include_detail_describe: true,
            Model: $vm.createCustomModel(field),
            data: {
              ...this.model.get('data'),
              subject: $vm.getDefaultSubject()
            },
            layout_type: 'add'
          };
        },

        getModelOptions() {
          return {
            viewType: 'single',
            isWM: true,
            subject: $vm.getDefaultSubject(),
            __type__: 'add'
          };
        }
      });
    },

    // 创建自定义模型
    createCustomModel(field) {
      const $vm = this;
      return field.Model.extend({
        parseDescribe: function (res) {
          console.log(res, 'parseDescribe')
          res = formatScheduleLayout(res, { whiteList: $vm.whiteList });
          // 先按照 DEFAULT_FIELDS 顺序重新排列所有字段
          // res.layout.components = self._reorderFields(res);
          // 注意：这里不调用 _toggleFields，因为 DOM 还没有渲染
          // 字段显示控制会在表单渲染完成后执行
          field.Model.prototype.parseDescribe.apply(this, arguments);
        },
        afterFetchSuccess(res) {
          console.log(res, 'afterFetchSuccess');
          $vm.showButton = true;
        }
      });
    },

    // 获取默认主题
    getDefaultSubject() {
      return 'onlineMeeting';
    },
    formatFields(components) {
      let newComponents = JSON.parse(JSON.stringify(components));
      newComponents.forEach(item => {
        if(item.api_name === "form_component"){
          item.field_section.forEach(item_sub=>{
            item_sub.show_header = true;
          })
        }
      })

      return newComponents
    },
  },
}
</script>
<style scoped lang="less">

.fields-box {
  min-height: 235px;
  .crm-action-nfield {
    padding: 0;
  }
}

.footer-box {
  display: flex;
  justify-content: flex-end;
  padding: 8px 24px 10px 24px;
  border-top: 1px solid var(--color-neutrals05, #dee1e8);
}

.warn-tip {
  display: flex;
  align-items: center;
  background: #fff7e6;
  border-radius: 4px;
  padding: 8px 16px;
  margin: 24px 24px 0 24px;
  font-size: 14px;
  i {
    margin-right: 8px;
    font-size: 16px;
  }
}

.meeting-setting-title {
  font-size: 16px;
  font-weight: 700;
  color: #333;
  margin: 24px 0 8px 24px;
}
</style>
