<template>
  <fx-dialog :visible.sync="visible" size="small2" :title="$t('sfa.gf.tencent_meeting')"
    custom-class="activity-meeting-dialog sfa-meeting-dialog" style="padding:0" @close="handleClose">
    <template #title>
      <div class="select-meeting-type">
        <span class="select-meeting-type-title">{{ $t('sfa.gf.create_tencent_meeting') }}</span>
      </div>
    </template>
    <div class="select-type-box" :class="[meetingType]">
      <div v-for="(item, index) in meetingMapsLists" :key="index" class="select-type-item"
        :class="{ 'active': meetingType === item.id }" @click="handleSelectType(item)">
        <img :src="item.icon" alt="">
        <div class="type-desc">
          {{ item.type }}
        </div>
        <div class="right-top-box">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M0 0H21C22.6569 0 24 1.34315 24 3V24L0 0Z" fill="#FF8000"/>
            <path d="M15.0296 11.1305L11.5906 7.52812L12.5281 6.59062L15.0296 8.31802L20.5618 4.15186L21.4993 5.08936L15.0296 11.1305Z" fill="white"/>
          </svg>
        </div>
      </div>
    </div>
    <div class="content-box" v-for="(item, index) in meetingMapsLists" :key="index" v-show="meetingType === item.id">
      <FormContentBox 
        :key="item.id" 
        :params="params" 
        :meetingType="item.id"
        :confirmText="item.confirmText"
        @close="handleClose"
      />
    </div>
  </fx-dialog>
</template>

<script>
import FormContentBox from './components/FormContentBox.vue';
import { requireField } from '@common/require';
import { createMeeting } from './apis/serverApis.js';
import scheduled from "./assets/yuyuehuiyi.svg";
import quick from "./assets/kuaisuhuiyi.svg";
import { formatScheduleLayout } from './apis/localApis.js';
export default {
  components: {
    // 注册所有使用的组件
    FormContentBox,
  },
  props: {
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      visible: true,
      meetingMapsLists: [
        {
          id: 'tencent_quick',
          icon: quick,
          type: this.$t('sfa.gf.quick_meeting'),
          confirmText: this.$t('创建')
        },
        {
          id: 'tencent_scheduled',
          icon: scheduled,
          type: this.$t('sfa.gf.scheduled_meeting'),
          confirmText: this.$t('sfa.gf.schedule')
        },
      ],
      showButton: false,
      meetingType: '',
    }
  },
  
  computed: {
  },
  mounted() {
    this.handleSelectType(this.meetingMapsLists[0]);
  },
  methods: {
    handleClose() {
      this.visible = false;
      this.$destroy();
    },
    handleSelectType(item) {
      if(this.meetingType === item.id) return;
      this.meetingType = item.id;
    }
  },
}
</script>
<style lang="less">
.activity-meeting-dialog,.sfa-meeting-dialog {
  border-radius: var(--16, 16px);
  border: 2px solid var(--color-special01);
  background: linear-gradient(221deg, rgba(255, 255, 255, 0.20) 79.4%, rgba(151, 106, 235, 0.20) 134.36%), linear-gradient(27deg, var(--color-neutrals01) 72.28%, #5A6FE9 213.37%);
  .el-dialog__header {
    padding: 22px 24px;
  }
  .el-dialog__body {
    padding-top: 0;
    padding-left: 0;
    padding-right: 0;
    padding-bottom: 0;
  }
  .crm-action-nfield {
    padding: 24px;
    padding-right: 44px;
  }
}
</style>
<style scoped lang="less">
.select-meeting-type-title {
  font-size: 24px;
  font-weight: 700;
  line-height: 30px;
}
.select-type-box {
  display: flex;
  justify-content: center;
}

.select-type-item {
  position: relative;
  display: flex;
  width: 160px;
  height: 160px;
  border-radius: 12px;
  margin: 25px 50px;
  margin-bottom: 0;
  box-sizing: border-box;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: var(--color-special01);
  cursor: pointer;
  .right-top-box {
    display: none;
    position: absolute;
    top: 0;
    right: 0;
  }
  &.active,
  &:hover {
    border: 2px solid var(--color-neutrals05);
  }
  &.active {
    .right-top-box {
      display: flex;
    }  
  }

  .type-desc {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    text-align: center;
    margin-top: 12px;
  }
}
</style>
