/*
 * @Descripttion: 语音转文字基类
 * @Author: LiAng
 * @Date: 2025-04-01 14:58:12
 * @LastEditors: LiAng
 * @LastEditTime: 2025-08-07 17:14:40
 */

/**
 * WebSocket配置参数说明
 * @typedef {Object} WSConfig
 * @property {string} objectId - 销售记录 ID，用于标识需要进行实时处理的销售记录
 * @property {string} sourceLanguage - 音视频的语言类型
 *   - 单语种可配置: cn（中文）、en（英文）、yue（粤语）、ja（日语）、ko（韩语）
 *   - 多语种可设置为: multilingual，并结合其他参数根据实际情况识别对应语种文本
 * @property {boolean} translationEnabled - 是否开启翻译功能
 *   - true: 开启翻译
 *   - false: 关闭翻译
 * @property {string} targetLanguages - 翻译的目标语言（当翻译功能开启时必填）
 *   - 可设置的目标语言包括: cn（中文）、en（英语）、ja（日语）、ko（韩语）、de（德语）、fr（法语）、ru（俄语）
 * @property {boolean} diarizationEnabled - 是否开启说话人分离功能
 *   - true: 在语音识别过程中区分不同说话人
 *   - false: 不区分说话人
 */
class Base {
    constructor(options = {}) {
        this.options = options;
        this.objectId = options.objectId; // 对象ID
        this.objectApiName = options.objectApiName; // 对象API名称
        this.wsConfig = options.wsConfig; // WebSocket配置
        this.onMessage = options.onMessage; // 消息回调
        this.onError = options.onError; // 错误回调
        this.onReconnect = options.onReconnect; // 重连回调
        this.beforeSaveResult = options.beforeSaveResult; // 保存结果前回调
        this.wsUrl = ''; // WebSocket URL
        this.taskId = ''; // 任务ID
        this.identifyList = []; // 识别结果列表
        this.identifyCacheNum = 1; // 识别结果缓存数量
        this.identifySaveDelay = 5000; // 识别结果保存延迟时间
        this.identifySaveTimer = null; // 识别结果保存定时器
        this.identifyCanSave = true; // 识别结果是否可以保存
        this.maxReconnectCount = 3; // 最大重连次数
        this.reconnectCount = 0; // 重连次数
        this.connectCount = 0; // 连接次数
        this.ws = null; // WebSocket实例
        this.connectTimer = null; // 连接定时器
        this.reconnectTimer = null; // 重连定时器
        this.disconnectTimer = null; // 断开连接定时器
    }

    /**
     * 开始识别
     * @param success 成功回调
     * @param error 错误回调
     */
    start({success, error}) {
        throw new Error('Method start() must be implemented');
    }

    /**
     * 停止识别
     * @param success 成功回调
     */
    stop({success, error}) {
        throw new Error('Method stop() must be implemented');
    }

    /**
     * 结束识别
     * @param success 成功回调
     * @param error 错误回调
     */
    finish({success, error}) {
        throw new Error('Method finish() must be implemented');
    }

    /**
     * 继续识别
     * @param success 成功回调
     * @param error 错误回调
     */
    continue({success, error}) {
        throw new Error('Method continue() must be implemented');
    }

    /**
     * 暂停识别
     * @param success 成功回调
     * @param error 错误回调
     */
    pause({success, error}) {
        throw new Error('Method pause() must be implemented');
    }

    /**
     * 发送音频数据
     * @param {ArrayBuffer} audioData 音频数据
     */
    sendAudioData(audioData) {
        throw new Error('Method sendAudioData() must be implemented');
    }

    /**
     * 格式化index
     * @param index 
     * @returns {String} 格式化后的index
     */
    formatIndex(index) {
       return Number(`${this.connectCount}${String(index).padStart(5, '0')}`);
    }

    /**
     * 保存识别结果
     * @param forceSave 是否强制保存
     */
    saveIdentifyResult(forceSave) {
        return new Promise(async (resolve, reject) => {
            /**
             * 可保存的场景
             * 1.有缓存数据，且缓存数据量大于设置的可缓存数量，且可以保存（满足接口调用间隔时间）
             * 2.有缓存数据，且强制保存
             */
            if (this.identifyList.length && ((this.identifyList.length >= this.identifyCacheNum && this.identifyCanSave) || forceSave)) {
                let identifyList;

                if (forceSave || !this.wsConfig.translationEnabled) {
                    identifyList = this.identifyList;
                }
                else {
                    identifyList = this.identifyList.filter(item => item.translateContent);
                }

                if (forceSave || identifyList.length >= this.identifyCacheNum) {
                    this.identifyList = this.identifyList.filter(item => identifyList.every(cItem => cItem.message_id !== item.message_id));

                    if (this.beforeSaveResult) {
                        identifyList = await this.beforeSaveResult(identifyList);
                    }

                    this.identifyCanSave = false;

                    CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/activity_text/service/save_meeting_doc_result",
                        data: {
                            objectId: this.objectId,
                            objectApiName: this.objectApiName,
                            taskId: this.taskId,
                            content: identifyList
                        },
                        success: (res) => {
                            if (res.Result.StatusCode === 0) {
                                if (typeof this.onMessage === 'function') {
                                    this.onMessage('identifyChange', {
                                        data: res.Value.dataList,
                                        fields: ['id', 'activityUserId']
                                    });
                                }
                                resolve();
                            }
                            else {
                                this.identifyList = ([]).concat(identifyList, this.identifyList);
                                resolve();
                            }
                        }
                    }, { errorAlertModel: 1 });

                    clearTimeout(this.identifySaveTimer);
                    this.identifySaveTimer = setTimeout(() => {
                        this.identifyCanSave = true;
                        if (this.identifyList.length) {
                            this.saveIdentifyResult();
                        }

                    }, this.identifySaveDelay);
                }
            }
            else {
                resolve();
            }
        })
    }

    /**
     * 毫秒转时分秒
     * @param ms 毫秒
     * @param showMilliseconds 是否保留毫秒
     */
    formatTime(ms, showMilliseconds = true) {
        if (typeof ms !== 'number' || ms < 0) {
            return '00:00:00';
        }

        const milliseconds = ms % 1000;
        const totalSeconds = Math.floor(ms / 1000);
        const seconds = totalSeconds % 60;
        const totalMinutes = Math.floor(totalSeconds / 60);
        const minutes = totalMinutes % 60;
        const hours = Math.floor(totalMinutes / 60);

        const pad = (num, size) => String(num).padStart(size, '0');

        let timeString = '';

        if (hours > 0) {
            timeString += `${pad(hours, 2)}:`;
        }

        timeString += `${pad(minutes, 2)}:${pad(seconds, 2)}`;

        if (showMilliseconds) {
            timeString += `.${pad(milliseconds, 3)}`;
        }

        return timeString;
    }

    destroy() {
        throw new Error('Method destroy() must be implemented');
    }
}

export default Base;