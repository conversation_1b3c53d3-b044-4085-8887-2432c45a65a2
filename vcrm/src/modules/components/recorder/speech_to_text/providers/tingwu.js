/*
 * @Descripttion: 听悟语音转文字
 * @Author: LiAng
 * @Date: 2025-04-01 14:58:12
 * @LastEditors: LiAng
 * @LastEditTime: 2025-08-07 17:17:42
 */
import Base from '../base';

class TingWu extends Base {
    constructor(options) {
        super(options); // 选项
    }

    /**
     * 开始识别
     * @param success 成功回调
     * @param error 错误回调
     */
    start({success, error}) {
        this.reconnectCount = 0;

        this.getWebSocketUrl().then(url => {
            if (url) {
                this.connectWebSocket({
                    url,
                    success: (ws) => {
                        if (typeof success === 'function') {
                            success(ws);
                        }
                    },
                    error: (err) => {
                        if (typeof error === 'function') {
                            error(err);
                        }
                    }
                });
            }
            else {
                if (typeof error === 'function') {
                    error();
                }
            }
        }).catch(err => {
            if (typeof error === 'function') {
                error(err);
            }
        });
    }

    /**
     * 停止识别
     * @param success 成功回调
     * @param error 错误回调
     */ 
    stop({success, error}) {
        this.disConnectWebSocket(success);
    }

    /**
     * 结束识别
     * @param success 成功回调
     * @param error 错误回调
     */
    finish({success, error}) {
        let isTimeout = false;

        CRM.util.waiting();

        this.finishTimer = setTimeout(() => {
            isTimeout = true;

            if (typeof error === 'function') {
                error();
            }

            CRM.util.waiting(false);

            this.onMessage && this.onMessage('log', {
                msg: 'recorder_stop_timeout',
                startTime: new Date().getTime()
            });
        }, 5000);

        Promise.all([this.saveEndRecord(), new Promise((resolve, reject) => {
            this.disConnectWebSocket(() => {
                this.saveIdentifyResult(true).then(() => {
                    resolve();
                }).catch(err => {
                    reject(err);
                });
            })
        })]).then(() => {
            clearTimeout(this.finishTimer);

            if (isTimeout) {
                return;
            }

            if (typeof success === 'function') {
                success();
            }

            CRM.util.waiting(false);
        }).catch(err => {
            clearTimeout(this.finishTimer);

            if (isTimeout) {
                return;
            }

            if (typeof error === 'function') {
                error(err);
            }

            CRM.util.waiting(false);
        });
    }

    /**
     * 继续识别
     * @param success 成功回调
     * @param error 错误回调
     */
    continue({success, error}) {
        return this.start({success, error});
    }

    /**
     * 暂停识别
     * @param success 成功回调
     * @param error 错误回调
     */
    pause({success, error}) {
        this.disConnectWebSocket(success);
    }

    /**
     * 获取WebSocket连接Url
     */
    getWebSocketUrl() {
        return new Promise((resolve, reject) => {
            if (this.wsUrl) {
                resolve(this.wsUrl);
                return;
            }

            const getUrlStartTime = new Date().getTime();

            CRM.util.FHHApi({
                url:"/EM1HNCRM/API/v1/object/activity_text/service/realtime_start",
                data: this.wsConfig,
                success: (res) => {
                    if (res.Result.StatusCode === 0) {
                        this.wsUrl = res.Value.wsUrl;
                        this.taskId = res.Value.taskId;
                        resolve(this.wsUrl);
                        this.sendLog({
                            msg: 'recorder_get_ws_url',
                            startTime: getUrlStartTime,
                            duration: new Date().getTime() - getUrlStartTime,
                            str1: this.taskId
                        });
                        return;
                    }
                    reject();
                },
                error: (err) => {
                    reject(err);
                }
            }, { errorAlertModel: 1 });
        });
    }

    /**
     * 建立WebSocket连接
     * @param url 
     * @param success 
     * @param error
     */
    connectWebSocket({url, success, error}) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            success && success(this.ws);
            return;
        }

        this.ws = new WebSocket(url);
        this.ws.binaryType = "arraybuffer"; //传输的是 ArrayBuffer 类型的数据
        this.ws.onopen = () => {
            clearTimeout(this.connectTimer);
            // 必须延迟执行，否则有一定概率发送开始指令失败，导致服务端关闭链接
            this.connectTimer = setTimeout(() => {
                if (this.ws.readyState === WebSocket.OPEN) {
                    this.connectCount++;
                    const params = {
                        header: {
                            name: "StartTranscription",
                            namespace: "SpeechTranscriber",
                        },
                        payload: {
                            format: "pcm"
                        },
                    };
                    this.ws.send(JSON.stringify(params));
                    success && success(this.ws);
                }
                else {
                    this.disConnectWebSocket(() => {
                        this.reconnectWebSocket({url, success: this.onReconnect, errorMessage: {status: this.ws.readyState}});
                    });
                }
            }, 100);
        };
        this.ws.onmessage = (msg) => {
            if (typeof msg.data === "string") {
                const dataJson = JSON.parse(msg.data);

                switch (dataJson.header.name) {
                    case "SentenceBegin": {
                        if (typeof this.onMessage === 'function') {
                            this.onMessage('identifyBegin', dataJson);
                        }
                        console.log('Start recognizing new sentences', dataJson);
                        break;
                    }
                    case "TranscriptionResultChanged": {
                        if (typeof this.onMessage === 'function') {
                            this.onMessage('identifying', this.formatData(dataJson));
                        }
                        console.log('In recognition', this.formatData(dataJson));
                        break;
                    }
                    case "SentenceEnd": {
                        let result = this.formatData(dataJson);

                        if (typeof this.onMessage === 'function') {
                            this.onMessage('identifyEnd', result);
                        }

                        this.identifyList.push(result._formatData);
                        this.saveIdentifyResult();

                        console.log('Identification completed', result);
                        break;
                    }
                    case "TranscriptionCompleted": {
                        if (typeof this.onMessage === 'function') {
                            this.onMessage('identifyComplete', dataJson);
                        }
                        this.saveIdentifyResult(true);
                        console.log('End recognition', dataJson);
                        break;
                    }
                    case "ResultTranslated": {
                        let result = this.translateHandle(dataJson);
                        if (typeof this.onMessage === 'function') {
                            this.onMessage('identifyTranslated', result);
                        }
                        this.saveIdentifyResult();
                        console.log('Translation completed', dataJson);
                        break;
                    }
                    case "TaskFailed": {
                        if (typeof this.onMessage === 'function') {
                            this.onMessage('identifyFailed', dataJson);
                        }
                        this.saveIdentifyResult(true);
                        this.disConnectWebSocket(() => {
                            this.reconnectWebSocket({url, success: this.onReconnect, errorMessage: {status: dataJson.header.status}});
                        })
                        console.log('Abort', dataJson);
                        break;
                    }
                    default: {
                        console.log(`Unknown message:`, dataJson.header.name, dataJson);
                    }
                }
            }
        };
        this.ws.onerror = (err) => {
            console.log(err);
        };
        this.ws.onclose = (event) => {
            if (this.ws) {
                clearTimeout(this.ws._closeTimer);

                if (this.ws._normal_close) {
                    const closeCb = this.ws._closeCb;
                    this.ws = null;
                    closeCb && closeCb();
                }
                else {
                    this.ws = null;
                    this.reconnectWebSocket({url, success: this.onReconnect, errorMessage: {status: event.code}});
                }
            }
        };

        // 主动关闭webSocket，增加正常关闭属性，关闭回调
        this.ws._close = (cb) => {
            clearTimeout(this.reconnectTimer);

            this.ws._normal_close = true;
            this.ws._closeCb = cb;
            this.ws._closeTimer = setTimeout(() => {
                if (this.ws && this.ws.readyState !== WebSocket.CLOSED && this.ws.readyState !== WebSocket.CLOSING) {
                    this.ws.close();
                } else {
                    this.ws = null;
                    cb && cb();
                }
            }, 1000)
        };
    }

    /**
     * 重连WebSocket
     * @param url 
     * @param success 
     * @param errorMessage 
     */
    reconnectWebSocket({url, success, errorMessage}) {
        clearTimeout(this.reconnectTimer);

        this.reconnectTimer = setTimeout(() => {
            if (this.reconnectCount >= this.maxReconnectCount) {
                if (typeof this.onError === 'function') {
                    this.onError(errorMessage);
                }

                this.reconnectCount = 0;

                return;
            }

            this.reconnectCount++;
            this.connectWebSocket({
                url,
                success: (ws) => {
                    this.reconnectCount = 0;

                    if (typeof success === 'function') {
                        success(ws);
                    }
                }
            });
        }, 1000);
    }

    /**
     * 断开WebSocket连接
     * @param cb 回调函数
     */
    disConnectWebSocket(cb) {
        if (this.ws) {
            clearTimeout(this.connectTimer);
            clearTimeout(this.reconnectTimer);
            clearTimeout(this.disconnectTimer);

            // 等待音频上传完毕
            this.disconnectTimer = setTimeout(() => {
                const params = {
                    header: {
                        name: "StopTranscription",
                        namespace: "SpeechTranscriber",
                    },
                    payload: {},
                };

                try {
                    if (this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify(params));
                        this.ws._close(cb);
                    }
                    else {
                        this.ws._close(cb);
                    }
                } catch (error) {
                    console.log('Error disconnecting WebSocket:', error);
                    this.ws && this.ws._close(cb);
                }
            }, 1000);
        }
        else {
            cb && cb();
        }
    }

    /**
     * 处理翻译结果
     * @param data 
     */
    translateHandle(data) {
        let { header, payload } = data;
        let translateStr = '';
        let translatePartialStr = '';
        let obj = this.identifyList.find(item => item.message_id === header.source_message_id) || {};

        if (payload.translate_result && payload.translate_result.length) {
            payload.translate_result.forEach(item => {
                if (item.partial) {
                    translatePartialStr = item.text;
                }
                else {
                    translateStr = item.text;
                }
            })
        }

        obj.translateContent = translateStr;
        obj.translatePartialContent = translatePartialStr;

        return obj;
    }

    /**
     * 格式化数据
     * @param data 
     */
    formatData(data) {
        let { header, payload } = data;
        let speaker_id = payload?.speaker_id || '1'
        let _formatData = {
            id: this.formatIndex(payload.index),
            seq: this.formatIndex(payload.index),
            ui: speaker_id,
            st: this.formatTime(payload.begin_time),
            et: this.formatTime(payload.time),
            content: payload.result,
            translateContent: '',
            message_id: header.message_id,
            // 新增参数，用于处理用户头像背景色
            nameAvaId: Number(speaker_id) - 1,
            isDefaultSpeaker: true
        };

        return Object.assign(data, {
            _formatData
        });
    }

    /**
     * 保存结束记录
     */
    saveEndRecord() {
        return new Promise((resolve, reject) => {
            CRM.util.FHHApi({
                url:"/EM1HNCRM/API/v1/object/activity_text/service/realtime_stop",
                data: {
                    objectId: this.objectId,
                    taskId: this.taskId
                },
                complete: (res) => {
                    this.onMessage && this.onMessage('log', {
                        msg: 'realtime_stop',
                        startTime: new Date().getTime(),
                        str1: res.responseText
                    });
                    resolve();
                }
            }, { errorAlertModel: 1 });
        })
    }

    /**
     * 发送日志
     * @param data 
     */
    sendLog(data) {
        if (typeof this.onMessage === 'function') {
            this.onMessage('log', data);
        }
    }

    /**
     * 发送音频数据
     * @param {ArrayBuffer} audioData 音频数据
     */
    sendAudioData(audioData) {
        const data = new Int8Array(audioData);

        if (data.length > 0) {
            const chunkSize = 2048;

            for (let i = 0; i < data.length; i += chunkSize) {
                const chunk = data.slice(i, Math.min(i + chunkSize, data.length));
                try {
                    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(chunk);
                    }
                } catch (error) {
                    console.log('Sending audio data failed:', error);
                    break;
                }
            }
        }
    }

    /**
     * 销毁实例
     */
    destroy() {
        // 清除WebSocket关闭定时器
        if (this.ws && this.ws._closeTimer) {
            clearTimeout(this.ws._closeTimer);
            this.ws._closeTimer = null;
        }

        // 清除重连定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        // 清除连接定时器
        if (this.connectTimer) {
            clearTimeout(this.connectTimer);
            this.connectTimer = null;
        }

        // 清除断开连接定时器
        if (this.disconnectTimer) {
            clearTimeout(this.disconnectTimer);
            this.disconnectTimer = null;
        }

        // 清除识别结果保存定时器
        if (this.identifySaveTimer) {
            clearTimeout(this.identifySaveTimer);
            this.identifySaveTimer = null;
        }
    }
}


export default TingWu;