/*
 * @Descripttion: 腾讯云实时语音识别
 * @Author: starlee
 * @Date: 2025-07-29 10:00:00
 * @LastEditors: LiAng
 * @LastEditTime: 2025-07-02 17:56:33
 */
import Base from '../base';
import CryptoJS from 'crypto-js';

// 将 wordArray 转为 Uint8Array
function toUint8Array(wordArray) {
    const { words, sigBytes } = wordArray;
    const u8 = new Uint8Array(sigBytes);
    for (let i = 0; i < sigBytes; i++) {
        u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    }
    return u8;
}

// 将 Uint8Array 转为 String
function uint8ArrayToString(fileData) {
    let dataString = '';
    for (let i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i]);
    }
    return dataString;
}

// 签名函数
function signCallback(secretKey, signStr) {
    const hash = CryptoJS.HmacSHA1(signStr, secretKey);
    const bytes = uint8ArrayToString(toUint8Array(hash));
    return window.btoa(bytes);
}

const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

class Tencent extends Base {
    constructor(options) {
        super(options);
        // 从wsConfig中获取腾讯云必要的信息，需要外部传入
        this.secretId = this.wsConfig.secretId || 'AKID2uc9TZWHzXYO1SffT4SbWDeqoY4LTOGy';
        this.secretKey = this.wsConfig.secretKey || 'Xs2ziEXelkbaOxKTOBi15jCbqvpeJky8';
        this.appId = this.wsConfig.appId || '**********';
    }

    start({ success, error }) {
        this.reconnectCount = 0;
        this.getWebSocketUrl().then(url => {
            if (url) {
                this.connectWebSocket({ url, success, error });
            } else {
                error && error();
            }
        }).catch(err => {
            error && error(err);
        });
    }

    stop({ success }) {
        console.log('[Tencent Provider] stop() method called.');
        this.disConnectWebSocket(success);
    }
    
    finish({success, error}) {
        this.disConnectWebSocket(() => {
            this.saveIdentifyResult(true).then(() => {
                this.saveEndRecord().then(() => {
                    if (typeof success === 'function') {
                        success();
                    }
                }).catch(err => {
                    if (typeof error === 'function') {
                        error(err);
                    }
                });
            }).catch(err => {
                if (typeof error === 'function') {
                    error(err);
                }
            });
        });
    }
    
    pause({success, error}) {
        this.stop({success});
    }

    continue({success, error}) {
        return this.start({success, error});
    }

    async getWebSocketUrl() {
        // 腾讯云实时语音识别参数，详见: https://cloud.tencent.com/document/product/1093/48982
        const params = {
            secretid: this.secretId,
            secretkey: this.secretKey,
            appid: this.appId,
            voice_id: guid(),
            voice_format: 1, // pcm
            needvad: 1, // 开启语音活动检测，用于服务器判断句末
        };

        // 根据 wsConfig 动态设置腾讯云参数
        // 经过反复测试，我们得出结论：当前实时语音识别(WebSocket)接口不支持 speaker_diarization 参数。
        // 虽然API会返回具体的模型不兼容错误，但这似乎是API网关层的行为，该功能本身并未在实时接口中开放。
        // 因此，此处移除所有 speaker_diarization 相关逻辑，以确保核心识别功能稳定。

        // 优先使用 wsConfig 中直接指定的 engine_model_type
        if (this.wsConfig.engine_model_type) {
            params.engine_model_type = this.wsConfig.engine_model_type;
        } else {
            // 映射 sourceLanguage 到 engine_model_type
            // 腾讯支持: 16k_zh, 16k_en, 16k_ca(粤语), 16k_ja, 16k_ko 等
            switch (this.wsConfig.sourceLanguage) {
                case 'cn':
                    // 怀疑 16k_zh_large 模型需要独立的资源包，暂时切换回标准模型进行测试
                    params.engine_model_type = '16k_zh';
                    break;
                case 'en':
                    // 怀疑 16k_zh_large 模型需要独立的资源包，暂时切换回标准模型进行测试
                    params.engine_model_type = '16k_en';
                    break;
                case 'yue':
                    params.engine_model_type = '16k_ca';
                    break;
                case 'ja':
                    params.engine_model_type = '16k_ja';
                    break;
                case 'ko':
                    params.engine_model_type = '16k_ko';
                    break;
                default:
                    // 默认也使用标准模型
                    params.engine_model_type = '16k_zh';
                    break;
            }
        }
        
        // 1. 创建查询字符串
        const query = {
            ...params
        };
        const time = new Date().getTime();
        const serverTime = Math.round(time / 1000); // 使用本地时间模拟服务器时间
        query['timestamp'] = serverTime;
        query['expired'] = serverTime + 24 * 60 * 60;
        query['nonce'] = Math.round(time / 100000);

        delete query.secretkey;
        delete query.appid;
        
        // 2. 格式化签名字符串
        let strParam = "";
        let signStr = "asr.cloud.tencent.com/asr/v2/";
        if(this.appId){
            signStr += this.appId;
        }
        const keys = Object.keys(query).sort();
        for (let i = 0, len = keys.length; i < len; i++) {
            strParam += `&${keys[i]}=${query[keys[i]]}`;
        }
        const finalSignStr = `${signStr}?${strParam.slice(1)}`;

        // 3. 计算签名
        const signature = signCallback(this.secretKey, finalSignStr);
        
        // 4. 构建URL
        this.wsUrl = `wss://${finalSignStr}&signature=${encodeURIComponent(signature)}`;
        return this.wsUrl;
    }

    connectWebSocket({ url, success, error }) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            success && success(this.ws);
            return;
        }
        
        this.ws = new WebSocket(url);
        this.ws.onopen = () => {
            this.connectCount++;
            success && success(this.ws);
        };

        this.ws.onmessage = (e) => {
            const response = JSON.parse(e.data);
            if (response.code !== 0) {
                this.onError && this.onError(response);
                this.ws.close();
                return;
            }

            if (response.final === 1) {
                this.onMessage && this.onMessage('identifyComplete', this.formatData(response));
                this.disConnectWebSocket();
                return;
            }
            
            if (response.result) {
                const { slice_type } = response.result;
                const formattedData = this.formatData(response);
                
                if (slice_type === 0) { // 一句话开始
                    this.onMessage && this.onMessage('identifyBegin', formattedData);
                } else if (slice_type === 2) { // 一句话结束
                    this.onMessage && this.onMessage('identifyEnd', formattedData);

                    if (formattedData._formatData) {
                        this.identifyList.push(formattedData._formatData);
                    }
                    
                    // 句子结束时保存识别结果
                    this.saveIdentifyResult(false);
                } else { // 识别中
                    this.onMessage && this.onMessage('identifying', formattedData);
                }
            }
        };

        this.ws.onerror = (err) => {
            this.onError && this.onError(err);
        };

        this.ws.onclose = (event) => {
            if (this.ws) {
                clearTimeout(this.ws._closeTimer);

                if (this.ws._normal_close) {
                    // 正常关闭：执行回调并清理
                    const closeCb = this.ws._closeCb;
                    this.ws = null;
                    closeCb && closeCb();
                }
                else {
                    // 异常关闭：尝试重连
                    this.ws = null;
                    this.reconnectWebSocket({
                        url: url,
                        errorMessage: {status: event.code}
                    });
                }
            }
        };

        // 自定义关闭方法，用于标记正常关闭
        this.ws._close = (cb) => {
            this.ws._normal_close = true;
            this.ws._closeCb = cb;
            this.ws._closeTimer = setTimeout(() => {
                if (this.ws && this.ws.readyState !== WebSocket.CLOSED && this.ws.readyState !== WebSocket.CLOSING) {
                    this.ws.close();
                } else {
                    this.ws = null;
                    cb && cb();
                }
            }, 1000);
        };
    }

    /**
     * 重连WebSocket
     * @param url 
     * @param success 
     * @param errorMessage 
     */
    reconnectWebSocket({url, success, errorMessage}) {
        clearTimeout(this.reconnectTimer);

        this.reconnectTimer = setTimeout(() => {
            if (this.reconnectCount >= this.maxReconnectCount) {
                if (typeof this.onError === 'function') {
                    this.onError(errorMessage);
                }

                this.reconnectCount = 0;
                return;
            }

            this.reconnectCount++;

            this.connectWebSocket({
                url,
                success: (ws) => {
                    this.reconnectCount = 0;

                    if (typeof success === 'function') {
                        success(ws);
                    }
                },
                error: (err) => {}
            });
        }, 1000);
    }
    
    formatData(data) {
        const { result, code, message, request_id } = data;
        if (code !== 0) {
            return {
                _formatData: data,
                error: message,
                code,
            };
        }
        
        const speaker_id = result.speaker_id ? String(result.speaker_id) : '1';

        const _formatData = {
            id: this.formatIndex(result.index),
            seq: this.formatIndex(result.index),
            ui: speaker_id,
            st: this.formatTime(result.start_time),
            et: this.formatTime(result.end_time),
            content: result.voice_text_str,
            translateContent: '',
            message_id: request_id,
            // 新增参数，用于处理用户头像背景色
            nameAvaId: Number(speaker_id) - 1,
            isDefaultSpeaker: true
        };
        
        return Object.assign(data, {
            _formatData
        });
    }

    disConnectWebSocket(cb) {
        if (this.ws) {
            // 清理所有定时器
            clearTimeout(this.connectTimer);
            clearTimeout(this.reconnectTimer);
            clearTimeout(this.disconnectTimer);

            // 等待音频上传完毕再关闭连接
            this.disconnectTimer = setTimeout(() => {
                try {
                    // 发送结束消息
                    if (this.ws.readyState === WebSocket.OPEN) {
                        this.ws.send(JSON.stringify({ type: 'end' }));
                        this.ws._close(cb);
                    }
                    else {
                        this.ws._close(cb);
                    }
                } catch (error) {
                    console.log('Error disconnecting WebSocket:', error);
                    this.ws && this.ws._close(cb);
                }
            }, 1000);
        }
        else {
            cb && cb();
        }
    }

    sendAudioData(audioData) {
        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(audioData);
        }
    }

    /**
     * 保存结束记录
     */
    saveEndRecord() {
        return new Promise((resolve, reject) => {
            CRM.util.waiting();

            CRM.util.FHHApi({
                url:"/EM1HNCRM/API/v1/object/activity_text/service/realtime_stop",
                data: {
                    objectId: this.objectId,
                    taskId: this.taskId
                },
                complete: (res) => {
                    CRM.util.waiting(false);
                    resolve();
                }
            }, { errorAlertModel: 1 });
        })
    }
    
    destroy() {
        // 清除WebSocket关闭定时器
        if (this.ws && this.ws._closeTimer) {
            clearTimeout(this.ws._closeTimer);
            this.ws._closeTimer = null;
        }

        // 清除重连定时器
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        // 清除连接定时器
        if (this.connectTimer) {
            clearTimeout(this.connectTimer);
            this.connectTimer = null;
        }

        // 清除断开连接定时器
        if (this.disconnectTimer) {
            clearTimeout(this.disconnectTimer);
            this.disconnectTimer = null;
        }

        this.stop({
            success: () => {
                console.log('Tencent provider destroyed');
            }
        });
        this.ws = null;
        this.options = null;
    }
}

export default Tencent;
