/*
 * @Descripttion: AWS 语音转文字服务提供者
 * @Author: LiAng
 * @Date: 2025-05-20 10:17:11
 * @LastEditors: LiAng
 * @LastEditTime: 2025-07-02 17:57:23
 */
import { EventStreamMarshaller } from '@aws-sdk/eventstream-marshaller';
import { toUtf8, fromUtf8 } from '@aws-sdk/util-utf8-node';
import Base from '../base';

class AWSTranscribeProvider extends Base {
    // 语言代码映射表
    static LANGUAGE_MAP = {
        'cn': 'zh-CN',    // 中文
        'en': 'en-US',    // 英文
        'yue': 'zh-HK',   // 粤语
        'ja': 'ja-JP',    // 日语
        'ko': 'ko-KR',    // 韩语
        'de': 'de-DE',    // 德语
        'fr': 'fr-FR',    // 法语
        'ru': 'ru-RU',    // 俄语
        'multilingual': 'auto'  // 多语种
    };

    /**
     * 构造函数
     * @param {Object} options - 配置选项
     * @param {string} options.region - AWS 区域
     * @param {string} options.accessKeyId - AWS 访问密钥ID
     * @param {string} options.secretAccessKey - AWS 秘密访问密钥
     * @param {string} options.sessionToken - AWS 会话令牌
     * @param {Object} options.wsConfig - WebSocket配置
     * @param {string} options.wsConfig.sourceLanguage - 源语言
     * @param {boolean} options.wsConfig.translationEnabled - 是否启用翻译
     * @param {Array<string>} options.wsConfig.targetLanguages - 目标翻译语言
     * @param {boolean} options.wsConfig.diarizationEnabled - 是否启用说话人分离
     */
    constructor(options) {
        super(options);

        // AWS specific configuration
        this.awsConfig = {
            region: options.region || 'us-west-2',
            accessKeyId: options.accessKeyId,
            secretAccessKey: options.secretAccessKey,
            sessionToken: options.sessionToken,
            sampleRate: 16000
        };

        // 设置AWS的语言代码
        this.awsConfig.languageCode = AWSTranscribeProvider.LANGUAGE_MAP[this.wsConfig.sourceLanguage] || 'auto';

        // AWS Transcribe specific properties
        this.eventStreamMarshaller = new EventStreamMarshaller(toUtf8, fromUtf8);
        this.socket = null;
        this.heartbeatInterval = null;

        // 扩展识别结果的处理
        this.speakerLabels = this.wsConfig.diarizationEnabled || false;
        this.translateEnabled = this.wsConfig.translationEnabled || false;
        this.targetLanguages = this.wsConfig.targetLanguages || [];
        this.minProcessSize = 2048; // 最小处理大小
        
        // 设置服务器地址
        this.serverUrl = this.wsConfig.serverUrl;
        if (!this.serverUrl) {
            console.log('No serverUrl provided in wsConfig, using default localhost:3000');
            this.serverUrl = 'http://localhost:3000/aws_transcribe_presigned_url';
        }
    }

    /**
     * 开始语音识别
     * 创建WebSocket连接并开始接收音频数据
     * @param {Object} params - 参数对象
     * @param {Function} params.success - 成功回调函数
     * @param {Function} params.error - 错误回调函数
     */
    start({success, error}) {
        this.reconnectCount = 0;

        this.getWebSocketUrl().then(url => {
            if (url) {
                this.connectWebSocket({
                    url,
                    success: (ws) => {
                        if (typeof success === 'function') {
                            success(ws);
                        }
                    },
                    error: (err) => {
                        if (typeof error === 'function') {
                            error(err);
                        }
                    }
                });
            } else {
                if (typeof error === 'function') {
                    error(new Error('Failed to get presigned URL'));
                }
            }
        }).catch(err => {
            if (typeof error === 'function') {
                error(err);
            }
        });
    }

    /**
     * 停止语音识别
     * 断开WebSocket连接，停止接收音频数据
     * @param {Object} params - 参数对象
     * @param {Function} params.success - 成功回调函数
     * @param {Function} params.error - 错误回调函数
     */
    stop({success, error}) {
        this.disConnectWebSocket(success);
    }

    /**
     * 继续语音识别
     * 重新建立WebSocket连接，继续接收音频数据
     * @param {Object} params - 参数对象
     * @param {Function} params.success - 成功回调函数
     * @param {Function} params.error - 错误回调函数
     */
    continue({success, error}) {
        return this.start({success, error});
    }

    /**
     * 暂停语音识别
     * 临时断开WebSocket连接，但保持状态
     * @param {Object} params - 参数对象
     * @param {Function} params.success - 成功回调函数
     * @param {Function} params.error - 错误回调函数
     */
    pause({success, error}) {
        this.disConnectWebSocket(success);
    }

    /**
     * 完成语音识别
     * 结束识别过程，保存所有结果，并清理资源
     * @param {Object} params - 参数对象
     * @param {Function} params.success - 成功回调函数
     * @param {Function} params.error - 错误回调函数
     */
    finish({success, error}) {
        this.disConnectWebSocket(() => {
            this.saveIdentifyResult(true).then(() => {
                if (typeof success === 'function') {
                    success();
                }
            }).catch(err => {
                if (typeof error === 'function') {
                    error(err);
                }
            });
        });
    }

    /**
     * 建立WebSocket连接
     * 处理连接建立、消息接收、错误处理和连接关闭的逻辑
     * @param {Object} params - 参数对象
     * @param {string} params.url - WebSocket连接URL
     * @param {Function} params.success - 成功回调函数
     * @param {Function} params.error - 错误回调函数
     */
    connectWebSocket({url, success, error}) {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            success && success(this.socket);
            return;
        }

        this.socket = new WebSocket(url);
        this.socket.binaryType = "arraybuffer";

        this.socket.onopen = () => {
            clearTimeout(this.connectTimer);
            this.connectTimer = setTimeout(() => {
                if (this.socket.readyState === WebSocket.OPEN) {
                    this.connectCount++;
                    this.startHeartbeat();
                    success && success(this.socket);
                } else {
                    this.disConnectWebSocket(() => {
                        this.reconnectWebSocket({url, success: this.onReconnect, errorMessage: {status: this.socket.readyState}});
                    });
                }
            }, 100);
        };

        this.socket.onmessage = (message) => {
            try {
                const messageBuffer = Buffer.from(message.data);
                const messageWrapper = this.eventStreamMarshaller.unmarshall(messageBuffer);
                const messageBody = JSON.parse(Buffer.from(messageWrapper.body).toString());

                if (messageWrapper.headers[':message-type'].value === 'event') {
                    const eventType = messageWrapper.headers[':event-type'].value;
                    
                    switch (eventType) {
                        // TranscriptEvent: AWS 转录事件，包含语音识别的结果
                        // - 如果 IsPartial 为 true，表示这是实时识别的中间结果
                        // - 如果 IsPartial 为 false，表示这是一个完整句子的最终结果
                        case 'TranscriptEvent': {
                            const result = this.formatData(messageBody);
                            if (result) {
                                const isPartial = messageBody.Transcript.Results[0]?.IsPartial;
                                const alt = messageBody.Transcript.Results[0]?.Alternatives?.[0];
                                if (!isPartial) {
                                    if (typeof this.onMessage === 'function') {
                                        this.onMessage('identifyEnd', result);
                                    }
                                    this.identifyList.push(result._formatData);
                                    this.saveIdentifyResult();

                                    // 处理翻译结果（修正判断条件）
                                    if (
                                        this.translateEnabled &&
                                        alt &&
                                        alt.Translations &&
                                        alt.Translations.length > 0
                                    ) {
                                        const translationResult = this.translateHandle(messageBody);
                                        if (typeof this.onMessage === 'function') {
                                            this.onMessage('identifyTranslated', translationResult);
                                        }
                                        this.saveIdentifyResult();
                                    }
                                } else {
                                    if (typeof this.onMessage === 'function') {
                                        this.onMessage('identifying', result);
                                    }
                                }
                            }
                            break;
                        }
                        // 错误处理事件组：
                        // BadRequestException: 请求参数错误，如无效的语言代码、采样率等
                        // InternalFailureException: AWS 服务内部错误
                        // LimitExceededException: 超出服务限制，如并发连接数、请求频率等
                        // 这些错误发生时，会：
                        // 1. 通知上层应用识别失败
                        // 2. 保存当前已识别的结果
                        // 3. 断开当前连接
                        // 4. 尝试重新连接
                        case 'BadRequestException':
                        case 'InternalFailureException':
                        case 'LimitExceededException': {
                            if (typeof this.onMessage === 'function') {
                                this.onMessage('identifyFailed', messageBody);
                            }
                            this.saveIdentifyResult(true);
                            this.disConnectWebSocket(() => {
                                this.reconnectWebSocket({
                                    url: this.socket.url, 
                                    success: this.onReconnect, 
                                    errorMessage: {status: messageBody.Message}
                                });
                            });
                            break;
                        }
                        // 未知事件类型：记录日志以便调试
                        // AWS 可能会在未来添加新的事件类型，所以需要妥善处理未知类型
                        default: {
                            console.log(`Unknown event type: ${eventType}`, messageBody);
                        }
                    }
                }
            } catch (error) {
                console.error('Error processing message:', error);
            }
        };

        this.socket.onerror = (err) => {
            console.error('WebSocket error:', err);
            error && error(err);
        };

        this.socket.onclose = (closeEvent) => {
            this.stopHeartbeat();
            if (this.socket) {
                clearTimeout(this.socket._closeTimer);

                if (this.socket._normal_close) {
                    const closeCb = this.socket._closeCb;
                    this.socket = null;
                    closeCb && closeCb();
                } else {
                    this.socket = null;
                    this.reconnectWebSocket({url, success: this.onReconnect, errorMessage: {status: closeEvent.code}});
                }
            }
        };

        this.socket._close = (cb) => {
            clearTimeout(this.reconnectTimer);

            this.socket._normal_close = true;
            this.socket._closeCb = cb;
            this.socket._closeTimer = setTimeout(() => {
                if (this.socket && this.socket.readyState !== WebSocket.CLOSED && this.socket.readyState !== WebSocket.CLOSING) {
                    this.socket.close();
                } else {
                    this.socket = null;
                    cb && cb();
                }
            }, 1000);
        };
    }

    /**
     * 重新连接WebSocket
     * 在连接断开时尝试重新建立连接，有最大重试次数限制
     * @param {Object} params - 参数对象
     * @param {string} params.url - WebSocket连接URL
     * @param {Function} params.success - 成功回调函数
     * @param {Object} params.errorMessage - 错误信息对象
     */
    reconnectWebSocket({url, success, errorMessage}) {
        clearTimeout(this.reconnectTimer);

        this.reconnectTimer = setTimeout(() => {
            if (this.reconnectCount >= this.maxReconnectCount) {
                if (typeof this.onError === 'function') {
                    this.onError(errorMessage);
                }
                this.reconnectCount = 0;
                return;
            }

            this.reconnectCount++;
            this.connectWebSocket({
                url,
                success: (ws) => {
                    this.reconnectCount = 0;
                    if (typeof success === 'function') {
                        success(ws);
                    }
                }
            });
        }, 1000);
    }

    /**
     * 断开WebSocket连接
     * 安全地关闭WebSocket连接，确保所有资源正确释放
     * @param {Function} cb - 断开连接后的回调函数
     */
    disConnectWebSocket(cb) {
        if (this.socket) {
            clearTimeout(this.connectTimer);
            clearTimeout(this.reconnectTimer);
            clearTimeout(this.disconnectTimer);

            // 等待音频上传完毕
            this.disconnectTimer = setTimeout(() => {
                try {
                    // 先检查socket是否还存在，因为可能在timeout期间被清空
                    if (this.socket) {
                        if (this.socket.readyState === WebSocket.OPEN) {
                            const emptyMessage = this.getAudioEventMessage(Buffer.from([]));
                            const emptyBuffer = this.eventStreamMarshaller.marshall(emptyMessage);
                            this.socket.send(emptyBuffer);
                        }
                        // 确保_close方法存在
                        if (typeof this.socket._close === 'function') {
                            this.socket._close(cb);
                        } else {
                            // 如果_close不存在，直接关闭并调用回调
                            this.socket.close();
                            this.socket = null;
                            if (typeof cb === 'function') {
                                cb();
                            }
                        }
                    } else {
                        // socket已经被清空，直接调用回调
                        if (typeof cb === 'function') {
                            cb();
                        }
                    }
                } catch (error) {
                    console.error('Error disconnecting WebSocket:', error);
                    // 确保在发生错误时也能正确清理资源
                    if (this.socket) {
                        try {
                            this.socket.close();
                        } catch (e) {
                            console.log('Error closing socket:', e);
                        }
                        this.socket = null;
                    }
                    if (typeof cb === 'function') {
                        cb();
                    }
                }
            }, 1000);
        } else {
            // socket不存在，直接调用回调
            if (typeof cb === 'function') {
                cb();
            }
        }
    }

    /**
     * 格式化识别数据
     * 将AWS返回的数据格式化为统一的数据结构
     * @param {Object} messageBody - AWS返回的原始消息体
     * @returns {Object|null} 格式化后的数据对象，如果数据无效则返回null
     */
    formatData(messageBody) {
        if (!messageBody.Transcript?.Results?.length || !messageBody.Transcript.Results[0]?.Alternatives?.length) {
            return null;
        }

        const result = messageBody.Transcript.Results[0];
        const alternative = result.Alternatives[0];

        // 获取说话人信息
        let speakerId = '1';
        if (this.speakerLabels && !result.IsPartial && alternative.Items) {
            const speakerItems = alternative.Items.filter(item => item.Speaker !== undefined);
            if (speakerItems.length > 0) {
                speakerId = String(speakerItems[speakerItems.length - 1].Speaker);
            }
        }

        // 获取翻译结果
        let translateContent = '';
        let translatePartialContent = '';
        if (this.translateEnabled && alternative && alternative.Translations && alternative.Translations.length > 0) {
            try {
                const translation = alternative.Translations[0];
                const rawContent = translation.Content;
                if (rawContent) {
                    if (result.IsPartial) {
                        translatePartialContent = rawContent;
                    } else {
                        translateContent = rawContent;
                    }
                }
            } catch (error) {
                console.log('Error processing translation in formatData:', error);
            }
        }

        // 使用 AWS 的 ResultId 作为基础 ID
        const baseId = messageBody.Transcript.ResultId;
        
        const _formatData = {
            id: baseId,
            seq: this.formatIndex(this.identifyList.length + 1),
            ui: speakerId,
            st: result.StartTime ? result.StartTime * 1000 : new Date().getTime(),
            et: result.EndTime ? result.EndTime * 1000 : new Date().getTime(),
            content: alternative.Transcript,
            translateContent,
            translatePartialContent,
            message_id: baseId,
            nameAvaId: Number(speakerId) - 1,
            isDefaultSpeaker: !this.speakerLabels
        };

        // 调试日志
        if (this.speakerLabels && !result.IsPartial) {
            console.log('Speaker identification result:', {
                speakerId,
                items: alternative.Items,
                isPartial: result.IsPartial
            });
        }

        return Object.assign({}, messageBody, {
            _formatData
        });
    }

    /**
     * 发送音频数据
     * 将音频数据通过WebSocket发送到AWS服务
     * @param {ArrayBuffer} audioData - 音频数据
     */
    sendAudioData(audioData) {
        try {
            if (!audioData || !audioData.byteLength) {
                console.log('Invalid audio chunk received');
                return;
            }

            const audioEventMessage = this.getAudioEventMessage(Buffer.from(audioData));
            const binaryMessage = this.eventStreamMarshaller.marshall(audioEventMessage);

            if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                this.socket.send(binaryMessage);
            }
        } catch (error) {
            console.error('Error sending audio data:', error);
        }
    }

    /**
     * 获取音频事件消息
     * 构造AWS Transcribe服务所需的音频事件消息格式
     * @param {Buffer} buffer - 音频数据buffer
     * @returns {Object} 格式化的音频事件消息
     */
    getAudioEventMessage(buffer) {
        return {
            headers: {
                ':message-type': {
                    type: 'string',
                    value: 'event'
                },
                ':event-type': {
                    type: 'string',
                    value: 'AudioEvent'
                }
            },
            body: buffer
        };
    }

    /**
     * 启动心跳检测
     * 定期发送心跳包以保持WebSocket连接活跃
     */
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.socket && this.socket.readyState === WebSocket.OPEN) {
                const emptyMessage = this.getAudioEventMessage(Buffer.from(new Uint8Array(2)));
                const binaryMessage = this.eventStreamMarshaller.marshall(emptyMessage);
                this.socket.send(binaryMessage);
            }
        }, 13000);
    }

    /**
     * 停止心跳检测
     * 清理心跳检测定时器
     */
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }

    /**
     * 获取WebSocket连接URL
     * 从后端服务获取预签名的AWS Transcribe WebSocket URL
     * @returns {Promise<string>} WebSocket连接URL
     * @throws {Error} 获取URL失败时抛出错误
     */
    async getWebSocketUrl() {
        try {
            // 确保 targetLanguages 是数组并转换语言代码
            const targetLangs = Array.isArray(this.wsConfig.targetLanguages) 
                ? this.wsConfig.targetLanguages.map(lang => AWSTranscribeProvider.LANGUAGE_MAP[lang] || lang)
                : (typeof this.wsConfig.targetLanguages === 'string' ? [AWSTranscribeProvider.LANGUAGE_MAP[this.wsConfig.targetLanguages] || this.wsConfig.targetLanguages] : []);

            const requestData = {
                languageCode: this.awsConfig.languageCode,
                region: this.awsConfig.region,
                mediaEncoding: 'pcm',
                sampleRate: this.awsConfig.sampleRate,
                expires: 300,
                enableSpeakerLabels: this.speakerLabels,
                maxSpeakers: this.speakerLabels ? 10 : undefined,
                enableTranslation: this.translateEnabled,
                targetLanguages: targetLangs,
                objectId: this.objectId,
                taskId: this.taskId || ''
            };

            const response = await fetch(this.serverUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Server error response:', {
                    status: response.status,
                    statusText: response.statusText,
                    body: errorText
                });
                throw new Error(`Server responded with ${response.status}: ${errorText}`);
            }

            const data = await response.json();
            
            if (!data.wsUrl) {
                throw new Error('Server response missing wsUrl');
            }

            return data.wsUrl;
        } catch (error) {
            console.error('Error getting presigned URL:', error);
        }
    }

    /**
     * 处理翻译结果
     * @param {Object} data - AWS 返回的消息体
     * @returns {Object} 格式化后的翻译结果
     */
    translateHandle(data) {
        const result = data.Transcript.Results[0];
        let translateStr = '';
        let translatePartialStr = '';

        if (result.Translations && result.Translations.length > 0) {
            try {
                const translation = result.Translations[0];
                const rawContent = translation.Content;
                if (rawContent) {
                    if (result.IsPartial) {
                        translatePartialStr = rawContent;
                    } else {
                        translateStr = rawContent;
                    }
                }
            } catch (error) {
                console.log('Error processing translation:', error);
            }
        }

        // 找到对应的识别结果并更新翻译内容
        const obj = this.identifyList.find(item => item.message_id === result.ResultId);

        if (obj) {
            obj.translateContent = translateStr;
            obj.translatePartialContent = translatePartialStr;
        }

        return obj || {};
    }

    /**
     * 销毁实例
     * 清理所有资源，包括WebSocket连接、定时器等
     */
    destroy() {
        this.stopHeartbeat();
        
        if (this.socket && this.socket._closeTimer) {
            clearTimeout(this.socket._closeTimer);
            this.socket._closeTimer = null;
        }

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.connectTimer) {
            clearTimeout(this.connectTimer);
            this.connectTimer = null;
        }

        if (this.disconnectTimer) {
            clearTimeout(this.disconnectTimer);
            this.disconnectTimer = null;
        }
    }
}

export default AWSTranscribeProvider;
