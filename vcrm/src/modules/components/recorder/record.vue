<template>
    <div class="activity-recorder-wrapper">
        <div class="activity-recorder-content">
            <div class="activity-recorder-control">
                <div class="activity-recorder-icon"></div>
                <div class="activity-recorder-info">
                    <div class="activity-recorder-label">{{recorderLabel}}</div>
                    <div class="activity-recorder-time">{{recorderTime}}<span class="activity-recorder-max-time"> / {{recorderMaxTime}}</span></div>
                </div>
            </div>
            <div class="activity-recorder-btn-control">
                <fx-tooltip class="activity-recorder-btn-wrapper" :content="startStopTip" placement="top" effect="light">
                    <div @click="startStopHandle" :class="startStopClass"></div>
                </fx-tooltip>
                <fx-tooltip class="activity-recorder-btn-wrapper" :content="pauseContinueTip" placement="top" effect="light">
                    <div v-loading="pauseContinueState" @click="pauseContinueHandle" :class="pauseContinueClass"></div>
                </fx-tooltip>
            </div>
        </div>
    </div>
</template>
<script>
/**
 * 录音机
 * @param stream 
 * @returns 
 */

import { initMicrophoneDetector } from './util';
import Recorder from './recorder.js';
import SpeechToText from './speech_to_text';

const CLIENT_ERROR_STATUS = {
    // 已到最大录音时间
    REACHED_MAX_RECORDING_TIME: 5001,
    // 录音已结束，无法在本记录继续录音
    RECORDING_ENDED: 5002,
    // 录音启动失败
    RECORDING_STARTUP_FAILED: 9101,
    // 录音出错，请重试
    RECORDING_ERROR: 9102,
    // 当前环境不安全，请使用HTTPS协议访问
    INSECURE_CONTEXT: 9103,
    // 浏览器不支持音频输入
    BROWSER_NOT_SUPPORT_AUDIO_INPUT: 9104,
    // 未检测到麦克风设备，请确认麦克风已正确连接
    NO_MICROPHONE_FOUND: 9105,
    // 麦克风被其他应用占用，请关闭可能使用麦克风的应用后重试
    MICROPHONE_BUSY: 9106,
    // 麦克风不满足录音要求，请尝试使用其他麦克风设备
    MICROPHONE_NOT_SATISFY: 9107,
    // 录音参数设置无效，请联系管理员
    INVALID_CONSTRAINTS: 9108,
    // 麦克风硬件错误，请检查设备后重试
    HARDWARE_ERROR: 9109,
    // 授权使用麦克风失败
    AUTHORIZE_USE_MICROPHONE: 9110,
    // 继续录音失败
    CONTINUE_RECORDING_FAILED: 9111
}

/**
 * WebSocket状态和错误码映射
 */
const WS_ERROR_STATUS = {
    // 连接状态码 (readyState)
    CONNECTION: {
        CONNECTING: 0, // 连接正在建立中
        OPEN: 1,       // 连接已建立，可以进行通信
        CLOSING: 2,    // 连接正在关闭
        CLOSED: 3      // 连接已关闭或无法建立
    },
    
    // 标准WebSocket关闭状态码 (CloseEvent.code)
    CLOSE: {
        NORMAL: 1000,        // 正常关闭（完成）
        GOING_AWAY: 1001,    // 离开（例如服务器关闭或导航离开页面）
        PROTOCOL_ERROR: 1002, // 协议错误
        UNSUPPORTED: 1003,   // 不可接受的数据（例如服务器不能处理的数据类型）
        RESERVED: 1004,      // 保留
        NO_STATUS: 1005,     // 无状态码（未显式设置）
        ABNORMAL: 1006,      // 异常关闭（连接意外断开）
        INVALID_DATA: 1007,  // 无效数据（例如非UTF-8数据）
        POLICY_VIOLATION: 1008, // 策略违规
        MESSAGE_TOO_BIG: 1009, // 消息太大
        EXTENSION_REQUIRED: 1010, // 客户端期望服务器协商的扩展
        UNEXPECTED_CONDITION: 1011, // 服务器遇到意外情况
        SERVICE_RESTART: 1012, // 服务重启
        TRY_AGAIN_LATER: 1013, // 服务器临时关闭，请稍后重试
        BAD_GATEWAY: 1014,   // 服务器作为网关，收到无效响应
        TLS_HANDSHAKE: 1015  // TLS握手失败
    },
    
    // 服务端特定错误码
    SERVER: {
        // 连接相关
        INACTIVE_CONNECTION: 40000000, // 客户端长时间未向服务端发送数据导致断开
        TIMEOUT_NO_DATA: 40000004,     // 请求建立连接后，长时间没有发送任何数据(超过10s)
        CLIENT_TERMINATED: 40010004,   // 在请求处理完成前客户端主动结束
        
        // 数据处理相关
        AUDIO_DECODE_FAILED: 40270003, // 音频解码失败
        
        // 资源限制相关
        CONCURRENT_LIMIT: 43040001,    // 实时记录同一个会议建立多次连接或并发超限(试用版2并发，商用版200并发)

        // 会议无效
        INVALID_MEETING: 43040002      // 超出听悟单次记录时长（24小时），需重新创建链接录音
    }
};

export default {
    name: 'activity-recorder',
    props: {
        // 默认开启录音
        enableRecord: {
            type: Boolean,
            default: false
        },
        // 支持重复录音
        repeatRecord: {
            type: Boolean,
            default: true
        },
        // 获取ws链接的配置
        wsConfig: {
            type: Object,
            default: {}
        },
        objectId: {
            type: String,
            default: ''
        },
        objectApiName: {
            type: String,
            default: ''
        },
        // 保存前参数处理，用户事中场景全局修改发言人并保存操作
        beforeSaveResult: null
    },
    computed: {
        startStopClass() {
            return this.isStart ? 'activity-recorder-btn activity-recorder-btn-stop' : 'activity-recorder-btn activity-recorder-btn-start'
        },
        pauseContinueClass() {
            return this.isRecording ? 'activity-recorder-btn activity-recorder-btn-pause' : 'activity-recorder-btn activity-recorder-btn-continue fx-icon-f-yuyinshibie';
        },
        recorderTime() {
            return this.speechToText.formatTime(this.identifiedTime * 1000, false);
        },
        recorderMaxTime() {
            return this.speechToText.formatTime(this.identifyMaxTime * 1000, false);
        },
        recorderLabel() {
            return this.isRecording ? `${$t('sfa.activity.recorder.recording')}...` : $t('sfa.activity.recorder.not_recorded');
        },
        startStopTip() {
            return this.isStart ? $t('sfa.activity.recorder.stop_recording') : $t('sfa.activity.recorder.start_recording');
        },
        pauseContinueTip() {
            return this.isRecording ? $t('sfa.activity.recorder.pause_recording') : $t('sfa.activity.recorder.continue_recording');
        }
    },
    data() {
        return {
            isStart: false,
            isRecording: false,
            startStopState: false, // 开启或关闭中，记录处理中状态，防止重复点击
            pauseContinueState: false, // 暂停或继续中，记录处理中状态，防止重复点击
            recordTimes: 0, // 录音次数
            identifiedTime: 0, // 已识别时间（秒）
            identifyMaxTime: 14400, // 最大识别时间（秒）
            deviceInfo: {}, // 设备信息
            currentDeviceId: '', // 当前设备ID
        }
    },
    methods: {
        /**
         * 开关录音
         */
        startStopHandle() {
            this.isStart ? this.stop(null, false) : this.start();
        },
        /**
         * 暂停恢复录音
         */
        pauseContinueHandle() {
            this.isRecording ? this.pause() : this.continue();
        },
        /**
         * 开始录音
         */
        start() {
            if(!this.repeatRecord && this.recordTimes >= 1) {
                // 录音已结束，无法在本记录继续录音
                this.errorHandler('client', {
                    status: CLIENT_ERROR_STATUS.RECORDING_ENDED
                })
                return;
            };

            if(this.startStopState || this.isStart) {
                return;
            }

            this.isStart = true;
            this.startStopState = true;

            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                this.startStopState = false;
                this.errorHandler('client', {
                    status: CLIENT_ERROR_STATUS.BROWSER_NOT_SUPPORT_AUDIO_INPUT
                })
                return;
            }

            this._start();
        },

        /**
         * 开始录音
         */
        _start() {
            if (this.deviceInfo.physicalDevices.length > 0) {
                this.currentDeviceId = this.getCurrentDeviceId();

                if (!this.currentDeviceId) {
                    this.errorHandler('client', {
                        status: CLIENT_ERROR_STATUS.NO_MICROPHONE_FOUND
                    })
                    return;
                }

                const audioConfig = {
                    audio: {
                        channelCount: 1,        // 双声道
                        sampleRate: 48000,      // 采样率
                        deviceId: this.currentDeviceId,
                        echoCancellation: false // 回声消除
                    }
                };

                navigator.mediaDevices.getUserMedia(audioConfig).then((stream) => {
                    this.stream = stream;
                    this.startTimer = setTimeout(() => {
                        this.startStopState = false;
                    }, 5000);

                    this.speechToText.start({
                        success: (ws) => {
                            clearTimeout(this.startTimer);
                            this.recorderInstance = new Recorder(stream, {
                                sendAudioData: this.speechToText.sendAudioData.bind(this.speechToText),
                                onMessage: (type, data) => {
                                    if (type === 'log') {
                                        this.actionLog(data);
                                    }
                                },
                            });

                            this.recorderInstance.init().then(() => {
                                this.recorderInstance.start();
                                this.isRecording = true;
                                this.startStopState = false;
                                this.timer();
                                this.$emit('startRecord');
                                this.actionLog({
                                    msg: 'recorder_start',
                                    startTime: new Date().getTime()
                                });
                                console.log("Start Recording");
                            })
                        },
                        error: () => {
                            this.isStart = false;
                            this.startStopState = false;
                            this.errorHandler('client', {
                                status: CLIENT_ERROR_STATUS.RECORDING_STARTUP_FAILED
                            })
                            this.$emit('failedRecord', {message: $t('sfa.activity.recorder.recording_startup_failed')});
                        }
                    })

                }).catch((error) => {
                    let errorStatus = {
                        error: error
                    };
                    switch (error.name) {
                        case 'NotAllowedError':
                        case 'PermissionDeniedError': // 兼容旧版浏览器
                            errorStatus.status = CLIENT_ERROR_STATUS.AUTHORIZE_USE_MICROPHONE
                            break;
                        case 'NotFoundError':
                        case 'DevicesNotFoundError': // 兼容旧版浏览器
                            errorStatus.status = CLIENT_ERROR_STATUS.NO_MICROPHONE_FOUND
                            break;
                        case 'NotReadableError':
                        case 'TrackStartError': // 兼容旧版浏览器
                            errorStatus.status = CLIENT_ERROR_STATUS.MICROPHONE_BUSY
                            break;
                        case 'OverconstrainedError':
                        case 'ConstraintNotSatisfiedError': // 兼容旧版浏览器
                            errorStatus.status = CLIENT_ERROR_STATUS.MICROPHONE_NOT_SATISFY
                            break;
                        case 'TypeError':
                            errorStatus.status = CLIENT_ERROR_STATUS.INVALID_CONSTRAINTS
                            break;
                        case 'AbortError':
                            errorStatus.status = CLIENT_ERROR_STATUS.HARDWARE_ERROR
                            break;
                        case 'SecurityError':
                            errorStatus.status = CLIENT_ERROR_STATUS.INSECURE_CONTEXT
                            break;
                        default:
                            errorStatus.status = CLIENT_ERROR_STATUS.RECORDING_ERROR
                    }

                    const errorMessage = this.errorHandler('client', errorStatus);
                    // 具体的错误信息
                    this.isStart = false;
                    this.startStopState = false;
                    this.$emit('failedRecord', { message: errorMessage, error });
                });
            }
            else {
                this.errorHandler('client', {
                    status: CLIENT_ERROR_STATUS.NO_MICROPHONE_FOUND
                })
                this.isStart = false;
                this.startStopState = false;
            }
        },
        /**
         * 结束录音
         * @param cb 回调
         * @param silentStop 静默停止
         */
        stop(cb, silentStop) {
            if (this.startStopState || !this.isStart) {
                cb && cb();
                return;
            }

            if (silentStop) {
                this._stop(cb);
            }
            else {
                let confirm = CRM.util.confirm($t('sfa.activity.recorder.cannot.continue.after.end'), $t('sfa.activity.recorder.end.recording'), () => {
                    confirm.hide();
                    this._stop(cb);
                    this.recordTimes++;
                });
            }
        },
        /**
         * 结束录音
         */
        _stop(cb) {
            this.isStart = false;
            this.startStopState = true;
            this.isRecording = false;
            this.stopRecording();
            this.stopTimer = setTimeout(() => {
                this.startStopState = false;
                cb && cb();
                this.$emit('stopRecord');
            }, 5000);

            this.speechToText.finish({
                success: () => {
                    clearTimeout(this.stopTimer);
                    this.startStopState = false;
                    cb && cb();
                    this.$emit('stopRecord');
                    this.actionLog({
                        msg: 'recorder_stop',
                        startTime: new Date().getTime()
                    });
                },
                error: () => {
                    clearTimeout(this.stopTimer);
                    this.startStopState = false;
                    cb && cb();
                    this.$emit('stopRecord');
                    this.actionLog({
                        msg: 'recorder_stop_error',
                        startTime: new Date().getTime()
                    });
                }
            })

            this.timer(true);
        },
        /**
         * 暂停录音
         */
        pause() {
            if (!this.isStart || this.startStopState || this.pauseContinueState) {
                return;
            }

            this.isRecording = false;
            this.pauseContinueState = true;

            if (this.recorderInstance) {
                this.recorderInstance.pause();
            }

            this.pauseTimer = setTimeout(() => {
                this.pauseContinueState = false;
            }, 5000)

            this.speechToText.pause({
                success: () => {
                    clearTimeout(this.pauseTimer);
                    this.pauseContinueState = false;
                    this.timer(true);
                    this.$emit('pauseRecord');
                }
            });
        },
        /**
         * 继续录音
         */
        continue() {
            if(!this.isStart && !this.repeatRecord && this.recordTimes >= 1) {
                this.errorHandler('client', {
                    status: CLIENT_ERROR_STATUS.RECORDING_ENDED
                })
                return;
            };

            if (!this.isStart || this.startStopState || this.pauseContinueState) {
                return;
            }

            this.isRecording = true;
            this.pauseContinueState = true;


            this.continueTimer = setTimeout(() => {
                this.pauseContinueState = false;
            }, 5000)

            if (this.recorderInstance) {
                this.speechToText.continue({
                    success: (ws) => {
                        clearTimeout(this.continueTimer);
                        this.pauseContinueState = false;
                        this.recorderInstance.continue(ws);
                        this.timer();
                        this.$emit('continueRecord');
                        console.log("Continue recording");
                    },
                    error: () => {
                        this.isRecording = false;
                        this.pauseContinueState = false;
                        const errorMessage = this.errorHandler('client', {
                            status: CLIENT_ERROR_STATUS.CONTINUE_RECORDING_FAILED
                        })
                        this.$emit('failedRecord', {message: errorMessage}); 
                    }
                })
            }
            else {
                this.isRecording = false;
                this.pauseContinueState = false;
                const errorMessage = this.errorHandler('client', {
                    status: CLIENT_ERROR_STATUS.CONTINUE_RECORDING_FAILED
                })
                this.$emit('failedRecord', {message: errorMessage}); 
            }
        },

        /**
         * 错误处理
         * @param type 类型
         * @param data 数据
         * @returns {String} 错误信息
         */
        errorHandler(type, data = {}, onClose) {
            if (!data.status) {
                return;
            }

            let errorMessage = '';

            if (type === 'client') {
                switch (data.status) {
                    case CLIENT_ERROR_STATUS.BROWSER_NOT_SUPPORT_AUDIO_INPUT:
                        errorMessage = $t('sfa.activity.recorder.browser_not_support_audio_input');
                        break;
                    case CLIENT_ERROR_STATUS.REACHED_MAX_RECORDING_TIME:
                        errorMessage = $t('sfa.activity.recorder.reached_max_recording_time');
                        break;
                    case CLIENT_ERROR_STATUS.NO_MICROPHONE_FOUND:
                        errorMessage = $t('sfa.activity.recorder.no_microphone_found');
                        break;
                    case CLIENT_ERROR_STATUS.RECORDING_STARTUP_FAILED:
                        errorMessage = $t('sfa.activity.recorder.recording_startup_failed');
                        break;
                    case CLIENT_ERROR_STATUS.INVALID_CONSTRAINTS:
                        errorMessage = $t('sfa.activity.recorder.invalid_constraints');
                        break;
                    case CLIENT_ERROR_STATUS.AUTHORIZE_USE_MICROPHONE:
                        errorMessage = $t('sfa.activity.recorder.authorize_use_microphone');
                        break;
                    case CLIENT_ERROR_STATUS.MICROPHONE_BUSY:
                        errorMessage = $t('sfa.activity.recorder.microphone_busy');
                        break;
                    case CLIENT_ERROR_STATUS.MICROPHONE_NOT_SATISFY:
                        errorMessage = $t('sfa.activity.recorder.microphone_not_satisfy');
                        break;
                    case CLIENT_ERROR_STATUS.HARDWARE_ERROR:
                        errorMessage = $t('sfa.activity.recorder.hardware_error');
                        break;
                    case CLIENT_ERROR_STATUS.INSECURE_CONTEXT:
                        errorMessage = $t('sfa.activity.recorder.insecure_context');
                        break;
                    case CLIENT_ERROR_STATUS.RECORDING_ERROR:
                        errorMessage = $t('sfa.activity.recorder.recording_error');
                        break;
                    case CLIENT_ERROR_STATUS.RECORDING_ENDED:
                        errorMessage = $t('sfa.activity.recorder.recording_ended');
                        break;
                    case CLIENT_ERROR_STATUS.CONTINUE_RECORDING_FAILED:
                        errorMessage = $t('sfa.activity.recorder.continuing_recording_failed');
                        break;
                    default:
                        break;
                }

                if (data.error && data.error.message) {
                    errorMessage = `${errorMessage}(${data.error.message})`;
                }
            }
            else if (type === 'server') {
                switch (data.status) {
                    case WS_ERROR_STATUS.SERVER.INVALID_MEETING:
                        errorMessage = $t('sfa.activity.recorder.invalid_meeting');
                        break;
                    default:
                        errorMessage = $t('sfa.activity.recorder.error_refresh_page');
                        break;
                }

                errorMessage = `${errorMessage}(${data.status || 0})`;
            }

            this.timer(true);

            if (this.$msg) {
                this.$msg.close();
            }

            this.$msg = this.$message({
                type: 'error',
                showClose: true,
                duration: 0,
                message: errorMessage,
                onClose
            });

            if ([CLIENT_ERROR_STATUS.REACHED_MAX_RECORDING_TIME, CLIENT_ERROR_STATUS.RECORDING_ENDED].includes(data.status)) {
                this.actionLog({
                    code: data.status,
                    msg: errorMessage
                })
            }
            else {
                this.errorLog({
                    code: data.status,
                    msg: errorMessage,
                    stack: data?.error?.stack || ''
                })
            }

            return errorMessage
        },
        /**
         * 操作日志
         * @param code 
         * @param message 
         */
        actionLog(params) {
            window?.logger?.action(Object.assign({
                eventId: 'activity_audio_record_action',
                apiName: this.objectApiName,
            }, params))
        },
        /**
         * 错误日志
         * @param code 
         * @param msg 
         */
        errorLog(params) {
            window?.logger?.error(Object.assign({
                eventId: 'activity_audio_record_error',
                apiName: this.objectApiName,
            }, params))
        },

        /**
         * 记录识别时间
         * @param stop 停止记录
         */
        timer(stop) {
            if (stop) {
                clearInterval(this._timer);
                this._timer = null;
                return;
            }

            if (!this._timer) {
                this._timer = setInterval(() => {
                    if (this.identifiedTime >= this.identifyMaxTime) {
                        clearInterval(this._timer);
                        this.stop(null, true);
                        this.errorHandler('client', {
                            status: CLIENT_ERROR_STATUS.REACHED_MAX_RECORDING_TIME
                        })
                    }

                    this.identifiedTime += 1;
                }, 1000)
            }
        },
        /**
         * 获取当前物理设备ID
         * @returns {String} 设备ID
         */
        getCurrentDeviceId() {
            if (!this.deviceInfo.physicalDevices?.length) {
                return '';
            }

            // 过滤出所有音频输入设备
            const audioInputDevices = this.deviceInfo.physicalDevices.filter(device => 
                device.kind === 'audioinput'
            );
            
            if (!audioInputDevices.length) {
                return '';
            }
            
            // 默认选择第一个音频输入设备
            let selectedDevice = audioInputDevices[0];
            
            // 如果第一个设备是"default"，尝试找到同组中的非默认设备
            if (selectedDevice.deviceId === 'default') {
                const nonDefaultDevice = audioInputDevices.find(device => 
                    device.groupId === selectedDevice.groupId && 
                    !['default', 'communications'].includes(device.deviceId) && 
                    selectedDevice.label.endsWith(device.label)
                );
                
                if (nonDefaultDevice) {
                    selectedDevice = nonDefaultDevice;
                }
            }
            
            return selectedDevice.deviceId || '';
        },
        async changeDevice() {
            if (!this.isDeviceAvailable()) {
                await this.stopRecording();
                this.speechToText.stop({
                    success: () => {
                        this._start();
                    }
                })
            }
        },
        isDeviceAvailable() {
            return !!this.deviceInfo.physicalDevices.find(item => item.deviceId === this.currentDeviceId);
        },
        async stopRecording() {
            if (this.recorderInstance) {
                await this.recorderInstance.stop();
                this.recorderInstance = null;
            }
        },
        isConnected() {
            if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                return true;
            }

            return false;
        },
        bindEvent() {
            window.addEventListener('online', this.onOnline);
            window.addEventListener('offline', this.onOffline);
        },
        unbindEvent() {
            window.removeEventListener('online', this.onOnline);
            window.removeEventListener('offline', this.onOffline);
        },
        onOnline() {
            this.onlineTimer = setTimeout(() => {
                if (this.$msg) {
                    this.$msg.close();
                }
                if (this.isConnected()) {
                    this.$msg = this.$message({
                        type: 'success',
                        showClose: true,
                        duration: 0,
                        message: $t('sfa.activity.recorder.online')
                    });
                }
            }, 3000);
        },
        onOffline() {
            if (this.$msg) {
                this.$msg.close();
            }

            this.$msg = this.$message({
                type: 'error',
                showClose: true,
                duration: 0,
                message: $t('sfa.activity.recorder.offline')
            });
        }
    },
    created() {
        this.actionLog({
            msg: 'recorder_load',
            startTime: new Date().getTime()
        });

        let provider = this.wsConfig.provider || 'TingWu';

        this.speechToText = SpeechToText.createService(provider, {
            objectId: this.objectId,
            objectApiName: this.objectApiName,
            wsConfig: this.wsConfig,
            onMessage: (type, data) => {
                if (type === 'log') {
                    this.actionLog(data);
                }
                else {
                    this.$emit(type, data);
                }
            },
            onError: (errorMessage) => {
                this.errorHandler('server', errorMessage)
            },
            onReconnect: (ws) => {
                if (this.recorderInstance) {
                    this.recorderInstance.continue(ws);
                }
                else {
                    this.recorderInstance = new Recorder(this.stream, {
                        sendAudioData: this.speechToText.sendAudioData.bind(this.speechToText)
                    });
                    this.recorderInstance.start();
                }
            },
            beforeSaveResult: this.beforeSaveResult
        });

        if (this.enableRecord) {
            initMicrophoneDetector({
                onSuccess: (data) => {
                    this.deviceInfo = data;
                    this.start();
                },
                onChange: (data) => {
                    this.deviceInfo = data;
                    this.changeDevice();
                }
            });
        }

        this.bindEvent();
    },
    beforeDestroy() {
        this.stop(null, true);
        this.unbindEvent();

        if (this.$msg) {
            this.$msg.close();
        }
        
        // 清除计时器
        if (this._timer) {
            clearInterval(this._timer);
            this._timer = null;
        }

        if (this.speechToText) {
            this.speechToText.destroy();
            this.speechToText = null;
        }

        clearTimeout(this.onlineTimer);
        clearTimeout(this.startTimer);
        clearTimeout(this.stopTimer);
        clearTimeout(this.pauseTimer);
        clearTimeout(this.continueTimer);
    }
}
</script>

<style lang="less" scoped>
    .activity-recorder-wrapper{
        display: flex;
        padding: 12px 16px 12px 8px;
        flex-direction: column;
        justify-content: flex-end;
        align-items: center;
        gap: 24px;
        align-self: stretch;
        border-radius: 8px;
        background: var(--color-special01);
    }

    .activity-recorder-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        align-self: stretch;
    }

    .activity-recorder-control {
        display: flex;
        align-items: center;
    }

    .activity-recorder-info {
        margin-left: 4px;
    }

    .activity-recorder-icon {
        width: 74px;
        height: 44px;
        flex-shrink: 0;
        opacity: 0.7;
        background: url('~@assets/images/activity/recorder.png') 50% / cover no-repeat;
    }

    .activity-recorder-btn-control {
        display: flex;
        align-items: center;
    }

    .activity-recorder-btn-wrapper {
        display: flex;
    }

    .activity-recorder-btn {
        display: flex;
        width: 38px;
        height: 38px;
        justify-content: space-between;
        align-items: center;
        border-radius: 8px;
        margin-left: 12px;
        cursor: pointer;
    }

    .activity-recorder-btn-start {
        position: relative;
        background: #fff;
        
        &:before {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url('~@assets/images/activity/play.png') no-repeat;
            background-size: contain;
        }
    }

    .activity-recorder-btn-stop {
        position: relative;
        background: #fff;
        
        &:before {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url('~@assets/images/activity/stop.png') no-repeat;
            background-size: contain;
        }
    }

    .activity-recorder-btn-pause {
        position: relative;
        background: #fff;
        
        &:before {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: url('~@assets/images/activity/pause.png') no-repeat;
            background-size: contain;
        }
    }

    .activity-recorder-btn-continue {
        position: relative;
        background: #fff;
        
        &:before {
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #5B70EA;
            font-size: 16px;
        }
    }

    .activity-recorder-label {
        color: var(--color-neutrals19);
        font-size: 14px;
        font-weight: 700;
        line-height: 24px;
    }

    .activity-recorder-time {
        color: var(--color-neutrals15);
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
    }

    .activity-recorder-max-time {
        color: var(--color-neutrals11);
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
    }
</style>

<style lang="less">
/* 全局样式，不使用scoped，以便应用于动态生成的元素 */
.activity-recorder-btn-wrapper {
    .el-loading-spinner {
        top: 6px;
        margin-top: 0;
    }
}
</style>