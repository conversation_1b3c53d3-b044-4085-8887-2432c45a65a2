/*
 * 组件
 * @Author: LiAng
 * @Date: 2020-01-13 16:14:24
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-07-02 19:10:04
 */

export default {
    fieldFormat: () => import('@components/field-format/index'),
    objectTable: () => import('@components/objecttable/objecttable'),
    // 开票模式3左右两栏选数据
    pickData: () => import('@components/pickdata/index'),
    // 开票模式3左右两栏选数据(旧)
    pickDataOld: () => import('@components/pickdata-old/index'),
    networkEditor:() => import('@components/networkeditor/index'),
    EditPricebookProduct:() => import('@components/EditPricebookProduct/EditPricebookProduct'),
    advanceBquery: () => import('@components/advancebquery/index'),
    // 银鹭政策产品分类
    category: () => import('@components/category/'),
    // 简易价格政策
    PricePolicyCommonPriceRule: () => import('@components/PricePolicyCommonPriceRule/index'),
    // 价格政策规则列表
    PricePolicyRuleObjList: () => import('@components/PricePolicyRuleObjList/index'),
    // 返利产生政策规则列表
    RebatePolicyRuleObjList: () => import('@components/RebatePolicyRuleObjList/index'),
    pluginService: () => import('@plugin/common/plugin-service'),
    pluginLog: () => import('@plugin/common/plugin-log'),
    searchAndTree: () => import('@components/search_and_tree/search_and_tree'),
    // 售中后台开关
    backstage: () => import('@components/backstage/index'),
    configGuide: () => import('@components/configGuide/index'),
    channelaccess: () => import('@components/channelaccess/index.vue'),
    channelguide: () => import('@components/channelguide/index.vue'),
    sessionArchives: () => import('@components/sessionArchives/index'),
    sessionPanel: () => import('@components/sessionPanel/index'),
    sessionChat: () => import('@components/sessionChat/index'),
    sessionChatNew: () => import('@components/sessionChatNew/index'),
    callOutPanel: () => import('@components/call-out-panel/call-out-panel'),
    // 售中后台开关--移动端左下角显示
    MobileSelect: () => import('@components/backstage/settings/tradeconfigure/MobileSelect.vue'),
    // 售中后台开关--移动端多单位设置
    MobileMulticonfig: () => import('@components/backstage/settings/cmmodityproduct/MobileMulticonfig.vue'),
    // 售中后台开关--移动端多单位显示设置
    MobileMultUnitConfig: () => import('@components/backstage/settings/cmmodityproduct/MobileMultUnitConfig.vue'),
    ProCurementsSubscribe: () => import('@components/ProCurementsSubscribe/index.vue'),
    accountmanage: () => import('@components/accountmanage/index'),
    g6Tree: () => import('@components/fishbone/components/demo'),
    // g6Tree: () => import('@components/businesstree/index'),
    // g6Tree: () => import('@components/orgtree/index'),
    ProCureSearchObj: () => import('@components/ProCureSearchObj/index'),
    ProjectTaskDrag:() => import('@components/projectTaskDrag/index'),
    ScrolledTree: () => import('@components/scrolledtree/ScrolledTree'),
    accountTreeConfig:() => import('@components/accountTreeConfig/index'),
    priceQuoterDesigner:() => import('@components/priceQuoterDesigner/index'),
    searchTree: () => import('@components/search-tree/index'),
    WorkbenchConfig: () => import('@components/WorkbenchConfig/WorkbenchConfig'),
    autoEntryAccount: () => import('@components/autoentryaccount/index'),
    SelectTimeInterval:() => import('@components/SelectTimeInterval/index'),
    CreateActivitiesAndTasks:() => import('@components/CreateActivitiesAndTasks/index'),
    WorkHourComponent:() => import('@components/workHourComponent/index'),
    BusinessSearchObj: () => import('@components/BusinessSearchObj/index'),
    IncentivePolicyRuleObj: () => import('@components/IncentivePolicyRuleObj/index'),
    RuleConditionWrapper: () => import('@components/RuleConditionWrapper/index'),
    IncentiveAction: () => import('@components/IncentiveAction/index'),
    SfaAiTitleText: () => import('@components/sfaAiTitleText/sfaAiTitleText.vue'),
    DhtModulesTable: () => import('@components/dhtmodules/index'),
    addField: () => import('@components/addField/index'),
    RelationTemplateFilterRule: () => import('@components/relationTemplateFilterRule/index'),
    VerticalTreeTemplate: () => import('@components/verticalTreeTemplate/verticalTreeTemplate'),
    FishBoneTreeTemplate: () => import('@components/fishBoneTreeTemplate/index'),
    incentiveCategoryTree: () => import('@components/incentiveCategoryTree/index'),
    CustomStore: () => import('@components/observer/store.js'),
    workBenchTable:() => import('@components/workBenchTable/index'),
    evaluationCantact:() => import('@components/evaluationCantact/index'),
    AppointmentDatetime: () => import('@components/appointment-datetime/components/date-picker/index'),
    attributeRangeSetting: () => import('@components/attributeRangeSetting/index'),
    attributeRangeRender: () => import('@components/attributeRangeRender/index'),
    priceQuoterRender: () => import('@components/priceQuoterRender/index'),
    categoryAttrSetting: () => import('@components/categoryAttrSetting/index'),
    receptionchannel: () => import('@components/receptionchannel/index'),
    ConfigServiceBom: () => import('@components/ConfigServiceBom/index'),
    DevicePicture: () => import('@components/ConfigServiceBom/components/DevicePicture.vue'),
    recorder: () => import('@components/recorder/index'),
    acitvityRecordDialog: () => import('@components/activityrecorddialog/index'),
    acitvityMeetingDialog: () => import('@components/acitvityMeetingDialog/index'),
    receptionchannel: () => import('@components/receptionchannel/index'),
    aiInteractiveIssues: () => import('../detail/view/v3/base/components/aiInteractiveIssues/aiInteractiveIssuesWithState.vue'),
    // aiInteractiveIssues: () => import('../detail/view/v3/base/components/aiInteractiveIssues/aiInteractiveIssues.vue'),
    aiSuggestIssues: () => import('../detail/view/v3/base/components/aiSuggestIssues/aiSuggestIssuesWithState.vue'),
    // aiSuggestIssues: () => import('../detail/view/v3/base/components/aiSuggestIssues/aiSuggestIssues.vue'),
    aitodo: () => import('../detail/view/v3/base/components/aitodo/aitodo.vue'),
    parentagreement: () => import('@components/parentagreement/index'),
    tencentMediaviewer: () => import('@components/tencentMediaviewer/index'),
    FullLifeCard: () => import('@components/full-life-card/index'),
    SfaNewAiChat: () => import('@components/sfa_new_ai_chat/index.vue'),
    sfaActivityNote: () => import('../detail/view/v3/activerecordobj/components/note/index.vue'),
};
