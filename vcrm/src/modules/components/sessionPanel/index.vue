<template>
    <div
        class="panel-wrapper"
        :style="[
            { width: comWidth },
            { padding: isClickPanelArrow ? '0' : '13px 0px' },
        ]"
    >
        <v-arrow v-if="isDetail" @bindSize="bindSizeFun"></v-arrow>
        <v-header
            :title="panelTitle"
            :isClickPanelArrow="isClickPanelArrow"
            :navTabs="panelTabs"
            :isHasSearch="isHasSearch"
            @searchHandle="searchHandleFun"
            @changehandle="changehandleFun"
        ></v-header>
        <ul :style="[{ 'overflow-y': 'auto' },{ 'margin-top': !panelData.length ? '13px' : '0' },{height: panelHeight}]" @scroll="scrollHandle">
            <li v-if="!panelData.length" class="no-data">暂无数据</li>
            <li
                v-for="(item, idx) in panelData"
                :key="idx"
                class="panel-list"
                :style="{
                    'justify-content': isClickPanelArrow ? 'center' : 'normal',
                }"
                @click="handleNodeClick(item)"
            >
                <fx-popover
                    placement="bottom-start"
                    popper-class="panel-popover"
                    trigger="hover"
                    @show="onPopoverShow()"
                >
                    <div class="pover-wrapper">
                        <div>
                            <img
                                :src="item.thumb_avatar"
                                class="panel-avatar"
                                alt=""
                            />
                            <div class="nick-name">
                                <h4 class="panel-wechat">
                                    {{ item.wechat_name
                                    }}<span
                                        :style="{
                                            color: item.isWechat
                                                ? 'var(--color-success06)'
                                                : 'var(--color-primary06)',
                                        }"
                                        >{{ item.alias }}</span
                                    >
                                </h4>
                                <h5 style="margin-top: 5px">{{setHoverName(item)}}</h5>
                            </div>
                        </div>
                        <ul class="time">
                            <li class="panel-info">
                                {{ setHoverCreateTime(item) }}
                                <span>{{
                                    item.external_contact_create_time
                                }}</span>
                            </li>
                            <li class="panel-info">
                                {{ $t("最近联系时间") }}
                                <span>{{ item.last_chat_time }}</span>
                            </li>
                        </ul>
                    </div>
                    <img
                        :src="item.avatar"
                        class="panel-avatar"
                        slot="reference"
                    />
                </fx-popover>
                <div
                    class="right-main"
                    :style="{
                        display: isClickPanelArrow ? 'none' : 'block',
                    }"
                >
                    <h4 class="panel-wechat">
                        {{ item.wechat_name
                        }}<span
                            :style="{
                                color: item.isWechat ? 'var(--color-success06)' : 'var(--color-primary06)',
                            }"
                            >{{ item.alias }}</span
                        ><span class="date">{{ item.create_time }}</span>
                    </h4>
                    <div>
                        <span
                            v-for="(it, index) in item.tags"
                            :class="[item.isBlue ? 'blue' : 'pink', 'tag']"
                            >
                                <template v-if="index < 3">{{ it.label }}</template>
                                <template v-if="index === 3"><i class="tag-num">{{ setOverTag(item.tags) }}</i></template>
                            </span
                        >
                    </div>
                    <div>
                        <span
                            v-for="(pel, ix) in item.panes"
                            class="panel-info"
                            >{{ setPanelItemMsg(pel) }}</span
                        >
                    </div>
                </div>
            </li>
        </ul>
    </div>
</template>
<script>
import Search from '../sessionCommon/search'
import Arrow from '../sessionCommon/arrow'
export default {
    name: 'panel',
    props: {
        panelTitle: {
            type: String
        },
        panelTabs: {
            type: Array,
            default: () => []
        },
        panelList: {
            type: Array,
            default: () => []
        },
        isDetail: {
            type: Boolean,
            default: false
        },
        comWidthObj: {
            type: Object,
            default: () => {
                return {
                    width1: "82px",
                    width2: "304px",
                };
            }
        },
        isHasSearch: {
            type: Boolean,
            default: true
        }
    },
    components: {
        "v-header": Search,
        "v-arrow": Arrow
    },
    data () {
        return {
            comWidth: this.comWidthObj.width2,
            isClickPanelArrow: false,
            panelData: [],
            panelHeight: ''
        }
    },
    watch: {
        panelList: {
            immediate: true,
            handler (val) {
                val.forEach(item => {
                    !item.employee_count && (item.panes = item.panes.filter(item => item.name !== 'employee'));
                    !item.contact_count && (item.panes = item.panes.filter(item => item.name !== 'contact'));
                });
                this.panelData = val;
            }
        }
    },
    mounted(){
      const _this = this;
      //窗口变化重置容器高度
      window.addEventListener('resize', () => {
        return (() => {
            _this.initContentHeight();
        })()
      });
      this.initContentHeight();
    },
    methods: {
        //初始化内容高度
        initContentHeight () {
            const _this = this;
            this.$nextTick(()=>{
                _this.panelHeight = $('.panel-wrapper').height() - $('.panel-wrapper .common-wrapper').height() + 'px';
            });
        },
        //处理panel展示数据格式
        setPanelItemMsg (item) {
            const label = `${item.name}_label`;
            const count = `${item.name}_count`;
            return item[label] + ':' + (item[count] || 0);
        },
        //panel hover
        onPopoverShow () {
            $('.panel-popover').css('width', 'min-content');
        },
        changehandleFun (currentIndex) {
            this.initContentHeight();
            this.$emit('changehandle', currentIndex, 'panel')
        },
        searchHandleFun (val) {
            this.$emit('searchHandle', val, 'panel')
        },
        bindSizeFun (isClickArrow) {
            this.isClickPanelArrow = isClickArrow;
            this.comWidth = isClickArrow ? this.comWidthObj.width1 : this.comWidthObj.width2;
        },
        handleNodeClick (data) {
            this.selectId = data.opposite_cipher_id;
            console.log(data)
            this.$emit('renderChat', data)
        },
        scrollHandle(e){
          const {scrollTop, clientHeight, scrollHeight} = e.target;
          if(scrollTop + clientHeight === scrollHeight){
            //滚动到底部
            this.$emit('scrollHandle');
          }
        },
        setHoverName(item){
            let name = '';
            switch (item.type){
                case 1:
                    name = $t('姓名')
                    break;
                case 3:
                    name = $t('群主')
                    break;
                case 9:
                    name = $t('未知联系人id')
                    break;
                case 10:
                    name = $t('未知群id')
                    break;
                default :
                    name = $t('姓名');
            }
            return `${name} : ${item.name}`
        },
        setHoverCreateTime(item) {
            let name = '';
            switch (item.type){
                case 1:
                    name = $t('添加好友时间')
                    break;
                case 3:
                    name = $t('群创建时间')
                    break;
                case 9:
                    name = $t('添加好友时间')
                    break;
                case 10:
                    name = $t('群创建时间')
                    break;
                default :
                    name = $t('添加好友时间');
            }
            return name;
        },
        //tag数超出3
        setOverTag(tags) {
            const num = tags.length - 3;
            return `+${num}`;
        }

    }
}
</script>
<style lang="less">
.panel-wrapper {
    position: relative;
    padding: 13px 0px;
    height: 100%;
    box-shadow: inset -1px 0px 0px #dee1e6;
    box-sizing: border-box;
    .refresh-icon{
      text-align: center;
      height: 30px;
      line-height: 30px;
      i{
        font-size: 16px;
      }
    }
    .no-data{
      height: 60px;
      line-height: 60px;
      text-align: center;
      color: #909399;
    }
    .panel-list {
        display: flex;
        padding: 10px 12px;
        box-sizing: border-box;
        border-bottom: 1px solid #ddd;
        &:hover {
            background: var(--color-neutrals03);
            box-shadow: inset -1px 0px 0px #dee1e6;
        }
        .right-main {
            width: 100%;
            line-height: 1.7;
            font-size: 12px;
            margin-left: 10px;
            box-sizing: border-box;
            .date {
                font-size: 12px;
                float: right;
                color: var(--color-neutrals11);
            }
            .tag {
                display: inline-block;
                font-size: 11px;
                padding: 1px 4px;
                margin-right: 5px;
                max-width: 60px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                &.blue {
                    background: var(--color-info01);
                    color: var(--color-info06);
                }
                &.pink {
                    background: var(--color-danger01);
                    color: var(--color-danger06);
                }
                .tag-num {
                    font-size: 12px;
                    font-style: normal;
                }
            }
        }
    }
}
.panel-popover {
    margin-top: -20px !important;
    margin-left: 20px;
    .pover-wrapper {
        & + .popper__arrow {
            display: none;
        }
        & > div {
            display: flex;
            .nick-name {
                margin-left: 10px;
                font-size: 12px;
                line-height: 1.5;
                color: var(--color-neutrals11);
            }
        }
        .time {
            line-height: 1.5;
            margin-top: 8px;
        }
    }
}
//公共样式
.panel-avatar {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.panel-wechat {
    font-weight: 400;
    font-size: 13px;
    color: var(--color-neutrals19);
}
.panel-info {
    display: inline-block;
    width: 50%;
    font-size: 12px;
    line-height: 2;
    color: var(--color-neutrals11);
    white-space: nowrap;
    span {
        margin-left: 5px;
        color: var(--color-neutrals19);
    }
}
</style>