<template>
<div class="pp-rule-actions">
    <fx-dropdown v-if="!progressive" trigger="click" @command="changeRuleGroup">
        <fx-button class="pp-rule-btn" size="mini">{{$t("切换分组")}}</fx-button>
        <fx-dropdown-menu slot="dropdown" class="pricepolicyobj price-rule-dropdown">
            <fx-dropdown-item v-for="(cg, cgi) in groups" :key="cgi" :command="[groupIndex, itemIndex, item, cgi]">
                <span class="icon"><i class="el-icon-check" v-if="cgi === groupIndex"></i></span>
                <span class="text">{{$t('组号') + (cgi + 1)}}</span>
            </fx-dropdown-item>
        </fx-dropdown-menu>
    </fx-dropdown>
    <fx-button
        v-for="action in getActions()"
        :key="action.value"
        class="pp-rule-btn"
        size="mini"
        @click="handleAction(action.value, groupIndex, itemIndex)"
    >
        {{action.label}}
    </fx-button>
</div>
</template>

<script>
export default {
    name: "RuleActions",
    props: {
        rules: {
            type: Array
        },
        groupIndex: {
            type: Number
        },
        itemIndex: {
            type: Number
        },
        item: {
            type: Object
        },
        operateBtns:{ 
            type:Object
        }
    },
    data() {
        return {
            actions: [
                {label: $t('删除'), value: 'del'},
                {label: $t('复制'), value: 'copy'},
                {label: $t('编辑'), value: 'edit'},
            ]
        }
    },
    computed: {
        groups() {
            return Array(this.rules.length + 1)
        },
        progressive() {
            const delBtns = this.operateBtns?.[this.item._id]?.del;
            const needDel = delBtns && delBtns.includes('changeGroup');
            return this.rules?.[this.groupIndex].list[this.itemIndex]?.progressive || needDel;
        },
    },
    methods: {
        getActions(){
            const config = this.operateBtns?.[this.item._id];
            if(!config){
                return this.actions;
            }
            return this.actions.filter(it=>!config.del.includes(it.value));
        },
        changeRuleGroup(args) {
            this.$emit('changeRuleGroup', args);
        },
        handleAction() {
            this.$emit('action', ...arguments);
        },
    }
}
</script>

<style lang="less">
.pp-rule-actions {
    display: flex;
    align-items: center;
    gap: 8px;

    .el-dropdown {
        color: inherit;
    }

    .pp-rule-btn {
        margin-left: 0;
    }
}
// 切换分组
.pricepolicyobj.price-rule-dropdown {
    max-height: 158px;
    overflow-y: scroll;

    li {
        padding: 0 8px 0 0;
        width: 112px;
        display: flex;

        span.icon {
            width: 24px;
            text-align: center;
            color: var(--color-primary06);
            display: inline-block;
            i {
                margin-right: 0;
            }
        }

        span.text {
            flex-grow: 2;
            border-bottom: 1px solid #DEE1E6;
            line-height: 32px;
            color: var(--color-neutrals19);
        }
    }
}
</style>