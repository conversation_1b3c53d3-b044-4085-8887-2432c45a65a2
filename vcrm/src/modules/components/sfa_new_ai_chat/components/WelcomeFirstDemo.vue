<template>
    <div>
        <p>WelcomeFirstDemo</p>
        <!-- <p>message: {{ message }}</p>
        <p>agentData: {{ agentData }}</p> -->
        <input type="text" v-model="message" />
        <fx-upload
        list-type="picture-card"
        :file-list="fileList"
        :on-success="handleSuccess"
        >
        <i class="el-icon-plus"></i>
        </fx-upload>
        <button @click="setMessage">setMessage</button>
    </div>
</template>

<script>
export default {
    name: 'WelcomeFirstDemo',
    data() {
        return {
            message: '',
            fileList: [],
            uploadList: []
        }
    },
    methods: {
        handleSuccess(response, file, fileList) {
            this.uploadList.push({
                path: response.TempFileName,
                filename: file.name,
                size: file.size,
                ext: response.FileExtension,
                url: Fx.file.getFilePath(response.TempFileName),
                // width: '',
                // height: ''
            })
        },
        setMessage() {
            const content = [];
            if (this.message) {
                content.push({
                    type: 'text',
                    text: this.message
                })
            }
            if (this.uploadList.length > 0) {
                content.push(...this.uploadList.map((item) => ({
                    type: 'image',
                    source: item
                })))
            }
            if (content.length > 0) {
                this.$emit('send-message', {content})
            }
        }
    }
}
</script>