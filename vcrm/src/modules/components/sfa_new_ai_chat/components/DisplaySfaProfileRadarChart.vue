<template>
    <div>
        <ProfileChartRadar v-if="!message.c139" :chartData="chartData" height="312px" />
        <ProfileChart139Radar v-else :chartData="chartData" height="312px" />
    </div>
</template>

<script>
import profileComponents from '@/modules/detail/view/v3/base/components/sfaAiCustomerProfile/components'
const { ProfileChartRadar, ProfileChart139Radar } = profileComponents;
export default {
    name: 'DisplaySfaProfileRadarChart',
    components: {
        ProfileChartRadar,
        ProfileChart139Radar
    },
    props: {
        agentData: {
            type: Object,
            default: () => ({})
        },
        message: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        chartData() {
            const {dataList = [], c139 = false} = this.message;
            if (c139) {
                return dataList.reduce((acc, item) => {
                    acc[item.name] = item.value;
                    return acc;
                }, {});
            }
            return dataList;
        }
    },
}
</script>