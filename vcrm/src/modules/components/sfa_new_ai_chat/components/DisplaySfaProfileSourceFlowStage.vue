<template>
    <ProfileSourceFlowStage :scoreData="message" />
</template>

<script>
import profileComponents from '@/modules/detail/view/v3/base/components/sfaAiCustomerProfile/components'
const { ProfileSourceFlowStage } = profileComponents;
export default {
    name: 'DisplaySfaProfileRadarChart',
    components: {
        ProfileSourceFlowStage
    },
    props: {
        agentData: {
            type: Object,
            default: () => ({})
        },
        message: {
            type: Object,
            default: () => ({})
        }
    },
    computed: {
        chartData() {
            const {dataList = [], c139 = false} = this.message;
            if (c139) {
                return dataList.reduce((acc, item) => {
                    acc[item.name] = item.value;
                    return acc;
                }, {});
            }
            return dataList;
        }
    },
}
</script>