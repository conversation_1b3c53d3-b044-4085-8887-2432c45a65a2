
## 组件开发规范

agent-chat组件支持welcome和displayData两个插槽，分别用于显示欢迎卡片和显示数据。

统一在components目录下开发导出。

- displayData插槽命名规范，驼峰命名并以Display开头，组件通过消息type来匹配
例如：消息的type = sfa_custome_type，则组件名称为DisplaySfaCustomeType。
- welcome插槽命名规范，驼峰命名并以Welcome开头，组件通过传入的welcomeType来匹配
例如：WelcomeTest

## paas文档

- [agent-chat组件](https://fe.firstshare.cn/paas-fe-biz-doc/components/ai/agent/AgentChat.html)
- [agent聊天server接口](https://wiki.firstshare.cn/pages/viewpage.action?pageId=537495356)
