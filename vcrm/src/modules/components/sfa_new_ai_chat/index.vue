<template>
  <div class="sfa-new-ai-chat-container">
    <AgentChat
        class="agent-chat"
        ref="agentChat"
        :api-name="agentApiName"
        :session-id="getBizSessionId()"
        :variables="cVariables"
        v-bind="$attrs"
        v-on="$listeners"
    >
        <template #welcome="{ agentData, message }">
            <component :is="welcomeType" :agentData="agentData" @send-message="setMessage" />
        </template>
        <template #displayData="{ agentData, message, type }">
            <component :is="formatDisplayComponentName(type)" :agentData="agentData" :message="message" @send-message="setMessage" />
        </template>
    </AgentChat>
  </div>
</template>

<script>
import components from './components';

// 业务开发的卡片组件需要同时提供给全局gpt注册，这里统一导出
export const getComponents = (type) => {
    return components[type];
}

export default {
    name: 'SfaNewAiChat',
    props: {
        welcomeType: {
            type: String,
            default: ''
        },
        agentApiName: {
            type: String,
            default: ''
        },
        sessionId: {
            type: String,
            default: ''
        },
        sessionIdVariables: {
            type: Array,
            default: () => ([])
        },
        variables: {
            type: Object,
            default: () => ({})
        },
        // 引用来源，用于区分不同的引用来源：详情页、其他AI应用等，方便后期埋点等
        from: {
            type: String,
            required: true
        }
    },
    components: {
        ...components,
        AgentChat: () =>
            Fx.getBizComponent("paasdev", "AgentChat").then((res) => res())
    },
    data() {
        return {
        };
    },
    computed: {
        cVariables() {
            return Object.entries(this.variables)
                .map(([name, value]) => ({
                    name,
                    value
                }));
        },
    },
    created() {
        console.log('SfaNewAiChat created');
    },
    methods: {
        setMessage(msg) {
            this.$refs.agentChat.sendMessage(msg);
        },
        getBizSessionId() {
            if (this.sessionId) {
                return this.sessionId;
            }
            const { enterpriseAccount, employeeID } = FS.contacts.getCurrentEmployee() || {}
            const isCross = this.isCross();
            // 下游取ERUpstreamEa
            const ea = isCross ? $.cookie('ERUpstreamEa') : enterpriseAccount;
            // 下游取EROuterUid
            const empId = isCross ? $.cookie('EROuterUid') : employeeID;
            const _sessionId = [ea, empId, this.agentApiName, this.from, ...this.sessionIdVariables].filter(Boolean).join('_');
            console.log('_sessionId', _sessionId);
            return _sessionId;
        },
        // 是否下游
        isCross() {
            return window?.PAAS?.app?.isCross();
        },
        // 根据type转换为相应组件名，type为sfa_custome_type，则组件名称为DisplaySfaCustomeType
        formatDisplayComponentName(type) {
            if (!type) return '';
            
            return 'Display' + type
                .split('_')
                .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
                .join('');
        },
        getComponent(type) {
            return components.getComponent(type);
        }
    }
};
</script>

<style lang="less">
.sfa-new-ai-chat-container {
    width: 100%;
    height: 800px;

    .agent-chat {
        width: 100%;
        height: 100%;
    }
}
</style>
