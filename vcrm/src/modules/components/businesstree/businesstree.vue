<template>
    <div class="crm-tree-wrapper">
        <fx-resize-sensor class="crm-tree-resize" v-if="resizeIsShow" @resized="onResize" :debounce="500"></fx-resize-sensor>
        <div class="crm-tree-container" ref="treeContainer">
            <div v-if="isShowSearchTree" class="crm-tree-searchtree" :style="{width: leftWidth + 'px'}"></div>
            <div v-if="isShowSearchTree" class="crm-tree-line" @mousedown="startDrag"></div>
            <div class="crm-tree-compacttree" ></div>
        </div>
        <div class="crm-treechart-footer">
            <div ref="crmTreechartMinimap" class="crm-treechart-minimap"></div>
            <div v-if="showZoom" class="crm-treechart-ftool">
                <div class="crm-treechart-minimap-icon minimap-icon-active" @click="onToggleMiniMap"><span class="fx-icon-tuliweizhi1"></span></div>
                <zoom-tool ref="zoomTool" @onZoom="onZoom" :zoomValue="zoomValue"></zoom-tool>
            </div>
        </div>
    </div>
</template>

<script>
import { requireTable } from '@common/require';
import CompactTree from '../g6/tree';
import SearchTree from './component/search-tree.js';
import api from './common/api';
import getActionApi from './common/actionapi';
import parse from './common/parse';
import directAction from './action/action';
import ZoomTool from '../treechart/components/zoomtool.vue';

export default {
    components: {
      ZoomTool,
    },
    props: {
        fullStatus: {
            type: Boolean,
            default: false,
        },
        relatedFieldsConfig: {
            type: Object,
            default: {
                showFieldName: true,
                showFields: [
                    "name",
                    // "field_IlLB1__c",  //查找关联
                    // "field_ZVIQ2__c",  //网址
                    // "industry_level1",  //单选
                    // "field_Z2Xi0__c"   //多选
                ]
            }
        },
        relatedObjectDataId: {
            type: String,
            default: '',
        },
        relatedObjectDataName: {
            type: String,
            default: '',
        },
        treeObjectApiName: {
            type: String,
            default: 'AccountTreeRelationObj',
        },
        relatedObjectApiName: {
            type: String,
            default: 'AccountMainDataObj'
        },
        relatedObjectDisplayName: {
            type: String,
            default: $t('crm.客户主数据'),
        },
        threshold: {
            type: Number,
            default: 150,
        },
        curId: {
            type: String,
            default: '*********',
        },
        orgTreeCompInfo: {
            type: Object,
            default: null,
        },
        apiName: {
            type: String,
            default: '',
        },
        dataId: {
            type: String,
            default: '',
        },
        isShowSearchTree: {
            type: Boolean,
            default: true,
        },
        noRelatedTip: {
            type: String,
            default: '',
        },
        pApi: {
            type: Object,
            default: api,
        },
        pActionMap: {
            type: Object,
            default: {},
        },
        pTipMap: {
            type: Object,
            default: {},
        },
        pluginTooltipConfig: {
            type: Object,
            default: {
                offsetX: -200,
                offsetY: -10,
            },
        },
        // 控制放大缩小组件展示
        showZoom: {
            default: true,
            type: Boolean,
        },
    },
    computed: {
        // noRelatedTip() {
        //     const map = {
        //         AccountMainDataObj: $t('crm.g6tree.nomaindata'),
        //         AccountObj: $t('crm.g6tree.noaccount'),
        //     }
        //     return map[this.relatedObjectApiName] || '';
        // },
    },
    data() {
        return {
            // noRightTip: $t('crm.g6tree.noright'),
            actionMap: _.extend({
                node: 'viewOrg',
                noRelated: CRM.util.isGrayScale('CRM_ACCOUNTTREE_NOCREATE') ? '' : 'newObj',
            }, this.pActionMap),
            tipMap: _.extend({
                noRight: $t('crm.g6tree.noright'),
                noRelated: '',
            }, this.pTipMap),
            lazy: true,
            resizeIsShow: true,
            isDragging: false,
            leftWidth: 220, // 初始左侧宽度，您可以根据需要设置
            source: 'relationTree',
            zoomValue: 1,
        }
    },
    mounted() {
        this.init();
    },
    methods: {

        onToggleMiniMap(e) {
            $(e.currentTarget).toggleClass('minimap-icon-active');
            $(this.$refs.crmTreechartMinimap).toggle();
        },
        onZoom(val) {
            this.rTree && this.rTree.zoomTo(val, false, false);
        },
        async init() {
            await this.getDescribeLayout();
            // 提前加载省市区数据和table helper，防止后面每次解析都调用;
            await CRM.util.getCountryAreaOptions();
            await requireTable();
            this.renderTree();
        },
        async getDescribeLayout() {
            const res = await CRM.util.getObjectDescribe(this.relatedObjectApiName);
            this.describeLayout = parse.parseDescribeLayout(res.objectDescribe, this.relatedFieldsConfig.showFields);
        },
        async renderTree() {
            const data = await this.fetchTreeData();
            const treeData = await this.parseData(data);
            this.baseInfo = this.getBaseInfo();
            this.treeAction = this.getTreeAction();
            console.log(treeData, 'treeData-----------------')
            this.initCompactTree(treeData);
            this.isShowSearchTree && this.initSearchTree(JSON.parse(JSON.stringify(treeData)));
        },
        parseData(data) {
            return parse.parseData(data, this);
        },
        fetchTreeData() {
            return this.pApi.fetchTreeData({
                "describeApiName": this.relatedObjectApiName,
                "id": this.curId, // 所选节点ID
                "threshold": this.threshold, // 端上单次查询所能承受的最大节点数，如果总结点数低于这个数则返回所有数据
                "limit": 5, // 每个节点展开兄弟节点 分页limit
                "offset": 0, // 每个节点展开兄弟节点 分页offset
                "with_fields": this.relatedFieldsConfig.showFields,
            })
        },
        getTreeAction() {
            const actionApi = getActionApi(this.pApi);
            return directAction(actionApi, parse);
        },
        initCompactTree(data) {
            if (this.compactTree) {
                this.rTree.clear();
                this.rTree.changeData(data);
                this.fitCenter(data);
                return;
            }
            const _options = this.parseCompactTreeOptions({
                container: $('.crm-tree-compacttree', this.$el)[0],
                data: data,
                baseInfo: this.baseInfo,
            });
            this.compactTree = new CompactTree(_options)
            this.rTree = this.compactTree.graph;
            this.fitCenter(data);
            this.compactTree.on('treeAction', (action, e, context) => {
                const { item } = e;
                const model = item.getModel();
                this.treeAction[action] && this.treeAction[action](model, this, 'compactTree');
            })
        },
        fitCenter(data) {
            setTimeout(() => {
                // const item = this.rTree.findById(data.id);
                // console.log(111, item);
                // this.rTree.focusItem(item, false);
                this.rTree && this.rTree.fitCenter();
            })
            // this.rTree.moveTo(item.x, item.y);
            // if (this.curItem) {
            //     this.rTree.focusItem(this.curItem.id, false);
            //     // this.rTree.layout();
            // } else {
                // this.rTree.fitCenter();
            // }
        },
        parseDefaultTreeOptions(options) {
            let me = this;
            options.getConfig = function(config) {
                if(config?.pluginsConfig?.Tooltip?.config) {
                    config.pluginsConfig.Tooltip.config = Object.assign({}, config.pluginsConfig.Tooltip.config, me.pluginTooltipConfig)
                }
                if(config?.pluginsConfig?.Minimap?.config) {
                    config.pluginsConfig.Minimap.config = Object.assign({}, config.pluginsConfig.Minimap.config, {
                        container: me.$refs.crmTreechartMinimap,
                    })
                }
                return config;
            }
            return options;
        },
        parseCompactTreeOptions(options) {
            options = this.parseDefaultTreeOptions(options);
            return options;
        },
        initSearchTree(data) {
            this.searchTree && this.searchTree.destroy();
            const _options = this.parseSearchTreeOptions({
                el: $('.crm-tree-searchtree', this.$el)[0],
                data: [data],
                baseInfo: this.baseInfo,
                treeParse: parse,
                api: this.pApi,
            });
            this.searchTree = new SearchTree(_options);
            this.lTree = this.searchTree.$refs.tree;
            this.searchTree.$on('treeAction', (action, model, context) => {
                this.treeAction[action] && this.treeAction[action](model, this, 'searchTree');
            })
        },
        parseSearchTreeOptions(options) {
            return options;
        },
        getBaseInfo() {
            return this.parseBaseInfo({
                treeObjectApiName: this.treeObjectApiName,
                relatedFieldsConfig: this.relatedFieldsConfig,
                relatedObjectApiName: this.relatedObjectApiName,
                relatedObjectDisplayName: this.relatedObjectDisplayName,
                relatedObjectDataId: this.relatedObjectDataId,
                relatedObjectDataName: this.relatedObjectDataName,
                threshold: this.threshold,
                curId: this.curId,
                curItem: this.curItem,
                describeLayout: this.describeLayout,
                // noRelatedTip: this.noRelatedTip,
                // noRightTip: this.noRightTip,
                actionMap: this.actionMap,
                tipMap: this.tipMap,
                lazy: this.lazy,
                source: this.source,
            });
        },
        parseBaseInfo(info) {
            return info;
        },
        findModelById(id) {
            const model = this.rTree.findDataById(id);
            return model;
        },
        toggleCollapsed(model, source) {
            // 左侧树展开
            this.compactTree.toggleCollapsed(model.id);
            // 如果是右侧树展开收起控制左侧树，则左侧树的展开属性值未改变，如果为自身则已经改变
            this.lTree?.toggleCollapsed(model.id, source === 'compactTree');
        },
        setStraightTreeExpand(data, flag) {
            this.compactTree.setStraightTreeExpand(data._id, flag);
            data.parent_id && this.lTree?.expandNode(data.parent_id);
        },
        setSelected(id) {
            this.compactTree.setSelected(id);
            this.lTree?.setCurrentKey(id);
        },
        startDrag(event) {
            this.isDragging = true;
            document.addEventListener('mousemove', this.handleDrag);
            document.addEventListener('mouseup', this.stopDrag);
        },
        handleDrag(event) {
            if (this.isDragging) {
                // 计算拖拽后的左侧宽度
                const containerRect = $('.crm-tree-container', this.$el)[0].getBoundingClientRect();
                let newLeftWidth = event.clientX - containerRect.left;
                const max = containerRect.width * 0.5 > 430 ? containerRect.width * 0.5 : 430;
                const min = 220;
                if (newLeftWidth < min) {
                    newLeftWidth = min;
                } else if (newLeftWidth > max) {
                    newLeftWidth = max;
                }
                this.leftWidth = newLeftWidth;
            }
        },
        stopDrag() {
            this.isDragging = false;
            document.removeEventListener('mousemove', this.handleDrag);
            document.removeEventListener('mouseup', this.stopDrag);

            this.onResize();
        },
        onResize() {
            $('.crm-tree-compacttree', this.$el).width(this.getTreeWidth());
            this.compactTree && this.compactTree.onResize();
            this.$emit('resize');
        },
        getTreeWidth() {
            return $('.crm-tree-container', this.$el).width() - $('.crm-tree-searchtree', this.$el).width() - $('.crm-tree-line', this.$el).width();
        },
        destroy() {
            this.compactTree && this.compactTree.destroy();
            this.searchTree && this.searchTree.destroy();
            this.$destroy();
        }
    },
    watch: {
        curId(val, oldVal) {
            if (val == oldVal) return;
            this.renderTree();
        },
        fullStatus(val, oldVal) {
            if (val == oldVal) return;
            this.resizeIsShow = false;
            this.$nextTick(() => {
                this.resizeIsShow = true;
            });
        }
    }
}
</script>

<style lang="less">
.crm-tree-wrapper{
    height: 100%;
    width: 100%;
    .crm-tree-resize{
        position: absolute;
        height: 100%;
        width: 100%;
        z-index: -1;
        iframe{
            height: 100%;
        }
    }
    .crm-treechart-footer{
        position: absolute;
        bottom: 5px;
        right: 5px;
        // background: #fff;
        text-align: center;
    }
    .crm-treechart-minimap{
        // width: 200px;
        // height: 120px;
        background: #fff;
        display: inline-block;
        margin: 0 auto;
        border-radius: 5px;
        border: 1px solid #DEE1E8;
        box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.15);
        canvas {
            display: block;
        }
        .g6-minimap{
          // position: absolute;
          // right: 5px;
          // bottom: 34px;
          .g6-minimap-viewport {
            border: 1px solid #189DFF;
            outline: none !important;
            border-radius: 3px;
          }
        }
    }
    .crm-treechart-ftool{
        background: #fff;
        border-radius: 3;
        display: flex;
        width: 300px;
        margin-top: 10px;
    }
    .crm-treechart-minimap-icon{
      font-size: 16px;
      width: 24px;
      height: 24px;
      text-align: center;
      border-radius: 4px;

      &.minimap-icon-active{
          background-color: #F2F4FB;
      }
    }
    .crm-treechart-zoom{
      display: flex;
      flex: 1;
      height: 24px;
      line-height: 24px;
      > div{
          margin-left: 6px;
      }
    }
    .crm-treechart-zoom-slider{
      flex: 1;
      .el-slider__bar,.el-slider__runway{
          height: 2px;
      }
      .el-slider__runway{
          margin:11px 0;
      }
      .el-slider__button-wrapper{
          width: 24px;
          height: 24px;
          top: -10px;
      }
      .el-slider__button{
          width: 10px;
          height: 10px;
      }
    }
    .crm-treechart-zoom-minus, .crm-treechart-zoom-plus{
      font-size: 12px;
      width: 24px;
      height: 24px;
      text-align: center;
    }
    .crm-treechart-zoom-percent{
      font-size: 12px;
    }
    .g6-component-contextmenu{
      li{
          cursor: pointer;
      }
    }
}
.crm-tree-container{
    display: flex;
    align-items: stretch;
    height: 100%;
    width:100%;
    background-color:#fff;
    overflow: hidden;
    .crm-tree-searchtree{
        // width: 220px;
        flex:0 0 auto;
    }
    .crm-tree-line{
        // border-right:#DEE1E8 solid 1px;
        border-left: #fff solid 2px;
        border-right: #fff solid 2px;
        flex: 0 0 1px;
        background-color:#DEE1E8;
        cursor: col-resize; /* 显示调整宽度的鼠标样式 */
        &:hover{
            background-color: #189DFF;
        }
    }
    .crm-tree-compacttree{
        flex: 1 0 auto;
        position: relative;
        // width: calc(100% - 221px);
    }
    .hight-light {
        color: --color-primary06
    }
}
.g6-component-tooltip {
    border: 1px solid #e2e2e2;
    border-radius: 4px;
    font-size: 12px;
    color: #545454;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    box-shadow: rgb(174, 174, 174) 0px 0px 10px;
}
// .crm-tree-compacttree{
//   .g6-minimap{
//     position: absolute;
//     //right: 5px;
//     //bottom: 5px;
//     right: 35px;
//     bottom: 35px;
//     background: #fff;
//     border-radius: 5px;
//     border: 1px solid #DEE1E8;
//     box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.15);
//     canvas {
//         display: block;
//     }
//   }
// }
.g6-minimap-viewport {
    border: 1px solid #189DFF;
    outline: none !important;
    border-radius: 3px;
}


</style>
