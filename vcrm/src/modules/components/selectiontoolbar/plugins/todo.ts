import { Plugin, SelectionInfo } from '../types';
import { getRangesContent } from '../utils/dom';

/**
 * 待办插件：点击后弹出任务创建弹窗
 */
export const todoPlugin: Plugin = {
  name: 'todo',
  title: $t('待办'),
  icon: 'fx-icon-obj-app352',
  validate(selection: Selection, info?: SelectionInfo) {
    // 始终可用
    return true;
  },
  async action(selection: Selection, info?: SelectionInfo, extraData?: any) {
    let apinames = [extraData.apiName];
    _.each(extraData.detailData.related_object_data, (item: any) => {
        apinames.push(item?.describe_api_name);
    });
    apinames = _.uniq(apinames);

    CRM.util.getMultipleObjectName(apinames)
    .then((res: any) => {
        let objectNamesMap = res;
        addTask(Selection, objectNamesMap);
    })

    function addTask(item: any, objectNamesMap: any) {
        const obj: any = {};
        // 其他whatlist字段
        _.each(extraData.detailData.related_object_data, (item: any) => {
            if (!obj[item.describe_api_name]) {
                obj[item.describe_api_name] = {
                    objectType: item.describe_api_name,
                    name: objectNamesMap[item.describe_api_name],
                    data:[],
                }
            }
            obj[item.describe_api_name].data.push({
                name: item.name,
                id: item.id,
            });
        })
        // 添加销售记录
        if (!obj[extraData.apiName]) {
            obj[extraData.apiName] = {
                objectType: extraData.apiName,
                name: objectNamesMap[extraData.apiName],
                data:[],
            }
        }
        if (!_.findWhere(obj[extraData.apiName].data, {id: extraData.detailData._id})) {
            obj[extraData.apiName].data.push({
                name: extraData.detailData.name,
                id: extraData.detailData._id,
            })
        }

        let selectionText = getRangesContent(info?.selectableRanges || []);

        CRM.api.add_taskobj({
            feedContent: [{
                text: selectionText,
            }],
            title: '',
            executer: {
                member: '',
            },
            deadLine: '',
            crmobjectOpts: _.values(obj),
            success(){}
        })
    }
  }
}; 