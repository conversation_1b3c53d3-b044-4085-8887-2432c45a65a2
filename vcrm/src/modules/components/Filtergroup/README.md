# Filtergroup Vue 组件

这是一个将 Backbone FilterGroup 组件封装为 Vue 组件的实现，支持基于 Backbone 对象继承的方式扩展功能。

## 功能特性

1. **Backbone 集成**: 异步加载并封装原有的 Backbone FilterGroup 组件
2. **继承扩展**: 支持通过 `extendConfig` 属性扩展 Backbone 组件功能
3. **响应式**: 支持 Vue 的响应式数据绑定和监听
4. **事件桥接**: 将 Backbone 事件桥接为 Vue 事件
5. **生命周期管理**: 自动处理组件的创建和销毁

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `options` | Object | `{}` | FilterGroup 的基础配置选项（包含 fields、apiname 等所有配置） |
| `defaultValue` | String/Array/Object | `null` | 默认值 |
| `extendConfig` | Object | `{}` | 扩展类配置，用于继承扩展 |
| `disabled` | Boolean | `false` | 是否禁用 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `ready` | `filterInstance` | FilterGroup 实例准备就绪 |
| `error` | `error` | 组件加载出错 |
| `initialized` | `filterInstance` | FilterGroup 初始化完成 |
| `rendered` | `filterInstance` | FilterGroup 渲染完成 |
| `value-change` | `value` | 值发生变化 |
| `filter-render` | `...args` | 筛选器渲染事件 |
| `filter-change` | `...args` | 筛选器变化事件 |
| `change-objects` | `...args` | 对象变化事件 |
| `change-props` | `...args` | 属性变化事件 |

## 方法

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `getValue()` | - | String | 获取筛选器的值 |
| `valid()` | - | Boolean | 验证筛选器是否有效 |
| `dataAllEmpty()` | - | Boolean | 检查数据是否全部为空 |
| `resetRender(defaultValue)` | `defaultValue` | - | 重置渲染 |
| `addFilter(defaultValue)` | `defaultValue` | Filter | 添加筛选器 |
| `removeFilter(filter)` | `filter` | - | 移除筛选器 |
| `removeAllFilters()` | - | - | 移除所有筛选器 |

## 基础使用示例

```vue
<template>
  <div>
    <Filtergroup
      :options="filterOptions"
      @ready="onFilterReady"
      @filter-change="onFilterChange"
      ref="filtergroup"
    />

    <button @click="getFilterValue">获取筛选值</button>
    <button @click="validateFilter">验证筛选器</button>
  </div>
</template>

<script>
import Filtergroup from '@/modules/components/Filtergroup/Filtergroup.vue'

export default {
  components: {
    Filtergroup
  },
  data() {
    return {
      filterOptions: {
        apiname: 'SomeObject',
        fields: {
          // 字段配置
        },
        AND_MAX: 5,
        OR_MAX: 3,
        width: 800,
        needLookUp: true,
        lastSelectCanEmpty: true
      }
    }
  },
  methods: {
    onFilterReady(filterInstance) {
      console.log('Filter ready:', filterInstance);
    },
    
    onFilterChange(...args) {
      console.log('Filter changed:', args);
    },
    
    getFilterValue() {
      const value = this.$refs.filtergroup.getValue();
      console.log('Filter value:', value);
    },
    
    validateFilter() {
      const isValid = this.$refs.filtergroup.valid();
      console.log('Filter valid:', isValid);
    }
  }
}
</script>
```

## 扩展使用示例

```vue
<template>
  <Filtergroup
    :options="filterOptions"
    :extend-config="extendConfig"
    @ready="onFilterReady"
  />
</template>

<script>
import Filtergroup from '@/modules/components/Filtergroup/Filtergroup.vue'

export default {
  components: {
    Filtergroup
  },
  data() {
    return {
      filterOptions: {
        apiname: 'SomeObject',
        fields: {},
        width: 800,
        OR_MAX: 1,
        lastSelectCanEmpty: true,
        layoutMode: 'custom',
        getCustItemWidth(level) {
          return [183, 183, 183][level];
        },
        formatGetItem(item) {
          const [field_name, operator, field_values, type] = item;
          if (type === 'object_reference') {
            item[0] = field_name + '.name';
          }
        }
      },
      // 扩展配置 - 类似于参考文件中的做法
      extendConfig: {
        // 重写 getValue 方法
        getValue() {
          const value = this.constructor.__super__.getValue.apply(this, arguments);
          if (value) {
            const filters = JSON.parse(value)?.[0]?.filters || [];
            if (filters.length > 0) {
              return JSON.stringify({
                limit: 2000,
                offset: 0,
                orders: [{isAsc: false, fieldName: "last_modified_time"}],
                filters,
              });
            }
          }
          return null;
        },
        
        // 可以添加其他扩展方法
        customMethod() {
          console.log('This is a custom method');
        }
      }
    }
  },
  methods: {
    onFilterReady(filterInstance) {
      console.log('Extended filter ready:', filterInstance);
      // 可以调用扩展的方法
      if (filterInstance.customMethod) {
        filterInstance.customMethod();
      }
    }
  }
}
</script>
```

## 注意事项

1. **异步加载**: 组件会异步加载 Backbone FilterGroup，在 `ready` 事件触发前实例不可用
2. **扩展继承**: `extendConfig` 中的方法会通过 `FilterGroup.extend()` 方式扩展到 Backbone 类
3. **事件桥接**: Backbone 事件会自动桥接为 Vue 事件，保持原有的事件机制
4. **生命周期**: 组件销毁时会自动清理 Backbone 实例，避免内存泄漏
5. **样式继承**: 原有的 CSS 样式会通过 `:deep()` 选择器继承

## 与参考实现的对应关系

这个组件的设计参考了 `utils.js` 中 `index_goal_data_condition` 的实现模式：

- `customRender` → Vue 组件的 `initFilterGroup` 方法
- `FilterGroup.extend()` → `extendConfig` 属性
- 异步加载和错误处理 → 组件内置的加载状态管理
- 实例管理 → Vue 组件的生命周期管理
