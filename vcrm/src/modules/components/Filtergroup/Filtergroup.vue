<template>
    <div class="filtergroup-container">
        <div class="filtergroup-wrapper" :class="{'disabled': disabled}" ref="filterWrapper"></div>
    </div>
</template>
<script>
import crmRequire from '@common/require'

export default {
    name: 'Filtergroup',
    props: {
        // 基础配置选项
        options: {
            type: Object,
            default: () => ({})
        },
        // 默认值
        value: {
            type: [String, Array, Object],
            default: null
        },
        // 扩展类配置 - 用于继承扩展
        extendConfig: {
            type: Object,
            default: () => ({})
        },
        // 是否禁用
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            filterInstance: null,
            loading: false,
            error: null
        }
    },
    computed: {
    },
    watch: {
        // 监听配置变化，重新渲染
        // options: {
        //     handler() {
        //         this.initFilterGroup();
        //     },
        //     deep: true
        // },

        // value: {
        //     handler(newVal) {
        //         if (this.filterInstance) {
        //             this.filterInstance.resetRender(this.formatDefaultValue(newVal));
        //         }
        //     },
        //     deep: true
        // },
        disabled(newVal) {
            if (this.filterInstance && this.filterInstance.$el) {
                this.filterInstance.$el.toggleClass('disabled', newVal);
            }
        }
    },
    mounted() {
        this.initFilterGroup();
    },
    beforeDestroy() {
        this.destroyFilterGroup();
    },
    methods: {
        // 初始化 FilterGroup
        async initFilterGroup() {
            if (this.loading) return;

            try {
                this.loading = true;
                this.error = null;

                // 销毁现有实例
                this.destroyFilterGroup();

                // 异步加载 FilterGroup
                const FilterGroup = await crmRequire('crm-modules/common/filtergroup/filtergroup');

                // 创建扩展类
                const ExtendedFilterGroup = this.createExtendedFilterGroup(FilterGroup);

                // 准备配置选项，在这里动态设置 $wrapper
                const options = {
                    $wrapper: $(Array.isArray(this.$refs.filterWrapper) ? this.$refs.filterWrapper[0] : this.$refs.filterWrapper),
                    defaultValue: this.formatDefaultValue(this.value),
                    ...this.options
                };

                // 创建实例
                this.filterInstance = new ExtendedFilterGroup(options);

                // 绑定事件
                this.bindEvents();

                this.$emit('ready', this.filterInstance);

            } catch (error) {
                console.error('FilterGroup initialization failed:', error);
                this.error = error.message || '组件加载失败';
                this.$emit('error', error);
            } finally {
                this.loading = false;
            }
        },

        // 创建扩展的 FilterGroup 类
        createExtendedFilterGroup(FilterGroup) {
            const vm = this;

            // 基础扩展配置
            const baseExtendConfig = {
                // 重写初始化方法以支持 Vue 集成
                initialize() {
                    FilterGroup.prototype.initialize.apply(this, arguments);
                    vm.$emit('initialized', this);
                },

                // 重写渲染方法
                render() {
                    const result = FilterGroup.prototype.render.apply(this, arguments);
                    vm.$emit('rendered', this);
                    return result;
                },

                // 扩展 getValue 方法
                getValue() {
                    const value = FilterGroup.prototype.getValue.apply(this, arguments);
                    vm.$emit('value-change', value);
                    return value;
                }
            };

            // 合并用户自定义扩展配置
            const finalExtendConfig = {
                ...baseExtendConfig,
                ...this.extendConfig
            };

            return FilterGroup.extend(finalExtendConfig);
        },

        // 绑定事件
        bindEvents() {
            if (!this.filterInstance) return;

            // 绑定 Backbone 事件到 Vue 事件
            this.filterInstance.on('render', (...args) => {
                this.$emit('filter-render', ...args);
            });

            this.filterInstance.on('change', (...args) => {
                this.$emit('filter-change', ...args);
            });

            this.filterInstance.on('change.objects', (...args) => {
                this.$emit('change-objects', ...args);
            });

            this.filterInstance.on('change.props', (...args) => {
                this.$emit('change-props', ...args);
            });
        },

        // 格式化默认值
        formatDefaultValue(value) {
            if (!value) return null;

            // 如果是字符串，尝试解析为 JSON
            if (typeof value === 'string') {
                try {
                    return JSON.parse(value);
                } catch (e) {
                    console.warn('Failed to parse defaultValue as JSON:', value);
                    return null;
                }
            }

            // 如果是数组或对象，直接返回
            if (Array.isArray(value) || typeof value === 'object') {
                return value;
            }

            return null;
        },

        // 销毁 FilterGroup 实例
        destroyFilterGroup() {
            if (this.filterInstance) {
                try {
                    this.filterInstance.destroy();
                } catch (e) {
                    console.warn('Error destroying FilterGroup:', e);
                }
                this.filterInstance = null;
            }
        },

        // 公共方法：获取值
        getValue() {
            return this.filterInstance ? this.filterInstance.getValue() : null;
        },

        // 公共方法：验证
        valid() {
            return this.filterInstance ? this.filterInstance.valid() : false;
        },

        // 公共方法：检查数据是否全部为空
        dataAllEmpty() {
            return this.filterInstance ? this.filterInstance.dataAllEmpty() : true;
        },

        // 公共方法：重置渲染
        resetRender(value) {
            if (this.filterInstance) {
                this.filterInstance.resetRender(this.formatDefaultValue(value));
            }
        },

        // 公共方法：添加筛选器
        addFilter(value) {
            if (this.filterInstance) {
                return this.filterInstance.addFilter(this.formatDefaultValue(value));
            }
            return null;
        },

        // 公共方法：移除筛选器
        removeFilter(filter) {
            if (this.filterInstance) {
                this.filterInstance.removeFilter(filter);
            }
        },

        // 公共方法：移除所有筛选器
        removeAllFilters() {
            if (this.filterInstance) {
                this.filterInstance.removeAllFilters();
            }
        }
    },
}
</script>

<style scoped>
</style>