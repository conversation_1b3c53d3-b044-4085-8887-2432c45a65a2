<template>
    <div
        class="media-viewer__wrapper"
        :class="{
            'is-show': showPanel,
            [`mediaType-${mediaType}`]: true
        }"
    >
        <div class="media-viewer__cover" @click="close"></div>
        <div
            class="media-viewer__container"
            :style="{ width: genStyles(width), height: genStyles(height) }"
        >
            <video
                class="tcplayer"
                :id="tcplayerId"
                preload="auto"
                playsinline
                webkit-playsinline
            ></video>
        </div>
        <a
            href="javascript:;"
            class="media-viewer__close fx-icon-close"
            @click="close"
        ></a>
    </div>
</template>

<script>
const Dialog = Vue.extend({
    name: 'TencentCmptmedia',
    template: '__template__',
    props: {
        url: {
            type: String,
            default: ''
        },
        width: {
            type: Number || String,
            default: '80%'
        },
        height: {
            type: Number || String,
            default: '60%'
        },
        mediaType: {
            type: String,
            default: 'video'
        }
    },
    data () {
        return {
            showPanel: false,
            player: null,
            timer: null,
            tcplayerId: 'tcplayer' + Date.now()
        }
    },
    watch: {},
    created () {
        this.$nextTick(() => {
            this.loadCDN()
        })
    },
    beforeDestroy () {
        this.destroyPlayer()
    },
    methods: {
        close () {
            this.showPanel = false
            this.$emit('cancel')
        },
        initTCPlayer () {
            const me = this
            let options = {
                controlBar: {
                    playToggle: true,
                    progressControl: true,
                    currentTimeDisplay: true,
                    fullscreenToggle: true,
                    durationDisplay: true
                },
                autoplay: true
            }
            seajs.use(
                'https://web.sdk.qcloud.com/player/tcplayer/release/v4.9.1/tcplayer.v4.9.1.min.js',
                () => {
                    me.player = new TCPlayer(me.tcplayerId, options)
                    me.player.src(me.url)
                }
            )
        },
        loadCDN () {
            const me = this
            seajs.use(
                'https://web.sdk.qcloud.com/player/tcplayer/release/v4.9.1/tcplayer.min.css'
            )
            seajs.use(
                'https://web.sdk.qcloud.com/player/tcplayer/release/v4.9.1/libs/hls.min.1.1.7.js',
                () => me.initTCPlayer()
            )
        },

        play () {
            this.player.play()
        },
        pause () {
            this.player.pause()
        },
        stop () {
            this.player.stop()
        },
        destroyPlayer () {
            this.player && this.player.dispose()
            this.player = null
        },
        genStyles (value) {
            let target = value
            if (/\d/.test(value)) {
                if (!/px|%/.test(value)) {
                    target = `${value}px`
                }
            } else {
                target = 'auto'
            }
            return target
        }
    }
})
Dialog.$show = (propsData = {}) => {
    return new Promise((resolve, reject) => {
        const $vm = new Dialog({
            el: document.createElement('div'),
            propsData
        })

        $vm.$on('hide', (...args) => {
            $vm.showPanel = false
            setTimeout(() => {
                $vm.$destroy()
                $vm.$el.remove()
            }, 1000)
        })

        $vm.$on('submit', (...args) => {
            $vm.$emit('hide')
            resolve(...args)
        })
        $vm.$on('cancel', (...args) => {
            $vm.$emit('hide')
            reject(...args)
        })

        $('body').append($vm.$el)
        setTimeout(() => {
            $vm.showPanel = true
        }, 20)
        $('body').append($vm.$el)
    })
}

module.exports = Dialog
</script>

<style lang="less" scoped>
.media-viewer__wrapper {
    position: fixed;
    z-index: 10000;
    opacity: 0;
    transition: all 0.5s ease-in-out;
    pointer-events: none;

    .media-viewer__close {
        color: #fff;
        position: absolute;
        top: 10px;
        right: 10px;
        height: 20px;
        width: 20px;
        text-align: center;
        line-height: 20px;
        font-size: 12px;
        text-decoration: none;
        z-index: 1000;
        border-radius: 20px;
        background-color: rgba(0, 0, 0, 0.5);
        transition: all 0.5s ease-in-out;
        &:hover {
            background-color: rgba(0, 0, 0, 0.8);
            transform: rotate(180deg);
        }
        &::before {
            color: #fff;
        }
    }
    &,
    .media-viewer__cover {
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    .media-viewer__cover {
        position: absolute;
        background-color: rgba(0, 0, 0, 0.5);
    }
    .media-viewer__container {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, 0);
        transition: all 0.5s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
        .tcplayer {
            width: 100%;
            height: 100%;
        }
    }
    &.is-show {
        pointer-events: all;
        opacity: 1;
        .media-viewer__container {
            transform: translate(-50%, -50%);
        }
    }
}
</style>
