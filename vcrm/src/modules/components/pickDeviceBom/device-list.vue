<template>
    <div
        class="pick-device-bom-menu-list"
        v-infinite-scroll="loadDataList"
        :infinite-scroll-disabled="hasShowAll"
        :infinite-scroll-distance="20"
        :infinite-scroll-immediate="false"
    >
        <div class="pick-device-bom-menu-list-cell"
             :class="{ 'cell-active': currentIndex == index }"
             @click="handleLeftClick(data)"
             v-for="(data, index) in dataList"
             :key="index"
        >
            <div class="selected-num">{{data.__selectedNum > 0 ? ($t('fx.selector.selected') + ' ' + data.__selectedNum) : ''}}</div>
            <div class="pick-device-bom-menu-list-cell-header list-cell-row-wh">{{ data[titleFieldApiName] }}</div>
            <ul class="pick-device-bom-menu-list-cell-content">
                <li class="list-cell-row-wh" v-for="item in displayFields" :key="item.fieldApiName">
                    <span class="list-cell-content-name">{{ item.fieldTextName }}</span>
                    <span class="list-cell-content-value">{{ data[item.fieldApiName] }}</span>
                </li>
            </ul>
        </div>

        <p
            v-loading="loading"
            :fx-loading-text="$t('数据加载中...')"
            fx-loading-spinner="el-icon-loading"
            class="device-list-p-loading"
        ></p>
        <p v-if="hasShowAll" class="device-list-p-nomore">{{ $t('没有更多了') }}</p>
    </div>
</template>

<script>
import getConfig from './config';
let config = null;

export default {
    name: "deviceList",
    props: {
        // 左栏根节点id
        rootIds: {
            type: Array,
            default: [],
        },
        // DeviceObj、ServiceBomObj
        apiName: {
            type: String,
            default: "DeviceObj",
        }
    },
    data() {
        return {
            currentIndex: 0,
            offset: 0, // 获取左栏数据时的偏移量
            dataList: [], // 左栏的所有数据
            loading: true, // 是否正在加载数据
            titleFieldApiName: '', // 每一项的标题取的是哪个字段的值
            displayFields: [], // 展示的字段
            hasShowAll: false, // 是否已经展示所有数据
        };
    },

    created() {
        config = getConfig(this.apiName);
        // 获取每一项的标题取的字段apiName
        this.titleFieldApiName = config.leftSectionFields[0].fieldApiName;
        // 除去第一个，第一项由于当做标题字段
        this.displayFields = config.leftSectionFields.slice(1);
    },

    methods: {
        handleLeftClick(rowData) {
            let curIndex;
            // 获取当前选中根节点的索引
            this.dataList.forEach((item, index) => {
                if (rowData._id === item._id) {
                    curIndex = index;
                }
            });
            // 通知父组件
            this.$emit("tabclick", {
                index: curIndex,
                rootId: rowData._id,
                rowData: rowData,
                rootProductId: rowData.product_id
            });
        },

        // 改变当前 index，父组件通过 ref 调用该组件的该方法
        changeIndex(currenIndex) {
            this.currentIndex = currenIndex;
        },

        // 右栏勾选条数改变时，改变左栏显示的勾选数量值
        changeNum(num, isSingleCheck) {
            const temp = this.dataList[this.currentIndex]
            // 单选的话需要将其他已选数量置零
            isSingleCheck && !!num && this.dataList.forEach(item => {
                item.__selectedNum = 0;
            });
            temp.__selectedNum = num;
            // 强制更新
            this.$forceUpdate();
        },

        // 获取首次渲染的数据
        getFirstRenderData(data) {
            data.forEach(item => {
                // 初始化右栏已选数量为零
                item.__selectedNum = 0;
            });
            this.dataList = data;
            this.loading = false;
            if (data.length < 20) {
                // 接口一次性最多获取 20 条数据，如果少于 20 条，说明已经获取了所有的数据了
                this.hasShowAll = true;
            }
        },

        // 无限滚动 触底加载函数
        async loadDataList() {
            if (this.loading || this.hasShowAll) {
                return;
            }
            this.loading = true; // 防抖，防止用户滚动过，快加载数据太频繁
            this.offset += 20; // 每次向服务器请求获取20条数据
            let newDataList = [];
            try {
                const ids = this.rootIds;
                newDataList = await config.getLeftSectionData(ids, this.offset);
            } catch (err) {
                console.error(err);
            }
            newDataList.forEach(item => {
                // 初始化右栏已选数量为零
                item.__selectedNum = 0;
            });
            this.dataList.push(...newDataList);
            this.loading = false;
            if (newDataList.length < 20) {
                // 接口一次性最多获取 20 条数据，如果少于 20 条，说明已经获取了所有的数据了
                this.hasShowAll = true;
            }
        },
    }
};
</script>

<style lang="less" scoped>
.pick-device-bom-menu-list {
    background-color: #f6f7f9;
    width: 260px;
    height: 92vh;
    position: relative;
    // border-right: 1.5px solid #dcdcdc;
    // margin-right: 14px;
    transition: width 0.4s;
    overflow-y: auto;
    overflow-x: hidden;
    box-sizing: border-box;
    // 设置滚动条样式
    &::-webkit-scrollbar {
        width: 5px;
    }

    scrollbar-gutter: stable;

    &::-webkit-scrollbar-thumb {
        background-color: #bfbfbf;
        border-radius: 10px;
        width: 10px;
        height: 20px;
    }

    &::-webkit-scrollbar-thumb:hover {
        background-color: #999999;
    }

    .pick-device-bom-menu-list-cell {
        position: relative;
        // width: 100%;
        // height: 84px;
        padding: 8px;
        font-weight: 400;
        font-size: 12px;
        line-height: 18px;
        // border-bottom: 1px solid #E5E5E5;
        margin: 4px;
        border-radius: 8px;

        .list-cell-row-wh {
            display: flex;
            width: 239px;
            height: 18px;
            margin-top: 4px;
        }

        .selected-num {
            display: inline-block;
            padding: 0 4px;
            position: absolute;
            right: 20px;
            bottom: 8px;
            line-height: 14px;
            height: 14px;
            color: #FF522A;
            font-size: 12px;
            text-align: center;
        }

        .pick-device-bom-menu-list-cell-header {
            display: inline-block;
            width: 245px;
            margin-top: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            color: #181c25;
        }

        .pick-device-bom-menu-list-cell-content {
            span {
                display: inline-block;
            }

            .list-cell-content-name {
                width: 84px;
                color: #91959e;
            }

            .list-cell-content-value {
                flex: 1;
                color: #181c25;
                // 单行溢出省略号
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
    }

    .cell-active {
        background-color: var(--color-primary01);
        border: 1px solid var(--color-primary06);
        .pick-device-bom-menu-list-cell-header {
            font-weight: 700;
        }
    }

    .device-list-p-loading {
        width: 100%;

        /deep/ .el-loading-spinner {
            margin-top: 10px;

            .circular {
                width: 25px;
                height: 25px;
            }
        }
    }

    .device-list-p-nomore {
        width: 100%;
        height: 45px;
        text-align: center;
        line-height: 45px;
        color: #aaa;
    }
}
</style>
