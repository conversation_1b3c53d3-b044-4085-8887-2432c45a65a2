<template>
    <fx-dialog
        class="pick-device-bom-dialog"
        :fullscreen="true"
        :append-to-body="true"
        :closeOnClickModal="false"
        :z-index="zIndex"
        :visible.sync="visible"
        @closed="onClosed"
    >
        <!-- vue2.6开始，下面这种 slot属性语法 已经废弃，但2.x还是支持这种用法 -->
        <div slot="title">
            <h4 class="pick-device-bom-title">{{ title }}</h4>
        </div>
        <div class="p-content">
            <device-list
                ref="leftComp"
                :api-name="apiName"
                :root-ids="rootIds"
                @tabclick="onTabClick"
                v-if="isMultipleRoot"
            ></device-list>
            <div class="p-card" :class="{ 'p-card-full': !isMultipleRoot }">
                <div
                    v-for="item in rootIds"
                    :key="item"
                    v-show="rootId === item"
                    :class="[
                        'p-outline',
                        'pick-device-bom-dialog-content-' + item,
                        isMultipleRoot ? 'p-outline-multi' : '',
                    ]"
                ></div>
            </div>
        </div>
        <div slot="footer">
            <div class="pick-device-bom-btns">
                <fx-button type="primary" size="small" @click="onConfirm">{{ $t("确定")}}</fx-button>
                <fx-button size="small" @click="onClosed">{{ $t("取消") }}</fx-button>
            </div>
        </div>
    </fx-dialog>
</template>

<script>
import getConfig from "./config";
import deviceList from "./device-list";
import {requireTreeTable} from "@common/require";

let config = null;

export default {
    name: "pickDeviceBom",
    components: { deviceList },
    props: {
        apiName: {
            type: String,
            default: "DeviceObj",
        },
        title: {
            type: String,
            default: $t("stock.pickDeviceBom.info.text1"), // 选择设备资产BOM数据
        },
        caseId: {
            type: String,
            default: "",
        },
        // 调用方传入的根节点数据 id
        devices: {
            type: Array,
            default: () => [],
        },
        masterData: {
            type: Object,
            default: {},
        },
        handleConfirm: {
            type: Function,
            default: null,
        },
        isWholeBom: {
            type: Boolean,
            default: false,
        },
        // 右栏表格参数
        tableOption: {
            type: Object,
            default: () => {
                return {
                    onlyCheckOne: false, // 是否单选
                };
            },
        },
    },
    data() {
        return {
            tables: {}, // 右栏表格组件对象 { rootId: table }
            TableComponent: null, // 引用的 treetable 组件
            visible: true,
            rootIds: [], // 左栏根节点数据id
            rootId: "", // 当前右栏表格对应的根节点 id
            searchSelectVal: '',
            rootProductId: '', // 根节点 product_id
            rootProductData: [], // 首次加载时左栏根节点数据
            columns: [], // 右栏表格渲染的表头字段信息
            isRightLoading: false, // 右栏表格数据是否正在加载中
        };
    },
    computed: {
        // 获取页面当前的最高 zIndex 层级
        zIndex() {
            return CRM.util.getzIndex(FxUI.Utils.getPopupZIndex() || 800, true);
        },

        // 左栏根节点数据是否有多个
        isMultipleRoot() {
            return this.rootIds.length > 1;
        },
    },

    async created() {
        config = getConfig(this.apiName);
        // 获取需要展示的左栏根节点 id
        this.rootIds = await config.getRootIds(this.devices, this.caseId) || [];
        this.TableComponent = await requireTreeTable();
        // 首次加载时，先获取左栏根节点数据
        this.rootProductData = await config.getLeftSectionData(this.rootIds) || [];
        // 将首次获取的左栏数据作为初始化数据传入左栏，左栏进行渲染
        this.isMultipleRoot && this.$refs.leftComp.getFirstRenderData(this.rootProductData);
        // 获取左栏数据中第一条数据的信息
        const res = this.getFirstRootProductInfo(this.rootProductData) || {};
        this.rootId = res._id;
        this.rootProductId = res.product_id;
        // 获取右栏表头字段信息
        this.columns = await config.getColumns(this.isWholeBom);
        this.isRightLoading = true;
        // 渲染右栏表格
        await this.renderTable(this.rootId, this.rootProductId);
        this.isRightLoading = false;
    },

    beforeDestroy() {
        // 卸载搜索框
        this.titleInput.destroy();
        config = null;
    },

    methods: {
        // 根据获取的左栏根产品数据，拿到第一个数据对应的 id 和 product_id
        getFirstRootProductInfo(data) {
            let _id = '', product_id = '';
            if (data && data.length) {
                _id = data[0] && data[0]._id;
                product_id = data[0] && data[0].product_id;
            }
            return {_id, product_id};
        },

        // 创建根节点名称元素，并挂载
        createRootProductTitleDiv(title) {
            // 先删除原有的
            $('.root-product-title').remove();
            // 再创建并挂载
            const caption = $('.pick-device-bom-dialog .p-content .dt-caption');
            caption.prepend($(`<div class="root-product-title">${ title || '' }</div>`));
        },

        // 创建右栏表格的搜索框
        createTableSearch(target, treeTable) {
            let that = this; // 将当前 vue实例 暂存起来
            let params = {
                wrapper: target, // 目标容器
                // 模板内容
                template: `<fx-input
                            :placeholder="$t('vcrm.modules.components.pickDeviceBom.serach')"
                            v-model="inputVal"
                            :size="size"
                            clearable
                            is-search
                            :disabled="inputDisabled"
                            @on-search="onSearch"
                            @keyup.enter.native="onSearch"
                          >
                            <fx-select
                              v-model="selectVal"
                              :size="size"
                              :options="options"
                              slot="prepend"
                              :placeholder="$t('vcrm.modules.components.pickDeviceBom.please_select')"
                              @change="onSelectChange"
                            ></fx-select>
                          </fx-input>`,
                // 响应式数据
                data() {
                    return {
                        size: "small",
                        inputVal: "",
                        selectVal: that.apiName === 'DeviceObj' ? 'device_id.display_name' : 'product_id.name',
                        inputDisabled: false,
                        options: that.apiName === 'DeviceObj' ? [
                            {
                                value: "device_id.display_name",
                                label: $t('vcrm.modules.components.pickDeviceBom.parent_device') // 父设备
                            },
                            {
                                value: "device_part_id.name",
                                label: $t('vcrm.modules.components.pickDeviceBom.child_product') // 子产品
                            },
                            {
                                value: "accessory_material_id.display_name",
                                label: $t('vcrm.modules.components.pickDeviceBom.child_device') // 子设备
                            },
                            {
                                value: "parent_product_id.name",
                                label: $t('vcrm.modules.components.pickDeviceBom.parent_product') // 父产品
                            },
                        ] : [
                            {
                                value: "product_id.name",
                                label: $t('vcrm.modules.components.pickDeviceBom.product_name') // 产品名称
                            },
                            {
                                value: "product_code",
                                label: $t('vcrm.modules.components.pickDeviceBom.material.code') // 物料编码
                            }
                        ]
                    };
                },
                // 当前 vue组件 用到的方法
                methods: {
                    // 发送请求
                    requestSearch(data) {
                        let url = '', postData = null;
                        if (that.apiName === 'DeviceObj') {
                            url = "/EM1HNCRM/API/v1/object/accessory_path/service/query_all_device_components_bom_by_filter";
                            postData = data;
                        } else {
                            url = "/EM1HNCRM/API/v1/object/BOMObj/controller/TreeRelatedListV1";
                            postData = {
                                bom_core_id: that.rootId,
                                root_product_ids: [that.rootProductId],
                                extend_obj_desc_api_name: "ProductGroupObj",
                                child_search_query_info: JSON.stringify(data),
                                include_desc: false,
                                include_constraint: true,
                                object_data: that.masterData,
                                filter_empty_groups: true,
                                include_all_sub_core_id: true
                            }
                        }
                        return new Promise((resolve, reject) => {
                            CRM.util.FHHApi(
                                {
                                    url,
                                    method: "post",
                                    data: postData,
                                    success: function (res) {
                                        if (
                                            that.apiName === 'DeviceObj' &&
                                            res &&
                                            res.Value &&
                                            res.Value.dataList &&
                                            res.Value.dataList.length !== 0
                                        ) {
                                            resolve(res.Value.dataList);
                                        } else if (
                                            that.apiName === 'ServiceBomObj' &&
                                            res &&
                                            res.Value
                                        ) {
                                            resolve(res.Value)
                                        } else{
                                            resolve([])
                                        }
                                    },
                                    error: function (err) {
                                        console.error(err);
                                        reject(err);
                                    },
                                },
                                {
                                    errorAlertModel: 1,
                                }
                            );
                        });
                    },
                    async getSearchDataList(name, key) {
                        let data = null;
                        if (that.apiName === 'DeviceObj') {
                            data = {
                                data_id: that.dataId,
                                query_key: name,
                                query_value: key,
                                filter: {
                                    offset: 0,
                                    limit: 2000,
                                    asc: true,
                                },
                            };
                        } else {
                            data = {
                                "limit": 2000,
                                "offset": 0,
                                "filters": [
                                    {
                                        "field_name": "enabled_status",
                                        "field_values": true,
                                        "operator": "EQ"
                                    },
                                    {
                                        "field_name": name,
                                        "field_values": [key],
                                        "operator": "LIKE"
                                    }
                                ]
                            }
                        }

                        return await this.requestSearch(data);
                    },
                    // 搜索事件回调
                    async onSearch() {
                        let key = this.selectVal, val = this.inputVal;
                        if(key.trim() === "") return; // 输入框值为空，直接返回；
                        this.inputDisabled = true; // 搜索框防抖；
                        treeTable.showLoading() // 开启表格 loading效果；

                        let searchData = [];
                        if (that.apiName === 'ServiceBomObj') {
                            let flattenTableData = CRM.util.openTreeData(treeTable.curData.data) || [];
                            // 搜索出的数据都先做平铺处理
                            flattenTableData.forEach(item => {
                                item.children && delete item.children;
                            });

                            searchData = flattenTableData.filter(item => {
                                let field = '';
                                if (item.object_describe_api_name === "ProductObj") {
                                    field = key === 'product_id.name' ? 'name' : 'product_code';
                                } else if (item.object_describe_api_name === "BOMObj") {
                                    field = key === 'product_id.name' ? 'product_id__r' : 'product_code';
                                } else if (item.object_describe_api_name === "ProductGroupObj") {
                                    field = key === 'product_id.name' ? 'name' : '';
                                }
                                return item[field] && item[field].indexOf(val) !== -1;
                            });
                        } else {
                            // 将相应的数据传入 getSearchDataList，发送请求获取数据；
                            searchData = await this.getSearchDataList(key, val);
                        }

                        this.inputDisabled = false;
                        treeTable.hideLoading();
                        // 通过调用 treeTable实例上的 doStaticData方法，将搜索得到的数据注入表格进行渲染；
                        // doStaticData方法 可以让 treeTable 直接将静态数据进行渲染，而无需再从后端获取数据；
                        treeTable.doStaticData(searchData);
                    },
                    onSelectChange(val){
                        that.searchSelectVal = val;
                    },
                },

                watch: {
                    // todo 监听 inputVal 的值，当其值为 "" 时，将 treeTable的数据全部渲染出来；
                    inputVal: function(newVal, oldValue){
                        if(newVal.trim() === oldValue.trim()) return;
                        if(newVal === '' ){
                            that.refreshTable() // 调用 vue实例 上刷新表格的方法
                        }
                    }
                },
            };
            // 生成搜索框，挂载到当前 vue实例上；
            this.titleInput = FxUI.create(params);
        },

        // 渲染右栏表格，用的是 TreeTable 组件
        async renderTable(rootId, rootProductId) {
            let me = this;
            // 每次进来都重新 new 出来一个新的 Table，即生成一个新的 Table 实例，从服务器获取最新的数据；新的 table实例 会覆盖原来 vue实例的tables属性 上的相应位置；
            let table = (me.tables[rootId] = new me.TableComponent({
                // 刷新时覆盖原table
                $el: $(".pick-device-bom-dialog-content-" + rootId),
                url: config.listUrl, // api接口，table数据的来源；
                title: " ",
                // 如果是服务bom，则静态获取数据
                doStatic: this.apiName === 'ServiceBomObj',
                // 不展示分组前面的勾选框
                showGroupCheckbox: false,
                requestType: "FHHApi",
                postData: config.getPostData(rootId, me.masterData, me.rootProductId), // 发送请求需要携带的参数；
                columns: me.columns,
                showPage: false,
                scrollLoadY: true,
                isMyObject: true,
                showMultiple: true,
                isShowAllChecked: me.tableOption && !me.tableOption.onlyCheckOne,
                noNeedCheckParentsAndChildren: true,
                lineCheck: true,
                custom_className: "v-detail-device-hierarchy",
                formatData: (data) => {
                    const res = config.formatData(data, me, table, rootId);
                    // me.isRightLoading = false;
                    me.dataId = res.data[0]._id;
                    this.createRootProductTitleDiv(res.data[0] && res.data[0].accessory_material_id__r);
                    return res;
                },
                ...me.tableOption,
            }));

            // 如果是服务bom，则手动加载数据，为表格填充静态数据
            if (this.apiName === 'ServiceBomObj') {
                const bomData = await CRM.util.fetchBomAndRelatedBomData([rootProductId], {bom_core_id: rootId});
                console.log(bomData);
                const formattedData = config.formatData(bomData, me, table, rootId);
                // 如果没有数据，则要置空
                if (formattedData.data[0] && !formattedData.data[0].product_id) {
                    formattedData.data = [];
                }
                table.doStaticData(formattedData.data);
                // me.isRightLoading = false;
                this.createRootProductTitleDiv(table.curData.data[0] && table.curData.data[0].name);
            }

            // 生成表格头部的搜索框；传入的参数为：搜索框的挂载容器和当前 treeTable实例
            this.createTableSearch($(".dt-tit ", table.$el)[0], table);
            table.device_id = rootId;
        },

        // 设备BOM构造树形数据
        parseDataToTree2(data, pathId, rootId) {
            const me = this;
            const _data = [];
            const tmp = [];
            const obj = {};

            _.each(data, function (item) {
                const cur_device_id = item.accessory_material_id;
                const cur_product_id = item.device_part_id;
                const accessory_path = item.accessory_path;

                // 是否只取工单关联设备下的节点
                if (!me.isWholeBom && !accessory_path.includes(pathId)) return;
                tmp.push(item);

                if (cur_device_id) {
                    obj[cur_device_id] = item;
                } else if (cur_product_id) {
                    obj[cur_product_id + "-" + accessory_path] = item;
                }
            });

            _.each(tmp, function (item) {
                const is_root = me.isWholeBom
                    ? !item.device_id &&
                    !item.parent_product_id &&
                    item.accessory_material_id
                    : item.accessory_material_id == rootId;

                let pid = "";
                if (item.device_id) {
                    pid = item.device_id;
                } else if (item.parent_product_id) {
                    const path_chips = item.accessory_path.split(".");
                    path_chips.length > 1 &&
                    item.accessory_path.includes(item._id) &&
                    path_chips.pop();
                    const p_accessory_path = path_chips.join(".");
                    pid = item.parent_product_id + "-" + p_accessory_path;
                }

                if (is_root) {
                    item._isRoot = true;
                    return _data.push(item);
                }

                if (obj[pid]) {
                    !obj[pid].children && (obj[pid].children = []);
                    obj[pid].children.push(item);
                } else {
                    console.error($t('vcrm.modules.components.pickDeviceBom.not_match_parent')+ ": " + item._id); // 未找到匹配父级
                }
            });
            return _data;
        },

        // 右栏行点击触发事件
        trclickHandle(rowData, $tr, $tg, $trs, table) {
            const action = $tg.data("action");
            const data = $tg.data();
            if (action) {
                table.options.lineCheck = false; // 执行action时 取消lineCheck(点击行选中)
                this["on" + action] &&
                this["on" + action].call(this, rowData, data, $tg);
                setTimeout(() => (table.options.lineCheck = true), 200);
            }
        },

        // 右栏表格渲染完后，增加刷新按钮
        completeRenderHandle(rowData, $tr, $tg, $trs, table) {
            if (!$(`.pick-device-bom-dialog-content-${this.rootId} .dt-caption #p-refresh`).length) {
                // 添加渲染刷新按钮
                const refreshElement = $('<div id="p-refresh" class="fx-icon-refresh"></div>');
                refreshElement.bind("click", () => this.refreshTable()); // 绑定刷新逻辑
                $(`.pick-device-bom-dialog-content-${this.rootId} .dt-caption`).append(refreshElement);
            }
        },

        checkboxclickHandle(isChecked, $tr, checkedLen) {
            const isSingleCheck = this.tableOption.onlyCheckOne;
            // 是否是单选
            if (isSingleCheck && isChecked) {
                const tables = this.tables || {};
                for (const rootId in tables) {
                    const table = tables[rootId];
                    // 单选选中时，将其他取消勾选
                    rootId !== this.rootId && table && table._clearChecked()
                }
            }
            const filterCheckedData = this.filterTableCheckedData(this.rootId) || [];
            this.$refs.leftComp && this.$refs.leftComp.changeNum(filterCheckedData.length, isSingleCheck);
        },

        /**
         * 返回 table 勾选数据中，过滤了分组的勾选数据
         * @param idToTable this.tables 中表格对应的 id
         * @return {T[]}
         */
        filterTableCheckedData(idToTable) {
            const table = this.tables[idToTable];
            const checkedData = table.getCheckedData() || [];
            // 当点击全选按钮时，表格 getCheckedData 方法的返回结果会包含分组，服务bom需要过滤掉分组产品
            return checkedData.filter(item => !item.isGroup);
        },

        // 右栏表格中，点击可查看详情页的数据触发
        onShowDetail(rowData, data) {
            CRM.api.show_crm_detail({
                type: data.apiname || rowData.object_describe_api_name,
                showMask: false,
                data: { crmId: data.id || rowData._id },
            });
        },

        // 刷新当前渲染的列表 默认为右侧选中的当前项（多设备情况）
        refreshTable() {
            this.renderTable(this.rootId, this.rootProductId);
            this.$refs.leftComp && this.$refs.leftComp.changeNum(0);
        },

        // 获得右栏表格的所有选中数据
        getAllCheckedData() {
            const data = [];
            const tables = this.tables || {};
            for (const rootId in tables) {
                const filterCheckedData = this.filterTableCheckedData(rootId);
                data.push(...filterCheckedData);
            }
            return data;
        },

        onConfirm() {
            const checkedResult = {
                mainCheckedData: [],
                subCheckedData: this.getAllCheckedData(),
            };
            if (checkedResult.subCheckedData && !checkedResult.subCheckedData.length) {
                return CRM.util.alert($t("请至少选择一条数据"));
            }
            if (!(this._onBlockup && this._onBlockup(checkedResult, this))) {
                // 将选择的数据和当前组件实例传递给父组件
                this.handleConfirm && this.handleConfirm(checkedResult, this);
                this._onConfirm && this._onConfirm(checkedResult, this);
                this.onClosed(); // 关闭当前页面
            }
        },

        on(name, callback) {
            if (name == "confirm") this._onConfirm = callback;
            if (name == "blockup") this._onBlockup = callback;
            if (name == "closed") this._onClosed = callback;
        },

        // 关闭当前组件并销毁当前组件实例
        onClosed() {
            this.visible = false;
            this._onClosed && this._onClosed();
            setTimeout(() => this.$destroy(), 200);
        },

        // 点击左侧栏中的选项 此父组件加载渲染对应的表格
        async onTabClick(tab) {
            if (this.isRightLoading) {
                // 防抖 有左侧设备正在加载，直接返回
                return CRM.util.remind(4, $t("stock.DeviceObj.bom.text_4")); // 您的操作频率过快,请稍后重试
            }
            this.$refs.leftComp.changeIndex(tab.index);
            this.rootId = tab.rootId;
            const table = this.tables[tab.rootId];
            if (table) {
                const title = this.apiName === 'ServiceBomObj' ?
                    (table.curData.data[0] && table.curData.data[0].name || '') :
                    (table.curData.data[0] && table.curData.data[0].accessory_material_id__r || '');
                this.createRootProductTitleDiv(title);
                return;
            }
            this.isRightLoading = true;
            await this.renderTable(tab.rootId, tab.rootProductId); // 渲染选中的那一个设备对应的列表数据
            this.isRightLoading = false;
        }
    },
};
</script>

<style lang="less">
.pick-device-bom-dialog {
    .el-dialog__header {
        padding: 7px 24px 7px 0;
    }
    .pick-device-bom-title {
        display: inline-block;
        // border-left: 4px solid var(--color-primary06);
        padding-left: 20px;
        font-size: 22px;
    }
    .pick-device-bom-btns {
        float: right;
        .el-button--small {
            padding: 8px 15px;
            font-size: 14px;
        }
    }
    .p-content {
        display: flex;
        overflow: hidden;
        height: 88vh;
        .p-card {
            position: relative;
            flex: 1;
            width: 51vw;
            .p-prefix {
                position: relative;
                display: inline-block;
                min-width: 130px;
                max-width: 800px;
                &::after {
                    content: "";
                    display: inline-block;
                    width: 1px;
                    height: 16px;
                    background-color: #dee1e6;
                    position: absolute;
                    right: 13px;
                    top: 6.5px;
                }
            }
            .p-error {
                color: #f34747;
            }
        }
        .p-card-full {
            width: 100vw;
        }
    }
    .p-loading {
        position: fixed;
        top: 49px;
        bottom: 3px;
        left: 0;
        right: 0;
        z-index: 9999;
        background-color: var(--color-neutrals01);
        .el-loading-spinner {
            top: 43%;
        }
    }
    .tb-cell[data-fieldname="name"] {
        overflow: visible;
    }
    .v-treetable-cur {
        position: absolute;
        left: -21px;
        margin-top: -1px;
        margin-left: 0;
        padding: 0px 3px;
        color: var(--color-primary06);
        border-radius: 2px;
        background-color: var(--color-primary01);
        text-align: center;
        text-indent: 0;
    }
    .fx-icon-boom-unfold-defined {
        position: relative;
        margin: 0 -4px 0 -8.5px;
        top: -8px;
        width: 26px;
        height: 26px;
        transform: scale(0.52);
        color: transparent;
        user-select: none;
        background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABsAAAAYCAYAAAALQIb7AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAGHSURBVHgB7ZZdTgIxEMdn2qIx+uAR9gbqDbiBHAFPwLLwoBEUMMYHWT5O4HoCvQFHcb1BX0xMoB2nBIzylXRXePL3Ms22nX8z0+ksRo34FoFaMIOIUgQsxvf19+r147lATPjzMeTA+SQhy+KnkAMRA0AouTELhXmF5j4RbFmsmiTA3AKrELBDBIdML35EgamzBMtzmbGolZHyTBrTZ88lDm5KBEmvEz1P55WsCmNTls0ZVtRWiTa6YdjqB3Ji3ghZ6K5+AVtixznbIcpncdSMTzh/IfhiSdsP1fYSQ6KEzSn4ggjyyKRq4QTFWqM72rDNX2iGeyjU7wNgwCaALbEYxlejxtV1i+V4b8SxDOAvxLjO9KB1la5bXLvpDcCA/wUB0niwn3hdkLgTDdkMISP/RT0lvHwIhFQV7qp+D3OWopaq8AKu1gj84KIWhxaWijpqdp82bAu8heYI+i5q7Zoo/+g4Z2XYEjgfcE/jPHxuzIWczMKYAdcr0WcDF3UFrB2AL4iaCoXiF0JYfsM5qK+7AAAAAElFTkSuQmCC)
        center no-repeat;
        &::before {
            content: "\6839";
        }
    }
    .el-scrollbar__view {
        padding: 10px 18px 14px 8px !important;
    }
    .dt-caption {
        margin-top: -5px;
        height: 51px;
        overflow: hidden;
    }
    .dt-main {
        margin-right: -0.5px;
        border-left: 1px solid #e5e9f2;
        .treetable-leaf {
            float: left;
            margin: -2.5px 0 0;
            padding: 6.5px;
            width: 9px;
            height: 9px;
            background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAALCAYAAAByF90EAAAAcklEQVQ4EWNggIKCggaB4pqe/KKa3npScXF1jzzMHIai6p77xTU9/8nBIL1wg4pre96TYwhMD9wgkHdggqTSIL1wg0AMcgzDMARmIimG4TSEFMMIGkKMYUQbgs8wkg3BZhjZhsAMK2joVyho6DKA8bHRADcHF0eFhJz1AAAAAElFTkSuQmCC)
            center no-repeat;
            background-size: contain;
            background-origin: content-box;
            &.close {
                padding-top: 7.5px !important;
            }
        }
    }
    .el-dialog__body {
        padding: 0px 1px 0px 7px;
    }
    .p-outline {
        position: absolute;
        top: 0;
        bottom: 35px;
        padding: 0 0px 13px;
        max-width: calc(100vw - 30px);
        height: 100%;
        .dt-main {
            margin-left: 15px;
        }
        .dt-tit {
            float: right;
            margin-right: 32px;
            .el-input-group__prepend {
                .el-select.el-select--small.fx-select {
                    width: 95px;
                }
            }
        }
    }
    .p-outline-multi {
        max-width: calc(100vw - 274px);
    }
    #p-refresh {
        position: absolute;
        right: 14px;
        bottom: 11px;
        color: #666;
        font-size: 16px;
        cursor: pointer;
    }

    .root-product-title {
        width: 600px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        position: absolute;
        left: 16px;
        top: 22px;
        color: #181C25;
    }
}
</style>
