<template>
  <transition name="fade" appear>
    <div v-if="visible" class="guide-container" >
      <example :currentStep="currentStep" />

      <!-- 底部提示文本 -->
      <!-- <div class="guide-tip">
        {{ currentText.text }}
      </div> -->

      <!-- 底部操作区 -->
      <div class="guide-footer">
        <div class="step-indicator">
          {{ $t('vcrm.aiguide.aiguide.feature_guide') }}{{ currentStep }}/2
        </div>
        <fx-button 
          type="primary" 
          size="micro"
          @click="handleStepAction"
        >
          {{ currentText.buttonText }}
        </fx-button>
      </div>
    </div>
  </transition>
</template>

<script>
import Example from './components/example.vue'
export default {
  name: 'GuideDialog',
  components: {
    Example
  },
  model: {
    prop: 'visible',
    event: 'update:visible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    pos:{
      type: Object,
      default: () => ({
        x: 0,
        y: 0
      })
    }
  },
  data() {
    return {
      currentStep: 1,
      textMap: [{
        text: this.$t('vcrm.aiguide.guide.double_click_tip'),
        buttonText: this.$t('vcrm.aiguide.guide.next_step'),
        step: 1
      }, {
        text: this.$t('vcrm.aiguide.guide.select_tip'),
        buttonText: this.$t('vcrm.aiguide.guide.got_it'),
        step: 2
      }]
    }
  },

  computed: {
    currentText() {
      return this.textMap.find(item => item.step === this.currentStep)
    },
  },
  methods: {
    handleStepAction() {
      if (this.currentStep === 1) {
        this.currentStep = 2;
      } else {
        CRM.setLocal('activity-guide-hidden', 'hidden');
        this.$emit('guide:hide');
        this.$emit('update:visible', false);
      }
    }
  },
  watch: {
    pos: {
      handler(newVal) {
        this.$nextTick(() => {
          const elm = $(this.$el);
          const width = elm.outerWidth();
          const height = elm.outerHeight();
          const clientHeight = $(window).height();
          let left = newVal.x - width / 2 < 0 ? 20 : newVal.x - width / 2;
          let top = newVal.y + height > clientHeight ? newVal.y - height - 20 : newVal.y;
          elm.css({
            top: `${top}px`,
            left: `${left}px`
          })
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: opacity .3s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.guide-container {
  // position: relative;
  width: 302px;
  position: fixed;
  background: #A887ED;
  border-radius: 8px;
  box-shadow: 0px 9px 10px rgba(0, 0, 0, 0.12);
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  box-sizing: border-box;
  z-index: 9999;
  &:after{
    content: '';
    position: absolute;
    bottom: -7px;
    left: 40px;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 8px solid #A887ED;
  }
}

.guide-tip {
  font-size: 13px;
  line-height: 18px;
  color: var(--color-neutrals01);
}

.guide-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .step-indicator {
    font-size: 13px;
    line-height: 18px;
    color: var(--color-neutrals01);
  }
}
</style> 