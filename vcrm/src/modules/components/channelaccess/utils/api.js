export default {
    fetchChannelConfig() {
        return new Promise(function(resolve) {
            CRM.util.FHHApi({
                    url:"/EM1HNCRM/API/v1/object/partner_management/service/fetch_channel_admission_config",
                    data: {},
                    success: function(res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        CRM.util.alert(
                            res.Result.FailureMessage ||
                                $t("暂时无法获取数据") + "!"
                        );
                    }
                },{ errorAlertModel: 1}
            );
        });
    },
    appConfigLayout() {
        return new Promise(function(resolve) {
            CRM.util.FHHApi({
                    url:"/EM1HNCRM/API/v1/object/partner_management/service/app_config_layout",
                    data: {},
                    success: function(res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        CRM.util.alert(
                            res.Result.FailureMessage ||
                                $t("暂时无法获取数据") + "!"
                        );
                    }
                },{ errorAlertModel: 1}
            );
        });
    }
}