const mockData = {
    cycleOptions:[
        {
            label:$t("el.datepicker.year"),
            value:'year'
        },
        {
            label:$t("crm.dlt.cycle.month"),
            value:'month'
        },
        {
            label:$t("crm.dlt.cycle.quarter"),
            value:'quarter'
        },
        {
            label:$t("crm.dlt.cycle.week"),
            value:'week'
        },
    ],
    automaticOptions:[
        {
            label:$t("crm.dlt.cycle.month"),
            value:'month'
        },
        {
            label:$t("crm.rebatepolicyobj.execute_mode.cycle_comp.options.day.right"),
            value:'day'
        },
        {
            label:$t("crm.dlt.cycle.week"),
            value:'week'
        },
    ],
    mouthOptions:[
        {
            label:1,
            value:'1'
        },
        {
            label:2,
            value:'2'
        },
        {
            label:3,
            value:'3'
        },
        {
            label:4,
            value:'4'
        },
        {
            label:5,
            value:'5'
        },
        {
            label:6,
            value:'6'
        },
        {
            label:7,
            value:'7'
        },
        {
            label:8,
            value:'8'
        },
        {
            label:9,
            value:'9'
        },
        {
            label:10,
            value:'10'
        },
        {
            label:11,
            value:'11'
        },
        {
            label:12,
            value:'12'
        },
    ],
    windowOptions:[
        {
            label:10,
            value:'10'
        },
        {
            label:20,
            value:'20'
        },
        {
            label:30,
            value:'30'
        },
        {
            label:40,
            value:'40'
        },
        {
            label:50,
            value:'50'
        },
        {
            label:60,
            value:'60'
        },
        {
            label:70,
            value:'70'
        },
        {
            label:80,
            value:'80'
        },
        {
            label:90,
            value:'90'
        },
        {
            label:120,
            value:'120'
        },
        {
            label:150,
            value:'150'
        },
        {
            label:180,
            value:'180'
        },
        {
            label:365,
            value:'365'
        },
        
    ]
};
export default mockData;