<template>
    <div>
        <div v-show="!other" class="agree-ment">
            <fx-tabs v-model="activeName" @tab-click="handleClick">
                <fx-tab-pane :label="$t('crm.agree.conset')" name="first" >
                    <div class="partner"></div>
                </fx-tab-pane>
                <!-- 审核流程 -->
                <fx-tab-pane :label="$t('crm.agree.rulset')" name="second">
                    <div class="third-content">
                         <div class="third-title">
                            <span class="plan" :class="{'plan-active': isplan}" @click="planList">{{ $t("crm.agree.planli") }}</span>
                            <span class="ruleset" :class="{'plan-active': !isplan}" @click="ruleset">{{ $t("crm.agree.ruleset") }}</span>
                        </div>
                        <div v-show="isplan" class="third-tablelist">
                            <div class="table-title">
                                <div class="table-span">
                                    <span>{{ $t("crm.agree.planli") }}</span>
                                    <span>{{ '(' }}</span>{{ tableTotal }}<span>{{ '/20)' }}</span>
                                </div>
                                <fx-button size="small" type="primary"  @click="newPlan">{{ $t("crm.agree.table.new") }}</fx-button>
                            </div>
                            <div class="table-content">
                                <fx-table
                                    :data="tableData">
                                    <fx-table-column
                                        v-for="column in columns"
                                        :key="column.prop"
                                        :prop="column.prop"
                                        :label="column.label"
                                    />
                                    <fx-table-column
                                        fixed="right"
                                        prop="operations"
                                        :label="$t('操作')"
                                        width="200"
                                    >
                                        <template slot-scope="scope">
                                            <div class="links">
                                                <fx-button v-if="!scope.row.pushed" @click="editPlan(scope.row,'edit')" type="text" size="small">{{ $t("edit") }}</fx-button>
                                                <fx-button v-show="!scope.row.pushed" @click="onPlan(scope.row)" type="text" size="small">{{ $t("bi.syncData.strategy.on") }}</fx-button>
                                                <fx-button style="margin-left: 0px;" v-show="scope.row.pushed" @click="editPlan(scope.row,'view')" type="text" size="small">{{ $t("预览") }}</fx-button>
                                                <fx-button  v-show="scope.row.pushed" @click="offPlan(scope.row)" type="text" size="small">{{ $t("bi.syncData.strategy.off") }}</fx-button>
                                                <fx-button  @click="editPlan(scope.row,'copy')" type="text" size="small">{{ $t("copy") }}</fx-button>
                                                <fx-button v-show="!scope.row.pushed" @click="deletePlan(scope.row,scope.$index,)" type="text" size="small">{{ $t("richtext.delete") }}</fx-button>
                                            </div>
                                        </template>
                                    </fx-table-column>
                                </fx-table>
                            </div>
                        </div>
                        <div v-show="!isplan" class="third-ruleset">
                            <div class="content">
                                <h1 class="cpntent-h">{{ $t("crm.agree.flow") }}</h1>
                                <div class="flow-content">
                                    1.<span>{{ $t("crm.agree.flow.span1") }}</span><br/>
                                    <span>{{ $t("crm.agree.flow.span2") }}</span><span @click="flowExamine" class="flow-span3">{{ $t("crm.agree.flow.span3") }}</span><br/>
                                    2.<span>{{ $t("crm.agree.flow.span4") }}</span>
                                </div>
                                <div class="flow-zh">
                                    <h1>{{ $t("crm.agree.flow.zh") }}</h1>
                                    <span>{{ $t("richtext.paragraph.title") }}</span>
                                    <fx-input size="small" :placeholder='$t("vcrm.pleaseEnterContent")' v-model="reviewTitie"></fx-input>
                                    <div class="flow-partner-content">
                                        <span>{{ $t("crm.agree.flow.zh.span") }}</span>
                                        <fx-input
                                            type="textarea"
                                            size="small"
                                            :rows="2"
                                            :placeholder='$t("vcrm.pleaseEnterContent")'
                                            maxlength="200"
                                            show-word-limit
                                            v-model="reviewContent"
                                            >
                                        </fx-input>
                                    </div>
                                </div>
                                <!-- <div class="flow-partner">
                                    <h1>{{ $t("crm.agree.flow.partner") }}</h1>
                                    <span>{{ $t("richtext.paragraph.title") }}</span>
                                    <fx-input size="small" :placeholder='$t("vcrm.pleaseEnterContent")' v-model="reviewPartner"></fx-input>
                                    <div class="flow-partner-content">
                                        <span>{{ $t("crm.agree.flow.zh.span") }}</span>
                                        <fx-input
                                            type="textarea"
                                            size="small"
                                            :rows="2"
                                            :placeholder='$t("vcrm.pleaseEnterContent")'
                                            maxlength="200"
                                            show-word-limit
                                            v-model="reviewPartnerContent"
                                            >
                                        </fx-input>
                                    </div>
                                </div> -->
                            </div>
                            <div class="flow-bottom">
                                <fx-button size="small" type="primary"  @click="flowSave">{{ $t("保存") }}</fx-button>
                            </div>
                        </div>
                    </div>
                </fx-tab-pane>


            </fx-tabs>
            <div class="edit">
                <editDialog 
                    @closedialog="closeDialog" 
                    :dataList="editoptions" 
                    :smsindex="smsIndex" 
                    :smsid="issmsapprovalNoticeId"
                    :isDisabled="supportCustomize"
                    :apiName="apiname"
                    v-if="editFlag" 
                    :smsneedobj = 'smsneedObj' 
                    >
                </editDialog>
            </div>
            <div class="email">
                <emailDialog @closeemail="closeEmail" :emailid="isemapprovalNoticeId" :emindex="emIndex" :needobj="needObj"  v-if="editEmail" :apiName="apiname"></emailDialog>
            </div>
            <div class="emailTel">
                <emailTelDialog
                    @clocseemtel="clocseEmTel"
                    :emuidindex="emUidIndex"
                    :emailuuid="emailUuid"
                    :ischoose="isChoose"
                    :isauto="isAuto"
                    :checkemail="checkEmail"
                    :apiName="apiname"
                    :checkemailhand="checkEmailhand"
                    v-if="emTelFlag"
                ></emailTelDialog>
            </div>
            <div class="pirewDialog">
                <pirewDialog
                    @closepirew="closePirew"
                    :pirewindex="pirewIndex"
                    :emailcontent="emailContent"
                    v-if="pirewFlag"
                ></pirewDialog>
            </div>
        </div>
        <div class="other" v-show="other">
            <div class="other-title"><h1>{{ $t("crm.channelguide.middle.sign") }}</h1></div>
            <div class="other-content">
                <img src="../assets/dltneedto.jpg" alt="">
                <p>{{ $t("crm.channelguide.market.need") }}</p>
                <span @click="toSetting('sign')">{{ $t("crm.channelguide.market.go") }}</span>
            </div>
        </div>
        <fx-dialog
            class="scheme-content"
            :visible="dialogVisible"
            ref="pickdataDialog"
            :fullscreen="true"
            :show-close="false"
            :close-on-click-outside="false"
            :append-to-body="true"
            @open="handleDialogOpen"
            @close="close">
            <div slot="title" class="dialog-title">
                <span class="scheme-title-text">{{ dialogTitle }}</span>
                <div class="pickdata-btns">
                    <fx-button size="mini" type="primary" :disabled="viewFlag"  @click="saveScheme">{{ $t("保存") }}</fx-button>  
                    <fx-button size="mini" plain type="primary"  @click="canelScheme">{{ $t("取消") }}</fx-button>
                </div>
            </div>
            <div class="scheme-content-nav" :class="{'nav-collapsed': !isCollapsed}">
                <div class="scheme-left" v-show="isCollapsed">
                    <div class="scheme-left-inner">
                        <div class="scheme-left-wrapper">
                            <div class="nav-item " :class="{'cur': currentSection === 'basic'}" @click="scrollToSection('basic')">{{ $t("crm.crm.PivotTableRuleObj.setting.onepagetitle") }}</div>
                            <div class="nav-item"  :class="{'cur': currentSection === 'rules'}" @click="scrollToSection('rules')">{{ $t("vcrm.schme.qy.set") }}</div>
                            <div class="nav-item"  v-show="isxyLeft" :class="{'cur': currentSection === 'xyrules'}" @click="scrollToSection('xyrules')">{{ $t("vcrm.schme.xy.set") }}</div>
                        </div>
                       
                    </div>
                </div>
                <div class="scheme-left-close"  :class="{'is-collapsed': !isCollapsed}" @click="closenav">
                    <span v-show="isCollapsed" class="fx-icon-arrow-left"></span>
                    <span v-show="!isCollapsed" class="fx-icon-arrow-right"></span>
                </div>
                <div class="scheme-right" ref="contentContainer" >
                    <div v-for="(item,index) in newagreeList" :key="index">
                        <!-- 基本信息 -->
                        <div class="scheme-right-sian" ref="basicSection" id="basic">
                            <h1>{{ $t("crm.crm.PivotTableRuleObj.setting.onepagetitle") }}</h1>
                            <!-- 方案名称-协议内容 -->
                            <div class="name-a padding">
                                <div class="scheme-name">
                                    <span class="rlname">{{ $t("crm.brow.name") }}</span>
                                    <fx-input size="small" v-model="item.name"></fx-input>
                                </div>
                                <div class="scheme-content-new">
                                    <span class="rlname">{{ $t("crm.brow.content") }}</span>
                                    <fx-select
                                        ref="select"
                                        v-model="item.agreementName"
                                        size="small"
                                        :options="item.options"
                                    ></fx-select>
                                </div>
                            </div>
                            <!-- 签署方式-签署类型 -->
                            <div class="manner-type">
                                <div class="scheme-type">
                                    <span class="span-type">{{ $t("crm.agree.dlt.type") }}</span>
                                    <div class="radio-type">
                                        <fx-radio v-model="item.radioType"  @change="changeType(index,$event)" label="1" size="mini">{{ $t("crm.dlt.fixed.once") }}</fx-radio>
                                        <fx-radio v-model="item.radioType"  @change="changeType(index,$event)" label="2" size="mini">{{ $t("vcrm.dlt.once.sign") }}</fx-radio>
                                    </div>
                                </div>
                                <div class="scheme-manner">
                                    <span class="dzq">{{ $t("crm.agree.type") }}</span>
                                    <fx-radio v-model="item.radio1"  @change="changeApl(index,$event)" label="1" size="mini">{{ $t("crm.hand.file") }}</fx-radio>
                                    <fx-radio v-model="item.radio1"  @change="changeApl(index,$event)" label="2" size="mini">{{ $t("sfa.vcrm.dlt.apl") }}</fx-radio>
                                </div>
                            </div>
                           <!-- 固定日-固定周期 -->
                           <div v-show="item.radioType == '2'" class="day-cycle">
                                <span class="span-cycle">{{ $t("vcrm.dlt.day.cycle") }}</span>
                                <fx-radio v-model="item.dayCycle"  label="1" size="mini" @change="changeDayCycle(index,$event)">{{ $t("vcrm.dlt.fixed.xy") }}</fx-radio>
                                <fx-radio v-model="item.dayCycle"  label="2" size="mini" @change="changeDayCycle(index,$event)">{{ $t("vcrm.dlt.cycle.xy") }}</fx-radio>
                             </div>
                            <!-- apl -->
                            <div class="appl" v-show="item.isAplSlowEamil">
                                <span class="appl-choose">{{ $t("sfa.vcrm.dlt.apl") }}</span>
                                <fx-input   @focus="focuspass(index,item.apl)" v-model="item.apl">
                                    <span @click="clearApl(index)" class="fx-icon-close" slot="suffix" style="padding-right:8px;padding-top:7px;cursor: pointer;"></span>
                                </fx-input>
                            </div>
                            <!-- 协议期限-协议开始日期 -->
                             <div class="start-ending">
                                <div class="end">
                                    <span class="rlname">{{ $t("vcrm.scheme.xx.end") }}</span>
                                    <fx-input class="input-zq" @blur="oncecycleBlur(index,$event)" isPositiveNum="true" :decimal-places="0"  type="number" size="small" v-model="item.oncezq"></fx-input>
                                    <fx-select
                                        class="select-zq"
                                        ref="select"
                                        v-model="item.onceTime"
                                        :options="item.oncecycleOptions"
                                    ></fx-select>
                                </div>
                                <div class="start">
                                    <span class="rlname">{{ $t("vcrm.scheme.xx.start") }}</span>
                                    <fx-select
                                        class="select-mou"
                                        ref="select"
                                        v-model="item.oncemouth"
                                        :options="item.oncemouthOptions"
                                        @change="handleOnceChange(index,$event)"
                                    ></fx-select>
                                    <span class="fixed-mouth">{{ $t("crm.rebatepolicyobj.execute_mode.cycle_comp.options.QUARTER.month.right") }}</span>
                                    <fx-select
                                        class="select-mou"
                                        ref="select"
                                        v-model="item.oncedayTime"
                                        :options="item.oncedayOptions"
                                    ></fx-select>
                                    <span class="fixed-day">{{ $t("el.datepicker.weeks.sun") }}</span>
                                </div>
                             </div>
                             <!-- 模板优先级-适用范围 -->
                            <div class="first padding">
                                <div class="tpl-first">
                                    <span class="rlname">{{ $t("模板优先级") }}</span>
                                    <fx-tooltip  class="item" :effect="effectType" :content='$t("crm.dlt.notice.first")' placement="top" transition="">
                                        <span class="fx-icon-question"></span>
                                    </fx-tooltip>
                                    <fx-select
                                        ref="select"
                                        v-model="item.newFirst"
                                        :options="item.firstOptions"
                                    ></fx-select>
                                </div>
                               <div class="range-tpl">
                                <span class="syrange">{{ $t("适用范围") }}</span>
                                <fx-radio v-model="item.radio"  @change="changeRanNew($event)" label="1" size="mini">{{ $t("全部") }}</fx-radio>
                                <fx-radio v-model="item.radio"  @change="changeRanNew($event)" label="2" size="mini">{{ $t("crm.brow.condition") }}</fx-radio>
                               </div>
                            </div>
                            <div  class="apl-condition" ref="filter-ref"></div>
                        </div>
                        <!-- 签约设置 -->
                        <div class="scheme-right-rule" ref="rulesSection" id="rules">
                            <h1>{{ $t("vcrm.schme.qy.set") }}</h1>
                            <div class="policy padding">
                                <!-- 审核结果通知 -->
                                <div class="audit-result">
                                    <span class="audit-h">{{ $t("crm.RegisterExamine.noticeChannel") }}</span>
                                    <div class="result">
                                        <div class="sms">
                                            <div class="one">
                                                <fx-tag v-show="!isSms" type="danger" size="mini">{{ $t("未开通") }}</fx-tag>
                                                <span class="one-sms">{{ $t("短信") }}</span>
                                                <fx-switch
                                                    v-model="item.smsValue"
                                                    :disabled="!isSms || viewFlag"
                                                    size="small"
                                                    @change="switch_status('sms',$event,index)"
                                                    >
                                                </fx-switch>
                                            </div>
                                            <div class="two">
                                            </div>
                                            <div class="three">
                                                <span class="three-sms"  v-show="!isSms" @click="dredge(index)">{{ $t("开通短信服务") }}</span>
                                                <span class="three-sms"  v-show="isSms" @click="edit(index)">{{ $t("配置短信内容") }}</span>
                                                <span class="three-apl"  @click="selectRule(index,'sms')">{{ $t("添加APL代码") }}</span>
                                                <fx-input   v-model="item.smsAplVal">
                                                    <span @click="clearAplsm('sms',index)" class="fx-icon-close" slot="suffix" style="padding-right:8px;padding-top:7px;cursor: pointer;"></span>
                                                </fx-input>
                                            </div>
                                        </div>
                                        <div class="email">
                                            <div class="one">
                                                <span class="one-sms">{{ $t("邮件") }}</span>
                                                <fx-switch
                                                    v-model="item.emailValue"
                                                    :disabled="viewFlag"
                                                    @change="switch_status('email',$event,index)"
                                                    size="small"
                                                    >
                                                </fx-switch>
                                            </div>
                                            <div class="two">
                                            </div>
                                            <div class="three">
                                                <span class="three-sms"  @click="editema(index)">{{ $t("crm.email.setemail") }}</span>
                                                <span class="three-apl"  @click="selectRule(index,'email')">{{ $t("添加APL代码") }}</span>
                                                <fx-input  v-model="item.emailAplVal">
                                                    <span @click="clearAplsm('email',index)" class="fx-icon-close" slot="suffix" style="padding-right:8px;padding-top:7px;cursor: pointer;"></span>
                                                </fx-input>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- 角色开通 -->
                                <div class="role">
                                    <span class="agree-role">{{ $t("crm.agree.role") }}</span>
                                    <fx-select
                                        ref="select"
                                        v-model="item.roles"
                                        multiple
                                        collapse-tags
                                        :options="item.roleOptions"
                                    ></fx-select>
                                </div>
                            </div>
                        </div>
                        <!-- 续约设置 -->
                         <div class="scheme-xy-rule" v-show="item.radioType == '2'" ref="xyrulesSection" id="xyrules">
                            <h1>{{ $t("vcrm.schme.xy.set") }}</h1>
                            
                            <!-- 固定周期 -->
                            <div class="cycle-period" v-show="item.dayCycle == '2'">
                                <!-- 续约周期 -->
                                 <div class="period-cycle">
                                    <span class="cycle-period-d">{{ $t("vcrm.dlt.cycle.d") }}</span>
                                    <span class="cycle-period-an">{{ $t("crm.dlt.cycle.an") }}</span>
                                    <fx-input class="input-zq" @blur="cycleBlur(index,$event)" isPositiveNum="true" :decimal-places="0"  type="number" size="small" v-model="item.cyclezq"></fx-input>
                                    <fx-select
                                        class="select-zq"
                                        ref="select"
                                        v-model="item.cycleTime"
                                        :options="item.cycleOptions"
                                    ></fx-select>
                                    <span class="cycle-sign">{{ $t("crm.dlt.cycle.sign") }}</span>
                                 </div>
                            </div>
                            <!-- 固定日签署 -->
                            <div class="cycle-fixed" v-show="item.dayCycle == '1'">
                                <span class="fixed-startday">{{ $t("vcrm.dlt.xy.startday") }}</span>
                                <span class="fixed-evert">{{ $t('crm.rebatepolicyobj.execute_mode.cycle_comp.options.YEAR') }}</span>
                                <fx-select
                                    class="select-mou"
                                    ref="select"
                                    v-model="item.mouthTime"
                                    :options="item.mouthOptions"
                                    @change="handleMonthChange(index,$event)"
                                    @blur="textBlur(index)"
                                ></fx-select>
                                <span class="fixed-mouth">{{ $t("crm.rebatepolicyobj.execute_mode.cycle_comp.options.QUARTER.month.right") }}</span>
                                <fx-select
                                    class="select-mou"
                                    ref="select"
                                    v-model="item.dayTime"
                                    :options="item.dayOptions"
                                    @change="dayBlur(index)"
                                ></fx-select>
                                <span class="fixed-day">{{ $t("el.datepicker.weeks.sun") }}</span>
                            </div>
                            <!-- 到期提醒设置 -->
                            <div class="notice">
                                <span class="notice-set">{{ $t("crm.dlt.notice.set") }}</span>
                                <fx-radio   v-model="item.handle"  label="1" size="mini">{{ $t("crm.dlt.notice.automatic") }}</fx-radio>
                                <fx-radio  v-model="item.handle"  label="2" size="mini">{{ $t("crm.dlt.notice.movement") }}</fx-radio>
                                <!-- 自动提醒 -->
                                <div v-show="item.handle == '1'" class="notice-content"> 
                                    <span class="automatic-type">{{ $t("crm.dlt.notice.automatic.type") }}</span>
                                    <div class="notice-right">
                                        <div v-for="(val,j) in item.noticeList" :key="j" class="notice-automatic" :class="{'height3': !val.model}">
                                            <fx-checkbox v-model="val.model" :disabled="!val.isKt"> {{ val.title }}</fx-checkbox>
                                            <fx-tag v-show="!val.isKt"  type="danger" size="mini">{{ $t("未开通") }}</fx-tag>
                                            <fx-tooltip v-show="val.isQue && val.crm" class="item" :effect="effectType" :content='$t("crm.dlt.notice.title")' placement="right" transition="">
                                                <span class="fx-icon-question"></span>
                                            </fx-tooltip>
                                            <fx-tooltip v-show="val.isQue && val.yw" class="item" :effect="effectType" :content='$t("crm.dlt.notice.title1")' placement="right" transition="">
                                                <span class="fx-icon-question"></span>
                                            </fx-tooltip>
                                            <br/>
                                            <span v-show="val.model && val.isKt" class="automatic-time">{{ val.time }}</span>
                                            <span v-show="val.model && val.isKt" class="automatic-before">{{ val.before }}</span>
                                            <fx-input v-model="val.num" v-show="val.model && val.isKt"  size="small" type="number"  isPositiveNum="true" @blur="cycleBlur1(index,j,$event)" :decimal-places="0" class="automatic-input"></fx-input>
                                            <fx-select
                                                class="automatic-select"
                                                v-model="val.nosel"
                                                v-show="val.model && val.isKt"
                                                ref="select"
                                                :options="item.automaticOptions"
                                            ></fx-select>
                                            <span v-show="val.model && val.isKt" class="automatic-tx">{{ $t("icm.mp.wechat_remind-240918") }}</span>
                                            <br/>
                                            <div class="automatic-text">
                                                <span v-show="val.model && val.isKt" class="automatic-wa">{{ val.words }}</span>
                                                <fx-input
                                                    v-show="!val.isEmail && val.model && val.isKt && !val.isPrm"
                                                    type="textarea"
                                                    v-model="val.noTel"
                                                    :placeholder='$t("vcrm.pleaseEnterContent")'
                                                    maxlength="200"
                                                    show-word-limit
                                                    size="small"
                                                    >
                                                </fx-input>

                                                <div v-show="!val.isEmail && val.model && val.isKt && val.isPrm" class="custom-textarea-container">
                                                    <textarea
                                                        v-model="userInput"
                                                        maxlength="200"
                                                        class="custom-textarea"
                                                        @input="checkFixedText"
                                                        ref="textarea"
                                                        ></textarea>
                                                    <div class="word-count">{{ userInput.length }}/200</div>
                                                </div>
                                                <span class="button" v-show="val.isEmail && val.model && val.isKt && !val.templ && !emLen" @click="addEmail(index,item._uuid)">
                                                    <span class="button-add">+</span>
                                                    {{$t("marketing.pages.mail_marketing.xzyjmb_b23268")}}
                                                </span>
                                                <span v-show="val.isEmail && val.model && val.isKt && emLen" class="danger">{{$t("crm.dlt.notice.email.danger")}}</span>

                                                <span v-show="val.isEmail && val.model && val.isKt && val.templ" class="xq">{{ val.templ }}</span>
                                                <span v-show="val.isEmail && val.model && val.isKt && val.templ" class="change" @click="changeEmail(index,item._uuid)" >{{$t("vcrm.change")}}</span>
                                                <span v-show="val.isEmail && val.model && val.isKt && val.templ" class="pirew" @click="pirewEmail(index)" >{{$t("marketing_pd.components.setting.yl_645dbc")}}</span>
                                            </div>
                                            <br/>
                                        </div> 
                                    </div>
                                </div>
                                <!-- 手动提醒 --> 
                                <div v-show="item.handle == '2'" class="notice-movement">
                                    <span class="automatic-type">{{ $t("crm.dlt.notice.automatic.type") }}</span>
                                    <div class="notice-right">
                                        <div v-for="(value,k) in item.movementList" :key="k" class="notice-automatic" :class="{'height3': !value.model}">
                                            <fx-checkbox v-model="value.model" :disabled="!value.isKt"> {{ value.title }}</fx-checkbox>
                                            <fx-tag v-show="!value.isKt"  type="danger" size="mini">{{ $t("未开通") }}</fx-tag>
                                            <br/>
                                            <div class="automatic-text">
                                                <span v-show="value.model && value.isKt" class="automatic-wa">{{ value.words }}</span>
                                                <fx-input
                                                    v-show="value.model && value.isKt && !value.isEmail && !value.isPrm"
                                                    type="textarea"
                                                    :placeholder='$t("vcrm.pleaseEnterContent")'
                                                    maxlength="200"
                                                    v-model="value.templ"
                                                    show-word-limit
                                                    size="small"
                                                    >
                                                </fx-input>
                                                <div v-show="!value.isEmail && value.model && value.isKt && value.isPrm" class="custom-textarea-container">
                                                    <textarea
                                                        v-model="userInputMove"
                                                        maxlength="200"
                                                        class="custom-textarea"
                                                        @input="checkFixedTextMove"
                                                        ref="textareaMove"
                                                        ></textarea>
                                                    <div class="word-count">{{ userInputMove.length }}/200</div>
                                                </div>
                                                <span class="button" v-show="value.isEmail && value.model && value.isKt && !value.emailtempl && !emLen" @click="addEmail1(index,item._uuid)">
                                                    <span class="button-add">+</span>
                                                    {{$t("marketing.pages.mail_marketing.xzyjmb_b23268")}}
                                                </span>
                                                <span v-show="value.isEmail && value.model && value.isKt && emLen" class="danger">{{$t("crm.dlt.notice.email.danger")}}</span>

                                                <span v-show="value.isEmail && value.model && value.isKt && value.emailtempl" class="xq">{{ value.emailtempl }}</span>
                                                <span v-show="value.isEmail && value.model && value.isKt && value.emailtempl" class="change"  @click="changeHandEmail(index,item._uuid)">{{$t("vcrm.change")}}</span>
                                                <span v-show="value.isEmail && value.model && value.isKt && value.emailtempl" class="pirew" @click="pirewHandEmail(index)">{{$t("marketing_pd.components.setting.yl_645dbc")}}</span>
                                            </div>
                                            <br/>
                                        </div> 
                                    </div>
                                </div>
                                <!-- 续约窗口期 -->
                                 <div class="xy-window">
                                    <div class="window-left">
                                        <span class="window-span">{{ $t("vcrm.dlt.window.span") }}</span>
                                        <fx-tooltip  class="item" :effect="effectType" :content='$t("todo")' placement="top" transition="">
                                            <span class="fx-icon-question"></span>
                                        </fx-tooltip>
                                    </div>
                                    <div class="window-right">
                                        <div v-show="item.handle == '2'">
                                            <span class="right-notice">{{ $t("vcrm.dlt.xy.startnotice") }}</span>
                                            <span class="right-start">{{ $t("vcrm.dlt.xy.before") }}</span>
                                            <fx-select
                                                class="window-select-start"
                                                v-model="item.windowStart"
                                                ref="select"
                                                :options="item.windowOptions"
                                            ></fx-select>
                                            <span  class="window-day">{{ $t("i18n.common.day") }}</span>
                                        </div>
                                        <span class="right-span">{{ $t("vcrm.dlt.window.end") }}</span>
                                        <span class="right-after">{{ $t("vcrm.dlt.window.after") }}</span>
                                        <fx-select
                                            class="window-select"
                                            v-model="item.window"
                                            ref="select"
                                            :options="item.windowOptions"
                                        ></fx-select>
                                        <span class="window-day">{{ $t("i18n.common.day") }}</span><br/>
                                        <div class="window-textarea">
                                            <span class="window-set">{{ $t("vcrm.dlt.window.set") }}</span>
                                            <div class="window-textarea-container">
                                                <textarea
                                                    v-model="windowuserInput"
                                                    maxlength="200"
                                                    class="custom-textarea"
                                                    @input="checkWindowText"
                                                    ref="textareaMove"
                                                    ></textarea>
                                                <div class="word-count">{{ windowuserInput.length }}/200</div>
                                            </div>
                                        </div>
                                    </div>
                                 </div>
                                 <!-- 展示内容 -->
                                  <div class="show-content">
                                        <div class="show-left">
                                            <span class="show-left-text">{{ $t("vcrm.dlt.show.text") }}</span>
                                        </div>
                                        <div class="show-right">
                                            <fx-checkbox-group v-model="showCheckList">
                                                <fx-checkbox label="provision_page">{{ $t("vcrm.dlt.xy.gd") }}</fx-checkbox>
                                                <fx-checkbox label="qualification_page">{{ $t("crm.channelguide.middle.qualification") }}</fx-checkbox>
                                                <fx-checkbox label="agreement_page" disabled>{{ $t("crm.brow.content") }}</fx-checkbox>
                                            </fx-checkbox-group>
                                        </div>
                                  </div>
                                   <!-- 提醒成员 -->
                                <div class="people">
                                    <span class="people-span">{{ $t("crm.dlt.notice.people.span") }}</span>
                                    <div class="right">
                                        <div class="internal">
                                            <span class="internal-span">{{ $t("内部员工") }}</span>
                                            <div :class= "`internal`+ item._uuid" style="width: 308px;"></div>
                                        </div>
                                        <div class="external">
                                            <span class="external-span">{{ $t("外部人员") }}</span>
                                            <div :class= "`external`+ item._uuid"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                           
                         </div>
                    </div>
                </div>
            </div>
        </fx-dialog>
    </div>
</template>
<script>
import crmRequire from '@common/require';
import { requireCRMList ,requireSelectorV2 } from '@common/require';
import {requirePickselfAPL} from '@common/require.js';
import emailDialog from "./emaildialog";
import editDialog from "./editdialog";
import emailTelDialog from "./emailteldialog";
import pirewDialog from "./pirewdialog";
import {getUUId} from '@common/utils';
import mockData from "./mock.js";
import api from '../utils/api';
export default {
    name: "AgreementSigning",
    components: {
        editDialog,
        emailDialog,
        emailTelDialog,
        pirewDialog,
    },
    watch: {
        userInput: {
            handler(newVal) {
                if (!newVal.includes(this.fixedText)) {
                this.userInput = this.fixedText;
                }
            },
            immediate: true
        },
        userInputMove: {
            handler(newVal) {
                if (!newVal.includes(this.fixedTextMove)) {
                this.userInputMove = this.fixedTextMove;
                }
            },
            immediate: true
        },
        windowuserInput: {
            handler(newVal) {
                if (!newVal.includes(this.windowFixedWords)) {
                this.windowuserInput = this.windowFixedWords;
                }
            },
            immediate: true
        },
        
    },
    data() {
        return {
            viewFlag: false,
            dialog: 'edit',
            showCheckList:['agreement_page'],
            dialogTitle: "",
            apiname:"",
            userInput: $t("crm.dlt.fix.words"),
            fixedText: $t("crm.dlt.fix.words"), 
            lastValidInput: $t("crm.dlt.fix.words"),
            userInputMove: $t("crm.dlt.fix.words"),
            fixedTextMove: $t("crm.dlt.fix.words"), 
            lastValidInputMove: $t("crm.dlt.fix.words"),
            windowFixedWords:$t("vcrm.dlt.fix.window.words"),
            windowuserInput:$t("vcrm.dlt.fix.window.words"),
            lastwindowuserInput:$t("vcrm.dlt.fix.window.words"),
            reviewTitie:'',
            reviewContent:'',
            // reviewPartner:'',
            // reviewPartnerContent:'',
            isCollapsed: true,
            currentSection: 'basic',
            scrollHandler: null,
            dialogVisible: false,
            tableData:[],
            columns:[
                {prop: 'schemeName', label: $t('crm.brow.name')},
                {prop: 'labelVal', label: $t('crm.brow.description')},
                {prop: 'typeLabel', label: $t('crm.brow.appKey')},
                {prop: 'priority', label: $t('crm.brow.appSecret')},
                {prop: 'signLabel', label: $t('crm.agree.type')},
                {prop: 'scheduleLabel', label: $t('crm.agree.cycle')},
            ],
            tableTotal: 0,
            isplan: true,
            isDl:true, // 是否代理商
            isApp:'prm',
            ispartner:true, // 是否合作伙伴对象
            other: false,
            Data: mockData,
            emUidIndex:0,
            emailUuid:'',
            checkEmail:{},
            checkEmailhand:{},
            emailList:[],
            isAuto:false,
            isChoose:false,
            emUid:'',
            emTelFlag:false,
            emTelFlag1:false,
            emLen:false,
            icselector:[],
            inSelect:[],
            effectType:'dark',
            isemapprovalNoticeId:'',
            issmsapprovalNoticeId:"",
            activeName:"first",
            newagreeList:[],
            options:[],
            roleOptions:[],
            editFlag: false,
            editoptions:[],
            smsapprovalNoticeId:"",
            editEmail:false,
            emapprovalNoticeId:"",
            emIndex:0,
            smsIndex:0,
            needObj:{},
            isSms:false,
            supportCustomize:false,
            filterComps: [],
            pirewFlag:false,
            pirewIndex:0,
            emailContent:'',
            dayOptions: this.generateDays(1,31),
            oncedayOptions: this.generateDays(1,31),
            firstOptions:[{
                value: '0',
                label: '0'
            },{
                value: '1',
                label: '1'
            },{
                value: '2',
                label: '2'
            },{
                value: '3',
                label: '3'
            },{
                value: '4',
                label: '4'
            },{
                value: '5',
                label: '5'
            },{
                value: '6',
                label: '6'
            },{
                value: '7',
                label: '7'
            },{
                value: '8',
                label: '8'
            },{
                value: '9',
                label: '9'
            },{
            }],
            isxyLeft: false,
        };
    },
    methods: {
        changeDayCycle(index,val) {
            if (val == '1') {
                this.newagreeList[index].oncecycleOptions = [{
                    label:$t("el.datepicker.year"),
                    value:'year'
                }];
            } else {
                this.newagreeList[index].oncecycleOptions = this.Data.cycleOptions;
            }
            this.newagreeList[index].onceTime = '';
        },
        changeType(i,val) {
            if (this.newagreeList[i].dayCycle == '1') { 
                this.newagreeList[i].oncecycleOptions = [{
                    label:$t("el.datepicker.year"),
                    value:'year'
                }];
            } else {
                this.newagreeList[i].oncecycleOptions = this.Data.cycleOptions;
            }
            this.isxyLeft = val == '2' ? true : false
        },
        closenav() {
            this.isCollapsed = !this.isCollapsed;
        },
        close() {
            if (this.scrollHandler) {
                const container = this.$refs.contentContainer;
                if (container) {
                    container.removeEventListener('scroll', this.scrollHandler);
                }
                this.scrollHandler = null;
            }
            this.dialogVisible = false;
        },
        handleScroll() {
            const container = this.$refs.contentContainer;
            if (!container) return;
            const containerRect = container.getBoundingClientRect();
            const basicSection = document.getElementById('basic');
            const rulesSection = document.getElementById('rules');
            const xyrulesSection = document.getElementById('xyrules');
            if (basicSection && rulesSection && xyrulesSection) {
                const rulesRect = rulesSection.getBoundingClientRect();
                const xyrulesRect = xyrulesSection.getBoundingClientRect();
                const rulesOffset = rulesRect.top - containerRect.top;
                const xyrulesOffset = xyrulesRect.top - containerRect.top;
                const threshold = 100;
                if (xyrulesOffset <= threshold) {
                    this.currentSection = 'xyrules';
                } else if (rulesOffset <= threshold) {
                    this.currentSection = 'rules';
                } else {
                    this.currentSection = 'basic';
                }
            }
        },
        scrollToSection(sectionId) {
            this.currentSection = sectionId;
            const element = document.getElementById(sectionId);
            const container = this.$refs.contentContainer;
            if (element && container) {
                const containerTop = container.getBoundingClientRect().top;
                const elementTop = element.getBoundingClientRect().top;
                const offset = elementTop - containerTop;
                container.scrollTo({
                    top: container.scrollTop + offset - 24,
                    behavior: 'smooth'
                });
            }
        },
        async editPlan(val,value) {
            let _this = this;
            this.dialogTitle = value == 'edit' ? $t("crm.scheme.edittitle") : value == 'copy' ? $t("crm.scheme.copytitle") : $t("crm.scheme.viewtitle");
            this.dialog = value == 'edit' ? 'edit' : value == 'copy' ? 'copy' : 'view';
            _this.viewFlag =  value == 'view' ? true : false;
            let { signScheme } = await this.fetchSchemeDetail(val.signSchemeId);
            console.log(signScheme,'--');
            let aplList = await _this.getAplList();
            const signArr = [{ ...signScheme }];
            _this.newagreeList =[];
            _this.dialogVisible = true;
            signArr.forEach(item => {
                item.aplmap =[];
                aplList.forEach(val => {
                    if (item?.aplApiName == val.api_name) {
                        item.aplmap.push(val)
                    }
                })
                item.approvalNoticeList.forEach(j => {
                    j.smsAPlmap = [];
                    j.emailAPlmap = [];
                    aplList.forEach(k => {
                        if (j?.aplApiName == k.api_name && j.notifyVia == 'sms') {
                            j.smsAPlmap.push(k)
                        }
                        if (j?.aplApiName == k.api_name && j.notifyVia == 'email') {
                            j.emailAPlmap.push(k)
                        }
                    })
                })
            })
            signArr.forEach((item,index) => {
                let _uuid = getUUId();
                let autoemailObj = {}
                if (item.scheduleType !== "one_time") {
                    _this.userInput = _this.unchangeText(item.expireReminderTypeView.autoExpireReminderType.prmAlertWindowReminder.message);
                    _this.userInputMove = _this.unchangeText(item.expireReminderTypeView.manualExpireReminderType.prmAlertWindowReminder.message);
                    _this.lastValidInput = _this.unchangeText(item.expireReminderTypeView.autoExpireReminderType.prmAlertWindowReminder.message);
                    _this.lastValidInputMove = _this.unchangeText(item.expireReminderTypeView.manualExpireReminderType.prmAlertWindowReminder.message);
                }
                if (item.expireReminderTypeView.autoExpireReminderType.emailReminder.templateId) {
                    autoemailObj = _this.idtoName(item.expireReminderTypeView.autoExpireReminderType.emailReminder.templateId,_uuid);
                }
                let manualEmail = {}
                if (item.expireReminderTypeView.manualExpireReminderType.emailReminder.templateId) {
                    manualEmail = _this.idtoNamehand(item.expireReminderTypeView.manualExpireReminderType.emailReminder.templateId,_uuid);
                }
                let defaultValue = item.conditionType == "CONDITION" ? JSON.parse(JSON.parse(item.condition).value) : null;
                let emailapprovalNotice = item?.approvalNoticeList.filter(item => {
                    return item.notifyVia == 'email'
                }) || [];
                let smslapprovalNotice = item?.approvalNoticeList.filter(item => {
                    return item.notifyVia == 'sms'
                }) || [];
                _this.emapprovalNoticeId = emailapprovalNotice[0]?.approvalNoticeId || "";
                _this.newagreeList.push({
                    isSaveFlag:item.pushed || item.signSchemeId !== "",
                    autocrmReminderId:  value == 'edit' ? item.expireReminderTypeView.autoExpireReminderType.crmReminder.reminderTypeId : '',
                    autoprmAlertWindowReminderId: value == 'edit' ? item.expireReminderTypeView.autoExpireReminderType.prmAlertWindowReminder.reminderTypeId : '',
                    autoemailReminderId: value == 'edit' ? item.expireReminderTypeView.autoExpireReminderType.emailReminder.reminderTypeId : '',
                    autoprmCrmReminderId: value == 'edit' ? item.expireReminderTypeView.autoExpireReminderType.prmCrmReminder.reminderTypeId : '',
                    autosmsReminderId: value == 'edit' ? item.expireReminderTypeView.autoExpireReminderType.smsReminder.reminderTypeId : '',
                    manualcrmReminderId: value == 'edit' ? item.expireReminderTypeView.manualExpireReminderType.crmReminder.reminderTypeId : '',
                    manualprmAlertWindowReminderId: value == 'edit' ? item.expireReminderTypeView.manualExpireReminderType.prmAlertWindowReminder.reminderTypeId : '',
                    manualemailReminderId: value == 'edit' ? item.expireReminderTypeView.manualExpireReminderType.emailReminder.reminderTypeId : '',
                    manualprmCrmReminderId: value == 'edit' ? item.expireReminderTypeView.manualExpireReminderType.prmCrmReminder.reminderTypeId : '',
                    manualsmsReminderId: value == 'edit' ? item.expireReminderTypeView.manualExpireReminderType.smsReminder.reminderTypeId : '',
                    isPush:item.pushed,
                    cyclezq: item.renewalCycleTime,  //签署周期
                    oncezq: item.planDuration,
                    cycleTime: item.renewalCycleUnit, //  年月日
                    onceTime: item.planDurationUnit,
                    mouthTime: item?.nextRenewalDate?.split('-')[0] || null,
                    window: item.renewalWindowEnd,
                    windowStart: item.renewalWindowStart,
                    oncemouth: item?.startDate?.split('-')[0] || null,
                    oncedayTime:item?.startDate?.split('-')[1] || null,
                    dayTime: item?.nextRenewalDate?.split('-')[1] || null,
                    handle: item.reminderTrigger == 'auto' ? '1' : '2',  //自动手动]
                    newFirst: item.priority,
                    firstOptions: _this.firstOptions,
                    noticeList: item.scheduleType == "one_time" ? [] : [
                        {
                            model: item.expireReminderTypeView.autoExpireReminderType.prmAlertWindowReminder.activated,
                            title:$t("crm.dlt.notice.automatic.model"),
                            time:$t("crm.dlt.notice.automatic.time"),
                            before:$t("crm.dlt.notice.automatic.before"),
                            words:$t("automatic-words"),
                            isEmail:false,
                            isQue:false,
                            isKt:true,
                            nosel:item.expireReminderTypeView.autoExpireReminderType.prmAlertWindowReminder.timeUnit,
                            num:item.expireReminderTypeView.autoExpireReminderType.prmAlertWindowReminder.reminderTime,
                            noTel:_this.unchangeText(item.expireReminderTypeView.autoExpireReminderType.prmAlertWindowReminder.message),
                            isPrm:true
                        },
                        {
                            model:item.expireReminderTypeView.autoExpireReminderType.smsReminder.activated,
                            title:$t("crm.dlt.notice.automatic.sms"),
                            time:$t("crm.dlt.notice.automatic.time"),
                            before:$t("crm.dlt.notice.automatic.before"),
                            words:$t("automatic-words"),
                            isEmail:false,
                            isQue:false,
                            isKt:this.isSms,
                            nosel:item.expireReminderTypeView.autoExpireReminderType.smsReminder.timeUnit,
                            num:item.expireReminderTypeView.autoExpireReminderType.smsReminder.reminderTime,
                            noTel:item.expireReminderTypeView.autoExpireReminderType.smsReminder.message,
                            isPrm:false
                        },
                        {
                            model:item.expireReminderTypeView.autoExpireReminderType.emailReminder.activated,
                            title:$t("crm.dlt.notice.automatic.email"),
                            time:$t("crm.dlt.notice.automatic.time"),
                            before:$t("crm.dlt.notice.automatic.before"),
                            words:$t("crm.dlt.email.words"),
                            nosel:item.expireReminderTypeView.autoExpireReminderType.emailReminder.timeUnit,
                            num:item.expireReminderTypeView.autoExpireReminderType.emailReminder.reminderTime,
                            templateId:item.expireReminderTypeView.autoExpireReminderType.emailReminder.templateId,
                            isEmail:true,
                            templ: autoemailObj?.name || "",
                            isQue:false,
                            isKt:true,
                            isPrm:false
                        },
                        {
                            model:item.expireReminderTypeView.autoExpireReminderType.crmReminder.activated,
                            title:$t("CRM提醒"),
                            time:$t("crm.dlt.notice.automatic.time"),
                            before:$t("crm.dlt.notice.automatic.before"),
                            words:$t("automatic-words"),
                            isEmail:false,
                            isQue:true,
                            isKt:true,
                            crm: true,
                            nosel:item.expireReminderTypeView.autoExpireReminderType.crmReminder.timeUnit,
                            num:item.expireReminderTypeView.autoExpireReminderType.crmReminder.reminderTime,
                            noTel:item.expireReminderTypeView.autoExpireReminderType.crmReminder.message,
                            isPrm:false
                        },
                        {
                            model:item.expireReminderTypeView.autoExpireReminderType.prmCrmReminder.activated,
                            title:$t("业务通知"),
                            time:$t("crm.dlt.notice.automatic.time"),
                            before:$t("crm.dlt.notice.automatic.before"),
                            words:$t("automatic-words"),
                            isEmail:false,
                            isQue:true,
                            isKt:true,
                            yw:true,
                            nosel:item.expireReminderTypeView.autoExpireReminderType.prmCrmReminder.timeUnit,
                            num:item.expireReminderTypeView.autoExpireReminderType.prmCrmReminder.reminderTime,
                            noTel:item.expireReminderTypeView.autoExpireReminderType.prmCrmReminder.message,
                            isPrm:false
                        },
                    ],
                    movementList: item.scheduleType == "one_time" ? [] : [
                        {
                            model:item.expireReminderTypeView.manualExpireReminderType.prmAlertWindowReminder.activated,
                            title:$t("crm.dlt.notice.automatic.model"),
                            words:$t("automatic-words"),
                            isKt:true,
                            isEmail:false,
                            templ:_this.unchangeText(item.expireReminderTypeView.manualExpireReminderType.prmAlertWindowReminder.message),
                            isPrm:true
                        },
                        {
                            model:item.expireReminderTypeView.manualExpireReminderType.smsReminder.activated,
                            title:$t("crm.dlt.notice.automatic.sms"),
                            words:$t("automatic-words"),
                            isKt:this.isSms,
                            isEmail:false,
                            templ:item.expireReminderTypeView.manualExpireReminderType.smsReminder.message,
                            isPrm:false
                        },
                        {
                            model:item.expireReminderTypeView.manualExpireReminderType.emailReminder.activated,
                            title:$t("crm.dlt.notice.automatic.email"),
                            words:$t("crm.dlt.email.words"),
                            isEmail:true,
                            templateId:item.expireReminderTypeView.manualExpireReminderType.emailReminder.templateId,
                            isKt:true,
                            emailtempl: manualEmail?.name || "",
                            isPrm:false
                            
                        },
                        {
                            model:item.expireReminderTypeView.manualExpireReminderType.crmReminder.activated,
                            title:$t("CRM提醒"),
                            words:$t("automatic-words"),
                            isKt:true,
                            isEmail:false,
                            templ:item.expireReminderTypeView.manualExpireReminderType.crmReminder.message,
                            isPrm:false
                        },
                        {
                            model:item.expireReminderTypeView.manualExpireReminderType.prmCrmReminder.activated,
                            title:$t("业务通知"),
                            words:$t("automatic-words"),
                            isKt:true,
                            isEmail:false,
                            templ:item.expireReminderTypeView.manualExpireReminderType.prmCrmReminder.message,
                            isPrm:false
                        },
                    ],

                    _uuid,
                    defaultValue,
                    smsValue:smslapprovalNotice[0]?.enabled,
                    emailValue:emailapprovalNotice[0]?.enabled,
                    name: value == 'copy' ? item.schemeName + $t("-副本") : item.schemeName,
                    agreementName: item.agreementId,
                    options:_this.options,
                    cycleOptions: _this.Data.cycleOptions,
                    oncecycleOptions: _this.Data.cycleOptions,
                    mouthOptions:_this.Data.mouthOptions,
                    oncemouthOptions: _this.Data.mouthOptions,
                    windowOptions:  _this.Data.windowOptions,
                    varOverdueMessage: item.varOverdueMessage,
                    renewalPage: item.renewalPage,
                    dayOptions: this.generateDays(1,31),
                    oncedayOptions: this.generateDays(1,31),
                    automaticOptions:_this.Data.automaticOptions,
                    radio: item.conditionType == "ALL" ? "1" : "2",
                    radio1: item.signMode == "manual" ? "1" : "2",
                    dayCycle: item.scheduleType == "fixed_date" ? "1" : item.scheduleType == "cycle" ? "2" :'',
                    roles: item.activateRoles,
                    roleOptions:_this.roleOptions,
                    signSchemeId: value == 'edit' ? item.signSchemeId : '', //copy
                    noticeIds: item.noticeIds,
                    emailapprovalNoticeId: emailapprovalNotice[0]?.approvalNoticeId || "",
                    smsapprovalNoticeId: smslapprovalNotice[0]?.approvalNoticeId || "",
                    apl:item?.aplmap[0]?.function_name || '',
                    aplArr:item?.aplmap || [],
                    aplId:item?.aplmap[0]?.id || '',
                    aplName:item?.aplApiName,
                    isAplSlowEamil: item.signMode == "manual" ? false : true,
                    radioType: item.scheduleType == "one_time" ? "1" : "2",
                    smsAplArr:smslapprovalNotice[0]?.smsAPlmap.length ? smslapprovalNotice[0].smsAPlmap  : [],
                    smsAplVal:smslapprovalNotice[0]?.smsAPlmap.length ? smslapprovalNotice[0]?.smsAPlmap[0]?.function_name :'',
                    emailAplArr: emailapprovalNotice[0]?.emailAPlmap.length ? emailapprovalNotice[0]?.emailAPlmap : [],
                    emailAplVal: emailapprovalNotice[0]?.emailAPlmap.length ? emailapprovalNotice[0]?.emailAPlmap[0]?.function_name : '',
                })
                this.$nextTick(()=>{
                    _this.changeRanNew(_this.newagreeList[0].radio);
                    _this.initicselectorV2(_uuid);
                    let outerRoleIds = new Array();
                    let outerTenantIds = new Array();
                    let outerTgroupIds = new Array();
                    let outerUids = new Array();
                    let group = new Array();
                    let member = new Array();
                    let usergroup = new Array();
                    item.expireReminderPersonView.externalPerson.forEach(item => {
                        switch(item.memberType) {
                            case 'person':
                                outerUids.push(item.dataId)
                                break;
                            case 'link_group':
                                outerTgroupIds.push(item.dataId)
                                break;
                            case 'link_enterprise':
                                outerTenantIds.push(item.dataId)
                                break;
                            case 'link_role':
                                outerRoleIds.push(item.dataId)
                                break;
                        }
                    })
                    item.expireReminderPersonView.internalPerson.forEach(item => {
                        switch(item.memberType) {
                            case 'person':
                            member.push(item.dataId)
                                break;
                            case 'group':
                                usergroup.push(item.dataId)
                                break;
                            case 'department':
                                group.push(item.dataId)
                                break;
                        }
                    })
                    setTimeout(() => {
                        if(_this.newagreeList[0].radioType !== "1") {
                            _this.showCheckList = _this.newagreeList[0].renewalPage;
                            _this.windowuserInput = _this.xyunchangeText(_this.newagreeList[0].varOverdueMessage)
                            _this.icselector[_uuid].$refs.icselector.setValue({
                                    outerRoleIds: outerRoleIds,
                                    outerTenantIds: outerTenantIds,
                                    outerTgroupIds: outerTgroupIds,
                                    outerUids: outerUids
                            })
                            _this.inSelect[_uuid]._selector.setValue({
                                group: group,
                                member: member,
                                usergroup: usergroup
                            })
                        }
                    },1000)
                    if (item.conditionType == "CONDITION") {
                        let _filter = _this.addFilterGroup('', defaultValue)
                        this.filterComps.push(_filter);
                    } else {
                        let _filter = this.addFilterGroup('', null)
                        this.filterComps.push(_filter);
                        $(".condition" + (index)).hide();
                    }
                })
            })
        },
        fetchSchemeDetail(signId) {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/fetch_sign_scheme_detail",
                        data: {
                            "signSchemeId": signId
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        onPlan(val) {
            this.toggleScheme(val.signSchemeId,!val.pushed)
        },
        offPlan(val) {
            this.toggleScheme(val.signSchemeId,!val.pushed)
        },
        toggleScheme(signId,pushed) {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/toggle_scheme_status",
                        data: {
                            "signSchemeId":signId,
                            "pushed":pushed
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                _this.handleClick({index:1});
                                if(pushed) {
                                    CRM.util.remind(1, $t("启用成功"));
                                } else {
                                    CRM.util.remind(1, $t("停用成功"));
                                }
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        deletePlan(val,i) {
            let _this = this;
            _this.$confirm($t('是否确认删除？'), $t('删除'), {
                confirmButtonText: $t('确定'),
                cancelButtonText: $t('取消'),
            }).then(() => {
                if(val.signSchemeId) {
                    return new Promise(function(resolve) {
                        CRM.util.FHHApi({
                                url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_delete_sign_scheme",
                                data: {
                                    "signSchemeId": val.signSchemeId
                                },
                                success: function(res) {
                                    if (res.Result.StatusCode === 0) {
                                        CRM.util.remind(1, $t("删除成功"));
                                        _this.tableData.splice(i, 1);
                                        resolve(res.Value);
                                        return;
                                    }
                                    CRM.util.alert(
                                        res.Result.FailureMessage ||
                                            $t("暂时无法获取数据") + "!"
                                    );
                                }
                            },{ errorAlertModel: 1}
                        );
                    });
                }
            }).catch(() => {
            });
        },
        canelScheme() {
            let uuid = this.newagreeList[0]._uuid;
            this.newagreeList = [];
            this.inSelect[uuid]._selector.destroy();
            this.inSelect[uuid] = null;
            this.$refs['filter-ref'] = null;
            this.icselector[uuid] = null;
            this.dialogVisible = false;
            this.filterComps = [];
        },
        handleDialogOpen() {
            const _this = this;
            setTimeout(() => {
                if (_this.newagreeList && _this.newagreeList.length > 0) {
                    const lastItem = _this.newagreeList[_this.newagreeList.length - 1];
                    if (lastItem && lastItem._uuid) {
                        const container = _this.$refs.contentContainer;
                        if (container) {    
                            _this.scrollHandler = _this.handleScroll.bind(_this);
                            container.addEventListener('scroll', _this.scrollHandler);
                        }
                        _this._requireSelectorV2(lastItem._uuid);
                    }
                }
            }, 200);
        },

        checkFixedText() {
            const currentValue = this.userInput;
            if (!currentValue.includes(this.fixedText)) {
                this.userInput = this.lastValidInput;
            } else {
                this.lastValidInput = currentValue;
            }
        },
        checkWindowText() {
            const currentValue = this.windowuserInput;
            if (!currentValue.includes(this.windowFixedWords)) {
                this.windowuserInput = this.lastwindowuserInput;
            } else {
                this.lastwindowuserInput = currentValue;
            }
        },
        checkFixedTextMove() {
            const currentValue = this.userInputMove;
            if (!currentValue.includes(this.fixedTextMove)) {
                this.userInputMove = this.lastValidInputMove;
            } else {
                this.lastValidInputMove = currentValue;
            }
        },
      
        newPlan() {
            let _this = this;
            this.dialog = 'add';
            this.dialogTitle = $t("crm.scheme.addtitle");
            this.userInput = this.fixedText;
            this.lastValidInput = this.fixedText;
            this.userInputMove = this.fixedTextMove;
            this.lastValidInputMove = this.fixedTextMove;
            this.lastwindowuserInput =this.windowFixedWords;
            this.windowuserInput =this.windowFixedWords;
            this.showCheckList = ['agreement_page'];
            this.viewFlag = false;
            let _uuid = getUUId();
            _this.newagreeList =[];
            _this.dialogVisible = true;
            _this.newagreeList.push({
                roles:'',
                roleOptions: this.roleOptions,
                smsAplVal:'',
                emailAplVal:'',
                emailValue:false,
                smsValue:false,
                _uuid: _uuid,
                noticeList:[
                            {
                                model:true,
                                title:$t("crm.dlt.notice.automatic.model"),
                                time:$t("crm.dlt.notice.automatic.time"),
                                before:$t("crm.dlt.notice.automatic.before"),
                                words:$t("automatic-words"),
                                isEmail:false,
                                isQue:false,
                                isKt:true,
                                nosel:"day",
                                num: 3,
                                noTel:$t("crm.dlt.notice.automatic.threeday"),
                                isPrm:true
                            },
                            {
                                model:this.isSms,
                                title:$t("crm.dlt.notice.automatic.sms"),
                                time:$t("crm.dlt.notice.automatic.time"),
                                before:$t("crm.dlt.notice.automatic.before"),
                                words:$t("automatic-words"),
                                isEmail:false,
                                isQue:false,
                                isKt:this.isSms,
                                nosel:"week",
                                num:1,
                                noTel:$t("crm.dlt.notice.automatic.oneweek"),
                                isPrm:false
                            },
                            {
                                model:false,
                                title:$t("crm.dlt.notice.automatic.email"),
                                time:$t("crm.dlt.notice.automatic.time"),
                                before:$t("crm.dlt.notice.automatic.before"),
                                words:$t("crm.dlt.email.words"),
                                isEmail:true,
                                templateId:"",
                                nosel:"week",
                                num:1,
                                templ:'',
                                isQue:false,
                                isKt:true,
                                isPrm:false
                            },
                            {
                                model:false,
                                title:$t("CRM提醒"),
                                time:$t("crm.dlt.notice.automatic.time"),
                                before:$t("crm.dlt.notice.automatic.before"),
                                words:$t("automatic-words"),
                                isEmail:false,
                                isQue:true,
                                isKt:true,
                                crm: true,
                                nosel:"week",
                                num:1,
                                noTel:$t("crm.dlt.notice.automatic.oneweek"),
                                isPrm:false
                            },
                            {
                                model:false,
                                title:$t("业务通知"),
                                time:$t("crm.dlt.notice.automatic.time"),
                                before:$t("crm.dlt.notice.automatic.before"),
                                words:$t("automatic-words"),
                                isEmail:false,
                                isQue:true,
                                isKt:true,
                                yw:true,
                                nosel:"week",
                                num:1,
                                noTel:$t("crm.dlt.notice.automatic.oneweek"),
                                isPrm:false
                            },
                        ],
                movementList:[
                    {
                        model:true,
                        title:$t("crm.dlt.notice.automatic.model"),
                        words:$t("automatic-words"),
                        isKt:true,
                        isEmail:false,
                        templ:$t("crm.dlt.notice.automatic.threeday"),
                        isPrm:true
                    },
                    {
                        model:this.isSms,
                        title:$t("crm.dlt.notice.automatic.sms"),
                        words:$t("automatic-words"),
                        isKt:this.isSms,
                        isEmail:false,
                        templ:$t("crm.dlt.notice.automatic.oneweek"),
                        isPrm:false
                    },
                    {
                        model:false,
                        title:$t("crm.dlt.notice.automatic.email"),
                        words:$t("crm.dlt.email.words"),
                        isEmail:true,
                        templateId:"",
                        isKt:true,
                        emailtempl:'',
                        isPrm:false
                    },
                    {
                        model:false,
                        title:$t("CRM提醒"),
                        words:$t("automatic-words"),
                        isKt:true,
                        isEmail:false,
                        templ:$t("crm.dlt.notice.automatic.oneweek"),
                        isPrm:false
                    },
                    {
                        model:false,
                        title:$t("业务通知"),
                        words:$t("automatic-words"),
                        isKt:true,
                        isEmail:false,
                        templ:$t("crm.dlt.notice.automatic.oneweek"),
                        isPrm:false
                    },
                ],
                automaticOptions:this.Data.automaticOptions,
                handle:'1',
                name:'', // 方案名称
                newFirst:'1', // 模板优先级
                firstOptions: this.firstOptions,
                options: this.options,
                agreementName:'', // 签署协议
                radio1:'1',
                radioType:'1',
                dayCycle:'1',
                radio:'1',
                isAplSlowEamil:false,
                apl:'',
                cyclezq:'',
                oncezq:'',
                cycleTime:'',
                onceTime:'',
                mouthTime:'',
                window:'',
                windowStart:'',
                oncemouth:'',
                dayTime:'',
                oncedayTime:'',
                isPush:false,
                mouthOptions:this.Data.mouthOptions,
                oncemouthOptions:this.Data.mouthOptions,
                windowOptions: this.Data.windowOptions,
                dayOptions:this.generateDays(1,31),
                oncedayOptions:this.generateDays(1,31),
                cycleOptions: this.Data.cycleOptions,
                oncecycleOptions: this.Data.cycleOptions,
                smsAplArr:[],
                emailAplArr:[]
            });
            _this.$nextTick(()=>{
                let _filter = _this.addFilterGroup('', null)
                _this.filterComps.push(_filter);
                _this.initicselectorV2(_uuid);
            })
        },
        querySiantext() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/fetch_custom_sign_text",
                        data: {
                           
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        flowSave() {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/save_custom_sign_text",
                        data: {
                            "signingText": {
                                "title": _this.reviewTitie,
                                "content": _this.reviewContent
                            },
                            // "renewalText": {
                            //     "title": _this.reviewPartner,
                            //     "content": _this.reviewPartnerContent
                            // }
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                CRM.util.remind(1, $t("保存成功"));
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        planList() {
            this.isplan = true;
        },
        ruleset() {
            this.isplan = false;
            this.querySiantext().then(res => {
                this.reviewTitie = res.signingText.title;
                this.reviewContent = res.signingText.content;
                // this.reviewPartner = res.renewalText.title;
                // this.reviewPartnerContent = res.renewalText.content;
            });
        },
        toSetting(val) {
            window.location.replace(`https://${window.location.host}/XV/UI/manage#crmmanage/=/module-channel-home/key-${val}`);
        },
        generateDays(start, end) {
            const days = [];
            for (let i = start; i <= end; i++) {
                days.push({
                    label:i,
                    value:i+ ''
                });
            }
            return days;
        },
        textBlur(index) {
            setTimeout(() => {
                if (this.newagreeList[index].oncemouth !== '') {
                    if (this.newagreeList[index].dayTime !== '') {
                        if((this.newagreeList[index].mouthTime == this.newagreeList[index].oncemouth)  && (this.newagreeList[index].dayTime == this.newagreeList[index].oncedayTime)) {
                            CRM.util.alert($t("vcrm.xy.startdate"));
                            this.newagreeList[index].dayTime = '';
                            this.newagreeList[index].mouthTime = '';
                            return
                        } else {
                            let year = new Date().getFullYear();
                            let endday = year + '-' + this.newagreeList[index].mouthTime + '-' + this.newagreeList[index].dayTime;
                            let startday = year + '-' + this.newagreeList[index].oncemouth + '-' + this.newagreeList[index].oncedayTime;
                            if (new Date(startday) > new Date(endday)) {
                                this.newagreeList[index].noticeList[0].num = this.getDaysBetweenDates(endday, startday);
                            } else {
                                let startday = (year + 1) + '-' + this.newagreeList[index].oncemouth + '-' + this.newagreeList[index].oncedayTime;
                                this.newagreeList[index].noticeList[0].num = this.getDaysBetweenDates(endday, startday);
                            }
                        }
                    }
                }
            },1000)
        },
        handleMonthChange(index,newMonth) {
            const daysInMonth = this.getDaysInMonth(newMonth);
            this.newagreeList[index].dayOptions = this.generateDays(1, daysInMonth);
        },
        dayBlur(index) {
            setTimeout(() => {
                if ((this.newagreeList[index].mouthTime !== '') && (this.newagreeList[index].dayTime !== '')) {
                    if((this.newagreeList[index].mouthTime == this.newagreeList[index].oncemouth)  && (this.newagreeList[index].dayTime == this.newagreeList[index].oncedayTime)) {
                        CRM.util.alert($t("vcrm.xy.startdate"));
                        this.newagreeList[index].dayTime = '';
                        this.newagreeList[index].mouthTime = '';
                        return
                    } else {
                        let year = new Date().getFullYear();
                        let endday = year + '-' + this.newagreeList[index].mouthTime + '-' + this.newagreeList[index].dayTime;
                        let startday = year + '-' + this.newagreeList[index].oncemouth + '-' + this.newagreeList[index].oncedayTime;
                        if (new Date(startday) > new Date(endday)) {
                            this.newagreeList[index].noticeList[0].num = this.getDaysBetweenDates(endday, startday);
                        } else {
                            let startday = (year + 1) + '-' + this.newagreeList[index].oncemouth + '-' + this.newagreeList[index].oncedayTime;
                            this.newagreeList[index].noticeList[0].num = this.getDaysBetweenDates(endday, startday);
                        }
                    }
                }
            },1000)
        },
        getDaysBetweenDates(date1, date2) {
            const d1 = new Date(date1);
            const d2 = new Date(date2);
            const timeDiff = Math.abs(d2.getTime() - d1.getTime());
            const dayDiff = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
            return dayDiff;
        },
        handleOnceChange(index,newMonth) {
            this.newagreeList[index].oncedayTime = '';
            const daysInMonth = this.getDaysInMonth(newMonth);
            this.newagreeList[index].oncedayOptions = this.generateDays(1, daysInMonth);
        },
        getDaysInMonth(month) {
            const year = new Date().getFullYear();
            return new Date(year, month, 0).getDate();
        },
        cycleBlur1(index,j,event) {
            if (event.target.value == '0') {
                this.newagreeList[index].noticeList[j].num = '';
            }
        },
        cycleBlur(index,value) {
            if(value.target.value == '0') {
                this.newagreeList[index].cyclezq = '';
            }
        },
        oncecycleBlur(index,value){
            if(value.target.value == '0') {
                this.newagreeList[index].oncezq = '';
            }
        },
        goPush() {
            window.open(`https://${window.location.host}/XV/UI/manage#applicationmanage/=/main-crossapp/sub-crossappList`, "_blank")
        },
        pirewEmail(index) {
            this.pirewFlag = true;
            this.pirewIndex = index;
            this.emailContent = this.newagreeList[index].noticeList[2].templ;
        },
        pirewHandEmail(index) {
            this.pirewFlag = true;
            this.pirewIndex = index;
            this.emailContent = this.newagreeList[index].movementList[2].emailtempl;
        },
        changeEmail(index,uuid) {
            this.emTelFlag = true;
            this.emUidIndex = index;
            this.isChoose = false;
            this.isAuto = true;
            this.emailUuid = uuid;
        },
        changeHandEmail(index,uuid) {
            this.emTelFlag = true;
            this.emUidIndex = index;
            this.isChoose = false;
            this.isAuto = false;
            this.emailUuid = uuid;
        },
        addEmail(index,uuid) {
            this.emTelFlag = true;
            this.emUidIndex = index;
            this.isChoose = true;
            this.isAuto = true;
            this.emailUuid = uuid;
        },
        addEmail1(index,uuid) {
            this.emTelFlag = true;
            this.emUidIndex = index;
            this.isChoose = true;
            this.isAuto = false;
            this.emailUuid = uuid;
        },
        async _requireSelectorV2(uuid) {
            try {
                    const _this = this;
                    const SelectorV2 = await requireSelectorV2();
                    await this.$nextTick();
                    const selector = `.internal${uuid}`;
                    const $wrapElement = $(selector);
                    if ($wrapElement.length) {
                        _this.inSelect[uuid] = new SelectorV2({
                            $wrap: $wrapElement,
                            label: _this.$t("添加选项"),
                            group: true,
                            member: true,
                            newRecent: true,
                            usergroup: true,
                            zIndex: 4000,
                            groupIncludeChildrenStatus: 2,
                            groupIncludeChildrenStatusDisabled: true
                        });
                    } else {
                        console.error(selector);
                    }
                } catch (error) {
                    console.error(error);
                }

            // let _this = this;
            // requireSelectorV2().then((SelectorV2) => {
            //     let _SelectorV2 = SelectorV2;
            //     _this.inSelect[uuid] = new _SelectorV2({
            //         $wrap: $(".internal" + uuid, _this.$el),
            //         label: $t("添加选项"),
            //         group: true,
            //         member: true,
            //         newRecent:true,
            //         usergroup: true,
            //         zIndex: 4000,
            //         groupIncludeChildrenStatus: 2,
            //         groupIncludeChildrenStatusDisabled: true
            //     });
            // })
        },
        initicselectorV2(uuid) {
            let _this = this;
            seajs.use("icmanage-modules/icselectorV2/icselector", function (mod) {
                const SelectWarp = Vue.extend({
					template:
						`<icSelector v-bind="icsProps" ref="icselector" @change="icsChange"></icSelector>`,
					components: {
						icSelector: mod.icSelectorInput,
					},
					data() {
						return {
							icsProps: {
								selectorOpts: {
									addBtnLabel: $t("添加选项"),
									tabs: [
                                        "outerUids",
                                        "outerTenantIds",
                                        "outerTgroupIds",
                                        "outerRoleIds"
									],
									excludeItems: {}
								},
							},
							icsChange(data) {
                                const { outerUids,outerTenantIds ,outerTgroupIds,outerRoleIds } = data;
							},
						};
					},
				});
                _this.icselector[uuid] = new SelectWarp().$mount(this.$(".external" + uuid)[0]);
            });
        },

        clocseEmTel(val) {
            if(val) {
                if (val.isAuto) {
                    this.newagreeList[val.index].noticeList[2].templ = val.name;
                    this.newagreeList[val.index].noticeList[2].templateId = val.templateId;
                    this.idtoName(val.templateId,val.uuid)
                } else {
                    this.newagreeList[val.index].movementList[2].emailtempl = val.name;
                    this.newagreeList[val.index].movementList[2].templateId = val.templateId;
                    this.idtoNamehand(val.templateId,val.uuid)
                }
            }
            this.emTelFlag = false;
        },
        closePirew() {
            this.pirewFlag = false;
        },
        clearAplsm(val,i) {
            if (val == 'sms') {
                this.newagreeList[i].smsAplVal = '';
                this.saveApl(i,'sms','')
            } else {
                this.newagreeList[i].emailAplVal = '';
                this.saveApl(i,'email','')
            }
        },
        clearApl(i) {
            this.newagreeList[i].apl = '';
        },
        addFilterGroup(index, defaultValue) {
            let _this = this;
            return new this.filterGroup({
                $wrapper: $(_this.$refs['filter-ref' + index]),
                apiname: this.ispartner ? 'PartnerObj' : 'AccountObj',
                defaultValue,
                width: 900,
                filterType: [
                    'object_reference',
                    'group',
                    'image',
                    'file_attachment',
                    'master_detail',
                    'auto_number',
                    'signature',
                    'quote',
                    'embedded_object_list',
                    'multi_level_select_one',
                    'tree_path',
                    'employee_many',
                    'department_many',
                    'html_rich_text',
                    'object_reference_many',
                    'big_file_attachment',
                ], 
                filterApiname: [],
                props: {
                    lazy: true,
                    checkStrictly: true, 
                    expandTrigger: 'click'
                },
            });

        },
        idtoName(val,_uuid) {
            let found = this.emailList.find(element => element.templateId === val);
            this.checkEmail = found;
            this.checkEmail.uuid = _uuid;
            return found
        },
        idtoNamehand(val,_uuid) {
            let found = this.emailList.find(element => element.templateId === val);
            this.checkEmailhand = found;
            this.checkEmailhand.uuid = _uuid;
            return found
        },
        async handleClick (tab) {
            let _this = this;
            if(tab.index == 1) {
                let listData = await _this.getListsData();
                _this.options = _.map(listData, val => ({
                    label: val.agreement_title,
                    value: val._id
                }));
                let roleList = await _this.roleList();
                _this.roleOptions = _.map(roleList.roles, val => ({
                    label: val.roleName,
                    value: val.roleCode
                }));
                let res = await this.channelQuerySignScheme();
                _this.tableTotal = res.signSchemes.length;
                res.signSchemes.forEach((item,index) => {
                    item.labelVal = _this.getValueById(_this.options, item.agreementId);
                    item.signLabel = item.signMode == 'apl' ? $t("sfa.vcrm.dlt.apl") : $t("crm.hand.file");
                    item.scheduleLabel = item.scheduleType == 'one_time' ? $t('crm.dlt.fixed.once') : item.scheduleType == 'fixed_date' ? $t('crm.dlt.fixed.signed') : $t('crm.dlt.fixed.agian');
                    item.typeLabel = JSON.parse(item.condition).type == "CONDITION" ? $t("crm.brow.condition") : $t("全部");
                })
                this.tableData = JSON.parse(JSON.stringify(res.signSchemes));
                console.log(this.tableData);
                let { result } = await _this.getEmailList();
                _this.emLen = result.list.length ? false : true;
                _this.emailList = result.list;
                let smsObject = await _this.provisioningSms();
                _this.isSms = smsObject.data.open;
                _this.supportCustomize = smsObject.data.supportCustomize;
            }
        },
        getValueById(arr,id) {
            const item = arr.find((item) => item.value === id);
            return item ? item.label : undefined;
        },
        channelQuerySignScheme() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_query_sign_scheme",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        provisioningSms() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/sms_hub/service/query_sms_status",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
         async focuspass(i,val) {
            let _this = this;
            let PickselfAPL = await requirePickselfAPL();
            _this.pickselfApl = new PickselfAPL({
                postData: {
                    binding_object_api_name: this.ispartner ? 'PartnerObj' : 'AccountObj', // 绑定对象
                    name_space: ['channel_sign'], // 范围规则
                    return_type: null, // 返回值类型
                },
                checkedData: val !=='' ? _this.newagreeList[i].aplArr :[]
            })
            _this.pickselfApl.on('dialogEnter', (checkedData) => {
                _this.newagreeList[i].aplArr = checkedData;
                _this.newagreeList[i].apl = checkedData[0].function_name;
                _this.newagreeList[i].aplId = checkedData[0].id;
                _this.newagreeList[i].aplName = checkedData[0].api_name;
                _this.$emit('dialogEnter')
            })
        },
        changeApl(i,val) {
            let _this = this;
            if(val == 1) {
                _this.newagreeList[i].isAplSlowEamil = false;
            } else {
                _this.newagreeList[i].isAplSlowEamil = true;
            }
        },
        roleList() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_role_list",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        editema(index) {
            let _this = this;
            if(!_this.viewFlag) {
                _this.isemapprovalNoticeId = _this.newagreeList[index]?.emailapprovalNoticeId || '';
                if(_this.newagreeList[index].emailapprovalNoticeId) {
                    _this.isemapprovalNoticeId = _this.newagreeList[index].emailapprovalNoticeId;
                } else if(!_this.isemapprovalNoticeId) {
                    _this.needObj = _this.newagreeList[index]?.emailobj || {};
                }
                _this.emIndex = index;
                _this.editEmail = true;
            }
        },
        switch_status(type,val,index) {
            let _this = this;
            if(_this.newagreeList[index].emailapprovalNoticeId || _this.newagreeList[index].smsapprovalNoticeId) {
                return new Promise(function(resolve) {
                    CRM.util.FHHApi({
                            url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_toggle_switch_status",
                            data: {
                                "approvalNoticeId": type =='sms' ? _this.newagreeList[index].smsapprovalNoticeId : _this.newagreeList[index].emailapprovalNoticeId,
                                "notifyVia": type,
                                "enabled": val,
                                "bizScope":"sign"
                            },
                            success: function(res) {
                                if (res.Result.StatusCode === 0) {
                                    resolve(res.Value);
                                    return;
                                }
                                CRM.util.alert(
                                    res.Result.FailureMessage ||
                                        $t("暂时无法获取数据") + "!"
                                );
                            }
                        },{ errorAlertModel: 1}
                    );
                });
            }
        },
        phoneDescribe() {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_phone_field_describe",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        edit(index) {
            let _this = this;
            if(!_this.viewFlag) {
                _this.issmsapprovalNoticeId = _this.newagreeList[index].smsapprovalNoticeId || '';
                if(_this.newagreeList[index].smsapprovalNoticeId) {
                    _this.issmsapprovalNoticeId = _this.newagreeList[index].smsapprovalNoticeId;
                } else if(!_this.issmsapprovalNoticeId ) {
                    _this.smsneedObj = _this.newagreeList[index]?.smsobj || {};
                }
                _this.phoneDescribe().then(res => {
                    const { fieldDescribeMap } = res;
                    let wantData = new Array();
                    let arr = Object.keys(fieldDescribeMap);
                    arr.forEach(item => {
                        wantData.push(fieldDescribeMap[item])
                    })
                    _this.editoptions = _.map(wantData, val => ({
                        label: val.label,
                        value: val.apiName
                    }));
                })
                _this.smsIndex = index;
                _this.editFlag = true;
            }
        },
        closeDialog(val) {
            if (val) {
                this.newagreeList[val.index].smsobj = val;
            }
            this.editFlag = false;
        },
        closeEmail(val) {
            if (val) {
                this.newagreeList[val.index].emailobj = val;
            }
            this.editEmail = false;
        },
        saveApl(index,type,name) {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_save_notice_apl",
                        data: {
                            "approvalNoticeId": type =='sms' ? _this.newagreeList[index].smsapprovalNoticeId : _this.newagreeList[index].emailapprovalNoticeId,
                            "notifyVia": type,
                            "aplApiName": name,
                            "bizScope":"sign"
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                               
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        async selectRule(index,type) {
            let _this = this;
            let PickselfAPL = await requirePickselfAPL();
            _this.pickselfApl = new PickselfAPL({
                postData: {
                    binding_object_api_name: this.ispartner ? 'PartnerObj' : 'AccountObj', // 绑定对象
                    name_space: ['channel_sign'], // 范围规则
                    return_type: null, // 返回值类型
                },
                checkedData: type  == 'sms' ? _this.newagreeList[index].smsAplArr.length &&  _this.newagreeList[index].smsAplVal !== '' ? _this.newagreeList[index].smsAplArr : [] : _this.newagreeList[index].emailAplArr.length && _this.newagreeList[index].emailAplVal !=='' ? _this.newagreeList[index].emailAplArr :[] 
            })
            _this.pickselfApl.on('dialogEnter', (checkedData) => {
                if(type == 'sms') {
                    _this.newagreeList[index].smsAplArr = checkedData;
                    _this.newagreeList[index].smsAplVal = checkedData[0].function_name;
                    _this.saveApl(index,type,checkedData[0].api_name)
                } else if (type == 'email') {
                    _this.newagreeList[index].emailAplArr = checkedData;
                    _this.newagreeList[index].emailAplVal = checkedData[0].function_name;
                    _this.saveApl(index,type,checkedData[0].api_name)
                }
                _this.$emit('dialogEnter')
            })
        },
        dredge() {
            window.open(`https://${window.location.host}/XV/UI/Home#/app/marketing/index/=/sms-marketing/init/welcome`, "_blank")
        },
        setDefaultValue(item, index) {
            let filter =  this.filterComps[index];
            if(item.radio == '2') {
                item.defaultValue = filter.getValue()
            } else {
                item.defaultValue = null
            }
        },
        unchangeText(val) {
            return val.replace("${var_message}",$t("crm.dlt.fix.words"))
        },
        changeText(val){
            return val.replace($t("crm.dlt.fix.words"),"${var_message}")
        },
        xyunchangeText(val) {
            return val.replace("${var_overdue_message}",$t("vcrm.dlt.fix.window.words"))
        },
        xychangeText(val){
            return val.replace($t("vcrm.dlt.fix.window.words"),"${var_overdue_message}")
        },
        saveScheme() {
            let _this = this;
            let uuid = _this.newagreeList[0]._uuid;
            let signSchemeList = new Object();
            let internalPerson = new Array();
            let externalPerson = new Array();
            if (_this.newagreeList[0].radioType !== '1') {
                let { outerRoleIds,outerTenantIds,outerTgroupIds,outerUids } = _this.icselector[uuid]?.$refs?.icselector?.getValue(); 
                if (outerRoleIds.length) {
                    outerRoleIds.forEach(item => {
                        externalPerson.push({
                            "memberType":"link_role",
                            "dataId":item
                        })
                    })
                }
                if (outerTenantIds.length) {
                    outerTenantIds.forEach(item => {
                        externalPerson.push({
                            "memberType":"link_enterprise",
                            "dataId":item
                        })
                    })
                }
                if (outerTgroupIds.length) {
                    outerTgroupIds.forEach(item => {
                        externalPerson.push({
                            "memberType":"link_group",
                            "dataId":item
                        })
                    })
                }
                if (outerUids.length) {
                    outerUids.forEach(item => {
                        externalPerson.push({
                            "memberType":"person",
                            "dataId":item
                        })
                    })
                }
                let { group,member,usergroup } = _this.inSelect[uuid]._selector.getValue();
                if(group.length) {
                    group.forEach(item => {
                        internalPerson.push({
                            "memberType":"department",
                            "dataId":item
                        })
                    })
                }
                if(member.length) {
                    member.forEach(item => {
                        internalPerson.push({
                            "memberType":"person",
                            "dataId":item
                        })
                    })
                }
                if(usergroup.length) {
                    usergroup.forEach(item => {
                        internalPerson.push({
                            "memberType":"group",
                            "dataId":item
                        })
                    })
                }
            }
            _this.newagreeList.forEach((item,index) => {
                _this.setDefaultValue(item, index);
                if(item.radioType == '2') {
                    if (item.dayCycle == '1') {
                        
                    } else {
                        // signSchemeList.signSchemeId = item.signSchemeId || "";
                        // signSchemeList.noticeIds = item.noticeIds || [];
                        signSchemeList.renewalCycleTime = item.cyclezq || null;
                        signSchemeList.renewalCycleUnit = item.cycleTime || null;
                    }
                    signSchemeList.nextRenewalDate = item.dayCycle == '1' ?  item.mouthTime + '-' + item.dayTime : "";
                    signSchemeList.renewalWindowStart = item.handle == '1' ? null : item.windowStart || '';
                    signSchemeList.renewalWindowEnd = item.window || "";
                    signSchemeList.renewalPage = _this.showCheckList;
                    signSchemeList.overdueMessage = _this.xychangeText(_this.windowuserInput) || $t("vcrm.dlt.fix.window.words")
                    signSchemeList.reminderTrigger = item.handle == '1' ? "auto" : "manual";  // 提醒类型
                    signSchemeList.expireReminderPersonView = { //提醒成员
                        "internalPerson": internalPerson, //内部成员
                        "externalPerson": externalPerson //外部成员
                    };
                    signSchemeList.expireReminderTypeView =  {                      // 到期提醒设置
                        "autoExpireReminderType": { //自动提醒
                            "prmAlertWindowReminder": {
                                "timeUnit": item?.noticeList[0].model ? item.handle == '1' ? item?.noticeList[0].nosel || null : null : null,
                                "reminderTime": item?.noticeList[0].model ? item.handle == '1' ? Number(item?.noticeList[0].num) || null : null : null,
                                "message": this.changeText(this.userInput) || $t("crm.dlt.fix.words"),
                                "activated": item.handle == '1' ? item?.noticeList[0].model : false,
                                "reminderTypeId":item?.autoprmAlertWindowReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "smsReminder": {
                                "timeUnit": item?.noticeList[1].model ? item.handle == '1' ? item?.noticeList[1].nosel || null: null : null,
                                "reminderTime": item?.noticeList[1].model ? item.handle == '1' ? Number(item?.noticeList[1].num) || null : null : null,
                                "message":item?.noticeList[1].model ? item.handle == '1' ? item?.noticeList[1].noTel : "" : "",
                                "activated": item.handle == '1' ? item?.noticeList[1].model : false,
                                "reminderTypeId":item?.autosmsReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "emailReminder": {
                                "templateId": item.handle == '1' ? item?.noticeList[2].templateId : "",
                                "timeUnit": item?.noticeList[2].model ? item.handle == '1' ? item?.noticeList[2].nosel || null : null : null,
                                "reminderTime": item?.noticeList[2].model ? item.handle == '1' ? Number(item?.noticeList[2].num) || null : null : null,
                                "message": "",
                                "activated": item.handle == '1' ? item?.noticeList[2].model : false,
                                "reminderTypeId":item?.autoemailReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "crmReminder": {
                                "timeUnit":  item?.noticeList[3].model ? item.handle == '1' ? item?.noticeList[3].nosel || null : null : null,
                                "reminderTime": item?.noticeList[3].model ? item.handle == '1' ? Number(item?.noticeList[3].num) || null : null : null,
                                "message":item?.noticeList[3].model ? item.handle == '1' ? item?.noticeList[3].noTel : "" : "",
                                "activated": item.handle == '1' ? item?.noticeList[3].model : false,
                                "reminderTypeId":item?.autocrmReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "prmCrmReminder": {
                                "timeUnit": item?.noticeList[4].model ? item.handle == '1' ? item?.noticeList[4].nosel || null : null : null,
                                "reminderTime": item?.noticeList[4].model ? item.handle == '1' ? Number(item?.noticeList[4].num) || null : null : null,
                                "message": item?.noticeList[4].model ? item.handle == '1' ? item?.noticeList[4].noTel : "" : "",
                                "activated": item.handle == '1' ? item?.noticeList[4].model : false,
                                "reminderTypeId":item?.autoprmCrmReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            }
                        },
                        "manualExpireReminderType": { //手动提醒 
                            "prmAlertWindowReminder": {
                                "message": this.changeText(this.userInputMove) || $t("crm.dlt.fix.words"),
                                "activated": item.handle == '2' ? item?.movementList[0].model : false,
                                "reminderTypeId":item?.manualprmAlertWindowReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "smsReminder": {
                                "message":item?.movementList[1].model ? item.handle == '2' ? item?.movementList[1].templ : "" : "",
                                "activated": item.handle == '2' ? item?.movementList[1].model : false,
                                "reminderTypeId":item?.manualsmsReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "emailReminder": {
                                "templateId": item.handle == '2' ? item?.movementList[2].templateId : "",
                                // "message": item.handle == '2' ? item?.movementList[2].emailtempl : "",
                                "activated": item.handle == '2' ? item?.movementList[2].model : false,
                                "reminderTypeId":item?.manualemailReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "crmReminder": {
                                "message":item?.movementList[3].model ? item.handle == '2' ? item?.movementList[3].templ : "" : "",
                                "activated": item.handle == '2' ? item?.movementList[3].model : false,
                                "reminderTypeId": item?.manualcrmReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            },
                            "prmCrmReminder": {
                                "message":item?.movementList[4].model ? item.handle == '2' ? item?.movementList[4].templ : "" : "",
                                "activated": item.handle == '2' ? item?.movementList[4].model : false,
                                "reminderTypeId":item?.manualprmCrmReminderId || "",
                                "signSchemeId":item?.signSchemeId || "",
                            }
                        }
                    }   
                }
                if (item.radioType == '1' && _this.dialog == 'edit') {
                    signSchemeList.renewalCycleTime =  null;
                    signSchemeList.renewalCycleUnit =  null;
                    signSchemeList.nextRenewalDate =  null;
                    signSchemeList.renewalWindowStart =  null;
                    signSchemeList.renewalWindowEnd =  null;
                    signSchemeList.renewalPage =  null;
                    signSchemeList.renewalWindowUnit = null;
                    signSchemeList.overdueMessage = null;
                    signSchemeList.reminderTrigger =  'auto';
                    signSchemeList.expireReminderPersonView =  {
                        "internalPerson": [], //内部成员
                        "externalPerson": [] //外部成员
                    };
                    signSchemeList.expireReminderTypeView =  {                      // 到期提醒设置
                        "autoExpireReminderType": { //自动提醒
                            "prmAlertWindowReminder": {
                                "timeUnit":  null,
                                "reminderTime": null,
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            },
                            "smsReminder": {
                                "timeUnit": null,
                                "reminderTime": null,
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            },
                            "emailReminder": {
                                "templateId": null,
                                "timeUnit": null,
                                "reminderTime": null,
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            },
                            "crmReminder": {
                                "timeUnit":  null,
                                "reminderTime": null,
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            },
                            "prmCrmReminder": {
                                "timeUnit": null,
                                "reminderTime":  null,
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            }
                        },
                        "manualExpireReminderType": { //手动提醒 
                            "prmAlertWindowReminder": {
                                "message": null,
                                "activated": null,
                                "reminderTypeId":null,
                                "signSchemeId": null,
                            },
                            "smsReminder": {
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            },
                            "emailReminder": {
                                "templateId": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            },
                            "crmReminder": {
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            },
                            "prmCrmReminder": {
                                "message": null,
                                "activated": null,
                                "reminderTypeId": null,
                                "signSchemeId": null,
                            }
                        }
                    } 
                }
                signSchemeList.schemeName = item.name || "";
                signSchemeList.agreementId = item.agreementName || "";
                signSchemeList.conditionType = item.radio == '1' ? "ALL" : "CONDITION";
                signSchemeList.condition = item.radio == '2' ? JSON.stringify({
                                                    "type": "CONDITION",
                                                    "value":item.defaultValue
                                                })  : "{\"type\":\"ALL\"}";
                signSchemeList.signMode = item.radio1 == '1' ? "manual" : "apl";
                signSchemeList.aplApiName = item.radio1 == '2' &&  item.apl !== '' ? item.aplName : '';
                signSchemeList.activateRoles = item.roles;
                //approvalNoticeList 还是 approvalNotices
                signSchemeList.approvalNotices = [
                        {
                            approvalNoticeId:_this.smsapprovalNoticeId || '',
                            notifyVia:'sms',
                            aplApiName:item?.smsAplArr[0]?.api_name || '',
                            bizScope:'sign',
                            receiver:item.smsobj ? item.smsobj.value :[], 
                            enabled: item.smsValue,
                            passSms: { 
                                "smsId": "",
                                "templateId": item.smsobj ? item.smsobj.passtemplateId : '',
                                "smsType": "template",
                                "content": item.smsobj ? item.smsobj.passcontent : '',
                                "category": "pass",
                                "smsContentParam": item.smsobj ? item.smsobj.passsmsContentParam : []
                            },
                            nonPassSms: { 
                                "smsId": "",
                                "templateId": item.smsobj ? item.smsobj.notemplateId : '',
                                "smsType": "template",
                                "content": item.smsobj ? item.smsobj.nocontent : '',
                                "category": "non_pass",
                                "smsContentParam": item.smsobj ? item.smsobj.nosmsContentParam : []
                            }
                        },
                        {
                            approvalNoticeId: _this.emailapprovalNoticeId || '',
                            notifyVia: "email",
                            aplApiName: item?.emailAplArr[0]?.api_name || '',
                            bizScope: "sign",
                            receiver: item.emailobj ? [item.emailobj.email] :[],
                            sender:item.emailobj?.value || '',
                            enabled: item.emailValue,
                            passEmail: {
                                "templateId":item.emailobj ?  item.emailobj?.patemId : '' ,
                                "emailType": "template",
                                "objectApiName": this.ispartner ? 'PartnerObj' : 'AccountObj',
                                "category": "pass"
                            },
                            "nonPassEmail": {
                                "templateId": item.emailobj? item.emailobj?.notemId : '',
                                "emailType": "template",
                                "objectApiName": this.ispartner ? 'PartnerObj' : 'AccountObj',
                                "category": "non_pass"
                            }
                        }
                ];
                signSchemeList.priority = item.newFirst;  // ???优先级怎么排
                signSchemeList.scheduleType = item.radioType == '1' ? 'one_time' : item.dayCycle == '1' ? 'fixed_date' : 'cycle';
                signSchemeList.planDuration = item.oncezq;// 协议期限时间
                signSchemeList.planDurationUnit =  item.onceTime;// 协议期限时间
                signSchemeList.startDate = item.oncemouth + '-' +  item.oncedayTime || '';// 协议开始时间
                if(this.dialog == 'edit') {
                    signSchemeList.signSchemeId = item.signSchemeId || "";
                    signSchemeList.noticeIds = item.noticeIds || [];
                    signSchemeList.varOverdueMessage = item.varOverdueMessage || '';
                    signSchemeList.pushed = false;
                    signSchemeList.signModelEnum = 'MANUAL';
                }
            })
            console.log(signSchemeList,'-0000-')
            if(_this.newagreeList[0].radioType == '2') {
                let autoType = signSchemeList.expireReminderTypeView.autoExpireReminderType;
                let manualType = signSchemeList.expireReminderTypeView.manualExpireReminderType;
                if (!autoType.crmReminder.activated && !autoType.emailReminder.activated && !autoType.prmAlertWindowReminder.activated && !autoType.prmCrmReminder.activated && !autoType.smsReminder.activated && !manualType.crmReminder.activated && !manualType.emailReminder.activated && !manualType.prmAlertWindowReminder.activated && !manualType.prmCrmReminder.activated && !manualType.smsReminder.activated) {
                    CRM.util.alert($t("crm.plan.manualType"));
                    return
                }
            }
            if (((signSchemeList.renewalCycleTime == '' || null) || (signSchemeList.renewalCycleUnit == '' || null )) && signSchemeList.scheduleType == 'cycle') {
                CRM.util.alert($t("crm.xycycle.notkong"));
                return
            }
            if (signSchemeList.renewalWindowEnd == "") {
                CRM.util.alert($t("crm.xyoverend.notkong"));
                return
            }
            if (signSchemeList.renewalWindowStart == '' && signSchemeList.reminderTrigger == 'manual') {
                CRM.util.alert($t("crm.xyoverstart.notkong"));
                return
            }
            if (signSchemeList.schemeName == '') {
                CRM.util.alert($t("crm.plan.notkong"));
                return
            }
            if (signSchemeList.planDuration == '' || signSchemeList.planDurationUnit == '') {
                CRM.util.alert($t("vcrm.xyqx.notkong"));
                return
            }
            if (signSchemeList.startDate == '') {
                CRM.util.alert($t("vcrm.xystart.notkong"));
                return
            }
            if (signSchemeList.nextRenewalDate == '' && signSchemeList.scheduleType == 'fixed_date') {
                CRM.util.alert($t("vcrm.xuyestart.notkong"));
                return
            }
            if (signSchemeList.signMode == 'apl' && signSchemeList.aplApiName == '') {
                CRM.util.alert($t("crm.apl.notkong"));
                return
            }
            if (!signSchemeList.activateRoles.length) {
                CRM.util.alert($t("crm.roles.notkong"));
                return
            }
            let url;
            if (this.dialog == 'add' || this.dialog == 'copy') {
                url = '/EM1HNCRM/API/v1/object/partner_management/service/channel_add_sign_scheme'
            } else if (this.dialog == 'edit') {
                url = '/EM1HNCRM/API/v1/object/partner_management/service/channel_edit_sign_scheme'
            }
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url: url,
                        data: {
                            "bizScope": "sign",
                            "signScheme":signSchemeList
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                CRM.util.remind(1, $t("保存成功"));
                                _this.filterComps = [];
                                _this.inSelect[uuid]._selector.destroy();
                                _this.inSelect[uuid] = null;
                                _this.$refs['filter-ref'] = null;
                                _this.icselector[uuid].$refs.icselector.setValue({
                                    outerRoleIds: [],
                                    outerTenantIds: [],
                                    outerTgroupIds: [],
                                    outerUids: []
                                })
                                _this.icselector[uuid] = null;
                                _this.newagreeList = [];
                                _this.handleClick({index:1})
                                _this.dialogVisible = false;
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        flowExamine() {
            window.open(`https://${window.location.host}/XV/UI/manage#crmmanage/=/module-approval`, "_blank")
        },
        async renderTable() {
            let _this = this;
            _this.$$table && _this.$$table.destroy();
            let List = await requireCRMList();
            _this.$$table = new List({
                wrapper: $('.partner', this.$el)[0],
                apiname: 'PartnerAgreementObj',
            });
            _this.$$table.render();
        },
        changeRanNew(val) {
            if (val == '2') {
                $(".apl-condition").show();
                $(".apl-condition").css({
                    "padding-top": "16px",
                    "padding-left": "16px"
                });
            } else {
                $(".apl-condition").hide();
            }
        },
        async changeRan(i,val) {
            if (val == '2') {
                $(".condition" + i).show();
                $(".apl-condition").css("padding-top","16px")
            } else {
                $(".condition" + i).hide();
            }
        },
        getListsData() {
            return new Promise((resolve, reject) => {
                    CRM.util.FHHApi({
                        url:
                            "/EM1HNCRM/API/v1/object/PartnerAgreementObj/controller/List",
                        data: {
                            object_describe_api_name:"PartnerAgreementObj",
                            search_query_info:JSON.stringify({"limit":20,"offset":0,"filters":[{"field_name":"biz_object_api_name","field_values":[this.curApiName],"operator":"EQ"}],"orders":[{"fieldName":"last_modified_time","isAsc":false}]})
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value?.dataList);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        getAplList() {
            return new Promise((resolve, reject) => {
				CRM.util.FHHApi({
                    url: '/EM1HFUNC/biz/query',
                    data: {
                        pageNumber: 1,
                        pageSize: 500,
                        is_include_used: true,
                        binding_object_api_name: this.ispartner ? 'PartnerObj' : 'AccountObj', // 绑定对象
                        name_space: ['channel_sign'], // 范围规则
                        return_type: null, // 返回值类型
                    },
                    success: (res) => {
                        if (res.Result.StatusCode == 0 && res.Value) {
                            resolve(res.Value?.function || [])
                        } else {
                            CRM.util.alert(res?.Value?.msg || res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
			});
        },
        getEmailList() {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:
                            "/EM1HCRMTemplate/emailTemplateAdminApi/page",
                        data: {
                            objDescApiName: _this.apiname,
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
    },
   async mounted() {
       let _this =this;
       let result = await api.fetchChannelConfig();
       if(result.applyModules.indexOf("sign") > -1) {
            this.other = false;
            $('.nav-header').show();
            $(".channel-access").css("padding", "0 16px");
            this.isDl = result.channelMode == "channel_agent" ? true : false;
            this.isApp = result.applyToApp == 'prm' ? 'prm' : result.applyToApp == 'dht' ? 'system' : 'fwt';
            this.ispartner = result.relatedObjectApiName == "PartnerObj" ? true : false;
            this.apiname = result.relatedObjectApiName;
            this.filterGroup = await crmRequire('crm-modules/common/filtergroup/filtergroup');
            _this.renderTable();
        } else {
            this.other = true;
            $('.nav-header').hide();
            $(".channel-access").css("padding", "0");
        }
    }
};
</script>
<style lang="less">
    .scheme-content {
        .fx-dialog {
            background: var(--color-special03, #f1f3f8);
            display: flex;
            flex-direction: column;
            height: 100%;
            .el-dialog__body {
                overflow: hidden;
                padding: 0;
                margin: 0 12px 12px 12px;
                border-radius: 8px;
                background: var(--color-neutrals01);
            }
        }
        .el-dialog__header {
            padding: 0 !important;
            border-bottom: none !important;
            .dialog-title {
                display: flex;
                justify-content: space-between;
                padding: 10px 0;
                .scheme-title-text {
                    font-size: 18px;
                    line-height: 32px;
                    color: var(--color-neutrals19);
                    // border-left: 4px solid var(--color-primary06);
                    padding-left: 16px;
                }
                .pickdata-btns {
                    padding-right: 16px;
                }
            }
        }
        .scheme-content-nav {
            &.nav-collapsed {
                .scheme-right {
                    margin-left: 0;
                }
                
                .scheme-left-close {
                    left: 0;
                    .fx-icon-arrow-right {
                        &::before {
                            color: var(--color-neutrals01);
                        }
                    }
                }
            }
            display: flex;
            height: calc(100vh - 64px);
            .scheme-left-close {
                position: absolute;
                left: 163px; // 默认位置在左侧导航右边
                top: 84px;
                transform: translateY(-50%);
                width: 12px;
                height: 32px;
                background: var(--color-neutrals07);
                border: 1px solid var(--color-neutrals04);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 10;
                transition: all 0.3s ease;
                &.is-collapsed {
                    left: 0; // 收起时靠左
                }
                .fx-icon-arrow-left {
                    &::before {
                        color: var(--color-neutrals01);
                    }
                }
            }
            .scheme-left {
                position: relative;
                flex-shrink: 0;
                width: 152px;
                padding-top: 16px;
                z-index: 1;
                font-size: 14px;
                line-height: 16px;
                color: var(--color-neutrals15);
                .scheme-left-inner {
                    position: relative;
                    border-right: 1px solid var(--color-neutrals05);
                    .scheme-left-wrapper {
                        height: 100%;
                        overflow: auto;
                        .nav-item {
                            border-radius: 3px 0 0 3px;
                            padding: 8px 24px;
                            text-align: right;
                            border-right: 1px solid transparent;
                            cursor: pointer;
                        }
                        .cur {
                            border-right: 2px solid var(--color-primary06);
                            color: var(--color-primary06);
                        }
                    }
                }
            }
            .scheme-right {
                flex: 1;
                overflow-y: auto;
                padding: 22px 16px 16px 16px;
                .scheme-right-sian {
                    h1 {
                        height: 16px;
                        line-height: 16px;
                        font-size: 16px;
                        padding-left: 8px;
                        color: var(--color-neutrals19);
                    }
                    .padding {
                        padding-left: 12px;
                    }
                    .apl-condition {
                        display: none;
                    }
                    .syrange {
                        color: var(--color-neutrals15);
                        display: inline-block;
                        word-wrap: break-word;
                        width: 133px;
                        &::before{
                            content: "*";
                            color: #ff5730;
                            vertical-align: middle;
                            font-size: 14px;
                        }
                    }
                    .el-radio {
                        .el-radio__label {
                            padding-left: 0px;
                            font-size: 14px!important;
                        }
                    }
                    .first {
                        display: flex;
                        margin-top: 12px;
                        .tpl-first {
                            margin-bottom: 16px;
                            .rlname {
                                color: var(--color-neutrals15);
                                display: inline-block;
                                width: 76px;
                                word-wrap: break-word;
                                &::before{
                                    content: "*";
                                    color: #ff5730;
                                    vertical-align: middle;
                                    font-size: 14px;
                                }
                            }
                            .el-select {
                                width: 270px;
                                padding-left: 38px;
                                .el-input__inner {
                                    height: 32px;
                                    line-height: 32px;
                                }
                            }
                        }
                       .range-tpl {
                            height: 32px;
                            line-height: 32px;
                            margin-left: 128px;
                       }
                    }
                    .start-ending {
                        display: flex;
                        margin-top: 12px;
                        .end {
                            margin-left: 12px;
                            .input-zq {
                                width: 130px;
                                margin-right: 8px;
                            }
                            .select-zq {
                                width: 130px;
                                .el-input__inner {
                                    height: 32px;
                                    line-height: 32px;
                                }
                            }
                            .rlname {
                                color: var(--color-neutrals15);
                                display: inline-block;
                                width: 132px;
                                word-wrap: break-word;
                                &::before{
                                    content: "*";
                                    color: #ff5730;
                                    vertical-align: middle;
                                    font-size: 14px;
                                }
                            }
                        }
                        .start {
                            padding-left: 126px;
                            .select-mou {
                                width: 118px;
                                .el-input__inner {
                                    height: 32px;
                                    line-height: 32px;
                                }
                            }
                            .rlname {
                                color: var(--color-neutrals15);
                                display: inline-block;
                                width: 132px;
                                word-wrap: break-word;
                                &::before{
                                    content: "*";
                                    color: #ff5730;
                                    vertical-align: middle;
                                    font-size: 14px;
                                }
                            }
                            .rlname {
                                color: var(--color-neutrals15);
                                &::before{
                                    content: "*";
                                    color: #ff5730;
                                    vertical-align: middle;
                                    font-size: 14px;
                                }
                            }
                        }
                    }
                    .day-cycle {
                        .span-cycle {
                            font-size: 14px;
                            padding-right: 72px;
                        }
                        padding-left: 16px;
                        margin-top: 16px;
                    }
                    .manner-type {
                        display: flex;
                        .scheme-manner {
                            padding-left: 134px;
                            .el-radio {
                                .el-radio__label {
                                    padding-left: 0px;
                                }
                            }
                            .dzq {
                               display: inline-block;
                               width: 128px;
                            }
                            
                        }
                        .scheme-type {
                            padding-left: 16px;
                            display: flex;
                            .radio-type {
                                width: 270px;
                            }
                            .span-type {
                                display: inline-block;
                                width: 132px;
                                word-wrap: break-word;
                            }
                        }
                    }
                    .appl {
                        margin-left: 12px;
                        margin-top: 12px;
                        .el-input {
                            width: 270px;
                            padding-left: 102px;
                            .el-input__inner {
                                height: 28px;
                                line-height: 28px;
                            }
                        }
                        .appl-choose {
                            color: var(--color-neutrals15);
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        }

                    }
                    .name-a {
                        margin-top: 8px;
                        margin-bottom: 16px;
                        display: flex;
                        .scheme-content-new {
                            padding-left: 128px;
                            .el-select {
                                width: 270px;
                                .el-input__inner {
                                    height: 32px;
                                    line-height: 32px;
                                }
                            }
                        }
                        .el-input {
                            width: 270px;
                            .el-input--small {
                                height: 28px;
                                line-height: 28px;
                            }
                        }
                        .rlname {
                            display: inline-block;
                            width: 132px;
                            word-wrap: break-word;
                            color: var(--color-neutrals15);
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        }
                    }
                }
                .scheme-xy-rule {
                    margin-top: 40px;
                    
                    h1 {
                        height: 16px;
                        line-height: 16px;
                        font-size: 16px;
                        padding-left: 8px;
                        color: var(--color-neutrals19);
                    }
                   
                    .notice {
                        background: var(--color-neutrals02);
                        margin-left: 30px;
                        border-bottom-left-radius: 8px;
                        border-bottom-right-radius: 8px;
                        padding-bottom: 12px;
                        .people {
                            display: flex;
                            margin-top: 12px;
                            .people-span {
                                padding-left: 16px;
                                margin-top: 6px;
                                width: 120px;
                            }
                            .right {
                                .external {
                                    margin-top: 8px;
                                    .icsel-vcnts {
                                        width: 308px;
                                    }
                                }
                                .internal,.external {
                                    display: flex;
                                    .fx-selector-v2 {
                                        margin-left: 8px;
                                        .selected-icon {
                                            margin-top: 0px;
                                        }
                                    }
                                }
                                .internal-span,.external-span {
                                    margin-top: 6px;
                                }
                            }
                        }
                        .el-select,.el-input {
                            width: 140px;
                            .el-input__inner {
                                height: 32px;
                                line-height: 32px;
                            }
                        }
                        .show-content {
                            display: flex;
                            padding-left: 12px;
                            margin-top: 12px;
                            .show-left {
                                width: 124px;
                                .show-left-text {
                                    color: var(--color-neutrals15);
                                    &::before{
                                        content: "*";
                                        color: #ff5730;
                                        vertical-align: middle;
                                        font-size: 14px;
                                    }
                                }
                            }
                            .show-right {
                                .el-checkbox {
                                    margin-right: 16px !important;
                                }
                            }
                        }
                        .xy-window {
                            display: flex;
                            padding-left: 12px;
                            .window-left {
                                width: 128px;
                                margin-top: 7px;
                                .window-span {
                                    color: var(--color-neutrals15);
                                    &::before{
                                        content: "*";
                                        color: #ff5730;
                                        vertical-align: middle;
                                        font-size: 14px;
                                    }
                                }
                            }
                            .window-right {
                                margin-left: 8px;
                                width: 100%;
                                .window-select-start {
                                    margin-bottom: 8px;
                                }
                                .right-after,.right-start {
                                    margin-left: 8px;
                                    margin-right: 2px;
                                }
                                .right-span,.right-notice {
                                    display: inline-block;
                                    width: 128px;
                                    word-wrap: break-word;
                                }
                                .window-day {
                                    margin-left: 2px;
                                }
                                .window-textarea {
                                    margin-top: 8px;
                                    display: flex;
                                    width: 100%;
                                    .window-set {
                                        width: 128px;
                                        display: inline-block;
                                        word-wrap: break-word;
                                    }
                                    .window-textarea-container {
                                        width: 100%;
                                        position: relative;
                                        margin-left: 30px;
                                        margin-right: 12px;
                                        .custom-textarea {
                                            width: 98%;
                                            min-height: 60px;
                                            padding: 8px;
                                            font-family: monospace;
                                            resize: vertical;
                                            border: 1px solid #dcdfe6;
                                            border-radius: 4px;
                                        }
                                        .word-count {
                                            position: absolute;
                                            bottom: 5px;
                                            right: 10px;
                                            font-size: 12px;
                                            color: #909399;
                                        }
                                    }
                                }
                                .el-select {
                                    width: 140px;
                                    .el-input__inner {
                                        height: 32px;
                                        line-height: 32px;
                                    }
                                }
                            }
                        }
                        .el-radio {
                            .el-radio__label {
                                padding-left: 0px;
                                font-size: 14px!important;
                            }
                        }
                        .notice-set {
                            padding-left: 12px;
                            padding-top: 4px;
                            width: 128px;
                            display: inline-block;
                            word-wrap: break-word;
                            margin-right: 7px;
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        } 
                        .notice-movement {
                            display: flex;
                            margin: 18px 0 0 12px;
                            border-radius: 4px;
                            .automatic-type {
                                display: inline-block;
                                width: 128px;
                                color: var(--color-neutrals15);
                                &::before{
                                content: "*";
                                    color: #ff5730;
                                    vertical-align: middle;
                                    font-size: 14px;
                                }
                            }
                            .notice-right {
                                width: 100%;
                                margin-left: 25px;
                                .height3 {
                                    height: 32px;
                                }
                                .automatic-input {
                                        margin-right: 6px;
                                        margin-top: 8px;
                                    }
                                    .automatic-tx {
                                        margin-left: 6px;
                                        color: var(--color-neutrals19);
                                    }
                                    .el-checkbox__inner {
                                        width: 14px !important;
                                        height: 14px !important;
                                        &::after {
                                            top: 1px !important;
                                            left: 4px !important;
                                        }
                                    }
                                    .el-checkbox__label {
                                        padding-left: 0px !important;
                                        font-size: 14px !important;
                                    }
                                .notice-automatic  {
                                    .el-checkbox {
                                        margin-right: 2px !important;
                                    }
                                    .automatic-text {
                                        display: flex;
                                        width: 100%;
                                        .custom-textarea-container {
                                            width: 100%;
                                            position: relative;
                                            margin-left: 48px;
                                            margin-right: 12px;
                                            .custom-textarea {
                                                width: 98%;
                                                min-height: 60px;
                                                padding: 8px;
                                                font-family: monospace;
                                                resize: vertical;
                                                border: 1px solid #dcdfe6;
                                                border-radius: 4px;
                                            }
                                            .word-count {
                                                position: absolute;
                                                bottom: 5px;
                                                right: 10px;
                                                font-size: 12px;
                                                color: #909399;
                                            }
                                        }
                                        .button,.danger {
                                            display: inline-block;
                                            margin-top: 14px;
                                            margin-left: -12px;
                                        }
                                        .button {
                                            cursor: pointer;
                                            margin-left: 40px;
                                            color:  var(--color-info06);
                                        }
                                        .danger {
                                            margin-left: 40px;
                                            color: var(--color-danger06);
                                        }
                                        .change,.pirew {
                                            margin-left: 8px;
                                            cursor: pointer;
                                            color: var(--color-info06);
                                        }
                                        .pirew,.change,.xq {
                                            display: inline-block;
                                            margin-top: 14px; 
                                        }
                                        .change-push,.pirew-push {
                                            cursor: no-drop !important;
                                            pointer-events: none;
                                            color: var(--color-info03);
                                        }
                                        .xq {
                                            margin-left: 40px;
                                        }
                                        .automatic-wa {
                                            margin-left: 18px;
                                            display: inline-block;
                                            width: 72px;
                                            margin-right: 20px;
                                            margin-top: 14px;
                                            color: var(--color-neutrals15);
                                        }
                                        .el-textarea {
                                            margin-top: 8px;
                                            margin-left: 48px;
                                            margin-right: 12px;
                                            padding-bottom: 0px !important;
                                        }
                                    }
                                }
                            }
                        }
                        .notice-content {
                            display: flex;
                            margin: 18px 0 0 12px;
                            border-radius: 4px;
                            .automatic-type {
                                display: inline-block;
                                width: 72px;
                                margin-right: 8px;
                                color: var(--color-neutrals15);
                                &::before{
                                content: "*";
                                    color: #ff5730;
                                    vertical-align: middle;
                                    font-size: 14px;
                                }
                            }

                            .notice-right {
                                width: 100%;
                                margin-left: 68px;
                                .height3 {
                                    height: 32px;
                                }
                                .notice-automatic {
                                    // margin-bottom: 16px;
                                    
                                    .el-tooltip {
                                        font-size: 14px;
                                        cursor: pointer;
                                    }
                                    .el-checkbox {
                                        margin-right: 2px !important;
                                    }
                                    .automatic-time {
                                        margin-left: 18px;
                                        display: inline-block;
                                        width: 72px;
                                        margin-right: 8px;
                                        color: var(--color-neutrals15);
                                    }
                                    
                                    .automatic-before {
                                        margin-right: 6px;
                                        margin-left: 48px;
                                        color: var(--color-neutrals19);
                                    }
                                    .automatic-text {
                                        display: flex;
                                        width: 100%;
                                        margin-top: 8px;
                                        .custom-textarea-container {
                                            width: 100%;
                                            position: relative;
                                            margin-left: 48px;
                                            margin-right: 12px;
                                            .custom-textarea {
                                                width: 98%;
                                                min-height: 60px;
                                                padding: 8px;
                                                font-family: monospace;
                                                resize: vertical;
                                                border: 1px solid #dcdfe6;
                                                border-radius: 4px;
                                            }
                                            .word-count {
                                                position: absolute;
                                                bottom: 5px;
                                                right: 10px;
                                                font-size: 12px;
                                                color: #909399;
                                            }
                                        }
                                        .button,.danger {
                                            display: inline-block;
                                            margin-top: 14px;
                                            margin-left: -12px;
                                        }
                                        .button {
                                            cursor: pointer;
                                            color:  var(--color-info06);
                                            margin-left: 40px;
                                        }
                                        .danger {
                                            color: var(--color-danger06);
                                            margin-left: 40px;
                                        }
                                        .change,.pirew {
                                            margin-left: 8px;
                                            cursor: pointer;
                                            color: var(--color-info06);
                                        }
                                        .pirew,.change,.xq {
                                            display: inline-block;
                                            margin-top: 14px; 
                                        }
                                        .change-push,.pirew-push {
                                            cursor: no-drop !important;
                                            pointer-events: none;
                                            color: var(--color-info03);
                                        }
                                        .xq {
                                            margin-left: 40px;
                                        }
                                        .automatic-wa {
                                            margin-left: 18px;
                                            display: inline-block;
                                            width: 72px;
                                            margin-right: 20px;
                                            margin-top: 14px;
                                            color: var(--color-neutrals15);
                                        }
                                        .el-textarea {
                                            margin-top: 8px;
                                            margin-left: 48px;
                                            margin-right: 12px;
                                            padding-bottom: 0px !important;
                                        }
                                    }
                                    .automatic-input {
                                        margin-right: 6px;
                                        margin-top: 8px;
                                    }
                                    .automatic-tx {
                                        margin-left: 6px;
                                        color: var(--color-neutrals19);
                                    }
                                    .el-checkbox__inner {
                                        width: 14px !important;
                                        height: 14px !important;
                                        &::after {
                                            top: 1px !important;
                                            left: 4px !important;
                                        }
                                    }
                                    .el-checkbox__label {
                                        padding-left: 0px !important;
                                        font-size: 14px !important;
                                    }
                                }
                            }
                        }
                    }
                    .cycle-period,.cycle-fixed {
                        margin-top: 12px;
                    }
                    .cycle-fixed {
                        background: var(--color-neutrals02);
                        margin-left: 30px;
                        padding: 12px;
                        border-top-left-radius: 8px;
                        border-top-right-radius: 8px;
                        color: var(--color-neutrals15);
                        .fixed-startday {
                            width: 128px;
                            display: inline-block;
                            word-wrap: break-word;
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        }
                        .fixed-evert {
                            margin: 0 6px;
                        }
                        .fixed-mouth {
                            margin-right: 6px;
                        }
                        .select-mou {
                            margin-right: 6px;
                            .el-input  {
                                width: 140px;
                            }
                            .el-input__inner {
                                height: 32px;
                                line-height: 32px;
                            }
                        }
                    }
                    .cycle-period {
                        background: var(--color-neutrals02);
                        margin-left: 30px;
                        border-bottom-left-radius: 8px;
                        border-bottom-right-radius: 8px;
                        padding: 12px;
                        .period-cycle {
                            .cycle-period-d {
                                width: 128px;
                                display: inline-block;
                                word-wrap: break-word;
                                &::before{
                                    content: "*";
                                    color: #ff5730;
                                    vertical-align: middle;
                                    font-size: 14px;
                                }
                            }
                            .input-zq {
                                width: 140px;
                                margin-right: 4px;
                            }
                            .el-select {
                                width: 140px;
                                .el-input__inner {
                                    height: 32px;
                                    line-height: 32px;
                                }
                            }
                            .cycle-period-an {
                                margin-right: 6px;
                                margin-left: 8px;
                            }
                        }
                    }
                    .cycle-span {
                        margin-right: 80px;
                    }
                    .cycle-setpush {
                        cursor: no-drop;
                        pointer-events: none;
                        color: var(--color-info03);
                    }
                    .select-zq {
                        margin-right: 6px;
                    }
                    .cycle-sign {
                        margin-right: 12px;
                    }
                }
                .scheme-right-rule {
                    margin-top: 28px;
                    .role {
                        .agree-role {
                            width: 128px;
                            display: inline-block;
                            word-wrap: break-word;
                            color: var(--color-neutrals15);
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        }
                        .el-select {
                            width: 270px;
                            .el-input__inner {
                                height: 32px;
                                line-height: 32px;
                            }
                        }
                    }
                    .audit-result {
                        display: flex;
                        .audit-h {
                            margin-top: 16px;
                            display: inline-block;
                            word-wrap: break-word;
                            width: 132px;
                        }
                        .result {
                            margin-top: -4px;
                            margin-bottom: 8px;
                            display: flex;
                            .sms,.email {
                                border: 1px solid var(--color-neutrals05);
                                margin-top: 12px;
                                padding: 12px;
                                border-radius: 6px;
                                .one {
                                    .el-tag {
                                        display: inline-block;
                                        height: 21px;
                                        line-height: 22px;
                                        margin-right: 2px;
                                    }
                                    .one-sms {
                                        font-size: 15px;
                                        font-weight: 700;
                                        display: inline-block;
                                        height: 24px;
                                        line-height: 24px;
                                    }
                                    .el-switch {
                                        float: right;
                                    }
                                }
                                .two {
                                    margin-top: 8px;
                                    font-size: 13px;
                                    color: var(--color-neutrals15);
                                }
                                .three {
                                    margin-top: 12px;
                                    font-size: 13px;
                                    color: var(--color-info06);
                                    .el-input {
                                        width: 170px;
                                        margin-left: 20px;
                                        .el-input__inner {
                                            height: 28px;
                                            line-height: 28px;
                                        }
                                    }
                                    .three-smspush,.three-aplpush {
                                        cursor: no-drop !important; 
                                        pointer-events: none;
                                        color: var(--color-info03);
                                    }
                                    .three-sms {
                                        display: inline-block;
                                        padding-right: 6px;
                                        cursor: pointer;
                                    }
                                    .three-apl {
                                        display: inline-block;
                                        cursor: pointer;
                                        padding-left: 8px;
                                        border-left: 1px solid rgba(193, 197, 206, .3);
                                    }
                                }
                            }
                            .sms {
                                margin-right: 24px;
                            }
                        }
                    }
                    .policy {
                        margin-top: 12px;
                        .el-select {
                            .el-input__inner {
                                height: 32px;
                                line-height: 32px;
                            }
                        }
                    }
                    .padding {
                        padding-left: 18px;
                    }
                    h1 {
                        height: 16px;
                        line-height: 16px;
                        font-size: 16px;
                        padding-left: 8px;
                        color: var(--color-neutrals19);
                    }
                }
            }
        }
    }
    .other {
        .other-title {
            padding: 10px 0;
            border-bottom: 1px solid var(--color-neutrals05);
            h1 {
                padding-left: 16px;
                font-size: 14px;
                line-height: 28px;
                color: var(--color-neutrals19);
                font-weight: 700;
            }
        }
        .other-content {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                margin-block: 20%;
                img {
                    width: 375px;
                    height: 120px;
                }
                p {
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--color-neutrals15);
                    margin-top: 24px;
                    text-align: center;
                }
                span {
                    display: block;
                    text-align: center;
                    margin-top: 8px;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--color-info06);
                    cursor: pointer;
                }
            }
    }

    //old
    .agree-ment {
       height: 80%;
       .el-tabs {
            height: 100%;
            .el-tabs__content {
                overflow: visible !important;
                .partner {
                    margin-top: -15px;
                    overflow-y: scroll;
                    height: calc(100vh - 200px);
                }
                .save {
                    // padding: 8px;
                    padding-left: 16px;
                    background: var( --color-neutrals01);
                    position: sticky;
                    height: 24px;
                    line-height: 24px;
                }
                .content {
                    // padding: 1px 16px 0 16px;
                    height: calc(100vh - 220px);
                    overflow-y: scroll;
                    .loop-agree {
                        display: flex;
                        .delete {
                            line-height: 580px;
                            .fx-icon-process-delete {
                                font-size: 16px;
                                cursor: pointer;
                            }
                        }
                        .loop {
                            .padding{
                                padding: 0 16px;
                            }
                            .content-save {
                                border-top: 1px solid var(--color-neutrals05);
                                padding-top: 16px;
                                margin-top: 16px;
                                padding-left: 16px;
                                .button-error {
                                    color: var(--color-neutrals07);
                                    background-color: var(--color-special01);
                                    border-color: var(--color-neutrals07);
                                    // cursor: no-drop;
                                }
                                .need {
                                    margin-left: 6px;
                                }
                                .go {
                                    color: var(--color-info06);
                                    cursor: pointer;
                                }
                            }
                            .role {
                                margin-top: 8px;
                                .agree-role {
                                    color: var(--color-neutrals15);
                                    padding-right: 72px;
                                    &::before{
                                        content: "*";
                                        color: #ff5730;
                                        vertical-align: middle;
                                        font-size: 14px;
                                    }
                                }
                                .el-select {
                                    width: 270px;
                                    .el-input__inner {
                                        height: 32px;
                                        line-height: 32px;
                                    }
                                }
                            }
                            .result {
                                margin-top: -4px;
                                margin-bottom: 20px;
                                display: flex;
                                .sms,.email {
                                    width: 377px;
                                    border: 1px solid var(--color-neutrals05);
                                    margin-top: 16px;
                                    padding: 16px;
                                    border-radius: 6px;
                                    .one {
                                        .el-tag {
                                            display: inline-block;
                                            height: 21px;
                                            line-height: 22px;
                                            margin-right: 2px;
                                        }
                                        .one-sms {
                                            font-size: 16px;
                                            font-weight: 700;
                                            display: inline-block;
                                            height: 24px;
                                            line-height: 24px;
                                        }
                                        .el-switch {
                                            float: right;
                                        }
                                    }
                                    .two {
                                        margin-top: 8px;
                                        font-size: 12px;
                                        color: var(--color-neutrals15);
                                    }
                                    .three {
                                        margin-top: 32px;
                                        font-size: 12px;
                                        color: var(--color-info06);
                                        .el-input {
                                            width: 170px;
                                            margin-left: 20px;
                                            .el-input__inner {
                                                height: 28px;
                                                line-height: 28px;
                                            }
                                        }
                                        .three-smspush,.three-aplpush {
                                           cursor: no-drop !important; 
                                           pointer-events: none;
                                           color: var(--color-info03);
                                        }
                                        .three-sms {
                                            display: inline-block;
                                            padding-right: 6px;
                                            cursor: pointer;
                                        }
                                        .three-apl {
                                            display: inline-block;
                                            cursor: pointer;
                                            padding-left: 8px;
                                            border-left: 1px solid rgba(193, 197, 206, .3);
                                        }
                                    }
                                }
                                .sms {
                                    margin-right: 24px;
                                }
                            }
                            .flow {
                                padding: 8px;
                                margin: 0px 16px 16px 16px;
                                background: var(--color-neutrals02);
                            }
                            .shflow {
                                margin-top: 16px;
                                margin-bottom: 8px;
                            }
                            .policy {
                                margin-top: 8px;
                                .el-radio {
                                    margin-top: 16px;
                                    .el-radio__label {
                                        padding-left: 0px;
                                    }
                                }
                                .sp,.dzq {
                                    padding-right: 79px;
                                }
                                .people {
                                    display: flex;
                                    margin-top: 8px;
                                    .right {
                                        margin-left: 85px;
                                        .external {
                                            margin-top: 8px;
                                            .icsel-vcnts {
                                                width: 308px;
                                            }
                                        }
                                        .internal,.external {
                                            display: flex;
                                            .fx-selector-v2 {
                                                margin-left: 8px;
                                                .selected-icon {
                                                    margin-top: 0px;
                                                }
                                            }
                                        }
                                        .internal-span,.external-span {
                                            &::before{
                                            content: "*";
                                                color: #ff5730;
                                                vertical-align: middle;
                                                font-size: 14px;
                                            }
                                        }
                                        .internal-span,.external-span,.people-span {
                                            margin-top: 6px;
                                        }
                                    }
                                }
                                .notice {
                                   .notice-set {
                                        margin-right: 56px;
                                   } 
                                   .notice-movement {
                                        display: flex;
                                        margin: 12px 0 0 132px;
                                        padding: 8px;
                                        border-radius: 4px;
                                        background: var(--color-neutrals02);
                                        .automatic-type {
                                            display: inline-block;
                                            width: 72px;
                                            margin-right: 8px;
                                            color: var(--color-neutrals15);
                                            &::before{
                                            content: "*";
                                                color: #ff5730;
                                                vertical-align: middle;
                                                font-size: 14px;
                                            }
                                        }
                                        .notice-right {
                                            width: 100%;
                                            .height3 {
                                                height: 32px;
                                            }
                                            .automatic-input {
                                                    width: 88px;
                                                    margin-right: 6px;
                                                    margin-top: 8px;
                                                }
                                                .automatic-tx {
                                                    margin-left: 6px;
                                                    color: var(--color-neutrals19);
                                                }
                                                .el-checkbox__inner {
                                                    width: 14px !important;
                                                    height: 14px !important;
                                                    &::after {
                                                        top: 1px !important;
                                                        left: 4px !important;
                                                    }
                                                }
                                                .el-checkbox__label {
                                                    padding-left: 0px !important;
                                                    font-size: 14px !important;
                                                }
                                            .notice-automatic  {
                                                .el-checkbox {
                                                    margin-right: 2px !important;
                                                }
                                                .automatic-text {
                                                    display: flex;
                                                    .button,.danger {
                                                        display: inline-block;
                                                        margin-top: 14px;
                                                        margin-left: -12px;
                                                    }
                                                    .button {
                                                        cursor: pointer;
                                                        color:  var(--color-info06);
                                                    }
                                                    .danger {
                                                        color: var(--color-danger06);
                                                    }
                                                    .change,.pirew {
                                                        margin-left: 8px;
                                                        cursor: pointer;
                                                        color: var(--color-info06);
                                                    }
                                                    .pirew,.change,.xq {
                                                        display: inline-block;
                                                        margin-top: 14px; 
                                                    }
                                                    .change-push,.pirew-push {
                                                        cursor: no-drop !important;
                                                        pointer-events: none;
                                                        color: var(--color-info03);
                                                    }
                                                    .xq {
                                                        margin-left: -9px;
                                                    }
                                                    .automatic-wa {
                                                        margin-left: 18px;
                                                        display: inline-block;
                                                        width: 72px;
                                                        margin-right: 20px;
                                                        margin-top: 14px;
                                                        color: var(--color-neutrals15);
                                                    }
                                                    .el-textarea {
                                                        margin-top: 8px;
                                                        padding-bottom: 0px !important;
                                                    }
                                                }
                                            }
                                        }
                                   }
                                   .notice-content {
                                        display: flex;
                                        margin: 12px 0 0 132px;
                                        padding: 8px;
                                        border-radius: 4px;
                                        background: var(--color-neutrals02);
                                        .automatic-type {
                                            display: inline-block;
                                            width: 72px;
                                            margin-right: 8px;
                                            color: var(--color-neutrals15);
                                            &::before{
                                            content: "*";
                                                color: #ff5730;
                                                vertical-align: middle;
                                                font-size: 14px;
                                            }
                                        }

                                        .notice-right {
                                            width: 100%;
                                            .height3 {
                                                height: 32px;
                                            }
                                            .notice-automatic {
                                                // margin-bottom: 16px;
                                                .el-tooltip {
                                                    font-size: 14px;
                                                    cursor: pointer;
                                                }
                                                .el-checkbox {
                                                    margin-right: 2px !important;
                                                }
                                                .automatic-time {
                                                    margin-left: 18px;
                                                    display: inline-block;
                                                    width: 72px;
                                                    margin-right: 8px;
                                                    color: var(--color-neutrals15);
                                                }
                                                
                                                .automatic-before {
                                                    margin-right: 6px;
                                                    color: var(--color-neutrals19);
                                                }
                                                .automatic-select {
                                                    .el-input  {
                                                        width: 88px;
                                                    }
                                                }
                                                .automatic-text {
                                                    display: flex;
                                                    .button,.danger {
                                                        display: inline-block;
                                                        margin-top: 14px;
                                                        margin-left: -12px;
                                                    }
                                                    .button {
                                                        cursor: pointer;
                                                        color:  var(--color-info06);
                                                    }
                                                    .danger {
                                                        color: var(--color-danger06);
                                                    }
                                                    .change,.pirew {
                                                        margin-left: 8px;
                                                        cursor: pointer;
                                                        color: var(--color-info06);
                                                    }
                                                    .pirew,.change,.xq {
                                                        display: inline-block;
                                                        margin-top: 14px; 
                                                    }
                                                    .change-push,.pirew-push {
                                                        cursor: no-drop !important;
                                                        pointer-events: none;
                                                        color: var(--color-info03);
                                                    }
                                                    .xq {
                                                        margin-left: -9px;
                                                    }
                                                    .automatic-wa {
                                                        margin-left: 18px;
                                                        display: inline-block;
                                                        width: 72px;
                                                        margin-right: 20px;
                                                        margin-top: 14px;
                                                        color: var(--color-neutrals15);
                                                    }
                                                    .el-textarea {
                                                        margin-top: 8px;
                                                        padding-bottom: 0px !important;
                                                    }
                                                }
                                                .automatic-input {
                                                    width: 88px;
                                                    margin-right: 6px;
                                                    margin-top: 8px;
                                                }
                                                .automatic-tx {
                                                    margin-left: 6px;
                                                    color: var(--color-neutrals19);
                                                }
                                                .el-checkbox__inner {
                                                    width: 14px !important;
                                                    height: 14px !important;
                                                    &::after {
                                                        top: 1px !important;
                                                        left: 4px !important;
                                                    }
                                                }
                                                .el-checkbox__label {
                                                    padding-left: 0px !important;
                                                    font-size: 14px !important;
                                                }
                                            }
                                        }
                                   }
                                }
                                .cycle {
                                    .cycle-period,.cycle-fixed {
                                        margin-top: 12px;
                                    }
                                    .cycle-fixed {
                                        .fixed-evert {
                                            margin-left: 130px;
                                            margin-right: 6px;
                                        }
                                        .fixed-mouth {
                                            margin-right: 6px;
                                        }
                                        .select-mou {
                                            margin-right: 6px;
                                            .el-input  {
                                                width: 88px;
                                            }
                                        }
                                    }
                                    .cycle-period {
                                        .cycle-period-an {
                                            margin-left: 130px;
                                            margin-right: 6px;
                                        }
                                    }
                                    .cycle-span {
                                        margin-right: 80px;
                                    }
                                    .cycle-setpush {
                                        cursor: no-drop;
                                        pointer-events: none;
                                        color: var(--color-info03);
                                    }
                                    .input-zq {
                                        width: 88px;
                                        margin-right: 6px;
                                    }
                                    .select-zq {
                                        margin-right: 6px;
                                        .el-input  {
                                            width: 88px;
                                        }
                                    }
                                    .cycle-sign {
                                        margin-right: 12px;
                                    }
                                }
                                .appl {
                                    margin-top: 8px;
                                    margin-bottom: 16px;
                                    .el-input {
                                        width: 270px;
                                        padding-left: 102px;
                                        .el-input__inner {
                                            height: 28px;
                                            line-height: 28px;
                                        }
                                    }
                                    .appl-choose {
                                        color: var(--color-neutrals15);
                                        &::before{
                                            content: "*";
                                            color: #ff5730;
                                            vertical-align: middle;
                                            font-size: 14px;
                                        }
                                    }

                                }
                                .syrange {
                                    display: inline-block;
                                    margin-top: 16px;
                                    padding-right: 79px;
                                }
                                .el-input {
                                    width: 270px;
                                    .el-input__inner {
                                        height: 32px;
                                        line-height: 32px;
                                    }
                                }
                            }
                            border-radius: 4px;
                            h1 {
                                color: var(--color-neutrals19);
                                height: 16px;
                                line-height: 16px;
                                font-size: 16px;
                            }
                            width: 93%;
                            margin: 16px 16px 16px 0;
                            padding: 16px 0;
                            border: 1px solid var(--color-neutrals05);
                            .radio {
                                margin-top: 16px;
                                .el-radio__label {
                                    padding-left: 0px;
                                }
                            }
                            .name-a {
                                margin-top: 8px;
                                margin-bottom: 16px;
                                .el-input {
                                    width: 270px;
                                    padding-left: 72px;
                                    .el-input--small {
                                        height: 28px;
                                        line-height: 28px;
                                    }
                                }
                                .rlname {
                                    color: var(--color-neutrals15);
                                    &::before{
                                        content: "*";
                                        color: #ff5730;
                                        vertical-align: middle;
                                        font-size: 14px;
                                    }
                                }
                            }
                        }
                    }
                    .title {
                        h1 {
                            color: var(--color-neutrals19);
                            padding-left: 8px;
                            border-radius: 1px;
                            height: 16px;
                            line-height: 16px;
                            font-size: 16px;
                            border-left: 4px solid var(--color-primary06);

                        }
                        .setting {
                            p {
                                font-size: 12px;
                                color: var(--color-neutrals11);
                                margin-top: 4px;
                                padding-left: 12px;
                            }
                            .sp {
                                font-size: 12px;
                                color: var(--color-neutrals11);
                                padding-left: 12px;
                            }
                            .to {
                                color: var(--color-info06);
                                cursor: pointer;
                                padding-left: 4px;
                            }
                        }
                    }
                }
                .third-content {
                    .third-tablelist {
                        .table-title {
                            display: flex;
                            justify-content: space-between;
                            padding: 8px 0;
                            .table-span {
                                color: var(--color-neutrals19);
                                font-size: 14px;
                                font-weight: 500;
                                line-height: 32px;
                            }
                        }
                        .table-content {
                            .el-table {
                                border: 1px solid var(--color-neutrals05);
                                border-bottom: none;
                                .el-button--text {
                                    color: var(--color-info06) !important;
                                }
                                th {
                                    background: var(--color-neutrals03);
                                    padding: 8px 0;
                                    .cell {
                                        color: var(--color-neutrals19);
                                    }
                                }
                                td {
                                    padding:0;
                                    .cell {
                                        color: var(--color-neutrals19);
                                    }
                                }
                            }
                        }
                    }
                    .third-ruleset {
                        .flow-bottom {
                                position: fixed;
                                bottom: 8px;
                                height: 32px;
                                line-height: 32px;
                            }
                        .content { 
                            height: calc(100vh - 250px);
                            overflow-y: scroll;
                           
                            .cpntent-h {
                                margin-top: 12px;
                                margin-bottom: 12px;
                                color: var(--color-neutrals19);
                                padding-left: 8px;
                                border-radius: 1px;
                                height: 16px;
                                line-height: 16px;
                                font-size: 16px;
                                border-left: 4px solid var(--color-primary06);
                            }
                            .flow-content {
                                padding: 8px;
                                background: var(--color-neutrals02);
                                font-size: 12px;
                                line-height: 18px;
                                color: var(--color-neutrals15);
                                .flow-span3 {
                                    margin-left: 4px;
                                    color: var(--color-info06);
                                    cursor: pointer;
                                }
                            }
                            .flow-zh,.flow-partner {
                                padding: 16px;
                                margin-top: 8px;
                                border: 1px solid var(--color-neutrals05);
                                border-radius: 4px;
                                h1 {
                                    font-size: 16px;
                                    color: var(--color-neutrals19);
                                    margin-bottom: 8px;
                                }
                                .flow-partner-content {
                                    display: flex;
                                    .el-textarea {
                                        width: 368px;
                                        margin-top: 8px;
                                        margin-left: 8px;
                                    }
                                    span {
                                        margin-top: 12px;
                                    }
                                }
                                .el-input {
                                    width: 368px;
                                    margin-left: 4px;
                                    .el-input__inner {
                                        height: 28px;
                                        line-height: 28px;
                                    }
                                }
                            }
                        }
                    }
                    .plan,.ruleset { 
                        background: var(--color-neutrals03);
                        padding: 5px 8px;
                        display: inline-block;
                        line-height: 18px;
                        cursor: pointer;
                        color: var(--color-neutrals15);
                        border-radius: 4px;
                    } 
                    .plan {
                        margin-right: 8px;
                    }
                    .plan-active {
                        background: var(--color-primary01) !important;
                        color: var(--color-primary06) !important;
                    }
                }
            }
        }
    }
</style>
