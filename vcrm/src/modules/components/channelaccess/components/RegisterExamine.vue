<template>
    <div style="height: 80%;overflow-y: scroll;">
        <div v-show="!other" class="resgister-examine">
            <div class="content">
                <div class="title">
                    <h1>{{ $t("crm.RegisterExamine.set") }}</h1>
                </div>
                <div class="des">
                    <h1>{{ $t("crm.RegisterExamine.parent") + (ispartner ? $t("合作伙伴") : $t("客户")) + $t("crm.RegisterExamine.end") }}</h1>
                    <div class="setting">
                        <p>{{ $t("crm.RegisterExamine.content") + (ispartner ? $t("合作伙伴") : $t("客户")) }}</p>
                        <span class="to" @click="flowExamine">{{ $t("crm.RegisterExamine.go") }}</span>
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="title">
                    <h1>{{ $t("crm.RegisterExamine.result") }}</h1>
                </div>
                <div class="des notice">
                    <h1>{{ $t("crm.RegisterExamine.noticeChannel") }}</h1>
                    <div class="channel">
                        <div class="sms">
                            <div class="one">
                                <fx-tag v-show="!isSms" type="danger" size="mini">{{ $t("未开通") }}</fx-tag>
                                <span class="one-sms">{{ $t("短信") }}</span>
                                <fx-switch
                                    v-model="smsValue"
                                    size="small"
                                    :disabled="!isSms"
                                    @change="switch_status('sms',$event)"
                                    >
                                </fx-switch>
                            </div>
                            <div class="two">
                            </div>
                            <div class="three">
                                <span class="three-sms" v-show="!isSms" @click="dredge">{{ $t("开通短信服务") }}</span>
                                <span class="three-sms" v-show="isSms" @click="edit">{{ $t("配置短信内容") }}</span>
                                <span class="three-apl" @click="selectRule('sms')">{{ $t("添加APL代码") }}</span>
                                <fx-input v-model="smsAplVal"><span @click="clearApl('sms')" class="fx-icon-close" slot="suffix" style="padding-right:8px"></span></fx-input>
                            </div>
                        </div>
                        <div class="email">
                            <div class="one">
                                <span class="one-sms">{{ $t("邮件") }}</span>
                                <fx-switch
                                    v-model="emailValue"
                                    @change="switch_status('email',$event)"
                                    size="small"
                                    >
                                </fx-switch>
                            </div>
                            <div class="two">
                            </div>
                            <div class="three">
                                <span class="three-sms" @click="editema">{{ $t("crm.email.setemail") }}</span>
                                <span class="three-apl" @click="selectRule('email')">{{ $t("添加APL代码") }}</span>
                                <fx-input  v-model="emailAplVal"><span @click="clearApl('email')" class="fx-icon-close" slot="suffix" style="padding-right:8px"></span></fx-input>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="content">
                <div class="title">
                    <h1>{{ $t("crm.RegisterExamine.setconfig") }}</h1>
                </div>
                <div class="des">
                    <h1>{{ $t("crm.RegisterExamine.des.open") + (ispartner ? $t("合作伙伴") : $t("客户")) + $t("crm.RegisterExamine.end1")}}</h1>
                    <div class="setting">
                        <p>{{ $t("crm.RegisterExamine.des.content") + (isApp == 'prm' ? $t("crm.channelguide.middle.dlt") : isApp == 'system' ? $t("app_sail.common.topbar.title") : $t("crm.channelguide.middle.service")) + $t("crm.RegisterExamine.des.now") + (isApp == 'prm' ? $t("crm.channelguide.middle.dlt") : isApp == 'system' ? $t("app_sail.common.topbar.title") : $t("crm.channelguide.middle.service")) + $t("crm.RegisterExamine.des.end2")}}</p>
                    </div>
                    <div class="all">
                        <div class="top">
                            <p>{{ $t("crm.RegisterExamine.allrules") }}</p>
                            <fx-radio @change="changeAll" v-model="allradio" label="1" size="mini">{{ $t("全部") }}</fx-radio>
                            <fx-radio @change="changeAll" v-model="allradio" label="2" size="mini">{{ $t("crm.brow.condition") }}</fx-radio>
                        </div>
                        <div v-show="allradio ==1">
                            <div class="select">
                                <span class="examine">{{ $t("crm.RegisterExamine.role") }}</span>
                                <fx-select
                                    ref="select"
                                    v-model="value"
                                    multiple
                                    collapse-tags
                                    clearable                        
                                    :options="options"
                                ></fx-select>
                            </div>
                            <div class="version">
                                <span class="version-span">{{ $t("crm.RegisterExamine.version") }}</span>
                                <fx-radio v-model="tenant" label="1" size="mini">{{ $t("crm.RegisterExamine.tenant") }}</fx-radio>
                                <fx-radio v-model="tenant" v-show="isDl" label="2" size="mini">{{ $t("crm.RegisterExamine.agentnew") }}</fx-radio>
                                <fx-radio v-model="tenant" v-show="!isDl" label="3" size="mini">{{ $t("crm.RegisterExamine.jxs") }}</fx-radio>
                            </div>
                            <div class="time">
                                <span class="time-span">{{ $t("crm.RegisterExamine.time") }}</span>
                                <fx-radio v-model="time" label="1" size="mini">{{ $t("crm.RegisterExamine.moment") }}</fx-radio>
                                <fx-radio v-model="time" label="2" size="mini">{{ $t("crm.RegisterExamine.day") }}</fx-radio>
                                <div class="moment" v-show="time == 1">
                                    <span class="reclaim">{{ $t("crm.RegisterExamine.recoveryMoment") }}</span>
                                    <fx-input
                                        v-model="inputmoment"
                                        isPositiveNum="true"
                                        decimal-places="0"
                                        type="number"
                                        :placeholder="$t('请输入')"
                                        >
                                        <span class="el-input__icon" slot="suffix" style="padding-right:8px">
                                            {{ $t("天") }}
                                        </span>
                                    </fx-input>
                                </div>
                                <div class="day" v-show="time == 2">
                                    <span class="reclaim">{{ $t("crm.RegisterExamine.recoveryDay") }}</span>
                                    <fx-input v-model="mouth" isPositiveNum="true" type="number" @input="handleInput" :placeholder="$t('crm.RegisterExamine.onetwo')"
        ></fx-input>
                                    <span>{{ $t("月") }}</span>
                                    <fx-input v-model="dayday" isPositiveNum="true" type="number" @input="dayBlur" @blur="regex"></fx-input>
                                    <span>{{ $t("日") }}</span>
                                </div>
                            </div>
                        </div>
                        <div v-show="allradio ==2">
                            <div style="display: flex;" v-for="(item,index) in exmainList" :key="item._uuid">
                                <div class="exmainFor" :key="item._uuid">
                                    <div class="condition" :ref="'filter-ref' + item._uuid"></div>
                                    <div class="select">
                                        <span class="examine">{{ $t("crm.RegisterExamine.role") }}</span>
                                        <fx-select
                                            ref="select"
                                            v-model="item.selectValue"
                                            multiple
                                            collapse-tags
                                            clearable                        
                                            :options="item.options"
                                        ></fx-select>
                                    </div>
                                    <div class="version">
                                        <span class="version-span">{{ $t("crm.RegisterExamine.version") }}</span>
                                        <fx-radio v-model="item.tenant" label="1" size="mini">{{ $t("crm.RegisterExamine.tenant") }}</fx-radio>
                                        <fx-radio v-model="item.tenant" v-show="isDl" label="2" size="mini">{{ $t("crm.RegisterExamine.agentnew") }}</fx-radio>
                                        <fx-radio v-model="tenant" v-show="!isDl" label="3" size="mini">{{ $t("crm.RegisterExamine.jxs") }}</fx-radio>
                                    </div>
                                    <div class="time">
                                        <span class="time-span">{{ $t("crm.RegisterExamine.time") }}</span>
                                        <fx-radio v-model="item.time" label="1" size="mini">{{ $t("crm.RegisterExamine.moment") }}</fx-radio>
                                        <fx-radio v-model="item.time" label="2" size="mini">{{ $t("crm.RegisterExamine.day") }}</fx-radio>
                                        <div class="moment" v-show="item.time == 1">
                                            <span class="reclaim">{{ $t("crm.RegisterExamine.recoveryMoment") }}</span>
                                            <fx-input
                                                v-model="item.inputmoment"
                                                isPositiveNum="true"
                                                decimal-places="0"
                                                type="number"
                                                :placeholder="$t('请输入')"
                                                >
                                                <span class="el-input__icon" slot="suffix" style="padding-right:8px">
                                                    {{ $t("天") }}
                                                </span>
                                            </fx-input>
                                        </div>
                                        <div class="day" v-show="item.time == 2">
                                            <span class="reclaim">{{ $t("crm.RegisterExamine.recoveryDay") }}</span>
                                            <fx-input v-model="item.mouth" isPositiveNum="true" type="number" @input="handleInputF(index,$event)" :placeholder="$t('crm.RegisterExamine.onetwo')"
                ></fx-input>
                                            <span>{{ $t("月") }}</span>
                                            <fx-input v-model="item.dayday" isPositiveNum="true" type="number" @input="dayBlurF(index,$event)" @blur="regexF(index)"></fx-input>
                                            <span>{{ $t("日") }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="delete" v-show="exmainList.length > 1">
                                    <span class="fx-icon-process-delete" @click="deleteQuali(index, item._uuid)"></span>
                                </div>
                            </div>
                            
                            <div class="add">
                                <span class="fx-icon-xinzeng" @click="addEx"></span><span class="addEx" @click="addEx">{{ $t("添加") }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="botton-bottom">
                        <fx-button size="small" type="primary" @click="dredgeSave">{{ $t("保存") }}</fx-button>
                    </div>
                </div>
            </div>
            <div class="edit">
                <editDialog @closedialog="closeDialog" :isDisabled="supportCustomize" :dataList="editoptions" :smsid="smsapprovalNoticeId" :apiName="apiname" v-if="editFlag" ></editDialog>
            </div>
            <div class="email">
                <emailDialog @closeemail="closeEmail" :emailid="emapprovalNoticeId" :apiName="apiname" v-if="editEmail"></emailDialog>
            </div>
        </div>
        <div class="other" v-show="other">
            <div class="other-title"><h1>{{ $t("crm.channelguide.middle.examine") }}</h1></div>
            <div class="other-content">
                <img src="../assets/dltneedto.jpg" alt="">
                <p>{{ $t("crm.channelguide.market.need") }}</p>
                <span @click="toSetting('examine')">{{ $t("crm.channelguide.market.go") }}</span>
            </div>
        </div>
    </div>
</template>
<script>
import {requirePickselfAPL} from '@common/require.js';
import editDialog from "./editdialog";
import emailDialog from "./emaildialog";
import crmRequire from '@common/require';
import {getUUId} from '@common/utils';
import api from '../utils/api';
export default {
    name: "RegisterExamine",
    components: {
        editDialog,
        emailDialog
    },
    data() {
        return {
            apiname:'', // 邮箱弹窗的api名称
            isDl:true, // 是否代理商
            isApp:'prm',
            ispartner:true, // 是否合作伙伴对象
            other: false,
            inputmoment:'',
            time:"1",
            tenant:"1",
            allradio:'1',
            smsValue:true,
            emailValue:false,
            editFlag: false,
            editEmail:false,
            value:'',
            options:[],
            editoptions:[],
            isSms: false,
            mouth:'',
            dayday:'',
            exmainList:[],
            index:1,
            smsapprovalNoticeId:'', //审批提醒配置的 Id
            emapprovalNoticeId:'', //审批提醒配置的 Id
            smsaplApiName:'', //APL 函数的 apiName
            emaplApiName:'', //APL 函数的 apiName
            enterpriseActivationSettingId:'',
            smsAplVal:'',
            emailAplVal:'',
            emailAplId:'',
            smsAplId:'',
            smsAplArr:[],
            emailAplArr:[],
            // app: false
            filterComps: [],
            supportCustomize:false
        };
    },
     async mounted() {
        let result = await api.fetchChannelConfig();
        if(result.applyModules.indexOf("register") > -1) {
            this.other = false;
            $('.nav-header').show();
            $(".channel-access").css("padding", "0 16px");
            this.isDl = result.channelMode == "channel_agent" ? true : false;
            this.isApp = result.applyToApp == 'prm' ? 'prm' : result.applyToApp == 'dht' ? 'system' : 'fwt';
            this.ispartner = result.relatedObjectApiName == "PartnerObj" ? true : false;
            this.apiname = result.relatedObjectApiName;
            this.filterGroup = await crmRequire('crm-modules/common/filtergroup/filtergroup');
            this.init();
        } else {
            this.other = true;
            $('.nav-header').hide();
            $(".channel-access").css("padding", "0");
        }
        
    },
    methods: {
        toSetting(val) {
            window.location.replace(`https://${window.location.host}/XV/UI/manage#crmmanage/=/module-channel-home/key-${val}`);
        },
        clearApl(val) {
            if (val == 'sms') {
                this.smsAplVal = '';
                this.saveApl('sms','')
            } else {
                this.emailAplVal = '';
                this.saveApl('email','')
            }
        },
        addFilterGroup(index, defaultValue) {
            let _this = this;
            return new this.filterGroup({
                $wrapper: $(_this.$refs['filter-ref' + index]),
                apiname: this.ispartner ? 'PartnerObj' : 'AccountObj',
                defaultValue,
                width: 900,
                filterType: [
                    'object_reference',
                    'group',
                    'image',
                    'file_attachment',
                    'master_detail',
                    'auto_number',
                    'signature',
                    'quote',
                    'embedded_object_list',
                    'multi_level_select_one',
                    'tree_path',
                    'employee_many',
                    'department_many',
                    'html_rich_text',
                    'object_reference_many',
                    'big_file_attachment',
                ], 
                filterApiname: [],
                props: {
                    lazy: true,
                    checkStrictly: true, 
                    expandTrigger: 'click'
                },
            });

        },
        deleteQuali(i, _uuid) {
            let _this = this;
            _this.index --;
            _this.$refs['filter-ref' + _uuid] = null;
            _this.filterComps.splice(i, 1);
            _this.exmainList.splice(i, 1);
        },
        //保存apl
        saveApl(type,name) {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_save_notice_apl",
                        data: {
                            "approvalNoticeId": type =='sms' ? _this.smsapprovalNoticeId : _this.emapprovalNoticeId,
                            "notifyVia": type,
                            "aplApiName": name,
                            "bizScope":"register"
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        //查询企业账号开通配置
        query_setting() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_query_activation_settings",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        //切换开关状态
        switch_status(type,val) {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_toggle_switch_status",
                        data: {
                            "approvalNoticeId": type =='sms' ? _this.smsapprovalNoticeId : _this.emapprovalNoticeId,
                            "notifyVia": type,
                            "enabled": val,
                            "bizScope":"register"
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        //查询注册审批通知界面数据
        approval_notice() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_query_approval_notice",
                        data: {
                            "approvalNotices": []
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        async addEx () {
            let _this = this;
            if (_this.exmainList.length > 19) {
                CRM.util.alert($t("crm.register.alert"));
                return;
            } else {
                let _uuid = getUUId()
                _this.exmainList.push({
                    _uuid,
                    selectValue:'',
                    options: _this.options,
                    tenant: '1',
                    time:'1',
                    inputmoment:'',
                    mouth:'',
                    dayday:'',
                    priority:_this.index +1
                })
                this.$nextTick(()=>{
                    let _filter = this.addFilterGroup(_uuid, null);
                    this.filterComps.push(_filter);
                })
                _this.index ++
            }
            
        },
        async changeAll(val) {
            let _this = this;
            if(val ==2) {
                if(!_this.exmainList.length) {
                    let _uuid = getUUId()
                    _this.exmainList.push({
                        _uuid,
                        selectValue:'',
                        options: _this.options,
                        tenant: '1',
                        time:'1',
                        inputmoment:'',
                        mouth:'',
                        dayday:'',
                        priority:1
                    })
                    this.$nextTick(()=>{

                        let _filter = _this.addFilterGroup(_uuid, null);
                        this.filterComps.push(_filter)
                    })
                    
                }
            }
        },
        regex(){
            if(!this.mouth) {
                CRM.util.alert($t('crm.dltobj.please'));
                this.dayday = '';
                return
            }
        },
        dayBlur(value) {
            const bigArr = ['1','3','5','7','8','10','12'];
            const smallArr = ['4','6','9','11'];
            const bigregex = /^([1-9]|[1-2]\d|3[01])$/;
            const smallregex = /^([1-9]|[12]\d|30)$/;
            const tworegex = /^([1-9]|[1]\d|2[0-8])$/;
            let lastValidValue = '';
            if(this.mouth) {
                if(bigArr.indexOf(this.mouth) > -1) {
                    if (!bigregex.test(value)) {
                        this.dayday = lastValidValue;
                    } else {
                        lastValidValue = value;
                    }
                } else if (smallArr.indexOf(this.mouth) > -1) {
                    if (!smallregex.test(value)) {
                        this.dayday = lastValidValue;
                    } else {
                        lastValidValue = value;
                    }
                } else {
                    if (!tworegex.test(value)) {
                        this.dayday = lastValidValue;
                    } else {
                        lastValidValue = value;
                    }
                }
            } 
        },
        regexF(v) {
            if(!this.exmainList[v].mouth) {
                CRM.util.alert($t('crm.dltobj.please'));
                this.exmainList[v].dayday = '';
                return
            }
        },
        dayBlurF(v,i) {
            const bigArr = ['1','3','5','7','8','10','12'];
            const smallArr = ['4','6','9','11'];
            const bigregex = /^([1-9]|[1-2]\d|3[01])$/;
            const smallregex = /^([1-9]|[12]\d|30)$/;
            const tworegex = /^([1-9]|[1]\d|2[0-8])$/;
            let lastValidValue = '';
            if(this.exmainList[v].mouth) {
                if(bigArr.indexOf(this.exmainList[v].mouth) > -1) {
                    if (!bigregex.test(i)) {
                        this.exmainList[v].dayday = lastValidValue;
                    } else {
                        lastValidValue = i;
                    }
                } else if (smallArr.indexOf(this.exmainList[v].mouth) > -1) {
                    if (!smallregex.test(i)) {
                        this.exmainList[v].dayday = lastValidValue;
                    } else {
                        lastValidValue = i;
                    }
                } else {
                    if (!tworegex.test(i)) {
                        this.exmainList[v].dayday = lastValidValue;
                    } else {
                        lastValidValue = i;
                    }
                }
            } 
        },
        handleInputF(v,i) {
            let _this = this;
            _this.exmainList[v].dayday = '';
            const regex = /^([1-9]|1[0-2]?)$/;
            let lastValidValue = '';
            if (!regex.test(i)) {
                _this.exmainList[v].mouth = lastValidValue;
            } else {
                lastValidValue = i;
            }
        },
        handleInput(value) {
            this.dayday = '';
            const regex = /^([1-9]|1[0-2]?)$/;
            let lastValidValue = '';
            if (!regex.test(value)) {
                this.mouth = lastValidValue;
            } else {
                lastValidValue = value;
            }
        },
        async selectRule(val) {
            let PickselfAPL = await requirePickselfAPL();
            this.pickselfApl = new PickselfAPL({
                postData: {
                    binding_object_api_name: this.ispartner ? 'PartnerObj' : 'AccountObj', // 绑定对象
                    name_space: ['channel_notice'], // 范围规则
                    return_type: null, // 返回值类型
                },
                checkedData: val  == 'sms' ? this.smsAplArr.length && this.smsAplVal !== '' ? this.smsAplArr :[] : this.emailAplArr.length && this.emailAplVal !== '' ? this.emailAplArr : [],
            })
            this.pickselfApl.on('dialogEnter', (checkedData) => {
                if(val == 'sms') {
                    this.smsAplArr = checkedData;
                    this.smsAplVal = checkedData[0].function_name;
                    this.smsAplId = checkedData[0].id;
                    this.saveApl(val,checkedData[0].function_name)
                } else if (val == 'email') {
                    this.emailAplArr = checkedData;
                    this.emailAplVal = checkedData[0].function_name;
                    this.emailAplId = checkedData[0].id;
                    this.saveApl(val,checkedData[0].function_name)
                }
                this.$emit('dialogEnter')
            })
        },
        getAplList() {
            return new Promise((resolve, reject) => {
				CRM.util.FHHApi({
                    url: '/EM1HFUNC/biz/query',
                    data: {
                        pageNumber: 1,
                        pageSize: 500,
                        is_include_used: true,
                        binding_object_api_name: this.ispartner ? 'PartnerObj' : 'AccountObj', // 绑定对象
                        name_space: ['channel_notice'], // 范围规则
                        return_type: null, // 返回值类型
                    },
                    success: (res) => {
                        if (res.Result.StatusCode == 0 && res.Value) {
                            resolve(res.Value?.function || [])
                        } else {
                            CRM.util.alert(res?.Value?.msg || res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
			});
        },
        async init() {
            let _this = this;
            let aplList = await _this.getAplList();
            Promise.all([_this.approval_notice(),_this.provisioningSms(),_this.roleList(),_this.query_setting()]).then(res => {
                const { approvalNotices } = res[0];
                approvalNotices.forEach(item => {
                    item.aplmap =[];
                    aplList.forEach(val => {
                        if (item?.aplApiName == val.function_name || item?.aplApiName == val.api_name) {
                            item.aplmap.push(val)
                        }
                    })
                })
                approvalNotices.forEach(item => {
                    if (item.notifyVia.indexOf('sms') > -1) {
                        _this.smsValue = item.enabled;
                        _this.smsapprovalNoticeId = item.approvalNoticeId;
                        _this.smsAplVal = item?.aplmap[0]?.function_name || '';
                        _this.smsAplArr = item?.aplmap || [];
                        _this.smsAplId = item?.aplmap[0]?.id || ''
                    } else {
                        _this.emailValue = item.enabled;
                        _this.emapprovalNoticeId = item.approvalNoticeId;
                        _this.emailAplVal = item.aplApiName;
                        _this.emailAplArr = item?.aplmap || [];
                        _this.emailAplId = item?.aplmap[0]?.id || ''
                    }
                })
                _this.isSms = res[1].data.open;
                _this.supportCustomize = res[1].data.supportCustomize;
                _this.options = _.map(res[2].roles, val => ({
                    label: val.roleName,
					value: val.roleCode
                }));
                let activation = res[3];
                _this.enterpriseActivationSettingId = activation.enterpriseActivationSettings[0]?.enterpriseActivationSettingId;
                _this.allradio = activation.conditionType == 'ALL' ? '1' : '2';
                if(_this.allradio == '1') {
                    _this.value = activation.enterpriseActivationSettings[0]?.defaultRoles;
                    _this.tenant = activation.enterpriseActivationSettings[0]?.enterpriseType == 'non_crm' ? "1" : "2";
                    _this.time = activation.enterpriseActivationSettings[0]?.recyclingMode == "fixed_day" ? "1" : '2';
                    if(_this.time == '1') {
                        _this.inputmoment = activation.enterpriseActivationSettings[0]?.expireDays;
                    } else {
                        _this.mouth = activation.enterpriseActivationSettings[0]?.expireCycleMonth;
                        _this.dayday = activation.enterpriseActivationSettings[0]?.expireCycleDay;
                    }
                } else {
                    _this.index = activation.enterpriseActivationSettings.length;
                    activation.enterpriseActivationSettings.forEach((item,index) => {
                        let defaultValue = JSON.parse(item.condition) ? JSON.parse(JSON.parse(item.condition).value) : null;
                        let _uuid = getUUId();
                        _this.exmainList.push({
                            _uuid,
                            selectValue: item.defaultRoles,
                            options: _this.options,
                            tenant: item.enterpriseType == 'non_crm' ? "1" : '2',
                            time: item.recyclingMode == 'fixed_day' ? "1" : '2',
                            inputmoment: item.recyclingMode == 'fixed_day'? item.expireDays: null,
                            mouth:item.recyclingMode == "fixed_date" ? item.expireCycleMonth: null,
                            dayday:item.recyclingMode == "fixed_date" ? item.expireCycleDay: null,
                            priority:item.priority,
                            defaultValue
                        })   
                        this.$nextTick(()=>{
                            let _filter = this.addFilterGroup(_uuid, defaultValue)
                            this.filterComps.push(_filter);
                        })
                    })     
                }
            })
        },
        dredge() {
            window.open(`https://${window.location.host}/XV/UI/Home#/app/marketing/index/=/sms-marketing/init/welcome`, "_blank")
        },
        provisioningSms() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/sms_hub/service/query_sms_status",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        roleList() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_role_list",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        dredgeSave() {
            let _this = this;
            _this.filterComps.forEach( (a, idx) => {
                _this.exmainList[idx].defaultValue = a.getValue();
            })
            if(_this.allradio == '1') {
                if(_this.value.length < 1) {
                    CRM.util.alert($t("请选择默认角色"));
                    return;
                }
                return new Promise(function(resolve) {
                    CRM.util.FHHApi({
                            url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_save_activation_settings",
                            data: {
                                "conditionType":"ALL",
                                "enterpriseActivationSettings": [
                                    {
                                        "enterpriseActivationSettingId": _this.enterpriseActivationSettingId,
                                        "condition": "{\"type\":\"ALL\"}",
                                        "defaultRoles": _this.value,
                                        "enterpriseType": _this.tenant == '1' ? "non_crm" : "crm",
                                        "recyclingMode": _this.time == '1' ? "fixed_day" : "fixed_date",
                                        "expireDays": _this.time == '1' ? _this.inputmoment : null,
                                        "expireCycleMonth": _this.time == '2' ? _this.mouth : null,
                                        "expireCycleDay": _this.time == '2' ? _this.dayday : null,
                                        "priority": 1
                                    }
                                ]
                            },
                            success: function(res) {
                                if (res.Result.StatusCode === 0) {
                                    CRM.util.remind(1, $t("保存成功"));
                                    resolve(res.Value);
                                    return;
                                }
                                CRM.util.alert(
                                    res.Result.FailureMessage ||
                                        $t("暂时无法获取数据") + "!"
                                );
                            }
                        },{ errorAlertModel: 1}
                    );
                });
            } else {
                let flag = _this.exmainList.findIndex(item => {
                    return item.selectValue.length < 1
                })
                if(flag !== -1) {
                    CRM.util.alert($t("请选择默认角色"));
                    return;
                } else {
                    let enterpriseActivationSettingsArr = [];
                    _this.exmainList.forEach((item) => {
                        item.condition = {
                            "type": "CONDITION",
                            "value": item.defaultValue
                        }
                    })
                    _this.exmainList.forEach((item) => {
                        item.condition = JSON.stringify(item.condition)
                        enterpriseActivationSettingsArr.push({
                            condition:item.condition,
                            enterpriseActivationSettingId:_this.enterpriseActivationSettingId,
                            defaultRoles:item.selectValue,
                            enterpriseType:item.tenant == '1' ? "non_crm" : "crm",
                            recyclingMode: item.time == '1' ? "fixed_day" : "fixed_date",
                            expireDays: item.time == '1' ? item.inputmoment : null,
                            expireCycleMonth:item.time == '2' ? item.mouth : null,
                            expireCycleDay:item.time == '2' ? item.dayday : null,
                            priority:item.priority
                        })
                    })
                    return new Promise(function(resolve) {
                        CRM.util.FHHApi({
                                url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_save_activation_settings",
                                data: {
                                    "conditionType":"CONDITION",
                                    "enterpriseActivationSettings": enterpriseActivationSettingsArr
                                },
                                success: function(res) {
                                    if (res.Result.StatusCode === 0) {
                                        CRM.util.remind(1, $t("保存成功"));
                                        resolve(res.Value);
                                        return;
                                    }
                                    CRM.util.alert(
                                        res.Result.FailureMessage ||
                                            $t("暂时无法获取数据") + "!"
                                    );
                                }
                            },{ errorAlertModel: 1}
                        );
                    });
                }
            }
        },
        flowExamine() {
            window.open(`https://${window.location.host}/XV/UI/manage#crmmanage/=/module-approval`, "_blank")
        },
        edit() {
            let _this = this;
            _this.editFlag = true;
            _this.phoneDescribe().then(res => {
                const { fieldDescribeMap } = res;
                let wantData = new Array();
                let arr = Object.keys(fieldDescribeMap);
                arr.forEach(item => {
                    wantData.push(fieldDescribeMap[item])
                })
                _this.editoptions = _.map(wantData, val => ({
                    label: val.label,
					value: val.apiName
                }));
            })
        },
        editema() {
            let _this = this;
            _this.editEmail = true;

        },
        phoneDescribe() {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_phone_field_describe",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        closeDialog() {
            this.editFlag = false;
        },
        closeEmail() {
            this.editEmail = false;
        },
    },
    destroyed() {
        this.filter?.destroy();
    },
};
</script>
<style lang="less">
    .other {
        .other-title {
            padding: 10px 0;
            border-bottom: 1px solid var(--color-neutrals05);
            h1 {
                padding-left: 16px;
                font-size: 14px;
                line-height: 28px;
                color: var(--color-neutrals19);
                font-weight: 700;
            }
        }
        .other-content {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                margin-block: 20%;
                img {
                    width: 375px;
                    height: 120px;
                }
                p {
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--color-neutrals15);
                    margin-top: 24px;
                    text-align: center;
                }
                span {
                    display: block;
                    text-align: center;
                    margin-top: 8px;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--color-info06);
                    cursor: pointer;
                }
            }
    }
    .resgister-examine{
        padding-top: 8px;
        .content {
            padding-bottom: 20px;
            .title {
                padding-left: 8px;
                height: 16px;
                line-height: 16px;
                font-size: 16px;
                border-radius: 1px;
                border-left: 4px solid var(--color-primary06);
                color: var(--color-neutrals19);
            }
            .notice {
                padding: 16px;
                .channel {
                    display: flex;
                    .sms,.email {
                        width: 377px;
                        border: 1px solid var(--color-neutrals05);
                        margin-top: 16px;
                        padding: 16px;
                        border-radius: 6px;
                        .one {
                            .el-tag {
                                display: inline-block;
                                height: 21px;
                                line-height: 22px;
                                margin-right: 2px;
                            }
                            .one-sms {
                                font-size: 16px;
                                font-weight: 700;
                                display: inline-block;
                                height: 24px;
                                line-height: 24px;
                            }
                            .el-switch {
                                float: right;
                            }
                        }
                        .two {
                            margin-top: 8px;
                            font-size: 12px;
                            color: var(--color-neutrals15);
                        }
                        .three {
                            margin-top: 32px;
                            font-size: 12px;
                            color: var(--color-info06);
                            .el-input {
                                width: 170px;
                                margin-left: 20px;
                                .el-input__inner {
                                    height: 28px;
                                    line-height: 28px;
                                }
                            }
                            span {
                                cursor: pointer;
                            }
                            .three-sms {
                                display: inline-block;
                                padding-right: 6px;
                            }
                            .three-apl {
                                display: inline-block;
                                padding-left: 8px;
                                border-left: 1px solid rgba(193, 197, 206, .3);
                            }
                            .fx-icon-close {
                                padding-top: 7px;
                            }
                        }
                    }
                    .sms {
                        margin-right: 24px;
                    }
                }
                .setmessage {
                    cursor: pointer;
                    color: var(--color-info06);
                }
            }
            .des {
                border: 1px solid var(--color-neutrals05);
                border-radius: 4px;
                margin-top: 16px;
                padding: 16px;
                h1 {
                    font-size: 16px;
                    color: var(--color-neutrals19);
                }
                .setting {
                    margin-top: 8px;
                    padding: 8px;
                    border-radius: 4px;
                    background: var(--color-neutrals02);
                    font-size: 12px;
                    line-height: 18px;
                    p {
                        color: var(--color-neutrals15);
                    }
                    .to {
                        color: var(--color-info06);
                        cursor: pointer;
                    }
                }
                .botton-bottom {
                    margin-top: 16px;
                }
                .all {
                    background: var(--color-neutrals02);
                    margin-top: 16px;
                    padding: 12px;
                    .delete {
                        line-height: 386px;
                        margin-left: 5px;
                        .fx-icon-process-delete {
                            font-size: 16px;
                            cursor: pointer;
                        }
                    }
                    .exmainFor {
                        width:95%;
                        background: #ffffff;
                        padding: 12px;
                        margin-top: 12px;
                    }
                    .add {
                        margin-top: 12px;
                        .addEx {
                            font-size: 12px;
                            color: var(--color-info06);
                            cursor: pointer;
                        }
                        .fx-icon-xinzeng {
                            cursor: pointer;
                            font-size: 14px;
                            margin-right: 3px;
                            &::before {
                                color: var(--color-info06);
                            }
                        }
                    }
                    .top {
                        p {
                            font-size: 12px;
                            color: var(--color-neutrals15);
                            margin-bottom: 8px;
                        }
                        .el-radio__label {
                            padding-left: 0px !important;
                        }
                    }
                    .version {
                        .version-span {
                            margin-right: 72px;
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        }
                        .el-radio__label {
                            padding-left: 0px !important;
                        }
                    }
                    .time {
                        margin-top: 16px;
                        .time-span {
                            margin-right: 48px;
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        }
                        .el-radio__label {
                            padding-left: 0px !important;
                        }
                        .day {
                            .reclaim {
                                margin-right: 54px;
                            }
                            margin-top: 16px;
                            .el-input {
                                width: 121px;
                                .el-input__inner {
                                    height: 28px;
                                    line-height: 28px;
                                }
                            }
                        }
                        .moment {
                            .reclaim {
                                margin-right: 54px;
                            }
                            margin-top: 16px;
                            .el-input {
                                width: 262px;
                                .el-input__inner {
                                    height: 28px;
                                    line-height: 28px;
                                }
                                .el-input__suffix {
                                    top: -5px;
                                }
                            }
                        }
                    }
                    .select {
                        margin: 16px 0;
                        .el-select {
                            width: 262px;
                            .el-input__inner {
                                height: 28px;
                                line-height: 28px;
                            }
                        }
                        .examine {
                            display: inline-block;
                            padding: 5px 0;
                            margin-right: 13px;
                            color: var(--color-neutrals15);
                            &::before{
                                content: "*";
                                color: #ff5730;
                                vertical-align: middle;
                                font-size: 14px;
                            }
                        }
                    }
                    .button {
                        padding-bottom: 8px;
                    }
                    .model {
                        margin: 12px 0;
                        .is {
                            margin-left: 145px;
                        }
                    }
                    .choose {
                        color: var(--color-neutrals15);
                    }
                    .el-checkbox {
                        color: var(--color-neutrals19);
                        margin-left: 4px;
                        margin-right: 4px;
                        .el-checkbox__label {
                            padding-left: 0px;
                        }
                    }
                    h1{
                        font-size: 16px;
                        line-height: 24px;
                        color: var(--color-neutrals19);
                    }
                    
                }

            }
        }
    }
</style>