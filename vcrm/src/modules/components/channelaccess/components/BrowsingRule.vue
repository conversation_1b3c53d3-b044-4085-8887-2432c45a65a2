<template>
    <div>
        <div v-show="!other" class="market-browsing">
            <fx-tabs v-model="activeName" @tab-click="handleClick">
                <fx-tab-pane :label="$t('crm.browsing.conset')" name="first" >
                    <div class="partner"></div>
                </fx-tab-pane>
                <fx-tab-pane :label="$t('crm.browsing.rulset')" name="second">
                    <div class="content">
                        <div class="title">
                            <h1>{{ $t("crm.browsing.rule") }}</h1>
                            <p>{{ $t("crm.browsing.content") }}</p>
                        </div>
                        <div class="loop-brow" v-for="(item,index) in browList" :key="item._uuid">
                            <div class="loop">
                                <h1>{{ $t("方案") + (index+1) }}</h1>
                                <div class="loop-name">
                                    <span class="rlname">{{ $t("crm.brow.name") }}</span>
                                    <fx-input size="small" v-model="item.name"></fx-input>
                                </div>
                                <h1>{{ $t("crm.brow.policy") }}</h1>
                                <div class="radio">
                                    <span class="yeno">{{ $t("crm.brow.ysno") }}</span>
                                    <fx-radio v-model="item.radio" label="1" size="mini">{{ $t("是") }}</fx-radio>
                                    <fx-radio v-model="item.radio" label="2" size="mini">{{ $t("否") }}</fx-radio>
                                </div>
                                <div class="step" v-for="(val,i) in item.stepList" :key="i">
                                    <span>{{ $t("sfa.vcrm.dlt.ordinal") }}</span><span>{{ (i+1) }}</span><span style="padding-right: 86px;">{{ $t("sfa.vcrm.dlt.step") }}</span>
                                    <fx-select
                                        ref="select"
                                        v-model="val.value"
                                        :options="val.stepOptions"
                                    ></fx-select>
                                    <span class="fx-icon-process-delete" v-show="item.stepList.length > 1" @click="deleteStep(index,i)"></span>
                                </div>
                                <div class="add">
                                    <span class="fx-icon-xinzeng" @click="addPolicy(index)"></span><span class="addPolicy" @click="addPolicy(index)">{{ $t("crm.brow.addpolicy") }}</span>
                                </div>
                                <h1>{{ $t("crm.brow.sign") }}</h1>
                                <div class="range">
                                    <span class="sy">{{ $t("适用范围") }}</span>
                                    <fx-radio @change="changeRan(index,$event)" v-model="item.range" label="1" size="mini">{{ $t("全部") }}</fx-radio>
                                    <fx-radio @change="changeRan(index,$event)" v-model="item.range" label="2" size="mini">{{ $t("crm.brow.condition") }}</fx-radio>
                                    <fx-radio @change="changeRan(index,$event)" v-model="item.range" label="3" size="mini">{{ $t("sfa.vcrm.dlt.apl") }}</fx-radio>
                                    <div :class= "`condition`+ index" class="apl-condition" :ref="'filter-ref' + item._uuid"></div>
                                </div>
                                <div class="appl" v-show="item.isAplSlowEamil">
                                    <span class="appl-choose">{{ $t("sfa.vcrm.dlt.apl") }}</span>
                                    <fx-input @focus="focuspass(index,item.apl)" v-model="item.apl">
                                        <span @click="clearApl(index)" class="fx-icon-close" slot="suffix" style="padding-right:8px;padding-top:7px;cursor: pointer;"></span>
                                    </fx-input>
                                </div>
                            </div>
                            <div class="delete" v-show="browList.length > 1">
                                <span class="fx-icon-process-delete" @click="deleteBrow(index,item._uuid)"></span>
                            </div>
                        </div>
                        <div class="addrule">
                            <span class="fx-icon-xinzeng" @click="addRule"></span><span class="add-rule" @click="addRule">{{ $t("crm.brow.addrule") }}</span>
                        </div>
                    </div>
                    <div class="save">
                        <fx-button size="small" type="primary" @click="save">{{ $t("保存") }}</fx-button>
                    </div>
                </fx-tab-pane>
            </fx-tabs>
        </div>
        <div class="other" v-show="other">
            <div class="other-title"><h1>{{ $t("crm.channelguide.middle.browse") }}</h1></div>
            <div class="other-content">
                <img src="../assets/dltneedto.jpg" alt="">
                <p>{{ $t("crm.channelguide.market.need") }}</p>
                <span @click="toSetting('browse')">{{ $t("crm.channelguide.market.go") }}</span>
            </div>
        </div>
    </div>
</template>
<script>
import crmRequire from '@common/require';
import { requireCRMList } from '@common/require';
import {requirePickselfAPL} from '@common/require.js';
import {getUUId} from '@common/utils';
import api from '../utils/api';
export default {
    name: "BrowsingRule",
   
    data() {
        return {
            other: false,
            ispartner: true,
            activeName:"first",
            browList:[],
            provisionSchemeId:'',
            stepOptions:[],
            filterComps: []
        };
    },
    methods: {
        toSetting(val) {
            window.location.replace(`https://${window.location.host}/XV/UI/manage#crmmanage/=/module-channel-home/key-${val}`);
        },
        clearApl(i) {
            this.browList[i].apl = '';
        },
        addFilterGroup(index, defaultValue) {
            let _this = this;
            return new this.filterGroup({
                $wrapper: $(_this.$refs['filter-ref' + index]),
                apiname: this.ispartner ? 'PartnerObj' : 'AccountObj',
                defaultValue,
                width: 900,
                filterType: [
                    'object_reference',
                    'group',
                    'image',
                    'file_attachment',
                    'master_detail',
                    'auto_number',
                    'signature',
                    'quote',
                    'embedded_object_list',
                    'multi_level_select_one',
                    'tree_path',
                    'employee_many',
                    'department_many',
                    'html_rich_text',
                    'object_reference_many',
                    'big_file_attachment',
                ], 
                filterApiname: [],
                props: {
                    lazy: true,
                    checkStrictly: true, 
                    expandTrigger: 'click'
                },
            });

        },
       async handleClick(tab) {
            let _this = this;
            if (tab.index == 1) {
                let listData = await _this.getListsData();
                _this.stepOptions = _.map(listData, val => ({
                    label: val.provision_title,
                    value: val._id
                }));
                let map = await _this.init();
                let aplList = await _this.getAplList();
                _this.provisionSchemeId = map.provisionScheme[0]?.provisionSchemeId;
                map.provisionScheme.forEach(item => {
                    item.aplmap =[];
                    aplList.forEach(val => {
                        if (item?.aplApiName == val.api_name) {
                            item.aplmap.push(val)
                        }
                    })
                })
                _this.browList = []
                map.provisionScheme.forEach((item,index) => {
                    let defaultValue = item.conditionType == "CONDITION" ? JSON.parse(JSON.parse(item.condition).value) : null;
                    let _uuid = getUUId();
                    _this.browList.push({
                        _uuid,
                        name:item.schemeName,
                        range: item.conditionType == "ALL" ? '1' : item.conditionType == "APL" ? "3" : "2",
                        radio: item.mustRead ? "1" : "2",
                        stepList: [],
                        isAplSlowEamil: item.conditionType =="APL" ? true : false ,
                        apl:item?.aplmap[0]?.function_name || '',
                        aplArr:item?.aplmap || [],
                        aplId:item?.aplmap[0]?.id || '',
                        aplName:item?.aplApiName || '',
                        defaultValue
                    })
                    item.provisionIds.forEach(val => {
                        _this.browList[index].stepList.push({
                            value:val,
                            stepOptions:_this.stepOptions
                        })
                    })
                    this.$nextTick(()=>{
                        if (item.conditionType == "CONDITION") {
                            let _filter = this.addFilterGroup(_uuid, defaultValue)
                            this.filterComps.push(_filter);
                        } else {
                            let _filter = this.addFilterGroup(_uuid, null)
                            this.filterComps.push(_filter);
                            $(".condition" + (index)).hide();
                        }
                    })
                })
                if(!_this.browList.length) {
                    let _uuid = getUUId();
                    _this.browList.push({
                        _uuid,
                        isAplSlowEamil:false,
                        apl:'',
                        aplArr:[],
                        aplId:'',
                        aplName:'',
                        name:'',
                        radio:'1',
                        range:'1',
                        stepList:[
                            {
                                value:'',
                                stepOptions:_this.stepOptions
                            }
                        ]
                    })
                    this.$nextTick(()=>{
                        let _filter = this.addFilterGroup(_uuid, null)
                        this.filterComps.push(_filter);
                        $(".condition" + (_this.browList.length -1)).hide();
                    })
                }
            }
        },
        async focuspass(i,val) {
            let _this = this;
            let PickselfAPL = await requirePickselfAPL();
            _this.pickselfApl = new PickselfAPL({
                postData: {
                    binding_object_api_name: this.ispartner ? 'PartnerObj' : 'AccountObj', // 绑定对象
                    name_space: ['channel_range'], // 范围规则
                    return_type: null, // 返回值类型
                },
                checkedData: val !== '' ? _this.browList[i].aplArr :[]
            })
            _this.pickselfApl.on('dialogEnter', (checkedData) => {
                _this.browList[i].aplArr = checkedData;
                _this.browList[i].apl = checkedData[0].function_name;
                _this.browList[i].aplId = checkedData[0].id;
                _this.browList[i].aplName = checkedData[0].api_name;
                _this.$emit('dialogEnter')
            })
        },
        deleteStep(val,i) {
            this.browList[val].stepList.splice(i,1)
        },
        deleteBrow(val,_uuid) {
            let _this = this;
            _this.$refs['filter-ref' + _uuid] = null;
            _this.filterComps.splice(val, 1);
            _this.browList.splice(val, 1);
        },
        containsDuplicate(nums) {
            return nums.length !== [...new Set(nums)].length;
        },
        setDefaultValue(item, index) {
            let filter =  this.filterComps[index];
            if(item.range == '2') {
                item.defaultValue = filter.getValue()
            } else {
                item.defaultValue = null
            }
        },
        save() {
            let _this = this;
            let provisonArr = [];
            _this.browList.forEach((item,index) => {
                this.setDefaultValue(item, index);
                provisonArr.push({
                    schemeName:item.name,
                    conditionType: item.range == '1' ? "ALL" : item.range == '2' ? "CONDITION" : "APL",
                    mustRead: item.radio == '1' ? true : false,
                    priority: (index + 1),
                    provisionSchemeId: _this.provisionSchemeId || "",
                    condition: item.range == '2' ? JSON.stringify({
                        "type": "CONDITION",
                        "value":item.defaultValue
                    })  : "{\"type\":\"ALL\"}",
                    aplApiName: item.range == '3' ?  item.aplArr.length && item.apl!=='' ? item.aplArr[0].api_name : "" : '',
                    provisionIds:[]
                })
                item.stepList.forEach(val => {
                    provisonArr[index].provisionIds.push(val.value)
                })
            })
            this.browList.forEach(item => {
                item.stepList.forEach(val => {
                    if(val.value == "") {
                        CRM.util.alert($t("crm.brow.alert"));
                        return
                    }
                })
            })
            let flag = provisonArr.findIndex(item => {
               return _this.containsDuplicate(item.provisionIds) == true
            })
            let flag1 = provisonArr.findIndex(item => {
               return item.conditionType == 'APL' &&  item.aplApiName == ''
            })
            if (flag1 !== -1) {
                CRM.util.alert($t("crm.apl.notkong"));
                return
            }
            if (flag !== -1) {
                CRM.util.alert($t("方案") + (flag+1) + $t("crm.have.chongfu"));
                return
            }
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_save_provision_scheme",
                        data: {
                            provisionScheme: provisonArr
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                CRM.util.remind(1, $t("保存成功"));
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
           
        },
        addPolicy(i){
            if(this.browList[i].stepList.length > 29) {
                CRM.util.alert($t("crm.register.step"));
                return;
            }
            this.browList[i].stepList.push({ value:'',stepOptions:this.stepOptions})
        },
        changeRan(i,val) {
            let _this = this;
            if (val == '2') {
                _this.browList[i].isAplSlowEamil = false;
                $(".condition" + i).show();
            } else if (val == '3') {
                _this.browList[i].isAplSlowEamil = true;
                $(".condition" + i).hide();
            } else {
                _this.browList[i].isAplSlowEamil = false;
                $(".condition" + i).hide();
            }
        },
        async addRule() {
            let _this = this;
            if(_this.browList.length > 19) {
                CRM.util.alert($t("crm.register.alert"));
                return;
            }
            let _uuid = getUUId()
            _this.browList.push({
                _uuid,
                name:'',
                radio:'1',
                range:'1',
                stepList:[{ value:'' ,stepOptions:_this.stepOptions}],
                isAplSlowEamil:false,
                apl:'',
                aplArr:[],
                aplId:'',
                aplName:''
            })
            this.$nextTick(()=>{
                let _filter = this.addFilterGroup(_uuid, null);
                this.filterComps.push(_filter);
                $(".condition" + (_this.browList.length -1)).hide();
            })
        },
        init() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_query_provision_scheme",
                        data: {
                            
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        async renderTable() {
            let _this = this;
            _this.$$table && _this.$$table.destroy();
            let List = await requireCRMList();
            _this.$$table = new List({
                wrapper: $('.partner', this.$el)[0],
                apiname: 'PartnerProvisionObj',
            });
            _this.$$table.render();
        },
        getListsData() {
            return new Promise((resolve, reject) => {
                    CRM.util.FHHApi({
                        url:
                            "/EM1HNCRM/API/v1/object/PartnerProvisionObj/controller/List",
                        data: {
                            object_describe_api_name:"PartnerProvisionObj",
                            search_query_info:JSON.stringify({"limit":20,"offset":0,"filters":[{"field_name":"biz_object_api_name","field_values":[this.curApiName],"operator":"EQ"}],"orders":[{"fieldName":"last_modified_time","isAsc":false}]})
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value?.dataList);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        getAplList() {
            return new Promise((resolve, reject) => {
				CRM.util.FHHApi({
                    url: '/EM1HFUNC/biz/query',
                    data: {
                        pageNumber: 1,
                        pageSize: 500,
                        is_include_used: true,
                        binding_object_api_name: this.ispartner ? 'PartnerObj' : 'AccountObj', // 绑定对象
                        name_space: ['channel_range'], // 范围规则
                        return_type: null, // 返回值类型
                    },
                    success: (res) => {
                        if (res.Result.StatusCode == 0 && res.Value) {
                            resolve(res.Value?.function || [])
                        } else {
                            CRM.util.alert(res?.Value?.msg || res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
			});
        }
    },
    async mounted() {
        let _this = this;
        let result = await api.fetchChannelConfig();
        if(result.applyModules.indexOf("browsing_rule") > -1) {
            this.other = false;
            $('.nav-header').show();
            $(".channel-access").css("padding", "0 16px");
            this.ispartner = result.relatedObjectApiName == "PartnerObj" ? true : false;
            this.filterGroup = await crmRequire('crm-modules/common/filtergroup/filtergroup');
            _this.renderTable();
        } else {
            this.other = true;
            $('.nav-header').hide();
            $(".channel-access").css("padding", "0");
        }
    }
};
</script>
<style lang="less">
    .other {
        .other-title {
            padding: 10px 0;
            border-bottom: 1px solid var(--color-neutrals05);
            h1 {
                padding-left: 16px;
                font-size: 14px;
                line-height: 28px;
                color: var(--color-neutrals19);
                font-weight: 700;
            }
        }
        .other-content {
                display: flex;
                justify-content: center;
                align-items: center;
                flex-direction: column;
                margin-block: 20%;
                img {
                    width: 375px;
                    height: 120px;
                }
                p {
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--color-neutrals15);
                    margin-top: 24px;
                    text-align: center;
                }
                span {
                    display: block;
                    text-align: center;
                    margin-top: 8px;
                    font-size: 14px;
                    line-height: 20px;
                    color: var(--color-info06);
                    cursor: pointer;
                }
            }
    }
    .market-browsing {
        height: 80%;
        // overflow-y: scroll;
        .el-tabs {
            height: 100%;
            .el-tabs__header {
                line-height: 40px;
            }
            .el-tabs__header {
                margin: 0;
            }
            .el-tabs__content {
                overflow: visible !important;
                .partner {
                    margin-top: -15px;
                    overflow-y: scroll;
                    height: calc(100vh - 200px);
                }
                .save {
                    position: sticky;
                    bottom: 8px;
                    height: 32px;
                    line-height: 32px;
                }
                .content {
                    padding-top: 1px;
                    height: calc(100vh - 224px);
                    overflow-y: scroll;
                    .addrule {
                        padding-top: 8px;
                        padding-bottom: 16px;
                        .fx-icon-xinzeng {
                            cursor: pointer;
                            font-size: 15px;
                            padding-right: 4px;
                            &::before{
                                color: var(--color-info06);
                            }
                        }
                        .add-rule {
                            cursor: pointer;
                            font-size: 12px;
                            color: var(--color-info06);
                        }
                    }
                    .loop-brow {
                        display: flex;
                        .delete {
                            line-height: 415px;
                            .fx-icon-process-delete {
                                font-size: 16px;
                                cursor: pointer;
                            }
                        }
                        .loop {
                            border-radius: 4px;
                            h1 {
                                color: var(--color-neutrals19);
                                height: 16px;
                                line-height: 16px;
                                font-size: 16px;
                            }
                            width: 93%;
                            margin: 16px 16px 16px 0;
                            padding: 16px;
                            border: 1px solid var(--color-neutrals05);
                            .step {
                                padding-left: 8px;
                                .el-input {
                                    width: 270px;
                                    .el-input__inner {
                                        height: 30px;
                                        line-height: 30px;
                                    }
                                }
                            }
                            .range {
                                margin-top: 16px;
                                .sy {
                                    font-size: 12px;
                                    padding-left: 8px;
                                    padding-right: 70px;
                                    color: var(--color-neutrals15);
                                }
                                .apl-condition {
                                    padding-top: 16px;
                                }
                            }
                            .add {
                                margin-top: 24px;
                                margin-bottom: 16px;
                                .fx-icon-xinzeng {
                                    cursor: pointer;
                                    font-size: 15px;
                                    padding-right: 4px;
                                    &::before{
                                        color: var(--color-info06);
                                    }
                                }
                                .addPolicy {
                                    cursor: pointer;
                                    font-size: 12px;
                                    color: var(--color-info06);
                                }
                            }
                            .step {
                                margin-top: 15px;
                                .fx-icon-process-delete {
                                    cursor: pointer;
                                }
                            }
                            .radio {
                                margin-top: 16px;
                                .yeno {
                                    color: var(--color-neutrals15);
                                    padding-right: 70px;
                                    padding-left: 8px;
                                }
                                .el-radio__label {
                                    padding-left: 0px;
                                }
                            }
                            .appl {
                                margin-top: 8px;
                                margin-bottom: 16px;
                                .el-input {
                                    width: 270px;
                                    padding-left: 102px;
                                    .el-input__inner {
                                        height: 28px;
                                        line-height: 28px;
                                    }
                                }
                                .appl-choose {
                                    color: var(--color-neutrals15);
                                    &::before{
                                        content: "*";
                                        color: #ff5730;
                                        vertical-align: middle;
                                        font-size: 14px;
                                    }
                                }

                            }
                            .loop-name {
                                margin-top: 8px;
                                margin-bottom: 16px;
                                .el-input {
                                    width: 270px;
                                    padding-left: 72px;
                                    .el-input--small {
                                        height: 28px;
                                        line-height: 28px;
                                    }
                                }
                                .rlname {
                                    color: var(--color-neutrals15);
                                    &::before{
                                        content: "*";
                                        color: #ff5730;
                                        vertical-align: middle;
                                        font-size: 14px;
                                    }
                                }
                            }
                        }
                    }

                    .title {
                        h1 {
                            color: var(--color-neutrals19);
                            padding-left: 8px;
                            border-radius: 1px;
                            height: 16px;
                            line-height: 16px;
                            font-size: 16px;
                            border-left: 4px solid var(--color-primary06);

                        }
                        p {
                            font-size: 12px;
                            color: var(--color-neutrals11);
                            margin-top: 4px;
                            padding-left: 12px;
                        }
                    }
                }
            }
        }
      
    }
</style>
