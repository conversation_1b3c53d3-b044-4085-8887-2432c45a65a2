<template>
    <fx-dialog
        class="agreement-dialog"
        :visible.sync="dialogVisible"
        :append-to-body="true"
        :close-on-press-escape="false"
        :close-on-click-outside="false"
        :close-on-click-modal="false"
        :width="'800px'"
        :title="emtitle"
        @closed="destroy"
    >
        <div class="dialog-container">
            <div class="registration-agreement-renew" v-show="lifeS">
                <div v-show="isSub" style="height: 90%;">
                    <div class="registration" v-for="(item,index) in dataShow" :key="index">
                        <div  class="head">
                            <h1>{{ item.title }}</h1>
                        </div>
                        <div class="content" ref="container" @scroll="handleScroll">
                            <div class="html" v-html="item.content"></div>
                            <div class="pdf" v-show="item.pdf">
                                <div class="top">
                                    <span class="fx-icon-fujian"></span>
                                    <span>{{ $t("附件") }}</span>：
                                    {{ item.attachment.length }}
                                    <span>{{ $t("个") }}</span>
                                </div>
                                <div class="bot" v-for="(val,i) in item.attachment" :key="i">
                                    <div class="le">
                                        <span class="fx-icon-filetype-pdf"></span>
                                        <span class="filename">{{ val.filename }}</span><span>{{ '(' + bytesToKB(val.size) + 'KB' + ')' }}</span> 
                                    </div>
                                    <div class="rig">
                                        <span class="yl" @click.stop="getPath(val)">{{ $t("预览") }}</span>
                                        <span class="down"  :class="!item.isDown ? 'cant' : ''" @click.stop="download(val,item.isDown)">{{ $t("下载") }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="secret" v-show="isYs">
                                <fx-checkbox v-model="checked"></fx-checkbox>
                                <span>{{ privacy_statement }}</span>
                            
                            </div>
                            <div class="downAdd" v-show="isxy">
                                <span>{{ $t("crm.channelmanage.please") }}</span>
                                <span class="save" @click="saveFile">{{ $t("保存") }}</span>
                                <span>{{ $t("crm.channelmanage.stamp") }}</span>
                                <input type="file" name="files"  accept=".jpg,.docx,.pdf,.ai,.psd,.png,.gif,.bmp,.xlsx,.jpeg" @change="uploadFile">
                            </div>
                        </div>
                    </div>
                    <div class="new-content" v-show="pageNum -1 == currentPage && !isxy && qualificationFlag">
                        <div class="nimg">
                            <img src="../../../assets/images/newregist.png" alt="">
                        </div>
                        <p>{{ $t("vcrm.dlt.new.regist") }}</p>
                    </div>
                    <div slot="footer" class="dialog-footer bottom">
                        <div class="left"  v-show="currentPage !== 0 || isxy && provisionFlag">
                            <fx-button size="small" type="primary" @click="prePage">{{ $t("上一步") }}</fx-button>
                        </div>
                        <div class="right" :class="rolling ? 'not' : ''"  v-show="(!(pageNum -1 == currentPage) && !isxy && provisionFlag)">
                            <fx-button size="small" type="primary" @click="nextPage(rolling)">{{ $t("下一步") }}</fx-button>
                        </div>
                        <div class="right" v-show="pageNum -1 == currentPage && !isxy && qualificationFlag">
                            <fx-button size="small" type="primary" @click="perfect">{{ $t("crm.registration.mess") }}</fx-button>
                        </div>
                        <div class="right" v-show="isxy || (!provisionFlag && !qualificationFlag)">
                            <fx-button size="small" type="primary" @click="submitxy">{{ $t("确定") }}</fx-button>
                        </div>
                        <div class="right">
                            <fx-button size="small" type="plain" @click="oncael">{{ $t("取消") }}</fx-button>
                        </div>
                    </div>
                </div>
                <div v-show="!isSub" class="sub">
                    <div v-if="!newTitle" class="head"> {{ title }}</div>
                    <div v-if="newTitle" class="head">{{ newTitle }}</div>
                    <div class="content">
                        <div class="examineing" v-show="isExamine">
                            <div class="img">
                                <img src="../../../assets/images/examineing.png" alt="">
                            </div>
                            <p>{{ submintAgg }}</p>
                        </div>
                        <div class="nexamine" v-show="isNoExamine">
                            <div class="nimg">
                                <img src="../../../assets/images/nexamine.png" alt="">
                            </div>
                            <p class="no">{{ $t("crm.partner.yistop") }}</p>
                            <p class="plea" @click="please">{{ $t("crm.partnerobj.wanshan") }}</p>
                        </div>
                        <div class="success" v-show="isSuExamine">
                            <div class="img">
                                <img src="../../../assets/images/successmine.jpg" alt="">
                            </div>
                            <p>{{ successExamine }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </fx-dialog>
</template>
<script>
export default {
    props: {
        options: {
            type: Object,
            default: () => ({})
        }
    },
   
    data() {
        return {
            provisionFlag: false, // 是否显示规定
            qualificationFlag: false, // 是否显示资质完善
            dialogVisible: true,
            emtitle: $t("crm.channelguide.middle.browse"), // $t("crm.channelguide.middle.qualification")
            newTitle:'',
            newContent:'',
            fileList:[],
            accept:'',
            multiple:true,
            limit: 2,
            name:'',

            mustRead:false, //是否必须阅读
            hasScroll:false,//是否有滚动条
            pageNum:1, // 共几页
            currentPage:0, //默认展示第一页
            totalPage:[],
            dataShow:[], // 当前显示的数据
            registrationList:[],
            signSchemeId:'',
            provisionSchemeId:'',
            Id:'',
            apiName:'',
            isxy:false,
            isYs: false,
            privacy_statement:'',
            checked:false,
            isSub: true,
            title: $t("crm.partnerobj.qianshu"),
            submintAgg:'',
            successExamine:$t("crm.partnerobj.successEx"),
            isExamine: true,
            isNoExamine:false,
            isSuExamine:false,
            partnerAgreementId:"",
            agreement_attachment:[],
            lifeS: true,
            business_license:[],
            signing_statusdq:'',
            life_statusdq:''
        };
    },
    computed:{
        rolling() {
            return this.mustRead && this.hasScroll
        }
    },
    methods: {
        oncael() {
            this.dialogVisible = false;
            this.$destroy();
        },
        provisionScheme() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/partner_management/service/channel_query_provision_scheme",
                        data: {
                            
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        async saveFile() {
            let res = await this.printAgree();
            this.agreement_attachment.push({
                "create_time": null,
                "size": res.data.fileSize,
                "filename": res.data.name + '.' + res.data.fileType,
                "ext": res.data.fileType,
                "path": res.data.path + '.' + res.data.fileType
            });
            this.download({
                filename: res.data.name + '.' + res.data.fileType,
                path: res.data.path + '.' + res.data.fileType
            },true)
        },
        printAgree() {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/prm_channel/service/print_agreement",
                        data: {
                            partnerAgreementId: _this.partnerAgreementId
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        uploadFile(event) {
            let _this = this;
        _this.business_license = [];
            let File = event.target.files[0];
            const formData = new FormData();
            formData.append('name', File.name);
            formData.append('file', File);
            FxUI.mediaApi.uploadFile(formData).then((res) => {
                let result = JSON.parse(res);
                _this.tradePath = result.TempFileName;
            _this.business_license.push({
                "create_time":File.lastModified,
                "size": File.size,
                "filename":File.name,
                "ext": File.type.split('/')[1],
                "path": result.TempFileName
            })
            })
        },
        please() {
            let _this = this;
            CRM.api.edit({
                apiname: _this.apiName,
                id: _this.Id,
                dataId:_this.Id,
                success() {
                    _this.dataShow = [];
                    _this.getAgreementData().then(res => {
                        _this.signSchemeId = res.data.signSchemeId;
                        if (res.data.agreementData && res.data.agreementData.containerDocument !== null) {
                            _this.isYs = res.data.agreementData.containerDocument.exists_privacy_statement;
                            _this.privacy_statement = res.data.agreementData.containerDocument.privacy_statement;
                            _this.partnerAgreementId = res.data.agreementData.containerDocument._id;
                            _this.isxy = true;
                            _this.isSub = true;
                            _this.dataShow.push({
                                title:res.data.agreementData.containerDocument.agreement_title,
                                content:res.data.agreementData.containerDocument.agreement_content,
                                pdf: res.data.agreementData.containerDocument.attachment.length ? true : false,
                                attachment:res.data.agreementData.containerDocument.attachment,
                                isDown: res.data.agreementData.containerDocument.attachment_downloadable
                            })
                        } else {
                            //todo
                        }
                    })
                }
            })
        },
        submitxy() {
            let _this = this;
            if(_this.isYs) {
                if(!_this.checked) {
                    CRM.util.alert($t("crm.please.check.content"));
                    return;
                }
            }
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/PartnerAgreementDetailObj/action/Renew",
                        data: {
                            objectDataId : _this.options.dataId,
                            trigger_info: {
                                "trigger_page": _this.options.type
                            },
                            submitAgreementItem:{
                                agreementId:  _this.partnerAgreementId,
                                signSchemeId: _this.signSchemeId,
                                valueMapping:{
                                    agreement_attachment:_this.business_license
                                }
                            },
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                let sign_status = res.Value.objectData.sign_status;
                                if(sign_status == "renewal") {
                                    let time = res.Value.objectData.signing_time;
                                    let formattedDate = '';
                                    if (time) {
                                        const date = new Date(time);
                                        const formatter = new Intl.DateTimeFormat('zh-CN', {
                                            year: 'numeric',
                                            month: '2-digit',
                                            day: '2-digit',
                                        });
                                        formattedDate = formatter.format(date).replace(/\//g, '-');
                                    }
                                    _this.dialogVisible = false;
                                    _this.$emit("confirm");
                                    let msg = $t("vcrm.dlt.agreement.endtime") + formattedDate
                                    _this.$alert(msg, $t("vcrm.dlt.agreement.success"), {
                                        confirmButtonText: $t("我知道了"),
                                        type: "success",
                                        callback: ()=> {
                                            
                                        }
                                    });
                                } else if (sign_status == 'pending_renewal') {
                                    if(res.Value.objectData.life_status == 'normal') {
                                        _this.lifeS = true;
                                        _this.isSub = true;
                                    } else if (res.Value.objectData.life_status == 'in_change') {
                                        _this.lifeS = true;
                                        _this.isSub = false;
                                        _this.isExamine = true;
                                        _this.emtitle = $t("审批中")
                                        _this.isNoExamine = false;
                                        _this.isSuExamine = false;
                                }
                            }
                                resolve(res.Value);
                                return;
                            } 
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        },
                       
                    },{ errorAlertModel: 1}
                );
            });

        },
        download(file,istf) {
            if (istf) {
                let url =  CRM.util.getFscLink(file.path, file.filename, true);
                location.href = url;
            }
        },
        isImage: function(name) {
            return /\.(?:tif|eps|png|gif|jpeg|jpg|dwg|ai|cdr|bmp|webp)$/i.test(name);
        },
        getPath(fileObj) {
            let obj = {
                fileId: 1,
                fileName: fileObj.filename || fileObj.path,
                filePath: fileObj.path
            }
            if(CRM.util.getUploadType && CRM.util.getUploadType(obj.fileName) === 'img') { //图片类型
                CRM.api.preview_image({
                    list: [{
		                bigUrl:	CRM.util.getFscLinkByOpt({
	                        id: obj.filePath,
	                        webp: true,
	                        name: obj.fileName
	                    }),
	                    originDownUrl: CRM.util.getFscLink(obj.filePath, obj.fileName, true)
	                }],
                    index: 0,
                    isNew: true,
                    zIndex: 15000,
                    hideDownAllLink: true
                })

                return;
            }
            if (CRM.util.getUploadType && CRM.util.getUploadType(obj.fileName) === 'video') { //视频类型
                CRM.api.preview_video({
                    fileName: fileObj.filename || fileObj.path,
                    filePath: fileObj.path})
                return;
            }
            seajs.use('base-modules/file-preview/index', function(FilePreview) {
                if(CRM.util.getFileExtText && !CRM.util.getFileExtText(obj.filePath)) {
                    let ext = CRM.util.getFileExtText(obj.fileName || '');
                    ext && (obj.filePath = obj.filePath + '.' + ext);
                }
                FilePreview.preview(obj);
            });
              
        },
        bytesToKB(val) {
            return Math.floor(val / 1024);
        },
        getProvisionData() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/prm_channel/service/match_provision_data",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        getAgreementData() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/prm_channel/service/match_agreement_data",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        handleScroll(event) {
            const { scrollTop, scrollHeight, clientHeight } = event.target;
            if (scrollTop + clientHeight >= scrollHeight - 10) {
                this.hasScroll = false;
            }
        },
        nextPage(val) {
            let _this = this;
            if (!val) {
                this.dataShow = [];
                // if(this.currentPage == this.pageNum -1) return;// todo
                this.dataShow.push(this.totalPage[++this.currentPage])
                this.$nextTick(()=>{
                    const scrollableDiv = this.$refs.container;
                    if (scrollableDiv[0].scrollHeight > scrollableDiv[0].clientHeight) {
                        this.hasScroll = true;
                    } else {
                        this.hasScroll = false;
                    }
                })
                //如果没有资质完善 下一步最后一步需要调协议接口
                if(!this.qualificationFlag) {
                    if (this.currentPage == this.pageNum) {
                        this.emtitle = $t("crm.brow.content")
                         this.dataShow = [];
                         this.getAgreementData().then(res => {
                             this.signSchemeId = res.data.signSchemeId;
                            if (res.data.agreementData && res.data.agreementData.containerDocument !== null) {
                                _this.isYs = res.data.agreementData.containerDocument.exists_privacy_statement;
                                _this.privacy_statement = res.data.agreementData.containerDocument.privacy_statement;
                                _this.partnerAgreementId = res.data.agreementData.containerDocument._id;
                                _this.isxy = true;
                                _this.dataShow.push({
                                    title:res.data.agreementData.containerDocument.agreement_title,
                                    content:res.data.agreementData.containerDocument.agreement_content,
                                    pdf: res.data.agreementData.containerDocument.attachment.length ? true : false,
                                    attachment:res.data.agreementData.containerDocument.attachment,
                                    isDown: res.data.agreementData.containerDocument.attachment_downloadable
                                })
                            } else {
                            }
                        });
                    }
                }
            }
        },
        prePage() {
            let _this = this;
            if (_this.isxy) {
                CRM.api.edit({
                    apiname: _this.apiName,
                    id: _this.Id,
                    dataId:_this.Id,
                    success() {
                        _this.dataShow = [];
                        _this.getAgreementData().then(res => {
                            _this.signSchemeId = res.data.signSchemeId;
                            _this.isYs = res.data.agreementData.containerDocument.exists_privacy_statement;
                            _this.privacy_statement = res.data.agreementData.containerDocument.privacy_statement;
                            if (res.data.agreementData && res.data.agreementData.containerDocument !== null) {
                                _this.isxy = true;
                                _this.dataShow.push({
                                    title:res.data.agreementData.containerDocument.agreement_title,
                                    content:res.data.agreementData.containerDocument.agreement_content,
                                    pdf: res.data.agreementData.containerDocument.attachment.length ? true : false,
                                    attachment:res.data.agreementData.containerDocument.attachment,
                                    isDown:res.data.agreementData.containerDocument.attachment_downloadable
                                })
                            } else {
                                //todo
                            }
                        })
                    }
                })
            } else {
                _this.dataShow = [];
                if(_this.currentPage == 0) return;
                _this.dataShow.push(_this.totalPage[--_this.currentPage])
            }
        },
        perfect() {
            let _this = this;
            CRM.api.edit({
                apiname: _this.apiName,
                id: _this.Id,
                dataId:_this.Id,
                success() {
                    _this.dataShow = [];
                    _this.getAgreementData().then(res => {
                        _this.signSchemeId = res.data.signSchemeId;
                        if (res.data.agreementData && res.data.agreementData.containerDocument !== null) {
                            _this.isYs = res.data.agreementData.containerDocument.exists_privacy_statement;
                            _this.privacy_statement = res.data.agreementData.containerDocument.privacy_statement;
                            _this.partnerAgreementId = res.data.agreementData.containerDocument._id;
                            _this.isxy = true;
                            _this.emtitle = $t("crm.brow.content")
                            _this.dataShow.push({
                                title:res.data.agreementData.containerDocument.agreement_title,
                                content:res.data.agreementData.containerDocument.agreement_content,
                                pdf: res.data.agreementData.containerDocument.attachment.length ? true : false,
                                attachment:res.data.agreementData.containerDocument.attachment,
                                isDown: res.data.agreementData.containerDocument.attachment_downloadable
                            })
                        } else {
                            //todo
                        }
                    })
                }
            })
        },
        getId() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/prm_channel/service/query_admission_data",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        queryLife() {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:`/EM1HNCRM/API/v1/object/${_this.apiName}/controller/WebDetail`,
                        data: {
                            management: false,
                            objectDataId: _this.Id,
                            objectDescribeApiName: _this.apiName
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        //查询注册自定义文案
        queryRegisterText() {
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/prm_channel/service/fetch_signing_text",
                        data: {},
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        },
        onSave() {
           this.dialogVisible = false;
        },
        onCancel() {
            this.dialogVisible = false;
        }, 
        destroy() {
            this.dialogVisible = false;
            this.$destroy();
        },
        fetchRenewPages() {
            let _this = this;
            return new Promise(function(resolve) {
                CRM.util.FHHApi({
                        url:"/EM1HNCRM/API/v1/object/prm_channel/service/fetch_renew_pages",
                        data: {
                            objectDataId: _this.options.dataId
                        },
                        success: function(res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                                return;
                            }
                            CRM.util.alert(
                                res.Result.FailureMessage ||
                                    $t("暂时无法获取数据") + "!"
                            );
                        }
                    },{ errorAlertModel: 1}
                );
            });
        }
    },
    async mounted() {
        let _this = this;
        let arrayFind = await _this.fetchRenewPages();
        let registerText = await _this.queryRegisterText();
        let res = await _this.getId()
        _this.Id = res.data.dataId;
        _this.apiName = res.data.objectDescribeApiName;
        _this.newTitle = _this.isSuExamine ? $t("crm.partnerobj.shenhe.success") : registerText?.data?.title || $t("crm.partnerobj.qianshu");
        _this.newContent = registerText?.data?.content || '';
        _this.submintAgg = _this.newContent ||  $t("crm.partnerobj.shenhe");
        let life = await _this.queryLife();
        if(life.data.signing_status == '' || life.data.signing_status ==  null) {
            this.lifeS = true
        } else if(life.data.signing_status == 'signed' || life.data.signing_status == 'renewal') {
            if(life.data.life_status == 'normal') {
                _this.lifeS = true;
                _this.isSub = false;
                _this.isExamine = false;
                _this.isNoExamine = false;
                _this.isSuExamine = true;
            }
        } else if (life.data.signing_status == 'pending_signature' || life.data.signing_status == 'pending_renewal') {
            if(life.data.life_status == 'normal') {
                _this.lifeS = true;
                _this.isSub = true;
            } else if (life.data.life_status == 'in_change') {
                _this.lifeS = true;
                _this.isSub = false;
                _this.isExamine = true;
                _this.emtitle = $t("审批中")
                _this.isNoExamine = false;
                _this.isSuExamine = false;
            }
        } else if (life.data.signing_status == 'reject') {
            if(life.data.life_status == 'normal') {
                _this.lifeS = true;
                _this.isSub = false;
                _this.isExamine = false;
                _this.isNoExamine = true;
                _this.isSuExamine = false;
            } else if (life.data.life_status == 'in_change') {
                _this.lifeS = true;
                _this.isSub = false;
                _this.isExamine = true;
                _this.isNoExamine = false;
                _this.isSuExamine = false;
            }
        }
        if (arrayFind.data.indexOf("provision_page") > -1) { 
            _this.provisionFlag = true;
        }
        if (arrayFind.data.indexOf("qualification_page") > -1) { 
            _this.qualificationFlag = true;
        }
        if(_this.qualificationFlag && !_this.provisionFlag) {
            _this.emtitle = $t("crm.registration.mess")
        }
        if(_this.provisionFlag) {
            let resut = await _this.getProvisionData();
            let museList = await _this.provisionScheme();
            _this.provisionSchemeId = resut.data.provisionSchemeId;
            let mustReadList = museList.provisionScheme.filter(item => item.matchSchemeId == _this.provisionSchemeId);
            _this.mustRead = mustReadList.length ? mustReadList[0].mustRead : false;
            resut.data.provisionDataList.forEach(item => {
                _this.registrationList.push({
                    title: item.containerDocument.provision_title,
                    content: item.containerDocument.provision_content,
                    pdf: item.containerDocument.attachment.length ? true : false,
                    attachment: item.containerDocument.attachment,
                    isDown: item.containerDocument.attachment_downloadable
                })
            })
            _this.pageNum = _this.registrationList.length || 1;
            for(let i = 0; i < this.pageNum;i ++) {
                _this.totalPage[i] = _this.registrationList[i]
            }
            _this.dataShow.push(this.totalPage[_this.currentPage])
            this.$nextTick(()=>{
                const scrollableDiv = this.$refs.container;
                if (scrollableDiv[0].scrollHeight > scrollableDiv[0].clientHeight) {
                    this.hasScroll = true;
                } else {
                    this.hasScroll = false;
                }
            })
        }
        //如果只有协议内容
        if (!_this.provisionFlag && !_this.qualificationFlag) {
            _this.dataShow = [];
            _this.emtitle = $t("crm.brow.content")
            _this.getAgreementData().then(res => {
                _this.signSchemeId = res.data.signSchemeId;
                if (res.data.agreementData && res.data.agreementData.containerDocument !== null) {
                    _this.isYs = res.data.agreementData.containerDocument.exists_privacy_statement;
                    _this.privacy_statement = res.data.agreementData.containerDocument.privacy_statement;
                    _this.partnerAgreementId = res.data.agreementData.containerDocument._id;
                    _this.isxy = true;
                    _this.dataShow.push({
                        title:res.data.agreementData.containerDocument.agreement_title,
                        content:res.data.agreementData.containerDocument.agreement_content,
                        pdf: res.data.agreementData.containerDocument.attachment.length ? true : false,
                        attachment:res.data.agreementData.containerDocument.attachment,
                        isDown: res.data.agreementData.containerDocument.attachment_downloadable
                    })
                } else {
                }
            })
        }
        
    }
};
</script>

<style lang="less">
.agreement-dialog {
    .el-dialog__title {
        font-weight: 700;
        font-size: 16px;
        height: 20px;
        line-height: 24px;
    }
    .el-dialog__body {
        padding: 0;
        .dialog-container {
            padding: 16px 24px;
        }
    }
    .registration-agreement-renew{
        height: calc(100vh - 220px);
        position: relative;
        .bottom {
            height: 50px;
            line-height: 50px;
            position: absolute;
            bottom: 0px;
            right: 0px;
            .left {
                display: inline-block;
                color: var(--color-info06);
                cursor: pointer;
                .fx-icon-arrow-left {
                    &::before {
                        color: var(--color-info06);
                    }
                }
            }
            .not {
                cursor: no-drop !important;
                color: var(--color-neutrals08) !important;
            }
            .right {
                display: inline-block;
                color: var(--color-info06);
                padding-left: 8px;
                cursor: pointer;
                .fx-icon-arrow-right {
                    &::before {
                        color: var(--color-info06);
                    }
                }
                .notr {
                    &::before {
                        color: var(--color-neutrals08) !important;
                    }
                }
            }
        }
        .sub {
            width: 100%;
            height: calc(100%);
            .head {
                text-align: center;
                margin: 0 48px;
                padding-bottom: 10px;
                height: 42px;
                line-height: 42px;
                font-size: 20px;
                font-weight: 500;
                color: var(--color-neutrals19);
                position: sticky;
                top: 0;
                border-bottom: 1px solid var(--color-neutrals05);
            }
            .content {
                margin-top: 150px;
                .nexamine {
                    .nimg {
                        display: flex;
                        justify-content: center;
                        img {
                            width: 375px;
                            height: 120px;
                        }
                    }
                    .no {
                        margin-top: 24px;
                        text-align: center;
                        color: var(--color-neutrals15);
                        font-size: 14px;
                        font-weight: 400;
                    }
                    .plea {
                        font-size: 14px;
                        font-weight: 400;
                        text-align: center;
                        color: var(--color-info06);
                        cursor: pointer;
                    }
                }
                .examineing,.success {
                    .img {
                        display: flex;
                        justify-content: center;
                        img {
                            width: 375px;
                            height: 120px;
                        }
                    }
                    p {
                        margin-top: 24px;
                        text-align: center;
                        color: var(--color-neutrals15);
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .new-content {
            p {
                text-align: center;
            }
        }
        .registration {
            width: 100%;
            height: calc(100% - 50px);
            .head {
                padding: 0 48px;
                height: 42px;
                line-height: 42px;
                font-size: 20px;
                font-weight: 500;
                color: var(--color-neutrals19);
                position: sticky;
                top: 0;
            }
            .content {
                margin: 16px 48px 0 48px;
                border-top: 1px solid var(--color-neutrals05);
                padding: 16px 0px;
                height: calc(100% - 120px);
                overflow-y: auto;
                .downAdd {
                    display: flex;
                    margin-top: 12px;
                    height: 24px;
                    line-height: 24px;
                    .save {
                        margin-right: 24px;
                    }
                    .file,.save {
                        border-radius: 4px;
                        display:inline-block;
                        cursor: pointer;
                        padding: 0 8px;
                        background: var(--color-special01);
                    }
                }
                .secret {
                    background: var(--color-neutrals02);
                    margin-top: 16px;
                    padding: 10px;
                    .el-checkbox {
                        margin-right: 0px !important;
                    }
                    span {
                        font-size: 12px;
                        font-weight: 400;
                        color: var(--color-neutrals19);
                    }
                }
                .pdf {
                    width: 626px;
                    border-radius: 4px;
                    margin-top: 2px;
                    padding: 0 8px;
                    border: 1px solid var(--color-neutrals05);
                    .top,.bot{
                        height: 40px;
                        line-height: 40px;
                    }
                    .top {
                        .fx-icon-fujian {
                            &::before {
                                color: var(--color-special02);
                            }
                        }
                    }
                    .bot {
                        border-top: 1px solid var(--color-neutrals05);
                        .le {
                            float: left;
                            .filename {
                                margin-right: 4px;
                            }
                        }
                        .rig {
                            float: right;
                            .yl,.down {
                                cursor: pointer;
                                color: var(--color-info06);
                            }
                            .cant {
                                cursor: no-drop;
                                color: var(--color-neutrals08);
                            }
                        }
                    }
                }
            }
            
        }
   }
}
</style>
