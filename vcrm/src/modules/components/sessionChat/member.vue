<template>
    <div class="member-list-panel">
        <div v-for="company in data" :key="company.company_name">
            <div class="company-name">{{company.company_name}}</div>
            <div>
                <div class="member-item" v-for="member in company.data" :key="member.name">
                    <img :src="member.avatar">
                    <div>
                        <div class="member-item-name" :title="member.name">{{member.name}}</div>
                        <div v-if="member.tag" :class="['member-item-tag', member.tag.color]" :title="member.tag.name">{{member.tag.name}}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </template>
  
  <script>

    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
        },
        data() {
            return {
                
            }
        },
        mounted() {
            
        },
        methods: {
          
        },
        components: {
          
        }
    }
  </script>
  
  <style lang='less' scoped>
  .member-list-panel {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    .company-name {
        padding: 10px 20px;
        background: var(--color-neutrals03);
        color: #999;
        font-size: 16px;
    }
    .member-item {
        display: inline-flex;
        align-items: center;
        margin: 20px;
        font-size: 0;
        position: relative;
        img {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            margin-right: 10px;
        }
        .member-item-name {
            width: 104px;
            font-size: 14px;
            color: #333;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .member-item-tag {
            font-size: 12px;
            width: 104px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            &.blue {
                background: var(--color-info01);
                color: var(--color-info06);
                display: inline-block;
                width: auto;
            }
            &.green {
                color: var(--color-success06);
            }
            &.orange {
                color: #FF8214;
            }
        }
    }
  }
</style>
