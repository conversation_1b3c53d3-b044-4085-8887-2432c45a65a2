<template>
    <div class="biz-full-life-card-wraper">
        <div v-if="!bizDetailIsShow" style="width: 100%;">
            <slot name="header"></slot>
            <div class="biz-full-life-card-content">
                <div
                    v-for="card in bizCardList"
                    :key="card.id"
                    class="biz-full-life-card-item"
                    @click="handleCardClick(card.id)"
                >
                    <div class="biz-card-title">
                        <span :class="card.icon" style="color:#C1C5CE;"></span>
                        <span>{{ card.title }}</span>
                    </div>
                    <div class="biz-card-desc">
                        {{ card.description }}
                    </div>
                    <fx-progress
                        :percentage="card.percentage"
                        :format="format"
                        color="#63c47d"
                        class="biz-card-progress"
                    ></fx-progress>
                    <div class="biz-card-btn-wrapper">
                        <fx-link 
                            type="standard" 
                            size="small" 
                            @click="handleCardClick(card.id)">
                            {{ $t('去配置') }}
                        </fx-link>
                    </div>
                </div>
            </div>
        </div>

        <div style="width: 100%;" v-else>
            <biz-full-life-card-detail
                ref="bizDetailCardDetail"
                :current-nav-id.sync="bizCurrentNavId"
                :keys-map="bizKeysStatusMap"
                :percentage-map="bizPercentageMap"
                @go-back="handleBack"
                @progress-update="handleProgressUpdate"
            ></biz-full-life-card-detail>
        </div>
    </div>
</template>

<script>
import BizFullLifeCardDetail from './detail.vue';
import  { businessConfig } from './dhtDms'
export default {
    name: ' BizFullLifeCard',
    components: {
        BizFullLifeCardDetail,
    },
    data() {
        return {
            bizDetailIsShow: false,
            bizCurrentNavId: '',
            bizGroup: businessConfig.bizGroup,
            bizKeys: businessConfig.bizKeys,
            bizCardList: businessConfig.bizCardList,
            bizPercentageMap: {},
            bizKeysStatusMap: {},
        }
    },
    created() {
        this.apiGetBizkeys();
        this.handleInitProgress();
    },
    methods: {

        format(percentage) {
            return percentage === 100 ? '100%' : `${percentage}%`;
        },

        handleCardClick(cardId) {
            this.bizCurrentNavId = cardId;
            this.bizDetailIsShow = true;
        },

        handleBack() {
            this.bizCurrentNavId = '';
            this.bizDetailIsShow = false;
        },

        handleProgressUpdate({ parentId,id, status}) {
            this.bizKeysStatusMap[id] = status;
            this.handleSubTotalProgress(this.bizKeysStatusMap);
        },

        handleInitProgress() {
            const savedProgress = {}
            this.bizCardList.forEach(card => {
                if (savedProgress[card.id]) {
                    card.percentage = savedProgress[card.id].percentage;
                }
            });
            this.handleDirectlyDisplayDetail();
        },

        handleSubTotalProgress(map) {
            const percentageMap = {};
            this.bizKeysStatusMap = map;
            this.bizCardList.forEach(card => {
                let completed = 0;
                let total = card.childKeys.length;
                card.childKeys.forEach(key => {
                    if (map[key] == 'completed') {
                        completed++;
                    }
                });
                card.percentage = completed > 0 ? Math.floor((completed / total) * 100) : 0;
                percentageMap[card.id] = card.percentage;
            });
            this.bizPercentageMap = percentageMap;
            this.$refs['bizDetailCardDetail'] && this.$refs['bizDetailCardDetail'].setPercentageMap(this.bizPercentageMap,this.bizKeysStatusMap);
        },

        handleDirectlyDisplayDetail(name) {
            const tab = sessionStorage.getItem('dmsjumpTab');
            if(tab) {
                sessionStorage.removeItem('dmsjumpTab');
                this.bizDetailIsShow = true;
                this.handleCardClick(tab);
            }
        },

        apiGetBizkeys() {
            let that = this;
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: "/EM1HNCRM/API/v1/object/biz_operation_data/service/query_by_keys",
                    data: {
                        bizGroup: this.bizGroup,
                        bizKeys: this.bizKeys
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            const map = {};
                            that.bizKeys.forEach((key, index) => {
                                let value = res.Value.bizOperationList.find(item => item.bizKey === key);
                                if (value) {
                                    value = value.bizValue;
                                } else {
                                    value = 'pending';
                                }
                                map[key] = value;
                            });
                            // 计算外边总卡片进度
                            that.handleSubTotalProgress(map);
                            resolve(map);
                        } else {
                            CRM.util.alert(res.Result.FailureMessage);
                            reject();
                        }
                    }
                }, { errorAlertModel: 1 })
            })
        }
    }
}
</script>

<style lang="less" scoped>
.biz-full-life-card-wraper {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
    /deep/.el-progress {
        display: flex;
        align-items: center;
        justify-content: space-around;
    }
    .biz-full-life-card-content {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        box-sizing: border-box;
        display: grid;
        grid-template-columns: repeat(auto-fill, 370px);
        justify-content: center;
        grid-column-gap: 16px;
        grid-row-gap: 16px;
        .biz-full-life-card-item:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .biz-full-life-card-item {
            width: 370px;
            padding: 24px;
            border-radius: 8px;
            background: #FFF;
            box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.05);
            box-sizing: border-box;
            cursor: pointer;

            .biz-card-title {
                display: flex;
                align-items: center;
                font-weight: 700;
                span {
                    font-size: 20px;
                    margin-right: 8px;
                }
                span:before{
                    color: #737C8C;
                }
            }
            .biz-card-desc {
                min-height: 80px;
                font-size: 12px;
                color: var(--color-neutrals15);
                margin-top: 12px;
            }
            .biz-card-progress {
                margin: 16px 0;
            }
            .biz-card-btn-wrapper{
                display: flex;
                justify-content: flex-end;
                margin: auto;
            }
        }
    }
}
</style>