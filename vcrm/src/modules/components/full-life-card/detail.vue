<template>
    <div class="biz-full-life-card-detail" v-if="bizDetailIsReady">
        <div class="biz-detail-wrapper">
            <!-- 左侧导航 -->
            <div class="biz-detail-nav">
                <div class="biz-detail-back-btn">
                    <fx-link :underline="false" icon="fx-icon-arrow-left" size="small" @click="handleBack">{{ biz_detail_btn_back }}</fx-link>
                </div>
                <div class="biz-detail-nav-list">
                    <div v-for="(item, index) in bizDetailNavItems" :key="item.id" class="biz-detail-nav-item"
                        :class="{active: bizDetailActiveNavId === item.id}" @click="handleNavClick(item.id)">
                        <span>{{ item.label }}</span>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区 -->
            <div class="biz-detail-content">
                <!-- 上部分：信息描述区域 -->
                <div class="biz-content-info-section">
                    <!-- 卡片信息 -->
                    <div class="card-info">
                        <div class="card-icon">
                            <span :class="bizDetailCurSteps.icon" style="color:#C1C5CE;"></span>
                        </div>
                        <div class="card-content">
                            <div class="card-title">{{ bizDetailCurSteps.title }}</div>
                            <div class="card-desc">{{ bizDetailCurSteps.description }}</div>
                        </div>
                    </div>
                </div>

                <!-- 下部分：进度卡片区域 -->
                <div class="biz-content-progress-card">
                    <!-- 进度条 -->
                    <div class="progress-section">
                        <div class="section-title">{{ biz_detail_progress }}</div>
                        <div class="progress-bar">
                            <fx-progress :percentage="bizDetailPercentageMap[currentNavId]" stroke-width="12" color="#63c47d"></fx-progress>
                        </div>
                    </div>

                    <!-- 卡片列表 -->
                    <div class="card-list">
                        <!-- 循环渲染卡片 -->
                        <div v-for="card in bizDetailCurSteps.cards" :key="card.id" class="setting-card" style="display: flex;flex-direction: column;">
                            <div style="display: flex;align-items: center;justify-content: space-between;">
                                <div class="card-header" style="flex-direction: column;">
                                    <div class="card-title">
                                        {{ card.title }}
                                    </div>
                                    <div class="card-content">
                                        {{ card.description }}
                                        <fx-link 
                                            type="standard" 
                                            v-if="card.isLink" 
                                            size="small" 
                                            @click="handleClickLink(card.link)">
                                            {{ card.link.name }}
                                        </fx-link>
                                    </div>
                                </div>
                                <div class="card-actions">
                                    <!-- 标记完成按钮 -->
                                    <fx-button v-if="bizDetailKeysStatusMap[card.id] == 'completed'" type="plain" class="action-button"
                                        icon="fx-icon-ok-2" @click="handleToggleCardStatus(card.id)" size="mini">
                                        {{ biz_detail_finished }}
                                    </fx-button>

                                    <fx-button v-else type="plain" size="mini" class="action-button"
                                        @click="handleToggleCardStatus(card.id)">
                                        {{ biz_detail_mark_as_completed }}
                                    </fx-button>
                                </div>
                            </div>
                            <div class="card-button" style="margin-top:10px">
                                <fx-button
                                    v-if="card.isButton"
                                    size="mini"
                                    type="primary"
                                    plain
                                    @click="handleClickBtn(card.button)">
                                {{ biz_detail_go_to_open }}</fx-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import  { businessConfig } from './dhtDms'
export default {
    name: 'BizFullLifeCardDetail',
    props: {
        currentNavId: {
            type: String,
            required: true
        },
        keysMap: {
            type: Object,
            default: () => ({}),
            required: true
        },
        percentageMap: {
            type: Object,
            default: () => ({}),
            required: true
        }
        
    },
    data() {
        return {
            // 当前激活的导航ID
            bizDetailActiveNavId: '',
            bizDetailIsReady: false,
            // 当前步骤数据
            bizDetailCurSteps: {},
            // 进度
            bizDetailPercentageMap: {},
            // 按钮（完成->标记已完成）
            bizDetailKeysStatusMap: {},
            // 按钮文本，业务侧可自定义
            biz_detail_go_to_open: businessConfig.bizDetailGoToOpen,
            biz_detail_finished: businessConfig.bizDetailFinished,
            biz_detail_mark_as_completed: businessConfig.bizDetailMarkAsCompleted,
            biz_detail_progress: businessConfig.bizDetailProgress,
            biz_detail_btn_back: businessConfig.bizDetailBtnBack,
            // 进度数据
            bizGroup: businessConfig.bizGroup,
            bizDetailNavItems: businessConfig.bizDetailNavItems,
            bizDetailProgressData: businessConfig.bizDetailProgressData,
        };
    },
    mounted() {
        this.bizDetailKeysStatusMap = JSON.parse(JSON.stringify(this.keysMap));
        this.bizDetailPercentageMap = JSON.parse(JSON.stringify(this.percentageMap));
        this.bizDetailIsReady = true;
        this.bizDetailRenderBefore();
    },
    watch: {
        currentNavId: {
            immediate: true,
            handler(val) {
                this.bizDetailActiveNavId = val;
                const currentNav = this.bizDetailNavItems.find(item => item.id === val);
                this.bizDetailCurSteps = this.bizDetailProgressData.find(item => item.id === val);
               
                this.bizDetailProgressData.forEach(item => {
                    item.cards.forEach(card => {
                        card.status = 'pending';
                        card.percentage = 0;
                    });
                });
            }
        }
    },
    methods: {
        bizDetailRenderBefore() {
           businessConfig.bizDetailRenderBefore && businessConfig.bizDetailRenderBefore(this.bizDetailProgressData);
        },

        setPercentageMap(percentageMap,keysMap) {
            this.bizDetailPercentageMap = JSON.parse(JSON.stringify(percentageMap));
            this.bizDetailKeysStatusMap = JSON.parse(JSON.stringify(keysMap));
        },

        handleClickLink(data) {
            window.open(data.url, '_blank');
        },

        handleClickBtn(data) {
            window.open(data.url, '_blank');
        },

        handleNavClick(navId) {
            this.$emit('update:currentNavId', navId);
            this.bizDetailCurSteps = this.bizDetailProgressData.find(item => item.id === navId);
        },

        handleBack() {
            this.$emit('go-back');
        },

        handleToggleCardStatus(cardId) {
            const card = this.bizDetailCurSteps.cards.find(card => card.id === cardId);
            if (card) {
                const status = this.bizDetailKeysStatusMap[cardId] === 'completed' ? 'pending' : 'completed';
                this.setValue({
                    bizGroup: this.bizGroup,
                    bizKey: card.id,
                    bizValue: status
                })
                this.bizDetailKeysStatusMap[cardId] = status;
                this.$emit('progress-update', {
                    parentId: this.bizDetailActiveNavId,
                    id: card.id,
                    status,
                });
            }
        },

        setValue(data) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: "/EM1HNCRM/API/v1/object/biz_operation_data/service/set_value",
                    data,
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                        } else {
                            CRM.util.alert(res.Result.FailureMessage);
                            reject();
                        }
                    }
                }, { errorAlertModel: 1 })
            })
        }
    }
};
</script>

<style lang="less" scoped>
.biz-full-life-card-detail {
    width: 100%;
    height: 100%;
    position: relative;
    background-color: transparent;
    /deep/.el-link [class*=fx-icon-]+span{
        margin-left: 0;
    }
    /deep/.el-progress {
        display: flex;
        align-items: center;
        justify-content: space-around;
    }
    /deep/.el-progress-bar{
        padding-right: 50px;
    }
    .biz-detail-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        background-color: transparent;
        gap: 12px;
        position: relative;
    }
    .biz-detail-nav {
        width: 200px;
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        padding: 12px;
        flex-shrink: 0; // 防止被压缩
        position: sticky;
        top: 0;
        display: flex;
        flex-direction: column;

        .biz-detail-back-btn {
            margin-bottom: 8px;
        }

        .biz-detail-nav-list {
            flex: 1;
            .biz-detail-nav-item {
                padding: 12px 16px;
                border-radius: 8px;
                cursor: pointer;

                span {
                    font-size: 14px;
                }

                &:hover {
                    background-color: #F5F5F5;
                }

                &.active {
                    background-color: #FFF7E6;
                    color: var(--color-primary);
                    border-left: 2px solid var(--color-primary);
                }
            }
        }
    }
    .biz-detail-content {
        flex: 1;
        min-height: calc(100vh - 230px);
        overflow-y: auto;
        display: flex;
        flex-direction: column;
        scrollbar-width: thin; 
        scrollbar-color: transparent transparent;
        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background-color: transparent;
            border-radius: 4px;
            transition: background-color 0.3s ease;
        }
        &::-webkit-scrollbar-track {
            background-color: transparent;
        }
        &:hover::-webkit-scrollbar-thumb,
        &:active::-webkit-scrollbar-thumb {
            background-color: #E0E0E0;
        }
        &:hover,
        &:active {
            scrollbar-color: #E0E0E0 transparent; // Firefox
        }
        .biz-content-info-section {
            padding: 20px 12px;
            border-radius: 8px;
            background-color: #fff;
            margin-bottom: 12px;
            .card-info {
                display: flex;
                align-items: flex-start;

                .card-icon {
                    margin-right: 8px;
                    height: 48px;
                    width: 48px;
                    display: flex;
                    align-items: center;
                    background: #D3E3FD;
                    border-radius: 4px;
                    justify-content: center;
                    span:before{
                        font-size: 20px;
                        color: #368DFF;
                    }
                }

                .card-content {
                    flex: 1;
                    .card-title {
                        font-size: 16px;
                        font-weight: 700;
                        color: #333;
                    }

                    .card-desc {
                        color: #666;
                        font-size: 12px;
                        line-height: 1.6;
                    }
                }
                .card-button{
                    margin-top: 3px;
                }
            }
        }
        .biz-content-progress-card {
            flex: 1;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            overflow: visible;
            .progress-section {
                margin-bottom: 24px;

                .section-title {
                    font-size: 16px;
                    font-weight: 700;
                    margin-bottom: 16px;
                }
            }
            // 卡片列表样式
            .card-list {
                overflow: visible; // 允许内容溢出，由父容器控制滚动
                .setting-card {
                    padding: 16px;
                    border: 1px solid #E8E8E8;
                    border-radius: 8px;
                    margin-bottom: 16px;
                    transition: all 0.3s ease;

                    &:hover {
                        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
                    }
                    &:last-child {
                        margin-bottom: 0;
                    }
                    .card-header {
                        display: flex;
                        

                        .card-title {
                            font-size: 14px;
                            font-weight: 700;
                        }

                        .card-actions {
                            display: flex;
                            align-items: center;

                            .action-text {
                                font-size: 14px;
                                color: #666;
                                margin-right: 16px;
                            }

                            .action-button {
                                margin-left: 8px;
                            }
                        }
                    }
                    .card-content {
                        color: #999;
                        font-size: 14px;
                        line-height: 1.5;
                        line-height: 17px;
                        margin-top: 4px;
                    }
                }
            }
        }
    }
}
</style>
