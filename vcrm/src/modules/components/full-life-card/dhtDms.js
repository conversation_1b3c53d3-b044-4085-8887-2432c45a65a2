
export const businessConfig = {
    bizGroup: 'dhtDms',
    bizKeys: [
        'customer_type', 
        'channel_level', 
        'channel_recruit', 
        'price_management_config', 
        'promotion_and_rebate_config', 
        'price_policy', 
        'order_config', 
        'channel_distribution_multilevel_ordering', 
        'inventory_management',
        'channel_reconciliation', 
        'demand_plan', 
        'partner_school', 
        'knowledge_base', 
        'notification_and_announcement',
        'budget_management',
        'rebate_policy',
        'notification_and_announcement',
        'bi_cockpit',
        'bi_report'
    ],
    bizCardList: [
        {
            id: 'setting',
            title: $t('dht_dms_channel_setup'),
            description: $t('dht_dms_channel_setup_desc'),
            icon: "fx-icon-f-obj-app156",
            percentage: 0,
            childKeys:['customer_type', 'channel_level', 'channel_recruit'],
        },
        {
            id: 'operation',
            title: $t('dht_dms_channel_perations'),
            description: $t('dht_dms_channel_perations_desc'),
            icon: 'fx-icon-f-obj-app403',
            percentage: 0,
            childKeys:['price_management_config', 'promotion_and_rebate_config'],
        },
        {
            id: 'trade',
            title: $t('dht_dms_channel_transaction'),
            description: $t('dht_dms_channel_transaction_desc'),
            icon: 'fx-icon-f-obj-app314',
            percentage: 0,
            childKeys:['order_config', 'inventory_management', 'channel_reconciliation'],
        },
        {
            id: 'function',
            title: $t('dht_dms_channel_mpowerment'),
            description: $t('dht_dms_channel_mpowerment_desc'),
            icon: 'fx-icon-f-book',
            percentage: 0,
            childKeys:['partner_school', 'knowledge_base', 'notification_and_announcement']
        },
        {
            id: 'reward',
            title: $t('dht_dms_channel_incentive'),
            description: $t('dht_dms_channel_incentive_desc'),
            icon: 'fx-icon-f-obj-app305',
            percentage: 0,
            childKeys:['budget_management', 'rebate_policy']
        },
        {
            id: 'analysis',
            title: $t('dht_dms_channel_analysis'),
            description: $t('dht_dms_channel_analysis_desc'),
            icon: 'fx-icon-f-report',
            percentage: 0,
            childKeys:['bi_cockpit','bi_report']
        }
    ],
    bizDetailNavItems: [
        { id: 'setting', label: $t('dht_dms_channel_setup')},
        { id: 'operation', label: $t('dht_dms_channel_perations') },
        { id: 'trade', label: $t('dht_dms_channel_transaction') },
        { id: 'function', label: $t('dht_dms_channel_mpowerment')},
        { id: 'reward', label: $t('dht_dms_channel_incentive') },
        { id: 'analysis', label:  $t('dht_dms_channel_analysis') }
    ],
    bizDetailProgressData:[
        {
            id:'setting',
            percentage: 0,
            icon: "fx-icon-f-obj-app156",
            title: $t('dht_dms_channel_setup'),
            description: $t('dht_dms_channel_setup_desc'),
            cards: [
                {
                    id: 'customer_type',
                    title: $t('dht_dms_channel_type'),
                    status: 'pending',
                    description: $t('dht_dms_channel_type_desc'),
                    isLink: true,
                    link: {
                        url: `/XV/UI/manage#crmmanage/=/module-sysobject/api_name-AccountObj/child_type-record`,
                        name: $t('dht_dms_maintenance_channel_cype')
                    }
                },
                {
                    id: 'channel_level',
                    title: $t('dht_dms_channel_level'),
                    status: 'pending',
                    description: $t('dht_dms_channel_level_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/manage#crmmanage/=/module-customerrule',
                        name: $t('dht_dms_maintain_channel_hierarchy')
                    }
                },
                {
                    id: 'channel_recruit',
                    title: $t('dht_dms_channel_recruitment'),
                    status: 'pending',
                    description: $t('dht_dms_channel_recruitment_desc'),
                    isButton: true,
                    button: {
                        url: '/XV/UI/manage#crmmanage/=/module-channel-home',
                    }
                }
            ]
        },
        {
            id: 'operation',
            percentage: 0,
            icon: 'fx-icon-f-obj-app403',
            title: $t('dht_dms_channel_perations'),
            description: $t('dht_dms_channel_perations_desc'),
            cards: [
                {
                    id: 'price_management_config',
                    title: $t('dht_dms_price_management_configuration'),
                    status: 'pending',
                    description: $t('dht_dms_price_management_configuration_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/manage#crmmanage/=/module-pricemanage',
                        name: $t('去配置')
                    }
                },
                {
                    id: 'promotion_and_rebate_config',
                    title: $t('促销与返利配置'),
                    status: 'pending',
                    description: $t('dht_dms_promotion_and_rebate_config_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/manage#crmmanage/=/module-promotionrebate',
                        name: $t('去配置')
                    }
                }
            ]
        },
        {
            id: 'trade',
            percentage: 0,
            icon: 'fx-icon-f-obj-app314',
            title: $t('dht_dms_channel_transaction'),
            description: $t('dht_dms_channel_transaction_desc'),
            cards: [
                {
                    id: 'order_config',
                    title: $t('dht_dms_order_config'),
                    status: 'pending',
                    description: $t('dht_dms_order_config_desc'),
                    isButton: true,
                    button: {
                        url: '/XV/UI/manage#crmmanage/=/module-approval',
                    }
                },
                {
                    id: 'inventory_management',
                    title: $t('dht_dms_inventory_management'),
                    status: 'pending',
                    description: $t('dht_dms_inventory_management_desc'),
                    isButton: true,
                    button: {
                        url: '/XV/UI/manage#admin/inventory-management/stock',
                    }
                },
                {
                    id: 'channel_reconciliation',
                    title: $t('dht_dms_channel_reconciliation'),
                    status: 'pending',
                    description: $t('dht_dms_channel_reconciliation_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/manage#crmmanage/=/module-transactionstatement',
                        name: $t('去配置')
                    }

                }
            ]
        },
        {
            id: 'function',
            percentage: 0,
            icon: 'fx-icon-f-book',
            title: $t('dht_dms_channel_mpowerment'),
            description: $t('dht_dms_channel_mpowerment_desc'),
            cards: [
                {
                    id: 'partner_school',
                    title: $t('dht_dms_partner_school'),
                    status: 'pending',
                    description: $t('dht_dms_partner_school_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/manage#app/train/index/=/trainType-1',
                        name: $t('去配置')
                    }
                },
                {
                    id: 'knowledge_base',
                    title: $t('dht_dms_knowledge_base'),
                    status: 'pending',
                    description: $t('dht_dms_knowledge_base_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/Home#paasapp/knowledge_setting/=/appId_FSAID_989ace',
                        name: $t('去配置')
                    }
                },
                {
                    id: 'notification_and_announcement',
                    title: $t('dht_dms_notification_and_announcement'),
                    status: 'pending',
                    description: $t('dht_dms_notification_and_announcement_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/Home#app/wechatconnect/releasenotice/=/param-%7B"appId"%3A"FSAID_11490c83"%7D',
                        name: $t('去配置')
                    }
                }
            ]
        },
        {
            id:'reward',
            percentage: 0,
            icon: 'fx-icon-f-obj-app305',
            title: $t('dht_dms_channel_incentive'),
            description: $t('dht_dms_channel_incentive_desc'),
            cards: [
                {
                    id: 'budget_management',
                    title: $t('dht_dms_budget_management'),
                    status: 'pending',
                    description: $t('dht_dms_budget_management_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/manage#crmmanage/=/module-tpm',
                        name: $t('去配置')
                    }
                },
                {
                    id:'rebate_policy',
                    title: $t('dht_dms_rebate_policy'),
                    status: 'pending',
                    description: $t('dht_dms_rebate_policy_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/manage#crmmanage/=/module-promotionrebate',
                        name: $t('去配置')
                    }
                }
            ]
        },
        {
            id: 'analysis',
            percentage: 0,
            icon: 'fx-icon-f-report',
            title: $t('dht_dms_channel_analysis'),
            description: $t('dht_dms_channel_analysis_desc'),
            cards: [
                {
                    id: 'bi_cockpit',
                    title: $t('dht_dms_bi_cockpit'),
                    status: 'pending',
                    description: $t('dht_dms_bi_cockpit_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/Home#bi/dashboard/=/dashboardId-BI_DSB_5cd2974bba6b0a00014bff70',
                        name: $t('去配置')
                    }
                },
                {
                    id: 'bi_report',
                    title: $t('dht_dms_bi_report'),
                    status: 'pending',
                    description: $t('dht_dms_bi_report_desc'),
                    isLink: true,
                    link: {
                        url: '/XV/UI/Home#paasapp/bi/list/=/appId_CRM',
                        name: $t('去配置')
                    }
                },
            ]
        },

    ],
    bizDetailBtnBack: $t('dht_dms_btn_back'),
    bizDetailProgress: $t('dht_dms_progress'),
    bizDetailFinished: $t('dht_dms_finished'),
    bizDetailMarkAsCompleted: $t('dht_dms_mark_as_completed'),
    bizDetailGoToOpen: $t('dht.init.footer.enable'),
    bizDetailRenderBefore: function (bizDetailProgressData) {
        const appid = window.location.origin.indexOf('fxiaoke') !== -1 ? 'FSAID_989ace' : 'FSAID_9897de';
        const cur = bizDetailProgressData.find(item => item.id === 'function');
        if (cur) {
            const zsData = cur.cards.find(item => item.id === 'knowledge_base');
            if(zsData) {
                zsData.link.url = `/XV/UI/Home#paasapp/knowledge_setting/=/appId_${appid}`;
            }
        }
    }
}

