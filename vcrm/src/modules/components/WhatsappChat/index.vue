<template>
    <div class="whatsapp-chat-container">
      <div class="head">
        <fx-tag effect="plain" size="mini" v-if="(type === 1)">{{$t('外部好友')}}</fx-tag>
        <fx-tag effect="plain" size="mini" v-else-if="(type === 2)">{{$t('员工')}}</fx-tag>
        <fx-tag effect="plain" size="mini" v-else-if="(type === 3)">{{$t('外部群')}}</fx-tag>
        <fx-tag effect="plain" size="mini" v-else-if="(type === 4)">{{$t('vcrm.internalGroup')}}</fx-tag>
        <span class="head-title">{{title}}</span>
      </div>

      <div class="filter-bar">
        <fx-tabs v-model="tabActiveName" @tab-click="tabHandleClick" >
          <fx-tab-pane v-for="item in tabList" :key="item.name" :label="item.label" :name="item.name" >
            <member v-if="tabActiveName === 'member'" :data="members" />
            <image-pane v-else-if="tabActiveName === 'image'" :data="images" @scrollHandle="scrollHandle" />
            <video-pane v-else-if="tabActiveName === 'video'" :data="videos" @scrollHandle="scrollHandle" />
            <file-pane v-else-if="tabActiveName === 'file'" :data="files" @scrollHandle="scrollHandle" />
            <link-pane v-else-if="tabActiveName === 'link'" :data="links" @scrollHandle="scrollHandle" />
            <mini-program-pane v-else-if="tabActiveName === 'miniProgram'" :data="miniPrograms" @scrollHandle="scrollHandle" />
            <audio-pane v-else-if="tabActiveName === 'audio'" :data="audios" @scrollHandle="scrollHandle" />
            <message v-else :data="message.data" :userId="message.userId" :scrollIntoDataId="scrollIntoDataId" @scrollHandle="scrollHandle" />
          </fx-tab-pane>
        </fx-tabs>

        <div class="filter-bar-search">
          <div  class="filter-bar-date-active" v-show="isDateActive" @click.stop>
            <fx-date-picker v-model="date" type="daterange" size="mini" :start-placeholder="$t('开始日期')" :end-placeholder="$t('结束日期')" :default-time="['00:00:00', '23:59:59']" :useLocaleFormat="true" format="yyyy-MM-dd" @change="dateOnChange"></fx-date-picker>
          </div>
          <div class="filter-bar-date-inactive" v-show="!isDateActive" @click.stop="isDateActive = true"><i class="fx-icon-calendar"></i></div>

          <div class="filter-bar-search-active" v-show="isSearchActive" @click.stop>
            <fx-input v-model="input" suffix-icon="el-icon-search" size="mini" :placeholder="$t('搜索')" @change="inputOnChange"></fx-input>
            <span class="result-count">{{searchCount.current}}/{{searchCount.total}}</span>
            <span class="navigator">
              <span :class="['previous', 'el-icon-caret-left', {'state-disabled': searchCount.current === 1 || this.searchCount.total === 0}]" @click="navigator('previous')"></span>
              <span :class="['next', 'el-icon-caret-right', {'state-disabled': searchCount.current === searchCount.total || this.searchCount.total === 0}]" @click="navigator('next')"></span>
            </span>
          </div>
          <div class="filter-bar-search-inactive" v-show="!isSearchActive" @click.stop="isSearchActive = true"><i class="el-icon-search"></i></div>
        </div>
      </div>
      

    </div>
  </template>
  
  <script>
    import message from './message/index.vue'
    import member from './member.vue'
    import imagePane from './imagePane.vue'
    import videoPane from './videoPane.vue'
    import filePane from './filePane.vue'
    import linkPane from './linkPane.vue'
    import miniProgramPane from './miniProgramPane.vue'
    import audioPane from './audioPane.vue'

    export default {
        props: {
          type: {
            type: Number,
            default: 1
          },
          title: {
            type: String,
            default: ''
          },
          members: {
            type: Array,
            default: []
          },
          images: {
            type: Array,
            default: []
          },
          videos: {
            type: Array,
            default: []
          },
          files: {
            type: Array,
            default: []
          },
          links: {
            type: Array,
            default: []
          },
          miniPrograms: {
            type: Array,
            default: []
          },
          audios: {
            type: Array,
            default: []
          },
          message: {
            type: Object,
            default: {}
          },
          searchCount: {
            type: Object,
            default: {}
          },
          scrollIntoDataId: {
            type: String,
            default: ''
          },
          putSearchInput: {
            type: String,
            default: ''
          },
          putSearchDate: {
            type: Array,
            default: []
          },
          //禁用某个tab
          disableTab: {
            type: Array,
            default: []
          }
        },
        watch: {
            putSearchInput(val) {
              this.isSearchActive = true;
              this.input = val;
              this.$emit('inputHandle', val);
            },
            putSearchDate(val) {
              this.isDateActive = true;
              this.date = val;
              this.$emit('dateHandle', val)
            },
        },
        data() {
            return {
              tabActiveName: 'all',
              isSearchActive: false,
              isDateActive: false,
              input: '',
              date: [],
              tabList: [
                { label: $t('聊天'), name: 'all' },
                // { label: $t('成员'), name: 'member' },
                { label: $t('图片'), name: 'image' },
                { label: $t('视频'), name: 'video' },
                { label: $t('文件'), name: 'file' },
                // { label: '链接', name: 'link' },
                { label: $t('小程序'), name: 'miniProgram' },
                { label: $t('语音'), name: 'audio' }
              ]
            }
        },
        mounted() {
          if (this.type === 1 || this.type === 2 || this.type === 9) {
            this.tabList.splice(1, 1);
          }
          if (this.disableTab.length > 0) {
            this.tabList = this.tabList.filter(item => !this.disableTab.includes(item.name));
          }
          document.documentElement.addEventListener('click', this.searchHide);
        },
        destroyed() {
          document.documentElement.removeEventListener('click', this.searchHide);
        },
        methods: {
          searchHide() {
            this.isSearchActive = false;
            this.isDateActive = false;
          },
          tabHandleClick(tab, event) {
            this.$emit('tabClickHandle', tab.name)
          },
          scrollHandle(position) {
            this.$emit('scrollHandle', position)
          },
          inputOnChange(val) {
            this.$emit('inputHandle', val)
          },
          navigator(direction) {
            if (this.searchCount.total === 0) return;
            if (direction === 'previous' && this.searchCount.current === 1) return;
            if (direction === 'next' && this.searchCount.current === this.searchCount.total) return;
            this.$emit('navigatorHandle', direction)
          },
          dateOnChange(val) {
            this.$emit('dateHandle', val)
          }
        },
        components: {
          message,
          member,
          imagePane,
          videoPane,
          filePane,
          linkPane,
          miniProgramPane,
          audioPane
        }
    }
  </script>
  
  <style lang='less' scoped>
  .whatsapp-chat-container {
    flex: 1;
    max-width: 1070px;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .head {
    padding: 12px 20px;
    .el-tag {
      margin-right: 8px;
    }
    .head-title {
      font-size: 16px;
      color: var(--color-neutrals19);
    }
  }
  .filter-bar {
    position: relative;
    flex: 1;
    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      .el-tab-pane {
        height: 100%;
        position: relative;
      }
    }
    .filter-bar-search {
      position: absolute;
      right: 10px;
      top: 0;
      background: var(--color-neutrals01);
      z-index: 100;
      display: flex;
      align-items: center;
      .el-date-editor {
        margin-right: 4px;
      }
      .filter-bar-search-inactive, .filter-bar-date-inactive {
        margin-left: 8px;
        width: 28px;
        height: 28px;
        border: 1px solid var(--color-neutrals05);
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .filter-bar-search-active {
        margin-left: 8px;
        .el-input {
          width: 198px;
        }
        .result-count {
          margin-right: 8px;
          color: #999;
        }
        .navigator {
          border-radius: 60px;
          overflow: hidden;
          border: 1px solid #ddd;
          cursor: pointer;
          display: inline-flex;
          .previous {
            border-right: 1px solid #ddd;
            &.state-disabled {
              color: #ccc;
            }
          }
          .next {
            &.state-disabled {
              color: #ccc;
            }
          }
        }
      }
    }
  }

  .whatsapp-chat-container /deep/ .el-tabs__content {
    flex: 1;
  }

  .whatsapp-chat-container /deep/ .el-tabs__nav-scroll {
    padding-left: 24px!important;
    padding-right: 122px;
  }

  .whatsapp-chat-container /deep/ .el-tabs__item {
    padding: 0 12px;
  }
  
</style>