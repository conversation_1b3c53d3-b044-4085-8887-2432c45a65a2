<template>
    <div ref="imagelistpanel" class="image-list-panel" @scroll.passive="fnHandleScroll($event)">
        <div class="image-list-panel-item" v-for="(image, index) in data" :key="image.id">
            <img :src="image.content | getFscLinkByOpt" @click="_preview(index)">
        </div>
    </div>
  </template>
  
  <script>

    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
        },
        watch: {
            data(val) {
                this.$nextTick(() => {
                    if (this.$refs.imagelistpanel.scrollHeight !== 0 && this.$refs.imagelistpanel.clientHeight !== 0 && this.$refs.imagelistpanel.scrollHeight === this.$refs.imagelistpanel.clientHeight) {
                        this.$emit('scrollHandle', 'bottom');
                    }
                });
            }
        },
        filters: {
            getFscLinkByOpt (val) {
                return FS.util.getFscLinkByOpt({
                    id: val.path + '.' + val.ext,
                    webp: true
                });
            }
        },
        data() {
            return {
                collapseList: []
            }
        },
        mounted() {
            
        },
        methods: {
            toggleList(index) {
                this.$set(this.collapseList, index, !this.collapseList[index])
            },
            fnHandleScroll: _.debounce(function(e) {
                console.log('滚动高',e.target.scrollTop)
                const {scrollTop, clientHeight, scrollHeight} = e.target;
                if(scrollTop + clientHeight === scrollHeight) {
                    //下拉刷新
                    this.$emit('scrollHandle', 'bottom');
                }
            },300),
            _preview (index) {
                FS.qxUtil.previewImg(this.data.map(({ content }) => ({
                    "previewPath": content.path + '.' + content.ext,
                    "originPath": content.path + '.' + content.ext,
                    "thumbPath": content.path + '.' + content.ext,
                    "fileName": content.name,
                    "fileSize": content.size,
                })), index);
            }
        },
        components: {
          
        }
    }
  </script>
  
  <style lang='less' scoped>
  .image-list-panel {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    padding-left: 10px;
    padding-top: 10px;
    &::before {
        content: "";
        display: table;
    }
    &::after {
        content: "";
        display: table;
        clear: both;
    }
    .image-list-panel-item {
        position: relative;
        margin: 0;
        padding: 10px 21px 10px 61px;
        border-bottom: 1px solid #F5F5F5;
        float: left;
        display: inline;
        padding-left: 2px;
        padding-bottom: 5px;
        margin-left: 0;
        margin-right: 0;
        margin-bottom: -1px;
        padding-right: 10px;
        img {
            width: 100px;
            height: 100px;
            cursor: pointer;
        }
    }
  }
</style>