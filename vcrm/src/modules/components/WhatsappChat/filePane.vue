<template>
    <div ref="filelistpanel" class="file-list-panel" @scroll.passive="fnHandleScroll($event)">
        <div class="file-list-panel-item" v-for="file in data" :key="file.id" @click="preview(file.content)">
            <p>
                <i :class="['file-icon', iconCls(file.content.ext)]"></i>
                <span class="name" :title="file.content.name">{{file.content.name}}</span>
                <span class="size">{{file.content.size | getFileSize}}</span>
            </p>
        </div>
    </div>
  </template>
  
  <script>

    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
        },
        watch: {
            data(val) {
                this.$nextTick(() => {
                    if (this.$refs.filelistpanel.scrollHeight !== 0 && this.$refs.filelistpanel.clientHeight !== 0 && this.$refs.filelistpanel.scrollHeight === this.$refs.filelistpanel.clientHeight) {
                        this.$emit('scrollHandle', 'bottom');
                    }
                });
            }
        },
        filters: {
            getFileSize (val) {
                return FS.crmUtil.getFileSize(val)
            }
        },
        data() {
            return {
                collapseList: []
            }
        },
        mounted() {
            
        },
        methods: {
            toggleList(index) {
                this.$set(this.collapseList, index, !this.collapseList[index])
            },
            iconCls(name) {
                return `file-item-${FS.util.getFileType({name:'.'+name},true,true)}`;
            },
            fnHandleScroll: _.debounce(function(e) {
                console.log('滚动高', e.target.scrollTop)
                const {scrollTop, clientHeight, scrollHeight} = e.target;
                if(scrollTop + clientHeight === scrollHeight) {
                    //下拉刷新
                    this.$emit('scrollHandle', 'bottom');
                }
            },300),
            preview(file) {
                if(file.isWhatsApp) {
                    //提示“由于Whatsapp会话内容加密，无法预览文件”
                    FxUI.Message.warning($t('crm.whatsappConversion.fileTip'));
                    return;
                }
                FS.qxUtil.previewDoc({
                    path: file.path + '.' + file.ext,
                    name: file.name
                });
            },
        },
        components: {
          
        }
    }
  </script>
  
  <style lang='less' scoped>
  .file-list-panel {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    padding-left: 10px;
    padding-top: 10px;
    .file-list-panel-item {
        position: relative;
        margin: 0;
        padding: 10px 21px 10px 61px;
        border-bottom: 1px solid #F5F5F5;
        padding-left: 46px;
        cursor: pointer;
        p {
            min-height: 18px;
            line-height: 18px;
            margin: 4px 0;
            .name {
                max-width: 280px;
                overflow: hidden;
                float: left;
                display: block;
                color: #333;
                font-size: 13px;
                white-space: nowrap;
                text-overflow: ellipsis;
                user-select: initial;
            }
            .size {
                margin: 0 10px;
                color: #999;
            }
            }
            .file-icon {
                overflow: hidden;
                position: absolute;
                left: 16px;
                width: 22px;
                height: 22px;
                display: inline-block;
                background: no-repeat;
                background-size: contain;
                vertical-align: middle;
            }
            .file-item-common {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/common.svg');
            }
            .file-item-txt {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/txt.svg');
            }
            .file-item-pdf {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/pdf.svg');
            }
            .file-item-doc {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/doc.svg');
            }
            .file-item-xls {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/xls.svg');
            }
            .file-item-ppt {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/ppt.svg');
            }
            .file-item-mp3 {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/mp3.svg');
            }
            .file-item-zip {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/zip.svg');
            }
            .file-item-jpg {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/jpg.svg');
            }
            .file-item-mov {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/mov.svg');
            }
            .file-item-mp4 {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/mov.svg');
            }
            .file-item-amr {
                background-image: url('https://a9.fspage.com/FSR/weex/avatar/object_form/images/mov.svg');
            }
    }
}
</style>