<template>
    <div ref="videolistpanel" class="video-list-panel" @scroll.passive="fnHandleScroll($event)">
        <div class="video-list-panel-item" v-for="video in data" :key="video.id" @click="preview(video.content)">
            <img :src="video.content | getFscLinkByOpt">
            <span class="duration">{{video.content.play_length | formatDuration}}</span>
        </div>
    </div>
  </template>
  
  <script>
    import dhtEmpty from '../../../assets/images/dht-empty.svg';

    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
        },
        watch: {
            data(val) {
                this.$nextTick(() => {
                    if (this.$refs.videolistpanel.scrollHeight !== 0 && this.$refs.videolistpanel.clientHeight !== 0 && this.$refs.videolistpanel.scrollHeight === this.$refs.videolistpanel.clientHeight) {
                        this.$emit('scrollHandle', 'bottom');
                    }
                });
            }
        },
        filters: {
            getFscLinkByOpt (val) {
                return  val.thumb_path ? FS.qxUtil.getFscLinkByOpt({
                    id: val.thumb_path,
                    webp: true
                }) : dhtEmpty;
            },
            formatDuration(val) {
                return val ? FS.qxUtil.formatSeconds(val) : val;
            },
        },
        data() {
            return {
                collapseList: []
            }
        },
        mounted() {
            
        },
        methods: {
            toggleList(index) {
                this.$set(this.collapseList, index, !this.collapseList[index])
            },
            fnHandleScroll: _.debounce(function(e) {
                console.log('滚动高',e.target.scrollTop)
                const {scrollTop, clientHeight, scrollHeight} = e.target;
                if(scrollTop + clientHeight === scrollHeight) {
                    //下拉刷新
                    this.$emit('scrollHandle', 'bottom');
                }
            },300),
            preview(msgContent) {
                if(msgContent.isWhatsApp) {
                    //提示“由于Whatsapp会话内容加密，无法播放视频”
                    FxUI.Message.warning($t('crm.whatsappConversion.videoTip'));
                    return;
                }
                seajs.use('qx-modules/video/pop-video', function (Video) {
                    const src = FS.qxUtil.getFscStreamLink(msgContent.path + '.' + msgContent.ext);
                    const poster = FS.qxUtil.getFscLinkByOpt({ id: msgContent.thumb_path, webp: true });
                    new Video({
                        src,
                        poster
                    });
                });
            }
        },
        components: {
          
        }
    }
  </script>
  
  <style lang='less' scoped>
  .video-list-panel {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    padding-left: 10px;
    padding-top: 10px;
    .video-list-panel-item {
        position: relative;
        margin: 0;
        padding: 10px 21px 10px 61px;
        border-bottom: 1px solid #F5F5F5;
        float: left;
        display: inline;
        padding-left: 2px;
        padding-bottom: 5px;
        margin-left: 0;
        margin-right: 0;
        margin-bottom: -1px;
        padding-right: 10px;
        cursor: pointer;
        img {
            width: 100px;
            height: 100px;
        }
        .duration {
            position: absolute;
            bottom: 5px;
            right: 5px;
            color: var(--color-neutrals01);
        }
    }
  }
</style>