<template>
    <div class="feed-session-message-miniProgramMsg">
        <div class="program-item-title">{{data.title}}</div>
        <div class="program-item-description">
            <span>{{data.description}}</span>
            <i class="el-icon-link"></i>
        </div>
        <div class="program-item-displayname">
            <i class="el-icon-link"></i>
            <span>{{data.displayname}}</span>
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            data:{
                type: Object,
                default: {}
            }
        },
        data() {
            return {}
        },
        created() {
            
        },
        mounted() {
        
        },
        methods: {
            
        }
    }
</script>
<style lang='less' scoped>
    .feed-session-message-miniProgramMsg {
        display: inline-block;
        width: 360px;
        border: 1px solid #DEE1E8;
        border-radius: 6px;
        padding: 12px 12px 0;
        box-sizing: border-box;
        text-align: left;
        .program-item-title {
            font-size: 14px;
            color: #181C25;
        }
        .program-item-description {
            display: flex;
            justify-content: space-between;
            margin-top: 2px;
            span {
                font-size: 12px;
                color: #91959E;
            }
            i {
                color: #EEF0F3;
                font-size: 50px;
                margin-left: 10px;
            }
        }
        .program-item-displayname {
            display: flex;
            align-items: center;
            margin-top: 12px;
            border-top: 1px solid #DEE1E8;
            padding: 4px 0;
            i {
                color: #0C6CFF;
                font-size: 20px;
            }
            span {
                color: #91959E;
                font-size: 12px;
                margin-left: 4px;
            }
        }
    }
</style>