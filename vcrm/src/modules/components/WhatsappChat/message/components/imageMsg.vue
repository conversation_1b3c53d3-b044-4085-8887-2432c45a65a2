<template>
    <div class="feed-session-message-imageMsg" @click="_preview">
        <img :src="data[index] | getFscLinkByOpt" class="info-item-img"/>
    </div>
</template>

<script>
    import dhtEmpty from '../assets/images/dht-empty.svg';
    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
            index: {
                type: Number,
                default: 0
            }
        },
        filters: {
            getFscLinkByOpt (val) {
                return FS ? FS.util.getFscLinkByOpt({
                    id: val.path + '2.' + val.ext,
                    webp: true
                }) : dhtEmpty;
            }
        },
        data() {
            return {}
        },
        created() {
            
        },
        mounted() {
        
        },
        methods: {
            _preview () {
                FS ? FS.qxUtil.previewImg(this.data.map(item => ({
                    "previewPath": item.path + '.' + item.ext,
                    "originPath": item.path + '.' + item.ext,
                    "thumbPath": item.path + '.' + item.ext,
                    "fileName": item.name,
                    "fileSize": item.size,
                })), this.index) : alert('当前环境不支持预览');
            }
        }
    }
</script>
<style lang='less' scoped>
    .feed-session-message-imageMsg{
        width: 30%;
        // height: 100%;
        display: inline-block;
        .info-item-img{
            height: 100px;
            width: 100px;
            cursor: pointer;
        }
        img{
            height: 100%;
            width: 100%;
        }
    }
</style>