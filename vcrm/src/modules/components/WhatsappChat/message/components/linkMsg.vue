<template>
    <a class="feed-session-message-linkMsg" :href="data.link_url" target="_blank">
        <div class="link-item-title">{{data.title}}</div>
        <div class="link-item-description">
            <span>{{data.description}}</span>
            <img :src="data.image_url">
        </div>
    </a>
</template>

<script>
    export default {
        props: {
            data:{
                type: Object,
                default: {}
            }
        },
        data() {
            return {}
        },
        created() {
            
        },
        mounted() {
        
        },
        methods: {
            
        }
    }
</script>
<style lang='less' scoped>
.feed-session-message-linkMsg {
    display: inline-block;
    width: 360px;
    border: 1px solid #DEE1E8;
    border-radius: 6px;
    padding: 12px;
    box-sizing: border-box;
    text-align: left;
    text-decoration: none;
    .link-item-title {
        font-size: 14px;
        color: #181C25;
    }
    .link-item-description {
        display: flex;
        justify-content: space-between;
        margin-top: 2px;
        span {
            font-size: 12px;
            color: #91959E;
        }
        img {
            width: 50px;
            height: 50px;
            margin-left: 10px;
        }
    }
}
</style>