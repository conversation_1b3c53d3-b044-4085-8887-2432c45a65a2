<template>
    <div class="feed-session-message-voiceMsg">
        <div class="voice-wrapper" @click="play">
            <img v-if="audio" src="../assets/images/playing-mine.gif">
            <img v-else src="../assets/images/play-mine.png">
            <span v-if="data.play_length" class="duration">{{data.play_length}}''</span>
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            data:{
                type: Object,
                default: {}
            }
        },
        data() {
            return {
                audio: null
            }
        },
        created() {
            
        },
        mounted() {
        
        },
        methods: {
            play() {
                if(this.data.isWhatsApp) {
                    //提示“由于Whatsapp会话内容加密，无法预览文件”
                    FxUI.Message.warning($t('crm.whatsappConversion.audioTip'));
                    return;
                }
                const _this = this;
                seajs ? seajs.use('qx-modules/base/audio-manager', function (audioManager) {
                    if (_this.audio) {
                        _this.audio.stop();
                    } else {
                        const src = FS ? FS.qxUtil.getMp3Url(_this.data.path + '.' + _this.data.ext) : _this.data.path;
                        const audio = _this.audio = audioManager.play(src);
                        const complete = function () {
                            _this.audio = null;
                        };
                        audio.on('ended', complete);

                        audio.on('error', function () {
                            complete();
                            FS ? FS.util.remind(3, $t('播放失败，请稍候重试')) : alert($t('播放失败，请稍候重试'));
                        });
                    }
                }) : alert('当前环境不支持播放');
            }
        }
    }
</script>
<style lang='less' scoped>
.feed-session-message-voiceMsg {
    display: inline-block;
    .voice-wrapper {
        position: relative;
        min-width: 90px;
        height: 20px;
        color: #9E9E9E;
        cursor: pointer;
        img {
            overflow: hidden;
            display: inline-block;
            width: 10px;
            height: 18px;
            vertical-align: middle;
            transform: rotate(180deg);
        }
        .duration {
            display: inline-block;
            width: 20px;
            position: absolute;
            white-space: nowrap;
            top: 1px;
        }
    }
}
</style>