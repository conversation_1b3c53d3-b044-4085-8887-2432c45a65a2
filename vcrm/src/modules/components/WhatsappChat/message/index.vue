<template>
    <div ref="feedsessionmessage" class="feed-session-message" @scroll.passive="fnHandleScroll($event)">
        <div class="feed-session-message_wrapper">
            <div style="display:flex;" v-for="(item,i) in data" :key="item.id" :id="item.id === scrollIntoDataId ? 'feed-session-message_target' : ''">
                <div class="msg-item-wrapper">
                    <div :class="getLeftOrRight(item.user.id)? 'msg-item-wrapper-mine' : 'msg-item-wrapper-other'">
                        <div v-if="getLeftOrRight(item.user.id)">
                            <span class="msg-item-time">{{item.create_time}}</span>
                            <span class="msg-item-name">{{item.user.name || '--'}}</span>
                        </div>
                        <div v-else>
                            <span class="msg-item-name">{{item.user.name || '--'}}</span>
                            <span class="msg-item-time">{{item.create_time}}</span>
                        </div>

                        <image-msg v-if="item.type === 'image'" :data="data.filter(image => image.type === 'image').map(image => image.content)" :index="data.filter(image => image.type === 'image').findIndex(image => image.id === item.id)"/>

                        <mini-program-msg v-else-if="item.type === 'miniProgram'" :data="item.content"/>

                        <link-msg v-else-if="item.type === 'link'" :data="item.content"/>

                        <video-msg v-else-if="item.type === 'video'" :data="item.content"/>

                        <div v-else-if="item.type === 'audio'" :class="['msg-item', getLeftOrRight(item.user.id) ? 'msg-item-name_mine':'msg-item-name_other']" >
                            <voice-msg :data="item.content"/>
                        </div>

                        <file-attach-msg v-else-if="item.type === 'file'" :data="item.content" :class="getLeftOrRight(item.user.id)? 'file-attach-msg_mine' : 'file-attach-msg_other'"/>

                        <div :class="['msg-item', getLeftOrRight(item.user.id) ? 'msg-item-name_mine':'msg-item-name_other']" v-else>
                            <div class="msg-content">
                                <template v-for="text in parseLink(item.content)">
                                    <a v-if="text.isLink" :href="text.value" target="_blank">{{ text.value }}</a>
                                    <template v-else>
                                        <template v-for="t in highlight(text.value, item.highlight_words)">
                                            <span class="highlight" v-if="t.isHighlight">{{ t.value }}</span>
                                            <template v-else>{{ t.value }}</template>
                                        </template>
                                    </template>
                                </template>
                            </div>
                        </div>

                        <template v-if="item.tags">
                            <div class="msg-item-tag" v-for="tag in item.tags">
                                <span>{{ tag }}</span>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
  </template>
  
  <script>
  import imageMsg from './components/imageMsg.vue'
  import fileAttachMsg from './components/fileAttachMsg.vue'
  import miniProgramMsg from './components/miniProgramMsg.vue'
  import linkMsg from './components/linkMsg.vue'
  import videoMsg from './components/videoMsg.vue'
  import voiceMsg from './components/voiceMsg.vue'

    export default {
        props: {
            data: {
              type: Array,
              default: []
            },
            userId: {
              type: String,
              default: ''
            },
            scrollIntoDataId: {
                type: String,
                default: ''
            }
        },
        watch: {
            data(val) {
                this.$nextTick(() => {
                    if (this.$refs.feedsessionmessage.scrollHeight !== 0 && this.$refs.feedsessionmessage.clientHeight !== 0 && this.$refs.feedsessionmessage.scrollHeight === this.$refs.feedsessionmessage.clientHeight) {
                        this.$emit('scrollHandle', 'top');
                        this.$emit('scrollHandle', 'bottom');
                    }
                });
            }
        },
        data() {
            return {

            }
        },
        mounted() {
            let _this = this;
            var ele = document.getElementsByClassName("feed-session-message")[0];

            setTimeout(function(){
                //设置滚动条
                ele.scrollTop = (_this.scrollIntoDataId ? document.getElementById('feed-session-message_target').offsetTop : ele.scrollHeight);
            },0);
        },
        methods: {
            parseLink(text) {
                let result = [];

                const httpReg = new RegExp("(http[s]{0,1}|ftp)://[a-zA-Z0-9\\.\\-]+\\.([a-zA-Z]{2,4})(:\\d+)?(/[a-zA-Z0-9\\.\\-~!@#$%^&amp;*+?:_/=<>]*)?", "gi");
                
                const linkList = text.match(httpReg);

                for (const [index, link] of linkList ? linkList.entries() : []) {
                    result.push({value: text.slice(0, text.indexOf(link))}, {isLink: true, value: link});
                    text = text.slice(text.indexOf(link) + link.length);
                    index === linkList.length - 1 && result.push({value: text});
                }

                return result.length ? result : [{value: text}];
            },
            highlight(text, highlightWords = []) {
                let result = [];
                let highlightIntervals = [];
                for (const highlightWord of highlightWords) {
                    const intervals = this.getStrPosition(text, highlightWord).map(index => ({ start: index, end: index + highlightWord.length }));
                    highlightIntervals = highlightIntervals.concat(intervals);
                }
                highlightIntervals = this.mergeIntervals(highlightIntervals);

                let prevEnd;
                for (const [index, highlightInterval] of highlightIntervals.entries()) {
                    result.push({value: text.slice(prevEnd || 0, highlightInterval.start)}, {isHighlight: true, value: text.slice(highlightInterval.start, highlightInterval.end)});
                    prevEnd = highlightInterval.end;
                    index === highlightIntervals.length - 1 && result.push({value: text.slice(prevEnd)});
                }

                return result.length ? result : [{value: text}];
            },
            mergeIntervals(intervals) {
                let res = [];
                intervals.sort((a, b) => a.start - b.start);

                let prev = intervals[0];

                for (let i = 1; i < intervals.length; i++) {
                    let cur = intervals[i];
                    if (prev.end >= cur.start) { // 有重合
                    prev.end = Math.max(cur.end, prev.end); 
                    } else {       // 不重合，prev推入res数组 
                    res.push(prev);
                    prev = cur;  // 更新 prev
                    }
                }

                prev && res.push(prev);
                return res;
            },
            getStrPosition(str, target) {
                let result = [];
                let index = str.indexOf(target);
                while (index !== -1) {
                    result.push(index);
                    index = str.indexOf(target, index + 1);
                }
                return result;
            },
            getLeftOrRight(val) {
                if(!val) {
                    return false
                }
                if(this.userId == val) {
                    return true
                }
                return false
            },
            fnHandleScroll: _.debounce(function(e) {
                const {scrollTop, clientHeight, scrollHeight} = e.target;
                console.log('滚动高',scrollTop)
                if(scrollTop === 0) {
                    //下拉刷新
                    this.$emit('scrollHandle', 'top');
                }
                if(scrollTop + clientHeight === scrollHeight) {
                    //上拉刷新
                    this.$emit('scrollHandle', 'bottom');
                }
            },300),
        },
        components: {
            imageMsg,
            fileAttachMsg,
            miniProgramMsg,
            linkMsg,
            videoMsg,
            voiceMsg
        }
    }
  </script>
  
  <style lang='less' scoped>
  .feed-session-message{
      overflow-x: hidden;
      overflow-y: scroll;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1;
      .feed-session-message_wrapper{
          box-sizing: border-box;
          width: 100%;
          padding: 12px;
          .feed-session-message_loading{
              text-align: center;
              margin-top: 10px;
          }
          .msg-item{
              display: inline-block;
              position: relative;
              max-width: 68%;
              padding: 10px 15px;
              border-radius: 8px;
              color: #181C25;
              text-align: left;
              word-wrap: break-word;
              margin-bottom:6px;
              background-color: #C7E7FD;
              .msg-content {
                  word-break: break-all;
                  text-align: left;
                  .highlight {
                    color: #FF522A;
                  }
              }
          }
          .msg-item-wrapper-mine{
              text-align: right;
          }

          .msg-item-name_mine {
              text-align: right;
              background-color: #C7E7FD;
          }
          .msg-item-name_other {
               background-color: #f2f3f5;
          }
          .file-attach-msg_mine {
              display: flex;
              justify-content: flex-end;
          }
          .msg-item-name {
              font-size: 11px;
              color: #545861;
              line-height: 18px;
          }
          .msg-item-tag {
            margin-bottom: 4px;
            span {
                font-size: 12px;
                background: #FFF5F0;
                color: #FF522A;
            }
          }
          .msg-item-time {
            font-size: 11px;
            color: #999999;
            line-height: 18px;
          }
          .msg-content-after {
              border: 4px solid #C7E7FD;
              border-top: 4px solid transparent;
              border-right: 4px solid transparent;
              border-bottom: 4px solid transparent;
              position: absolute;
              right: -8px;
              top:15px;
          }
          .msg-content-before{
              border: 4px solid #fff;
              border-top: 4px solid transparent;
              border-left: 4px solid transparent;
              border-bottom: 4px solid transparent;
              position: absolute;
              left: -8px;
              top:15px;
          }
      }
  }
  .msg-item-wrapper {
      position: relative;
      flex:1;
      z-index:3;
      width: 100%;
      margin-bottom: 12px;
  }

  .msg-item-name_mine /deep/ .duration {
    left: -40px;
  }
  .msg-item-name_other /deep/ .duration {
    right: -40px;
  }
</style>
