<template>
    <div ref="audiolistpanel" class="audio-list-panel" @scroll.passive="fnHandleScroll($event)">
        <div class="audio-list-panel-item" v-for="(item, index) in data" :key="item.id" @click="play(item.content.path + '.' + item.content.ext, index, item.content.isWhatsApp)">
            <div class="panel-item-icon">
                <i class="el-icon-microphone"></i>
            </div>
            <div class="panel-item-info">
                <div class="voice-wrapper">
                    <img v-if="audio[index]" src="../../../assets/images/playing-mine.gif">
                    <img v-else src="../../../assets/images/play-mine.png">
                    <span v-if="item.content.play_length" class="duration">{{item.content.play_length}}''</span>
                </div>
                <div class="user-wrapper">
                    <p class="panel-item-info-time">{{item.create_time}}</p>
                    <p class="panel-item-info-user">{{$t('来自：')}}{{item.user.name}}</p>
                </div>
            </div>
        </div>
    </div>
  </template>
  
  <script>

    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
        },
        watch: {
            data(val) {
                this.$nextTick(() => {
                    if (this.$refs.audiolistpanel.scrollHeight !== 0 && this.$refs.audiolistpanel.clientHeight !== 0 && this.$refs.audiolistpanel.scrollHeight === this.$refs.audiolistpanel.clientHeight) {
                        this.$emit('scrollHandle', 'bottom');
                    }
                });
            }
        },
        data() {
            return {
                audio: []
            }
        },
        mounted() {
            
        },
        methods: {
            fnHandleScroll: _.debounce(function(e) {
                console.log('滚动高',e.target.scrollTop)
                const {scrollTop, clientHeight, scrollHeight} = e.target;
                if(scrollTop + clientHeight === scrollHeight) {
                    //下拉刷新
                    this.$emit('scrollHandle', 'bottom');
                }
            },300),
            play(path, index, isWhatsApp) {
                if(isWhatsApp) {
                    //提示“由于Whatsapp会话内容加密，无法播放音频”
                    FxUI.Message.warning($t('crm.whatsappConversion.audioTip'));
                    return;
                }
                const _this = this;
                seajs.use('qx-modules/base/audio-manager', function (audioManager) {
                    if (_this.audio[index]) {
                        _this.audio[index].stop();
                    } else {
                        const src = FS.qxUtil.getMp3Url(path);
                        const audio = audioManager.play(src);
                        _this.$set(_this.audio, index, audio);
                        const complete = function () {
                            _this.$set(_this.audio, index, null);
                        };
                        audio.on('ended', complete);
                        audio.on('error', function () {
                            complete();
                            FS.util.remind(3, $t('播放失败，请稍候重试'));
                        });
                    }
                });
            }
        },
        components: {
          
        }
    }
  </script>
  
  <style lang='less' scoped>
  .audio-list-panel {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    padding: 20px 12px;
    .audio-list-panel-item {
        display: flex;
        cursor: pointer;
        .panel-item-icon {
            width: 72px;
            height: 72px;
            background: #EEF0F3;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-neutrals01);
            font-size: 40px;
        }
        .panel-item-info {
            flex: 1;
            margin-left: 14px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--color-neutrals05);
            height: 78px;
            .voice-wrapper {
                background: var(--color-neutrals05);
                padding: 10px 15px;
                border-radius: 8px;
                position: relative;
                min-width: 90px;
                height: 20px;
                color: #9E9E9E;
                img {
                    overflow: hidden;
                    display: inline-block;
                    width: 10px;
                    height: 18px;
                    vertical-align: middle;
                    transform: rotate(180deg);
                }
                .duration {
                    display: inline-block;
                    width: 20px;
                    position: absolute;
                    white-space: nowrap;
                    top: 1px;
                    right: -40px;
                }
            }
            .user-wrapper {
                height: 100%;
            }
            .panel-item-info-time,.panel-item-info-user {
                font-size: 12px;
                color: var(--color-neutrals11);
            }
            .panel-item-info-user {
                margin-top: 2px;
            }
        }
    }
  }
</style>