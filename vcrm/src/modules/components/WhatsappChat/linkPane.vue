<template>
    <div ref="linklistpanel" class="link-list-panel" @scroll.passive="fnHandleScroll($event)">
        <a class="link-list-panel-item" v-for="(item, index) in data" :key="item.id" :href="item.content.link_url" target="_blank">
            <div class="panel-item-icon">
                <i class="el-icon-link"></i>
            </div>
            <div class="panel-item-info">
                <div>
                    <p class="panel-item-info-title">{{item.content.title}}</p>
                    <p class="panel-item-info-link">{{item.content.description}}</p>
                </div>
                <div>
                    <p class="panel-item-info-time">{{item.create_time}}</p>
                    <p class="panel-item-info-user">{{$t('来自：')}}{{item.user.name}}</p>
                </div>
            </div>
        </a>
    </div>
  </template>
  
  <script>

    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
        },
        watch: {
            data(val) {
                this.$nextTick(() => {
                    if (this.$refs.linklistpanel.scrollHeight !== 0 && this.$refs.linklistpanel.clientHeight !== 0 && this.$refs.linklistpanel.scrollHeight === this.$refs.linklistpanel.clientHeight) {
                        this.$emit('scrollHandle', 'bottom');
                    }
                });
            }
        },
        data() {
            return {
                
            }
        },
        mounted() {
            
        },
        methods: {
            fnHandleScroll: _.debounce(function(e) {
                console.log('滚动高',e.target.scrollTop)
                const {scrollTop, clientHeight, scrollHeight} = e.target;
                if(scrollTop + clientHeight === scrollHeight) {
                    //下拉刷新
                    this.$emit('scrollHandle', 'bottom');
                }
            },300),
        },
        components: {
          
        }
    }
  </script>
  
  <style lang='less' scoped>
  .link-list-panel {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    padding: 20px 12px;
    .link-list-panel-item {
        display: flex;
        .panel-item-icon {
            width: 72px;
            height: 72px;
            background: #EEF0F3;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-neutrals01);
            font-size: 40px;
        }
        .panel-item-info {
            flex: 1;
            margin-left: 14px;
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid var(--color-neutrals05);
            padding-bottom: 20px;
            min-height: 78px;
            box-sizing: border-box;
            .panel-item-info-title {
                font-size: 14px;
                color: var(--color-neutrals19);
            }
            .panel-item-info-link {
                max-width: 300px;
                word-break: break-all;
            }
            .panel-item-info-link,.panel-item-info-time,.panel-item-info-user {
                font-size: 12px;
                color: var(--color-neutrals11);
            }
            .panel-item-info-link,.panel-item-info-user {
                margin-top: 2px;
            }
        }
    }
  }
</style>