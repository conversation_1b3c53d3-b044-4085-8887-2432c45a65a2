<template>
    <div ref="miniprogramlistpanel" class="mini-program-list-panel" @scroll.passive="fnHandleScroll($event)">
        <div class="mini-program-list-panel-item" v-for="(item, index) in data" :key="item.id">
            <div class="panel-item-icon">
                <i class="el-icon-link"></i>
            </div>
            <div class="panel-item-info">
                <div>
                    <p class="panel-item-info-title">{{item.content.title}}</p>
                    <p class="panel-item-info-link">{{item.content.displayname}} | {{item.content.username}}</p>
                </div>
                <div>
                    <p class="panel-item-info-time">{{item.create_time}}</p>
                    <p class="panel-item-info-user">{{$t('来自：')}}{{item.user.name}}</p>
                </div>
            </div>
        </div>
    </div>
  </template>
  
  <script>

    export default {
        props: {
            data: {
                type: Array,
                default: []
            },
        },
        watch: {
            data(val) {
                this.$nextTick(() => {
                    if (this.$refs.miniprogramlistpanel.scrollHeight !== 0 && this.$refs.miniprogramlistpanel.clientHeight !== 0 && this.$refs.miniprogramlistpanel.scrollHeight === this.$refs.miniprogramlistpanel.clientHeight) {
                        this.$emit('scrollHandle', 'bottom');
                    }
                });
            }
        },
        data() {
            return {
                
            }
        },
        mounted() {
            
        },
        methods: {
            fnHandleScroll: _.debounce(function(e) {
                console.log('滚动高',e.target.scrollTop)
                const {scrollTop, clientHeight, scrollHeight} = e.target;
                if(scrollTop + clientHeight === scrollHeight) {
                    //下拉刷新
                    this.$emit('scrollHandle', 'bottom');
                }
            },300),
        },
        components: {
          
        }
    }
  </script>
  
  <style lang='less' scoped>
  .mini-program-list-panel {
    overflow-x: hidden;
    overflow-y: scroll;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1;
    padding: 20px 12px;
    .mini-program-list-panel-item {
        display: flex;
        .panel-item-icon {
            width: 72px;
            height: 72px;
            background: #EEF0F3;
            display: flex;
            justify-content: center;
            align-items: center;
            color: var(--color-neutrals01);
            font-size: 40px;
        }
        .panel-item-info {
            flex: 1;
            margin-left: 14px;
            display: flex;
            justify-content: space-between;
            border-bottom: 1px solid var(--color-neutrals05);
            padding-bottom: 20px;
            min-height: 78px;
            box-sizing: border-box;
            .panel-item-info-title {
                font-size: 14px;
                color: var(--color-neutrals19);
            }
            .panel-item-info-link,.panel-item-info-time,.panel-item-info-user {
                font-size: 12px;
                color: var(--color-neutrals11);
            }
            .panel-item-info-link,.panel-item-info-user {
                margin-top: 2px;
            }
        }
    }
  }
</style>