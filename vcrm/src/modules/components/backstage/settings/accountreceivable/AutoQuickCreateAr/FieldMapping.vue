<template>
    <p>
        <fx-button v-for="item in objOptions" :key="item.value" plain size="mini" @click="handleClick(item)">{{ item.label }}<span class="fx-icon-obj-app159"></span></fx-button>
        <rule-comp
            v-if="dialogFormVisible"
            :dialogFormVisible.sync="dialogFormVisible"
            :sourceApiName="sourceApiName"
            :targetApiName="targetApiName"
            :ruleApiName="ruleApiName"
            :includeDetail="true"
            :confirmLoading="confirmLoading"
            :maxMappingCount="2"
            :parseFetchFields="handleParseFetchFields"
            @confirm="handleConfirm"
        />
    </p>
</template>

<script>
import FieldMappingForm from '../../components/MultiSourceMappingRuleComp/FieldMappingForm.vue'
export default {
    name: 'FieldMapping',
    components: {
        RuleComp: FieldMappingForm
    },
    props: {
        objOptions: {
            type: Array,
            required: true
        }
    },
    data() {
        return {
            sourceApiName: '',
            targetApiName: 'AccountsReceivableNoteObj',
            ruleApiName: '',
            detailRuleApiName: '',
            dialogFormVisible: false,
            confirmLoading: false,
        }
    },
    methods: {
        handleClick(item) {
            this.dialogFormVisible = true;
            this.sourceApiName = item.value;
            this.ruleApiName = item.ruleApiName;
            this.detailRuleApiName = item.detailRuleApiName;
        },
        handleParseFetchFields(rst, apiName, type) {
            // 目标对象只能设置应收单明细
            if (type === 'target') {
                rst.details = rst.details.filter((item) => item.api_name === 'AccountsReceivableDetailObj');
            }
            return rst;
        },
        handleConfirm(data) {
            this.confirmLoading = true;
            CRM.util.ajax_base('/EM1HNCRM/API/v1/object/data_mapping/service/create_or_update_rule', {
                bizType: 'quickAr',
                createRuleArg: {
                    ...data,
                    rule_list: data.rule_list.map(item => {
                        const isMasterObj = item.source_api_name === this.sourceApiName;
                        return {
                            ...item,
                            rule_api_name: isMasterObj ? this.ruleApiName : this.detailRuleApiName,
                            master_rule_api_name: !isMasterObj ? this.ruleApiName : '',
                            master_api_name: !isMasterObj ? this.sourceApiName : '',
                        }
                    })
                },
            }, null).then(rst => {
                this.$message.success($t('操作成功'));
                this.dialogFormVisible = false;
            }).finally(() => {
                this.confirmLoading = false;
            });
        }
    }
}
</script>