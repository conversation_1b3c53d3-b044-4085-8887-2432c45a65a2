<template>
    <div ref="reactiveFormWrapper" class="create-rule-form-field-cascader-condition"></div>
</template>

<script>
import crmRequire from '@common/require'
const getConditionPropLabel = (prop) => {
    return $t(`sfa.crm_setting.accountreceivable.open_ar_quick_rule_condition.prop_${prop}`)
}
export default {
    name: 'CreateRuleFormFieldCascaderCondition',
    props: {
        // [
        //     {
        //         "filters": [
        //             {
        //                 "field_name": "product_id",
        //                 "operator": "EQ",
        //                 "field_values": [
        //                     "67f1f264167eba000113248c"
        //                 ]
        //             },
        //             {
        //                 "field_name": "product_id.product_status",
        //                 "operator": "EQ",
        //                 "field_values": [
        //                     "1"
        //                 ]
        //             }
        //         ],
        //         "connector": "OR"
        //     }
        // ]
        value: {
            type: Array,
            default: () => ([])
        }
    },
    data() {
        return {
            productSelectFieldOptions: [],
        }
    },
    mounted() {
        this.renderReactiveForm();
    },
    methods: {
        parseData() {
            const value = typeof this.value === 'string' ? JSON.parse(this.value) : this.value;
            return (value?.[0]?.filters || []).map(item => {
                const { field_name, field_values, field_value__s, min_value } = item;
                const isProductCategory = field_name.split('.').length > 1;
                const field_value = field_values[0];
                return {
                    feild_type: isProductCategory ? 'product_category' : 'product_id',
                    field_value: isProductCategory ? {
                        field_name: field_value,
                        object_api_name: field_name.split('.')[1],
                    } : {id: field_value, name: field_value__s},
                    min_value,
                }
            })
        },
        valid() {
            if (this.reactiveForm) {
                const data = this.reactiveForm.collect();
                const noValidItems = ['feild_type', 'rowId'];
                _.each(data, (item) => {
                    if (item.min_value > 100) {
                        throw new Error($t('sfa.crm_setting.accountreceivable.open_ar_quick_rule_condition.valid_max'));
                    }
                    const validItemValues = Object.entries(item).filter(([key]) => !noValidItems.includes(key)).map(([key, value]) => value);
                    if (
                        !validItemValues.every(value => !CRM.util.isEmptyValue(value)) &&
                        !validItemValues.every(value => CRM.util.isEmptyValue(value))
                    ) {
                        throw new Error($t('请填写完整'));
                    }
                })
            }
        },
        getValue() {
            const filters = this.reactiveForm.getValue(false)
                .filter((item) => !Object.values(item).some(value => CRM.util.isEmptyValue(value)))
                .map((item) => {
                    const { feild_type, field_value, min_value } = item;
                    const isProductCategory = feild_type === 'product_category';
                    if (isProductCategory) {
                        return {
                            field_name: 'product_id.' + field_value.object_api_name,
                            field_values: [field_value.field_name],
                            operator: 'EQ',
                            min_value,
                        }
                    } else {
                        return {
                            field_name: 'product_id',
                            field_values: [field_value.id],
                            field_value__s: field_value.name,
                            operator: 'EQ',
                            min_value,
                        }
                    }
                })
            if (filters.length === 0) {
                return '';
            }
            return JSON.stringify([{
                filters,
                connector: 'OR'
            }]);
        },
        async getColumns() {
            const productSelectFieldOptions = await this.fetchProductFieldOptions();
            return [
                {
                    label: getConditionPropLabel('text'),
                    type: 'text'
                },
                {
                    api_name: 'feild_type',
                    type: 'select',
                    defaultValue: 'product_id',
                    options: [
                        {
                            label: getConditionPropLabel('product_id'),
                            value: 'product_id'
                        },
                        {
                            label: getConditionPropLabel('product_category'),
                            value: 'product_category'
                        }
                    ],
                    children: {
                        'product_id': [
                            {
                                api_name: 'field_value',
                                type: 'custom',
                                component: this.renderProductSelectField.bind(this),
                                width: '180px'
                            },
                        ],
                        'product_category': [
                            {
                                api_name: 'field_value',
                                type: 'cascade',
                                options: productSelectFieldOptions,
                                width: '180px'
                            },
                        ]
                    },
                },
                {
                    type: 'text',
                    label: getConditionPropLabel('min_value_label'),
                    api_name: 'min_value_label'
                },
                {
                    api_name: 'min_value',
                    type: 'numInput',
                    inputType: 'percent',
                    width: "120px",
                    props: {
                        'decimal-places': 2,
                        isPositiveNum: true,
                    }
                }
            ]
        },
        async renderReactiveForm() {
            const me = this;
            const ReactiveForm = await crmRequire('crm-modules/buscomponents/action_field/index').then((rst) => rst.C.ReactiveForm.ReactiveForm);
            const columns = await this.getColumns();
            const defaultValue = this.parseData();
            this.reactiveForm = new ReactiveForm(
                $(me.$refs.reactiveFormWrapper),
                columns,
                defaultValue,
                false,
            );
        },
        renderProductSelectField(wrapper, field, data = {}) {
            const me = this;
            return FxUI.create({
                wrapper,
                template: `
                    <fx-input
                        :value="value.name"
                        :placeholder="placeholder"
                        size="small"
                        readonly
                        :el-style="selectStyle"
                    >
                        <span slot="append" class="fx-icon-add-2" @click="onClick"></span>
                    </fx-input>
                `,
                data() {
                    const {id, name} = data[field.api_name] || {};
                    return {
                        selectStyle: {
                            width: field.width
                        },
                        value: {
                            id,
                            name
                        },
                        placeholder: $t('请选择')
                    }
                },
                methods: {
                    onClick() {
                        CRM.api.pick_data({
                            apiName: 'ProductObj',
                            data: [{_id: this.value?.id}],
                            methods: {
                                select: (res) => {
                                    const {id, name} = res.selected || {};
                                    if (id && name) {
                                        this.value = {id, name}
                                        me.reactiveForm.formValueChange(this.value, field, data);
                                    }
                                }
                            }
                        })
                    }
                }
            })
        },
        async fetchProductFieldOptions() {
            const res = await CRM.util.getDescribeLayout({
                apiname: 'ProductObj',
                include_layout: false,
                include_detail_describe: false
            })
            const allowType = ['select_one']
            return Object.values(res?.objectDescribe?.fields || {})
                .filter(item => item.define_type !== 'system')
                .filter(item => allowType.includes(item.type))
                .map(item => ({
                    label: item.label,
                    value: item.api_name,
                    children: item.options
                }));
        },
    }
}
</script>

<style lang="less">
.create-rule-form-field-cascader-condition {
    .crm-reactive-form-wrapper .crm-reactive-form {
        padding: 0;
        .crm-reactive-form-content .form-row {
            margin-bottom: 0;
            .row-item-field_value {
                .el-input-group__append {
                    cursor: pointer;
                    &:hover {
                        .fx-icon-add-2::before {
                            color: var(--color-primary06);
                        }
                    }
                }
            }
            .row-item-min_value_label {
                line-height: 18px;
                align-items: center;
            }
        }
    }
}
</style>