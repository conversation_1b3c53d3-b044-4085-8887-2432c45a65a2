<template>
    <fx-dialog
        custom-class="create-rule-form"
        :visible.sync="cVisible"
        :title="isEdit ? $t('编辑规则') : $t('新建规则')"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
    >
        <fx-form :model="formData" :rules="formRules" ref="rForm" label-width="120px">
            <fx-form-item v-for="item in formDataConfig" :label="item.label" :prop="item.prop" :key="item.prop">
                <fx-input
                    v-if="item.type === 'input'"
                    v-model="formData[item.prop]"
                    :placeholder="item.placeholder"
                    v-bind="item.attrs"
                    :hasFormItem="false"
                />
                <fx-transfer
                    :ref="'rField' + item.prop"
                    v-else-if="item.type === 'transfer'"
                    v-model="formData[item.prop]"
                    :data="reveivableFields"
                    :hasFormItem="false"
                />
                <filtergroup
                    :ref="'rField' + item.prop"
                    v-else-if="item.type === 'filter'"
                    :value="formData[item.prop]"
                    :options="{
                        apiname: objApiName
                    }"
                />
                <cascader-condition
                    :ref="'rField' + item.prop"
                    v-else-if="item.type === 'cascadercondition'"
                    :value="formData[item.prop]"
                />
                <span v-else>{{ item.prop }}</span>
            </fx-form-item>
        </fx-form>
        <div slot="footer" class="dialog-footer">
            <fx-button type="primary" size="small" @click="handleSubmit" :loading="confirmLoading">{{ $t('确定') }}</fx-button>
            <fx-button size="small" @click="handleCancel">{{ $t('取消') }}</fx-button>
        </div>
    </fx-dialog>
</template>

<script>
import Filtergroup from '@components/Filtergroup/Filtergroup.vue'
import CascaderCondition from './CreateRuleFormFieldCascaderCondition.vue'
const getFormPropLabel = (prop) => {
    return $t(`sfa.crm_setting.accountreceivable.open_ar_quick_rule_prop_${prop}`)
}
const formDataConfig = [
    {
        label: getFormPropLabel('name'),
        prop: 'name',
        type: 'input',
        placeholder: $t('请输入')
    },
    {
        label: getFormPropLabel('condition'),
        prop: 'conditions',
        type: 'filter',
        manualSetValue: true,
        options: {
            fields: {
                type: Object,
                default: () => ({})
            },
        }
    },
    {
        label: getFormPropLabel('max_period_count'),
        prop: 'max_period_count',
        type: 'input',
        attrs: {
            type: 'number',
            max: 9999,
            decimalPlaces: 0,
            isPositiveNum: true,
        },
        placeholder: $t('请输入')
    },
    {
        label: getFormPropLabel('allow_min_split'),
        prop: 'minimum_amount_condition',
        type: 'cascadercondition',
        manualSetValue: true
    },
    {
        label: getFormPropLabel('add_key_info'),
        prop: 'information_fields',
        type: 'transfer',
        options: []
    }
]
export default {
    name: 'CreateRuleForm',
    components: {
        Filtergroup,
        CascaderCondition
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        objApiName: {
            type: String,
            default: ''
        },
        editData: {
            type: Object,
            default: () => ({})
        },
        confirmLoading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            formDataConfig,
            formData: {
                information_fields: []
            },
            formRules: {
                name: [
                    { required: true, message: getFormPropLabel('name') + ' ' + $t('不能为空'), trigger: 'blur' }
                ],
                max_period_count: [
                    { required: true, message: getFormPropLabel('max_period_count') + ' ' + $t('不能为空'), trigger: 'blur' }
                ],
                conditions: [
                    {
                        validator: (rule, value, callback) => {
                            const component = this.$refs[`rField${rule.field}`][0];
                            if (component && component.valid) {
                                const valid = component.valid();
                                if (!valid) {
                                    callback(new Error($t('请填写完整')))
                                } else {
                                    callback()
                                }
                            }
                        },
                    }
                ],
                minimum_amount_condition: [
                    {
                        validator: (rule, value, callback) => {
                            const component = this.$refs[`rField${rule.field}`][0];
                            if (component && component.valid) {
                                try {
                                    component.valid();
                                    callback()
                                } catch (error) {
                                    callback(error)
                                }
                            }
                        }
                    }
                ]
            },
            reveivableFields: []
        }
    },
    computed: {
        cVisible: {
            get() {
                return this.visible
            },
            set(val) {
                this.$emit('update:visible', val)
            }
        },
        isEdit() {
            return !_.isEmpty(this.editData)
        }
    },
    created() {
        this.fetchReveivableFieldList();
        this.parseFormData();
    },
    methods: {
        parseFormData() {
            if (this.isEdit) {
                this.formData = {
                    ...(_.pick(this.editData, Object.values(this.formDataConfig).map(item => item.prop))),
                    id: this.editData._id,
                }
            }
            if (!this.formData.information_fields) {
                this.formData.information_fields = [];
            }
            this.formData.object_api_name = this.objApiName;
        },
        manualSetValue(field, value) {
            const manualSetValueFields = formDataConfig.filter(item => item.manualSetValue);
            manualSetValueFields.forEach((field) => {
                const component = this.$refs[`rField${field.prop}`][0];
                if (component && component.getValue) {
                    this.formData[field.prop] = component.getValue();
                }
            });
        },
        validate() {
            return new Promise((resolve, reject) => {
                this.$nextTick(() => {
                    this.$refs.rForm.validate((valid, fields) => {
                        if (valid) {
                            this.manualSetValue();
                            resolve();
                        } else {
                            reject(fields);
                        }
                    });
                });
            });
        },
        handleCancel() {
            this.$emit('update:visible', false)
        },
        async handleSubmit() {
            await this.validate();
            this.$emit('confirm', this.formData)
        },
        async fetchReveivableFieldList() {
            const res = await CRM.util.getDescribeLayout({
                apiname: 'AccountsReceivableNoteObj',
                include_layout: false,
                include_detail_describe: false
            })
            // 数量、文本、日期、金额
            const allowType = ['number', 'text', 'long_text', 'date', 'date_time', 'currency']
            this.reveivableFields = Object.values(res?.objectDescribe?.fields || {})
                .filter(item => item.define_type !== 'system')
                .filter(item => allowType.includes(item.type))
                .map(item => ({
                    label: item.label,
                    key: item.api_name
                }));
        }
    }
}
</script>

<style lang="less">
.create-rule-form {
}
</style>