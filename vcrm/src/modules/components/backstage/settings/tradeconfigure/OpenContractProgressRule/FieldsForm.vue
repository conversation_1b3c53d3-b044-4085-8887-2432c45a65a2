<template>
    <fx-form
        ref="form"
        :model="formData"
        :rules="formRules"
        label-width="128px"
        label-position="left"
        size="small">
        <template v-for="(config, field) in formDataConfig">
            <fx-form-item
                v-if="!config.visible || config.visible(formData)"
                :key="field"
                :label="config.label"
                :prop="field"
                :labelTips="config.labelTips">
                <!-- Input类型 -->
                <fx-input
                    v-if="config.type === 'input'"
                    :value="formData[field]"
                    @input="val => handleFieldChange(field, val)"
                    :placeholder="config.placeholder"
                    />
                
                <!-- Textarea类型 -->
                <fx-input
                    v-else-if="config.type === 'textarea'"
                    :value="formData[field]"
                    @input="val => handleFieldChange(field, val)"
                    type="textarea"
                    :rows="2"
                    :placeholder="config.placeholder"
                    />
                
                <!-- Switch类型 -->
                <fx-radio-group v-else-if="config.type === 'radio'" :value="formData[field]" @input="val => handleFieldChange(field, val)">
                    <fx-radio v-for="item in config.options" :key="item.value" :label="item.value">{{ item.label }}</fx-radio>
                </fx-radio-group>
                
                <!-- Select类型 -->
                <template v-else-if="config.type === 'select'">
                    <div class="cascade-container">
                        <fx-select
                            :value="formData[field]"
                            @change="val => handleFieldChange(field, val)"
                            :options="config.options"
                            :loading="loadingFields.has(field)"
                            :placeholder="config.placeholder"
                            :disabled="config.disabled ? config.disabled(formData) : false"
                            :multiple="config.multiple"
                            :filterable="config.filterable"
                            :clearable="config.clearable !== false"
                            />
                        <!-- 渲染子级选择器 -->
                        <template v-for="childField in cascadeDisplayFields[field]">
                            <span v-if="formDataConfig[childField].leftText">{{ formDataConfig[childField].leftText }}</span>
                            <fx-select
                                :key="childField"
                                v-if="formDataConfig[childField].type === 'select' &&(formDataConfig[childField].cascade.visible ? formDataConfig[childField].cascade.visible() : true)"
                                :value="formData[childField]"
                                @change="val => handleFieldChange(childField, val, field)"
                                :options="formDataConfig[childField].options"
                                :loading="loadingFields.has(childField)"
                                :placeholder="formDataConfig[childField].placeholder"
                                :disabled="formDataConfig[childField].disabled ? formDataConfig[childField].disabled(formData) : false"
                                />
                        </template>
                    </div>
                </template>

                <div v-else-if="config.customRender" :ref="'rCustomField__' + field" :key="`${field}_${customRenderKeys[field]}`"></div>

            </fx-form-item>
        </template>
    </fx-form>
</template>

<script>
export default {
    name: 'FieldsForm',
    props: {
        formDataConfig: {
            type: Object,
            required: true
        },
        formData: {
            type: Object,
            required: true
        },
        loadingFields: {
            type: Set,
            required: true
        },
        cascadeDisplayFields: {
            type: Object,
            required: true
        },
        formRules: {
            type: Object,
            required: true
        },
        isEdit: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            customComponents: {},
            customRenderKeys: {}
        }
    },
    watch: {
        formData: {
            handler(newFormData, oldFormData) {
                if (!oldFormData) return;
                Object.entries(this.formDataConfig).forEach(([field, config]) => {
                    if (config.customRender && config.cascade) {
                        const parentField = config.cascade.renderParent;
                        if (!_.isEqual(newFormData[parentField], oldFormData[parentField]) && config.cascade.triggerRender) {
                            this.renderCustomComponent(field, config, true);
                        }
                    }
                });
            },
            deep: true
        },
        formDataConfig: {
            handler(newConfig, oldConfig) {
                this.$nextTick(() => {
                    const newFields = Object.keys(newConfig || {});
                    const oldFields = Object.keys(oldConfig || {});

                    newFields.forEach(field => {
                        const config = newConfig[field];
                        if (config.customRender && !oldFields.includes(field)) {
                            this.renderCustomComponent(field, config);
                        }
                    });

                    oldFields.forEach(field => {
                         if (!newFields.includes(field) && this.customComponents[field]) {
                            this.destroyCustomComponent(field);
                         }
                    });
                });
            },
            immediate: true,
            deep: false
        }
    },
    computed: {
        manualGetValueFields() {
            return Object.entries(this.formDataConfig).filter(([field, config]) => config.manualGetValue).map(([field]) => field);
        }
    },
    destroyed() {
        this.destroyCustomComponents();
    },
    methods: {
        renderCustomComponent(field, config, forceReRender = false) {
             const wrapperRef = this.$refs[`rCustomField__${field}`];
             const wrapper = Array.isArray(wrapperRef) ? wrapperRef[0] : wrapperRef;

             if (!wrapper) {
                 this.$nextTick(() => {
                     const updatedWrapperRef = this.$refs[`rCustomField__${field}`];
                     const updatedWrapper = Array.isArray(updatedWrapperRef) ? updatedWrapperRef[0] : updatedWrapperRef;
                     if (!updatedWrapper) {
                         console.warn(`Wrapper for custom field ${field} not found even after nextTick.`);
                         return;
                     } else {
                         this._doRenderCustomComponent(field, config, forceReRender, updatedWrapper);
                     }
                 });
                 return;
             }

             this._doRenderCustomComponent(field, config, forceReRender, wrapper);
         },

        _doRenderCustomComponent(field, config, forceReRender, wrapper) {
            if (forceReRender && this.customComponents[field]) {
                this.destroyCustomComponent(field);
            }

            if (!forceReRender && this.customComponents[field]) {
                return;
            }

            this.$set(this.customRenderKeys, field, (this.customRenderKeys[field] || 0) + 1);

            this.$nextTick(async () => {
                try {
                    const defaultValue = this.formData[field];
                    const currentWrapperRef = this.$refs[`rCustomField__${field}`];
                    const currentWrapper = Array.isArray(currentWrapperRef) ? currentWrapperRef[0] : currentWrapperRef;
                    if (!currentWrapper) {
                        console.warn(`Wrapper for custom field ${field} disappeared after key change.`);
                        return;
                    }
                    const componentInstance = await config.customRender.call(this, config, field, defaultValue, currentWrapper);
                    if (componentInstance) {
                        this.customComponents[field] = componentInstance;
                    }
                } catch (error) {
                    console.error(`Error rendering custom component ${field}:`, error);
                    if (wrapper) wrapper.innerHTML = `Error rendering ${field}`;
                }
            });
        },

        destroyCustomComponent(field) {
            const instance = this.customComponents[field];
            if (instance) {
                if (typeof instance.destroy === 'function') {
                     instance.destroy();
                } else {
                    console.warn(`Custom component for field ${field} does not have a destroy method.`);
                }
                delete this.customComponents[field];
                delete this.customRenderKeys[field];
            }
        },

        destroyCustomComponents() {
            Object.keys(this.customComponents).forEach(field => {
                this.destroyCustomComponent(field);
            });
        },

        getCustomComponentValue(field) {
            const instance = this.customComponents[field];
            if (instance && typeof instance.getValue === 'function') {
                return instance.getValue();
            } else if (instance) {
                console.warn(`Custom component for field ${field} does not have a getValue method.`);
            }
            return undefined;
        },
        resetFields(fields) {
            this.$refs.form.resetFields(fields);
        },
        manualSetValue(field, value) {
            this.manualGetValueFields.forEach((field) => {
                const value = this.getCustomComponentValue(field);
                if (value !== undefined) {
                    this.$emit('update:formData', { ...this.formData, [field]: value });
                }
            });
        },
        validate() {
            this.manualSetValue();
            return new Promise((resolve, reject) => {
                this.$nextTick(() => {
                    this.$refs.form.validate((valid, fields) => {
                        if (valid) {
                            resolve();
                        } else {
                            console.log('Validation errors:', fields);
                            reject(fields);
                        }
                    });
                });
            });
        },
        handleFieldChange(field, value, parentField) {
            const config = this.formDataConfig[field];
            if (config.onChange) {
                config.onChange(value);
            }
            this.$emit('update:formData', { ...this.formData, [field]: value });
            this.$nextTick(() => {
                if (this.$refs.form && config && !config.manualGetValue) {
                    this.$refs.form.validateField(parentField || field);
                }
            });
        },
        clearValidate(field) {
            this.$refs.form?.clearValidate(field);
        }
    }
}
</script>

<style lang="less" scoped>
.cascade-container {
    display: flex;
    gap: 8px;
    
    :deep(.fx-select) {
        flex: 1;
        min-width: 0;
    }
}
</style> 