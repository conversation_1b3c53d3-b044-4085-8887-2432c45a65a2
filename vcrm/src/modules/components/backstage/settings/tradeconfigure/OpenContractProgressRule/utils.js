import crmRequire from '@common/require'

const ajaxBase = (url, param, {parseValueSuccess = false} = {}) => {
    return new Promise((resolve, reject) => {
        CRM.util.showLoading_new();
        CRM.util.FHHApi({
            url,
            data: param,
            success: (res) => {
                if (res.Result.StatusCode === 0) {
                    if (parseValueSuccess) {
                        if (res.Value.success) {
                            resolve(res.Value.data);
                            return;
                        }
                        reject(res.Value.msg || $t("暂时无法获取相关数据请稍后重试"));
                        return;
                    }
                    resolve(res.Value);
                    return;
                }
                reject(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
            },
            complete: () => {
                CRM.util.hideLoading_new();
            }
        }, {
            errorAlertModel: 1
        })
    })
}

// 新建form相关配置
export const INDEX_TYPE_CONFIG = {
    '1': {
        object: 'SaleContractLineObj',
        getOptions: () => {
            return [];
        }
    },
    '2': {
        object: 'ProductObj',
        getOptions: async () => {
            const describe = await API.getDescribeFields('ProductObj')
            return describe ? API.parseFieldsList(describe.fields, item => item.type === 'select_one') : []
        }
    },
    '3': {
        object: 'SaleContractObj',
        getOptions: async () => {
            return API.getObjectRefNumFields('SaleContractObj')
        }
    }
}

const createFieldConfig = (vm, field, config = {}) => {
    const baseField = vm.getRuleDescribeField(field)
    const isSelect = config.type === 'select'
    const isRadio = config.type === 'radio'
    // 尝试判断是否为布尔类型的 Radio
    const isBooleanRadio = isRadio && baseField.options?.length === 2 && baseField.options.every(opt => typeof opt.value === 'boolean');

    // 更健壮地确定默认值
    let defaultValue = config.defaultValue;
    if (defaultValue === undefined) {
        if (isSelect) defaultValue = config.multiple ? [] : ''; // 多选默认为空数组
        else if (isRadio) defaultValue = isBooleanRadio ? false : null; // 布尔默认false，其他radio默认null
        // 其他类型如 input, textarea 默认为 ''
        else defaultValue = '';
    }

    const defaultConfig = {
        label: baseField.label,
        placeholder: `${isSelect ? $t('请选择') : $t('请输入')}${baseField.label || ''}`,
        defaultValue: defaultValue, // 使用确定的默认值
    }

    if (config.required === true) {
        defaultConfig.rules = [{
            required: true,
            // 多选提示可能需要调整
            message: `${config.multiple ? $t('请至少选择一项') : (isSelect ? $t('请选择') : $t('请输入'))}${baseField.label || ''}`,
            trigger: isSelect ? 'change' : 'blur'
        }]
         // 多选数组的校验器
        if (config.multiple) {
            defaultConfig.rules.push({
                type: 'array',
                required: true,
                message: `${$t('请至少选择一项')}${baseField.label || ''}`,
                trigger: 'change'
            });
        }
    }

    if ((isSelect || isRadio) && !config.options && baseField.options) {
        defaultConfig.options = baseField.options
    }

    // 默认cascade行为
    if (config.cascade) {
        config.cascade = {
            resetValue: true, // 默认重置
            triggerRender: false, // 默认不触发完全重渲染
            ...config.cascade // 合并传入的配置
        };
    }


    return {
        ...defaultConfig,
        ...config,
        rules: [...(defaultConfig.rules || []), ...(config.rules || [])],
    }
}

export const getFormDataConfig = (vm) => {
    return {
        name: createFieldConfig(vm, 'name', {
            type: 'input',
            required: true
        }),
        description: createFieldConfig(vm, 'description', {
            type: 'textarea',
            required: true
        }),
        enabled_status: createFieldConfig(vm, 'enabled_status', {
            type: 'radio',
            defaultValue: true,
            required: true
        }),
        contract_record_type: createFieldConfig(vm, 'contract_record_type', {
            type: 'select',
            options: vm.recordTypes,
            multiple: true,
            required: true, // 根据业务确认是否必填，createFieldConfig已处理required=true的情况
            defaultValue: ['all'], // 多选默认空数组
            onChange(value) {
                const oldValue = vm.formData['contract_record_type']
                const isAddAll = value.includes('all') && !oldValue.includes('all')
                if (isAddAll) {
                    vm.$nextTick(() => {
                        vm.$set(vm.formData, 'contract_record_type', ['all'])
                    })
                }
                const isAddOther = value.includes('all') && oldValue.includes('all') && value.length > 1;
                if (isAddOther) {
                    vm.$nextTick(() => {
                        vm.$set(vm.formData, 'contract_record_type', value.filter(item => item !== 'all'))
                    })
                }
            }
        }),
        index_type: createFieldConfig(vm, 'index_type', {
            type: 'select',
            required: true,
            disabled: () => vm.isEdit,
            rules: [
                {
                    validator(rule, value, callback) {
                        if (
                            (value !== '1' && !vm.formData['index_type_object_field']) ||
                            (value === '2' && !vm.formData['index_type_object_field_value'])
                        ) {
                            callback(new Error($t('请填写完整')))
                            return;
                        }
                        callback();
                    }
                }
            ],
            onChange(value) {
                vm.formData['index_type_object'] = INDEX_TYPE_CONFIG[value].object;
                vm.$nextTick(() => {
                    vm.$set(vm.formData, 'index_goal_data_ref_product_field', '');
                    vm.$set(vm.formData, 'index_goal_data_object_field', '');
                })
            }
        }),
        index_type_object_field: createFieldConfig(vm, 'index_type_object_field', {
            type: 'select',
            required: true,
            cascade: {
                displayParent: 'index_type',
                optionsParent: 'index_type',
                visible: () => vm.formData['index_type'] && vm.formData['index_type'] !== '1'
            },
            async lazyLoad() {
                const { index_type } = vm.formData
                const config = INDEX_TYPE_CONFIG[index_type]
                // 重置时，如果父字段为空，确保返回空数组
                if (!index_type || !config) return [];
                vm.loadingFields.add('index_type_object_field'); // 手动管理下loading状态
                try {
                    return await config.getOptions();
                } finally {
                    vm.loadingFields.delete('index_type_object_field');
                }
            },
            disabled: form => !form.index_type || vm.isEdit,
            visible() { return false },
        }),
        index_type_object_field_value: createFieldConfig(vm, 'index_type_object_field_value', {
            type: 'select',
            required: true,
            options: [],
            cascade: {
                displayParent: 'index_type',
                optionsParent: 'index_type_object_field',
                visible: () => vm.formData['index_type'] === '2'
            },
            disabled: form => !form.index_type || vm.isEdit,
            visible() { return false },
            lazyLoad() {
                const { index_type_object_field} = vm.formData
                if (!index_type_object_field) return [];
                return API.getDescribeFields('ProductObj').then((res) => res.fields[index_type_object_field]?.options || []);
            },
        }),
        index_type_category: createFieldConfig(vm, 'index_type_category', {
            type: 'select',
            defaultValue: 'numberType',
            required: true,
            disabled: () => vm.isEdit,
            visible: () => false
        }),
        index_type_calc_type: createFieldConfig(vm, 'index_type_calc_type', {
            type: 'select',
            defaultValue: 'sum',
            required: true,
            disabled: () => vm.isEdit,
            visible: () => false
        }),
        index_goal_object: createFieldConfig(vm, 'index_goal_object', {
            type: 'select',
            options: vm.relatedObjects,
            required: true,
            disabled: () => vm.isEdit,
            rules: [
                {
                    validator(rule, value, callback) {
                        if (vm.formData['index_goal_object_field']) {
                            callback();
                            return;
                        }
                        callback(new Error($t('请填写完整')))
                    }
                }
            ]
        }),
        index_goal_object_field: createFieldConfig(vm, 'index_goal_object_field', {
            type: 'select',
            required: true,
            cascade: { // 使用增强的 cascade 配置
                displayParent: 'index_goal_object',
                optionsParent: 'index_goal_object',
            },
            disabled: form => !form.index_goal_object || vm.isEdit,
            visible() { return false },
            lazyLoad() {
                return API.getObjectRefNumFields(vm.formData['index_goal_object']);
            }
        }),
        index_goal_data_object: createFieldConfig(vm, 'index_goal_data_object', {
             type: 'select',
             options: vm.relatedObjects,
             required: true,
             disabled: () => vm.isEdit,
             rules: [
                 {
                     validator(rule, value, callback) {
                         if (vm.formData['index_goal_data_object_field']) {
                             callback();
                             return;
                         }
                         callback(new Error($t('请填写完整')))
                     }
                 }
             ],
             onChange(value) {
                vm.$nextTick(() => {
                    vm.$set(vm.formData, 'index_goal_data_condition', '');
                })
             }
         }),
         index_goal_data_ref_product_field: createFieldConfig(vm, 'index_goal_data_ref_product_field', {
            type: 'select',
            required: true,
            cascade: {
                displayParent: 'index_goal_data_object',
                optionsParent: 'index_goal_data_object',
                visible: () => vm.formData['index_type'] === '1' || vm.formData['index_type'] === '2'
            },
            disabled: form => !form.index_goal_data_object || vm.isEdit,
            visible() { return false },
            async lazyLoad() {
                const parentValue = vm.formData['index_goal_data_object'];
                if (!parentValue) return [];
                const [masterProductFields, detailProductFields] = await Promise.all([
                    API.getObjectRefProductFields(parentValue),
                    API.getDetailRelatedObjectRefProductFields(parentValue)
                ]);
                return [...masterProductFields, ...detailProductFields];
            },
        }),
        index_goal_data_object_field: createFieldConfig(vm, 'index_goal_data_object_field', {
            type: 'select',
            required: true,
            leftText: $t('sfa.crm.setting_tradeconfigure.is_open_contract_progress_data_source_goal_data_object_field_left_text'),
            cascade: {
                displayParent: 'index_goal_data_object',
                optionsParent: ['index_goal_data_object', 'index_goal_data_ref_product_field'],
            },
            disabled: form => !form.index_goal_data_object || vm.isEdit,
            visible() { return false },
            async lazyLoad() {
                if (vm.formData['index_type'] === '1' || vm.formData['index_type'] === '2') {
                    const pValue = vm.formData['index_goal_data_ref_product_field'];
                    if (!pValue) return [];
                    const [apiname, field] = pValue.split('.') || [];
                    return API.getObjectRefNumFields(apiname);
                }
                return API.getObjectRefNumFields(vm.formData['index_goal_data_object']);
            },
        }),
        index_goal_data_ref_time_field: createFieldConfig(vm, 'index_goal_data_ref_time_field', {
            type: 'select',
            required: true,
            cascade: {
                optionsParent: 'index_goal_data_object',
            },
            disabled: (form) => !form.index_goal_data_object || vm.isEdit,
            lazyLoad() {
                return API.getDescribeFields(vm.formData['index_goal_data_object']).then((objectDescribe) => {
                    return API.parseFieldsList(objectDescribe.fields || [], (field) => field.type === 'date' || field.type === 'date_time', {filterSystem: false});
                })
            }
        }),
        index_goal_data_ref_contract_field: createFieldConfig(vm, 'index_goal_data_ref_contract_field', {
            type: 'select',
            required: true,
            cascade: {
                optionsParent: 'index_goal_data_object',
                setDefaultValue: true,
            },
            labelTips: $t('sfa.crm.setting_tradeconfigure.is_open_contract_progress_data_source_goal_data_ref_contract_field_help_title'),
            visible() {
                return this.options.length > 1;
            },
            disabled: (form) => !form.index_goal_data_object || vm.isEdit,
            lazyLoad() {
                return API.getDescribeFields(vm.formData['index_goal_data_object']).then((objectDescribe) => {
                    return API.parseFieldsList(objectDescribe.fields || [], (field) => field.target_api_name === 'SaleContractObj', {filterSystem: true});
                })
            }
        }),
        index_goal_data_condition: createFieldConfig(vm, 'index_goal_data_condition', {
            type: 'filter',
            cascade: {
                renderParent: 'index_goal_data_object',
                triggerRender: true,
            },
            disabled: form => !form.index_goal_data_object,
            manualGetValue: true,
            async customRender(config, field, defaultValue, wrapper) {
                try {
                    if (!wrapper) return;
                    const apiName = vm.formData['index_goal_data_object']
                    if (!apiName) {
                        wrapper.innerHTML = `<div class="crm-filter-group-empty">${$t('请先选择') + vm.formDataConfig['index_goal_data_object']?.label || ''}</div>`
                        return null;
                    };
                    wrapper.innerHTML = $t('加载中') + '...'
                    const FilterGroup = await crmRequire('crm-modules/common/filtergroup/filtergroup')
                    const describe = await API.getDescribeFields(apiName)
                    const filterFields = {};
                    _.each(describe.fields, (fieldDescribe, _field) => {
                        if (fieldDescribe.is_index) {
                            filterFields[_field] = fieldDescribe;
                        }
                    })

                    const NFilterGroup = FilterGroup.extend({
                        getValue() {
                            const value = FilterGroup.prototype.getValue.apply(this, arguments);
                            if (value) {
                                const filters = JSON.parse(value)?.[0]?.filters || [];
                                if (filters.length > 0) {
                                    return JSON.stringify({
                                        limit: 2000,
                                        offset: 0,
                                        orders: [{isAsc: false, fieldName: "last_modified_time"}],
                                        filters,
                                    });
                                }
                            }
                            return null;
                        }
                    })

                    let formatDefaultValue = null;
                    try {
                        const filters = (defaultValue && JSON.parse(defaultValue)?.filters || []).map((item) => ({
                            ...item,
                            field_name: item.type === 'object_reference' ? item.field_name.split('.')[0] : item.field_name,
                        }));

                        if (filters.length > 0) {
                            formatDefaultValue = [{
                                filters,
                                connector: 'OR'
                            }]
                        }
                    } catch (error) {
                        console.error(`customRender for ${field} failed:`, error)
                    }

                    const filter = new NFilterGroup({
                        $wrapper: $(wrapper),
                        fields: filterFields,
                        width: 800,
                        defaultValue: formatDefaultValue, // 使用解析后的值
                        OR_MAX: 1,
                        lastSelectCanEmpty: true,
                        filterType: [],
                        layoutMode: 'custom',
                        getCustItemWidth(level) {
                            return [183, 183, 183][level];
                        },
                        formatGetItem(item) {
                            const [field_name, operator, field_values, type] = item;
                            if (type === 'object_reference') {
                                item[0] = field_name + '.name';
                            }
                        },
                    })
                    vm.formDataConfig['index_goal_data_condition'].customRenderInstance = filter;

                    return filter;
                } catch (error) {
                    console.error(`customRender for ${field} failed:`, error)
                    if (wrapper) wrapper.innerHTML = 'Error loading component.'; // Provide user feedback
                    return null;
                }

            },
            rules: [ // 校验逻辑保持不变
                {
                    validator(rule, value, callback) {
                        const comp = vm.formDataConfig['index_goal_data_condition'].customRenderInstance
                        if (comp && comp.valid()) {
                            callback();
                            return;
                        }
                        callback(new Error($t('请填写完整')))
                    }
                }
            ]
        })
    }
}

export const formStepConfig = [
    {
        title: $t('sfa.crm.setting_tradeconfigure.is_open_contract_progress_basic_info'),
        fields: ['name', 'description', 'enabled_status', 'contract_record_type']
    },
    {
        title: $t('sfa.crm.setting_tradeconfigure.is_open_contract_progress_index_config'),
        fields: ['index_type', 'index_type_object_field', 'index_type_object_field_value', 'index_type_category', 'index_type_calc_type']
    },
    {
        title: $t('sfa.crm.setting_tradeconfigure.is_open_contract_progress_data_source'),
        fields: ['index_goal_object', 'index_goal_object_field', 'index_goal_data_object', 'index_goal_data_ref_product_field', 'index_goal_data_object_field', 'index_goal_data_ref_contract_field', 'index_goal_data_ref_time_field', 'index_goal_data_condition']
    },
]

// 列表相关配置
export const columnsDataConfig = {
    name: {
        prop: 'name',
        minWidth: '120'
    },
    description: {
        prop: 'description',
        minWidth: '180'
    },
    enabled_status: {
        prop: 'enabled_status',
        minWidth: '80',
        type: 'tag',
        formatter(row) {
            const options = this.getRuleDescribeField('enabled_status')?.options || [];
            return {
                type: row.enabled_status ? 'success' : 'info',
                label: options.find(item => item.value === row.enabled_status)?.label || row.enabled_status,
                size: 'mini'
            }
        }
    },
    last_modified_time: {
        prop: 'last_modified_time',
        minWidth: '120',
        formatter(row) {
            return row.last_modified_time ? CRM.util.formatTime(row.last_modified_time) : ''
        }
    },
    actions: {
        prop: 'actions',
        label: $t('操作'),
        fixed: 'right',
        width: '180',
        type: 'action',
        actions: ({row}) => [
            {label: $t('编辑'), action: 'edit'},
            {label: $t('删除'), action: 'delete'}
        ]
    }
}

const allowNumFiledsFilter = (field) => (
    ['currency', 'number'].includes(field.type) ||
    (['formula', 'count'].includes(field.type) && ['currency', 'number'].includes(field.return_type))
)

const DESCRIBE_FIELDS_MAP = new Map();

export const API = {
    getRecordTypes() {
        return CRM.util.getRecordType({
            describeApiName: 'SaleContractObj',
            is_only_active: true,
        }).then((res) => ([{label: $t('全部业务类型'), value: 'all'}, ...res.map(item => ({label: item.label, value: item.api_name}))]));
    },
    parseFieldsList(fields, filterFunc, {filterSystem = false, whitelist = [], formatFunc} = {}) {
        return Object.values(fields).filter((item) => 
            whitelist.includes(item.api_name) ||
            (
                filterFunc(item) &&
                (filterSystem ? item.define_type !== 'system' : true)
            )
        ).map(formatFunc ?? (item => ({value: item.api_name, label: item.label})));
    },
    async getDescribeFields(apiname) {
        if (!apiname) return {fields: []};
        if (DESCRIBE_FIELDS_MAP.has(apiname)) {
            return DESCRIBE_FIELDS_MAP.get(apiname);
        }
        return this.fetchDescribeFields(apiname);
    },
    fetchDescribeFields(apiname) {
        return ajaxBase('/EM1HNCRM/API/v1/object/' + apiname + '/controller/DescribeLayout', {
            apiname,
            include_layout: false,
            include_detail_describe: false,
            include_ref_describe: false
        }).then((res = {}) => {
            const {objectDescribe} = res;
            DESCRIBE_FIELDS_MAP.set(apiname, objectDescribe);
            return objectDescribe;
        });
    },
    getObjectRefNumFields(apiname) {
        return this.getDescribeFields(apiname).then((objectDescribe) => {
            return this.parseFieldsList(objectDescribe.fields || [], (field) => allowNumFiledsFilter(field), {filterSystem: true});
        })
    },
    getObjectRefProductFields(apiname) {
        return this.getDescribeFields(apiname).then((objectDescribe) => {
            return this.parseFieldsList(objectDescribe.fields || [], (field) => field.target_api_name === 'ProductObj', {filterSystem: true, formatFunc: (_field) => ({
                label: objectDescribe.display_name + '.' + _field.label,
                value: objectDescribe.api_name + '.' + _field.api_name,
            })});
        })
    },
    getDetailRelatedObjectRefProductFields(masterApiName) {
        return this.getDetailRelatedObjectFields(masterApiName).then((detailDescribeList) => {
            return detailDescribeList.map((item) => {
                return this.parseFieldsList(
                    item.fields,
                    (field) => field.target_api_name === 'ProductObj',
                    {
                        filterSystem: true,
                        formatFunc: (_field) => ({
                            label: item.display_name + '.' + _field.label,
                            value: item.api_name + '.' + _field.api_name,
                        })
                    }
                )
            }).flat();
        })
    },
    getDetailRelatedObjectFields(apiname) {
        return this.getRelatedObjectList(apiname, {includeDetailList: true, includeRefList: true})
        .then((res = {}) => {
            const {detailDescribeList = []} = res;
            detailDescribeList.forEach((item) => {
                DESCRIBE_FIELDS_MAP.set(item.api_name, item);
            })
            return detailDescribeList;
        })
    },
    getRelatedObjectList(apiname, params = {}) {
        return ajaxBase('/EM1HNCRM/API/v1/object/describe/service/findRelatedObjectList', {
            describeApiName: apiname,
            includeDetailList: true,
            includeRefList: true,
            includeWhatList: false,
            sourceInfo: 'object_management',
            includeBigObject: false,
            includeSocialObject: false,
            ...params
        });
    },
    getContractRelatedObjectList() {
        const blockList = ['ContractProgressRuleGoalObj'];
        return this.getRelatedObjectList('SaleContractObj').then((res = {}) => {
            const {detailDescribeList = [], lookupDescribeList = []} = res;
            const result = [];
            [...detailDescribeList, ...lookupDescribeList].forEach((item) => {
                const fields = Object.values(item.fields || {});
                if (blockList.includes(item.api_name)) return;
                result.push({
                    label: item.display_name,
                    value: item.api_name,
                    numFields: fields.filter((field) => allowNumFiledsFilter(field)).map((field) => ({
                        label: field.label,
                        value: field.api_name,
                    })),
                    refContractFields: fields.filter((field) => field.target_api_name === 'SaleContractObj').map((field) => ({
                        label: field.label,
                        value: field.api_name,
                    })),
                    refProductFields: fields.filter((field) => field.target_api_name === 'ProductObj').map((field) => ({
                        label: field.label,
                        value: field.api_name,
                    }))
                });
                DESCRIBE_FIELDS_MAP.set(item.api_name, item);
            });
            return result;
        });
    },
    getContractProgressRuleList() {
        return ajaxBase('/EM1HNCRM/API/v1/object/contract/service/queryRuleList', {object_describe_api_name: 'ContractProgressRuleObj'}, {parseValueSuccess: true});
    },
    createContractProgressRule(data) {
        return ajaxBase('/EM1HNCRM/API/v1/object/contract/service/addRule', data, {parseValueSuccess: true});
    },
    updateContractProgressRule(data) {
        return ajaxBase('/EM1HNCRM/API/v1/object/contract/service/editRule', data, {parseValueSuccess: true});
    },
    deleteContractProgressRule(_id) {
        return ajaxBase('/EM1HNCRM/API/v1/object/contract/service/invalidRule', {_id}, {parseValueSuccess: true});
    }
}
