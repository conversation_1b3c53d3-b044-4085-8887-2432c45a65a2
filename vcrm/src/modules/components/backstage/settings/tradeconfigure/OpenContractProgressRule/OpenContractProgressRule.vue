<!--
 * @Descripttion: 合同进度规则配置
 * @Author: chaoxin
 * @Date: 2024-04-10
-->
<template>
    <div class="open-contract-progress-rule">
        <div class="table-actions">
            <fx-button icon="fx-icon-add-2" size="mini" plain @click="handleAction('add')">{{ $t('sfa.crm.setting_tradeconfigure.is_open_contract_progress_add_rule') }}</fx-button>
        </div>
        <fx-table :data="tableData" max-height="300px">
            <fx-table-column
                v-for="(config, field) in columnsDataConfig"
                :key="field"
                :prop="config.prop"
                :label="getColumnsLabel(field, config)"
                :width="config.width"
                :min-width="config.minWidth"
                :fixed="config.fixed">
                <template slot-scope="scope">
                    <template v-if="config.type === 'tag'">
                        <fx-tag v-bind="formatter(config, scope.row)">
                            {{ formatter(config, scope.row).label }}
                        </fx-tag>
                    </template>
                    <template v-else-if="config.type === 'action'">
                        <span class="column-actions">
                            <fx-link 
                                v-for="ac in config.actions(scope)" 
                                :key="ac.action"
                                type="standard" 
                                size="mini" 
                                :underline="false"
                                @click="handleAction(ac.action, scope)">
                                {{ ac.label }}
                            </fx-link>
                        </span>
                    </template>
                    <template v-else>
                        <span>{{ formatter(config, scope.row) }}</span>
                    </template>
                </template>
            </fx-table-column>
        </fx-table>

        <!-- 新建/编辑弹窗 -->
        <dialog-form 
            v-if="dialogVisible" 
            :visible.sync="dialogVisible"
            :item-data="currentItem"
            :record-types="recordTypes"
            :related-objects="relatedObjects"
            :rule-describe-fields="progressRuleDescribeFields"
            @confirm="handleDialogConfirm" />
    </div>
</template>

<script>
import DialogForm from './DialogForm.vue'
import { API, columnsDataConfig } from './utils'

export default {
    name: 'OpenContractProgressRule',
    components: {
        DialogForm
    },
    data() {
        return {
            progressRuleDescribeFields: {},
            columnsDataConfig,
            tableData: [
            ],
            dialogVisible: false,
            currentItem: null,
            itemIndex: -1,
            recordTypes: [],
            relatedObjects: [],
            actionStrategies: {
                add: ({row, index}) => {
                    this.itemIndex = -1
                    this.currentItem = null
                    this.dialogVisible = true
                },
                edit: ({row, index}) => {
                    this.itemIndex = index
                    this.currentItem = {...row}
                    this.dialogVisible = true
                },
                toggle: ({row, index}) => {
                    this.toggleStatus(index)
                },
                delete: ({row, index}) => {
                    this.deleteItem(index)
                }
            }
        }
    },
    async created() {
        this.fetchRuleData();
        API.getDescribeFields('ContractProgressRuleObj').then((res) => {
            this.progressRuleDescribeFields = res.fields;
        })
        API.getRecordTypes().then((res) => {
            this.recordTypes = res;
        })
        API.getContractRelatedObjectList().then((res) => {
            this.relatedObjects = res;
        })
    },
    methods: {
        getRuleDescribeField(field) {
            return this.progressRuleDescribeFields[field] || {};
        },
        getColumnsLabel(field, config) {
            return this.getRuleDescribeField(field)?.label || config.label || field
        },
        formatter(config, row) {
            if (config.formatter) {
                return config.formatter.call(this, row)
            }
            return row[config.prop]
        },
        async fetchRuleData() {
            const res = await API.getContractProgressRuleList()
            this.tableData = res;
        },
        handleAction(action, {row, $index} = {}) {
            const strategy = this.actionStrategies[action]
            if (strategy) {
                strategy({row, index: $index})
            }
        },
        async handleDialogConfirm(data) {
            try {
                if (!data._id) {
                    await API.createContractProgressRule(data)
                    this.tableData.unshift(data);
                } else {
                    await API.updateContractProgressRule(data)
                    this.$set(this.tableData, this.itemIndex, data);
                }
                this.fetchRuleData();
                this.dialogVisible = false;
                this.$message.success($t('操作成功'));
            } catch (error) {
                this.$message.error(error || $t('操作失败'));
            }
        },
        async toggleStatus(index) {
            const item = this.tableData[index];
            item.enabled_status = !item.enabled_status;
            // TODO: 调用后端API更新状态
        },
        async deleteItem(index) {
            await this.$confirm($t('sfa.crm.setting_tradeconfigure.is_open_contract_progress_delete_confirm'), $t('提示'), {
                type: 'warning'
            });
            try {
                await API.deleteContractProgressRule(this.tableData[index]?._id)
                this.tableData.splice(index, 1);
                this.$message.success($t('操作成功'));
            } catch (error) {
                this.$message.error(error || $t('操作失败'));
            }
        },
    }
}
</script>

<style lang="less" scoped>
.open-contract-progress-rule {
    padding: 16px;
    padding-top: 0;

    .table-actions {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 8px;
    }
    .column-actions {
        display: flex;
        column-gap: 12px;
        white-space: nowrap;
        flex-wrap: wrap;
    }
}
</style>
