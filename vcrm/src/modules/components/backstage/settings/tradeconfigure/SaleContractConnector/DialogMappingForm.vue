<template>
    <fx-dialog custom-class="sale-contract-connector-mapping-form" width="600px" min-height="300px" max-height="500px" :title="title" :visible="visible" :close-on-click-modal="false" @close="handleClose">
        <div class="content crm-scroll">
            <div v-for="(item, index) in ruleList">
                <mapping-form
                    :ref="'rMappingForm' + index"
                    :sourceDisplayName="item.source_api_name === sourceMasterApiName ? $t('sfa.crm.setting_tradeconfigure.contract_connector_mapping_source_master') : $t('sfa.crm.setting_tradeconfigure.contract_connector_mapping_source_detail')"
                    :targetDisplayName="$t('对象名称')"
                    :sourceApiName="item.source_api_name"
                    :targetApiName="item.target_api_name"
                    :sourceOptions="item.source_api_name === sourceMasterApiName ? sourceMasterOptions : sourceDetailOptions"
                    :fieldsMappings="item.field_mapping"
                />
            </div>
            <span class="add-line" @click="handleAddMappingForm"><span class="fx-icon-add"></span><span>{{ $t('添加分组') }}</span></span>
        </div>
        <div slot="footer">
            <fx-button size="small" type="primary" @click="handleConfirm">{{ $t('保存') }}</fx-button>
            <fx-button size="small" @click="handleClose">{{ $t('取消') }}</fx-button>
        </div>
    </fx-dialog>
</template>

<script>
import {apiRequestFieldOptions} from './utils'
import MappingForm from './MappingForm.vue'
    export default {
        name: 'DialogMappingForm',
        components: {
            MappingForm,
        },
        props: {
            visible: {
                type: Boolean,
                default: false
            },
            itemData: {
                type: Object,
            },
            value: {
                type: Object,
                default: () => {
                    return {
                        describe_api_name: '',
                        connector: '',
                        rule_list: [],
                    }
                }
            },
        },
        data() {
            return {
                title: $t('字段映射'),
                ruleList: this.value.rule_list,
                sourceMasterApiName: 'SaleContractObj',
                sourceDetailApiName: 'SaleContractLineObj',
                sourceMasterOptions: [],
                sourceDetailOptions: [],
            }
        },
        created() {
            this.fetchOptions();
            this.initRuleList();
        },
        methods: {
            initRuleList() {
                let res = [];
                if (this.value.rule_list?.length) {
                    res = this.value.rule_list;
                } else {
                    res.push({
                        source_api_name: this.sourceMasterApiName,
                        target_api_name: '',
                        field_mapping: [],
                    }, {
                        source_api_name: this.sourceDetailApiName,
                        target_api_name: '',
                        field_mapping: [],
                    })
                }
                this.ruleList = res;
            },
            async fetchOptions() {
                const parseFields = (fields) => {
                    return Object.values(fields).map((field) => ({
                        label: field.label,
                        value: field.api_name,
                    }))
                }
                apiRequestFieldOptions(this.sourceMasterApiName, true).then(({fields}) => {
                    this.sourceMasterOptions = parseFields(fields);
                });
                apiRequestFieldOptions(this.sourceDetailApiName, true).then(({fields}) => {
                    this.sourceDetailOptions = parseFields(fields);
                });
            },
            handleAddMappingForm() {
                this.ruleList.push({
                    source_api_name: this.sourceDetailApiName,
                    target_api_name: '',
                    field_mapping: [],
                })
            },
            handleConfirm() {
                const rMappingForms = Object.entries(this.$refs).filter(([key, value]) => key.startsWith('rMappingForm')).map(([key, value]) => value?.[0]);
                if (rMappingForms.some((rMappingForm) => !rMappingForm?.validate())) return;
                const rule_list = rMappingForms.map((rMappingForm) => rMappingForm.getValue()).filter((item) => item.target_api_name || (item.field_mapping?.length && item.field_mapping.every((item) => item.source_field_api_name && item.target_field_api_name)));
                this.$emit('confirm', {
                    describe_api_name: this.sourceMasterApiName,
                    connector: this.itemData.connectApiName,
                    rule_list
                });
            },
            handleClose() {
                this.$emit('update:visible', false);
            }
        }
    }
</script>

<style lang="less" scoped>
.content {
    display: flex;
    flex-direction: column;
    gap: 16px;

    .add-line {
        align-self: flex-start;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        color: var(--color-primary06);
        cursor: pointer;

        .fx-icon-add {
            font-size: 16px;
            &:before {
                color: var(--color-primary06);
            }
        }
    }
}
</style>