<template>
    <div class="field-mapping-item-form">
        <div class="field-mapping-item-title">
            <h2>{{ sourceDisplayName }}</h2>
            <span></span>
            <div class="target">
                <h2>{{ targetDisplayName }}</h2>
                <fx-input size="small" v-model="dTargetApiName" />
            </div>
        </div>
        <mapping-form-item
            ref="rFieldMappingItem"
            v-for="(item, index) in dFieldMappings"
            :key="item.source_field_api_name"
            :selectedSourceFiled="selectedSourceFiled"
            :selectedTargetFiled="selectedTargetFiled"
            :sourceOptions="sourceOptions"
            :targetOptions="targetOptions"
            :data="item"
            @update:data="(data) => handleAction('update', {index, data})"
            @del="handleAction('del', {index})"
        ></mapping-form-item>
        <span class="add-line" @click="handleAction('add')"><span class="fx-icon-add"></span><span>{{ $t('添加') }}</span></span>
    </div>
</template>

<script>
import MappingFormItem from './MappingFormItem.vue'
    export default {
        name: 'MappingForm',
        components: {
            MappingFormItem,
        },
        props: {
            sourceDisplayName: {
                type: String,
            },
            targetDisplayName: {
                type: String,
            },
            sourceOptions: {
                type: Array,
                default: () => []
            },
            targetOptions: {
                type: Array,
                default: () => []
            },
            fieldsMappings: {
                type: Array,
                default: () => []
            },
            sourceApiName: {
                type: String,
                default: ''
            },
            targetApiName: {
                type: String,
                default: ''
            },
        },
        data() {
            return {
                dTargetApiName: this.targetApiName,
                dFieldMappings: this.fieldsMappings,
            }
        },
        computed: {
            selectedSourceFiled() {
                return this.dFieldMappings.map(item => item.source_field_api_name);
            },
            selectedTargetFiled() {
                return this.dFieldMappings.map(item => item.target_field_api_name);
            }
        },
        created() {
            if (!this.dFieldMappings.length) {
                this.handleAction('add');
            }
        },
        methods: {
            handleAction(action, {index, data} = {}) {
                if (action === 'del') {
                    this.dFieldMappings.splice(index, 1);
                } else if (action === 'add') {
                    this.dFieldMappings.push({
                        source_field_api_name: '',
                        target_field_api_name: '',
                    })
                } else if (action === 'update') {
                    this.$set(this.dFieldMappings, index, data);
                }
            },
            validate() {
                // const valid = (this.$refs.rFieldMappingItem || []).every(item => item.validate());
                // let errorMsg = '';
                // if (!valid) {
                //     errorMsg = $t('crm.setting.order_payment_mapping_rule.warning_fill_source_and_target_field');
                // } else if (this.fieldsMappings.length === 0) {
                //     errorMsg = $t('crm.setting.order_payment_mapping_rule.warning_add_mapping_relation');
                // }
                // if (errorMsg) {
                //     this.$message({
                //         message: errorMsg,
                //         type: 'error'
                //     })
                //     return false;
                // }
                return true;
            },
            getValue() {
                if (this.validate()) {
                    return {
                        source_api_name: this.sourceApiName,
                        target_api_name: this.dTargetApiName,
                        field_mapping: this.dFieldMappings
                    };
                }
                return [];
            }
        }
    }
</script>

<style lang="less" scoped>
.field-mapping-item-form {
    display: flex;
    flex-direction: column;
    gap: 14px;
    border-radius: 4px;
    border: 1px solid var(--color-neutrals05);
    padding: 8px;

    .field-mapping-item-title {
        display: flex;
        gap: 12px;
        align-items: center;

        & > h2 {
            // font-size: 12px;
            line-height: 18px;
            flex-basis: 240px;
            color: var(--color-neutrals19);
        }

        span {
            flex-basis: 20px;
        }

        .target {
            display: flex;
            align-items: center;
            flex: 1;
            padding-right: 28px;
            gap: 8px;

            h2 {
                white-space: nowrap;
            }
        }
    }
}
.add-line {
    align-self: flex-start;
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 14px;
    color: var(--color-primary06);
    cursor: pointer;

    .fx-icon-add {
        font-size: 16px;
        &:before {
            color: var(--color-primary06);
        }
    }
}
</style>