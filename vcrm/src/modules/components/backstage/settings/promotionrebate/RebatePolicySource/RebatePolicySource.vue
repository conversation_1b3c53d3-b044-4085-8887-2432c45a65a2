<template>
    <div class="backstage-rebate_policy_source">
        <div class="crm-module-title">
            <p>{{listData.title}}</p>
            <p class="subtitle" v-if="listData.subTitle">{{listData.subTitle}}</p>
            <p class="line" v-if="listData.showTitleLine" />
        </div>
        <div class="content">
            <div class="object-item-list">
                <div class="object-item" v-for="(opt, i) in cValueOptions" :key="i">
                    <div class="title">
                        <h4>{{opt.api_name__r}}</h4>
                        <div class="action">
                            <fx-button size="micro" v-if="opt.is_edit" @click="handleQuicklyAction('edit', opt)">{{$t('编辑')}}</fx-button>
                            <fx-button size="micro" v-if="opt.is_del" @click="handleQuicklyAction('del', opt)">{{$t('删除')}}</fx-button>
                            <fx-button size="micro" type="primary" v-if="!opt.used" @click="handleQuicklyAction('open', opt)">{{$t('启用')}}</fx-button>
                            <span v-if="opt.used" class="opened">{{$t('已启用')}}<span class="fx-icon-ok-2"></span></span>
                        </div>
                    </div>
                    <div class="description">
                        <p>{{opt.description}}</p>
                    </div>
                </div>
            </div>
            <div class="add-btn circle-add-btn" @click="handleAddClick(false)">
                <i class="fx-icon-add"></i>
                <span>{{$t('crm.setting.promotionrebate.rebate_policy_source_add_more', null, '增加来源')}}</span>
            </div>
        </div>
        <dialog-form
            :dialog-form-visible="dialogFormVisible"
            :api-name-options="cApiNameOptions"
            :p-form-data="formData"
            :changeLoading="changeLoading"
            @close="dialogFormVisible = false"
            @confirm="updateObjectList"
        />
    </div>
</template>

<script>
import DialogForm from "./DialogForm";
import {ajax} from "@common/utils";
// 返利产生来源系统预置四个对象：销售订单、开票申请、发货单、回款
const PRESET_API_NAMES = ['SalesOrderObj', 'InvoiceApplicationObj', 'DeliveryNoteObj', 'PaymentObj'];
export default {
    name: "RebatePolicySource",
    components: {
        DialogForm,
    },
    props: {
        listData: {
            type: Object,
            require: true
        },
        index: {
            type: Number
        },
        changeLoading: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            dialogFormVisible: false,
            apiNameOptions: [],
            objectList: [],
            formData: {},
        }
    },
    computed: {
        cValue() {
            return _.isString(this.listData.value) ? JSON.parse(this.listData.value) : this.listData.value || [];
        },
        cValueOptions() {
            return this.cValue.map((item) => {
                const {api_name, api_name__r, used, detail_api_name} = item;
                const isPreset = PRESET_API_NAMES.includes(api_name);
                return {
                    ...item,
                    description: !detail_api_name ?
                        $t('crm.setting.promotionrebate.rebate_policy_source_description_master', {name: api_name__r}, `按${api_name__r}主对象数据产生返利`) :
                        $t('crm.setting.promotionrebate.rebate_policy_source_description', {name: api_name__r}, `按${api_name__r}主从对象数据产生返利`),
                    is_edit: isPreset ? false :(used && !detail_api_name) || !used,
                    is_del: isPreset ? false : !used,
                }
            })
        },
        cApiNameOptions() {
            const selected = this.cValue
                .map((c) => c.api_name)
                .concat(PRESET_API_NAMES)
                .filter((a) => a !== this.formData.api_name);
            return this.apiNameOptions.filter((item) => {
                return !selected.includes(item.api_name);
            });
        }
    },
    created() {
        this.getFormApiNameOptions();
    },
    methods: {
        handleAddClick(isEdit = false, data) {
            this.formData = isEdit ? data : {};
            this.dialogFormVisible = true;
        },
        // 新增编辑删除
        updateObjectList(dataItem, isDel = false) {
            const objectList = JSON.parse(JSON.stringify(this.cValue));
            const index = objectList.findIndex((obj) => obj.api_name === dataItem.api_name);
            if (index !== -1) {
                if (isDel) {
                    objectList.splice(index, 1);
                } else {
                    objectList.splice(index, 1, dataItem);
                }
            } else {
                objectList.push(dataItem);
            }
            this.$emit('change', {key:this.listData.key, value: JSON.stringify(objectList)});
        },
        handleQuicklyAction(type, {api_name}) {
            const me = this;
            const confirm = (msg, cb) => {
                this.$confirm(msg, $t('提示'), {
                    beforeClose(action, instance, done) {
                        if (action === 'confirm') {
                            instance.confirmButtonLoading = true;
                            cb();
                            const unwatch = me.$watch('changeLoading', (loading) => {
                                if (loading === false) {
                                    instance.confirmButtonLoading = false;
                                    done();
                                    unwatch();
                                }
                            })
                        } else {
                            done();
                        }
                    }
                })
            }
            const oData = this.cValue.find((d) => d.api_name === api_name);
            if (type === 'edit') {
                this.handleAddClick(true, oData);
            } else if (type === 'del') {
                confirm(
                    $t('crm.setting.promotionrebate.rebate_policy_source_del_confirm', null, '确认删除？'),
                    () => {me.updateObjectList(oData, true);}
                )
            } else if (type === 'open') {
                confirm(
                    $t('crm.setting.promotionrebate.rebate_policy_source_used_confirm', null, '启用该来源后，将不可取消，是否继续启用？'),
                    () => {me.updateObjectList({...oData, used: true});}
                )
            }
        },

        getFormApiNameOptions() {
            ajax({
                data: {
                    // describeDefineType: "custom",
                    isIncludeFieldDescribe: false,
                    isIncludeUnActived: true,
                    isIncludeSystemObj: true,
                    packageName: "CRM",
                    sourceInfo: "promotionrebate"
                },
                url: "/EM1HNCRM/API/v1/object/describe/service/findDescribeManageList"
            }).then((res) => {
                this.apiNameOptions = (res.objectDescribeList || []).filter((item) => item.is_active).map((item) => {
                    const {api_name, display_name} = item;
                    return {
                        ...item,
                        label: display_name,
                        value: api_name
                    }
                });
            })
        },

    }
}
</script>

<style lang="less">
.backstage-rebate_policy_source-dialog {
    .el-form-item {
        .el-form-item__label {
            text-align: left;
        }
    }

    h3 {
        font-size: 16px;
        line-height: 24px;
        color: #212B36;
        display: inline-block;
        padding-bottom: 8px;
    }
}


.backstage-rebate_policy_source {
    * {
        box-sizing: border-box;
    }
    .content {
        margin: 0 16px 16px;

        .object-item-list {
            display: flex;
            flex-wrap: wrap;
        }

        .object-item {
            border: 1px solid var(--color-neutrals05);
            border-radius: 4px;
            padding: 8px;
            width: calc((100% - 16px)/2);
            margin-bottom: 8px;

            &:nth-child(odd) {
                margin-right: 16px;
            }

            .title {
                display: flex;
                justify-content: space-between;
                margin-bottom: 16px;

                h4 {
                    font-size: 15px;
                    font-weight: 700;
                    color: var(--color-neutrals19);
                    line-height: 24px;
                }

                .el-button {
                    color: var(--color-neutrals19);
                }
                .el-button--primary {
                    color: var(--color-neutrals01);
                }

                .el-button+.el-button {
                    margin-left: 6px;
                }

                .action {
                    .opened {
                        font-size: 14px;
                        line-height: 18px;
                        color: var(--color-neutrals15);
                    }
                }
            }
            .description {
                font-size: 14px;
                color: var(--color-neutrals19);
                line-height: 18px;
            }
        }

        .circle-add-btn {
            cursor: pointer;
            display: flex;
            align-items: center;
            display: inline-block;

            i {
                font-size: 16px;
                &:before {
                    color: var(--color-primary06);
                }
            }

            span {
                font-size: 14px;
                line-height: 18px;
                color: var(--color-primary06);
                margin-left: 4px;
            }
        }
    }
}
</style>