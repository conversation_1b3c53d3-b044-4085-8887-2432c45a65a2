<template>
    <fx-dialog
        :title="title"
        ref="rDialog"
        :visible="dialogFormVisible"
        @close="() => $emit('update:dialogFormVisible', false)"
        custom-class="field-mapping-form order_payment_mapping_rule"
    >
        <div class="content crm-scroll" v-if="!initLoading">
            <!-- 字段映射组 -->
            <div
                class="field-mapping-item-group-wrapper"
                :class="{'is-detail-item': index > 0}"
                v-for="(config, index) in mappingConfigs"
                :key="index"
            >
                <p class="field-mapping-item-group-title" v-if="index > 0">{{ $t('crm.setting.order_payment_mapping_rule.field_mapping_add_sub_obj_title') }}</p>
                <field-mapping-item-group
                    :ref="`rFieldMappingItemGroup_${index}`"
                    :sourceApiName="config.sourceApiName"
                    :targetApiName="config.targetApiName"
                    :isMasterObj="config.isMasterObj"
                    :fieldsMapping="config.fieldsMapping"
                    :readOnlyFields="config.readOnlyFields"
                    :allSourceFieldDesc="allSourceFieldDesc"
                    :allTargetFieldDesc="allTargetFieldDesc"
                    :selectedSourceApiNames="selectedSourceApiNames"
                    :selectedTargetApiNames="selectedTargetApiNames"
                    :customConfig="customConfig"
                    :customFieldsProcess="customFieldsProcess"
                    @update:fieldsMapping="(value) => updateMappingConfig(index, 'fieldsMapping', value)"
                    @update:sourceApiName="(value) => updateMappingConfig(index, 'sourceApiName', value)"
                    @update:targetApiName="(value) => updateMappingConfig(index, 'targetApiName', value)"
                    @delMappingGroup="handleDelMappingGroup(index)"
                />
            </div>
            <!-- 添加从对象按钮 -->
            <div class="add-mapping-group" v-if="includeDetail && mappingConfigs.length < maxMappingCount">
                <fx-button type="text" @click="handleAddMappingGroup" size="small">
                    <span class="fx-icon-add"></span>
                    <span>{{ $t('添加从对象映射') }}</span>
                </fx-button>
            </div>

        </div>
        <div slot="footer" class="dialog-footer">
            <fx-button type="primary" @click="handleAddConfirm" size="small" :loading="confirmLoading">{{$t('确 定')}}</fx-button>
            <fx-button @click="handleCancel" size="small">{{$t('取 消')}}</fx-button>
        </div>
    </fx-dialog>
</template>

<script>
import FieldMappingItemGroup from './FieldMappingItemGroup'
import {apiRequestCreateUpdateRule, apiRequestFieldOptions, apiRequestGetMappingDetail, parseFieldOptions} from './utils/index'

export default {
    name: 'FieldMappingForm',
    components: {
        FieldMappingItemGroup
    },
    props: {
        // 是否主从一起新建
        includeDetail: {
            type: Boolean,
            default: false
        },
        dialogFormVisible: {
            type: Boolean,
            default: false
        },
        // TODO 弃用，明确传入ruleApiName\sourceApiName代替
        data: {
            // {objectApiName, fieldApiName, ruleApiName, masterRuleApiName}
            type: Object,
            default: () => ({})
        },
        ruleApiName: {
            type: String,
        },
        sourceApiName: {
            type: String,
        },
        targetApiName: {
            type: String,
        },
        confirmLoading: {
            type: Boolean,
            default: false
        },
        // 设置source主对象的只读字段
        readOnlyFields: {
            type: Array,
            default: () => []
        },
        // *** 字段映射字段处理 ***
        customConfig: {
            type: Object,
            default: void 0
        },
        customFieldsProcess: {
            type: Function,
            default: null
        },
        // 直接处理描述完成的结果
        parseFetchFields: {
            type: Function,
        },
        // *** 字段映射字段处理 ***
        maxMappingCount: {
            type: Number,
            default: 1
        }
    },
    data() {
        return {
            initLoading: false,
            title: $t('crm.setting.order_payment_mapping_rule.field_mapping_title'),
            // 映射配置数组，支持多个对象映射
            mappingConfigs: [],
            // 所有源字段描述
            allSourceFieldDesc: [],
            // 所有目标字段描述
            allTargetFieldDesc: []
        }
    },
    created() {
        this.initMasterConfig();
        this.initFetch();
    },
    computed: {
        cRuleApiName() {
            return this.data?.ruleApiName || this.ruleApiName;
        },
        cSourceApiName() {
            return this.data?.objectApiName || this.sourceApiName;
        },
        selectedSourceApiNames() {
            return this.mappingConfigs.map(config => config.sourceApiName).filter(Boolean);
        },
        selectedTargetApiNames() {
            return this.mappingConfigs.map(config => config.targetApiName).filter(Boolean);
        }
    },
    methods: {
        initMasterConfig() {
            // 初始化主对象配置
            this.handleAddMappingGroup({
                sourceApiName: this.cSourceApiName,
                targetApiName: this.targetApiName,
                isMasterObj: true,
                readOnlyFields: this.readOnlyFields
            });
        },
        updateMappingConfig(index, key, value) {
            if (this.mappingConfigs[index]) {
                // 使用 Vue.set 确保响应式更新
                this.$set(this.mappingConfigs[index], key, value);
            }
        },
        handleDelMappingGroup(index) {
            this.mappingConfigs.splice(index, 1);
        },
        handleAddMappingGroup(configs) {
            // 添加新的从对象映射组
            const newConfig = {
                sourceApiName: '',
                targetApiName: '',
                isMasterObj: false,
                fieldsMapping: [{}],
                readOnlyFields: [],
                ...configs
            };
            this.mappingConfigs.push(newConfig);
        },
        async initFetch() {
            this.initLoading = true;

            await Promise.all([
                this.fetchOptions(),
                this.fetchMappingDetail(),
            ]);
            this.initLoading = false
        },
        async fetchOptions() {
            const fetchTasks = [];

            // 添加源对象获取任务
            if (this.cSourceApiName) {
                fetchTasks.push(this.fetchFieldOptions(this.cSourceApiName, 'source'));
            }

            // 添加目标对象获取任务（如果与源对象不同）
            if (this.targetApiName && this.targetApiName !== this.cSourceApiName) {
                fetchTasks.push(this.fetchFieldOptions(this.targetApiName, 'target'));
            }

            await Promise.all(fetchTasks);
        },
        async fetchFieldOptions(apiName, type) {
            let rst = await apiRequestFieldOptions(apiName, {
                include_detail_describe: this.includeDetail
            });

            if (this.parseFetchFields) {
                rst = this.parseFetchFields(rst, apiName, type);
            }

            const {fields, display_name, api_name, details} = rst;

            // 构建字段描述对象
            const fieldDescList = [{
                displayName: display_name,
                apiName: api_name,
                fields: fields
            }];

            // 添加 details 中的对象
            if (details && details.length > 0) {
                fieldDescList.push(...details.map(detail => ({
                    displayName: detail.display_name,
                    apiName: detail.api_name,
                    fields: detail.fields
                })));
            }

            // 根据类型设置到对应的数组中
            if (this.cSourceApiName === this.targetApiName) {
                this.allSourceFieldDesc = this.allTargetFieldDesc = fieldDescList;
            } else if (type === 'source') {
                this.allSourceFieldDesc = fieldDescList;
            } else if (type === 'target') {
                this.allTargetFieldDesc = fieldDescList;
            }
        },
        async fetchMappingDetail() {
            const {masterRuleApiName} = this.data;
            if (!this.cRuleApiName && !masterRuleApiName) {
                this.initMasterConfig();
                return;
            }
            // 从对象的映射存在主对象的映射里，合同层级结构配置
            const rst = await apiRequestGetMappingDetail(masterRuleApiName || this.cRuleApiName);
            (rst.ruleList || []).forEach(rItem => {
                const {
                    source_api_name,
                    target_api_name,
                    rule_api_name,
                    master_rule_api_name,
                    field_mapping = [{}],
                    _id
                } = rItem;

                if (this.includeDetail && master_rule_api_name) {
                    this.handleAddMappingGroup({
                        sourceApiName: source_api_name,
                        targetApiName: target_api_name,
                        ruleApiName: rule_api_name,
                        masterApiName: this.cSourceApiName,
                        masterRuleApiName: master_rule_api_name,
                        fieldsMapping: field_mapping,
                        _id,
                    });
                }
                // 主对象
                if (source_api_name === this.cSourceApiName) {
                    this.$set(this.mappingConfigs, 0, {
                        ...this.mappingConfigs[0],
                        fieldsMapping: field_mapping,
                        ruleApiName: rule_api_name,
                        masterApiName: '',
                        _id,
                    })
                }
            });
        },
        validate() {
            // 验证所有映射组
            const allRefs = this.mappingConfigs.map((_, index) => this.$refs[`rFieldMappingItemGroup_${index}`]?.[0]);
            return allRefs.every(ref => ref?.validate());
        },
        handleAddConfirm() {
            if (!this.validate()) {
                return;
            }

            // 构建规则列表
            const ruleList = this.mappingConfigs.map(config => ({
                source_api_name: config.sourceApiName,
                target_api_name: config.targetApiName,
                field_api_name: this.data.fieldApiName, // TODO 弃用
                field_mapping: config.fieldsMapping,
                rule_api_name: config.ruleApiName,
                master_rule_api_name: config.masterRuleApiName,
                master_api_name: config.masterApiName,
                _id: config._id,
            }));

            this.$emit('confirm', {
                describe_api_name: this.cSourceApiName,
                rule_list: ruleList
            }, {ruleData: this.data});
        },
        handleCancel() {
            this.$emit('update:dialogFormVisible', false);
        },
    }
}
</script>

<style lang="less">
.order_payment_mapping_rule.field-mapping-form {
    .content {
        display: flex;
        flex-direction: column;
        gap: 14px;
        height: 400px;
    }

    .field-mapping-item-group-wrapper {
        &.is-detail-item {
            padding: 12px;
            border-radius: 8px;
            border: 1px dashed var(--color-neutrals05);
            .field-mapping-item-group-title {
                color: var(--color-neutrals11);
                font-size: 12px;
                line-height: 18px;
                margin-bottom: 8px;
            }
        }
    }

    .add-mapping-group {
        align-self: flex-start;

        .fx-button {
            display: flex;
            align-items: center;
            gap: 4px;
            color: var(--color-primary06);

            .fx-icon-add {
                font-size: 16px;
                &:before {
                    color: var(--color-primary06);
                }
            }
            
        }
    }
}
</style>