<template>
    <div class="mapping-rule-setting-item">
        <span v-if="openDrag" class="fx-icon-drag handle"></span>
        <fx-select
            :value="cValue"
            :options="cOptions"
            size="small"
            filterable
            :disabled="!!cValue"
            @change="handleChange"
        />
        <fx-link type="standard" :underline="false" @click="handleConfig">{{ $t('crm.setting.order_payment_mapping_rule.field_mapping') }}</fx-link>
        <fx-popover
            placement="top"
            v-model="delPopoverVisible"
        >
            <div>{{ $t('crm.setting.order_payment_mapping_rule.rule_del_confirm') }}</div>
            <div class="actions">
                <fx-button type="primary" size="micro" @click="handleDel">{{ $t('确 定') }}</fx-button>
                <fx-button size="micro" plain  @click="delPopoverVisible = false">{{ $t('取 消') }}</fx-button>
            </div>
            <span slot="reference" class="fx-icon-process-delete"></span>
        </fx-popover>
    </div>
</template>

<script>

export default {
    name: 'OrderPaymentMappingRuleItem',
    props: {
        selectedOptions: {
            required: true,
        },
        options: {
            required: true,
            default: () => []
        },
        data: {
            type: Object,
            default: () => ({
                objectApiName: '',
                fieldApiName: '',
                ruleApiName: '',
            })
        },
        openDrag: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            delPopoverVisible: false,
        }
    },
    computed: {
        cValue() {
            const {objectApiName, fieldApiName} = this.data;
            if (!objectApiName || !fieldApiName) return '';
            const value = `${objectApiName}_FLAG_${fieldApiName}`;
            const hasValue = !this.options.length || this.options.find((opt) => opt.value === value);
            return hasValue ? value : (fieldApiName + $t('该字段已删除'));
        },
        cOptions() {
            return this.options.filter(({value}) => value === this.cValue || !this.selectedOptions.includes(value));
        }
    },
    methods: {
        handleChange(value) {
            const [objectApiName, fieldApiName] = value.split('_FLAG_');
            this.$emit('change', {...this.data, objectApiName, fieldApiName});
        },
        handleDel() {
            this.delPopoverVisible = false;
            this.$emit('del');
        },
        handleConfig() {
            if (!this.data.objectApiName) {
                this.$message({
                    message: $t('crm.setting.order_payment_mapping_rule.warning_select_source_document'),
                    type: 'warning',
                });
                return;
            }
            this.$emit('config');
        },
    }
}
</script>

<style lang="less">
.mapping-rule-setting-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .fx-icon-drag {
        font-size: 16px;
        cursor: move;
    }

    .el-link {
        font-size: 14px;
        line-height: 20px;
    }

    .fx-icon-process-delete {
        font-size: 16px;
        cursor: pointer;
    }
}
</style>