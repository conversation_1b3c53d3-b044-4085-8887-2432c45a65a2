<template>
    <div class="field-mapping-item-group">
        <!-- 字段映射标题 -->
        <div class="field-mapping-item-title">
            <!-- 主对象展示态 -->
            <template v-if="isMasterObj">
                <h2>{{ sourceDisplayName }}</h2>
                <span></span>
                <h2>{{ targetDisplayName }}</h2>
            </template>
            <!-- 从对象编辑态 -->
            <template v-else>
                <fx-select
                    :value="sourceApiName"
                    :options="sourceApiOptions"
                    size="small"
                    filterable
                    clearable
                    @change="handleSourceApiChange"
                    :placeholder="$t('crm.setting.order_payment_mapping_rule.field_mapping_select_source_obj')"
                />
                <span class="fx-icon-jiantou"></span>
                <fx-select
                    :value="targetApiName"
                    :options="targetApiOptions"
                    size="small"
                    filterable
                    clearable
                    @change="handleTargetApiChange"
                    :placeholder="$t('crm.setting.order_payment_mapping_rule.field_mapping_select_target_obj')"
                />
                <span class="fx-icon-process-delete" @click="$emit('delMappingGroup')"></span>
            </template>
        </div>
        <!-- 字段映射项列表 -->
        <field-mapping-item
            ref="rFieldMappingItem"
            v-for="(item, i) in fieldsMappings"
            :key="i"
            :selectedSourceFiled="selectedSourceFiled"
            :selectedTargetFiled="selectedTargetFiled"
            :sourceOptions="sourceOptions"
            :targetOptions="targetOptions"
            :data="item"
            :readOnly="isReadOnly(item)"
            :isMasterObj="isMasterObj"
            @update:data="(data) => handleDataChangeItem(i, data)"
            @del="handleDelItem(i)"
        />
        <!-- 添加按钮 -->
        <span class="add-line" @click="handleAddLine">
            <span class="fx-icon-add"></span>
            <span>{{ $t('添加') }}</span>
        </span>
    </div>
</template>

<script>
import FieldMappingItem from './FieldMappingItem'
import {parseFieldOptions} from './utils/index'

export default {
    name: 'FieldMappingItemGroup',
    components: {
        FieldMappingItem
    },
    props: {
        // 源对象API名称
        sourceApiName: {
            type: String,
            default: ''
        },
        // 目标对象API名称
        targetApiName: {
            type: String,
            default: ''
        },
        // 是否为主对象
        isMasterObj: {
            type: Boolean,
            default: true
        },
        // 字段映射数组
        fieldsMapping: {
            type: Array,
            default: () => []
        },
        // 只读字段列表
        readOnlyFields: {
            type: Array,
            default: () => []
        },
        // 所有源字段描述数组
        allSourceFieldDesc: {
            type: Array,
            default: () => []
            // 结构: [{displayName: '来源对象', apiName: 'source_object_api', fields: []}]
        },
        // 所有目标字段描述数组
        allTargetFieldDesc: {
            type: Array,
            default: () => []
            // 结构: [{displayName: '目标对象', apiName: 'target_object_api', fields: []}]
        },
        // 已选择的源API名称列表（用于排除选项）
        selectedSourceApiNames: {
            type: Array,
            default: () => []
        },
        // 已选择的目标API名称列表（用于排除选项）
        selectedTargetApiNames: {
            type: Array,
            default: () => []
        },
        // 自定义配置
        customConfig: {
            type: Object,
            default: void 0
        },
        // 自定义字段处理函数
        customFieldsProcess: {
            type: Function,
            default: null
        }
    },
    data() {
        return {
            localFieldsMappings: [],
            sourceOptions: [],
            targetOptions: []
        }
    },
    computed: {
        fieldsMappings: {
            get() {
                return this.fieldsMapping || this.localFieldsMappings;
            },
            set(value) {
                this.localFieldsMappings = value;
                this.$emit('update:fieldsMapping', value);
            }
        },
        sourceFieldDesc() {
            return this.allSourceFieldDesc.find(item => item.apiName === this.sourceApiName);
        },
        targetFieldDesc() {
            return this.allTargetFieldDesc.find(item => item.apiName === this.targetApiName);
        },
        sourceDisplayName() {
            return this.sourceFieldDesc?.displayName || '';
        },
        targetDisplayName() {
            return this.targetFieldDesc?.displayName || '';
        },
        sourceApiOptions() {
            return this.allSourceFieldDesc
                .filter(item => {
                    // 排除已选择的API名称，但保留当前选中的
                    if (this.selectedSourceApiNames.includes(item.apiName) && item.apiName !== this.sourceApiName) return false;
                    return true;
                })
                .map(item => ({
                    label: item.displayName,
                    value: item.apiName
                }));
        },
        targetApiOptions() {
            return this.allTargetFieldDesc
                .filter(item => {
                    // 排除已选择的API名称，但保留当前选中的
                    if (this.selectedTargetApiNames.includes(item.apiName) && item.apiName !== this.targetApiName) return false;
                    return true;
                })
                .map(item => ({
                    label: item.displayName,
                    value: item.apiName
                }));
        },
        selectedSourceFiled() {
            return this.fieldsMappings.map(item => item.source_field_api_name);
        },
        selectedTargetFiled() {
            return this.fieldsMappings.map(item => item.target_field_api_name);
        }
    },
    watch: {
        sourceApiName: {
            handler() {
                this.initOptions();
            },
            immediate: true
        },
        targetApiName: {
            handler() {
                this.initOptions();
            },
            immediate: true
        },
        allSourceFieldDesc: {
            handler() {
                this.initOptions();
            },
            immediate: true,
            deep: true
        },
        allTargetFieldDesc: {
            handler() {
                this.initOptions();
            },
            immediate: true,
            deep: true
        },
        fieldsMapping: {
            handler(newVal) {
                if (newVal) {
                    this.localFieldsMappings = [...newVal];
                }
            },
            immediate: true
        }
    },
    methods: {
        initOptions() {
            if (!this.allSourceFieldDesc.length || !this.allTargetFieldDesc.length) {
                return;
            }

            // 处理源字段选项
            if (this.sourceFieldDesc?.fields) {
                let sourceOptions = parseFieldOptions(this.sourceFieldDesc.fields, this.sourceFieldDesc.apiName, 'source', this.customConfig);
                if (this.customFieldsProcess) {
                    sourceOptions = this.customFieldsProcess({
                        options: sourceOptions,
                        fields: this.sourceFieldDesc.fields,
                        type: 'source',
                        apiName: this.sourceFieldDesc.apiName,
                        customConfig: this.customConfig
                    });
                }
                this.sourceOptions = sourceOptions;
            }

            // 处理目标字段选项
            if (this.targetFieldDesc?.fields) {
                let targetOptions = parseFieldOptions(this.targetFieldDesc.fields, this.targetFieldDesc.apiName, 'target', this.customConfig);
                if (this.customFieldsProcess) {
                    targetOptions = this.customFieldsProcess({
                        options: targetOptions,
                        fields: this.targetFieldDesc.fields,
                        type: 'target',
                        apiName: this.targetFieldDesc.apiName,
                        customConfig: this.customConfig
                    });
                }
                this.targetOptions = targetOptions;
            }

            // 初始化字段映射
            if (this.fieldsMapping && this.fieldsMapping.length > 0) {
                this.localFieldsMappings = [...this.fieldsMapping];
            } else if (this.localFieldsMappings.length === 0) {
                this.localFieldsMappings = [{}];
            }
        },
        handleSourceApiChange(value) {
            this.$emit('update:sourceApiName', value);
            this.fieldsMappings = [];
        },
        handleTargetApiChange(value) {
            this.$emit('update:targetApiName', value);
            this.fieldsMappings = [];
        },
        isReadOnly(item) {
            return this.readOnlyFields.includes(item.source_field_api_name);
        },
        validate() {
            const valid = (this.$refs.rFieldMappingItem || []).every(item => item.validate());
            let errorMsg = '';
            if (!valid) {
                errorMsg = $t('crm.setting.order_payment_mapping_rule.warning_fill_source_and_target_field');
            } else if (this.fieldsMappings.length === 0) {
                errorMsg = $t('crm.setting.order_payment_mapping_rule.warning_add_mapping_relation');
            }
            if (errorMsg) {
                this.$message({
                    message: errorMsg,
                    type: 'error'
                })
                return false;
            }
            return true;
        },
        handleAddLine() {
            const newMappings = [...this.fieldsMappings, {}];
            this.fieldsMappings = newMappings;
        },
        handleDelItem(index) {
            const newMappings = [...this.fieldsMappings];
            newMappings.splice(index, 1);
            this.fieldsMappings = newMappings;
        },
        handleDataChangeItem(index, data) {
            const newMappings = [...this.fieldsMappings];
            newMappings[index] = data;
            this.fieldsMappings = newMappings;
        },
        // 获取字段映射数据，供父组件调用
        getFieldMappings() {
            return this.fieldsMappings;
        }
    }
}
</script>

<style lang="less" scoped>
.field-mapping-item-group {
    display: flex;
    flex-direction: column;
    gap: 14px;

    .field-mapping-item-title {
        display: flex;
        gap: 12px;
        align-items: center;

        h2 {
            line-height: 18px;
            flex-basis: 240px;
            color: var(--color-neutrals19);
        }

        span {
            flex-basis: 20px;
        }

        .fx-icon-jiantou {
            font-size: 20px;
            height: 32px;
            line-height: 32px;
            display: inline-block;
        }

        .fx-icon-process-delete {
            cursor: pointer;
            height: 32px;
            line-height: 32px;
        }

        .el-select {
            flex-basis: 240px;
        }
    }

    .add-line {
        align-self: flex-start;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        color: var(--color-primary06);
        cursor: pointer;

        .fx-icon-add {
            font-size: 16px;
            &:before {
                color: var(--color-primary06);
            }
        }
    }
}
</style>