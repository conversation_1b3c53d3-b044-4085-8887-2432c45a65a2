<template>
    <div class="field-mapping-item" :class="{'is-detail-obj': !isMasterObj}">
        <!-- 来源对象 -->
        <div class="source-field">
            <fx-select
                :options="cSourceOptions"
                :value="data.source_field_api_name"
                size="small"
                filterable
                clearable
                :disabled="readOnly"
                @change="(source_field_api_name) => handleDataChange({source_field_api_name})"
            >
                <span slot="suffix">{{ sourceValueTypeText }}</span>
            </fx-select>
            <div class="child-selects-wrapper" v-if="data.option_mapping && data.option_mapping.length">
                <div class="line-icon"></div>
                <div class="child-selects">
                    <fx-select
                        v-for="item in data.option_mapping"
                        :key="item.source_option"
                        :value="item.source_option"
                        :options="sourceChildOptions"
                        disabled
                        size="small"
                    />
                </div>
            </div>
        </div>
        <div class="arrow-container">
            <span class="fx-icon-jiantou"></span>
            <span class="fx-icon-jiantou" v-for="item in data.option_mapping" :key="item.source_option"></span>
        </div>
        <!-- 目标对象  -->
        <div class="target-field">
            <fx-select
                :options="cTargetOptions"
                :value="data.target_field_api_name"
                size="small"
                filterable
                clearable
                :disabled="readOnly"
                @change="(target_field_api_name) => handleDataChange({target_field_api_name})"
            >
                <span slot="suffix">{{ targetValueTypeText }}</span>
            </fx-select>
            <div class="child-selects-wrapper" v-if="data.option_mapping && data.option_mapping.length">
                <div class="line-icon"></div>
                <div class="child-selects">
                    <fx-select
                        v-for="(item, index) in data.option_mapping"
                        :key="item.target_option"
                        :options="targetChildOptions"
                        :value="item.target_option"
                        filterable
                        clearable
                        :disabled="readOnly"
                        size="small"
                        @change="(val) => handleTargetChildChange(val, index)"
                    />
                </div>
            </div>
        </div>
        <span class="fx-icon-process-delete" @click="$emit('del')" v-if="!readOnly"></span>
    </div>
</template>

<script>
import {requireSyncFieldConfig} from './utils/index'
    export default {
        name: 'FieldMappingItem',
        props: {
            isMasterObj: {
                type: Boolean,
                default: true
            },
            selectedSourceFiled: {
                type: Array,
                default: () => []
            },
            selectedTargetFiled: {
                type: Array,
                default: () => []
            },
            sourceOptions: {
                type: Array,
                required: true
            },
            targetOptions: {
                type: Array,
                required: true
            },
            data: {
                type: Object,
                required: true,
                default: () => ({
                    source_field_api_name: '',
                    target_field_api_name: '',
                    option_mapping: [],
                })
            },
            readOnly: {
                type: Boolean,
                default: false
            }
        },
        data() {
            return {
            }
        },
        watch: {
            
        },
        computed: {
            sourceChildOptions() {
                return this.getValueType(this.data.source_field_api_name,'source')?.options || [];
            },
            targetChildOptions() {
                return this.getValueType(this.data.target_field_api_name,'target')?.options || [];
            },
            sourceValueTypeText() {
                return this.getValueType(this.data.source_field_api_name,'source')?.typeText;
            },
            targetValueTypeText() {
                return this.getValueType(this.data.target_field_api_name,'target')?.typeText;
            },
            cSourceOptions() {
                return this.sourceOptions.filter(item => {
                    // 已选字段不显示
                    if (item.value === this.data.source_field_api_name) {
                        return true;
                    }
                    if (this.selectedSourceFiled.includes(item.value)) {
                        return false;
                    }
                    if (this.data.target_field_api_name) {
                        const targetType = this.getValueType(this.data.target_field_api_name,'target')?.type;
                        return item.type === targetType;
                    }
                    return true;
                })
            },
            cTargetOptions() {
                return this.targetOptions.filter(item => {
                    if (item.value === this.data.target_field_api_name) {
                        return true;
                    }
                    if (this.selectedTargetFiled.includes(item.value)) {
                        return false;
                    }
                    if (this.data.source_field_api_name) {
                        const sourceType = this.getValueType(this.data.source_field_api_name,'source')?.type;
                        return item.type === sourceType;
                    }
                    return true;
                })
            }
        },
        methods: {
            getValueType(value, source) {
                const options = source === 'target' ? this.targetOptions : this.sourceOptions;
                return options.find(item => item.value === value);
            },
            handleTargetChildChange(value, index) {
                const optionMapping = JSON.parse(JSON.stringify(this.data.option_mapping));
                optionMapping[index].target_option = value;
                this.handleDataChange({option_mapping: optionMapping});
            },
            handleDataChange(data) {
                const secondType = requireSyncFieldConfig()?.secondType || [];
                let optionMapping = null;
                if (data.source_field_api_name) {
                    // 如果选中的来源对象是二级对象，则需要展示二级对象的子选项
                    const sourceField = this.getValueType(data.source_field_api_name,'source');
                    const sourceType = sourceField?.type;
                    if (secondType.includes(sourceType)) {
                        optionMapping = sourceField.options.map(item => ({
                            source_option: item.value,
                            target_option: '',
                        }));
                    } else {
                        optionMapping = [];
                    }
                }
                this.$emit('update:data', {
                    ...this.data,
                    option_mapping: optionMapping || this.data.option_mapping,
                    ...data
                });
            },
            validate() {
                if (!this.data.source_field_api_name || !this.data.target_field_api_name) {
                    return false;
                } else if (this.data.option_mapping?.length) {
                    // 如果选中的来源对象是二级对象，则需要校验二级对象的子选项
                    const empty = !!this.data.option_mapping.filter(item => !item.target_option).length;
                    return !empty;
                }
                return true;
            }
        }
    }
</script>

<style lang="less" scoped>
.field-mapping-item {
    display: flex;
    gap: 12px;
    align-items: flex-start;

    &.is-detail-obj {
        .source-field,
        .target-field {
            flex-basis: 232px;
            padding-left: 8px;
        }
    }

    .source-field,
    .target-field {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
        align-self: stretch;
        flex-basis: 240px;
        position: relative;

        .el-select {
            width: 100%;
            /deep/ .el-input__suffix {
                display: flex;
                align-items: center;
                font-size: 12px;
                color: #91959E;
                padding: 0 8px;
            }
        }

        .child-selects-wrapper {
            position: relative;
            align-self: stretch;
        }
        .line-icon {
            position: absolute;
            width: 8px;
            height: calc(100% - 8px);
            border-right: 1px solid transparent;
            border-top: 1px solid transparent;
            border-left: 1px solid #DEE1E8;
            border-bottom: 1px solid #DEE1E8;
            border-radius: 0 0 0 10px;
            left: 8px;
            top: -8px;
        }
        .child-selects {
            padding-left: 16px;
            display: flex;
            flex-direction: column;
            gap: 8px;

            .el-input, .el-select {
                width: 100%;
            }
        }
    }

    .arrow-container {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-self: stretch;
    }

    .fx-icon-jiantou {
        font-size: 20px;
        height: 32px;
        line-height: 32px;
        display: inline-block;
    }

    .fx-icon-process-delete {
        cursor: pointer;
        height: 32px;
        line-height: 32px;
    }
}
</style>