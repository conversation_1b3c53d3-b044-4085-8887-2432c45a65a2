/*
 * @Descripttion: 
 * @Author: chaoxin
 * @Date: 2024-09-04 10:14:52
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-07-29 14:36:19
 */
export {parseFieldOptions, requireSyncFieldConfig} from './field'

export const apiRequestFieldOptions = (apiname, opts = {}) => {
    if (!apiname) return Promise.resolve(null);
    return CRM.util.getDescribeLayout({
        apiname,
        include_layout: false,
        include_detail_describe: false,
        ...opts
    }).then((res) => {
        const {objectDescribe = fields, detailObjectList = []} = res || {};
        return {
            fields: objectDescribe.fields,
            display_name: objectDescribe.display_name,
            api_name: objectDescribe.api_name,
            details: detailObjectList.map(item => ({
                display_name: item.objectDescribe?.display_name,
                api_name: item.objectDescribe?.api_name,
                fields: item.objectDescribe?.fields
            }))
        };
    });
}

/** 获取映射详情 */
export const apiRequestGetMappingDetail = (ruleApiName) => {
    return CRM.util.ajax_base('/EM1HNCRM/API/v1/object/object_mapping/service/findByApiName', {rule_api_name: ruleApiName}, null)
}
