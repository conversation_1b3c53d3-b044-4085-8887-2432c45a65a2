/*
 * @Descripttion: 字段映射的一些配置项，参考crm2/modules/setting/objmap
 * @Author: chao<PERSON>
 * @Date: 2024-09-04 17:56:06
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-06-03 16:35:18
 */
import crmRequire from '@common/require';

/** 字段映射配置项 */
// 业务自己维护
let config = {
    blackType: [
        'formula',
        'auto_number',
        'signature',
        'quote',
        'embedded_object_list',
        'multi_level_select_one',
        'count',
        'out_department'
    ],
    blackApiNames: [
        'id',
        'tenant_id',
        'lock_status',
        'extend_obj_data_id',
        'package',
        'object_describe_id',
        'object_describe_api_name',
        'version',
        'lock_user',
        'lock_rule',
        'life_status_before_invalid',
        'is_deleted',
        'data_auth_code',
        'change_type',
        'out_data_auth_code',
        'order_by',
        'data_auth_id',
        'out_data_auth_id',
        'origin_source',
        'sys_modified_time'
    ],
    secondType: [
        'select_one',
        'select_many',
        'record_type'
    ],
    fieldInfo: {
        text: $t("单行文本"),//自动编号类别使用IsCodeField：true判断
        long_text: $t("多行文本"),
        select_one: $t("单选"),
        select_many: $t("多选"),
        number: $t("数字"),
        currency: $t("金额"),
        date: $t("日期"),
        time: $t("时间"),
        date_time: $t("日期时间"),
        phone_number: $t("手机"),
        email: $t("邮箱"),
        true_or_false: $t("布尔值"),
        percentile: $t("百分数"),
        url: $t("网址"),
        object_reference: $t("查找关联"),
        master_detail: $t("主从关系"),
        employee: $t("crm.人员"),
        location: $t("定位")	,
        record_type: $t("业务类型"),
        department: $t("crm.部门"),
        country: $t("国家"),
        province:$t("省"),
        city:$t("市"),
        district:$t("区"),
        town:$t('乡镇'),
        village:$t('village'),
        address: $t("详细地址"),
        image: $t("图片"),
        file_attachment: $t("附件")
    },
    
};

export const requireSyncFieldConfig = () => {
    return config;
}

const defaultCustomConfig = {
    source: {
        blackApiNames: [],
        whiteApiNames: [],
    },
    target: {
        blackApiNames: [],
        whiteApiNames: [],
    }
}
/** 处理可配置字段 */
export const parseFieldOptions = (fields, api_name, directionType, customConfig = defaultCustomConfig) => {
    // console.log(fields)
    if (!config) return fields;
    const ret = [];
    const isSub = '';
    _.each(fields, function(field) {
        if (_.contains(customConfig[directionType]?.whiteApiNames, field.api_name)) {
            ret.push(field);
            return;
        }
        if (_.contains(customConfig[directionType]?.blackApiNames, field.api_name)) {
            return;
        }
        
        // 通用逻辑 符合黑名单字段 直接去除
        if (_.contains(config.blackType, field.type)) {
            return
        }

        // 过滤组件名称信息字段
        if (field.type == 'group') {
            return;
        }

        if (_.contains(config.blackApiNames, field.api_name)) {
            return;
        }

        // 用户自定义字段均支持映射
        ret.push(field);

    });
    console.log(api_name, ret);
    return ret.map((field) => {
        const {label, api_name, type, return_type, options = []} = field;
        const fieldType = type === 'formula' ? return_type : type;
        return {
            label,
            value: api_name,
            type: fieldType,
            typeText: config.fieldInfo[fieldType],
            options,
        }
    });
}
