<!--
 * @Descripttion: 多数据源映射规则开关
 * @Author: chao<PERSON>
 * @Date: 2024-11-18 14:20:31
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-07-01 16:58:07
-->
<template>
    <div class="order-payment-mapping-rule-comp">
        <fx-draggable :list="rules" handle=".handle" class="drag-list" :disabled="!openDrag" @end="handleDragEnd">
            <rule-item
                v-for="(item, index) in rules"
                :key="item.ruleApiName"
                :options="cOptions"
                :selectedOptions="selectedOptions"
                :data="item"
                :openDrag="openDrag"
                @config="handleConfig(item)"
                @change="(data) => $emit('change', data, index)"
                @del="handleDel(item, index)"
                @confirm="$emit('confirm')"
            />
        </fx-draggable>
        <span v-if="rules.length < maxNum" class="add-line" @click="$emit('add')"><span class="fx-icon-add"></span><span>{{ $t('添加') }}</span></span>
        <field-mapping-form
            v-if="dialogFormVisible"
            :dialogFormVisible.sync="dialogFormVisible"
            :data="curData"
            :targetApiName="curData.targetApiName || targetApiName"
            :customConfig="curFormCustomConfig"
            v-on="$listeners"
        />
    </div>
</template>

<script>
import RuleItem from './MultiSourceMappingRuleItem'
import {apiRequestFieldOptions} from './utils'
import FieldMappingForm from './FieldMappingForm'

export default {
    name: 'MultiSourceMappingRuleComp',
    components: {
        RuleItem,
        FieldMappingForm,
    },
    props: {
        rules: {
            type: Array,
            default: () => [
                {

                }
            ]
        },
        openDrag: {
            type: Boolean,
            default: false
        },
        rulesOptions: {
            type: Array,
        },
        targetApiName: {
            type: String,
        },
        dialogFormVisible: {
            type: Boolean,
            default: false
        },
        requiredNum: {
            type: Number,
            default: 1
        },
        maxNum: {
            type: Number,
            default: 999
        },
        formCustomConfig: {
            type: Object,
        }
    },
    data() {
        return {
            curData: {
                objectApiName: '',
                fieldApiName: '',
                ruleApiName: '',
            },
            options: [],
        }
    },
    computed: {
        curFormCustomConfig() {
            return this.formCustomConfig?.[this.curData.objectApiName];
        },
        cOptions() {
            if (this.rulesOptions?.length) {
                return this.rulesOptions;
            }
            const selectsObjs = this.rules.map(({objectApiName}) => objectApiName);
            return this.options.filter((field) => {
                const selected = this.rules.find(({objectApiName, fieldApiName}) => field.target_api_name == objectApiName && field.api_name == fieldApiName);
                if (selected) {
                    return true;
                } else if (selectsObjs.includes(field.target_api_name)) {
                    return false;
                }
                return true;
            }).map((field) => ({
                label: field.label,
                value: field.target_api_name + '_FLAG_' + field.api_name,
            }))
        },
        selectedOptions() {
            return this.rules.map(({objectApiName, fieldApiName}) => objectApiName + '_FLAG_' + fieldApiName);
        },
    },
    created() {
        this.fetchOptions();
    },
    methods: {
        fetchOptions() {
            if (this.rulesOptions?.length) {
                return;
            }
            apiRequestFieldOptions(this.targetApiName, true).then(({fields}) => {
                this.options = Object.values(fields)
                    // 过滤查找关联字段 指定预设字段 自定义对象
                    .filter((field) => (field.type === 'object_reference'))
            });
        },
        handleConfig(data) {
            this.curData = data;
            this.$emit('update:dialogFormVisible', true);
        },
        handleDel(item, index) {
            if (this.requiredNum > 0 && this.rules.length - 1 < this.requiredNum) {
                this.$message({
                    message: $t('sfa.crm.setting_required_num_options', {num: this.requiredNum}),
                    type: 'warning',
                })
                return;
            }
            this.$emit('del', item, index);
        },
        handleDragEnd() {
            this.$emit('dragEnd', this.rules);
        }
    }
}
</script>

<style lang="less">
    .order-payment-mapping-rule-comp {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;

        .drag-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

       .add-line {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--color-primary06);
            cursor: pointer;
            font-size: 14px;
            line-height: 20px;

            .fx-icon-add {
                font-size: 16px;
                &:before {
                    color: var(--color-primary06);
                }
            }
        }
    }
</style>