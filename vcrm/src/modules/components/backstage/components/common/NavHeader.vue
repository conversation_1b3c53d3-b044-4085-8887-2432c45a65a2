<template>
   <div class="nav-header">
        <div class="header-title">
            <h1>{{activeNav.title}}</h1>
        </div>
        <div class="nav">
            <div
                class="nav-bar"
                :class="{'active': nav.key === activeKey}"
                v-for="nav in navs"
                :key="nav.key"
                @click="navigate(nav.key)"
            >
                <span class="nav-text">{{nav.title}}</span>
            </div>
        </div>
    </div>

</template>

<script>
const navs = [
    {
        title: $t('商品/产品配置'),
        description: $t('多单位/商品/属性/固定搭配等相关配置'),
        key: 'cmmodityproduct'
    },
    {
        title: $t('CPQ配置'),
        description: $t('CPQ开关/BOM价格等相关配置'),
        key: 'cpqconfigure'
    },
    {
        title: $t('价格管理配置'),
        description: $t('价目表/可售范围相关配置'),
        key: 'pricemanage'
    },
    {
        title: $t('促销与返利配置'),
        description: $t('高级定价/返利/优惠券相关配置'),
        key: 'promotionrebate'
    },
    {
        title: $t('交易相关单据配置'),
        description: $t('业务单据/重新取价/选数据页相关配置'),
        key: 'tradeconfigure'
    },
    {
        title: $t('sfa.vcrm.settings_paymentconfigure.title'),
        key: 'paymentconfigure'
    },
    {
        title: $t('sfa.vcrm.settings_contractconfigure.title'),
        key: 'contractconfigure'
    }
]
export default {
    name: 'NavHeader',
    props: {
        activeKey: {
            type: String
        }
    },
    data() {
        return {
            navs,
        }
    },
    
    computed: {
        activeNav() {
            return navs.find((nav) => nav.key === this.activeKey) || {};
        }
    },
    methods: {
        navigate(key) {
            CRM.control.navigate(`#crmmanage/=/module-${key}`);
        }
    }
}
</script>

<style lang="less" scoped>
.nav-header {
    background-color: var(--color-neutrals01);
    .header-title {
        padding: 16px;

        h1 {
            font-size: 16px;
            line-height: 24px;
            font-weight: 700;
            color: var(--color-neutrals19);
            white-space: nowrap;
        }
    }

    .nav {
        margin: 4px 16px 20px;
        display: flex;

        .nav-bar {
            display: flex;
            align-items: center;
            padding: 8px;
            min-width: 124px;
            min-height: 20px;
            margin-right: 9px;
            cursor: pointer;
            background-image: url("../../assets/navbar-bg.png");
            background-position: center center;
            background-repeat: no-repeat;
            background-size: 100% 100%;
            filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.15));
            overflow: hidden;
            .nav-text {
                font-size: 14px;
                line-height: 20px;
                color: var(--color-neutrals19);
                align-content: center;
            }
            &:last-child {
                margin-right: 0;
            }
            &.active {
                background-image: url("../../assets/navbar-bg-active.png");
                .nav-text {
                    font-weight: 700;
                    color: var(--color-neutrals01);
                }
            }
        }
    }
}
</style>