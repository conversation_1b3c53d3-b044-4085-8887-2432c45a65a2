<template>
     <div class="backstage-switch">
         <template v-if="isChild">
            <div class="child-content">
                <fx-switch
                    :value="listData.value"
                    :disabled="listData.value && !listData.enableClose"
                    size="mini"
                    @change="handleChange"
                />
                <p class="title">{{listData.title}}</p>
                <label v-show="listData.enableClose === false">
                    <span class="fx-icon-jingshi"></span>
                    {{$t('crm.setting.backstage.warn_cannot_closed', null, '一旦启用，将无法停用')}}
                </label>
                <label v-show="listData.enableOpen === false">
                    <span class="fx-icon-jingshi"></span>
                    {{$t('crm.setting.backstage.warn_cannot_opened', null, '一旦关闭，将无法启用')}}
                </label>
                <slot name="title-extend" />
            </div>
         </template>
         <template v-else>
             <div class="crm-module-title">
                 <p>{{listData.title}}</p>
                 <p class="subtitle" v-if="listData.subTitle">{{listData.subTitle}}</p>
                 <p class="line" v-if="listData.showTitleLine" />
             </div>
             <div class="on-off">
                 <label v-show="listData.enableClose === false">
                    <span v-if="listData.extraStatus">
                        <i v-if="listData.extraStatus.isWaiting" class="el-icon-loading"></i>
                        <span class="fx-icon-jingshi" v-if="listData.extraStatus.isWarning"></span>
                        {{ listData.extraStatus.statusInfo}}
                    </span>
                    <label v-else>
                        <span class="fx-icon-jingshi"></span>
                        {{$t('crm.setting.backstage.warn_cannot_closed', null, '一旦启用，将无法停用')}}
                    </label>
                 </label>
                <label v-show="listData.enableOpen === false">
                    {{$t('crm.setting.backstage.warn_cannot_opened', null, '一旦关闭，将无法启用')}}
                </label>
                 <fx-switch
                     :value="listData.value"
                     :disabled="isDisabled"
                     size="small"
                     @change="handleChange"
                 />
             </div>
         </template>
     </div>
</template>
<script>
    export default {
    // name: "backstageswitch",
    props: {
        listData: {
            type: Object,
            require: true
        },
        index: {
            type: Number
        },
        isChild: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
        };
    },
    watch: {
    },
    computed: {
        isDisabled() {
            return (this.listData.value && this.listData.enableClose === false) ||
                (this.listData.extraStatus && this.listData.extraStatus.isDisable) ||
                (!this.listData.value && this.listData.enableOpen === false);
        }
    },
    mounted() {
       
    },
    methods: {
        handleChange(value) {
            this.$emit('change',{key:this.listData.key,type:this.listData.type, value})
        },
        
    },
};
</script>

<style lang="less">
.backstage-switch {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .fx-icon-jingshi {
        font-size: 14px;
    }
    .child-content {
        display: flex;
        justify-content: flex-start;
        padding: 0 16px 8px;
        align-items: center;

        .title {
            color: var(--color-neutrals19);
            font-size: 12px;
            line-height: 18px;
            margin: 0 8px;
        }

        label {
            color: var(--color-danger06);
        }
    }


    label {
        height: 18px;
        line-height: 18px;
        font-size: 12px;
        color: #ff522a;
        margin-right: 10px;
    }
    .on-off {
        padding-top: 16px;
        padding-right: 16px;
        display: flex;
        align-items: center;
    }

}
</style>