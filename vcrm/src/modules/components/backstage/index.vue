<template>
    <div class="backstage" :class="{'new': isNewVersion}">
        <nav-header :activeKey="activeKey" />
        <div class="crm-transaction-section crm-scroll" v-if="isRender">
            <div class="crm-content">
                <item-list
                    v-for="(item, index) in cDataList"
                    :key="index"
                    :data-item="item"
                    :changeLoading="changeLoading"
                    @change="change"
                    @update="(keys) => $emit('update', keys)"
                />
                <no-content v-if="!cDataList.length" />
            </div>
            <fixed-navigation v-if="cDataList.length" :list="cDataList" />
        </div>
    </div>
</template>

<script>
import * as components from './components/common'
export * from './settings'
export default {
    name: "backstage",
    props: {
        data: {
            type: Array,
            default: []
        },
        methodList:{
            type: Object,
            default: () => {
                return {
                    // beforeGetConfig: fn,  获取开关前。可以处理请求入参
                    // afterGetConfig: fn,   获取开关后。可以处理按钮状态
                    // beforeSetConfig: fn,   保存开关前。可以拦截请求 stop:true，或处理入参
                    // afterSetConfig: fn,    保存开关后。可以处理其他开关状态
                }
            }
        },

        changeLoading: {
            type: Boolean,
            default: false
        }

    },
    components,
    provide() {
        return {
            commonSetConfig: this.commonSetConfig
        }
    },
    data() {
        return {
            isRender: false,
            activeKey: '',
        }
    },
    computed: {
        cDataList() {
            // 数据不全，过滤隐藏数据
            const baseDataItem = () => {
                return {
                    title: '',
                    visible: true,
                    moduleList: []
                }
            }
            const baseModuleItem = () => {
                return {
                    type: 'switch',
                    title: '',
                    key: '',
                    value: '',
                    displayCount: 3,
                    isDisabled: false,
                    isShow: true,
                    enableClose: true,
                    enableOpen: true,
                    render: null,
                    radioOptions: [],
                    describeList: []
                }
            }
            const getModuleList = (moduleList = []) => {
                return moduleList.map((m) => {
                    // 兼容多层结构
                    if (m.moduleList && m.moduleList.length) {
                        return {
                            ...m,
                            moduleList: getModuleList(m.moduleList)
                        }
                    }
                    // 兼容一组多个开关
                    if (m.children && m.children.length) {
                        return {
                            ...baseModuleItem(),
                            ...m,
                            children: getModuleList(m.children)
                        }
                    }
                    return {
                        ...baseModuleItem(),
                        ...m,
                    }
                }).filter((m) => m.isShow !== false);
            }
            return this.data.map((d) => {
                return {
                    ...baseDataItem(),
                    ...d,
                    moduleList: getModuleList(d.moduleList)
                }
            }).filter((d) => d.visible);
        },
        isNewVersion() {
            return !!window.Fx;
        }
    },
    async mounted() {
        let params = this.getRouterParams();
        this.activeKey = params.module;
        await this.init();
        setTimeout(() => {
            this.scrollToView(params);
        }, 1000);

    },
    methods: {
        // 滚动到指定元素
        scrollToView({level, key}) {
            if (!level && !key) return;
            let id = key ? `key__${key}` : (level && `level${level}`);
            let wrap = document.getElementById('crmmanage');
            let el = wrap.querySelector(`#${id}`);
            if (!el) {
                console.error($t('未找到'), id);
                return;
            };
            let $module = key ? $(el).parents('.crm-transaction-module') : $(el).find('.crm-multi').eq(0);
            $module[0].scrollIntoView(true);
            $module.addClass('animation-highlight');
            setTimeout(() => {
                $module && $module.removeClass('animation-highlight');
            }, 3000);
        },
        /** 
        *@desc解析路由参数
        *@return { module[, level, key] }
        */ 
        getRouterParams() {
            let route = location.hash.split('/=/');
            if (route.length && route[1]) {
                let params = route[1].match(/(\w+\-\w+)/g);
                return params.reduce((obj, item) => {
                    let [key, val] = item.split('-');
                    obj[key] = val;
                    return obj;
                }, {});
              
            }
            return {};
        },
        change(opt, moduleItem) {
            this.setConfig(opt);
            this.$emit('change',opt, moduleItem)
        },
        // =========================================自动请求 保存开关=======================================================
        async init() {
            let autoList = this.data.filter(item => item.autoSetConfig);
            if (autoList.length) {
                await this.getConfig(autoList);
            } else {
                this.isRender = true;
            }
        },
        showMessage(msg){
            if(!msg) return true ;
            return new Promise(resolve => {
                this.$confirm(msg, $t('提示'), {
                    confirmButtonText: $t('确定'),
                    cancelButtonText: $t('取消'),
                    type: 'warning'
                }).then(async () => {
                    resolve(true)
                }).catch(() => {
                    resolve(false)
                    // this.$message({
                    //     type: 'info',
                    //     message: '已取消删除'
                    // });
                });
            })
        },
        /**
         * 封装设置开关接口，统一调用，并埋点
         * @param {Object} {url, data = {key, value}, ...}
         * @config {Object} FHHApi时 配置项
         * @return {Object} 接口返回数据 
         * {
         *  Result: {
         *      StatusCode: 0
         *  },
         *  Value: {
         *  }
         * }
         */
        commonSetConfig(param, config, logData) {
            let me = this;
            return new Promise((resolve, reject) => {
                if (param.url) { 
                    CRM.util.FHHApi({
                        ...param,
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                me.send_log(param, logData);
                                resolve(res);
                            } else {
                                reject(res?.Result?.FailureMessage)
                            }
                        },
                        error() {
                            reject(new Error($t('网络异常，请稍后重试')))
                        }
                    }, {
                        errorAlertModel: 1,
                        ...config
                    })
                } else {
                    CRM.util.setConfigValue(param.data)
                    .then(res => {
                        me.send_log(param, logData);
                        resolve(res);
                    }).fail(err => {
                        reject(err);
                    })
                }
            })
        },
        send_log({data = {}}, logData = {}) {
            console.log('send_log', [...arguments])
            CRM.util.sendLog('crm_setting_backstage', this.activeKey, {
                operationId: data.key || logData.key,
                eventType: 'cl',
                pageData: {
                    logInfo: {
                        ...data,
                        ...logData
                    }
                }
            });
        },
        // 开关保存
        async setConfig(opt){
            if(this._allConfig){
                let {key, value} = opt;
                let res;
                let switchConfig = this._allConfig[key];
                let val = value;
                if(switchConfig.type === 'switch') val = value ? '1' : '0';
                let param = {
                    key: key,
                    value: val
                };
                try {
                    if(this.methodList.beforeSetConfig) {
                        let r = await this.methodList.beforeSetConfig(key, value, switchConfig);
                        if(r && r.stop) return;
                        if(r && r.param) param = r.param;
                    }
                    if(switchConfig.confirmMessage){
                         let status = await this.showMessage(switchConfig.confirmMessage);
                         if(!status) return;
                    }
                    CRM.util.showLoading_tip();
                    res = await this.commonSetConfig({url: switchConfig.setUrl || null, data: param})
                    CRM.util.hideLoading_tip();
                    switchConfig.value = value;
                    CRM.util.remind(1, $t("设置成功"));

                    if(this.methodList.afterSetConfig) {
                        await this.methodList.afterSetConfig(key, value, switchConfig);
                    }
                } catch(e) {
                    CRM.util.hideLoading_tip();
                    e && CRM.util.alert(e);
                }
            }
        },

        async getConfig(autoList){
            let colRes = this.collectRequest(autoList);
            CRM.util.showLoading_tip();
            await this.batchRequest(colRes);
            CRM.util.hideLoading_tip();
        },

        // 收集所有开关请求。区分普通的和特殊接口的
        collectRequest(autoList = []) {
            let normal = {},
                special = {};
            autoList.forEach(item => {
                CRM.util.forEachTreeData(item.moduleList, mo => {
                    if(mo.getUrl) {
                        special[mo.key] = mo
                    }else {
                        normal[mo.key] = mo
                    }
                })
            });
            this._allConfig = Object.assign({}, normal, special);
            return {
                normal,
                special
            }
        },

        // 包装getconfig
        normalRequest(keys){
           return new Promise((resolve, reject) => {
               CRM.util.getConfigValues(keys).then(res => {
                   resolve(res)
               })
               .fail(err => {
                    CRM.util.alert(err);
                    reject(err);
               });
           })
        },

        /**
         * @desc 批量请求开关
         * @param normal 走getconfig 的开关
         * @param special 走自定义接口的开关
         * @returns {Promise<void>}
         */
        async batchRequest({normal = {}, special = {},} = {}){
            let arr = [];
            let normalKeys = Object.keys(normal);
            if(normalKeys && normalKeys.length) arr.push(this.normalRequest(normalKeys));
            for (let [key, item] of Object.entries(special)) {
                let param = {key: key};
                if(this.methodList.beforeGetConfig) param = await this.methodList.beforeGetConfig(key);
                arr.push(CRM.util.ajax_base(item.getUrl, param))
            }
            try {
                let result = await Promise.all(arr);
                await this.parseRes(result);
            } catch(e) {
                console.error(e);
            }
        },

        // 请求完开关，更新开关value
        // 有需求处理其他开关类型再补充
        async parseRes(res) {
            res.forEach(list => {
                list.forEach(item => {
                    let val = item.value;
                    let config = this._allConfig[item.key];
                    if (config.type === 'switch') val = item.value === '1';
                    // config.value = val;
                    this.$set(config, 'value', val)
                })
            });
            if (this.methodList.afterGetConfig) res = await this.methodList.afterGetConfig(res);
            this.isRender = true;
        }

    },
};
</script>

<style lang="less">
.backstage {
    background-color: var(--color-neutrals01);
    width: 100%;
    height: 100%;
    overflow-y: hidden;
    overflow-x: auto;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .crm-transaction-section {
        display: flex;
        align-items: flex-start;
        padding-left: 16px;
        overflow-y: auto;

        .crm-content {
            margin-right: 16px;
            flex: 0 834px;
        }

        .crm-transaction-navigation {
            position: sticky;
            top: 0;
            flex: 0 128px;
        }
    }

    &.new {
        .crm-content {
            min-width: 834px;
        }

        .crm-transaction-navigation {
            min-width: 128px;
        }
    }
}
</style>
