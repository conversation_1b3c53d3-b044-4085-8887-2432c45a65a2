<template>
    <fx-dialog
        :visible.sync="dialogVisible"
        class="poolnew-dialog"
        :append-to-body="true"
        :title="title"
        :max-height="height"
        @closed="destroy"
        >
        <div class="poolnew-tips poolnew-tips-unfold">
            <div class="poolnew-tips-icon">
                <span class="poolnew-tips-triangle"></span>
                <em>!</em>
            </div>
            <div class="poolnew-tips-content">
                <div class="poolnew-tips-title" @click="toggleTips">{{$t('注意')}}</div>
                <div class="poolnew-tips-text">{{tips[0]}}</div>
                <div v-if="isOpenPRM" class="poolnew-tips-text">{{tips[1]}}</div>
            </div>
        </div>
        <div class="poolnew-content">
            <div class="poolnew-item" v-for="item in dColumns" :key="item.field" :class="`item-${item.field || item.type}`">
                <n-line :field="item.field" :model="item" :value="dValue" :row="row">
                    <template v-slot:content>
                        <component
                        :fromBackstage="fromBackstage"
                        :is="item.renderComp" 
                        :ref="item.renderComp" 
                        :field="item.field" 
                        v-model="dValue"
                        :model="getModel(item)"  
                        :row="row"
                        :valid="valid"></component>
                    </template>
                </n-line>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <fx-button type="primary" @click="onConfirm" size="small">{{$t("确定")}}</fx-button>
            <fx-button @click="onCancel" size="small">{{$t("取消")}}</fx-button>
        </span>
    </fx-dialog>
</template>

<script>
import forms from '../form';
import getConfig from '../config/config';
import { toSubmitData } from '../util/format';
export default {
    components: forms,
    props: {
        url: '',
        apiname: '',
        columns: {
            type: Array,
            // default() {
            //     return [];
            // }
        },
        value: '',
        title: {
            type: String,
            default: $t('选择数据'),
        },
        authority:{
            default : {},
        },
        // 是否是后台
        fromBackstage:{
            type:Boolean,
            default:true,
        }
    },
    data() {
        return {
            dColumns: [],
            dValue: {},
            dialogVisible: true,
            // row: this.value,
            valid: false,
            height: ($(window).height() - 200) + 'px',
            isOpenPRM: false,
            tips: [],
        }
    },
    mounted() {
        // let fn = !CRM.util.isCrmRulesNewArea() ? CRM.util.getCountryAreaOptions : CRM.util.areaCode2Label;
        // let param = !CRM.util.isCrmRulesNewArea() ? '' : this.getRulesAreaCode();
        // fn(param).then(() => {
            this.init();
        // })
    },
    methods: {
        init() {
          CRM.util.getPRMRight().then((res) => {
                this.isOpenPRM = res;
                this.config = getConfig(this.apiname);
                this.tips = this.config.tips;
                this.object_apiname = this.config.object_apiname;
                this.display_name = this.config.display_name;
                this.object_display_name = this.config.object_display_name;
                this.initData = this.config.initData;
                this.dColumns = this.getdColumns();
                // this.dModel = this.getdModel();
                this.dValue = this.getdValue();
                this.row = this.dValue;
            })
        },
        getRulesAreaCode() {
            let codes = [];
            let data = this.value || {};
            let code1 = CRM.util.getRuleListAreaCode(data.recycling_rule_list);
            let code2 = CRM.util.getRuleListAreaCode(data.allocate_rule_list);
            let code3 = CRM.util.getRuleListAreaCode(data.allocate_rule_list, 'member_wheres');
            let code4 = CRM.util.getRuleAreaCode(data.pool_owner_rule);

            codes = _.union(code1, code2, code3, code4);
            return codes;
        },
        getdColumns() {
            let _columns = this.columns || this.config.newColumns;
            if (!this.isOpenPRM) {
                _columns = _.filter(_columns, (col) => {
                    return col.field != 'pool_type';
                })
            }
            if(this.apiname == 'LeadsPoolObj' && !this.authority.grayAllocateLimitFlag){
                 _columns = _.filter(_columns, (col) => {
                    return col.field != 'time_claim_limit' && col.field != 'time_allocate_limit';
                })
            }

            // _.each(_columns, (col) => {
            //     col.valid = this.valid; //设定每个独立的valid
            // })
            return _columns;
        },
        getdValue() {
            let val = this.value || this.config.initData;
            // 兼容老的数据没有这些选项的时候也有默认值
            !val.pool_type && (val.pool_type = 'normal');
            !val.limit_type && (val.limit_type = 'personal');
            return val;
        },
        getModel(item) {
            return Object.assign({
                apiname: this.apiname, 
                object_apiname: this.object_apiname,
                display_name: this.display_name, 
                object_display_name: this.object_display_name, 
                isOpenPRM: this.isOpenPRM,
                outer: CRM.get('outer'),
            }, item);
        },
        getcValue() {
            let _val = this.value || {};
            _val = _.extend(_val, this.dValue);
            return _val;
        },
        validate() {
            let errs = $('.fm-error', this.$el);
            let flag = errs.length ? false : true;
            return flag;
        },
        onConfirm(e) {
            this.valid = true;
            this.$nextTick(() => {
                this.valid = false;
                if (!this.validate()) {
                    // CRM.util.remind(3, $t('请填写数据'));
                    return;
                }
                let cValue = this.getcValue();
                let data = this.toSubmitData(cValue)[0];
                this.doSubmit(data, $(e.target));
            })
        },
        doSubmit(data, submitEl) {
            const me = this;
            CRM.util.FHHApi({
                url: me.url,
                data: data,
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        me.hide();
                        me.$emit('submit.suc', data);
                        return;
                    }
                    CRM.util.alert(res.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1,
                submitSelector: submitEl,
            })
        },
        toSubmitData(data) {
            const me = this;
            return toSubmitData(data, me.apiname, me.config.topFields);
        },
        toggleTips: function(e) {
			var $target = $(e.currentTarget);
			$target.closest('.poolnew-tips').toggleClass('poolnew-tips-unfold');
		},
        onCancel() {
            this.hide();
        },
        hide(){
            this.dialogVisible = false;
        },
        destroy() {
            this.$destroy();
        }
    }
}
</script>

<style lang="less">
    .poolnew-dialog{
        .poolnew-tips{
            display:flex;
            overflow: hidden;
            margin-bottom: 20px;
            background-color: #ffab00;
            border: 1px solid #ffab00;
            border-radius: 3px;
            height: 40px;
            font-size: 12px;
        }
        .poolnew-tips-unfold{
            height: auto;
            .poolnew-tips-title:before{
                border-bottom-color: transparent;
                border-top-color: #aaa;
                top: 15px;
            }
            .poolnew-tips-title:after{
                border-bottom-color: transparent;
                border-top-color: #fff8ec;
                top: 12px;
            }
        }
        .poolnew-tips-icon{
			position:relative;
			display:inline-block;
			width: 40px;
			flex: 0 0 40px;
			vertical-align: top;
			margin-top: 13px;
			em{
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				text-align: center;
				color: #ffab00;
				font-size: 12px;
			}
		}
		.poolnew-tips-triangle{
			display: block;
			width: 2px;
			margin: auto;
			border-bottom: 16px solid var(--color-neutrals01);
		    border-left: 8px solid transparent;
		    border-right: 8px solid transparent;
		}
		.poolnew-tips-content{
			padding-bottom: 5px;
			width: 100%;
			background-color: #fff8ec;
			border-radius: 3px;
		}
		.poolnew-tips-title{
			color: var(--color-neutrals19);
			line-height: 40px;
			position: relative;
			padding: 0 24px 0 16px;
			&:before,
			&:after{
				position: absolute;
				right: 15px;
				content: '';
				display: inline-block;
				border: 8px solid transparent;
			}
			&:before{
				top: 6px;
				border-bottom-color: #aaa;
			}
			&:after{
				top: 9px;
				border-bottom-color: #fff8ec;
			}
		}
		.poolnew-tips-text{
			color: #94979c;
			padding: 0 24px 14px 16px;
		}

        .poolnew-content{
            font-size: 13px;
        }
        .pb-checkbox .el-checkbox{
            padding: 5px 0;
            color: #333;
        }
        .pb-radio .el-radio{
            padding: 5px 0;
            color: #333;
        }
        .pb-receiverule .el-radio{
            color: #333;
        }
        .pb-nline{
            .pb-label{
                color:#000;
            }
            .pb-content{
                color: #333;
            }
        } 
        .poolnew-item{
            margin-top: 15px;
            &.item-group{
                font-size: 14px;
                border-bottom: 1px solid #eaeaea;
                .pb-nline{
                    .pb-label{
                        color:#999;
                    }
                }
            }
        }
        .icsel-vcnts{
            position: relative;
        }
    }
    
</style>