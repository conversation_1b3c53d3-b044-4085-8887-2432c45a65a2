<template>
<div class="pb-allocaterule-all">
	<div class="pb-allocaterule-next" v-if="showRadio">
		<p>{{$t('crm.leads.rule_allocation_fail')}}</p>
  		<fx-radio-group v-model="radio" @change="onChangeRadio">
  		  <fx-radio :label="0">{{$t('crm.leads.rule_allocation_over')}}</fx-radio>
  		  <fx-radio :label="1">{{$t('crm.leads.rule_allocation_next')}}</fx-radio>
  		</fx-radio-group>
	</div>	
	 <div class="pb-allocaterule"></div>
</div>
   
</template>

<script>
import crmRequire from '@common/require';
import RecycleRule from '../recyclerule/recyclerule';
export default {
	extends: RecycleRule,
	data(){
		return {
			radio:this.value.allocate_choice || 0,
			showRadio :false,
		}
	},
	created() {
		// 905 全网时放开走这个接口得灰度
		let me = this
		CRM.util.getAuthority('LeadsPoolObj').then(
			(authority) => {
				me.showRadio = authority.graySfaAllocateContinue
			}
		)
	},
    methods: {
        initRule() {
            const me = this;
            const _data = this.parseRuleData(this.value.member_pool_permissions || []);
            crmRequire('crm-modules/action/cluepool/cluepool').then((Comp) => {
                me.comp = new Comp.Rules({
					el:  $(me.$el).find('.pb-allocaterule'),
					data: {
                        rulelist: me.dValue,
                        employeelist: _data.members,  //同事
                        memberlist: _data.groups,  //部门
                        grouplist: _data.userGroups,  //用户组
                        rolelist: _data.roles,  //角色
                        exEmployeelist: _data.exEmployees,  //外部人员
                        exEnterpriselist: _data.exEnterprise,  //外部企业
                        isOpenPRM: me.model.isOpenPRM,
                        poolType: me.parsePoolType(me.row.pool_type),
						from:'leadsPool_allocaterule',
                    },
					apiName: me.model.object_apiname,
                    poolData: me.row
				});
                me.comp.render();
				me.comp.on('change', me.onChange);
            })
        },
        parseRuleData(data) {
			var me = this;
			var groups = [], members = [], exEmployees = [], exEnterprise = [], userGroups = [], roles = [];
			let outer = CRM.get('outer');
			_.each(data || [], function (item) {
				switch(item.type) {
					case 1:  //内部人员
						var _m = CRM.util.getEmployeeById(item.data_id);
						_m && members.push(_m);
						break;
					case 2:  //内部部门
						var _g = CRM.util.getCircleById(item.data_id);
						_g && groups.push(_g);
						break;
					case 3:  //对接企业
						if(outer){
							var _exe = outer.contactsObject[item.data_id];
							_exe && exEnterprise.push(_exe);
						}
						break;
					case 4:  //对接人
						if(outer){
							var _exm = outer.employeesObject[item.data_id];
							_exm && exEmployees.push(_exm);
						}
						break;
					case 5:
						var _ug = CRM.util.getUserGroupByIds(item.data_id);
						_ug && _ug.length && userGroups.push(_ug[0]);
						break;
					case 6:
						var _r = CRM.util.getRoleByIds(item.data_id);
						_r && _r.length && roles.push(_r[0]);
						break;
					default:
						break;
				}
			});
			return {
				groups,
				members,
				userGroups,
				roles,
				exEmployees,
				exEnterprise
			}
        },
		parsePoolType(pool_type){
			let map = {
				'normal': 0,
				'private': 1
			}
			return map[pool_type] || 0;
		},
		refreshModel() {
			const me = this;
			if (!me.comp) return;
			const _data = this.parseRuleData(this.value.member_pool_permissions || []);
			const poolType = this.parsePoolType(this.value.pool_type);
			me.comp.set('employeelist', _data.members);
			me.comp.set('memberlist', _data.groups);
			me.comp.set('grouplist', _data.userGroups);
			me.comp.set('rolelist', _data.roles);
			me.comp.set('exEmployeelist', _data.exEmployees);
			me.comp.set('exEnterpriselist', _data.exEnterprise);
			me.comp.set('poolType', poolType);
		},
		onChangeRadio(){
			let _val = this.value;
      		_val = _.extend(_val,{
      		 allocate_choice: this.radio
      		})
		 	this.$emit("change", _val);
		}
    },
	watch: {
		'row.member_pool_permissions': function(val, oldVal) {
			this.refreshModel();
		},
		'row.pool_type': function(val, oldVal) {
			if (!this.comp) return;
			this.comp.resetRules();
			// this.refreshModel();
		},
		'row.partner_id': function(val, oldVal) {
			if (!this.comp) return;
			this.comp.resetRules();
			this.comp.set('exEmployeelist', []);
		}
	}
}
</script>

<style>

</style>