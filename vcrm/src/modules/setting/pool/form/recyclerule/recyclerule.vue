<template>
    <div class="pb-recyclerule"></div>
</template>

<script>
import crmRequire from '@common/require';
import Base from '../base';
import {toRenderRule, toSubmitRule} from '../../util/format';
export default {
    mixins: [Base],
    mounted() {
        this.initRule();
    },
    methods: {
        getdValue() {
            let val = (this.model.fields ? _.pick(this.value, this.model.fields) : this.value[this.field]) || [];
            val = toRenderRule(val, this.field);
            return val;
        },
        initRule() {
            const me = this;
            crmRequire('crm-modules/components/customerrule/customerrule').then((Comp) => {
                me.comp = new Comp.Rules({
					el: $(this.$el),
					// suffix: fieldname && fieldname.toLocaleLowerCase(),
					defaultValue: this.dValue,
					// compName: item.element,
                    apiName: this.model.object_apiname,
                    needDepartmentChildren: FS.util.getls('sfa_vcrm_leadpool_department_includechildren'),
                    data: {
                        RecyclingRuleList: this.dValue,
                        DataType: 1,
                        HighSeasID: this.row._id,
                    },
				});
                me.comp.render();
                me.comp.on('change', me.onChange);
            })
        },
        getValue() {
            console.log(this.field);
            let val = this.comp && this.comp.getValue();
            // 负责人规则
            if(val && Array.isArray(val) && val.length > 0 && this.field == 'pool_owner_rule'){
                  val = val.map(item => {
                item.filters.forEach(filter => {
                    let allCascade = filter.field_values.every(value => value.endsWith('_y'));
                    if (allCascade) {
                        filter.is_cascade = true;
                        // 去掉所有field_values中的_y后缀
                        filter.field_values = filter.field_values.map(value => 
                            value.endsWith('_y') ? value.slice(0, -2) : value
                        );
                    }
                });
                return item;
            });
            }
            val = toSubmitRule(val, this.field);
            // 回收规则
            if(this.field == 'recycling_rule_list'){
                val.forEach(rule => {
                    if (rule.recycling_filter_list && Array.isArray(rule.recycling_filter_list.filters)) {
                        rule.recycling_filter_list.filters.forEach(filter => {
                            if (filter.field_values && Array.isArray(filter.field_values)) {
                                const allCascade = filter.field_values.every(value => typeof value === 'string' && value.endsWith('_y'));
                                if (allCascade) {
                                    filter.is_cascade = true;
                                    filter.field_values = filter.field_values.map(value =>
                                        value.endsWith('_y') ? value.slice(0, -2) : value
                                    );
                                }
                            }
                        });
                        rule.wheres = JSON.stringify(rule.recycling_filter_list);
                    }
                });
            }
            // 分配规则
            if(this.field == 'allocate_rule_list'){
                val.forEach(rule => {
                    if (rule.wheres) {
                        // 1. 解析wheres为对象（数组）
                        let wheresArr = JSON.parse(rule.wheres);

                        // 2. 遍历wheres数组
                        wheresArr.forEach(whereItem => {
                            if (whereItem.filters && Array.isArray(whereItem.filters)) {
                                whereItem.filters.forEach(filter => {
                                    if (filter.field_values && Array.isArray(filter.field_values)) {
                                        // 判断是否全部以 _y 结尾
                                        const allCascade = filter.field_values.every(
                                            value => typeof value === 'string' && value.endsWith('_y')
                                        );
                                        if (allCascade && filter.field_values.length > 0) {
                                            filter.is_cascade = true;
                                            filter.field_values = filter.field_values.map(
                                                value => value.endsWith('_y') ? value.slice(0, -2) : value
                                            );
                                        }
                                    }
                                });
                            }
        });
        // 3. 处理完再 stringify 回去
        rule.wheres = JSON.stringify(wheresArr);
    }
});
            }
            return val;
        },
        getcValue() {
            let val = this.value;
            val[this.field] = this.getValue();
            return val;
        },
        onChange() {
			this.cValue = this.getcValue();
			this.$emit('change', this.cValue);
		},
    }
}
</script>

<style>

</style>