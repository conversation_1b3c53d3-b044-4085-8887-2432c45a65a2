<template>
    <div class="pb-recyclerule"></div>
</template>

<script>
import crmRequire from '@common/require';
import Base from '../base';
import {toRenderRule, toSubmitRule} from '../../util/format';
export default {
    mixins: [Base],
    mounted() {
        this.initRule();
    },
    methods: {
        getdValue() {
            let val = (this.model.fields ? _.pick(this.value, this.model.fields) : this.value[this.field]) || [];
            val = toRenderRule(val, this.field);
            return val;
        },
        initRule() {
            const me = this;
            crmRequire('crm-modules/components/customerrule/customerrule').then((Comp) => {
                me.comp = new Comp.Rules({
					el: $(this.$el),
					// suffix: fieldname && fieldname.toLocaleLowerCase(),
					defaultValue: this.dValue,
					// compName: item.element,
                    apiName: this.model.object_apiname,
                    data: {
                        RecyclingRuleList: this.dValue,
                        DataType: 1,
                        HighSeasID: this.row._id,
                    },
				});
                me.comp.render();
                me.comp.on('change', me.onChange);
            })
        },
        getValue() {
            let val = this.comp && this.comp.getValue();
            val = toSubmitRule(val, this.field);
            return val;
        },
        getcValue() {
            let val = this.value;
            val[this.field] = this.getValue();
            return val;
        },
        onChange() {
			this.cValue = this.getcValue();
			this.$emit('change', this.cValue);
		},
    }
}
</script>

<style>

</style>