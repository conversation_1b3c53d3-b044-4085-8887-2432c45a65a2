<template>
  <div 
  class="form-inputmult-leadpool j-comp-wrap"
  :data-apiname="model.apiname"
  >
    <fx-input
      ref="formInput"
      v-bind="model.compConfig"
      v-model="inputValue"
      size="small"
      :class="inputClass"
      :placeholder="cplaceholder"
      :clearable="false"
      @blur="onBlur"
      @focus="onFocus"
      @change="onChange"
    ></fx-input>
  </div>
</template>

<script>
import Base from "../base";
export default {
  mixins: [Base],
  props: {
        options: null,
  },
  computed: {
    cplaceholder() {
      return this.model.placeholder || `${$t("请输入")}${this.model.label}`;
    },
    cValue() {
      const me = this
      let _val = this.value;
      _val = _.extend(_val,{
        name: me.inputValue,
        name__lang : me.nameLang
      })
      return _val;
      }
  },
  data() {
    return {
      inputClass:'form-fxinput-class',
      nameLang : {},
      inputValue:''
    }
  },
  mounted() {
      const me = this;
      this.initData()
      me.getDescribeField().done((nameField) => {
      // 没有开启字段级别多余直接reture，开启才会渲染多语下拉框
      if (nameField.enable_multi_lang) {
        me.getCrmMultilang().done((Moudel) => {
          // 重写他的方法
            this.newModule = me.getNewModule(nameField,Moudel);
            this.newModule.renderMultilineActionbar()
        });
      }
      return;
    });
  },
  beforeDestroy(){
      this.newModule && this.newModule.destroyMultilineActionbar()
  },
  methods: {
    validate() {
      if (
        this.model.is_require &&
        (this.inputValue === "" || this.inputValue === " ")
      ) {
        CRM.util.showErrmsg(this.$wrap, $t("请输入") + this.model.label);
        return false;
      }
      CRM.util.hideErrmsg(this.$wrap);
      return true;
    },
    onBlur() {
      this.validate();
    },
    onFocus() {
      CRM.util.hideErrmsg(this.$wrap);
    },
    onChange() {
      this.$emit("change", this.cValue);
    },
    // 初始化组件需要的数据
    initData(){
      const me = this
      let v = this.dValue
      _.each(this.model.fields, (key) => {
            if(key === this.model.field){
              me.inputValue =  v[key] || ''
            }
            if(key === this.model.field + '__lang'){
               me.nameLang = v[key] || {}
            }
        })
    },
    // 复写平台多语的方法
    getNewModule(nameField,Moudel) {
      const me  = this
      return _.extend({},Moudel, {
            fieldAttr: nameField,
            $el: me.$wrap,
            $ipt:me.$wrap.find('input'),
            is_readonly:false,
            apiname:me.model.apiname,
            getData(){
              return  me.inputValue || ''
            },
            _getMultiLangData() {
              return me.nameLang || {}
            },
            _setMultiLangData(value, lang) {
              me.nameLang = lang
              me.$emit("change", me.cValue)
            },
      });
    },
    // 获取相关描述
    getDescribeField() {
      const me = this;
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi(
          {
            url:
              "/EM1HNCRM/API/v1/object/" +
              me.model.apiname +
              "/controller/DescribeLayout",
            data: {
              include_layout: true,
              include_ref_describe: true,
              apiname: me.model.apiname,
            },
            success: function (res) {
              if (res.Result.StatusCode === 0) {
                if (res.Value) {
                  resolve(res.Value?.objectDescribe?.fields?.name || {});
                }
                return;
              }
              CRM.util.alert(res.Result.FailureMessage);
            },
          },
          {
            errorAlertModel: 1,
          }
        );
      });
    },
    // 获取数据集多语模块
    getCrmMultilang() {
      return new Promise((resolve) => {
        seajs.use(
          "crm-modules/action/field/field",
          function (Module) {
            resolve(Module.C.multilang);
          }
        );
      });
    },
  },
};
</script>

<style  lang="less">
.form-inputmult-leadpool {
  position: relative;
}
</style>