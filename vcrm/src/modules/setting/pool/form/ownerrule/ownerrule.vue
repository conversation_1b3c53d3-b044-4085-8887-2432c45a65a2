<template>
    <div class="pb-ownerrule"></div>
</template>

<script>
import crmRequire from '@common/require';
import RecycleRule from '../recyclerule/recyclerule';
export default {
    extends: RecycleRule,
    methods: {
        initRule() {
            const me = this;
            crmRequire('crm-modules/action/cluepool/cluepool').then((Comp) => {
                me.comp = new Comp.OwnerRule({
                    el: $(me.$el),
                    data: me.dValue || null,
                    apiName: me.model.object_apiname,
                    needDepartmentChildren: FS.util.getls('sfa_vcrm_leadpool_department_includechildren'),//是否包含子部门
				});
                me.comp.on('change', me.onChange);
            })
        },
    }
}
</script>