<template>
    <fx-dialog
        :visible.sync="dialogVisible"
        ref="dialog1"
        class="batchedit-dialog"
        :append-to-body="true"
        :title="$t('批量编辑')"
        @closed="destroy">
        <div class="batchedit-content">
            <!-- <fx-scrollbar class="fx-scrollbar"
            :noresize="true"> -->
            <div class="batchedit-tabs" @click="switchTab">
                <div v-for="item in dColumns" :key="item.field" class="batchedit-tab" :class="[tabClass(item), {'is-active': item.field === curValue}]" :data-value="item.field">{{item.label}}</div>
            </div>
            <!-- </fx-scrollbar> -->
            <div class="batchedit-panels">
                <div v-for="item in dColumns" :key="item.field" class="batchedit-panel" :class="[panelClass(item), {'is-active': item.field === curValue}]" :data-value="item.field">
                    <div class="batchedit-panel-label">{{item.label}}</div>
                    <div class="batchedit-panel-content">
                        <component 
                        :ref="`${item.field}Comp`" 
                        :is="item.renderComp" 
                        :field="item.field" 
                        v-model="dValue" 
                        :row="dValue" 
                        :model="getModel(item)"
                        :valid="item.valid || valid"></component>
                    </div>
                </div>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <fx-button type="primary" @click="onSave" size="small">{{$t("保存当前项")}}</fx-button>
            <fx-button type="primary" @click="onSaveAll" size="small">{{$t("保存全部")}}</fx-button>
            <fx-button @click="onCancel" size="small">{{$t("取消")}}</fx-button>
        </span>
    </fx-dialog>
</template>

<script>
import forms from '../form/';
import getConfig from '../config/config';
import { toSubmitData } from '../util/format';
export default {
    components: forms,
    props: {
        columns: {
            type: Array,
        },
        apiname: '',
        value: {},
        model: {},
        isValid: {
            default: false,
        },
        isSubmit: {
            default: false,
        },
        authority:{
            default:{}
        }
    },
    data() {
        return {
            dColumns: [],
            dModel: {},
            dValue: {},
            dialogVisible: true,
            curValue: '',
            noTip: CRM.getLocal('batchedit-confirm-notip'),
            valid: false,
        }
    },
    mounted() {
        this.ajaxs = {};
        this.config = getConfig(this.apiname);
        this.dColumns = this.getdColumns();
        this.dModel = this.getdModel();
        this.dValue = this.getdValue();
        this.curValue = this.dColumns[0].field;
        this.dataList = this.model.dataList;
    },
    methods:{
        getdValue() {
            let _val = {
                object_describe_api_name: this.apiname,
                partner_id: null,
                pool_type:'normal',
                admin_pool_permissions: [],
                member_pool_permissions: [],
                is_claim_limit_include_dealed_customers: false,
                is_visible_to_member: false,
                claim_interval_days: 0,
                only_allow_member_move: false,
                only_allow_member_return: false,
                is_recycling_team_member: false,
                is_clean_owner: false,
                allow_member_relation: false,
                allow_member_send_feed: false,
                allow_member_view_feed: false,
                allow_member_view_log: false,
                skip_holidays: false,
                skip_allocate_work_free: false,
                recycling_rule_list: [],
                limit_type: 'personal',
            }
            if (this.apiname === 'LeadsPoolObj') {
                _val = _.extend(_val, {
                    is_new_to_notify_admin: true,
                    limit_count: 100,
                    is_choose_to_notify: false,
                    allocate_overtime_hours: null,
                    allocate_overtime_minutes: null,
                    overtime_hours: null,
                    overtime_minutes: null,
                    pool_owner_rule: '',
                })
            } else {
                _val = _.extend(_val, {
                    claim_limit_num: 10,
                    allow_member_move: false,
                    allow_admin_change_owner: false,
                    remind_rule_list: [],
                })
            }

            return _.extend(_val, this.config.initData, this.value);
        },
        getdColumns() {
            let _columns = this.columns || this.config.editColumns;
            if(this.apiname == 'LeadsPoolObj' && !this.authority.grayAllocateLimitFlag){
                 _columns = _.filter(_columns, (col) => {
                    return col.field != 'time_claim_limit' && col.field != 'time_allocate_limit';
                })
            }
            _.each(_columns, (col) => {
                col.valid = this.valid; //设定每个独立的valid
            })
            return _columns;
        },
        getdModel() {
            let _model = _.pick(this.config, 'object_apiname', 'display_name', 'object_display_name');
            _model = _.extend({
                apiname: this.apiname,
            }, _model, this.model);
            return _model;
        },
        getModel(item) {
            return _.extend({}, this.dModel, item);
        },
        tabClass(item) {
            return `batchedit-tab-${item.field}`;
        },
        panelClass(item) {
            return `batchedit-panel-${item.field}`;
        },
        switchTab(e) {
            let $tg = $(e.target);
            if (!$tg.hasClass('batchedit-tab')) return;
            let value = $tg.data('value');
            this.curValue = value;

        },
        getData() {
            let data = {};
            let curColumn = _.findWhere(this.dColumns, {field: this.curValue});
            curColumn.fields ? data = _.pick(this.dValue, curColumn.fields) : data[curColumn.field] = this.dValue[curColumn.field];
            return data;
        },
        validate(field) {
            let errs = $('.fm-error', this.$el);
            let _errs = [];
            if (field === 'all') {
                _errs = errs
            } else {
                _.each(errs, (err) => {
                    if ($(err).closest('.batchedit-panel.is-active').length) {
                        _errs.push(err);
                    }
                })
            }
            let flag = _errs.length ? false : true;
            return flag;
        },
        doValidate(field) {
            return new Promise((resolve) => {
                if (!this.isValid) return resolve(true);
                if (field === 'all') {
                    this.valid = true;
                    this.$nextTick(() => {
                        this.valid = false;
                        if (!this.validate(field)) {
                            CRM.util.remind(3, $t('请填写必填项'));
                            return resolve(false);
                        }
                        return resolve(true);
                    })
                } else {
                    let curColumn = _.findWhere(this.dColumns, {field: this.curValue});
                    curColumn.valid = true;
                    this.$nextTick(() => {
                        curColumn.valid = false;
                        if (!this.validate()) {
                            CRM.util.remind(3, $t('请填写数据'));
                            return resolve(false);
                        }
                        return resolve(true);
                    })
                }
            })
        },
        async onSave() {
            let flag = await this.doValidate();
            if (!flag) return;
            let data = this.getData();
            this.$emit('save', data, this.curValue);
            if (this.isSubmit) {
                this.onSubmit(data);
            }
        },
        async onSaveAll() {
            const me = this;
            let flag = await me.doValidate('all');
            if (!flag) return;

            if (this.noTip) {
                me.doSaveAll();
                return;
            }
            const h = this.$createElement;
            this.$msgbox({
                title: $t('提示'),
                message: h('div', null, [
                    h('div', {
                        class: 'confirm-content',
                    }, $t('此操作将覆盖被选中所有配置项')),
                    h('div', {
                        class: 'confirm-notip',
                    }, [h('fx-checkbox', {
                        directives: [{
                            name: 'model',
                            value: this.noTip,
                        }],
                        on: {
                            change(val) {
                                CRM.setLocal('batchedit-confirm-notip', val);
                            },
                        },
                    }, $t('不再提示'))]),
                ]),
                customClass: 'batchedit-confirm',
                showCancelButton: true,
                confirmButtonText: $t('确定'),
                cancelButtonText: $t('取消'),
            }).then(() => {
                me.doSaveAll();
            }).catch(() => {});
        },
        doSaveAll() {
            let data = this.dValue;
            this.$emit('save', data, 'all');
            if (this.isSubmit) {
                this.onSubmit(data);
            } else {
                this.onCancel();
            }
        },      
        onSubmit(data) {
            const me = this;
            data = this.toSubmitData(data);
            me.checkDuplicate(data).then(() => {
                me.doSubmit(data);
            }, (msg) => {
                this.$msgbox({
                    title: $t('提示'),
                    message: msg,
                    customClass: 'batchedit-confirm',
                    showCancelButton: true,
                    confirmButtonText: $t('继续保存'),
                    cancelButtonText: $t('取消'),
                }).then(() => {
                    me.doSubmit(data);
                }).catch(() => {});
            });
            
        },
        doSubmit(data) {
            const me = this;
            CRM.util.waiting(true);
            let _tip = $t('编辑数据需要较长时间，完成后发通知');
            me.ajaxs.submit = CRM.util.createJob({
                "apiFullName": me.display_name,
                "apiName": me.apiname,
                "queryParam": {
                    "pool_data_args": data,
                },
                "jobType": 3,
                "templateId": "crm_005"
            }).then((res) => {
                if (res.status < 2) {
                    setTimeout(function(){
                        CRM.util.queryJobStatus(res, me.ajaxs, _tip).then((msg) => {
                            resSuccess(msg);
                        }, (msg) => {
                            resFailure(msg);
                        });
                    }, 2000);
                }
            }, (msg) => {
                resFailure(msg);
            }).finally(() => {
                me.ajaxs.submit = null;
            });

            function resFailure(msg) {
                CRM.util.waiting(false);
                CRM.util.alert(msg);
            }
    
            function resSuccess(msg) {
                CRM.util.waiting(false);
                me.$emit('success');
                me.hide();
                msg ? CRM.util.alert(msg) : CRM.util.remind(msg || $t('编辑成功'));
            }
        },
        toSubmitData(data) {
            let _data = toSubmitData([data], this.apiname, this.config.topFields);
            _data = _data[0];
            let datas = [];
            _.each(this.dataList, (item) => {
                let _item = JSON.parse(JSON.stringify(_data));
                _item.data._id = item._id;
                _item.data.object_describe_api_name = this.apiname;
                datas.push(_item);
            })
            return datas;
        },
        checkDuplicate(data) {
            const me = this;
            return new Promise((resolve, reject) => {
                me.ajaxs.check = CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/pool_service/service/bulk_update_validate',
                    data: {
                        pool_data_args: data,
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(); 
                            return;
                        }
                        reject(res.Result.FailureMessage || $t("服务器返回错误!"));
                    }
                }, {
                    errorAlertModel: 1,
                })
            })
        },
        onCancel() {
            this.hide();
        },
        hide() {
            this.dialogVisible = false;
        },
        destroy() {
            _.each(this.ajaxs, function(ajax){
                ajax && ajax.abort && ajax.abort();
                ajax = null;
            });
            _.each(this.comps, (comp) => {
                comp && comp.destroy && comp.destroy();
            });
            this.$destroy();
        },
    },
}
</script>

<style lang="less">
.batchedit-dialog{
    .el-dialog__headerbtn .el-icon{
        font-size: 20px;
    }
    .el-dialog__body{
        padding:0;
        min-height:380px;
    }
    .batchedit-content{
        display: flex;
        font-size: 13px;
    }
    .batchedit-tabs{
        width:220px;
        border-right: 1px solid #DEE1E6;
        line-height: 35px;
        padding: 15px 0;
    }
    .batchedit-tab{
        padding: 0 20px;
        cursor: pointer;
        &.is-active{
            background-color: var(--color-primary01);
        }
    }
    .batchedit-panels{
        padding:17px 20px;
        flex: 1;
    }
    .batchedit-panel{
        display: none;
        line-height: 30px;
        &.is-active{
            display: block;
        }
    }
}
.batchedit-confirm{
    .confirm-notip{
        position:absolute;
        bottom: -36px;
    }
    .el-message-box__content{
        padding: 20px 15px;
    }
    .el-message-box__btns{
        border-top: #ddd solid 1px;
        padding-top: 10px;
    }
}
</style>