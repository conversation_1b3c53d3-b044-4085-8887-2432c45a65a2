/*
 * @Descripttion:
 * @Author: LiAng
 * @Date: 2023-11-17 21:25:38
 * @LastEditors: LiAng
 * @LastEditTime: 2025-04-14 11:52:18
 */

// 多级订货插件加载条件: 开启多级订货 && 上游应用
const multiLevelOrderSFAInnerPluginLoad = (config) => (config.dht_multi_level_order == '1' && !CRM.util.isConnectApp());

/**
 * 字段说明
 * https://wiki.firstshare.cn/pages/viewpage.action?pageId=162503203#id-1.%E4%B8%9A%E5%8A%A1%E6%8F%92%E4%BB%B6%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88-%E6%9F%A5%E8%AF%A2%E5%AF%B9%E8%B1%A1%E6%8F%92%E4%BB%B6%E6%8E%A5%E5%8F%A3(PluginList)
 *
 * 特殊字段说明
 * load:https://wiki.firstshare.cn/pages/viewpage.action?pageId=336495953#id-%E4%B8%9A%E5%8A%A1%E6%8F%92%E4%BB%B6%E6%96%87%E6%A1%A3-1.%E6%8F%92%E4%BB%B6%E4%BB%8E%E5%93%AA%E6%9D%A5
 */
export default function (opts) {
    const result = {
        // 测试代码
        // 'object_4O885__c': {
        //     domains: [{
        //         pluginApiName: 'test',
        //         objectApiName: 'object_4O885__c',
        //         fieldApiName: '',
        //         resource: function () {
        //             return window.PAAS.plugin?.libs.get('TestPlugin')
        //         },
        //         params: {}
        //     }],
        //     'FSAID_PaaS_861247244485': {
        //         domains: [{
        //             pluginApiName: 'test',
        //             objectApiName: 'object_4O885__c',
        //             fieldApiName: '',
        //             resource: function () {
        //                 return window.PAAS.plugin?.libs.get('TestPlugin')
        //             },
        //             params: {}
        //         }],
        //     }
        // },
        PriceBookObj: {
            domains: [
                {
                    pluginApiName: 'applyaccountrange',
                    objectApiName: 'PriceBookObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/applyaccountrange',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'PriceBookAccountObj',
                                fieldMapping: {}
                            },
                        ],
                    },
                    load: function (config, plugin) {
                        return true;
                    }
                },
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'PriceBookObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                    },
                    load: multiLevelOrderSFAInnerPluginLoad
                }
            ]
        },
        PricePolicyObj: {
            domains: [
                {
                    pluginApiName: 'pricepolicyaccountobj',
                    objectApiName: 'PricePolicyObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/pricepolicyaccountobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'PricePolicyAccountObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                }, {
                    pluginApiName: 'pricepolicyproductobj',
                    objectApiName: 'PricePolicyObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/pricepolicyproductobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'PricePolicyProductObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },
        AvailableRangeObj: {
            domains: [
                {
                    pluginApiName: 'availableaccountobj',
                    objectApiName: 'AvailableRangeObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/availableaccountobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            // {
                            //     objectApiName: 'AvailableProductObj',
                            //     fieldMapping: {}
                            // },
                            {
                                objectApiName: 'AvailableAccountObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
                {
                    pluginApiName: 'availableproductobj',
                    objectApiName: 'AvailableRangeObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/availableproductobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AvailableProductObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
                {
                pluginApiName: 'availablepricebookobj',
                    objectApiName: 'AvailableRangeObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/availablepricebookobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AvailablePriceBookObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'AvailableRangeObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                    },
                    load(config) {
                        return multiLevelOrderSFAInnerPluginLoad(config) && config.available_price_book != '1';
                    }
                },
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'AvailableRangeObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AvailablePriceBookObj',
                                fieldMapping: {}
                            }
                        ],
                        customParam: {
                            detailFilterObjectApiNames: ['AvailablePriceBookObj.price_book_id']
                        }
                    },
                    load(config) {
                        return multiLevelOrderSFAInnerPluginLoad(config) && config.available_price_book == '1';
                    }
                }
            ]
        },
        QuoteObj: {
            domains: [
                {
                    pluginApiName: 'quotetrialcalculate',
                    objectApiName: 'QuoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/quotetrialcalculate',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'QuoteLinesObj',
                                fieldMapping: {}
                            }
                        ]
                    }, load: function (config, plugin) {
                        const hasPolicyPlugin = plugin.findIndex(p => p.pluginApiName == 'price_policy') >= 0;
                        return !hasPolicyPlugin && config.is_test_calculate == '1';
                    }
                },
                {
                    pluginApiName: 'quotelinehistory',
                    objectApiName: 'QuoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/quotelinehistory',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'QuoteLinesObj',
                                fieldMapping: {}
                            }
                        ]
                    },
                    load: function (config, plugin) {
                        // return true;
                        const hasPolicyPlugin = plugin.findIndex(p => p.pluginApiName == 'price_policy') >= 0;
                        return !hasPolicyPlugin && !config.priceBookPriority && !config.openAttribute && opts.type === 'add' && config.quote_history_price;
                    }
                },
                {
                    pluginApiName: 'backcalculation',
                    objectApiName: 'QuoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/backcalculation',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'QuoteLinesObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
                {
                    pluginApiName: 'quoteobjhistory',
                    objectApiName: 'QuoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/quoteobjhistory',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'QuoteLinesObj',
                                fieldMapping: {}
                            }
                        ]
                    },
                    load: function (config, plugin) {
                        // 开价格政策、开强制优先级，不展示历史报价
                        const hasPolicyPlugin = plugin.findIndex(p => p.pluginApiName == 'price_policy') >= 0;
                        return !hasPolicyPlugin && !config.priceBookPriority && opts.type === 'add' && config.quote_history_price;
                    }
                },
                {
                    pluginApiName: 'mc_currency',
                    objectApiName: 'QuoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/mc_currency',
                    params: {
                        "fieldMapping": {
                            // "form_price_book_id": "price_book_id",
                            // "form_life_status": "life_status ",
                            // "form_account_id": "account_id",
                            // "form_partner_id": "partner_id",
                            "form_mc_currency": "mc_currency"
                        },
                        "details": [{
                            "objectApiName": "QuoteLinesObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {
                                // "quantity": "quantity",
                                // "is_giveaway": "is_giveaway",
                                // "price_book_product_id": "price_book_product_id",
                                // "discount": "discount",
                                // "price_book_price": "price_book_price",
                                // "product_price": "price",
                                // "price_book_discount": "price_book_discount",
                                // "subtotal": "total_amount",
                                // "price_book_id": "price_book_id",
                                // "product_id": "product_id",
                                // "sales_price": "selling_price",
                                // "parent_prod_package_id": "parent_prod_package_id",
                                // "parent_prod_pkg_key": "parent_prod_pkg_key"
                            }
                        }]
                    },
                    load: function (config) {
                        return CRM._cache.currencyStatus;
                    }
                },
                // {
                //     pluginApiName: 'excelimport',
                //     objectApiName: 'QuoteObj',
                //     fieldApiName: '',
                //     resource: 'vcrm/plugin/excelimport',
                //     params: {
                //         fieldMapping: {
                //             "form_price_book_id": "price_book_id",
                //             "form_account_id": "account_id",
                //             "form_partner_id": "partner_id",
                //             "form_mc_currency": "mc_currency"
                //         },
                //         details: [
                //             {
                //                 objectApiName: 'QuoteLinesObj',
                //                 fieldMapping: {
                //                     product_id: "product_id"
                //                 }
                //             }
                //         ]
                //     },
                //     // load: function (config) {
                //     //     return CRM.util.isGrayScale('CRM_USE_PLUGIN_EXCELIMPORT');
                //     // }
                // },
                {
                    pluginApiName: 'excelpaste',
                    objectApiName: 'QuoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/excelpaste',
                    params: {
                        fieldMapping: {
                            form_price_book_id: 'price_book_id',
                        },
                        details: [
                            {
                                objectApiName: 'QuoteLinesObj',
                                fieldMapping: {
                                    product_id: "product_id",
                                    price_book_product_id: "price_book_product_id",
                                    price_book_id: "price_book_id",
                                    product_price: 'price',
                                }
                            }
                        ]
                    },
                },
                {
                    pluginApiName: 'selfcheck',
                    objectApiName: 'QuoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/selfcheck',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'QuoteLinesObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
            ],
            fields: [],
            hooks: []
        },
        SalesOrderObj: {
            domains: [
                {
                    pluginApiName: 'backcalculation',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/backcalculation',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SalesOrderProductObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
                {
                    pluginApiName: 'mc_currency',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/mc_currency',
                    params: {
                        "fieldMapping": {
                            "form_price_book_id": "price_book_id",
                            "form_life_status": "life_status ",
                            "form_account_id": "account_id",
                            "form_partner_id": "partner_id",
                            "form_mc_currency": "mc_currency"
                        },
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {
                                // "quantity": "quantity",
                                // "is_giveaway": "is_giveaway",
                                // "price_book_product_id": "price_book_product_id",
                                // "discount": "discount",
                                // "price_book_price": "price_book_price",
                                // "price_book_discount": "price_book_discount",
                                // "price_book_id": "price_book_id",
                                // "product_id": "product_id",
                                // "parent_prod_package_id": "parent_prod_package_id",
                                // "parent_prod_pkg_key": "parent_prod_pkg_key",
                                //
                                // "product_price": "product_price",
                                // "sales_price": "sales_price",
                                // "subtotal": "subtotal",
                            }
                        }]
                    },
                    load: function (config) {
                        return CRM._cache.currencyStatus;
                    }
                },
                {
                    pluginApiName: 'salesorderobj_shopcategory',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salesorderobj_shopcategory',
                    params: {
                        "fieldMapping": {},
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return config.use_shop_category == '1';
                    }
                },
                {
                    pluginApiName: 'salesorderobj_salecontract',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salesorderobj_salecontract',
                    params: {
                        "fieldMapping": {
                            // "form_price_book_id": "price_book_id",
                            // "form_life_status": "life_status ",
                            // "form_account_id": "account_id",
                            // "form_partner_id": "partner_id",
                            // "form_mc_currency": "mc_currency"
                        },
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {
                                // "quantity": "quantity",
                                // "is_giveaway": "is_giveaway",
                                // "price_book_product_id": "price_book_product_id",
                                // "discount": "discount",
                                // "price_book_price": "price_book_price",
                                // "price_book_discount": "price_book_discount",
                                // "price_book_id": "price_book_id",
                                // "product_id": "product_id",
                                // "parent_prod_package_id": "parent_prod_package_id",
                                // "parent_prod_pkg_key": "parent_prod_pkg_key",
                                // "product_price": "product_price",
                                // "sales_price": "sales_price",
                                // "subtotal": "subtotal",
                                mdApiName: 'SalesOrderProductObj'
                            }
                        }]
                    },
                    load: function (config) {
                        return config.sale_contract;
                    }
                },
                {
                    pluginApiName: 'salesorderobj_history',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salesorderobj_history',
                    params: {
                        "fieldMapping": {
                            form_account_id: 'account_id',
                            form_partner_id: 'partner_id',
                            form_price_book_id: 'price_book_id',
                            form_mc_currency: 'mc_currency', // 价目表id
                        },
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return !config.openAttribute && config.clone_history_order_product == '1' && opts.type === 'add';
                        //return true
                    }
                },
                {
                    pluginApiName: 'manual_gift',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/manual_gift',
                    params: {
                        "fieldMapping": {
                            "account_id": "account_id",
                            "partner_id": "partner_id",
                            "form_price_book_id": "price_book_id",
                        },
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "detailKey": "manual_gift_detail",
                            "fieldMapping": {
                                product_id: "product_id",
                                price_book_id: "price_book_id",
                                price_book_product_id: "price_book_product_id",
                                quantity: "quantity",
                                base_unit_count: "base_unit_count",
                                actual_unit: "actual_unit",
                                price_book_price: "price_book_price",
                                product_price: "product_price",
                                discount: "discount",
                                policy_dynamic_amount: "policy_dynamic_amount",
                                gift_amortize_price: "gift_amortize_price",
                                stand_price: "stand_price",
                                conversion_ratio: "conversion_ratio",
                                dynamic_amount:"dynamic_amount"
                            }
                        }]
                    },
                    load: function (config) {
                        return config.manual_gift == '1';
                    }
                },
                {
                    pluginApiName: 'statistics',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/statistics',
                    params: {
                        "fieldMapping": {
                            "account_id": "account_id",
                            "misc_content": "misc_content"
                        },
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "detailKey": "statistics_detail",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return true;
                    }
                },
                {
                    pluginApiName: 'salesorderobj_customer',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salesorderobj_customer',
                    params: {
                        "fieldMapping": {
                            form_account_id: 'account_id',
                            form_ship_to_tel: 'ship_to_tel',
                            form_ship_to_add: 'ship_to_add',
                            form_ship_to_add_control: 'ship_to_add_control', // 新增客户地址用于设置筛选条件
                            form_ship_to_id: 'ship_to_id', // 收货人
                        },
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return true;
                    }
                },
                // {
                //     pluginApiName: 'excelimport',
                //     objectApiName: 'SalesOrderObj',
                //     fieldApiName: '',
                //     resource: 'vcrm/plugin/excelimport',
                //     params: {
                //         fieldMapping: {
                //             "form_price_book_id": "price_book_id",
                //             "form_account_id": "account_id",
                //             "form_partner_id": "partner_id",
                //             "form_mc_currency": "mc_currency"
                //         },
                //         details: [
                //             {
                //                 objectApiName: 'SalesOrderProductObj',
                //                 fieldMapping: {
                //                     product_id: "product_id"
                //                 }
                //             }
                //         ]
                //     },
                //     // load: function (config) {
                //     //     return CRM.util.isGrayScale('CRM_USE_PLUGIN_EXCELIMPORT');
                //     // }
                // },
                {
                    pluginApiName: 'excelpaste',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/excelpaste',
                    params: {
                        fieldMapping: {
                            form_price_book_id: 'price_book_id',
                        },
                        details: [
                            {
                                objectApiName: 'SalesOrderProductObj',
                                fieldMapping: {
                                    product_id: "product_id",
                                    price_book_product_id: "price_book_product_id",
                                    price_book_id: "price_book_id",
                                    product_price: 'product_price',
                                }
                            }
                        ]
                    },
                },
                {
                    pluginApiName: 'add_md_by_product',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/add_md_by_product',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SalesOrderProductObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
                {
                    pluginApiName: 'orderformhly',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    index: 9999,
                    resource: 'vcrm/plugin/orderformhly',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SalesOrderProductObj',
                                fieldMapping: {}
                            }
                        ]
                    },
                    load: function (config) {
                        return CRM.util.getUserAttribute('isHLYQuotoObj') && CRM.util.isUsePlugin('SalesOrderObj')
                    }
                },
                {
                    pluginApiName: 'selfcheck',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/selfcheck',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SalesOrderProductObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },{
                    pluginApiName: 'closeorder',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/closeorder',
                    params: {
                        "fieldMapping": {
                            "account_id": "account_id",
                            "misc_content": "misc_content"
                        },
                        "details": [{
                            "objectApiName": "SalesOrderProductObj",
                            "detailKey": "close_order",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        let data = {};
                        try {
                            data = config.order_close ? JSON.parse(config.order_close) : {};
                         } catch (error) {
                            console.log('Failed to parse order_close:', error);
                        }
                        return data.status === '1';
                    }
                },
                {
                    pluginApiName: 'asynccommit',
                    objectApiName: 'SalesOrderObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/async_commit',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SalesOrderProductObj',
                                fieldMapping: {}
                            }
                        ],
                        customParam: {
                            templateId: 'sfa_sales_order',
                            jobType: '5',
                        }
                    },
                    load: function (config) {
                        return config.async_create_order === '1';
                    }
                },
                // {
                //     pluginApiName: 'point',
                //     objectApiName: 'SalesOrderObj',
                //     resource: 'vcrm/plugin/point',
                //     params: {
                //         fieldMapping: {
                //             "receivable_amount": "payment_amount",
                //             "loyalty_amount": "loyalty_amount",
                //             "loyalty_detail": "loyalty_detail"
                //         },
                //         details: [
                //             {
                //                 objectApiName: 'SalesOrderProductObj',
                //                 fieldMapping: {}
                //             }
                //         ]
                //     },
                //     load: function (config) {
                //         return config.sfa_loyalty_plugin_switch_apply_SalesOrderObj;
                //     }
                // }

                // {
                //     pluginApiName: 'non_standard_product',
                //     objectApiName: 'SalesOrderObj',
                //     fieldApiName: '',
                //     resource: 'vcrm/plugin/nonstandard_product',
                //     params: {
                //         fieldMapping: {},
                //         details: [
                //             {
                //                 objectApiName: 'SalesOrderProductObj',
                //                 "detailKey": "real_price_detail",
                //                 fieldMapping: {
                //                     "discount": "discount",
                //                     "product_price": "product_price",
                //                     "detail_type": "detail_type",
                //                     "product_id": "product_id",
                //                     "non_standard_pro_description": "non_standard_pro_description"
                //                 }
                //             }
                //         ]
                //     }
                // },
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'SalesOrderObj',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                        customParam: {
                            masterFilterFields: ['price_book_id', 'shipping_warehouse_id']
                        }
                    },
                    load: multiLevelOrderSFAInnerPluginLoad
                },
            ],
            fields: [],
            hooks: []
        },
        SaleContractObj: {
            domains: [
                {
                    pluginApiName: 'backcalculation',
                    objectApiName: 'SaleContractObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/backcalculation',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SaleContractLineObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
                {
                    pluginApiName: 'salesorderobj_salecontract',
                    objectApiName: 'SaleContractObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salesorderobj_salecontract',
                    params: {
                        "fieldMapping": {
                            form_account_id: 'account_id',
                            form_sale_contract_id: 'quote_id',
                            form_price_book_id: 'price_book_id', // 价目表id
                            form_partner_id: 'form_partner_id', // 合作伙伴id
                        },
                        "details": [{
                            "objectApiName": "SaleContractLineObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {
                                quantity: 'quantity',
                                product_id: 'product_id',
                                price_book_id: 'price_book_id',
                                sale_contract_id: 'quote_id',
                                sale_contract_line_id: 'quote_line_id',
                                // 开cpq用
                                prod_pkg_key: 'prod_pkg_key',
                                parent_prod_pkg_key: 'parent_prod_pkg_key',
                                root_prod_pkg_key: 'root_prod_pkg_key',
                                product_group_id: 'product_group_id',
                                is_package: 'is_package',
                                // 特殊字段
                                filterApiName: 'quote_id',
                                sourceMasterApiName: 'QuoteObj', // 数据来源的主对象apiname
                                mdApiName: 'SaleContractLineObj'
                            }
                        }]
                    },
                    load: function (config) {
                        return true;
                    }
                },
                {
                    pluginApiName: 'mc_currency',
                    objectApiName: 'SaleContractObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/mc_currency',
                    params: {
                        "fieldMapping": {
                            "form_mc_currency": "mc_currency"
                        },
                        "details": [{
                            "objectApiName": "SaleContractLineObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return CRM._cache.currencyStatus;
                    }
                },
                {
                    pluginApiName: 'salecontractobj_md',
                    objectApiName: 'SaleContractObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salecontractobj_md',
                    params: {
                        "fieldMapping": {
                            form_account_id: 'account_id',
                            form_sale_contract_id: 'quote_id',
                            form_price_book_id: 'price_book_id', // 价目表id
                        },
                        "details": [{
                            "objectApiName": "SaleContractLineObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {}
                        }]
                    }
                },
                // {
                //     pluginApiName: 'excelimport',
                //     objectApiName: 'SaleContractObj',
                //     fieldApiName: '',
                //     resource: 'vcrm/plugin/excelimport',
                //     params: {
                //         fieldMapping: {
                //             "form_price_book_id": "price_book_id",
                //             "form_account_id": "account_id",
                //             "form_partner_id": "partner_id",
                //             "form_mc_currency": "mc_currency"
                //         },
                //         details: [
                //             {
                //                 objectApiName: 'SaleContractLineObj',
                //                 fieldMapping: {
                //                     product_id: "product_id"
                //                 }
                //             }
                //         ]
                //     }
                // },
                {
                    pluginApiName: 'excelpaste',
                    objectApiName: 'SaleContractObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/excelpaste',
                    params: {
                        fieldMapping: {
                            form_price_book_id: 'price_book_id',
                        },
                        details: [
                            {
                                objectApiName: 'SaleContractLineObj',
                                fieldMapping: {
                                    product_id: "product_id",
                                    price_book_product_id: "price_book_product_id",
                                    price_book_id: "price_book_id",
                                    product_price: 'product_price',
                                }
                            }
                        ]
                    },
                },
                {
                    pluginApiName: 'statistics',
                    objectApiName: 'SaleContractObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/statistics',
                    params: {
                        "fieldMapping": {
                            "account_id": "account_id",
                            "misc_content": "misc_content"
                        },
                        "details": [{
                            "objectApiName": "SaleContractLineObj",
                            "detailKey": "statistics_detail",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return CRM.util.isGrayScale("CRM_SALECONTRACT_USE_STATISTICS");

                    }
                },
                {
                    pluginApiName: 'selfcheck',
                    objectApiName: 'SaleContractObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/selfcheck',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SaleContractLineObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
            ],
            fields: [],
            hooks: []
        },
        NewOpportunityObj: {
            domains: [
                {
                    pluginApiName: 'mc_currency',
                    objectApiName: 'NewOpportunityObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/mc_currency',
                    params: {
                        "fieldMapping": {
                            // "form_price_book_id": "price_book_id",
                            // "form_life_status": "life_status ",
                            // "form_account_id": "account_id",
                            // "form_partner_id": "partner_id",
                            "form_mc_currency": "mc_currency"
                        },
                        "details": [{
                            "objectApiName": "NewOpportunityLinesObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return CRM._cache.currencyStatus;
                    }
                },
                {
                    pluginApiName: 'backcalculation',
                    objectApiName: 'NewOpportunityObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/backcalculation',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'NewOpportunityLinesObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
                // {
                //     pluginApiName: 'excelimport',
                //     objectApiName: 'NewOpportunityObj',
                //     fieldApiName: '',
                //     resource: 'vcrm/plugin/excelimport',
                //     params: {
                //         fieldMapping: {
                //             "form_price_book_id": "price_book_id",
                //             "form_account_id": "account_id",
                //             "form_partner_id": "partner_id",
                //             "form_mc_currency": "mc_currency"
                //         },
                //         details: [
                //             {
                //                 objectApiName: 'NewOpportunityLinesObj',
                //                 fieldMapping: {
                //                     product_id: "product_id"
                //                 }
                //             }
                //         ]
                //     },
                //     // load: function (config) {
                //     //     return CRM.util.isGrayScale('CRM_USE_PLUGIN_EXCELIMPORT');
                //     // }
                // },
                {
                    pluginApiName: 'excelpaste',
                    objectApiName: 'NewOpportunityObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/excelpaste',
                    params: {
                        fieldMapping: {
                            form_price_book_id: 'price_book_id',
                        },
                        details: [
                            {
                                objectApiName: 'NewOpportunityLinesObj',
                                fieldMapping: {
                                    product_id: "product_id",
                                    price_book_product_id: "price_book_product_id",
                                    price_book_id: "price_book_id",
                                    product_price: 'price',
                                }
                            }
                        ]
                    },
                },
                {
                    pluginApiName: 'selfcheck',
                    objectApiName: 'NewOpportunityObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/selfcheck',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'NewOpportunityLinesObj',
                                fieldMapping: {}
                            }
                        ]
                    }
                },
            ]
        },
        AccountObj: {
            domains: [{
                pluginApiName: 'accountaddrobj',
                objectApiName: 'AccountObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/accountaddrobj',
                params: {
                    fieldMapping: {},
                    details: [{
                        objectApiName: 'AccountAddrObj',
                        fieldMapping: {}
                    }]
                }
            }, {
                pluginApiName: 'accountfininfoobj',
                objectApiName: 'AccountObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/accountfininfoobj',
                params: {
                    fieldMapping: {},
                    details: [{
                        objectApiName: 'AccountFinInfoObj',
                        fieldMapping: {}
                    }]
                }
            },
                // }, {
                //     pluginApiName: "business_circles_plug",
                //     objectApiName: "AccountObj",
                //     resource: "vcrm/plugin/bquery",
                //     params: {
                //         fieldMapping: {
                //             fieldApiName: "name",
                //             bizRegNameFieldApiName: "biz_reg_name",
                //             industryExtFieldApiName: "industry_ext"
                //         },
                //         customParam: {
                //             mapping_rule_api_name: "button_fillICInfo__c"
                //         }
                //     },
                // }, {
                //     pluginApiName: 'accountbquery',
                //     objectApiName: 'AccountObj',
                //     fieldApiName: '',
                //     resource: 'vcrm/plugin/accountbquery',
                //     params: {
                //         fieldMapping: {},
                //     }
                {
                    pluginApiName: 'mengniu-salesarea',
                    objectApiName: 'AccountObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/mengniu-salesarea',
                    params: {
                        fieldMapping: {}
                    },
                    load: function (config) {
                        return location.host.indexOf('mengniu') > -1
                    }
            }]
        },
        InvoiceApplicationObj: {
            domains: [
                {
                    pluginApiName: 'detail_multi_source',
                    objectApiName: 'InvoiceApplicationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/detail_multi_source',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'InvoiceApplicationLinesObj',
                                fieldMapping: {}
                            },
                        ],
                        customParam: {
                            retainMDSysBtns: ['Single_Add', 'Import_Excel'],
                            ruleConfigKey: 'invoice_lines_mapping_rule',
                        }
                    },
                    load: function (config) {
                        return config.invoice_lines_multi_source === '1';
                    }
                },
                {
                    pluginApiName: 'invoiceapplicationlinesobj',
                    objectApiName: 'InvoiceApplicationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/invoiceapplicationlinesobj',
                    params: {
                        fieldMapping: {},
                        details: [{
                            objectApiName: 'InvoiceApplicationLinesObj',
                            fieldMapping: {}
                        }]
                    },
                    load({invoice_order_binding_status}) {
                        return invoice_order_binding_status === void 0 || invoice_order_binding_status === '1';
                    }
                },
                {
                    pluginApiName: 'mc_currency',
                    objectApiName: 'InvoiceApplicationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/mc_currency',
                    params: {
                        "fieldMapping": {
                            "form_mc_currency": "mc_currency"
                        },
                        "details": [{
                            "objectApiName": "InvoiceApplicationLinesObj",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return CRM._cache.currencyStatus;
                    }
                },
                {
                    pluginApiName: 'invoiceapplication_address_invoice_backfill',
                    objectApiName: 'InvoiceApplicationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/invoiceapplication_address_invoice_backfill',
                    params: {
                        fieldMapping: {},
                    }
                },
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'InvoiceApplicationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'InvoiceApplicationLinesObj',
                                fieldMapping: {}
                            }
                        ],
                        customParam: {
                            masterFilterFields: ['order_id'],
                            detailFilterObjectApiNames: ['InvoiceApplicationLinesObj.order_id']
                        }
                    },
                    load: multiLevelOrderSFAInnerPluginLoad
                }
            ]
        },
        BomAttributeConstraintObj: {
            domains: [
                {
                    pluginApiName: 'bom_attribute_constraintobj_product_id',
                    objectApiName: 'BomAttributeConstraintObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/bom_attribute_constraintobj_product_id',
                    params: {
                        "fieldMapping": {},
                        // "details": [{
                        //     "objectApiName": "SalesOrderProductObj",
                        //     "detailKey": "real_price_detail",
                        //     "fieldMapping": {
                        //
                        //     }
                        // }]
                    },
                    load: function (config) {
                        return true;
                    }
                }, {
                    pluginApiName: 'bom_attribute_constraintobj_md',
                    objectApiName: 'BomAttributeConstraintObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/bom_attribute_constraintobj_md',
                    params: {
                        "fieldMapping": {},
                        "details": [{
                            "objectApiName": "BomAttributeConstraintLinesObj",
                            "detailKey": "real_price_detail",
                            "fieldMapping": {}
                        }]
                    },
                    load: function (config) {
                        return true;
                    }
                },
            ],
            fields: [],
            hooks: []
        },
        AttributeObj: {
            domains: [
                {
                    pluginApiName: 'attribute_value',
                    objectApiName: 'AttributeObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/attribute_value',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AttributeValueObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },
        SpecificationObj: {
            domains: [
                {
                    pluginApiName: 'specification_value',
                    objectApiName: 'SpecificationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/specification_value',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SpecificationValueObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },
        AttributePriceBookObj: {
            domains: [
                {
                    pluginApiName: 'attribute_pricebooklines',
                    objectApiName: 'AttributePriceBookObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/attribute_pricebooklines',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AttributePriceBookLinesObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },
        CouponInstanceObj: {
            domains: [
                {
                    pluginApiName: 'couponinstanceobj_couponplan',
                    objectApiName: 'CouponInstanceObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/couponinstanceobj_couponplan',
                    params: {
                        fieldMapping: {},
                    }
                },
            ]
        },
        AccountsReceivableNoteObj: {
            domains: [
                {
                    pluginApiName: 'accountsreceivabledetailobj',
                    objectApiName: 'AccountsReceivableNoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/accountsreceivabledetailobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AccountsReceivableDetailObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },

        MatchNoteObj: {
            domains: [
                {
                    pluginApiName: 'matchnotedetailobj',
                    objectApiName: 'MatchNoteObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/matchnotedetailobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'MatchNoteDetailObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },

        SalesInvoiceObj: {
            domains: [
                {
                    pluginApiName: 'salesinvoicedetailobj',
                    objectApiName: 'SalesInvoiceObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salesinvoicedetailobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SalesInvoiceDetailObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },

        FAccountAuthorizationObj: {
            domains: [
                {
                    pluginApiName: 'authorizationdetailobj',
                    objectApiName: 'FAccountAuthorizationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/authorizationdetailobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AuthorizationDetailObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
                {
                    pluginApiName: 'unfreezeauthdetailobj',
                    objectApiName: 'FAccountAuthorizationObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/unfreezeauthdetailobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'UnfreezeAuthDetailObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },

        PaymentObj: {
            domains: [
                {
                    pluginApiName: 'orderpaymentobj',
                    objectApiName: 'PaymentObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/orderpaymentobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'OrderPaymentObj',
                                fieldMapping: {}
                            },
                        ],
                    },
                    load(config) {
                        return config.is_open_order_payment_multi_source != '1';
                    }
                },
                {
                    pluginApiName: 'detail_multi_source',
                    objectApiName: 'PaymentObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/detail_multi_source',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'OrderPaymentObj',
                                fieldMapping: {}
                            },
                        ],
                        customParam: {
                            retainMDSysBtns: ['Import_Excel'],
                            ruleConfigKey: 'order_payment_mapping_rule',
                        }
                    },
                    load(config) {
                        return config.is_open_order_payment_multi_source == '1';
                    }
                },
                {
                    pluginApiName: 'payment-pay',
                    objectApiName: 'PaymentObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/payment-pay',
                    params: {
                        fieldMapping: {},
                    },
                    load() {
                        return CRM.util.isGrayScale('CRM_PAYMENT_V2');
                    }
                },
                // {
                //     pluginApiName: 'manualmatchworkpage',
                //     objectApiName: 'PaymentObj',
                //     fieldApiName: '',
                //     resource: 'vcrm/plugin/manualmatchworkpage',
                //     params: {
                //         fieldMapping: {},
                //     },
                // },
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'PaymentObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'OrderPaymentObj',
                                fieldMapping: {}
                            }
                        ],
                        customParam: {
                            detailFilterObjectApiNames: ['OrderPaymentObj.order_id'],
                        }
                    },
                    load: multiLevelOrderSFAInnerPluginLoad
                }
            ]
        },

        SettlementObj: {
            domains: [
                {
                    pluginApiName: 'detail_multi_source',
                    objectApiName: 'SettlementObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/detail_multi_source',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'SettlementDetailObj',
                                fieldMapping: {}
                            },
                        ],
                        customParam: {
                            retainMDSysBtns: ['Single_Add', 'Import_Excel'],
                            ruleConfigKey: 'settlement_detail_mapping_rule',
                        }
                    }
                },
            ]
        },

        ElectronicSignObj: {
            domains: [
                {
                    pluginApiName: 'electronicsignobj',
                    objectApiName: 'ElectronicSignObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/electronicsignobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'ElectronicSignerObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },

        TransactionStatementObj: {
            domains: [
                {
                    pluginApiName: 'transactionstatementobj',
                    objectApiName: 'TransactionStatementObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/transactionstatementobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'ReconciliationDetail1__c',
                                fieldMapping: {}
                            },
                            {
                                objectApiName: 'ReconciliationDetail2__c',
                                fieldMapping: {}
                            },
                            {
                                objectApiName: 'ReconciliationDetail3__c',
                                fieldMapping: {}
                            },
                            {
                                objectApiName: 'ReconciliationDetail4__c',
                                fieldMapping: {}
                            },
                            {
                                objectApiName: 'ReconciliationDetail5__c',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },

        BomCoreObj: {
            domains: [
                {
                    pluginApiName: 'bomcore_md',
                    objectApiName: 'BomCoreObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/bomcore_md',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'BOMObj',
                                fieldMapping: {}
                            },
                        ],
                    },
                    load: function (config) {
                        return true;
                    }
                },
            ],
        },

        CreditOccupiedRuleObj: {
            domains: [
                {
                    pluginApiName: 'creditoccupiedruledetailobj',
                    objectApiName: 'CreditOccupiedRuleObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/creditoccupiedruledetailobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'CreditOccupiedRuleDetailObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ]
        },

        CustomerCreditAuthObj: {
            domains: [
                {
                    pluginApiName: 'customercreditauthobj',
                    objectApiName: 'CustomerCreditAuthObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/customercreditauthobj',
                    params: {}
                },
            ]
        },
        NewAdvertisementObj: {
            domains: [
                {
                    pluginApiName: 'dht_newadvertisementobj',
                    objectApiName: 'NewAdvertisementObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/dht_newadvertisementobj',
                    params: {}
                },
            ]
        },

        IncentivePolicyObj: {
            domains: [
                {
                    pluginApiName: 'incentivepolicyobj',
                    objectApiName: 'IncentivePolicyObj',
                    resource: 'vcrm/plugin/incentivepolicyobj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'IncentivePolicyStoreObj',
                                fieldMapping: {}
                            },
                        ],
                    }
                },
            ],
        },
        OnlineStoreObj: {
          domains: [
            {
              pluginApiName: 'OnlineStoreObj-form',
              objectApiName: 'OnlineStoreObj',
              resource: 'vcrm/plugin/onlinestoreobj-form',
              params: {}
            },
          ],
        },
        AccountAddrObj: {
            domains: [
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'AccountAddrObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                        customParam: {
                            masterFilterFields: ['contact_id']
                        }
                    },
                    load: multiLevelOrderSFAInnerPluginLoad
                }
            ]
        },
        RefundObj: {
            domains: [
                {
                    pluginApiName: 'multi_level_order_sfa_inner',
                    objectApiName: 'RefundObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/multi_level_order_sfa_inner',
                    params: {
                        fieldMapping: {},
                        customParam: {
                            masterFilterFields: ['order_id']
                        }
                    },
                    load: multiLevelOrderSFAInnerPluginLoad
                }
            ]
        },
        ActiveRecordObj: {
            domains: [
                {
                    pluginApiName: 'activerecordobj_interactive_records',
                    objectApiName: 'ActiveRecordObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/activerecordobj_interactive_records',
                    params: {},
                }
            ]
        },
        ProductObj: {
          domains: [
            {
              pluginApiName: 'ProductObj-form',
              objectApiName: 'ProductObj',
              fieldApiName: '',
              resource: 'vcrm/plugin/product-form',
              params: {
                fieldMapping: {},
                customParam: {}
              },
            }
          ]
        },
        SPUObj: {
          domains: [
            {
              pluginApiName: 'SPUObj-form',
              objectApiName: 'SPUObj',
              fieldApiName: '',
              resource: 'vcrm/plugin/spu-form',
              params: {
                fieldMapping: {},
                customParam: {}
              },
            }
          ]
        },
        ShopCategoryObj: {
            domains: [
              {
                pluginApiName: 'ShopCategoryObj-form',
                objectApiName: 'ShopCategoryObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/shopcategoryobj-form',
                params: {
                  fieldMapping: {},
                  customParam: {}
                },
              }
            ]
          },
          AdvancedFormulaObj: {
            domains: [
                {
                    pluginApiName: 'advanced_formula_obj',
                    objectApiName: 'AdvancedFormulaObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/advanced_formula_obj',
                    params: {
                        fieldMapping: {},
                        details: [
                            {
                                objectApiName: 'AdvancedFormulaLineObj',
                                fieldMapping: {}
                            },
                        ],
                    },
                    load: function (config, plugin) {
                        return true;
                    }
                }
            ]
        },
        // 工资绩效指标
        SalaryKPIObj: {
            domains: [
                {
                    pluginApiName: 'salary-kpi-metric',
                    objectApiName: 'SalaryKPIObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salary-kpi-metric',
                }
            ]
        },
        SalaryItemObj: {
            domains: [
                {
                    pluginApiName: 'salary-item',
                    objectApiName: 'SalaryItemObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salary-item',
                }
            ]
        },
        SalaryPaymentSlipObj: {
            domains: [
                {
                    pluginApiName: 'salary-payment-slip',
                    objectApiName: 'SalaryPaymentSlipObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/salary-payment-slip',
                }
            ]
        },
        EmployeeFixedSalaryObj: {
            domains: [
                {
                    pluginApiName: 'employee-fixed-salary',
                    objectApiName: 'EmployeeFixedSalaryObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/employee-fixed-salary',
                }
            ]
        }
    };

    return result
}
