/*
 * @Descripttion:
 * @Author: LiAng
 * @Date: 2023-11-17 21:25:38
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-08-11 10:49:44
 */

/**
 * 字段说明
 * https://wiki.firstshare.cn/pages/viewpage.action?pageId=162503203#id-1.%E4%B8%9A%E5%8A%A1%E6%8F%92%E4%BB%B6%E6%8A%80%E6%9C%AF%E6%96%B9%E6%A1%88-%E6%9F%A5%E8%AF%A2%E5%AF%B9%E8%B1%A1%E6%8F%92%E4%BB%B6%E6%8E%A5%E5%8F%A3(PluginList)
 *
 * 特殊字段说明
 * load:https://wiki.firstshare.cn/pages/viewpage.action?pageId=336495953#id-%E4%B8%9A%E5%8A%A1%E6%8F%92%E4%BB%B6%E6%96%87%E6%A1%A3-1.%E6%8F%92%E4%BB%B6%E4%BB%8E%E5%93%AA%E6%9D%A5
 */
export default function (opts) {
    const result = {
        SaleContractObj: {
            domains: [{
                pluginApiName: 'salesorderobj_detail_quick_ar',
                objectApiName: 'SaleContractObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/salesorderobj_detail_quick_ar',
                params: {}
            }]
        },
        SalesOrderObj: {
            domains: [{
                pluginApiName: 'salesorderobj_detail_quick_ar',
                objectApiName: 'SalesOrderObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/salesorderobj_detail_quick_ar',
                params: {}
            }]
        },
        PaymentObj: {
            domains: [{
                pluginApiName: 'detailmanualmatch',
                objectApiName: 'PaymentObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/detailmanualmatch',
                params: {}
            }]
        },
        // 信用占用规则明细
        CreditOccupiedRuleDetailObj: {
            domains: [{
                pluginApiName: 'plugin-creditoccupiedruledetailobj-detail',
                objectApiName: 'CreditOccupiedRuleDetailObj',
                fieldApiName: '',
                resource: 'vcrm/plugin/creditoccupiedruledetailobj-detail',
                params: {}
            }]
        },
        NewAdvertisementObj: {
            domains: [
                {
                    pluginApiName: 'dht_detail_newadvertisementobj',
                    objectApiName: 'NewAdvertisementObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/dht_detail_newadvertisementobj',
                    params: {}
                },
            ]
        },
        // AccountObj: {
        //     domains: [{
        //         pluginApiName: 'sfa_ai',
        //         objectApiName: 'AccountObj',
        //         fieldApiName: '',
        //         resource: 'vcrm/plugin/sfa_ai',
        //         params: {}
        //     }]
        // },
        PriceBookObj: {
            domains: [
                {
                    pluginApiName: 'distribution-detail-pricebookobj',
                    objectApiName: 'PriceBookObj',
                    fieldApiName: '',
                    resource: 'vcrm/plugin/distribution-detail-pricebookobj',
                    params: {},
                    load: function (config, plugin) {
                        return config.dht_multi_level_order === '1';
                    },
                },
            ]
        },
        OnlineStoreObj: {
          domains: [
            {
              pluginApiName: 'OnlineStoreObj_detail_plugin',
              objectApiName: 'OnlineStoreObj',
              resource: 'vcrm/plugin/onlinestoreobj-detail',
              fieldApiName: '',
              params: {}
            },
          ]
        },
        // ProjectObj: {
        //     domains: [
        //         {
        //             pluginApiName: 'detail-ProjectObj',
        //             objectApiName: 'ProjectObj',
        //             fieldApiName: '',
        //             resource: 'vcrm/plugin/detail-ProjectObj',
        //             params: {}
        //         },
        //     ]
        // },
        // 测试代码
        // 'object_4O885__c': {
        //     domains: [{
        //         pluginApiName: 'test',
        //         objectApiName: 'object_4O885__c',
        //         fieldApiName: '',
        //         resource: function(){
        //             return window.PAAS.plugin?.libs.get('TestPlugin')
        //         },
        //         params: {}
        //     }],
        //     'FSAID_PaaS_861247244485': {
        //         domains: [{
        //             pluginApiName: 'test',
        //             objectApiName: 'object_4O885__c',
        //             fieldApiName: '',
        //             resource: function(){
        //                 return window.PAAS.plugin?.libs.get('TestPlugin')
        //             },
        //             params: {}
        //         }],
        //     }
        // },
        // ActiveRecordObj: {
        //     domains: [
        //         {
        //             pluginApiName: 'active_record_base',
        //             objectApiName: 'ActiveRecordObj',
        //             fieldApiName: '',
        //             resource: 'vcrm/plugin/active_record_base',
        //             params: {},
        //             load: function (config, plugin) {
        //                 return CRM.util.isGrayScale('PAAS_OBJECT_ACTIVERECORDOBJ_LAYOUT');
        //             },
        //         },
        //     ]
        // },
    };

    return result;
}
