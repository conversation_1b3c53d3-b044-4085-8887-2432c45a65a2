export const configStock = {
  DeviceMeterReadingsObj: {
      domains: [
        {
          pluginApiName: "stock-detail-devicemeterreadingsobj",
          objectApiName: "DeviceMeterReadingsObj",
          fieldApiName: "",
          resource: "vcrm/plugin/stock-detail-devicemeterreadingsobj",
          params: {
              fieldMapping: {},
          },
        }
      ],
      fields: [],
      hooks: []
  },

  DeviceMeterObj: {
    domains: [
      {
        pluginApiName: "stock-detail-devicemeterobj",
        objectApiName: "DeviceMeterObj",
        fieldApiName: "",
        resource: "vcrm/plugin/stock-detail-devicemeterobj",
        params: {
          fieldMapping: {},
        },
      }
    ],
    fields: [],
    hooks: []
  },
}
