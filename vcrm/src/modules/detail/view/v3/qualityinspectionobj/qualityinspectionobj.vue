<!--
 * @Descripttion: 
 * @Author: LiAng
 * @Date: 2024-12-27 11:00:05
 * @LastEditors: LiAng
 * @LastEditTime: 2025-03-18 22:25:24
-->
<template>
  <faci-detail
      :apiName="apiName"
      :dataId="dataId"
      :hooks="dHooks"
      :isSlide="isSlide"
      :dataIds="dataIds"
      :top="top"
      :postData="postData"
      :extendData="extendData"
      :source="source"
      class="faci-detail_preset faci-detail_preset_qualityInspectionobj"
  >
      <template
          v-for="compApiName in dExtendComps"
          v-slot:[compApiName]="slotProps"
      >
          <component
              :is="compApiName"
              :key="compApiName"
              v-bind="{...slotProps, extendData}"
          ></component>
      </template>
  </faci-detail>
</template>
<script>
import Base from "../base/base";
import components from "./components";

export default {
  extends: Base,
  components: {
      ...components,
  },
  props: ["apiName", "dataId", "dataIds", "isSlide", "dHooks", "entry", "extendData"],
  data() {
      return {
          customComps: Object.keys(components),
          customHooks: {}
      };
  },
  computed: {
  },
  methods: {
  },
  mounted () {
  },
  beforeDestroy () {
  }
};
</script>

<style lang="less" scoped>
.faci-detail_preset_activerecordobj {
  ::v-deep .fx-frame-layout-grid .grid-cell {
      border-right: none;
  }
}
</style>