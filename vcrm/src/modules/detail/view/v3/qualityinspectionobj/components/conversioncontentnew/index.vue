<template>
  <div class="conversion-content-new-wrapper" ref="conversionContentNewWrapper">
    <message :data="messageData" :scrollIntoDataId="scrollIntoDataId" @scrollHandle="scrollHandle" />
  </div>
</template>

<script>
  import Message from '@components/sessionChatNew/message';

  export default {
    name: 'ConversionContentNew',
    props: {},
    data() {
      return {  
        messageData: [],
        scrollIntoDataId: '',
        isShow: true
      }
    },
    methods: {
      /**
       * @description: 滚动加载消息
       * @param {string} pos 滚动方向, top | bottom
       * @return {*}
       */
      scrollHandle: _.debounce(async function (pos) {
        const _this = this;
        CRM.util.waiting(true);
        try {
          const newItems = await _this.fetchMessages(pos);
          if (pos === 'bottom') {
            _this.messageData.push(...newItems);
          } else {
            _this.messageData.unshift(...newItems);
          }
        } catch (e) {
          //
        } finally {
          CRM.util.waiting(false);
        }
      }, 150),
      /**
       * @description: 获取消息列表
       * @return {*}
       */
      async getMsgDataHandler () {
        const _this = this;
        const objectData = _this.$context.getData();

        CRM.util.waiting(true);

        try {
          // 初始化加载
          await new Promise((resolve, reject) => {
            CRM.util.FHHApi({
              url: "/EM1HNCRM/API/v1/object/qi_enterprise_wechat_session/service/get_conversion_by_positioning",
              data: {
                sessionId: objectData.session_id,
                asc: true,
                messageTime: objectData.trigger_time,
                limit: 20
              },
              success: function (res) {
                console.log(res, 'getMsgDataHandler')
                if (res.Result.StatusCode === 0) {
                  _this.messageData = _this.formatMsgItem(res.Value.dataList, res.Value.userMap);
                  resolve();
                } else {
                  CRM.util.alert(res.Result.FailureMessage);
                  reject(new Error(res.Result.FailureMessage));
                }
              },
              error: reject
            },
            {
              errorAlertModel: 1,
            });
          });

          // 设置最大尝试次数，防止无限循环
          const MAX_FETCH_ATTEMPTS = 10;
          // 如果初始加载的消息不足10条，则继续向上查找历史消息
          for (let i = 0; i < MAX_FETCH_ATTEMPTS; i++) {
            // 如果消息数量已达到10条或以上，则终止循环
            if (this.messageData.length >= 10) {
              break;
            }
            const newItems = await _this.fetchMessages('top');
            if (newItems.length > 0) {
              _this.messageData.unshift(...newItems);
            } else {
              // 没有更多历史消息，跳出循环
              break;
            }
          }
        } catch (error) {
          // 
        } finally {
          CRM.util.waiting(false);
        }
      },
      /**
       * @description: 根据位置(pos)获取消息列表
       * @param {string} pos 滚动方向, top | bottom
       * @return {Promise}
       */
      fetchMessages(pos) {
        const _this = this;
        const objectData = _this.$context.getData();
        const firstItem = _this.messageData[0];
        const lastItem = _this.messageData[_this.messageData.length - 1];

        if ((pos !== 'bottom' && !firstItem) || (pos === 'bottom' && !lastItem)) {
          return Promise.resolve([]);
        }
        
        return new Promise((resolve, reject) => {
          CRM.util.FHHApi({
            url: "/EM1HNCRM/API/v1/object/qi_enterprise_wechat_session/service/find_conversion_by_page",
            data: {
              sessionId: objectData.session_id,
              asc: true,
              messageTime: pos === 'bottom' ? lastItem?.messageTime : firstItem?.messageTime,
              limit: 20,
              // 表示查询大于当前时间的消息还是小于, false 表示小于
              greaterThan: pos === 'bottom' ? true : false
            },
            success: function (res) { 
              if (res.Result.StatusCode === 0) {
                const newItems = _this.formatMsgItem(res.Value.dataList, res.Value.userMap);
                resolve(newItems);
              } else {
                CRM.util.alert(res.Result.FailureMessage);
                reject(new Error(res.Result.FailureMessage));
              }
            },
            error: reject
          },
          {
            errorAlertModel: 1,
          });
        });
      },
      formatMsgItem (msgList, userMap) {
        const _this = this;
        const objectData = _this.$context.getData();
        if (!msgList?.length) return [];
        return msgList.map(item => {
          // 拼接质检关键字，用于高亮显示
          if (!!objectData?.dirty_words) {
            // return Object.assign({}, item, {
            //   highlight_words: objectData.dirty_words.split(',')
            // })
            item.highlight_words = objectData.dirty_words.split(',');
          }
          if (item?.messageTime) {
            item.createTime = FS.qxUtil.getDateSummaryDesc(CRM.util.moment.unix(parseInt(item.messageTime, 10),true), 1) || '';
          }
          if (userMap[item?.fromUser]) {
            item.fromUserType = `${userMap[item?.fromUser]?.type}`;
            item.fromUserType === '1' && (item.isMine = true);
          }
          // 如果是群聊且外部联系人，则需要处理open-id
          if (objectData?.session_type === '2' && item.fromUserType === '2') {
            item.fromUser = `${item.roomId}/${item.fromUser}`
          }
          return item;
        });
      }
    },
    components: {
      Message
    },
    mounted() {
      this.getMsgDataHandler();
    }
  }
</script>

<style lang="less" scoped>
.conversion-content-new-wrapper {
  width: auto;
  max-width: 1070px;
  height: 600px;
  display: flex;
  flex-direction: column;
  position: relative;
}
</style>