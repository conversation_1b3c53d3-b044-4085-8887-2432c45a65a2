<script>
    import OrderCloseComp from "@components/orderclosecomp";
    import FreezeInventoryAdjustmentComp from "@components/FreezeInventoryAdjustmentComp";

    let util = CRM.util;

    export default {
        props: ['compInfo', 'apiName', 'dataId'],
        data() {
            return {
                customHooks: {
                    beforeInitData: this.beforeInitData,
                    beforeActionEdit: this.beforeActionEditAsync,
                },
            }
        },
        methods: {
            beforeInitData(data) {
                const buttons = data.compInfo.buttons.filter(btn=>btn.api_name!=='Edit_button_kx__c');
                data.compInfo.buttons = buttons;
            },

            beforeActionEditAsync(next, data) {
                const isDHT = window.Fx && Fx.IS_DHT_CONNECTAPP;
                if (isDHT) {
                    data.__type = 'edit';
                    window.$dht.createOrder({
                        ...data,
                        success: () => {
                            this.$context.emit('root.refresh', {
                                isRefreshList: true,
                                action: 'edit'
                            })
                        }
                    });
                    return;
                };
                next(data);
            },

            // 收款
            handleCollect: function() {
                var me = this;
                var order = me.$context.getData();
                console.log(this.compInfo);

                var params = {
                    apiname: "PaymentObj",
                    displayName: $t("收款"),
                    showDetail: false,
                    isTriggerPay: true,
                    data: {
                        account_id: order.account_id,
                        account_id__r: order.account_id__r,
                        order_id: order._id,
                        order_id__r: order.name
                    },
                    nonEditable: true,
                    success: function() {
                        me.$context.emit('root.refresh')
                    }
                }

                // 灰度控制：点击订单[收款]按钮创建回款时，默认生成一条回款明细
                if (CRM.util.isGrayScale('CRM_ADD_DEFAULT_ORDERPAYMENT')) {
                    _.extend(params.data, {
                        order_data_id: order._id,
                        from_salesorder_by_collectbtn: true,
                        order_detail_data: order,
                    });
                }

                CRM.api.add(params);
                util.uploadLog("order", "detail", {
                    eventId: "payment",
                    eventType: "cl"
                });
            },

            // 冻结库存调整按钮
            handleFreezeInventoryAdjustment() {
                const orderId = this?.data?._id;
                new FreezeInventoryAdjustmentComp(orderId);
            },

            // 创建发货单
            handleAddDeliveryNote: async function() {
                var me = this;
                var order = me.$context.getData();
                const map = {};

                const isOpenTransferoOrderRes = await util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/crm_config/service/get_config_values',
                    data: {
                        isAllConfig: false,
                        keys: ['mlo_transfer_order']
                    }
                });

                if( isOpenTransferoOrderRes.Result.StatusCode === 0) {
                    const list = isOpenTransferoOrderRes.Value.values || []
                    _.each(list, a => {
                        map[a.key] = a.value;
                    });
                }

                if(map['mlo_transfer_order'] === '1') {
                    // 判断是源单还是转单（虚拟发货场景）
                    const res = await util.FHHApi({
                        url: '/EM1HDHT/API/v1/object/multi_level/service/check_action',
                        data: {
                            objectApiName: order.object_describe_api_name,
                            dataId: order._id,
                            action: 'VirtualDelivery',
                        }
                    });
                    if (res.Result.StatusCode !== 0) return;

                    let { orderType, transferOrderDeliveryNote } = res.Value;

                    // 源单
                    if (orderType === "OriginalOrder") {
                        // 是否创建发货单
                        // 有仓库订货 or 无仓库订货
                        // 有仓库订货：是否已入库
                        if (transferOrderDeliveryNote) {
                            if (order.shipping_warehouse_id && transferOrderDeliveryNote.virtual_stock_in_status !== '2') {
                                // 有仓库订货，且未入库，不能创建发货单
                                util.alert(`<div>${$t("transferOrderDeliveryNote.alert_message_left")}<a href="javascript:void(0)" onclick="(function(){
                                        const url = '${origin}/XV/Cross/Portal?fs_out_appid=${window.Portal.appId}#/portal/depend/dht-api-DeliveryNoteObj?transfer_order_delivery_note_id=${transferOrderDeliveryNote._id}';
                                        window.open(url, '_blank');
                                    })();" style="color: #409eff; text-decoration: none;">${$t("transferOrderDeliveryNote.alert_message_middle")}${transferOrderDeliveryNote.name}</a>${$t("transferOrderDeliveryNote.alert_message_right")}</div>`,
                                    null,
                                    {
                                        dangerouslyUseHTMLString: true,
                                    }
                                );
                            } else {
                                CRM.api.add({
                                    apiname: "DeliveryNoteObj",
                                    displayName: $t("transferOrderDeliveryNote.display_name"), // 发货单
                                    nonEditable: true,
                                    data: {
                                        sales_order_id: me.dataId,
                                        sales_order_id__r: order.name,
                                        __addFormSalesOrder: true,
                                        transferOrderDeliveryNote: transferOrderDeliveryNote
                                    },
                                    showDetail: true,
                                    success: function () {
                                        me.$context.emit('root.refresh')
                                    }
                                });
                            }
                        }

                        return;
                    }
                }


                //待确认的订单不能创建发货单
                if (order.order_status == 6) {
                    util.alert($t("订单尚未确认不能创建发货单"));
                    return;
                }

                //已发货和已收货时不能创建发货单
                if (_.contains([3, 5], order.logistics_status)) {
                    util.alert($t("订单已发货请查看发货记录"));
                    return;
                }

                CRM.api.add({
                    apiname: "DeliveryNoteObj",
                    displayName: $t("发货单"),
                    nonEditable: true,
                    data: {
                        sales_order_id: me.dataId,
                        sales_order_id__r: order.name,
                        __addFormSalesOrder: true
                    },
                    showDetail: true,
                    success: function() {
                        me.$context.emit('root.refresh')
                    }
                });
            },

            handleAbolish: function() {
                var me = this;
                var order = me.$context.getData();

                var data = [
                    {
                        // object_describe_id: this.get("apiId"),
                        object_describe_api_name: this.apiName,
                        _id: this.dataId,
                        tenant_id: order.tenant_id
                    }
                ];

                CRM.api.abolish({
                    apiname: this.apiName,
                    dataList: data,
                    success: function() {
                        // me.trigger("refresh", "list", "abolish");
                        me.$context.emit('root.refresh', {
                            isRefreshList: true
                        });
                        me.$context.emit('root.hide');
                        // me.hide();
                    }
                });
            },

            // 确认收货
            handleConfirmReceipt: function() {
                var me = this;
                CRM.api.confirm_receipt({
                    id: me.dataId,
                    success: function() {
                        me.$context.emit('root.refresh')
                    }
                });
            },

            // 确认发货
            handleConfirmDelivery: function() {
                var me = this;
                CRM.api.confirm_delivery({
                    id: me.dataId,
                    success: function() {
                        me.$context.emit('root.refresh')
                    }
                });
            },

            // 立即付款
            handlePayInstantly: function() {
                var me = this;
                var objectData = this.$context.getData();
                CRM.api.PayInstantly_button_default({
                    displayName: $t('回款'),
                    nonEditable: true,
                    SettleType: '1',
                    data: objectData,
                    success: function() {
                        me.$context.emit('root.refresh');
                    }
                });
            },

            // 再次下单
            handleBuyAgain: function() {
                var me = this;
                CRM.api.BuyAgain_button_default({
                    dataId: me.dataId,
                    apiname: 'SalesOrderObj',
                    success: function() {
                        me.$context.emit('root.refresh');
                    }
                })
            },

            // 确认收货2
            handleConfirmReceipt2: function() {
                var me = this;
                var objectData = this.$context.getData();
                CRM.api.ConfirmReceipt2_button_default({
                    dataId: this.dataId,
                    data: objectData,
                    success: function() {
                        me.$context.emit('root.refresh');
                    }
                })
            },

            // 一键转单
            handleTransferOrder: function() {
                const me = this;
                const data = me.$context.getData() || {};
                debugger

                CRM.api.TransferOrder_button_default({
                    dataId: this.dataId,
                    apiname: data.object_describe_api_name,
                    data: data,
                    success: function() {}
                });
            },

            dhtUtil: function() {
                return window.$dht;
            },

            // 付款
            handleDhtPay__c: function(btn) {
                var me = this;
                if(!me.dhtUtil) return
                var orderData = this.$context.getData();
                me.dhtUtil.handleDhtPay__c({
                    btnOpts: btn,
                    data: orderData,
                    success() {
                        me.$context.emit('root.refresh', {
                            isRefreshList: true
                        })
                    },
                });
            },

            // 取消订单
            handleDhtCancel__c: function(btn) {
                var me = this;
                if(!me.dhtUtil) return
                var orderData = this.$context.getData();
                me.dhtUtil.handleDhtCancel__c({
                    btnOpts: btn,
                    data: orderData,
                    success() {
                        me.$context.emit('root.refresh', {
                            isRefreshList: true
                        })
                    },
                });
            },

            // 再次购买
            handleDhtBuyAgain__c: function(btn) {
                var me = this;
                if(!me.dhtUtil) return
                var orderData = this.$context.getData();
                me.dhtUtil.handleDhtBuyAgain__c({
                    btnOpts: btn,
                    data: orderData,
                    success() {
                        me.$context.emit('root.refresh', {
                            isRefreshList: true
                        })
                    },
                });
            },

            /**
             * @desc 指定配送员
             */
            handledesignated_distributor__c: function() {
                this.designatedDeliveryPerson('designated_distributor__c')
            },
            /**
             * @desc 更改配送员
             */
            handlechange_distributor__c: function() {
                this.designatedDeliveryPerson('change_distributor__c')
            },
            designatedDeliveryPerson: function(button_api_name) {
                var me = this;
                let data = me.$context.getData() || {};
                let { _id, delivery_date, deliveryman, account_id,delivery_personnel } = data;
                util.waiting();
                FS.MEDIATOR.trigger("appCheckin.getModule", {
                    moduleInfo: [{
                        url: 'app-checkin-modules/es6/set-deliveryMan-dialog/index.js',
                        isEs6: true
                    }],
                    complete: function complete(Comp) {
                        var dialog = new Comp({
                            zIndex: 1000,
                            data: {
                                orderList: [{
                                    _id, account_id,delivery_date,delivery_personnel
                                }],
                                dateOfDelivery: delivery_date,
                                deliveryman,
                                "deliveryType": "SalesOrderObj",
                                button_api_name
                            },
                            saveSuccess: function saveSuccess() {
                                    me.$context.emit('root.refresh')
                            }
                        });
                        dialog.show();
                        util.waiting(false);
                    },
                    isNeedCheckAdmin: false
                });
            },

            /**
             * 余额扣减
             */
            handleBalanceReduce:function(btn){
                let me = this;
                let data = me.$context.getData() || {};
                CRM.api.BalanceReduce_button_default({data:data});
            },

            /**
             * 订单关闭
             */
            handleCloseSalesOrder:function(){
                let str ="";
                const data = this.$context.getData() || {};
                const rebateAmount = parseFloat(data?.misc_content?.rebate_amount) || 0;
                const couponAmount = parseFloat(data?.misc_content?.coupon_amount) || 0;

                if(rebateAmount>0){
                    str= $t("crm.closeOrder.useRebate");
                }else if(couponAmount>0){
                    str= $t("crm.closeOrder.useCoupon");
                }else if(data.close_status){
                    str=$t("crm.closeOrder.close");
                }
                if(str){
                    CRM.util.alert(str);
                    return;
                }
                if (!this.closeProductComp) {
                    this.closeProductComp = new OrderCloseComp({
                        orderId: this.dataId,
                        confirmCallback: this.closeOrderCb.bind(this)
                    });
                }
                this.closeProductComp.setInfo("master");
            },

            closeOrderCb:function(){
                this.$context.emit('root.refresh')
            }

            // /**
            //  *@desc 更换确认人 - 销售订单/退货单
            //  */
            // handleChangeConfirmor: function() {
            //     var me = this;
            //     var oData = me.$context.getData();
            //
            //     CRM.api.change_confirmer({
            //         apiname: me.apiName,
            //         id: me.dataId,
            //         work_flow_id: oData.work_flow_id || '',
            //         oData:oData,
            //         success: function() {
            //             // me.trigger('refresh');
            //             // me.refresh();
            //             me.$context.emit('root.refresh')
            //
            //         }
            //     })
            // },

        }
    }
</script>
