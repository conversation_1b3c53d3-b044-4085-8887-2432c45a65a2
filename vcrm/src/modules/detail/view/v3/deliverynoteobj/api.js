/**
 * @desc 创建销售出库单
 * @param {Object} options
 * @param {string} options.id
 * @returns {Promise<Object>}
 */
export function createSalesOutboundNote(options) {
  const params = {
    objectDataId: options.dataId,
  };

  return new Promise((resolve, reject) => {
    CRM.util.FHHApi(
      {
        url: '/EM1HNCRM/API/v1/object/delivery_note/service/create_sale_outbound',
        data: params,
        success: res => {
          if (res.Result.StatusCode === 0) {
            resolve(res.Value);
          } else {
            // 业务异常
            reject(res.Result.FailureMessage);
          }
        },
        error: err => {
          // 系统异常
          reject(err);
        },
      },
      {
        errorAlertModel: 1,
      }
    );
  });
}
