<script>
    let util = CRM.util;
    import Api from '@common/api'

    export default {
        props: ['compInfo', 'apiName', 'dataId'],
        data() {
            return {
                visible: false,
                isSubmitting: false,
                submitTimer: null,
                objectDescribe: {},
            }
        },
        mounted() {
            const _this = this, detailData = _this.$context.getData();
            Api.findDescribeByApiName({ api_name: detailData.used_object_api_name }).then(({ objectDescribe }) => {
                console.log(objectDescribe, 'objectDescribe');
                _this.objectDescribe = objectDescribe;
            });
        },
        methods: {
            handleApplyExistAccounts: function() {
                let me = this;
                let detailData = me.$context.getData();
                console.log('detailData', detailData);
                me.visible = true;
                this.createTooltip(detailData)
            },
            createTooltip (detailData) {
                const _this = this
                var params = {
                    wrapper: $('body')[0],
                    template: `<fx-dialog
                        :title="$t('提示')"
                        append-to-body
                        custom-class="interaction-strategy-create-task-dialog"
                        :visible.sync="visible"
                        width="480px"
                        top="30vh"
                    >
                        <div class="content-text-box">
                            <div class="content-item-box content-item1">
                                <div class="content-item-title">{{$t('sfa.ai.interaction.stragey.create.task.content.title1')}}</div>
                                <div class="content-item-text">{{$t('sfa.ai.interaction.stragey.create.task.content.text1')}}</div>
                                <div class="content-item-text">{{$t('sfa.ai.interaction.stragey.create.task.content.text5')}}</div>
                            </div>
                            <div class="content-item-box content-item2">
                                <div class="content-item-title">{{$t('sfa.ai.interaction.stragey.create.task.content.title2')}}</div>
                                <div class="content-item-text"><span>{{$t('sfa.ai.interaction.stragey.create.task.content.text2')}}</span><span class="content-item-title">{{$t('sfa.ai.interaction.stragey.create.task.content.text4')}}</span></div>
                            </div>
                            <div class="content-item-box">
                                <div class="content-item-title">{{$t('sfa.ai.interaction.stragey.create.task.content.title3')}}</div>
                                <div class="content-item-title">{{$t('sfa.ai.interaction.stragey.create.task.content.text3',{objectName: objectName, lastFollowedTime: lastFollowedTime })}}</div>
                            </div>
                            <div class="content-item-tip">{{$t('sfa.ai.interaction.stragey.create.task.content.tip')}}</div>
                        </div>
                        <div slot="footer" class="dialog-footer">
                            <fx-button size="small" type="primary" @click="onConfirm($event,true)"
                                >{{$t('sfa.ai.interaction.stragey.create.task.button')}}</fx-button
                            >
                            <fx-button size="small" @click="onCancel($event,false)">{{$t('取消')}}</fx-button>
                        </div>
                    </fx-dialog>`,
                    data: function () {
                        return {
                            visible: _this.visible,
                            objectName: _this.objectDescribe.display_name_r || _this.objectDescribe.display_name,
                            lastFollowedTime: _this.objectDescribe.fields.last_followed_time.label_r || _this.objectDescribe.fields.last_followed_time.label
                        }
                    },
                    methods: {
                        onConfirm: function (e, isCustomer) {
                            _this.visible = this.visible = false;
                            _this.createStrategyTask(detailData)
                        },
                        onCancel: function (e, isCustomer) {
                            _this.visible = this.visible = false;
                        }
                    }
                }
                FxUI.create(params)
            },
            createStrategyTask(detailData) {
                if(detailData._id) {
                    let me = this;
                    if (this.isSubmitting) return;
                    this.isSubmitting = true;
                    this.submitTimer = setTimeout(() => {
                        this.isSubmitting = false;
                    }, 5000);
                    CRM.util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/interact_question/service/create_strategy_task',
                        data: {
                            "strategyId": detailData._id
                        },
                        success: function (res) {
                            console.log(res, 'res----create_strategy_task');
                            me.isSubmitting = false;
                            if (res.Result.StatusCode === 0) {
                                FxUI.Message({
                                    message: res.Value.message,
                                    type: 'success',
                                    offset: 100
                                })
                            } else {
                                FxUI.Message({
                                    message: res.Result.FailureMessage,
                                    type: 'warning',
                                    offset: 100
                                })
                            }

                        },
                        error(err) {
                            me.isSubmitting = false;
                        }
                    }, {
                        errorAlertModel: 1
                    });
                }
            }
        },
        beforeDestroy() {
            clearTimeout(this.submitTimer);
            this.submitTimer = null;
        }
    }
</script>
<style lang="less">
.interaction-strategy-create-task-dialog.fx-dialog {
    .el-dialog__header {
        // border-bottom: none;
    }
    .content-text-box {
        .content-item-box {
            color: var(--color-neutrals19);
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;
            .content-item-title {
                font-weight: 700;
            }

        }
        .content-item-tip{
            margin: 16px 0;
        }
        .content-item1,
        .content-item2 {
            margin-bottom: 8px;
        }
    }
    .el-dialog__footer {
        // border-top: none;
    }
}
</style>