/**
 * @desc: 互动策略
 * @author: wa<PERSON><PERSON><PERSON>
 * @date: 2025-05-14
 */

<script>
    import Base from '../base/base'
    import HeadInfo from './components/headinfo/index.js'


    export default {
        extends: Base,

        props:{
            apiName:String,
            dataId:String,
            'dataIds': Array,
        },

        data() {
            return {
                customComps: [
                    'HeadInfo',
                ],
            }
        },

        components: {
            HeadInfo,
        },

        methods: {


        }
    };
</script>

<style scoped>

</style>
