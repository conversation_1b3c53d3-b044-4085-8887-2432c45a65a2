<template>
    <faci-detail
        :apiName="apiName"
        :dataId="dataId"
        :isSlide="isSlide"
        :dataIds="dataIds"
    >

        <template v-slot:HeadInfo="slotProps">
            <head-info v-bind="slotProps"></head-info>
        </template>

        <!-- 服务进度 -->
        <template v-slot:eservice_repair_process="slotProps">
            <eservice-repair-process
                slot="eservice_repair_process"
                :compInfo="slotProps.compInfo"
                :apiName="slotProps.apiName"
                :dataId="slotProps.dataId"
            ></eservice-repair-process>
        </template>
    </faci-detail>
</template>
<script>
import HeadInfo from "./components/headinfo/index";
import EserviceRepairProcess from "./components/eserviceRepairProcess";

export default {
    name: "ServiceRequestObjDetail",
    props: ["apiName", "dataId", "dataIds", "isSlide"],
    _isPaasWraper_: true,
    components: {
        HeadInfo,
        EserviceRepairProcess,
    },
    data() {
        return {
            dHooks: {
                beforeInitDataAsync: this.beforeInitDataAsync,
            },
            woretunvisitData: {},
        };
    },
    methods: {
        async beforeParseAsync(next, originData) {
            const { layout, data } = originData,
                head_info = layout.components.find(
                    ({ api_name }) => api_name === "head_info"
                );
            next(originData);
        },
        beforeInitDataAsync(next, data) {
            next(data);
        },
    },
    mounted() {
        // console.log('bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb');
    },
};
</script>
