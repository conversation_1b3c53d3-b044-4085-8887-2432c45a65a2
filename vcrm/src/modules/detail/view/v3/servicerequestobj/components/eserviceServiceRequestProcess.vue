<template>
    <faci-widget-frame mode="wide mini" :mini-max-width="200">
        <div slot="wide">
            <div class="detail-service-request-process">
                <div class="crm-scroll crm-scroll-nobar">
                    <fx-steps
                        :active="activeIndex"
                        finish-status="myfinish"
                        align-center
                    >
                        <fx-step
                            v-for="(item, index) in stepList"
                            :key="index"
                            :title="item.name"
                            :icon="icons[item.status] || icons['wait']"
                        >
                        </fx-step>
                    </fx-steps>
                </div>
            </div>
        </div>
        <div slot="mini" class="wo-widget-dynamic_mini" @click="expandComp">
            <div class="wo-widget-dynamic_miniicon">
                <i class="p-unicode">&#xe83d;</i>
            </div>
        </div>
    </faci-widget-frame>
</template>

<script>
export default {
    props: ["compInfo", "apiName", "dataId"],

    data() {
        return {
            nameMaps: {},
            stepList: [],
            activeIndex: 0,
            icons: {
                pass: "fx-icon-ok", // 已通过
                in_progress: "fx-icon-time", // 正在进行中
                wait: "fx-icon-start", // 等待中
                no_pass: "fx-icon-minus-gray", // 不通过
                reject: "fx-icon-minus-gray", // 已驳回
                cancel: "fx-icon-minus-gray", // 已撤回
            },
        };
    },

    created() {
        this.initData();
    },

    methods: {
        expandComp() {
            // 展开组件
            this.$context.emit("root.layout", true);
            this.$context.emit("root.scrollTo", this.$el);
        },
        initData() {
            const detailData = this.$context.getData(); // 工单详情页的数据
            const stepList = detailData["eservice_service_request_process_data"] || [];

            const describe = this.$context.getDescribe();
            const service_request_status = describe.fields["service_request_status"];
            const opts = (service_request_status && service_request_status.options) || [];
            const nameMaps = {};
            _.each(opts, (item, index) => {
                nameMaps[item.value] = item;
            });
            this.nameMaps = nameMaps;
            const rt = [];

            let idx = 0;

            _.each(stepList, (item, index) => {
                // 已经删除的选项 不再显示
                if (!nameMaps[item.val]) {
                    return;
                }
                let obj = {
                    name: nameMaps[item.val].label, //取描述中的名称
                    status: item.status || "wait",
                };
                rt.push(obj);
                if (item.status == "in_progress") {
                    this.activeIndex = idx;
                }
                idx += 1;
            });
            this.stepList = rt;
        },
    },
    mounted() {
        console.log("eservice_service_request_process running");
    },
};
</script>

<style lang="less">
.detail-service-request-process {
    .is-myfinish {
        color: #67c23a;
    }
    .el-steps {
        margin-bottom: 20px;
    }
    .el-step.is-horizontal {
        min-width: 114px;
    }
    .el-step__head.is-myfinish {
        .el-step__line-inner {
            border-width: 1px !important;
            width: 100% !important;
        }
    }
    .el-step__icon.is-icon {
        width: 24px;
    }
    .el-step__icon-inner {
        font-size: 20px;
    }
    .el-step__title {
        color: var(--color-neutrals15) !important;
        font-size: 12px;
        line-height: inherit;
        margin: 10px 0;
    }
    .fx-icon-start:before {
        color: #dddbda;
    }
    .fx-icon-minus-gray:before {
        color: var(--color-danger05);
    }
    .crm-scroll-nobar {
        &::-webkit-scrollbar {
            height: 6px;
        }
    }
}
.wo-widget-dynamic_mini {
  cursor: pointer;
}
.wo-widget-dynamic_miniicon {
  height: 24px;
  text-align: center;
}
.wo-widget-dynamic_mininame {
  text-align: center;
  cursor: pointer;
  font-size: 12px;
}
</style>
