<template>
    <SfaNewAiChat
        agent-api-name="Copilot_n51K3__c"
        :sessionIdVariables="[this.apiName, this.dataId]"
        :variables="variables"
        :debug="true"
        from="obj_detail"
        welcome-type="WelcomeFirstDemo"
        @action="handleAction"
        @sendStatus="handleSendStatus"
    />
  </template>

<script>
import SfaNewAiChat from '@/modules/components/sfa_new_ai_chat/index.vue';
export default {
    props: ['compInfo', 'apiName', 'dataId'],
    components: {
        SfaNewAiChat,
    },
    data() {
        return {
        };
    },
    computed: {
        variables() {
            const data = this.$context.getData();
            const describe = this.$context.getDescribe();
            return {
                name: data.name,
                objectDataId: this.dataId,
                objectApiName: this.apiName,
                objectName: describe.display_name,
            }
        },
    },
    methods: {
        handleAction(error, ...results) {
            if (error) {
                console.error("动作执行失败:", error);
                return;
            }
            console.log("动作执行结果:", results);
        },
        handleSendStatus(event, data) {
            console.log("消息状态:", event, data);
        }
    }
};
</script>
