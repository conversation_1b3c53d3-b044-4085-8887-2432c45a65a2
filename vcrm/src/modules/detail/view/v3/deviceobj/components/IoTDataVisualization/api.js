// 根据设备id获取关联的仪表点对象
export function getDeviceMeterObjList(deviceId, offset = 0, limit = 20, isNeedKeyDeviceMeter = false, isNeedExplicitTotalNum = false) {
  const search_query_info = {
    limit,
    offset,
    filters: [
      {
        field_name: 'device_id',
        field_values: [deviceId],
        operator: 'EQ'
      }
    ],
    orders: []
  };

  if (isNeedKeyDeviceMeter) {
    search_query_info.filters.push({
      field_name: 'key_device_meter',
      field_values: [true],
      operator: 'EQ'
    })
  }

  const data = {
    extractExtendInfo: true,
    ignore_scene_record_type: false,
    search_query_info: JSON.stringify(search_query_info),
    object_describe_api_name: "DeviceMeterObj",
    include_describe: false,
    include_layout: false,
    need_tag: true,
    search_template_type: "default",
    pageSizeOption: [
      20,
      50,
      100
    ],
    serializeEmpty: false
  };

  if (isNeedExplicitTotalNum) {
    data.find_explicit_total_num = true; // 是否需要返回精确总条数
  }

  return new Promise((resolve, reject) => {
    CRM.util.FHHApi({
      url: '/EM1HNCRM/API/v1/object/DeviceMeterObj/controller/List',
      data,
      success: function (res) {
        if (res.Result.StatusCode === 0 && res.Value.dataList) {
          resolve({
            deviceMeterObjList: res.Value.dataList,
            total: res.Value.total
          });
        }
      }
    }, {
      errorAlertModel: 1
    });
  })
}

// 获取仪表点对象id关联的仪表点读数
export function getRelatedDeviceMeterReadingsList(deviceMeterIds) {
  return new Promise((resolve, reject) => {
    CRM.util.FHHApi({
      url: '/EM1HNCRM/API/v1//object/device_meter_graph/service/query_iot_card_info',
      data: {
        deviceMeterIds
      },
      success: function (res) {
        if (res.Result.StatusCode === 0 && res.Value.dataList) {
          resolve(res.Value.dataList);
        }
      }
    }, {
      errorAlertModel: 1
    });
  })
}