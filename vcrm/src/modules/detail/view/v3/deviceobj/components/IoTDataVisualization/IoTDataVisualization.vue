<template>
  <div id="iot-data-visualization-container" class="iot-data-visualization-container"
    ref="iotDataVisualizationContainerRef" :class="{ inactive: !isUnfold }" v-loading="isLoading">
    <header class="header" :class="{ inactive: !isUnfold }">
      <div class="left-box"><span :class="['fx-icon-unfold-2', { 'inactive': !isUnfold }]"
          @click="handleUnfold"></span>{{ this.compInfo.header }}</div>
      <div class="right-box" @click="handleRefresh">
        <span class="fx-icon-refresh"></span>
      </div>
    </header>
    <div class="content-container" v-show="displayDataList.length !== 0"
      :class="{ active: isUnfold, 'not-in-detail': !isInDetail }">
      <div class="content-body" :class="{ 'full-screen': isFullScreen }" ref="contentBodyRef">
        <fx-card shadow="never" class="card-box" v-for="item in displayDataList" :key="item.name">
          <div class="top-box">
            <div class="name">{{ item.name }}</div>
            <div class="time">
              {{ item.time }}
            </div>
          </div>
          <div class="bottom-box">
            <div v-show="item.value || item.eventMessage">
              <span class="value">{{ item.value || item.eventMessage }}</span>
              <span class="unit" v-show="item.unit && item.value">{{ item.unit }}</span>
              <span class="icon" v-show="item.icon && item.value">
                <img :src="item.icon" alt="icon">
              </span>
            </div>
            <div v-show="!item.value && !item.eventMessage">
              <span class="value">--</span>
            </div>
          </div>
        </fx-card>
      </div>
      <div class="content-footer" v-if="total > pageSize">
        <fx-pagination @current-change="handleCurrentChange" @click.native="handleClick"
          layout="total2, jumper2, prev2, next2" :page-size="pageSize" :total="total">
        </fx-pagination>
      </div>
    </div>
    <div v-if="displayDataList.length === 0" class="empty-box" id="empty-box">
      {{ $t('stock.deviceobj.vcrm.detail.device_life_circle_related_list.no_data') }}
    </div>
  </div>
</template>
<script>
import { getDeviceMeterObjList, getRelatedDeviceMeterReadingsList } from './api'
import { DATA_SOURCE } from './constant'

let resizeObserver;
export default {
  props: ["compInfo", "apiName", "dataId"],

  data() {
    return {
      isUnfold: true,
      isFullScreen: false,
      displayDataList: [],
      pageSize: 8, // 8 | 20
      total: 0,
      offset: 0,
      isLoading: false,
      isInDetail: false,
    };
  },
  computed: {
  },
  created() {

  },
  mounted() {
    // 监听窗口大小变化
    resizeObserver = new ResizeObserver(_.debounce(entries => {
      const { width } = entries[0].contentRect;
      this.handleFullScreen(width);
    }, 200));

    resizeObserver.observe(this.$refs.contentBodyRef);

    this.handleFullScreen(this.$refs.contentBodyRef.offsetWidth);

    setTimeout(() => {
      this.isInDetail = !!document.querySelector('.fx-od-form-component.fx-od-form-component--undefined');

      if (!this.isInDetail) {
        this.pageSize = 20;
      }

      this.fetchData(this.offset, this.pageSize);
    }, 100);
  },
  updated() {
    // fx-od-form-component fx-od-form-component--undefined
  },
  beforeDestroy() {
    resizeObserver.disconnect();
  },
  methods: {
    formatTime(timestamp) {
      if (!timestamp) return '';

      // 处理时间戳（支持毫秒和秒两种格式）
      const date = new Date(String(timestamp).length === 10 ? timestamp * 1000 : timestamp);

      // 格式化年月日
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      // 格式化时分秒
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      // 返回格式化后的时间字符串
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    processData(deviceMeterObjList, deviceMeterReadingList) {
      const idToDisplayDataMap = {};
      deviceMeterObjList.forEach(item => {
        idToDisplayDataMap[item._id] = {
          deviceMeterObjCreateTime: item.create_time,
          name: item.name,
          unit: item.measure_unit,
          _id: item._id
        }
      })

      const trendToIconMap = {
        up: this.getCdnUrl('iot_rising_arrow.svg'),
        down: this.getCdnUrl('iot_falling_arrow.svg'),
        noChange: '',
        none: ''
      }

      deviceMeterReadingList.forEach(item => {
        const { deviceMeterId, dateTime, value, trend, eventMessage
        } = item;
        const targetDisplayData = idToDisplayDataMap[deviceMeterId];

        if (!targetDisplayData) {
          return;
        }

        Object.assign(targetDisplayData, {
          time: this.formatTime(dateTime),
          value,
          eventMessage,
          trend,
          icon: trendToIconMap[trend]
        });
      })

      if (this.compInfo.dataSource === DATA_SOURCE.KEY_INSTRUMENT_POINTS) {
        this.displayDataList = Object.values(idToDisplayDataMap).sort((a, b) => b.deviceMeterObjCreateTime - a.deviceMeterObjCreateTime).slice(0, this.compInfo.instrumentPointsNumber || 8);
      } else {
        this.displayDataList = Object.values(idToDisplayDataMap);
      }

      this.isLoading = false;
    },

    async fetchData(offset = 0, limit = 8, isNeedExplicitTotalNum = false) {
      if (this.isLoading) return;
      this.isLoading = true;
      const { deviceMeterObjList, total } = await getDeviceMeterObjList(this.dataId, offset, limit, this.compInfo.dataSource === DATA_SOURCE.KEY_INSTRUMENT_POINTS, isNeedExplicitTotalNum);

      if (this.compInfo.dataSource === DATA_SOURCE.KEY_INSTRUMENT_POINTS) {
        this.total = Math.min(total, this.compInfo.instrumentPointsNumber || 8);
      } else {
        this.total = total;
      }

      // todo 更换为后端接口
      const deviceMeterReadingList = await getRelatedDeviceMeterReadingsList.call(this, deviceMeterObjList.map(item => item._id));

      this.processData(deviceMeterObjList, deviceMeterReadingList);
    },

    getCdnUrl(imageName) {
      return `https://a9.fspage.com/FSR/weex/stock/device/${imageName}`;
    },

    handleUnfold() {
      this.isUnfold = !this.isUnfold;
    },

    handleCurrentChange(curPage) {
      this.offset = (curPage - 1) * this.pageSize;
      this.fetchData(this.offset, this.pageSize);
    },

    handleRefresh() {
      this.fetchData(this.offset, this.pageSize);
    },

    handleFullScreen(width) {
      if (this.isFullScreen && width > 1500) return;
      this.isFullScreen = width > 1500;
      if (this.isFullScreen) {
        this.pageSize = 20;
        this.offset = 0;
      } else {
        this.pageSize = 8;
        this.offset = 0;
      }
      this.fetchData(this.offset, this.pageSize);
    },

    handleClick(e) {
      if (e.target.className === 'view') {
        this.fetchData(this.offset, this.pageSize, true);
      }
    }
  }
};
</script>

<style lang="less" scoped>
.iot-data-visualization-container {
  border-radius: 8px;
  padding: 0 16px;
  background-color: #fff;

  .header {
    display: flex;
    justify-content: space-between;
    height: 26px;
    margin: 16px 0 8px;

    .left-box {
      // font-family: Source Han Sans CN;
      font-weight: 700;
      font-size: 13px;
      line-height: 26px;
      letter-spacing: 0px;
      color: rgba(24, 28, 37, 1);
      transition: transform 0.3s ease;

      .fx-icon-unfold-2 {
        font-size: 13px;
        color: rgba(24, 28, 37, 1);
        transform-origin: center center;
        cursor: pointer;
      }

      .fx-icon-unfold-2.inactive {
        transform: rotate(-90deg);
      }
    }

    .right-box {
      width: 33px;
      height: 28px;
      background-color: rgba(242, 244, 251, 1);
      border-radius: 7px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      .fx-icon-refresh {
        width: 13px;
        height: 13px;

        &::before {
          color: rgba(24, 28, 37, 1);
        }
      }
    }

  }

  .content-container {
    position: relative;
    max-height: 0;
    transition: transform 0.3s ease-out, max-height 0.3s ease-out;
    margin-top: 16px;
    overflow: auto;
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
    /* IE/Edge */

    /* 针对Webkit浏览器 (Chrome, Safari) */
    &::-webkit-scrollbar {
      display: none;
    }

    .content-body {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      overflow: auto;
      max-height: calc(328px - 44px);

      // margin-top: 8px;
      /* 针对Webkit浏览器 (Chrome, Safari) */
      &::-webkit-scrollbar {
        width: 10px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 5px;
      }

      &::-webkit-scrollbar-thumb {
        background: #C6CDD4;
        border-radius: 5px;
        border: 2px solid #f1f1f1;
      }

      &::-webkit-scrollbar-thumb:hover {
        background: #8BB6F7;
      }

      .card-box {
        // width: 248px;
        height: 136px;
        display: flex;
        justify-content: center;
        align-items: center;
        font-family: PingFang SC;
        line-height: 100%;
        letter-spacing: 0px;

        .top-box {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;

          .name {
            font-weight: 500;
            font-size: 16px;
            color: rgba(24, 28, 37, 1);
          }

          .time {
            font-weight: 400;
            font-size: 12px;
            color: rgba(145, 149, 158, 1);
            margin-top: 4px;
          }
        }

        .bottom-box {
          text-align: center;
          margin-top: 12px;

          .value {
            font-weight: 500;
            font-size: 16px;
            color: rgba(24, 28, 37, 1);
          }

          .unit {
            font-weight: 400;
            font-size: 12px;
            color: rgba(145, 149, 158, 1);
          }

          .icon {
            display: inline-block;
            width: 16px;
            height: 16px;

            img {
              width: 100%;
              height: 100%;
            }
          }

        }
      }

    }

    .content-body.full-screen {
      grid-template-columns: repeat(5, 1fr);
    }

    .content-footer {
      display: flex;
      justify-content: center;
      margin-top: 16px;
    }
  }

  .content-container.active {
    max-height: 328px;
    transform: translateY(0);
  }

  // .content-container.not-in-detail {
  //   // max-height: 0px;

  //   .content-body {
  //     max-height: calc(620px - 44px)!important;
  //   }
  // }

  .content-container.active.not-in-detail {
    max-height: 620px;
    transform: translateY(0);

    .content-body {
      max-height: calc(620px - 44px);
    }
  }

  .empty-box {
    text-align: center;
    line-height: 100px;
    font-size: 14px;
    color: #91959E;
  }
}

.iot-data-visualization-container.inactive {
  margin: 8px 0 0;
}
</style>
