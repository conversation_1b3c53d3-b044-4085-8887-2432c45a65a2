<template>
    <div class="monitor-over-wrapper">
        <div class="monitor-title">{{ title }}</div>
        <div class="monitor-list">
            <template v-if="monitorList.length">
                <!-- 左边按钮 -->
                <div
                    :class="[
                        currentClickNumber > 0 ? '' : 'arrowOpacity',
                        'btn',
                    ]"
                    @click="scrollLeft"
                >
                    <i class="fx-icon-arrow-left-gray-2" />
                </div>
                <!-- 中间列表 -->
                <div class="fixedBox" ref="fixedBox">
                    <div
                        class="centerScroll"
                        :style="`width:${
                            signleWidth * monitorList.length
                        }px;transform:translate(${scrollResultWidth}px,0);transition:1s;`"
                    >
                        <div
                            v-for="(item, index) in monitorList"
                            :key="item.id"
                            :ref="`list-${item.id}`"
                            :data-id="item.id"
                            :class="getShapeClass(item, index)"
                            @click="changeStage(item, $event)"
                            @mouseenter="mouseover(item)"
                            @mouseleave="mouseLeave(item)"
                        >
                            <i
                                :class="statusClass(item.biz_status)"
                                :ref="`icon-${item.id}`"
                                @click="handleClick(`list-${item.id}`, item)"
                            ></i>
                            <span
                                :class="stageLabelCalss(item, index)"
                                :title="item.stage_name"
                            >
                                <i
                                    class="text"
                                    :style="{
                                        width: item.showIcon ? '90%' : '100%',
                                    }"
                                    >{{ item.order }}:{{ item.stage_name }}</i
                                >
                                <i
                                    :class="{ 'stage-icon': item.showIcon }"
                                    @click="showObjDetail(item)"
                                ></i>
                            </span>
                        </div>
                    </div>
                    <transition name="slideDown">
                        <ul class="status-select-main" ref="statusMain">
                            <li
                                v-for="(o, idx) of bizOptions"
                                :key="idx"
                                @click="changeStatus(o.value)"
                            >
                                {{ o.label }}
                            </li>
                        </ul>
                    </transition>
                </div>
                <!-- 右边按钮 -->
                <div
                    :class="[noScrollRight > 0 ? '' : 'arrowOpacity', 'btn']"
                    @click="scrollRight"
                >
                    <i class="fx-icon-arrow-right-gray-2" />
                </div>
            </template>
            <template v-else>
                <div class="no-data">{{ $t("暂无数据") }}</div>
            </template>
        </div>
        <ul class="monitor-card" v-if="Object.keys(monitorCard).length">
            <li
                v-if="
                    (!checkedLists ||
                        checkedLists.indexOf('overdue_days') > -1) &&
                    monitorCard.overdue_days !== undefined
                "
            >
                <h4>
                    {{ $t("项目阶段") }}{{ $t("未完成")
                    }}<span
                        class="f-title-help crm-ui-title"
                        :data-title="overdaysTitle"
                        data-pos="top"
                        >?</span
                    >
                </h4>
                <p>
                    {{ $t("crm.project.stage.overdays")
                    }}<i class="red">{{ monitorCard.overdue_days }}</i
                    >{{ $t("天") }}
                </p>
                <p>
                    <span class="date"
                        >{{
                            $t("ProjectTaskObj.field.plan_end_date.label")
                        }}:</span
                    ><span class="num">{{ monitorCard.date }}</span>
                </p>
            </li>
            <li
                v-if="
                    (!checkedLists ||
                        checkedLists.indexOf('extended_days') > -1) &&
                    monitorCard.extended_days !== undefined
                "
            >
                <h4>
                    {{ $t("项目阶段") }}{{ $t("已完成")
                    }}<span
                        class="f-title-help crm-ui-title"
                        :data-title="extendaysTitle"
                        data-pos="top"
                        >?</span
                    >
                </h4>
                <p>
                    {{ $t("crm.project.stage.extendays")
                    }}<i class="green">{{ monitorCard.extended_days }}</i
                    >{{ $t("天") }}
                </p>
                <p>
                    <span class="date"
                        >{{
                            $t("ProjectTaskObj.field.plan_end_date.label")
                        }}:</span
                    ><span class="num">{{ monitorCard.date }}</span>
                </p>
            </li>
            <li v-if="monitorCard.total_task_count !== undefined">
                <h4>{{ $t("全部任务") }}</h4>
                <p>
                    <i>{{ monitorCard.total_task_count }}</i
                    >{{ $t("crm.project.task.num") }}
                </p>
                <p class="taks-status">
                    <span
                        >{{ $t("已完成") }}:<b>{{
                            monitorCard.completed_task_count
                        }}</b></span
                    ><span
                        >{{ $t("未开始") }}:<b>{{
                            monitorCard.unstarted_task_count
                        }}</b></span
                    ><span
                        >{{ $t("进行中") }}:<b>{{
                            monitorCard.ongoing_task_count
                        }}</b></span
                    ><span
                        >{{ $t("已暂停") }}:<b>{{
                            monitorCard.pause_task_count
                        }}</b></span
                    ><span
                        >{{ $t("已取消") }}:<b>{{
                            monitorCard.cancel_task_count
                        }}</b></span
                    >
                </p>
            </li>
            <li
                v-if="
                    (!checkedLists ||
                        checkedLists.indexOf('recorded_working_hours') > -1) &&
                    monitorCard.recorded_working_hours !== undefined
                "
            >
                <h4>
                    {{ $t("crm.project.stage.workinghours")
                    }}<span
                        class="f-title-help crm-ui-title"
                        :data-title="hoursTitle"
                        data-pos="top"
                        >?</span
                    >
                </h4>
                <p>
                    <i>{{ monitorCard.recorded_working_hours }}</i
                    >h
                </p>
                <p>
                    <span class="date">{{ $t("crm.project.plan_work") }}:</span
                    ><span class="num"
                        >{{ monitorCard.planned_working_hours }}h</span
                    >
                </p>
            </li>
        </ul>
        <div :class="[tableShowLen === 1 ? 'only' : '', 'monitor-table']">
            <fx-table
                :data="unCompleteTable"
                border
                style="width: 50%"
                class="monitor-item"
                :header-cell-style="setHeaderStyle"
                :cell-class-name="setCellOverColor"
                @row-click="showObjDetail"
                v-if="
                    (!checkedLists ||
                        checkedLists.indexOf('unCompleteTable') > -1) &&
                    unCompleteTable.length
                "
            >
                <fx-table-column
                    prop="name"
                    :label="$t('crm.project.unfinish_tasks')"
                    class="column"
                    :render-header="
                        (h, obj) => renderTableHeader(h, obj, 'overdays')
                    "
                ></fx-table-column>
                <fx-table-column
                    prop="overdue_days"
                    :label="$t('crm.project.overdays')"
                    width="150"
                    class="column"
                ></fx-table-column>
            </fx-table>
            <fx-table
                :data="completeTable"
                border
                style="width: 50%"
                class="monitor-item"
                :header-cell-style="setHeaderStyle"
                :cell-class-name="setCellExtendColor"
                @row-click="showObjDetail"
                v-if="
                    (!checkedLists ||
                        checkedLists.indexOf('completeTable') > -1) &&
                    completeTable.length
                "
            >
                <fx-table-column
                    prop="name"
                    :label="$t('crm.project.extendays_tasks')"
                    class="column"
                    :render-header="
                        (h, obj) => renderTableHeader(h, obj, 'extendays')
                    "
                ></fx-table-column>
                <fx-table-column
                    prop="extended_days"
                    :label="$t('crm.project.extendays')"
                    width="150"
                    class="column"
                ></fx-table-column>
            </fx-table>
        </div>
    </div>
</template>

<script>
import moment from "base-moment";
const util = CRM.util;
export default {
    props: ["compInfo", "apiName", "dataId"],
    data() {
        return {
            detailData: this.$context.getData(),
            fields: this.$context.getDescribe().fields || {},
            comps: {},
            filters: [],
            monitorList: [],
            monitorCard: {},
            unCompleteTable: [],
            completeTable: [],
            title: '--',
            currentSatgeId: 0,
            bizOptions: {},
            //阶段器滚动的相关参数
            currentProgressId: '',
            scrollResultWidth: 0, //transform滚动的距离
            signleWidth: 179, //单个流程的宽度
            activeName: 0,
            currentClickNumber: 0,
            noScrollRight: true,
            checkedLists: [], // 后台配置
            tableShowLen: 0,
            //存储的id信息
            storageStageId: '',//存储阶段id
            storageProjectId: '',//存储项目id

            clickNum: 0, //点击阶段次数
            isOpenswitch: false, //后台开关是否开启
            isHasStageEdit: false, //是否存在编辑权限
            //tip
            overdaysTitle: $t('crm.project.stage.overdays.desc'),
            extendaysTitle: $t('crm.project.stage.extendays.desc'),
            hoursTitle: $t('crm.project.stage.workinghours.desc')
        }
    },
    created() {
        const _this = this;
        this.$nextTick(async () => {
            const storageIds = localStorage.getItem('projectStageId');
            _this.comps = _this.$context.getComponents() || {};
            
            // 解析存储的ID，格式：项目ID_阶段ID
            const [projectId = '', stageId = ''] = (storageIds || '').split('_');
            _this.storageProjectId = projectId;
            _this.storageStageId = stageId;
            _this.bizOptions = _this.fields['biz_status'].options || [];
            _this.title = $t('crm.项目阶段');
            //埋点
            util.sendLog('ProjectStageObj', "detail", {
                operationId: 'initStage',
                eventType: 'cl',
            });
            await _this.initMonitorList();
            await _this.getChecked();
            await _this.initTaskData();
        })
    },
    mounted() {
        const _this = this;
        document.addEventListener('click', (e) => {
            const curEl = e.target.classList.contains('status-icon');
            if (!curEl && _this.$refs.statusMain) {
                _this.$refs.statusMain.style.visibility = 'hidden';
            }
        })
    },
    watch: {
        monitorList: {
            handler: function (newVal, oldVal) {
                newVal.length && this.initGoRightArrow();
            }
        }
    },
    methods: {
        //获取后台配置
        getChecked() {
            let componentOptions = this.comps.project_stage_overview;
            // componentOptions['checkedSatgeLists'] = undefined;
            this.checkedLists = componentOptions['checkedSatgeLists'];
            console.log('checkedSatgeLists', componentOptions)

        },
        //获取数据整合
        async initTaskData(isRefresh) {
            let tableData = await this.initMonitorTable();
            tableData.date = moment.unix(tableData.plan_end_date / 1000, true).format("YYYY-MM-DD");
            this.monitorCard = tableData;
            if (isRefresh) return;
            const overdueLen = tableData.overdue_task && tableData.overdue_task.length ? 1 : 0;
            const extendedLen = tableData.extended_task && tableData.extended_task.length ? 1 : 0;
            this.tableShowLen = overdueLen + extendedLen;
            this.unCompleteTable = tableData.overdue_task.map(
                ({ _id, name, overdue_days, object_describe_api_name }) =>
                    ({ _id, name, overdue_days, object_describe_api_name })
            );
            this.completeTable = tableData.extended_task.map(
                ({ _id, name, extended_days, object_describe_api_name }) =>
                    ({ _id, name, extended_days, object_describe_api_name })
            );
        },
        //获取阶段
        initMonitorList() {
            const _this = this;
            return new Promise((resolve, reject) => {
                _this._dataAjax = util.FHHApi(
                    {
                        url: `/EM1HNCRM/API/v1/object/ProjectStageObj/controller/RelatedList`,
                        data: {
                            associate_object_data_id: _this.dataId,
                            associate_object_describe_api_name: _this.apiName,
                            associated_object_describe_api_name: "ProjectStageObj",
                            associated_object_field_related_list_name: "stage_project_list",
                            ignore_scene_record_type: false,
                            include_associated: true,
                            include_describe: false,
                            name: _this.detailData && _this.detailData['name'],
                            object_describe_api_name: "ProjectStageObj",
                            search_query_info: JSON.stringify({ "limit": 2000, "offset": 0, "filters": _this.filters, "orders": [{ "fieldName": "index", "isAsc": true }] }),
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0 && res.Value.dataList.length) {
                                let processData = res.Value.dataList;
                                _this.currentSatgeId = _this.dataId === _this.storageProjectId && _this.storageStageId  ? _this.storageStageId : processData[0]._id;
                                _this.monitorList = processData.map((item, index) => {
                                    return {
                                        id: item._id,
                                        order: index + 1,
                                        object_describe_api_name: item.object_describe_api_name,
                                        biz_status: item.biz_status,
                                        stage_name: item.name,
                                        showIcon: false,
                                        oldData: item
                                    }
                                });
                                resolve(res);
                            } else {
                                _this.monitorList = [];
                                return;
                            }
                        },
                        error: function (res) {
                            reject(res);
                        }
                    },
                    { errorAlertModel: 1 }
                )
            })
        },
        //任务表格
        initMonitorTable() {
            const _this = this;
            return new Promise((resolve, reject) => {
                _this._tableAjax = util.FHHApi(
                    {
                        url: `/EM1HNCRM/API/v1/object/project_stage/service/overview`,
                        data: {
                            stage_id: _this.currentSatgeId
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                return resolve(res.Value.data)
                            }
                            reject(res);
                        },
                        error: function (res) {
                            reject(res);
                        }
                    }, { errorAlertModel: 1 })
            });
        },
        //初始化判断是否向右滚动
        initGoRightArrow() {
            const boxW = ($('.monitor-list').width() - 29 * 2) / 179;
            const currentScrollW = Math.floor(boxW) * 179;
            $('.fixedBox').css("maxWidth", currentScrollW + "px");
            //当前宽度能容纳的数据个数
            const canNumber = Math.floor(currentScrollW / this.signleWidth);
            //若是最后一个则停止滚动
            if (this.currentClickNumber + canNumber >= this.monitorList.length) {
                this.noScrollRight = false;
                return;
            }
        },
        //进入详情
        showObjDetail(row) {
            const { object_describe_api_name, _id, id } = row;
            CRM.api.show_crm_detail({
                type: object_describe_api_name,
                data: {
                    crmId: _id || id
                }
            });
        },
        //向左滚动
        scrollLeft() {
            //如果右点击的次数大于0，才可以左滚
            if (this.currentClickNumber > 0) {
                this.currentClickNumber -= 1;
                this.noScrollRight = true;
                this.fnScrollW('reduce');
            } else {
                return false;
            }
        },
        //向右滚动
        scrollRight() {
            const currentScrollW = this.$refs['fixedBox'].clientWidth;
            //可以放下的个数
            const canNumber = Math.floor(currentScrollW / this.signleWidth);
            //最后一个阶段图已展示出来，则停止滚动
            if (this.currentClickNumber + canNumber >= this.monitorList.length) {
                return;
            }
            //说明放不下有滚动条
            if (this.monitorList.length > canNumber) {
                this.currentClickNumber += 1;
                if (this.currentClickNumber + canNumber >= this.monitorList.length) {
                    this.noScrollRight = false;
                }
                this.fnScrollW('add');
            }
        },
        //translate的宽度
        fnScrollW(type) {
            let result = 0;
            if (type === 'reduce') {
                result = 179;
            } else if (type === 'add') {
                result = -179;
            } else {
                result = 0;
            }
            this.scrollResultWidth += result;
        },
        //切换阶段器
        changeStage(item, e) {
            //清除所有元素的active
            $(e.currentTarget).siblings().removeClass('active');
            //给当前元素添加active
            $(e.currentTarget).addClass('active');
            
            localStorage.setItem('projectStageId', `${this.dataId}_${item.id}`);
            this.currentSatgeId = item.id;
            const apiname = item.object_describe_api_name;
            //埋点
            util.sendLog(apiname, "detail", {
                operationId: 'clickStage',
                eventType: 'cl',
            });
            this.initTaskData();
        },
        //阶段状态切换
        handleClick(curRef, item) {
            this.curStage = item.oldData;
            //调用异步方法
            this.handleAsyncClick(curRef);
        },
        async handleAsyncClick(curRef) {
            if (this.clickNum) return this._showStatusTip(curRef);
            try {
                const switchVal = await util.getConfigValues(['project_biz_status_edit']);
                const editData = await util.getActionRights({ ProjectStageObj: ['Edit'] }, true);
                this.clickNum++;
                //存储值
                this.isOpenswitch = parseInt(switchVal[0].value);
                this.isHasStageEdit = editData.ProjectStageObj.Edit;
                this._showStatusTip(curRef);
            } catch (error) {
                console.log('error:', error);
            }
        },
        //不同结果提示框
        _showStatusTip(curRef) {
            if (!this.isOpenswitch) return util.alert($t('stage_status_open.update'));
            if (!this.isHasStageEdit) return util.alert($t('stage_no_edit'));
            //存在编辑权限，则显示下拉框
            const boxLeft = this.$refs.fixedBox.offsetLeft;
            const iconEl = this.$refs[curRef][0].querySelector('.status-icon');
            const left = boxLeft + this.$refs[curRef][0].offsetLeft + this.scrollResultWidth + iconEl.offsetLeft + iconEl.clientWidth / 2 - this.$refs.statusMain.offsetWidth / 2;
            this.$refs.statusMain.style.left = left + 'px';
            this.$refs.statusMain.style.visibility = 'visible';
        },
        //切换状态
        changeStatus(currentVal) {
            const _this = this;
            let confirm = util.confirm($t('update_stage_status'), $t('提示'), function () {
                confirm.hide();
                util.showLoading_tip('', $('.monitor-over-wrapper'));
                //更新装填图标
                util.FHHApi(
                    {
                        url: "/EM1HNCRM/API/v1/object/ProjectStageObj/action/Edit",
                        data: {
                            object_data: _.extend({}, _this.curStage, { biz_status: currentVal, version: JSON.stringify(JSON.parse(_this.curStage.version) + 1) }),
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                util.updateResult(res.Value, (permiss) => {
                                    if (!permiss) return;
                                    if (!('biz_status' in _this.curStage)) return util.alert($t('stage_status_no_edit'));
                                    util.remind(1, $t('操作成功'));
                                    _this.$refs[`icon-${_this.curStage._id}`][0].className = _this.statusClass(currentVal);
                                    _this.initTaskData(true);
                                })
                            } else {
                                util.alert(res.Result.FailureMessage);
                            }
                        },
                        complete: function () {
                            util.hideLoading_tip();
                        },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
            }, {
                hideFn: function () {
                    confirm = null;
                }
            });

        },
        mouseover(item, ev) {
            item.showIcon = true;
        },
        mouseLeave(item, ev) {
            item.showIcon = false;
        },
        //设置表头样式
        setHeaderStyle() {
            return 'color:var(--color-neutrals15);font-size:12px;'
        },
        //设置列样式
        setCellOverColor({ row, columnIndex }) {
            if (columnIndex === 0) {
                return 'column-blue'
            }
            if (columnIndex === 1) {
                return 'column-red'
            }
        },
        setCellExtendColor({ row, columnIndex }) {
            if (columnIndex === 0) {
                return 'column-blue'
            }
            if (columnIndex === 1) {
                return 'column-green'
            }
        },
        statusClass(status) {
            let icon_name = '';
            let img_position = '';
            switch (status) {
                case 'unstarted':
                    icon_name = 'icon-unstart';
                    img_position = '';
                    break;
                case 'completed':
                    icon_name = 'el-icon-success';
                    img_position = '';
                    break;
                case 'ongoing':
                    icon_name = 'icon-ongoing';
                    img_position = 'center';
                    break;
                case 'pause':
                case 'cancel':
                    icon_name = `status_${status}`;
                    img_position = 'center';
                    break;
                default:
                    icon_name = 'icon-error default';
                    img_position = 'center';
            }
            const isHide = icon_name ? 'show' : 'hide';
            return `${icon_name} ${isHide} ${img_position} status-icon`;
        },
        //表头添加tip信息
        renderTableHeader(h, { column, $index }, tableName) {
            let title = tableName === 'overdays' ? $t('crm.project.unfinish_tasks.desc') : $t('crm.project.extendays_tasks.desc');
            let label = tableName === 'overdays' ? $t('crm.project.unfinish_tasks') : $t('crm.project.extendays_tasks');
            return h('div', [
                h('span', label),
                h('span', {
                    attrs: {
                        class: 'f-title-help crm-ui-title',
                        'data-title': title,
                        'data-pos': 'top'
                    },
                    domProps: {
                        innerText: '?'
                    },
                })
            ])
        },
        //阶段器图形
        getShapeClass(item, index) {
            const item_class = item.biz_status === 'unstarted' ? 'gray' : item.biz_status === 'ongoing' ? 'light' : (item.biz_status === 'pause' || item.biz_status === 'cancel') ? 'drak_gray' : '';
            const number = index > 0 && index < this.monitorList.length - 1 ? 'half' : '';
            let isActive = '';
            // 判断是否是当前元素
            if (this.dataId === this.storageProjectId && item.id === this.storageStageId) {
                isActive = 'active';
            } else if (this.dataId !== this.storageProjectId && index === 0) {
                isActive = 'active';
            }
            
            return `list-item ${item_class} ${number} ${isActive}`;
        },
        //阶段label距离
        stageLabelCalss(item, index) {
            const number = index > 0 && index < this.monitorList.length - 1 ? 'half' : '';
            const status = item.biz_status === 'unstarted' ? 'unstart' : '';
            return `stage-label ${number} ${status}`;
        }
    },
    destroyed() {
        this.clickNum && (this.clickNum = null);
    }
}
</script>
<style lang="less" scoped>
.monitor-over-wrapper {
    .monitor-title {
        position: relative;
        font-size: 16px;
        margin: 10px 3px;
        color: var(--color-neutrals19);
        padding-left: 10px;
        &::before {
            content: "";
            width: 4px;
            height: 16px;
            border-radius: 2px;
            background: var(--color-primary06);
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .monitor-list {
        position: relative;
        display: flex;
        align-items: center;
        margin: 20px 0;
        .no-data {
            text-align: center;
            width: 100%;
        }
        .btn {
            font-size: 24px;
            cursor: pointer;
            // opacity: 0.6;
            &.arrowOpacity {
                cursor: default;
                opacity: 0.4;
            }
            &:first-child {
                margin-right: 10px;
            }
            &:last-child {
                margin-left: 5px;
            }
        }
        .fixedBox {
            overflow: hidden;

            .centerScroll {
                display: flex;
                transform: all 2s;
                .list-item {
                    position: relative;
                    width: 170px;
                    height: 30px;
                    margin-left: 10px;
                    line-height: 29px;
                    text-align: left;
                    padding: 0 12px;
                    font-size: 14px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    color: #1ba854;
                    border-radius: 21px;
                    cursor: pointer;
                    background: #dcfae4;
                    &.drak_gray {
                        background: #c1c5ce;
                        color: #fff;
                    }
                    &:first-child {
                        margin-left: 0;
                    }
                    .stage-label {
                        display: inline-block;
                        width: 86%;
                        &.unstart {
                            width: 83%;
                            &.half {
                                margin-left: -2px;
                                width: 88%;
                            }
                        }
                        .text {
                            display: inline-block;
                            width: 100%;
                            font-style: normal;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            white-space: nowrap;
                            vertical-align: middle;
                        }
                    }
                    &.half {
                        padding: 0 10px 0 16px;
                        border-radius: 0 21px 21px 0;
                    }
                    &:hover,
                    &.active {
                        background: #1ba854 !important;
                        color: var(--color-neutrals01) !important;
                    }
                    .stage-icon {
                        position: absolute;
                        right: 10px;
                        top: 50%;
                        transform: translateY(-50%);
                        width: 20px;
                        height: 17px;
                        background-image: url("~@assets/images/projectdoc/stage-icon.svg");
                        background-repeat: no-repeat;
                    }
                    &.gray {
                        background: var(--color-special01);
                        color: var(--color-neutrals15);
                    }
                    &.light {
                        background: #4fcf8a;
                        color: var(--color-neutrals01);
                    }
                    .status-icon {
                        display: inline-block;
                        width: 20px;
                        height: 18px;
                        margin-right: 3px;
                        font-size: 18px;
                        background-image: none;
                        background-repeat: no-repeat;
                        &.hide {
                            display: none;
                        }
                        &.icon-ongoing {
                            background-image: url("~@assets/images/projectdoc/ongoing.svg");
                        }
                        &.status_pause {
                            background-image: url("~@assets/images/projectdoc/pause.svg");
                        }
                        &.center {
                            background-position: center;
                        }
                        &.status_cancel {
                            background-image: url("~@assets/images/projectdoc/cancel.svg");
                        }
                        &.icon-unstart {
                            background-position: bottom;
                            background-image: url("~@assets/images/projectdoc/unstart.svg");
                        }
                        &.icon-error {
                            background-position: center;
                            background-image: url("~@assets/images/projectdoc/error.svg");
                        }
                    }
                }
                .list-item.half::before {
                    content: "";
                    display: block;
                    width: 14px;
                    height: 33px;
                    position: absolute;
                    top: 50%;
                    left: -10px;
                    background: white;
                    transform: translateY(-50%);
                    border-bottom-right-radius: 16px 26px;
                    border-top-right-radius: 16px 26px;
                }
                position: relative;
                left: 0;
                transition: left 1s;
            }
        }
        .status-select-main {
            position: absolute;
            left: 20px;
            top: 30px;
            padding: 5px 10px;
            line-height: 22px;
            font-size: 13px;
            visibility: hidden;
            color: var(--color-neutrals15);
            background-color: #fff;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            cursor: pointer;
            &::before {
                content: "";
                width: 0px;
                height: 0px;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-bottom: 6px solid #fff;
                position: absolute;
                top: -5px;
                left: 50%;
                transform: translateX(-50%);
            }
            &.slideDown-enter-active,
            &.slideDown-leave-active {
                transition: opacity 0.25s;
            }
            &.slideDown-enter, &.slideDown-leave-to /* .fade-leave-active in <2.1.8 */ {
                opacity: 0;
            }
        }
    }
    .monitor-card {
        display: flex;
        justify-content: space-around;
        border: 1px solid var(--color-neutrals05);
        border-radius: 4px;
        padding: 12px 0px;
        box-sizing: border-box;
        li {
            // width:25%;
            line-height: 1.7;
            h4 {
                font-size: 13px;
                color: var(--color-neutrals15);
            }
            i {
                padding: 0 1px;
                font-size: 24px;
                font-style: normal;
                font-weight: normal;
                color: var(--color-neutrals19);
                &.red {
                    color: var(--color-danger06);
                }
                &.green {
                    color: var(--color-success06);
                }
            }
            .status {
                margin-left: 5px;
                font-size: 13px;
            }
            .date,
            .taks-status span {
                margin-right: 8px;
                color: var(--color-neutrals11);
            }
            .num,
            .taks-status i {
                color: var(--color-neutrals15);
            }
            .num,
            .date,
            .taks-status {
                font-size: 12px;
            }
            .taks-status.b {
                font-weight: normal;
            }
        }
    }
    .monitor-table {
        display: flex;
        margin-top: 10px;
        &.only {
            .monitor-item:first-child {
                margin-right: 0;
            }
        }
        .monitor-item {
            max-height: 208px;
            overflow-y: auto;
            &:first-child {
                margin-right: 20px;
            }
            .column {
                font-size: 12px;
            }
            /deep/ .column-blue {
                color: var(--color-info06);
            }
            /deep/ .column-red {
                color: var(--color-danger06);
            }

            /deep/ .column-green {
                color: #30c776;
            }
            .f-title-help {
                width: 12px;
                height: 12px;
                min-width: 12px;
                min-height: 12px;
                line-height: 13px;
                margin: 2px 0 0 4px;
                border: 1px solid var(--color-special02);
                border-radius: 100%;
                color: var(--color-special02);
                text-align: center;
                cursor: pointer;
                display: inline-block;
            }
        }
        /deep/ th,
        /deep/ td {
            padding: 0;
            height: 42px;
            line-height: 42px;
            .cell {
                line-height: normal;
                white-space: nowrap;
            }
            div {
                line-height: normal;
            }
        }
        /deep/ th .cell > div {
            padding: 0;
        }
        /deep/ td {
            height: 41px;
            line-height: 41px;
        }
        /deep/ tbody tr:hover > td {
            cursor: pointer;
        }
    }
    /deep/ .f-title-help {
        display: inline-block;
        width: 12px;
        height: 12px;
        text-align: center;
        line-height: 13px;
        margin: 2px 0 0 4px;
        border: 1px solid var(--color-special02);
        border-radius: 100%;
        color: var(--color-special02);
        cursor: pointer;
    }
}
</style>