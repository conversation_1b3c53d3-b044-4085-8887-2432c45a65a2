/*
 * @Descripttion:
 * @Author: LiAng
 * @Date: 2024-11-22 18:38:44
 * @LastEditors: LiAng
 * @LastEditTime: 2025-01-06 15:02:06
 */
import activityRecordDetail from './activityrecorddetail/activityrecorddetail.vue'
import corpus from './corpus/corpus.vue'
import Abstract from './abstract/abstract.vue'
import withComponentState from '../../base/components/sfaAiComponentStatus/sfaAiComponentStatus.js'
import sfaActivityAttendeesInsight from './participantinsights/participantinsights.vue'
import sfaActivityNote from './note'
// 使用高阶组件包装Abstract组件
const AbstractWrappedComponent = withComponentState(Abstract);
const AbstractWithState = {
  name: 'AbstractWithState',
  props: ['compInfo', 'apiName', 'dataId', "pluginService", "extendData"],
  render(h) {
    // 将props传递给高阶组件
    return h(AbstractWrappedComponent, {
      props: {
        ...this.$props,
        stateServiceParams: {
          dataId: this.dataId,
          apiName: this.apiName,
          compInfo: this.compInfo,
          pluginService: this.pluginService,
          extendData: this.extendData,
          componentName: 'interactive_summary'
        }
      },
      attrs: this.$attrs,
      on: this.$listeners,
      scopedSlots: this.$scopedSlots
    });
  }
};

// 使用高阶组件包装corpus组件
const CorpusWrappedComponent = withComponentState(corpus);
const CorpusWithState = {
    name: 'CorpusWithState',
    props: ['compInfo', 'apiName', 'dataId', "pluginService", "extendData"],
    render(h) {
      // 将props传递给高阶组件
      return h(CorpusWrappedComponent, {
        props: {
          ...this.$props,
          stateServiceParams: {
            dataId: this.dataId,
            apiName: this.apiName,
            compInfo: this.compInfo,
            pluginService: this.pluginService,
            extendData: this.extendData,
            componentName: 'interactive_content'
          },
          fullHeight: true
        },
        attrs: this.$attrs,
        on: this.$listeners,
        scopedSlots: this.$scopedSlots
      });
    }
  };


  // 使用高阶组件包装sfaActivityAttendeesInsight组件
  const SfaActivityAttendeesInsightWrappedComponent = withComponentState(sfaActivityAttendeesInsight);
  const SfaActivityAttendeesInsightWithState = {
    name: 'SfaActivityAttendeesInsightWithState',
    props: ['compInfo', 'apiName', 'dataId', "pluginService", "extendData"],
    render(h) {
      // 将props传递给高阶组件
      return h(SfaActivityAttendeesInsightWrappedComponent, {
        props: {
          ...this.$props,
          stateServiceParams: {
            dataId: this.dataId,
            apiName: this.apiName,
            compInfo: this.compInfo,
            pluginService: this.pluginService,
            extendData: this.extendData,
             componentName: 'sfa_activity_attendees_insight'
          }
        },
        attrs: this.$attrs,
        on: this.$listeners,
        scopedSlots: this.$scopedSlots
      });
    }
  };
export default {
  form_component: activityRecordDetail,
  sfa_activity_audio_record: CorpusWithState,
  activity_meeting_summary: AbstractWithState,
  sfa_activity_attendees_insight:SfaActivityAttendeesInsightWithState,
  sfa_activity_note: sfaActivityNote
}
