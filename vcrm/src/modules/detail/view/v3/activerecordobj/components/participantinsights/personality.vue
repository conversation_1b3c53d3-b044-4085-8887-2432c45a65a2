<template>
  <div 
    class="personality-container"
    ref="personalityContainer"
    :style="{ gridTemplateColumns: gridColumns }"
  >
    <div
      v-for="(item, index) in personalityData"
      :key="index"
      class="personality-item-container"
    >
      <div class="personality-item">
        <span class="personality-item-title">
          {{  configData[item.insightType] ? configData[item.insightType].name : "--" }} 
        </span>
        :
        <span class="personality-item-content">
          {{ configData[item.insightType] ? configData[item.insightType][item.insightText] : "--" }}
        </span>
        <fx-popover
        v-if="item.link_data && item.link_data.length && keyType === 'cantactInsight'"
        placement="bottom-start"
        popper-class="insight-text-settings-popover"
        trigger="click"
        :ref="'popover-' + index"
      >
        <span slot="reference" class="locate-source">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="6"
            height="6"
            viewBox="0 0 6 6"
            fill="none"
          >
            <path
              d="M3.30811 4.66129C4.28108 4.19355 4.78378 3.30645 4.78378 2.79032C4.71892 2.83871 4.58919 2.87097 4.44324 2.87097C3.76216 2.87097 3.30811 2.35484 3.30811 1.69355C3.30811 1.06452 3.77838 0.5 4.57297 0.5C5.38378 0.5 6 1.25806 6 2.24194C6 3.83871 4.94595 4.96774 3.76216 5.5L3.30811 4.66129ZM0 4.66129C0.989189 4.19355 1.49189 3.30645 1.49189 2.79032C1.42703 2.83871 1.2973 2.87097 1.13514 2.87097C0.47027 2.87097 0.0162162 2.35484 0.0162162 1.69355C0.0162162 1.06452 0.47027 0.5 1.28108 0.5C2.09189 0.5 2.70811 1.25806 2.70811 2.24194C2.70811 3.83871 1.65405 4.96774 0.47027 5.5L0 4.66129Z"
              fill="#181C25"
            />
          </svg>
        </span>
        <div class="popover-content">
          <div class="popover-title">{{$t('crm.sfa.emiss.view_related_sales_record')}}</div>
          <ul v-if="item.link_data && item.link_data.length">
            <li
              v-for="link in item.link_data"
              :key="link._id || link.id"
              @click="handleRecordClick(link._id || link.id, index,item)"
              class="record-item"
            >
              <span>{{ link.name || "--" }}</span>
              <i class="fx-icon-f-lianjie"></i>
            </li>
          </ul>
        </div>
      </fx-popover>
        <span v-if="keyType != 'cantactInsight'" class="locate-source" @click.stop="toSource(item)">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="6"
            height="6"
            viewBox="0 0 6 6"
            fill="none"
          >
            <path
              d="M3.30811 4.66129C4.28108 4.19355 4.78378 3.30645 4.78378 2.79032C4.71892 2.83871 4.58919 2.87097 4.44324 2.87097C3.76216 2.87097 3.30811 2.35484 3.30811 1.69355C3.30811 1.06452 3.77838 0.5 4.57297 0.5C5.38378 0.5 6 1.25806 6 2.24194C6 3.83871 4.94595 4.96774 3.76216 5.5L3.30811 4.66129ZM0 4.66129C0.989189 4.19355 1.49189 3.30645 1.49189 2.79032C1.42703 2.83871 1.2973 2.87097 1.13514 2.87097C0.47027 2.87097 0.0162162 2.35484 0.0162162 1.69355C0.0162162 1.06452 0.47027 0.5 1.28108 0.5C2.09189 0.5 2.70811 1.25806 2.70811 2.24194C2.70811 3.83871 1.65405 4.96774 0.47027 5.5L0 4.66129Z"
              fill="#181C25"
            />
          </svg>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "Personality",
  props: {
    insight: {
      type: Object,
      default: null,
    },
    keyType:{
      type: String,
      default: null
    }
  },
  data() {
    return {
      containerWidth: 0,
      resizeObserver: null,
      containerWidth: 0,
      resizeObserver: null,
      configData: {
        decision_style: {
          name: $t("sfa.vcrm.participantinsights.decision_style.name"),
          decisive: $t("sfa.vcrm.participantinsights.decision_style.decisive"),
          hesitant: $t("sfa.vcrm.participantinsights.decision_style.hesitant"),
        },
        communication_preference: {
          name: $t("sfa.vcrm.participantinsights.communication_preference.name"),
          direct: $t("sfa.vcrm.participantinsights.communication_preference.direct"),
          indirect: $t("sfa.vcrm.participantinsights.communication_preference.indirect"),
        },
        risk_preference: {
          name: $t("sfa.vcrm.participantinsights.risk_preference.name"),
          avoidance: $t("sfa.vcrm.participantinsights.risk_preference.avoidance"),
          preference: $t("sfa.vcrm.participantinsights.risk_preference.preference"),
        },
        cooperation_orientation: {
          name: $t("sfa.vcrm.participantinsights.cooperation_orientation.name"),
          relation: $t("sfa.vcrm.participantinsights.cooperation_orientation.relation"),
          task: $t("sfa.vcrm.participantinsights.cooperation_orientation.task"),
        },
        professional_drive: {
          name: $t("sfa.vcrm.participantinsights.professional_drive.name"),
          expert: $t("sfa.vcrm.participantinsights.professional_drive.expert"),
          newbie: $t("sfa.vcrm.participantinsights.professional_drive.newbie"),
        },
        motivation_pattern: {
          name: $t("sfa.vcrm.participantinsights.motivation_pattern.name"),
          sensitive: $t("sfa.vcrm.participantinsights.motivation_pattern.sensitive"),
          rational: $t("sfa.vcrm.participantinsights.motivation_pattern.rational"),
        },
      },
    };
  },
  computed: {
    personalityData() {
      let data;
      const me = this;
      if(this.keyType && this.keyType === 'cantactInsight'){
        console.log(this.insight);
        data = this.insight;
      } else {
        try {
          data = JSON.parse(this.insight.insight_result__o);
          console.log(data);
        } catch (error) {
          data = this.insight.insight_result__o || [];
        }
      }
      
      // 过滤数据，只返回符合条件的项目
      const filteredData = Array.isArray(data) 
        ? data.filter(item => item.insightType && item.insightText && me.configData[item.insightType] && me.configData[item.insightType][item.insightText])
        : [];
      
      // 数据更新后重新检查容器宽度
      this.$nextTick(() => {
        this.updateContainerWidth();
      });
      console.log(this.objectFields);
      return filteredData;
    },
    // 根据容器宽度计算列数和对应的grid样式
    gridColumns() {
      if (this.containerWidth <= 350) {
        return 'repeat(1, minmax(0, 1fr))';
      } else if (this.containerWidth <= 475) {
        return 'repeat(2, minmax(0, 1fr))';
      } else {
        return 'repeat(3, minmax(0, 1fr))';
      }
    }
  },
  mounted() {
    this.initResizeObserver();
  },
  beforeDestroy() {
    this.cleanupResizeObserver();
    // 根据容器宽度计算列数和对应的grid样式
  },
  mounted() {
    this.initResizeObserver();
  },
  beforeDestroy() {
    this.cleanupResizeObserver();
  },
  methods: {
    toSource(item) {
      console.log(item);
      this.$emit("locate-source", item);
    },
     handleRecordClick(id, index, item) {
      // 在这里处理点击事件，例如跳转页面
      console.log("Clicked record with id:", id);
      CRM.api.show_crm_detail({
        type: "ActiveRecordObj",
        data: {
          crmId: id,
          apiName: "ActiveRecordObj",
        },
      });

      // --- 修正后的调试代码 ---
      const popoverRefArray = this.$refs["popover-" + index];
      if (Array.isArray(popoverRefArray) && popoverRefArray.length > 0) {
        const popoverInstance = popoverRefArray[0];

        // 尝试关闭
        if (typeof popoverInstance.doClose === "function") {
          popoverInstance.doClose();
        } else {
          popoverInstance.showPopper = false;
        }
      }
    },
    // 初始化ResizeObserver来监听容器宽度变化
    initResizeObserver() {
      if (this.$refs.personalityContainer) {
        // 初始化时获取一次宽度
        this.updateContainerWidth();
        
        // 创建ResizeObserver
        if (window.ResizeObserver) {
          this.resizeObserver = new ResizeObserver(this.debounce((entries) => {
            for (let entry of entries) {
              this.containerWidth = entry.contentRect.width;
            }
          }, 100));
          this.resizeObserver.observe(this.$refs.personalityContainer);
        } else {
          // 兼容老浏览器，使用window resize事件
          window.addEventListener('resize', this.debounce(this.updateContainerWidth, 100));
        }
      }
    },
    // 更新容器宽度
    updateContainerWidth() {
      if (this.$refs.personalityContainer) {
        this.containerWidth = this.$refs.personalityContainer.offsetWidth;
      }
    },
    // 清理ResizeObserver
    cleanupResizeObserver() {
      if (this.resizeObserver) {
        this.resizeObserver.disconnect();
        this.resizeObserver = null;
      } else {
        window.removeEventListener('resize', this.debounce(this.updateContainerWidth, 100));
      }
    },
    // 防抖函数
    debounce(func, wait) {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    }
  },
};
</script>
<style>
.insight-text-settings-popover{
  padding: 12px;
}
</style>

<style lang="less" scoped>
.personality-container {
  display: grid;
  height: auto;
  height: auto;
  row-gap: 12px;
  column-gap: 12px;
  align-self: stretch;
  /* grid-template-columns 现在由 JavaScript 动态设置 */
  .personality-item-container {
    display: flex;
    align-items: center;
    height: 40px;
    border-radius: 8px;
    height: 40px;
    border-radius: 8px;
    background: #F5EDFF;

    .personality-item {
      display: flex;
      padding: 1px 6px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      flex: 1 0 0;
      grid-row: 1 / span 1;
      grid-column: 1 / span 1;
      .personality-item-title {
        color: var(--purple-purple-06, #7341de);
        font-size: 12px;
        max-width: 105px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px; /* 150% */
        display: flex;
        align-items: center;
        margin-top: 2px;
      }
      .personality-item-content {
        color: var(--purple-purple-06, #7341de);
        font-feature-settings: "liga" off, "clig" off;
        font-family: "Source Han Sans CN";
        font-size: 12px;
        max-width: 105px;
        font-style: normal;
        font-weight: 700;
        line-height: 18px;
        display: flex;
        align-items: center;
      }
      .locate-source {
        cursor: pointer;
        font-size: 12px;
        display: inline-block;
        white-space: nowrap;
        width: 14px;
        height: 14px;
        align-items: center;
        text-align: center;
        line-height: 12px;
        gap: 10px;
        border-radius: 22px;
        background: #f5f6f9;
      }
    }
  }
}

.popover-content {
  .popover-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 4px;
  }
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    cursor: pointer;
    font-size: 13px;
    color: #0C6CFF;
    i {
      margin-left: 8px;
      color: #999;
    }
  }
}
</style>