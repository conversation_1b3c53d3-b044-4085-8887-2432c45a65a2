<template>
  <div class="sfa-activity-note-wrapper">
    <FeedReply 
      :feedId="feedId"
    ></FeedReply>
  </div>
</template>

<script>
  export default {
    name: 'SfaActivityNote',
    components: {
        FeedReply: async () => (await Fx.getBizComponent('paasxt', 'FeedReply'))(),
    },
    data () {
      return {
        feedId: this.$context?.getData()?.feed_id
      }
    }
  }
</script>

<style lang="less" scoped>
.sfa-activity-note-wrapper {
  /deep/ .fl-replylist-related-cmpt .fl-replylist-related-cmpt__content {
    padding: 0;
    padding-top: 8px;
    padding-bottom: 8px;
  }
  /deep/ .fl-replylist-related-cmpt .fl-replylist-related-cmpt__replylist {
    padding: 0;
  }
}
</style>