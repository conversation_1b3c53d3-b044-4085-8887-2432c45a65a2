<template>
    <div class="activity-corpus-text-wrapper" ref="text-wrapper">
        <div class="list-empty" v-if="!innerText">
            <div class="list-empty-image"></div>
            <div class="list-empty-text">
                <p>{{ $t('sfa.activity.corpus.text_nodata_tip1') }}</p>
                <p>{{ $t('sfa.activity.corpus.text_nodata_tip2') }}</p>
            </div>
        </div> 
        <fx-scrollbar
            v-else
            class="activity-corpus-text-scroll"
        >
            <!-- <div class="activity-corpus-text-content" v-html="innerText"></div> -->
            <Markdown
                :content="innerText"
                :defaultExpanded="true"
                :expandable="false"
                maxHeight="auto"
            ></Markdown>
        </fx-scrollbar>
    </div>
</template>
<script>
export default {
    name: 'activity-corpus-text',
    props: ["dataId", "apiName", "compInfo", "pluginService"],
    components: {
        Markdown:async () => (await Fx.getBizComponent('paasbiz', 'MarkdownRender'))()
    },
    data() {
        return {
            innerText: '',
            resizeTimer: null,
        }
    },
    created() {
        let data = this.$context.getData();
        this.innerText = _.escape(data.interactive_content || '');
        // this.innerText = ''
        // console.log(data, 'innerText')
    },
    mounted() {
        this.$emit('component-mounted');
    },
    methods: {
        // computeWrapperHeight() {
        //     if (this.resizeTimer) {
        //         clearTimeout(this.resizeTimer);
        //     }

        //     this.resizeTimer = setTimeout(() => {
        //         const wrapper = this.$refs['text-wrapper'];
        //         if (!wrapper) return;
                
        //         const rect = wrapper.getBoundingClientRect();
        //         const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        //         const distanceToTop = rect.top + scrollTop;
                
        //         const minHeight = 530;
        //         const calculatedHeight = Math.max(
        //             minHeight,
        //             window.innerHeight - distanceToTop - 32
        //         );
                
        //         wrapper.style.height = `${calculatedHeight}px`;
        //     }, 100);
        // }
    },
    beforeDestroy() {
        // window.removeEventListener('resize', this.computeWrapperHeight);
        // if (this.resizeTimer) {
        //     clearTimeout(this.resizeTimer);
        // }
    }
}
</script>

<style lang="less" scoped>
 .activity-corpus-text-wrapper {
    // width: 100%;
    overflow: hidden;
    text-align: justify;
    padding-right: 12px;
    // min-height: 530px;
    height: 100%;
    .list-empty {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    .list-empty-image {
        width: 100%;
        height: 120px;
        background: image-set(url("~@assets/images/nodata2.png") 1x,
        url("~@assets/images/<EMAIL>") 2x) center center no-repeat;
    }
    .list-empty-text {
        margin-top: 24px;
        font-size: 14px;
        color: var(--color-neutrals19);
        text-align: center;
        line-height: 20px;
        :last-child {
        font-size: 12px;
        color: var(--color-neutrals11);
        line-height: 18px;
        }
    }
    .activity-corpus-text-scroll {
        height: 100%;   
    }
    .activity-corpus-text-content {
        font-size: 13px;
        line-height: 26px;
        font-weight: 400;
        color: var(--color-neutrals19);
        white-space: pre-line;
    }
 }
</style>
