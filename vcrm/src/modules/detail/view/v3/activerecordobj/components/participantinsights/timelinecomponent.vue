<template>
  <div class="timeline-container">
    <div class="timeline-tracks">
      <div v-for="(userData, index) in comments" :key="index" class="timeline-user-track" style="position: relative;">
        <svg 
          v-if="activeUser === userData.user_id" 
          class="active-indicator" 
          xmlns="http://www.w3.org/2000/svg" 
          width="6" 
          height="27" 
          viewBox="0 0 6 27" 
          fill="none"
        >
          <path d="M0 1.18018C0 0.904034 0.223858 0.680176 0.5 0.680176H1.9C2.17614 0.680176 2.4 0.904033 2.4 1.18018V8.49022C2.4 9.57063 2.83704 10.6051 3.61169 11.3582L5.20368 12.906C5.62932 13.3198 5.60284 14.0111 5.14678 14.3912L3.47945 15.7806C2.79547 16.3506 2.4 17.195 2.4 18.0853V26.1802C2.4 26.4563 2.17614 26.6802 1.9 26.6802H0.5C0.223857 26.6802 0 26.4563 0 26.1802V1.18018Z" fill="#FF9B29"/>
        </svg>
        <div style="position: relative; display: inline-block;">
          <div
            class="user-label"
            :class="{
              'active': activeUser === userData.user_id,
              'their_side-label': userData.participant_types === 'their_side'
            }"
            @click="handleUserClick(userData)"
            style="z-index: 1;"
          >
            <sfaAiAvatar
             :showTooltip="true"
             :data="{
              dataId: userData._id,
              useAvatarImg: !!(userData.profile_image && userData.profile_image.length),
              userName: userData.user,
              backgroundColor: userData.avatar_bg_color,
              avatarImgSrc: userData.profile_image && userData.profile_image[0] ? userData.profile_image[0].signedUrl : '',
              personnelId:getPersonnelId(userData)
            }" />
            <span class="is_key_person" v-show="userData.primary_contact === '2'"> kp </span>
            <span class="user-label-text" 
            @mouseenter=" (showUserLabelTooltip = userData.user_id)"
            @mouseleave="showUserLabelTooltip = null">
              <span class="user_name">{{ userData.user }}</span>
              <span class="position" v-if="userData.job_title && userData.participant_types === 'their_side' ">-{{ userData.job_title }}</span>
            </span>
          </div>
        </div>
        <!-- 悬浮层，直接放在 .timeline-user-track 下方，绝对定位参考 .timeline-user-track -->
        <HoverCard
          :showContent="showUserLabelTooltip === userData.user_id "
          customClass="user-label-tooltip user-label"
        >
          <div>
            <sfaAiAvatar :data="{
              dataId: userData.user_id,
              userName: userData.user,
              useAvatarImg: !!(userData.profile_image && userData.profile_image.length),
              backgroundColor: userData.avatar_bg_color,
              avatarImgSrc: userData.profile_image && userData.profile_image[0] ? userData.profile_image[0].signedUrl : ''
            }" />
            <span class="is_key_person" v-show="userData.primary_contact === '2'" style="margin-left: 4px;"> kp </span>
            <span class="user_name" style="margin-left: 4px;">{{ userData.user }}</span>
            <span class="position" v-show="userData.job_title && userData.participant_types === 'their_side' " style="margin-left: 4px;">-{{ userData.job_title }}</span>
          </div>
        </HoverCard>
        <div class="attitude" v-show="userData.participant_types === 'their_side' && userData.attitude">
          <span class="attitude-icon" :class="[getAttitudeClass(userData.attitude), 'attitude-other']">
            <div 
              class="attitude-label-icon"
            ></div>
            {{ getAttitudeLabel(userData.attitude) }}
          </span>
        </div>
        <div class="participation-proportion">{{userData.participation_proportion ? userData.participation_proportion  + '%' : "--" }}</div>
        <div class="timeline-track" :ref="'track_' + index">
          <div
            v-for="(item, itemIndex) in userData.main"
            :key="itemIndex"
            class="timeline-segment"
            :style="getSegmentStyle(item.time, userData)"
            @click="handleClick(userData, item)"
          >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import sfaAiAvatar from '../../../base/components/sfaAiAvatar/sfaAiAvatar.vue'
import HoverCard from './hoverCard.vue'
export default {
  name: 'TimelineComponent',
  components: {
    sfaAiAvatar,
    HoverCard
  },
  props: {
    comments: {
      type: Array,
      required: true
    },
    startTime: {
      type: String,
      required: true
    },
    endTime: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      colorMap: new Map(),
      activeUser: null,
      attitudeConfig: {
        support: { label: $t('sfa.crm.timelinecomponent.support'), class: 'attitude-support' },
        neutrality: { label: $t('sfa.crm.timelinecomponent.neutrality'), class: 'attitude-neutral' },
        oppose: { label: $t('sfa.crm.timelinecomponent.oppose'), class: 'attitude-oppose' },
        other: { label: $t('sfa.crm.timelinecomponent.other'), class: 'attitude-other' }
      },
      showUserLabelTooltip: null, // 用于控制悬浮框显示
    }
  },
  watch: {
    comments: {
      immediate: true,
      handler(newComments) {
        if (newComments?.length) {
          // 默认选中我方参与人的第一项，如果没有则选择他方参与人的第一项
          const firstour_side = newComments.find(item => item.participant_types === 'our_side');
          const firsttheir_side = newComments.find(item => item.participant_types === 'their_side');
          const defaultUser = firstour_side || firsttheir_side;
          console.log(newComments);
          if (defaultUser) {
            this.handleUserClick(defaultUser);
          }

          // // 重新设置颜色映射
          // const colors = [
          //   '#1890ff', '#52c41a', '#722ed1', '#fa8c16', 
          //   '#eb2f96', '#faad14', '#13c2c2', '#f5222d'
          // ];
          // // 从评论数据中提取所有用户名
          // const users = newComments.map(item => item.user);
          
          // // 清空之前的颜色映射
          // this.colorMap.clear();
          
          // // 为每个用户分配一个颜色
          // // 使用 index % colors.length 可以循环使用颜色数组
          // // 例如:当用户数超过颜色数时,会重新从头开始分配颜色
          // users.forEach((user, index) => {
          //   this.colorMap.set(user, colors[index % colors.length]);
          // });
        }
      }
    }
  },
  created() {
    // 为不同用户分配不同颜色
    const colors = [
      '#1890ff', '#52c41a', '#722ed1', '#fa8c16', 
      '#eb2f96', '#faad14', '#13c2c2', '#f5222d'
    ];
    const users = this.comments.map(item => item.user);
    users.forEach((user, index) => {
      this.colorMap.set(user, colors[index % colors.length]);
    });
  },
  methods: {
     getPersonnelId(item) {
      return !!item.user_api_name && item.user_api_name !== 'ContactObj' ? item.user_id : ''
    },
    handleUserClick(userData) {
      this.activeUser = userData.user_id;
      this.$emit('user-click', userData);
    },
    // 将时间字符串转换为分钟数
    parseTimeToMinutes(timeStr) {
        const parts = timeStr.split(':').map(Number);
        if (parts.length === 3) {
          // hh:mm:ss
          return parts[0] * 60 + parts[1] + parts[2] / 60;
        } else if (parts.length === 2) {
          // mm:ss
          return parts[0] + parts[1] / 60;
        } else {
          // 其他格式，直接返回0或抛错
          return 0;
        }
    },
    // 解析时间段字符串
    parseTimeRange(timeRange) {
      const [start, end] = timeRange.split('-').map(time => time.trim());
      return {
        start: this.parseTimeToMinutes(start),
        end: this.parseTimeToMinutes(end)
      };
    },
    
    // 获取时间段样式
    getSegmentStyle(timeRange, user) {
      const totalDuration = this.parseTimeToMinutes(this.endTime);
      const { start, end } = this.parseTimeRange(timeRange);
      const startPosition = (start / totalDuration) * 100;
      const width = ((end - start) / totalDuration) * 100;
      
      return {
        left: `${startPosition}%`,
        width: `${width}%`,
        backgroundColor: user.avatar_bg_color || '#1890ff'
      };
    },
    
    // 处理点击事件
    handleClick(userData, item) {
      // 阻止事件冒泡，避免触发父元素的点击事件
      event.stopPropagation();
      // 发送事件通知父组件进行滚动
      this.$emit('segment-click', userData, item);
    },
    
    /**
     * 获取态度对应的标签文字
     */
    getAttitudeLabel(attitude) {
      return this.attitudeConfig[attitude]?.label || '';
    },

    /**
     * 获取态度对应的样式类名
     */
    getAttitudeClass(attitude) {
      return this.attitudeConfig[attitude]?.class || '';
    }
  },
  computed: {
    filteredTimelineData() {
      // 根据当前选择的类型过滤数据
      const filteredUsers = this.resultUserDataList.filter(user => user.participant_types === this.currentType);
      const timelineData = filteredUsers.map(user => ({
        ...user, // 保留用户的所有原始信息
        user: user.user_name, // 为了兼容现有的显示逻辑
        main: user.corpusEntries.map(entry => ({
          time: `${entry.startTime}-${entry.endTime}`,
          content: entry.content,
          originalData: entry
        }))
      }));
      return timelineData;
    },
    displayedTimelineData() {
      if (!this.showViewAllButton || this.isViewAll) {
        return this.filteredTimelineData;
      }
      return this.filteredTimelineData.slice(0, this.defaultDisplayCount);
    }
  }
}
</script>

<style lang="less" scoped>
.timeline-container {
  width: calc(100% - 24px);
  position: relative;
  padding: 12px;
  flex: 0 0 auto;
  border-radius: 8px;
  background: #FFF;
  margin-bottom: 12px;
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.timeline-tracks {
  display: flex;
  width: 100%;
  flex-direction: column;
  gap: 12px;
}
.participation-proportion{
  width: 25px;
  padding: 4px;
  font-size: 12px;
  font-weight: 700;
  color: var(--color-neutrals19);
  height: 18px;
  border-radius: 6px;
  background: var(--color-special01);
}

.timeline-user-track {
  display: flex;
  align-items: center;
  gap: 15px;
  position: relative;
  width: 100%;
}

.active-indicator {
  position: absolute;
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
}

.user-label {
  display: flex;
  width: 76px;
  height: 32px;
  align-items: center;
  gap: 3px;
  color: var(--color-neutrals19);
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s;
  margin-left: 6px;
  padding: 0 6px;
  overflow: hidden;
  white-space: nowrap;

  &.their_side-label {
    width: 125px;
  }

  .user_name{
    color: var(--color-neutrals19);
    font-size: 12px;
  }
}

.user-label .is_key_person {
  border-radius: 4px;
  border: 1px solid var(--color-special01);
  background: #FFF;
  display: flex;
  width: 14px;
  height: 12px;
  flex-direction: column;
  justify-content: center;
  color: var(--color-success06);
  text-align: center;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "Source Han Sans CN";
  font-size: 8px;
  font-style: normal;
  font-weight: 700;
  line-height: 18px;
}
.user-label .position {
 color: var(--color-neutrals11);
 font-size: 12px;
 font-style: normal;
 font-weight: 400;
 line-height: 18px;
}

.user-label.active {
  background: var(--color-primary01);
}

.timeline-track {
  flex: 1;
  height: 8px;
  background-color: #F2F4FB;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.timeline-segment {
  position: absolute;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 2px;
}

.timeline-segment:hover {
  filter: brightness(1.1);
  transform: scaleY(1.1);
}

.user-avatar,
.user-avatar-placeholder {
  width: 20px;
  height: 18px;
  margin-left: 8px;
  border-radius: 50%;
  margin-right: 4px;
  flex-shrink: 0;
}

.user-avatar-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 9px;
  font-weight: bold;
  text-transform: uppercase;
}

.attitude {
  margin: 0 8px;
}

.attitude-icon {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
}

.attitude-label-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}

.attitude-other {
  border-radius: 6px;
  display: flex;
  padding: 2px 4px;
  justify-content: center;
  align-items: center;
  gap: 2px;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  line-height: 18px; /* 150% */

  /* 默认或其他情况下的图标 */
  .attitude-label-icon {
  background: image-set(url("~@assets/images/activity/face_support.svg") 1x,
    url("~@assets/images/activity/face_support.svg") 2x) center center no-repeat;
  }
}

.attitude-support {
  border-radius: 6px;
  background: #F0FFF8;
  color: var(--teal-teal-05, #36C2B6);

  .attitude-label-icon {
  background: image-set(url("~@assets/images/activity/face_support.svg") 1x,
    url("~@assets/images/activity/face_support.svg") 2x) center center no-repeat;
  }
}

.attitude-neutral {
  border-radius: 6px;
  background: var(--color-warning01);
  color: var(--color-primary06);

  .attitude-label-icon {
   background: image-set(url("~@assets/images/activity/face_neutral.svg") 1x,
    url("~@assets/images/activity/face_neutral.svg") 2x) center center no-repeat;
  
  }
}

.attitude-oppose {
  background: var(--color-danger01);
  color: var(--color-primary06);

  .attitude-label-icon {
  background: image-set(url("~@assets/images/activity/face_oppose.svg") 1x,
    url("~@assets/images/activity/face_oppose.svg") 2x) center center no-repeat;
  }
}

.user-label.their_side-label {
  max-width: 125px;
  overflow: hidden;
  display: flex;
  align-items: center;
}
.user-label-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  max-width: 80px;
  vertical-align: middle;
}
.user-label-tooltip {
  /* 只要不是 .user-label 的子元素，宽度就不会被裁剪 */
  position: absolute;
  left: 0;
  top: 40px; /* 32px高度+margin */
  background: #fff;
  border: 1px solid #eee;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  padding: 4px 12px;
  border-radius: 6px;
  margin-top: 2px;
  z-index: 99;
  white-space: normal;
  min-width: 125px;
  width: auto;
  max-width: 400px;
  display: block;
  min-width: 125px;
  width: auto;
  max-width: 400px;
  display: block;
}
.user-label-tooltip > div {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  width: auto;
  margin-top: 2px;
}
</style> 
