<template>
  <div class="user-content" v-loading="loading">
    <p class="user-name">{{$t('sfa.crm.participantinsights.user_name.title')}}:
     <span class="user-name-text">{{ userData.user_name }}</span>
     <span 
              class="fx-icon-refresh"
              @click="getToAllNewInsights()"
      ></span>
    </p>
    <div v-if="userData" class="insight-list">
      <template v-if="filteredAndSortedInsights && filteredAndSortedInsights.length > 0 && !nowGenerateInsights">
        <div v-for="(insight, idx) in filteredAndSortedInsights" v-loading="insight.loading" :key="idx" class="insight-item">
        <div class="insight-type" :style="{ background: getInsightTypeConfig(insight.insight_type).color }">
          <span style="width: 14px;height: 14px;margin-top: -5px;">
            <span 
              :class="[
                insight.insight_type === 'invisible_concern' ? 'insight-icon-special' : getInsightTypeConfig(insight.insight_type).icon
              ]"
            ></span>
          </span>
          {{ getInsightTypeConfig(insight.insight_type).name }}
          <span v-if="insight.insight_type === 'attitude'" 
                class="attitude-icon" 
                :class="getAttitudeInfo(insight.attitude).class">
            <div 
              class="attitude-label-icon"
            ></div>
            {{ getAttitudeInfo(insight.attitude).label }}
          </span>
          <span v-if="insight.insight_type === 'sop_coverage' && insight.sop_coverage_rate"  class="sop-coverage-icon">
            {{ parseInt(insight.sop_coverage_rate ) + "%" || '' }}
          </span>
          <div class="insight-actions">
            <span 
              class="fx-icon-refresh"
              @click.stop="refreshInsightMessage(insight)"
            ></span >
            <span
              :class="[
                'fx-icon-unfold-2',
                { 'is-fold': !expandedItems[insight.insight_type] }
              ]"
              @click="toggleInsight(insight.insight_type)"
            ></span>
          </div>
        </div>
        <div 
          class="insight-result"
          v-if="insight.insight_result__o && insight.insight_type != 'personality'"
          :class="{ 'is-hidden': !expandedItems[insight.insight_type] }"
        >
          <div 
            class="insight-text" 
            :class="{ 'is-expanded': expandedTextItems[insight.insight_type] }"
            ref="insightText"
          >
          <template v-if="insight.insight_type != 'sop_coverage'">
            <li class="text-content" v-for="(item,index) in JSON.parse(insight.insight_result__o)" :key="index">
              {{ item.insightText }}
              <span 
              class="locate-source"
              @click.stop="$emit('locate-source', item)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="6" height="6" viewBox="0 0 6 6" fill="none">
                    <path d="M3.30811 4.66129C4.28108 4.19355 4.78378 3.30645 4.78378 2.79032C4.71892 2.83871 4.58919 2.87097 4.44324 2.87097C3.76216 2.87097 3.30811 2.35484 3.30811 1.69355C3.30811 1.06452 3.77838 0.5 4.57297 0.5C5.38378 0.5 6 1.25806 6 2.24194C6 3.83871 4.94595 4.96774 3.76216 5.5L3.30811 4.66129ZM0 4.66129C0.989189 4.19355 1.49189 3.30645 1.49189 2.79032C1.42703 2.83871 1.2973 2.87097 1.13514 2.87097C0.47027 2.87097 0.0162162 2.35484 0.0162162 1.69355C0.0162162 1.06452 0.47027 0.5 1.28108 0.5C2.09189 0.5 2.70811 1.25806 2.70811 2.24194C2.70811 3.83871 1.65405 4.96774 0.47027 5.5L0 4.66129Z" fill="#181C25"/>
                </svg>
             </span>

            </li> 
          </template>
          <template v-else>
            <li class="text-content-sop">
              <span>{{ $t('sfa.crm.participantinsights.confirmed_questions') }}</span>
              {{ JSON.parse(insight.insight_result__o) ?  (JSON.parse(insight.insight_result__o).confirmedQuestions || '') : '' }}
            </li>
            <li class="text-content-sop">
              <span>{{ $t('sfa.crm.participantinsights.unconfirmed_questions') }}</span>
                {{ JSON.parse(insight.insight_result__o) ? (JSON.parse(insight.insight_result__o).unconfirmedQuestions || '') : '' }}
            </li>
          </template>
          </div>
          <div 
            v-if="shouldShowExpand(insight.insight_type)" 
            class="expand-button"
            @click.stop="toggleExpandText(insight.insight_type)"
          >
            {{ expandedTextItems[insight.insight_type] ? $t('sfa.crm.participantinsights.collapse') : $t('sfa.crm.participantinsights.expand') }}
          </div>
        </div>
        <div v-else-if="insight.insight_result__o && insight.insight_type === 'personality' && JSON.parse(insight.insight_result__o).length">
          <div 
          class=" insight-result insight-result-personality"
          :class="{ 'is-hidden': !expandedItems[insight.insight_type] }"
          >
           <Personality
            :insight="insight"
            :userData="userData"
            @locate-source="$emit('locate-source', $event)"
            />
          </div>
        </div>
        <div class="epic-loading" v-else>
          <EpicAtomSpinner></EpicAtomSpinner>
        </div>
        </div>
      </template>
      <template v-if="!filteredAndSortedInsights.length && !nowGenerateInsights">
        <div  class="no-data">
            <div class="image-placeholder"></div>
            <div class="text">{{$t('crm.sfa.aiUseContent.nodata')}}</div>
        </div>
      </template>
      <template v-if="nowGenerateInsights">
       <EpicAtomSpinner></EpicAtomSpinner>
      </template>
    </div>
  </div>
</template>

<script>
import EpicAtomSpinner from '@/modules/components/epicAtomSpinner/epicAtomSpinner.vue';
import Personality from './personality.vue';
export default {
  name: 'UserContent',
  props: {
    selectedUser: {
      type: Object,
      default: null
    },
    dataId: {
      type: String,
      default: null
    },
    nowGenerateInsights: {
      type: Boolean,
      default: false
    }
  },
   components: {
    EpicAtomSpinner,
    Personality
  },
  data() {
    return {
      userData: null,
      insightList: [],  
      loading: false,
      expandedItems: {},
      expandedTextItems: {},
      textNeedsExpand: {},
      attitudeConfig: {
        support: { label: $t('sfa.crm.timelinecomponent.support'), class: 'attitude-support' },
        neutrality: { label: $t('sfa.crm.timelinecomponent.neutrality'), class: 'attitude-neutral' },
        oppose: { label: $t('sfa.crm.timelinecomponent.oppose'), class: 'attitude-oppose' },
        other: { label: $t('sfa.crm.timelinecomponent.other'), class: 'attitude-other' }
      },
      // 添加角色类型对应的洞察类型顺序配置
      insightTypes: {
        our_side: [
          'question_answer_performance',
          'dialogue_skill_performance',
          'sop_coverage',
          'sales_skill_evaluation',
          'improvement_suggestion'
        ],
        their_side: [
          'attitude',
          'focus_point',
          'competitor_info',
          "personality",
          'meeting_summary',
          'invisible_concern'
        ]
      },
      insightTypeConfig: {
        // our_side角色的洞察类型配置
        question_answer_performance: { color: '#F0F9FF', icon: 'fx-icon-f-mgt-coordinationmanage', name: $t('sfa.crm.participantinsights.question_answer_performance') },
        dialogue_skill_performance:  { color: '#FDF3EB', icon: 'fx-icon-f-list2', name: $t('sfa.crm.participantinsights.dialogue_skill_performance') },
        sop_coverage:                { color: '#FFFAE0', icon: 'fx-icon-f-obj-app200', name: $t('sfa.crm.participantinsights.sop_coverage') },
        sales_skill_evaluation:    { color: '#F0FFFB', icon: 'fx-icon-f-obj-app339', name: $t('sfa.crm.participantinsights.sales_skill_evaluation') },
        improvement_suggestion:    { color: '#F8F0FF', icon: 'fx-icon-f-product_hot', name: $t('sfa.crm.participantinsights.improvement_suggestion') },

        // their_side角色的洞察类型配置
        attitude:                  { color: '#F0F9FF', icon: 'fx-icon-f-biaoqing', name: $t('sfa.crm.participantinsights.attitude') },
        focus_point:               { color: '#FFFAF0', icon: 'fx-icon-f-app19', name: $t('sfa.crm.participantinsights.focus_point') },
        competitor_info:           { color: '#F0FFFB', icon: 'fx-icon-f-obj-app138', name: $t('sfa.crm.participantinsights.competitor_info') },
        meeting_summary:           { color: '#F8F0FF', icon: 'fx-icon-f-kuaisuhuifu', name: $t('sfa.crm.participantinsights.meeting_summary') },
        invisible_concern:         { color: '#F7FBF2 ', icon: 'fx-icon-f-biaoqing', name: $t('sfa.crm.participantinsights.invisible_concern') },
        personality:               { color: '#F2F4FB', icon: 'fx-icon-f-obj-app3', name: $t('性格') }
      },
      defaultInsightConfig: { color: '#F0F9FF', icon: 'fx-icon-f-biaoqing' },
      userLoadingMap: {}, // 新增：每个用户的 loading 状态
    }
  },
  computed: {
    // 修改计算属性，根据用户角色过滤和排序洞察内容
    filteredAndSortedInsights() {
      if (!this.userData || !this.insightList.length) return [];
      
      // 获取当前用户角色对应的洞察类型顺序
      const typeOrder = this.insightTypes[this.userData.participant_types] || [];
      if (!typeOrder.length) return this.insightList;
      // 过滤出当前角色可以显示的洞察类型
      console.log(this.insightList.filter(insight => typeOrder.includes(insight.insight_type))
        .sort((a, b) => {
          // 根据类型顺序排序
          return typeOrder.indexOf(a.insight_type) - typeOrder.indexOf(b.insight_type);
        }));
      return this.insightList.filter(insight => typeOrder.includes(insight.insight_type))
        .sort((a, b) => {
          // 根据类型顺序排序
          return typeOrder.indexOf(a.insight_type) - typeOrder.indexOf(b.insight_type);
        });
    },
    // 新增计算属性，用于获取态度配置
    getAttitudeInfo(a) {
      return (attitude) => {
        // 返回对应态度的配置，如果找不到则返回 'other' 的配置
        return this.attitudeConfig[attitude] || this.attitudeConfig.other;
      }
    },
  },
  watch: {
    selectedUser: {
      immediate: true,
      deep: true,
      handler(newUser) {
        if (newUser) {
          // 添加日志，检查传入的 selectedUser 数据结构 
          this.userData = {...newUser};
          this.loading = true;
          this.getUserInfo().finally(() => {
            this.loading = false;
          });
        }
      }
    }
  },
  methods:{
    /**
     * 获取用户洞察信息
     * our_side角色洞察类型顺序：
     * - question_answer_performance (问题回答表现)
     * - dialogue_skill_performance (对话技巧表现)
     * - sop_coverage (SOP 覆盖率)
     * - sales_skill_evaluation (销售技巧评价)
     * - improvement_suggestion (改进建议)
     * 
     * their_side角色洞察类型顺序：
     * - attitude (态度)
     * - focus_point (关注点)
     * - competitor_info (竞对信息)
     * - meeting_summary (会议总结)
     * - invisible_concern (隐形担忧)
     */
    getUserInfo(type){
      const _this = this;
      // this.nowGenerateInsights = false;
      _this.$emit('generate-insights-done', _this.userData.user_id); 
      // 模拟接口调用
      return new Promise((resolve,reject) => {
             CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/activity_attendees_insight/service/get_insights',
                    data: {
                        activityUserId: _this.userData._id,
                        activeRecordId: _this.dataId,
                        insightType: type ? type : void 0
                    },
                    success: (res) => {
                        const mockData = (res.Value?.dataList || []).map(item => ({...item, loading: false}));
                        if(type){
                          resolve(mockData);
                          return;
                        }
                        // 初始化展开状态，使用 insight_type 作为 key
                        const initialExpandedItems = {};
                        mockData.forEach((item) => {
                          if (item.insight_type) {
                            initialExpandedItems[item.insight_type] = true; // 默认展开
                          }
                        });
                        _this.insightList = mockData;
                        _this.expandedItems = initialExpandedItems;
                        _this.expandedTextItems = {}; // 重置文本展开状态
                        _this.textNeedsExpand = {}; // 重置文本需要展开状态
                        _this.checkTextNeedsExpand();
                        resolve(mockData);
                    },
                    error: (err) => {
                        _this.insightList = [];
                        reject(err);
                    }
                }, { errorAlertModel: 1 });
            
      });
        
    },
    getRefreshInfo(type){
      const _this = this;
     return new Promise((resolve,reject) => {
             CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/activity_attendees_insight/service/regenerate',
                    data: {
                        activityUserId: _this.userData._id,
                        activeRecordId: _this.dataId,
                        insightType: type
                    },
                    success: (res) => {
                      if (res.Result.StatusCode === 0) {
                          const mockData = (res.Value?.dataList || []).map(item => ({...item, insight_result__o: false}));
                          resolve(mockData);
                          return;
                      }else{
                        CRM.util.alert(res.Result.FailureMessage);
                        reject(res);
                      }   
                    },
                    error: (err) => {
                        reject(err);
                    }
                }, { errorAlertModel: 1 });
            
      });
    },
    getToAllNewInsights(){
      if(this.filteredAndSortedInsights && this.filteredAndSortedInsights.length > 0  && !this.nowGenerateInsights){
        this.$confirm($t('sfa.crm.participantinsights.refresh_insight_message'), $t('提示'), {
            confirmButtonText: $t('确定'),
            cancelButtonText: $t('取消'),
            type: 'warning',
          }).then((action,obj) => {
            this.getAllNewInsights();
          })
          return;
      }
      this.getAllNewInsights();
    },
    
    getAllNewInsights() {
      const _this = this;
      console.log(_this.userData);
      CRM.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/activity_attendees_insight/service/regenerate',
        data: {
          activityUserId: _this.userData._id,
          activeRecordId: _this.dataId,
        },
        success: (res) => {
          if (res.Result.StatusCode === 0) {
            // 这里不再设置 this.nowGenerateInsights
            _this.$emit('generate-insights', this.userData.user_id); // 通知父组件开始 loading
            return;
          } else {
            CRM.util.alert(res.Result.FailureMessage);
          }
        },
        error: (err) => {
          CRM.util.alert(err);
        }
      }, { errorAlertModel: 1 });
    },

    refreshInsightMessage(insightToRefresh){
       const me = this;
       const originalIndex = this.insightList.findIndex(item => item.insight_type === insightToRefresh.insight_type);
       if(originalIndex != -1 && this.insightList[originalIndex]?.insight_result__o){
           this.$confirm($t('sfa.crm.participantinsights.refresh_insight_message'), $t('提示'), {
            confirmButtonText: $t('确定'),
            cancelButtonText: $t('取消'),
            type: 'warning',
          }).then((action,obj) => {
            me.refreshInsight(insightToRefresh)
          })
          return;
       }
       this.refreshInsight(insightToRefresh)
    },
    /**
     * 点击刷新图标时触发，重新生成
     */
    refreshInsight(insightToRefresh) {
      const originalIndex = this.insightList.findIndex(item => item.insight_type === insightToRefresh.insight_type);
   
      this.getRefreshInfo(insightToRefresh.insight_type)
        .then(res => {
             // 先将insight_result__o置为空
            if (originalIndex !== -1) {
              this.$set(this.insightList[originalIndex], 'insight_result__o', null);
            }
        })
        .catch(error => {
          console.error('Failed to refresh insight:', error);
        })
    },
    toggleInsight(insightType) {
      this.$set(this.expandedItems, insightType, !this.expandedItems[insightType]);
    },
    /**
     * 获取洞察类型对应的配置（颜色和图标）
     */
    getInsightTypeConfig(type) {
      return this.insightTypeConfig[type] || this.defaultInsightConfig;
    },
    /**
     * 判断是否应该显示展开按钮
     */
    shouldShowExpand(insightType) {
      return this.textNeedsExpand[insightType] || false;
    },
    /**
     * 检查并更新文本是否需要展开
     */
    checkTextNeedsExpand() {
      this.$nextTick(() => {
        const textElements = this.$refs.insightText;
        const insights = this.filteredAndSortedInsights; // 获取当前渲染的洞察项
        if (!textElements || !insights || textElements.length !== insights.length) {
             // 如果 ref 数量和数据数量不匹配，可能还在渲染中或出错，暂时跳过
             return;
        }

        // 创建一个新的状态对象，避免直接修改旧对象导致不必要的更新
        const newTextNeedsExpand = { ...this.textNeedsExpand }; 

        textElements.forEach((el, index) => {
          const insightType = insights[index]?.insight_type;
          if (!insightType) return;

          const originalMaxHeight = el.style.maxHeight;
          el.style.maxHeight = 'none'; 
          const fullHeight = el.scrollHeight;
          el.style.maxHeight = originalMaxHeight; 

          // 更新对应 insightType 的状态
          newTextNeedsExpand[insightType] = fullHeight > 120; 
        });
        // 一次性更新状态
        this.textNeedsExpand = newTextNeedsExpand;
      });
    },
    /**
     * 切换文字展开状态
     */
    toggleExpandText(insightType) {
      this.$set(this.expandedTextItems, insightType, !this.expandedTextItems[insightType]);
    }
  }
}
</script>

<style lang="less" scoped>
.user-content {
  padding: 16px;
  background: var(--color-neutrals01);
  border-radius: 4px;
  width: calc(100% - 24px);
  padding: 12px;
  .sop-coverage-icon{
    display: flex;
    height: 18px;
    padding: 2px 4px;
    width: 25px;
    height: 14px;
    justify-content: center;
    align-items: center;
    color: var(--color-neutrals01);
    font-size: 12px;
    font-weight: 700;
    border-radius: 4px;
    background: var(--color-warning04);
  }
  .attitude-icon {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
}
.attitude-label-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}
.attitude-label-icon {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  display: inline-block;
}

.attitude-other {
  display: flex;
  padding: 2px 4px;
  justify-content: center;
  align-items: center;
  gap: 2px;
  text-align: center;
  font-size: 12px;
  font-style: normal;
  font-weight: 700;
  line-height: 18px; /* 150% */

  /* 默认或其他情况下的图标 */
  .attitude-label-icon {
  background: image-set(url("~@assets/images/activity/face_support.svg") 1x,
    url("~@assets/images/activity/face_support.svg") 2x) center center no-repeat;
  }
}

.attitude-support {
  border-radius: 4px;
  background: #F0FFF8;
  color: var(--teal-teal-05, #36C2B6);

  .attitude-label-icon {
  background: image-set(url("~@assets/images/activity/face_support.svg") 1x,
    url("~@assets/images/activity/face_support.svg") 2x) center center no-repeat;
  }
}

.attitude-neutral {
  border-radius: 4px;
  background: var(--color-warning01);
  color: var(--color-primary06);

  .attitude-label-icon {
   background: image-set(url("~@assets/images/activity/face_neutral.svg") 1x,
    url("~@assets/images/activity/face_neutral.svg") 2x) center center no-repeat;
  
  }
}

.attitude-oppose {
  background: var(--color-danger01);
  color: var(--color-primary06);

  .attitude-label-icon {
  background: image-set(url("~@assets/images/activity/face_oppose.svg") 1x,
    url("~@assets/images/activity/face_oppose.svg") 2x) center center no-repeat;
  }
}
  .user-name{
    color: var(--color-neutrals19);
    font-size: 13px;
    font-weight: 700;
    line-height: 18px;
    margin-bottom: 8px;
    .fx-icon-refresh{
      cursor: pointer;
      float: right;
    }
}

  .user-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--color-neutrals19);
    }

    .user-role {
      font-size: 12px;
      color: var(--color-neutrals11);
    }

    .key-person-tag {
      padding: 2px 8px;
      background: var(--color-warning01);
      color: var(--color-warning06);
      border-radius: 2px;
      font-size: 12px;
    }
  }

  .participation-info {
    font-size: 13px;

    .label {
      color: var(--color-neutrals11);
    }

    .value {
      color: var(--color-neutrals19);
      font-weight: 500;
    }
  }

  .insight-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
    .epic-loading{
      padding: 10px;
    }
  }

  .insight-item {
    border-radius: 8px;
    border: 2px solid var(--color-special01);

    .insight-type {
      position: relative;
      padding-right: 48px;
      font-size: 13px;
      font-weight: 700;
      color: #000;
      display: flex;
      gap: 8px;
      height: 18px;
      padding: 6px 12px;
      align-items: center;
      align-self: stretch;
      border-radius: 8px 8px 0px 0px;
      transition: background-color 0.3s ease;

      .insight-icon-special {
        width: 14px;
        height: 14px;
        display: inline-block;
        background: image-set(
          url("~@assets/images/activity/face_oppose.svg") 1x,
          url("~@assets/images/activity/face_oppose.svg") 2x
        ) center center no-repeat;
      }

      .insight-actions {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        gap: 8px; // 图标之间的间距

        span{
          cursor: pointer;
          transition: transform 0.3s;
          color: var(--color-neutrals11);
          font-size: 12px;

          &:hover {
            color: var(--color-primary06);
          }

          &.fx-icon-unfold-2.is-fold {
            transform: rotate(-90deg);
          }
        }
      }
    }

    .insight-result {
      font-size: 13px;
      padding: 12px;
      color: var(--color-neutrals19);
      line-height: 1.8;
      transition: all 0.3s;
      overflow: hidden;
      max-height: 1000px;
      opacity: 1;
      position: relative;
      
      &.is-hidden {
        max-height: 0;
        opacity: 0;
        margin: 0;
        padding: 0;
      }

      .insight-text {
        max-height: 119px; // 大约5行文字的高度 (1.5 * 16px * 5)
        overflow: hidden;
        transition: max-height 0.3s ease;
        
        &.is-expanded {
          max-height: none;
        }

        .text-content {
          margin-right: 8px;
        }

        .locate-source {
          cursor: pointer;
          font-size: 12px;
          display: inline-block;
          white-space: nowrap;
          width: 14px;
          height: 14px;
          align-items: center;
          text-align: center;
          line-height: 12px;
          gap: 10px;
          border-radius: 22px;
          background: #F5F6F9;
          
          &:hover {
            opacity: 0.8;
          }
        }
      }

      .expand-button {
        color: var(--color-info06);
        cursor: pointer;
        font-size: 12px;
        display: block;
        margin-top: 4px;
        
        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
 .no-data {
      width: 100%;
      border-radius: 8px;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 265px;
      text-align: center;
      font-family: Arial, sans-serif;
      .image-placeholder {
            width: 150px;
            height: 120px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            background: image-set(url("~@assets/images/nodata2.png") 1x,
                    url("~@assets/images/<EMAIL>") 2x) center center no-repeat;
        }
        .text {
            color: var(--Text-H2, #545861);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
        .reload {
            color: var(--Text-Blue, #0C6CFF);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
    }

.fx-icon-f-biaoqing::before {
  color: #368DFF;
}
.fx-icon-f-mgt-coordinationmanage::before {
  color: #368DFF;
}
.fx-icon-f-obj-app3::before {
  color: #927EE8;
}
.fx-icon-f-list2::before {
  color: #FF7383;
}
.fx-icon-f-obj-app200::before {
  color: #FFB56B;
}
.fx-icon-f-obj-app339::before {
  color: #36C2B6;
}
.fx-icon-f-product_hot::before {
  color: #976AEB;
}
.fx-icon-f-app19::before {
  color: #FFCA2B;
}
.fx-icon-f-obj-app138::before {
  color: #16B4AB;
}
.fx-icon-f-kuaisuhuifu::before {
  color: #976AEB;
}
</style>