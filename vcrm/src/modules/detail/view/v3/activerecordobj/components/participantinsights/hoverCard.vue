<template>
  <div v-if="showContent" :class="['hover-card', customClass]" :style="cardStyle">
    <slot v-if="$slots.default"></slot>
    <template v-else>{{ content }}</template>
  </div>
</template>

<script>
export default {
  name: 'HoverCard',
  props: {
    showContent: {
      type: Boolean,
      default: false
    },
    customClass: {
      type: String,
      default: ''
    },
    content: {
      type: [String, Object],
      default: ''
    },
    left: {
      type: [String, Number],
      default: '0'
    },
    top: {
      type: [String, Number],
      default: '40px'
    }
  },
  computed: {
    cardStyle() {
      return {
        position: 'absolute',
        left: typeof this.left === 'number' ? this.left + 'px' : this.left,
        top: typeof this.top === 'number' ? this.top + 'px' : this.top,
        background: '#fff',
        border: '1px solid #eee',
        boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
        padding: '4px 12px',
        borderRadius: '6px',
        zIndex: 99,
        whiteSpace: 'normal',
        minWidth: '125px',
        width: 'auto',
        maxWidth: '400px',
        display: 'block',
      };
    }
  }
}
</script>

<style scoped>
.hover-card {
  /* 额外样式可自定义 */
}
</style> 