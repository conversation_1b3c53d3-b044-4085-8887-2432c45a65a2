<!--
 * @Descripttion:
 * @Author: LiAng
 * @Date: 2024-12-27 11:00:05
 * @LastEditors: LiAng
 * @LastEditTime: 2025-05-07 21:00:47
-->
<template>
    <div class="activity-corpus-audio-wrapper" ref="audio-wrapper">
        <activityCorpusList
            ref="activityCorpusListRef"
            v-loading="listLoading"
            :items="identifyList"
            :extend-data="extendData"
            :data-id="dataId"
            :api-name="apiName"
            :comp-info="compInfo"
            :identifying-data="identifyingData"
            :translate-data="translateData"
            :activity-action-state="interactiveProcesses"
            :avatar-color-list="avaterColorList"
        ></activityCorpusList>
        <div ref="recorder" class="activity-corpus-audio-recorder"></div>
    </div>
</template>
<script>
import activityCorpusList from '../../../../../base/components/activityCorpusList'
import corpusUserNameI18n from '../../../../../base/components/sfaAiMixin/corpusUserNameI18n'

const INTERACTIVE_PROCESS = {
    PROCESSING: '1', // 进行中
    FINISHED: '2', // 已完成
    PAUSED: '3', // 已暂停
    INSIGHT_COMPLETED: '4' // 已洞察
}

const AUTHORIZATION_DURATION = 60 * 60 * 12; // 音频授权播放时长12小时

export default {
    name: 'activity-corpus-audio',
    props: ["dataId", "apiName", "compInfo", "pluginService", "extendData", "$context"],
    provide() {
        return {
            $context: this.$context
        }
    },
    mixins: [corpusUserNameI18n],
    data() {
        return {
            identifyList: [],
            identifyingData: '',
            translateData: '',
            recorderInstance: null,
            playerInstance: [],
            listLoading: false,
            refreshByMe: false,
            resizeTimer: null,
            interactiveProcesses: this.$context.getData()?.interactive_processes,
            avaterColorList: []
        }
    },
    computed: {
        /**
         * 处理用户列表数据
         * 1. 从 identifyList 中提取用户信息并去重
         * 2. 用于子组件展示和用户 ID 计算
         * @returns {Array} 格式化后的用户列表
         */
        userItems() {
            return [...new Set(this.identifyList
                .filter(item => item.userName)
                .map(item => item.userName))]
                .map(userName => {
                    const item = this.identifyList.find(i => i.userName === userName);
                    return {
                        ...item,
                        label: userName
                    };
                });
        }
    },
    components: { activityCorpusList },
    created() {
        this.initRecorder();
        // if (!this.extendData?._activityAudioConfig) {
        // if (this.interactiveProcesses !== INTERACTIVE_PROCESS.PROCESSING) {
        //     this.getItems()
        // }
        this.getItems()
        this.$context.$on('corpus.list.refresh', this.handleCorpusListRefresh)
    },
    mounted () {
        this.initMinPopContent();

        // this.$nextTick(() => {
        //     this.computeWrapperHeight();
        //     window.addEventListener('resize', this.computeWrapperHeight);
        // })
    },
    methods: {
        handleCorpusListRefresh(data) {
            // 如果data没有refreshType属性或为空，则进行全局刷新
            if (!data || !data.refreshType) {
                if (this.interactiveProcesses === INTERACTIVE_PROCESS.PROCESSING) {
                    // 保持原有逻辑，不变动
                    if (data && data.userId) {
                        this.identifyList = this.identifyList.map(item =>
                            data.userId === item.userId ? Object.assign(item, data) : item
                        )
                    }
                } else {
                    this.getItems()
                }
                return;
            }

            // 处理不同的刷新类型
            switch (data.refreshType) {
                case 'single':
                    // 处理单条编辑 (原来的corpus.list.item.edit-username)
                    this.$set(this.identifyList, data.itemIndex, Object.assign(this.identifyList[data.itemIndex], {
                        userApiName: data.userApiName,
                        userName: data.userName,
                        nameAvaId: data.nameAvaId,
                        isDefaultSpeaker: false,
                        profileImage: data.profileImage || []
                    }));
                    break;
                case 'global':
                    // 处理全局更新
                    if (this.interactiveProcesses === INTERACTIVE_PROCESS.PROCESSING) {
                        if (data.userId) {
                            this.identifyList = this.identifyList.map(item =>
                                data.userId === item.userId ? Object.assign(item, data, {
                                    userId: data.targetUserId
                                }) : item
                            )
                        }
                    } else {
                        this.getItems()
                    }
                    break;
                default:
                    // 默认刷新
                    this.getItems();
            }
        },
        initRecorder() {
            let me = this;
            Fx.getBizComponent('CRM', 'recorder').then((recorder) => {
                if (this.interactiveProcesses === INTERACTIVE_PROCESS.PROCESSING && this.extendData._activityAudioConfig) {
                    // 删除无用参数
                    delete this.extendData._activityAudioConfig.translation

                    let activityAudioConfig = Object.assign({
                        objectId: '',
                        sourceLanguage: 'cn',
                        translationEnabled: true,
                        targetLanguages: 'en',
                        diarizationEnabled: true
                    }, this.extendData._activityAudioConfig);

                    this.recorderInstance = recorder.record({
                        wrapper: this.$refs.recorder,
                        identifyBegin: this.identifyBegin,
                        identifying: this.identifying,
                        identifyEnd: this.identifyEnd,
                        identifyChange: this.identifyChange,
                        identifyTranslated: this.identifyTranslated,
                        startRecord: () => {
                            this.bindBeforeUnload();
                            me.$context.setData('sfaActivityActionState', INTERACTIVE_PROCESS.PROCESSING);
                            this.$context.$emit('activity.actionState.change.after', INTERACTIVE_PROCESS.PROCESSING)
                        },
                        stopRecord: () => {
                            this.unBindBeforeUnload();
                            me.$context.setData('sfaActivityActionState', INTERACTIVE_PROCESS.FINISHED);
                            this.$context.$emit('activity.actionState.change.after', INTERACTIVE_PROCESS.FINISHED);
                            this.refreshByMe = true;
                            // 关闭弹窗详情页
                            this.$context.$emit('root.hide');
                            // 打开侧滑详情页
                            CRM.api.show_crm_detail({
                                apiName: this.apiName,
                                modal: false,      // 不显示遮罩
                                data: { crmId: this.dataId },
                            });
                        },
                        pauseRecord: () => {
                            me.$context.setData('sfaActivityActionState', INTERACTIVE_PROCESS.PAUSED);
                            this.$context.$emit('activity.actionState.change.after', INTERACTIVE_PROCESS.PAUSED)
                        },
                        continueRecord: () => {
                            me.$context.setData('sfaActivityActionState', INTERACTIVE_PROCESS.PROCESSING);
                            this.$context.$emit('activity.actionState.change.after', INTERACTIVE_PROCESS.PROCESSING)
                        },
                        failedRecord: (data) => {
                            me.$context.setData('sfaActivityActionState', INTERACTIVE_PROCESS.FAILED);
                            this.$context.$emit('activity.actionState.change.after', INTERACTIVE_PROCESS.FAILED)
                        },
                        propsData: {
                            enableRecord: true,
                            objectId: this.dataId,
                            objectApiName: this.apiName,
                            repeatRecord: false,
                            wsConfig: Object.assign({}, activityAudioConfig, {
                                objectId: this.dataId
                            }),
                            beforeSaveResult: (result) => {
                                result.map(item => {
                                    if (this.hasSpeakerCache(item.ui)) {
                                        const catchUser = this.getSpeakerCache(item.ui);
                                        const targetUser = {
                                            userId: catchUser.userId,
                                            userName: catchUser.userName,
                                            userApiName: catchUser.userApiName,
                                            isDefaultSpeaker: catchUser.isDefaultSpeaker,
                                            nameAvaId: catchUser.nameAvaId,
                                            profile_image: catchUser.profile_image,
                                        }
                                        return Object.assign(item, targetUser);
                                    }  else {
                                        let changeableUser = this.identifyList.find(cItem => item.seq === cItem.seq && !cItem.isDefaultSpeaker)
                                        if (changeableUser) { // 修改单条发言人后需要保存数据
                                            const targetUser = {
                                                userId: changeableUser.userId,
                                                userName: changeableUser.userName,
                                                userApiName: changeableUser.userApiName,
                                                isDefaultSpeaker: changeableUser.isDefaultSpeaker,
                                                nameAvaId: changeableUser.nameAvaId,
                                                profile_image: changeableUser.profile_image,
                                            }
                                            return Object.assign(item, targetUser)
                                        }
                                        return item
                                    }
                                })
                                console.log(result, 'beforeSaveResult')
                                return result;
                            }
                            // wsConfig: {
                            //     // 销售记录 ID，用于标识需要进行实时处理的销售记录。
                            //     objectId: this.dataId,
                            //     // 音视频的语言类型。• 如果语音是单语种，可配置 cn（中文）、en（英文）、yue（粤语）、ja（日语）、ko（韩语）。• 如果包含多个语种，可设置为 multilingual，并结合其他参数根据实际情况识别对应语种文本。
                            //     sourceLanguage: activityAudioConfig.sourceLanguage,
                            //     // 是否开启翻译功能。• true：开启翻译。• false：关闭翻译。
                            //     translationEnabled: activityAudioConfig.translationEnabled,
                            //     // 翻译的目标语言（当翻译功能开启时必填）。可设置的目标语言包括：• cn（中文）• en（英语）• ja（日语）• ko（韩语）• de（德语）• fr（法语）• ru（俄语）
                            //     targetLanguages: activityAudioConfig.targetLanguages,
                            //     // 是否开启说话人分离功能。• true：在语音识别过程中区分不同说话人。• false：不区分。
                            //     diarizationEnabled: activityAudioConfig.diarizationEnabled
                            // }
                        }
                    })

                    this.onRefresh();
                }
                else {
                    this.getAudioUrl((urls = []) => {
                        if (urls.length) {
                            urls.forEach((url) => {
                                let player = recorder.player({
                                    wrapper: this.$refs.recorder,
                                    $context: this.$context,
                                    propsData: {
                                        enablePlay: true,
                                        audioUrl: url,
                                        objectId: this.dataId,
                                        objectApiName: this.apiName,
                                    }
                                });

                                this.playerInstance.push(player);
                            })
                        }
                        else {
                            let player = recorder.player({
                                wrapper: this.$refs.recorder,
                                $context: this.$context,
                                propsData: {
                                    enablePlay: true,
                                    audioUrl: '',
                                    objectId: this.dataId,
                                    objectApiName: this.apiName,
                                }
                            });

                            this.playerInstance.push(player);
                        }
                    })
                }

                this.onBeforeHide();
            })
        },
        onBeforeHide() {
            this.$context.tapHook('root.beforeHide', 'activity-corpus-audio', (next) => {
                this.$nextTick(() => {
                    if (this.recorderInstance) {
                        this.recorderInstance.stop(() => {
                            next();
                        })
                    }
                    else if (this.playerInstance.length) {
                        this.playerInstance.forEach(item => {
                            item.pause && item.pause();
                        });

                        next();
                    }
                    else {
                        next();
                    }
                })
            });
        },
        onRefresh() {
            this.$context.tapHook('root.beforeRefresh', 'activity-corpus-audio', (next)=> {
                if (!this.refreshByMe) {
                    return;
                }
                this.extendData._activityAudioConfig = null;
                // this.extendData._activityActionState = null;
                next();
            });
        },
        bindBeforeUnload() {
            this.unBindBeforeUnload();

            $(window).on('beforeunload.activity-corpus-audio', (event) => {
                event.preventDefault();
                event.returnValue = '';
            });
        },
        unBindBeforeUnload() {
            $(window).off('beforeunload.activity-corpus-audio');
        },
        identifying(data) {
            this.identifyingData = data._formatData.content;
        },
        /**
         * 格式化语音识别结果数据
         * @param {Object} obj - 原始识别数据
         * @returns {Object} - 格式化后的数据对象
         */
        formatIdentifyData(obj) {
            let targetUser = this.getSpeakerCache(obj.ui);
            let userName = !!this.hasSpeakerCache(obj.ui)
                ? targetUser.userName
                : `${$t(`sfa.activity.corpus.list_item_user_label`, null, `发言人`)}${obj.ui}`

            let pushData = {
                id: obj.id,
                seq: obj.seq,
                userId: obj.ui,
                userName: userName,
                originalUserId: obj.ui,
                originalUserName: obj.ui,
                startTime: obj.st,
                endTime: obj.et,
                content: obj.content,
                message_id: obj.message_id,
                nameAvaId: this.getNameAvaId(userName),
                isDefaultSpeaker: obj.isDefaultSpeaker
            };

            // 如果targetUser存在，添加额外的用户信息
            if (targetUser) {
                Object.assign(pushData, {
                    userName: targetUser.userName,
                    nameAvaId: targetUser.nameAvaId,
                    isDefaultSpeaker: targetUser.isDefaultSpeaker,
                    userApiName: targetUser.userApiName
                });
            }

            return pushData;
        },

        identifyEnd(data) {
            const formattedData = this.formatIdentifyData(data._formatData);
            this.identifyList.push(formattedData);
            this.identifyingData = '';
            this.translateData = '';
        },
        identifyChange(data, fields = []) {
            fields = fields.length ? fields : this.identifyList.length ? Object.keys(this.identifyList[0]) : [];

            data.forEach(item => {
                this.identifyList.some((cItem) => {
                    if (item.seq === cItem.seq) {
                        fields.forEach(field => {
                            cItem[field] = item[field];
                        })
                        return true;
                    }
                })
            })
        },
        identifyTranslated(data) {
            if (data.translatePartialContent) {
                this.translateData = data.translatePartialContent;
            }

            if (data.message_id && data.translateContent) {
                this.identifyList.some((item, index) => {
                    if (item.message_id === data.message_id) {
                        item.translateContent = data.translateContent;

                        return true;
                    }
                })
            }
        },
        getItems() {
            this.listLoading = true;
            let _this = this
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/activity_text/service/full_text_list',
                data: {
                    objectId: _this.dataId,
                    // objectId: '677b45ffd7b6a20001c9f8f4',
                    tenantId: CRM.ea,
                    offset: 0,
                    limit: 2000
                },
                success: (res) => {
                    // console.log(res, 'res')
                    _this.avaterColorList = res.Value?.avaterColorList || []
                    _this.identifyList = res.Value?.dataList.map(item => {
                        let __user = item.userName ? item.userName.split('_') : []
                        let userName = item.userName
                        if (!!__user.length && __user[0] == 'user') {
                            userName = _this.getUserNameI18n(userName)
                            // 批量修改发言人需要
                            item.isDefaultSpeaker = true
                        }
                        return !!userName ? Object.assign({}, item, { userName, __originalUserName: userName }) : item
                    }) || []
                    // _this.identifyList = []
                },
                complete: () => {
                    _this.listLoading = false;
                }
            }, { errorAlertModel: 1 })
        },

        getAudioUrl(cb) {
            let urls = [];
            let interaction_records = this.$context.getData()?.interaction_records;

            if (!interaction_records || !interaction_records.length) {
                cb && cb(urls);
                return;
            }

            let index = 0;

            interaction_records.forEach(item => {
                if (index >= interaction_records.length) {
                    return;
                }

                CRM.api.alioss_getdownloadpath({
                    fileName: item.filename,
                    path: item.path,
                    duration: AUTHORIZATION_DURATION,
                    successCb(url) {
                        urls.push(url);
                        index++;

                        if (index >= interaction_records.length) {
                            cb && cb(urls);
                        }
                    },
                    failCb() {
                        index++;

                        if (index >= interaction_records.length) {
                            cb && cb(urls);
                        }
                    }
                });
            });
        },
        initMinPopContent () {
            const content = Vue.component('recording-min-pop-bg-image', {
                template: `<div class="recording-min-pop-bg-image"></div>`
            })
            this.$context.tapHook('root.popMiniBefore', this.apiName, (next)=> {
                const addComponentToPopMini = this.$context.addComponentToPopMini;
                addComponentToPopMini(content);
                next();
            });
        },
        getSpeakerCache(userId) {
            return this.$refs.activityCorpusListRef.getSpeakerCache(userId);
        },
        hasSpeakerCache(userId) {
            return this.$refs.activityCorpusListRef.hasSpeakerCache(userId);
        },
        getNameAvaId(userName) {
            return this.$refs.activityCorpusListRef.getNameAvaId(userName);
        },
    },
    beforeDestroy() {
        // window.removeEventListener('resize', this.computeWrapperHeight);
        // if (this.resizeTimer) {
        //     clearTimeout(this.resizeTimer);
        // }
        this.recorderInstance && this.recorderInstance.destroy && this.recorderInstance.destroy();
        this.playerInstance.forEach(item => {
            item.destroy && item.destroy();
        });
        this.unBindBeforeUnload();
    }
}
</script>

<style lang="less" scoped>
.activity-corpus-audio-wrapper {
    display: flex;
    flex-direction: column;
    // padding-right: 12px;
    // min-height: 530px;
    height: 100%;
}
</style>

<style lang="less">
.el-min-bar .recording-min-pop-bg-image {
    width: 168px;
    height: 89px;
    background: image-set(url("~@assets/images/activity/recording_minPop_bg.svg")) center center no-repeat;
}
</style>
