export default {
    data() {
      return {
        showAnswerEditBox: false,
        answerEditBoxTimer: null,
        showAnswerEditBox2: false,
        answerEditBoxTimer2: null,
        showEditTip: false,
        showEditTip2: false,
        showEditTitle: true,
        showEditTitle2: true,
      };
    },
    methods: {
        /**
         * 保存数据
         */
        handleEditContentSave(itemData, content, type) {
            console.log(itemData, content, 'itemData, content');
            // Check if content is empty or only contains whitespace
            if (!content || content.trim() === '') {
                // Show error tip
                if(type == 'singleEdit') {
                    this.showEditTip2 = true;
                }else{
                    this.showEditTip = true;
                }
                // Keep edit mode active
                this.$nextTick(() => {
                    if(type == 'singleEdit') {
                        if (this.$refs.answerEditBoxRef2?._editable?.triggerEdit) {
                            this.$refs.answerEditBoxRef2._editable.triggerEdit();
                            this.showEditTitle2 = false;
                        }
                    }else{
                        if (this.$refs.answerEditBoxRef?._editable?.triggerEdit) {
                            this.$refs.answerEditBoxRef._editable.triggerEdit();
                            this.showEditTitle = false;
                        }
                    }
                });

                // Return a rejected promise to prevent saving
                return Promise.reject(new Error('Content cannot be empty'));
            }
            // Hide error tip if content is not empty
            if(type == 'singleEdit') {
                this.showEditTip2 = false;
            }else{
                this.showEditTip = false;
            }
            // Proceed with saving if content is not empty
            return this.$emit('edit-content-save', itemData, content, type, (answer_summary) => {
                // 更新数据
                this.updateItemData(answer_summary, type);

                // 保存成功后退出编辑状态
                this.$nextTick(() => {
                    if(type == 'singleEdit') {
                        if (this.$refs.answerEditBoxRef2?._editable?.cancel) {
                            this.$refs.answerEditBoxRef2._editable.cancel();
                            this.showEditTitle2 = true;
                        }
                    } else {
                        if (this.$refs.answerEditBoxRef?._editable?.cancel) {
                            this.$refs.answerEditBoxRef._editable.cancel();
                            this.showEditTitle = true;
                        }
                    }
                });
            }).catch(error => {
                // Keep edit state
                this.$nextTick(() => {
                    if(type == 'singleEdit') {
                        if (this.$refs.answerEditBoxRef2?._editable?.triggerEdit) {
                            this.$refs.answerEditBoxRef2._editable.triggerEdit();
                            this.showEditTitle2 = false;
                        }
                    }else{
                        if (this.$refs.answerEditBoxRef?._editable?.triggerEdit) {
                            this.$refs.answerEditBoxRef._editable.triggerEdit();
                            this.showEditTitle = false;
                        }
                    }
                });
                throw error;
            }).finally(() => {

            });
        },
        /**
         * 取消错误提示
         */
        handleEditContentCancel(itemData) {
            this.showEditTip = false;
            this.showEditTip2 = false;
            this.showAnswerEditBox = false;
            this.showAnswerEditBox2 = false;
            this.showEditTitle = true;
            this.showEditTitle2 = true;
        },
        /**
         * 更新数据
         */
        updateItemData(answer_summary, type) {
            if(type == 'singleEdit') {
                this.itemData.activity_topic_answer.answer_summary = answer_summary;
                this.itemData.activity_topic_answer.answer_summary__o = answer_summary;
            }else{
                this.itemData.answer_summary = answer_summary;
            }
        },
        /**
         * 切换编辑模式
         */
        toggleEditMode(event, type) {
            if (!this.useAnswerEdit) return;
            event.stopPropagation();

            if(type == 'singleEdit') {
                this.showAnswerEditBox2 = false;
                this.showEditTitle2 = false;
                this.$nextTick(() => {
                    this.$refs.answerEditBoxRef2?._editable?.triggerEdit && this.$refs.answerEditBoxRef2?._editable?.triggerEdit();
                });

            }else{
                this.showAnswerEditBox = false;
                this.showEditTitle = false;
                this.$nextTick(() => {
                    this.$refs.answerEditBoxRef?._editable?.triggerEdit && this.$refs.answerEditBoxRef?._editable?.triggerEdit();
                });
            }
        },
        /**
         * 处理第一个答案编辑框的鼠标进入事件
         */
        handleAnswerMouseEnter() {
            if (!this.useAnswerEdit) return;
            // 先取消之前的计时器（如果有）
            this.cancelAnswerEditBoxTimer();

            // 设置一个新的延迟计时器
            this.answerEditBoxTimer = setTimeout(() => {
            this.showAnswerEditBox = true;
            }, 200); // 延迟200毫秒显示
        },

        /**
         * 处理第一个答案编辑框的鼠标离开事件
         */
        handleAnswerMouseLeave() {
            if (!this.useAnswerEdit) return;
            // 清除延迟计时器
            this.cancelAnswerEditBoxTimer();

            // 立即隐藏按钮
            this.showAnswerEditBox = false;
        },

        /**
         * 取消第一个答案编辑框的计时器
         */
        cancelAnswerEditBoxTimer() {
            if (this.answerEditBoxTimer) {
            clearTimeout(this.answerEditBoxTimer);
            this.answerEditBoxTimer = null;
            }
        },

        /**
         * 处理第二个答案编辑框的鼠标进入事件
         */
        handleAnswerMouseEnter2() {
            if (!this.useAnswerEdit) return;
            // 先取消之前的计时器（如果有）
            this.cancelAnswerEditBoxTimer2();

            // 设置一个新的延迟计时器
            this.answerEditBoxTimer2 = setTimeout(() => {
            this.showAnswerEditBox2 = true;
            }, 200); // 延迟200毫秒显示
        },

        /**
         * 处理第二个答案编辑框的鼠标离开事件
         */
        handleAnswerMouseLeave2() {
            if (!this.useAnswerEdit) return;
            // 清除延迟计时器
            this.cancelAnswerEditBoxTimer2();

            // 立即隐藏按钮
            this.showAnswerEditBox2 = false;
        },

        /**
         * 取消第二个答案编辑框的计时器
         */
        cancelAnswerEditBoxTimer2() {
            if (this.answerEditBoxTimer2) {
            clearTimeout(this.answerEditBoxTimer2);
            this.answerEditBoxTimer2 = null;
            }
        },
    },
    beforeDestroy() {
        // 清理计时器
        this.cancelAnswerEditBoxTimer();
        this.cancelAnswerEditBoxTimer2();
    },
};
