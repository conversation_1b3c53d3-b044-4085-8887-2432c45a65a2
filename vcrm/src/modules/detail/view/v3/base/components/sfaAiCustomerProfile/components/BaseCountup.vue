<!--
 * 参考实现 https://github.com/xlsdg/vue-countup-v2/blob/master/src/countup.vue
-->
<template>
    <span />
</template>

<script>
import { CountUp } from 'countup.js';

export default {
    __countup__: CountUp,
    name: 'VueCountUp',
    props: {
        delay: {
            type: Number,
            required: false,
            default: 0,
        },
        endVal: {
            type: Number,
            required: true,
        },
        options: {
            type: Object,
            required: false,
        },
    },
    data() {
        return {
            instance: null,
        };
    },
    watch: {
        endVal: {
            handler(value) {
                const that = this;

                if (that.instance && _.isFunction(that.instance.update)) {
                    that.instance.update(value);
                }
            },
            deep: false
        }
    },
    methods: {
        create() {
            const that = this;
            if (that.instance) {
                return;
            }

            const dom = that.$el;
            const instance = new CountUp(
                dom,
                that.endVal,
                that.options
            );

            if (instance.error) {
                // error
                return;
            }

            that.instance = instance;

            if (that.delay < 0) {
                that.$emit('ready', instance, CountUp);
                return;
            }

            setTimeout(() => instance.start(() => that.$emit('ready', instance, CountUp)), that.delay);
        },
        destroy() {
            const that = this;
            that.instance = null;
        },
        printValue(value) {
            const that = this;

            if (that.instance && _.isFunction(that.instance.printValue)) {
                return that.instance.printValue(value);
            }
        },
        start(callback) {
            const that = this;

            if (that.instance && _.isFunction(that.instance.start)) {
                return that.instance.start(callback);
            }
        },
        pauseResume() {
            const that = this;

            if (that.instance && _.isFunction(that.instance.pauseResume)) {
                return that.instance.pauseResume();
            }
        },
        reset() {
            const that = this;

            if (that.instance && _.isFunction(that.instance.reset)) {
                return that.instance.reset();
            }
        },
        update(newEndVal) {
            const that = this;

            if (that.instance && _.isFunction(that.instance.update)) {
                return that.instance.update(newEndVal);
            }
        }
    },
    mounted() {
        const that = this;
        that.create();
    },
    beforeDestroy() {
        const that = this;
        that.destroy();
    },
};
</script>