<script>
import with<PERSON><PERSON><PERSON><PERSON> from '../mixins/withBase<PERSON>hart'

export default {
    mixins: [withB<PERSON><PERSON><PERSON>],
    props: {
        chartDataUnit: {
            type: String,
            default: ''
        }
    },
    methods: {
        getChartOption() {
            const value = this.chartData;
            const percent = value / 100;
            return {
                series: [
                    {
                        type: "gauge",
                        center: ['50%', '100%'],
                        startAngle: 180,
                        endAngle: 0,
                        min: 0,
                        max: 100,
                        radius: '200%',
                        pointer: {
                            show: false
                        },
                        axisLine: {
                            lineStyle: {
                                width: 14,
                                color: [
                                    [percent, {
                                        type: "linear",
                                        x: 0,
                                        y: 0,
                                        x2: 1,
                                        y2: 0,
                                        colorStops: [
                                            { offset: 0, color: "#FFB452" },
                                            { offset: 1, color: "#FF8000" },
                                        ],
                                    }],
                                    [1, "#FFDDA3"],
                                ],
                            },
                        },
                        progress: { show: false },
                        axisTick: { show: false },
                        splitLine: { show: false },
                        axisLabel: { show: false },
                        detail: {
                            show: false,
                        },
                        data: [{ value }],
                        z: 1,
                    },
                ],
            }
        }
    }
}
</script>