<template>
  <div class="chart-wrapper" ref="chartWrapper">
    <svg :width="width" :height="height" class="chart-svg">
      <!-- 定义渐变 -->
      <defs>
        <!-- 上半区渐变：白色到浅蓝色 -->
        <linearGradient id="upperGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#eaf6ff;stop-opacity:1" />
        </linearGradient>
        <!-- 下半区渐变：浅粉色到白色 -->
        <linearGradient id="lowerGradient" x1="0%" y1="0%" x2="0%" y2="100%">
          <stop offset="0%" style="stop-color:#ffeaea;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#ffffff;stop-opacity:1" />
        </linearGradient>
      </defs>
      
      <!-- 背景分区 -->
      <rect x="0" y="0" :width="width" :height="height / 2" fill="url(#upperGradient)" />
      <rect x="0" :y="height / 2" :width="width" :height="height / 2" fill="url(#lowerGradient)" />

      <!-- 中间分界线 -->
      <line x1="0" :y1="height / 2" :x2="width" :y2="height / 2" class="divider-line" />

      <!-- 波浪线 -->
      <path :d="pathData" class="wave-line" />

      <!-- 数据点、表情 和 Tooltip -->
      <g class="data-points-group">
        <g v-for="(point, index) in points" :key="'point-group-' + index">
          <!-- 1. 直接绘制可见的SVG元素 -->
          <g class="interactive-trigger">
            <circle
              :cx="point.x"
              :cy="point.y"
              r="5"
              class="data-point"
            />
            <image
              :x="point.emojiX - 10"
              :y="point.emojiY - 10"
              width="20"
              height="20"
              :href="emojiMap[point.value]"
              class="emoji-image"
            />
          </g>

          <!-- 2. 使用 foreignObject 创建一个不可见的HTML区域来触发Popover -->
          <foreignObject :x="point.x - 10" :y="point.y - 10" width="20" height="30">
              <fx-popover
                :key="'popover-' + index"
                placement="top"
                trigger="click"
                popper-class="attitude-tooltip"
                :ref="'chart-popover-' + index"
              >
                <!-- Popover 内容 -->
                <div class="tooltip-content-wrapper">
                  <div class="tooltip-title">
                    {{$t("sfa.crm.participantinsights.attitude_now")}}
                    <div class="tooltip-attitude-row">
                    <span class="attitude-tag" style="min-width: 45px;" :class="point.value">
                      <img :src="emojiMap[point.value]" class="attitude-emoji" alt="">
                      <span class="attitude-text">{{ getAttitudeText(point.value) }}</span>
                    </span>
                  </div>
                    </div>
                  <div class="tooltip-time-row" @click="handleTimeClick(point, index)">
                    <span class="time-text">{{ point.time }}</span>
                    <i class="el-icon-link link-icon"></i>
                  </div>
                </div>

                <!-- 触发 Popover 的元素, 一个撑满 foreignObject 的隐形 div -->
                <div
                  slot="reference"
                  style="width: 20px; height: 30px; cursor: pointer;"
                ></div>
              </fx-popover>
          </foreignObject>
        </g>
      </g>
    </svg>
    <!-- 日期标签容器 -->
    <div class="date-labels">
      <span
        v-for="(point, index) in points"
        :key="'date-' + index"
        class="date-label"
        :style="{ left: point.x + 'px' }"
      >
        {{ point.time.replace(/-/g, '') }}
      </span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AttitudeChart',
  props: {
    lineData: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data() {
    return {
      width: 0,
      height: 72, // 高度固定为72px
      resizeObserver: null,
      emojiMap: {
        support: require("@/assets/images/activity/face_support.svg"),    // 支持
        neutrality: require("@/assets/images/activity/face_neutral.svg"), // 中立
        oppose: require("@/assets/images/activity/face_oppose.svg")       // 不满/反对
      }
    };
  },
  computed: {
    pathData() {
      if (this.points.length < 2) {
        return ''; // 如果点少于2个，不画线
      }
      return this.catmullRom2bezier(this.points);
    },
    points() {
      // 如果没有数据或容器宽度未初始化，则返回空数组
      if (!this.lineData.length || this.width === 0) {
        return [];
      }

      // 1. 定义图表边距
      const margin = { left: 20, right: 20 };
      const drawableWidth = this.width - margin.left - margin.right;

      // 2. 定义三种状态的Y轴位置
      const pointRadius = 5; // 和SVG中circle的r属性保持一致
      const supportY = pointRadius;           // 顶到上边缘
      const neutralityY = this.height * 0.5;  // 中间
      const opposeY = this.height - pointRadius; // 顶到下边缘

      // 3. 计算点与点之间的水平间距
      // 如果只有一个点，间距为0，它将被画在中间
      const xStep = this.lineData.length > 1 ? drawableWidth / (this.lineData.length - 1) : 0;

      const emojiOffsetY = 20; // 表情与点之间的垂直距离

      // 4. 遍历lineData，计算每个点的具体坐标
      return this.lineData.map((d, i) => {
        let y;
        let emojiY;

        switch (d.value) {
          case 'support':
            y = supportY;
            emojiY = y + emojiOffsetY; // 点的下方
            break;
          case 'neutrality':
            y = neutralityY;
            emojiY = y + emojiOffsetY; // 点的下方
            break;
          case 'oppose':
            y = opposeY;
            emojiY = y- 15 ; // 点的上方，留出更多空隙
            break;
          default:
            y = neutralityY;
            emojiY = y + emojiOffsetY;
        }

        // 如果只有一个点，将其放在可绘制区域的中间
        const x = this.lineData.length > 1
          ? margin.left + (i * xStep)
          : this.width / 2;
        
        // 返回包含原始数据和坐标的新对象
        return { ...d, x, y, emojiX: x, emojiY };
      });
    }
  },
  mounted() {
    // 确保DOM加载完成后执行
    this.$nextTick(() => {
      this.setChartDimensions();
      
      // 使用 ResizeObserver 监听父容器宽度变化
      if (this.$refs.chartWrapper) {
        // 修正: 必须传入一个回调函数，箭头函数确保`this`指向正确
        this.resizeObserver = new ResizeObserver(() => {
          this.setChartDimensions();
        });
        this.resizeObserver.observe(this.$refs.chartWrapper);
      }
    });
  },
  beforeDestroy() {
    // 组件销毁前，停止监听
    if (this.resizeObserver && this.$refs.chartWrapper) {
      this.resizeObserver.unobserve(this.$refs.chartWrapper);
    }
  },
  methods: {
    setChartDimensions() {
      // 获取父容器的实时宽度
      if (this.$refs.chartWrapper) {
        this.width = this.$refs.chartWrapper.clientWidth;
      }
    },
    catmullRom2bezier(points) {
      const pointRadius = 5;
      const clamp = (val, min, max) => Math.max(min, Math.min(val, max));
      
      let d = `M${points[0].x},${points[0].y}`;
      for (let i = 0; i < points.length - 1; i++) {
        const p0 = points[i - 1] || points[i];
        const p1 = points[i];
        const p2 = points[i + 1];
        const p3 = points[i + 2] || p2;
        
        let cp1x = p1.x + (p2.x - p0.x) / 6;
        let cp1y = p1.y + (p2.y - p0.y) / 6;
        let cp2x = p2.x - (p3.x - p1.x) / 6;
        let cp2y = p2.y - (p3.y - p1.y) / 6;

        // Bug修复: 限制控制点的Y坐标在可见范围内
        cp1y = clamp(cp1y, pointRadius, this.height - pointRadius);
        cp2y = clamp(cp2y, pointRadius, this.height - pointRadius);
        
        d += ` C${cp1x},${cp1y} ${cp2x},${cp2y} ${p2.x},${p2.y}`;
      }
      return d;
    },
    getAttitudeText(value) {
      const map = {
        support: '支持',
        neutrality: '中立',
        oppose: '反对'
      };
      return map[value] || '';
    },
    handleTimeClick(data, index) {
      console.log(data);
         CRM.api.show_crm_detail({
        type: "ActiveRecordObj",
        data: {
          crmId: data.active_record_id,
          apiName: "ActiveRecordObj",
        },
      });

      // Close the popover after clicking, using the same logic as in text.vue
      const popoverRefArray = this.$refs["chart-popover-" + index];
      if (Array.isArray(popoverRefArray) && popoverRefArray.length > 0) {
        const popoverInstance = popoverRefArray[0];
        if (typeof popoverInstance.doClose === "function") {
          popoverInstance.doClose();
        } else {
          popoverInstance.showPopper = false;
        }
      }
    }
  }
}
</script>

<style>
/* 非 scoped 样式，用于定制 el-tooltip */
.attitude-tooltip.el-tooltip__popper {
  border: 1px solid #ebeef5 !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1) !important;
  padding: 14px 18px !important;
  background: #fff !important;
}
.attitude-tooltip .popper__arrow {
  border-top-color: #ebeef5 !important;
}
.attitude-tooltip .popper__arrow::after {
  border-top-color: #fff !important;
}
.attitude-tooltip{
  width: 140px;
  padding: 8px;
}

.tooltip-content-wrapper {
  color: #303133;
  line-height: 1.4;
}
.tooltip-title {
  display: flex;
  padding: 1px 4px;
  font-weight: 700;
  height: 20px;
  font-size: 13px;
  gap: 8px;
  justify-content: space-between;
  align-items: center;
}
.attitude-tag {
  display: inline-flex;
  align-items: center;
  padding: 2px 4px;
  border-radius: 12px;
  font-weight: 700;
}
.attitude-tag.support { background-color: #e6f7ff; color: #1890ff; }
.attitude-tag.neutrality { background-color: #fffbe6; color: #faad14; }
.attitude-tag.oppose { background-color: #fff1f0; color: #f5222d; }
.attitude-emoji {  margin-right: 2px; vertical-align: middle; }
.tooltip-time-row {  margin: 10px 0 0 4px; display: flex; align-items: center; cursor: pointer; color: #409eff; }
.time-text { font-size: 14px; }
.link-icon { margin-left: 5px; }
</style>

<style scoped>
.chart-wrapper {
  width: 100%;
  position: relative;
  /* 为下方的日期标签留出空间 */
  padding-bottom: 27px; 
  margin-top: 2px;
}

.chart-svg {
  display: block; /* 避免SVG下方出现不明空隙 */
}

.divider-line {
  stroke-width: 1px;
  stroke: var(---Line-2-, #E5E6EB);
  stroke-dasharray: 4, 4; /* 虚线样式：4px实线，4px空隙 */
}

.wave-line {
  fill: none;
  stroke: #6c63ff;
  stroke-width: 3;
  stroke-linejoin: round;
  stroke-linecap: round;
  transition: d 0.3s ease;
}

.data-point {
  fill: #1976d2;
  stroke: none;
  transition: all 0.3s ease;
}

.emoji-image {
  pointer-events: none; /* 让表情不影响鼠标事件 */
}

.date-labels {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 20px; /* 日期标签的高度 */
}

.date-label {
  position: absolute;
  top: 0;
  font-size: 12px;
  color: #888;
  transform: translateX(-50%); /* 水平居中 */
  white-space: nowrap;
}

.interactive-trigger {
  cursor: pointer;
}
</style>