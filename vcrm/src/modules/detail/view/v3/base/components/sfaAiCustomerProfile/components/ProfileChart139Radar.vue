<script>
import withB<PERSON><PERSON><PERSON> from '../mixins/withBase<PERSON><PERSON>';

export default {
    name: 'ProfileChart139Radar',
    mixins: [withBase<PERSON><PERSON>],
    methods: {
        getChartOption() {
            const { '1W': W, '3F': F, '9C': C } = this.chartData;
            return {
                legend: {
                    data: [
                        this.$t('sfa.aiCustomerProfile.chartLegend.win'), 
                        this.$t('sfa.aiCustomerProfile.chartLegend.lose'), 
                        this.$t('sfa.aiCustomerProfile.chartLegend.now')
                    ],
                    bottom: 12,
                    left: 'center'
                },
                radar: {
                    indicator: [
                        { name: '1W', max: 1 },
                        { name: '3F', max: 3 },
                        { name: '9C', max: 9 }
                    ],
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['#fff']
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: ['#EEF0F3']
                        }
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#DEE1E8'
                        }
                    },
                    // splitNumber: 3,
                    center: ['50%', '55%'],
                    radius: '82%',
                    name: {
                        formatter: function(name) {
                            if (name === '1W' || name === '3F' || name === '9C') {
                                return `{a|${name}}`;
                            }
                            return name;
                        },
                        rich: {
                            a: {
                                color: '#181C25'
                            }
                        }
                    }
                },
                series: [{
                    type: 'radar',
                    lineStyle: {
                        width: 1.5
                    },
                    data: [
                        // 数据依据 https://hr0dd5kw59.feishu.cn/docx/AIn4dzU7Vo4k8AxjoBEc0jO0nAc
                        {
                            value: [1, 1, 6],
                            name: this.$t('sfa.aiCustomerProfile.chartLegend.win'),
                            itemStyle: {
                                color: '#5498FF'
                            },
                            areaStyle: {
                                color: 'rgba(54, 141, 255, 0.06)'
                            }
                        },
                        {
                            value: [0, 2, 6],
                            name: this.$t('sfa.aiCustomerProfile.chartLegend.lose'),
                            itemStyle: {
                                color: '#FF8497'
                            },
                            areaStyle: {
                                color: 'rgba(251, 142, 159, 0.10)'
                            }
                        },
                        {
                            value: [W, F, C],
                            name: this.$t('sfa.aiCustomerProfile.chartLegend.now'),
                            itemStyle: {
                                color: '#8CD9A2'
                            },
                            areaStyle: {
                                color: 'rgba(48, 199, 118, 0.06)'
                            }
                        }
                    ]
                }]
            };
        }
    }
};
</script>
