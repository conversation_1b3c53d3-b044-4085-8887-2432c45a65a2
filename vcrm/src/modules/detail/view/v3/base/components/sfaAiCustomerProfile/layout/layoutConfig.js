// 基础布局配置
const createLayoutConfig = (context) => {
    const {currentMethodology, apiName, displayAllComponents, displayComponents = [], isC139Methodology, currentProfileId} = context;
    const isDisplay = (name) => context.displayComponents === void 0 ? displayAllComponents : displayComponents.includes(name);
    const getComponents = (components) => {
        return components.filter(item => item.isShow || item.isShow === undefined).map(item => ({
            ...item,
            components: item.components ? getComponents(item.components) : []
        }));
    }
    // 无数据布局
    const isEmpty = !currentProfileId;
    if (isEmpty) {
        return {
            type: 'column',
            style: {
                gap: '8px',
            },
            components: [
                {
                    name: 'profile-header-operate-actions',
                    props: {
                        profileContext: 'profileContext',
                        dataContext: 'dataContext',
                        firstInit: true,
                    },
                    style: {
                        alignSelf: 'flex-end',
                    }
                },
                {name: 'profile-empty'},
            ]
        }
    }
    return {
        type: 'column',
        style: {
            gap: '8px',
        },
        components: [
            {
                type: 'row',
                style: {
                    justifyContent: 'flex-end',
                },
                components: [
                    {
                        name: 'profile-header-operate-actions',
                        props: {
                            profileContext: 'profileContext',
                            dataContext: 'dataContext',
                        },
                    }
                ]
            },
            {
                type: 'column',
                style: {
                    padding: '12px',
                    gap: '12px',
                    borderRadius: '8px',
                    backgroundColor: '#F1F3F8'
                },
                components: getComponents([
                    {
                        type: 'column',
                        style: {
                            padding: '12px',
                            borderRadius: '8px',
                            boxSizing: 'border-box',
                            backgroundImage: 'linear-gradient(to top right, #F9FAF7 0.71%, #F6FCF4 58.4%, #F6FCFF 99.62%)',
                            gap: '8px',
                            overflowX: 'auto',
                        },
                        class: 'crm-scroll',
                        components: isC139Methodology ?
                            [
                                {
                                    type: 'row',
                                    style: {gap: '8px', flexWrap: 'wrap'},
                                    components: [
                                        {
                                            name: 'profile-source-overview',
                                            props: {
                                                scoreData: 'winRateScoreData',
                                                dataContext: 'dataContext',
                                                profileContext: 'profileContext',
                                            },
                                            isShow: isDisplay('profile-source-overview'),
                                            style: {
                                                flex: '1 1 calc(50% - 16px)',
                                                minWidth: '400px',
                                            }
                                        },
                                        {
                                            name: 'profile-source-trend',
                                            props: {
                                                currentMethodology: 'currentMethodology',
                                                currentMethodologyType: 'currentMethodologyType',
                                                objectApiName: 'objectApiName',
                                                objectId: 'objectId',
                                            },
                                            style: {
                                                flex: '1 1 calc(50% - 16px)',
                                                minWidth: '400px',
                                            },
                                            isShow: isDisplay('profile-source-trend'),
                                        },
                                        {
                                            type: 'row',
                                            style: {
                                                flex: '1 1 calc(50% - 16px)',
                                                minWidth: '400px',
                                                minHeight: '250px',
                                                backgroundColor: 'rgba(255, 255, 255, 0.76)',
                                                borderRadius: '8px',
                                            },
                                            isShow: isDisplay('profile-chart-139-radar'),
                                            components: [
                                                {
                                                    name: 'profile-chart-139-radar',
                                                    props: {
                                                        chartData: 'c139RadarData',
                                                    },
                                                }
                                            ]
                                        },
                                        {
                                            style: {
                                                flex: '1 1 calc(50% - 16px)',
                                                minWidth: '400px',
                                            },
                                            name: 'profile-chart-139-describe',
                                            props: {
                                                indicatorGroups: 'dimensionsData',
                                            },
                                            isShow: isDisplay('profile-chart-139-describe'),
                                        }
                                    ]
                                }
                            ] :
                            [
                                {
                                    type: 'row',
                                    style: { gap: '16px', flexWrap: 'wrap'},
                                    components: [
                                        {
                                            type: 'column',
                                            style: {
                                                gap: '8px',
                                                flex: 1,
                                                minWidth: '400px',
                                                height: '312px',
                                            },
                                            components: [
                                                {
                                                    name: 'profile-source-overview',
                                                    props: {
                                                        scoreData: 'winRateScoreData',
                                                        dataContext: 'dataContext',
                                                        profileContext: 'profileContext',
                                                    },
                                                    style: {
                                                        flex: 1,
                                                    },
                                                    isShow: isDisplay('profile-source-overview'),
                                                },
                                                {
                                                    name: 'profile-source-trend',
                                                    props: {
                                                        currentMethodology: 'currentMethodology',
                                                        currentMethodologyType: 'currentMethodologyType',
                                                        objectApiName: 'objectApiName',
                                                        objectId: 'objectId',
                                                    },
                                                    style: {
                                                        flex: 1,
                                                    },
                                                    isShow: isDisplay('profile-source-trend'),
                                                },
                                            ],
                                        },
                                        {
                                            type: 'row',
                                            style: {
                                                minWidth: '452px',
                                                height: '312px',
                                                flex: 1,
                                            },
                                            isShow: isDisplay('profile-chart-radar'),
                                            components: [
                                                {
                                                    name: 'profile-chart-radar',
                                                    props: {
                                                        chartData: 'dimensionsData'
                                                    },
                                                }
                                            ]
                                        }
                                    ]
                                },
                            ]
                    },
                    {
                        name: 'profile-dimension-features',
                        props: {
                            dimensions: 'switchDimensionsData',
                            'currentDimension.sync': 'currentDimension',
                            dataId: 'dataId',
                            apiName: 'apiName'
                        },
                    },
                    {
                        name: 'profile-dimension-summary',
                        isShow: isDisplay('profile-dimension-summary'),
                        props: {
                            summary: 'currentSummary',
                        },
                    },
                    {
                        name: 'profile-dimension-change-trends',
                        props: {
                            profileContext: 'profileContext',
                            dataContext: 'dataContext',
                            dimensionsData: 'dimensionsData'
                        },
                        isShow: isDisplay('profile-dimension-change-trends'),
                    },
                    {
                        type: 'row',
                        style: { gap: '12px' },
                        components: [
                            {
                                name: 'profile-dimension-advantage',
                                style: { flex: 1 },
                                props: ['currentDimension', 'currentProfileId', 'apiName', 'dataId'],
                                isShow: isDisplay('profile-dimension-advantage'),
                            },
                            {
                                name: 'profile-dimension-disadvantage',
                                style: { flex: 1 },
                                props: ['currentDimension', 'currentProfileId', 'apiName', 'dataId'],
                                isShow: isDisplay('profile-dimension-disadvantage'),
                            },
                        ]
                    },
                    {
                        name: 'profile-dimension-suggestion',
                        props: {
                            'profileContext': 'profileContext',
                            'dataContext': 'dataContext',
                            'scoreData': 'stageScoreData',
                        },
                        isShow: isDisplay('profile-dimension-suggestion'),
                    }
                ])
            }
        ]
    }
};

export default createLayoutConfig;