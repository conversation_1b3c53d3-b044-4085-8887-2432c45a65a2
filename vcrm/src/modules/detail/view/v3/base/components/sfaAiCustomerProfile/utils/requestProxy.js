class RequestProxy {
    constructor() {
        this.pendingRequests = new Map();
        this.cache = new Map();
    }
    
    // 仅先使用并发请求的逻辑
    async request(key, requestFn, options = {useCache: false}) {
        // console.log(key)
        // 使用缓存
        if (options.useCache && this.cache.has(key)) {
            return this.cache.get(key);
        }
        
        // 复用进行中的请求
        if (this.pendingRequests.has(key)) {
            return this.pendingRequests.get(key);
        }
        
        // 发起新请求
        const promise = requestFn()
            .then(result => {
                this.cache.set(key, result);
                return result;
            })
            .finally(() => {
                this.pendingRequests.delete(key);
            });
            
        this.pendingRequests.set(key, promise);
        return promise;
    }
    
    clearCache(key) {
        if (key) {
            this.cache.delete(key);
        } else {
            this.cache.clear();
        }
    }
}

export default new RequestProxy(); 