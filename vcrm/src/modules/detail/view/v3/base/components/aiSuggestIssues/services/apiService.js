/**
 * 将驼峰命名的 API 名称转换为下划线分隔的字段名称
 * @param {string} apiName - API 名称，如 'AccountObj', 'ActiveRecordObj', 'NewOpportunityObj'
 * @returns {string} 转换后的字段名称，如 'account_id', 'active_record_id', 'new_opportunity_id'
 *
 * 转换示例：
 * - AccountObj -> account_id
 * - ActiveRecordObj -> active_record_id
 * - NewOpportunityObj -> new_opportunity_id
 * - SomeComplexApiNameObj -> some_complex_api_name_id
 */
const convertApiNameToFieldName = (apiName) => {
  // 去掉末尾的 'Obj'
  const nameWithoutObj = apiName.replace(/Obj$/, '');

  // 将驼峰命名转换为下划线分隔
  const snakeCaseName = nameWithoutObj
    .replace(/([A-Z])/g, '_$1')  // 在大写字母前加下划线
    .toLowerCase()               // 转为小写
    .replace(/^_/, '');          // 去掉开头的下划线

  // 末尾加上 'id'
  return `${snakeCaseName}_id`;
};

export const fetchRelatedObjectList = async (apiName, detailData) => {
    // 使用 convertApiNameToFieldName 动态生成字段名
    const fieldName = convertApiNameToFieldName(apiName);

    const data = {
        "source_api_name": apiName
    };

    // 动态设置字段值，不再使用硬编码
    data[fieldName] = detailData._id;

    // 兼容其他已知字段，确保向后兼容
    if (detailData.active_record_id) data["active_record_id"] = detailData.active_record_id;
    if (detailData.account_id) data["account_id"] = detailData.account_id;
    if (detailData.leads_id) data["leads_id"] = detailData.leads_id;
    if (detailData.new_opportunity_id) data["new_opportunity_id"] = detailData.new_opportunity_id;

    return new Promise((resolve, reject) => {
      CRM.util.FHHApi({
        url: '/EM1HNCRM/API/v1/object/advice_topic/service/objectsList',
        data,
        success: function (res) {
          if (res.Result.StatusCode === 0) {
            resolve(res.Value.objectsList || []);
          } else {
            reject(new Error(res.Result.FailureMessage));
          }
        },
        error: function(err) {
          reject(err);
        }
      }, {
        errorAlertModel: 1
      });
    });
};

export const fetchSuggestListData = async (apiName, detailData, params) => {
    const { limit, offset, questionType, tagsType, relatedObjectName, isHistory } = params;

    // 使用 convertApiNameToFieldName 动态生成字段名
    const fieldName = convertApiNameToFieldName(relatedObjectName);

    let search_query_info = {
        limit,
        offset,
        "filters": [],
        "orders": [{"field_name":"order_field","isAsc":"true"}]
    };

    // 动态生成过滤器参数，不再使用硬编码映射
    let _filters = [
        {"field_name": fieldName, "field_values": [detailData._id], "operator": "EQ"},
        {"field_name": "question_type", "field_values": ['2'], "operator": "EQ"},
    ];

    // 对于 ActiveRecordObj，特殊处理数据值
    if (apiName === 'ActiveRecordObj') {
        const fieldValue = detailData[fieldName] || detailData._id;
        _filters[0].field_values = [fieldValue];
    }
    if(isHistory) {
        _filters.push({"field_name":"history_flag","field_values":["history"],"operator":"EQ"})
    }else{
        _filters.push({"field_name":"history_flag","field_values":[""],"operator":"IS"})
    }
    if(questionType) {
        _filters.push({"field_name":"advice_status","field_values":[questionType],"operator":"EQ"})
    }
    // 添加标签类型筛选
    if (!tagsType.includes('all')) {
        _filters.push({"field_name": 'library_tags', "field_values": tagsType, "operator": "HASANYOF"});
    }
    search_query_info.filters = _filters;

    let data = {
        "serializeEmpty": false,
        "extractExtendInfo": true,
        "object_describe_api_name": "ActivityQuestionObj",
        "search_template_id": "",
        "include_describe": true,
        "include_layout": false,
        "need_tag": true,
        "search_template_type": "default",
        "ignore_scene_record_type": false,
        "list_type": "related",
        "extraData":{
            "skip_authority":true
        },
        "search_query_info": JSON.stringify(search_query_info),
    };

    if(apiName == 'ActiveRecordObj') {
        data.extraData.active_record_id = detailData._id;
    }

    return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/ActivityQuestionObj/controller/RelatedList',
            data,
            success: function (res) {
                if (res.Result.StatusCode === 0) {
                    resolve(res.Value);
                } else {
                    resolve({});
                }
            },
            error(err) {
                reject(err);
            }
        }, {
            errorAlertModel: 1
        });
    });
};
