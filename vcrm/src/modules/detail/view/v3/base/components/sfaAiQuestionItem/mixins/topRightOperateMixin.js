export default {
    props: {
        userAvaBgColor: {
            type: Object,
            default: {},
        },
        useTopRightButton: {
            type: Boolean,
            default: true,
        },
        isExpanded: {
            type: Boolean,
            default: true,
        },
        useKnowledgeAnswer: {
          type: Boolean,
          default: true
        },
        useQuestionAvatar: {
            type: Boolean,
            default: true,
        },
        relatedOpenDetail: {
            type: Boolean,
            default: true,
        },
        useAttitudeImg: {
          type: Boolean,
          default: false
        },
        useAnswerEdit: {
          type: Boolean,
          default: false
        },
        openMultipleSummary: {
            type: Boolean,
            default: true
        },
        relatedFieldName: {
            type: String,
            default: 'match_suggest_topic__r',
        },
        attitudeFieldName: {
            type: String,
            default: 'proposer_attitude_label',
        },
        keyword: {
            type: String,
            default: ''
        },
        tagsTypeList: {
            type: Array,
            default: () => [],
        }
    },
    data() {
        return {
            itemIsExpanded: this.isExpanded,
            showTopRightBox: false,
            topRightBoxTimer: null,
        };
    },
    mounted() {
        // 添加鼠标事件监听
        this.$el.addEventListener('mouseover', this.handleContainerMouseOver);
        this.$el.addEventListener('mouseleave', this.handleContainerMouseLeave);
    },
    methods: {
        setMark(itemData, type, index) {
            console.log(itemData, type, index, 'itemData, type');
            if(!this.maskLoading) {
                this.maskLoading = true;
                this.$emit('set-mark', {itemData, type, index, hideMaskLoading: this.hideMaskLoading})
            }
        },
        addTask(itemData) {
            let text = $t('sfa.ai.infinite.list.item.answer', {answerPerson: itemData.answer_person}) + '——' + (itemData.answer_summary || '');
            CRM.api.add_taskobj({
                feedContent: [
                    {
                        text: itemData.answer_person || itemData.answer_summary ? text : ''
                    },
                ],
                crmobjectOpts: [

                ],
                success: function(){
                    CRM.util.remind(1, $t('任务创建成功。'));
                }
            })
        },
        refreshItemAisuggest(itemData) {
            let me = this;
            let data = {
                objectApiName: this.apiName,
                objectDataId: this.dataId,
                aiQuestionId: itemData._id
            }
            me.knowledgeLoading = true;
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/interact_question/service/get_ai_suggestion',
                data,
                success: function (res) {
                    console.log(res, 'res----item——get_ai_suggestion');
                    if(res.Result.StatusCode == 0 ) {
                        FxUI.Message({
                            message: $t('sfa.ai.interactive.issue.refresh.tip'),
                            type: 'success',
                            duration: 3000,
                            offset: 100
                        })
                        // if(res.Value?.data) {
                        //     if(res.Value.data.ai_answer_content && res.Value.data.ai_answer_content.__xt.__json) {
                        //         me.itemData.ai_answer_content_label = res.Value.data.ai_answer_content.text;
                        //         me.itemData.ai_answer_content = res.Value.data.ai_answer_content.__xt.__json;

                        //         // 内容更新后，确保重新检查高度并设置观察器
                        //         me.$nextTick(() => {
                        //             me.setupContentResizeObserver();
                        //             me.checkContentHeight();
                        //         });
                        //     }else{
                        //         me.$message({
                        //             type: 'warning',
                        //             message:  $t('sfa.ai.infinite.list.item.refresh.nocontent')
                        //         })
                        //     }
                        //     me.itemData.ai_answer_time = res.Value.data.ai_answer_time;
                        // }
                        // me.$emit('refresh-itemKnowledge')
                    }else{
                        FxUI.Message({
                            message: res.Result.FailureMessage || $t('sfa.ai.interactive.suggest.refresh.error.tip'),
                            type: 'warning',
                            offset: 100
                        })
                    }
                    me.knowledgeLoading = false;
                },
                error(err) {
                    me.knowledgeLoading = false;
                }
            }, {
                errorAlertModel: 1
            });
        },
        toggleExpand() {
            this.itemIsExpanded = !this.itemIsExpanded;
        },
        handleContainerMouseOver(event) {
            const target = event.target;

            // 如果鼠标进入relative-item-box区域，隐藏top-right-box
            if (target.closest('.relative-item-box')) {
                this.cancelTopRightBoxTimer();
                this.showTopRightBox = false;
                return;
            }

            // 如果鼠标悬停在头像或头像容器上，不显示顶部按钮
            if (target.closest('.avatar') ||
                target.closest('.question-content-avatar') ||
                target.closest('.right-answer') ||
                target.closest('.fx-tooltip') ||
                target.closest('.fx-tooltip__popper') ||
                document.querySelector('.fx-tooltip__popper')) {
                this.cancelTopRightBoxTimer();
                this.showTopRightBox = false;
                return;
            }

            // 在container区域内延迟显示top-right-box
            if (target.closest('.sfa-ai-question-item-container')) {
                // 先取消之前的计时器（如果有）
                this.cancelTopRightBoxTimer();

                // 设置一个新的延迟计时器
                this.topRightBoxTimer = setTimeout(() => {
                    this.showTopRightBox = true;
                }, 200); // 延迟200毫秒显示
            }
        },
        handleContainerMouseLeave() {
            // 清除延迟计时器
            this.cancelTopRightBoxTimer();

            // 立即隐藏按钮
            this.showTopRightBox = false;
        },
        cancelTopRightBoxTimer() {
            if (this.topRightBoxTimer) {
                clearTimeout(this.topRightBoxTimer);
                this.topRightBoxTimer = null;
            }
        },
    },
    beforeDestroy() {
        this.$el.removeEventListener('mouseover', this.handleContainerMouseOver);
        this.$el.removeEventListener('mouseleave', this.handleContainerMouseLeave);
        this.cancelTopRightBoxTimer();
    },
};
