<template>
    <common-card :title="title" class="profile-source-trend-card" background="rgba(255, 255, 255, 0.76)">
        <common-data-fetcher-status :loading="loading" :error="error" :data="trendData">
            <trend-chart :chartData="trendData" :chartDataUnit="sourceUnit" minHeight="80px" />
        </common-data-fetcher-status>
    </common-card>
</template>

<script>
import CommonCard from './CommonCard.vue'
import CommonDataFetcherStatus from './CommonDataFetcherStatus.vue'
import TrendChart from './BaseChartTrend.vue'
import profileService from '../services/profileService'
import utils from '../utils'
export default {
    name: 'ProfileSourceTrend',
    components: {
        CommonDataFetcherStatus,
        CommonCard,
        TrendChart
    },
    props: {
        currentMethodology: {
            type: String,
            default: '',
        },
        currentMethodologyType: {
            type: String,
            default: '',
        },
        objectApiName: {
            type: String,
            default: '',
        },
        objectId: {
            type: String,
            default: '',
        }
    },
    watch: {
        currentMethodology: {
            handler(newVal) {
                this.fetchTrendData();
            },
            immediate: true,
        }
    },
    data() {
        return {
            trendData: [],
            loading: false,
            error: null,
        }
    },
    computed: {
        title() {
            return utils.getSourceProps(this.objectApiName).trendTitle;
        },
        sourceUnit() {
            return utils.getSourceProps(this.objectApiName).sourceUnit;
        }
    },
    methods: {
        async fetchTrendData() {
            if (!this.currentMethodologyType) return;
            this.loading = true;
            try {
                const res = await profileService.getCustomerProfileTrend({
                    methodologyId: this.currentMethodology,
                    methodologyType: this.currentMethodologyType,
                    objectApiName: this.objectApiName,
                    objectId: this.objectId,
                })
                this.trendData = res;
            } catch (error) {
                this.error = error;
            } finally {
                this.loading = false;
            }
        }
    }
}
</script>