/**
 * 音频播放器相关功能 Mixin
 * 
 * 功能包括：
 * 1. 语料项点击事件处理
 * 2. 文本选择状态跟踪
 * 3. 音频时间跳转
 * 4. 冲突检测与避免
 * 5. 时间解析工具
 * 6. "回到当前"按钮控制
 * 7. 智能滚动策略
 * 8. 可视区域检测
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */

import { EVENT_NAMES } from '../event-manager';

export default {
  data() {
    return {
      // 用户交互状态跟踪（简化版）
      userInteractionState: {
        mouseDownTime: null, // 鼠标按下时间
        mouseUpTime: null, // 鼠标抬起时间
        lastSelectionTime: 0
      },
      
      // 时间解析缓存
      _timeParseCache: null,
      
      // 当前位置按钮控制
      showCurrentPositionBtnInternal: false, // 内部控制按钮显示状态
      
      // 音频播放状态
      highlightedItemId: null,
      lastTimeUpdate: null, // 最后一次时间更新时间戳
      timeUpdateTimeout: null, // 时间更新超时定时器
      
      // 滚动状态控制
      isScrolling: false, // 是否正在滚动中
      isAutoScrolling: false, // 是否正在自动滚动中（音频播放时）
      scrollTimer: null, // 滚动定时器
      
      // 文本选择跟踪
      textSelectionTracking: {
        isMouseDown: false,
        startTime: 0,
        moved: false
      },
      
      // 点击高亮保护机制
      clickHighlightProtection: {
        itemId: null,
        timestamp: 0,
        duration: 1000 // 保护时长1秒
      }, 
      
      // 双击检测机制
      clickDetection: {
        timer: null,
        preventClick: false, // 是否阻止单击事件执行
        delay: 150, // 减少单击延迟时间，从250ms降到150ms
        lastClickedItemId: null, // 记录上次点击的项目ID
        lastClickTime: 0 // 记录上次点击时间
      }
    };
  },

  computed: {
    // 是否显示"当前位置"按钮
    showCurrentPositionBtn() {
      return this.showCurrentPositionBtnInternal && !this.fromToRecord;
    }
  },

  created() {
    // 初始化防抖检查当前位置按钮状态
    this.debouncedCheckCurrentPosition = _.debounce(this._checkCurrentPositionInternal, 100); // 100ms防抖
  },

  mounted() {
    this.initTextSelectionTracking();
  },

  beforeDestroy() {
    this.destroyTextSelectionTracking();
    
    // 清理滚动定时器
    if (this.scrollTimer) {
      clearTimeout(this.scrollTimer);
      this.scrollTimer = null;
    }

    // 重置滚动状态
    this.isScrolling = false;
    this.isAutoScrolling = false;

    // 清理时间更新超时定时器
    if (this.timeUpdateTimeout) {
      clearTimeout(this.timeUpdateTimeout);
      this.timeUpdateTimeout = null;
    }

    // 清理防抖方法
    if (this.debouncedCheckCurrentPosition && this.debouncedCheckCurrentPosition.cancel) {
      this.debouncedCheckCurrentPosition.cancel();
    }
    
    // 清理缓存
    if (this._timeParseCache) {
      this._timeParseCache.clear();
      this._timeParseCache = null;
    }

    // 清理用户交互状态
    this.userInteractionState = {
      mouseDownTime: null,
      mouseUpTime: null
    };

    // 清理双击检测定时器
    if (this.clickDetection.timer) {
      clearTimeout(this.clickDetection.timer);
      this.clickDetection.timer = null;
    }

    // 清理防抖方法
    if (this._checkCurrentPositionInternal && this._checkCurrentPositionInternal.cancel) {
      this._checkCurrentPositionInternal.cancel();
    }
  },

  methods: {
    /**
     * 处理语料项点击事件
     * @param {Object} item - 语料项数据
     * @param {Event} event - 点击事件
     */
    handleItemClick(item, event) {
      let interaction_records = this.$context.getData()?.interaction_records;
      // 防止在非历史音频模式下  或 没有音频文件 执行
      if (this.fromToRecord || !interaction_records?.length) {
        return;
      }

      // 优化：缓存target，减少重复访问
      const target = event.target;
      
      // 检查是否点击在交互元素上，直接返回不阻止事件
      if (target.closest('button') || 
          target.closest('a') || 
          target.closest('.editable-content-wrapper') ||
          target.hasAttribute('contenteditable')) {
        return;
      }

      // 优化：简化文本选择检查
      const selection = window.getSelection();
      if (selection && selection.rangeCount > 0 && !selection.isCollapsed) {
        return;
      }

      // 优化：快速重复点击检测 - 如果在很短时间内重复点击同一项，直接执行
      const now = Date.now();
      const isQuickRepeatClick = this.clickDetection.lastClickedItemId === item.id && 
                                 (now - this.clickDetection.lastClickTime) < 300;
      
      if (isQuickRepeatClick) {
        this.executeItemClick(item);
        return;
      }

      // 更新点击记录
      this.clickDetection.lastClickedItemId = item.id;
      this.clickDetection.lastClickTime = now;

      // 双击检测：如果已标记阻止点击，则不执行单击逻辑
      if (this.clickDetection.preventClick) {
        this.clickDetection.preventClick = false; // 重置标记
        return;
      }

      // 清除之前的单击定时器
      if (this.clickDetection.timer) {
        clearTimeout(this.clickDetection.timer);
        this.clickDetection.timer = null;
      }

      // 使用延迟执行单击逻辑，以便双击时可以取消
      this.clickDetection.timer = setTimeout(() => {
        this.executeItemClick(item);
        this.clickDetection.timer = null;
      }, this.clickDetection.delay);
    },

    /**
     * 处理双击开始编辑事件
     * 在v-editable的onEdit回调中调用
     * @param {string} itemId - 语料项ID
     */
    handleItemDoubleClick(itemId) {
      // 标记阻止单击事件执行
      this.clickDetection.preventClick = true;
      
      // 清除单击定时器
      if (this.clickDetection.timer) {
        clearTimeout(this.clickDetection.timer);
        this.clickDetection.timer = null;
      }
    },

    /**
     * 执行实际的单击逻辑
     * @param {Object} item - 语料项数据
     */
    executeItemClick(item) {
      // 防止重复跳转到当前正在播放的项
      if (this.highlightedItemId === item.id) {
        return;
      }

      // 立即设置高亮并设置保护机制
      this.highlightedItemId = item.id;
      this.clickHighlightProtection = {
        itemId: item.id,
        timestamp: Date.now(),
        duration: 1000 // 1秒保护期
      };

      // 优化：只在需要时才执行滚动，避免不必要的DOM操作
      if (!this.isItemInViewport(item.id)) {
        this.scrollToTargetItem(item.id, 'immediate');
      }

      // 执行音频跳转
      this.jumpToAudioTime(item);
    },

    /**
     * 跳转到音频指定时间并播放
     * @param {Object} item - 语料项数据
     */
    jumpToAudioTime(item) {
      if (!item.startTime) {
        return;
      }

      // 优化：直接计算时间，避免除法操作
      const startTimeInMillis = this.parseTimeToMillis(item.startTime);
      const startTimeInSeconds = startTimeInMillis * 0.001; // 乘法比除法快

      // 优化：减少对象创建，只传递必要数据
      const jumpPayload = {
        time: startTimeInSeconds,
        itemId: item.id
      };

      // 使用已有的事件管理器或$context（优先使用事件管理器）
      if (this.eventManager) {
        this.eventManager.emit(EVENT_NAMES.CORPUS_AUDIO_JUMP_TO, jumpPayload);
      } else if (this.$context) {
        this.$context.$emit(EVENT_NAMES.CORPUS_AUDIO_JUMP_TO, jumpPayload);
      }
    },

    /**
     * 解析时间字符串为毫秒数
     * @param {string|number} timeString - 时间字符串或数字
     * @returns {number} 毫秒数
     */
    parseTimeToMillis(timeString) {
      // 如果已经是数字，直接返回
      if (typeof timeString === 'number') {
        return timeString;
      }
      
      if (!timeString || typeof timeString !== 'string') {
        return 0;
      }

      // 简单缓存机制
      if (!this._timeParseCache) {
        this._timeParseCache = new Map();
      }
      
      if (this._timeParseCache.has(timeString)) {
        return this._timeParseCache.get(timeString);
      }

      const cleanTimeString = timeString.trim();
      const timeParts = cleanTimeString.split(':');
      
      let hours = 0;
      let minutes = 0;
      let seconds = 0;
      let milliseconds = 0;

      if (timeParts.length === 2) {
        // 格式: MM:SS 或 MM:SS.mmm
        minutes = parseInt(timeParts[0], 10) || 0;
        const secondsPart = timeParts[1];
        
        if (secondsPart.includes('.')) {
          const [sec, ms] = secondsPart.split('.');
          seconds = parseInt(sec, 10) || 0;
          milliseconds = parseInt(ms.padEnd(3, '0').slice(0, 3), 10) || 0;
        } else {
          seconds = parseInt(secondsPart, 10) || 0;
        }
      } else if (timeParts.length === 3) {
        // 格式: HH:MM:SS 或 HH:MM:SS.mmm
        hours = parseInt(timeParts[0], 10) || 0;
        minutes = parseInt(timeParts[1], 10) || 0;
        const secondsPart = timeParts[2];
        
        if (secondsPart.includes('.')) {
          const [sec, ms] = secondsPart.split('.');
          seconds = parseInt(sec, 10) || 0;
          milliseconds = parseInt(ms.padEnd(3, '0').slice(0, 3), 10) || 0;
        } else {
          seconds = parseInt(secondsPart, 10) || 0;
        }
      } else {
        return 0;
      }

      // 转换为总毫秒数
      const result = (hours * 3600 + minutes * 60 + seconds) * 1000 + milliseconds;
      
      // 缓存结果，限制缓存大小避免内存泄漏
      if (this._timeParseCache.size < 200) {
        this._timeParseCache.set(timeString, result);
      }
      
      return result;
    },

    /**
     * 滚动到目标语料项
     * @param {string} itemId - 语料项ID
     * @param {string} scrollType - 滚动类型：'immediate'(立即) | 'auto'(自动判断)
     */
    scrollToTargetItem(itemId, scrollType = 'immediate') {
        if (!itemId) return;

        if (scrollType === 'immediate') {
            // 立即滚动 - 用于快进/快退/拖拽操作
            this.performImmediateScroll(itemId);
        } else {
            // 自动判断滚动 - 用于音频播放时的实时跟随
            this.handleAutoPlayScroll(itemId);
        }
    },

    /**
     * 执行立即滚动（快进/快退/拖拽时使用）
     * @param {string} itemId - 语料项ID
     */
    performImmediateScroll(itemId) {
        // 优化：缓存DOM查询结果
        const scrollbar = this.$refs.listScrollRef;
        if (!scrollbar || !scrollbar.$el) return;

        const itemElement = this.$el.querySelector(`li[data-id="${itemId}"]`);
        if (!itemElement) return;

        // 直接滚动到目标位置，使用更快的scrollIntoView
        try {
            itemElement.scrollIntoView({ 
                behavior: 'auto', // 改为auto减少动画卡顿
                block: 'center' 
            });
        } catch (error) {
            // 降级方案：使用传统滚动方式
            const scrollContainer = scrollbar.$el.querySelector('.el-scrollbar__wrap');
            if (scrollContainer) {
                const containerRect = scrollContainer.getBoundingClientRect();
                const itemRect = itemElement.getBoundingClientRect();
                const relativeTop = itemRect.top - containerRect.top + scrollContainer.scrollTop;
                const offset = (scrollContainer.clientHeight - itemRect.height) / 2;
                const scrollTop = Math.max(0, relativeTop - offset);
                
                scrollContainer.scrollTop = scrollTop;
            }
        }
    },

    /**
     * 处理音频播放时的自动滚动
     * @param {string} itemId - 语料项ID
     */
    handleAutoPlayScroll(itemId) {
        const isInViewport = this.isItemInViewport(itemId);
        
        if (isInViewport) {
            // 语料项在可视区域内：隐藏按钮，检查是否需要微调位置
            this.showCurrentPositionBtnInternal = false;
            this.considerAutoScroll(itemId);
        } else {
            // 语料项不在可视区域：显示按钮，不自动滚动
            this.showCurrentPositionBtnInternal = true;
        }
    },

    /**
     * 检查并更新按钮状态（用于音频暂停/结束时）
     * @param {string} itemId - 当前高亮项ID
     */
    checkAndUpdateButtonState(itemId) {
        if (!itemId) {
            this.showCurrentPositionBtnInternal = false;
            return;
        }
        
        const isInViewport = this.isItemInViewport(itemId);
        this.showCurrentPositionBtnInternal = !isInViewport;
    },

    /**
     * 考虑是否需要自动滚动
     * 只有在语料项虽然在可视区域但位置不够理想时才滚动
     * @param {string} itemId - 语料项ID
     */
    considerAutoScroll(itemId) {
        // 防止在用户手动滚动时自动滚动
        if (this.isScrolling || this.isAutoScrolling) {
            return;
        }

        // 检查语料项是否在理想位置（可视区域的上半部分）
        if (this.isItemInIdealPosition(itemId)) {
            return; // 位置理想，不需要滚动
        }

        // 执行温和的自动滚动
        this.performAutoScroll(itemId);
    },

    /**
     * 检查语料项是否在理想位置
     * @param {string} itemId - 语料项ID
     * @returns {boolean} 是否在理想位置
     */
    isItemInIdealPosition(itemId) {
        const scrollbar = this.$refs.listScrollRef;
        if (!scrollbar || !scrollbar.$el) return true;

        const scrollContainer = scrollbar.$el.querySelector('.el-scrollbar__wrap');
        if (!scrollContainer) return true;

        const itemElement = this.$el.querySelector(`li[data-id="${itemId}"]`);
        if (!itemElement) return true;

        const containerRect = scrollContainer.getBoundingClientRect();
        const itemRect = itemElement.getBoundingClientRect();

        // 检查语料项是否在可视区域的上半部分（理想位置）
        const containerCenter = containerRect.top + containerRect.height * 0.5;
        return itemRect.top >= containerRect.top && itemRect.bottom <= containerCenter;
    },

    /**
     * 执行自动滚动
     * @param {string} itemId - 语料项ID
     */
    performAutoScroll(itemId) {
        this.isAutoScrolling = true;

        this.$nextTick(() => {
            const scrollbar = this.$refs.listScrollRef;
            const itemElement = this.$el.querySelector(`li[data-id="${itemId}"]`);

            if (!itemElement || !scrollbar || !scrollbar.$el) {
                this.isAutoScrolling = false;
                return;
            }

            const scrollContainer = scrollbar.$el.querySelector('.el-scrollbar__wrap');
            if (!scrollContainer) {
                this.isAutoScrolling = false;
                return;
            }

            const itemRect = itemElement.getBoundingClientRect();
            const containerRect = scrollContainer.getBoundingClientRect();

            // 计算滚动位置 - 让目标元素在可视区域的上1/4位置
            const relativeTop = itemRect.top - containerRect.top + scrollContainer.scrollTop;
            const targetScrollTop = relativeTop - containerRect.height * 0.25;

            // 限制滚动范围
            const maxScrollTop = scrollContainer.scrollHeight - scrollContainer.clientHeight;
            const finalScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));

            // 执行平滑滚动
            scrollContainer.scrollTo({
                top: finalScrollTop,
                behavior: 'smooth'
            });

            // 滚动完成后重置状态
            setTimeout(() => {
                this.isAutoScrolling = false;
            }, 600);
        });
    },

    /**
     * 回到当前播放位置
     */
    goToCurrentPosition() {
        if (this.highlightedItemId) {
            this.scrollToHighlightedItem(this.highlightedItemId);
        }
    },

    /**
     * 检测语料项是否在可视区域内
     * @param {string} itemId - 语料项ID
     * @returns {boolean} 是否在可视区域内
     */
    isItemInViewport(itemId) {
        if (!itemId) return false;

        // 优化：缓存DOM查询结果，减少重复查询
        const scrollbar = this.$refs.listScrollRef;
        if (!scrollbar || !scrollbar.$el) return false;

        const scrollContainer = scrollbar.$el.querySelector('.el-scrollbar__wrap');
        if (!scrollContainer) return false;

        const itemElement = this.$el.querySelector(`li[data-id="${itemId}"]`);
        if (!itemElement) return false;

        // 优化：使用更高效的位置检查
        const containerRect = scrollContainer.getBoundingClientRect();
        const itemRect = itemElement.getBoundingClientRect();

        // 检查语料项是否完全或部分在可视区域内
        return itemRect.bottom > containerRect.top && itemRect.top < containerRect.bottom;
    },

    /**
     * 滚动到指定高亮项
     * @param {string} itemId - 语料项ID
     */
    scrollToHighlightedItem(itemId) {
        this.$nextTick(() => {
            const scrollbar = this.$refs.listScrollRef;
            const itemElement = this.$el.querySelector(`li[data-id="${itemId}"]`);

            if (!itemElement || !scrollbar || !scrollbar.$el) return;

            const scrollContainer = scrollbar.$el.querySelector('.el-scrollbar__wrap');
            if (!scrollContainer) return;
            
            const itemRect = itemElement.getBoundingClientRect();
            const containerRect = scrollContainer.getBoundingClientRect();

            if (itemRect.top < containerRect.top || itemRect.bottom > containerRect.bottom) {
                  itemElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    },

    /**
     * 根据时间查找对应的语料项
     * @param {number} timeInMillis - 时间（毫秒）
     * @returns {Object|null} 匹配的语料项
     */
    findItemByTime(timeInMillis) {
        const items = this.displayItems;
        if (!items?.length) return null;

        for (let i = 0; i < items.length; i++) {
            const item = items[i];
            const startTimeMs = this.parseTimeToMillis(item.startTime);
            
            // 如果有endTime，使用时间范围匹配
            if (item.endTime) {
                const endTimeMs = this.parseTimeToMillis(item.endTime);
                if (startTimeMs <= timeInMillis && endTimeMs >= timeInMillis) {
                    return item;
                }
            } else {
                // 如果没有endTime，使用容错匹配（前后250ms范围内）
                if (Math.abs(startTimeMs - timeInMillis) <= 250) {
                    return item;
                }
            }
        }
        
        return null;
    },

    /**
     * 根据时间查找最接近的语料项（用于跳转操作）
     * @param {number} timeInMillis - 时间（毫秒）
     * @returns {Object|null} 最接近的语料项
     */
    findClosestItemByTime(timeInMillis) {
        const items = this.displayItems;
        if (!items?.length) return null;

        let closestItem = items[0];
        let minDistance = Math.abs(this.parseTimeToMillis(closestItem.startTime) - timeInMillis);

        // 使用for循环替代for...of，性能更好
        for (let i = 1; i < items.length; i++) {
            const startTimeMs = this.parseTimeToMillis(items[i].startTime);
            const distance = Math.abs(startTimeMs - timeInMillis);
            
            if (distance < minDistance) {
                minDistance = distance;
                closestItem = items[i];
                
                // 如果找到完全匹配的项，直接返回
                if (distance === 0) break;
            }
        }

        return closestItem;
    },

    /**
     * 处理音频时间更新事件
     * @param {Object|number} eventData - 事件数据
     */
    handleAudioTimeUpdate(eventData) {
        // 兼容旧格式（直接传递时间）和新格式（对象格式）
        let currentTimeInSeconds, source;
        if (typeof eventData === 'object' && eventData !== null) {
            currentTimeInSeconds = eventData.currentTime;
            source = eventData.source || 'normal';
        } else {
            currentTimeInSeconds = eventData;
            source = 'normal';
        }

        if (!this.displayItems || this.displayItems.length === 0) {
            return;
        }

        // 记录最后一次时间更新，用于判断播放状态
        this.lastTimeUpdate = Date.now();
        
        // 清除之前的超时检查
        if (this.timeUpdateTimeout) {
            clearTimeout(this.timeUpdateTimeout);
        }

        // 设置超时检查：如果1秒内没有新的时间更新，认为音频已暂停或结束
        this.timeUpdateTimeout = setTimeout(() => {
            // 音频暂停或结束时，重新检查按钮状态
            if (this.highlightedItemId) {
                this.checkAndUpdateButtonState(this.highlightedItemId);
            }
        }, 1000);

        // 查找当前时间对应的语料项
        const currentTimeInMillis = currentTimeInSeconds * 1000;
        let targetItem = this.findItemByTime(currentTimeInMillis);
        
        // 如果没找到精确匹配，尝试找最接近的语料项
        if (!targetItem) {
            targetItem = this.findClosestItemByTime(currentTimeInMillis);
        }
        
        const newHighlightedId = targetItem ? targetItem.id : null;

        // 检查是否在点击高亮保护期内
        const now = Date.now();
        const isInProtectionPeriod = this.clickHighlightProtection.itemId && 
                                   (now - this.clickHighlightProtection.timestamp) < this.clickHighlightProtection.duration;

        // 当高亮项发生变化时
        if (this.highlightedItemId !== newHighlightedId) {
            // 如果在保护期内且当前高亮就是点击的项，不改变高亮
            if (isInProtectionPeriod && this.highlightedItemId === this.clickHighlightProtection.itemId) {
                console.log('点击高亮保护期内，忽略时间更新事件:', {
                    currentHighlight: this.highlightedItemId,
                    newHighlight: newHighlightedId,
                    protectionItem: this.clickHighlightProtection.itemId,
                    timeRemaining: this.clickHighlightProtection.duration - (now - this.clickHighlightProtection.timestamp)
                });
                return;
            }

            // 检查目标项是否处于编辑状态
            const isTargetEditing = newHighlightedId && this.isItemInEditingState && 
                                   this.isItemInEditingState({ id: newHighlightedId });
            
            // 如果目标项不在编辑状态，则可以设置高亮
            if (!isTargetEditing) {
                this.highlightedItemId = newHighlightedId;
                
                // 处理新的高亮项 - 根据事件来源决定滚动方式
                if (newHighlightedId) {
                    if (source === 'seek') {
                        // 跳转操作（快进/快退/拖拽）- 立即滚动
                        this.scrollToTargetItem(newHighlightedId, 'immediate');
                    } else {
                        // 正常播放 - 智能滚动
                        this.scrollToTargetItem(newHighlightedId, 'auto');
                    }
                }
            } else {
                // 如果目标项在编辑状态，不设置新的高亮，保持编辑状态的优先级
                // 但保留当前已有的音频高亮状态，因为编辑其他语料项不应影响当前音频高亮
            }
        }

        // 清理过期的保护期
        if (isInProtectionPeriod && (now - this.clickHighlightProtection.timestamp) >= this.clickHighlightProtection.duration) {
            this.clickHighlightProtection.itemId = null;
            this.clickHighlightProtection.timestamp = 0;
        }
    },

    /**
     * 立即检查当前位置按钮状态
     * 供防抖函数调用的核心逻辑
     */
    _checkCurrentPositionInternal() {
      if (this.highlightedItemId) {
        this.checkAndUpdateButtonState(this.highlightedItemId);
      }
    },

    /**
     * 初始化文本选择跟踪
     */
    initTextSelectionTracking() {
      // 简化：只监听基本的鼠标事件用于记录时间
      const listContainer = this.$el.querySelector('.list-container');
      if (listContainer) {
        listContainer.addEventListener('mousedown', this.handleMouseDown, { passive: true });
        listContainer.addEventListener('mouseup', this.handleMouseUp, { passive: true });
      }
    },

    /**
     * 处理鼠标按下事件
     * @param {MouseEvent} event - 鼠标事件
     */
    handleMouseDown(event) {
      // 简化：只记录鼠标按下时间
      const messageText = event.target.closest('.message-text');
      if (messageText) {
        this.userInteractionState.mouseDownTime = Date.now();
      }
    },

    /**
     * 处理鼠标抬起事件
     * @param {MouseEvent} event - 鼠标事件
     */
    handleMouseUp(event) {
      // 简化：记录鼠标抬起时间
      const messageText = event.target.closest('.message-text');
      if (messageText) {
        this.userInteractionState.mouseUpTime = Date.now();
      }
    },

    /**
     * 检查用户是否正在进行文本选择操作
     * @returns {boolean} 是否正在选择文本
     */
    isUserSelecting() {
      // 简化：只检查是否有活跃的文本选择
      return window.getSelection && window.getSelection().toString().trim().length > 0;
    },

    /**
     * 轻量级的文本选择检查
     * @returns {boolean} 是否最近有文本选择
     */
    isQuickTextSelection() {
      // 简化检查：只检查最近的选择状态，减少计算开销
      const now = Date.now();
      return this.userInteractionState.lastSelectionTime && 
             (now - this.userInteractionState.lastSelectionTime) < 200; // 缩短到200ms
    },

    /**
     * 销毁文本选择跟踪
     */
    destroyTextSelectionTracking() {
      const listContainer = this.$el?.querySelector('.list-container');
      if (listContainer) {
        listContainer.removeEventListener('mousedown', this.handleMouseDown);
        listContainer.removeEventListener('mouseup', this.handleMouseUp);
      }
    },

    /**
     * 检查语料项是否处于编辑状态
     * @param {Object} item - 语料项数据
     * @returns {boolean} 是否处于编辑状态
     */
    isItemInEditingState(item) {
      // 优先检查响应式编辑状态数据（O(1)查找，最快、无卡顿）
      if (this.editingItemIds[item.id]) {
        return true;
      }

      // 检查用户名编辑状态
      if (this.userEditState.editingUsername && 
          this.userEditState.editingUsername.id === item.id) {
        return true;
      }

      return false;
    },

    /**
     * 更新语料项的编辑状态
     * 使用响应式数据管理，避免$forceUpdate造成的卡顿
     * @param {string} itemId - 语料项ID
     * @param {boolean} isEditing - 是否进入编辑状态
     */
    updateItemEditingState(itemId, isEditing) {
      if (isEditing) {
        // 进入编辑状态 - 使用Vue.set确保响应式
        this.$set(this.editingItemIds, itemId, true);
      } else {
        // 退出编辑状态 - 使用Vue.delete确保响应式
        this.$delete(this.editingItemIds, itemId);
      }
    },

    /**
     * @deprecated 已被updateItemEditingState替代，避免使用$forceUpdate
     * 强制更新语料项的CSS类状态
     * @param {string} itemId - 语料项ID
     */
    forceUpdateItemClass(itemId) {
      // 向后兼容：检查DOM状态并更新响应式数据
      // 移除$nextTick以减少延迟
      const itemElement = this.$el?.querySelector(`li[data-id="${itemId}"]`);
      const isEditing = !!(itemElement && (
        itemElement.querySelector('.editable-content-wrapper') ||
        itemElement.querySelector('.username.editing') ||
        itemElement.querySelector('[contenteditable="true"]')
      ));
      this.updateItemEditingState(itemId, isEditing);
    },
  }
}; 