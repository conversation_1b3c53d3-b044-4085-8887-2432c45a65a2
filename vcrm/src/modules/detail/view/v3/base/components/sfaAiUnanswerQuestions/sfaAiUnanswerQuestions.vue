<template>
  <div class="sfa-ai-unanswer-questions-container">
    <div class="unanswer-questions-box">
          <div class="header">
              <div class="header-left">
                  <!-- <span class='fx-icon-obj-app376'></span> -->
                  <span class='title-detail'>{{$t('sfa.ai.unanswer.question.suggest.topic')}}（{{unaskedNum}}/{{dataList.length}}）</span>
                  <fx-tooltip :open-delay="600" effect="light" :content="$t('sfa.ai.unanswer.question.suggest.topic.tip')" placement="bottom">
                      <span class="fx-icon-question"></span>
                  </fx-tooltip>
              </div>
              <div class="header-right">
                <div class="header-right-left">
                    <div class="selector-label-box" v-if="relatedObjectOptions.length > 1">
                        <!-- <span class="selector-label">{{ $t('sfa.ai.suggest.related.object.text') }}</span> -->
                        <fx-select
                            v-model="relatedObjectName"
                            :options="relatedObjectOptions"
                            size="micro"
                            filterable
                            @change="onChange"
                        ></fx-select>
                    </div>
                </div>
                <div class="header-right-center-line" v-if="relatedObjectOptions.length > 1"></div>
                <div class="header-right-right" @click="toggleExpand">
                    <span class="collapse-type-text">{{ isExpanded ? $t('收起') : $t('展开') }}</span>
                    <span v-if='!isExpanded' class="fx-icon-arrow-down2"></span>
                    <span v-if='isExpanded' class="fx-icon-arrow-up2"></span>
                </div>
              </div>
          </div>
          <transition
              name="expand-animation"
              @before-enter="beforeEnter"
              @enter="enter"
              @leave="leave"
          >
              <div v-if="isExpanded" class="data-rows">
                  <div class="expanded-list">
                  <div class="question-item-box" v-for="(item, index) in dataList" :key="index">
                      <div class="question-item-container" :class="[item.questionType == 'not_asked' ? 'unanswer-item' : 'answer-item']">
                          <!-- <div class="item-question-type"><fx-tag :type="[item.questionType == 1 ? 'danger' : 'success']" effect="light-noborder" size="mini" >{{ item.questionType == 1 ? $t('sfa.ai.unanswer.question.unasked') : $t('sfa.ai.unanswer.question.asked') }}</fx-tag></div> -->
                          <div class="item-question-type"><fx-tag :type="item.questionType" effect="light-noborder" size="mini" >{{ item.questionTypeLabel }}</fx-tag></div>
                          <div class="item-question-content">
                              <span class="question-number">{{`${item.questionNumber}、`}}</span><span class="question-text">{{ item.text }}</span>
                          </div>
                      </div>
                  </div>
              </div>
              </div>
          </transition>
          <!-- 收起状态下显示第一条 -->
          <div v-if="!isExpanded" class="data-rows">
              <div class="question-item-box" v-for="(item, index) in dataList.slice(0,1)" :key="index">
                  <div class="question-item-container" :class="[item.questionType == 'not_asked' ? 'unanswer-item' : 'answer-item']">
                      <!-- <div class="item-question-type"><fx-tag :type="[item.questionType == 1 ? 'danger' : 'success']" effect="light-noborder" size="mini" >{{ item.questionType == 1 ? $t('sfa.ai.unanswer.question.unasked') : $t('sfa.ai.unanswer.question.asked') }}</fx-tag></div> -->
                      <div class="item-question-type"><fx-tag :type="item.questionType" effect="light-noborder" size="mini" >{{ item.questionTypeLabel }}</fx-tag></div>
                      <div class="item-question-content">
                          <span class="question-number">{{ `${item.questionNumber}、` }}</span><span class="question-text">{{ item.text }}</span>
                      </div>
                  </div>
              </div>
          </div>
    </div>
  </div>
</template>
<script>

export default {
  props: {
      dataList: {
          type: Array,
          default: () => [
          ],
      },
      clickItem: {
          type: Function,
          default: () => {
          },
      },
      fishImg: {
        type: String,
        defalut: ''
      },
      dataId: {
        type: String,
        default: ''
      },
      apiName: {
        type: String,
        default: 'AccountObj'
      },
      questionType: {
        type: String,
        default: 'all'
      },
      unaskedNum: {
        type: Number,
        default: 0
      },
      relatedObjectOptions: {
        type: Array,
        default: () => []
      },
      relatedObjectName: {
        type: String,
        default: ''
      }
  },
  data() {
    return {
      isExpanded: false, // 控制展开/收起状态
    };
  },
  mounted() {
    // 等待组件完全渲染后发送通知
    this.$nextTick(() => {
      console.log('未回答问题组件已加载完成，发送通知');
      this.$emit('component-loaded');
    });
  },
  watch: {
    questionType(newVal, oldVal) {
      if (newVal != oldVal) {
        this.isExpanded = false;
      }
    },
    // relatedObjectName(newVal, oldVal) {
    //   if (newVal !== oldVal) {
    //     console.log('Related object changed:', oldVal, '->', newVal);
    //     // 当关联对象改变时，自动收起展开状态
    //     this.isExpanded = false;
    //   }
    // }
  },
  methods: {
    init() {
    //   this.fetchUnanswerQuestionData();
    },
    // fetchUnanswerQuestionData() {
    //   let me = this;
    //   const data = {
    //     "objectApiName": this.apiName,
    //     "objectDataId": this.dataId,
    //   };
    //   CRM.util.FHHApi({
    //     url: '/EM1HNCRM/API/v1/object/interact_question/service/get_suggested_topic',
    //     data,
    //     success: function (res) {
    //         console.log(res, 'res----sfaAiUnanswerQuestions');
    //         if (res.Result.StatusCode === 0 && res.Value.dataList) {
    //             let dataObj = {};
    //             res.Value.dataList.forEach(item => {
    //                 dataObj[item.type] = item.num;
    //             })
    //             me.questionTypesData.forEach(itemData => {
    //                 itemData.num = dataObj[itemData.type]
    //             })
    //         }
    //     }
    //   }, {
    //   errorAlertModel: 1
    //   });
    // },
    changeCollapseType(name) {
      this.collapseType = name;
    },
    onChange(value) {
      // 当选择的关联对象改变时，向父组件发送事件
      this.$emit('change-related-object', value);
    },
    toggleExpand() {
        // 切换展开/收起状态
        console.log(this.dataList, 'datalist');
        this.isExpanded = !this.isExpanded;
        this.$nextTick(() => {
            // 向父组件发送展开/收起状态变化的事件
            this.$emit('toggle-expand', this.isExpanded);
        });

    },
      beforeEnter(el) {
          el.style.height = '0';
          el.style.opacity = '0';
          el.style.overflow = 'hidden';
      },
      enter(el) {
      el.style.transition = 'height 0.3s ease, opacity 0.3s ease';
      el.style.height = `${el.scrollHeight}px`;
      el.style.opacity = '1';
      },
      leave(el) {
      el.style.transition = 'height 0.3s ease, opacity 0.3s ease';
      el.style.height = '0';
      el.style.opacity = '0';
      },

    showAllContent(e) {
      e.target.parentNode.classList.toggle("is-ellipsis");
      e.target.style.display = 'none';
      e.target.previousElementSibling.style.display = 'none';  // 让省略号消失
    }
  }
};
</script>

<style lang="less" scoped>
  .sfa-ai-unanswer-questions-container{
      border-radius: 8px;
      background-image: linear-gradient(#FFF, #FFF), linear-gradient(270deg,#acb0fa 0,#98ccfc 49.5%,#c58ef8 100%);
      background-clip: padding-box, border-box;
      background-origin: padding-box, border-box;
      border: 1px solid transparent;
      margin-bottom: 8px;
      padding: 8px 12px;

      // .el-collapse{
      //     border-top: none;
      //     border-bottom: none;

      // }
      // .el-collapse-item__header{
      //     border-bottom: none;
      //     padding-right: 0px;
      //     padding-left: 0px;
      //     .header-action{
      //         right: 15px;
      //     }
      // }
      // .el-collapse-item:last-child{
      //     margin-bottom: 0px;
      // }
      // .el-collapse-item__arrow{
      //     margin-right: 0;
      // }
      // .el-collapse-item__arrow::before{
      //     color: var(--color-info06) !important;
      // }

      .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          .header-left{
              height: 18px;
              line-height: 18px;
              .fx-icon-obj-app376, .fx-icon-question{
                font-size: 12px;
              }
              .fx-icon-obj-app376::before{
                  color: var(--color-neutrals19);
              }
          }
          .header-right {
              font-size: 14px;
              color: #007aff;
              cursor: pointer;
              display: flex;
              align-items: center;
              gap: 8px;
              .header-right-left{
                display: flex;
                align-items: center;
                .selector-label-box{
                    background-color: #fff;
                    box-sizing: border-box;
                    border-radius: 8px;
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    .selector-label {
                        color: var(--color-neutrals19);
                        font-size: 12px;
                        font-weight: 400;
                        white-space: nowrap;
                    }
                    .fx-select{
                        width: 80px;
                        background: #F2F4FB;
                        border-radius: 4px;
                        // height: 20px;
                    }
                    /deep/ .el-input__inner {
                        border: none;
                        color: var(--color-neutrals19);
                        font-weight: 400;
                        line-height: 20px;
                        // height: 20px;
                        font-size: 13px;
                        background: #F2F4FB;
                    }
                }
              }
              .header-right-center-line{
                display: inline-block;
                position: relative;
                top: 0px;
                width: 1px;
                height: 8px;
                background: #dee1e8;
              }
              .header-right-right{
                display: flex;
                align-items: center;
              }
              .arrow {
                  margin-left: 4px;
              }
          }
      }
      .unanswer-questions-box {
          position: relative; /* 让子元素的绝对定位基于容器 */
          width: 100%;        /* 容器宽度可调整 */
          word-wrap: break-word; /* 文本内容自动换行 */
          font-size: 15px;
          font-style: normal;
          font-weight: 700;
          line-height: 24px;
          .expanded-list{
              max-height: 214px;
              overflow-y: auto;
              overflow-x: hidden;

                &::-webkit-scrollbar {
                    width: 5px; /* 设置滚动条的宽度 */
                }
                &::-webkit-scrollbar-thumb {
                    background-color: transparent; /* 设置滚动条滑块背景颜色 */
                    border-radius: 5px;
                    min-height: 50px !important; /* 最小高度设置 */
                    max-height: 50px !important; /* 最大高度设置 */
                }
                &::-webkit-scrollbar-track{
                    background-color: transparent; /* 设置滚动条轨道背景颜色 */
                    border-radius: 5px;
                }
          }

          /* 当鼠标悬停在expanded-list上时显示滚动条 */
          .expanded-list:hover::-webkit-scrollbar,
          .expanded-list:active::-webkit-scrollbar {
              width: 5px; /* 显示滚动条宽度 */
          }

          /* 悬停时的滚动条滑块样式 */
          .expanded-list:hover::-webkit-scrollbar-thumb,
          .expanded-list:active::-webkit-scrollbar-thumb {
              background-color: #888; /* 滑块颜色 */
              border-radius: 5px; /* 滑块圆角 */
              min-height: 50px !important; /* 最小高度设置 */
              max-height: 50px !important; /* 最大高度设置 */
          }

          /* 悬停时的滚动条轨道样式 */
          .expanded-list:hover::-webkit-scrollbar-track,
          .expanded-list:active::-webkit-scrollbar-track {
              background-color: #f1f1f1; /* 轨道颜色 */
              border-radius: 5px;
          }
          .fx-icon-arrow-right{
              font-size: 12px;
          }
          .fx-icon-arrow-right::before{
              color: var(--color-info06) !important;
          }
          .collapse-type-text{
              color: var(--color-info06);
              font-size: 12px;
              font-weight: 400;
              margin-right: 4px;
              line-height: 18px;
              display: inline-block;
            //   min-width: 48px;
              text-align: center;
          }
          .fx-icon-arrow-down2{
              font-size: 12px;
          }
          .fx-icon-arrow-down2::before{
              color: var(--color-info06);
          }
          .fx-icon-arrow-up2{
              font-size: 12px;
          }
          .fx-icon-arrow-up2::before{
              color: var(--color-info06);
          }
          .fx-icon-tiwen{
              position: relative;
              top: 2px;
              font-size: 14px;
          }
          .title-detail{
              color: var(--color-neutrals19);
              font-size: 12px;
              font-weight: 400;
              line-height: 18px;
              display: inline-block;
          }
          .fx-icon-obj-app376{
              font-size: 14px;
          }
      }
      .question-item-box{
          color: var(--Text-H1, #181C25);
          font-size: 13px;
          font-weight: 400;
          line-height: 18px;
          margin-bottom: 4px;
          .question-item-container{
              display: flex;
              .item-question-type{
                  margin-right: 4px;
              }
              .question-number{
                  display: inline-block;
                  line-height: 20px;
              }
              .question-text{
                  // display: inline-block;
                  line-height: 20px;
              }
          }
          &:last-child {
              margin-bottom: 0;
          }

      }
      /* 动画样式 */
      .expand-animation-enter-active,
      .expand-animation-leave-active {
          transition: height 0.3s ease, opacity 0.3s ease;
      }

      .expand-animation-enter-to {
      height: auto;
      opacity: 1;
      }

      .expand-animation-leave-to {
      height: 0;
      opacity: 0;
      }
  }
</style>
