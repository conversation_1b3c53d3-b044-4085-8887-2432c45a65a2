<template>
    <common-card
        :title="$t('sfa.aiCustomerProfile.cardTitle.dimensionDisadvantage')"
        icon="fx-icon-f-obj-app323"
        iconColor="#FFCA2B"
        class="disadvantage-card"
    >
        <common-data-fetcher-status
            :loading="loading"
            :error="error"
            :data="disadvantages"
        >
            <common-text-list-display :list="disadvantages" :showIndex="!!currentDimension" />
        </common-data-fetcher-status>
    </common-card>
</template>

<script>
import {CommonCard, CommonDataFetcherStatus, CommonTextListDisplay} from './index'
import { getCustomerDisadvantages } from '../services/profileService';
export default {
    name: 'ProfileDimensionDisadvantage',
    components: {
        CommonTextListDisplay,
        CommonDataFetcherStatus,
        CommonCard
    },
    props: {
        currentDimension: {
            type: String,
            default: ''
        },
        currentProfileId: {
            type: String,
            default: ''
        }
    },
    watch: {
        currentDimension: {
            handler() {
                this.fetchData();
            },
            immediate: true
        },
        currentProfileId: {
            handler() {
                this.fetchData();
            },
            immediate: true
        }
    },
    data() {
        return {
            disadvantages: [],
            loading: false,
            error: null,
        };
    },

    methods: {
        fetchData() {
            if (!this.currentProfileId) return;
            this.loading = true;
            this.error = null;
            getCustomerDisadvantages({
                profileId: this.currentProfileId,
                featureDimensionId: this.currentDimension,
            }).then(res => {
                this.disadvantages = res;
            }).catch(err => {
                this.error = err;
            }).finally(() => {
                this.loading = false;
            });
        }
    }
};
</script>

<style lang="less" scoped>

</style> 