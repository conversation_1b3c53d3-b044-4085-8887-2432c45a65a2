<template>
    <div 
        class="common-card" 
        :style="{
            padding: padding,
            borderRadius: radius + 'px',
            background: background,
        }"
    >
        <h3 v-if="title || $slots.title" class="card-title">
            <template v-if="icon">
                <img v-if="icon === 'ai'" src="../assets/ai_generate.svg">
                <span v-else :class="[icon, 'icon', {'custom-color': iconColor}]" :style="{ color: iconColor }"></span>
            </template>
            <slot name="title">{{ title }}</slot>
        </h3>
        <div
            class="card-content"
            :style="{ marginTop: title || $slots.title ? contentMarginTop : '0' }"
        >
            <slot></slot>
        </div>
    </div>
</template>

<script>

export default {
    name: 'CommonCard',
    components: {
    },
    props: {
        // 卡片标题
        title: {
            type: String,
            default: ''
        },
        // 图标class名，当值为'ai'时使用CommonAiIcon组件
        icon: {
            type: String,
            default: ''
        },
        // 图标颜色
        iconColor: {
            type: String,
            default: ''
        },
        // 内边距，支持传入具体数值或css padding格式
        padding: {
            type: String,
            default: '12px'
        },
        // 圆角大小
        radius: {
            type: Number,
            default: 8
        },
        // 背景色
        background: {
            type: String,
            default: 'rgba(255, 255, 255)'
        },
        // 是否显示阴影
        withShadow: {
            type: Boolean,
            default: false
        },
        // 内容上边距
        contentMarginTop: {
            type: String,
            default: '8px'
        }
    }
};
</script>

<style lang="less" scoped>
.common-card {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;

    .card-title {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 700;
        font-size: 13px;
        line-height: 18px;
        color: var(--color-neutrals19);
        margin: 0;

        .icon.custom-color:before {
            color: inherit;
        }
        .icon:before {
            font-size: 16px;
        }
    }

    .card-content {
        flex: 1;
        width: 100%;
        font-size: 13px;
        line-height: 22px;
        color: var(--color-neutrals19);
    }
}
</style>
