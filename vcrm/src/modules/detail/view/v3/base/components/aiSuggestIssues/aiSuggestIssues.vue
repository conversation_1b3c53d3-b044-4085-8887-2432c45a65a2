<template>
  <fx-scrollbar class="sfa-ai-suggest-container">
    <sfa-ai-question-types
      :dataList="questionTypesData"
      @changeQuestionType="changeQuestionType"
      :selectedType.sync="tagsType"
      :tagsTypeList="tagsTypeList"
    />
    <div class="sfa-ai-suggest-bottom">
      <ai-suggest-operate-section
        @toggle-expand="toggleExpand"
        :isExpanded="isExpanded"
        :relatedObjectOptions="relatedObjectOptions"
        :relatedObjectName="relatedObjectName"
        @change-object="changeRelatedObject"
        :showExpand="dataList.length > 1"
        :apiName="apiName"
        :dataId="dataId"
        :detailData="$context.getData()"
        :showSingleSummary="showSingleSummary"
        :openMultipleSummary="openMultipleSummary"
        @change-open-multiple-summary="changeOpenMultipleSummary"
        :showHistoryListIcon="showHistoryListIcon"
      />
      <sfa-ai-question-search
        @search="handleSearch"
        @navigate="handleSearchNavigate"
        ref="searchComponent"
      />
      <sfa-ai-infinite-list
        ref="infiniteList"
        :dataId="dataId"
        :apiName="apiName"
        :noDataListText="$t('sfa.ai.infinite.suggest.list.nodata')"
        :questionType="questionType"
        :loadMore="loadMore"
        :listLoading="listLoading"
        :limit="limit"
        :offset="offset"
        :noMore="noMore"
        :dataList="dataList"
        :isLoading="isLoading"
        :containerSelector="'.sfa-ai-suggest-container'"
      >
        <template v-slot:item-content="{ questionData, showDialog, index }">
          <sfa-ai-question-item
            :questionData="questionData"
            @trigger-dialog="showDialog"
            :dataId="dataId"
            :apiName="apiName"
            :useRightAction=false
            :useKnowledgeAnswer=false
            :useQuestionAvatar=false
            :fxTagType="questionData.fxTagType"
            attitudeFieldName="advice_status_label"
            :isLoading="isLoading"
            :useTopRightButton=false
            relatedFieldName="active_record_id__r"
            @click-related-data="openActiveRecotdDetail"
            :relatedOpenDetail=false
            :keyword="searchKeyword"
            @match-found="handleMatchFound"
            :tagsTypeList="tagsTypeList"
            tagsName="library_tags"
            :isExpanded="isExpanded"
            :useAnswerEdit="useAnswerEdit"
            :itemIndex="index"
            @edit-content-save="handleEditContentSave"
            :openMultipleSummary="openMultipleSummary"
            :userAvaBgColor="userAvaBgColor"
          >
            <!-- <template v-slot:relatedContent="{ questionData }">
              <div class="relative-object-data-box" v-if="questionData.active_record_id__r && questionData.active_record_id__r.length" >
                <div class="relative-object-data-list">
                  <span
                    class="relative-object-data-item-text"
                    v-for="(relatedData, index) in visibleRelatedData(questionData.active_record_id__r)"
                    :key="index"
                    @click="openActiveRecotdDetail(relatedData)"
                  >
                    {{ relatedData.name }}{{ index < Math.min(4, questionData.active_record_id__r.length) - 1 ? ';' : '' }}
                  </span>
                  <fx-tooltip
                    v-if="questionData.active_record_id__r.length > 4"
                    :open-delay="600"
                    effect="light"
                    placement="bottom"
                    popper-class="activity-suggest-issues-tooltip"
                  >
                    <span class="relative-object-data-item-text relative-object-data-item-text-more more-items">...</span>
                    <template #content>
                      <div class="tooltip-content">
                        <span
                          class="relative-object-data-item-text"
                          v-for="(relatedData, index) in remainingRelatedData(questionData.active_record_id__r)"
                          :key="index"
                          @click="openActiveRecotdDetail(relatedData)"
                        >
                          {{ relatedData.name }}{{ index < remainingRelatedData(questionData.active_record_id__r).length - 1 ? ';' : '' }}
                        </span>
                      </div>
                    </template>
                  </fx-tooltip>
                </div>
              </div>
            </template> -->
          </sfa-ai-question-item>
        </template>
      </sfa-ai-infinite-list>
    </div>
  </fx-scrollbar>
</template>
<script>
  import SfaAiInfiniteList from "../sfaAiInfiniteList/sfaAiInfiniteList.vue";
  import SfaAiQuestionTypes from "../sfaAiQuestionTypes/sfaAiQuestionTypes.vue";
  import SfaAiQuestionItem from "../sfaAiQuestionItem/sfaAiQuestionItem.vue";
  import SfaAiQuestionSearch from "../sfaAiQuestionSearch/sfaAiQuestionSearch.vue";
  import AiSuggestOperateSection from "./components/aiSuggestOperateSection.vue";
  import setHeightMixin from '../sfaAiMixin/sfaAiActivityHeightMixin';
  import dataMixin from './mixins/dataMixin';
  import { fetchRelatedObjectList, fetchSuggestListData } from './services/apiService';
  const pageSize = 500;
  const relatedObjectParam = {
    AccountObj: 'account_id',
    ActiveRecordObj: 'active_record_id',
    NewOpportunityObj: 'new_opportunity_id',
    LeadsObj: 'leads_id'
  }
  export default {
    props: ["compInfo", "apiName", "dataId"],
    components: {
      SfaAiInfiniteList,
      SfaAiQuestionTypes,
      SfaAiQuestionItem,
      SfaAiQuestionSearch,
      AiSuggestOperateSection
    },
    mixins: [setHeightMixin, dataMixin],
    data() {
      return {
        questionTypesData: [{
            icon: 'fx-icon-f-obj-app94',
            text: '',
            id: 1,
            num: 0,
            unit: '',
            type: 'total',
            },
            {
            icon: 'fx-icon-ok',
            text: '',
            id: 2,
            num: 0,
            unit: '',
            type: 'clearly_defined',
            tip: $t('sfa.ai.suggest.type.clearly.tip')
            },
            {
            icon: 'fx-icon-warning',
            text: '',
            id: 3,
            num: 0,
            unit: '',
            type: 'unclearly_defined',
            tip: $t('sfa.ai.suggest.type.unclearly.tip')

            },
            {
            icon: 'fx-icon-close-2',
            text: '',
            id: 4,
            num: 0,
            unit: '',
            type: 'not_asked',
            tip: $t('sfa.ai.suggest.type.notasked.tip')
            },
        ],
        isExpanded: true,
        relatedObjectName: '',
        relatedObjectOptions: [],
        questionType: 'total',
        listLoading: true,
        isLoading: false,
        limit: pageSize,
        offset: -pageSize,
        noMore: false,
        dataList: [],
        tagsType: ['all'],
        tagsTypeList: [],
        searchKeyword: '',
        firstMatchFound: false,
        matchedElements: [],
        currentMatchIndex: -1,
        tagsTypeDebounceTimer: null,
        isDebouncing: false,
        matchedItems: new Map(),
        totalMatchCount: 0,
        currentMatchId: null,
        matchIndexMap: [],
        showSingleSummary: true,
        openMultipleSummary: this.handleOpenMultipleSummary(),
        userAvaBgColor: {},
      };
    },
    computed: {

    },
    created() {
        let me = this;
        Fx.util.getBizConfig('sfaVcrmAiSuggestAnswerEdit').then((data) => {
            console.log(data, 'sfaVcrmAiSuggestAnswerEdit');
            me.useAnswerEdit = data;
        });
        FS.MEDIATOR.on('polling.activity_suggest_issues_refresh_all.change', (data, mode) => {
            console.log(data, mode, 'polling----activity_suggest_issues_refresh_all');
            me.init();
        });

    },
    mounted() {
      this.init('init');
      if($('.grid-container .grid-cell .fx-frame-layout-tabs .sfa-ai-suggest-container')) {
        $('.grid-container .grid-cell .fx-frame-layout-tabs .sfa-ai-suggest-container').css('paddingLeft', '6px')
      }
    },
    watch: {
        tagsType: {
            handler(newValue) {
                console.log('tagsType changed:', newValue);
                this.debounceLoadMore();
            }
        }
    },
    methods: {
      async init(type) {
        this.questionType = 'total';
        this.listLoading = true;
        this.limit = pageSize;
        this.offset = -pageSize;
        this.dataList = [];
        this.noMore = false;
        if(type == 'init') {
            const relatedObjectList = await fetchRelatedObjectList(this.apiName, this.$context.getData());
            console.log(relatedObjectList, 'relatedObjectList');
            this.handleRelatedObjectList(relatedObjectList);
        }
        await this.fetchQuestionTypesData(this.loadMore, type);
        await this.fetchSuggestIssuesHistory();
      },
      fetchQuestionTypesData(callback, type) {
        let me = this;
        const detailData = this.$context.getData();
        console.log(detailData, 'detailData');
        const data = {
            // "id": detailData.account_id || detailData._id,
            "active_record_id": this.apiName === 'ActiveRecordObj' ? detailData._id : detailData.active_record_id,
            "account_id": this.apiName === 'AccountObj' ? detailData._id : detailData.account_id,
            "leads_id": this.apiName === 'LeadsObj' ? detailData._id : detailData.leads_id,
            "new_opportunity_id": this.apiName === 'NewOpportunityObj' ? detailData._id : detailData.new_opportunity_id,
            "source_api_name": this.relatedObjectName || this.apiName
        };
        CRM.util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/advice_topic/service/statistic',
            data,
            success: function (res) {
                console.log(res, 'res----advice_topic_count');
                if (res.Result.StatusCode === 0) {
                    if(res.Value?.statistic) {
                        me.questionTypesData.forEach(itemData => {
                            itemData.num = res.Value?.statistic[itemData.type].count || 0
                            itemData.text = res.Value?.statistic[itemData.type].label;
                        })
                    }
                    if(res.Value?.tagsList && res.Value?.tagsList.length) {
                        me.tagsTypeList = res.Value?.tagsList.map(item => {
                            return {
                                label: item,
                                value: item
                            }
                        });
                        me.tagsTypeList.unshift({
                            label: $t('sfa.ai.activity.list.filter.allType'),
                            value: 'all'
                        });
                    }
                }
                if(callback) {
                    callback();
                }
            }
        }, {
            errorAlertModel: 1
        });
      },
      async loadMore() {
        if(this.noMore || this.isLoading || !this.relatedObjectName) {
          return;
        }
        this.isLoading = true;
        const { dataList, total, objectDescribe } = await this.getRelatedList(this.offset += pageSize);

        if(dataList && dataList.length) {
            this.handleAdviceStatus(dataList, objectDescribe);
            this.dataList.push(...dataList);
            if(this.dataList.length >= total && total > 0) this.noMore = true;
        }else{
            this.noMore = true;
        }
        this.listLoading = false;
        this.isLoading = false;
        if (this.LoadingService) {
          this.LoadingService.close();
          this.LoadingService = null;
        }
      },
      getRelatedList(offset = 0) {
        const detailData = this.$context.getData();
        return fetchSuggestListData(this.apiName, detailData, {
          limit: this.limit,
          offset,
          questionType: this.questionType,
          tagsType: this.tagsType,
          relatedObjectName: this.relatedObjectName
        });
      },
      openActiveRecotdDetail(relatedData) {
        CRM.api.show_crm_detail({
            id: relatedData._id,
            apiName: 'ActiveRecordObj',
        })
      },
      toggleExpand() {
        this.isExpanded = !this.isExpanded;
      },
      changeRelatedObject(value) {
        this.relatedObjectName = value;
        this.init();
      },
      handleAdviceStatus(dataList=[], objectDescribe) {

        if(dataList.length) {
          dataList.forEach(item => {
            if(item.order_field) {
                if(item.library_id__r) {
                    if(item.history_flag != 'history') {
                        item.question_content__o = item.order_field + '、' + item.library_id__r;
                    } else {
                        item.question_content__o = item.library_id__r;
                    }
                }
            }
            if(item.library_tags && item.library_tags.length && typeof item.library_tags === 'string') {
                item.library_tags = [item.library_tags];
            }
            if(objectDescribe?.fields?.advice_status?.options?.length){
                if(item.advice_status == 'clearly_defined') {
                    item.fxTagType = 'success'
                }else if(item.advice_status == 'unclearly_defined') {
                    item.fxTagType = 'link'
                } else {
                    item.fxTagType = 'warning'
                }
                const option = objectDescribe.fields.advice_status.options.find(option => option.value ==  item.advice_status);
                if(option) {
                item.advice_status_label = option.label;
                const questionType = this.questionTypesData.find(o => o.type ==  item.advice_status);
                    if(questionType) {
                        item.attitudeIcon = questionType.icon
                    }
                }
            }

          })
        }
      },
      changeQuestionType(newType) {
        this.questionType = newType;
        this.listLoading = true;
        this.limit = pageSize;
        this.offset = -pageSize;
        this.dataList = [];
        this.noMore = false;
        this.loadMore();
      },
      visibleRelatedData(dataArray) {
        return dataArray.slice(0, 2);
      },
      remainingRelatedData(dataArray) {
        return dataArray.slice(2);
      },
      /**
       * 处理搜索事件
       */
      handleSearch({ keyword }) {
        // 清除当前高亮样式
        const previousHighlight = document.querySelector('.activity-search-highlight.current-highlight');
        if (previousHighlight) {
            previousHighlight.classList.remove('current-highlight');
        }

        this.searchKeyword = keyword;
        this.matchedItems.clear();
        this.matchIndexMap = [];
        this.totalMatchCount = 0;
        this.currentMatchId = null;

        if (!keyword) {
            this.$refs.searchComponent.updateMatchCount(0);
            return;
        }

        // 强制让所有项目重新渲染以应用高亮
        this.$nextTick(() => {
            // 创建一个临时变量存储原始数据
            const tempData = [...this.dataList];

            // 清空数据列表然后重新填充，强制所有组件重新渲染
            this.dataList = [];
            this.$nextTick(() => {
                this.dataList = tempData;
                // 确保匹配计数准确
                if (this.totalMatchCount > 0 && this.matchIndexMap.length > 0) {
                    this.currentMatchId = this.matchIndexMap[0];
                    this.scrollToHighlight(this.currentMatchId);
                } else {
                    this.$refs.searchComponent.updateMatchCount(0);
                }
            });
        });
      },

      /**
       * 处理找到匹配项
       */
      handleMatchFound({ itemId, matchCount, questionMatchCount, answerMatchCount }) {
        // 存储每个项的匹配信息
        this.matchedItems.set(itemId, {
          total: matchCount,
          questionCount: questionMatchCount,
          answerCount: answerMatchCount
        });

        // 重新生成匹配项索引映射
        this.matchIndexMap = [];
        for (const [itemId, counts] of this.matchedItems) {
          // 先添加问题内容的匹配
          for (let i = 1; i <= counts.questionCount; i++) {
            this.matchIndexMap.push(`${itemId}-q${i}`);
          }
          // 再添加答案内容的匹配
          for (let i = 1; i <= counts.answerCount; i++) {
            this.matchIndexMap.push(`${itemId}-a${i}`);
          }
        }

        this.totalMatchCount = this.matchIndexMap.length;
        this.$refs.searchComponent.updateMatchCount(this.totalMatchCount);

        // 如果是第一次找到匹配项，自动滚动到第一个匹配
        if (this.totalMatchCount > 0 && !this.currentMatchId) {
          this.currentMatchId = this.matchIndexMap[0];
          this.scrollToHighlight(this.currentMatchId);
        }
      },

      /**
       * 处理搜索导航
       */
      handleSearchNavigate({ currentIndex }) {
        if (this.totalMatchCount === 0 || currentIndex < 0 || currentIndex >= this.totalMatchCount) {
            return;
        }

        // 直接通过索引获取目标匹配项ID
        this.currentMatchId = this.matchIndexMap[currentIndex];
        this.scrollToHighlight(this.currentMatchId);
      },

      /**
       * 滚动到指定的高亮元素
       */
      scrollToHighlight(matchId) {
        this.$nextTick(() => {
            // 移除之前的当前高亮样式
            const previousHighlight = document.querySelector('.activity-search-highlight.current-highlight');
            if (previousHighlight) {
                previousHighlight.classList.remove('current-highlight');
            }

            // 找到新的目标元素并添加当前高亮样式
            const highlightElement = document.querySelector(`[data-id="${matchId}"]`);
            if (!highlightElement) return;

            highlightElement.classList.add('current-highlight');

            const listContainer = this.$refs.infiniteList.$el.querySelector('.list-container');
            if (!listContainer) return;

            const containerRect = listContainer.getBoundingClientRect();
            const elementRect = highlightElement.getBoundingClientRect();
            const relativeTop = elementRect.top - containerRect.top;
            const containerHeight = containerRect.height;

            listContainer.scrollTop = listContainer.scrollTop + relativeTop - (containerHeight / 2) + (elementRect.height / 2);
        });
      },

      // 添加防抖处理方法
      debounceLoadMore() {
        // 如果已经在防抖中，清除之前的定时器
        if (this.tagsTypeDebounceTimer) {
          clearTimeout(this.tagsTypeDebounceTimer);
        }

        // 立即显示加载状态
        if (!this.isDebouncing) {
          this.isDebouncing = true;
        }

        this.tagsTypeDebounceTimer = setTimeout(() => {
          this.limit = pageSize;
          this.offset = -pageSize;
          this.dataList = [];
          this.noMore = false;
          this.listLoading = true;
          this.loadMore().finally(() => {
            this.isDebouncing = false; // 加载完成后重置防抖状态
          });
        }, 800); // 增加到 800ms，给用户足够的多选操作时间
      },

      // 清理防抖定时器和状态
      clearDebounceTimer() {
        if (this.tagsTypeDebounceTimer) {
          clearTimeout(this.tagsTypeDebounceTimer);
          this.tagsTypeDebounceTimer = null;
        }
        this.isDebouncing = false;
      },
      handleEditContentSave(itemData, content, type, callback) {
        console.log(itemData, content, type, 'itemData, content, type');
        let data = {
          data: {
            answer: content
          }
        }
        if(type == 'singleEdit') {
            data.topic_answer_id = itemData.activity_topic_answer._id;
        }else{
            data.question_id = itemData._id;
        }
        return new Promise((resolve, reject) => {
          CRM.util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/advice_topic/service/update',
            data,
            success: (res) => {
              if (res.Result.StatusCode === 0) {
                resolve(res.Value);
                this.$message.success($t("保存成功"));
                callback && callback(content);
              } else {
                this.$message.error({
                    message: res?.Result?.FailureMessage || $t('保存失败'),
                    offset: 200
                });
                reject(res?.Result?.FailureMessage);
              }
            },
            error: (err) => {
                this.$message.error({
                    message: err?.message || $t('保存失败'),
                    offset: 200
                });
                reject(err);
            }
          }, {
            errorAlertModel: 1
          });
        });
      },
      changeOpenMultipleSummary(value) {
        this.openMultipleSummary = value;
        localStorage.setItem(`${this.apiName}_open_multip_summary`, value ? '1' : '0');
      },
      handleOpenMultipleSummary() {
        let localValue = localStorage.getItem(`${this.apiName}_open_multip_summary`);
        if(localValue == undefined) {
            localValue = '1';
            localStorage.setItem(`${this.apiName}_open_multip_summary`, localValue);
        }
        return localValue == '1' ? true : false;
      }
    },
    beforeDestroy() {
      this.clearDebounceTimer();
      FS.MEDIATOR.off('polling.activity_suggest_issues_refresh_all.change');
    }
  };
</script>
<style lang="less">
  .sfa-ai-suggest-container{
    overflow-y: auto;
    font-family: -apple-system, Roboto, Source Han Sans CN, Microsoft YaHei, Microsoft YaHei UI, Arial, sans-serif;
    .el-scrollbar__bar{
      display: none;
    }
    .sfa-ai-suggest-bottom{
      padding: 12px;
      background-color: #f7f9fa;
      border-radius: 8px;
    }
    // .relative-object-data-item-text{
    //   overflow: hidden;
    //   color: var(--color-info06);
    //   text-overflow: ellipsis;
    //   font-size: 12px;
    //   font-weight: 400;
    //   line-height: 18px;
    //   margin-right: 8px;
    //   cursor: pointer;
    //   &.more-items {
    //     color: var(--color-info06);
    //     cursor: pointer;
    //     margin-left: 4px;
    //   }
    // }
    .fx-icon-warning::before{
      color: var(--color-info06);
    }
    .fx-icon-genjinjilu::before{
      color: var(--color-success06);
    }
    .sfa-ai-infinite-list-container .list-container .item-box {
      margin-bottom: 10px;
      .answer-box{
        padding-left: 0;
      }
      .relative-object-data-box{
        padding-left: 0;
      }
    }
  }

  // Add styles for tooltip
 .activity-suggest-issues-tooltip {
    max-width: 400px;
    max-height: 400px;
    overflow-y: auto;
    .el-tooltip__popper {
      padding: 8px 12px;
    }

    .tooltip-content {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;

      .relative-object-data-item-text {
        color: var(--color-info06);
        font-size: 12px;
        font-weight: 400;
        line-height: 18px;
        cursor: pointer;
      }
    }

  }
</style>
