<template>
  <fx-dialog
    :visible.sync="visible"
    :title="$t('sfa.activity.corpus.bind_contact_dialog_title')"
    width="529px"
    custom-class="bind-contact-dialog"
    :close-on-click-modal="false"
    @close="handleClose"
    noHeaderBorderBottom
  >
    <template #headerRightBtn v-if="showTitleRadio">
      <fx-radio-group v-model="titleRadio" size="micro">
        <fx-radio label="single">{{ $t('sfa.activity.corpus.list_item_username_btn_single') }}</fx-radio>
        <fx-radio label="global">{{ $t('sfa.activity.corpus.list_item_username_btn_global') }}</fx-radio>
      </fx-radio-group>
    </template>
    <div class="bind-contact-dialog-inner">
      <!-- 绑定联系人 -->
      <div class="bind-contact-dialog-block">
        <div class="bind-contact-dialog-block-title">
          <span class="fx-icon-f-xiefang bind-contact-dialog-block-title-icon"></span>
          <span class="bold-text">{{ $t('sfa.activity.corpus.audio_popover_bind_toggle_btn') }}</span>
          <span class="tip-text" v-if="!defaultContactObjList.length">{{ tipText('sfa.activity.corpus.bind_item_btn1') }}</span>
          <span class="link-text" :style="{ 'margin-left': !!defaultContactObjList.length ? 'auto' : '' }" @click="openPickselfObject('ContactObj')">{{ linkText('sfa.activity.corpus.bind_item_btn1') }}</span>
        </div>
        <div class="bind-contact-dialog-block-content" v-if="defaultContactObjList.length">
          <div 
            v-for="(item, index) in defaultContactObjList"
            :key="index"
            class="bind-contact-dialog-block-content-item" 
            @click="handleUserSelect(item)"
          >
            <span 
              class="fx-icon-ok-2 selected-icon" 
              v-show="isUserSelected(item)"
            ></span>
            <sfaAiAvatar
              :data="{
                userName: item.name,
                backgroundColor: '#FFA142',
                isAvaDefault: true,
                dataId: item._id,
                useAvatarImg: !!(item.profile_image && item.profile_image.length)
              }"
            />
            <fx-tooltip 
              :content="item.name" 
              :open-delay="400"
              placement="top"
            >
              <span class="block-content-item-name">{{ item.name }}</span>
            </fx-tooltip>
          </div>
        </div>
      </div>
      <!-- 绑定员工 -->
      <div class="bind-contact-dialog-block">
        <div class="bind-contact-dialog-block-title">
          <span class="fx-icon-f-contacts bind-contact-dialog-block-title-icon"></span>
          <span class="bold-text">{{ $t('sfa.activity.corpus.bind_item_btn2') }}</span>
          <span class="tip-text" v-if="!defaultPersonnelObjList.length">{{ tipText('sfa.activity.corpus.bind_item_btn2') }}</span>
          <span class="link-text" :style="{ 'margin-left': !!defaultPersonnelObjList.length ? 'auto' : '' }" @click="openSelectorBox">{{ linkText('sfa.activity.corpus.bind_item_btn2') }}</span>
        </div>
        <div class="bind-contact-dialog-block-content" v-if="defaultPersonnelObjList.length">
          <div
            v-for="(item, index) in defaultPersonnelObjList"
            :key="index"
            class="bind-contact-dialog-block-content-item" 
            @click="handleUserSelect(item)"
          >
            <span 
              class="fx-icon-ok-2 selected-icon" 
              v-show="isUserSelected(item)"
            ></span>
            <sfaAiAvatar
              :data="{
                userName: item.name,
                backgroundColor: '#FFA142',
                isAvaDefault: true,
                dataId: item._id,
                useAvatarImg: !!(item.profile_image && item.profile_image.length),
                avatarImgSrc: getAvaterImgSrc(item),
                personnelId: getPersonnelId(item)
              }"
            />
            <fx-tooltip 
              :content="item.name" 
              :open-delay="400"
              placement="top"
            >
              <span class="block-content-item-name">{{ item.name }}</span>
            </fx-tooltip>
          </div>
        </div>
      </div>
      <!-- 互联用户 -->
      <div class="bind-contact-dialog-block" v-if="isOpenPRM">
        <div class="bind-contact-dialog-block-title">
          <span class="fx-icon-f-obj-app273 bind-contact-dialog-block-title-icon"></span>
          <span class="bold-text">{{ $t('sfa.activity.corpus.bind_item_btn3') }}</span>
          <span class="tip-text" v-if="!defaultPublicEmployeeObjList.length">{{ tipText('sfa.activity.corpus.bind_item_btn3') }}</span>
          <span class="link-text" :style="{ 'margin-left': !!defaultPublicEmployeeObjList.length ? 'auto' : '' }" @click="openPickselfObject('PublicEmployeeObj')">{{ linkText('sfa.activity.corpus.bind_item_btn3') }}</span>
        </div>
        <div class="bind-contact-dialog-block-content" v-if="defaultPublicEmployeeObjList.length">
          <div
            v-for="(item, index) in defaultPublicEmployeeObjList"
            :key="index"
            class="bind-contact-dialog-block-content-item" 
            @click="handleUserSelect(item)"
          >
            <span 
              class="fx-icon-ok-2 selected-icon" 
              v-show="isUserSelected(item)"
            ></span>
            <sfaAiAvatar
              :data="{
                userName: item.name,
                backgroundColor: '#FFA142',
                isAvaDefault: true,
                dataId: item._id,
                useAvatarImg: !!(item.profile_image && item.profile_image.length),
                avatarImgSrc: getAvaterImgSrc(item),
                personnelId: getPersonnelId(item)
              }"
            />
            <fx-tooltip 
              :content="item.name" 
              :open-delay="400"
              placement="top"
            >
              <span class="block-content-item-name">{{ item.name }}</span>
            </fx-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <fx-button @click="handleClose" size="mini">{{ $t('sfa.activity.corpus.bind_contact_dialog_cancel') }}</fx-button>
      <fx-button 
        type="primary" 
        size="mini" 
        @click="handleConfirm" 
        :loading="loading"
        :disabled="isButtonDisabled"
      >{{ $t('sfa.activity.corpus.bind_contact_dialog_confirm') }}</fx-button>
    </div>
    <fx-selector-box-v2
      ref="selectorInput"
      :show.sync="showSelectorBox"
      v-bind="selectorBoxOpts"
      @confirm="handleSelectorBoxConfirm"
    ></fx-selector-box-v2>
  </fx-dialog>
</template>

<script>
import { requirePickselfObject } from '@common/require.js';
import sfaAiAvatar from '../../sfaAiAvatar/sfaAiAvatar.vue'

export default {
  name: 'BindContactDialog',
  components: {
    sfaAiAvatar
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    dataId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      lookupInstance: null,
      selectedContact: null,
      selectedUsers: [],
      titleRadio: 'global',
      // 默认联系人
      defaultContactObjList: [],
      // 默认员工
      defaultPersonnelObjList: [],
      // 默认互联用户 
      defaultPublicEmployeeObjList: [],
      userInfo: null,
      showTitleRadio: false,
      isOpenPRM: false,
      showSelectorBox: false,
      selectorBoxOpts: FS.selectorParseContactV2.parseContacts({
        member: true,
        single: true
      })
    }
  },
  computed: {
    /**
     * 确定按钮是否禁用
     * 条件：
     * 1. 没有选中联系人(selectedContact为空)
     * 2. 选中的联系人与当前userInfo一致
     * 只有选中了不同于userInfo的联系人时按钮才可用
     */
    isButtonDisabled() {
      // 如果没有选择任何联系人，禁用按钮
      if (!this.selectedContact) return true;
      
      // 如果没有userInfo，则只要选中了联系人就可以点击
      if (!this.userInfo) return false;
      
      // 判断选中的联系人是否与当前userInfo一致
      // 只有当选中了与userInfo不同的联系人时，按钮才可用
      const isSameContact = this.selectedContact._id === this.userInfo.userId && 
                           this.selectedContact.object_describe_api_name === this.userInfo.userApiName;
      
      // 返回true表示禁用按钮，返回false表示启用按钮
      return isSameContact; // 如果选中的是相同联系人则禁用
    }
  },
  watch: {},
  methods: {
    /**
     * 打开弹窗
     * @param {Object} item - 用户信息
     * @param {Boolean} isEditSpeaker - 是否是编辑发言人
     */
    open(item, isEditSpeaker = false) {
      this.userInfo = item;
      // this.visible = true;
      this.showTitleRadio = isEditSpeaker;
      // 如果是编辑发言人，设置为单条修改
      this.getObjectDataList();
    },

    /**
     * 获取数据
     */
    getObjectDataList() {
      CRM.util.FHHApi({
        url: `/EM1HNCRM/API/v1/object/activity_text/service/get_object_data_list`,
        data: {
          // objectId: '67876444c65e8c000132098c'
          objectId: this.dataId
        },
        success: ({ Result, Value }) => {
          console.log(Result, Value, 'Value')
          if (Result.StatusCode === 0) {
            const { ContactObj, PersonnelObj, PublicEmployeeObj } = Value?.speakerObjectData;
            this.defaultContactObjList = ContactObj || [];
            this.defaultPersonnelObjList = PersonnelObj || [];
            this.defaultPublicEmployeeObjList = PublicEmployeeObj || [];
            // 无代理通权限不下发defaultPublicEmployeeObjList字段
            this.isOpenPRM = !!Value?.speakerObjectData?.PublicEmployeeObj;
            
            // 添加当前用户到对应数组（如果不存在）
            if (this.userInfo) {
              this.addUserInfoToList();
            }
          }
        },
        error: (err) => {
          reject(err);
        }
      });
    },
    
    /**
     * 将userInfo添加到对应的数组中（如果不存在）
     */
    addUserInfoToList() {
      if (!this.userInfo) return;
      
      const { userApiName, userId, userName, profileImage } = this.userInfo;
      const userDataMap = {
        'ContactObj': this.defaultContactObjList,
        'PersonnelObj': this.defaultPersonnelObjList,
        'PublicEmployeeObj': this.isOpenPRM ? this.defaultPublicEmployeeObjList : null
      };
      
      const targetList = userDataMap[userApiName];
      if (!targetList) return;
      
      // 检查是否已存在
      const isExist = targetList.some(item => item._id === userId);
      if (!isExist) {
        // 添加到数组，兼容数据结构
        targetList.push({
          _id: userId,
          name: userName,
          object_describe_api_name: userApiName,
          profile_image: profileImage || []
        });
      }
      
      // 默认选中该用户
      this.selectedContact = {
        _id: userId,
        name: userName,
        object_describe_api_name: userApiName,
        profile_image: profileImage
      };
    },

    /**
     * 处理用户选择
     * @param {Object} item - 用户信息对象
     */
    handleUserSelect(item) {
      if (!item?._id) return;
      
      // 点击的是userInfo对应的用户，且已经没有其他选中项，则不处理
      if (!this.selectedContact && this.userInfo?.userId === item._id) {
        return;
      }

      // 如果点击的是当前已选中的联系人，则取消选中
      if (this.selectedContact?._id === item._id) {
        this.selectedContact = null;
        return;
      }
      
      // 否则选中该联系人
      this.selectedContact = item;
    },
    
    /**
     * 关闭弹窗
     */
    handleClose() {
      // 清空数据
      this.defaultContactObjList.splice(0, this.defaultContactObjList.length);
      this.defaultPersonnelObjList.splice(0, this.defaultPersonnelObjList.length);
      this.defaultPublicEmployeeObjList.splice(0, this.defaultPublicEmployeeObjList.length);
      this.selectedContact = null;
      this.userInfo = null;
      this.$emit('update:visible', false);
    },

    /**
     * 确认绑定
     */
    async handleConfirm() {
      if (!this.selectedContact) {
        return this.$message.warning(this.$t('sfa.activity.corpus.bind_contact_dialog_empty_tip'));
      }
      const { object_describe_api_name, _id, name, profile_image } = this.selectedContact;
      const userInfo =  Object.assign({}, _.clone(this.userInfo),{
        targetUserApiName: object_describe_api_name,
        targetUserId: _id,
        targetUserName: name
      })
      if (object_describe_api_name === 'PersonnelObj') {
        userInfo.profile_image = profile_image;
      }

      // 触发父组件的确认事件
      this.$emit('confirm', userInfo, this.titleRadio);
    },

    /**
     * 打开选择对象弹窗
     * @param {string} objName - 对象名称
     */
    async openPickselfObject(objName) {
      const _this = this;
      let PickSelfObject = await requirePickselfObject(),
      pickObject = new PickSelfObject();
      pickObject.on('select', async (obj) => {
        const { object_describe_api_name, _id, name } = obj;
        if (!!_id) {
          const userInfo = Object.assign({}, _.clone(_this.userInfo), {
            targetUserApiName: object_describe_api_name,
            targetUserId: _id,
            targetUserName: name
          })
          if (object_describe_api_name === 'PersonnelObj') {
            userInfo.profile_image = obj.profile_image;
          }
          // await _this._bindContact({
          //   objectId: _this.dataId,
          //   replaceInfos: [userInfo]
          // })
          console.log(userInfo, obj, 'userInfo')
          _this.$emit('confirm', userInfo, _this.titleRadio)
          pickObject.destroy();
          pickObject = null;
        }
      })
      pickObject.render({
        apiname: objName
      })
    },

    openSelectorBox () {
      this.showSelectorBox = true;
      this.$nextTick(() => {
        this.$refs['selectorInput'].setValue({
          member: [this.userInfo?.userId]
        });
      });
    },

    /**
     * 获取提示文本
     * @param {string} i18nKey - 国际化key
     * @returns {string}
     */
    tipText (i18nKey) {
      return this.$t('sfa.activity.corpus.bind_dialog_content_item_tip', {
        objName: this.$t(i18nKey)
      })
    },

    /**
     * 获取链接文本
     * @param {string} i18nKey - 国际化key
     * @returns {string}
     */
    linkText(i18nKey) {
      return this.$t('sfa.activity.corpus.bind_dialog_content_item_btn', {
        objName: this.$t(i18nKey)
      })
    },

    /**
     * 判断用户是否被选中
     * @param {Object} item - 用户信息对象
     * @returns {Boolean} - 是否选中
     */
    isUserSelected(item) {
      if (!item?._id) return false;
      
      // 情况1：有selectedContact，则只显示selectedContact被选中
      if (this.selectedContact) {
        return this.selectedContact?._id == item._id;
      }
      
      // 情况2：没有selectedContact，但有userInfo，则显示userInfo对应的用户被选中
      if (this.userInfo?.userId == item._id) {
        return true;
      }
      
      // 其他情况都未被选中
      return false;
    },
    getAvaterImgSrc(item) {
      return item.profile_image?.length ? item.profile_image[0].signedUrl : ''
    },
    getPersonnelId(item) {
      return item._id || ''
    },
    async handleSelectorBoxConfirm (obj) {
      if (!obj?.member?.length) return;
      const employeeId = obj.member[0];
      if (employeeId == this.userInfo?.userId) return; // 如果选中的用户是已绑定用户，则不处理
      const employee = await FS.contacts.getEmployeeById(employeeId);
      if (employee) { 
        const userInfo = Object.assign({}, _.clone(this.userInfo), {
          targetUserApiName: 'PersonnelObj',
          targetUserId: employeeId,
          targetUserName: employee.name,
          profile_image: [
            { signedUrl: CRM.util.getImgPath(employee.profileImagePath) }
          ]
        })
        this.handleClose();
        this.$emit('confirm', userInfo, this.titleRadio)
      }
      // this.$refs['selectorInput'].destroy();
    }
  }
}
</script>

<style lang="less">
.bind-contact-dialog {
  border-radius: 12px;
  background: linear-gradient(346deg, rgba(247, 228, 255, 0.03) -12.18%, #FFF 17.86%, #FFF 74.03%, rgba(198, 220, 255, 0.08) 122.97%), #FFF;
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.15);
  .el-dialog__header {
    padding: 16px;
    .el-dialog__headerbtn {
      align-items: baseline;
      margin-top: -12px;
    }
  }

  .el-dialog__body {
    padding: 0 12px;
  }

  .bind-contact-dialog-inner {
    display: flex;
    flex-direction: column;
    gap: 16px;
    .bind-contact-dialog-block {
      padding: 12px;
      border-radius: 8px;
      border: 2px solid var(--color-special01);
    }
    .bind-contact-dialog-block-title {
      display: flex;
      align-items: baseline;
      font-size: 13px;
      gap: 4px;
      line-height: 1;
      .bind-contact-dialog-block-title-icon {
        font-size: 14px;
      }
      .bold-text {
        font-weight: 700;
        color: #181C25;
      }
      .link-text {
        color: var(--color-info06);
        cursor: pointer;
        font-size: 12px;
      }
      .tip-text {
        font-size: 12px;
        color: var(--color-neutrals11);
        margin-left: auto;
        margin-right: 8px;
      }
    }
    .bind-contact-dialog-block-content {
      display: flex;
      gap: 12px;
      margin-top: 10px;
      flex-wrap: wrap;
    }
    .bind-contact-dialog-block-content-item {
      padding: 6px;
      border-radius: 6px;
      border: 1px solid var(--color-special01);
      background: #F8FAFF;
      font-size: 12px;
      color: #181C25;
      position: relative;
      display: flex;
      align-items: center;
      gap: 6px;
      .selected-icon {
        position: absolute;
        top: -1px;
        right: -1px;
        font-size: 7px;
        font-weight: bold;
        background-image: linear-gradient(45deg, transparent, transparent 50%, var(--color-primary06) 50%);;
        width: 15px;
        height: 15px;
        padding-right: 2px;
        padding-top: 2px;
        text-align: right;
        border-radius: 0 6px 0 0;
        &:before {
          color: #fff;
        }
      }
      &:hover {
        cursor: pointer;
      }
      .block-content-item-name {
        display: inline-block;
        width: 56px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
  
  .el-dialog__footer {
    text-align: right;
    padding: 12px 16px 16px;
    border-top: none;
    .fx-button + .fx-button {
      margin-left: 12px;
    }
  }
}
</style> 