<template>
    <common-card
        :title="profileContext.currentDimension ? $t('sfa.aiCustomerProfile.cardTitle.dimensionSuggestion.nextAction') : $t('sfa.aiCustomerProfile.cardTitle.dimensionSuggestion.flow')"
        icon="fx-icon-f-product_hot"
        class="suggestion-card"
        contentMarginTop="12px"
    >
        <common-data-fetcher-status
            :loading="loading"
            :error="error"
            :data="allDisplayData"
            content-class="suggestion-content"
        >
            <!-- 概述下才有流程分 -->
            <profile-source-flow-stage v-if="!profileContext.currentDimension" :score-data="scoreData" />
            <p v-if="suggestionSummary">{{ suggestionSummary }}</p>
            <div class="suggestion-wrapper">
                <div class="suggestion-task-item" :class="{'only-one-child': suggestions.length === 1}" v-for="item in suggestions" :key="item.task_id">
                    <div class="suggestion-task-name">
                        <fx-tag size="mini" type="info">{{ $t('sfa.aiCustomerProfile.dimensionSuggestion.task') }}</fx-tag>
                        <p class="suggestion-task-name-text">{{ item.task }}</p>
                    </div>
                    <div class="suggestion-task-content">
                        <template v-for="(child, index) in item.children">
                            <suggestion-item
                                :key="index"
                                :item="child"
                                :data-context="dataContext"
                            />
                            <div 
                                v-if="index !== item.children.length - 1" 
                                :key="`divider-${index}`"
                                class="suggestion-divider"
                            ></div>
                        </template>
                    </div>
                </div>
            </div>
        </common-data-fetcher-status>
    </common-card>
</template>

<script>
import CommonCard from './CommonCard.vue'
import CommonDataFetcherStatus from './CommonDataFetcherStatus.vue';
import SuggestionItem from './BaseDimensionSuggestionItem.vue';
import { getCustomerSuggestions } from '../services/profileService';
import ProfileSourceFlowStage from './ProfileSourceFlowStage.vue';

export default {
    name: 'ProfileDimensionSuggestion',
    components: {
        CommonDataFetcherStatus,
        SuggestionItem,
        CommonCard,
        ProfileSourceFlowStage,
    },
    props: {
        scoreData: {
            type: Object,
            default: () => ({})
        },
        profileContext: {
            type: Object,
            default: () => ({})
        },
        dataContext: {
            type: Object,
            default: () => ({})
        },
    },
    computed: {
        currentProfileIdAndDimension() {
            return {
                currentFlowProfileId: this.profileContext.currentFlowProfileId,
                currentProfileId: this.profileContext.currentProfileId,
                currentDimension: this.profileContext.currentDimension,
            }
        },
        allDisplayData() {
            const rst = {};
            if (this.suggestionSummary) rst.suggestionSummary = this.suggestionSummary;
            if (this.suggestions?.length) rst.suggestions = this.suggestions;
            if (!this.profileContext.currentDimension && this.scoreData?.stages?.length) rst.scoreData = this.scoreData;
            return rst;
        },
    },
    watch: {
        currentProfileIdAndDimension: {
            handler() {
                this.fetchData();
            },
            deep: true,
            immediate: true
        },
    },

    data() {
        return {
            suggestionSummary: '',
            suggestions: [],
            loading: false,
            error: null,
        };
    },

    methods: {
        fetchData() {
            const { currentDimension, currentProfileId, currentFlowProfileId } = this.profileContext;
            const profileId = currentDimension ? currentProfileId : (currentFlowProfileId || currentProfileId);
            if (!profileId) return;
            this.loading = true;
            getCustomerSuggestions({
                profileId,
                featureDimensionId: currentDimension,
            }).then(res => {
                this.suggestions = (res.profileAdvice || []);
                this.suggestionSummary = res.profileAdviceSummary;
            }).catch(err => {
                this.error = err;
            }).finally(() => {
                this.loading = false;
            });
        },
    }
};
</script>

<style lang="less">
.suggestion-card {
    line-height: 22px;
    .suggestion-content {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }
    .suggestion-task-item {
        border-radius: 8px;
        border: 2px solid #F2F4FB;
        padding: 12px;
        display: flex;
        flex-direction: column;
        .suggestion-task-content {
            flex: 1;
            margin-top: 8px;
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        .suggestion-task-name {
            display: flex;
            gap: 4px;
            align-items: center;
            .suggestion-task-name-text {
                flex: 1;
                font-size: 13px;
                line-height: 18px;
                font-weight: 700;
                color: #181C25;
            }
        }
        .suggestion-item {
            flex: 1;
        }
        .suggestion-divider {
            border-bottom: 2px dashed #F2F4FB;
        }
    }
    .suggestion-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 12px;
        .suggestion-task-item {
            flex: 1 1 calc(50% - 34px);
            max-width: calc(50% - 34px);
            &.only-one-child {
                flex: 1;
                max-width: max-content;
            }
        }
    }
}
</style> 