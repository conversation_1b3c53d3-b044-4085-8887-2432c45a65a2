import SfaNewAiChat from './sfaNewAiChat'
export default {
    props: ['compInfo', 'apiName', 'dataId'],
    render(createElement) {
        const objs = ['NewOpportunityObj', 'LeadsObj'];
        const chatComponent = objs.includes(this.apiName) ? SfaNewAiChat : this.$context.vueComponents['AiChatComponent'];
        return createElement(chatComponent, {
            props: {
                compInfo: this.compInfo,
                apiName: this.apiName,
                dataId: this.dataId
            }
        });
    }
}
