<script>
import withB<PERSON><PERSON>hart from '../mixins/withBaseChart'

export default {
    mixins: [withBase<PERSON><PERSON>],
    props: {
        chartDataUnit: {
            type: String,
            default: ''
        }
    },
    methods: {
        getChartOption() {
            const me = this;
            const xData = this.chartData.map(item => item.date || '');
            const yData = this.chartData.map(item => item.value !== undefined ? item.value : item);
            const maxCount = 5;
            const filteredXData = xData.slice(0, maxCount);
            const filteredYData = yData.slice(0, maxCount);
            
            const modifiedXData = [...filteredXData];
            const modifiedYData = [...filteredYData];
            

            const xAxisLabelStyle = {
                color: '#181C25',
                fontSize: 12,
                fontWeight: 700,
            }
            return {
                grid: {
                    top: 0,
                    left: 5,
                    right: 5,
                    bottom: 0,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: modifiedXData,
                    boundaryGap: false,
                    axisLine: { lineStyle: { color: '#F2F4FB' } },
                    axisLabel: {
                        ...xAxisLabelStyle,
                        interval: 0,
                        // 对于首尾标签特殊处理
                        formatter: function(value, index) {
                            if (index === 0) {
                                return '{left|' + value + '}';
                            }
                            else if (index === modifiedYData.length - 1) {
                                return '{right|' + value + '}';
                            }
                            return value;
                        },
                        rich: {
                            left: {
                                ...xAxisLabelStyle,
                                align: 'right',
                                padding: [0, 0, 0, 55]
                            },
                            right: {
                                ...xAxisLabelStyle,
                                align: 'right',
                                padding: [0, 55, 0, 0]
                            }
                        }
                    },
                    axisTick: { show: false },
                    offset: 0,
                    
                },
                yAxis: {
                    min: 0,
                    max: 100,
                    interval: 20,
                    axisLabel: { show: false },
                    splitLine: { lineStyle: { color: '#F2F4FB', type: 'dashed' } },
                    axisLine: { show: false },
                    axisTick: { show: false },
                },
                series: [{
                    data: modifiedYData,
                    type: 'line',
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: { color: '#FF8001', width: 2 },
                    itemStyle: { color: '#FF8001' },
                    smoothMonotone: 'x',
                    areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [
                                    { offset: 0, color: 'rgba(255, 123, 22, 0.20)' },
                                    { offset: 1, color: 'rgba(255, 123, 22, 0.00)' }
                                ]
                            }
                        },
                }],

                tooltip: {
                    trigger: 'axis',
                    padding: 4,
                    borderColor: '#FFF0E4',
                    backgroundColor: '#FFF0E4',
                    borderWidth: 1,
                    textStyle: { color: '#FF8001', fontSize: 13, fontWeight: 700, lineHeight: 18 },
                    extraCssText: {
                        borderRadius: '8px',
                        boxShadow: '0px 0px 8px 0px rgba(255, 155, 41, 0.12)',
                    },
                    formatter: params => {
                        if (!params.length) return '';
                        return `${params[0].data}${this.chartDataUnit}`;
                    },
                    position: (pos) => {
                        return [pos[0] + 10, pos[1] - 30];
                    }
                }
            }
        }
    }
}
</script>
