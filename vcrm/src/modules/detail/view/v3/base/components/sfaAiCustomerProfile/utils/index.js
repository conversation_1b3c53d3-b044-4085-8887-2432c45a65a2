export const getSourceColor = (source) => {
    if (source >= 80) return '#30C776' // var(--color-success06)
    if (source >= 60) return '#FF8000' // var(--color-primary06)
    return '#FF522A' // var(--color-danger06)
}

export const getSourceBgColor = (source) => {
    if (source >= 80) return '#F0FFF4'
    if (source >= 60) return '#FFF7E6'
    return '#FFF5F0'
}

export const DIMENSION_SOURCE_TREND_COLORS = [
    'rgba(20, 201, 201, 1)',
    'rgba(22, 93, 255, 1)',
    'rgba(111, 221, 160, 1)',
    'rgba(255, 224, 108, 1)',
    'rgba(255, 132, 151, 1)',
    'rgba(158, 140, 240, 1)',
    'rgba(163, 217, 98, 1)',
    'rgba(255, 155, 41, 1)',
    'rgba(255, 82, 42, 1)',
    'rgba(48, 199, 118, 1)',
];

export const getSourceProps = (objectApiName) => {
    const overviewTitleMap = {
        'NewOpportunityObj': $t('sfa.aiCustomerProfile.overviewTitle.NewOpportunityObj'),
        'AccountObj': $t('sfa.aiCustomerProfile.overviewTitle.AccountObj'),
        'LeadsObj': $t('sfa.aiCustomerProfile.overviewTitle.LeadsObj'),
    }
    const overviewDescriptionMap = {
        'NewOpportunityObj': [
            [
                $t('sfa.aiCustomerProfile.overviewTitle.NewOpportunityObj') + $t('sfa.aiCustomerProfile.colon'),
                $t('sfa.aiCustomerProfile.overviewDescriptionContent.NewOpportunityObj')
            ]
        ],
        'AccountObj': [
            [
                $t('sfa.aiCustomerProfile.overviewTitle.AccountObj') + $t('sfa.aiCustomerProfile.colon'),
                $t('sfa.aiCustomerProfile.overviewDescriptionContent.AccountObj')
            ]
        ],
        'LeadsObj': [
            [
                $t('sfa.aiCustomerProfile.overviewTitle.LeadsObj') + $t('sfa.aiCustomerProfile.colon'),
                $t('sfa.aiCustomerProfile.overviewDescriptionContent.LeadsObj')
            ]
        ],
    }
    const trendTitleMap = {
        'NewOpportunityObj': $t('sfa.aiCustomerProfile.trendTitle.NewOpportunityObj'),
        'AccountObj': $t('sfa.aiCustomerProfile.trendTitle.AccountObj'),
        'LeadsObj': $t('sfa.aiCustomerProfile.trendTitle.LeadsObj'),
    }
    const unitMap = {
        'NewOpportunityObj': '%',
        'AccountObj': '',
        'LeadsObj': '',
    }
    return {
        overviewTitle: overviewTitleMap[objectApiName],
        overviewDescription: overviewDescriptionMap[objectApiName],
        trendTitle: trendTitleMap[objectApiName],
        sourceUnit: unitMap[objectApiName],
    }
}

export default {
    getSourceColor,
    getSourceBgColor,
    DIMENSION_SOURCE_TREND_COLORS,
    getSourceProps,
}