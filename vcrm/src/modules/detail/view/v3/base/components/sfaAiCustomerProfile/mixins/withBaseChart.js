import { requireEcharts } from '@common/require';
import withContainerResize from './withContainerResize';

export default {
    mixins: [withContainerResize],
    
    props: {
        // 图表数据
        chartData: {
            type: [Object, Array],
            required: true
        },
        // 图表配置项
        options: {
            type: Object,
            default: () => ({})
        },
        width: {
            type: [String, Number],
            default: '100%'
        },
        height: {
            type: [String, Number],
            default: '100%'
        },
        // 最小高度配置
        minHeight: {
            type: [String, Number],
            default: 'auto'
        },
        // 图表主题
        theme: {
            type: String,
            default: ''
        },
        // 是否自动调整大小
        autoResize: {
            type: Boolean,
            default: true
        },
        // 加载状态
        loading: {
            type: Boolean,
            default: false
        },
        // 加载文本
        loadingText: {
            type: String,
            default: $t('加载中...')
        }
    },

    data() {
        return {
            chart: null,
            defaultLoadingOptions: {
                text: '',
                textColor: '#999',
                maskColor: 'rgba(255, 255, 255, 0.76)',
                zlevel: 0
            }
        }
    },

    methods: {
        /**
         * 检查数据是否有效
         * @param {Object|Array} chartData 要检查的数据
         * @returns {boolean} 数据是否有效
         */
        isValidChartData(chartData) {
            // 空值检查
            if (!chartData) return false;
            
            // 数组类型检查
            if (Array.isArray(chartData)) {
                // 检查是否为空数组
                if (!chartData.length) return false;
                
                // 检查数组元素是否都是空值
                const hasValidItem = chartData.some(item => {
                    if (typeof item === 'object') {
                        return this.isValidChartData(item);
                    }
                    return item !== null && item !== undefined;
                });
                
                return hasValidItem;
            }
            
            // 对象类型检查
            if (typeof chartData === 'object') {
                // 检查是否为空对象
                const keys = Object.keys(chartData);
                if (!keys.length) return false;
                
                // 检查对象的值是否都是空值
                const hasValidValue = keys.some(key => {
                    const value = chartData[key];
                    if (typeof value === 'object') {
                        return this.isValidChartData(value);
                    }
                    return value !== null && value !== undefined;
                });
                
                return hasValidValue;
            }
            
            // 基础类型检查
            return chartData !== null && chartData !== undefined;
        },

        async initChart() {
            try {
                // 使用新的数据检查方法
                if (!this.isValidChartData(this.chartData)) {
                    console.warn('data is invalid');
                    return;
                }

                const echarts = await requireEcharts();
                if (this.chart) {
                    this.chart.dispose();
                }
                
                // 确保DOM元素已经渲染
                // if (!this.$el || !this.$el.offsetHeight) {
                //     console.warn('chart container is not ready');
                //     return;
                // }
                
                this.chart = echarts.init(this.$el, this.theme);
                this.updateChart();
                
                // 绑定事件
                this.bindChartEvents();
                
                this.loading ? this.setLoading() : this.setOption();
            } catch (error) {
                console.error('chart init error:', error);
                this.$emit('init-error', error);
            }
        },

        bindChartEvents() {
            if (!this.chart) return;
            
            // 点击事件
            this.chart.on('click', params => {
                this.$emit('chart-click', params);
            });

            // 图例切换事件
            this.chart.on('legendselectchanged', params => {
                this.$emit('legend-change', params);
            });

            // 数据区域缩放事件
            this.chart.on('datazoom', params => {
                this.$emit('data-zoom', params);
            });

            // 鼠标悬浮事件
            this.chart.on('mouseover', params => {
                this.$emit('mouse-over', params);
            });

            // 鼠标移出事件
            this.chart.on('mouseout', params => {
                this.$emit('mouse-out', params);
            });

            this.chart.on('rendered', () => {
                console.log('rendered');
                this.$emit('rendered');
            });

            this.chart.on('finished', () => {
                console.log('finished');
                this.$emit('finished');
            });
        },

        setOption() {
            if (!this.chart) return;
            
            const option = this.getChartOption();
            if (!option) {
                console.warn('getChartOption() is not implemented or return null');
                return;
            }

            try {
                // 添加 notMerge 参数，避免数据更新时的合并问题
                this.chart.setOption(option, true);
                this.$nextTick(() => {
                    this.$emit('rendered');
                });
            } catch (error) {
                console.error('set option error:', error);
                this.$emit('option-error', error);
            }
        },

        // 需要被继承的方法
        getChartOption() {
            console.warn('getChartOption() is not implemented');
            return null;
        },

        setLoading() {
            if (!this.chart) return;
            const loadingOptions = {
                ...this.defaultLoadingOptions,
                text: this.loadingText
            };
            this.chart.showLoading(loadingOptions);
        },

        handleResize() {
            if (this.chart && this.autoResize) {
                this.chart.resize();
            }
        },

        updateChart() {
            if (this.loading) {
                this.setLoading();
            } else {
                this.chart && this.chart.hideLoading();
                this.setOption();
            }
        },

        // 对外暴露的方法
        getChart() {
            return this.chart;
        },

        // 重新计算图表大小
        resize() {
            this.handleResize();
        },

        // 销毁图表
        dispose() {
            if (this.chart) {
                this.chart.dispose();
                this.chart = null;
            }
        },
        
        // 重写容器尺寸变化回调
        onContainerResize(size) {
            this.$nextTick(() => {
                this.handleResize();
            });
            this.$emit('container-resize', size);
        }
    },

    watch: {
        chartData: {
            handler(newVal) {
                if (this.isValidChartData(newVal)) {
                    if (!this.chart) {
                        this.initChart();
                    } else {
                        this.updateChart();
                    }
                }
            },
            deep: true,
            immediate: true
        },
        loading(val) {
            if (val) {
                this.setLoading();
            } else {
                this.chart && this.chart.hideLoading();
                this.setOption();
            }
        },
        options: {
            handler() {
                this.updateChart();
            },
            deep: true
        }
    },

    mounted() {
        this.initChart();
    },

    beforeDestroy() {
        this.dispose();
    },

    render(h) {
        return h('div', {
            style: {
                width: this.width,
                height: this.height,
                minHeight: typeof this.minHeight === 'number' ? `${this.minHeight}px` : this.minHeight,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            }
        });
    }
};
