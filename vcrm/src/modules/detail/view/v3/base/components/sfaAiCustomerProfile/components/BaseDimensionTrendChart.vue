<script>
import BaseTooltip from './BaseDimensionTrendChartTooltip.vue'
import withBase<PERSON>hart from '../mixins/withBaseChart'
import ProfileService from '../services/profileService'

export default {
    name: 'BaseDimensionTrendChart',
    props: {
        profileContext: {
            type: Object,
            default: () => ({
                currentDimension: '',
                currentProfileId: '',
                currentMethodology: '',
                currentMethodologyType: '',
                currentMethodologyInstanceId: '',
            })
        },
        dataContext: {
            type: Object,
            default: () => ({
                apiName: '',
                dataId: '',
            })
        },
        chartData: {
            type: Object,
            default: () => ({
                dimensions: [],
                dimensionHistorySource: []
            })
        },
    },
    mixins: [withBaseChart],
    data() {
        return {
            tooltipInstance: null,
            tooltipContainer: null,
            scoreDetail: {},
            scoreDetailLoading: {},
        }
    },
    beforeDestroy() {
        // 组件销毁时清理tooltip实例
        if (this.tooltipInstance) {
            this.tooltipInstance.$destroy()
            this.tooltipInstance = null
        }
        // 移除可能残留的tooltip容器
        if (this.tooltipContainer && document.body.contains(this.tooltipContainer)) {
            document.body.removeChild(this.tooltipContainer)
        }
    },
    methods: {
        fetchScoreDetail: _.debounce(function(params) {
            const isAll = params.length > 1;
            let {profileId, dataId} = params[0].data;
            if (isAll) dataId = `${profileId}:all`;
            if (!profileId || !dataId || this.scoreDetail[dataId] || this.scoreDetailLoading[dataId]) return;
            this.$set(this.scoreDetailLoading, dataId, true)
            ProfileService.getCustomerDimensionTrendDetail({
                profileId,
                featureDimensionId: this.profileContext.currentDimension,
                methodologyType: this.profileContext.currentMethodologyType,
                methodologyInstanceId: this.profileContext.currentMethodologyInstanceId,
                objectApiName: this.dataContext.apiName,
                objectId: this.dataContext.dataId
            }).then(details => {
                this.scoreDetail[dataId] = details;
                if (isAll) {
                    details.forEach((detail) => {
                        const realDataId = `${profileId}:${detail.featureDimensionId}`;
                        if (this.scoreDetail[realDataId]) {
                            this.scoreDetail[realDataId].push(detail)
                        } else {
                            this.$set(this.scoreDetail, realDataId, [detail])
                        }
                    })
                }
            }).finally(() => {
                this.$set(this.scoreDetailLoading, dataId, false)
            })
        }, 0, true),
        getChartOption() {
            const me = this;
            const getColor = (rgbaColor, alpha) => {
                return rgbaColor.replace(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/, (_, r, g, b) => {
                    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
                });
            }
            const { dimensions, dimensionHistorySource } = this.chartData

            // 获取所有不重复的日期并排序
            const dates = [...new Set(dimensionHistorySource.map(item => item.date))].sort()

            // 处理每个维度的数据
            const series = dimensions.map((dimension, index) => {
                const data = dates.map(date => {
                    const {score, profile_id, node_id, feature_dimension_id} = dimensionHistorySource.find(item => 
                        item.date === date && item.id === dimension.id
                    ) || {};
                    return {
                        value: score || 0,
                        profileId: profile_id,
                        dataId: `${profile_id}:${node_id || feature_dimension_id}`
                    }
                })

                return {
                    name: dimension.label,
                    type: 'line',
                    data,
                    symbol: 'circle',
                    symbolSize: 8,
                    itemStyle: {
                        color: dimension.color,
                    },
                    lineStyle: {
                        color: dimension.color,
                        width: 2
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 0,
                            y2: 1,
                            colorStops: [{
                                offset: 0, color: getColor(dimension.color, 0.08) // 0% 处的颜色
                            }, {
                                offset: 1, color: getColor(dimension.color, 0) // 100% 处的颜色
                            }],
                            global: false // 缺省为 false
                        }
                    },
                    smooth: true,
                    smoothMonotone: 'none'
                }
            })

            return {
                tooltip: {
                    trigger: 'axis',
                    backgroundColor: 'transparent',
                    padding: [8, 12],
                    formatter: (params) => {
                        // 使用固定ID避免重复创建
                        const id = 'trend-chart-tooltip'
                        
                        // 延迟挂载，确保DOM已经准备好
                        this.$nextTick(() => {
                            // 查找或创建容器
                            let container = document.getElementById(id)
                            if (!container) {
                                // 如果不存在则创建新容器
                                container = document.createElement('div')
                                container.id = id
                                document.body.appendChild(container)
                                this.tooltipContainer = container
                            }
                            
                            // 如果已有实例，先销毁
                            if (this.tooltipInstance) {
                                this.tooltipInstance.$destroy()
                            }
                            
                            // 创建新实例
                            this.tooltipInstance = new Vue({
                                render: h => h(BaseTooltip, {
                                    props: { 
                                        params,
                                        date: params[0].axisValue,
                                        items: params.map(param => ({
                                            name: param.seriesName,
                                            value: param.value,
                                            color: param.color,
                                            id: param.dataId,
                                            description: me.scoreDetail[param.data.dataId] || [],
                                            descriptionLoading: me.scoreDetailLoading[param.data.dataId] || false
                                        })),
                                    },
                                    on: {
                                        'tooltip-mouseleave': (e) => {
                                            // 检查鼠标是否移动到了图表区域
                                            const chartDom = me.chart.getDom();
                                            const relatedTarget = e.relatedTarget;
                                            
                                            // 如果鼠标不是移动到图表区域，则隐藏tooltip
                                            if (!chartDom.contains(relatedTarget)) {
                                                me.chart.dispatchAction({
                                                    type: 'hideTip'
                                                });
                                            }
                                        }
                                    }
                                })
                            })

                            this.fetchScoreDetail.call(this, params)

                            // 挂载到容器
                            this.tooltipInstance.$mount(container)
                        })
                        
                        // 返回容器的HTML
                        return `<div id="${id}" style="width: 300px"></div>`
                    },
                    confine: true,
                    enterable: true,
                    hideDelay: 100
                },
                legend: {
                    data: dimensions.map(item => item.label),
                    textStyle: {
                        color: '#1D2129',
                        fontSize: 12
                    },
                    itemWidth: 12,
                    itemHeight: 12,
                    itemGap: 16,
                    top: 0,
                    right: 0
                },
                grid: {
                    top: '32px',
                    left: 0,
                    right: 0,
                    bottom: 0,
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    boundaryGap: true,
                    axisLine: {
                        lineStyle: {
                            color: '#F2F4FB',
                            width: 4
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#86909C',
                        fontSize: 12
                    }
                },
                yAxis: {
                    type: 'value',
                    min: 0,
                    max: 100,
                    interval: 20,
                    nameTextStyle: {
                        color: '#86909C',
                        fontSize: 12,
                        padding: [0, 0, 0, 30]
                    },
                    splitLine: {
                        lineStyle: {
                            type: 'dashed',
                            color: '#E5E6EB'
                        }
                    },
                    axisLine: {
                        show: false
                    },
                    axisTick: {
                        show: false
                    },
                    axisLabel: {
                        color: '#86909C',
                        fontSize: 12
                    }
                },
                series
            }
        },
        bindChartEvents() {
            if (!this.chart) return
            
            // 监听tooltip隐藏事件
            this.chart.on('hideTip', () => {
                // 可以选择在tooltip隐藏时不销毁实例，只隐藏内容
                if (this.tooltipInstance) {
                    const el = this.tooltipInstance.$el
                    if (el) el.style.display = 'none'
                }
            })
            
            // 监听图表销毁事件
            this.chart.on('dispose', () => {
                if (this.tooltipInstance) {
                    this.tooltipInstance.$destroy()
                    this.tooltipInstance = null
                }
            })
        }
    }
}
</script>