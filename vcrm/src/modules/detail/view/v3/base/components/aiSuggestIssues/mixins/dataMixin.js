import { fetchSuggestListData } from '../services/apiService';

export default {
  data() {
    return {
        showHistoryListIcon: false,
    };
  },
  methods: {
    handleRelatedObjectList(relatedObjectList = []) {
        if(relatedObjectList?.length) {
            this.relatedObjectOptions = relatedObjectList.map(item => {
                if(item.api_name == 'NewOpportunityObj') {
                    this.relatedObjectName = 'NewOpportunityObj';
                }
                return {
                    label: item.display_name__r,
                    value: item.api_name
                }
            });
            if(this.relatedObjectOptions.length == 1) {
                this.relatedObjectName = this.relatedObjectOptions[0].value;
            }
        }else{
            this.relatedObjectName = this.apiName;
        }
    },
    async fetchSuggestIssuesHistory() {
        let me = this;
        const { dataList, total, objectDescribe } = await this.getRelatedHistoryList(0);
        if(dataList && dataList.length) {
            this.showHistoryListIcon = true;
        }

    },
    getRelatedHistoryList(offset) {
        const detailData = this.$context.getData();
        return fetchSuggestListData(this.apiName, detailData, {
          limit: this.limit,
          offset,
          questionType: this.questionType,
          tagsType: this.tagsType,
          relatedObjectName: this.relatedObjectName,
          isHistory: true
        });
    }
  }
};
