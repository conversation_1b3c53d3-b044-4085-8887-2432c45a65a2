import CommonCard from './CommonCard.vue'
import CommonDataFetcherStatus from './CommonDataFetcherStatus.vue'
import CommonTextListDisplay from './CommonTextListDisplay.vue'

import ProfileHeaderMethodologyTabs from './ProfileHeaderMethodologyTabs.vue'
import ProfileHeaderOperateActions from './ProfileHeaderOperateActions.vue'
import ProfileSourceOverview from './ProfileSourceOverview.vue'
import ProfileSourceTrend from './ProfileSourceTrend.vue'
import ProfileSourceFlowStage from './ProfileSourceFlowStage.vue'
import ProfileScoreDivider from './ProfileScoreDivider.vue'
import ProfileDimensionFeatures from './ProfileDimensionFeatures.vue'
import ProfileDimensionChangeTrends from './ProfileDimensionChangeTrends.vue'
import ProfileDimensionAdvantage from './ProfileDimensionAdvantage.vue'
import ProfileDimensionDisadvantage from './ProfileDimensionDisadvantage.vue'
import ProfileDimensionSuggestion from './ProfileDimensionSuggestion.vue'
import ProfileDimensionSummary from './ProfileDimensionSummary.vue'
import ProfileChart139Describe from './ProfileChart139Describe.vue'
import ProfileChart139Radar from './ProfileChart139Radar.vue'
import ProfileChartRadar from './ProfileChartRadar.vue'
import ProfileEmpty from './ProfileEmpty.vue'

export {
    CommonCard,
    CommonDataFetcherStatus,
    CommonTextListDisplay,
}

export default {
    ProfileChartRadar,
    ProfileChart139Describe,
    ProfileChart139Radar,
    ProfileHeaderMethodologyTabs,
    ProfileHeaderOperateActions,
    ProfileSourceOverview,
    ProfileSourceTrend,
    ProfileSourceFlowStage,
    ProfileScoreDivider,
    ProfileDimensionFeatures,
    ProfileDimensionChangeTrends,
    ProfileDimensionAdvantage,
    ProfileDimensionDisadvantage,
    ProfileDimensionSuggestion,
    ProfileDimensionSummary,
    ProfileEmpty,
}
