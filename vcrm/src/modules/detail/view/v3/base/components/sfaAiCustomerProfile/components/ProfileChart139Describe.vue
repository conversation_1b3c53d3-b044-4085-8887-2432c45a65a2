<template>
  <div class="profile-chart-139-describe">
      <div
        class="layout-group"
        :class="{ 'is-row': layoutGroup.isRow }"
        v-for="(layoutGroup, layoutIndex) in groupedIndicators" :key="layoutIndex"
      >
        <div 
          v-for="(group, groupIndex) in layoutGroup.groups" 
          :key="groupIndex"
          class="indicator-card"
        >
          <div class="indicator-section">
            <div class="indicator-header">
              <div class="title">{{ group.title }}</div>
              <div class="tag-group">
                <div 
                  v-if="group.completedTasksCount" 
                  class="tag tag-success"
                >{{ group.completedText }}</div>
                <div 
                  v-if="group.incompleteTasksCount" 
                  class="tag tag-danger"
                >{{ group.incompleteText}}</div>
              </div>
            </div>
            <div 
              class="indicator-content-group"
              :class="{ 'is-grid': group.layout === 'grid' }"
              :style="getGridStyle(group)"
            >
                <div class="indicator-row" v-for="(row, rowIndex) in getGroupedItems(group.tasks, group.layout, group.columns)" :key="rowIndex">
                  <div 
                    v-for="(item, itemIndex) in row"
                    :key="itemIndex"
                    class="indicator-item"
                    :class="{ 
                      'success': item.status === true,
                      'danger': item.status === false,
                      'warning': item.status === 'warning',
                      'info': item.status === 'info'
                    }"
                    @click="handleItemClick(item, group)"
                  >
                    {{ item.name }}
                  </div>
                </div>
            </div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import CommonCard from './CommonCard.vue'

export default {
    name: 'ProfileChart139Describe',
    components: {
        CommonCard
    },
    props: {
        // 指标组数据
        // 数据结构示例：
        // [
        //     {
        //         title: '指标组标题', // 指标组的名称
        //         completedTasksCount: 0, // 已完成任务的数量
        //         incompleteTasksCount: 1, // 未完成任务的数量
        //         tasks: [ // 任务列表
        //             {
        //                 name: '任务名称', // 任务的名称
        //                 status: false // 任务的状态，true表示完成，false表示未完成，'warning'表示警告状态，'info'表示信息状态
        //             }
        //         ],
        //         layout: 'grid', // 布局类型，可选值为'grid'或其他
        //         columns: 2 // 新增：每个组可以独立配置列数，可选值1-3
        //     }
        // ]
        indicatorGroups: {
            type: Array,
            required: true,
            default: () => []
        },
        // // 布局配置：[[0,1], [2]] 表示第0、1组在第一行，第2组在第二行
        // groupLayout: {
        //   type: Array,
        //   default: () => []
        // }
    },
    data() {
        return {
            groupLayout: [[0, 1], [2]]
        }
    },
    computed: {
        // 根据布局配置处理指标组
        groupedIndicators() {
            const layout = this.groupLayout.length ? this.groupLayout : [this.indicatorGroups.map((_, i) => i)];

            return layout.map(groupIndexes => ({
                isRow: groupIndexes.length > 1,
                groups: groupIndexes.map(index => this.indicatorGroups[index]).filter(Boolean)
            }));
        }
    },
    methods: {
        // 根据布局方式对items进行分组
        getGroupedItems(items = [], layout = 'default', columns = 1) {
            if (!items.length) return [];

            // grid布局时，根据group的columns属性决定每行数量
            if (layout === 'grid') {
                // 确保columns在1-3之间
                const validColumns = Math.min(Math.max(columns, 1), 3);
                const rows = [];
                for (let i = 0; i < items.length; i += validColumns) {
                    rows.push(items.slice(i, i + validColumns));
                }
                return rows;
            }

            // 默认每个item独立一行
            return items.map(item => [item]);
        },

        // 获取group的列数样式
        getGridStyle(group) {
            if (group.layout !== 'grid') return {};
            
            const columns = Math.min(Math.max(group.columns || 1, 1), 3);
            return {
                '--grid-columns': columns
            };
        },

        // 点击事件处理
        handleItemClick(item, group) {
            this.$emit('item-click', { item, group });
        }
    }
}
</script>

<style lang="less" scoped>
.profile-chart-139-describe {
    display: flex;
    flex-direction: column;
    gap: 8px;
    width: 100%;

    .layout-group {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;

        &.is-row {
            flex-direction: row;

            .indicator-card {
                flex: 1;
            }
        }
    }

    .indicator-card {
        background: rgba(255, 255, 255, 0.76);
        border-radius: 8px;
        padding: 10px 8px;

        .indicator-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .indicator-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 4px;

            .title {
                font-weight: 700;
                font-size: 13px;
                line-height: 18px;
                color: var(--color-neutrals19);
            }

            .tag-group {
                display: flex;
                gap: 2px;
            }
        }

        .tag {
            padding: 1px 4px;
            border-radius: 2px;
            font-size: 12px;
            line-height: 18px;
            display: flex;
            align-items: center;
            background: #fff;

            &.tag-danger {
                color: var(--color-danger06);
                border: 1px solid var(--color-danger03);
            }

            &.tag-success {
                color: var(--color-success06);
                border: 1px solid var(--color-success03);
            }
        }

        .indicator-content-group {
            display: flex;
            flex-direction: column;
            gap: 4px;
            width: 100%;

            &.is-grid {
                .indicator-row {
                    display: grid;
                    grid-template-columns: repeat(var(--grid-columns, 1), 1fr);
                    gap: 4px;
                }
            }
        }

        .indicator-row {
            display: flex;
            gap: 4px;
            width: 100%;
        }

        .indicator-item {
            flex: 1;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 12px;
            line-height: 18px;
            font-weight: 500;
            color: var(--color-neutrals19);
            cursor: pointer;
            transition: all 0.3s;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
            text-align: center;

            &:hover {
                opacity: 0.8;
            }

            &.danger {
                background: var(--color-danger01);
            }

            &.success {
                background: var(--color-success01);
            }

            &.warning {
                background: var(--color-warning01);
            }

            &.info {
                background: var(--color-info01);
            }
        }
    }
}
</style>