<template>
    <common-card
        :title="$t('sfa.aiCustomerProfile.cardTitle.dimensionAdvantage')"
        icon="fx-icon-f-obj-app185"
        iconColor="#55D48C"
        class="advantage-card"
    >
        <common-data-fetcher-status
            :loading="loading"
            :error="error"
            :data="advantages"
        >
            <!-- 综述不展示序号 -->
            <common-text-list-display :list="advantages" :showIndex="!!currentDimension" />
        </common-data-fetcher-status>
    </common-card>
</template>

<script>
import {CommonCard, CommonDataFetcherStatus, CommonTextListDisplay} from './index'
import { getCustomerAdvantages } from '../services/profileService';

export default {
    name: 'ProfileDimensionAdvantage',
    components: {
        CommonTextListDisplay,
        CommonDataFetcherStatus,
        CommonCard
    },
    props: {
        currentDimension: {
            type: String,
            default: ''
        },
        currentProfileId: {
            type: String,
            default: ''
        },
    },
    watch: {
        currentDimension: {
            handler() {
                this.fetchData();
            },
            immediate: true
        },
        currentProfileId: {
            handler() {
                this.fetchData();
            },
            immediate: true
        }
    },
    data() {
        return {
            advantages: [],
            loading: false,
            error: null,
        };
    },
    methods: {
        fetchData() {
            if (!this.currentProfileId) return;
            this.loading = true;
            this.error = null;
            getCustomerAdvantages({
                profileId: this.currentProfileId,
                featureDimensionId: this.currentDimension,
            }).then(res => {
                this.advantages = res;
            }).catch(err => {
                this.error = err;
            }).finally(() => {
                this.loading = false;
            });
        }
    }
};
</script>

<style lang="less" scoped>

</style> 