<template>
    <fx-dialog
      class="ai-suggest-issues-history-list-dialog"
      :visible.sync="dialogVisible"
      :append-to-body="true"
      :title="title"
      :z-index="zIndex"
      @closed="handleClosed"
    >
        <div slot="title" class="history-list-title-box">
            <!-- Header with title and selector -->
            <span class="history-list-title">{{ title }}</span>
            <div class="left-content-box">
                <div class="selector-label-box" v-if="relatedObjectOptions.length">
                    <span class="selector-label">{{ $t('sfa.ai.suggest.related.object.text') }}</span>
                    <fx-select
                        v-model="relatedObjectName"
                        :options="relatedObjectOptions"
                        size="micro"
                        filterable
                        @change="changeRelatedObject"
                    ></fx-select>
                </div>
            </div>
        </div>
      <div class="history-list-content">
        <!-- Alert -->
        <fx-alert
            class="alert-info"
            type="info"
            show-icon
            :closable="false"
            :title="$t('sfa.ai.suggest.history.list.tip')"
        >
        </fx-alert>
        <!-- 历史话题列表 -->
        <sfa-ai-infinite-list
          ref="infiniteList"
          :dataId="dataId"
          :apiName="apiName"
          :noDataListText="$t('sfa.ai.infinite.suggest.list.nodata')"
          :questionType="questionType"
          :loadMore="loadMore"
          :listLoading="listLoading"
          :limit="limit"
          :offset="offset"
          :noMore="noMore"
          :dataList="dataList"
          :isLoading="isLoading"
          :containerSelector="'.history-list-content'"
        >
          <template v-slot:item-content="{ questionData, showDialog }">
            <sfa-ai-question-item
              :questionData="questionData"
              @trigger-dialog="showDialog"
              :dataId="dataId"
              :apiName="apiName"
              :useRightAction="false"
              :useKnowledgeAnswer="false"
              :useQuestionAvatar="false"
              :fxTagType="questionData.fxTagType"
              attitudeFieldName="advice_status_label"
              :isLoading="isLoading"
              :useTopRightButton="false"
              relatedFieldName="active_record_id__r"
              @click-related-data="openActiveRecotdDetail"
              :relatedOpenDetail="false"
              :keyword="searchKeyword"
              @match-found="handleMatchFound"
              :tagsTypeList="tagsTypeList"
              tagsName="library_tags"
              :isExpanded="isExpanded"
            />
          </template>
        </sfa-ai-infinite-list>
      </div>
    </fx-dialog>
  </template>

  <script>
  import SfaAiInfiniteList from "../../sfaAiInfiniteList/sfaAiInfiniteList.vue";
  import SfaAiQuestionItem from "../../sfaAiQuestionItem/sfaAiQuestionItem.vue";
  import { fetchSuggestListData } from '../services/apiService';
  const pageSize = 500;
  export default {
    name: 'SiSuggestIssuesHistoryList',
    components: {
      SfaAiInfiniteList,
      SfaAiQuestionItem,
    },
    props: {
      dialogVisible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: $t('sfa.ai.suggest.history.list.title'),
      },
      apiName: {
        type: String,
        required: true,
      },
      dataId: {
        type: [String, Number],
        required: true,
      },
      relatedObjectOptions: {
        type: Array,
        default: () => [],
      },
      relatedObjectName: {
        type: String,
        default: '',
      },
      detailData: {
        type: Object,
        default: () => ({})
      },
    },
    data() {
      return {
        zIndex: CRM.util.getzIndex() + 10,
        listLoading: true,
        isLoading: false,
        limit: pageSize,
        offset: -pageSize,
        noMore: false,
        dataList: [],
        questionType: '',
        tagsTypeList: [],
        isExpanded: true,
        searchKeyword: '',
        tagsType: ['all'],
      };
    },
    watch: {
      dialogVisible(val) {
        if (val) {
          this.init();
        }
      },
    },
    methods: {
      async init() {
        this.questionType = '';
        this.listLoading = true;
        this.limit = pageSize;
        this.offset = -pageSize;
        this.dataList = [];
        this.noMore = false;
        this.tagsType = ['all'];
        await this.loadMore();
      },
      async loadMore() {
        if(this.noMore || this.isLoading || !this.relatedObjectName) {
          return;
        }
        this.isLoading = true;
        const { dataList, total, objectDescribe } = await this.getRelatedList(this.offset += pageSize);

        if(dataList && dataList.length) {
            this.handleAdviceStatus(dataList, objectDescribe);
            this.dataList.push(...dataList);
            if(this.dataList.length >= total && total > 0) this.noMore = true;
        }else{
            this.noMore = true;
        }
        this.listLoading = false;
        this.isLoading = false;
      },
      getRelatedList(offset = 0) {
        const detailData = this.getDetailData();
        return fetchSuggestListData(this.apiName, detailData, {
          limit: this.limit,
          offset,
          questionType: this.questionType,
          tagsType: this.tagsType,
          relatedObjectName: this.relatedObjectName,
          isHistory: true
        });
      },
      getDetailData() {
        // 使用从父组件传递的详细数据
        return this.detailData || {
          _id: this.dataId,
          active_record_id: this.apiName === 'ActiveRecordObj' ? this.dataId : undefined,
          account_id: this.apiName === 'AccountObj' ? this.dataId : undefined,
          leads_id: this.apiName === 'LeadsObj' ? this.dataId : undefined,
          new_opportunity_id: this.apiName === 'NewOpportunityObj' ? this.dataId : undefined,
        };
      },
      handleAdviceStatus(dataList=[], objectDescribe) {
        const questionTypesData = [
          {
            icon: 'fx-icon-f-obj-app94',
            type: 'total',
          },
          {
            icon: 'fx-icon-ok',
            type: 'clearly_defined',
          },
          {
            icon: 'fx-icon-warning',
            type: 'unclearly_defined',
          },
          {
            icon: 'fx-icon-close-2',
            type: 'not_asked',
          },
        ];

        if(dataList.length) {
          dataList.forEach(item => {
            if(item.order_field) {
                if(item.library_id__r) {
                    if(item.history_flag != 'history') {
                        item.question_content__o = item.order_field + '、' + item.library_id__r;
                    } else {
                        item.question_content__o = item.library_id__r;
                    }
                }
            }
            if(item.library_tags && item.library_tags.length && typeof item.library_tags === 'string') {
                item.library_tags = [item.library_tags];
            }
            if(objectDescribe?.fields?.advice_status?.options?.length){
                if(item.advice_status == 'clearly_defined') {
                    item.fxTagType = 'success'
                }else if(item.advice_status == 'unclearly_defined') {
                    item.fxTagType = 'link'
                } else {
                    item.fxTagType = 'warning'
                }
                const option = objectDescribe.fields.advice_status.options.find(option => option.value ==  item.advice_status);
                if(option) {
                item.advice_status_label = option.label;
                const questionType = questionTypesData.find(o => o.type ==  item.advice_status);
                    if(questionType) {
                        item.attitudeIcon = questionType.icon
                    }
                }
            }
          })
        }
      },
      changeRelatedObject(val) {
        this.init();
      },
      destroy() {
        this.$emit('hideDialog');
        // this.$destroy(); // 移除组件销毁以允许重复打开弹窗
      },
      openActiveRecotdDetail(relatedData) {
        CRM.api.show_crm_detail({
          id: relatedData._id,
          apiName: 'ActiveRecordObj',
        });
      },
      handleMatchFound() {},
      handleClosed() {
        this.$emit('hideDialog');
      },
    },
  };
  </script>

  <style lang="less" scoped>
  .ai-suggest-issues-history-list-dialog {
    /deep/ .relative-object-data-box{
        padding-left: 0;
    }
    .history-list-title-box{
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 25px;
        .history-list-title{
            font-weight: 700;
            font-size: 16px;
            line-height: 24px;
            color: var(--color-neutrals19);
        }
        .left-content-box {
            display: flex;
            align-items: center;
            gap: 4px;
            .selector-label-box{
                background-color: #fff;
                padding: 0px 6px;
                box-sizing: border-box;
                border-radius: 8px;
                border: 1px solid var(--color-neutrals05);
                background: var(--color-neutrals01);
                .fx-select{
                    width: 80px;
                }
                /deep/  .el-input__inner{
                    border: none;
                    color: var(--color-neutrals19);
                    font-weight: 400;
                    line-height: 20px;
                    font-size: 13px;
                }
            }
        }
    }
    .history-list-content {
        min-height: 400px;
        // max-height: 500px;
      .header-row {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 16px;
        .header-title {
          font-weight: 700;
          font-size: 16px;
          color: #181C25;
        }
        .object-selector {
          width: 146px;
          height: 28px;
        }
      }
      .alert-info {
        margin-bottom: 16px;
      }
    }
  }
  </style>
