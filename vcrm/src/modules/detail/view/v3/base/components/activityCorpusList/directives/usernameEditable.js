/**
 * 用户名可编辑指令
 * 使span元素变为可编辑状态，支持双击或通过按钮编辑
 * 优化版本：减少DOM操作，优化性能
 */
export default {
  // 缓存按钮模板，避免重复创建DOM元素
  buttonTemplates: null,

  // 创建按钮模板（只创建一次）
  createButtonTemplates(context) {
    if (this.buttonTemplates) return this.buttonTemplates;

    // 创建按钮容器
    const footer = document.createElement('div');
    footer.className = 'username-input-button';

    // 创建单条按钮
    const singleBtn = document.createElement('button');
    singleBtn.className = 'activity-text-button-custom fx-button el-button el-button--text el-button--micro';
    singleBtn.textContent = context.$t('sfa.activity.corpus.list_item_username_btn_single');
    singleBtn.style.color = 'var(--color-info06)';
    singleBtn.dataset.action = 'single';

    // 创建分隔线
    const divider = document.createElement('span');
    divider.className = 'activity-corpus-list-divider';

    // 创建全局按钮
    const globalBtn = document.createElement('button');
    globalBtn.className = 'activity-text-button-custom fx-button el-button el-button--text el-button--micro';
    globalBtn.textContent = context.$t('sfa.activity.corpus.list_item_username_btn_global');
    globalBtn.style.color = 'var(--color-info06)';
    globalBtn.dataset.action = 'global';

    // 创建按钮容器包装
    const btnWrapper = document.createElement('div');
    btnWrapper.className = 'username-buttons-wrapper';
    btnWrapper.style.display = 'inline-flex';
    btnWrapper.style.alignItems = 'center';

    // 添加到footer
    btnWrapper.appendChild(singleBtn);
    btnWrapper.appendChild(divider);
    btnWrapper.appendChild(globalBtn);
    footer.appendChild(btnWrapper);

    this.buttonTemplates = footer;
    return footer;
  },

  bind(el, binding, vnode) {
    const context = vnode.context;
    const { item, index } = binding.value;

    // 存储原始文本，便于取消操作
    let originalText = '';

    // 使用函数引用，便于解绑事件
    const dblclickHandler = function() {
      startEdit();
    };

    const blurHandler = function(event) {
      // 检查是否点击了操作按钮，如果是则不处理失焦事件
      const footer = el.nextElementSibling;
      if (footer && footer.classList.contains('username-input-button') &&
          (footer.contains(event.relatedTarget) || footer === event.relatedTarget)) {
        return;
      }

      // 使用requestAnimationFrame代替setTimeout，性能更好
      requestAnimationFrame(() => {
        if (el.getAttribute('contenteditable') === 'true') {
          cancelEdit();
        }
      });
    };

    // 按钮点击处理函数
    const buttonClickHandler = function(e) {
      e.preventDefault(); // 防止失去焦点
      if (e.target.tagName === 'BUTTON' && e.target.dataset.action) {
        saveEdit(e.target.dataset.action);
      }
    };

    // 滚动事件处理函数 - 用于更新视觉提示
    const scrollHandler = function() {
      // 只在编辑状态下处理滚动事件
      if (el.getAttribute('contenteditable') !== 'true') return;

      // 检查滚动位置，更新左侧渐变显示状态
      if (el.scrollLeft > 5) { // 滚动超过5px时显示左侧渐变
        el.setAttribute('data-scrolled', 'true');
      } else {
        el.setAttribute('data-scrolled', 'false');
      }
    };

    // 键盘事件处理函数 - 处理方向键滚动
    const keydownHandler = function(e) {
      // 只处理编辑状态下的键盘事件
      if (el.getAttribute('contenteditable') !== 'true') return;

      // 获取文本内容和光标位置信息
      const selection = window.getSelection();
      const range = selection.getRangeAt(0);
      const text = el.textContent;
      const cursorPosition = range.startOffset;

      // 处理左右方向键滚动
      if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
        // 获取当前滚动位置和元素尺寸信息
        const currentScroll = el.scrollLeft;
        const scrollAmount = 15; // 每次滚动15像素，增加滚动速度
        const contentWidth = el.scrollWidth;
        const visibleWidth = el.clientWidth;

        // 检查是否需要滚动
        // 如果光标在可见区域的边缘，则需要滚动
        const isAtLeftEdge = cursorPosition <= 3; // 光标在文本开始位置附近
        const isAtRightEdge = cursorPosition >= text.length - 3; // 光标在文本结束位置附近
        const hasHiddenContentLeft = currentScroll > 0;
        const hasHiddenContentRight = currentScroll + visibleWidth < contentWidth;

        if (e.key === 'ArrowLeft' && hasHiddenContentLeft && isAtLeftEdge) {
          // 向左滚动
          el.scrollLeft = Math.max(0, currentScroll - scrollAmount);
        } else if (e.key === 'ArrowRight' && hasHiddenContentRight && isAtRightEdge) {
          // 向右滚动
          el.scrollLeft = Math.min(contentWidth - visibleWidth, currentScroll + scrollAmount);
        }

        // 不阻止默认行为，允许光标正常移动
        // 这样用户体验更自然，光标移动的同时内容也会滚动
      }

      // 处理Home和End键，直接滚动到开始或结束位置
      if (e.key === 'Home') {
        el.scrollLeft = 0;
      } else if (e.key === 'End') {
        el.scrollLeft = el.scrollWidth - el.clientWidth;
      }
    };

    // 双击事件处理
    el.addEventListener('dblclick', dblclickHandler);

    // 失去焦点时取消编辑
    el.addEventListener('blur', blurHandler);

    // 添加键盘事件监听
    el.addEventListener('keydown', keydownHandler);

    // 添加滚动事件监听
    el.addEventListener('scroll', scrollHandler);

    // 开始编辑
    function startEdit() {
      if (context.disableOperation) {
        context.$message.warning(context.$t('sfa.activity.corpus.audio_disable_operation_tip_text'));
        return;
      }

      originalText = el.textContent;

      // 设置为可编辑
      el.setAttribute('contenteditable', 'true');
      el.classList.add('editing');

      // 为编辑态的span增加4px的右侧边距
      el.style.marginRight = '4px';

      // 为父容器添加包含编辑状态的类，用于CSS控制
      const wrapper = el.closest('.username-hover-wrapper');
      if (wrapper) {
        wrapper.classList.add('has-editing');
      }

      // 选中文本内容
      el.focus();

      // 使用requestAnimationFrame代替setTimeout，性能更好
      requestAnimationFrame(() => {
        const range = document.createRange();
        range.selectNodeContents(el);
        const selection = window.getSelection();
        selection.removeAllRanges();
        selection.addRange(range);

        // 确保滚动条位置正确 - 解决不同浏览器滚动条初始位置不一致的问题
        el.scrollLeft = 0;

        // 检查内容是否超出可见区域，如果是，添加提示类
        if (el.scrollWidth > el.clientWidth) {
          el.classList.add('has-overflow');
        } else {
          el.classList.remove('has-overflow');
        }
      });

      // 设置临时数据，以便保存时使用 - 只保存必要的属性，避免深拷贝整个对象
      el._tempUserData = {
        id: item.id,
        seq: item.seq,
        userId: item.userId,
        userName: item.userName,
        originalUserName: item.originalUserName,
        activityUserId: item.activityUserId,
        isDefaultSpeaker: item.isDefaultSpeaker
      };

      // 创建操作按钮
      createEditButtons();
    }

    // 创建操作按钮 - 使用模板克隆而不是每次都创建新元素
    function createEditButtons() {
      // 检查是否已存在按钮
      let footer = el.nextElementSibling;
      if (footer && footer.classList.contains('username-input-button')) {
        footer.style.display = 'inline-flex';
        return;
      }

      // 克隆按钮模板
      const template = vnode.context.$options.directives.usernameEditable.createButtonTemplates(context);
      footer = template.cloneNode(true);

      // 添加事件监听
      footer.addEventListener('mousedown', buttonClickHandler);

      // 插入到DOM
      el.after(footer);
    }

    // 隐藏操作按钮
    function hideEditButtons() {
      const footer = el.nextElementSibling;
      if (footer && footer.classList.contains('username-input-button')) {
        footer.style.display = 'none';
      }
    }

    // 保存编辑
    function saveEdit(type) {
      const newName = el.textContent.trim();

      // 恢复为不可编辑
      el.removeAttribute('contenteditable');
      el.classList.remove('editing');
      // 清除编辑时添加的边距
      el.style.marginRight = '';
      hideEditButtons();

      // 移除父容器的编辑状态类
      const wrapper = el.closest('.username-hover-wrapper');
      if (wrapper) {
        wrapper.classList.remove('has-editing');
      }

      // 如果内容未变，则不执行保存
      if (newName === originalText || !newName) {
        el.textContent = originalText;
        return;
      }

      // 准备更新数据
      const tempData = el._tempUserData;

      // 调用原有的保存方法
      if (tempData) {
        // 创建临时userName属性 - 只传递必要的数据
        context.userEditState.editingUsername = {
          ...tempData,
          userName: newName
        };

        // 调用原有保存方法
        context.handleUsernameConfirm(tempData, index, type);
      }
    }

    // 取消编辑
    function cancelEdit() {
      el.removeAttribute('contenteditable');
      el.classList.remove('editing');
      el.textContent = originalText;
      // 清除编辑时添加的边距
      el.style.marginRight = '';
      hideEditButtons();

      // 移除父容器的编辑状态类
      const wrapper = el.closest('.username-hover-wrapper');
      if (wrapper) {
        wrapper.classList.remove('has-editing');
      }
    }

    // 将方法和事件处理函数存储到元素上，便于后续解绑
    el._handlers = {
      dblclick: dblclickHandler,
      blur: blurHandler,
      buttonClick: buttonClickHandler,
      keydown: keydownHandler,
      scroll: scrollHandler
    };

    // 将方法暴露给外部
    el._startEdit = startEdit;
    el._saveEdit = saveEdit;
    el._cancelEdit = cancelEdit;
  },

  update(el, binding) {
    // 当绑定的项目更新时更新显示内容
    const { item } = binding.value;
    if (el.getAttribute('contenteditable') !== 'true') {
      el.textContent = item.userName || binding.value.defaultText;
    }
  },

  unbind(el) {
    // 清理事件监听器
    if (el._handlers) {
      el.removeEventListener('dblclick', el._handlers.dblclick);
      el.removeEventListener('blur', el._handlers.blur);
      el.removeEventListener('keydown', el._handlers.keydown);
      el.removeEventListener('scroll', el._handlers.scroll);

      // 移除按钮的事件监听
      const footer = el.nextElementSibling;
      if (footer && footer.classList.contains('username-input-button')) {
        footer.removeEventListener('mousedown', el._handlers.buttonClick);
      }

      delete el._handlers;
    }

    // 清理方法和数据
    delete el._tempUserData;
    delete el._startEdit;
    delete el._saveEdit;
    delete el._cancelEdit;

    // 移除可能添加的数据属性
    el.removeAttribute('data-scrolled');
  }
};