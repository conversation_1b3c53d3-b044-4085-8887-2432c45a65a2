import { mockData } from './mock';

// 模拟网络延迟
const delay = (ms = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// 统一的mock响应格式
const createResponse = (data, success = true, message = '') => (data);

// Mock服务类
class MockService {
    constructor(mockData) {
        this.mockData = mockData;
    }

    async getProfileData() {
        console.log('mock getProfileData');
        await delay();
        return createResponse(JSON.parse(JSON.stringify(this.mockData)));
    }

}

// 创建单例
export const mockService = new MockService(mockData); 