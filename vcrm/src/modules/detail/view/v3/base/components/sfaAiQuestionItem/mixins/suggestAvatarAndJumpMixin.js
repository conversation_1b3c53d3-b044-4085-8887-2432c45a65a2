export default {
    data() {
      return {
        suggestUserAvaBgColor: {},
        singleSummaryUserAvatarList: this.getSingleSummaryUserAvatarList(this.questionData?.activity_topic_answer)
      };
    },
    created() {
        let me = this;
        this.$context.$on('corpus.list.updated', (userAvaBgColor) => {
            console.log('userAvaBgColor:', userAvaBgColor);
            me.suggestUserAvaBgColor = userAvaBgColor;
            if(me?.singleSummaryUserAvatarList?.userIdArray?.length) {
                me.updateSingleSummaryUserAvatarList(userAvaBgColor);
            }
        });
        if(!Object.keys(me.suggestUserAvaBgColor).length) {
            this.$context.$emit('activity.interactive.issues.mounted')
        }
    },
    methods: {
        getSingleSummaryUserAvatarList(itemData = []) {
            return {
                userName: this.getUserNameI18n(itemData.answer_person),
                backgroundColor: 'rgb(255, 161, 66)',
                isAvaDefault: false,
                userIdArray: itemData?.activity_users?.length ? itemData.activity_users.map(
                    item => {
                        return {
                            userName: this.getUserNameI18n(item.user_name),
                            dataId: item._id,
                            backgroundColor: item.avatar_bg_color || 'rgb(255, 161, 66)',
                            isAvaDefault: item.is_default_speaker == '1',
                            useAvatarImg: !!(item.profile_image && item.profile_image.length),
                            avatarImgSrc: item.profile_image && item.profile_image[0] && item.profile_image[0].signedUrl,
                        }
                    }
                ) : []
            }
        },
        updateSingleSummaryUserAvatarList(userAvaBgColor) {
            if(!this.singleSummaryUserAvatarList?.userIdArray?.length || !userAvaBgColor) return;
            // 先构建 activityUserId => value 的 Map
            const avaMap = {};
            for (const [key, value] of Object.entries(userAvaBgColor)) {
            if (value.activityUserId) {
                avaMap[value.activityUserId] = value;
            }
            }
            let hasChanged = false;
            const newUserIdArray = this.singleSummaryUserAvatarList.userIdArray.map((item) => {
                const value = avaMap[item.dataId];
                if (value) {
                    let newItem = { ...item };
                    // ...你的更新逻辑...
                    if (
                        value.profileImage && !item.avatarImgSrc ||
                        !value.profileImage && item.avatarImgSrc ||
                        (value.profileImage && value.profileImage[0] && item.avatarImgSrc && JSON.stringify(value.profileImage[0].signedUrl) !== JSON.stringify(item.avatarImgSrc))
                    ) {
                        hasChanged = true;
                        newItem.useAvatarImg = !!value.profileImage;
                        newItem.avatarImgSrc = value.profileImage && value.profileImage[0] && value.profileImage[0].signedUrl;
                    }
                    if (value.avaColor !== item.backgroundColor) {
                        hasChanged = true;
                        newItem.backgroundColor = value.avaColor;
                    }
                    if (value.userName !== item.userName) {
                        hasChanged = true;
                        newItem.userName = this.getUserNameI18n(value.userName);
                    }
                    if (value.isAvaDefault !== item.isAvaDefault) {
                        hasChanged = true;
                        newItem.isAvaDefault = value.isAvaDefault;
                    }
                    return newItem;
                }
                return item;
            });
            if (hasChanged) {
                // 关键：替换整个数组，确保响应式
                this.$set(this.singleSummaryUserAvatarList, 'userIdArray', newUserIdArray);
            }
        },
    },
    beforeDestroy() {

    },
};
