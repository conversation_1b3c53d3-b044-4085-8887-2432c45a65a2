<template>
    <BaseSfaNewAiChat
        :agent-api-name="compInfo.agent"
        :variables="variables"
        from="obj_detail"
    />
</template>

<script>
import BaseSfaNewAiChat from '@/modules/components/sfa_new_ai_chat/index.vue';
export default {
    name: 'SfaNewAiChat',
    components: {
        BaseSfaNewAiChat,
    },
    props: {
        compInfo: {
            type: Object,
            default: () => ({
                agent: ''
            })
        },
        apiName: {
            type: String,
            default: ''
        },
        dataId: {
            type: String,
            default: ''
        },
        // 业务
        welcomeType: {
            type: String,
            default: ''
        },
        agentApiName: {
            type: String,
            default: ''
        },
        sessionId: {
            type: String,
            default: ''
        },
        sessionIdVariables: {
            type: Array,
            default: () => []
        },
        // variables: {
        //     type: Object,
        //     default: () => ({})
        // },
    },
    data() {
        return {
        }
    },
    computed: {
        variables() {
            const data = this.$context.getData();
            const describe = this.$context.getDescribe();
            return {
                name: data.name,
                objectDataId: this.dataId,
                objectApiName: this.apiName,
                objectName: describe.display_name,
            }
        }
    }
}
</script>