<template>
    <div class="sfa-ai-question-item-container" :class="{'sfa-ai-activity-mark-keynote': itemData.mark === 'keynote', 'sfa-ai-activity-mark-invalid': itemData.mark === 'invalid', 'sfa-ai-activity-mark-loading': maskLoading}" @click="clickItem(itemData)">
        <div class="mark-overlay" v-if="itemData.mark === 'invalid'"></div>
        <div class="top-right-box" v-if="useTopRightButton" :style="{ opacity: showTopRightBox ? '1' : '0', zIndex: showTopRightBox ? '6' : '0' }">
            <span class="jump-to-yuliao" v-if="userAvaBgColor && Object.keys(userAvaBgColor).length && itemData.mark !== 'invalid' && (itemData.answer_seq || itemData.question_seq)" @click="jumpToYuliaoOrigin($event, itemData)"><span class="fx-icon-obj-app320"></span><span>{{ $t('sfa.ai.infinite.item.jump.yuliao') }}</span></span>
            <span class="middle-line expand-line" v-if="userAvaBgColor && Object.keys(userAvaBgColor).length && itemData.mark !== 'invalid' && (itemData.answer_seq || itemData.question_seq)"></span>
            <span class="add-task-button" v-if="itemData.mark !== 'invalid'" @click="setMark(itemData, itemData.mark === 'keynote' ? '' : 'keynote', itemIndex)"><span class="fx-icon-obj-app29"></span><span>{{ itemData.mark === 'keynote' ? $t('sfa.ai.infinite.item.unset.keynote') : $t('sfa.ai.infinite.item.set.keynote') }}</span></span>
            <span class="middle-line" v-if="itemData.mark !== 'invalid'"></span>
            <span class="add-task-button" @click="setMark(itemData, itemData.mark === 'invalid' ? '' : 'invalid', itemIndex)"><span class="fx-icon-rubber"></span><span>{{ itemData.mark === 'invalid' ? $t('sfa.ai.infinite.item.unset.invalid') : $t('sfa.ai.infinite.item.set.invalid') }}</span></span>
            <span class="middle-line"></span>
            <span v-if="useRightAction && itemData.mark !== 'invalid'" class="add-task-button" @click="addTask(itemData)"><span class="fx-icon-obj-app352"></span><span>{{ $t('创建任务') }}</span></span>
            <span class="middle-line" v-if="itemData.mark !== 'invalid'"></span>
            <span class="ai-create-button" v-if="itemData.mark !== 'invalid'" @click="refreshItemAisuggest(itemData)" ><img class="refresh-item-icon" src="../../../../../../../assets/images/sfai/fx_AI.svg">{{ $t('sfa.ai.infinite.list.item.refresh.konwledge') }}</span>
            <span class="middle-line expand-line" v-if="itemData.mark !== 'invalid'"></span>
            <span class="expand-collapse-list" @click="toggleExpand">
                <span v-if="itemIsExpanded" class='fx-icon-list-expand'></span>
                <span v-else class='fx-icon-list-collapse'></span>
            </span>
        </div>
        <div class="question-title-box" >
            <div class="question-content-box">
                <div class="question-content-avatar" v-if="useQuestionAvatar"><SfaAiAvatar :data="getAvatarData(itemData, 'question_seq')" showTooltip="true"/></div>
                <div class="question-content">
                    <span v-if="index">{{ index }}{{'. '}}</span>
                    <span class="question-content-text" v-html="highlightText(itemData.question_content || itemData.question_content__o, 'question')"></span>
                    <span class="question-content-tag">
                        <fx-tag
                            v-if="!useAttitudeImg"
                            size="mini"
                            :type="itemData.fxTagType"
                            :closable="false"
                            effect="light-noborder"
                        >
                            {{ itemData[attitudeFieldName] }}
                        </fx-tag>
                        <fx-tag
                            v-for="(tagData, index) in itemData[tagsName]"
                            :key="index"
                            v-if="itemData[tagsName] && itemData[tagsName].length && getTagsLabel(tagData)"
                            size="mini"
                            type="link"
                            :closable="false"
                            effect="light-noborder"
                            class="question-content-tag-item"
                        >
                            {{ getTagsLabel(tagData) }}
                        </fx-tag>
                        <fx-tag
                            type="info"
                            size="mini"
                            effect="light-noborder"
                            :closable="false"
                            class="question-history-tag-item"
                            v-if="itemData.history_flag && itemData.history_flag === 'history'"
                        >
                            {{ $t('sfa.ai.infinite.item.history.flag') }}
                        </fx-tag>
                    </span>
                </div>
            </div>
        </div>
        <div v-if="itemIsExpanded && itemData.answer_summary && openMultipleSummary" class="answer-box" :class="!useKnowledgeAnswer ? 'noknowledge-answer-box' : ''">
            <div
                class="top-answer left-answer"
                @mouseenter="handleAnswerMouseEnter"
                @mouseleave="handleAnswerMouseLeave"
            >
                <div class="answer-edit-box" v-if="itemData.answer_summary && useAnswerEdit" :style="{ opacity: showAnswerEditBox ? '1' : '0', zIndex: showAnswerEditBox ? '6' : '0' }">
                    <span class="edit-button" @click="toggleEditMode" >
                        <span class="fx-icon-bianji"></span>
                        <span>{{ $t('编辑') }}</span>
                    </span>
                </div>
                <div v-if="tagsName == 'library_tags' && showEditTitle" class="single-all-record-summary-box">
                    <span class="record-summary-tip">
                        <span>
                            {{ $t('sfa.ai.infinite.item.all.record.summary') }}
                        </span>
                        <span>
                            <fx-tooltip :open-delay="600" effect="light" :content="$t('sfa.ai.infinite.item.all.record.summary.tip')" placement="bottom">
                                <span class="fx-icon-question"></span>
                            </fx-tooltip>
                        </span>
                    </span>
                </div>
                <div
                    v-editable.custom="{
                        version: `${itemData.answer_summary}_${itemData.keyword}`,
                        onSave: (content, target) => handleEditContentSave(itemData, content),
                        onCancel: () => handleEditContentCancel(itemData),
                        editableSelector: '.top-answer-content',
                        buttonPosition: 'bottom-right',
                        disabled: !useAnswerEdit
                    }"
                    ref="answerEditBoxRef"
                    :class="[itemData.answer_summary ?
                    'top-answer-content' : 'top-answer-nocontent']"
                    v-html="highlightText(itemData.answer_summary || $t('sfa.ai.infinite.list.item.unanswer'), 'answer')"

                >
                </div>
                <slot name="relatedContent" :questionData="itemData">
                    <div class="relative-object-data-box" v-if="itemData[relatedFieldName] && itemData[relatedFieldName].length && itemIsExpanded && openMultipleSummary && showEditTitle" >
                        <div class="relative-object-data-list">
                            <div class="relative-object-data-item"
                                v-for="(relatedData, index) in visibleRelatedData(itemData[relatedFieldName])"
                                :key="index"
                                @click="clickRelatedData(relatedData)"
                            >
                                <div class="relative-item-box">
                                    <span class="relative-object-data-item-text">{{ relatedData.name__r || relatedData.name }}</span>
                                    <span class="fx-icon-lianjie"></span>
                                </div>
                            </div>
                            <fx-tooltip
                                v-if="itemData[relatedFieldName].length > 2"
                                :open-delay="600"
                                effect="light"
                                placement="bottom"
                                popper-class="sfa-activity-relative-data-item-tooltip"
                            >
                                <div class="relative-object-data-item more-items">
                                    <div class="relative-item-box">
                                        <span class="relative-object-data-item-text relative-object-data-item-text-more">...</span>
                                    </div>
                                </div>
                                <template #content>
                                    <div class="tooltip-content">
                                        <div class="relative-object-data-item"
                                            v-for="(relatedData, index) in remainingRelatedData(itemData[relatedFieldName])"
                                            :key="index"
                                            @click="clickRelatedData(relatedData)"
                                        >
                                            <div class="relative-item-box">
                                                <span class="relative-object-data-item-text">{{ relatedData.name__r || relatedData.name }}</span>
                                                <span class="fx-icon-lianjie"></span>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </fx-tooltip>
                        </div>
                    </div>
                </slot>
            </div>
            <div class="right-answer">
                <SfaAiAvatar :data="getAvatarData(itemData, 'answer_seq')" showTooltip="true"/>
            </div>
        </div>
        <div class="fm-error crm-ico-error edit-answer-tip" v-if="showEditTip">{{ $t('sfa.ai.infinite.item.edit.answer.tip') }}</div>
        <!-- 单销售记录答案 -->
        <div v-if="itemIsExpanded && itemData.activity_topic_answer" class="answer-box" :class="!useKnowledgeAnswer ? 'noknowledge-answer-box' : ''">
            <div
                class="top-answer left-answer"
                @mouseenter="handleAnswerMouseEnter2"
                @mouseleave="handleAnswerMouseLeave2"
            >
                <div class="answer-edit-box" v-if="itemData.activity_topic_answer.answer_summary__o && useAnswerEdit" :style="{ opacity: showAnswerEditBox2 ? '1' : '0', zIndex: showAnswerEditBox2 ? '6' : '0' }">
                    <span class="edit-button" v-if="suggestUserAvaBgColor && Object.keys(suggestUserAvaBgColor).length && itemData.activity_topic_answer.interactive_document_seq && itemData.activity_topic_answer.interactive_document_seq.length" @click="jumpToYuliaoOrigin($event, itemData.activity_topic_answer)">
                        <span class="jump-to-yuliao"><span class="fx-icon-obj-app320"></span><span>{{ $t('sfa.ai.infinite.item.jump.yuliao') }}</span></span>
                    </span>
                    <span class="middle-line expand-line" v-if="suggestUserAvaBgColor && Object.keys(suggestUserAvaBgColor).length && itemData.activity_topic_answer.interactive_document_seq && itemData.activity_topic_answer.interactive_document_seq.length"></span>
                    <span class="edit-button" @click="toggleEditMode($event, 'singleEdit')" >
                        <span class="fx-icon-bianji"></span>
                        <span>{{ $t('编辑') }}</span>
                    </span>
                </div>
                <div v-if="tagsName == 'library_tags' && showEditTitle2" class="single-all-record-summary-box">
                    <span class="record-summary-tip">
                        <span>
                            {{ $t('sfa.ai.infinite.item.current.record.summary') }}
                        </span>
                        <span>
                            <fx-tooltip :open-delay="600" effect="light" :content="$t('sfa.ai.infinite.item.current.record.summary.tip')" placement="bottom">
                                <span class="fx-icon-question"></span>
                            </fx-tooltip>
                        </span>
                    </span>
                </div>
                <div
                    v-editable.custom="{
                        version: `${itemData.activity_topic_answer.answer_summary || itemData.activity_topic_answer.answer_summary__o}_${itemData.keyword}`,
                        onSave: (content, target) => handleEditContentSave(itemData, content, 'singleEdit'),
                        onCancel: () => handleEditContentCancel(itemData),
                        editableSelector: '.top-answer-content',
                        buttonPosition: 'bottom-right',
                        disabled: !useAnswerEdit
                    }"
                    ref="answerEditBoxRef2"
                    :class="[itemData.activity_topic_answer.answer_summary || itemData.activity_topic_answer.answer_summary__o ?
                    'top-answer-content' : 'top-answer-nocontent']"
                    v-html="highlightText(itemData.activity_topic_answer.answer_summary || itemData.activity_topic_answer.answer_summary__o || $t('sfa.ai.infinite.list.item.unanswer'), 'answer')"

                >
                </div>
            </div>
            <div class="right-answer">
                <SfaAiAvatar :data="singleSummaryUserAvatarList" showTooltip="true"/>
            </div>
        </div>
        <div class="fm-error crm-ico-error edit-answer-tip" v-if="showEditTip2">{{ $t('sfa.ai.infinite.item.edit.answer.tip') }}</div>
        <div v-if="useKnowledgeAnswer && (itemIsExpanded && itemData.ai_answer_content || knowledgeLoading)" class="answer-box knowledge-answer-box">
            <div class="top-answer left-answer" v-loading="knowledgeLoading">
                <div :class="[itemData.ai_answer_content ? 'top-answer-content-ai' : 'top-answer-nocontent']" :style="aiAnswerContentStyle">
                    <div class="ai-no-reference" v-if="!itemData.ai_answer_document || !itemData.ai_answer_document.length">
                        <img class="unbase-knowledge-icon" src="../../../../../../../assets/images/sfai/unbase_knowledge.svg">
                        <span class="ai-text">{{ $t('sfa.ai.infinite.item.unfrom.knowledge') }}</span>
                    </div>
                    <XXVUIEditorRenderDoc
                        v-if="itemData.ai_answer_content"
                        :node="itemData.ai_answer_content"
                        ref="aiContentRef"
                    >
                    </XXVUIEditorRenderDoc>
                    <span v-if="!itemData.ai_answer_content">{{ $t('sfa.ai.infinite.list.item.unanswer') }}</span>
                </div>
                <div v-if="itemData.ai_answer_document && itemData.ai_answer_document.length" class="document-list">
                    <span class="document-list-label">{{ $t('sfa.ai.infinite.item.from.knowledge.title') }}：</span>
                    <fx-tooltip
                        v-if="itemData.ai_answer_document.length > 2"
                        :open-delay="600"
                        effect="light"
                        placement="bottom"
                        popper-class="sfa-activity-document-list-tooltip"
                    >
                        <span class="document-list-items">
                            <span
                                v-for="(doc, index) in visibleDocuments"
                                :key="doc.dataId"
                                class="document-item"
                                @click.stop="openDocumentUrl(doc.url)"
                            >
                                <span class="document-item-text" :title="doc.title">{{ doc.title }}</span>
                                <span
                                    class="document-separator"
                                    v-if="index == 0"
                                >、</span>
                            </span>
                            <span class="document-item document-more">...</span>
                        </span>
                        <template #content>
                            <div class="tooltip-content">
                                <div
                                    v-for="(doc, index) in itemData.ai_answer_document"
                                    :key="doc.dataId"
                                    class="document-tooltip-item"
                                >
                                    <span @click.stop="openDocumentUrl(doc.url)">{{ doc.title }}</span>
                                    <span v-if="index < itemData.ai_answer_document.length - 1" class="document-tooltip-separator">、</span>
                                </div>
                            </div>
                        </template>
                    </fx-tooltip>
                    <span v-else class="document-list-items">
                        <span
                            v-for="(doc, index) in visibleDocuments"
                            :key="doc.dataId"
                            class="document-item"
                            @click.stop="openDocumentUrl(doc.url)"
                        >
                            <span class="document-item-text" :title="doc.title">{{ doc.title }}</span>
                            <span
                                class="document-separator"
                                v-if="index < visibleDocuments.length - 1"
                            >、</span>
                        </span>
                    </span>
                </div>
                <div v-if="itemData.ai_answer_content && contentTruncated" class="view-more-btn" @click.stop="expandContent">
                    <span>{{ $t(contentExpanded ? '收起' : '查看更多') }}</span>
                    <span :class="contentExpanded ? 'fx-icon-arrow-up2' : 'fx-icon-arrow-down2'"></span>
                </div>
            </div>
            <div class="right-answer">
                <SfaAiAvatar :data="{userName: itemData.question_proposer, backgroundColor: '#F2F4FB', useAvatarImg: true}"/>
            </div>
        </div>

    </div>
  </template>
  <script>
  import SfaAiAvatar from "../sfaAiAvatar/sfaAiAvatar.vue";
  import corpusUserNameI18n from '../sfaAiMixin/corpusUserNameI18n'
  import { editable } from '@components/editable';
  import topRightOperateMixin from './mixins/topRightOperateMixin';
  import editSummaryMixin from './mixins/editSummaryMixin';
  import suggestAvatarAndJumpMixin from './mixins/suggestAvatarAndJumpMixin';

  export default {
    mixins: [corpusUserNameI18n, topRightOperateMixin, editSummaryMixin, suggestAvatarAndJumpMixin],
    components: {
        SfaAiAvatar
    },
    directives: {
        editable,
    },
    props: {
        index: {
            type: Number,
        },
        itemIndex: {
            type: Number,
        },
        questionData: {
            type: Object,
            default: {},
        },
        apiName: {
            type: String,
            default: '',
        },
        dataId: {
            type: String,
            default: '',
        },
        fxTagType: {
            type: String,
            default: 'success',
        },
        tagsName: {
            type: String,
            default: 'tags',
        },
        clickItem: {
            type: Function,
            default: () => {
            },
        },
        useRightAction: {
          type: Boolean,
          default: true
        },
        isExpanded: {
            type: Boolean,
            default: true,
        },
        useKnowledgeAnswer: {
          type: Boolean,
          default: true
        },
        useQuestionAvatar: {
            type: Boolean,
            default: true,
        },
        relatedOpenDetail: {
            type: Boolean,
            default: true,
        },
        useAttitudeImg: {
          type: Boolean,
          default: false
        },
        useAnswerEdit: {
          type: Boolean,
          default: false
        },
        openMultipleSummary: {
            type: Boolean,
            default: true
        },
        relatedFieldName: {
            type: String,
            default: 'match_suggest_topic__r',
        },
        attitudeFieldName: {
            type: String,
            default: 'proposer_attitude_label',
        },
        keyword: {
            type: String,
            default: ''
        },
        tagsTypeList: {
            type: Array,
            default: () => [],
        }
    },
    data() {
      return {
        satisfiedImg: "@assets/images/satisfied.svg",
        generalImg: "@assets/images/general.svg",
        unsatisfiedImg: "@assets/images/unsatisfied.svg",
        isLoading: false,
        detailDialogVisible: false,
        detailDialogLoading: false,
        zIndex: CRM.util.getzIndex() + 10,
        detailItemData: {},
        dialogVisible1_2: false,
        knowledgeLoading: false,
        itemData: this.questionData,
        showTopRightBox: false,
        contentTruncated: false,
        contentExpanded: false,
        maxContentHeight: 330,
        contentResizeObserver: null,
        questionMatchCount: 0,
        answerMatchCount: 0,
        totalMatchCount: 0,
        maskLoading: false,
      };
    },
    watch: {
        isExpanded: {
            handler(newVal) {
                this.itemIsExpanded = newVal;
            },
            immediate: true
        },
        keyword: {
            handler(newVal, oldVal) {
                // 重置计数器
                this.questionMatchCount = 0;
                this.answerMatchCount = 0;
                this.totalMatchCount = 0;

                if (newVal) {
                    // console.log(`Keyword changed for item ${this.itemData._id}: ${oldVal} -> ${newVal}`);
                    this.$nextTick(() => {
                        this.checkAndScrollToMatch();
                    });
                }
            }
        },
        questionData: {
            handler(newVal) {
                // Deep copy the new data to ensure reactivity
                this.itemData = JSON.parse(JSON.stringify(newVal));
                // console.log('questionData updated:', this.itemData);
            },
            deep: true,
            immediate: true
        },
        'itemData.ai_answer_content': {
            handler(newVal, oldVal) {
                // 处理内容为空的情况
                if (!newVal) {
                    this.contentTruncated = false;
                    return;
                }

                // 只有当内容明显变化时才重新设置观察器
                if (!oldVal || newVal.length !== oldVal.length || Math.abs(newVal.length - oldVal.length) > 50) {
                    // console.log('[SfaAiQuestionItem] AI content changed significantly, refreshing observer');
                    this.setupContentResizeObserver();
                }
            },
            deep: true
        },
        // Add watcher for expanded state to recheck content
        contentExpanded: {
            handler() {
                // 展开状态变化时必须重新检查高度
                this.$nextTick(() => {
                    this.checkContentHeight();
                });
            }
        }
    },
    created() {

    },
    mounted() {
        // 只在有AI答案内容时设置观察器
        if (this.itemData && this.itemData.ai_answer_content) {
            this.setupContentResizeObserver();
        }
    },
    computed: {
        /**
         * 缓存搜索关键字对应的正则表达式
         * @returns {RegExp|null} 搜索关键字的正则表达式
         */
        highlightRegex() {
            if (!this.keyword) return null;
            // 对搜索关键字中的特殊字符进行转义，保证正则表达式安全正确
            const escapedKeyword = this.keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            return new RegExp(`(${escapedKeyword})`, 'gi');
        },
        /**
         * 计算AI答案内容区域的样式
         * @returns {Object} 样式对象
         */
        aiAnswerContentStyle() {
            // 只有当内容被截断且未展开时，才应用最大高度限制
            if (this.itemData.ai_answer_content && this.contentTruncated && !this.contentExpanded) {
                // console.log('Applying max height limit:', this.maxContentHeight);
                return {
                    maxHeight: `${this.maxContentHeight}px`,
                    overflow: 'hidden',
                    position: 'relative' // Ensure proper positioning for absolute elements
                };
            }

            // 如果内容已展开或不需要截断，则不限制高度
            // console.log('No height limit applied');
            return {
                position: 'relative' // Maintain positioning for absolute elements
            };
        },
        /**
         * 获取可见的文档列表（最多两条）
         * @returns {Array} 可见的文档列表
         */
        visibleDocuments() {
            if (!this.itemData.ai_answer_document || !this.itemData.ai_answer_document.length) {
                return [];
            }
            return this.itemData.ai_answer_document.slice(0, 2);
        }
    },
    methods: {
        jumpToYuliaoOrigin(event, itemData) {
            // 阻止事件冒泡，避免触发父元素的点击事件
            event.stopPropagation();
            // 发送事件通知父组件进行滚动
            let seqArray = [];
            if(itemData.question_seq && itemData.question_seq.length) {
                seqArray = seqArray.concat(itemData.question_seq.split(','));
            }
            if(itemData.answer_seq && itemData.answer_seq.length && JSON.parse(itemData.answer_seq) && JSON.parse(itemData.answer_seq).length) {
                seqArray = seqArray.concat(JSON.parse(itemData.answer_seq));
            }
            if(itemData.interactive_document_seq && itemData.interactive_document_seq.length) {
                seqArray = seqArray.concat(itemData.interactive_document_seq);
            }
            this.$context.$emit('corpus.list.scroll.to', JSON.stringify(seqArray));
        },
        hideMaskLoading() {
            this.maskLoading = false;
        },
        clickItem(itemData) {
            // console.log(itemData, 'itemData---');
        },
        getAvatarData(itemData, type = 'question_seq') {
            return {
                userName: type == 'question_seq' ? this.getUserNameI18n(itemData.question_proposer) : this.getUserNameI18n(itemData.answer_person),
                backgroundColor: 'rgb(255, 161, 66)',
                isAvaDefault: false,
                userIdArray: this.userAvaBgColor && Object.keys(this.userAvaBgColor).length ? this.getUserIdArrayData(itemData, type) : []
            }
        },
        getUserIdArrayData(itemData, type) {
            if(typeof itemData[type] === 'string') {
                // Try to parse the string as JSON if it looks like an array
                if(itemData[type].startsWith('[') && itemData[type].endsWith(']')) {
                    const parsedArray = JSON.parse(itemData[type]);
                    if(parsedArray.length) {
                        const realArray = parsedArray;
                        if(Array.isArray(realArray)) {
                            return this.filterUserIds(realArray).map(item => {
                                item = (item + '').trim();
                                const dongchaAvaData = this.userAvaBgColor[item] || {};
                                return {
                                    dataId: item,
                                    userName: dongchaAvaData.userName || '',
                                    backgroundColor: dongchaAvaData.avaColor || 'rgb(255, 161, 66)',
                                    useAvatarImg: !!dongchaAvaData.profileImage,
                                    avatarImgSrc: dongchaAvaData.profileImage && dongchaAvaData.profileImage[0] && dongchaAvaData.profileImage[0].signedUrl,
                                    isAvaDefault: dongchaAvaData.isAvaDefault != undefined ? dongchaAvaData.isAvaDefault : false,
                                    personnelId: dongchaAvaData.personnelId
                                }
                            });
                        }
                    }

                }
                // If not JSON or parsing failed, handle as semicolon-separated string
                return this.filterUserIds(itemData[type].split(',')).map(item => {
                    item = (item + '').trim();
                    const dongchaAvaData = this.userAvaBgColor[item] || {};
                    return {
                        dataId: item,
                        userName: dongchaAvaData.userName || '',
                        backgroundColor: dongchaAvaData.avaColor || 'rgb(255, 161, 66)',
                        isAvaDefault: dongchaAvaData.isAvaDefault != undefined ? dongchaAvaData.isAvaDefault : false,
                        useAvatarImg: !!dongchaAvaData.profileImage,
                        avatarImgSrc: dongchaAvaData.profileImage && dongchaAvaData.profileImage[0] && dongchaAvaData.profileImage[0].signedUrl,
                        personnelId: dongchaAvaData.personnelId
                    }
                })
            }else if(Array.isArray(itemData[type])) {
                return this.filterUserIds(itemData[type]).map(item => {
                    item = (item + '').trim();
                    const dongchaAvaData = this.userAvaBgColor[item] || {};
                    return {
                        dataId: item,
                        userName: dongchaAvaData.userName || '',
                        backgroundColor: dongchaAvaData.avaColor || 'rgb(255, 161, 66)',
                        isAvaDefault: dongchaAvaData.isAvaDefault != undefined ? dongchaAvaData.isAvaDefault : false,
                        useAvatarImg: !!dongchaAvaData.profileImage,
                        avatarImgSrc: dongchaAvaData.profileImage && dongchaAvaData.profileImage[0] && dongchaAvaData.profileImage[0].signedUrl,
                        personnelId: dongchaAvaData.personnelId
                    }
                })
            }
            return []
        },
        getTagsLabel(tagData) {
            if(this.tagsTypeList?.length) {
                const tag = this.tagsTypeList.find(item => item.value == tagData);
                if(tag) {
                    return tag.label;
                }
            }
            // return '';
        },
        copyInteractiveAnswer(itemData) {
            let copyOriginText = itemData.answer_person ? $t('sfa.ai.infinite.list.item.answer', {answerPerson: itemData.answer_person}) + '——' + itemData.answer_summary : itemData.answer_summary
            navigator.clipboard.writeText(copyOriginText).then(() => {
                this.$message({
                    message: $t("复制成功"),
                    type: "success",
                });
            });
        },
        copyAiAnswer(itemData) {
            if(itemData?.ai_answer_content_label) {
                navigator.clipboard.writeText($t('sfa.ai.infinite.list.item.konwledge.answer') + '——' + itemData.ai_answer_content_label).then(() => {
                    this.$message({
                        message: $t("复制成功"),
                        type: "success",
                    });
                });
            }

        },
        getBackgroundStyle(imgUrl) {
        // 如果 imgUrl 存在，生成背景样式
            return imgUrl
            ? { backgroundImage: `url(${imgUrl})` }
            : {};
        },
        formatTime(date) {
            if (!date) {
                return '--'
            }
            if (_.isNumber(date)) {
                return CRM.util.moment(date).format('YYYY-MM-DD HH:mm:ss')
            } else {
                return date || '--'
            }
        },
        clickRelatedData(relatedData) {
            this.$emit('click-related-data', relatedData);
            if(this.relatedOpenDetail) {
                this.relatedDataDetail(relatedData);
            }
        },
        relatedDataDetail(relatedData) {
            let me = this;
            let data = {
                aiQuestionId: relatedData._id
            }
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/interact_question/service/getById',
                data,
                success: function (res) {
                    console.log(res, 'res----interact_question/service/getById');
                    if(res.Result.StatusCode == 0 ) {
                        if(res.Value?.data) {
                            console.log(res.Value);
                            if(res.Value.objectDescribe && res.Value.data) {
                                const objectDescribe = res.Value.objectDescribe;
                                const item = res.Value.data;
                                if(res.Value.data.order_field) {
                                    if(res.Value.data.library_id__r) {
                                        if(res.Value.data.history_flag != 'history') {
                                            res.Value.data.question_content__o = item.order_field + '、' + item.library_id__r;
                                        }else{
                                            res.Value.data.question_content__o = item.library_id__r;
                                        }
                                    }
                                }
                                if(item.advice_status == 'clearly_defined') {
                                    item.fxTagType = 'success'
                                }else if(item.advice_status == 'unclearly_defined') {
                                    item.fxTagType = 'link'
                                } else {
                                    item.fxTagType = 'warning'
                                }
                                if(objectDescribe?.fields?.advice_status?.options) {
                                    const option = objectDescribe.fields.advice_status.options.find(option => option.value ==  item.advice_status);
                                    if(option) {
                                        res.Value.data.advice_status = option.label;
                                    }
                                }

                            }
                            me.$emit('trigger-dialog', res.Value.data)

                        }
                    }
                },
                error(err) {
                }
            }, {
                errorAlertModel: 1
            });
        },
        hideDetailDialog() {
          this.detailDialogVisible = false;
        },
        destroy() {
            this.detailDialogVisible = false;
            this.detailDialogLoading = false;
            this.detailItemData = {};
        },
        visibleRelatedData(dataArray) {
            return dataArray.slice(0, 2);
        },
        remainingRelatedData(dataArray) {
            return dataArray.slice(2);
        },
        /**
         * 高亮显示文本中匹配搜索关键字的部分
         * @param {string} text - 待高亮的文本
         * @param {string} type - 文本类型 ('question' 或 'answer')
         * @returns {string} 渲染后的HTML字符串
         */
        highlightText(text, type = 'question') {
            if (!this.keyword || !text) return text;

            try {
                // 重置计数器
                if (type === 'question') {
                    this.questionMatchCount = 0;
                } else if (type === 'answer') {
                    this.answerMatchCount = 0;
                }

                // 确保text是字符串
                const textStr = String(text);

                // 对搜索关键字中的特殊字符进行转义，保证正则表达式安全正确
                const escapedKeyword = this.keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                const regex = new RegExp(`(${escapedKeyword})`, 'gi');

                // 应用高亮
                const highlightedText = textStr.replace(regex, (match) => {
                    if (type === 'question') {
                        this.questionMatchCount++;
                        return `<span class="activity-search-highlight" data-id="${this.itemData._id}-q${this.questionMatchCount}">${match}</span>`;
                    } else {
                        this.answerMatchCount++;
                        return `<span class="activity-search-highlight" data-id="${this.itemData._id}-a${this.answerMatchCount}">${match}</span>`;
                    }
                });

                // 发送匹配事件
                if ((type === 'question' && this.questionMatchCount > 0) ||
                    (type === 'answer' && this.answerMatchCount > 0)) {
                    this.$nextTick(() => {
                        this.totalMatchCount = this.questionMatchCount + this.answerMatchCount;
                        this.$emit('match-found', {
                            itemId: this.itemData._id,
                            matchCount: this.totalMatchCount,
                            questionMatchCount: this.questionMatchCount,
                            answerMatchCount: this.answerMatchCount
                        });
                    });
                }

                return highlightedText;
            } catch (error) {
                console.error('Error in highlightText:', error);
                return text;
            }
        },
        /**
         * 检查内容是否包含关键词并触发滚动
         */
        checkAndScrollToMatch() {
            if (!this.keyword) return;

            // 检查问题内容
            const questionContent = this.itemData.question_content || this.itemData.question_content__o || '';
            // 检查答案内容
            const answerContent = this.itemData.answer_summary || '';

            // 如果任一内容包含关键词，触发匹配事件
            if (questionContent.toLowerCase().includes(this.keyword.toLowerCase()) ||
                answerContent.toLowerCase().includes(this.keyword.toLowerCase())) {
                this.$emit('match-found', this.$el);
            }
        },
        /**
         * 检查内容高度是否超过最大高度
         */
        checkContentHeight() {
            // 如果没有内容，不需要显示"查看更多"按钮
            if (!this.itemData || !this.itemData.ai_answer_content) {
                this.contentTruncated = false;
                return;
            }

            // 确保内容引用存在
            if (!this.$refs.aiContentRef) {
                console.warn('aiContentRef not found, cannot check height');
                return;
            }

            const element = this.$refs.aiContentRef.$el;
            if (!element) {
                console.warn('aiContentRef.$el not found, cannot check height');
                return;
            }

            try {
                // 获取实际渲染的内容高度
                const scrollHeight = element.scrollHeight;
                const offsetHeight = element.offsetHeight;
                const clientHeight = element.clientHeight;

                // 判断是否需要截断显示
                const shouldTruncate = scrollHeight > this.maxContentHeight;

                // 只有当内容高度合理时才设置状态
                if (scrollHeight > 0) {
                    // 如果内容已展开，保持按钮可见（显示"收起"）
                    // 否则按照内容高度决定是否显示"查看更多"按钮
                    this.contentTruncated = this.contentExpanded || shouldTruncate;

                    console.log('Content height check:', {
                        scrollHeight,
                        offsetHeight,
                        clientHeight,
                        maxHeight: this.maxContentHeight,
                        shouldTruncate,
                        contentTruncated: this.contentTruncated,
                        contentExpanded: this.contentExpanded
                    });
                } else {
                    console.warn('Invalid content height detected, scrollHeight =', scrollHeight);
                }

                // 确保UI更新
                this.$forceUpdate();
            } catch (error) {
                console.error('Error checking content height:', error);
            }
        },
        /**
         * 展开或收起内容
         */
        expandContent(event) {
            event.stopPropagation();
            this.contentExpanded = !this.contentExpanded;
            this.contentTruncated = true; // Always show the button
        },
        /**
         * 打开文档URL
         * @param {string} url - 文档URL
         */
        openDocumentUrl(url) {
            if (!url) return;
            window.open(url, '_blank');
        },
        /**
         * 设置ResizeObserver监控内容区域大小变化
         */
        setupContentResizeObserver() {
            // 如果已经存在观察器，先清理
            if (this.contentResizeObserver) {
                this.contentResizeObserver.disconnect();
                this.contentResizeObserver = null;
            }

            // 等待内容组件挂载
            this.$nextTick(() => {
                if (this.$refs.aiContentRef && this.$refs.aiContentRef.$el) {
                    // 创建并配置ResizeObserver
                    this.contentResizeObserver = new ResizeObserver(entries => {
                        // 当观察到尺寸变化时，重新检查高度
                        this.checkContentHeight();
                        // console.log('Content resize detected, rechecking height');
                    });

                    // 开始观察AI内容元素
                    this.contentResizeObserver.observe(this.$refs.aiContentRef.$el);
                    // console.log('ResizeObserver setup for AI content');

                    // 设置完观察器后立即检查一次高度
                    this.checkContentHeight();
                } else {
                    // 如果元素还不存在，延迟重试
                    // console.log('AI content ref not found, retrying later');
                    setTimeout(() => this.setupContentResizeObserver(), 200);
                }
            });
        },
        /**
         * 处理用户ID数组，按照nameAvaId去重
         * @param {Array} userIds - 用户ID数组
         * @returns {Array} 处理后的用户数据数组
         */
        filterUserIds(userIds) {
            if (!userIds || !userIds.length) {
                return [];
            }
            // 按nameAvaId分组
            const nameAvaIdGroups = {};
            const noNameAvaIdUsers = [];
            // 第一步：分组
            userIds.forEach(userId => {
                userId = (userId + '').trim();
                const userData = this.userAvaBgColor[userId];
                if (userData && userData.nameAvaId+'') {
                    // 有nameAvaId的用户按nameAvaId分组
                    const nameAvaId = userData.nameAvaId + '';
                    if (!nameAvaIdGroups[nameAvaId]) {
                        nameAvaIdGroups[nameAvaId] = [];
                    }
                    nameAvaIdGroups[nameAvaId].push(userId);
                } else {
                    // 没有nameAvaId的用户直接加入结果
                    noNameAvaIdUsers.push(userId);
                }
            });
            // 第二步：从每个分组中取第一个元素
            const result = [...noNameAvaIdUsers];
            Object.values(nameAvaIdGroups).forEach(group => {
                if (group.length > 0) {
                    result.push(group[0]);
                }
            });
            return result;
        },
    },
    beforeDestroy() {
        // 清理ResizeObserver
        if (this.contentResizeObserver) {
            this.contentResizeObserver.disconnect();
            this.contentResizeObserver = null;
        }
    }
  }
  </script>
<!-- 复制答案区域，新建任务 回填答案区域，策略明显选择问题单选，明细序号如何处理 -->
  <style lang="less" scoped>
    .sfa-ai-activity-mark-loading{
        // opacity: 0.5;
        // /deep/.el-loading-mask{
        //     background-color: rgba(255, 255, 255, .7) !important;
        // }
    }
    .sfa-ai-activity-mark-keynote{
        border-radius: 8px;
        border-left: 3px solid #BC97F7;
        background: var(--color-neutrals01);
    }
    .sfa-ai-activity-mark-invalid{
        border-radius: 8px;
        border-left: 3px solid #DEE1E8;
        background: var(--color-neutrals01);
    }
    .sfa-ai-question-item-container{
        border-radius: 12px;
        background: var(--color-neutrals01);
        padding: 12px 12px 12px 12px;
        position: relative;
        z-index: 1;
        .mark-overlay{
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(255, 255, 255, 0.5); // 半透明白色背景
            pointer-events: none; // 确保蒙层不影响下层内容的交互
            z-index: 5; // 确保蒙层在内容之上
        }
        /deep/.activity-search-highlight {
            background-color: var(--color-primary06);
            padding: 0 2px;
            border-radius: 2px;
            font-weight: 500;
            color: white;

            &.current-highlight {
                background-color: #976AEB;  // 当前高亮元素的特殊样式
                transition: background-color 0.2s ease;  // 添加过渡效果
            }
        }

        .top-right-box{
            cursor: pointer;
            position: absolute;
            right: 0px;
            top: -8px;
            padding: 5px 8px;
            padding-bottom: 7px;
            border-radius: 4px 12px 4px 4px;
            background: #FFF;
            box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.10);
            box-sizing: border-box;
            line-height: 12px;
            opacity: 0;
            z-index: 6;
            font-size: 11px;
            .add-task-button{
                color: #000;
                // margin-right: 4px;
                .fx-icon-obj-app352{
                    margin-right: 2px;
                    font-size: 12px;
                    position: relative;
                    top: 1px;
                }
                .fx-icon-obj-app352::before{
                    color: #181C25;
                }
                .fx-icon-obj-app29{
                    margin-right: 2px;
                }
                .fx-icon-obj-app29::before{
                    color: #7341de;
                }
                .fx-icon-rubber{
                    margin-right: 2px;
                }
                .fx-icon-rubber::before{
                    color: #30c776;
                }

            }

            .middle-line{
                display: inline-block;
                position: relative;
                top: 0px;
                width: 1px;
                height: 8px;
                background: var(--color-neutrals05);
                margin: 0 8px;
                &.expand-line {
                    margin: 0 6px;
                }
            }
            .jump-to-yuliao{
                color: #000;
                .fx-icon-obj-app320{
                    margin-right: 2px;
                    font-size: 12px;
                    position: relative;
                    top: 1px;
                }
                .fx-icon-obj-app320::before{
                    color: #181C25;
                }
            }
            .ai-create-button{
                font-size: 11px;
                color: #000;
                // margin-left: 4px;
                .refresh-item-icon{
                    position: relative;
                    top: 2px;
                    margin-right: 4px;
                    height: 13px;
                    width: 13px;
                }
            }
            .expand-collapse-list {
                cursor: pointer;
                .fx-icon-list-expand {
                    position: relative;
                    top: 0px;
                }
                .fx-icon-list-collapse {
                    position: relative;
                    top: 0px;
                }
            }
        }
        .top-right-box:hover{
            opacity: 1;
            z-index: 3;
        }
        .question-title-box {
            position: relative; /* 让子元素的绝对定位基于容器 */
            width: 100%;        /* 容器宽度可调整 */
            word-wrap: break-word; /* 文本内容自动换行 */
            font-size: 15px;
            font-style: normal;
            font-weight: 500;
            width: 100%;
            padding-right: 34px;
            box-sizing: border-box;
            .question-content-box {
                display: flex; /* 文本部分自然流动，支持自动换行 */
                color: var(--color-neutrals19);
                .question-content-avatar{
                    width: 34px;
                    padding: 0 8px 0 2px;
                    box-sizing: border-box;
                }
                .question-content{
                    flex: 1;
                    font-size: 14px;
                    font-weight: 700;
                    line-height: 24px;
                    word-wrap: break-word;
                    word-break: break-all;
                    .question-content-tag{
                        font-weight: 400;
                        position: relative;
                        top: -1px;
                        margin-left: 8px;
                        .question-content-tag-item{
                            margin-left: 4px;
                            background: #E2FFF7;
                            color: #16B4AB;
                            border-color: #E2FFF7;
                        }
                        .question-history-tag-item{
                            margin-left: 4px;
                        }
                    }

                }
                .occupy-empty-content{
                    display: inline-block;
                    width: 65px;
                    height: 16px;
                    position: relative;
                    top: 2px;
                }
            }
            .right-action-button {
                position: absolute;
                white-space: nowrap; /* 避免"生成代办"内容换行 */
                right: 0px;            /* 紧贴容器右侧 */
                cursor: pointer;
                color: var(--color-info06);
                text-align: center;
                font-size: 12px;
                font-weight: 400;
                line-height: 24px;
                .fx-icon-obj-app352{
                    margin-right: 4px;
                    font-size: 12px;
                }
                .fx-icon-obj-app352::before{
                    color: var(--color-info06);
                }
            }
        }
        .answer-box{
            width: 100%;
            padding-left: 34px;
            box-sizing: border-box;
            display: flex;
            margin-top: 6px;
            .single-all-record-summary-box{
                color: var(--color-neutrals11);
                text-align: left;
                font-size: 12px;
                font-weight: 400;
                line-height: 18px;
                margin-bottom: 2px;
                .record-summary-tip{
                    margin-right: 2px;
                }
                .fx-icon-question::before{
                    color: var(--color-neutrals07);
                }
            }
            .top-answer{
                .top-answer-origin{
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 2px;
                    align-items: center;
                    .origin-text{
                        color: var(----H1, #181C25);
                        font-size: 13px;
                        font-weight: 500;
                        line-height: 18px;
                        .fx-icon-AI{
                            margin-right: 2px;
                        }
                        .fx-icon-AI::before{
                            color: #5B70EA;
                        }
                    }
                    .answer-attitude{
                        // color: var(--color-danger06);
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 18px;
                        align-items: center;
                        position: relative;
                        top: -2px;
                        .answer-attitude-img{
                            position: relative;
                            top: 4px;
                            background-size: contain;
                            background-repeat: no-repeat;
                            background-position: center;
                            display: inline-block;
                            width: 16px;
                            height: 16px;
                        }
                        .answer-attitude-icon{
                            display: inline-block;
                            position: relative;
                            top: 2px;
                            font-size: 16px;
                        }
                        .satisfied {
                            color: var(--color-success06);
                        }
                        .general {
                            color: #FFB602;
                        }
                        .dissatisfied {
                            color: var(--color-danger06);
                        }
                        .not_responded {
                            color: var(--color-neutrals15);
                        }
                        .answer-attitude-text{
                            display: inline-block;
                            line-height: 18px;
                        }
                    }
                }
                .top-answer-content{
                    color: var(----H1, #181C25);
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 22px;
                    word-wrap: break-word;
                    word-break: break-all;
                    white-space: pre-wrap;
                    .fxeditor-render-doc{
                        font-size: 13px;
                    }
                }
                .top-answer-content-ai{
                    color: var(----H1, #181C25);
                    font-size: 13px;
                    font-weight: 400;
                    line-height: 22px;
                    position: relative;
                    .fxeditor-render-doc{
                        font-size: 13px;
                    }
                }
                .top-answer-nocontent{
                    color: var(--color-neutrals07);
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 20px;
                    justify-content: space-between;
                    display: flex;
                    .fx-icon-refresh{
                        position: relative;
                        top: 4px;
                    }
                }
            }
            .left-answer{
                border-radius: var(--8, 8px);
                border: 2px solid var(--color-special01);
                padding: 6px 8px;
                box-sizing: border-box;
                flex: 1;                /* 关键属性：占据剩余空间 */
                min-width: 0;
                position: relative;
                .answer-edit-box{
                    cursor: pointer;
                    position: absolute;
                    right: 0px;
                    top: 0px;
                    background: #FFF;
                    box-sizing: border-box;
                    opacity: 0;
                    z-index: 6;
                    font-size: 12px;
                    display: flex;
                    padding: 0px 6px;
                    justify-content: center;
                    align-items: center;
                    gap: 8px;
                    border-radius: 0px 8px 4px 4px;
                    background: #FFF;
                    box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.10);
                    height: 21px;
                    .edit-button{
                        color: #000;
                        .fx-icon-bianji{
                            font-size: 12px;
                        }
                        .fx-icon-bianji::before{
                            color: #181C25;
                        }
                    }
                    .middle-line{
                        display: inline-block;
                        position: relative;
                        top: 0px;
                        width: 1px;
                        height: 8px;
                        background: var(--color-neutrals05);
                    }
                    .jump-to-yuliao{
                        color: #000;
                        .fx-icon-obj-app320{
                            margin-right: 2px;
                            font-size: 12px;
                            position: relative;
                            top: 1px;
                        }
                        .fx-icon-obj-app320::before{
                            color: #181C25;
                        }
                    }
                }
                .el-loading-mask{
                    background-color: #fff;
                    bottom: 1px;
                    left:  1px;
                    right: 1px;
                    top: 1px;
                    .circular{
                        width: 20px;
                    }
                }
                .editable--editing{
                    padding: 0px;
                    border: none;
                    border-radius: 0;
                }
                /deep/ .editable-actions{
                    position: relative;
                }
                /deep/ .editable-actions--bottom-right{
                    position: relative;
                    bottom: 0px;
                    right: 0px;
                    display: flex;
                    justify-content: flex-end;
                }
            }
            .right-answer{
                width: 34px;
                padding-left: 6px;
                box-sizing: border-box;
                padding-top: 3px;
                .avatar {
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 12px;
                    flex-shrink: 0;
                    // margin-top: 2px;
                    .avatar-text {
                        color: #fff;
                        font-size: 12px;
                        transform: scale(0.833);
                        transform-origin: center center;
                    }
                }

            }
            .bottom-time-action{
                margin-top: 2px;
                display: flex;
                justify-content: space-between;
                .bottom-time{
                    color: var(---Light, #C1C5CE);
                    font-size: 12px;
                    font-weight: 400;
                    line-height: 18px;
                }
                .bottom-action{
                    span {
                        margin-right: 12px;
                        &:last-child{
                            margin-right: 0px;
                        }
                    }
                    &:last-child{
                        margin-right: 0px;
                    }
                    .fx-icon-refresh{
                        cursor: pointer;
                    }
                    .fx-icon-fuzhi{
                        cursor: pointer;
                    }
                }
            }
        }
        .noknowledge-answer-box{
            margin-bottom: 0px;
        }
        .knowledge-answer-box{
            margin-bottom: 0px;
            /deep/ .el-loading-spinner{
                margin-top: -15px;
            }

            /deep/ .fxeditor-render-doc-con > p:first-child,
            ::v-deep .fxeditor-render-doc-con > p:first-child {
                margin-bottom: 0px;
                margin-top: 0px;
            }
        }
        .relative-object-data-box{
            margin-top: 8px;
            padding-left: 34px;
            .relative-object-title{
                margin-bottom: 8px;
                color: var(--color-neutrals19);
                font-size: 13px;
                font-weight: 500;
                line-height: 18px;
            }
            .relative-object-data-list{
                .relative-object-data-item{
                    display: inline-block;
                    padding: 0 4px;
                    margin-right: 8px;
                    color: var(--color-neutrals15);
                    text-align: center;
                    font-size: 12px;
                    font-weight: 400;
                    max-width: 330px;
                    border-radius: 4px;
                    background: var(--color-special01);
                    height: 24px;
                    cursor: pointer;
                    .relative-item-box{
                        width: 100%;
                        height: 24px;
                        .relative-object-data-item-text{
                            display: inline-block;
                            line-height: 24px;
                            margin-right: 2px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            max-width: 308px;
                            position: relative;
                            top: 1px;
                        }
                        .relative-object-data-item-text-more{
                            position: relative;
                            top: -2px;
                            left: 1px;
                        }
                        .fx-icon-lianjie{
                            display: inline-block;
                            position: relative;
                            top: -8px;
                        }
                        .fx-icon-lianjie::before{
                            color: var(--color-special02);
                        }
                    }
                }
            }
        }
        .ai-no-reference {
            display: flex;
            align-items: flex-start;  // 改为 flex-start 以确保图标在顶部
            margin-bottom: 4px;
            .unbase-knowledge-icon {
                width: 12px;
                height: 12px;
                flex-shrink: 0;  // 防止图标缩小
                position: relative;
                top: 3px;
            }
            .ai-text {
                color: #91959E;
                font-family: Source Han Sans CN;
                font-size: 12px;
                font-weight: 400;
                line-height: 18px;
                margin-left: 4px;
                flex: 1;  // 让文本区域占据剩余空间
                word-wrap: break-word;  // 允许文本换行
                word-break: break-word;  // 确保长单词也能换行
            }
        }
        .view-more-btn {
            cursor: pointer;
            position: relative;
            color: var(--color-info06);
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            text-align: left;
            .fx-icon-arrow-down2, .fx-icon-arrow-up2 {
                margin-left: 2px;
            }
            .fx-icon-arrow-down2::before, .fx-icon-arrow-up2::before {
                color: var(--color-info06);
            }
        }
        .document-list {
            font-size: 13px;
            display: block;
            line-height: 24px;

            .document-list-label {
                font-size: 13px;
                font-weight: 400;
                color: var(--color-neutrals11);
                margin-right: 4px;
                display: inline;
            }

            .document-list-items {
                display: inline;
                cursor: default;
                .document-item {
                    margin-right: 0;
                    cursor: pointer;
                    color: var(--color-info06);
                    font-size: 13px;
                    font-weight: 400;
                    display: inline-flex;
                    align-items: center;

                    .document-item-text {
                        display: inline-block;
                        white-space: nowrap !important;
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                        max-width: 210px !important;
                        vertical-align: middle;
                    }
                }

                .document-separator {
                    color: var(--color-neutrals11);
                    font-size: 13px;
                    font-weight: 400;
                    margin: 0 2px;
                    cursor: default;
                    flex-shrink: 0;
                }

                .document-more {
                    cursor: default;
                    color: var(--color-info06);
                    font-size: 13px;
                    font-weight: 400;
                    flex-shrink: 0;
                }
            }
        }
        .edit-answer-tip {
            color: var(--color-danger06);
            font-size: 12px;
        }
    }
  </style>
  <style lang="less">
    .sfa-activity-relative-data-item-tooltip {
        max-width: 420px;
        max-height: 400px;
        overflow-y: auto;

        .tooltip-content {
            padding: 8px;

            .relative-object-data-item{
                    display: inline-block;
                    padding: 0 4px;
                    margin-right: 8px;
                    color: var(--color-neutrals15);
                    text-align: center;
                    font-size: 12px;
                    font-weight: 400;
                    max-width: 210px;
                    border-radius: 4px;
                    background: var(--color-special01);
                    height: 24px;
                    cursor: pointer;
                    .relative-item-box{
                        width: 100%;
                        height: 24px;
                        .relative-object-data-item-text{
                            display: inline-block;
                            line-height: 24px;
                            margin-right: 2px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            max-width: 308px;
                            position: relative;
                            top: 1px;
                        }
                        .relative-object-data-item-text-more{
                            position: relative;
                            top: -2px;
                            left: 1px;
                        }
                        .fx-icon-lianjie{
                            display: inline-block;
                            position: relative;
                            top: -8px;
                        }
                        .fx-icon-lianjie::before{
                            color: var(--color-special02);
                        }
                    }
                }
        }
    }
    .sfa-activity-document-list-tooltip {
        max-width: 400px;
        max-height: 400px;
        overflow-y: auto;
        min-width: 150px;

        .tooltip-content {
            padding: 8px;
            width: 100%;
            display: flex;
            flex-wrap: wrap;
            .document-tooltip-item {
                margin-bottom: 8px;
                cursor: pointer;
                color: var(--color-info06);
                font-size: 13px;
                font-weight: 400;
                line-height: 20px;
                margin-right: 8px;
                display: inline-flex;
                align-items: center;
                white-space: nowrap;
                span {
                    &:first-child {
                        color: var(--color-info06);
                        cursor: pointer;
                        white-space: break-spaces;
                    }
                }

                .document-tooltip-separator {
                    color: var(--color-neutrals11);
                    margin: 0 2px;
                    cursor: default;
                }

                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        /* Global style to ensure document-item-text works everywhere */
        .document-item .document-item-text {
            display: inline-block !important;
            white-space: nowrap !important;
            overflow: hidden !important;
            text-overflow: ellipsis !important;
            max-width: 210px !important;
            vertical-align: middle !important;
        }
    }
  </style>