<template>
    <fx-scrollbar
    ref="scrollbar"
    class="custom-chart-tooltip-scrollbar"
    wrapClass="custom-chart-tooltip-scrollbar-wrap"
    noresize
    >
    <div class="profile-custom-tooltip" @mouseleave="handleMouseLeave">
        <div v-for="(item, index) in items" :key="index" class="tooltip-item">
            <p class="tooltip-item-title">
                <span class="tooltip-item-name">{{ item.name }}</span>
                <span class="tooltip-item-value" v-if="item.value">{{ item.value }}{{ $t('sfa.aiCustomerProfile.scoreUnit') }}</span>
            </p>
            <span class="fx-icon-loading-ring" v-if="item.descriptionLoading"></span>
            <ul class="tooltip-item-description" v-if="item.description.length">
                <li v-for="(item, index) in item.description" :key="index" class="tooltip-item-description-item">
                    <span> {{ item.name }}:</span>
                    <span v-html="item.value"></span>
                </li>
            </ul>
        </div>
        <div v-if="date" class="tooltip-date">{{ date }}</div>
    </div>
    </fx-scrollbar>
</template>

<script>
export default {
    name: 'BaseDimensionTrendChartTooltip',
    props: {
        date: {
            type: String,
            default: ''
        },
        items: {
            type: Array,
            default: () => []
        },
    },
    watch: {
        items: {
            handler() {
                this.updateScrollbarHeight()
            },
            immediate: true,
            deep: true,
        }
    },
    methods: {
        handleMouseLeave(e) {
            // 触发自定义事件，通知父组件
            this.$emit('tooltip-mouseleave', e);
        },
        updateScrollbarHeight() {
            this.$nextTick(() => {
                const $scrollbar = this.$refs.scrollbar
                if (!$scrollbar) return

                // 获取内容实际高度
                const contentEl = $scrollbar.$el.querySelector('.profile-custom-tooltip')
                if (!contentEl) return
                
                // 计算内容实际高度
                const contentHeight = contentEl.scrollHeight
                
                // 设置scrollbar高度，不超过最大高度500px
                const maxHeight = 500
                const newHeight = Math.min(contentHeight, maxHeight)
                
                // 设置最小高度100px
                const finalHeight = Math.max(newHeight, 100)
                
                // 应用高度
                $scrollbar.$el.style.height = `${finalHeight}px`
            })
        }
    }
}
</script>

<style lang="less">
.custom-chart-tooltip-scrollbar {
    box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
    .custom-chart-tooltip-scrollbar-wrap {
        /* 隐藏滚动条 */
        &::-webkit-scrollbar {
            display: none;
        }
        scrollbar-width: none;
        -ms-overflow-style: none;
         /* 关键：移除滚动条占位 */
        margin-right: 0 !important;
        padding-right: 0 !important;
        
        /* 确保内容宽度正确 */
        width: 100% !important;
    }
}
</style>
<style lang="less" scoped>
.profile-custom-tooltip {
    padding: 12px;
    border-radius: 8px;
    background: var(--color-neutrals01);
    display: flex;
    flex-direction: column;
    gap: 20px;
    box-sizing: border-box;
}
.tooltip-date {
    font-size: 12px;
    line-height: 20px;
    color: #86909c;
}
.tooltip-item {
    font-size: 13px;
    color: var(--color-neutrals19);
    .tooltip-item-title {
        display: flex;
        align-items: center;
        gap: 4px;
        font-weight: 700;
        line-height: 12px;

        .tooltip-item-name {
            font-size: 13px;
        }
        .tooltip-item-value {
            font-size: 12px;
            line-height: 18px;
            font-weight: 700;
            display: inline-block;
            border-radius: 4px;
            padding: 4px;
            background: #FFF7E6;
            color: #FF8000;
        }
    }
    .tooltip-item-description {
        margin-top: 8px;
        font-size: 13px;
        line-height: 22px;
        list-style: disc;
        list-style-position: inside;
    }
}

</style>
