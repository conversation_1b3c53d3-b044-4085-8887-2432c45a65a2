<template>
    <div class="sfa-ai-question-types-container" ref="container">
        <div class="question-types-box" >
            <div class="question-types-item" @click="changQuestioneType(item)" :class="[item.id == nowTypesName ?'now-types-item' : '', shouldHideText ? 'hide-types-item' : '']" v-for="(item,index) in dataList" :key="index">
                <div v-if="!item.tip">
                    <div
                    :class="`types-item types-item-icon ${item.icon}`"
                    >
                        <div
                        v-if="item.imgUrl"
                        :class="`types-item-img`"
                        :style="getBackgroundStyle(item.imgUrl)"
                        >
                        </div>
                    </div>
                    <div class="types-item types-item-text" :class="{ 'hide-text': shouldHideText }">{{ item.text }}</div>
                    <div class="types-item types-item-num" :title="shouldHideText ? item.num : ''" :class="{ 'hide-text-num': shouldHideText }">{{ item.num }}</div>
                    <div class="types-item types-item-unit">{{ item.unit }}</div>
                </div>
                <fx-tooltip v-if="item.tip" :open-delay="600" effect="light" :content="item.tip" placement="top">
                    <div>
                        <div
                        :class="`types-item types-item-icon ${item.icon}`"
                        >
                            <div
                            v-if="item.imgUrl"
                            :class="`types-item-img`"
                            :style="getBackgroundStyle(item.imgUrl)"
                            >
                            </div>
                        </div>
                        <div class="types-item types-item-text" :class="{ 'hide-text': shouldHideText }">{{ item.text }}</div>
                        <div class="types-item types-item-num" :title="shouldHideText ? item.num : ''" :class="{ 'hide-text-num': shouldHideText }">{{ item.num }}</div>
                        <div class="types-item types-item-unit">{{ item.unit }}</div>
                    </div>
                </fx-tooltip>
            </div>
        </div>
         <fx-popover
            placement="bottom-end"
            :arrowOffset="1"
            width="256"
            ref="popover"
            trigger="click"
            popper-class="sfa-activity-tags-popover"
        >
            <div class="tags-list">
                <div class="filter-list-title">{{$t('sfa.ai.activity.type.filter')}}</div>
                <div class="tags-list-title" v-if="tagsTypeList.length && tagsTypeList.length > 1">{{$t('sfa.ai.activity.list.filter.all')}}</div>
                <div class="tags-list-item-box" v-if="tagsTypeList.length && tagsTypeList.length > 1">
                    <div class="tags-list-item"
                        :class="{ 'selected': selectedType.includes(item.value) }"
                        v-for="(item,index) in tagsTypeList"
                        :key="index"
                        @click="handleTagClick(item)">
                        <div class="tags-list-item-text">{{ item.label }}</div>
                    </div>
                </div>
                <div class="mark-list-title" v-if="markTypeList.length">{{$t('sfa.ai.activity.list.filter.mark')}}</div>
                <div class="tags-list-item-box" v-if="markTypeList.length">
                    <div class="tags-list-item"
                        :class="{ 'selected': selectedMaskType.includes(item.value) }"
                        v-for="(item,index) in markTypeList"
                        :key="index"
                        @click="handleMarkClick(item)">
                        <div class="tags-list-item-text">{{ item.label }}</div>
                    </div>
                </div>
                <div class="mark-list-title" v-if="userAvatarList.length">{{$t('sfa.ai.activity.list.filter.avatar')}}</div>
                <div class="tags-list-item-box" v-if="userAvatarList.length">
                    <div class="tags-list-item tags-list-item-avatar"
                        v-for="(item,index) in userAvatarList"
                        :key="index"
                    >
                        <div class="tags-list-item tags-list-item-avatar-all"
                            v-if="item.value == 'all'"
                            :class="{ 'selected': selectedUserAvatar.includes(item.value)}"
                            :key="index"
                            @click="handleAvatarClick(item)">
                            <div class="tags-list-item-text">{{ item.label }}</div>
                        </div>
                        <div
                            v-else
                            class="tags-list-item-avatar-item"
                            @click="handleAvatarClick(item)"
                            :class="{ 'selected': selectedUserAvatar.includes(item.dataId)}"
                        >
                            <sfa-ai-avatar
                                :data="item"
                                :show-tooltip="false"
                                :showtopRightBadge="false"
                            />
                            <span class="tags-list-item-avatar-item-text">{{ item.userName }}</span>
                        </div>

                    </div>
                </div>
            </div>
            <span slot="reference" class="right-icon fx-icon-filter" v-show="tagsTypeList.length || markTypeList.length || userAvatarList.length"></span>
        </fx-popover>
    </div>
  </template>
  <script>
  import sfaAiAvatar from '../sfaAiAvatar/sfaAiAvatar.vue';
  export default {
    components: {
        sfaAiAvatar
    },
    props: {
        dataList: {
            type: Array,
            default: () => [],
        },
        selectedType: {
            type: Array,
            default: () => ['all']
        },
        selectedMaskType: {
            type: Array,
            default: () => ['all']
        },
        tagsTypeList: {
            type: Array,
            default: () => [],
        },
        markTypeList: {
            type: Array,
            default: () => [],
        },
        userAvatarList: {
            type: Array,
            default: () => [],
        },
        selectedUserAvatar: {
            type: Array,
            default: () => [],
        },
    },
    data() {
      return {
        nowTypesName: '',
        shouldHideText: false,
        resizeObserver: null
      };
    },
    mounted() {
        if(this?.dataList?.length) {
            this.nowTypesName = this.dataList[0].id;
        }
        this.checkTextVisibility();
        // 创建 ResizeObserver 来监听容器大小变化
        this.resizeObserver = new ResizeObserver(() => {
            this.checkTextVisibility();
        });

        if (this.$refs.container) {
            this.resizeObserver.observe(this.$refs.container);
        }
    },
    beforeDestroy() {
        // 清理 ResizeObserver
        if (this.resizeObserver) {
            this.resizeObserver.disconnect();
        }
    },
    methods: {
      getBackgroundStyle(imgUrl) {
        // 如果 imgUrl 存在，生成背景样式
        return imgUrl
        ? { backgroundImage: `url(${imgUrl})` }
        : {};
      },
      changQuestioneType(item) {
        this.nowTypesName = item.id;
        this.$emit('changeQuestionType', item.type)
      },
      checkTextVisibility() {
        this.$nextTick(() => {
            const container = this.$refs.container;
            if (!container) return;

            // 临时显示所有文本以获取完整宽度
            this.shouldHideText = false;
            this.$nextTick(() => {
                const containerWidth = container.offsetWidth;
                const items = container.querySelectorAll('.question-types-item');
                const filterIcon = container.querySelector('.right-icon');
                let totalWidth = 0;

                items.forEach(item => {
                    totalWidth += item.offsetWidth;
                });

                // 考虑到 gap 的宽度 (12px * (items.length - 1))
                totalWidth += (items.length - 1) * 12;

                // 考虑 filter icon 的宽度和间距
                if (filterIcon) {
                    totalWidth += filterIcon.offsetWidth + 12; // 12px 是 gap
                }

                // 如果总宽度超过容器宽度，则隐藏文本
                this.shouldHideText = totalWidth > containerWidth;
            });
        });
      },
      handleTagClick(item) {
        let newSelectedType = [...this.selectedType];

        if (item.value === 'all') {
            // 如果点击"全部"，则清除其他选择
            newSelectedType = ['all'];
        } else {
            // 如果已选中，则取消选中
            if (newSelectedType.includes(item.value)) {
                newSelectedType = newSelectedType.filter(type => type !== item.value);
                // 如果没有选中任何项，则自动选中"全部"
                if (newSelectedType.length === 0) {
                    newSelectedType = ['all'];
                }
            } else {
                // 如果未选中，则添加到选中列表
                // 如果当前包含"全部"，则需要移除"全部"
                if (newSelectedType.includes('all')) {
                    newSelectedType = [item.value];
                } else {
                    newSelectedType.push(item.value);
                }
            }
        }

        this.$emit('update:selectedType', newSelectedType);
      },
      handleMarkClick(item) {
        let newSelectedType = [...this.selectedMaskType];

        if (item.value === 'all') {
            // 如果点击"全部"，则清除其他选择
            newSelectedType = ['all'];
        } else {
            // 如果已选中，则取消选中
            if (newSelectedType.includes(item.value)) {
                newSelectedType = newSelectedType.filter(type => type !== item.value);
                // 如果没有选中任何项，则自动选中"全部"
                if (newSelectedType.length === 0) {
                    newSelectedType = ['all'];
                }
            } else {
                // 如果未选中，则添加到选中列表
                // 如果当前包含"全部"，则需要移除"全部"
                if (newSelectedType.includes('all')) {
                    newSelectedType = [item.value];
                } else {
                    newSelectedType.push(item.value);
                }
            }
        }
        this.$emit('update:selectedMaskType', newSelectedType);
      },
      handleAvatarClick(item) {
        let newSelectedUserAvatar = [...this.selectedUserAvatar];

        if (item.value === 'all') {
            // If clicking the "all" option, clear other selections
            newSelectedUserAvatar = ['all'];
        } else {
            // If already selected, remove it
            if (newSelectedUserAvatar.includes(item.dataId)) {
                newSelectedUserAvatar = newSelectedUserAvatar.filter(id => id !== item.dataId);
                // If no selections left, auto-select "all"
                if (newSelectedUserAvatar.length === 0) {
                    newSelectedUserAvatar = ['all'];
                }
            } else {
                // If selecting a specific avatar, remove the "all" option
                if (newSelectedUserAvatar.includes('all')) {
                    newSelectedUserAvatar = [item.dataId];
                } else {
                    newSelectedUserAvatar.push(item.dataId);
                }
            }
        }

        this.$emit('update:selectedUserAvatar', newSelectedUserAvatar);
      }
    },
    watch: {
        dataList: {
            handler() {
                this.$nextTick(() => {
                    this.checkTextVisibility();
                });
            },
            deep: true
        }
    }
  };
  </script>

  <style lang="less">
    .sfa-ai-question-types-container{
      width: 100%;
      padding: 6px;
      background-color: #f7f9fa;
      margin-bottom: 12px;
      border-radius: 8px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      gap: 12px;
      .question-types-box {
        display: flex;
        flex-wrap: nowrap;
        gap: 2px 12px;
        justify-content: flex-start;
        flex: 1;
        min-width: 0;
        margin: 0;
        box-sizing: border-box;
        overflow: hidden;
        .question-types-item{
          float: left;
          padding: 4px 10px;
          height: 32px;
          box-sizing: border-box;
          overflow: hidden;
          cursor: pointer;
          flex: 0 0 auto;
          min-width: fit-content;
          white-space: nowrap;
          .types-item{
            display: inline-block;
            float: left;
          }
          .fx-icon-f-obj-app247::before{
            color: var(--color-primary06);
          }
          .fx-icon-f-obj-app43::before{
            color: #FF7383;
          }
        }
        .hide-types-item{
            padding: 4px 4px;
            .hide-text-num{
                max-width: 28px;
                white-space: nowrap; /* 防止文本换行 */
                overflow: hidden; /* 隐藏溢出的内容 */
                text-overflow: ellipsis; /* 使用省略号表示溢出的内容 */
                margin-left: 8px;
            }
        }
        .types-item-icon{
          display: inline-block;
          height: 24px;
          width: 16px;
          margin-right: 4px;
          font-size: 16px;
          display: inline-block;
          position: relative;
          padding-top: 4px;
          .types-item-img{
            position: absolute;
            top: 4px;
            left: 0;
            width: 16px;
            height: 16px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
          }
        }
        .types-item-text{
          color: var(--color-neutrals15);
          font-size: 12px;
          font-weight: 400;
          line-height: 24px;
          margin-right: 8px;
          &.hide-text {
            display: none;
          }
        }
        .types-item-num, .types-item-unit{
          color: var(--Text-H1, #181C25);
          font-size: 15px;
          font-weight: 700;
          line-height: 24px;
        }
        .now-types-item{
            background: #fff;
            border-radius: 8px;
        }
      }
      .right-icon {
        flex: 0 0 auto;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        color: var(--color-neutrals15);
        cursor: pointer;
        margin-left: auto;
      }
      .fx-icon-filter::before{
        color: var(--color-primary06);
      }
    }

    // 添加弹出层样式
    .tags-list {
      padding: 8px;
      background: #FFFFFF;
      border-radius: 8px;
      box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
        .filter-list-title{
            font-size: 13px;
            font-weight: 700;
            line-height: 18px;
            color: var(--color-neutrals19);
        }
      .tags-list-title {
        font-size: 13px;
        font-family: 'Source Han Sans CN';
        color: var(--Text-H1, #181C25);
        line-height: 18px;
        margin-bottom: 10px;
        text-align: left;
        margin-top: 8px;
      }
      .mark-list-title{
        margin-top: 12px;
        margin-bottom: 4px;
        font-size: 13px;
      }
      .tags-list-item-box {
        display: flex;
        flex-wrap: wrap;
        gap: 6px;
        padding: 0 1px;

        .tags-list-item {
          height: 24px;
          padding: 5px 12px;
          background: #F2F4FB;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s ease-in-out;
          position: relative;
          box-sizing: border-box;
          &:hover {
            color: var(--color-primary06);
            // background: var(--color-neutrals04, #F0F2F5);
          }

          &:active {
            background: var(--color-primary01);
          }

          &.selected {
            .tags-list-item-text {
              color: var(--color-primary06);
            }

            &::after {
              content: '';
              position: absolute;
              right: 0;
              bottom: 0;
              width: 0;
              height: 0;
              border-style: solid;
              border-width: 0 0 11px 11px;
              border-color: transparent transparent var(--color-primary06) transparent;
              border-radius: 0 0 4px 0;
            }

            &::before {
              content: '';
              position: absolute;
              right: 2px;
              bottom: 3px;
              width: 4px;
              height: 3px;
              border: 1px solid #fff;
              border-top: 0;
              border-right: 0;
              transform: rotate(-45deg);
              z-index: 1;
            }
          }

          .tags-list-item-text {
            font-family: 'Source Han Sans CN';
            font-size: 12px;
            color: var(--Text-H1, #181C25);
            line-height: 18px;
            font-weight: 400;
            user-select: none;
            text-align: center;
          }
          .tags-list-item-avatar-item{
            display: flex;
            background: #F2F4FB;
            border-radius: 4px;
            padding: 1px 6px;
            height: 26px;
            box-sizing: border-box;
            .tags-list-item-avatar-item-text{
                line-height: 12px;
                font-size: 12px;
                color: var(--Text-H1, #181C25);
                padding: 6px 0px;
                margin-left: 4px;
            }
            &.selected {
                .tags-list-item-text {
                    color: var(--color-primary06);
                }

                &::after {
                    content: '';
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    width: 0;
                    height: 0;
                    border-style: solid;
                    border-width: 0 0 11px 11px;
                    border-color: transparent transparent var(--color-primary06) transparent;
                    border-radius: 0 0 4px 0;
                }

                &::before {
                    content: '';
                    position: absolute;
                    right: 2px;
                    bottom: 3px;
                    width: 4px;
                    height: 3px;
                    border: 1px solid #fff;
                    border-top: 0;
                    border-right: 0;
                    transform: rotate(-45deg);
                    z-index: 1;
                }
            }
          }
        }
        .tags-list-item-avatar{
            padding: 0;
            background: transparent;
        }
        .tags-list-item-avatar-all{
            height: 26px;
        }
      }
    }
    .sfa-activity-tags-popover{
        padding: 0;
    }
  </style>
