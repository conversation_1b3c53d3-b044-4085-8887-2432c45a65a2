import { API_URLS } from '../constants/constants';
import { fetchSuggestListData as fetchSuggestListDataFromSuggest, fetchRelatedObjectList as fetchRelatedObjectListFromSuggest } from '../../aiSuggestIssues/services/apiService';

/**
 * 将驼峰命名的 API 名称转换为下划线分隔的字段名称
 * @param {string} apiName - API 名称，如 'AccountObj', 'ActiveRecordObj', 'NewOpportunityObj'
 * @returns {string} 转换后的字段名称，如 'account_id', 'active_record_id', 'new_opportunity_id'
 *
 * 转换示例：
 * - AccountObj -> account_id
 * - ActiveRecordObj -> active_record_id
 * - NewOpportunityObj -> new_opportunity_id
 * - SomeComplexApiNameObj -> some_complex_api_name_id
 */
const convertApiNameToFieldName = (apiName) => {
  // 去掉末尾的 'Obj'
  const nameWithoutObj = apiName.replace(/Obj$/, '');

  // 将驼峰命名转换为下划线分隔
  const snakeCaseName = nameWithoutObj
    .replace(/([A-Z])/g, '_$1')  // 在大写字母前加下划线
    .toLowerCase()               // 转为小写
    .replace(/^_/, '');          // 去掉开头的下划线

  // 末尾加上 'id'
  return `${snakeCaseName}_id`;
};

/**
 * 根据 API 名称和数据 ID 生成过滤器参数
 * @param {string} apiName - API 名称
 * @param {string} dataId - 数据 ID
 * @returns {Array} 过滤器数组
 */
const generateFiltersByApiName = (apiName, dataId) => {
  const fieldName = convertApiNameToFieldName(apiName);

  return [
    {"field_name": fieldName, "field_values": [dataId], "operator": "EQ"},
    {"field_name": "question_type", "field_values": ['1'], "operator": "EQ"},
  ];
};

export const getRelatedListData = (params) => {
  // 记录请求前的时间
  const requestTime = params.time || new Date().toISOString();

  const {
    apiName,
    dataId,
    limit,
    offset,
    questionType,
    tagsType,
    markType,
    detailData,
    selectedUserAvatar,
    fetchInteractiveType = 'default'
  } = params;

  let search_query_info = {
    limit,
    offset,
    "filters": [],
    "orders": [{"field_name":"create_time","isAsc":"true"}]
  };

  // 使用公共方法动态生成过滤器参数
  let _filters = generateFiltersByApiName(apiName, dataId);

  if(questionType == 'satisfied' || questionType == 'general' || questionType == 'dissatisfied') {
    _filters.push({"field_name": 'proposer_attitude', "field_values":[questionType],"operator":"EQ"});
  } else if(questionType == 'not_responded') {
    _filters.push({"field_name": 'advice_status', "field_values":[questionType],"operator":"EQ"});
  }

  if (!tagsType.includes('all')) {
    _filters.push({"field_name": 'tags', "field_values": tagsType, "operator": "HASANYOF"});
  }
  if(!markType.includes('all')) {
    _filters.push({"field_name": 'mark', "field_values": markType, "operator": "HASANYOF"});
  }

  if(!selectedUserAvatar.includes('all') && selectedUserAvatar.length) {
    _filters.push({"field_name": 'question_user', "field_values": selectedUserAvatar, "operator": "HASANYOF"});
    // _filters.push({"field_name": 'answer_user', "field_values": selectedUserAvatar, "operator": "HASANYOF"});  //暂不支持
  }

  search_query_info.filters = _filters;
  let data = {
    "serializeEmpty": false,
    "extractExtendInfo": true,
    "object_describe_api_name": "ActivityQuestionObj",
    "search_template_id": "",
    "include_describe": true,
    "include_layout": false,
    "need_tag": true,
    "search_template_type": "default",
    "ignore_scene_record_type": false,
    "list_type": "related",
    "extraData":{
      "skip_authority": true,
      "fetchInteractiveType": fetchInteractiveType
    },
    "search_query_info": JSON.stringify(search_query_info),
  };

  if(apiName == 'ActiveRecordObj') {
    const fieldName = convertApiNameToFieldName(apiName);
    data.extraData[fieldName] = detailData._id;
  }

  return new Promise((resolve, reject) => {
    CRM.util.FHHApi({
      url: API_URLS.GET_RELATED_LIST,
      data,
      success: function (res) {
        if (res.Result.StatusCode === 0 && res.Value.dataList) {
          // 将原始请求参数 data 和结果一起返回
          const result = {
            ...res.Value,
            requestData: data,  // 添加原始请求参数
            requestTime,        // 使用请求前记录的时间
            fetchInteractiveType  // 添加请求类型
          };
          resolve(result);
          return;
        }
        resolve({ requestData: data, fetchInteractiveType, requestTime });
      },
      error: reject
    }, {
      errorAlertModel: 1
    });
  });
};

export const fetchQuestionTypesData = async (apiName, dataId) => {
  const data = {
    "objectApiName": apiName,
    "objectDataId": dataId,
  };

  return new Promise((resolve, reject) => {
    CRM.util.FHHApi({
      url: API_URLS.GET_INTERACT_COUNT,
      data,
      success: function (res) {
        if (res.Result.StatusCode === 0) {
          resolve(res.Value);
        } else {
          reject(new Error(res.Result.FailureMessage));
        }
      },
      error: function(err) {
        reject(err);
      }
    }, {
      errorAlertModel: 1
    });
  });
};

export const fetchSuggestListData = async (apiName, dataId, detailData, relatedObjectName = null) => {
  return fetchSuggestListDataFromSuggest(apiName, detailData, {
    limit: 200,
    offset: 0,
    questionType: "questionType",
    tagsType: ['all'],
    relatedObjectName: relatedObjectName || apiName
  });
};

export const fetchRelatedObjectList = async (apiName, detailData) => {
  return fetchRelatedObjectListFromSuggest(apiName, detailData);
};

export const refreshAll = async (apiName, dataId) => {
  let data = {
    objectApiName: apiName,
    objectDataId: dataId
  };

  return new Promise((resolve, reject) => {
    CRM.util.FHHApi({
      url: API_URLS.GET_AI_SUGGESTION,
      data,
      success: function (res) {
        if(res.Result.StatusCode == 0) {
          resolve(res.Value);
        } else {
          reject(new Error(res.Result.FailureMessage));
        }
      },
      error: function(err) {
        reject(err);
      }
    }, {
      errorAlertModel: 1
    });
  });
};

export const setMarkType = async (itemData, type) => {
  return new Promise((resolve, reject) => {
    CRM.util.FHHApi({
      url: API_URLS.SET_MARK,
      data: {
        aiQuestionId: itemData._id,
        mark: type
      },
      success: function (res) {
        if (res.Result.StatusCode === 0) {
          resolve(res.Value);
        } else {
          reject(new Error(res.Result.FailureMessage));
        }
      },
      error: function(err) {
        reject(err);
      }
    }, {
      errorAlertModel: 1
    });
  });
};

export const setAiAutoSwitch = async (dataId, state) => {
  return new Promise((resolve, reject) => {
    CRM.util.FHHApi({
      url: API_URLS.SET_AI_AUTO_SWITCH,
      data: { dataId, state },
      success: function (res) {
        if (res.Result.StatusCode === 0) {
          resolve(res.Value);
        } else {
          reject(new Error(res.Result.FailureMessage));
        }
      },
      error: function(err) {
        reject(err);
      }
    }, {
      errorAlertModel: 1
    });
  });
};
