class Communicator {
  constructor() {
    // 仅用作事件总线
    this.bus = new Vue()
  }

  /**
   * 发送事件
   * @param {string} eventName 事件名，建议格式：`${componentId}:eventName`
   * @param {any} payload 事件数据
   */
  emit(eventName, payload) {
    this.bus.$emit(eventName, payload)
  }

  /**
   * 监听事件
   * @param {string} eventName 事件名
   * @param {Function} handler 处理函数
   */
  on(eventName, handler) {
    this.bus.$on(eventName, handler)
  }

  /**
   * 取消监听
   * @param {string} eventName 事件名
   * @param {Function} handler 处理函数
   */
  off(eventName, handler) {
    this.bus.$off(eventName, handler)
  }

  /**
   * 一次性监听
   * @param {string} eventName 事件名
   * @param {Function} handler 处理函数
   */
  once(eventName, handler) {
    this.bus.$once(eventName, handler)
  }
}

export default new Communicator()