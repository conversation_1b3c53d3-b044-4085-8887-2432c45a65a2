export const mockData = {
        "profile": {
             "_id": "profile_001",
             "methodology_instance_id": "method_001",
             "methodology_id": "method_type_001",
             "methodology_id__r": "通用画像",
             "lead_id": "lead_001",
             "account_id": "account_001",
             "opportunity_id": "opp_001",
             "type": "商机",
             "integrated_score": "86",
             "phase_cumulative_score": "82",
             "calc_time": "2024-03-20 10:00:00",
             "is_latest": true,
             "summary": "整体画像评分85分，属于较高水平。基本面表现突出，客户互动频繁，但在销售能力和竞争态势方面仍有提升空间。",
             "last_modified_time": *************
        },
        "integratedScoreTrend": [
            {
                "id": "profile_001",
                "integrated_score": 70,
                "is_latest": false,
                "calc_time": new Date("2024-03-10T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-10T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 60,
                "is_latest": false,
                "calc_time": new Date("2024-03-20T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-20T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 85,
                "is_latest": true,
                "calc_time": new Date("2024-03-27T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-27T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 75,
                "is_latest": false,
                "calc_time": new Date("2024-03-05T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-05T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 80,
                "is_latest": false,
                "calc_time": new Date("2024-03-15T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-15T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 90,
                "is_latest": true,
                "calc_time": new Date("2024-03-25T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-25T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 65,
                "is_latest": false,
                "calc_time": new Date("2024-03-12T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-12T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 78,
                "is_latest": false,
                "calc_time": new Date("2024-03-18T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-18T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 88,
                "is_latest": true,
                "calc_time": new Date("2024-03-30T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-30T10:00:00").getTime()
            },
            {
                "id": "profile_001",
                "integrated_score": 72,
                "is_latest": false,
                "calc_time": new Date("2024-03-22T10:00:00").getTime(),
                "last_modified_time": new Date("2024-03-22T10:00:00").getTime()
            }
        ],
        "methodologyInstance": [
                {
                    "id": "method_001",
                    "methodology_id": "method_type_001",
                    "methodology_id__r": "通用画像",
                    "status": "1",
                    "type": "流程"
                },
                {
                    "id": "method_002",
                    "methodology_id": "method_type_002",
                    "methodology_id__r": "C139画像",
                    "status": "1",
                    "type": "流程"
                },
                {
                    "id": "method_003",
                    "methodology_id": "method_type_003",
                    "methodology_id__r": "SPIN画像",
                    "status": "1",
                    "type": "流程"
                }
        ],
        "nodeInstance": [
                {
                    "node_id" : "node_001",
                    "node_id__r" : "验证客户",
                    "status" : "2",
                    "node_order": "1",
                    "methodology_id" : "method_type_001",
                    "methodology_instance_id" : "method_001",
                    "object_api_name" : "opportunity",
                    "object_id" : "opp_001"
                },
                {
                    "node_id" : "node_002",
                    "node_id__r" : "需求确定",
                    "status" : "2",
                    "node_order": "2",
                    "methodology_id" : "method_type_001",
                    "methodology_instance_id" : "method_001",
                    "object_api_name" : "opportunity",
                    "object_id" : "opp_001"
                },
                {
                    "node_id" : "node_003",
                    "node_id__r" : "方案/报价",
                    "status" : "1",
                    "node_order": "3",
                    "methodology_id" : "method_type_001",
                    "methodology_instance_id" : "method_001",
                    "object_api_name" : "opportunity",
                    "object_id" : "opp_001"
                },
                {
                    "node_id" : "node_004",
                    "node_id__r" : "谈判审核",
                    "status" : "0",
                    "node_order": "4",
                    "methodology_id" : "method_type_001",
                    "methodology_instance_id" : "method_001",
                    "object_api_name" : "opportunity",
                    "object_id" : "opp_001"
                }
        ],
        "profileItemScore": [
                // {
                //     "profile_id": "profile_001",
                //     "type": "dimension",
                //     "feature_dimension_id": "",
                //     "feature_dimension_id__r": "维度综述",
                //     "score": 0
                // },
                {
                    "profile_id": "profile_001",
                    "type": "dimension",
                    "feature_dimension_id": "dim_basic",
                    "feature_dimension_id__r": "基本面",
                    "summary": "客户基本面评分92分，企业规模大，行业地位突出，财务状况稳健。",
                    "score": 92
                },
                {
                    "profile_id": "profile_001",
                    "type": "dimension",
                    "feature_dimension_id": "dim_demand",
                    "feature_dimension_id__r": "需求匹配",
                    "summary": "需求匹配度52分，已明确核心需求，但部分需求点仍需深入挖掘。",
                    "score": 52
                },
                {
                    "profile_id": "profile_001",
                    "type": "dimension",
                    "feature_dimension_id": "dim_relation",
                    "feature_dimension_id__r": "客户关系",
                    "summary": "客户关系评分65分，已建立稳定合作关系，但关键决策人互动有待加强。",
                    "score": 65
                },
                {
                    "profile_id": "profile_001",
                    "type": "dimension",
                    "feature_dimension_id": "dim_interaction",
                    "feature_dimension_id__r": "客户互动",
                    "summary": "客户互动评分72分，近期互动频繁，反馈积极，展现出较强合作意愿。",
                    "score": 72
                },
                {
                    "profile_id": "profile_001",
                    "type": "dimension",
                    "feature_dimension_id": "dim_sales",
                    "feature_dimension_id__r": "销售能力",
                    "summary": "销售能力评分32分，销售策略和执行效果需要明显改进。",
                    "score": 32
                },
                {
                    "profile_id": "profile_001",
                    "type": "dimension",
                    "feature_dimension_id": "dim_competition",
                    "feature_dimension_id__r": "竞争态势",
                    "summary": "竞争态势评分42分，面临较大竞争压力，差异化优势不明显。",
                    "score": 42
                },
                {
                    "profile_id": "profile_001",
                    "type": "dimension",
                    "feature_dimension_id": "dim_business",
                    "feature_dimension_id__r": "商务",
                    "summary": "商务评分42分，价格竞争力一般，商务条款需要优化。",
                    "score": 42
                },
                // {
                //     "profile_id": "profile_001",
                //     "type": "dimension",
                //     "feature_dimension_id": "dim_other",
                //     "feature_dimension_id__r": "其他",
                //     "score": 87
                // },
                // {
                //     "profile_id": "profile_001",
                //     "type": "dimension",
                //     "feature_dimension_id": "dim_other1",
                //     "feature_dimension_id__r": "其他1",
                //     "score": 87
                // },
                // {
                //     "profile_id": "profile_001",
                //     "type": "dimension",
                //     "feature_dimension_id": "dim_other2",
                //     "feature_dimension_id__r": "其他2",
                //     "score": 87
                // },
                {
                    "profile_id": "profile_001",
                    "type": "node",
                    "node_id": "node_001",
                    "node_id__r": "验证客户",
                    "score": 85
                },
                {
                    "profile_id": "profile_001",
                    "type": "node",
                    "node_id": "node_002",
                    "node_id__r": "需求确定",
                    "score": 65
                },
                {
                    "profile_id": "profile_001",
                    "type": "node",
                    "node_id": "node_003",
                    "node_id__r": "方案/报价",
                    "score": 72
                },
                // {
                //     "profile_id": "profile_001",
                //     "type": "node",
                //     "node_id": "node_004",
                //     "node_id__r": "谈判审核",
                //     "score": 66
                // }
        ],
        "trendsChange": [
            // 基本面历史数据 (最终92分)
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_basic",
                "feature_dimension_id__r": "基本面",
                "score": 65,
                "last_modified_time": new Date('2024-03-21').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_basic",
                "feature_dimension_id__r": "基本面",
                "score": 88,
                "last_modified_time": new Date('2024-03-22').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_basic",
                "feature_dimension_id__r": "基本面",
                "score": 76,
                "last_modified_time": new Date('2024-03-23').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_basic",
                "feature_dimension_id__r": "基本面",
                "score": 95,
                "last_modified_time": new Date('2024-03-24').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_basic",
                "feature_dimension_id__r": "基本面",
                "score": 82,
                "last_modified_time": new Date('2024-03-25').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_basic",
                "feature_dimension_id__r": "基本面",
                "score": 98,
                "last_modified_time": new Date('2024-03-26').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_basic",
                "feature_dimension_id__r": "基本面",
                "score": 92,
                "last_modified_time": new Date('2024-03-27').getTime(),
            },
            // 需求匹配历史数据 (最终52分)
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_demand",
                "feature_dimension_id__r": "需求匹配",
                "score": 25,
                "last_modified_time": new Date('2024-03-21').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_demand",
                "feature_dimension_id__r": "需求匹配",
                "score": 68,
                "last_modified_time": new Date('2024-03-22').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_demand",
                "feature_dimension_id__r": "需求匹配",
                "score": 36,
                "last_modified_time": new Date('2024-03-23').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_demand",
                "feature_dimension_id__r": "需求匹配",
                "score": 75,
                "last_modified_time": new Date('2024-03-24').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_demand",
                "feature_dimension_id__r": "需求匹配",
                "score": 45,
                "last_modified_time": new Date('2024-03-25').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_demand",
                "feature_dimension_id__r": "需求匹配",
                "score": 65,
                "last_modified_time": new Date('2024-03-26').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_demand",
                "feature_dimension_id__r": "需求匹配",
                "score": 52,
                "last_modified_time": new Date('2024-03-27').getTime(),
            },
            // 客户关系历史数据 (最终65分)
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_relation",
                "feature_dimension_id__r": "客户关系",
                "score": 35,
                "last_modified_time": new Date('2024-03-21').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_relation",
                "feature_dimension_id__r": "客户关系",
                "score": 82,
                "last_modified_time": new Date('2024-03-22').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_relation",
                "feature_dimension_id__r": "客户关系",
                "score": 48,
                "last_modified_time": new Date('2024-03-23').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_relation",
                "feature_dimension_id__r": "客户关系",
                "score": 88,
                "last_modified_time": new Date('2024-03-24').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_relation",
                "feature_dimension_id__r": "客户关系",
                "score": 55,
                "last_modified_time": new Date('2024-03-25').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_relation",
                "feature_dimension_id__r": "客户关系",
                "score": 78,
                "last_modified_time": new Date('2024-03-26').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_relation",
                "feature_dimension_id__r": "客户关系",
                "score": 65,
                "last_modified_time": new Date('2024-03-27').getTime(),
            },
            // 客户互动历史数据 (最终72分)
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_interaction",
                "feature_dimension_id__r": "客户互动",
                "score": 45,
                "last_modified_time": new Date('2024-03-21').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_interaction",
                "feature_dimension_id__r": "客户互动",
                "score": 85,
                "last_modified_time": new Date('2024-03-22').getTime(),
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_interaction",
                "feature_dimension_id__r": "客户互动",
                "score": 58,
                "last_modified_time": new Date('2024-03-23').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_interaction",
                "feature_dimension_id__r": "客户互动",
                "score": 92,
                "last_modified_time": new Date('2024-03-24').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_interaction",
                "feature_dimension_id__r": "客户互动",
                "score": 65,
                "last_modified_time": new Date('2024-03-25').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_interaction",
                "feature_dimension_id__r": "客户互动",
                "score": 88,
                "last_modified_time": new Date('2024-03-26').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_interaction",
                "feature_dimension_id__r": "客户互动",
                "score": 72,
                "last_modified_time": new Date('2024-03-27').getTime()
            },
            // 销售能力历史数据 (最终32分)
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_sales",
                "feature_dimension_id__r": "销售能力",
                "score": 15,
                "last_modified_time": new Date('2024-03-21').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_sales",
                "feature_dimension_id__r": "销售能力",
                "score": 55,
                "last_modified_time": new Date('2024-03-22').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_sales",
                "feature_dimension_id__r": "销售能力",
                "score": 28,
                "last_modified_time": new Date('2024-03-23').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_sales",
                "feature_dimension_id__r": "销售能力",
                "score": 62,
                "last_modified_time": new Date('2024-03-24').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_sales",
                "feature_dimension_id__r": "销售能力",
                "score": 25,
                "last_modified_time": new Date('2024-03-25').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_sales",
                "feature_dimension_id__r": "销售能力",
                "score": 45,
                "last_modified_time": new Date('2024-03-26').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_sales",
                "feature_dimension_id__r": "销售能力",
                "score": 32,
                "last_modified_time": new Date('2024-03-27').getTime()
            },
            // 竞争态势历史数据 (最终42分)
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_competition",
                "feature_dimension_id__r": "竞争态势",
                "score": 25,
                "last_modified_time": new Date('2024-03-21').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_competition",
                "feature_dimension_id__r": "竞争态势",
                "score": 68,
                "last_modified_time": new Date('2024-03-22').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_competition",
                "feature_dimension_id__r": "竞争态势",
                "score": 35,
                "last_modified_time": new Date('2024-03-23').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_competition",
                "feature_dimension_id__r": "竞争态势",
                "score": 72,
                "last_modified_time": new Date('2024-03-24').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_competition",
                "feature_dimension_id__r": "竞争态势",
                "score": 38,
                "last_modified_time": new Date('2024-03-25').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_competition",
                "feature_dimension_id__r": "竞争态势",
                "score": 58,
                "last_modified_time": new Date('2024-03-26').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_competition",
                "feature_dimension_id__r": "竞争态势",
                "score": 42,
                "last_modified_time": new Date('2024-03-27').getTime()
            },
            // 商务历史数据 (最终42分)
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_business",
                "feature_dimension_id__r": "商务",
                "score": 22,
                "last_modified_time": new Date('2024-03-21').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_business",
                "feature_dimension_id__r": "商务",
                "score": 65,
                "last_modified_time": new Date('2024-03-22').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_business",
                "feature_dimension_id__r": "商务",
                "score": 32,
                "last_modified_time": new Date('2024-03-23').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_business",
                "feature_dimension_id__r": "商务",
                "score": 75,
                "last_modified_time": new Date('2024-03-24').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_business",
                "feature_dimension_id__r": "商务",
                "score": 35,
                "last_modified_time": new Date('2024-03-25').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_business",
                "feature_dimension_id__r": "商务",
                "score": 55,
                "last_modified_time": new Date('2024-03-26').getTime()
            },
            {
                "profile_id": "profile_001",
                "type": "dimension",
                "feature_dimension_id": "dim_business",
                "feature_dimension_id__r": "商务",
                "score": 42,
                "last_modified_time": new Date('2024-03-27').getTime()
            }
        ],
        "profileProsCons": [
                {
                    "profile_id": "profile_001",
                    "range_type": "",
                    "type": "pros",
                    "feature_dimension_id": "",
                    "information": "整体表现稳定，各维度得分均衡",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 1
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "",
                    "type": "cons",
                    "feature_dimension_id": "",
                    "information": "部分关键维度得分偏低，需要重点改进",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 2
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_basic",
                    "type": "pros",
                    "feature_dimension_id": "dim_basic",
                    "information": "企业规模位列行业前十，具有较强的市场影响力",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 3
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_basic",
                    "type": "pros",
                    "feature_dimension_id": "dim_basic",
                    "information": "近三年营收持续增长，年均增速超过20%",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 4
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_demand",
                    "type": "pros",
                    "feature_dimension_id": "dim_demand",
                    "information": "核心需求明确，项目预算充足",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 5
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_demand",
                    "type": "cons",
                    "feature_dimension_id": "dim_demand",
                    "information": "部分关键需求点未得到充分验证",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 6
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_relation",
                    "type": "pros",
                    "feature_dimension_id": "dim_relation",
                    "information": "已与技术部门建立良好沟通渠道",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 7
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_relation",
                    "type": "cons",
                    "feature_dimension_id": "dim_relation",
                    "information": "高层决策者接触不足",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 8
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_interaction",
                    "type": "pros",
                    "feature_dimension_id": "dim_interaction",
                    "information": "最近一个月平均每周有2次以上的深度交流",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 9
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_interaction",
                    "type": "cons",
                    "feature_dimension_id": "dim_interaction",
                    "information": "客户反馈处理时效有待提升",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 10
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_sales",
                    "type": "pros",
                    "feature_dimension_id": "dim_sales",
                    "information": "销售团队保持高频互动",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 11
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_sales",
                    "type": "cons",
                    "feature_dimension_id": "dim_sales",
                    "information": "销售团队对客户业务理解不够深入",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 12
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_competition",
                    "type": "pros",
                    "feature_dimension_id": "dim_competition",
                    "information": "技术方案具有明显优势",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 13
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_competition",
                    "type": "cons",
                    "feature_dimension_id": "dim_competition",
                    "information": "主要竞品在价格方面具有明显优势",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 14
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_business",
                    "type": "pros",
                    "feature_dimension_id": "dim_business",
                    "information": "商务谈判进展顺利",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 15
                },
                {
                    "profile_id": "profile_001",
                    "range_type": "dim_business",
                    "type": "cons",
                    "feature_dimension_id": "dim_business",
                    "information": "付款条件要求较为严格，影响商务谈判进展",
                    "is_accept": false,
                    "is_ignore": false,
                    "seq": 16
                }
        ],
        "profileAdvice": [
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "",
                    "task_id": "task_overview_001",
                    "task_id__r": "整体分析",
                    "advice": "建议对各维度短板制定专项提升计划，重点关注销售能力和竞争态势",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                },
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "dim_basic",
                    "task_id": "task_basic_001",
                    "task_id__r": "深入分析",
                    "advice": "建议进一步收集客户未来3年的业务发展规划，为长期合作做准备",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                },
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "dim_demand",
                    "task_id": "task_demand_001",
                    "task_id__r": "需求验证",
                    "advice": "建议安排技术专家与客户进行深度需求探讨，特别是在系统集成方面",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                },
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "dim_relation",
                    "task_id": "task_relation_001",
                    "task_id__r": "关系提升",
                    "advice": "建议本周内约见客户决策层，提升项目层级",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                },
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "dim_interaction",
                    "task_id": "task_interaction_001",
                    "task_id__r": "互动优化",
                    "advice": "建议制定常态化拜访计划，保持高频互动",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                },
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "dim_sales",
                    "task_id": "task_sales_001",
                    "task_id__r": "能力提升",
                    "advice": "建议销售团队参与行业培训，提升专业能力",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                },
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "dim_competition",
                    "task_id": "task_competition_001",
                    "task_id__r": "竞争分析",
                    "advice": "建议完成竞品详细对比分析，制定差异化策略",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                },
                {
                    "profile_id": "profile_001",
                    "feature_dimension_id": "dim_business",
                    "task_id": "task_business_001",
                    "task_id__r": "商务优化",
                    "advice": "建议设计灵活的付款方案，提高商务谈判的灵活性",
                    "is_accept": "0",
                    "is_ignore": "0",
                    "type": "系统建议"
                }
        ],
        "profileHistory": [ //画像历史
            {
                "id": "profile_001",
                "methodology_instance_id": "method_001",
                "methodology_id": "method_type_001",
                "methodology_id__r": "通用画像",
                "lead_id": "lead_001",
                "account_id": "account_001",
                "opportunity_id": "opp_001",
                "type": "商机",
                "integrated_score": "86",
                "phase_cumulative_score": "82",
                "calc_time": "2024-03-27 10:00:00",
                "is_latest": true,
                "last_modified_time": new Date('2024-03-27 10:00:00').getTime(),
                "profileItemScore": [
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_basic",
                        "feature_dimension_id__r": "基本面",
                        "score": 92
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_demand",
                        "feature_dimension_id__r": "需求匹配",
                        "score": 52
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_relation",
                        "feature_dimension_id__r": "客户关系",
                        "score": 65
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_interaction",
                        "feature_dimension_id__r": "客户互动",
                        "score": 72
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_sales",
                        "feature_dimension_id__r": "销售能力",
                        "score": 32
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_competition",
                        "feature_dimension_id__r": "竞争态势",
                        "score": 42
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_business",
                        "feature_dimension_id__r": "商务",
                        "score": 42
                    }
                ],
                "profileProsCons": [
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_basic",
                        "type": "pros",
                        "feature_dimension_id": "dim_basic",
                        "information": "企业规模位列行业前十，具有较强的市场影响力",
                        "seq": 1,
                        "is_accept": true,
                        "is_ignore": false
                    },
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_demand",
                        "type": "cons",
                        "feature_dimension_id": "dim_demand",
                        "information": "部分关键需求点未得到充分验证",
                        "seq": 2,
                        "is_accept": true,
                        "is_ignore": false
                    }
                ],
                "profileAdvice": [
                    {
                        "profile_id": "profile_001",
                        "feature_dimension_id": "dim_basic",
                        "task_id": "task_basic_001",
                        "task_id__r": "深入分析",
                        "advice": "建议进一步收集客户未来3年的业务发展规划",
                        "is_accept": "1",
                        "is_ignore": "0",
                        "type": "系统建议",
                        "range_type": "dimension",
                        "seq": 1
                    }
                ]
            },
            {
                "id": "profile_001",
                "methodology_instance_id": "method_001",
                "methodology_id": "method_type_001",
                "methodology_id__r": "通用画像",
                "lead_id": "lead_001",
                "account_id": "account_001",
                "opportunity_id": "opp_001",
                "type": "商机",
                "integrated_score": "82",
                "phase_cumulative_score": "78",
                "calc_time": "2024-03-26 10:00:00",
                "is_latest": false,
                "last_modified_time": new Date('2024-03-26 10:00:00').getTime(),
                "profileItemScore": [
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_basic",
                        "feature_dimension_id__r": "基本面",
                        "score": 98
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_demand",
                        "feature_dimension_id__r": "需求匹配",
                        "score": 65
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_relation",
                        "feature_dimension_id__r": "客户关系",
                        "score": 78
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_interaction",
                        "feature_dimension_id__r": "客户互动",
                        "score": 88
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_sales",
                        "feature_dimension_id__r": "销售能力",
                        "score": 45
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_competition",
                        "feature_dimension_id__r": "竞争态势",
                        "score": 58
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_business",
                        "feature_dimension_id__r": "商务",
                        "score": 55
                    }
                ],
                "profileProsCons": [
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_basic",
                        "type": "pros",
                        "feature_dimension_id": "dim_basic",
                        "information": "企业规模位列行业前十，具有较强的市场影响力",
                        "seq": 1,
                        "is_accept": true,
                        "is_ignore": false
                    }
                ],
                "profileAdvice": [
                    {
                        "profile_id": "profile_001",
                        "feature_dimension_id": "dim_basic",
                        "task_id": "task_basic_001",
                        "task_id__r": "深入分析",
                        "advice": "建议进一步收集客户未来3年的业务发展规划",
                        "is_accept": "0",
                        "is_ignore": "0",
                        "type": "系统建议",
                        "range_type": "dimension",
                        "seq": 1
                    }
                ]
            },
            {
                "id": "profile_001",
                "methodology_instance_id": "method_001",
                "methodology_id": "method_type_001",
                "methodology_id__r": "通用画像",
                "lead_id": "lead_001",
                "account_id": "account_001",
                "opportunity_id": "opp_001",
                "type": "商机",
                "integrated_score": "75",
                "phase_cumulative_score": "72",
                "calc_time": "2024-03-25 10:00:00",
                "is_latest": false,
                "last_modified_time": new Date('2024-03-25 10:00:00').getTime(),
                "profileItemScore": [
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_basic",
                        "feature_dimension_id__r": "基本面",
                        "score": 82
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_demand",
                        "feature_dimension_id__r": "需求匹配",
                        "score": 45
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_relation",
                        "feature_dimension_id__r": "客户关系",
                        "score": 55
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_interaction",
                        "feature_dimension_id__r": "客户互动",
                        "score": 65
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_sales",
                        "feature_dimension_id__r": "销售能力",
                        "score": 25
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_competition",
                        "feature_dimension_id__r": "竞争态势",
                        "score": 38
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_business",
                        "feature_dimension_id__r": "商务",
                        "score": 35
                    }
                ],
                "profileProsCons": [
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_basic",
                        "type": "pros",
                        "feature_dimension_id": "dim_basic",
                        "information": "企业规模位列行业前十，具有较强的市场影响力",
                        "seq": 1,
                        "is_accept": true,
                        "is_ignore": false
                    }
                ],
                "profileAdvice": [
                    {
                        "profile_id": "profile_001",
                        "feature_dimension_id": "dim_basic",
                        "task_id": "task_basic_001",
                        "task_id__r": "深入分析",
                        "advice": "建议进一步收集客户未来3年的业务发展规划",
                        "is_accept": "0",
                        "is_ignore": "0",
                        "type": "系统建议",
                        "range_type": "dimension",
                        "seq": 1
                    }
                ]
            },
            {
                "id": "profile_001",
                "methodology_instance_id": "method_001",
                "methodology_id": "method_type_001",
                "methodology_id__r": "通用画像",
                "lead_id": "lead_001",
                "account_id": "account_001",
                "opportunity_id": "opp_001",
                "type": "商机",
                "integrated_score": "88",
                "phase_cumulative_score": "85",
                "calc_time": "2024-03-24 10:00:00",
                "is_latest": false,
                "last_modified_time": new Date('2024-03-24 10:00:00').getTime(),
                "profileItemScore": [
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_basic",
                        "feature_dimension_id__r": "基本面",
                        "score": 95
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_demand",
                        "feature_dimension_id__r": "需求匹配",
                        "score": 75
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_relation",
                        "feature_dimension_id__r": "客户关系",
                        "score": 88
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_interaction",
                        "feature_dimension_id__r": "客户互动",
                        "score": 92
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_sales",
                        "feature_dimension_id__r": "销售能力",
                        "score": 62
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_competition",
                        "feature_dimension_id__r": "竞争态势",
                        "score": 72
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_business",
                        "feature_dimension_id__r": "商务",
                        "score": 75
                    }
                ],
                "profileProsCons": [
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_basic",
                        "type": "pros",
                        "feature_dimension_id": "dim_basic",
                        "information": "企业规模位列行业前十，具有较强的市场影响力",
                        "seq": 1,
                        "is_accept": true,
                        "is_ignore": false
                    }
                ],
                "profileAdvice": [
                    {
                        "profile_id": "profile_001",
                        "feature_dimension_id": "dim_basic",
                        "task_id": "task_basic_001",
                        "task_id__r": "深入分析",
                        "advice": "建议进一步收集客户未来3年的业务发展规划",
                        "is_accept": "0",
                        "is_ignore": "0",
                        "type": "系统建议",
                        "range_type": "dimension",
                        "seq": 1
                    }
                ]
            },
            {
                "id": "profile_001",
                "methodology_instance_id": "method_001",
                "methodology_id": "method_type_001",
                "methodology_id__r": "通用画像",
                "lead_id": "lead_001",
                "account_id": "account_001",
                "opportunity_id": "opp_001",
                "type": "商机",
                "integrated_score": "68",
                "phase_cumulative_score": "65",
                "calc_time": "2024-03-23 10:00:00",
                "is_latest": false,
                "last_modified_time": new Date('2024-03-23 10:00:00').getTime(),
                "profileItemScore": [
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_basic",
                        "feature_dimension_id__r": "基本面",
                        "score": 76
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_demand",
                        "feature_dimension_id__r": "需求匹配",
                        "score": 36
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_relation",
                        "feature_dimension_id__r": "客户关系",
                        "score": 48
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_interaction",
                        "feature_dimension_id__r": "客户互动",
                        "score": 58
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_sales",
                        "feature_dimension_id__r": "销售能力",
                        "score": 28
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_competition",
                        "feature_dimension_id__r": "竞争态势",
                        "score": 35
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_business",
                        "feature_dimension_id__r": "商务",
                        "score": 32
                    }
                ],
                "profileProsCons": [
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_basic",
                        "type": "pros",
                        "feature_dimension_id": "dim_basic",
                        "information": "企业规模位列行业前十，具有较强的市场影响力",
                        "seq": 1,
                        "is_accept": true,
                        "is_ignore": false
                    }
                ],
                "profileAdvice": [
                    {
                        "profile_id": "profile_001",
                        "feature_dimension_id": "dim_basic",
                        "task_id": "task_basic_001",
                        "task_id__r": "深入分析",
                        "advice": "建议进一步收集客户未来3年的业务发展规划",
                        "is_accept": "0",
                        "is_ignore": "0",
                        "type": "系统建议",
                        "range_type": "dimension",
                        "seq": 1
                    }
                ]
            },
            {
                "id": "profile_001",
                "methodology_instance_id": "method_001",
                "methodology_id": "method_type_001",
                "methodology_id__r": "通用画像",
                "lead_id": "lead_001",
                "account_id": "account_001",
                "opportunity_id": "opp_001",
                "type": "商机",
                "integrated_score": "78",
                "phase_cumulative_score": "75",
                "calc_time": "2024-03-22 10:00:00",
                "is_latest": false,
                "last_modified_time": new Date('2024-03-22 10:00:00').getTime(),
                "profileItemScore": [
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_basic",
                        "feature_dimension_id__r": "基本面",
                        "score": 88
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_demand",
                        "feature_dimension_id__r": "需求匹配",
                        "score": 68
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_relation",
                        "feature_dimension_id__r": "客户关系",
                        "score": 82
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_interaction",
                        "feature_dimension_id__r": "客户互动",
                        "score": 85
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_sales",
                        "feature_dimension_id__r": "销售能力",
                        "score": 55
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_competition",
                        "feature_dimension_id__r": "竞争态势",
                        "score": 68
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_business",
                        "feature_dimension_id__r": "商务",
                        "score": 65
                    }
                ],
                "profileProsCons": [
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_basic",
                        "type": "pros",
                        "feature_dimension_id": "dim_basic",
                        "information": "企业规模位列行业前十，具有较强的市场影响力",
                        "seq": 1,
                        "is_accept": true,
                        "is_ignore": false
                    }
                ],
                "profileAdvice": [
                    {
                        "profile_id": "profile_001",
                        "feature_dimension_id": "dim_basic",
                        "task_id": "task_basic_001",
                        "task_id__r": "深入分析",
                        "advice": "建议进一步收集客户未来3年的业务发展规划",
                        "is_accept": "0",
                        "is_ignore": "0",
                        "type": "系统建议",
                        "range_type": "dimension",
                        "seq": 1
                    }
                ]
            },
            {
                "id": "profile_001",
                "methodology_instance_id": "method_001",
                "methodology_id": "method_type_001",
                "methodology_id__r": "通用画像",
                "lead_id": "lead_001",
                "account_id": "account_001",
                "opportunity_id": "opp_001",
                "type": "商机",
                "integrated_score": "62",
                "phase_cumulative_score": "58",
                "calc_time": "2024-03-21 10:00:00",
                "is_latest": false,
                "last_modified_time": new Date('2024-03-21 10:00:00').getTime(),
                "profileItemScore": [
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_basic",
                        "feature_dimension_id__r": "基本面",
                        "score": 65
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_demand",
                        "feature_dimension_id__r": "需求匹配",
                        "score": 25
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_relation",
                        "feature_dimension_id__r": "客户关系",
                        "score": 35
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_interaction",
                        "feature_dimension_id__r": "客户互动",
                        "score": 45
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_sales",
                        "feature_dimension_id__r": "销售能力",
                        "score": 15
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_competition",
                        "feature_dimension_id__r": "竞争态势",
                        "score": 25
                    },
                    {
                        "profile_id": "profile_001",
                        "type": "dimension",
                        "feature_dimension_id": "dim_business",
                        "feature_dimension_id__r": "商务",
                        "score": 22
                    }
                ],
                "profileProsCons": [
                    {
                        "profile_id": "profile_001",
                        "range_type": "dim_basic",
                        "type": "pros",
                        "feature_dimension_id": "dim_basic",
                        "information": "企业规模位列行业前十，具有较强的市场影响力",
                        "seq": 1,
                        "is_accept": true,
                        "is_ignore": false
                    }
                ],
                "profileAdvice": [
                    {
                        "profile_id": "profile_001",
                        "feature_dimension_id": "dim_basic",
                        "task_id": "task_basic_001",
                        "task_id__r": "深入分析",
                        "advice": "建议进一步收集客户未来3年的业务发展规划",
                        "is_accept": "0",
                        "is_ignore": "0",
                        "type": "系统建议",
                        "range_type": "dimension",
                        "seq": 1
                    }
                ]
            }
        ]
    }