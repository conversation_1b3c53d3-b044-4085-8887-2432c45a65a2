<template>
<div>
  <div class="cantactinsights-container" v-if="objectData && Object.keys(objectData).length">
    <div class="cantactinsights-set-actions">
      <span style="color: #8048FF;">{{$t('sfa.activity.business.summary.create')}}</span>
      <span>
        <span>{{$t('sfa.vcrm.cantactinsights.last_modified_time')}}</span>
        {{new Date(objectData.last_modified_time).toLocaleDateString('zh-CN', {year: 'numeric', month: '2-digit', day: '2-digit'}).replace(/\//g, '-') + ' ' + new Date(objectData.last_modified_time).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit', hour12: false})}}
        </span>
      <fx-popover
        placement="bottom-end" 
        trigger="click"
        popper-class="insight-settings-popover"
      >
        <span slot="reference" class="fx-icon-set"></span>
        <div class="settings-panel">
          <div class="setting-group">
            <div class="group-title">{{$t("sfa.crm.participantinsights.attitude")}}</div>
            <div class="setting-item">
              <span>{{apiName === 'ContactObj' ? $t("sfa.crm.participantinsights.attitude_update_to_attitude_field_cantact") : $t("sfa.crm.participantinsights.attitude_update_to_attitude_field_newObj")}}</span>
              <fx-switch v-model="attitudeUpdField" size="mini"></fx-switch>
            </div>
            <div class="setting-item">
              <span>{{ $t("sfa.crm.participantinsights.attitude_update_to_attitude_tag_cantact")}}</span>
              <fx-switch v-model="attitudeUpdTag" size="mini"></fx-switch>
            </div>
          </div>
          <div class="setting-group">
            <div class="group-title">{{$t("sfa.vcrm.cantactinsights.personality")}}</div>
            <div class="setting-item">
              <span>{{$t("sfa.crm.participantinsights.attitude_update_to_tag")}}</span>
              <fx-switch v-model="characterUpdTag" size="mini"></fx-switch>
            </div>
          </div>
        </div>
      </fx-popover>
    </div>
    <!-- 态度 -->
    <div class="cantactinsights-item cantactinsights-attitude-container">
      <div
        class="insight-type"
        :style="{ background: getInsightTypeConfig('attitude').color }"
      >
        <span style="width: 14px; height: 14px; margin-top: -5px">
          <span :class="[getInsightTypeConfig('attitude').icon]"></span>
        </span>
        {{ getInsightTypeConfig("attitude").name }}
      </div>
      <div class="insight-result" v-if="objectData">
        <Attitude
          :objectFields="objectFields"
          :objectData="objectData"
          @update:comprehensive-attitude="handleAttitudeChange"
          @update:ai-upd="handleAiUpdChange"
        />
      </div>
    </div>
    <!-- 性格 -->
    <div
      class="cantactinsights-item cantactinsights-attitude-personality"
      v-if="objectData.character_content"
    >
      <div
        class="insight-type"
        :style="{ background: getInsightTypeConfig('personality').color }"
      >
        <span style="width: 14px; height: 14px; margin-top: -5px">
          <span :class="[getInsightTypeConfig('personality').icon]"></span>
        </span>
        {{ getInsightTypeConfig("personality").name }}
      </div>
      <div
        class="insight-result insight-result-personality"
        v-if="objectData.character_content"
      >
        <Personality
          :keyType="'cantactInsight'"
          :objectFields="objectFields"
          :insight="objectData.character_content"
          @locate-source="$emit('locate-source', $event)"
        />
      </div>
    </div>
    <!-- 关注点 -->
    <div class="cantactinsights-item" v-if="objectData.bantAllData && objectData.bantAllData.length">
      <div
        class="insight-type"
        :style="{ background: getInsightTypeConfig('focus_point').color }"
      >
        <span style="width: 14px; height: 14px; margin-top: -5px">
          <span :class="[getInsightTypeConfig('focus_point').icon]"></span>
        </span>
        {{ getInsightTypeConfig("focus_point").name }}
      </div>
      <div class="insight-result" v-if="objectData.bantAllData">
        <ContentText :keyType="'bantAllData'" :text="objectData.bantAllData" />
      </div>
    </div>
    <!-- 会议总结 -->
    <div class="cantactinsights-item" v-if="objectData.meeting_content">
      <div
        class="insight-type"
        :style="{ background: getInsightTypeConfig('meeting_content').color }"
      >
        <span style="width: 14px; height: 14px; margin-top: -5px">
          <span :class="[getInsightTypeConfig('meeting_content').icon]"></span>
        </span>
        {{ getInsightTypeConfig("meeting_content").name }}
      </div>
      <div class="insight-result" v-if="objectData.meeting_content">
        <ContentText :text="objectData.meeting_content" />
      </div>
    </div>
    <!-- 隐形担忧 -->
    <div class="cantactinsights-item"  v-if="objectData.invisible_content">
      <div
        class="insight-type"
        :style="{ background: getInsightTypeConfig('invisible_content').color }"
      >
        <span style="width: 14px; height: 14px; margin-top: -5px">
          <span
             class="invisible-content"
          ></span>
        </span>
        {{ getInsightTypeConfig("invisible_content").name }}
      </div>
      <div class="insight-result" v-if="objectData.invisible_content">
        <ContentText :text="objectData.invisible_content" />
      </div>
    </div>
  </div>
  <div v-else>
    <div class="cantactinsights-empty">
       <div class="no-data" style="margin-top: 12px;">
        <div class="image-placeholder"></div>
        <div class="text">{{$t('crm.sfa.list.nodata.tip')}}</div>
      </div>
    </div>
  </div>
</div>
  
</template>

<script>
import Api from "@common/api";
import Personality from "../../../activerecordobj/components/participantinsights/personality.vue";
import ContentText from "./text.vue";
import Attitude from "./attitude/attitude.vue";
export default {
  name: "Cantactinsights",
  props: ["compInfo", "apiName", "dataId"],
  components: {
    Personality,
    ContentText,
    Attitude,
  },
  data() {
    console.log("[][]");
    return {
      objectData: {},
      objectFields: {},
      insightTypeConfig: {
        personality: {
          color: "#F2F4FB",
          icon: "fx-icon-f-obj-app3",
          name: $t("sfa.vcrm.cantactinsights.personality"),
        },
        meeting_content: {
          color: "#F0FFFB",
          icon: "fx-icon-f-kuaisuhuifu",
          name: $t("sfa.crm.participantinsights.meeting_summary"),
        },
        invisible_content: {
          color: "#F7FBF2",
          icon: "fx-icon-f-biaoqing",
          name: $t("sfa.crm.participantinsights.invisible_concern"),
        },
        focus_point: {
          color: "#FFFAF0",
          icon: "fx-icon-f-app19",
          name: $t("sfa.crm.participantinsights.focus_point"),
        },
        attitude: {
          color: "#F0F9FF",
          icon: "fx-icon-f-biaoqing",
          name: $t("sfa.crm.participantinsights.attitude"),
        },
      },
    };
  },
  computed: {
    attitudeUpdField: {
      get() {
        return !!(this.objectData && this.objectData.attitude_upd_field);
      },
      async set(value) {
        try {
          await this.changConfig("attitude_upd_field", value);
        } catch (error) {
          // The error is already handled in changConfig, just catch it to prevent unhandled promise rejection.
        }
      },
    },
    attitudeUpdTag: {
      get() {
        return !!(this.objectData && this.objectData.attitude_upd_tag);
      },
      async set(value) {
        try {
          await this.changConfig("attitude_upd_tag", value);
        } catch (error) {
          // The error is already handled in changConfig, just catch it to prevent unhandled promise rejection.
        }
      },
    },
    characterUpdTag: {
      get() {
        return !!(this.objectData && this.objectData.character_upd_tag);
      },
      async set(value) {
        try {
          await this.changConfig("character_upd_tag", value);
        } catch (error) {
          // The error is already handled in changConfig, just catch it to prevent unhandled promise rejection.
        }
      },
    },
  },
  mounted() {
    const _this = this;
    return Promise.all([
      this.fetchData(),
      Api.findDescribeByApiName({ api_name: "ActivityContactInsightObj" }),
    ])
      .then(([objectData, objectFields]) => {
        _this.objectFields = objectFields.objectDescribe.fields || {};
        _this.parseData(objectData);
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        this.resultUserDataList = [];
      })
      .finally(() => {});
  },
  methods: {
    async handleAiUpdChange(newValue) {
      try {
        await this.changConfig(
          "comprehensive_attitude_is_ai_upd",
          newValue
        );
      } catch (error) {
        // The error is already handled in changConfig, just catch it to prevent unhandled promise rejection.
      }
    },
    async handleAttitudeChange(newValue) {
      try {
        await this.$confirm(
          $t("sfa.crm.participantinsights.attitude_ai_upd_tip"),
          $t("确认"),
          {
            distinguishCancelAndClose: true,
            confirmButtonText: $t("是"),
            cancelButtonText: $t("否"),
          }
        );
        // 用户点击 "是"
        await this.changConfig("comprehensive_attitude_is_ai_upd", true);
        this.updateAttitude(newValue);
      } catch (action) {
        // 用户点击 "否" 或关闭弹窗
        await this.changConfig("comprehensive_attitude_is_ai_upd", false);
        this.updateAttitude(newValue);
      }
    },
    updateAttitude(newValue) {
      const _this = this;
      CRM.util.FHHApi(
        {
          url: "/EM1HNCRM/API/v1/object/contact_insight/service/update_details",
          data: {
            objectData: {
              _id: _this.objectData._id,
              comprehensive_attitude: newValue,
            },
            updateFieldList: ["comprehensive_attitude"],
          },
          success: (res) => {
            if (this.objectData) {
              _this.$set(this.objectData, "comprehensive_attitude", newValue);
            }
          },
          error: (err) => {},
        },
        { errorAlertModel: 1 }
      );
    },

    changConfig(type, value) {
      const _this = this;
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi(
          {
            url: "/EM1HNCRM/API/v1/object/contact_insight/service/set_switch",
            data: {
              objectApiName: _this.apiName,
              objectDataId:  _this.dataId,
              fieldApiName: type,
              value: value,
            },
            success: (res) => {
              if (_this.objectData) {
                this.$set(this.objectData, type, value);
              }
              resolve(res);
            },
            error: (err) => {
              if (_this.objectData) {
                this.$set(this.objectData, type, !value);
              }
              reject(err);
            },
          },
          { errorAlertModel: 1 }
        );
      });
    },
    getInsightTypeConfig(type) {
      return this.insightTypeConfig[type] || this.defaultInsightConfig;
    },
    // 处理数据
    parseData(objectData) {
      console.log(objectData);
      const bantAllData = [];

      if (objectData.bant_authority) {
        bantAllData.push({
          topics: objectData.bant_authority,
          topics_source: objectData.authority_active_record_id,
          name: this.objectFields.bant_authority.label,
          link_data: objectData.authority_active_record_id__r,
        });
      }

      if (objectData.bant_budget) {
        bantAllData.push({
          topics: objectData.bant_budget,
          topics_source: objectData.budget_active_record_id,
          name: this.objectFields.bant_budget.label,
          link_data: objectData.budget_active_record_id__r,
        });
      }

      if (objectData.bant_need) {
        bantAllData.push({
          topics: objectData.bant_need,
          topics_source: objectData.need_active_record_id,
          name: this.objectFields.bant_need.label,
          link_data: objectData.need_active_record_id__r,
        });
      }

      if (objectData.bant_time) {
        bantAllData.push({
          topics: objectData.bant_time,
          topics_source: objectData.time_active_record_id,
          name: this.objectFields.bant_time.label,
          link_data: objectData.time_active_record_id__r,
        });
      }
      if(bantAllData.length){
        objectData.bantAllData = bantAllData;
      }
      this.objectData = objectData;
    },

    fetchData() {
      const _this = this;
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi(
          {
            url: "/EM1HNCRM/API/v1/object/contact_insight/service/query_details",
            data: {
              objectApiName: _this.apiName,
              objectDataId: _this.dataId,
            },
            success: (res) => {
              const pageData = res.Value?.objectData || [];
              resolve(pageData);
            },
            error: (err) => {
              reject(err);
            },
          },
          { errorAlertModel: 1 }
        );
      });
    },
  },
};
</script>

<style lang="less" scoped>
.cantactinsights-container {
  padding: 16px;
  background: var(--color-neutrals01);
  border-radius: 4px;
  width: calc(100% - 24px);
  padding: 12px;
  position: relative;
  .cantactinsights-set-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(---16, 16px);
    flex: 1 0 0;
    span{
      color: var(--color-neutrals11);
      font-size: var(---12_size, 13px);
      font-style: normal;
      font-weight: 400;
      line-height: var(---12_line-height, 18px); /* 150% */
    }
    .fx-icon-set {
      cursor: pointer;
      font-size: 16px;
      color: #666;
      &:hover {
        color: var(--color-primary06);
      }
    }
  }
  .cantactinsights-item {
    border-radius: 8px;
    border: 2px solid var(--color-special01);
    margin: 12px 0;

    .insight-type {
      position: relative;
      padding-right: 48px;
      font-size: 14px;
      color: #181C25;
      font-weight: 700;
      display: flex;
      gap: 8px;
      height: 18px;
      padding: 6px 12px;
      align-items: center;
      align-self: stretch;
      border-radius: 8px 8px 0px 0px;
      transition: background-color 0.3s ease;
      .invisible-content{
        width: 14px;
        height: 14px;
        display: inline-block;
        background: image-set(
          url("~@assets/images/activity/face_oppose.svg") 1x,
          url("~@assets/images/activity/face_oppose.svg") 2x
        ) center center no-repeat;
      }
      .fx-icon-f-biaoqing::before {
          color: #368DFF;
        }
        .fx-icon-f-mgt-coordinationmanage::before {
          color: #368DFF;
        }
        .fx-icon-f-obj-app3::before {
          color: #927EE8;
        }
        .fx-icon-f-list2::before {
          color: #FF7383;
        }
        .fx-icon-f-obj-app200::before {
          color: #FFB56B;
        }
        .fx-icon-f-obj-app339::before {
          color: #36C2B6;
        }
        .fx-icon-f-product_hot::before {
          color: #976AEB;
        }
        .fx-icon-f-app19::before {
          color: #FFCA2B;
        }
        .fx-icon-f-obj-app138::before {
          color: #16B4AB;
        }
        .fx-icon-f-kuaisuhuifu::before {
          color: #36C2B6;
        }

      .insight-icon-special {
        width: 14px;
        height: 14px;
        display: inline-block;
        background: image-set(
            url("~@assets/images/activity/face_support.svg") 1x,
            url("~@assets/images/activity/face_support.svg") 2x
          )
          center center no-repeat;
      }

      .insight-actions {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        gap: 8px; // 图标之间的间距

        span {
          cursor: pointer;
          transition: transform 0.3s;
          color: var(--color-neutrals11);
          font-size: 12px;

          &:hover {
            color: var(--color-primary06);
          }

          &.fx-icon-unfold-2.is-fold {
            transform: rotate(-90deg);
          }
        }
      }
    }

    .insight-result {
      font-size: 13px;
      padding: 12px;
      color: var(--color-neutrals19);
      line-height: 1.8;
      transition: all 0.3s;
      max-height: 1000px;
      opacity: 1;
      position: relative;

      &.is-hidden {
        max-height: 0;
        opacity: 0;
        margin: 0;
        padding: 0;
      }

      .expand-button {
        color: var(--color-info06);
        cursor: pointer;
        font-size: 12px;
        display: block;
        margin-top: 4px;

        &:hover {
          opacity: 0.8;
        }
      }
    }
  }
}
.no-data {
      width: 100%;
      border-radius: 8px;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 265px;
      text-align: center;
      font-family: Arial, sans-serif;
      .image-placeholder {
            width: 150px;
            height: 120px;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            background: image-set(url("~@assets/images/nodata2.png") 1x,
                    url("~@assets/images/<EMAIL>") 2x) center center no-repeat;
        }
        .text {
            color: var(--Text-H2, #545861);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
        .reload {
            color: var(--Text-Blue, #0C6CFF);
            text-align: center;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px; /* 142.857% */
        }
    }
</style>

<style lang="less">
.insight-settings-popover {
  padding: 0 !important;
  border-radius: 8px;
  .settings-panel {
    padding: 4px 8px;
    width: 175px;
    .setting-group {
      margin-bottom: 12px;
      &:last-child {
        margin-bottom: 0;
      }
      .group-title {
        font-size: 12px;
        font-weight: 400;
        color: #91959E;
        margin: 8px 0;
      }
      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        gap: 10px;
        font-size: 13px;
        font-weight: 400;
      }
    }
  }
}
</style>