<template>
  <div class="ai-suggest-operate-section">
    <!-- 顶部选择器 -->
    <div class="left-content-box">
        <div class="selector-label-box" v-if="relatedObjectOptions.length">
            <span class="selector-label">{{ $t('sfa.ai.suggest.related.object.text') }}</span>
            <fx-select
                v-model="relatedObjectName"
                :options="relatedObjectOptions"
                size="micro"
                filterable
                @change="onChange"
            ></fx-select>
        </div>
        <div class="selector-help-tip" v-if="relatedObjectOptions.length">
            <fx-tooltip :open-delay="600" effect="light" :content="$t('sfa.ai.suggest.related.object.tip')" placement="bottom">
                <span class="fx-icon-question"></span>
            </fx-tooltip>
        </div>
    </div>
    <!-- 右侧操作-开关和互动综述 -->
    <div class="right-content-box">
        <div class="switch-box" v-if="showSingleSummary">
            <fx-switch
                v-model="openMultipleSummary"
                size="mini"
                @change="onChangeOpenMultipleSummary"
            >
            </fx-switch>
            <span class="summary-label">{{ $t('sfa.ai.suggest.show.interactive.text') }}</span>
        </div>
        <div class="summary-help-tip" v-if="showSingleSummary">
            <fx-tooltip :open-delay="600" effect="light" :content="$t('sfa.ai.open.multiple.summary.tip')" placement="bottom">
                <span class="fx-icon-question"></span>
            </fx-tooltip>
        </div>
        <div class="middle-line" v-if="showExpand || showHistoryListIcon"></div>
        <div class="icon-box">
            <div><span class="fx-icon-obj-app455" @click="handleShowHistoryList" v-if="showHistoryListIcon"></span></div>
            <div class='expand-collapse-list' @click="handleToggleExpand"  v-if="showExpand">
                <span v-if="isExpanded" class='fx-icon-list-expand'></span>
                <span v-else class='fx-icon-list-collapse'></span>
            </div>
        </div>
    </div>
    <si-suggest-issues-history-list
      :dialogVisible="historyListDialogVisible"
      :apiName="apiName"
      :dataId="dataId"
      :relatedObjectOptions="relatedObjectOptions"
      :relatedObjectName="relatedObjectName"
      :detailData="detailData"
      @hideDialog="historyListDialogVisible = false"
    />
  </div>
</template>

<script>
import SiSuggestIssuesHistoryList from './aiSuggestIssuesHistoryList.vue';

export default {
  name: 'AiSuggestOperateSection',
  components: {
    SiSuggestIssuesHistoryList
  },
  props: {
    relatedObjectOptions: {
        type: Array,
        default: () => []
    },
    isExpanded: {
      type: Boolean,
      required: true
    },
    relatedObjectName: {
        type: String,
        default: ''
    },
    showExpand: {
        type: Boolean,
        default: false
    },
    showSingleSummary: {
        type: Boolean,
        default: false
    },
    openMultipleSummary: {
        type: Boolean,
        default: false
    },
    apiName: {
      type: String,
      required: true
    },
    dataId: {
      type: [String, Number],
      required: true
    },
    showHistoryListIcon: {
      type: Boolean,
      default: false
    },
    detailData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
        historyListDialogVisible: false,
    };
  },
  methods: {
    onChange(value) {
        this.$emit('change-object', value);
    },
    onChangeOpenMultipleSummary(value) {
        this.$emit('change-open-multiple-summary', value);
    },
    handleToggleExpand() {
        this.$emit('toggle-expand');
    },
    handleShowHistoryList() {
        this.historyListDialogVisible = true;
    }
  }
};
</script>

<style scoped lang="less">
.ai-suggest-operate-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 24px;
  justify-content: space-between;
  margin-bottom: 8px;
  .left-content-box {
    display: flex;
    align-items: center;
    gap: 4px;
    .selector-label-box{
        background-color: #fff;
        padding: 0px 6px;
        box-sizing: border-box;
        border-radius: 8px;
        .fx-select{
            width: 80px;
        }
        /deep/  .el-input__inner{
            border: none;
            color: var(--color-neutrals19);
            font-weight: 400;
            line-height: 20px;
            font-size: 13px;
        }
    }
  }
  .fx-icon-question::before{
    color: #c1c5ce;
  }
  .right-content-box {
    display: flex;
    align-items: center;
    gap: 8px;
    .middle-line{
        display: inline-block;
        position: relative;
        top: 0px;
        width: 1px;
        height: 10px;
        background: #dee1e8;
    }
    .icon-box{
        display: flex;
        align-items: center;
        // gap: 8px;
        .expand-collapse-list{
            cursor: pointer;
        }
        .fx-icon-obj-app455{
            cursor: pointer;
            margin-right: 8px;
        }
    }
  }
  .selector-label {
    font-size: 13px;
    color: #181C25;
  }
  .summary-label {
    font-size: 12px;
    color: #181C25;
  }
}
</style>
