<!--
 * @Author: <PERSON>
 * @LastEditors: <PERSON>
 * @LastEditTime: 2025-05-16 14:33:21
-->

<template>
  <div 
    class="activity-corpus-list-wrapper" 
    ref="activity-corpus-list-wrapper"
  >
    <div class="activity-corpus-list-search-container">
      <div class="search-input-wrapper">
        <fx-input
          type="text"
          @input="handleSearch"
          :placeholder="$t('sfa.activity.corpus.audio_search_input_placeholder')"
          :class="['activity-corpus-list-search-input', { 'no-result': !searchState.searchResults.length }]"
          size="mini"
          is-search
          clearable
        />
        <div class="search-navigation" v-if="searchState.searchResults.length">
          <span class="match-count">{{ searchState.currentMatchIndex + 1 }}/{{ searchState.matchCount }}</span>
          <div class="navigation-arrows">
            <span
              class="fx-icon-arrow-up nav-arrow"
              @click="navigateToMatch('prev')"
              :class="{ 'disabled': !canNavigateUp }"
            ></span>
            <span
              class="fx-icon-arrow-down nav-arrow"
              @click="navigateToMatch('next')"
              :class="{ 'disabled': !canNavigateDown }"
            ></span>
          </div>
        </div>
      </div>
      <!-- 发言人管理 -->
      <fx-popover
        ref="speakerGuidePopoverRef"
        v-model="showSpeakerGuide"
        placement="top-end"
        trigger="manual"
        popper-class="speaker-guide-popover"
        :zIndex="popperZIndex"
      >
        <div class="speaker-guide-content">
          <span class="guide-icon"></span>
          <span class="guide-text">{{ $t('sfa.activity.corpus.unbound_speaker_guide_text') }}</span>
          <span class="guide-icon-2"></span>
          <span class="guide-close fx-icon-close" @click.stop="hideSpeakerGuide"></span>
        </div>
        <div
          slot="reference"
          class="operate-btn-wrapper"
          :class="{ 'has-unbound-speakers': hasUnboundSpeakers }"
        >
          <fx-button
            size="mini"
            type="primary2"
            icon="fx-icon-obj-app276"
            square
            class="operate-btn"
            @click="toggleSpeakerManagement($event, 'bottom-end')"
            ref="speakerManagementBtnRef"
          ></fx-button>
        </div>
      </fx-popover>
    </div>
    <div class="list-empty" v-if="!displayItems.length && !fromToRecord">
      <div class="list-empty-image"></div>
      <div class="list-empty-text">
        <p>{{ $t('sfa.activity.corpus.text_nodata_tip1') }}</p>
        <p>{{ $t('sfa.activity.corpus.text_nodata_tip2') }}</p>
      </div>
    </div>
    <fx-scrollbar v-else class="list-scroll-container" ref="listScrollRef" @scroll="handleScroll">
      <!-- 滚动到底部按钮 -->
      <div
        v-show="showScrollToBottom && fromToRecord"
        class="scroll-to-bottom-btn"
        @click="scrollToBottom"
      >
        <i class="fx-icon-arrow-down2"></i>
      </div>
      <!-- 当前位置按钮 -->
      <div
        v-show="showCurrentPositionBtn"
        class="current-position-btn"
        @click="goToCurrentPosition"
      >
        <i class="fx-icon-obj-app194"></i>
      </div>
      <!-- <div class="list-empty" v-if="!displayItems.length">暂无数据</div>
        v-infinite-scroll="load"
        :infinite-scroll-disabled="scrollDisabled"
      -->
      <ul
        class="list-container"
      >
        <li
          v-for="(item, index) in displayItems"
          :key="index"
          :class="getItemClass(item)"
          :data-id="item.id"
        >
          <div class="message-wrapper" :data-id="item.seq">
            <sfaAiAvatar
              :showtopRightBadge="item.userApiName === 'ContactObj'"
              :data="{
                userName: item.userName || item.originalUserName,
                backgroundColor: item.avatarBgColor || getAvatarColor(item),
                isAvaDefault: !!item.isDefaultSpeaker,
                dataId: item.id,
                useAvatarImg: !!(item.profileImage && item.profileImage.length),
                avatarImgSrc: getAvaterImgSrc(item),
                personnelId: getPersonnelId(item)
              }"
            />
            <div class="message-content"
              v-editable="{
                disabled: disableOperation,
                version: itemVertions[index] && itemVertions[index].version,
                onSave: (content, target) => {
                  handleContentSave(item, content, target.parentElement.classList.contains('translate-content') || false);
                  updateItemEditingState(item.id, false); // 保存完成，退出编辑状态
                },
                onEdit: () => {
                  updateItemEditingState(item.id, true); // 进入编辑状态
                  handleItemDoubleClick(item.id); // 处理双击检测
                }, // 进入编辑状态
                onCancel: () => updateItemEditingState(item.id, false), // 取消编辑，退出编辑状态
                editableSelector: '.message-text div',
                buttonPosition: (target) => target.parentElement.classList.contains('translate-content') ? 'bottom-right' : 'top-right'
              }"
            >
              <div class="message-header">
                <div class="username-hover-wrapper">
                  <div class="username-wrapper">
                    <span
                      class="username"
                      v-username-editable="{
                        item,
                        index,
                        defaultText: $t('sfa.activity.corpus.list_item_user_label')
                      }"
                    >{{item.userName || $t('sfa.activity.corpus.list_item_user_label')}}</span>
                    <span class="username-line"></span>
                    <fx-button
                      class="activity-text-button-custom username-edit-btn username-edit-icon"
                      type="text"
                      size="micro"
                      @click="startUsernameEdit($event)"
                    >{{ $t('sfa.activity.corpus.list_item_username_btn_edit') }}</fx-button>
                    <fx-button
                      style="margin-left: 8px;"
                      class="activity-text-button-custom username-edit-icon"
                      type="text"
                      size="micro"
                      @click="handleBindContact(Object.assign(item, { itemIndex: index }), true)"
                    >{{ $t('sfa.activity.corpus.list_item_username_btn_bind') }}</fx-button>
                  </div>
                  <span class="time">{{formatTime(item.startTime, false)}}</span>
                </div>
              </div>
              <div class="message-text" v-html="defaultRender(item)" :class="{'message-text-disabled': !hasSavedId(item.id)}" @click="handleItemClick(item, $event)"></div>
              <div class="message-text-line" v-show="item.translateContent"></div>
              <div class="message-text translate-content" v-show="item.translateContent" v-html="translateRender(item)" :class="{'message-text-disabled': !hasSavedId(item.id)}" @click="handleItemClick(item, $event)"></div>
            </div>
          </div>
        </li>
      </ul>
      <div class="identifying-wrapper" v-if="activityActionState === '1' && fromToRecord">
        <div class="header">
          <sfaAiAvatar :data="{
            userName: $t('sfa.activity.corpus.audio_recording_user_label'),
            backgroundColor: getAvatarColor({}),
            isAvaDefault: true,
            dataId: '',
            useAvatarImg: false
          }" />
          <div class="user">
            <span class="username">{{ $t('sfa.activity.corpus.audio_recording_user_label') }}</span>
            <!-- <span class="time">{{'00:00'}}</span> -->
          </div>
        </div>
        <div class="content">
          <div class="content-inner">
            <div class="loading-dots" v-if="!identifyingData">{{ $t('sfa.activity.corpus.audio_recording_content_label') }}...</div>
            <div class="identifying-content" v-else>
              <div>{{ identifyingData || '' }}</div>
              <div class="translate-identifying-content" v-show="translateData">{{ translateData || '' }}</div>
            </div>
          </div>
        </div>
      </div>
    </fx-scrollbar>
    <bind-contact-dialog
      ref="bindContactDialogRef"
      :visible.sync="userEditState.bindContactDialogVisible"
      :data-id="dataId"
      @confirm="handleBindContactConfirm"
      v-if="userEditState.bindContactDialogVisible"
    ></bind-contact-dialog>
    <speaker-management
      :user-items="userItems"
      :data-id="dataId"
      :get-avatar-color="getAvatarColor"
      :placement="speakerManagementPlacement"
      ref="speakerManagementRef"
      @reset-dialog="userEditState.speakerManagementVisible = false; checkToShowSpeakerGuide()"
      @merge-confirm="handleMergeSpeakerConfirm"
      @open-bind-contact="handleBindContact"
    ></speaker-management>
    <ai-guide ref="aiGuideRef" v-model="guideVisible" :pos="guidePos" @guide:hide="destroyGuide"></ai-guide>
  </div>
</template>

<script>
import { editable } from '@components/editable';
import { SelectionToolbar } from '@components/selectiontoolbar';
import AiGuide from '@components/aiguide/aiguide.vue'
import SearchUtils from './search-utils';
import { SpeakerManagement, BindContactDialog } from './components'
import sfaAiAvatar from '../sfaAiAvatar/sfaAiAvatar.vue'
import { usernameEditable } from './directives';
import { createEventManager, EVENT_NAMES } from './event-manager';
import audioPlayerMixin from './mixins/audio-player';

const AVATAR_BG_COLORS = [
  '#FFA142', '#368DFF',
  '#55D48C', '#FF7752',
  '#C1C5CE', '#BC97F7',
  '#5BCFC1', '#A3D962',
]

export default {
  name: 'List',
  components: {
    SpeakerManagement,
    BindContactDialog,
    AiGuide,
    BindContactDialog,
    sfaAiAvatar,
  },
  directives: {
    editable,
    usernameEditable
  },
  mixins: [audioPlayerMixin],
  props: {
    items: {
      type: Array,
      required: true
    },
    renderItem: {
      type: Function,
      default: null
    },
    extendData: {
      type: Object,
      default: () => ({})
    },
    dataId: {
      type: String,
      default: ''
    },
    apiName: {
      type: String,
      default: ''
    },
    identifyingData: {
      type: String,
      default: ''
    },
    translateData: {
      type: String,
      default: ''
    },
    activityActionState: {
      type: String,
      default: ''
    },
    avatarColorList: {
      type: Array,
      default: () => []
    }
  },

  watch: {
    'items': {
      handler (newArr) {
        // 监听items数组长度变化，避免全树深度监听带来的性能开销
        if (this.activityActionState === '1') {
          this.$nextTick(() => {
            const scrollbar = this.$refs.listScrollRef;
            if (scrollbar) {
              // 更新滚动条
              scrollbar.update();
              // 如果按钮显示中，说明用户在查看历史消息，不执行自动滚动
              if (!this.showScrollToBottom) {
                scrollbar.setScrollTop(99999); // 保持原有的滚动逻辑
              }
            }
          });
        }
        if (newArr.length) {
          // 使用事件管理器触发事件
          if (this.eventManager) {
            this.eventManager.emit(EVENT_NAMES.CORPUS_LIST_UPDATED, this.userAvaBgColor);
          } else {
            // 兼容处理：如果事件管理器未初始化，则使用原始方式触发事件
            this.$context.$emit(EVENT_NAMES.CORPUS_LIST_UPDATED, this.userAvaBgColor);
          }
        }
      },
      deep: true
    },
    'userItems': {
      handler(newItems, oldItems) {
        // 检查是否真的发生了变化，避免不必要的事件触发
        if (JSON.stringify(newItems) !== JSON.stringify(oldItems) && newItems.length) {
          // 使用事件管理器触发用户列表更新事件
          if (this.eventManager) {
            this.eventManager.emit(EVENT_NAMES.CORPUS_USER_LIST_UPDATED, newItems);
          } else {
            // 兼容处理：如果事件管理器未初始化，则使用原始方式触发事件
            this.$context.$emit(EVENT_NAMES.CORPUS_USER_LIST_UPDATED, newItems);
          }
          
          // 如果所有发言人都已绑定，则触发发言人绑定完成事件
          if (!this.hasUnboundSpeakers) {
            if (this.eventManager) {
              this.eventManager.emit(EVENT_NAMES.CORPUS_SPEAKER_BIND_COMPLETE);
            } else {
              this.$context.$emit(EVENT_NAMES.CORPUS_SPEAKER_BIND_COMPLETE);
            }
          }
        }
      },
      deep: true
    },
    hasUnboundSpeakers: {
      handler(newValue) {
        if (newValue) {
          this.checkToShowSpeakerGuide();
        } else {
          this.showSpeakerGuide = false; // 如果没有未绑定的人，则隐藏
        }
      },
      immediate: true, // 立即执行一次
    }
  },

  data() {
    return {
      searchState: {
        keyword: '',
        matchCount: 0,
        currentMatchIndex: -1,
        currentPage: 1,
        pageSize: 20,
        loading: false,
        hasMore: true,
        searchResults: []
      },
      userEditState: {
        editingUsername: null,
        bindContactDialogVisible: false,
        speakerManagementVisible: false,
        showBindContact: false,
        selectedUsers: [],
        bindContactData: [],
        mergeSpeakerValue: ''
      },
      // activityActionState: this.extendData?._activityActionState?.[0]?.state || '',
      popperZIndex: FxUI.Utils.getPopupZIndex(),
      guideVisible: false,
      guidePos: {
        x: 0,
        y: 0
      },
      speakerCache: {}, // 用对象存储发言人缓存数据
      showScrollToBottom: false, // 是否显示滚动到底部按钮
      eventManager: null, // 事件管理器实例
      detailData: this.$context?.getData(),
      showSpeakerGuide: false, // 控制引导气泡的显示
      guideClosedInThisInstance: false, // 标记引导是否在当前实例中被用户关闭
      // 编辑状态跟踪 - 响应式数据，避免DOM查询和$forceUpdate造成的卡顿
      editingItemIds: {}, // 使用对象跟踪正在编辑的语料项ID，O(1)查找性能
      resizeObserver: null, // 用于监听右侧侧边栏变化，动态修改引导气泡位置
    }
  },
  computed: {
    // 是否从实时录音入口进入
    fromToRecord() {
      return !!this.extendData?._activityAudioConfig;
    },
    disableOperation() {
      return !this.fromToRecord && this.activityActionState === '1';
    },
    displayItems() {
      return this.items;
    },
    scrollDisabled() {
      const { loading, hasMore } = this.searchState;
      return loading || !hasMore;
    },
    // 缓存搜索关键字对应的正则表达式，避免每次高亮都重复创建正则对象
    highlightRegex() {
      if (!this.searchState.keyword) return null;
      // 对搜索关键字中的特殊字符进行转义，保证正则表达式安全正确
      const escapedKeyword = this.searchState.keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      return new RegExp(`(${escapedKeyword})`, 'gi');
    },
    // 生成用户头像背景色映射表，key为用户名，value为对应的背景色
    userAvaBgColor() {
      return this.items.reduce((acc, item) => {
        const userName = item.userName || item.originalUserName || '';
        const avaColor = item.avatarBgColor || this.getAvatarColor(item);
        if (userName) {
          acc[item.seq ?? item.id] = {
            userName,
            avaColor,
            nameAvaId: this.getNameAvaId(userName),
            isAvaDefault: !!item.isDefaultSpeaker,
            activityUserId: item.activityUserId,
            profileImage: item.profileImage,
            personnelId: this.getPersonnelId(item)
          };
        }
        return acc;
      }, {});
    },
    /**
     * 处理用户列表数据
     * 1. 从 identifyList 中提取用户信息并去重
     * 2. 用于子组件展示和用户 ID 计算
     * @returns {Array} 格式化后的用户列表
     */
    userItems() {
      return [...new Set(this.displayItems
          .filter(item => item.userName)
          .map(item => item.userName))]
          .map(userName => {
              const item = this.displayItems.find(i => i.userName === userName);
              return {
                  ...item,
                  label: userName
              };
          });
    },
    canNavigateUp() {
      return this.searchState.searchResults.length > 0 && this.searchState.currentMatchIndex > 0;
    },
    canNavigateDown() {
      return this.searchState.searchResults.length > 0 &&
        this.searchState.currentMatchIndex < this.searchState.searchResults.length - 1;
    },
    itemVertions() {
      return this.displayItems.map(item => ({
        id: item.id,
        version: `${this.searchState.keyword}_${item.content}_${item.translateContent}`
      }));
    },
    // 判断是否存在未绑定的发言人，用于显示发言人管理徽标
    hasUnboundSpeakers() {
      return !!this.userItems.some(item => !item.userApiName);
    },

  },
  methods: {
    /**
     * 移除HTML标签
     * @param {string} str - 包含HTML标签的字符串
     * @returns {string} 移除HTML标签后的字符串
     */
    removeHTMLTags(str) {
      return SearchUtils.removeHTMLTags(str);
    },
    /**
     * 处理搜索输入
     * 使用debounce防抖优化搜索性能
     * @param {string} value - 搜索关键词
     */
    handleSearch: _.debounce(function(value) {
      // 如果新值为空且旧值不为空，立即重置（不使用debounce延迟）
      if (!value && this.searchState.keyword) {
        this.resetSearchState();
        return;
      }

      this.searchState.keyword = value;
      if (!value) {
        this.resetSearchState();
        return;
      }
      this.doSearch(value);
    }, 300),

    /**
     * 执行搜索
     * @param {string} keyword - 搜索关键词
     */
    doSearch(keyword) {
      if (!keyword) {
        this.resetSearchState();
        return;
      }

      try {
        // 使用SearchUtils工具类执行搜索
        const searchResult = SearchUtils.search(keyword, this.displayItems);

        this.searchState.searchResults = searchResult.searchResults;
        this.searchState.matchCount = searchResult.matchCount;

        if (searchResult.matchCount > 0) {
          this.searchState.currentMatchIndex = 0;
          this.$nextTick(() => {
            if (searchResult.searchResults[0] && this.scrollToMatch) {
              this.scrollToMatch(searchResult.searchResults[0]);
            }
          });
        } else {
          this.resetSearchState();
        }
      } catch (error) {
        console.error('Error in doSearch:', error);
        this.resetSearchState();
      }
    },

    /**
     * 高亮显示文本中匹配搜索关键字的部分，并添加高亮样式
     * @param {string} text - 待高亮的文本
     * @returns {string} 渲染后的 HTML 字符串
     */
    highlightText(text, isTranslate = false) {
      if (!this.searchState.keyword || !text) return text;

      const keyword = this.searchState.keyword;
      const currentMatch = this.searchState.searchResults[this.searchState.currentMatchIndex];

      return SearchUtils.highlightText(text, keyword, currentMatch, isTranslate);
    },

    /**
     * 获取头像背景颜色
     * @param {Object} item - 用户信息对象
     * @returns {string} 颜色值
     */
    getAvatarColor(item) {
      const colors = this.avatarColorList.length ? this.avatarColorList : AVATAR_BG_COLORS;

      const nameAvaId = item.nameAvaId == 0 || item.nameAvaId ? item.nameAvaId : 0;
      const index = (nameAvaId || 0) % colors.length;


      return colors[index];
    },

    /**
     * 默认渲染内容
     * @param {Object} item - 列表项数据
     * @returns {string} 渲染后的HTML字符串
     */
    defaultRender(item) {
      // 使用SearchUtils工具类渲染内容
      const keyword = this.searchState.keyword;
      const currentMatch = this.searchState.searchResults[this.searchState.currentMatchIndex];
      return SearchUtils.renderContent(item, keyword, currentMatch, false);
    },

    /**
     * 渲染翻译内容
     * @param {Object} item - 列表项数据
     * @returns {string} 渲染后的HTML字符串
     */
    translateRender(item) {
      // 使用SearchUtils工具类渲染翻译内容
      const keyword = this.searchState.keyword;
      const currentMatch = this.searchState.searchResults[this.searchState.currentMatchIndex];
      return SearchUtils.renderContent(item, keyword, currentMatch, true);
    },

    /**
     * 加载更多数据
     */
    load() {
      this.searchState.currentPage++;
      this.searchState.hasMore = this.searchState.currentPage * this.searchState.pageSize < this.items.length;
    },

    /**
     * 绑定联系人
     * @param {Object} item - 用户信息对象
     */
     handleBindContact(item, isEditSpeaker = false) {
      if (this.disableOperation) {
        this.$message.warning(this.$t('sfa.activity.corpus.audio_disable_operation_tip_text'))
        return;
      }
      this.userEditState.bindContactDialogVisible = true
      this.$nextTick(() => {
        this.$refs.bindContactDialogRef.open(item, isEditSpeaker)
      })
    },

    /**
     * 编辑用户名
     * @param {Object} item - 用户信息对象
     */
    handleEditUsername (item) {
      if (this.disableOperation) {
        this.$message.warning(this.$t('sfa.activity.corpus.audio_disable_operation_tip_text'))
        return;
      }
      this.userEditState.editingUsername = CRM.util.deepClone(item);
      this.$nextTick(() => {
        const inputRef = this.$refs?.usernameInputRef?.[0];
        if (inputRef) {
          inputRef.focus();
          // 聚焦后选中输入框的全部内容
          inputRef.$el.querySelector('input').select();
        }
      });
    },

    /**
     * 取消编辑用户名
     */
    handleUsernameCancel () {
      this.userEditState.editingUsername = null;
    },

    /**
     * 格式化联系人操作数据
     * @param {Object} userData - 原始用户数据
     * @param {string} opType - 操作类型：'edit'|'bind'|'merge'
     * @param {string} type - 修改类型：'single' 或 'global'
     * @param {Object} additionalData - 额外数据参数
     * @returns {Promise<Object>} 包含requestData和extendParams的对象
     */
    async formatContactOperationData(userData, opType, type = 'single', additionalData = {}) {
      // 基础配置
      const isGlobal = type === 'global';

      // 基础事件参数
      const extendParams = {
        isGlobal,
        refreshType: isGlobal ? 'global' : 'single',
        ...additionalData // 合并传入的额外数据
      };

      // 初始化替换信息数组
      let replaceInfos = [];

      // 根据操作类型处理数据
      switch (opType) {
        case 'merge': // 合并发言人操作
          // 直接使用传入的replaceInfos数组
          replaceInfos = userData.replaceInfos || [];
          break;

        case 'bind': // 绑定联系人操作
          {
            // 构建用户替换信息
            const userInfo = {
              targetUserApiName: userData.targetUserApiName,
              targetUserId: userData.targetUserId,
              targetUserName: userData.targetUserName,
              targetNameAvaId: userData.targetNameAvaId || this.getNameAvaId(userData.targetUserName),
              userName: userData.isDefaultSpeaker ? userData.originalUserName : userData.userName,
              userId: userData.userId,
              profileImage: userData.profile_image || undefined,
              activityUserId: userData.activityUserId || null
            };

            // 单条修改时添加文档ID
            if (type === 'single' && userData.id) {
              userInfo.docId = userData.id;
            }

            replaceInfos = [userInfo];

            // 扩展参数 - 为绑定联系人操作添加特定参数
            Object.assign(extendParams, {
              userName: userData.targetUserName,
              userId: userData.userId,
              targetUserId: userData.targetUserId,
              userApiName: userData.targetUserApiName,
              itemIndex: userData.itemIndex,
              nameAvaId: this.getNameAvaId(userData.targetUserName),
              isDefaultSpeaker: false,
              profileImage: userData.profile_image || undefined
            });

            // 处理录音状态下的缓存
            if (this.fromToRecord && isGlobal) {
              const cacheData = _.clone({
                ...userData,
                userId: userData.targetUserId,
                userName: userData.targetUserName,
                userApiName: userData.targetUserApiName,
                nameAvaId: this.getNameAvaId(userData.targetUserName),
                isDefaultSpeaker: false
              });
              this.setSpeakerCache(cacheData);
            }
          }
          break;

        case 'edit': // 编辑用户名操作
          {
            // 从additionalData中提取用户名，或使用目标用户名
            const newUserName = additionalData.userName || userData.targetUserName;

            // 构建用户替换信息
            const userInfo = {
              userName: userData.isDefaultSpeaker ? userData.originalUserName : userData.userName,
              userId: userData.userId,
              activityUserId: userData.activityUserId,
              targetUserId: '',
              targetUserName: newUserName,
              targetUserApiName: '',
              targetNameAvaId: this.getNameAvaId(newUserName)
            };

            // 单条修改时添加文档ID
            if (type === 'single' && userData.id) {
              userInfo.docId = userData.id;
            }

            replaceInfos = [userInfo];

            // 处理录音状态下的缓存
            if (this.fromToRecord && isGlobal) {
              const cacheData = _.clone({
                ...userData,
                userName: newUserName,
                nameAvaId: this.getNameAvaId(newUserName),
                isDefaultSpeaker: false
              });
              this.setSpeakerCache(cacheData);
            }
          }
          break;

        default:
          // console.warn(`未知的操作类型: ${opType}`);
      }

      // 构建最终的请求数据
      const requestData = {
        objectId: this.dataId,
        replaceInfos,
        isGlobal
      };

      // 标记是否需要调用API
      // 当全局操作或者有docId时需要调用API
      const needCallApi = isGlobal || replaceInfos.some(item => !!item.docId);

      

      return { requestData, extendParams, needCallApi };
    },

    /**
     * 统一的接口调用方法
     * @param {Object} data - 请求数据
     * @param {string} type - 操作类型：'edit'|'bind'|'merge'|'refresh'
     * @param {Object} extendData - 额外数据，用于事件触发
     * @returns {Promise}
     */
    async handleContactOperation(data, type = 'edit', extendData = {}) {
      const loadingTarget = this.$refs['activity-corpus-list-wrapper'];
      const loading = this.$loading({
        target: loadingTarget,
        lock: true,
        background: "rgba(255, 255, 255, 0.7)"
      });
      data.opType = type
      const { isGlobal } = extendData;

      try {
        // 单条操作或者replaceInfos中有docId的项时才调用API
        if (data.replaceInfos?.length) {
            // 如果不是全局操作，过滤出有docId的项再调用API
            if (!isGlobal) {
              const replaceInfosWithDocId = data.replaceInfos.some(item => !!item.docId)
              if (replaceInfosWithDocId) {
                await this._bindContact(data);
              }
            } else {
              // 全局操作直接调用API
              await this._bindContact(data);
            }
        }

        if (extendData) {
          // 使用事件管理器触发刷新事件
          if (isGlobal) {
            // 全局修改，使用corpus.list.refresh
            if (this.activityActionState === '1' && type === 'edit') {
              // 对于正在录音状态下的编辑操作
              this.emitRefreshEvent({
                refreshType: 'global',
                ...extendData
              });
            } else {
              // 其他情况，直接刷新或传递完整数据
              if (Object.keys(extendData).length > 1) {
                this.emitRefreshEvent({
                  refreshType: 'global',
                  ...extendData
                });
              } else {
                this.emitRefreshEvent();
              }
            }
          } else {
            // 单条修改，使用corpus.list.refresh带refreshType='single'
            this.emitRefreshEvent({
              refreshType: 'single',
              ...extendData
            });
          }
        }
        return true;
      } catch (error) {
        console.error('Contact operation failed:', error);
        return false;
      } finally {
        loading.close();
      }
    },

    /**
     * 调用绑定联系人API
     * @param {Object} data - 请求数据
     * @returns {Promise}
     */
    _bindContact(data) {
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: `/EM1HNCRM/API/v1/object/activity_text/service/batch_change_speaker`,
          data: data,
          success: (res) => {
            resolve(res);
          },
          error: (err) => {
            reject(err);
          }
        });
      });
    },

    /**
     * 处理绑定联系人确认
     * @param {Object} data - 绑定数据
     * @param {string} type - 修改类型：'single' 或 'global'，默认为 'single'
     */
    async handleBindContactConfirm(data, type = 'single') {
      // 使用格式化方法处理数据
      const { requestData, extendParams } = await this.formatContactOperationData(
        data,
        'bind',
        type
      );

      const result = await this.handleContactOperation(requestData, 'bind', extendParams);

      if (result) {
        // 关闭弹窗
        this.userEditState.bindContactDialogVisible = false;
      }
    },

    /**
     * 处理合并发言人确认
     * @param {Object} data - 合并数据
     */
    async handleMergeSpeakerConfirm(data) {
      // 在调用格式化方法前处理缓存
      if (this.fromToRecord) {
        data.replaceInfos.forEach(item => {
          const cacheData = _.clone(item)
          let extendData = {}
          if (this.hasSpeakerCache(cacheData.userId)) {
            const existCache = this.getSpeakerCache(cacheData.userId)
            extendData = Object.assign(existCache, {
              userName: cacheData.targetUserName,
              nameAvaId: cacheData.targetNameAvaId,
              isDefaultSpeaker: cacheData.isDefaultSpeaker,
              userApiName: cacheData.targetUserApiName,
            })
          }
          this.setSpeakerCache(Object.assign(cacheData, extendData))
        })
      }

      // 获取目标用户信息，用于事件数据
      const targetUser = data.replaceInfos &&
                         data.replaceInfos.length > 0 &&
                         data.targetUser ? data.targetUser :
                         (data.currentSpeaker || {});

      const additionalData = targetUser.userName ? {
        userId: targetUser.userId || targetUser.targetUserId,
        userName: targetUser.userName || targetUser.targetUserName,
        nameAvaId: targetUser.nameAvaId || targetUser.targetNameAvaId,
        userApiName: targetUser.userApiName || targetUser.targetUserApiName
      } : {};

      // 使用格式化方法处理数据
      const { requestData, extendParams } = await this.formatContactOperationData(
        data,
        'merge',
        'global', // 合并操作总是全局的
        additionalData
      );

      const result = await this.handleContactOperation(requestData, 'merge', extendParams);

      if (result) {
        // 关闭弹窗
        this.userEditState.speakerManagementVisible = false;
      }
    },

    /**
     * 处理用户名确认修改
     * @param {Object} item - 用户信息对象
     * @param {number} index - 列表项索引
     * @param {string} type - 修改类型：'single' 或 'global'
     */
    async handleUsernameConfirm(item, index, type = 'single') {
      // 使用局部变量获取用户名，避免多次访问对象属性
      const userName = this.userEditState.editingUsername?.userName;

      // 如果用户名未变或为空，直接返回
      if (!userName || userName === item.userName) {
        this.userEditState.editingUsername = null;
        return;
      }

      // 计算nameAvaId - 使用缓存避免重复计算
      const nameAvaId = this.getNameAvaId(userName);

      // 准备额外数据 - 只包含必要的属性
      const additionalData = {
        userName,
        itemIndex: index,
        nameAvaId,
        isDefaultSpeaker: false,
        userId: item.userId
      };

      try {
        // 使用格式化方法处理数据
        const { requestData, extendParams } = await this.formatContactOperationData(
          item,
          'edit',
          type,
          additionalData
        );

        // 执行操作
        const result = await this.handleContactOperation(requestData, 'edit', extendParams);

        if (result) {
          // 清除编辑状态
          this.userEditState.editingUsername = null;
        }
      } catch (error) {
        console.error('Failed to confirm username edit:', error);
        // 出错时也清除编辑状态，避免界面卡在编辑态
        this.userEditState.editingUsername = null;
      }
    },

    /**
     * 格式化时间显示
     * @param {number} ms - 毫秒数
     * @param {boolean} showMilliseconds - 是否显示毫秒
     * @returns {string} 格式化后的时间字符串
     */
    formatTime(ms, showMilliseconds = true) {

      // 处理无效值
      if (typeof ms !== 'number' || ms < 0) {
          // 如果ms是有效数字且大于0,返回整数部分
          // 否则返回默认值'00:00'
          return !isNaN(ms) || ms ? ms.toString().split('.')[0] : '00:00';
      }

      const milliseconds = ms % 1000;
      const totalSeconds = Math.floor(ms / 1000);
      const seconds = totalSeconds % 60;
      const totalMinutes = Math.floor(totalSeconds / 60);
      const minutes = totalMinutes % 60;
      const hours = Math.floor(totalMinutes / 60);

      const pad = (num, size) => String(num).padStart(size, '0');

      let timeString = '';

      if (hours > 0) {
          timeString += `${pad(hours, 2)}:`;
      }

      timeString += `${pad(minutes, 2)}:${pad(seconds, 2)}`;

      if (showMilliseconds) {
          timeString += `.${pad(milliseconds, 3)}`;
      }

      return timeString;
    },
    getAvatarText(item) {
      const name = item.userName || item.originalUserName || '';
      if (!name) return '';
      const avatarText = name.slice(-2);
      // 如果最后两个字符都是英文字符，则转换为大写
      if (/^[A-Za-z]+$/.test(avatarText)) {
        return avatarText.toUpperCase();
      }
      return avatarText;
    },
    handleDetailScroll() {
      // 更新 speakerManagement popover
      const speakerManagementPopover = this.$refs.speakerManagementRef?.$refs.bindContactPopperRef;
      if (speakerManagementPopover && speakerManagementPopover.showPopper) {
        speakerManagementPopover.updatePopper();
      }
      // 更新 speaker-guide popover
      const speakerGuidePopover = this.$refs.speakerGuidePopoverRef;
      if (speakerGuidePopover && this.showSpeakerGuide) {
        speakerGuidePopover.updatePopper();
      }
    },
    bindDetailScroll() {
      if (this.$context?.getDetailRegionContainer()) {
        this.$context?.getDetailRegionContainer()?.$scroll?.addEventListener('scroll', this.handleDetailScroll);
      }
    },
    destroyDetailScroll() {
      if (this.$context?.getDetailRegionContainer()) {
        this.$context?.getDetailRegionContainer()?.$scroll?.removeEventListener('scroll', this.handleDetailScroll);
      }
    },
    /**
     * 获取用户头像ID
     * @param {string} userName - 用户名
     * @returns {number} 头像ID
     */
    getNameAvaId (userName = '') {
        // 使用缓存优化性能
        if (!this._nameAvaIdCache) {
            this._nameAvaIdCache = new Map();
        }

        // 如果缓存中已有该用户名的ID，直接返回
        if (userName && this._nameAvaIdCache.has(userName)) {
            return this._nameAvaIdCache.get(userName);
        }

        // 直接从 displayItems 中获取用户数据，避免循环依赖
        const uniqueUsers = new Map();
        
        // 收集所有有nameAvaId的用户
        this.displayItems.forEach(item => {
            if (item.userName && (item.nameAvaId || item.nameAvaId === 0)) {
                if (!uniqueUsers.has(item.userName)) {
                    uniqueUsers.set(item.userName, item.nameAvaId);
                }
            }
        });

        let maxId = 0;
        
        if (userName) {
            // 查找是否已存在该用户名
            if (uniqueUsers.has(userName)) {
                const existingId = uniqueUsers.get(userName);
                // 缓存结果并返回
                this._nameAvaIdCache.set(userName, existingId);
                return existingId;
            }
        }

        // 如果有现有用户，计算最大ID
        if (uniqueUsers.size > 0) {
            maxId = Math.max(...Array.from(uniqueUsers.values()));
        }
        
        const newId = maxId + 1;
        
        // 缓存新ID
        if (userName) {
            this._nameAvaIdCache.set(userName, newId);
        }

        return newId;
    },
    removeHighlights() {
      const highlightedElements = this.$el.querySelectorAll('.message-text');
      highlightedElements.forEach(element => {
        element.classList.remove('highlight-message');
      });
    },
    handleDocumentClick(event) {
      // 处理用户名编辑
      if (this.userEditState.editingUsername) {
        const usernameInput = this.$refs?.usernameInputRef?.[0]?.$el;
        // 如果点击的不是输入框本身或其内部元素
        if (usernameInput && !usernameInput.contains(event.target)) {
          this.handleUsernameCancel();
        }
      }

      // 保持原有的高亮消息处理逻辑
      const highlightedElements = this.$el.querySelectorAll('.message-text.highlight-message');
      let clickedHighlight = false;

      highlightedElements.forEach(element => {
        if (element.contains(event.target)) {
          clickedHighlight = true;
        }
      });

      if (!clickedHighlight) {
        this.removeHighlights();
      }
    },
    navigateToMatch(direction) {
      const { currentMatchIndex, searchResults } = this.searchState;

      if (direction === 'next' && !this.canNavigateDown) return;
      if (direction === 'prev' && !this.canNavigateUp) return;

      let nextIndex;

      if (direction === 'next') {
        nextIndex = currentMatchIndex + 1;
      } else {
        nextIndex = currentMatchIndex - 1;
      }

      this.searchState.currentMatchIndex = nextIndex;
      this.scrollToMatch(searchResults[nextIndex]);
    },
    scrollToMatch(match) {
      if (!match || !this.$refs.listScrollRef || this.isScrolling) return;

      this.$nextTick(() => {
        const scrollbar = this.$refs.listScrollRef;
        const messageWrapper = this.$el.querySelector(`.message-wrapper[data-id="${match.item.seq}"]`);

        if (!messageWrapper || !scrollbar.$el) return;

        // 获取滚动容器
        const scrollContainer = scrollbar.$el.querySelector('.el-scrollbar__wrap');
        if (!scrollContainer) return;

        // 计算目标元素的位置
        const containerRect = scrollContainer.getBoundingClientRect();
        const elementRect = messageWrapper.getBoundingClientRect();

        // 计算相对滚动位置
        const relativeTop = elementRect.top - containerRect.top + scrollContainer.scrollTop;

        // 计算居中位置
        const offset = (scrollContainer.clientHeight - elementRect.height) / 2;
        const scrollTop = Math.max(0, relativeTop - offset);

        // 平滑滚动到目标位置
        scrollContainer.scrollTo({
          top: scrollTop,
          behavior: 'smooth'
        });

        // 添加临时高亮效果
        messageWrapper.classList.add('highlight-message');
        setTimeout(() => {
          messageWrapper.classList.remove('highlight-message');
        }, 2000);
      });
    },
    resetSearchState() {
      this.searchState = {
        ...this.searchState,
        keyword: '',
        currentMatchIndex: -1,
        searchResults: [],
        matchCount: 0
      };

      // 清除所有高亮
      document.querySelectorAll('.current-match').forEach(el => {
        el.classList.remove('current-match');
      });

      // 触发一次渲染更新，恢复原始内容结构
      this.$nextTick(() => {
        this.displayItems.forEach((item, index) => {
          if (item.content) {
            // 使用原始内容强制更新显示
            this.$set(this.itemVertions, index, {
              id: item.id,
              version: `_${Date.now()}_${item.content}_${item.translateContent}`
            });
          }
        });
      });
    },
    // 将类绑定逻辑封装为函数
    getItemClass(item) {
      const isCurrentMatch = this.isCurrentMatch(item);
      const isEditing = this.isItemInEditingState(item);

      return {
        'list-item': true,
        'current-match': isCurrentMatch,
        'audio-highlight': this.highlightedItemId === item.id, // 保持音频高亮类，通过CSS控制优先级
        'item-editing': isEditing // 添加编辑状态标识，用于CSS选择器
      };
    },


    // 判断是否为当前匹配项的逻辑
    isCurrentMatch(item) {
      const { searchResults, currentMatchIndex } = this.searchState;

      // 安全检查
      if (!searchResults || !searchResults.length || !item || (!item.id || !item.seq)) {
        return false;
      }

      // 检查当前匹配索引是否有效
      if (currentMatchIndex < 0 || currentMatchIndex >= searchResults.length) {
        return false;
      }

      // 确保当前匹配项存在且有ID
      const currentMatch = searchResults[currentMatchIndex];
      if (!currentMatch || !currentMatch.id || !currentMatch.seq) {
        return false;
      }

      // 比较ID确定是否为当前匹配项
      // 使用id或seq进行比较，这些是唯一标识符，不受内容HTML标签影响
      return currentMatch.id === item.id || currentMatch.seq === item.seq;
    },
    /**
     * 切换发言人管理面板
     * @param {Event} event - 触发事件
     * @param {String} placement - 弹出位置
     */
    toggleSpeakerManagement(event, placement = 'bottom-end') {
      if (this.disableOperation) {
        this.$message.warning(this.$t('sfa.activity.corpus.audio_disable_operation_tip_text'))
        return;
      }
      if (this.$refs.speakerManagementRef) {
        if (this.userEditState.speakerManagementVisible) {
          this.$refs.speakerManagementRef.close();
          this.userEditState.speakerManagementVisible = false;
        } else {
          // 打开 speaker-management 时，隐藏引导
          this.hideSpeakerGuide(false);
          this.$nextTick(() => {
            this.$refs.speakerManagementRef.open(event, placement);
            this.userEditState.speakerManagementVisible = true;
          });
        }
      }
    },
    /**
     * 获取发言人缓存数据
     * @param {string} userId - 用户ID
     * @returns {Object|null} 返回缓存的用户数据，如果未找到则返回null
     */
    getSpeakerCache(userId) {
      try {
        return this.speakerCache[userId] || null;
      } catch (error) {
        // console.error('获取发言人缓存数据失败:', error);
        return null;
      }
    },

    /**
     * 设置发言人缓存数据
     * @param {Object} userData - 用户数据
     * @returns {boolean} 是否设置成功
     */
    setSpeakerCache(userData) {
      try {
        const userId = !!userData.userApiName ? userData.originalUserId : userData.userId;
        this.speakerCache[userId] = {
          ...userData,
          updateTime: new Date().getTime()
        };

        // 使用事件管理器触发缓存更新事件
        if (this.eventManager) {
          this.eventManager.emit(EVENT_NAMES.CORPUS_SPEAKER_CACHE_UPDATED, {
            userId,
            data: this.speakerCache[userId]
          });
        } else {
          // 兼容处理：如果事件管理器未初始化，则使用原始方式触发事件
          this.$context.$emit(EVENT_NAMES.CORPUS_SPEAKER_CACHE_UPDATED, {
            userId,
            data: this.speakerCache[userId]
          });
        }

        return true;
      } catch (error) {
        console.error('设置发言人缓存数据失败:', error);
        return false;
      }
    },

    /**
     * 检查用户是否在缓存中
     * @param {string} userId - 用户ID
     * @returns {boolean} 是否存在于缓存中
     */
    hasSpeakerCache(userId) {
      return !!this.speakerCache[userId];
    },

    /**
     * 触发刷新事件
     * 统一处理事件触发逻辑，使用事件管理器或原始方式
     * @param {Object} payload - 事件数据
     */
    emitRefreshEvent(payload) {
      if (this.eventManager) {
        // 使用事件管理器触发事件
        this.eventManager.emit(EVENT_NAMES.CORPUS_LIST_REFRESH, payload);
      } else {
        // 兼容处理：如果事件管理器未初始化，则使用原始方式触发事件
        this.$context.$emit(EVENT_NAMES.CORPUS_LIST_REFRESH, payload);
      }
    },

    /**
     * 处理发言人绑定事件
     * 当收到corpus.speaker.bind事件时执行此方法
     * @param {Object} data - 绑定数据
     */
    handleSpeakerBind() {
      this.toggleSpeakerManagement({
        target: this.$refs['speakerManagementBtnRef']?.$el
      })
    },

    /**
     * 处理滚动事件
     * 检测是否需要显示"滚动到底部"按钮和"定位当前"按钮
     */
    handleScroll() {
      const scrollbar = this.$refs.listScrollRef;
      if (!scrollbar) return;

      const wrap = scrollbar.$refs.wrap;
      if (!wrap) return;

      // 计算是否需要显示回到底部按钮
      // 当距离底部超过100px时显示按钮
      const isNearBottom = wrap.scrollHeight - wrap.scrollTop - wrap.clientHeight < 100;
      this.showScrollToBottom = !isNearBottom;

      // 防抖处理：检查"定位当前"按钮状态（通过mixin）
      this.debouncedCheckCurrentPosition();
    },

    /**
     * 防抖检查当前位置按钮状态
     */
    debouncedCheckCurrentPosition: _.debounce(function() {
      if (this.highlightedItemId) {
        this.checkAndUpdateButtonState(this.highlightedItemId);
      }
    }, 100), // 100ms防抖

    /**
     * 滚动到底部
     * 该方法优先级最高，会中断其他滚动操作
     */
    scrollToBottom() {
      const scrollbar = this.$refs.listScrollRef;
      if (!scrollbar) return;

      // 标记正在滚动中，阻止其他滚动操作
      this.isScrolling = true;

      // 清除之前的定时器
      if (this.scrollTimer) {
        clearTimeout(this.scrollTimer);
      }

      // 执行滚动
      const wrap = scrollbar.$refs.wrap;
      if (wrap) {
        wrap.scrollTo({
          top: wrap.scrollHeight,
          behavior: 'smooth'
        });
      }

      // 滚动完成后重置状态
      this.scrollTimer = setTimeout(() => {
        this.isScrolling = false;
        this.showScrollToBottom = false;
      }, 500);
    },
    /**
     * 处理内容保存
     * @param {Object} item - 当前项
     * @param {string} content - 编辑后的内容
     * @param {boolean} isTranslate - 是否是翻译内容
     */
    handleContentSave(item, content, isTranslate) {
      const data = [{
        id: item.id,
      }];
      isTranslate ? (data[0].translateContent = content): (data[0].content = content);

      return this._saveContent(data).then((res) => {
        res = res[0] || {};
        // 如果翻译内容为空，则删除该条记录
        if(!isTranslate && _.isEmpty(content)){
          const index = this.displayItems.findIndex(i => i.id === item.id);
          if (index !== -1) {
            this.displayItems.splice(index, 1);
          }
        } else {
          item.content = res.content;
          item.translateContent = res.translateContent;
        }
      });
    },

    hasSavedId(id) {
      return id && (id + '').length > 10;
    },

    /**
     * 保存内容到服务器
     * @param {Object} data - 请求数据
     * @returns {Promise} Promise对象
     */
    _saveContent(data) {
      let flag = true;
      data.forEach(item => {
        if (!item.id || (item.id + '').length < 10) {
          flag = false;
          return;
        }
      });
      if (!flag) {
        this.$message.warning({
          message: $t('crm.activity.corpus.update_content_not_saved'), offset: 300,
          offset: 200,
        });
        return Promise.reject();
      }
      const _data = {
        arg : data,
      }
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: `/EM1HNCRM/API/v1/object/activity_text/service/update_content`,
          data: _data,
          success: (res) => {
            if(res.Result.StatusCode == 0) {
              resolve(res.Value.documentList);
            } else {
              this.$message.error({
                message: res.Result.FailureMessage || $t('保存失败'),
                offset: 200
              });
              reject(res.Result.FailureMessage);
            }
          },
          error: (err) => {
            this.$message.error({
              message: err.message || $t('保存失败'),
              offset: 200
            });
            reject(err);
          },
        }, {
          errorAlertModel: 1,
        });
      });
    },

    initSelectionToolbar() {
      // 创建实例
      const toolbar =  this.selectionToolbar = new SelectionToolbar({
        // 配置划词区域
        selectors: ['.message-text >div'],
        // 配置排除区域
        excludeSelectors: ['.message-header', '.avatar-wrapper'],
        // 配置工具条
        toolbar: {
          // 外显的功能点
          visiblePlugins: ['keynote', 'invalid', 'todo'],
        },
        extraData:{
          detailData: this.detailData,
          apiName: this.apiName,
        }
      });

      // 注册回调函数
      toolbar.eventBus.on('afterPluginAction', this.afterPluginAction);

      // 初始化
      toolbar.init();
    },
    afterPluginAction(result) {
      if (!_.contains(['keynote', 'invalid'], result.plugin.name)) return;
      const data = [];
      // 检查选区内是否包含标记标签
      result.selectionInfo.selectableRanges.forEach(range => {
        const commonAncestor = range.commonAncestorContainer;
        const rootElement = commonAncestor.nodeType === Node.TEXT_NODE ? commonAncestor.parentElement : commonAncestor;
        const textElement = rootElement.closest('.message-text');
        const itemElement = rootElement.closest('.list-item');
        const dataId = itemElement.getAttribute('data-id');
        const _item = {
          id: dataId,
        }

        // 获取内容，保留marktype标签，因为这是用户添加的标记
        let content = '';
        if (textElement.classList.contains('translate-content')) {
          content = rootElement.innerHTML;
          _item.translateContent = content;
        } else {
          content = rootElement.innerHTML;
          _item.content = content;
        }

        data.push(_item);
      });

      this._saveContent(data).then((res) => {
        res = res || [];
        res.forEach((item) => {
          const index = this.displayItems.findIndex(i => i.id === item.id);
          if (index !== -1) {
            this.displayItems[index].content = item.content;
            this.displayItems[index].translateContent = item.translateContent;
          }
        })
      });
    },
    destroySelectionToolbar() {
      this.selectionToolbar.destroy();
    },
    initGuide() {
      // 已经查看过则不再显示引导
      if (CRM.getLocal('activity-guide-hidden')) return;
      // 鼠标停留0.4秒后显示引导
      // $(this.$el).delegate('.list-scroll-container', 'mousemove', _.debounce((e) => {
      // 进入页面15后显示引导
      this.guideTimer = setTimeout(() => {
        if (this.guideVisible) return;
        const rect = $('.list-scroll-container', this.$el)[0].getBoundingClientRect();
        this.guideVisible = true;
        this.guidePos = {
          x: rect.left + rect.width / 2,
          y: rect.top - 130,
        }
        this.guideTimer = null;
      }, 15000);
      // }, 400))
    },
    // 销毁引导的事件代理
    destroyGuide() {
      if (this.guideTimer) {
        clearTimeout(this.guideTimer);
        this.guideTimer = null;
      }
      this.guideVisible = false;
    },
    /**
     * 获取发言人头像图片
     * @param {Object} item - 当前项
     * @returns {string} 头像图片地址
     */
    getAvaterImgSrc (item) {
      return item.profileImage?.length ? item.profileImage[0].signedUrl : ''
    },
    /**
     * 获取发言人的ID
     * @param {Object} item - 当前项
     * @returns {string} 发言人的ID
     */
    getPersonnelId(item) {
      return !!item.userApiName && item.userApiName !== 'ContactObj' ? item.userId : ''
    },
    /**
     * 启动用户名编辑
     * @param {Event} event - 点击事件
     */
    startUsernameEdit(event) {
      const usernameSpan = event.target.closest('.username-wrapper').querySelector('.username');
      if (usernameSpan && usernameSpan._startEdit) {
        usernameSpan._startEdit();
      }
    },



    /**
     * 处理滚动到指定消息
     * @param {Array|String} seqIds - 消息序列ID数组或JSON字符串
     */
    handleScrollToMessage(seqIds) {
      try {
        // 解析seqIds（如果是字符串）
        const ids = typeof seqIds === 'string' ? JSON.parse(seqIds) : seqIds;

        // 移除已有的高亮
        this.removeHighlights();



        // 为所有匹配的消息添加高亮
        ids.forEach(id => {
          const element = this.$el.querySelector(`[data-id="${id}"]`);
          if (element) {
            const messageText = element.querySelector('.message-text');
            if (messageText) {
              messageText.classList.add('highlight-message');
            }
          }
        });

        // 滚动到序列ID最小的元素
        if (ids.length > 0) {
          const minId = Math.min(...ids.map(id => parseInt(id)));
          const targetElement = this.$el.querySelector(`[data-id="${minId}"]`);
          if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
          }
        }
      } catch (error) {
        console.error('Error processing seqIds:', error);
      }
    },
    /**
     * 初始化事件管理器
     * 统一管理所有事件监听
     */
    initEventManager() {
      // 创建事件管理器实例
      this.eventManager = createEventManager(this);

      // 注册事件监听
      this.eventManager.registerEvents({
        // 状态变更事件
        [EVENT_NAMES.ACTIVITY_ACTION_STATE_CHANGE]: (state) => {
          this.activityActionState = state;
        },
  
        // 互动话题挂载事件
        [EVENT_NAMES.ACTIVITY_INTERACTIVE_ISSUES_MOUNTED]: () => {
          if (Object.keys(this.userAvaBgColor).length) {
            this.eventManager.emit(EVENT_NAMES.CORPUS_LIST_UPDATED, this.userAvaBgColor);
          }
        },
  
        // 语料列表滚动事件
        [EVENT_NAMES.CORPUS_LIST_SCROLL_TO]: this.handleScrollToMessage,
  
        // 发言人绑定事件
        [EVENT_NAMES.CORPUS_SPEAKER_BIND]: this.handleSpeakerBind,

        // 音频时间更新事件
        [EVENT_NAMES.CORPUS_AUDIO_TIME_UPDATE]: this.handleAudioTimeUpdate
      });
    },
    /**
     * 检查是否显示发言人引导
     */
    checkToShowSpeakerGuide() {
      // 如果 speaker-management 可见，则不显示引导
      if (this.userEditState.speakerManagementVisible) {
        return;
      }
      // 如果用户在当前实例中已经关闭过，则不再显示
      if (this.guideClosedInThisInstance) {
        return;
      }
      // 必须有未绑定的发言人才显示
      if (!this.hasUnboundSpeakers) {
        return;
      }
      this.showSpeakerGuide = true;
    },

    /**
     * 隐藏发言人引导
     */
    hideSpeakerGuide(markAsClosed = true) {
      this.showSpeakerGuide = false;
      if (markAsClosed) {
        // 标记为在本组件实例中被用户手动关闭
        this.guideClosedInThisInstance = true;
      }
    },

    registerLifecycleHooks() {
      this.$context.tapHook('root.popMiniBefore', 'activity-corpus-list', (next) => {
        // 关闭引导
        if (this.showSpeakerGuide) {
          this.hideSpeakerGuide(false);
        }
        next();
      });
      this.$context.tapHook('root.popNormalBefore', 'activity-corpus-list', (next) => {
        this.checkToShowSpeakerGuide();
        next();
      });
      this.$context.tapHook('root.beforeHide', 'activity-corpus-list', (next) => {
        if (this.showSpeakerGuide) {
          this.hideSpeakerGuide(false);
        }
        next();
      });
    },

    initResizeObserver() {

      const { $right } = this.$context?.getDetailRegionContainer();

      this.resizeObserver = new ResizeObserver(_.throttle(() => {
        // 更新 speaker-guide popover
        const speakerGuidePopover = this.$refs.speakerGuidePopoverRef;
        if (speakerGuidePopover && this.showSpeakerGuide) {
          speakerGuidePopover.updatePopper();
        }
      }, 100));
      if ($right) {
          this.resizeObserver.observe($right);
      }
    }
  },



  mounted () {
    // 初始化事件管理器
    this.initEventManager();

    // 添加点击事件监听器，用于处理高亮移除
    document.addEventListener('click', this.handleDocumentClick);

    this.initResizeObserver();
    this.bindDetailScroll();
    this.initSelectionToolbar();
    this.initGuide();
    this.registerLifecycleHooks();
  },

  beforeDestroy () {
    // 移除所有事件监听
    if (this.eventManager) {
      this.eventManager.removeAll();
      this.eventManager = null;
    }

    if (this.showSpeakerGuide) {
      this.hideSpeakerGuide(false);
    }

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
      this.resizeObserver = null;
    }

    // 移除点击事件监听器
    document.removeEventListener('click', this.handleDocumentClick);

    this.destroyDetailScroll();
    this.destroySelectionToolbar();
    this.destroyGuide();

    // 清理缓存
    if (this._nameAvaIdCache) {
      this._nameAvaIdCache.clear();
      this._nameAvaIdCache = null;
    }
  }
}
</script>

<style lang="less" scoped>
.activity-corpus-list-wrapper {
  flex: 1;
  overflow: hidden;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-right: 5px;
  padding-top: 5px;

  .activity-corpus-list-search-container {
    margin-bottom: 17px;
    display: flex;
    flex-wrap: wrap;
    // gap: 10px;

    .search-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      flex: 1;
      min-width: 0;
      // background-image: linear-gradient(270deg,#acb0fa 0,#98ccfc 49.5%,#c58ef8 100%);
      // padding: 1px;
      border-radius: 6px;
      border: 1px solid var(--color-neutrals05);
      overflow: hidden;

      .activity-corpus-list-search-input {
        // flex: 1;
        // background: #fff;
        // border-radius: 3px;

        /deep/ .el-input__inner {
          // border-color: var(--color-neutrals05);
          border: none;
          height: 26px;
          line-height: 26px;
          padding-right: 80px !important;

          &:hover,
          &:focus {
            border: none;
            box-shadow: none;
          }
        }

        /deep/ .el-input__suffix {
          right: 90px;
          top: -1px;
        }

        &.no-result {
          /deep/ .el-input__suffix {
            right: 5px;
          }
        }
      }

      .search-navigation {
        position: absolute;
        right: 1px;
        top: 1px;
        bottom: 1px;
        display: flex;
        align-items: center;
        padding: 0 8px;
        // background: #fff;
        border-radius: 0 3px 3px 0;

        .match-count {
          font-size: 12px;
          color: var(--color-neutrals11);
          margin-right: 8px;
        }

        .navigation-arrows {
          display: flex;
          flex-direction: row;
          gap: 4px;

          .nav-arrow {
            font-size: 12px;
            color: var(--color-primary);
            cursor: pointer;
            padding: 2px;
            border-radius: 2px;

            &:hover {
              background-color: var(--color-primary-light);
            }

            &.disabled {
              color: var(--color-neutrals08);
              cursor: not-allowed;
              opacity: 0.5;

              &:hover {
                background-color: transparent;
              }
            }
          }
        }
      }
    }

    .operate-btn-wrapper {
      position: relative;
      margin-left: 10px;
      flex-shrink: 0;

      &.has-unbound-speakers {
        position: relative;
        border-radius: 6px;
        padding: 2px; /* 增加光环厚度 */
        background: #fff; /* 按钮背景色 */
        overflow: hidden; /* 隐藏伪元素超出部分 */
        box-shadow: 0 0 5px rgba(160, 51, 255, 0.2); /* 固定的紫色阴影 */

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 200%; /* 放大伪元素以避免旋转时出现边缘 */
          height: 200%;
          background: conic-gradient(
            from 0deg,
            rgba(0, 153, 255, 0.62),
            rgba(160, 51, 255, 0.62),
            rgba(255, 82, 128, 0.62),
            rgba(255, 112, 97, 0.62),
            rgba(0, 153, 255, 0.62) /* 回到起点颜色以实现平滑过渡 */
          );
          transform: translate(-50%, -50%);
          animation: border-spin 1.5s cubic-bezier(0.4, 0, 1, 1) infinite; /* 提升速度 */
        }

        .operate-btn {
          background: #fff;
          border-radius: 4px;
          border: none;
          position: relative; /* 确保按钮在伪元素之上 */
          z-index: 1;
        }

        /deep/ .operate-btn.el-button--primary2:hover,
        /deep/ .operate-btn.el-button--primary2:focus,
        /deep/ .operate-btn.el-button--primary2:active {
          color: var(--color-neutrals19); /* 固定图标颜色 */
          background-color: #fff; /* 固定背景色 */
          border-color: transparent; /* 移除边框 */
        }
        /deep/ .operate-btn.el-button--primary2:focus [class*=fx-icon-]:before,
        /deep/ .operate-btn.el-button--primary2:hover [class*=fx-icon-]:before {
          color: var(--color-neutrals19); /* 固定图标颜色 */
        }
      }

      .operate-btn {
        font-size: 14px;
        background-color: #f2f4fb;
      }
    }

    @keyframes border-spin {
      0% {
        transform: translate(-50%, -50%) rotate(0deg);
      }
      100% {
        transform: translate(-50%, -50%) rotate(360deg);
      }
    }

  }

  .list-empty {
    height: calc(100% - 58px);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  .list-empty-image {
    width: 100%;
    height: 120px;
    background: image-set(url("~@assets/images/nodata2.png") 1x,
    url("~@assets/images/<EMAIL>") 2x) center center no-repeat;
  }
  .list-empty-text {
    margin-top: 24px;
    font-size: 14px;
    color: var(--color-neutrals19);
    text-align: center;
    line-height: 20px;
    :last-child {
      font-size: 12px;
      color: var(--color-neutrals11);
      line-height: 18px;
    }
  }
  .list-scroll-container {
    // height: calc(100% - 58px);
    border-radius: 8px;
    flex: 1;
    margin-bottom: 6px;
    .list-container {
      padding: 3px 0 0 0;
      margin: 0;
      list-style: none;
    }

    .list-item {
      position: relative; // 确保定位准确
      margin-bottom: 16px;
      transition: background-color 0.3s ease;

      &.current-match {
        .message-text {
          background-color: #E6F4FF;
          
          .highlight {
            background-color: var(--color-primary06);
            color: var(--color-primary);
          }
        }
      }

      &.audio-highlight {
        .message-text {
          background-color: #CFE0FF;
          // border-left: 3px solid #368DFF;
          border-radius: 4px;
          padding: 4px 6px;
        }
      }

      // 当语料项同时具有音频高亮和编辑状态时，两种状态可以同时存在
      &.audio-highlight.item-editing {
        .message-text {
          // 保持音频高亮的背景色
          background-color: #CFE0FF;
          
          // 编辑元素在音频高亮背景上的样式调整
          .editable--editing {
            background-color: rgba(255, 255, 255, 0.9) !important; // 半透明白色背景，既能看到编辑态又能看到音频高亮
            border: 2px solid var(--color-special01) !important;
            border-radius: 8px !important;
            padding: 8px 12px !important;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important; // 添加阴影增强层次
          }
        }
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
    .message-wrapper {
      display: flex;
      align-items: flex-start;
      .avatar-wrapper {
        user-select: none;
      }
      .avatar {
        position: absolute;
        top: 0;
        left: 0;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        flex-shrink: 0;
        cursor: pointer;

        .avatar-text {
          color: #fff;
          font-size: 12px;
          transform: scale(0.833);
          transform-origin: center center;
        }
      }
      .avatar-icon::before {
        color: #fff;
      }

      .message-content {
        margin-left: 6px;
        flex: 1;

        .message-header {
          display: flex;
          align-items: center;
          line-height: 24px;
          height: 24px;
          user-select: none;

          .username-hover-wrapper {
            display: flex;
            align-items: center;
            border-radius: 4px;

            &:hover {
              background-color: #F2F4FB;
              padding: 5px 6px;
              .username-edit-icon {
                display: block;
              }
              .username-line {
                display: block;
              }

              .time {
                display: none;
              }
            }

            // 当父容器包含编辑状态的元素时，禁用hover效果
            &.has-editing {
              background-color: transparent !important;
              padding: 0 !important;

              .username-edit-icon {
                display: none !important;
              }
              .username-line {
                display: none !important;
              }
              .time {
                display: none !important;
              }
            }
          }
          .username-input-wrapper {
            display: flex;
            align-items: center;
          }

          .username-input-button {
            margin-left: 4px;
          }
          .el-button--micro {
            padding: 0;
          }
          .el-button+.el-button {
            margin-left: 0;
          }
          .el-button--text {
            color: var(--color-info06);
          }

          .username-hover-wrapper {
            display: flex;
            align-items: center;
            border-radius: 4px;
            padding-right: 50px;
            &:hover {
              background-color: #F2F4FB;
              padding: 5px 6px;

              .username-edit-icon {
                display: block;
              }
              .username-line {
                display: block;
              }

              .time {
                display: none;
              }
            }
          }

          .username-wrapper {
            display: flex;
            align-items: center;
            border-radius: 4px;

            .username {
              color: var(--color-neutrals15);
              font-weight: 500;
              font-size: 12px;
              line-height: 1;

              &.editing {
                background: #fff;
                border: 1px solid #c1c5ce;
                border-radius: 6px;
                padding: 0 8px;
                display: inline-block;
                outline: none;
                height: 24px;
                line-height: 24px;
                box-sizing: border-box;
                width: 100px;
                z-index: 10;
                position: relative;
                white-space: nowrap;
                overflow-x: auto;

                /* 隐藏滚动条但保留滚动功能 - 跨浏览器兼容方案 */
                /* Webkit浏览器 (Chrome, Safari) */
                &::-webkit-scrollbar {
                  height: 0;
                  width: 0;
                  display: none;
                }

                /* Firefox */
                scrollbar-width: none;

                /* IE和Edge */
                -ms-overflow-style: none;

                &:focus {
                  border-color: var(--color-primary06);
                }

                /* 当内容超出可见区域时显示的视觉提示 */
                &.has-overflow {
                  /* 右侧渐变提示，表示有更多内容 */
                  &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    right: 0;
                    width: 12px;
                    height: 100%;
                    background: linear-gradient(to right, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
                    pointer-events: none; /* 确保不会影响鼠标事件 */
                    z-index: 1;
                  }

                  /* 左侧渐变提示，当滚动到中间位置时显示 */
                  &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 12px;
                    height: 100%;
                    background: linear-gradient(to left, rgba(255, 255, 255, 0), rgba(255, 255, 255, 1));
                    pointer-events: none;
                    z-index: 1;
                    opacity: 0; /* 初始不显示 */
                    transition: opacity 0.2s;
                  }

                  /* 当滚动位置不在最左侧时，显示左侧渐变 */
                  &[data-scrolled="true"]::before {
                    opacity: 1;
                  }
                }
              }
            }

            .username-line {
              display: none;
              width: 1px;
              height: 8px;
              background-color: var(--color-neutrals05);
              margin:0 10px;
            }

            .username-edit-icon {
              display: none;
              cursor: pointer;
              font-size: 12px;
            }

            // &.username-hover:hover {
            //   background-color: #F2F4FB;
            //   padding: 6px;

            //   .username-edit-icon {
            //     display: block;
            //   }
            //   .username-line {
            //     display: block;
            //   }

            //   & + .time {
            //     display: none;
            //   }
            // }
          }

          .time {
            color: var(--color-neutrals11);
            font-size: 12px;
            margin-left: 6px;
          }
        }

        .message-text-line {
          width: 100%;
          height: 1px;
          background-color: var(--color-neutrals05);
          margin: 6px 0;
        }

        .message-text {
          margin-top: 2px;
          line-height: 22px;
          color: var(--color-neutrals19);
          font-size: 13px;
          word-break: break-word;
          text-align: justify;
          position: relative;
          cursor: pointer;

          &.translate-content {
            // border-top: 1px solid #e5e9f2;
            // margin-top: 6px;
            // padding-top: 10px; // 调整为 6px + 4px 保持视觉一致性
          }


        }
        .message-text-disabled{
          user-select: none;
          color: #91959e;
          cursor: default;
        }
      }
    }

    &.item-matched {
      .message-text {
        background: rgba(255, 215, 0, 0.05);
      }
    }
    .identifying-wrapper {
      margin-top: 8px;
      background-color: #fff;
      position: sticky;
      bottom: 0;
      z-index: 5;
      .header {
        display: flex;
        align-items: center;
        padding-bottom: 8px;
        padding-top: 8px;
      }
      .avatar {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        flex-shrink: 0;
      }
      .avatar-icon::before {
        color: #fff;
      }
      .user {
        margin-left: 6px;
        display: inline-flex;
        align-items: center;
      }
      .username {
        color: var(--color-neutrals15);
        font-weight: 500;
        font-size: 12px;
        line-height: 1;
      }
      .time {
        color: var(--color-neutrals11);
        font-size: 12px;
        margin-left: 8px;
      }
      .content-inner {
        display: flex;
        align-items: flex-start;
        width: 100%;
        padding: 16px 24px;
        border-radius: 6px;
        box-sizing: border-box;
        min-height: 80px;
        background-color: #8092FB;
        color: #fff;
      }
      .identifying-content {
        font-size: 13px;
        color: #fff;
        line-height: 18px;
        text-align: justify;
        word-break: break-word;
        width: 100%;
        .translate-identifying-content {
          border-top: 1px solid rgba(255, 255, 255, .4);
          padding-top: 6px;
          margin-top: 6px;
        }
      }
      .loading-dots {
        font-size: 13px;
        color: #fff;
      }
    }

    // 添加滚动到底部按钮样式
    .scroll-to-bottom-btn {
      position: absolute;
      right: 16px;
      bottom: 100px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

      &:hover {
        transform: translateY(-2px);
      }

      i {
        color: #fff;
        font-size: 16px;
      }
    }

    // 当前位置按钮样式
    .current-position-btn {
      position: absolute;
      right: 16px;
      bottom: 32px;
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 4px;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      // padding: 0 12px;

      &:hover {
        transform: translateY(-2px);
      }

      i {
        color: #fff;
        font-size: 14px;
        &::before {
          color: #7341DE;
        }
      }

      // span {
      //   color: #fff;
      //   font-size: 12px;
      //   white-space: nowrap;
      // }
    }
  }

  .loading,
  .no-more {
    text-align: center;
    padding: 12px;
    color: #999;
    font-size: 13px;
  }

  /deep/.highlight {
    background-color: var(--color-primary06);
    padding: 2px;
    border-radius: 2px;
    font-weight: 500;
    color: #fff;

    &.current-highlight {
      background-color: #976aeb;
    }
  }
  /deep/.activity-corpus-list-divider {
    display: inline-flex;
    width: 1px;
    height: 10px;
    background: var(--color-neutrals05);
    margin: 0 6px;
  }
  /deep/.activity-text-button-custom {
    line-height: 1;
  }

  .message-text {
    &.highlight-message {
      border-radius: 4px;
      background: #C1D8FF;
      padding: 0 4px;
    }
  }

}
</style>

<style lang="less">
/* 覆盖 popover 默认样式 (如果需要) */
.speaker-guide-popover {
  padding: 10px 12px !important; /* 强制覆盖默认内边距 */
  border: none;
  &.el-popper[x-placement^=top] .popper__arrow {
    border-top-color: transparent !important;
  }
  /* Popover 的内容样式 - 全局 */
  .speaker-guide-content {
    display: flex;
    align-items: flex-start;
    gap: 6px;
    color: #1F2329;
    max-width: 258px;
  }

  .guide-icon {
    width: 18px;
    height: 18px;
    background: url('~@assets/images/activity/unbound_speaker_guide_icon_1.svg') no-repeat center;
    background-size: contain;
  }

  .guide-icon-2 {
    width: 18px;
    height: 18px;
    background: url('~@assets/images/activity/unbound_speaker_guide_icon_2.png') no-repeat center;
    background-size: contain;
  }

  .guide-text {
    flex: 1;
    color: var(--color-neutrals19);
    font-size: 13px;
    font-weight: 700;
  }

  .guide-close {
    color: var(--color-neutrals11);
    cursor: pointer;
    font-size: 12px;
    margin-left: 6px;
    margin-top: 4px;
    &:hover {
      color: var(--color-neutrals11);
    }
  }
}
</style>