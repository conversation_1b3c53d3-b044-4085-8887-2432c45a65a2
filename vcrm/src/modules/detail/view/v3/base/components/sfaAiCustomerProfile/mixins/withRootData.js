import utils from '../utils'
import * as profileService from '../services/profileService'
export default {
    data() {
        return {
            objectApiName: null,
            objectId: null,
            objectData: null,
            objectDescribe: null,
            currentMethodology: null,
            currentMethodologyType: null,
            currentMethodologyInstanceId: null,
            currentDimension: '',
            currentProfileId: null,
            currentFlowProfileId: null,

            methodologiesData: [],
            dimensionsData: [],
            profileData: {},
            flowProfileData: {},
            stageScoreData: {
                stageName: '',
                stageScore: 0,
                stages: [],
            },

            hasMethodologyInstance: false,

            loading: {
                methodologiesData: false,
                dimensionsData: false,
                stageScoreData: false,
                profileData: false,
            }
        }
    },
    computed: {
        profileContext() {
            return {
                currentDimension: this.currentDimension,
                currentProfileId: this.currentProfileId,
                currentMethodology: this.currentMethodology,
                currentMethodologyInstanceId: this.currentMethodologyInstanceId,
                currentMethodologyType: this.currentMethodologyType,
                currentFlowProfileId: this.currentFlowProfileId,
                dimensionsData: this.dimensionsData,
                switchDimensionsData: this.switchDimensionsData,
                profileData: this.profileData,
                hasMethodologyInstance: this.hasMethodologyInstance,
            }
        },
        dataContext() {
            return {
                apiName: this.objectApiName,
                dataId: this.objectId,
                objectId: this.objectId,
                objectApiName: this.objectApiName,
                objectData: this.objectData,
                objectDescribe: this.objectDescribe,
            }
        },
        isC139Methodology() {
            return this.currentMethodology === '683063399c6dd800060bec1c';
        },
        currentSummary() {
            return !this.currentDimension ?
                this.profileData?.summary :
                this.dimensionsData.find(item => item.id === this.currentDimension)?.summary
        },
        isCommonMethodology() {
            return this.currentMethodologyType === 'flow';
        },
        rootDataLoading() {
            return this.loading.profileData
        },
        winRateScoreData() {
            return {
                winRate: this.profileData.integrated_score,
                winRateTrend: this.profileData.integrated_score_change,
            }
        },

        c139RadarData() {
            return this.dimensionsData.reduce((acc, item) => {
                acc[item.shortName] = item.completedTasksCount;
                return acc
            }, {})
        },


        switchDimensionsData() {
            return [
                {label: $t('sfa.aiCustomerProfile.dimensionSummary'), id: '', icon: 'fx-icon-f-obj-app283', iconColor: '#FFCA2B'},
                ...this.dimensionsData
            ]
        },
    },
    created() {
        this.objectApiName = this.apiName;
        this.objectId = this.dataId;
        this.objectData = this.$context.getData();
        this.objectDescribe = this.$context.getDescribe();
        const {methodologyId, methodologyType} = this.compInfo;
        this.currentMethodologyType = methodologyType;
        this.currentMethodology = methodologyType === 'flow' ? null : methodologyId;
        this.fetchDimensions();
        this.fetchProfile();
        this.fetchStageScoreData();
    },
    methods: {
        setLoading(type, value) {
            this.$set(this.loading, type, value);
        },

        fetchMethodologyInstance() {
            profileService.getMethodologyInstance({
                objectApiName: this.objectApiName,
                objectId: this.objectId,
                methodologyType: this.currentMethodologyType,
                methodologyId: this.currentMethodology,
            }).then(res => {
                this.hasMethodologyInstance = res.length > 0;
            })
        },

        async fetchProfile() {
            this.setLoading('profileData', true)
            try {
                const profileData = await profileService.getCustomerProfile({
                    objectApiName: this.objectApiName,
                    objectId: this.objectId,
                    methodologyId: this.currentMethodology,
                    methodologyType: this.currentMethodologyType,
                })
                this.profileData = {
                    ...profileData,
                    last_modified_time: profileData.last_modified_time && CRM.util.formatTime(profileData.last_modified_time, 'time'),
                }
                this.currentProfileId = profileData?._id
                this.currentMethodologyInstanceId = profileData?.methodology_instance_id
                if (!this.currentProfileId) {
                    this.fetchMethodologyInstance();
                }
            } catch (error) {
                console.error(error)
                this.$alert(error)
            } finally {
                this.setLoading('profileData', false)
            }
        },

        async fetchDimensions() {
            this.dimensionsData = []
            this.setLoading('dimensionsData', true)
            try {
                const result = await profileService.getCustomerDimensionScores({
                    objectApiName: this.objectApiName,
                    objectId: this.dataId,
                    methodologyId: this.currentMethodology,
                    methodologyType: this.currentMethodologyType,
                }, {isC139: this.isC139Methodology})
                const {dimensions, nodes} = result;
                // 通用画像取维度、方法论取节点数据
                this.dimensionsData = this.isCommonMethodology ? dimensions : nodes;
            } catch (error) {
                console.error(error)
            } finally {
                this.setLoading('dimensionsData', false)
            }
        },

        async fetchStageScoreData() {
            this.setLoading('stageScoreData', true)
            try {
                const {stageScoreData, flowPorfile} = await profileService.getCustomerStageSource({
                    objectApiName: this.objectApiName,
                    objectId: this.objectId,
                    methodologyId: this.currentMethodology,
                    methodologyType: 'flow',
                })
                this.stageScoreData = stageScoreData;
                this.flowProfileData = flowPorfile;
                this.currentFlowProfileId = flowPorfile?._id;
            } catch (error) {
                console.error(error)
            } finally {
                this.setLoading('stageScoreData', false)
            }
        },
    }
}
