<template>
    <div class="dimension-summary">
        <common-card
            :title="$t('sfa.aiCustomerProfile.cardTitle.dimensionSummary')"
            icon="ai"
            class="dimension-summary-card"
        >
            <common-data-fetcher-status
                :loading="loading"
                :error="error"
                :data="summary"
            >
                <common-text-list-display :list="summary" />
            </common-data-fetcher-status>
        </common-card>
    </div>
</template>

<script>
import {CommonCard, CommonDataFetcherStatus, CommonTextListDisplay} from './index'

export default {
    name: 'ProfileDimensionSummary',
    components: {
        CommonTextListDisplay,
        CommonDataFetcherStatus,
        CommonCard,
    },
    
    props: {
        summary: {
            type: String,
            default: ''
        }
    }
};
</script>

<style lang="less" scoped>
.dimension-summary {
    background: linear-gradient(to top right, #09f 0, #a033ff 50%, #ff5280 75%, #ff7061 100%);
    border-radius: 8px;
    .dimension-summary-card {
        margin: 1px;
        width: calc(100% - 2px);
    }
}
</style> 