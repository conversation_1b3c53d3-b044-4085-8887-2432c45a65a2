<template>
    <div class="crm-acctree-wrapper">
        <div v-show="!showTree && initial" class="crm-acctree-status">
            <div class="crm-acctree-apply">
                <p v-if="status<2" class="crm-acctree-status-p">{{text.notree}}</p>
                <fx-link v-if="status===1" @click="onApply" type="standard">{{text.applytree}}</fx-link>
            </div>
            <p v-if="status===2" class="crm-acctree-status-p">{{text.applying}}</p>
            <div v-show="status===4" class="crm-acctree-multiple">
                <p>{{text.multipletree}}</p>
                <tree-dropdown ref="treeDropdown" :menu="dropMenu" :value="dropMenuValue" @change="onTreeChange"></tree-dropdown>
            </div>
            <div v-if="status===5" class="crm-acctree-apply">
              <p class="crm-acctree-status-p">{{text.samenametree}}</p>
              <fx-link type="standard" @click="openDetail">{{$t('crm.g6tree.samenametree.link')}}</fx-link>
            </div>
        </div>
        <div v-show="showTree && initial" class="crm-acctree-container" ref="treeContainer">
            <div class="crm-acctree-operations">
                <span class="crm-acctree-dropwrapper"></span>
                <span class="crm-acctree-fullscreen" @click="onToggleFull">
                    <i v-if="!fullStatus" class="p-unicode">&#xe62b;</i>
                    <i v-else class="p-unicode">&#xe651;</i>
                </span>
            </div>
            <business-tree
            v-if="showTree"
            ref="businessTree"
            :curId="curTreeId"
            :orgTreeCompInfo="orgTreeCompInfo"
            :apiName="apiName"
            :dataId="dataId"
            :treeObjectApiName="treeObjectApiName"
            :relatedObjectApiName="relatedObjectApiName"
            :relatedObjectDisplayName="relatedObjectDisplayName"
            :relatedFieldsConfig="relatedFieldsConfig"
            :relatedObjectDataId="relatedObjectDataId"
            :relatedObjectDataName="relatedObjectDataName"
            :isShowSearchTree="isShowSearchTree"
            :fullStatus="fullStatus"
            :pTipMap="pTipMap"
            :pActionMap="pActionMap"
            :pApi="api"
            :pluginTooltipConfig="pluginTooltipConfig"
            @resize="onResize"
            ></business-tree>
        </div>
    </div>
</template>

<script>
// window.G6 = require('@common/g6');
// if (!window.G6 && !window.loadG6) {
//     window.loadG6 = true;
//     const script = document.createElement('script');
//     script.src = window.FS_STATIC_PATH + '/vcrm-dist/common/g6.js'; // 请根据实际路径修改
//     script.defer = true; // 确保脚本在文档解析完毕后再执行
//     document.body.appendChild(script);
// }
Fx.getLib('g6');
import api from '@components/businesstree/common/api';
import components from './components/index';
export default {
    props: ['compInfo', 'apiName', 'dataId'],
    components: {
        BusinessTree: () => import('@components/businesstree/businesstree'),
        ...components,
    },
    data() {
        return {
            detailData: this.$context.getData(),
            relatedObjectApiName: 'AccountMainDataObj',
            relatedObjectDisplayName: $t('crm.客户主数据'),
            relatedFieldsConfig: this.compInfo.fieldsConfig,
            status: 0, // 0 - 没权限，1 - 未申请， 2 - 申请中 ，3 - 一棵树， 4 - 多棵树， 5 - 客户同名称，有个创建了客户树
            showTree: false,
            dropMenu: [],
            dropMenuValue: '',
            curTreeId: '',
            fullStatus: false,
            initial: false,
            isShowSearchTree: true,
            treeObjectApiName: "AccountTreeRelationObj",
            // noRelatedTip: $t('crm.g6tree.nomaindata'),
            pTipMap: {
                noRelated: $t('crm.g6tree.nomaindata'),
            },
            pluginTooltipConfig: {
                offsetX: -200,
                offsetY: -10,
            },
            pActionMap: {},
            text: {
                notree: $t('crm.g6tree.notree'),
                applytree: $t('crm.g6tree.applytree'),
                multipletree: $t('crm.g6tree.multipletree'),
                applying: $t('crm.g6tree.apllying'),
                confrmtip: $t('crm.g6tree.apply.confrmtip'),
                comfirmtitle: $t('crm.g6tree.apply.comfirmtitle'),
                samenametree: '',
            },
            linkId: '',
        }
    },
    computed: {
        relatedObjectDataId() {
            // return '62a19293eb331800018ae106';
            switch (this.apiName) {
                case 'AccountMainDataObj':
                    return this.dataId;
                case 'AccountObj':
                    return this.detailData.account_main_data_id;
            }
        },
        relatedObjectDataName() {
            // return '库卡机器人（上海）有限公司';
            switch (this.apiName) {
                case 'AccountMainDataObj':
                    return this.detailData.name;
                case 'AccountObj':
                    return this.detailData.account_main_data_id__r;
            }
        },
    },
    mounted() {
        if(this.compInfo?.api_name === "common_relationship_tree"){
             // 客户数埋点
            this.sendLog('AccountTreeView','pv')
        }
        this.beforeInit();
        this.init();
        this.afterInit();
    },
    methods: {
        beforeInit() {
            this.orgTreeCompInfo = this.$context.getComponents().account_org_distribution;
            this.api = api;
        },
        async init() {
            const res = await this.api.fetchAllTree([this.relatedObjectDataId], this.relatedObjectApiName);
            this.initial = true;
            res.relationDescribeApiName && (this.treeObjectApiName = res.relationDescribeApiName);
            // 没有数据
            if (!res.data || !res.data.length) {
                const pres = await this.getPermission(this.relatedObjectDataName, this.relatedObjectApiName);
                this.status = pres.status;
                this.text.samenametree = pres.message;
                this.linkId = pres.linkId;
            } else if(res.data && res.data.length > 1) {
                this.status = 4;
                this.dropMenu = _.map(res.data, (item) => {
                    return {
                        label: item.root_id__r,
                        maxThreePathLabel: this.formatThreePath(item.tree_path_context, item.tree_path_title),
                        allPathLabel: (item.tree_path_context && item.tree_path_context[item.tree_path_context.length - 1] == '/') ? item.tree_path_context.slice(0, -1) : item.tree_path_context,
                        value: item._id,

                    }
                })
                // 如果缓存了选择的树根，则展示对应的树
                this.dropMenuValue = CRM.getLocal('crmAccountTreeSelected') && CRM.getLocal('crmAccountTreeSelected')[this.dataId];
                if (this.dropMenuValue) {
                    this.onTreeChange(this.dropMenuValue);
                }
            } else if (res.data && res.data.length == 1) {
                this.status = 3;
                this.curTreeId = res.data[0]._id;
                this.showTree = true;
            }
        },

        /**
         * 高亮搜索词
         * @param {string} query 搜索词
         * @param {Array} data 搜索结果
         * @returns
        */
        formatThreePath(tree_path_context='', tree_path_title='') {
            let result = '', maxThreePath = tree_path_title, allPathContent = tree_path_context;
            if(tree_path_title[tree_path_title.length - 1] == '/') {
              maxThreePath = tree_path_title.slice(0, -1);
            }
            if(allPathContent[allPathContent.length - 1] == '/') {
              allPathContent = allPathContent.slice(0, -1);
            }
            const allPathArray = allPathContent.split('/');
            const maxThreePathArray = maxThreePath.split('/');
            const lastPath = maxThreePathArray[maxThreePathArray.length - 1]
            if(allPathArray.length > 3) {
              result = '.../' + maxThreePath.slice(0, maxThreePath.length - lastPath.length);
              result = `${result}<span class="now-path">${$t('当前')}</span>${lastPath}`
            }else if(allPathArray.length == 1){
              result = maxThreePath.trim().replace(/^\/+|\/+$/g, '');
              result = `<span class="now-path">${$t('当前')}</span>` + `${result}`
            }else{
              result = maxThreePath.slice(0, maxThreePath.length - lastPath.length);
              result = `${result}<span class="now-path">${$t('当前')}</span>${lastPath}`
            }
            return result;
        },

        afterInit() {},
        getPermission(name, objectApiName) {
            return this.api.getTreePermission(name, objectApiName);
        },
        doApply(name, objectApiName, treeObjectApiName) {
            return this.api.applyTree(name, objectApiName, treeObjectApiName);
        },
        openDetail() {
          if(this.linkId) {
            CRM.api.show_crm_detail({
                apiName: this.relatedObjectApiName,
                id: this.linkId
            });
          }
        },
        onApply() {
            // 客户树创建埋点
            this.sendLog('AccountTreeCreate','cl')
            this.$confirm(this.text.confrmtip, this.text.comfirmtitle, {
                confirmButtonText: $t('确定'),
                cancelButtonText: $t('取消'),
            }).then(() => {
                this.doApply(this.relatedObjectDataName, this.relatedObjectApiName, this.treeObjectApiName).then(() => {
                    this.status = 2;
                });
            });
        },
        sendLog(operationId,eventType){
              CRM.util.sendLog("AccountObj", "Detail", {
                    operationId,
                    eventType
            })
        },
        onTreeChange(value) {
            if (!this.curTreeId) {
                this.showTree = true;
                $('.crm-acctree-dropwrapper', this.$el).append(this.$refs.treeDropdown.$el);
            }
            this.curTreeId = value;
            const local = CRM.getLocal('crmAccountTreeSelected') || {};
            local[this.dataId] = this.curTreeId;
            CRM.setLocal('crmAccountTreeSelected', local);
        },
        onToggleFull() {
            this.fullStatus = !this.fullStatus;
            if (!this.fullStatus || this.fulldialog) {
                this.$el.appendChild(this.$refs.treeContainer);
                if (this.fulldialog) {
                    this.fulldialog.destroy();
                    this.fulldialog = null;
                }
            }else {
                this.fulldialog = CRM.util.fullDialog({
                    el: this.$refs.treeContainer,
                    className: 'fx-treecontainer-dialog fx-treecontainer-dialog-fullscreen'
                });
            }

            this.onResize(true);
        },
        onResize(isDeliver) {
            if (this.$refs.businessTree) {
                $(this.$refs.treeContainer).height(this.getHeight());
                isDeliver && this.$refs.businessTree.onResize();
            }
        },
        getHeight() {
            return this.fullStatus ? '100%' : 600;
        },
    },
    beforeDestroy() {
        this.$refs.businessTree && this.$refs.businessTree.destroy();
    }
}
</script>

<style lang="less">
    .crm-acctree-wrapper{
        position: relative;
        .crm-acctree-status{
            text-align: center;
            line-height: 26px;
            font-size: 14px;
            padding: 140px 0 40px;
            background: url("~@assets/images/nodata3.png") no-repeat center top;
            background: -webkit-image-set(
                url("~@assets/images/nodata3.png") 1x,
                url("~@assets/images/<EMAIL>") 2x
            )
            no-repeat center top;
        }
        .crm-acctree-apply{

        }
        .crm-acctree-status-p{
            color:#91959E;
            max-width: 350px;
            margin: 0 auto;
        }
        .crm-acctree-multiple{
            p {
                color:#91959E;
            }
        }
    }
    .crm-acctree-container{
        position: relative;
        width: 100%;
        height: 600px;
        .crm-acctree-operations{
            position: absolute;
            right: 20px;
            top: 5px;
            z-index: 1;
        }
        .crm-acctree-fullscreen{
            margin-left: 10px;
            cursor: pointer;
            .p-unicode{
                font-size: 12px;
                color: #737C8C;
            }
        }
        .crm-tree-container {
            .crm-tree-compacttree{
                margin-top: 30px;
                position: relative;
            }
        }
    }
    .el-dropdown-menu__item.is-active {
      .account-tree-drop-item-title, .account-tree-drop-item-path{
        color:var(--color-primary06);
      }
    }
    .account-tree-drop-item{
      .account-tree-drop-item-title{
        font-size: 14px;
        color: #181C25;
      }
      .account-tree-drop-item-path{
        font-size: 12px;
        color: #91959E;
      }
      .now-path{
        display: inline-block;
        height: 20px;
        padding: 1px 4px;
        margin: 0 2px;
        border-radius: 2px;
        border: 1px solid #FFCD94;
        background: #FFF5E6;
        color: #FF7C19
      }
    }

</style>
