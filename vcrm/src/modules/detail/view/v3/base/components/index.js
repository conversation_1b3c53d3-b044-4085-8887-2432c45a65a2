import Attach from './attach/';
import Email from './email/email';
import HeadInfo from './headinfo/';
import ActiveMember from './activemember/';
import Servicelog from './servicelog/service_record_card_layout';
import Hierarchy from './hierarchy/';
import EmailObj from './emailObj';
import MarketingEventInfluence from './marketingeventinfluence/';
import marketingUserActionPanel from './marketingUserActionPanel/marketingUserActionPanel';
import MarketingUserList from './marketingUserList/marketingUserList';
import rfm_analysis from './rfmanalysis/rfmanalysis';
import ContactPersonnelGraph from './contactpersonnelgraph/contactpersonnelgraph';
import SessionArchives from './sessionarchives/sessionarchives';
import SessionArchivesNew from './sessionarchivesnew/sessionarchivesnew';
import QualityInspection from './qualityinspection/qualityinspection';
import AccountTree from './accounttree/accounttree';
import OrgTree from './orgtree/orgtree';
import EquityTree from './equitytree/equitytree';
import TreeChart from './dtreechart/dtreechart';
import RightsTree from './rightstree/rightstree';
import PenetrateTable from './penetrateTable/penetrateTable'
import partnerTree from './partnertree/partnertree';
import partnerOrganizationTree from './partnerOrganizationTree/partnerOrganizationTree';
import FishBoneChart from './fishbonechart/fishbonechart';
import CommonRelationTree from './commonrelationtree/commonrelationtree';
import FollowupBusiness from './followupBusiness/followupBusiness';
import BiBusinessInfo from './biBusinessInfo/biBusinessInfo';
import ShareGPTWidget from './sharegptWidget/index.vue';
import sfaAi from './sfaAi/sfaAi';
import basicBusinessInsights from './basicBusinessInsights/basicBusinessInsights';
import financialReportInsights from './financialReportInsights/financialReportInsights';
import riskInsights from './riskInsights/riskInsights';
import publicOpinionInsights from './publicOpinionInsights/publicOpinionInsights'
import winrate from './winrate/winrate.vue' //ai赢率
import bantinsight from "./bantinsight/bantinsight" // BANT洞察
import markintersum from "./markintersum/markintersum" //营销互动摘要
import transferscore from "./transferscore/transferscore" //转换评分
import salecordsum from "./salecordsum/salecordsum" //销售记录摘要
import C139Model from "./C139Model/AIPage" //C139销售模型
import productintention from "./productintention/productintention" //产品意向
import customerCases from './customerCases/customerCases'; //客户案例
import knowledgeRecommend from './knowledgeRecommend/knowledgeRecommend'; //知识推荐
import productRecommend from './productRecommend/productRecommend'; //产品推荐
// import newBusinessInfo from './newbusinessinfo/newbusinessinfo'; //新业务信息
import newBusinessInfo from './newbusinessinfo/newbusinessinfoWithState.vue'; //新业务信息
import aiInteractiveIssues from './aiInteractiveIssues/aiInteractiveIssuesWithState.vue'; // 互动话题
// import aiInteractiveIssues from './aiInteractiveIssues/aiInteractiveIssues.vue'; // 互动话题
import aiSuggestIssues from './aiSuggestIssues/aiSuggestIssuesWithState.vue'; // 建议话题
// import aiSuggestIssues from './aiSuggestIssues/aiSuggestIssues.vue'; // 建议话题
import AITodo from './aitodo/aiTodoWithState.vue';  //activity-待办
// import EmotionAnalysis from './emotionanalysis/emotionanalysis'; //activity-情感分析
import EmotionAnalysis from './emotionanalysis/emotionanalysisWithState.vue'; //activity-情感分析

import LoyaltyHierarchy from './LoyaltyHierarchy/LoyaltyHierarchy'; //会员层级忠诚度
import sfaAiCustomerProfile from './sfaAiCustomerProfile/sfaAiCustomerProfile.vue'; // 客户画像
import SalesContractOriginal from './SalesContractOriginal/SalesContractOriginal'; // 合同对接外部系统，合同原件组件
import SfaNewAiChat from './sfaNewAiChat/index.js';
import Cantactinsights from './cantactinsights/cantactinsights.vue';



const PublicEmployee = (resolve) => window.seajs.use('icmanage-modules/crmobj-account/usage-analysis', resolve);

export {
    HeadInfo,
    Attach,
    Email,
    ActiveMember,
    Servicelog,
    Hierarchy,
    MarketingEventInfluence,
    PublicEmployee,
    rfm_analysis,
    ContactPersonnelGraph,
    AccountTree,
    OrgTree,
    EquityTree,
    TreeChart,
    RightsTree,
    PenetrateTable,
    partnerTree,
    partnerOrganizationTree,
    FishBoneChart,
    CommonRelationTree,
    ShareGPTWidget,
    basicBusinessInsights,
    financialReportInsights,
    riskInsights,
    publicOpinionInsights,
    winrate,
    bantinsight,
    markintersum,
    transferscore,
    salecordsum,
    C139Model,
    productintention,
    customerCases,
    knowledgeRecommend,
    productRecommend,
    newBusinessInfo,
    aiInteractiveIssues,
    aiSuggestIssues,
    sfaAi,
    AITodo,
    EmotionAnalysis,
    Cantactinsights,
}

export default {
    HeadInfo,
    attach_component: Attach,
    CRMEmail_related_list: Email,
    MailObj_account_id_related_list: EmailObj, // 客户详情的邮箱
    MailObj_account_id_list_related_list: EmailObj, // 客户（多选）详情的邮箱
    MailObj_contact_id_related_list: EmailObj, // 联系人详情的邮箱
    MailObj_contact_id_list_related_list: EmailObj, // 联系人（多选）详情的邮箱
    MailObj_leads_id_related_list: EmailObj, // 线索详情的邮箱
    MailObj_leads_id_list_related_list: EmailObj, // 线索（多选）详情的邮箱
    MailObj_new_opportunity_id_related_list: EmailObj, // 商机详情的邮箱
    MailObj_opportunity_id_list_related_list: EmailObj, // 商机（多选）详情的邮箱
    CampaignMembersObj_leads_id_related_list: ActiveMember, // 市场活动历史（销售线索）
    CampaignMembersObj_account_id_related_list: ActiveMember, // 市场活动历史（客户）
    CampaignMembersObj_contact_id_related_list: ActiveMember, // 市场活动历史（联系人）
    CampaignMembersObj_marketing_event_id_related_list: ActiveMember,  // 活动成员（市场活动）
    ServiceRecordObj_account_obj_id_related_list: Servicelog, // 客户、通话记录
    ServiceRecordObj_contact_obj_id_related_list: Servicelog, // 联系人、通话记录
    ServiceRecordObj_leads_obj_id_related_list: Servicelog, // 线索通话记录
    account_hierarchy_component: Hierarchy,
    marketing_event_path: Hierarchy,
    landing_page_path: Hierarchy,
    partner_path: Hierarchy, // 合作伙伴层级
    MarketingEventInfluenceObj_new_opportunity_id_related_list: MarketingEventInfluence,
    MarketingEventInfluenceObj_sales_order_id_related_list: MarketingEventInfluence,
    new_opportunity_path: Hierarchy,
    contact_path : Hierarchy,
    public_employee_usage_analysis: PublicEmployee, // 合作伙伴&客户详情的互联用户情况
    marketing_user_account: MarketingUserList, // 客户营销用户
    MarketingBehaviorObj_lead_id_related_list: marketingUserActionPanel, // 销售线索营销动态
    MarketingBehaviorObj_account_id_related_list: marketingUserActionPanel, // 客户营销动态
    MarketingBehaviorObj_contact_id_related_list: marketingUserActionPanel, // 联系人营销动态
    rfm_analysis: rfm_analysis,
    contact_member_relationship: ContactPersonnelGraph,
    qywx_conversion: SessionArchives,
    wecom_session_archive: SessionArchivesNew,
    conversion_content: QualityInspection,
    account_relationship_tree: AccountTree,
    account_org_distribution: OrgTree,
    equity_relationship_view: EquityTree,
    RelationshipTreeStructure: TreeChart,
    account_contact_tree_structure: RightsTree,
    penetrate_table_comonent: PenetrateTable,
    partner_rights_map_tree: partnerTree,
    partner_department_structure_tree: partnerOrganizationTree,
    fishbone_diagram_view: FishBoneChart,
    common_relationship_tree: CommonRelationTree,
    sfa_business_in_follow: FollowupBusiness,
    sfa_business_information_module: BiBusinessInfo,
    ShareGPT_widget: ShareGPTWidget,
    crm_assistant_component: sfaAi, // AI助手
    sfa_basic_business_insights: basicBusinessInsights,  // Ai 基本工商洞察
    sfa_financial_report_insights: financialReportInsights,  // Ai 财报洞察
    sfa_risk_insights: riskInsights,  // Ai 风险洞察
    sfa_public_opinion_insights: publicOpinionInsights,  // Ai 舆情洞察
    opportunity_score:winrate,//Ai 赢率预测
    loyalty_member_tree_path:LoyaltyHierarchy,
    sfa_win_first_values_clear:C139Model,//Ai C139销售模型
    sfa_basic_leads_bant_insights:bantinsight,//BANT洞察
    sfa_basic_leads_marketing_summary:markintersum,//营销互动摘要
    sfa_basic_leads_transfer_score:winrate,//转换评分
    sfa_basic_leads_feed_summary:salecordsum,//销售记录摘要
    sfa_basic_leads_product_intention:productintention,//产品意向
    sfa_basic_deal_account_case: customerCases,  // 客户案例
    sfa_basic_knowledge_recommend: knowledgeRecommend,  // 知识推荐
    sfa_basic_sell_product: productRecommend,  // 产品推荐
    sfa_activity_new_business_info: newBusinessInfo, //新业务信息
    sfa_activity_interactive_issues: aiInteractiveIssues, // Ai 互动话题
    sales_contract_original: SalesContractOriginal, // 合同原件
    sfa_activity_suggest_issues: aiSuggestIssues, // Ai 建议话题
    sfa_activity_todo: AITodo, //activity 待办
    sfa_activity_contact_insight:Cantactinsights,
    sfa_activity_emotion_analysis: EmotionAnalysis, //activity 情感分析
    SfaAiProfile: sfaAiCustomerProfile, // 客户画像，匹配type使用驼峰命名
    // AiChatComponent: SfaNewAiChat, // 替换paas的agent组件，目前仅在112打开
}
