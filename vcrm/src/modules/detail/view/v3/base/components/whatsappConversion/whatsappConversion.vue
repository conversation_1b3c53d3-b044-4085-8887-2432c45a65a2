<template>
  <div class="crm-module-whatsapp-conversion">
    <div class="chat-wrapper">
      <whatsapp-chat v-if="chatShow" :type="chatType" :title="chatTitle" :members="chatMembers"
        :images="chatMessage.data" :videos="chatMessage.data" :files="chatMessage.data" :links="chatMessage.data"
        :miniPrograms="chatMessage.data" :audios="chatMessage.data" :message="chatMessage" :searchCount="searchCount"
        :scrollIntoDataId="scrollIntoDataId" :putSearchInput="putSearchInput" :putSearchDate="putSearchDate"
        :disableTab="disableTab"
        @tabClickHandle="queueHandle(arguments, 'tab')" @scrollHandle="queueHandle(arguments, 'scroll')"
        @inputHandle="queueHandle(arguments, 'input')" @navigatorHandle="queueHandle(arguments, 'navigator')"
        @dateHandle="queueHandle(arguments, 'date')" />
    </div>
  </div>
</template>

<script>
import WhatsappChat from "@components/WhatsappChat";
import transfer from "../sessionarchives/transfer";

const { chatListTransfer, membersListTransfer } = transfer;

export default {
  components: {
    WhatsappChat
  },
  props: ['dataId', 'apiName'],
  data() {
    return {
      chatShow: false,
      chatType: -1,
      chatTitle: '',
      chatMembers: [],
      chatMessage: {
        data: [],
        userId: ''
      },
      searchCount: {
        current: 0,
        total: 0
      },
      scrollIntoDataId: '',
      putSearchInput: '',
      putSearchDate: [],
      currentPageNum: 1,
      hasMoreData: true,
      disableTab: ['miniProgram'],
    }
  },
  methods: {
    getMember() {
      const _this = this;
      const objectData = _this.$context.getData();

      CRM.util.waiting(true);

      return new Promise((resolve, reject) => {
        CRM.util.FHHApi(
          {
            url: "/EM1HNCRM/API/v1/object/enterprise_wechat_session/service/getWechatGroupUser",
            data: {
              wechatGroupId: objectData.wechat_group_id,
            },
            success: function (res) {
              if (res.Result.StatusCode === 0) {
                _this.chatMembers = membersListTransfer(res.Value.dataList, objectData.leader_id);
                resolve();
              } else {
                CRM.util.alert(res.Result.FailureMessage);
                CRM.util.waiting(false);
                reject();
              }
            },
            complete: function () {
              CRM.util.waiting(false);
              reject();
            },
          },
          {
            errorAlertModel: 1,
          }
        );
      });


    },
    getChat({ seq, fuzzyWordSeq, isReverse, isFuzzyWord, isFirstPage = false }) {
      const { enterpriseAccount, id } = FS.contacts.getCurrentEmployee() || {}
      const _this = this;
      const objectData = _this.$context.getData();

      let data = {
        ea: enterpriseAccount,
        fsUserId: id,
        objectDataId: _this.dataId,
        objectApiName: _this.apiName,
        pageSize: _this.chatPageSize,
        pageNum: _this.currentPageNum,
        messageType: _this.currentChatType,
        startTime: _this.currentChatDate[0]?.originTime,
        endTime: _this.currentChatDate[1]?.originTime,
      };

      if (isFuzzyWord) {
        data.keyWord = _this.currentChatInput;
        // data.fuzzyWordSeq = fuzzyWordSeq;
      } else {
        // data.seq = seq;
        // data.order = isReverse ? 0 : 1;
      }

      // if (objectData.type === 1 || objectData.type === 2 || objectData.type === 9) {
      //   data.fromId = objectData.owner_plaintext_id__s;
      //   data.toId = objectData.opposite_plaintext_id__s;
      // }

      // if (objectData.type === 3 || objectData.type === 4 || objectData.type === 10) {
      //   data.roomId = objectData.opposite_plaintext_id__s;
      //   data.wechatGroupId = objectData.wechat_group_id;
      // }


      CRM.util.waiting(true);
      return new Promise((resolve, reject) => {

        CRM.util.FHHApi(
          {
            url: "/EM8HMARKETING/whatsapp/getWhatsAppChatMessage",
            data,
            success: function (res) {
              if (res.Result.StatusCode === 0) {
                _this.chatType = _this.apiName === 'WechatGroupObj' ? 3 : 1;
                // _this.chatTitle = objectData.name;
                // const groupUserList = objectData.type === 1 ? [{plaintextId: objectData.owner_plaintext_id__s, name: objectData.owner__l[0].name}, {plaintextId: objectData.opposite_plaintext_id__s, name: objectData.external_contact_name}] : res.Value.groupUserList;
                // _this.chatMessage.data = isReverse ? chatListTransfer(res.Value.dataList, isReverse, groupUserList, _this.currentChatInput).concat(_this.chatMessage.data) : _this.chatMessage.data.concat(chatListTransfer(res.Value.dataList, isReverse, groupUserList, _this.currentChatInput));
                // _this.chatMessage.userId = objectData.owner_plaintext_id__s;
                // if (!res.Value.dataList.length && _this.chatMessage.data.length) {
                //   isReverse ? _this.chatMessage.data[0].isMin = true : _this.chatMessage.data[_this.chatMessage.data.length - 1].isMax = true;
                // }
                // if (isFuzzyWord) {
                //   _this.chatMessageMatch = res.Value.dataListdetail.reverse();
                //   _this.searchCount.total = res.Value.dataListdetail.length;
                // } else if (!_this.currentChatInput) {
                //   _this.chatMessageMatch = [];
                //   _this.searchCount.total = 0;
                // }

                const dataList = res.Value.data.objectDataList || [];
                _this.chatMessageMatch = [];
                _this.searchCount.total = res.Value.data.totalCount;

                // 检查是否还有更多数据
                _this.hasMoreData = dataList.length === _this.chatPageSize;

                // 数据验证：确保dataList是数组且不为空
                if (!Array.isArray(dataList)) {
                  console.warn('WhatsApp Chat: dataList is not an array:', dataList);
                  _this.chatMessage.data = [];
                  _this.chatShow = true;
                  resolve();
                  return;
                }

                // 转换数据结构
                let convertedData = dataList.map((item, index) => {


                  // 解析from字段获取用户ID，确保有默认值
                  const fromUserId = item.from ? item.from.replace('@c.us', '') : '';
                  const whatsAppUserId = item.whatsAppUserId || fromUserId || 'unknown';

                  // 构造messageData
                  const messageData = {
                    msgid: item.messageId || '',
                    action: "send",
                    from: whatsAppUserId,
                    tolist: [item.to || ''],
                    roomid: "",
                    msgtime: item.messageTime || Date.now(),
                    msgtype: 'text'
                  };

              

                  // 根据消息类型添加特定数据
                  // if (item.messageType === 'link') {
                  //   messageData.link = {
                  //     title: "详情",
                  //     description: "",
                  //     link_url: item.content || '',
                  //     image_url: ""
                  //   };
                  // }

                  // 格式化创建时间，确保时间戳有效
                  const create_time = FS.qxUtil.getDateSummaryDesc(CRM.util.moment.unix(parseInt(item.messageTime, 10),true), 1) || '';
                  item.tags = [];
                  if (item.timeOut) item.tags.push($t('vcrm.qualityinspectionruleobj.rule_time_out_text', { time: item.ruleTimeOutText }));
                  if (item.dirty) {
                      item.tags.push($t('ava.object_detail.qualityinspectionruleobj.sensitive_word'));
                      item.highlight_words = [];
                      for (const dirtyWord of item.dirtyWords) {
                          item.highlight_words = item.highlight_words.concat(dirtyWord.split(' '));
                      }
                  }
                  switch (item.messageType) {
                    case 'text':
                      item.type = 'text';
                      item.content = item.content || '';
                      break;
                    case 'weapp':
                      item.type = 'miniProgram';
                      item.content = JSON.parse(item.content);
                      break;
                    case 'document':
                      item.type = 'file';
                      const extMap = {
                        'xlsx': 'xls',
                      }
                      const ext = (item.fileName || '').split('.').pop();
                      item.content = {
                        name: item.fileName || 'Unknown',
                        path: item.fileNPath,
                        ext: extMap[ext] || ext,
                        size: item.size,
                        isWhatsApp: true,
                      };
                      break;
                    case 'video':
                      item.type = 'video';
                      item.content = {
                        path: item.fileNPath,
                        ext: item.fileExt,
                        size: item.size,
                        isWhatsApp: true,
                      };
                      break;
                    case 'audio':
                      item.type = 'audio';
                      item.content = {
                        path: item.fileNPath,
                        ext: item.fileExt,
                        play_length: item.playLength,
                        isWhatsApp: true,
                      };
                      break;
                    case 'image':
                      item.type = 'image';
                      item.content = {
                        name: item.fileName || '',
                        path: item.fileNPath,
                        ext: 'jpg',
                        size: item.size,
                        isWhatsApp: true,
                      };
                      break;
                  }
                  const userId = (item.from || "").replace(/@c.us/, "").replace(/:.*/, "");
                  return {
                    ...item,
                    id: item.messageId || '',
                    fsEa: item.ea || '',
                    messageId: item.messageId || '',
                    seq: index + 1, // 简单递增序列号
                    keyVersion: 4,
                    fromUser: whatsAppUserId,
                    toList: [item.chatId || ''],
                    messageTime: item.messageTime || '',
                    createTime: item.createTime || '',
                    updateTime: item.updateTime || '',
                    messageData: JSON.stringify(messageData),
                    create_time: create_time,
                    npath: item.fileNPath || '',
                    user: {
                      id: userId,
                      name: item.nickName || item.from || 'Unknown' // 确保name不为null
                    },
                  };
                }).filter(item => item !== null); // 过滤掉无效的数据项

                convertedData = convertedData.reverse();

                // 根据isFirstPage参数决定数据拼接方式
                if (isFirstPage) {
                  // 第一页或搜索时，直接替换数据
                  _this.chatMessage.data = convertedData;
                } else {
                  // 加载更多时，追加到现有数据前面
                  _this.chatMessage.data = convertedData.concat(_this.chatMessage.data);
                }

                // 设置消息用户ID（用于区分自己和他人的消息）
                if (!_this.chatMessage.userId && convertedData.length > 0) {
                  _this.chatMessage.userId = convertedData[0].whatsAppUserId;
                }

                // 处理边界标记
                if (!dataList.length && _this.chatMessage.data.length) {
                  _this.chatMessage.data[_this.chatMessage.data.length - 1].isMax = true;
                }

                if (isFuzzyWord) {
                  _this.chatMessageMatch = dataList;
                  _this.searchCount.total = dataList.length;
                } else if (!_this.currentChatInput) {
                  _this.chatMessageMatch = [];
                  _this.searchCount.total = 0;
                }
                _this.chatShow = true;
                resolve();
              } else {
                CRM.util.alert(res.Result.FailureMessage);
                CRM.util.waiting(false);
                reject();
              }
            },
            complete: function () {
              CRM.util.waiting(false);
              reject();
            },
          },
          {
            errorAlertModel: 1,
          }
        );
      });
    },
    queueHandle(args, type) {
      let handle;
      switch (type) {
        case 'tab':
          handle = () => { return this.tabClickHandle(...args) };
          break;
        case 'scroll':
          handle = () => { return this.chatScrollHandle(...args) };
          break;
        case 'input':
          handle = () => { return this.chatInputHandle(...args) };
          break;
        case 'navigator':
          handle = () => { return this.chatNavigatorHandle(...args) };
          break;
        case 'date':
          handle = () => { return this.chatDateHandle(...args) };
          break;
      }
      if (!handle) return;

      const exec = async () => {
        try {
          await handle();
        } catch (e) {
          console.error('handle错误', e);
        }
        this.queue.shift();
        handle.thenFunc && await handle.thenFunc();
      };

      this.queue.length ? (this.queue[this.queue.length - 1].thenFunc = exec) : exec();

      this.queue.push(handle);

    },

    tabClickHandle(name) {
      this.chatMessage.data = [];
      this.currentPageNum = 1; // 重置页码
      this.hasMoreData = true; // 重置更多数据标志

      switch (name) {
        case 'all':
          this.currentChatType = undefined;
          return this.getChat({
            isFirstPage: true
          });
        case 'member':
          return this.getMember();
        case 'miniProgram':
          this.currentChatType = 'weapp';
          return this.getChat({
            isFirstPage: true
          });
        case 'file':
          this.currentChatType = 'document';
          return this.getChat({
            isFirstPage: true
          });
        case 'video':
          this.currentChatType = 'video';
          return this.getChat({
            isFirstPage: true
          });
        case 'audio':
          this.currentChatType = 'voice';
          return this.getChat({
            isFirstPage: true
          });
        case 'image':
          this.currentChatType = 'image';
          return this.getChat({
            isFirstPage: true
          });
      }
    },
    chatScrollHandle(position) {
      if (!this.chatMessage.data.length) return;

      // 只处理滚动到底部的情况，加载下一页
      if (position === 'bottom') {
        
      }

      // 暂时不处理向上滚动加载历史记录的逻辑
      if (position === 'top') {
        // 检查是否还有更多数据
        if (!this.hasMoreData) {
          console.log('没有更多数据了');
          return;
        }

        // 页码+1并加载下一页
        this.currentPageNum++;
        return this.getChat({
          isFirstPage: false // 标记为加载更多，不是第一页
        });
        return;
      }
    },
    async chatInputHandle(val) {
      this.currentChatInput = val;
      this.chatMessage.data = [];
      this.currentPageNum = 1; // 搜索时重置页码为第一页
      this.hasMoreData = true; // 重置更多数据标志

      if (val) {
        await this.getChat({
          isFuzzyWord: true,
          isFirstPage: true
        });
        this.searchCount.current = this.chatMessageMatch.length;
        this.scrollIntoDataId = this.chatMessageMatch[this.searchCount.current - 1]?.messageId || '';
      } else {
        await this.getChat({
          isFirstPage: true
        });
        this.searchCount.current = 0;
      }
    },
    async chatNavigatorHandle(direction) {
      direction === 'previous' ? (this.searchCount.current--) : (this.searchCount.current++);
      this.chatMessage.data = [];
      this.currentPageNum = 1; // 重置页码
      this.hasMoreData = true; // 重置更多数据标志

      await this.getChat({
        fuzzyWordSeq: this.chatMessageMatch[this.searchCount.current - 1].seq,
        isFuzzyWord: true,
        isFirstPage: true
      });
      this.scrollIntoDataId = this.chatMessageMatch[this.searchCount.current - 1]?.messageId || '';
    },
    async chatDateHandle(val) {
      this.currentChatDate = val || [];
      this.chatMessage.data = [];
      this.currentPageNum = 1; // 重置页码为第一页
      this.hasMoreData = true; // 重置更多数据标志

      if (!this.currentChatInput) {
        await this.getChat({
          isFirstPage: true
        });
      } else {
        await this.getChat({
          isFuzzyWord: true,
          isFirstPage: true
        });
        this.searchCount.current = this.chatMessageMatch.length;
        this.scrollIntoDataId = this.chatMessageMatch[this.searchCount.current - 1]?.messageId || '';
      }
    },
  },
  async created() {
    this.currentChatType = undefined;
    this.chatPageSize = 10;
    this.chatMessageMatch = [];
    this.currentChatInput = '';
    this.currentChatDate = [];
    this.queue = [];
    this.currentPageNum = 1; // 初始化页码
    this.hasMoreData = true; // 初始化更多数据标志

    await this.getChat({
      isFirstPage: true
    });

    if (this.$context.getExtendData().extendData?.sessionContent) {
      this.putSearchInput = this.$context.getExtendData().extendData.sessionContent.value;
      this.putSearchDate = this.$context.getExtendData().extendData.sessionContent.searchDate;
    }

  },
  destroyed() {
    CRM.util.waiting(false);
  }
}
</script>

<style lang="less" scoped>
.crm-module-whatsapp-conversion {

  display: flex;

  height: 696px;

  background: var(--color-neutrals01);

  // overflow-x: scroll;
  border-top: 0;

  .chat-wrapper {

    flex: 1;

  }

}
</style>