<template>
  <div class="insight-text">
    <li
      v-for="(item, index) in text"
      :key="index"
      class="text-content"
      :style="keyType === 'bantAllData' ? 'list-style:none' : ''"
    >
      <span class="text-name" v-if="item.name">{{ item.name }}</span>
      {{ item.topics }}
      <fx-popover
        v-if="item.link_data && item.link_data.length"
        placement="bottom-start"
        popper-class="insight-text-settings-popover"
        trigger="click"
        :ref="'popover-' + index"
      >
        <span slot="reference" class="locate-source">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="6"
            height="6"
            viewBox="0 0 6 6"
            fill="none"
          >
            <path
              d="M3.30811 4.66129C4.28108 4.19355 4.78378 3.30645 4.78378 2.79032C4.71892 2.83871 4.58919 2.87097 4.44324 2.87097C3.76216 2.87097 3.30811 2.35484 3.30811 1.69355C3.30811 1.06452 3.77838 0.5 4.57297 0.5C5.38378 0.5 6 1.25806 6 2.24194C6 3.83871 4.94595 4.96774 3.76216 5.5L3.30811 4.66129ZM0 4.66129C0.989189 4.19355 1.49189 3.30645 1.49189 2.79032C1.42703 2.83871 1.2973 2.87097 1.13514 2.87097C0.47027 2.87097 0.0162162 2.35484 0.0162162 1.69355C0.0162162 1.06452 0.47027 0.5 1.28108 0.5C2.09189 0.5 2.70811 1.25806 2.70811 2.24194C2.70811 3.83871 1.65405 4.96774 0.47027 5.5L0 4.66129Z"
              fill="#181C25"
            />
          </svg>
        </span>
        <div class="popover-content">
          <div class="popover-title">{{$t("sfa.crm.participantinsights.view_related_sales_record")}}</div>
          <ul>
            <li
              v-for="link in item.link_data"
              :key="link._id || link.id"
              @click="handleRecordClick(link._id || link.id, index,item)"
              class="record-item"
            >
              <span>{{ link.name }}</span>
              <i class="fx-icon-f-lianjie"></i>
            </li>
          </ul>
        </div>
      </fx-popover>
    </li>
  </div>
</template>

<script>
export default {
  name: "ContentText",
  props: ["text", "keyType"],

  methods: {
    handleRecordClick(id, index, item) {
      // 在这里处理点击事件，例如跳转页面
      console.log("Clicked record with id:", id);
      CRM.api.show_crm_detail({
        type: "ActiveRecordObj",
        data: {
          crmId: id,
          apiName: "ActiveRecordObj",
        },
      });

      // --- 修正后的调试代码 ---
      const popoverRefArray = this.$refs["popover-" + index];
      if (Array.isArray(popoverRefArray) && popoverRefArray.length > 0) {
        const popoverInstance = popoverRefArray[0];

        // 尝试关闭
        if (typeof popoverInstance.doClose === "function") {
          popoverInstance.doClose();
        } else {
          popoverInstance.showPopper = false;
        }
      }
    },
  },
};
</script>
<style>
.insight-text-settings-popover{
  padding: 12px;
}
</style>

<style scoped lang="less">
.insight-text {
  overflow: hidden;
  transition: max-height 0.3s ease;
  &.is-expanded {
    max-height: none;
  }
  .text-content {
    margin-right: 8px;
  }
  .text-name {
    color: var(--color-neutrals19);
    font-size: 13px;
    font-weight: 700;
  }
  .locate-source {
    cursor: pointer;
    font-size: 12px;
    display: inline-block;
    white-space: nowrap;
    width: 14px;
    height: 14px;
    align-items: center;
    text-align: center;
    line-height: 12px;
    gap: 10px;
    border-radius: 22px;
    background: #f5f6f9;
    &:hover {
      opacity: 0.8;
    }
  }
}
.popover-content {
  .popover-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 4px;
  }
  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  .record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 8px;
    cursor: pointer;
    font-size: 13px;
    color: #0C6CFF;
    i {
      margin-left: 8px;
      color: #999;
    }
  }
}
</style>