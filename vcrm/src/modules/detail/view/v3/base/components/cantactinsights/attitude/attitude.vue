<template>
  <div class="attitude-container">
    <div class="attitude-left">
      <div class="attitude-now">
        <span class="attitude-now-label">{{$t("sfa.crm.participantinsights.attitude_now")}}</span>
        <div class="attitude-now-value">
          <div
            class="attitude-icon"
            :style="currentAttitudeDisplay.style"
          ></div>
          <span class="attitude-text" :style="currentAttitudeDisplay.textStyle">{{ currentAttitudeDisplay.label }}</span>
        </div>
      </div>
      <div class="attitude-auto-container" ref="dropdownContainer">
        <div class="attitude-auto">
          <span class="attitude-now-label">{{$t("sfa.crm.participantinsights.attitude_many")}}</span>
          <span class="fx-icon-info" :title="$t('sfa.crm.participantinsights.attitude_from_history_tip')"></span>
          <div class="attitude-now-value">
            <div
              class="attitude-icon"
              :style="comprehensiveAttitudeDisplay.style"
            ></div>
            <span class="attitude-auto-label"  @click.stop="toggleDropdown" :style="comprehensiveAttitudeDisplay.textStyle">{{ comprehensiveAttitudeDisplay.label }}</span>
          </div>
         <span  style="width: 12px;height: 12px;  font-size: 12px;"  @click.stop="toggleDropdown" :class="isDropdownOpen ? 'fx-icon-arrow-up' : 'fx-icon-arrow-down'"></span>
        </div>        
        <div v-if="isDropdownOpen" class="attitude-dropdown">
          <ul>
            <li
              v-for="option in attitudeOptionsForDropdown"
              :key="option.value"
              class="dropdown-item"
              :class="{
                active:
                  option.value ===
                  (objectData && objectData.comprehensive_attitude),
              }"
              @click="selectOption(option)"
            >
              <div class="attitude-icon" :style="option.style"></div>
              <span :style="option.textStyle">{{ option.label }}</span>
            </li>
          </ul>
          <div class="attitude-auto-container-switch" style="margin: 10px auto;text-align: center;">
            <span>{{$t("sfa.web.from_AI")}}</span>
            <fx-switch
            v-model="changeAiValue"
            size="mini"
            @click.native.stop
          ></fx-switch>
          </div>
        </div>
      </div>
    </div>
    <div class="attitude-right">
      <AttitudeChart :lineData="five_records" />
    </div>
  </div>
</template>

<script>
import AttitudeChart from "./attitudeChart.vue";
const ATTITUDE_CONFIG = {
  support: {
    icon: require("@/assets/images/activity/face_support.svg"),
    color: "#F0FFFB",
    textColor: "#36C2B6",
  },
  neutrality: {
    icon: require("@/assets/images/activity/face_neutral.svg"),
    color: "#FFF5E6",
    textColor: "#FF8000",
  },
  oppose: {
    icon: require("@/assets/images/activity/face_oppose.svg"),
    color:'#FFF6F0',
    textColor: "#FF522A",
  },
  '101': {
    icon: require("@/assets/images/activity/face_strongSupport.svg"),
    color: "#F0FFFB",
    textColor: "#36C2B6",
  },
  '102': {
    icon: require("@/assets/images/activity/face_support.svg"),
    textColor:'#368DFF',
    color:'#F0F9FF',
  },
  '103': {
    icon: require("@/assets/images/activity/face_neutral.svg"),
    color: "#FFF5E6",
    textColor: "#FF8000",
  },
  '104': {
    icon: require("@/assets/images/activity/face_oppose.svg"),
    color: "#F5EDFF",
    textColor: "#8F67E5",
  },
  '105': {
    icon: require("@/assets/images/activity/face_strongOppose.svg"),
    color:'#F5EDFF',
    textColor: "#FF522A",
  },
};
export default {
  name: "Attitude",
  props: {
    objectData: {
      type: Object,
      required: true,
    },
    objectFields: {
      type: Object,
      required: true,
    },
  },
  components: {
    AttitudeChart,
  },
  data() {
    return {
      isDropdownOpen: false,
    };
  },
  watch: {
    isDropdownOpen(isOpen) {
      if (isOpen) {
        document.addEventListener("click", this.closeDropdownOnClickOutside);
      } else {
        document.removeEventListener("click", this.closeDropdownOnClickOutside);
      }
    },
  },
  computed: {
    currentAttitudeDisplay() {
      const key =
        (this.objectData && this.objectData.current_attitude) || "support";
        console.log(key);
      const config = ATTITUDE_CONFIG[key] || {};
      const options = 
        (this.objectFields &&
          this.objectFields.current_attitude &&
          this.objectFields.current_attitude.options) ||
        []
      const option = options.find((o) => o.value === key);
      return {
        label: option ? option.label : "",
        style: {
          backgroundImage: `url(${config.icon || ""})`,
        },
        textStyle: {
          color: config.textColor || "",
          backgroundColor: config.color || "",
        },
      };
    },
    comprehensiveAttitudeDisplay() {
      const key =
        (this.objectData && this.objectData.comprehensive_attitude) || "";
      if (!key) return { label: "", style: {} };

      const config = ATTITUDE_CONFIG[key] || {};
      const options =
        (this.objectFields &&
          this.objectFields.comprehensive_attitude &&
          this.objectFields.comprehensive_attitude.options) ||
        [];
      const option = options.find((o) => o.value === key);

      return {
        label: option ? option.label : "",
        style: {
          backgroundImage: `url(${config.icon || ""})`,
        },
         textStyle: {
          color: config.textColor || "",
          backgroundColor: config.color || "",
          padding: '2px 4px',
          borderRadius: '4px',
        },
      };
    },
    attitudeOptionsForDropdown() {
      const options =
        (this.objectFields &&
          this.objectFields.comprehensive_attitude &&
          this.objectFields.comprehensive_attitude.options) ||
        [];
      return options.map((option) => {
        const config = ATTITUDE_CONFIG[option.value] || {};
        return {
          ...option, // includes value and label
          style: {
            backgroundImage: `url(${config.icon || ""})`,
          },
          textStyle: {
            color: config.textColor || "",
            backgroundColor: config.color || "",
          },
        };
      });
    },
    changeAiValue: {
      get() {
        return !!(
          this.objectData && this.objectData.comprehensive_attitude_is_ai_upd
        );
      },
      set(newValue) {
        const _this = this;
         this.$confirm($t('sfa.crm.participantinsights.attitude_ai_upd_tip'), $t('确认'), {
          distinguishCancelAndClose: true,
          confirmButtonText: $t('是'),
          cancelButtonText: $t('否')
        })
          .then(() => {
            _this.$emit("update:ai-upd", newValue);
          })
          .catch(action => {
            
          });
       
      },
    },
    five_records() {
    return this.objectData.five_records || []
    },
  },
  methods: {
    toggleDropdown() {
      this.isDropdownOpen = !this.isDropdownOpen;
    },
    selectOption(option) {
      this.$emit("update:comprehensive-attitude", option.value);
      this.isDropdownOpen = false;
    },
    closeDropdownOnClickOutside(event) {
      if (
        this.$refs.dropdownContainer &&
        !this.$refs.dropdownContainer.contains(event.target)
      ) {
        this.isDropdownOpen = false;
      }
    },
  },
  mounted() {},
  beforeDestroy() {
    document.removeEventListener("click", this.closeDropdownOnClickOutside);
  },
};
</script>

<style lang="less" scoped>
.attitude-container {
  display: flex;
  height: 96px;
  align-items: flex-start;
  gap: 16px;
  flex-shrink: 0;
  align-self: stretch;
  .attitude-left {
    width: 175px;
    height: 96px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    .attitude-now {
      display: flex;
      margin-right: 1px;
      width: 163px;
      padding: 6px;
      align-items: center;
      gap: 8px;
      flex: 1 0 0;
      border-radius: 8px;
      border: 1px solid var(--color-special01);
      .attitude-now-label {
        color: var(--color-neutrals19);
        font-size: var(---13_size, 13px);
        max-width: 52px;
        font-style: normal;
        margin-right: 14px;
        font-weight: 400;
        line-height: var(---13_line-height, 18px); /* 138.462% */
      }
      .attitude-now-value {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px; /* 157.143% */
        .attitude-icon {
          width: 20px;
          height: 20px;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
        }
        .attitude-auto-label {
          padding: 2px 4px;
          border-radius: 4px;
        }
        .attitude-text {
          color: var(--color-primary06);
          font-size: 13px;
          font-style: normal;
          padding: 2px 4px;
          border-radius: 4px;
          font-weight: 700;
          line-height: 18px; /* 138.462% */
        }
      }
    }
    .attitude-auto-container {
      position: relative;
       display: flex;
      width: 163px;
      padding: 6px;
      align-items: center;
      flex: 1 0 0;
      border-radius: 8px;
      border: 1px solid var(--color-special01);
    }
    .attitude-auto {
      display: flex;
       width: 163px;
      align-items: center;
      flex: 1 0 0;
      border-radius: 8px;
      cursor: pointer;
      user-select: none;
      .attitude-now-label {
        margin-right: 4px;
        max-width: 52px;
         color: var(--color-neutrals19);
        font-size: var(---13_size, 13px);
        font-style: normal;
        font-weight: 400;
        line-height: var(---13_line-height, 18px); /* 138.462% */
      }
      .fx-icon-info:before{
        color: #C1C5CE;
      }
      .attitude-now-value {
        display: flex;
        align-items: center;
        gap: 4px;
        color: var(--color-text-p);
        padding: 2px 4px;
        border-radius: 4px;
        font-size: 13px;
        font-style: normal;
        font-weight: 700;
        line-height: 18px; /* 157.143% */
        flex-grow: 1;
        .attitude-icon {
          width: 20px;
          height: 20px;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          flex-shrink: 0;
        }
      }
      .arrow-down {
        width: 16px;
        height: 16px;
        border: 6px solid transparent;
        border-top-color: var(--color-text-s);
        transition: transform 0.3s;
        &.is-open {
          transform: rotate(180deg);
        }
      }
    }
    .attitude-dropdown {
      position: absolute;
      top: 105%;
      left: 25px;
      width: 138px;
      background-color: #fff;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      z-index: 10;
      padding: 4px 0;
      ul {
        list-style: none;
        margin: 0;
        padding: 0;
      }
      .dropdown-item {
        display: flex;
        align-items: center;
        font-size: 13px;
        font-weight: 700;
        margin: 4px 12px;
        padding: 4px 0;
        cursor: pointer;
        gap: 8px;
        &:hover {
          background-color: #F2F4FB;
          border-radius: 8px;
        }
        &.active {
          background-color: #F2F4FB;
          border-radius: 8px;
        }
        .attitude-icon {
          width: 24px;
          height: 24px;
          margin-left: 20px;
          background-size: contain;
          background-position: center;
          background-repeat: no-repeat;
          flex-shrink: 0;
        }
        span {
          width: 39px;
          font-size: 13px;
          text-align: center;
          padding: 2px 4px;
          border-radius: 4px;
          color: var(--color-text-p);
        }
      }
    }
  }
  .attitude-right {
    height: 96px;
    flex: 1;
    min-width: 0;
  }
}
</style>