<template>
  <div>
    <component
      :is="popoverComponent"
      popper-class="activity-bind-contact-dialog"
      width="463"
      :visible-arrow="false"
      ref="bindContactPopperRef"
      :zIndex="popperZIndex"
      :placement="currentPlacement"
      @hide="resetDialogData"
      trigger="manual"
      v-model="dialogVisible"
      :key="popoverKey"
    >
      <div class="bind-contact-header">
        <div class="bind-contact-header-title">{{ $t('sfa.activity.corpus.audio_popover_title') }}</div>
        <div class="bind-contact-header-operation">
          <fx-button size="micro" type="primary" :disabled="selectedUsers.length < 2" plain @click="handleMergeSpeaker">{{ $t('sfa.activity.corpus.audio_popover_merge_toggle_btn') }}</fx-button>
        </div>
        <fx-button size="micro" icon="fx-icon-close" @click="dialogVisible = false" class="popover-icon-close"></fx-button>
      </div>
      <fx-alert
        class="speaker-management-alert"
        :title="$t('sfa.activity.corpus.audio_popover_alert_tip_text')"
        show-icon
        :closable="false"
        v-show="!!userItems.some(item => !item.userApiName)"
      ></fx-alert>
      <div class="bind-contact-content">
        <div class="user-list-wrapper">
          <!-- 发言人列表 -->
          <template v-if="!!userItems.length">
            <div
              v-for="(item, index) in userItems"
              :key="index"
              :class="['user-list-item', {'is-selected': selectedUsers.includes(item)}]"
              @click="handleUserSelect(item)"
            >
              <span class="fx-icon-ok-2 selected-icon" v-if="selectedUsers.includes(item)"></span>
              <sfaAiAvatar 
                :showtopRightBadge="item.userApiName === 'ContactObj'"
                :data="{
                  userName: item.userName || item.originalUserName,
                  backgroundColor: item.avatarBgColor || getAvatarColor(item),
                  isAvaDefault: !!item.isDefaultSpeaker,
                  dataId: item.id,
                  useAvatarImg: !!(item.profileImage && item.profileImage.length),
                  avatarImgSrc: getAvaterImgSrc(item),
                  personnelId: getPersonnelId(item)
                }"
              />
              <fx-tooltip 
                :content="item.userName || item.originalUserName" 
                :open-delay="400"
                placement="top"
              >
                <span class="bind-contact-content-item-name">{{ item.userName || item.originalUserName }}</span>
              </fx-tooltip>
              <fx-button 
                class="bind-contact-content-item-btn" 
                size="micro" 
                type="primary2" 
                @click.stop="handleOpenBindContactDialog(item)"
                :title="!item.userApiName ? $t('sfa.activity.corpus.speaker_popover_bind_btn') : $t('sfa.activity.corpus.speaker_popover_bind_btn1')"
              >
                {{ !item.userApiName ? $t('sfa.activity.corpus.speaker_popover_bind_btn') : $t('sfa.activity.corpus.speaker_popover_bind_btn1') }}
              </fx-button>
            </div>
          </template>
        </div>
      </div>
      <slot name="reference" slot="reference"></slot>
    </component>
  </div>
</template>

<script>

import sfaAiAvatar from '../../sfaAiAvatar/sfaAiAvatar.vue'

export default {
  name: 'SpeakerManagement',
  components: {
    sfaAiAvatar
  },
  props: {
    userItems: {
      type: Array,
      default: () => []
    },
    dataId: {
      type: String,
      default: ''
    },
    getAvatarColor: {
      type: Function,
      required: true,
      default: () => '#FFA142'
    }
  },
  data() {
    return {
      dialogVisible: false,
      selectedUsers: [],
      popperZIndex: FxUI.Utils.getPopupZIndex(),
      currentReferenceEl: null,
      currentPlacement: 'bottom-end',
      popoverKey: 0,
      popoverComponent: 'fx-popover',
      currentSpeaker: {},
      msgboxInstance: null,
      selectValue: ''
    }
  },
  computed: {
    canMerge() {
      return this.selectedUsers.length >= 2;
    }
  },
  methods: {
    /**
     * 注册生命周期钩子
     */
    registerLifecycleHooks() {
      if (!this.$context) return;
      
      // 注册beforeHide钩子
      this.$context.tapHook('root.beforeHide', 'speaker-management', (next) => {
        this.$nextTick(() => {
          // 关闭弹窗
          this.close();
          next();
        });
      });

      // 注册popMiniBefore钩子
      this.$context.tapHook('root.popMiniBefore', 'speaker-management', (next) => {
        // 关闭弹窗
        this.close();
        next();
      });
      
      // 注册beforeRefresh钩子
      this.$context.tapHook('root.beforeRefresh', 'speaker-management', (next) => {
        // 关闭弹窗
        this.close();
        next();
      });
    },
    
    /**
     * 打开弹窗
     * @param {Event} event - 触发事件
     * @param {String} customPlacement - 自定义位置
     */
    open(event, customPlacement) {
      // 关闭当前弹窗
      this.dialogVisible = false;
      
      // 保存当前触发元素
      if (event && event.target) {
        this.currentReferenceEl = event.target;
      }
      
      // 更新placement并重新创建popover
      this.currentPlacement = customPlacement || 'bottom-end';
      this.popoverKey++;
      
      // 确保DOM更新完成后再显示弹窗
      this.$nextTick(() => {
        const popover = this.$refs.bindContactPopperRef;
        if (!popover) return;
        
        popover.referenceElm = this.currentReferenceEl;
        this.dialogVisible = true;
      });
    },
    
    /**
     * 关闭弹窗
     */
    close() {
      this.dialogVisible = false;
      this.currentReferenceEl = null;
    },

    /**
     * 重置对话框数据
     */
    resetDialogData() {
      this.selectedUsers = [];
      this.currentReferenceEl = null;
      this.$emit('reset-dialog');
    },

    /**
     * 处理用户选择
     * @param {Object} item - 用户信息对象
     */
    handleUserSelect(item) {
      const index = this.selectedUsers.findIndex(user => user.userName === item.userName);
      if(index > -1) {
        this.selectedUsers.splice(index, 1);
      } else {
        this.selectedUsers.push(item);
      }
    },

    /**
     * 创建选择器组件
     */
    createSelect() {
      // 重置select值
      this.selectValue = '';
      
      return this.$createElement('fx-select', {
        key: `select-${Date.now()}`, // 每次创建新组件
        props: {
          size: 'mini',
          options: this.selectedUsers,
          'option-value-key': 'userName',
          value: ''
        },
        on: {
          change: value => {
            this.selectValue = value;
            this.currentSpeaker = this.selectedUsers.find(
              speaker => speaker.userName === value
            );
          }
        },
        style: {
          display: 'block',
          marginTop: '16px',
          width: '300px'
        }
      });
    },

    /**
     * 处理合并发言人操作
     */
    handleMergeSpeaker() {
      if (!this.canMerge) return;
      
      // 关闭已存在的对话框
      this.closeMsgbox();
      
      // 重置数据
      this.currentSpeaker = {};
      this.selectValue = '';
      
      // 显示合并对话框
      const h = this.$createElement;
      const speakerStr = this.selectedUsers
        .map(user => `【${user.userName}】`)
        .join(' ');
      
      // 创建select组件
      const select = this.createSelect();
      
      this.msgboxInstance = this.$msgbox({
        title: this.$t('sfa.activity.corpus.audio_popover_merge_toggle_btn'),
        message: h('div', [
          h('p', this.$t('sfa.activity.corpus.audio_popover_merge_operate_tip', { speakerStr })),
          select
        ]),
        showCancelButton: true,
        confirmButtonText: this.$t('sfa.activity.corpus.audio_popover_merge_operate_btn_confirm'),
        cancelButtonText: this.$t('sfa.activity.corpus.audio_popover_merge_operate_btn_cancel'),
        beforeClose: this.handleMergeDialogClose
      });
    },
    
    /**
     * 关闭消息框
     */
    closeMsgbox() {
      if (this.msgboxInstance) {
        try {
          this.msgboxInstance.close();
          this.msgboxInstance = null;
        } catch (e) {
          // console.error('关闭对话框失败', e);
        }
      }
    },
    
    /**
     * 清理select组件
     */
    clearSelect() {
      this.selectValue = '';
      try {
        const selectEl = document.querySelector('.el-message-box__wrapper:last-child .fx-select');
        if (selectEl && selectEl.__vue__) {
          // 重置组件属性
          selectEl.__vue__.value = '';
          
          // 尝试调用清除方法
          if (typeof selectEl.__vue__.handleClear === 'function') {
            selectEl.__vue__.handleClear();
          }
        }
      } catch (e) {
        // console.error('重置select失败', e);
      }
    },

    /**
     * 处理合并对话框关闭
     */
    handleMergeDialogClose(action, instance, done) {
      // 清理select组件
      this.clearSelect();
      
      // 取消操作直接关闭
      if (action !== 'confirm') {
        this.currentSpeaker = {};
        done();
        return;
      }

      // 验证选择
      if (!this.currentSpeaker.userName) {
        return this.$message.error(this.$t('sfa.activity.corpus.audio_popover_merge_err_tip'));
      }

      // 创建合并数据
      const mergeData = {
        replaceInfos: this.selectedUsers.map(speaker => ({
          userApiName: speaker.userApiName,
          userId: speaker.userId,
          userName: speaker.isDefaultSpeaker ? speaker.originalUserName : speaker.userName,
          activityUserId: speaker.activityUserId,
          targetUserId: this.currentSpeaker.userId,
          targetUserName: this.currentSpeaker.isDefaultSpeaker
            ? this.currentSpeaker.originalUserName
            : this.currentSpeaker.userName,
          targetUserApiName: this.currentSpeaker.userApiName,
          targetNameAvaId: this.currentSpeaker.nameAvaId,
          targetActivityUserId: this.currentSpeaker.activityUserId
        }))
      };

      // 触发合并确认事件并清空数据
      this.$emit('merge-confirm', mergeData);
      this.currentSpeaker = {};
      this.selectedUsers = [];
      
      done();
    },

    /**
     * 处理打开绑定联系人
     * @param {Object} item - 用户信息对象
     */
    handleOpenBindContactDialog(item) {
      this.$emit('open-bind-contact', item)
    },

    getAvaterImgSrc (item) {
      return item.profileImage?.length ? item.profileImage[0].signedUrl : ''
    },

    getPersonnelId(item) {
      return !!item.userApiName && item.userApiName !== 'ContactObj' ? item.userId : ''
    }
  },
  mounted() {
    // 注册生命周期钩子
    this.registerLifecycleHooks();
  }
}
</script>

<style lang="less">
.activity-bind-contact-dialog {
  background: linear-gradient(346deg, rgba(247, 228, 255, 0.03) -12.18%, #FFF 17.86%, #FFF 74.03%, rgba(198, 220, 255, 0.08) 122.97%), #FFF;
  box-shadow: 0px 6px 24px 0px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 20px 16px 32px;
  .speaker-management-alert {
    margin-top: 16px;
    padding-top: 8px;
    padding-bottom: 8px;
    .el-alert__title {
      font-size: 12px;
    }
    .el-alert__icon {
      top: 10px;
    }
  }
  .popover-icon-close {
    border: none;
    background: transparent;
    margin-left: 6px;
    &:hover {
      background: transparent;
    }
  }
  .bind-contact-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .bind-contact-header-title {
    font-size: 18px;
    color: var(--color-neutrals19);
    font-weight: 700;
    line-height: 28px;
  }
  .bind-contact-header-operation {
    display: inline-flex;
    align-items: center;
    margin-left: auto;
  }
  .bind-contact-content {
    // background-color: #fff;
    border-radius: 24px;
    // padding: 16px;
    margin-top: 16px;
    min-height: 200px;
  }
  .bind-content-bg-image {
    background-image: url("~@assets/images/activity/contact_dialog_bg_image.png");
    background-size: 100%;
    background-repeat: no-repeat;
    background-position: right bottom;
    height: 72px;
    width: 72px;
    position: absolute;
    right: 0;
    bottom: 0;
  }
  .user-list-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    max-height: 220px;
    overflow-y: auto;
    gap: 8px;
  }
  .user-list-item {
    width: 84px;
    // height: 94px;
    padding: 8px 0;
    background-color: #fff;
    border-radius: 6px;
    box-sizing: border-box;
    border: 1px solid var(--color-neutrals05);
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    
    &.is-selected {
      // background: linear-gradient(180deg, rgba(247, 249, 255, 0.00) 0%, #F7F9FF 100%);
      border: 1px solid var(--color-primary06);
      
      // 选中图标样式
      .selected-icon {
        position: absolute;
        top: -1px;
        right: -1px;
        font-size: 8px;
        font-weight: bold;
        background-image: linear-gradient(45deg, transparent, transparent 50%, var(--color-primary06) 50%);
        width: 20px;
        height: 20px;
        padding-right: 4px;
        padding-top: 4px;
        text-align: right;
        border-radius: 0 6px 0 0;
        
        &:before {
          color: #fff;
        }
      }
    }

    &:hover {
      border-color: var(--color-primary06);
    }
  }
  .avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    flex-shrink: 0;
  }
  .avatar-icon::before {
    color: #fff;
  }
  .bind-contact-content-item-name {
    margin-top: 4px;
    font-size: 12px;
    color: var(--color-neutrals19);
    line-height: 18px;
    max-width: 70px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .bind-contact-content-item-btn {
    margin-top: 12px;
    // height: 20px;
    // line-height: 20px;
    // color: var(--color-neutrals19);
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .activity-corpus-list-divider {
    display: inline-flex;
    width: 1px;
    height: 10px;
    background: var(--color-neutrals05);
    margin: 0 6px;
  }
  .activity-text-button-custom {
    line-height: 1;
  }
  .bind-contact-select-wrapper {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px dashed var(--color-neutrals05);
  }
  .bind-contact-select-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    .expand-wrap {
      display: inline-flex;
      align-items: center;
      color: var(--color-neutrals19);
      font-size: 12px;
      cursor: pointer;
      .label {
        margin-right: 4px;
      }
    }
    .bind-contact-select-text {
      font-size: 12px;
      color: var(--color-neutrals19);
      font-weight: 500;
      line-height: 1;
      display: inline-flex;
      align-items: center;
      .icon {
        margin-right: 4px;
        font-size: 16px;
        &:before {
          color: var(--color-neutrals19);
        }
      }
    }
  }
  .bind-contact-select-list {
    max-height: 180px;
    overflow-y: auto;
  }
  .bind-contact-select-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 4px;
    & + .bind-contact-select-item {
      margin-top: 10px;
    }
    .label {
      font-size: 12px;
      line-height: 18px;
      font-weight: 400;
      width: 100px;
      margin-right: 8px;
      text-align: left;
    }
    .lookup-comp {
      flex: 1;
    }
    .crm-action-field-lookup {
      height: 28px;
    }
  }
}
</style>