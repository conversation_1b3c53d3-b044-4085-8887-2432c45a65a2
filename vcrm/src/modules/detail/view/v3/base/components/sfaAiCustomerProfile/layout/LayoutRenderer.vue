<template>
    <div class="layout-renderer" :class="layout.class" :style="layoutStyle">
        <template v-for="(item, index) in layout.components">
            <!-- 布局容器组件 -->
            <template v-if="isLayoutContainer(item)">
                <layout-renderer
                    :key="getKey(item, index)"
                    :layout="item"
                    :data-source="dataSource"
                    @update:prop="handlePropUpdate"
                />
            </template>
            <!-- 普通组件 -->
            <component
                v-else
                :is="item.name"
                :key="getKey(item, index)"
                v-bind="resolveProps(item)"
                :style="item.style"
                v-on="resolveEvents(item)"
            />
        </template>
    </div>
</template>

<script>
import components from '../components'
export default {
    name: 'LayoutRenderer',
    components,
    props: {
        // 布局配置
        layout: {
            type: Object,
            required: true,
            default: () => ({
                type: 'column',
                class: '',
                components: []
            })
        },
        // 数据源
        dataSource: {
            type: Object,
            required: true
        }
    },
    computed: {
        // 布局样式
        layoutStyle() {
            const { type, style = {} } = this.layout;
            const baseStyle = {
                display: 'flex',
                flexDirection: type === 'row' ? 'row' : 'column'
            };
            // 确保样式对象的键都是有效的CSS属性名
            return this.normalizeStyle({ ...baseStyle, ...style });
        }
    },
    methods: {
        // 获取组件的key
        getKey(item, index) {
            return item.name || `item-${index}`;
        },

        // 判断是否为布局容器
        isLayoutContainer(item) {
            return item.type === 'row' || item.type === 'column' || item.type === 'grid';
        },

        // 规范化样式对象
        normalizeStyle(style) {
            const normalized = {};
            Object.entries(style).forEach(([key, value]) => {
                // 确保样式键是有效的CSS属性名
                const normalizedKey = key.replace(/[A-Z]/g, match => `-${match.toLowerCase()}`);
                normalized[normalizedKey] = value;
            });
            return normalized;
        },

        // 解析组件属性
        resolveProps(item) {
            if (!item.props) return {};
            
            // 处理数组形式的props
            if (Array.isArray(item.props)) {
                return item.props.reduce((acc, prop) => {
                    acc[prop] = this.dataSource[prop];
                    return acc;
                }, {});
            }

            // 处理对象形式的props
            return Object.entries(item.props).reduce((acc, [key, value]) => {
                // 处理.sync修饰符
                const isSync = key.endsWith('.sync');
                const realKey = isSync ? key.slice(0, -5) : key;

                // 处理数据路径
                if (typeof value === 'string') {
                    const pathParts = value.split('.');
                    let currentValue = this.dataSource;
                    pathParts.forEach(part => {
                        currentValue = currentValue?.[part];
                    });
                    acc[realKey] = currentValue;
                } else {
                    acc[realKey] = value;
                }
                return acc;
            }, {});
        },

        // 解析事件处理
        resolveEvents(item) {
            const events = {};
            const { events: itemEvents = {} } = item;

            // 处理配置的事件
            Object.entries(itemEvents).forEach(([event, config]) => {
                if (typeof config === 'string') {
                    events[event] = (...args) => this.dataSource[config]?.(...args);
                    return;
                }

                const { handler, params = {}, modifiers = [] } = config;
                let eventHandler = (...args) => this.dataSource[handler]?.(params, ...args);

                // 应用事件修饰符
                modifiers.forEach(modifier => {
                    const originalHandler = eventHandler;
                    switch (modifier) {
                        case 'stop':
                            eventHandler = (...args) => {
                                args[0]?.stopPropagation();
                                return originalHandler(...args);
                            };
                            break;
                        case 'prevent':
                            eventHandler = (...args) => {
                                args[0]?.preventDefault();
                                return originalHandler(...args);
                            };
                            break;
                    }
                });

                events[event] = eventHandler;
            });

            // 处理.sync修饰符的更新事件
            if (item.props) {
                Object.keys(item.props).forEach(key => {
                    if (key.endsWith('.sync')) {
                        const propName = key.slice(0, -5);
                        events[`update:${propName}`] = (val) => {
                            this.$emit('update:prop', propName, val);
                        };
                    }
                });
            }

            return events;
        },

        // 处理属性更新
        handlePropUpdate(prop, value) {
            this.$emit('update:prop', prop, value);
        }
    }
};
</script>

<style lang="less" scoped>
.layout-renderer {
    width: 100%;
    box-sizing: border-box;
}
</style> 