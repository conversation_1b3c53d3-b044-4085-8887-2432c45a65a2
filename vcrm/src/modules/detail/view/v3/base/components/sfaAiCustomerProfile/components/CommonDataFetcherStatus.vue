<template>
    <div class="data-fetcher-status">
        <!-- 加载状态 -->
        <div v-if="loading" class="data-fetcher-status-loading">
            <slot name="loading">
                <span>{{ $t('sfa.aiCustomerProfile.loading') }}</span>
            </slot>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="data-fetcher-status-error">
            <slot name="error">
                <span>{{ error.message || $t('sfa.aiCustomerProfile.fetchDataFailed') }}</span>
                <!-- <button @click="refetch" class="retry-button">重试</button> -->
            </slot>
        </div>

        <!-- 数据展示 -->
        <div v-else-if="!isEmptyValue(data)" class="data-fetcher-status-content" :class="contentClass">
            <slot></slot>
        </div>

        <!-- 初始或空状态 -->
        <div v-else class="data-fetcher-status-empty">
            <slot name="empty">
                <span>{{ $t('sfa.aiCustomerProfile.noData') }}</span>
            </slot>
        </div>
    </div>
</template>

<script>
export default {
    name: 'DataFetcherStatus',
    props: {
        loading: {
            type: Boolean,
            default: false
        },
        error: {
            type: Object,
            default: null
        },
        data: {
            type: [Array, Object, String, Number, Boolean],
            default: null
        },
        contentClass: {
            type: String,
            default: ''
        }
    },
    methods: {
        refetch() {
            this.$emit('refetch');
        },
        isEmptyValue(value) {
            return CRM.util.isEmptyValue(value);
        }
    }
}
</script>


<style lang="less" scoped>
.data-fetcher-status {
    position: relative;
    height: 100%;

    &-content {
        height: 100%;
    }

    &-loading,
    &-error,
    &-empty {
        display: flex;
        justify-content: center;
        align-items: center;
        color: var(--color-neutrals11);
        height: 100%;
    }

    &-error {
        color: var(--color-danger06);
        justify-content: space-between;

        .retry-button {
            margin-left: 10px;
            padding: 2px 8px;
            border: 1px solid var(--color-neutrals06);
            background: var(--color-neutrals01);
            border-radius: 3px;
            cursor: pointer;
            
            &:hover {
                border-color: var(--color-neutrals08);
            }
        }
    }
}
</style>
