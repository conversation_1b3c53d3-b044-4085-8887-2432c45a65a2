<template>
    <fx-dialog
        v-if="dialogVisible"
        :visible.sync="dialogVisible"
        :title="title"
        size="small"
        noBodyPadding
        noHeaderBorderBottom
        custom-class="common-source-description-dialog"
    >
        <div class="common-source-description-content">
            <div
                v-for="(item, index) in descriptionList"
                :key="index"
                class="description-item"
            >
                <span class="description-item-name">{{ item[0] }}</span>
                <span class="description-item-desc">{{ item[1] }}</span>
            </div>
            <p class="description-footer-tip" v-if="otherDescription">{{ otherDescription }}</p>
        </div>
    </fx-dialog>
</template>

<script>
export default {
    name: 'CommonSourceDescriptionDialog',
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: $t('sfa.aiCustomerProfile.descriptionTitle')
        },
        descriptionList: {
            type: Array,
            default: () => []
        },
        otherDescription: {
            type: String,
            default: ''
        }
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible
            },
            set(value) {
                this.$emit('update:visible', value)
            }
        }
    }
}
</script>

<style lang="less">
.common-source-description-dialog {
    border-radius: 12px;
    background: linear-gradient(to top right, #FBF4FF 0%, #FFF 18.53%, #FFF 75.61%, #F3F8FF 98.4%), #FFF;
    .el-dialog__header {
        padding: 16px;
        padding-bottom: 0;
    }
    .common-source-description-content {
        padding: 16px;
        font-size: 13px;
        line-height: 22px;
        color: #181C25;
        display: flex;
        flex-direction: column;
        gap: 16px;
        .description-item {
            .description-item-name {
                font-weight: 700;
            }
            .description-item-desc {
                font-weight: 350;
            }
        }
        .description-footer-tip {
            font-size: 12px;
            color: #91959E;
        }
    }
}
</style>