/**
 * 事件管理器
 * 用于统一管理组件中的事件监听
 * @Author: <PERSON>
 */

// 事件名称常量
export const EVENT_NAMES = {
  // 活动状态变更事件
  ACTIVITY_ACTION_STATE_CHANGE: 'activity.actionState.change.after',
  // 交互问题挂载事件
  ACTIVITY_INTERACTIVE_ISSUES_MOUNTED: 'activity.interactive.issues.mounted',
  // 语料列表滚动事件
  CORPUS_LIST_SCROLL_TO: 'corpus.list.scroll.to',
  // 语料列表更新事件
  CORPUS_LIST_UPDATED: 'corpus.list.updated',
  // 语料列表刷新事件
  CORPUS_LIST_REFRESH: 'corpus.list.refresh',
  // 发言人缓存更新事件
  CORPUS_SPEAKER_CACHE_UPDATED: 'corpus.speaker.cache.updated',
  // 发言人绑定事件
  CORPUS_SPEAKER_BIND: 'corpus.speaker.bind',
  // 发言人绑定完成事件
  CORPUS_SPEAKER_BIND_COMPLETE: 'corpus.speaker.bind.complete',
  // 语料用户列表更新事件
  CORPUS_USER_LIST_UPDATED: 'corpus.user.list.updated',
  // 语料音频时间更新事件
  CORPUS_AUDIO_TIME_UPDATE: 'corpus.audio.time.update',
  // 语料音频跳转事件
  CORPUS_AUDIO_JUMP_TO: 'corpus.audio.jump.to'
};

/**
 * 事件管理器类
 * 用于统一管理组件中的事件监听和触发
 */
export class EventManager {
  /**
   * 构造函数
   * @param {Object} component - 组件实例，用于访问组件方法和属性
   */
  constructor(component) {
    this.component = component;
    this.context = component.$context; // 直接从组件中获取上下文
    this.listeners = new Map(); // 存储所有注册的事件监听器
  }

  /**
   * 注册事件监听
   * @param {string} eventName - 事件名称
   * @param {Function} handler - 事件处理函数
   * @param {boolean} bind - 是否绑定组件实例，默认为true
   * @returns {EventManager} 返回实例本身，支持链式调用
   */
  on(eventName, handler, bind = true) {
    // 如果需要绑定组件实例，则使用bind方法
    const boundHandler = bind ? handler.bind(this.component) : handler;

    // 保存原始处理函数和绑定后的处理函数的映射关系
    this.listeners.set(handler, {
      eventName,
      boundHandler
    });

    // 注册事件监听
    this.context.$on(eventName, boundHandler);

    return this;
  }

  /**
   * 移除事件监听
   * @param {string} eventName - 事件名称
   * @param {Function} handler - 事件处理函数
   * @returns {EventManager} 返回实例本身，支持链式调用
   */
  off(eventName, handler) {
    const listenerInfo = this.listeners.get(handler);

    if (listenerInfo && listenerInfo.eventName === eventName) {
      if (typeof this.context?.off === 'function') {
        // 移除事件监听
        this.context.off(eventName, listenerInfo.boundHandler);
      }
      // 从映射中删除
      this.listeners.delete(handler);
    }

    return this;
  }

  /**
   * 触发事件
   * @param {string} eventName - 事件名称
   * @param {any} payload - 事件数据
   * @returns {EventManager} 返回实例本身，支持链式调用
   */
  emit(eventName, payload) {
    this.context.$emit(eventName, payload);
    return this;
  }

  /**
   * 移除所有事件监听
   * @returns {EventManager} 返回实例本身，支持链式调用
   */
  removeAll() {
    // 遍历所有监听器并通过off方法移除
    this.listeners.forEach((listenerInfo, handler) => {
      this.off(listenerInfo.eventName, handler);
    });

    return this;
  }

  /**
   * 批量注册事件监听
   * @param {Object} eventMap - 事件映射对象，格式为 {eventName: handler}
   * @returns {EventManager} 返回实例本身，支持链式调用
   */
  registerEvents(eventMap) {
    Object.entries(eventMap).forEach(([eventName, handler]) => {
      this.on(eventName, handler);
    });

    return this;
  }
}

/**
 * 创建事件管理器实例
 * @param {Object} component - 组件实例
 * @returns {EventManager} 事件管理器实例
 */
export function createEventManager(component) {
  return new EventManager(component);
}

export default {
  EVENT_NAMES,
  EventManager,
  createEventManager
};
