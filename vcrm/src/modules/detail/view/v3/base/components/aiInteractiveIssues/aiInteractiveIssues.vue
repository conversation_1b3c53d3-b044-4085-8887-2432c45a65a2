<template>
  <fx-scrollbar class="sfa-ai-interactive-container" ref="sfa-interactive-container">
    <!-- 仅在开发环境显示的测试按钮 -->
    <!-- <div class="mock-test-buttons">
        <div class="mock-test-button" @click="mockPollingEvent">
            <span>模拟数据更新</span>
        </div>
    </div> -->
    <sfa-ai-question-types
      :dataList="questionTypesData"
      @changeQuestionType="changeQuestionType"
      :selectedType.sync="tagsType"
      :selectedMaskType.sync="markType"
      :tagsTypeList="tagsTypeList"
      :markTypeList="markTypeList"
      :userAvatarList="userAvatarList"
      :selectedUserAvatar.sync="selectedUserAvatar"
    />
    <div class="sfa-ai-interactive-bottom">
      <sfa-ai-unanswer-questions
        :questionType="questionType"
        :dataId="dataId"
        :apiName="apiName"
        :dataList="unanswerQuestionsData"
        :unaskedNum="unaskedNum"
        :relatedObjectOptions="relatedObjectOptions"
        :relatedObjectName="relatedObjectName"
        v-if="unanswerQuestionsData.length"
        @toggle-expand="handleUnansweredQuestionsToggle"
        @component-loaded="handleUnansweredQuestionsToggle"
        @change-related-object="handleRelatedObjectChange"
      />
      <!-- <sfa-ai-question-search
        @search="handleSearch"
        @navigate="handleSearchNavigate"
        ref="searchComponent"
      /> -->
      <ai-interactive-refresh-section
        :has-data="hasData"
        :show-auto-fetch-suggest="showAutoFetchSuggest"
        :ai-suggestion-switch="aiSuggestionSwitch"
        :is-expanded="isExpanded"
        @refresh-all="refreshAll"
        @toggle-auto-fetch="openAutoFetchSuggest"
        @toggle-expand="toggleExpand"
        @refresh-topics="handleRefreshTopics"
        :interactive-processes="interactiveProcesses"
        :has-new-topic="hasNewTopic"
      />
      <sfa-ai-infinite-list
        ref="infiniteList"
        :dataId="dataId"
        :apiName="apiName"
        :noDataListText="$t('sfa.ai.infinite.interactive.list.nodata')"
        :questionType="questionType"
        :loadMore="loadMore"
        :listLoading="listLoading"
        :limit="limit"
        :offset="offset"
        :noMore="noMore"
        :dataList="dataList"
        :isLoading="isLoading"
        :isExpanded="isExpanded"
        :hasNewTopic="hasNewTopic"
        :newAddedItemIds="newAddedItemIds"
        :containerSelector="'.sfa-ai-interactive-container'"
        :interactiveProcesses="interactiveProcesses"
        @refresh-topics="handleRefreshTopics"
        @hide-new-topic-notification="hideNewTopicNotification"
      >
      <template v-slot:item-content="{ questionData, showDialog, index, isExpanded }">
        <sfa-ai-question-item
          :key="`item-${questionData._id}`"
          :fxTagType="questionData.fxTagType"
          :questionData="questionData"
          @refresh-itemKnowledge="handleListExpand"
          @trigger-dialog="showDialog"
          :dataId="dataId"
          :itemIndex="index"
          :apiName="apiName"
          :isExpanded="isExpanded"
          :userAvaBgColor="userAvaBgColor"
          :keyword="searchKeyword"
          :tagsTypeList="tagsTypeList"
          @match-found="handleMatchFound"
          @set-mark="handleSetMark"
        ></sfa-ai-question-item>
        </template>
      </sfa-ai-infinite-list>
    </div>
  </fx-scrollbar>
</template>
  <script>
  import SfaAiInfiniteList from "../sfaAiInfiniteList/sfaAiInfiniteList.vue";
  import SfaAiQuestionTypes from "../sfaAiQuestionTypes/sfaAiQuestionTypes.vue";
  import SfaAiUnanswerQuestions from "../sfaAiUnanswerQuestions/sfaAiUnanswerQuestions.vue";
  import SfaAiQuestionItem from "../sfaAiQuestionItem/sfaAiQuestionItem.vue";
  import SfaAiQuestionSearch from "../sfaAiQuestionSearch/sfaAiQuestionSearch.vue";
  import setHeightMixin from '../sfaAiMixin/sfaAiActivityHeightMixin';
  import dataMixin from './mixins/dataMixin';
  import timerMixin from './mixins/timerMixin';
  import eventMixin from './mixins/eventMixin';
  import { fetchQuestionTypesData, fetchSuggestListData, refreshAll, setMarkType, setAiAutoSwitch, fetchRelatedObjectList } from './services/apiService';
  import { QUESTION_TYPES, PAGE_SIZE, REFRESH_INTERVAL, FETCH_INTERACTIVE_TYPES } from './constants/constants';
  import AiInteractiveRefreshSection from './components/aiInteractiveRefreshSection.vue';
  import corpusUserNameI18n from '../sfaAiMixin/corpusUserNameI18n'

  export default {
    name: 'SfaAiInteractiveIssues',
    props: ["compInfo", "apiName", "dataId"],
    components: {
      SfaAiInfiniteList,
      SfaAiQuestionTypes,
      SfaAiUnanswerQuestions,
      SfaAiQuestionItem,
      SfaAiQuestionSearch,
      AiInteractiveRefreshSection,
    },
    mixins: [setHeightMixin, dataMixin, timerMixin, eventMixin, corpusUserNameI18n],
    data() {
      return {
        questionTypesData: QUESTION_TYPES.map(type => ({
          ...type,
          text: this.$t(type.textKey),
          tip: type.tipKey ? this.$t(type.tipKey) : undefined
        })),
        unanswerQuestionsData: [],
        questionType: 'all',
        tagsType: ['all'],
        markType: ['all'],
        tagsTypeList: [],
        markTypeList: [],
        aiSuggestionSwitch: true,
        listLoading: true,
        isLoading: false,
        limit: PAGE_SIZE,
        offset: -PAGE_SIZE,
        noMore: false,
        dataList: [],
        showAutoFetchSuggest: this.getActivityActionState(),
        unaskedNum: 0,
        isExpanded: true,
        userAvaBgColor: {},
        searchKeyword: '',
        firstMatchFound: false,
        matchedElements: [],
        currentMatchIndex: -1,
        hasNewTopic: false,
        newAddedItemIds: [],
        autoMockInterval: null,
        tagsTypeDebounceTimer: null,
        isDebouncing: false,
        interactiveProcesses: this.$context.getData()?.interactive_processes == '1' && this.$context.getData()?.interactive_processes != '501',
        refreshTimer: null,
        userAvatarList: [],
        selectedUserAvatar: ['all'],
        relatedObjectList: [],
        relatedObjectOptions: [],
        relatedObjectName: ''
      };
    },
    computed: {
      hasData() {
        return this.dataList.length > 0;
      },

      showRefreshSection() {
        return this.hasData || this.showAutoFetchSuggest;
      }
    },
    mounted() {
        this.init();
        if($('.grid-container .grid-cell .fx-frame-layout-tabs .sfa-ai-interactive-container')) {
            $('.grid-container .grid-cell .fx-frame-layout-tabs .sfa-ai-interactive-container').css('paddingLeft', '6px')
        }
        if (this.interactiveProcesses) {
            this.setupRefreshTimer();
        }
    },
    created() {
        let me = this;
        FS.MEDIATOR.on('polling.activity_interactive_issues_refresh_all.change', async (data, mode) => {
            console.log(data, mode, 'polling----activity_interactive_issues_refresh_all');
            me.silentRefreshData(FETCH_INTERACTIVE_TYPES.POLLING_UPDATE);
            me.fetchQuestionTypesData();
            await me.fetchRelatedObjectList();
            await me.fetchSuggestListData();

            // 销毁当前定时器并重新开始计时
            if (me.interactiveProcesses) {
                if (me.refreshTimer) {
                    clearInterval(me.refreshTimer);
                    me.refreshTimer = null;
                }
                me.setupRefreshTimer();
            }
        });
        this.$context.$on('corpus.list.updated', (userAvaBgColor) => {
            console.log('userAvaBgColor:', userAvaBgColor);
            me.userAvaBgColor = userAvaBgColor;
            me.updateUserAvatarListFromAvaBgColor(userAvaBgColor);
        });
        if(!Object.keys(me.userAvaBgColor).length) {
            this.$context.$emit('activity.interactive.issues.mounted')
        }
    },
    methods: {
        getActivityActionState() {
            return this.$context.getData()?.interactive_processes == '1';
        },
      async init() {
        await this.fetchRelatedObjectList();
        this.fetchQuestionTypesData();
        this.fetchSuggestListData();
        this.loadMore(FETCH_INTERACTIVE_TYPES.INITIAL_LOAD);
      },
      async fetchRelatedObjectList() {
        try {
          const relatedObjectList = await fetchRelatedObjectList(this.apiName, this.$context.getData());
          console.log(relatedObjectList, 'relatedObjectList');
          this.handleRelatedObjectList(relatedObjectList);
        } catch (error) {
          console.error('Failed to fetch related object list:', error);
        }
      },
      async fetchQuestionTypesData() {
        try {
          const result = await fetchQuestionTypesData(this.apiName, this.dataId);
          this.aiSuggestionSwitch = result.aiSuggestionSwitch;
          if(result?.dataList?.length) {
            let dataObj = {};
            result.dataList.forEach(item => {
              dataObj[item.type] = item.num;
            });
            this.questionTypesData.forEach(itemData => {
              itemData.num = dataObj[itemData.type];
            });
          }
          if(result?.objectDescribe?.fields?.tags?.options) {
            this.tagsTypeList = result.objectDescribe.fields.tags.options;
            this.tagsTypeList.unshift({
              label: $t('sfa.ai.activity.list.filter.allType'),
              value: 'all'
            });
          }
          if(result?.objectDescribe?.fields?.mark?.options) {
            this.markTypeList = result.objectDescribe.fields.mark.options;
            this.markTypeList.unshift({
              label: $t('sfa.ai.activity.list.filter.allMark'),
              value: 'all'
            });
          }
          if(result?.activityUserList?.length) {
            this.userAvatarList = result.activityUserList.map(item => {
                return {
                    userName: this.getUserNameI18n(item.user_name),
                    dataId: item._id,
                    backgroundColor: item.avatar_bg_color || 'rgb(255, 161, 66)',
                    isAvaDefault: item.is_default_speaker == '1',
                    useAvatarImg: !!item.profile_image,
                    // personnelId: item.personnel_id,
                    avatarImgSrc: item.profile_image && item.profile_image[0] && item.profile_image[0].signedUrl,
                };
            });
            this.userAvatarList.unshift({
              label: $t('sfa.ai.activity.list.filter.allAvatar'),
              value: 'all'
            });
          }
        } catch (error) {
          console.error('Failed to fetch question types:', error);
        }
      },
      async fetchActivityUserList() {
        try {
          const result = await fetchQuestionTypesData(this.apiName, this.dataId);
          if(result?.activityUserList?.length) {
            this.userAvatarList = result.activityUserList.map(item => {
                return {
                    userName: this.getUserNameI18n(item.user_name),
                    dataId: item._id,
                    backgroundColor: item.avatar_bg_color,
                    isAvaDefault: item.is_default_speaker == '1',
                    useAvatarImg: !!item.profile_image,
                    avatarImgSrc: item.profile_image && item.profile_image[0] && item.profile_image[0].signedUrl,
                };
            });
            this.userAvatarList.unshift({
              label: $t('sfa.ai.activity.list.filter.allAvatar'),
              value: 'all'
            });
          }
        } catch (error) {
          console.error('Failed to fetch question types:', error);
        }
      },
      async fetchSuggestListData() {
        try {
          const detailData = this.$context.getData();
          if(!this.relatedObjectName) {
            return;
          }
          const result = await fetchSuggestListData(this.apiName, this.dataId, detailData, this.relatedObjectName);
          if(result?.dataList?.length) {
            let unaskedNum = 0;
            this.unanswerQuestionsData = result.dataList.map(item => {
                if(item.advice_status != 'not_asked') {
                    unaskedNum += 1;
                }
                let questionType, questionTypeLabel;
                if(item.advice_status == 'clearly_defined') {
                    questionType = 'success'
                }else if(item.advice_status == 'unclearly_defined') {
                    questionType = 'link'
                } else {
                    questionType = 'warning'
                }
                const option = result.objectDescribe?.fields?.advice_status?.options?.find(option => option.value == item.advice_status);
                if(option) {
                    questionTypeLabel = option.label;
                }
                return {
                    id: item._id,
                    questionType,
                    questionTypeLabel,
                    questionNumber: item.order_field,
                    text: item.library_id__r || item.question_content__r,
                };
            });
            this.unaskedNum = unaskedNum;
          }
        } catch (error) {
          console.error('Failed to fetch suggest list:', error);
        }
      },
      async refreshAll() {
        try {
          await this.$confirm(
            $t('sfa.ai.issues.refresh.all.content'),
            $t('sfa.ai.issues.refresh.all.title'),
            {
              confirmButtonText: $t('确定'),
              cancelButtonText: $t('取消'),
              type: 'warning'
            }
          );

          const result = await refreshAll(this.apiName, this.dataId);
          this.hasNewTopic = false;

          this.silentRefreshData(FETCH_INTERACTIVE_TYPES.MANUAL_REFRESH);

          FxUI.Message({
            message: $t('sfa.ai.interactive.issue.refresh.tip'),
            type: 'success',
            duration: 5000,
            offset: 100
          });
        } catch (error) {
          if (error !== 'cancel') {
            console.error('Failed to refresh all:', error);
            CRM.util.alert(error.message);
          }
        }
      },
      async openAutoFetchSuggest() {
        let title = this.aiSuggestionSwitch ?
          $t('sfa.ai.issues.auto.refresh.title1') :
          $t('sfa.ai.issues.auto.refresh.title2');
        let tipContent = this.aiSuggestionSwitch ?
          $t('sfa.ai.issues.auto.refresh.content1') :
          $t('sfa.ai.issues.auto.refresh.content2');

        try {
          await this.$confirm(tipContent, title, {
            confirmButtonText: $t('确定'),
            cancelButtonText: $t('取消'),
            type: 'warning'
          });

          const result = await setAiAutoSwitch(this.dataId, !this.aiSuggestionSwitch);
          if (result && result.aiSuggestionSwitch !== undefined) {
            this.aiSuggestionSwitch = result.aiSuggestionSwitch;
          }
        } catch (error) {
          if (error !== 'cancel') {
            console.error('Failed to toggle auto fetch:', error);
          }
        }
      },
      changeQuestionType(newType) {
        this.questionType = newType;
        this.listLoading = true;
        this.limit = PAGE_SIZE;
        this.offset = -PAGE_SIZE;
        this.dataList = [];
        this.noMore = false;
        this.loadMore(FETCH_INTERACTIVE_TYPES.TOPIC_TYPE_CHANGE);
      },
      pollingRefreshAllData() {
        this.questionType = 'all';
        this.limit = PAGE_SIZE;
        this.offset = -PAGE_SIZE;
        this.dataList = [];
        this.noMore = false;
        this.fetchQuestionTypesData();

        this.loadMore(FETCH_INTERACTIVE_TYPES.TOPIC_TYPE_CHANGE);
      },
      toggleExpand() {
        this.isExpanded = !this.isExpanded;
        this.$nextTick(() => {
            if (this.$refs.infiniteList) {
                this.$refs.infiniteList.updateContainerHeight();
                setTimeout(() => {
                    this.$refs.infiniteList.updateContainerHeight();
                }, 350);
            }
        });
      },
      handleListExpand() {
        this.isExpanded = true;
      },
      handleSetMark({itemData, type, index, hideMaskLoading}) {
        console.log(itemData, type, index, 'handleSetMark');
        setMarkType(itemData, type)
          .then(res => {
            if (res.data) {
              this.handleDataItem(res.data, this.objectDescribe);
              this.$set(this.dataList, index-1, { ...res.data });
            }
            hideMaskLoading();
          })
          .catch(() => {
            hideMaskLoading();
          });
      },
      handleSearch({ keyword, results }) {
        this.searchKeyword = keyword;
        this.firstMatchFound = false;
        this.matchedElements = [];
        this.currentMatchIndex = -1;

        if (!keyword) {
          this.$refs.searchComponent.updateMatchCount(0);
        }
      },
      handleMatchFound(el) {
        if (!this.matchedElements.includes(el)) {
          this.matchedElements.push(el);
          this.$refs.searchComponent.updateMatchCount(this.matchedElements.length);

          if (!this.firstMatchFound) {
            this.firstMatchFound = true;
            this.currentMatchIndex = 0;
            this.scrollToElement(el);
          }
        }
      },
      handleSearchNavigate({ currentIndex }) {
        if (currentIndex >= 0 && currentIndex < this.matchedElements.length) {
          this.currentMatchIndex = currentIndex;
          this.scrollToElement(this.matchedElements[currentIndex]);
        }
      },
      scrollToElement(el) {
        if (el) {
          this.$nextTick(() => {
            const listContainer = this.$refs.infiniteList.$el.querySelector('.list-container');
            if (!listContainer) return;

            const containerRect = listContainer.getBoundingClientRect();
            const elementRect = el.getBoundingClientRect();

            const relativeTop = elementRect.top - containerRect.top;
            const containerHeight = containerRect.height;

            listContainer.scrollTop = listContainer.scrollTop + relativeTop - (containerHeight / 2) + (elementRect.height / 2);
          });
        }
      },
      handleUnansweredQuestionsToggle(isExpanded) {
        this.$nextTick(() => {
          if (this.$refs.infiniteList) {
            this.$refs.infiniteList.updateContainerHeight();
            setTimeout(() => {
              this.$refs.infiniteList.updateContainerHeight();
            }, 350);
          }
        });
      },
      /**
       * 根据 userAvaBgColor 同步 userAvatarList 的头像、名字等信息
       * @param {Object} userAvaBgColor
       */
      updateUserAvatarListFromAvaBgColor(userAvaBgColor) {
        if (!Array.isArray(this.userAvatarList) || !this.userAvatarList.length || !userAvaBgColor) return;
        // 先构建 activityUserId => value 的 Map
        const avaMap = {};
        for (const [key, value] of Object.entries(userAvaBgColor)) {
          if (value.activityUserId) {
            avaMap[value.activityUserId] = value;
          }
        }
        let hasChanged = false;
        this.userAvatarList.forEach((item, index) => {
          if (item.value === 'all') return;
          const value = avaMap[item.dataId];
          if (value) {
            let newItem = {...item};
            if (
                value.profileImage && !item.useAvatarImg ||
                !value.profileImage && item.useAvatarImg ||
                (value.profileImage && value.profileImage[0] && item.useAvatarImg && JSON.stringify(value.avatarImgSrc) !== JSON.stringify(value.profileImage[0].signedUrl))
            ) {
                hasChanged = true;
                newItem.useAvatarImg = !!value.profileImage;
                newItem.avatarImgSrc = value.profileImage && value.profileImage[0] && value.profileImage[0].signedUrl;
            }
            if (value.avaColor !== item.backgroundColor) {
                hasChanged = true;
                newItem.backgroundColor = value.avaColor;
            }
            if (value.userName !== item.userName) {
                hasChanged = true;
                newItem.userName = this.getUserNameI18n(value.userName);
            }
            if (value.isAvaDefault !== item.isAvaDefault) {
                hasChanged = true;
                newItem.isAvaDefault = value.isAvaDefault ;
            }
            if(hasChanged) {
                // this.userAvatarList.splice(index, 1, {...item, ...newItem});
                this.$set(this.userAvatarList, index, newItem);
            }
          }

        });
        // if (hasChanged) {
        //     this.fetchActivityUserList();
        // }
      },
      handleRelatedObjectChange(relatedObjectName) {
        this.relatedObjectName = relatedObjectName;
        // 当关联对象改变时，重新获取建议问题数据
        this.fetchSuggestListData();
      },
    },
    watch: {
      tagsType: {
        handler() {
          this.debounceLoadMore();
        }
      },
      markType: {
        handler() {
          this.debounceLoadMore();
        }
      },
      selectedUserAvatar: {
        handler() {
          this.debounceLoadMore();
        }
      }
    },
    beforeDestroy() {
      this.clearAllTimers();
      FS.MEDIATOR.off('polling.activity_interactive_issues_refresh_all.change');
    }
  };
  </script>

  <style lang="less">
    .sfa-ai-interactive-container{
      position: relative;
      // height: 100%;
      overflow-y: auto;
      font-family: -apple-system, Roboto, Source Han Sans CN, Microsoft YaHei, Microsoft YaHei UI, Arial, sans-serif;
      .mock-test-buttons{
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 1000;
        .mock-test-button{
            padding: 4px 8px;
        }
      }
      .el-scrollbar__bar{
          display: none;
      }
      .selector-label-box{
        .fx-select{
            width: 80px;
        }
        /deep/  .el-input__inner{
            border: none;
            color: var(--color-neutrals19);
            font-weight: 400;
            line-height: 20px;
            font-size: 13px;
        }
        }

      .sfa-ai-interactive-bottom{
        padding: 12px;
        background-color: #f7f9fa;
        border-radius: 8px;
      }
        .fxeditor-render-doc p{
            margin-top: 0px;
            margin-bottom: 0px;
        }
        .refresh-fetch-ai-suggest{
            margin-bottom: 6px;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 10px;
            align-self: stretch;
            height: 18px;
            .refresh-all{
                color: #000;
                text-align: center;
                font-size: 12px;
                font-weight: 400;
                line-height: 18px;
                cursor: pointer;
            }
            .refresh-all-icon{
                width: 12px;
                height: 12px;
                position: relative;
                top: 2px;
                margin-right: 4px;
            }
            .auto-fetch-button{
                color: var(--color-neutrals15);
                text-align: center;
                font-size: 12px;
                font-weight: 400;
                line-height: 18px;
                margin-left: 10px;
                .auto-fetch-text{
                    margin-left: 4px;
                }
            }
            .expand-collapse-list{
                cursor: pointer;
                .fx-icon-list-expand{
                    position: relative;
                    top: 0px;
                }
                .fx-icon-list-collapse{
                    position: relative;
                    top: 0px;
                }
            }
        }
        .sfa-ai-infinite-list-container .list-container .item-box {
            // min-height: 80px;
            margin-bottom: 10px;

            // 新项目高亮效果
            &.highlight-new-item {
                animation: highlight-pulse 1.5s ease-in-out;
                position: relative;
                z-index: 1;
            }
        }

        @keyframes highlight-pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(24, 144, 255, 0.4);
                background-color: rgba(24, 144, 255, 0.1);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(24, 144, 255, 0);
                background-color: rgba(24, 144, 255, 0.05);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(24, 144, 255, 0);
                background-color: transparent;
            }
        }
    }
  </style>
