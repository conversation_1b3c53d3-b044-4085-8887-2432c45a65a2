<template>
    <div class="text-list-display">
        <!-- 主显示区域 -->
        <div 
            ref="contentRef" 
            class="content-wrapper"
            :class="{ 'is-collapsed': isCollapsed && shouldCollapse }"
            :style="{ gap: listItemGap, ...collapsedStyle }"
        >
            <div 
                v-for="(item, index) in displayList" 
                :key="index"
                class="content-item"
            >
                <!-- <span v-if="showIndex" class="item-index">{{ index + 1 }}.</span> -->
                <slot name="content-item" :item="item" :index="index">
                    <span class="item-content" v-html="parseDisplayText(item, index)"></span>
                </slot>
            </div>
        </div>

        <!-- 展开提示 -->
        <fx-popover
            v-if="shouldShowMore"
            placement="top"
            trigger="hover"
            :width="400"
            popper-class="text-list-popover"
        >
            <fx-link class="view-more-text" slot="reference" type="standard" size="small" :underline="false">{{ $t('sfa.aiCustomerProfile.viewMore') }}<span class="fx-icon-arrow-right2"></span></fx-link>
            
            <!-- Popover内容 -->
            <div class="popover-content" :style="{ gap: listItemGap }">
                <div 
                    v-for="(item, index) in formattedList" 
                    :key="index"
                    class="popover-item"
                >
                    <!-- <span v-if="showIndex" class="item-index">{{ index + 1 }}.</span> -->
                    <slot name="content-item" :item="item" :index="index">
                        <span class="item-content" v-html="parseDisplayText(item, index)"></span>
                    </slot>
                </div>
            </div>
        </fx-popover>
    </div>
</template>

<script>
export default {
    name: 'TextListDisplay',
    props: {
        // 支持数组或者单个字符串
        list: {
            type: [Array, String],
            default: () => []
        },
        listItemGap: {
            type: String,
            default: '0'
        },
        // 是否显示序号
        showIndex: {
            type: Boolean,
            default: false
        },
        // 最大显示行数
        maxLines: {
            type: Number,
            default: 99
        },
        // 获取文本的字段名(当list为对象数组时使用)
        textField: {
            type: String,
            default: ''
        },
        // 主区域显示的列表项数量，优先级高于maxLines
        displayCount: {
            type: Number,
            default: 0 // 0表示按maxLines控制
        }
    },
    data() {
        return {
            isCollapsed: true,
            shouldCollapse: false,
            lineHeight: 22, // 默认行高
        }
    },
    computed: {
        formattedList() {
            if (!this.list) return [];
            if (typeof this.list === 'string') return [this.list];
            if (Array.isArray(this.list)) {
                if (this.textField) {
                    return this.list.map(item => {
                        if (typeof item === 'string') return item.trim();
                        return item[this.textField]?.trim() || '';
                    });
                }
                // 如果没有指定textField，直接返回原始数组
                return this.list;
            }
            return [];
        },
        // 主区域显示的列表
        displayList() {
            // displayCount优先级高于maxLines
            if (this.displayCount > 0) {
                return this.formattedList.slice(0, this.displayCount);
            }
            return this.formattedList;
        },
        // 是否需要显示查看更多
        shouldShowMore() {
            // 优先判断displayCount
            if (this.displayCount > 0) {
                return this.formattedList.length > this.displayCount;
            }
            // 其次判断高度
            return this.shouldCollapse;
        },
        collapsedStyle() {
            // 当使用maxLines且需要折叠时才应用最大高度
            if (!this.displayCount && this.isCollapsed && this.shouldCollapse) {
                return {
                    maxHeight: `${this.lineHeight * this.maxLines}px`
                };
            }
            return {};
        }
    },
    mounted() {
        this.$nextTick(() => {
            this.checkIfShouldCollapse();
            window.addEventListener('resize', this.checkIfShouldCollapse);
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.checkIfShouldCollapse);
    },
    methods: {
        checkIfShouldCollapse() {
            // 如果设置了displayCount，不需要检查高度
            if (this.displayCount > 0) {
                this.shouldCollapse = false;
                return;
            }

            const content = this.$refs.contentRef;
            if (!content) return;
            
            // 计算内容实际高度
            const totalHeight = content.scrollHeight;
            const maxHeight = this.lineHeight * this.maxLines;
            
            this.shouldCollapse = totalHeight > maxHeight;
        },
        toggleCollapse() {
            this.isCollapsed = !this.isCollapsed;
        },
        parseDisplayText(text, index) {
            const NEWLINES_REGEX = /\n+|\&lt;br\&gt;+|<br>+/g;
            const CONSECUTIVE_BR_REGEX = /(?:<br>)+/g;

            // 参数安全检查
            if (!text && text !== 0) {
                return this.showIndex ? `${index + 1}. ` : '';
            }
            
            // 确保输入是字符串
            const rawText = String(text);
            
            // 1. 防止XSS攻击：转换特殊字符为HTML实体
            let processedText = _.escape(rawText);
            
            // 2. 统一处理所有类型的换行符为<br>
            // 包括：
            // - 原始换行符 \n
            // - HTML转义的换行标签 &lt;br&gt;
            // - HTML换行标签 <br>
            processedText = processedText.replace(NEWLINES_REGEX, '<br>');
            
            // 3. 处理连续的<br>，只保留一个
            processedText = processedText.replace(CONSECUTIVE_BR_REGEX, '<br>');
            
            // 4. 添加序号（如果需要）并返回处理后的文本
            return this.showIndex ? `${index + 1}. ${processedText}` : processedText;
        }
    }
}
</script>

<style lang="less" scoped>
.text-list-display {
    .content-wrapper {
        position: relative;
        transition: max-height 0.3s ease-in-out;
        display: flex;
        flex-direction: column;

        &.is-collapsed {
            overflow: hidden;
        }
    }

    .content-item {
        display: flex;
        gap: 4px;
        margin: 0;
        font-size: 13px;
        line-height: 22px;
        color: var(--color-neutrals19);

        .item-index {
            flex: none;
        }

        .item-content {
            flex: 1;
            word-break: break-all;
            white-space: normal;
            // white-space: pre-line;
        }
    }

    .view-more-text {
        font-size: 13px;
        line-height: 18px;
        margin-top: 8px;
    }
}


</style> 

<style lang="less">
.text-list-popover {
    .popover-content {
        max-height: 600px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;

        .popover-item {
            display: flex;
            gap: 4px;
            margin: 0;
            font-size: 13px;
            line-height: 22px;
            color: var(--color-neutrals19);

            .item-index {
                flex: none;
            }

            .item-content {
                flex: 1;
                word-break: break-all;
                white-space: normal;
            }

            .suggestion-item {
                background: inherit;
                border: none;
                padding: 0;
            }
        }
    }
}
</style>