<script>
import with<PERSON><PERSON><PERSON><PERSON> from '../mixins/withBase<PERSON>hart'
import utils from '../utils'

export default {
    mixins: [withBase<PERSON><PERSON>],
    props: {
        
    },
    computed: {
        cChartData() {
            return this.chartData.map(item => ({
                name: item.name,
                value: item.value,
                color: utils.getSourceColor(item.value)
            })).map((_, index, array) => {
                // 雷达图默认逆时针，手动修改为顺时针
                if (index === 0) return array[0];
                return array[array.length - index];
            });
        }
    },
    methods: {
        onContainerResize() {
            this.$nextTick(() => {
                this.handleResize();
                this.updateChart();
            });
        },
        containerChangeCalcOptions() {
            const options = {
                labelMaxLength: 10,
                radius: '80%',
                nameGap: 12,
            }
            // 小尺寸时显示更少字符
            if (this.containerWidth < 500) {
                options.labelMaxLength = 6;
                options.radius = '70%';
                options.nameGap = 8;
            } else if (this.containerWidth < 600) {
                options.labelMaxLength = 6;
                options.radius = '80%';
                options.nameGap = 8;
            }
            return options;
        },
        getChartOption() {
            const { labelMaxLength, radius, nameGap } = this.containerChangeCalcOptions();
            const formatLabelText = (text, idx) => {
                const maxLength = labelMaxLength;
                if (text.length > maxLength) {
                    return `{label|${text.substring(0, maxLength)}...} {score${idx}|${this.cChartData[idx].value}}`;
                }
                return `{label|${text}} {score${idx}|${this.cChartData[idx].value}}`;
            };
            return {
                radar: {
                    indicator: this.cChartData.map((item, idx) => ({
                        name: formatLabelText(item.name, idx),
                        max: 100
                    })),
                    splitNumber: 5,
                    name: {
                        formatter: function(name) {
                            return name;
                        },
                        rich: (() => {
                            const rich = { label: { color: '#181C25', fontSize: 13, lineHeight: 18, fontWeight: 700 } };
                            this.cChartData.forEach((item, idx) => {
                                rich[`score${idx}`] = { color: item.color, fontSize: 13, lineHeight: 18, fontWeight: 900, paddingLeft: 2 };
                            });
                            return rich;
                        })()
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#DEE1E8'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: '#DEE1E8'
                        }
                    },
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: [
                                '#F2F4FB',
                                'rgba(255, 255, 255, 1)',
                            ]
                        }
                    },
                    center: ['50%', '50%'],
                    radius,
                    nameGap
                },
                series: [{
                    type: 'radar',
                    data: [{
                        value: this.cChartData.map(item => item.value),
                        symbol: 'circle',
                        symbolSize: 4,
                        itemStyle: {
                            color: '#0C6CFF',
                            borderColor: '#fff',
                            borderWidth: 1
                        },
                        areaStyle: {
                            color: 'rgba(84, 152, 255, 0.24)'
                        },
                        lineStyle: {
                            width: 2,
                            color: '#368DFF'
                        }
                    }]
                }],
            }
        }
    }
}
</script>