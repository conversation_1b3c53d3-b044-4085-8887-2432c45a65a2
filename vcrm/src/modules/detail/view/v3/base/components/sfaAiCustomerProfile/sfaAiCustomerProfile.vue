<template>
    <layout-renderer
        :layout="currentLayout"
        :data-source="this"
        v-loading="rootDataLoading"
        @update:prop="handlePropUpdate"
    />
</template>

<script>
import components from './components'
import LayoutRenderer from './layout/LayoutRenderer.vue'
import createLayoutConfig from './layout/layoutConfig'
import withRootData from './mixins/withRootData'

export default {
    name: "sfaAiCustomerProfile",
    components: {
        ...components,
        LayoutRenderer
    },
    props: ["compInfo", "apiName", "dataId"],
    mixins: [withRootData],
    computed: {
        currentLayout() {
            const context = {
                currentProfileId: this.currentProfileId,
                currentMethodology: this.currentMethodology,
                apiName: this.apiName,
                displayComponents: this.compInfo.displayComponents,
                isC139Methodology: this.isC139Methodology,
            }
            return createLayoutConfig(context);
        }
    },
    methods: {
        handlePropUpdate(prop, value) {
            this[prop] = value;
        },
    }
};
</script>

<style lang="less" scoped>
</style>
