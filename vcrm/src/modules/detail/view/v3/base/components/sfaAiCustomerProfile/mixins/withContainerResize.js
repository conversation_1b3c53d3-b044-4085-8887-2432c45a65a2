/**
 * 容器尺寸变化监听 mixin
 * 用于监听组件容器尺寸变化并提供相应的回调方法
 */
export default {
    props: {
        // 是否启用容器尺寸监听
        enableResizeObserver: {
            type: Boolean,
            default: true
        },
        // 防抖延迟时间(ms)
        resizeDebounceTime: {
            type: Number,
            default: 100
        }
    },
    
    data() {
        return {
            // 容器尺寸监听器
            containerResizeObserver: null,
            // 容器宽度
            containerWidth: 0,
            // 容器高度
            containerHeight: 0,
            // 是否已初始化尺寸
            containerSizeInitialized: false
        };
    },
    
    mounted() {
        if (this.enableResizeObserver) {
            this.$nextTick(() => {
                this.initContainerResizeObserver();
            });
        }
    },
    
    beforeDestroy() {
        this.destroyContainerResizeObserver();
    },
    
    methods: {
        /**
         * 初始化容器尺寸监听器
         */
        initContainerResizeObserver() {
            // 确保DOM已渲染
            if (!this.$el) {
                return;
            }
            
            // 初始化容器尺寸
            this.updateContainerSize();
            
            // 创建 ResizeObserver 实例
            if (window.ResizeObserver) {
                this.containerResizeObserver = new ResizeObserver(
                    _.debounce(this.handleContainerResizeChange, this.resizeDebounceTime)
                );
                
                // 开始观察容器元素
                this.containerResizeObserver.observe(this.$el);
                
            } else {
                // 降级处理：使用 window resize 事件
                window.addEventListener('resize', this.handleWindowResize);
            }
        },
        
        /**
         * 销毁容器尺寸监听器
         */
        destroyContainerResizeObserver() {
            if (this.containerResizeObserver) {
                this.containerResizeObserver.disconnect();
                this.containerResizeObserver = null;
            }
            
            // 移除降级处理的事件监听
            window.removeEventListener('resize', this.handleWindowResize);
        },
        
        /**
         * 处理容器尺寸变化
         * @param {ResizeObserverEntry[]} entries - ResizeObserver 条目
         */
        handleContainerResizeChange(entries) {
            if (!entries || !entries.length) return;
            
            const { width, height } = entries[0].contentRect;
            const sizeChanged = width !== this.containerWidth || height !== this.containerHeight;
            
            if (sizeChanged) {
                const oldWidth = this.containerWidth;
                const oldHeight = this.containerHeight;
                
                this.containerWidth = width;
                this.containerHeight = height;
                
                // 调用尺寸变化回调
                this.onContainerResize({
                    width,
                    height,
                    oldWidth,
                    oldHeight,
                    widthChanged: width !== oldWidth,
                    heightChanged: height !== oldHeight
                });
                
                // 标记尺寸已初始化
                if (!this.containerSizeInitialized) {
                    this.containerSizeInitialized = true;
                    this.onContainerSizeInitialized({
                        width,
                        height
                    });
                }
            }
        },
        
        /**
         * 处理窗口尺寸变化（降级处理）
         */
        handleWindowResize() {
            this.updateContainerSize();
        },
        
        /**
         * 更新容器尺寸
         */
        updateContainerSize() {
            if (!this.$el) return;
            
            const { offsetWidth, offsetHeight } = this.$el;
            const sizeChanged = offsetWidth !== this.containerWidth || offsetHeight !== this.containerHeight;
            
            if (sizeChanged) {
                const oldWidth = this.containerWidth;
                const oldHeight = this.containerHeight;
                
                this.containerWidth = offsetWidth;
                this.containerHeight = offsetHeight;
                
                // 调用尺寸变化回调
                this.onContainerResize({
                    width: offsetWidth,
                    height: offsetHeight,
                    oldWidth,
                    oldHeight,
                    widthChanged: offsetWidth !== oldWidth,
                    heightChanged: offsetHeight !== oldHeight
                });
                
                // 标记尺寸已初始化
                if (!this.containerSizeInitialized) {
                    this.containerSizeInitialized = true;
                    this.onContainerSizeInitialized({
                        width: offsetWidth,
                        height: offsetHeight
                    });
                }
            }
        },
        
        /**
         * 容器尺寸变化回调（可在组件中重写）
         * @param {Object} size - 尺寸信息
         */
        onContainerResize(size) {
            // 默认实现，子组件可以重写此方法
            this.$emit('container-resize', size);
        },
        
        /**
         * 容器尺寸首次初始化回调（可在组件中重写）
         * @param {Object} size - 尺寸信息
         */
        onContainerSizeInitialized(size) {
            // 默认实现，子组件可以重写此方法
            this.$emit('container-size-initialized', size);
        },
        
        /**
         * 手动触发容器尺寸更新
         */
        refreshContainerSize() {
            this.updateContainerSize();
        }
    }
};