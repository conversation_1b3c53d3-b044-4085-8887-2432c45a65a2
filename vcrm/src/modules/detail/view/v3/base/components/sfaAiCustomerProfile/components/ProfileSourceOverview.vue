<template>
    <common-card :title="title" class="profile-source-overview-card" background="rgba(255, 255, 255, 0.76)">
        <gauge-chart :chartData="scoreData.winRate" :chartDataUnit="scoreUnit" height="124px" @rendered="chartRendered = true" />
        <!-- 右上角说明 -->
        <fx-tag class="description-tag" size="mini" type="info" @click="showDescriptionDialog = true">
            {{ $t('sfa.aiCustomerProfile.descriptionTitle') }}<span class="fx-icon-info icon"></span>
        </fx-tag>
        <common-source-description-dialog :visible.sync="showDescriptionDialog" :descriptionList="description" />
        <!-- 分数趋势 -->
        <div class="chart-detail" v-if="chartRendered">
            <p class="chart-detail-score">
                <countup class="chart-detail-score-value" :end-val="scoreData.winRate" />
                <span class="chart-detail-score-unit">{{ scoreUnit }}</span>
            </p>
            <fx-tag class="trend-tag" size="mini" v-if="scoreData.winRateTrend" :type="scoreData.winRateTrend > 0 ? 'success' : 'danger'">
                <img v-if="scoreData.winRateTrend > 0" class="img-icon" src="../assets/change_up.svg" alt="">
                <img v-else class="img-icon" src="../assets/change_down.svg" alt="">
                <span>{{ winRateTrendText }}</span>
                <fx-popover v-if="changeDescriptionList && changeDescriptionList.length" popper-class="profile-source-overview-trend-popover" placement="top-start" trigger="hover" @show="popoverShow = true" @hide="popoverShow = false">
                    <span slot="reference" class="fx-icon-obj-app460 icon"></span>
                    <base-dimension-trend-chart-tooltip v-if="popoverShow" :items="changeDescriptionList" />
                </fx-popover>
            </fx-tag>
        </div>
        
    </common-card>
</template>

<script>
import CommonCard from './CommonCard.vue'
import GaugeChart from './BaseChartGauge.vue'
import CommonSourceDescriptionDialog from './CommonSourceDescriptionDialog.vue'
import BaseDimensionTrendChartTooltip from './BaseDimensionTrendChartTooltip.vue'
import Countup from './BaseCountup.vue'
import utils from '../utils'
import ProfileService from '../services/profileService'
export default {
    name: 'ProfileSourceOverview',
    props: {
        scoreData: {
            type: Object,
            required: true,
        },
        dataContext: {
            type: Object,
            default: () => ({})
        },
        profileContext: {
            type: Object,
            default: () => ({})
        }
    },
    components: {
        CommonSourceDescriptionDialog,
        CommonCard,
        Countup,
        GaugeChart,
        BaseDimensionTrendChartTooltip
    },
    computed: {
        scoreUnit() {
            return utils.getSourceProps(this.dataContext.objectApiName).sourceUnit;
        },
        title() {
            return utils.getSourceProps(this.dataContext.objectApiName).overviewTitle;
        },
        description() {
            return utils.getSourceProps(this.dataContext.objectApiName).overviewDescription;
        },
        winRateTrendText() {
            const source = `${Math.abs(this.scoreData.winRateTrend)}${this.scoreUnit || ''}`
            return `${this.scoreData.winRateTrend > 0 ? $t('sfa.aiCustomerProfile.overviewDescription.winRateTrendUp', {num: source}) : $t('sfa.aiCustomerProfile.overviewDescription.winRateTrendDown', {num: source})}`;
        }
    },
    watch: {
        'profileContext.currentProfileId': {
            handler(value) {
                this.fetchDescriptionList();
            },
            deep: true,
            immediate: true,
        }
    },
    data() {
        return {
            popoverShow: false,
            chartRendered: false,
            showDescriptionDialog: false,
            changeDescriptionList: [],
            changeDescriptionLoading: false,
        }
    },
    methods: {
        fetchDescriptionList() {
            if (this.changeDescriptionLoading || this.changeDescriptionList.length) return;
            this.changeDescriptionLoading = true;
            ProfileService.getCustomerDimensionTrendDetail({
                profileId: this.profileContext.currentProfileId,
                methodologyType: this.profileContext.currentMethodologyType,
                methodologyInstanceId: this.profileContext.currentMethodologyInstanceId,
                objectApiName: this.dataContext.apiName,
                objectId: this.dataContext.dataId
            }).then(details => {
                this.changeDescriptionList = details.reduce((acc, item) => {
                    const itemDimension = acc.find(aItem => aItem.featureDimensionId === item.featureDimensionId);
                    if (itemDimension) {
                        itemDimension.description.push(item);
                    } else {
                        const dimensionName = this.profileContext.dimensionsData.find(dItem => dItem.id === item.featureDimensionId)?.label;
                        acc.push({
                            featureDimensionId: item.featureDimensionId,
                            name: dimensionName,
                            description: [item]
                        })
                    }
                    return acc;
                }, []);
            }).finally(() => {
                this.changeDescriptionLoading = false;
            })
        }
    },
}
</script>

<style lang="less">
.profile-source-overview-card {
    position: relative;

    .description-tag {
        cursor: pointer;
        position: absolute;
        right: 12px;
        top: 12px;
        color: #545861;
        .icon {
            margin-left: 2px;
        }
        .icon:before {
            color: #91959E;
        }
    }

    .chart-detail {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 8px;
        position: absolute;
        bottom: 12px;
        left: 50%;
        transform: translateX(-50%); 

        @keyframes gradient {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        .chart-detail-score {
            background: linear-gradient(to left, #FF8000 21.05%, #FFB452 67.38%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 700;
            animation: gradient 3s linear infinite;
            background-size: 200% 100%;
            .chart-detail-score-value {
                font-size: 58px;
                line-height: 1;
            }
            .chart-detail-score-unit {
                font-size: 20px;
                line-height: 24px;
            }
        }
    }
    .trend-tag {
        display: flex;
        align-items: center;
        gap: 2px;
        .img-icon {
            width: 12px;
            height: 12px;
        }
        .icon {
            cursor: pointer;
            &:before {
                color: #737C8C !important;
            }
        }
    }
}
</style>

<style lang="less">
.profile-source-overview-trend-popover {
    padding: 0;
    .custom-tooltip {
        box-shadow: none;
    }
}
</style>
