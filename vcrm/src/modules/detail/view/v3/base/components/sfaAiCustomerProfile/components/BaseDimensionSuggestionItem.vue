<template>
    <div class="suggestion-item">
        <!-- <p class="title">{{ item.title }}</p> -->
        <common-text-list-display :list="item.content" class="content" />
        <p class="reference" v-if="item.reference.length && item.type === 'customer'">
            <template v-for="(ref, index) in item.reference">
                <fx-link :key="ref.value" type="standard" size="small" :underline="false" @click="showReference(ref.value)">
                    {{ ref.name }}
                </fx-link>
                <span v-if="index !== item.reference.length - 1">、</span>
            </template>
        </p>
        <div class="footer">
            <div class="source">
                <fx-tag v-if="item.type === 'fs'" icon="fx-icon-AI" size="small" effect="light-noborder">{{ $t('sfa.aiCustomerProfile.source.fs') }}</fx-tag>
                <fx-tag v-else-if="item.type === 'customer'" type="info" size="small" effect="light-noborder">{{ $t('sfa.aiCustomerProfile.source.customer') }}</fx-tag>
            </div>
            <div v-if="showActions" class="actions">
                <!-- <div class="action-btn" @click="$emit('ask')">
                    <img class="img-icon" src="../assets/ai_generate.svg">
                    <span>追问ShareAI</span>
                </div>
                <span class="divider"></span> -->
                <div class="action-btn" @click="handleCreateTask(item)">
                    <span class="fx-icon-renwu icon"></span>
                    <span>{{ $t('sfa.aiCustomerProfile.createTask') }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CommonTextListDisplay from './CommonTextListDisplay.vue';
export default {
    name: 'ProfileDimensionSuggestionItem',
    components: {
        CommonTextListDisplay
    },
    props: {
        item: {
            type: Object,
            required: true
        },
        dataContext: {
            type: Object,
            default: () => ({})
        },
        showActions: {
            type: Boolean,
            default: true
        }
    },
    methods: {
        showReference(id) {
            // CRM.api.show_crm_detail({
            //     apiName: 'ServiceKnowledgeObj',
            //     id,
            // })
        },
        handleCreateTask(item) {
            const { objectApiName, objectDescribe, objectData, objectId } = this.dataContext;
            CRM.api.add_taskobj({
                title: item.task_id__r,
                feedContent: [{
                    text: item.content,
                }],
                crmobjectOpts: [
                    {
                        objectType: objectApiName,
                        name: objectDescribe?.display_name,
                        data: [
                            {
                                name: objectData?.name,
                                id: objectId
                            }
                        ],
                    },
                    {
                        objectType: item.object_describe_api_name,
                        name: item.object_describe_api_name,
                        data: [
                            {
                                name: item.name || item._id,
                                id: item._id
                            }
                        ],
                    }
                ],
            })
        }
    }
}
</script>

<style lang="less" scoped>
.suggestion-item {
    display: flex;
    flex-direction: column;

    .title {
        font-size: 13px;
        line-height: 22px;
        font-weight: 700;
    }
    .content {
        flex: 1;
    }

    /deep/ .fx-icon-AI {
        margin-right: 2px;
    }
    .reference {
        /deep/ .el-link {
            font-size: 13px;
            line-height: 22px;
        }
    }

    .footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        margin-top: 8px;
        gap: 8px;
        
        .source {
            font-size: 12px;
            line-height: 18px;
            color: var(--color-neutrals12);
        }

        .actions {
            display: flex;
            align-items: center;
            gap: 8px;

            .divider {
                width: 1px;
                height: 12px;
                background-color: var(--color-neutrals05);
            }
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            line-height: 18px;
            color: var(--color-neutrals15);
            cursor: pointer;

            .img-icon {
                width: 14px;
                height: 14px;
            }

            .icon {
                font-size: 14px;
                &:before {
                    color: var(--color-neutrals12);
                }
            }

            &:hover {
                color: var(--color-primary06);
                .icon:before {
                    color: var(--color-primary06);
                }
            }
        }
    }
}
</style> 