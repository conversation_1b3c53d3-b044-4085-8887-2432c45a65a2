<template>
    <div class="stage-section">
        <div class="stage-score-badge">
            <span class="score-badge-name">{{ $t('sfa.aiCustomerProfile.stageScore.processScore') }}</span>
            <span class="score-badge-score">
                {{ scoreData.stageScore }}<span class="score-badge-score-unit">{{ $t('sfa.aiCustomerProfile.scoreUnit') }}</span>
            </span>
        </div>
        <div class="stage-progress-bar" ref="progressBar">
            <div class="stage-bar-bg"></div>
            <div
                class="stage-bar-active"
                :style="{ width: activeBarPx + 'px' }"
            ></div>
            <div
                v-for="(stage, index) in scoreDataStages"
                :key="index"
                class="stage-v2"
                :class="{
                    active: isStageActive(index),
                    inactive: !isStageActive(index),
                    lastActive: index === activeStageIndex,
                    'first-node': index === 0,
                    'last-node': index === scoreDataStages.length - 1
                }"
                :style="{ 
                    left: index === scoreDataStages.length - 1 ? 'auto' : index * nodeGap + 'px',
                    right: index === scoreDataStages.length - 1 ? '8px' : 'auto'
                }"
            >
                <div
                    class="score-bubble"
                    v-if="stage.value !== undefined && stage.value !== null"
                    :style="{ backgroundColor: stage.bgColor, color: stage.color }"
                >
                    {{ stage.value }} {{ $t('sfa.aiCustomerProfile.scoreUnit') }}
                </div>
                <template v-if="index === activeStageIndex">
                    <div class="stage-dot-v2 dot-green last-dot"></div>
                </template>
                <template v-else-if="isStageActive(index)">
                    <div class="stage-dot-v2 dot-green">
                        <span class="dot-outer dot-outer-green"></span>
                    </div>
                </template>
                <template v-else>
                    <div class="stage-dot-v2 dot-gray last-dot"></div>
                </template>
                <div class="stage-label-v2">{{ stage.name }}</div>
            </div>
        </div>
        <div class="stage-score-description">
            <span>{{ $t('sfa.aiCustomerProfile.stageScore.flowBased') }}{{ scoreData.stageName }}</span>
            <span class="divider"></span>
            <span class="description-reference" @click="dialogVisible = true"><span class="fx-icon-obj-app460 icon"></span>{{ $t('sfa.aiCustomerProfile.descriptionTitle') }}</span>
        </div>
        <CommonSourceDescriptionDialog :visible.sync="dialogVisible" :description-list="descriptionList" :other-description="otherDescription" />
    </div>
</template>

<script>
import withContainerResize from '../mixins/withContainerResize';
import CommonSourceDescriptionDialog from './CommonSourceDescriptionDialog.vue';
import utils from '../utils';
export default {
    name: "StageSection",
    mixins: [withContainerResize],
    components: {
        CommonSourceDescriptionDialog,
    },
    props: {
        scoreData: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            sidePadding: 0,
            nodeGap: 0,
            dialogVisible: false,
            descriptionList: [
                [$t('sfa.aiCustomerProfile.stageScore.processScore') + $t('sfa.aiCustomerProfile.colon'), this.$t('sfa.aiCustomerProfile.stageScore.processScoreDescription')],
                [$t('sfa.aiCustomerProfile.stageScore.stageScore') + $t('sfa.aiCustomerProfile.colon'), this.$t('sfa.aiCustomerProfile.stageScore.stageScoreDescription')]
            ],
            otherDescription: this.$t('sfa.aiCustomerProfile.stageScore.otherDescription')
        };
    },
    watch: {
        scoreData: {
            handler() {
                this.calcLayout();
            },
            deep: true,
            immediate: true,
        },
    },
    computed: {
        activeStageIndex() {
            // 最后一个有分数的阶段为当前阶段
            let idx = -1;
            this.scoreDataStages.forEach((s, i) => {
                if (s.value !== undefined && s.value !== null) idx = i;
            });
            return idx;
        },
        activeBarPx() {
            // 修改：直接使用节点间距乘以当前激活的索引
            return this.nodeGap * this.activeStageIndex;
        },
        scoreDataStages() {
            return this.scoreData.stages.map(item => ({
                ...item,
                color: utils.getSourceColor(item?.value),
                bgColor: utils.getSourceBgColor(item?.value),
            }));
        }
    },
    methods: {
        isStageActive(index) {
            return index <= this.activeStageIndex;
        },
        onContainerResize() {
            this.calcLayout();
        },
        calcLayout() {
            this.$nextTick(() => {
                const el = this.$refs.progressBar;
                if (!el) return;
                const width = el.offsetWidth;
                const count = this.scoreDataStages.length;
                // 修改：直接用总宽度除以间隔数(节点数-1)来计算节点间距
                this.nodeGap = count > 1 ? width / (count - 1) : width;
                // 修改：边距设为0，让首尾节点位于进度条两端
                this.sidePadding = 0;
            });
        },
    },
};
</script>

<style lang="less" scoped>
.stage-section {
    position: relative;
    display: flex;
    gap: 24px;
    .stage-score-badge {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        gap: 8px;
        align-items: center;
        background: #fff7e6;
        border-radius: 8px;
        padding: 12px;
        border: 1px solid #FF8000;
        .score-badge-name {
            color: var(--color-neutrals19);
            font-size: 13px;
            line-height: 18px;
            font-weight: 700;
            white-space: nowrap;
        }
        .score-badge-score {
            color: #FF8000;
            font-size: 24px;
            font-weight: 700;
            .score-badge-score-unit {
                font-size: 13px;
                line-height: 18px;
                font-weight: 700;
            }
        }
    }
    .stage-progress-bar {
        position: relative;
        box-sizing: border-box;
        flex: 1;
        .stage-bar-bg {
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 4px;
            background: #DEE1E8;
            border-radius: 4px;
            z-index: 1;
            transform: translateY(-50%);
        }
        .stage-bar-active {
            position: absolute;
            top: 50%;
            left: 0;
            height: 4px;
            background: #30c776;
            border-radius: 4px;
            z-index: 2;
            transition: width 0.3s;
            transform: translateY(-50%);
        }
        .stage-v2 {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            width: 1px;
            min-width: 0;
            z-index: 3;
            .stage-dot-v2 {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #fff;
                border: 2px solid #e5e5e5;
                margin: 0 auto;
                position: relative;
                z-index: 3;
                box-sizing: border-box;
                transition: border-color 0.3s;
            }
            .dot-green {
                background: #fff;
                border: 2px solid #30C776;
            }
            .dot-gray {
                background: #fff;
                border: 2px solid #dee1e8;
            }
            .dot-outer {
                content: "";
                display: block;
                position: absolute;
                left: 50%;
                top: 50%;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                transform: translate(-50%, -50%);
                z-index: 2;
            }
            .dot-outer-green {
                background: rgba(48, 199, 118, 0.4);
            }
            .last-dot {
                width: 10px;
                height: 10px;
                border-width: 4px;
                &::after {
                    content: "";
                    display: block;
                    position: absolute;
                    left: 50%;
                    top: 50%;
                    width: 4px;
                    height: 4px;
                    background: #fff;
                    border-radius: 50%;
                    transform: translate(-50%, -50%);
                }
            }
            .score-bubble {
                background: #F0FFF4;
                color: #30C675;
                font-size: 13px;
                line-height: 18px;
                font-weight: 700;
                border-radius: 8px;
                padding: 4px;
                margin-bottom: 2px;
                display: inline-block;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
                position: absolute;
                left: 50%;
                top: -32px;
                transform: translateX(-50%);
                white-space: nowrap;
            }
            .stage-label-v2 {
                margin-top: 8px;
                font-size: 12px;
                line-height: 18px;
                color: #91959e;
                position: absolute;
                left: 50%;
                transform: translateX(-50%);
                white-space: nowrap;
                font-weight: 400;
            }
            &.first-node {
                .score-bubble,
                .stage-label-v2 {
                    left: 0;
                    transform: translateX(0);
                }
            }
            &.last-node {
                .score-bubble,
                .stage-label-v2 {
                    left: auto;
                    right: 0;
                    transform: translateX(0);
                }
            }
            &.inactive {
                .score-bubble {
                    background: #f5f5f5;
                    color: #bbb;
                }
            }
            &.lastActive {
                .stage-label-v2 {
                    color: #181c25;
                }
            }
        }
    }
    .stage-score-description {
        position: absolute;
        right: 0;
        top: -30px;
        font-size: 12px;
        line-height: 18px;
        color: #91959E;
        display: flex;
        align-items: center;
        gap: 8px;
        .description-reference {
            cursor: pointer;
            .icon:before {
                color: #91959E;
            }
        }
        .divider {
            width: 1px;
            height: 10px;
            background: #DEE1E8;
        }
    }
}
</style> 