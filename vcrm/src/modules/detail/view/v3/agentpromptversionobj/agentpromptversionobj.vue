<template>
  <faci-detail
    :apiName="apiName"
    :dataId="dataId"
    :hooks="dHooks"
    :isSlide="isSlide"
    :dataIds="dataIds"
    :top="top"
    :postData="postData"
    :extendData="extendData"
    :source="source"
    class="faci-detail-agentpromptversionobj"
  >
    <template
      v-for="compApiName in dExtendComps"
      v-slot:[compApiName]="slotProps"
    >
      <component
        :is="compApiName"
        :key="compApiName"
        :compInfo="slotProps.compInfo"
        :apiName="slotProps.apiName"
        :dataId="slotProps.dataId"
        class="forecastrule"
      ></component>
    </template>
  </faci-detail>
</template>
<script>
import Base from "../base/base";
import components from "./components/index";
export default {
  extends: Base,
  props: {
    apiName: String,
    dataId: String,
  },
  components: {
    ...components,
  },
  data() {
    console.log("[][][][]");
    return {
      customComps: Object.keys(components),
    };
  },
  methods: {},
};
</script>

<style lang="less">
</style>
