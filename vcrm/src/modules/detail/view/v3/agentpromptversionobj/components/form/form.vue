<template>
  <faci-detail-form :compInfo="compInfo" :apiName="apiName" :dataId="dataId">
    <div
      class="faci-field-display p-flex align-left"
      :slot="ruleData.apiName"
      :data-apiname="ruleData.apiName"
    >
      <div class="faci-field-display_label">{{ ruleData.field.label }}</div>
      <div class="faci-field-display_value p-border-bottom">
        <span>   {{ ruleData.result }}</span>
      </div>
    </div>
    <div
      class="faci-field-display p-flex align-left"
      style="margin-left: -3px"
      :slot="subtemplateData.apiName"
      :data-apiname="subtemplateData.apiName"
    >
      <div class="faci-field-display_label">
        {{ subtemplateData.field.label }}
      </div>
      <div
        @click="showTemplateDetailEdit(subtemplateData)"
        class="faci-field-display_value p-border-bottom faci-field-display_a"
      >
      <span>   {{ subtemplateData.templateData.promptName }}</span>
      </div>
    </div>
  </faci-detail-form>
</template>

<script>
export default {
  props: ["compInfo", "apiName", "dataId"],
  data() {
    return {
      detailData: this.$context.getData(),
      fields: this.$context.getDescribe().fields,
    };
  },
  mounted() {},
  computed: {
    ruleData() {
      const apiName = "rule";
      const data = this.detailData[apiName] || "";
      try {
        let checkedData = JSON.parse(data)?.filter[0]?.field_values || [];
        let field_name = this.fields?.interactive_scenario?.options || [];
        let result = checkedData
          .map((value) => {
            let option = field_name.find((item) => item.value === value);
            return option ? option.label : value;
          })
          .join(",");
        return {
          apiName,
          result,
          field: this.fields[apiName] || {},
        };
      } catch {
        return {
          apiName,
          result: "",
          field: this.fields[apiName] || {},
        };
      }
    },
    subtemplateData() {
      const apiName = "subtemplate_api_name";
      const data = this.detailData[apiName] || "";
      let parsedData;
      if (!data) {
        parsedData = {
          promptName: "",
          promptApiName:""
        };
      } else {
        parsedData = JSON.parse(data);
      }
      return {
        apiName,
        templateData: parsedData,
        field: this.fields[apiName] || {},
      };
    },
  },
  methods: {
    showTemplateDetailEdit(data) {
      Fx.getBizAction('paasdev', 'openPromptFormDialog', {
       status: 'update',
       apiName: data.templateData.promptApiName}
       )
    },
  },
};
</script>
