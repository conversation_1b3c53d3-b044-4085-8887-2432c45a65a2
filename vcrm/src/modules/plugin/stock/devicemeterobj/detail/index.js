import { formatDate } from "../utils";

export default class DeviceMeterObj {
    constructor(pluginService, pluginParam) {
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
    }

    handleDetailRelatedListRenderBefore(plugin, hookParam) {
        if(hookParam.targetObjectApiName !== "DeviceMeterReadingsObj") return Promise.resolve();
        return Promise.resolve({
            columnsExtendConfig: {
                render: {
                    datetime: (value, type, data, format, index, field) => {
                    	return formatDate(data.datetime);
                    }
                }
            }
        });
    }

    apply() {
        return [
            {
                event: "detail.relatedlist.render.before",
                functional: this.handleDetailRelatedListRenderBefore.bind(this),
            },
        ];
    }
}
