export function padZero(num, digit = 2, target = 10) {
    if (num < target) return (num + "").padStart(digit, "0");
    return num;
}

export function formatDate(now) {
    const date = new Date(now);
    var y = date.getFullYear(); // 年份
    var m = padZero(date.getMonth() + 1); // 月份，注意：js里的月要加1
    var d = padZero(date.getDate()); // 日
    var h = padZero(date.getHours()); // 小时
    var min = padZero(date.getMinutes()); // 分钟
    var s = padZero(date.getSeconds()); // 秒
    // 返回值，根据自己需求调整，现在已经拿到了年月日时分秒了
    const temp = y + "-" + m + "-" + d + " " + h + ":" + min + ":" + s;
    const tempToMs = +new Date(temp);
    return temp + "." + padZero(now - tempToMs, 3, 100);
}
