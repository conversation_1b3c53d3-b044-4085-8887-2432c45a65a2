/*
 * @Descripttion: 取价服务 (添加数据)
 * @Author: LiAng
 * @Date: 2021-12-15 19:24:38
 * @LastEditors: chaoxin
 * @LastEditTime: 2023-06-11 15:23:19
 */

import PPM from 'plugin_public_methods'

export default class Add {

    constructor(pluginService, pluginParam, parent) {
        this.request = pluginService.api.request;
        this.parent = parent;
    }

    getAllFields(mdApiname) {
        return this.parent.getAllFields(mdApiname);
    }

    getConfig(key) {
        return this.parent.getConfig(key);
    }

    /**
     * @desc 点从xx添加，更改添加数据组件路径
     * @param pluginService
     * @param param
     * @returns {Promise<{modulePath: string, master_data: *, target_related_list_name}>}
     * @private
     */
    async _batchAddBeforeHook(pluginService, param) {
        let masterData = param.dataGetter.getMasterData();
        let lookUpApiName = param.lookupField.api_name;
        let mdApiName = param.objApiName;
        let {
            price_book_product_id,
            product_id,
            form_account_id,
            form_price_book_id
        } = this.parent.getAllFields(mdApiName);
        let path = 'crm-modules/components/pickselfobject_classification/pickselfobject_classification';
        if (lookUpApiName === price_book_product_id) path = 'crm-modules/components/pickselfobject_newpricebook/pickselfobject_newpricebook';
        // 点从XX添加时，更该添加数据组件路径
        let r = await this.parent.runPlugin('price-service.batchAdd.before', {
            path: path,  // 组件路径
            lookUpApiName: lookUpApiName, // 从xx添加apiname
            param
        });
        if (r && r.path) path = r.path;
        let isSpecial = [price_book_product_id, product_id].includes(lookUpApiName);
        let baseConfig = {};
        if (isSpecial) baseConfig = this.getPickSelfConfig({mdApiName, lookUpApiName, masterData}, param);
        return Object.assign({}, baseConfig, {
            modulePath: path,
            target_related_list_name: param.lookupField.target_related_list_name
        })
    }

    /**
     * @desc 选数据配置
     * @param mdApiName
     * @param lookUpApiName 数据来源对象
     * @param masterData 主对象数据
     * @param isFrom 来源 add or lookup
     * @param rowData 行数据
     * @param param
     * @returns {{extendParam: {filterIds: Array, source_api_name: string, isSupportCopy: boolean, btnInfo: {fieldname: string}}, skipReadOnlyValidate: boolean, filterIds: Array, hideAdd: boolean, filters: Array}}
     */
    getPickSelfConfig({mdApiName = '', lookUpApiName = '', masterData = {}, isFrom = 'add', rowData = {}} = {}, param) {
        const me = this;
        let {
            product_id,
            form_price_book_id,
            form_account_id,
            form_partner_id,
            price_book_id,
            price_book_product_id,
        } = this.parent.getAllFields();
        // 添加过滤条件；
        let details = param.dataGetter.getDetail(mdApiName);
        let priceBookDesc = param.dataGetter.getDescribe(mdApiName)?.fields?.price_book_id;
        let recordType = param.recordType;
        let filterIds = this.getAllProductIds(details, product_id, recordType);
        let filterProductIds=this.getAllProductIds(details, product_id, recordType);
        let filters = [];
        let pbId = '';
        if (lookUpApiName === price_book_product_id) {
            pbId = isFrom === 'add' ? masterData[form_price_book_id] : rowData[price_book_id];
            if(pbId) {
                filters.push({
                    field_name: 'pricebook_id',
                    field_values: [pbId || ''],
                    operator: 'EQ',
                    value_type: 0
                });
            }
        }
        let isSupportCopy = this.getConfig('43') == '1';
        let btnInfo = isFrom === 'add' ? {
            fieldname: lookUpApiName
        } : null;

        // 补充字段给server、底层组件用
        masterData.account_id = masterData[form_account_id] || '';
        masterData.partner_id = masterData[form_partner_id] || '';
        masterData.price_book_id = masterData.pricebook_id = masterData[form_price_book_id] || '';
        masterData.price_book_id__r = masterData[form_price_book_id + '__r'] || '';

        rowData.product_id = rowData[product_id];
        rowData.price_book_id = rowData[price_book_id];
        rowData.price_book_product_id = rowData[price_book_product_id];

        let res = {
            skipSearch: CRM.util.isGrayScale('CRM_SHOW_ADD_ROW') && this.getConfig('openPriceList'),
            skipReadOnlyValidate: true, // 跳过底层只读字段不允许选数据
            filters,
            filterIds,
            isSupportCopy,
            source_api_name: mdApiName,
            btnInfo,
            fieldName: lookUpApiName,
            cellData: rowData,
            master_data: masterData,
            details: param.dataGetter.getDetails(),
            priceBookDesc,
            beforeRenderHook: (p) => {
                let {opt} = p;
                if (opt.object_data) {
                    opt.object_data.product_id = opt.object_data[product_id];
                }
            },
            extendParam: {
                hideAdd: lookUpApiName === price_book_product_id,
                filterIds,
                filterProductIds,
                isSupportCopy,
                source_api_name: mdApiName,
                btnInfo,
                fieldName: lookUpApiName,
                cellData: rowData,
                master_data: masterData,
                accountId: masterData[form_account_id],

            }
        };
        // 开属性后，不过滤已选产品；自定义从对象也不过滤，有需要自己配筛选条件；
        if (this.getConfig('openAttribute') || mdApiName.includes('__c')) {
            res.filterIds = res.extendParam.filterIds = res.filterProductIds = res.extendParam.filterProductIds = [];
        }

        if (rowData.rowId) {
            param.dataUpdater.updateDetail(mdApiName, rowData.rowId, {
                pricebook_id: pbId
            });
        }

        // 补充pricebook_id字段，server用
        res.beforeRequest = (p) => {
            if (p.object_data) {
                if (pbId) p.object_data.pricebook_id = pbId;
                p.object_data = Object.assign({}, masterData, p.object_data);
            } else {
                let baseData = param.getRowBasicData(mdApiName, recordType);
                p.object_data = Object.assign({}, masterData, baseData);
            }
            if (!p.master_data) p.master_data = masterData;
            p.account_id = masterData[form_account_id] || '';
            p.partner_id = masterData[form_partner_id] || '';
            p.details = me.parseBeforeRequestDetails(p.details, p.associated_object_describe_api_name, param);
            p = this.addParams(p, rowData, param);
            return p
        };
        return res;
    }

    // 价目表字段支持数据范围筛选，RelatedList 补全 details 数据
    parseBeforeRequestDetails(details, targetApiName, param) {
        const apinames = ['ProductObj', 'SPUObj'];
        const mdApiName = param.objApiName;
        if (!apinames.includes(targetApiName)) return details;
        const allDetails = param.dataGetter.getDetails();
        const tDetails = {};
        // 原 details 当前从对象的第一条数据是 object_data，此处需要补全
        PPM.each(allDetails, (detail, key) => {
            if (key === mdApiName && details[mdApiName].length) {
                tDetails[key] = [details[mdApiName][0], ...detail]
            } else {
                tDetails[key] = detail;
            }
        })
        return tDetails;
    }

    // 编辑前勾子
    async _mdEditBeforeHook(plugin, param) {
        let mdApiName = param.objApiName;
        let lookUpApiName = param.fieldName;
        let masterData = param.dataGetter.getMasterData();
        let masterApiName = param.masterObjApiName;
        let {
            price_book_product_id,
            product_id,
            price_book_id,
            form_account_id,
            form_partner_id,
            quantity
        } = this.parent.getAllFields();
        let rowId = param.dataIndex[0];
        let rowData = param.dataGetter.getData(mdApiName, rowId);
        let isSpecial = [price_book_product_id, product_id, price_book_id].includes(lookUpApiName);
        if (isSpecial) {
            let baseConfig = this.getPickSelfConfig({
                mdApiName,
                lookUpApiName,
                masterData,
                rowData,
                isFrom: 'lookup'
            }, param);
            if (lookUpApiName === price_book_id) {
                return Object.assign({}, baseConfig, this.getPriceBookIdConfig(masterData, rowData));
            }
            //  过滤掉已选产品
            if (lookUpApiName === product_id) {
                return Object.assign({}, baseConfig);
            }
            // 切换价目表明细，需要根据 包含本单已选 开关状态，过滤已选数据；不包含要过滤
            if (lookUpApiName === price_book_product_id) {
                // 企业默认配置
                let defConfig = this.getConfig('tenant_whether_filter_order_select_product');
                // 个人级配置；优先
                let userConfig = await this.getIncludeSelected({key: 'whether_filter_order_select_product'});
                let includeSelected = PPM.hasValue(userConfig.value) ? userConfig.value == '1' : defConfig == '1';
                let res = Object.assign({}, baseConfig, {
                    modulePath: 'crm-modules/components/pickselfobject_classification/pickselfobject_classification',
                });
                res.skipSearch = true; // 屏蔽模糊搜索
                if (includeSelected) return res;
                let details = param.dataGetter.getDetail(mdApiName);
                let recordType = param.recordType;
                let filterIds = this.getAllProductIds(details, price_book_product_id, recordType);
                res.beforeRequest = (p) => {
                    let searchQueryInfo =(p.search_query_info?JSON.parse(p.search_query_info): {})||{} ;
                    let filters = searchQueryInfo.filters || [];
                    if (filterIds.length) filters.push({
                        field_name: "_id",
                        field_values: filterIds,
                        operator: "NIN"
                    });
                    searchQueryInfo.filters = filters;
                    p.search_query_info = JSON.stringify(searchQueryInfo);
                    p.account_id = masterData[form_account_id] || '';
                    p.partner_id = masterData[form_partner_id] || '';
                    p = this.addParams(p, rowData, param);
                    return p;
                };
                return res;
            }
        }
    }
    addParams(p, rowData, param) {
        let mdApiName = param.objApiName;
        let {
            quantity
        } = this.parent.getAllFields(mdApiName);
        // 不开价目表优先级 && 开阶梯 或 分层 根据数量过滤价目表明细
        if (!this.getConfig('priceBookPriority') && (this.getConfig('price_book_product_tiered_price') === '1'
        || this.getConfig('stratified_pricing') === '1')) {
            p.search_query_info = p.search_query_info || '{}';
            let searchQueryInfo = JSON.parse(p.search_query_info);
            let filters = searchQueryInfo.filters || [];
            if (p.object_data) {
                rowData[quantity] && (p.object_data.filter_quantity = rowData[quantity]);
                if (!p.object_data.pricebook_id) {
                    let priceBookIds = (filters.find(item => item.field_name === 'pricebook_id') || {}).field_values;
                    priceBookIds && (p.object_data.pricebook_id = priceBookIds[0]);
                }
            }
        }
        return p;
    }
    // 获取用户级配置，选产品或明细时，是否包含已选产品；
    getIncludeSelected(param) {
        let url = `FHH/EM1HNCRM/API/v1/object/biz_config/service/get_set_user_config`;
        let p = Object.assign({}, {
            key: '',
        }, param);
        return PPM.ajax(this.request, url, p);
    }

    // 价目表换为价目表明细id
    getPriceBookIdConfig(masterData, rowData) {
        return {
            skipSearch: true,
            cellData: rowData,
            modulePath: 'crm-modules/components/pickselfobject_pricebook/pickselfobject_pricebook',
            master_data: masterData,
            "apiname": "PriceBookProductObj",
            "api_name": "PriceBookProductObj",
            "fieldName": "price_book_product_id",
            "relatedname": "price_book_product_quote_lines_list",
            "target_related_list_name": "price_book_product_quote_lines_list",
            "isMultiple": false,
            "wheres": [{
                "connector": "OR",
                "filters": [{
                    "value_type": 0,
                    "operator": "EQ",
                    "field_name": "product_status",
                    "field_values": ["1"]
                }]
            }],
            "isNet": false,
            "source": "lookup",
            rootSource: 'PriceBookObj'
        }
    }

    // 获取所有产品id，不含子产品
    getAllProductIds(details = [], field = '', recordType) {
        let res = [];
        details.forEach(item => {
            if (!item.parent_rowId && item.record_type === recordType && item[field]) res.push(item[field]);
        });
        return res;
    }

    getHook() {
        return [
            {
                event: 'md.batchAdd.before',
                functional: this._batchAddBeforeHook.bind(this)
            }, {
                event: 'md.edit.before',
                functional: this._mdEditBeforeHook.bind(this)
            },
        ];
    }

}

