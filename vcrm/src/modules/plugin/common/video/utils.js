const config = {
    queryVideoUploadToken: {
        url: '/video/token/get',
        type: 'GET',
        warnings: '*',
        noPrefix: true,
        default: {}
    },
    fakeVideoStartUpload: {
        url: '/video/startupload',
        type: 'POST',
        dataType: 'json',
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        data,
        timeout: 30000,
        xhrFields: {
            withCredentials: true
        },
        crossDomain: true,
        crossSubDomain: true,
        noPrefix: true
    },
    fakeVideoUploadChunk: {
        url: '/video/uploadchunk'
    },
    fakeVideoCompleteUpload: {
        url: '/video/completeupload',
        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
        noPrefix: true,
        default: {}
    }
}

export const handleUploadFileToTxCloud = () => {
    return new Promise((resolve, reject) => {
        FS.util.api(
            {
                url: '/video/token/get',
                type: 'GET',
                cache: false,
                timeout: 15000,
                data: {
                    source: 0
                },
                success: function (data) {
                    if (data.code === 0) {
                        const token = data.tokenInfo.token
                        resolve({ token })
                    } else {
                        util.remind(3, $t('train.common.jktysbqzs_a5b73b'))
                    }
                },
                error: function (xhr, textStatus, errorThrown) {
                    util.remind(3, $t('train.common.hqwjsfbssb_664255'))
                }
            },
            { autoPrependPath: false, noTraceId: true }
        )
    })
}

export const fakeStartUpload = () => {
    return new Promise((resolve, reject) => {
        FS.util.api(
            {
                url: '/video/startupload',
                type: 'POST',
                dataType: 'json',
                contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                data,
                timeout: 30000,
                xhrFields: {
                    withCredentials: true
                },
                crossDomain: true,
                crossSubDomain: true,
                success (res) {
                    if (res.code === 0) {
                        resolve({status: true})
                    }
                }
            },
            { autoPrependPath: false, noTraceId: true }
        )
    })
}

export const processFile = () => {

}

export const fakeUploadChunk = (file) => {
    return new Promise((resolve, reject) => {
        FS.util.api(config.fakeVideoUploadChunk, {
            success: (data) => {
                resolve(data)
            }
        })
    })
}

export const fakeCompleteUpload = () => {
    return new Promise((resolve, reject) => {
        FS.util.api(config.fakeVideoCompleteUpload, {
            success: (data) => {
                resolve(data)
            }
        })
    })
}
