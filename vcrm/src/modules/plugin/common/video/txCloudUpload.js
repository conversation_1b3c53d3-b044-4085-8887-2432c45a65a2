let uploaders = []

export const txCloudUpload = param => {
    switch (param.name) {
        case 'upload_file':
            let upLoader = new UploadVideo({
                progress: data => {
                    param.progressCb && param.progressCb(data)
                },
                success: data => {
                    uploaders = _.filter(
                        uploaders,
                        a => a.upLoader !== upLoader
                    )
                    param.successCb && param.successCb(data)
                },
                fail: data => {
                    param.failCb && param.failCb(data)
                },
                file: param.file
            })
            uploaders.push({
                upLoader,
                file: param.file
            })
            upLoader.handleUploadFileToTxCloud()
            break
        case 'upload_abort':
            let tt = _.findWhere(uploaders, { file: param.file })
            tt && tt.upLoader.abort()
            break
        case 'upload_remove':
            uploaders = _.filter(uploaders, a => {
                if (a.file === param.file) {
                    a.upLoader.remove()
                    return false
                }
                return true
            })
            break
        case 'upload_retry':
            let ttt = _.findWhere(uploaders, { file: param.file })
            ttt && ttt.upLoader.retry()
            break
    }
}

const sliceSize = 250000

const uploadStageEumn = {
    tokenQueryBofore: 'tokenQueryBofore',
    uploadStartBefore: 'uploadStartBefore',
    chunkUploading: 'chunkUploading',
    completeUploadBofore: 'completeUploadBofore',
    confirmUploadBefore: 'confirmUploadBefore'
}

export class UploadVideo {
    constructor ({ progress, success, fail, file }) {
        this.token = ''
        this.name = ''
        this.chunkCount = 0
        this.percentage = 0
        this.start = 0
        this.end = 0
        this.chunk = 0
        this.uploadStage = uploadStageEumn.tokenQueryBofore
        this.progress = progress
        this.success = success
        this.fail = fail
        this.file = file
        this.uploaderXhr = null
    }

    async handleUploadFileToTxCloud () {
        const { token } = await this.queryVideoUploadToken()
        if (token) {
            this.token = token
            this.uploadStage = uploadStageEumn.uploadStartBefore
            this.processFile()
        }
    }

    queryVideoUploadToken () {
        return new Promise((resolve, reject) => {
            FS.util.api(
                {
                    url: '/video/token/get',
                    type: 'GET',
                    cache: false,
                    timeout: 15000,
                    data: {
                        source: 2
                    },
                    success: function (data) {
                        if (data.code === 0) {
                            const token = data.tokenInfo.token
                            resolve({ token })
                        } else {
                            util.remind(3, $t('train.common.jktysbqzs_a5b73b'))
                        }
                    },
                    error: function (uploaderXhr, textStatus, errorThrown) {
                        util.remind(3, $t('train.common.hqwjsfbssb_664255'))
                    }
                },
                { autoPrependPath: false, noTraceId: true }
            )
        })
    }

    fakeStartUpload (data) {
        return new Promise((resolve, reject) => {
            FS.util.api(
                {
                    url: '/video/startUploadV2',
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify(data),
                    timeout: 30000,
                    uploaderFields: {
                        withCredentials: true
                    },
                    crossDomain: true,
                    crossSubDomain: true,
                    success (res) {
                        if (res.code === 0) {
                            resolve(true)
                        }
                    }
                },
                { autoPrependPath: false, noTraceId: true }
            )
        })
    }

    async processFile () {
        const { size, name } = this.file
        const chunkCount = Math.ceil(size / sliceSize)
        this.chunkCount = chunkCount
        const fileName = encodeURI(name)
        this.name = name
        const bool = await this.fakeStartUpload({
            chunkCount,
            fileName,
            fileSize: size,
            token: this.token
        })
        if (!bool) {
            FxUI.Message.error($t('marketing.commons.scsb_54e5de'))
            this.fail()
            return
        }

        this.percentage = 0
        this.uploadStage = uploadStageEumn.chunkUploading
        this.uploadLoop()
    }

    uploadLoop () {
        const { size } = this.file
        const maxResendTimes = 5 // 最大失败重试次数
        let resendTimes = 0 // 当前重试次数
        this.end = this.start + sliceSize

        if (size - this.end < 0) {
            this.end = size
        }
        const s = this.slice(this.file, this.start, this.end)
        const request = () => {
            const uploaderXhr = this.fakeUploadChunk(s, this.chunk)
            this.uploaderXhr = uploaderXhr
            uploaderXhr.onreadystatechange = () => {
                if (
                    uploaderXhr.readyState === XMLHttpRequest.DONE &&
                    uploaderXhr.status === 200
                ) {
                    const res = JSON.parse(uploaderXhr.responseText)
                    if (res.code === 0) {
                        if (this.end < size) {
                            this.start += sliceSize
                            this.chunk += 1
                            this.percentage = (
                                (this.chunk / this.chunkCount) *
                                100
                            ).toFixed(1)
                            this.progress(this.percentage)
                            this.uploadLoop()
                        } else {
                            this.percentage = 100
                            this.progress(this.percentage)
                            this.uploadStage =
                                uploadStageEumn.completeUploadBofore
                            this.fakeCompleteUpload()
                        }
                    } else {
                        // 上传失败200ms后重试
                        // eslint-disable-next-line no-use-before-define
                        setTimeout(() => reRequest(), 200)
                    }
                }
            }
        }

        // 上传失败，重新上传
        const reRequest = () => {
            if (resendTimes < maxResendTimes) {
                request()
                resendTimes += 1
            } else {
                this.fail()
                FxUI.Message.error(
                    $t('marketing.pages.video.wlycscsbqs_8bf9fb')
                )
            }
        }

        request()
    }

    fakeUploadChunk (piece, chunk) {
        const formdata = new FormData()
        const xhr = new XMLHttpRequest()
        xhr.open('POST', '/video/uploadchunk', true)
        xhr.timeout = 3600000
        xhr.withCredentials = true
        formdata.append('chunk', chunk)
        formdata.append('token', this.token)
        formdata.append('file', piece)
        xhr.send(formdata)
        return xhr
    }

    fakeCompleteUpload () {
        return new Promise((resolve, reject) => {
            FS.util.api(
                {
                    url: '/video/completeUploadV2',
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify({
                        token: this.token
                    }),
                    success: data => {
                        if (data.code === 0) {
                            FxUI.Message.success(
                                $t('marketing.commons.sccg_a7699b')
                            )
                            setTimeout(() => {
                                this.uploadStage =
                                    uploadStageEumn.confirmUploadBefore
                                return this.fakeConfirmUpload()
                            }, 600)
                        } else {
                            FxUI.Message.error(
                                $t('marketing.commons.scsb_54e5de')
                            )
                        }
                    }
                },
                { autoPrependPath: false, noTraceId: true }
            )
        })
    }

    fakeConfirmUpload () {
        const me = this
        return new Promise((resolve, reject) => {
            FS.util.api(
                {
                    url: '/video/confirm',
                    type: 'POST',
                    dataType: 'json',
                    data: JSON.stringify({
                        token: this.token
                    }),
                    success: data => {
                        if (data.code === 0 && data.data.successed === true) {
                            me.success({ token: me.token })
                            resolve()
                        } else {
                            FxUI.Message.error(
                                $t('marketing.commons.scsb_54e5de')
                            )
                        }
                    }
                },
                { autoPrependPath: false, noTraceId: true }
            )
        })
    }

    remove () {
        this.abort()
    }

    abort () {
        if (this.uploaderXhr) {
            this.uploaderXhr.abort && this.uploaderXhr.abort()
        }
    }

    retry () {
        if (this.uploadStage === uploadStageEumn.tokenQueryBofore) {
            this.handleUploadFileToTxCloud()
        } else if (this.uploadStage === uploadStageEumn.uploadStartBefore) {
            this.processFile()
        } else if (this.uploadStage === uploadStageEumn.chunkUploading) {
            this.uploadLoop()
        } else if (this.uploadStage === uploadStageEumn.completeUploadBofore) {
            this.fakeCompleteUpload()
        } else if (this.uploadStage === uploadStageEumn.confirmUploadBefore) {
            this.fakeConfirmUpload()
        }
    }

    slice (file, start, end) {
        const slice =
            file.mozSlice || file.webkitSlice || file.slice || (() => {})

        return slice.bind(file)(start, end)
    }
}
