<template>
    <fx-dialog
        class="crm-comps-upload-bigfile-video"
        :visible.sync="showPanel"
        :close-on-click-modal="false"
        :close-on-press-escap="false"
        :before-close="handleClose"
        :title="$t('上传视频')"
        :z-index="9999"
        append-to-body
    >
        <div class="inner">
            <div class="upload-file-destination" v-if="isTxCloudPlay">
                <div class="upload-file-destination-title">{{$t('ava.video.upload.title')}}</div>
                <fx-radio-group size="mini" v-model="channel" :disabled="isSureDestination">
                    <fx-radio label="fxiaokeCloudPay">{{
                        $t('ava.video.upload.fxiaoke_system')
                    }}</fx-radio>
                    <fx-radio label="txCloudPlay">{{
                        $t('ava.video.upload.tx_cloud_system')
                    }}</fx-radio>
                </fx-radio-group>
            </div>
            <div class="fw">
                <fx-button size="small" type="primary">{{
                    $t('添加文件')
                }}</fx-button>
                <input
                    ref="fileInput"
                    @change="handleChange"
                    type="file"
                    multiple="multiple"
                    accept="*"
                    class="upload-input"
                />
            </div>

            <div class="file-wrapper">
                <div class="header fd">
                    <span class="name fss">{{ $t('文件名') }}</span>
                    <span class="size fss">{{ $t('大小') }}</span>
                    <span class="status fss">{{ $t('上传进度') }}</span>
                    <span class="operate fss">{{ $t('操作') }}</span>
                </div>
                <div
                    class="content"
                    @dragover.prevent=""
                    @drop.prevent="handleDrop"
                    @dragenter.prevent=""
                    draggable="true"
                >
                    <div
                        :key="file.id"
                        v-show="dFiles.length"
                        class="fd"
                        v-for="file in dFiles"
                    >
                        <span class="name fss"
                            ><i
                                style="margin-right: 4px"
                                :class="getFileIco(file.name)"
                            ></i
                            >{{ file.name }}</span
                        >
                        <span class="size fss">{{
                            formatSize(file.size)
                        }}</span>
                        <fx-progress
                            class="status fss"
                            :text-inside="true"
                            :stroke-width="16"
                            :percentage="file.progress"
                            :status="
                                file.status === 3 ? 'exception' : 'success'
                            "
                        ></fx-progress>
                        <div class="operate fss">
                            <span
                                class="fb"
                                @click="handleRetry(file)"
                                v-show="file.status === 3"
                                >{{ $t('重试') }}</span
                            >
                            <span
                                class="fb"
                                @click="handlePause(file)"
                                v-show="file.status === 1"
                                >{{ $t('暂停') }}</span
                            >
                            <span
                                class="fb"
                                @click="handleRetry(file)"
                                v-show="file.status === 4"
                                >{{ $t('继续') }}</span
                            >
                            <span class="fb" @click="handleDel(file)">{{
                                $t('删除')
                            }}</span>
                        </div>
                    </div>
                    <div v-show="!dFiles.length" class="drop-area">
                        <p>{{ $t('将文件拖至此区域进行上传') }}</p>
                    </div>
                </div>
            </div>
        </div>
        <span slot="footer" class="dialog-footer">
            <fx-button type="primary" @click="handleConfirm" size="small">{{
                $t('确 定')
            }}</fx-button>
            <fx-button @click="handleClose" size="small">{{
                $t('取 消')
            }}</fx-button>
        </span>
    </fx-dialog>
</template>

<script>
import crmRequire from '@common/require'
import { txCloudUpload } from './txCloudUpload'
export default {
    name: 'm-upload-big-file-video',

    /**
     * 组件的属性列表
     */
    props: {
        useFsFileService: {
            type: Boolean,
            default: true
        },
        supportTypes: {
            type: Array,
            default: () => []
        },
        isTxCloudPlay: {
            type: Boolean,
            default: false
        },
        dMaxNum: {
            type: Number,
            default: 1
        },
    },

    data () {
        return {
            showPanel: true,
            dFiles: [],
            channel: 'fxiaokeCloudPay' // 'fxiaokeCloudPay','txCloudPlay'
        }
    },
    computed: {
        isSureDestination () {
            return this.dFiles && this.dFiles.length > 0
        }
    },

    methods: {
        handleClose () {
            if (_.findWhere(this.dFiles, { status: 2 })) {
                var confirm1 = FS.crmUtil.confirm(
                    $t('确认关闭吗'),
                    $t('提示'),
                    () => {
                        confirm1.hide()
                        this.destroyUploadFiles()
                        this.$emit('cancel')
                    }
                )
            } else {
                this.$emit('cancel')
            }
        },

        handleConfirm () {
            if (_.find(this.dFiles, file => file.status !== 2)) {
                FxUI.Message({
                    isMiddler: true,
                    duration: 1000,
                    message: $t('paas.crm.uploadfile.tip'),
                    type: 'error'
                })
                return
            }

            this.$emit(
                'success',
                _.map(this.dFiles, file => ({
                    filePath: file.filePath,
                    size: file.size,
                    ext: file.ext,
                    name: file.name,
                    token: file.token,
                    channel: file.channel
                }))
            )
        },

        getFileIco (name) {
            return FS.crmUtil.getFileIco(name)
        },

        formatSize (size) {
            return FS.crmUtil.getFileSize(size)
        },

        handleDel (file) {
            this.dFiles = _.without(this.dFiles, file)
            if (file.channel === 'txCloudPlay') {
                txCloudUpload({ name: 'upload_remove', file: file.__file })
            } else {
                CRM.api['upload_remove']({
                    file: file.__file
                })
            }
        },

        handlePause (file) {
            file.status = 4
            if (file.channel === 'txCloudPlay') {
                txCloudUpload({ name: 'upload_abort', file: file.__file })
            } else {
                CRM.api['upload_abort']({
                    file: file.__file
                })
            }
        },

        handleRetry (file) {
            file.status = 1
            if (file.channel === 'txCloudPlay') {
                txCloudUpload({ name: 'upload_retry', file: file.__file })
            } else {
                CRM.api.upload_retry({ file: file.__file })
            }
        },

        createId (file) {
            this.dIndex || (this.dIndex = 0)
            return 'id_' + file.size + '_' + this.dIndex++
        },

        handleChange (e) {
            this.addFiles(e.target.files)
            this.$refs.fileInput.value = ''
        },

        handleDrop (e) {
            this.addFiles(e.dataTransfer.files)
        },

        validate (files) {
            let errors = []
            let limitMB =
                CRM.util.getCurrentEmployee().uploadFileSizeLimit || 100
            let list = _.filter(files, file => {
                let fileName = file.name
                let arisFileName = `${fileName}------`
                let ext = FS.util.getFileExtText(fileName)
                let errorTip
                if (!ext || !FS.util.getFileNamePath(fileName)) {
                    errorTip = `${arisFileName}${$t(
                        '不支持上传无文件类型的文件'
                    )}`
                } else if (fileName.length > 128) {
                    errorTip = `${fileName.substr(0, 10)}...${$t(
                        '文件名不能超过200个字符'
                    )}`
                } else if (file.size <= 0) {
                    errorTip = `${arisFileName}${$t(
                        '文件大小为0B或者文件不存在'
                    )}`
                } else if (_.findWhere(this.dFiles, { name: fileName })) {
                    errorTip = `${arisFileName}${$t('已选择相同文件')}`
                } else if (/(?:\/|\\|\:|\*|\?|"|<|>|\|)/.test(fileName)) {
                    errorTip = `${arisFileName}${$t(
                        '文件名不能含有'
                    )}\\ / : * ? " < > |`
                } else if (
                    ext === 'exe' ||
                    (this.supportTypes && !_.contains(this.supportTypes, ext))
                ) {
                    errorTip = `${arisFileName}${$t(
                        'crm.file.upload.nosupport'
                    )}`
                } else if (
                    this.useFsFileService &&
                    file.size > limitMB * 1024 * 1024
                ) {
                    errorTip = `${arisFileName}${$t(
                        '单个文件上传最大支持'
                    )}${limitMB}MB`
                }
                errorTip && errors.push(errorTip)
                return !errorTip
            })

            let len1 = list.length,
                len2 = this.dFiles.length
            if (len1 + len2 > this.dMaxNum) {
                errors.unshift(
                    $t('附件最多只能上传{{num}}个!', {
                        num: this.dMaxNum
                    })
                )
                let length = this.dMaxNum - len2
                list.length = length > 0 ? length : 0
            }

            return { list, errors }
        },

        addFiles (files) {
            const me = this
            let { list, errors } = this.validate(files)

            errors.length &&
                FS.crmUtil.alert(
                    _.map(
                        errors,
                        error => `<div style="margin-bottom:4px">${error}</div>`
                    ).join('')
                )

            _.each(list, file => {
                let newFile = {
                    name: file.name,
                    size: file.size,
                    status: 0, //0 等待上传 1 上传中 2上传完成 3上传错误 4暂停
                    progress: 0,
                    id: this.createId(file),
                    ext: FS.util.getFileExtText(file.name),
                    channel: this.channel,
                    token: '',
                    __file: file
                }
                me.dFiles.push(newFile)
                if (me.channel === 'txCloudPlay') {
                    me.handleUploadToTxCloud(newFile)
                } else {
                    me.handleUploadToFS(newFile)
                }
            })
        },

        handleUploadToFS (file) {
            file.status = 1
            CRM.api['upload_file']({
                file: file.__file,
                progressCb (progress) {
                    file.progress = progress
                },
                successCb (res) {
                    file.filePath = res.filePath
                    file.status = 2
                },
                failCb (res) {
                    file.status = 3
                    res &&
                        FxUI.Message({
                            isMiddler: true,
                            duration: 3000,
                            message: res.FailureMessage || $t('上传失败'),
                            type: 'error'
                        })
                }
            })
        },

        handleUploadToTxCloud (file) {
            file.status = 1
            txCloudUpload({
                name: 'upload_file',
                file: file.__file,
                progressCb (progress) {
                    file.progress = progress
                },
                successCb (res) {
                    file.token = res.token
                    file.filePath = '998'
                    file.status = 2
                },
                failCb (res) {
                    file.status = 3
                    res &&
                        FxUI.Message({
                            isMiddler: true,
                            duration: 3000,
                            message: res.FailureMessage || $t('上传失败'),
                            type: 'error'
                        })
                }
            })
        },

        destroyUploadFiles () {
            _.each(this.dFiles, file => {
                try {
                    if (file.channel === 'txCloudPlay') {
                        txCloudUpload({
                            name: 'upload_remove',
                            file: file.__file
                        })
                    } else {
                        CRM.api['upload_remove']({
                            file: file.__file
                        })
                    }
                } catch (e) {}
            })
        }
    }
}
</script>

<style lang="less" scoped>
.crm-comps-upload-bigfile-video {
    .upload-file-destination {
        display: flex;
        flex-direction: column;
        margin-bottom: 16px;
        .upload-file-destination-title {
            font-weight: 400;
            font-size: 14px;
            letter-spacing: 0px;
            color: var(--H2--color-neutrals15, #545861);
            line-height: 14px;
            margin-bottom: 6px;
        }
    }
    .fw {
        position: relative;
        display: inline-block;
        .el-button {
            z-index: 1;
            position: relative;
        }
        .upload-input {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            z-index: 10;
        }
    }
    .fd {
        position: relative;
        display: flex;
        height: 32px;
        line-height: 32px;
        box-sizing: border-box;
        border-bottom: 1px dotted #e5e5e5;

        .fss {
            padding: 0 8px;
            height: 100%;
            z-index: 10;
        }
        .name {
            flex: 1;
        }

        .size {
            width: 120px;
        }
        .status {
            width: 128px;
            align-items: center;
            display: flex;
        }
        .operate {
            width: 130px;
        }
    }
    .fb {
        margin-left: 4px;
        color: #407fff;
        cursor: pointer;
        &:hover {
            text-decoration: underline;
        }
    }
    .el-progress-bar {
        margin-top: 8px;
    }
    .header.fd {
        height: 28px;
        line-height: 28px;
        border-bottom: 1px solid #d8d8d8;
        background: #f2f4f6;
        .fss {
            border-right: 1px solid #d8d8d8;
        }
        .operate {
            border-right: none;
        }
    }
    .file-wrapper {
        border: 1px solid #d8d8d8;
        border-radius: 4px;
        margin-top: 16px;
    }
    .content {
        height: 204px;
        width: 100%;
        overflow: auto;
        background: #fffaf0;
    }
    .drop-area {
        margin-top: 80px;
        font-size: 24px;
        color: #d8d5d4;
        text-align: center;
    }
}
</style>