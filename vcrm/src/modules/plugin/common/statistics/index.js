/**
 * @desc: 返利优惠券统计政策 插件-Web
 * @author: lingj
 * @date: 6/27/22
 */
import PPM from "plugin_public_methods";
import Base from "plugin_base";
import { Subject } from "rxjs";

import Statistics from "./statistics.vue";

export default class StatisticsPolicy extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.statisticsFields = [];
        this.hasStatisticsComp = false;
        this.fields = {};
        this.statistics_info$ = new Subject();
        this.showTip = {
            "rebate": $t("请在客户账户组件中使用返利")
        };
        this.fieldConfig = {
            "SalesOrderObj": "receivable_amount"
        };
        this.couponHasPending = false;
    }

    getHook() {
        return [{
            event: "md.render.before",
            functional: this.statistics_mdRenderBefore.bind(this)
        }, {
            event: "form.render.after",  //　页面初始化结束
            functional: this.statistics_formRenderAfter.bind(this)
        }, {
            event: "price_policy.match.end",
            functional: this.statistics_policyChange.bind(this)
        }, {
            event: "coupon.matchCoupon.end",
            functional: this.statistics_policyChange.bind(this)
        }, {
            event: "dhtCustomerAccount.sum.amount",
            functional: this.statistics_policyChange.bind(this)
        }, {
            event: "form.change.end",
            functional: this.statistics_dataChange.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.statistics_dataChange.bind(this)
        }, {
            event: "md.del.end",
            functional: this.statistics_dataChange.bind(this)
        }, {
            event: "md.edit.end",
            functional: this.statistics_dataChange.bind(this)
        }, {
            event: "md.copy.end",   //行复制
            functional: this.statistics_dataChange.bind(this)
        }, {
            event: "crmRebate.match.change",
            functional: this.statistics_policyChange.bind(this)
        }, {
            event: "salesOrderHistory.calculate.end", //从历史订单添加，计算结束后
            functional: this.statistics_policyChange.bind(this)
        }, {
            event: "coupon.pendingLabel.update",
            functional: this.statistics_couponPendingLabel.bind(this)
        },{
            event: "bom.twiceConfig.after",  //bom二次配置
            functional: this.statistics_policyChange.bind(this)
        },{
            event: "customer.statistics.refresh",  //自定义刷新
            functional: this.statistics_policyChange.bind(this)
        },{
            event: "point.match.end",
            functional: this.statistics_policyChange.bind(this)
        },{
            event: "point.submit.showError",
            functional: this.point_show_error.bind(this)
        },{
            event: "point.match.end",
            functional: this.point_hide_error.bind(this)
        }];
    }

    statistics_mdRenderBefore(plugin, param, $wrapper) {
        this.detailApi = param.objApiName;
        this.masterApi = param.masterObjApiName;
        this.getConfigFields(param);
        if (this.hasStatisticsComp) {
            return {
                __execResult: {
                    footerSlot: [this.initPolicyDom.bind(this, plugin, param)],
                },
                __mergeDataType: {
                    array: "concat"
                }
            };
        } else {
            return {};
        }

    }

    // 获取配置字段
    getConfigFields(param) {
        const { masterObjApiName } = param;
        const res = param.dataGetter.getDescribeLayout();
        const isFormLayout = res.layout.layout_type === 'edit' && res.layout.layout_structure;
        const component = res.layout.components.find(c => c.api_name === "order_settlement");
        const hasFAccountComp = res.layout.components.some(c => c.api_name === "dht_order_customer_account");
        const fields = res.objectDescribe.fields;

        const policyFields = {
            pricePolicy: $t("促销"),
            rebate: $t("返利"),
            coupon: $t("优惠券"),
            loyalty_amount: $t("积分"),
        };

        // 获取从配置或者默认中获得的字段
        let configFields = this.getConfigOrDefaultFields({
            isFormLayout,
            component,
            masterObjApiName
        });

        // 过滤字段以适应布局
        // const layoutFields = param.dataGetter.getLayoutFields();
        // configFields = this.filterFieldsBasedOnLayout(configFields, layoutFields);

        // 设置警告和字段信息
        this.setWarningsAndFields({
            hasFAccountComp,
            configFields,
            policyFields,
            fields
        });
    }
    // 获取从配置或默认设置中得到的字段
    getConfigOrDefaultFields({ isFormLayout, component, masterObjApiName }) {
        let configFields = [];
        if (component) {
            configFields = this.preallocationField((component.list_header ?? []), masterObjApiName);
        } 
        return configFields;
    }

    // 根据布局过滤字段
    filterFieldsBasedOnLayout(configFields, layoutFields) {
        const layoutFieldSet = new Set(layoutFields);
        //特殊添加优惠券返利价格政策字段，避免被过滤
        const specialFieldSet = new Set(["coupon", "rebate", "pricePolicy"])
        return configFields.filter(c => layoutFieldSet.has(c) || specialFieldSet.has(c));
    }
    // 设置警告和字段信息
    setWarningsAndFields({ hasFAccountComp, configFields, policyFields, fields }) {
        if (!hasFAccountComp) {
            this.showTip = {
                "rebate": $t("请联系管理员在布局中配置客户账户组件,在客户账户组件中使用返利"),
                "faccount_amount": $t("请联系管理员在布局中配置客户账户组件")
            };
        }
        this.hasStatisticsComp = configFields.length >= 1;
        this.statisticsFields = configFields.map(f => {
            return policyFields[f]
                ? { apiname: f, label: policyFields[f] }
                : { apiname: f, label: fields[f].label };
        });
        this.fields = fields;
    }

    preallocationField(configFields, masterObjApiName) {
        const targetField = this.fieldConfig[masterObjApiName];
        if (targetField) {
            const idx = configFields.indexOf(targetField);
            if (idx >= 0) {
                configFields.splice(idx, 1);
                configFields.push(targetField);
            }
        }
        return configFields;
    }

    async initPolicyDom(plugin, param, $wrapper) {
        $('body').find('.crm-policy-statistics-wrapper').remove();
        $($wrapper).append(`<div class="crm-policy-statistics-wrapper"></div>`);

        const me = this,
            policyData = await CRM.util.composeAsync(
                this.getStatisticsData.bind(this),
                this.getStatisticsByParam.bind(this)
            )(param);

        const template = await this.customBeforeRenderComp(param);
        this.statistics = FxUI.create(Object.assign({}, template, {
            wrapper: '.crm-policy-statistics-wrapper',
            data() {
                return {
                    data: policyData,
                    showTip: me.showTip,
                    hasPending: me.couponHasPending,
                    info: {}
                }
            },
            created() {
                const vue = this;
                me.statistics_info$.subscribe(value => {
                    const info = value.info;
                    vue.info.loyalty_amount = info;
                    vue.$forceUpdate();
                })
            },
            methods: {
                isShowTip(apiname) {
                    return me.showTip[apiname];
                }
            }
        }))
        this.bindEvents(param);
        return {
            destroy() {
                me.destroy();
            }
        }
    }

    getStatisticsByParam(param) {
        return {
            masterData: param.dataGetter.getMasterData(),
            detailData: param.dataGetter.getDetail(this.detailApi),
        };
    }

    async getStatisticsData(data) {
        const me = this,
            { masterData, detailData } = data,
            miscContent = masterData.misc_content || {},
            currencyUnit = ` ${masterData.mc_currency || $t("元")}`;

        const policyData = this.statisticsFields.map(f => {
            let amount = 100;
            switch (f.apiname) {
                case 'pricePolicy':
                    const isGiftOrBom = (item) => item.gift_type == "manual" || item.parent_gift_key || item.parent_rowId,
                        productDetail = detailData.filter(d => !isGiftOrBom(d));
                    amount = this.countTotal(productDetail, "policy_dynamic_amount", masterData.policy_dynamic_amount);
                    amount += currencyUnit;
                    break;
                case 'coupon':
                    amount = -1 * this.countTotal(miscContent.coupon, 'amount', 0);
                    amount += currencyUnit;
                    break;
                case 'rebate':
                    amount = -1 * this.countRebateTotal(miscContent, 'amount', 0);
                    amount += currencyUnit;
                    break;
                case 'loyalty_amount':
                    amount = Number(masterData.loyalty_amount || 0);
                    amount += currencyUnit;
                    break;
                default:
                    let field = me.fields[f.apiname],
                        unit = "";
                    if (field) {
                        switch (field.type) {
                            case "percentile":
                                unit = "%";
                                break;
                            case "currency":
                                unit = currencyUnit;
                                break;
                        }
                    }
                    amount = (masterData[f.apiname] || 0) + unit;
            }
            return Object.assign(f, {
                amount: amount
            })
        })

        const customerData = await this.runPlugin('crmStatistics.compUpdate.before', {
            masterData,
            detailData,
            statisticsData: policyData
        });

        return customerData??policyData;
    }

    /**
     * 组件渲染前，可自定义结算组件数据和模板
     * data:自定义数据，格式为
     * [{
     *      apiname:"test",
            amount:"1111",
            label:"测试补充数据"
     * }]
     * template: .vue文件，类似statistics.vue,可以包含完整的template和style
     */
    async customBeforeRenderComp(param) {
        const options= await this.runPlugin('crmStatistics.compRender.before', {
            param,
        });
        return options?.template ?? Statistics
    }

    /**
     * 组件渲染前，可自定义样式
     * styles: 样式对象，格式为：{".class-name":{"background-color":"gray"}}
     */
    async customAfterRenderComp(plugins, param) {
        const wrapperClass = ".crm-policy-statistics-wrapper";
        const options = await this.runPlugin('crmStatistics.compRender.after', {
            param,
            wrapper: wrapperClass
        });
        if (options?.styles) {
            this.applyCustomStyles(wrapperClass, options.styles);
        }
    }

    applyCustomStyles(wrapperClass, styles) {
        Object.entries(styles).forEach(([selector, styleRules]) => {
            $(`${wrapperClass} ${selector}`).css(styleRules);
        });
    }

    //主对象渲染结束
    statistics_formRenderAfter(plugins, param) {
        this.statistics_dataChange(plugins, param);
        this.customAfterRenderComp(plugins, param);
    }


    //价格政策、账户组件、优惠券变化
    statistics_policyChange(name, obj) {
        this.statistics_dataChange(null, obj.param);
    }

    point_match_end(plugins, obj) {
        const masterData = obj.calculatedMasterData;
        const dataGetter = obj.param.dataGetter;
        const detailData = dataGetter.getDetail(this.detailApi);
        this.updateStatistics({masterData, detailData});
    }

    //主从数据变化
    async statistics_dataChange(plugins, param) {
        await CRM.util.composeAsync(
            this.updateStatistics.bind(this),
            this.getStatisticsByParam.bind(this)
        )(param);
    }

    //保存前校验数据改动
    statistics_beforeSubmitChange(plugins, data) {
        this.updateStatistics.bind(data);
    }

    //更新结算组件
    async updateStatistics(args) {
        if (!this.statistics_policyChange) {
            return;
        }
        const data = await this.getStatisticsData(args);
        data.forEach(d => {
            $('.crm-policy-statistics-wrapper').find(`.value-${d.apiname}`).html(d.amount);
        })
    }

    point_show_error(plugin, param) {
        const { info } = param;
        this.statistics_info$.next({
            info,
        });
    }

    point_hide_error(plugin, param) {
        this.statistics_info$.next({
            info: '',
        });
    }

    countTotal(arr, key, total = 0) {
        return (arr || []).reduce((total, cur) => {
            total = CRM.util.accAdd(total, (cur[key] || 0));
            return total;
        }, total);
    }

    countRebateTotal(miscContent, key, total = 0) {
        const { rangeRebate = [], rebate = [] } = miscContent;
        total = this.countTotal(rebate, key, total);
        rangeRebate.forEach(r => {
            total = this.countTotal(r.rangeRebates, key, total);
        });
        return total;
    }

    statistics_couponPendingLabel(plugins, obj) {
        this.couponHasPending = obj.hasPending;
        if (this.statistics) {
            this.statistics.hasPending = obj.hasPending;
        }
    }

    bindEvents() {
        const me = this;

        $('body').delegate('.j-show-detail', 'click', async (event) => {
            event.stopPropagation && event.stopPropagation();

            let $item = $(event.target);
            if (!$item.hasClass("j-item-amount")) {
                $item = $(event.target).parents(".j-item-amount");
            }
            switch ($item.attr("data-id")) {
                case 'coupon':
                    return me.runPlugin('statistics.showCoupon.start', {});
                case 'loyalty_amount':
                    return me.runPlugin('statistics.showPoint.start', {});
                default:
                    break;
            }
        });
    }

    unbindEvents() {
        $('body').undelegate('.j-show-detail', 'click');
    }

    destroy() {
        this.unbindEvents();
        this.statistics && this.statistics.destroy();
        this.statistics = null;
    }
}
