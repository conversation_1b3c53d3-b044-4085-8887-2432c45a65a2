/*
 * @Descripttion: md组件适用范围字段(可售范围、价格政策)
 * @Author: chaoxin
 * @Date: 2022-06-21 18:19:31
 * @LastEditors: chaoxin
 * @LastEditTime: 2023-12-13 15:58:49
 */
import './filtertabs.less'

export default class FilterTabs {

    constructor(config, parent) {
        this.parent = parent;
        this.config = config;
        // filtertab 组件实例
        this.comp = null;
        // filtertab 组件实例化参数
        this.options = {};
        // 缓存从对象数据，tab切换到非从对象选项时要清空，切换回来要填充
        this.cacheData = [];
        // 从对象 apiname
        this.apiName = '';
        // 从对象业务类型
        this.recordTypes = [];
    }

    _mdRenderBefore(plugin, param) {
        console.log('FilterTabs md.render.before ----------------')
        this.apiName = param.objApiName;
        if (!this.apiName) return;
        let fieldData = param.dataGetter.getMasterData()[this.config.field];
        if(!fieldData) return;
        if (typeof fieldData === 'string') {
            fieldData = JSON.parse(fieldData);
        }
        if (fieldData.type !== 'FIXED') {
            param.dataUpdater.delDetail(this.apiName)
        }

        const recordTypeOpts = param.dataGetter.getFieldAttr('record_type', this.apiName)?.options || [];
        this.recordTypes = recordTypeOpts.map((i) => i.api_name);
        this.recordTypes.forEach((recordType) => {
            param.UI.hideDetailsComp(this.apiName, recordType);
        });
        return {
            headerSlot: [this.renderFilterTabs.bind(this, plugin, param, true)]
        }
    }

    // _mdRenderAfter(plugin, param) {
    //     if (!this.apiName) return;
    //     let fieldData = param.dataGetter.getMasterData()[this.config.field];
    //     if(!fieldData) return;
    //     if (typeof fieldData === 'string') {
    //         fieldData = JSON.parse(fieldData);
    //     }
    //     console.log('FilterTabs md.render.after ----------------', fieldData.type)
    //     if (fieldData.type !== 'FIXED') {
    //         // 这里直接利用hook的ps删除，多个从对象时有问题
    //         setTimeout(() => {
    //             this.clearDetailData();
    //         }, 10)
    //     }
    // }

    _getMdComp() {
        const me = this;
        return class MdComp {
            constructor(options) {
                this.options = options;
            }

            afterShow() {
                const factory = me.parent.pluginService.api.pluginServiceFactory({
                    pluginApiName: me.parent.pluginParam.pluginApiName
                });
                me.recordTypes.forEach((recordType) => {
                    factory.UI.showDetailsComp(me.apiName, recordType);
                })
                factory.end();
            }

            afterHide() {
                const factory = me.parent.pluginService.api.pluginServiceFactory({
                    pluginApiName: me.parent.pluginParam.pluginApiName
                });
                me.recordTypes.forEach((recordType) => {
                    factory.UI.hideDetailsComp(me.apiName, recordType);
                })
                factory.end();
            }

            getValue() {
                return 'FIXED';
            }

            setValue() {}
        }

    }

    _formatFields(fields) {
        return Object.entries(fields).reduce((cur, pre) => {
            const [key, value] = pre;
            !!value.is_index && (cur[key] = value);
            return cur;
        }, {});
    }

    // 重置filtertabs组件选项，会重新渲染整个组件
    // 弃用，设计不合理，如需动态更新options，可以监听 filtertabs.init.before 时间
    resetOptions(options) {
        if (!Object.keys(options).length) return;
        this.options = $.extend(
            true,
            {},
            this.options,
            options
        );
        // 若此时 comp 还未渲染，不需要重新 render
        if (this.comp) {
            this.comp.destroy();
            this.renderFilterTabs(null, null, false)
        }
    }

    // 动态更新tab选项的状态，显示隐藏、不可选中
    // 仅重新渲染tab选项，tab的内容组件不变
    updateTabOptions(type, opts) {
        this.comp?.updateTabOptions(type, opts);
    }

    renderFilterTabs(plugin, param, isInit, $wrapper) {
        const me = this;

        this.parent.pluginService.api.import('crm-modules/common/filtertabs/filtertabs').then((FilterTabs) => {
            this.parent.pluginService.api.import('crm-modules/common/filtergroup/filtergroup').then((FilterGroup) => {
                // 这里手动嵌套了一层，filtertab组件异常提示会在父元素后追加异常信息
                const wrapperEl = $('<div class="crm-filtertabs-md20-wrapper"></div>');
                wrapperEl.appendTo($wrapper);

                if (isInit) {
                    const compDefaultOptions = FilterTabs.prototype.defaultOptions;
                    // 合并配置，优先级低-->高 filtertab组件默认配置，本插默认配置，初始化插件传入的配置
                    this.options = $.extend(
                        true,
                        {},
                        compDefaultOptions,
                        {
                            el: wrapperEl,
                            defaultValue: param.dataGetter.getMasterData()[this.config.field],
                            title: this.parent.i18n('适用范围'),
                            tabs: {
                                filter: {
                                    options: {
                                        formatFields: this._formatFields,
                                        width: 840,
                                        helper: FilterGroup.helper
                                    }
                                },
                                selector: {
                                    isHide: true
                                },
                                md: {
                                    type: 'FIXED',
                                    index: 3,
                                    isHide: false,
                                    selected: false,
                                    component: this._getMdComp(),
                                    options: {
                                        $target: $wrapper,
                                        $wrapper: wrapperEl
                                    }
                                }
                            }
                        },
                        this.config,
                        this.options
                    )
                }

                this.parent.runPlugin(
                    'filtertabs.init.before',
                    {options: this.options, objApiName: this.apiName, isReset: !isInit, param}
                ).then((rst) => {
                    const {options = {}, isReplace = false} = rst || {};
                    this.options = isReplace ? options : $.extend(true, {}, this.options, options);

                    this.comp = new FilterTabs(this.options);
                    this.comp.render();
                    this.comp.on('change', () => {
                        const value = this.updateCompValue();
                        this.comp?.hideError();
                        if (value?.type === 'FIXED') {
                            this.updateDetailData();
                        } else {
                            this.clearDetailData(true);
                        }
                    });
                });
            })
        });

        return {
            destroy() {
                console.log('destroy ----- filtertab')
                me.comp?.destroy();
            }
        }
    }

    // 更新组件数据到form表单
    updateCompValue(value) {
        value = value || this.comp.getValue();
        const factory = this.parent.pluginService.api.pluginServiceFactory({
            pluginApiName: this.parent.pluginParam.pluginApiName
        });
        factory.dataUpdater.updateMasterExt({
            [this.config.field]: JSON.stringify(value)
        });
        factory.end();
        return value;
    }

    // 重置从对象数据，tab切换到非从对象，提交时清空数据
    clearDetailData(isCache = false) {
        if (this.cacheData.length) return;
        const factory = this.parent.pluginService.api.pluginServiceFactory({
            pluginApiName: this.parent.pluginParam.pluginApiName
        });
        const data = factory.dataUpdater.delDetail(this.apiName);
        if (isCache) this.cacheData = data;
        factory.end();
    }

    updateDetailData() {
        if (!this.cacheData.length) return;
        const factory = this.parent.pluginService.api.pluginServiceFactory({
            pluginApiName: this.parent.pluginParam.pluginApiName
        });
        factory.dataUpdater.add(this.cacheData);
        this.cacheData = [];
        factory.end();
    }

    // 提交前置钩子
    _formSubmitBefore(plugin, param) {
        if (!this.apiName) return;
        const compValue = this.updateCompValue();
        // 1. filtertab 校验
        // 2. filtertab 选择从对象，从对象是否有值
        const detailValidate = !(compValue?.type === 'FIXED' && !param.dataGetter.getDetail(this.apiName)?.length);
        if (!this.comp?.validate() || !detailValidate) {
            const displayName = param.dataGetter.getDescribe(this.apiName)?.display_name;
            const errorMsg = `【${displayName}】` + (!detailValidate ? plugin.api.i18n('从对象不能为空') : plugin.api.i18n('从对象校验失败'));
            plugin.api.alert(errorMsg);
            plugin.skipPlugin();
        }
    }
    getHook() {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            },
            // {
            //     event: 'md.render.after',
            //     functional: this._mdRenderAfter.bind(this)
            // },
            {
                event: 'form.submit.before',
                functional: this._formSubmitBefore.bind(this)
            }
        ]
    }
}
