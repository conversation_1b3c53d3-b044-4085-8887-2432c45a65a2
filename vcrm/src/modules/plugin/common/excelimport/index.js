/*
 * @Descripttion:
 * @Author: LiAng
 * @Date: 2022-07-19 14:25:10
 * @LastEditors: chaoxin
 * @LastEditTime: 2024-07-31 14:45:37
 */
import PPM from 'plugin_public_methods'
import Base from "plugin_base";

export let util = {
    getPluginField(plugin = {}, fields = [], param = {}) {
        let result = {};

        if (plugin.params && plugin.params.details) {
            let detail = plugin.params.details.filter(item => item.objectApiName === param.objApiName)[0];

            if (detail && detail.fieldMapping) {
                if (fields.length) {
                    fields.forEach(field => {
                        result[field] = detail.fieldMapping[field];
                    })
                }
                else {
                    Object.keys(detail.fieldMapping).forEach(field => {
                        result[field] = detail.fieldMapping[field];
                    })
                }
            }
        }

        return result;
    },

    getPlugin(plugins, name) {
        return plugins.filter(item => item.pluginApiName === name)[0];
    },

    getDisableField(plugins = [], getConfig, param = {}, priceBookPriority) {
        let mapField = {};

        // 是否屏蔽价目表
        this.isBlockPriceBook = util.getIsBlockPriceBook(param, priceBookPriority);
        const blockArray = [
            'price_book_product_id', //价目表明细
        ];

        if (this.isBlockPriceBook) {
            blockArray.push('price_book_id'); //价目表
        }

        // 默认屏蔽
        mapField = Object.assign(mapField, util.getPluginField(util.getPlugin(plugins, 'price-service'), blockArray, param));

        // 开多单位后屏蔽
        if (getConfig('multiunitStatus')) {
            mapField = Object.assign(mapField, util.getPluginField(util.getPlugin(plugins, 'multi-unit'), [
                'stat_unit_count', // 统计单位数量
                'conversion_ratio', // 转换比例
                'other_unit_quantity' // 其他单位合计数量
            ], param));
        }

        // 开启价格政策后屏蔽
        const key=`price_policy_${param.masterObjApiName}`;

        if (getConfig('advancedPricing') && getConfig(key) === '1') {
            mapField = Object.assign(mapField, util.getPluginField(util.getPlugin(plugins, 'price_policy'), [
                'master_policy_id',
                'price_policy_id',
                'price_policy_rule_ids',
                'policy_dynamic_amount',
                'group_key',
                'parent_gift_key',
                'gift_amortize_price',
                'is_giveaway'
            ], param));
        }

        return Object.values(mapField);
    },

    getIsBlockPriceBook(param, priceBookPriority) {
        const describeLayout = param.dataGetter.getDescribeLayout(param.objApiName);
        const price_book_id = describeLayout?.objectDescribe?.fields?.price_book_id;
        // 没有价目表时屏蔽
        if (!price_book_id) {
            return true;
        }
        // 价目表不可用时屏蔽
        if (!price_book_id?.is_active) {
            return true;
        }
        // 价目表只读时屏蔽
        if (price_book_id?.is_readonly) {
            return true;
        }
        // 价目表强制优先级时屏蔽
        if (priceBookPriority) {
            return true;
        }
        return false;
    },

    isPriceBookFromImport(result) {
        if (!result || !result.data || !result.data.length) {
            return;
        }
        result.data.forEach(item => {
            // 如果是从导入来的数据且有价目表id，则标记为从导入来的价目表
            item.price_book_id__from_import = !!item.price_book_id;
        })
    },

    formatAttributeInputString(str) {
        return this.convertChinesePunctuationToEnglish(this.removeAllSpace(str));
    },

    convertChinesePunctuationToEnglish(str) {
        return str.replace(/，/g, ',').replace(/；/g, ';').replace(/：/g, ':').replace(/。/g, '.').replace(/？/g, '?').replace(/！/g, '!');
        // 替换全部中文符号
    },

    removeAllSpace(str) {
        return str.replace(/\s+/g, '');
    }
};
export default class ExcelImport extends Base {
    constructor(pluginService, pluginParam) {
        super(pluginService, pluginParam);

        this.lookupField = {
            api_name: 'product_id',
            target_api_name: 'ProductObj',
            target_related_list_name: 'salesorderproduct_product_list',
            __action_from: 'excel_import'
        };
    }

    /**
     * 渲染ecxcel导入按钮
     *
     * @param {object} plugin
     * @param {object} param
     * @returns
     */
    _mdRenderBefore(plugin, param) {
        // // 使用属性、CPQ（需配置客开插件支持）时不支持Excel导入
        // 属性限制 需要放开属性的限制
        // let pluginApiNames = [];
        let plugins = plugin.api.getPlugins() || [];
        let res = plugin.preData || {};

        res.buttons = res.buttons || {};

        // if (plugins.filter(item => pluginApiNames.includes(item.pluginApiName)).length) {
        //     res.buttons.del = res.buttons.del || [];
        //     res.buttons.del.push('Import_Excel');
        // }

        // 开了价目表或已经有了白名单，Import_Excel加入白名单
        if (res.buttons.retain) {
            res.buttons.retain = res.buttons.retain || [];
            res.buttons.retain.push('Import_Excel');
        }

        this._cachePlugins = plugins;

        return res;
    }

    /**
     * 获取校验对象
     *
     * @returns
     */
    excelGetValidateObj() {
        if(!this.getConfig('openPriceList')){
            return {}
        }
        return {
            QuoteLinesObj: [
                'product_id', //产品名称
            ],
            SalesOrderProductObj: [
                'product_id', //产品名称
            ],
            NewOpportunityLinesObj: [
                'product_id', //产品名称
            ],
            SaleContractLineObj: [
                'product_id', //产品名称
            ],
        };
    }

    /**
     * 屏蔽导入字段
     *
     * @param {object} param
     * @param {array} columns
     * @returns
     */
    excelFiltersMappingFields(param, columns = []) {
        const priceBookPriority = this.getConfig('priceBookPriority');
        let fields = util.getDisableField(this._cachePlugins, (...args) => {
            return this.getConfig.apply(this, args);
        }, param, priceBookPriority);

        columns =  columns.filter(item => !fields.includes(item.id));

        return columns;
    }

    /**
     * 校验必选字段
     *
     * @param {array} columns
     * @param {array} columnsAll
     * @param {object} opts
     * @returns
     */
    excelValidateMappingFields(param, columns, columnsAll, opts) {
        let validateObj = this.excelGetValidateObj();

        if (!Object.keys(validateObj).includes(opts.apiName)) {
            return;
        }
        let r1 =  this.runPluginSync('excelImport.validateMappingFields.before', {
            param,
        });
        if(r1 && r1.isNoCheck){
            return;
        }

        let validateFields = validateObj[opts.apiName];
        let unCheckedField = validateFields.filter(item => !columns.some(cItem => cItem.id === item));
        // let info = columnsAll.filter(item => unCheckedField.includes(item.id)).map(item => item.label).join(',');
        let fields = param.dataGetter.getDescribe(opts.apiName).fields || {};
        let info = unCheckedField.map(item => fields[item].label).join(',');

        if (unCheckedField.length) {
            return `${$t('缺少必选字段')}: ${info}`;
        }
    }

    /**
     * 未匹配到产品名称的数据不能导入
     * @param {*} data
     * @param {*} param
     * @returns
     */
    excelImportCheckProductId(data, param) {
        let msg = {};

        // let validateObj = this.excelGetValidateObj();

        // if (!Object.keys(validateObj).includes(param.objApiName)) {
        //     return {
        //         msg,
        //         data
        //     };
        // }

        let _data = data;
        let errorData = [];


        // 开了价目表或多单位必须有product_id字段
        if (this.getConfig('openPriceList') || this.getConfig('multiunitStatus')) {
            let {product_id} = this.getAllFields(param.objectApiName);
            _data = data.filter(item => item[product_id]);
            errorData = data.filter(item => !item[product_id]);
        }

        if (_data.length !== data.length) {
            // msg.push($t('未匹配到产品名称的数据不能导入'));
            msg = this.getAlertMessage(errorData, $t('未匹配到产品名称的数据不能导入'), 'error',  )
        }

        return {
            msg,
            data: _data,
        };
    }

    /**
     * excel导入支持多单位
     * @param {*} data
     * @param {*} param
     * @returns
     */
    excelImportCheckMultiUnit(data, param, pluginService) {
        let msg = {};
        let multiUnitPlugin = this.getMultiUnitPlugin();

        if (!multiUnitPlugin) {
            return Promise.resolve({
                msg,
                data
            });
        }

        return new Promise((resolve, reject) => {
            let unitErr = [];
            let unit = 'unit';
            let {product_id, actual_unit, is_multiple_unit} = this.getPluginField(multiUnitPlugin, ['product_id', 'actual_unit','is_multiple_unit'], param);

            if (!product_id || !actual_unit) {
                return resolve({
                    msg,
                    data
                });
            }

            // 补充字段
            function addField() {
                let fields = {
                    [is_multiple_unit]: 'is_multiple_unit',
                };
                data.forEach(item => {
                    for (let [key, value] of Object.entries(fields)){
                        item[key] = item[product_id + '__ro'] && item[product_id + '__ro'][value];
                    }
                });
            }

            // 排序数据
            function sortData(indexs = []) {
                return function(data = []) {
                    let res = [];

                    indexs.forEach(index => {
                        let arr = data.filter(item => item.rowId === index);

                        if (arr.length) {
                            res.push(arr[0]);
                        }
                    })

                    return res;
                }
            }

            addField();

            let sortDataHandle = sortData(data.map(item => item.rowId));
            let dataSingle = data.filter(item => item[product_id] && item[product_id + '__ro'] && !item[product_id + '__ro'].is_multiple_unit);
            let dataMulti = data.filter(item => item[product_id] && item[product_id + '__ro'] && item[product_id + '__ro'].is_multiple_unit);

            // 非多单位产品
            if (dataSingle.length) {
                // 单位字段值不是产品的单位，不能导入
                dataSingle = dataSingle.filter(item => {
                    let flag = true;

                    if (item[product_id + '__ro'] && item[product_id + '__ro'][unit] && item.__fields && item.__fields[actual_unit] && item.__fields[actual_unit].options) {
                        let options = item.__fields[actual_unit].options;
                        let value = item[product_id + '__ro'][unit];
                        let opts = options.filter(item => item.value === value);

                        if(!opts[0] || opts[0].value !== item[actual_unit]) {
                            unitErr.push(item);
                            flag = false;
                        }

                        return opts[0] && opts[0].value === item[actual_unit];
                    }

                    return flag;
                });

                // 设置单位字段只读
                // dataSingle.forEach(item => {
                //     pluginService.dataUpdater.setReadOnly({
                //         fieldName: [actual_unit],
                //         dataIndex: item.rowId,
                //         objApiName: param.objApiName,
                //         status: true
                //     })
                // })
            }

            // 多单位产品
            if (dataMulti.length) {
                // 获取可用单位，单位字段值不在可用单位内的数据不能导入
                CRM.util.getProductUnitOptionsBatch({
                    dataIds: _.pluck(dataMulti, product_id),
                    sourceObjApiName: param.objApiName
                }).then((result) => {
                    let optionList = result.optionList || [];

                    dataMulti = dataMulti.filter(item => {
                        let flag = true;

                        optionList.forEach(cItem => {
                            if (cItem.productId === item[product_id]) {
                                if (!(cItem.options || []).filter(opt => opt.value === item[actual_unit]).length) {
                                    unitErr.push(item);
                                    flag = false;
                                }
                            }
                        })

                        return flag;
                    });

                    if (unitErr.length) {
                        // msg.push($t('未匹配到单位的数据不能导入'));
                        msg = this.getAlertMessage(unitErr, $t('未匹配到单位的数据不能导入'), 'error' )
                    }

                    resolve({
                        msg,
                        data: sortDataHandle(([]).concat(dataSingle, dataMulti)),
                        unitErr
                    })
                });
                return;
            }

            if (unitErr.length) {
                // msg.push($t('未匹配到单位的数据不能导入'));
                msg = this.getAlertMessage(unitErr, $t('未匹配到单位的数据不能导入'), 'error' )
            }

            resolve({
                msg,
                data: sortDataHandle(([]).concat(dataSingle, dataMulti)),
                unitErr
            })
        })
    }

    /**
     * 验证处理导入的数据
     *
     * @param {object} param
     * @param {object} data
     * @param {object} pluginService
     * @returns
     */
     excelParseFormatDatas(param, plugin, obj) {
         let data = obj.formatDatas;
        let me = this;
        let msg = {};
        return new Promise(async (resolve, reject) => {
            // 先缓存数据原rowId，处理完数据还要恢复rowId
            data.forEach(item => {
                item.__rowId = item.rowId;
                item.__from = this.lookupField.__action_from;
            });

            // 导入bom标准化removeAllSpace
            let r1 = this._addRowIdAndPid(data, plugin);
            if(r1?.res === false) return resolve(r1.messages);

            let res = await this.runPlugin('excelImport.parseFormData.before',{
                data: data,
                param,
            });

            if(res && res.data) data = res.data;

            let productIdResult = me.excelImportCheckProductId(data, param);
            const attributeIsLegal =  this.validateAttributeData(productIdResult, param);
            if (!attributeIsLegal.status) {
                msg = this.mergeAlertMsg([productIdResult.msg, attributeIsLegal.msg]);
                msg.formatDatas =  productIdResult.data;
                return resolve(msg);
                // return plugin.skipPlugin();
            }
            util.isPriceBookFromImport(productIdResult);

            let checkPriceBookRes = await this.checkPriceBook(productIdResult.data, param);

            let checkPPRes = this.checkPricingPeriod(productIdResult.data);

            // 开bom，处理bom数据；
            if(this._getPluginBom()){
                let data = productIdResult.data;
                let {normalData, bomData} = this._splitData(data);
                let newBomData = await this.parseBomData(bomData, param);
                if(!newBomData) return resolve({formatDatas: []});
                if(newBomData?.messages) return resolve(newBomData.messages);
                let r2 = this.checkBomVersion(normalData);
                if(!r2.status) return resolve(r2.messages);
                data = [...newBomData, ...normalData];
                productIdResult.data = data;
            }

            let multiUnitResult = await me.excelImportCheckMultiUnit(productIdResult.data, param);

            // msg = msg.concat(productIdResult.msg, multiUnitResult.msg);
            msg = this.mergeAlertMsg([productIdResult.msg, multiUnitResult.msg, checkPriceBookRes.msg, checkPPRes.msg]);

            // 缓存导入的数据
            this._cacheImportData = PPM.deepClone(multiUnitResult.data);

            if (msg.messages && Object.keys(msg.messages).length) {
                msg.formatDatas =  multiUnitResult.data;
                resolve(msg);
            } else {
                resolve({formatDatas: multiUnitResult.data});
            }
        })
    }

    isPricing_modeOne(data){

    }

    // 校验非周期性产品，期数不为 1 ，重置为 1，提示用户
    checkPricingPeriod(data){
        let {pricing_period, product_id} = this.getSomePluginFields(['period_product', ]);
        if(!pricing_period) return {res: true, msg: {}};
        let errorData = [];
        data.forEach(item => {
            if(item[product_id + '__ro']?.pricing_mode === 'one' && PPM.hasValue(item[pricing_period]) && Number(item[pricing_period] !== 1)){
                item[pricing_period] = 1;
                errorData.push(item);
            }
        });
        return {
            res: false,
            msg: this.getAlertMessage(errorData,  $t('crm.excelbom.checkPricingPeriod1'), 'tip') // 非周期性产品，期数已重置为1
        };
    }

    // 校验普通产品里是否有产品包
    checkBomVersion(normalData){
         let r = [];
         let {product_id} = this.getAllFields();
         normalData.forEach(item => {
             if(item[product_id + '__ro']?.is_package) {
                 r.push(item)
             }
         });
        if(r.length) {
            return {
                status: false,
                messages: this.getAlertMessage(r, this.i18n('crm.excelbom.alert8'), 'error', true )  //  这些产品为产品组合，必须导入BOM版本和相关虚拟key字段
            }
        }
        return {
            status: true,
        }
    }

    /**
     * 执行导入前
     *
     * @param {object} plugin
     * @param {object} param
     * @returns
     */
    async excelImportBefore(plugin, param) {
        let masterData = param.dataGetter.getMasterData();
        let {i18n, alert} = this.pluginService.api;
        let {form_account_id} = this.getPluginFields('price-service');
        if (!masterData[form_account_id]) {
            alert(i18n('请先选客户'));
            return plugin.skipPlugin();
        }

        let {product_id} = this.getAllFields();
        let forceImportFields = [product_id];
        forceImportFields = this._addBomFields(forceImportFields);
        this.#addAttributeFields(forceImportFields, param.objApiName);
        this.#addNonStandardAttributeFields(forceImportFields, param.objApiName);
        // this.#addProductIdFields(forceImportFields);

        return {
            importConfig: {
                filtersMappingFields: this.excelFiltersMappingFields.bind(this, param),
                validateMappingFields: this.excelValidateMappingFields.bind(this, param),
                // parseFormatDatas: this.excelParseFormatDatas.bind(this, param, plugin),
                afterFormatDatas: this.excelParseFormatDatas.bind(this, param, plugin),
                filterExcelDatas: this.filterExcelDatas.bind(this, param),
                forceImportFields,
                parseRelatedListParam :this._parseRelatedListParam.bind(this, param),
            }
        }
    }

    // 拦截底层请求，更改请求参数
    _parseRelatedListParam(param, request){
        let masterData = param.dataGetter.getMasterData();
        let {form_account_id, form_partner_id} = this.getPluginFields('price-service');
        request.account_id = masterData[form_account_id] || '';
        request.partner_id = masterData[form_partner_id] || '';
        request.object_data = Object.assign({}, masterData, request.object_data);
        request.object_data.not_filter_non_product_flag = true;
        return request;
    }

    /**
     * 导入数据回填明细前
     *
     * @param {object} plugin
     * @param {object} param
     * @returns
     */
    async excelImportAfter(plugin, param) {
        param.lookupField = param.lookupField || {
            api_name: this.lookupField.api_name,
            target_api_name: this.lookupField.target_api_name,
            target_related_list_name: this.lookupField.target_related_list_name,
            __action_from: this.lookupField.__action_from
        };

        let masterData = param.dataGetter.getMasterData();
        let addDatas = param.dataGetter.getDetail(param.objApiName).filter(item => param.newDataIndexs.includes(item.rowId));
        let {product_id} = this.getAllFields(param.objApiName);
        let {actual_unit, unit} = this.getConfig('multiunitStatus') ? this.getPluginField(this.getMultiUnitPlugin(), ['actual_unit', 'unit'], param) : {actual_unit: 'unit', unit: 'unit'};
        let lookupData = this._cacheImportData.map(item => {
            let obj = item[product_id + '__ro'] || item;

            // 添加字段
            obj._selected_num = item.quantity;

            // 没开价目表强制优先级，单位保留导入单位
            if (!this.pluginParam.bizStateConfig.priceBookPriority && typeof item[actual_unit] !== 'undefined') {
                obj.unit = item[actual_unit];
            }

            return obj;
        });

        this._parseLookUpData(addDatas, lookupData, param.objApiName);

        if(addDatas[0] && addDatas[0][product_id]){
            // 没有不计算的字段
            // 灰度，不计算产品名称字段
            if(CRM.util.isGrayScale('CRM_IMPORT_NO_CAL_PRODUCT')){
                param.filterFields[param.objApiName] = [product_id]
            }else{
                param.filterFields[param.objApiName] = [];
            }
        }

        param = Object.assign(param, {
            addDatas: addDatas,
            lookupDatas: lookupData,
            master_data: masterData
        });

        await this.runPlugin('md.batchAdd.after', param);
    }

    /**
     * 导入数据执行UI事件后
     *
     * @param {object} context
     * @param {object} param
     * @returns
     */
    async excelImportEnd (context, param) {
        await this.runPlugin('md.batchAdd.end', param);
    }

    /**
     * 是否执行取价
     *
     * @param {object} plugin
     * @param {object} opts
     * @returns
     */
    priceServiceBatchAddAfterBefore(plugin, {param} = opts) {
        if (!param.lookupField
            || param.lookupField.__action_from !== this.lookupField.__action_from
            || param.lookupField.api_name !== this.lookupField.api_name) {
            return;
        }

        return {
            isDoPriceService: true
        };
    }

    /**
     * 取价后更新导入数据
     *
     * @param {*} plugin
     * @param {*} param1
     * @returns
     */
    priceServiceBatchAddAfterAfter(plugin, {data, realPriceData, lookupDatas, mdApiName, param} = opts) {
        // if (!param.lookupField
        //     || param.lookupField.__action_from !== this.lookupField.__action_from
        //     || param.lookupField.api_name !== this.lookupField.api_name) {
        //     return;
        // }

        // let multiUnitPlugin = this.getMultiUnitPlugin();

        // if (!multiUnitPlugin) {
        //     return;
        // }

        // let {actual_unit} = this.getPluginField(multiUnitPlugin, ['actual_unit'], param);

        // data.forEach(item => {
        //     let obj = this._cacheImportData.find(cItem => cItem.rowId === item.rowId);

        //     if (obj) {
        //         item[actual_unit] = obj[actual_unit];
        //     }
        // })

        // return {
        //     data
        // }

        // lookupDatas.forEach(item => {
        //     item._id = item.get_real_price_result._id
        // })

        // let {product_id} = this.getAllFields(mdApiName);

        // let notRealPriceData = data.filter(item => !realPriceData.some(cItem => cItem[product_id] === item[product_id]));

        // param.dataUpdater.__filterOldAddDatas(notRealPriceData.map(item => item.rowId));
        // param.newDataIndexs = param.addDatas.map(item => item.rowId);

        // return {
        //     data: param.dataGetter.getDetail(mdApiName)
        // }
    }

    getMultiUnitPlugin() {
        return util.getPlugin(this._cachePlugins, 'multi-unit');
    }

    getPluginField(plugin = {}, fields = [], param = {}) {
        return util.getPluginField.apply(this, arguments);
    }

    // ===================================导入bom======================================================

    /**
     * @desc 处理导入的bom数据
     * @param bomData
     * @param param
     * @returns {Promise<void>}
     */
    async parseBomData(bomData, param) {
        if (!bomData.length) return bomData;
        await this.fetchCoreVersion(bomData, param);
        let newData = PPM.parseDataToTree(bomData);
        // 校验导入数据是否可以成功组装为树状结构；
        let r1 = this._checkBomStruts(bomData, newData);
        if(!r1.status) return r1;
        let {product_id} = this.getAllFields();
        // 添加productIdPath
        this._addProductIdPath(newData, product_id);
        // 更新rowId parent_rowId
        PPM.updateRowIdAndPid(newData);
        // 请求产品包默认配置数据
        newData = newData.filter(item => item.core_id);

        if (newData && newData.length) {
           let res = await this.getAllBomData(newData, param);
            return await this.extendBomData(newData, res, param);
        }
        return bomData;
    }

    // 根据bom版本 查bomcoreid
    async fetchCoreVersion(bomData, param){
        let {product_id} = this.getAllFields();
        let version = [];
        bomData.forEach(item => {
            item._cacheRowId = item.rowId;
            if(item.bomVersion) version.push(item.bomVersion);
            item.product_id = item[product_id];
            item.product_id__r = item[product_id + '__r'];
        });
        if(!version.length) return;
        let searchQuery = {"limit":100,"offset":0,"filters":[],"orders":[{"fieldName":"name","isAsc":false}]};
        searchQuery.filters.push({
            field_name: 'core_version',
            field_values: version,
            operator: 'IN'
        });
        let r  = await CRM.util.fetchObjList('BomCoreObj', {
            search_query_info: JSON.stringify(searchQuery)
        });
        let mdApiName = param.objApiName;
        let {bom_core_id, related_core_id, sale_strategy} = this._getOtherPluginFields('bom', mdApiName);
        if(r && r.dataList.length){
            bomData.forEach(item => {
                if(item.bomVersion) {
                    let f = r.dataList.find(c => c.core_version === item.bomVersion && c.product_id === item[product_id]);
                    if(!f) return item._noFindVersion = true;
                    item.core_id = f._id;
                    item.core_id__r = f.name;
                    item[bom_core_id] = f._id;
                    item[sale_strategy] = f.sale_strategy;
                    if(!item.__isRoot){
                        item[related_core_id] = f._id;
                    }
                }
            });
        }
    }

    // 分批请求bom数据
    getAllBomData(importData, param){
        let arr = [];
        this._cacheBomAttrConstraintRule = [];
        let masterData = param.dataGetter.getMasterData();
        let mdApiName = param.objApiName;
        let allFields = this._getOtherPluginFields('bom', mdApiName);
        let {form_price_book_id, form_account_id, form_partner_id, form_mc_currency} = allFields;
        // 请求默认产品包配置数据
        importData.forEach(item => {
            let id = item.core_id;
            let rq = CRM.util.fetchBomAndRelatedBomData([id], {
                price_book_id: masterData[form_price_book_id] || '',
                account_id: masterData[form_account_id] || '',
                partner_id: masterData[form_partner_id] || '',
                mc_currency: masterData[form_mc_currency] || '',
                bom_core_id: id,
                include_constraint: true,
                bom_list: this.getBOMList(item.children,mdApiName),
            });
            arr.push(rq)
        });
        return Promise.all(arr).then(allRes => {
            let r = [
                {
                    dataList: [],
                    describeApiName: "BOMObj"
                }, {
                    dataList: [],
                    describeApiName: "ProductObj"
                }, {
                    dataList: [],
                    describeApiName: "ProductGroupObj"
                }, {
                    dataList: [],
                    describeApiName: "PriceBookProductObj"
                },
            ];

            function _fn(data, apiname) {
                return data.find(item => item.describeApiName === apiname);
            }

            function _concat(a, b){
                return a.dataList.concat(b?.dataList || []);
            }

            allRes.reduce((prev, curData) => {
                let bomData_p = _fn(prev, "BOMObj");
                let ProductObj_p = _fn(prev, "ProductObj");
                let groupObj_p = _fn(prev, "ProductGroupObj");
                let priceBookProduct_p = _fn(prev, "PriceBookProductObj");

                let dataMap = curData.dataMapList;
                let bomData_c = _fn(dataMap, "BOMObj");
                let ProductObj_c = _fn(dataMap, "ProductObj");
                let groupObj_c = _fn(dataMap, "ProductGroupObj");
                let priceBookProduct_c = _fn(dataMap, "PriceBookProductObj");
                let BomAttributeConstraintLinesObj_c = _fn(dataMap, "BomAttributeConstraintLinesObj");
                let AplFunctionList_c = _fn(dataMap, "AplFunctionList");
                // 缓存约束规则，校验用
                if(BomAttributeConstraintLinesObj_c){
                    this._cacheBomAttrConstraintRule =  this._cacheBomAttrConstraintRule.concat(BomAttributeConstraintLinesObj_c.dataList)
                }
                if (AplFunctionList_c) {
                    this._cacheAplFunctionList_c = AplFunctionList_c.dataList;
                }

                bomData_c.dataList = bomData_c.dataList.filter(d => CRM.util.isNormal(d));
                bomData_p.dataList = _concat(bomData_p, bomData_c);
                ProductObj_p.dataList = _concat(ProductObj_p, ProductObj_c);
                groupObj_p.dataList = _concat(groupObj_p, groupObj_c);
                priceBookProduct_p.dataList = _concat(priceBookProduct_p, priceBookProduct_c);
                return prev;
            }, r);
            return r;
        });
    }

    getBOMList (children = [], mdApiName) {
        if(!children || !children.length) return;
        let nc = PPM.parseTreeToNormal(children);
        let allFields = this._getOtherPluginFields('bom', mdApiName);
        let {node_type, price_mode, bom_id, attribute_json, price_book_id, related_core_id, new_bom_path, product_id} = allFields;
        let mapFields = {
            'node_type': node_type,
            'price_mode': price_mode + '__v',
            'bom_id': bom_id,
            'attribute_json': attribute_json,
            'price_book_id': price_book_id,
            'related_core_id': related_core_id,
            'new_bom_path': new_bom_path,
            'product_id': product_id,
            'product_id__r': product_id + '__r',

        };
        nc.forEach(item => {
            for(let key in mapFields){
                item[key] = item[mapFields[key]] || '';
            }
        });
        const allowKeys = ['_id', 'node_type', 'price_mode', 'bom_id', 'attribute_json', 'price_book_id', 'related_core_id', 'new_bom_path'];
        console.log(nc)
        let r = nc.filter((item) => item.price_book_id || this.isTempNode(item, allFields) || item.related_core_id)
            .map((item) => (this.isTempNode(item, allFields) ? item : _.pick(item, allowKeys)));
        console.log(nc)
        return r;
    }

    // 是否是临时子件
    isTempNode(row, fieldMap) {
        let {node_type} = fieldMap;
        return row.node_type === 'temp' || row[node_type] === 'temp';
    }

    // 根据product_id path 和 order 查找数据;
    findDataByProductPath(data, PIdPath, coreId){
        let res = null;
        function _fn(nData) {
            if(res) return;
            for(let i = 0; i < nData.length; i++){
                let item = nData[i];
               if (item._PIdPath === PIdPath && !item._hasMapping && (!coreId || coreId && item.related_core_id === coreId)) {
                    res = item;
                    return;
                }
                if (item.children) {
                    _fn(item.children)
                }
            }
        }
        _fn(data);
        if(res) res._hasMapping = true;
        return res;
    }

    // 校验重复产品组合的整包唯一标记不能相同
    checkBomRepeat(importData){
        let r = [];
        // let {product_id} = this.getAllFields();
        importData.forEach(item => {
            let f = importData.find(c => c.__bomVersion === item.__bomVersion && c.rowId !== item.rowId && c.__packageCode == item.__packageCode);
            if(f) r.push(item)
        });
        if(r.length){
            return {
                status: false,
                messages: this.getAlertMessage(r, $t('crm.excelbom.alert2'), 'error', true ) // 导入相同产品组合，整包唯一标记不能相同
            };
        }
        return {
            status: true,
        };
    }

    /**
     * @desc 导入的BOM数据，需要混合一遍子产品默认配置数据。如果未匹配到数据，阻断，提示
     * @param importData 导入数据
     * @param bomInfo 查treev1得到的bom数据
     */
    async extendBomData(importData, bomInfo, param){
        let r = [];
        let unitDisabled = [];
        let mdApiName = param.objApiName;
        let {actual_unit} = this.getPluginField(this.getMultiUnitPlugin(), ['actual_unit'], param);
        let bomFields = this._getOtherPluginFields('bom', mdApiName);
        let {quantity, product_price, product_id} = bomFields;
        const { attribute, attribute_json, nonstandard_attribute, nonstandard_attribute_json } = this.getAttributeFields(mdApiName);
        let {pricing_period} = this.getSomePluginFields(['period_product', ]);
        let r3 = this.checkBomRepeat(importData);
        if(!r3.status) return r3;
        // 补bom_id
        this._addBomId(importData, bomInfo, bomFields);
        // 获取默认数据，包含分组
        let bomData = CRM.util.flatBomData(bomInfo);
        bomData = _.uniq(bomData, item => item.bom_id);
        let priceDisabled = [];
        // 处理导入的子产品数据
        importData.forEach(item => {
            let imChildren = item.children || [];

            // 默认数据组装树状结构
            let defChildren = PPM.parseDataToBOM(bomData, item.bom_id);
            if (defChildren.length) {
                // 先计算下产品包中，子产品默认选中的总金额，下边计算包价格用
                // 如果导入的数据有价格，就不计算了
                defChildren = PPM.deepClone(defChildren);
                defChildren = PPM.sortTreeData(defChildren, 'order_field');
                // 添加productIdPath 和 order，用于匹配数据
                this._addProductIdPath(defChildren, 'product_id', item[product_id]);
                // 如果包含默认选中，那需要计算下默认选中金额；如果不包含，默认选中就是0
                if(!PPM.hasValue(item[product_price]) && this.isIncludeDefSelect()){
                    item._defSelectMoney = this.calculateSelectMoney(defChildren);
                    this.setDefSelectedMoney(defChildren);
                }else{
                    item._defSelectMoney = 0;
                }
                // // 设置必填项勾选
                // CRM.util.setBomChecked(defChildren, null, true, true);
                // 将导入的数据合并到查询到的默认产品包数据上，这样分组也有了；
                let rootQuantity = PPM.hasValue(item[quantity]) ? item[quantity] : 1;
                PPM.forEachTreeData(imChildren, (ic) => {
                    if(ic.isGroup) return;
                    let dc = this.findDataByProductPath(defChildren, ic._PIdPath, ic.core_id);
                    if(dc){
                        ic = PPM.deepClone(ic);
                        delete ic.rowId;
                        delete ic.pid;
                        delete ic.parent_rowId;
                        delete ic.children;
                        Object.assign(dc, ic);
                        dc.isChecked = true;
                        dc.bom_id = dc._id;
                        if(!dc.isGroup) dc.modified_adjust_price = dc.adjust_price;

                        if(PPM.hasValue(ic.price_book_id)) dc.price_book_id = ic.price_book_id ;
                        if(PPM.hasValue(ic[product_price])) {
                            if(dc.price_editable){
                                dc.modified_adjust_price = ic[product_price] ;
                                dc.__importPrice = true;
                            }else {
                                priceDisabled.push(ic)
                            }
                        }
                        dc.__amount = dc.amount;
                        if(pricing_period){
                            dc.pricing_mode = dc.product_id__ro.pricing_mode;
                            dc.pricing_period = dc.pricing_period || 1;
                            dc.__pricing_period = dc.pricing_period;
                            if(PPM.hasValue(ic[pricing_period])) dc.pricing_period = ic[pricing_period];
                        }

                        if(PPM.hasValue(ic[quantity])) dc.amount = dc.amount_any ? ic[quantity]: PPM.division(Number(ic[quantity]), Number(rootQuantity));
                        if (actual_unit && dc[actual_unit] != dc.unit_id) {
                            unitDisabled.push(ic);
                        }
                    }else{
                        r.push(ic);
                    }
                });
                // 设置父级分组选中状态
                PPM.forEachTreeData(defChildren, (c, pData) => {
                   if(c.isChecked && pData && pData.isGroup) pData.isChecked = true;
                });
                // 设置必填项勾选
                CRM.util.setBomChecked(defChildren, null, true);
                // 过滤出勾选项
                defChildren = CRM.util.getOnlyCheckedData(defChildren);
                // 设置子产品父级id，处理属性
                item.rowId = item.__rowId;
                defChildren.forEach(c => {
                    c.parent_rowId = item.rowId;
                    if (CRM._cache.openAttribute) {
                        CRM.util.forEachTreeData([c], d => {
                            if (d.attribute && !d[attribute_json]) {
                                CRM.util.setDefaultAttr(d);
                                CRM.util.parseDataAttr(d);
                            }
                        })
                    }
                });
                // 根据序号排序
                item.children = PPM.sortTreeData(defChildren, 'order_field');
            }
        });

        console.log(importData);
        if(r.length) {
            return {
                 messages: this.getAlertMessage(r,this.i18n('crm.excelbom.alert5'), 'error', true )      // 这些子产品未匹配到数据，请检查
            }
        }

        if(priceDisabled.length){
            return {
                messages: this.getAlertMessage(priceDisabled, this.i18n('crm.excelbom.alert6'), 'error', true )    // 这些产品价格导入无效，因这些产品在产品选配明细对象设置了“价格不可编辑”，系统将重新取价
            }
        }

        if (unitDisabled.length) {
            return {
                messages: this.getAlertMessage(unitDisabled, this.i18n('crm.excelbom.alert_unit_disabled'), 'error', true )      // 这些子产品的单位不符合搭配配置，请检查
            }
        }

        // 计算产品包数据
        this.parseRootData(importData, bomInfo, mdApiName, param);
        // 校验 bom 分组规则 数量规则
        let r1 = await this._checkBomConfig(importData);
        if(!r1) return;

        await this._getRealPriceForChildren(importData, param);

        return importData;
    }

    // 校验 bom 分组规则、数量规则、属性约束
    async _checkBomConfig(importData){
        let r = true;
        for (let i = 0; i < importData.length; i++){
            let item = importData[i];
            let children = item.children || [];
            // var r1 = CRM.util.validAmountBeforeSubmit(null, children, null, null, true);
            // var r2 = CRM.util.validGroupData(null, children);
            let r1 = await CRM.util.checkBom_server(children, item, item.newestPrice);
            let r2 = await CRM.util.checkBomAttrRule_server(item, this._cacheBomAttrConstraintRule, this._cacheAplFunctionList_c);

            r = r && r1 && r2 ;
        }
        return r;
    }

    // 有属性的子件需要走一次取价接口，取对应的属性价目表价格；
    async _getRealPriceForChildren(importData, param){
        let children = [];
        PPM.forEachTreeData(importData, item => {
            if(item.isGroup) return;
            if(item.parent_rowId && (item.attribute_json || item.price_book_id__from_import)){
                children.push(item)
            }
        });
        if(!children.length) return;
        let masterData = param.dataGetter.getMasterData();
        let masterApiName = param.masterObjApiName;
        let psData = await this.runPlugin('price-service.getRealPrice', {
            data: children,
            masterData,
            masterApiName,
            param
        });
        if (psData) {
            let res = psData.newRst;
            children.forEach(item => {
                let f = res.find(d => d.rowId === item.rowId);
                if(!f) return;
                if(!item.__importPrice) item.modified_adjust_price = f.pricebook_price;
                item.price_book_id = f.pricebook_id;
                item.price_book_id__r = f.pricebook_id__r;
            })
        }
    }

    // 计算产品包金额。如果导入的数据中有price，就不计算了，以导入为准
    // 1. importData 导入母件数据，子件数据在母件的 children 中
    // 2. bomInfo 查treev1得到的bom数据
    parseRootData(importData, bomInfo, mdApiName, param){
        let {product_price, product_id, pricing_period, sale_strategy} = this.getSomePluginFields(['period_product', 'bom' ], mdApiName);
        let productData = _.find(bomInfo, function (item) {
            return item.describeApiName === 'ProductObj'
        });
        let PriceBookProductObj = _.find(bomInfo, function (item) {
            return item.describeApiName === 'PriceBookProductObj'
        });
        let des = param.dataGetter.getDescribe(mdApiName).fields;
        importData.forEach(rd => {
            if(rd[sale_strategy] !== 'sub' && PPM.hasValue(rd[product_price])) return;
            // 有价目表信息，优先取价目表价格
            if(PriceBookProductObj && PriceBookProductObj.dataList.length){
                let proInfo = _.find(PriceBookProductObj.dataList, item => {
                    return item.product_id === rd[product_id];
                });
                rd.totalPrice = proInfo.pricebook_sellingprice;
            }else{
                let proInfo = _.find(productData.dataList, item => {
                    return item._id === rd[product_id];
                });
                rd.totalPrice = proInfo.price;
            }
            rd.newestPrice = rd.totalPrice;

            // 母件定价的组合，计算层级价格
            if(rd[sale_strategy] !== 'sub'){
                 // 层层计算子产品价格，最后计算 产品包总金额 = 包本身的金额 - 默认选中子产品的总金额 + 当前选中子产品的总金额
                if(rd.children){
                    let {totalPrice, totalSingleSetPrice,} = CRM.util.calculateAllChildrenPrice(rd.children);
                    let p = PPM.accAdd(PPM.accAdd(Number(rd.totalPrice), -rd._defSelectMoney), totalPrice);
                    rd.newestPrice = PPM.toFixed(p, des[product_price].decimal_places);
                    if(pricing_period){
                        let p2 = PPM.accAdd(PPM.accAdd(PPM.multiplicational(rd.totalPrice, rd[pricing_period]), -rd._defSelectMoney), totalSingleSetPrice);
                        rd.totalSingleSetPrice = PPM.toFixed(p2, des[product_price].decimal_places);
                    }
                }
            }
        });
    }

    // 给每个父节点缓存自己的默认选中的子件总金额
    setDefSelectedMoney(data){
        PPM.forEachTreeData(data, (item) => {
            if (!item.isGroup && item.children && item.children.length) {
                item._defSelectMoney = this.calculateSelectMoney(item.children);

            }
        });
    }

    /**
     * @desc 统计产品包 默认选中的子产品总金额；
     * @param data
     * @param field
     */
    calculateSelectMoney(data, field = 'adjust_price') {
        let money = 0;

        function _add(c){
            if (c.is_required || c.selected_by_default) money = PPM.accAdd(money, PPM.multiplicational(Number(c[field]), Number(c.amount)));
        }

        _.each(data, function (item) {
            if (item.isGroup) {
                if (item.children && item.children.length) {
                    _.each(item.children, function (c) {
                        _add(c)
                    })
                }
            } else {
                _add(item)
            }
        });
        return money;
    }

    // 必选或默认选中项是否计入默认产品包总金额；
    isIncludeDefSelect() {
        return CRM._cache.bom_price_calculation_configuration == '0';
    }

    // 拆分数据，普通产品，bom
    _splitData(data){
        let normalData = [], bomData = [];
        data.forEach(item => {
            if(item.isPackage || item.parent_rowId){
                bomData.push(item)
            }else{
                normalData.push(item)
            }
        });
        return {
            normalData,
            bomData
        }
    }

    // 分析导入的bom数据，添加bom相关字段
    _addRowIdAndPid(data, plugin){
        if(!this._getPluginBom()) return;
        let __code = '__code';
        let __parentCode = '__parentCode';
        let __bomVersion = '__bomVersion';
        let __packageCode = '__packageCode';
        let {product_id} = this.getAllFields();
        let errorProduct = [];
        let errorData = [];
        data.forEach(item => {
            if(item[__bomVersion] && (!PPM.hasValue(item[__code]) || !PPM.hasValue(item[__parentCode]))){
               errorProduct.push(item[product_id + '__r']);
               errorData.push(item);
            }
            if(!PPM.hasValue(item[__code])) return;
            if(item[__code] === item[__parentCode]) {
                item.isPackage = true;
                item.bomVersion = item[__bomVersion];
                item.__isRoot = true;
            }else{
                let par = data.find(d => d[__code] == item[__parentCode] && (d[__packageCode] == item[__packageCode] || !item[__packageCode]));
                if(par) item.parent_rowId = par.rowId;
                if(item[__bomVersion]) item.bomVersion = item[__bomVersion]
            }
        });
        if(errorProduct.length){
            plugin.skipPlugin();
            return {
                res: false,
                messages: this.getAlertMessage(errorData,  $t('crm.excelbom.alert1'), 'error', true) //有BOM版本时，虚拟key和父级虚拟key不能为空
            };
        }
    }

    // 补lookup数据
    _parseLookUpData(addDatas, lookupData, mdApiName){
        let fields = ['bom_id', 'newestPrice', 'bom_id' , 'core_id', 'core_id__r', 'totalSingleSetPrice'];
        const { attribute, attribute_json, nonstandard_attribute, nonstandard_attribute_json } = this.getAttributeFields(mdApiName);
        addDatas.forEach((item, index) => {
            let ld =  lookupData[index];
            if(item.children){
                ld.children = item.children;
                fields.forEach(f => ld[f] = item[f]);
                delete item.children;
            }
            // 如果产品支持属性 就需要补充属性
            if (attribute && attribute_json && item[attribute_json] && item[attribute]) {
                ld.attribute_group_text = item[attribute];
                ld.attribute_group = ld[attribute_json] = item[attribute_json];
            }
            if (nonstandard_attribute && nonstandard_attribute_json) {
                if (item[`__$${nonstandard_attribute}`]) {
                    ld.nonstandard_attribute_group_text = item[nonstandard_attribute];
                    ld.nonstandard_attribute_group = ld[nonstandard_attribute_json] = item[nonstandard_attribute_json];
                }
            }
        })
    }

    // 获取bom插件
    _getPluginBom(){
        return this.pluginService.api.getPlugins().find(item => item.pluginApiName === 'bom');
    }
    // 获取其他插件字段映射
    _getOtherPluginFields(pluginName, mdApiName){
        return this.getPluginFields(pluginName, mdApiName)
    }

    /**
     * @desc 校验BOM数据结构是否正常。如果缺少了数据，阻断，提示；
     * @param bomData 原始导入数据
     * @param newData 组装树之后的数据
     * @returns {boolean}
     * @private
     */
    _checkBomStruts(bomData, newData){
        let {product_id} = this.getAllFields();
        let res = [];
        let msg = '';
        let errPro = [];
        bomData.forEach(item => {
            if(!item[product_id]){
                errPro.push(item);
                msg = this.i18n('未匹配到产品名称的数据不能导入');
            }
            if(item._noFindVersion){
                errPro.push(item);
                msg = this.i18n('sfa.crm.bom.excel.noVersion'); // 该产品未找到对应的产品组合数据，请检查BOM版本是否匹配
            }
            let f = PPM.getDataByKey(item.rowId, newData);
            if(!f) res.push(item);
        });
        if(msg){
            // this.alert(msg);
            return {
                status: false,
                messages: this.getAlertMessage(errPro, msg, 'error', true)
            };
        }
        // 校验产品包版本是否有值
        PPM.forEachTreeData(newData, item => {
            if(item.isGroup) return;
            if((item.isPackage || item.bomVersion) && !item.core_id){
                errPro.push(item);
            }
        });

        if(errPro.length){
            // let msg = '【' + errPro.join() + '】，';
            // this.alert(msg += this.i18n('crm.excelbom.alert3')); // 产品包必须导入版本，请检查BOM版本是否正确
            return {
                status: false,
                messages: this.getAlertMessage(errPro, this.i18n('crm.excelbom.alert3'), 'error', true)
            };
        }
        if(res.length) {
            // this.alert( res.join(',') +  this.i18n('crm.excelbom.alert4')); // BOM结构不正确，请检查
            return {
                status: false,
                messages: this.getAlertMessage(res, this.i18n('crm.excelbom.alert4'), 'error', true)
            };
        }
        return {
            status: true,
        };
    }

    /**
     * @desc 添加productIdPath，用于匹配bom数据
     * @param data
     */
    _addProductIdPath(data, fieldName, rootProductId = ''){
        let path = [];
        let obj = {};

        PPM.forEachTreeData(data, (item) => {
            obj[item.rowId] = item;
        });

        function _getPath(d){
            if(!d.isGroup) path.unshift(d[fieldName]);
            if(d.parent_rowId){
                let pd = obj[d.parent_rowId];
                if(pd) _getPath(pd);
            }else{
                if(rootProductId) path.unshift(rootProductId);
            }
            return path.join('.');
        }

        PPM.forEachTreeData(data, (item) => {
            if(item.isGroup) return;
            path = [];
            item._PIdPath = _getPath(item);
        });

        obj = null;
    }

    /**
     * @desc 补bom_id。根据product_id_path匹配bom数据
     * @param importData 导入数据
     * @param bomInfo bom 数据
     */
    _addBomId(importData, bomInfo, bomFields) {
        let {product_id, bom_id} = bomFields;
        let fields = ['new_bom_path', 'core_id', 'related_core_id', 'node_bom_core_type', 'node_bom_core_version', 'node_type',
            'product_id', 'product_status', 'adjust_price', 'quantity', 'bom_id',  'prod_pkg_key', 'parent_prod_pkg_key', 'price_book_id'];
        let productData = _.find(bomInfo, function (item) {
            return item.describeApiName === 'ProductObj'
        });
        // 如果同层级有重复的子产品，需要按顺序匹配默认配置
        importData.forEach(rd => {
            let proInfo = _.find(productData.dataList, item => {
                return item._id === rd[product_id] && rd.core_id === item.core_id;
            });
            fields.forEach(k => {
                rd[k] = rd[bomFields[k]]
            });
            rd.bom_id = rd[bom_id] = proInfo?.bom_id || '';
        });

    }

    // 给导入的同层级的重复子产品，添加序号标记，用于按顺序匹配默认配置
    // _addOrder(importData){
    //     importData.forEach(item => {
    //         if(!item.children) return;
    //         item.children.forEach(c => {
    //             if(c.hasOwnProperty('_order')) return;
    //             let samePath = item.children.filter(cc => cc._PIdPath === c._PIdPath);
    //             if(samePath.length > 1){
    //                 samePath.forEach((s, index) => s._order = index);
    //             }
    //             if(c.children) this._addOrder([c]);
    //         })
    //     })
    // }

    // 过滤掉空行
    filterExcelDatas(param, data, pluginService){
        return data.filter(item => item.length);
    }

    isFromImport(param){
        if (!param.lookupField
            || param.lookupField.__action_from !== this.lookupField.__action_from
            || param.lookupField.api_name !== this.lookupField.api_name) {
            return false;
        }
        return true;
    }

    // 导入的bom数据，保留自定义字段
    bomAddChildrenBefore(plugin, {param} = {}){
        if(!this.isFromImport(param)) return;
        let des = param.dataGetter.getDescribe();
        if(!des) return;
        let keys = Object.keys(des.fields || {});
        let fields = keys.filter(f => f.includes('__c'));
        return {
            __execResult:{
                usefulFields: fields
            },
            __mergeDataType:{
                array: 'concat'
            }
        }
    }

    /** -------------- 属性相关 ---------------- **/

    // 校验属性是否合法
    validateAttributeData = (productIdResult, param) => {
        this.#transformAttributeData(productIdResult, param);
        this.#transformNonStandardAttributeData(productIdResult, param);
        if (productIdResult.attribute_msg && Object.keys(productIdResult.attribute_msg)?.length) {
            return {
                status: false,
                msg: {
                    messages:productIdResult.attribute_msg,
                    stopImport: true
                }
            }
            // this.pluginService.api.alert(productIdResult.attribute_msg.join(';'));
            // return false;
        }
        return {
            status: true
        };
    }

    // 根据插件获取属性相关字段
    getAttributeFields = (objApiName) => {
        const { attribute, attribute_json, product_id } = this.getPluginFields('attribute', objApiName) || {};
        const { nonstandard_attribute, nonstandard_attribute_json } = this.getPluginFields('nonstandard_attribute', objApiName) || {};
        return {
            product_id,
            attribute,
            attribute_json,
            nonstandard_attribute,
            nonstandard_attribute_json
        }
    }

    #convertArrayToMap = (array = []) => {
        const map = new Map();
        array.forEach(item => {
            if (!!item) {
                const [key, value] = item.split(':');
                map.set(key, value);
            }
        })
        return map;
    }

    #getDefaultAttribute = (attributeArray) => {
        const keyValueArray = attributeArray
            .map(attribute => {
                const key = attribute?.name || '';
                // 默认属性值 如果没有默认属性值则取第一个
                const value = attribute.attribute_values?.find(attribute_value => attribute_value.is_default === '1')?.name || attribute.attribute_values?.[0]?.name || '';
                return `${key}:${value}`;
            })
        return this.#convertArrayToMap(keyValueArray);
    }

    #getAttributeFromString = (attributeString) => {
        if(!attributeString) return new Map();
        const keyValueArray = util.formatAttributeInputString(attributeString).split(';');
        return this.#convertArrayToMap(keyValueArray);
    }

    // 将导入的 属性 数据转化为可识别的attribute_group
    #transformAttributeData = (productIdResult, param) => {
        const { attribute, attribute_json, product_id } = this.getAttributeFields(param.objApiName);
        if (!productIdResult?.data || !productIdResult.data?.length || !attribute || !attribute_json) return;
        const msg = [];
        const attribute_field_name = `__${attribute}`;
        const product_id__ro = `${product_id}__ro`;
        /**
         * map结构
         * key: 产品名
         * value: 属性名:属性值 的数组
         */
        const illegalName = new Map();
        productIdResult.data = productIdResult.data.map(item => {
            if(!item[product_id__ro]) return item;
            const attributeGroup = {},
                attributeArray = item[product_id__ro][attribute_field_name] || item[product_id__ro].attribute || [],
                newAttributeArray = [],
                illegalAttribute = new Set(),
                attributeString = item[attribute_field_name] || item[attribute] || '';
            if(attributeArray?.length) item.__cacheAttribute = attributeArray;
            // 输入的字符串格式 '颜色：黑色；大小：小'
            // 根据数据获取属性值映射
            const inputAttributeMap = !!attributeString
                ? this.#getAttributeFromString(attributeString)
                : this.#getDefaultAttribute(attributeArray);
            // 如果没导入属性值，就获取默认属性值
            // 如果缺少属性值，根据默认属性值补全
            attributeArray.forEach(attribute => {
                const key = attribute.name;
                if (inputAttributeMap.has(key)) {
                    const value = inputAttributeMap.get(key);
                    const valueId = attribute.attribute_values?.find(attribute_value => attribute_value.name === value)?.id;
                    if (valueId) {
                        attributeGroup[attribute.id] = valueId;
                        newAttributeArray.push(`${key}:${value}`);
                        inputAttributeMap.delete(key);
                    } else {
                        // 提示不存在的属性
                        illegalAttribute.add(`${key}:${value}`);
                    }
                } else {
                    const value = attribute.attribute_values.find(value => value.is_default === '1');
                    attributeGroup[attribute.id] = value.id;
                    newAttributeArray.push(`${key}:${value.name}`);
                }
            });
            item[attribute] = item.attribute = newAttributeArray.join(';');
            item[attribute_json] = item.attribute_json = attributeGroup;
            // 提示多余的属性
            if (inputAttributeMap.size) {
                inputAttributeMap.forEach((value, key) => {
                    illegalAttribute.add(`${key}:${value}`);
                });
            }
            if (illegalAttribute.size) {
                illegalName.set(item.rowId, {
                    productName: item[product_id__ro].name,
                    value:  Array.from(illegalAttribute),
                });
            }
            return item;
        });
        let messages = {};
        if (!!illegalName.size) {
            const msgArr = [];
            let type = 'error';
            illegalName.forEach((obj, key) => {
                messages[key] = {
                    type,
                    // 产品:{{key}},未匹配到属性"{{value}}"不能导入
                    content:  `${$t('sfa_vcrm_plugin_excelimport_product_nonstandard_attribute_0', { key:obj.productName, value: Array.from(obj.value).join('、') })}`
                };
                // msgArr.push(`<br />${$t('产品:{{key}},未匹配到属性"{{value}}"不能导入', { key, value: Array.from(value).join('、') })}`);
            });
            msg.push(...msgArr);
            productIdResult.attribute_msg = messages;
        }
    }

    #transformNonStandardAttributeData = (productIdResult, param) => {
        const { nonstandard_attribute, nonstandard_attribute_json, product_id } = this.getAttributeFields(param.objApiName);
        if (!productIdResult?.data || !productIdResult.data?.length || !nonstandard_attribute || !nonstandard_attribute_json) return;
        let messages = productIdResult.attribute_msg || {};
        const ns_attribute_field_name = `__${nonstandard_attribute}`;
        const product_id__ro = `${product_id}__ro`;
        /**
         * map结构
         * key: 产品名
         * value: 非标属性名的数值
         */
        const illegalName = new Map();
        const missingName = new Map();
        productIdResult.data = productIdResult.data.map(item => {
            if(!item[product_id__ro]) return item;
            const nonStandardAttributeGroup = {},
                nonStandardAttributeArray = item[product_id__ro].nonstandardAttribute || item.nonstandardAttribute || [],
                newNonStandardAttributeArray = [],
                inputString = item[ns_attribute_field_name],
                illegalAttribute = new Set(),
                missingAttribute = new Set();
            // const inputNonStandardAttributeMap = !!inputString
            //     ? this.#getAttributeFromString(inputString)
            //     : this.#getDefaultAttribute(nonStandardAttributeArray);
            const inputNonStandardAttributeMap = this.#getAttributeFromString(inputString);
            nonStandardAttributeArray.forEach(attribute => {
                const key = attribute.name;
                if (inputNonStandardAttributeMap.has(key)) {
                    const keyId = attribute.id;
                    nonStandardAttributeGroup[keyId] = inputNonStandardAttributeMap.get(key);
                    newNonStandardAttributeArray.push(`${key}:${inputNonStandardAttributeMap.get(key)}`);
                    inputNonStandardAttributeMap.delete(key);
                } else {
                    missingAttribute.add(key);
                }
            });
            inputNonStandardAttributeMap.forEach((value, key) => {
                illegalAttribute.add(key);
            })
            // 不存在的非标属性 不允许导入
            if (illegalAttribute.size) {
                illegalName.set(item.rowId, {
                    productName: item[product_id__ro].name,
                    value:  Array.from(illegalAttribute),
                });
            }
            // 缺少非标属性 不允许导入
            if (missingAttribute.size) {
                missingName.set(item.rowId, {
                    productName: item[product_id__ro].name,
                    value:  Array.from(missingAttribute),
                });
            }
            item[nonstandard_attribute] = item.nonstandard_attribute = newNonStandardAttributeArray.join(';');
            item[nonstandard_attribute_json] = item.nonstandard_attribute_json = nonStandardAttributeGroup;
            return item;
        });
        let type = 'error';
        if (!!illegalName.size) {
            illegalName.forEach((obj, key) => {
                messages[key] = {
                    type,
                    // 产品:{{key}},未匹配到非标属性"{{value}}"不能导入
                    content: `${$t('sfa_vcrm_plugin_excelimport_product_nonstandard_attribute_0', { key: obj.productName, value: Array.from(obj.value).join('、') })}`
                };
            });
        }

        if (!!missingName.size) {
            missingName.forEach((obj, key) => {
                // 产品:{{key}},缺少非标属性"{{value}}"不能导入
                let m = `${$t('sfa_vcrm_plugin_excelimport_product_nonstandard_attribute_0', { key: obj.productName, value: Array.from(obj.value).join('、') })}`;
                if(messages[key]){
                    messages[key].content =  messages[key].content + '/' + m;
                }else{
                    messages[key] = {
                        type,
                        content: m
                    };
                }
            });
        }

        // if (!!illegalName.size) {
        //     const msgArr = [];
        //     illegalName.forEach((value, key) => {
        //         msgArr.push(`<br/>${$t('产品:{{key}},未匹配到非标属性"{{value}}"不能导入', { key, value: Array.from(value).join('、') })}`);
        //     });
        //     msg.push(...msgArr);
        // }
        // if (!!missingName.size) {
        //     const msgArr = [];
        //     missingName.forEach((value, key) => {
        //         msgArr.push(`<br/>${$t('产品:{{key}},缺少非标属性"{{value}}"不能导入', { key, value: Array.from(value).join('、') })}`);
        //     })
        // }
        productIdResult.attribute_msg = messages;
    }

    #addAttributeFields = (fields, objApiName) => {
        // 没有属性不做处理
        const { attribute } = this.getAttributeFields(objApiName);
        if (!attribute) {
            return;
        }
        fields.push({
            field_name: `__${attribute}`,
            label: $t('属性') + '(' + $t('虚拟字段') + ')',
            type: 'text',
        });
    }

    #addNonStandardAttributeFields = (fields, objApiName) => {
        // 没有属性不做处理
        const { nonstandard_attribute } = this.getAttributeFields(objApiName);
        if (!nonstandard_attribute) {
            return;
        }
        fields.push({
            field_name: `__${nonstandard_attribute}`,
            label: $t('非标属性') + '(' + $t('虚拟字段') + ')',
            type: 'text',
        })
    }

    #addProductIdFields = (fields) => {
        fields.push({
            field_name: '__price_book_id',
            label: $t('') + '(' + $t('虚拟字段') + ')',
            type: 'text',
        });
    }

    // bom导入标准化，添加虚拟字段
    _addBomFields(f){
        let bomPlugin = this._getPluginBom();
        if(!bomPlugin) return f;
        let fields = [
            {
                field_name:'__code',
                label: $t('crm.bom.virtual') + 'key' + '(' + $t('虚拟字段') + ')', // 虚拟
                type: 'text',
            }, {
                field_name:'__parentCode',
                label: $t('crm.bom.parent') +  $t('crm.bom.virtual') + 'key' + '(' + $t('虚拟字段') + ')', // 父级虚拟
                type: 'text',
            }, {
                field_name:'__bomVersion',
                label: $t('BOM版本') + '(' + $t('虚拟字段') + ')',
                type: 'text',
            },{
                field_name:'__packageCode',
                label: $t('crm.bom.prodkey') + '(' + $t('虚拟字段') + ')',  //  整包唯一标记
                type: 'text',
            },
        ];
        return f.concat(fields);
    }

    // shouldContinueExecution(plugin, data) {
    //     // 如果不允许价目表导入，或者不是导入的数据，不做处理
    //     if (util.isBlockPriceBook || !this.isFromImport(data.param)) {
    //         return false;
    //     }
    //
    //     return true;
    // }

    // filterRealPriceData(data) {
    //     const { realPriceData, data: addData, mdApiName, param: { dataUpdater: { del } } } = data;
    //     const {product_id} = this.getAllFields();
    //     const realPriceDataProductIdSet = new Set(realPriceData.map(item => item[product_id]));
    //     const rowIds = [], filteredAddData = [];
    //     addData?.forEach(item => {
    //         if (!realPriceDataProductIdSet.has(item[product_id])) {
    //             rowIds.push(item.__rowId);
    //         } else {
    //             filteredAddData.push(item);
    //         }
    //     })
    //
    //     rowIds.length && rowIds.forEach(rowId => {
    //         del && del(mdApiName, rowId);
    //     })
    //     return filteredAddData;
    // }

    // matchRealPriceDataAfter = (plugin, data) => {
    //     const continueExecution = this.shouldContinueExecution(plugin, data);
    //     if (!continueExecution) {
    //         return;
    //     }
    //     const filteredAddData = this.filterRealPriceData(data);
    //     const returnData = { data: filteredAddData };
    //
    //     // 如果价目表数全部为空或者价目表全部不是导入的数据，不做处理
    //     if (filteredAddData.every(item => item.price_book_id__from_import === false)) {
    //         return;
    //     }
    //
    //     const msg = [],
    //         realPriceData = data.realPriceData || [],
    //         realPriceDataProductIdMap = new Map();
    //     realPriceData.forEach(item => {
    //         const { rowId, pricebook_id, param_priceBookId, product_name } = item;
    //         realPriceDataProductIdMap.set(rowId, { param_priceBookId, pricebook_id, product_name });
    //     });
    //     filteredAddData.forEach(value => {
    //         if(realPriceDataProductIdMap.has(value.rowId)) {
    //             const item = realPriceDataProductIdMap.get(value.rowId);
    //             const { param_priceBookId, pricebook_id } = item;
    //             if (!!param_priceBookId && param_priceBookId !== pricebook_id) {
    //                 // {product_name}的价目表不匹配,系统会根据价目表规则匹配t 合适的价目表进行倒入
    //                 msg.push(`${$t('plugin_excelImport_illegal_pricebook', {temp: item.product_name})}<br />`);
    //             }
    //         }
    //
    //     })
    //     return new Promise((resolve, reject) => {
    //         if (msg.length) {
    //             plugin.api.hideLoading();
    //             plugin.api.confirm({
    //                 msg: msg,
    //                 success: () => {
    //                     resolve();
    //                 }
    //             });
    //         } else {
    //             resolve();
    //         }
    //     });
    // };

    /**
     * @desc 组装提示信息，和底层一起提示
     * @param data
     * @param msg
     * @param type error/tip
     * @param isStop
     * @returns {{stopImport: boolean, messages: *}}
     */
    getAlertMessage(data, msg, type = 'error', isStop = false){
        let messages = data.reduce((cur, item)=>{
            let rId = item.hasOwnProperty('_cacheRowId') ? item._cacheRowId : item.rowId;
            cur[rId] = {
                type,
                content: msg,
            };
            return cur;
        }, {});
        return {
            messages,
            stopImport: isStop
        }
    }

    // 合并提示信息，阻断优先；
    mergeAlertMsg(arr){
        let res = {
            messages: {},
            stopImport: false
        };
        arr.forEach(item => {
            if(item.stopImport) res.stopImport = true;
            res.messages = Object.assign(res.messages, item.messages)
        });
        return res;
    }

    // 校验导入价目表 取价之后是否有变化，提示用户；
    async checkPriceBook(data, param){
        let r =  {
            msg: {}
        };
        let mdApiName = param.objApiName;
        let {price_book_id} = this.getPluginFields('price-service', mdApiName);
        data = data.filter(c => c[price_book_id]);
        if(!data.length) return r;
        let masterData = param.dataGetter.getMasterData();
        let masterApiName = param.masterObjApiName;
        let res = await this.runPlugin('price-service.getRealPriceAndCheckPriceBook', {
            data,
            masterData,
            masterApiName,
            param
        });
        // todo: errorData 加提示
        let {errorData, changeData} = res;
        if(changeData?.length){
            let msg = $t('plugin_excelImport_illegal_pricebook2'); // 价目表不匹配,系统会根据价目表规则匹配t 合适的价目表进行倒入
            r.msg = this.getAlertMessage(changeData, msg, );
        }
        return r;
    }
    // ===================================================================================

    getHook() {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            },
            {
                event: 'md.excelimport.before',
                functional: this.excelImportBefore.bind(this)
            },
            {
                event: 'md.excelimport.after',
                functional: this.excelImportAfter.bind(this)
            },
            {
                event: 'md.excelimport.end',
                functional: this.excelImportEnd.bind(this)
            },
            {
                event: 'price-service.batchAddAfter.before',
                functional: this.priceServiceBatchAddAfterBefore.bind(this)
            },  {
                event: 'bom.addChildren.before',
                functional: this.bomAddChildrenBefore.bind(this)
            },
            // {
            //     event: 'price-service.batchAddAfter.after',
            //     functional: this.matchRealPriceDataAfter
            // }
            // {
            //     event: 'price-service.batchAddAfter.after',
            //     functional: this.priceServiceBatchAddAfterAfter.bind(this)
            // }
        ]
    }

}
