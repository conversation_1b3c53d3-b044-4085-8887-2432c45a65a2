/*
 * @Descripttion: excel 粘贴功能
 * @Author: LiAng
 * @Date: 2022-07-19 14:25:10
 * @LastEditors: LiAng
 * @LastEditTime: 2023-10-23 16:19:32
 */
// import PPM from 'plugin_public_methods'
import Base from "plugin_base";
import {util} from '../excelimport'

export default class ExcelPaste extends Base {
    constructor(pluginService, pluginParam) {
        super(pluginService, pluginParam);
        this.lookupField = {
            api_name: 'product_id',
            target_api_name: 'ProductObj',
            target_related_list_name: 'salesorderproduct_product_list',
            __action_from: 'excel_paste'
        };
    }

    // 使用属性、CPQ、价格政策、返利、优惠券插件时不支持Excel粘贴
    notSupportExcelPaste() {
        let pluginApiNames = ['attribute'];
        // let pluginApiNames = [];
        let plugins = this.pluginService.api.getPlugins() || [];
        return plugins.filter(item => pluginApiNames.includes(item.pluginApiName)).length;
    }

    /**
     * 渲染ecxcel导入按钮
     *
     * @param {object} plugin
     * @param {object} param
     * @returns
     */
    _mdRenderBefore(plugin, param) {
        let plugins = plugin.api.getPlugins() || [];
        let res = plugin.preData;
        this._cachePlugins = plugins;
        return res;
    }

    /**
     * 获取校验对象
     *
     * @returns
     */
    excelGetValidateObj() {
        return {
            QuoteLinesObj: [
                'product_id', //产品名称
            ],
            SalesOrderProductObj: [
                'product_id', //产品名称
            ],
            NewOpportunityLinesObj: [
                'product_id', //产品名称
            ],
            SaleContractLineObj: [
                'product_id', //产品名称
            ],
        };
    }

    /**
     * 屏蔽导入字段
     *
     * @param {arr} columns
     * @returns
     */
    excelFiltersMappingFields(param, columns = []) {
        let fields = util.getDisableField(this._cachePlugins, (...args) => {
            return this.getConfig.apply(this, args);
        }, param);

        columns =  columns.filter(item => !fields.includes(item.id));

        return columns;
    }

    /**
     * excel导入支持多单位
     * @param {*} data 产品数据
     * @param {*} param
     * @returns
     */

    excelImportCheckMultiUnit(data, param,) {
        let msg = [];
        let multiUnitPlugin = this.getMultiUnitPlugin();

        if (!multiUnitPlugin) {
            return Promise.resolve({
                msg,
                data
            });
        }

        return new Promise((resolve, reject) => {
            let unitErr = [];
            let unit = 'unit';
            let {product_id, actual_unit, is_multiple_unit} = this.getPluginField(multiUnitPlugin, ['product_id', 'actual_unit', 'is_multiple_unit'], param);

            if (!product_id || !actual_unit) {
                return resolve({
                    msg,
                    data
                });
            }

            // 补充字段
            function addField() {
                let fields = {
                    [actual_unit]: 'unit',
                    [is_multiple_unit]: 'is_multiple_unit',
                };

                data.forEach(item => {
                    for (let [key, value] of Object.entries(fields)) {
                        if (key === actual_unit && item[key]) return;
                        item[key] = item[product_id + '__ro'] && item[product_id + '__ro'][value];
                    }
                });
            }

            // 排序数据
            function sortData(indexs = []) {
                return function (data = []) {
                    let res = [];
                    indexs.forEach(index => {
                        let arr = data.filter(item => item.rowId === index);

                        if (arr.length) {
                            res.push(arr[0]);
                        }
                    })

                    return res;
                }
            }

            addField();

            let sortDataHandle = sortData(data.map(item => item.rowId));
            let dataSingle = data.filter(item => item[product_id] && item[product_id + '__ro'] && !item[product_id + '__ro'].is_multiple_unit);
            let dataMulti = data.filter(item => item[product_id] && item[product_id + '__ro'] && item[product_id + '__ro'].is_multiple_unit);

            // 非多单位产品
            if (dataSingle.length) {
                // 单位字段值不是产品的单位，不能导入
                dataSingle = dataSingle.filter(item => {
                    if (item[product_id + '__ro'] && item[product_id + '__ro'][unit] && item.__fields && item.__fields[actual_unit] && item.__fields[actual_unit].options) {
                        let options = item.__fields[actual_unit].options;
                        let value = item[product_id + '__ro'][unit];
                        let opts = options.find(item => item.value === value);
                        if (!opts || item[actual_unit] && opts.value !== item[actual_unit]) {
                            unitErr.push(item);
                            return false;
                        }
                        return true;
                    }
                });
            }

            // 多单位产品
            if (dataMulti.length) {
                // 获取可用单位，单位字段值不在可用单位内的数据不能导入
                CRM.util.getProductUnitOptionsBatch({
                    dataIds: _.pluck(dataMulti, product_id),
                    sourceObjApiName: param.objApiName
                }).then((result) => {
                    let optionList = result.optionList || [];
                    dataMulti = dataMulti.filter(item => {
                        let flag = true;
                        optionList.forEach(cItem => {
                            if (cItem.productId === item[product_id] && item[actual_unit]) {
                                if (!(cItem.options || []).filter(opt => opt.value === item[actual_unit]).length) {
                                    unitErr.push(item);
                                    flag = false;
                                }
                            }
                        });
                        return flag;
                    });

                    if (unitErr.length) {
                        msg.push($t('单位不匹配的数据不允许粘贴'));
                    }

                    resolve({
                        msg,
                        data: sortDataHandle(([]).concat(dataSingle, dataMulti)),
                        unitErr
                    })
                });
                return;
            }
            resolve({
                msg,
                data: sortDataHandle(([]).concat(dataSingle, dataMulti)),
                unitErr
            })
        })
    }

    /**
     * 执行导入前
     *
     * @param {object} plugin
     * @param {object} param
     * @returns
     */
    excelImportBefore(plugin, param) {
        let {product_id} = this.getAllFields();
        let masterData = param.dataGetter.getMasterData();
        let {i18n, alert} = this.pluginService.api;
        if (!masterData.account_id && this.getConfig('openPriceList')) {
            alert(i18n('请先选客户'));
            return plugin.skipPlugin();
        }
        let details = param.dataGetter.getDetail(param.objApiName);
        this._details = Object.assign([], details);
        return {
            supportPaste: param.fieldName === product_id && !this.notSupportExcelPaste(),
            fieldIsReadOnly: param.fieldName === product_id,
            noSupportPaste: this.notSupportExcelPaste(),    // 开了某些开关，不支持excel粘贴
            pasteConfig: {
                filtersMappingFields: this.excelFiltersMappingFields.bind(this, param),
                // filterExcelDatas: this.filterExcelDatas.bind(this),
                // validateMappingFields: this.excelValidateMappingFields.bind(this),
                // parseFormatDatas: this.excelParseFormatDatas.bind(this, param)
                parseRelatedListParam :this._parseRelatedListParam.bind(this, param),

            }
        }
    }

    // 拦截底层请求，更改请求参数，支持粘贴非标产品
    _parseRelatedListParam(param, request){
        if(request?.associated_object_describe_api_name === 'ProductObj'){
            request.object_data = request.object_data || {};
            request.object_data.not_filter_non_product_flag = true;
        }
        return request;
    }

    // 开了价目表的企业，产品名称不能编辑，所以需要校验粘贴的数据中是否有新增的数据，是否有产品名称为空的多余数据
    delAddData(param, addDatas) {
        if (this.getConfig('openPriceList')) {
            let msg = '';
            let {product_id} = this.getAllFields();
            // 如果粘贴的数据中有产品名称，那新增的数据中，产品名称必须要有值。change的数据产品名称也必须有值
            if (param.pasteContent.changeFields.includes(product_id)) {
                if (param.pasteContent.addDatas && param.pasteContent.addDatas.length) {
                    let f = param.pasteContent.addDatas.find(item => !item[product_id]);
                    if (f) {
                        msg += this.i18n('因产品名称不可编辑，不能粘贴多余数据') + '<br/>';
                        param.pasteContent.addDatas.forEach(item => {
                            if (!item[product_id]) {
                                param.dataUpdater.del(param.objApiName, item.rowId);
                                CRM.util.deleteArrObj(addDatas, 'rowId', item.rowId);
                            }
                        });
                        param.pasteContent.addDatas = param.pasteContent.addDatas.filter(item => item[product_id]);
                    }
                }
                if (param.pasteContent.changeData && param.pasteContent.changeData.length) {
                    let f = param.pasteContent.changeData.find(item => !item[product_id]);
                    if (f) {
                        msg += this.i18n('因产品名称不可编辑，不能粘贴产品名称为空的数据') + '<br/>';
                        param.pasteContent.changeData = param.pasteContent.changeData.filter(item => item[product_id]);
                        CRM.util.deleteArrObj(addDatas, 'rowId', item.rowId);
                    }
                }
            } else {
                // 如果粘贴的数据没有产品名称，不能粘贴多余数据
                if (param.pasteContent.addDatas && param.pasteContent.addDatas.length) {
                    msg += this.i18n('因产品名称不可编辑，不能粘贴多余数据') + '<br/>';
                    param.pasteContent.addDatas.forEach(item => {
                        param.dataUpdater.del(param.objApiName, item.rowId);
                        CRM.util.deleteArrObj(addDatas, 'rowId', item.rowId);
                    });
                    param.pasteContent.addDatas = [];
                }
            }
            if (msg) this.alert(msg);
        }
    }

    // 不支持粘贴组合产品，不支持粘贴母子件产品的所有字段
    delAddDatForBom(param, addDatas){
        let {root_prod_pkg_key} = this.getPluginFields('bom', param.objApiName) || {};
        if(!root_prod_pkg_key) return;
        let msg = '';
        let {product_id} = this.getAllFields();
        // 如果粘贴的数据中有产品名称
        if (param.pasteContent.changeFields.includes(product_id)) {
            if (param.pasteContent.addDatas && param.pasteContent.addDatas.length) {
                // 如果数据中有组合产品，删除组合产品并提示
                if (param.pasteContent.addDatas.some(item => item[product_id + '__ro'] && item[product_id + '__ro'].is_package)) {
                    let productName = [];
                    param.pasteContent.addDatas.forEach(item => {
                        if (item[product_id + '__ro'] && item[product_id + '__ro'].is_package) {
                            productName.push(item[product_id + '__ro'].name);
                            param.dataUpdater.del(param.objApiName, item.rowId);
                            CRM.util.deleteArrObj(addDatas, 'rowId', item.rowId);
                        }
                    });
                    param.pasteContent.addDatas = param.pasteContent.addDatas.filter(item => !item[product_id + '__ro'] || !item[product_id + '__ro'].is_package);
                    if(productName.length) msg += '【' + productName.join(',') + '】'  + this.i18n('sfa.crm.excelpaste.not_support_package_product') + '<br/>'; // 不支持粘贴组合产品
                }
            }
            if (param.pasteContent.changeData && param.pasteContent.changeData.length) {
                // 如果数据中有组合产品，删除组合产品并提示
                if (param.pasteContent.changeData.some(item => item[product_id + '__ro'] && item[product_id + '__ro'].is_package)) {
                    let productName = [];
                    param.pasteContent.changeData = param.pasteContent.changeData.filter(item => {
                        if (item[product_id + '__ro'] && item[product_id + '__ro'].is_package) {
                            productName.push(item[product_id + '__ro'].name);
                            CRM.util.deleteArrObj(addDatas, 'rowId', item.rowId);
                            return false;
                        }
                        return true;
                    });
                    if(productName.length) msg += '【' + productName.join(',') + '】' + this.i18n('sfa.crm.excelpaste.not_support_package_product') + '<br/>'; // 不支持粘贴组合产品
                }
            }
        }
        // 如果粘贴的数据中有母子件产品，删除 changeData 中的母子件产品
        if(param.pasteContent.changeData?.length){
            let productName = [];
            let changeData = param.pasteContent.changeData;
            param.pasteContent.changeData = changeData.filter(item => {
                let row = param.dataGetter.getData(param.objApiName, item.rowId);
                if(row && row[root_prod_pkg_key]){
                    productName.push(row[product_id + '__r']);
                    CRM.util.deleteArrObj(addDatas, 'rowId', item.rowId);
                    return false;
                }
                return true;
            });
            if(productName.length) msg += '【' + productName.join(',') + '】' + this.i18n('sfa.crm.excelpaste.not_support_package_product', null, '不支持粘贴组合产品') + this.i18n('字段') + '<br/>'; // 不支持粘贴组合产品
        }
        if (msg) this.alert(msg);
    }

    // 反算
    async calculateSomeField(addDatas, param) {
        let changeFields = param.pasteContent.changeFields;
        if (!changeFields || !changeFields.length) return;
        changeFields.forEach(async f => {
            await this.runPlugin('backcalculation.calculateMeta', {
                data: addDatas,
                field: f,
                mdApiName: param.objApiName,
                param
            });
        })
    }

    // 过滤重复产品
    filterRepeatData(param) {
        let isSupportCopy = this.getConfig('43') == '1';
        if (isSupportCopy) return;
        let {product_id} = this.getAllFields();
        let msg = [];
        let filterData = [];
        let repeatData = [];

        function _spliceRepeatData(data, ids, apiDel) {
            let len = data.length;
            while (len--) {
                let item = data[len];
                if (ids.includes(item[product_id])) {
                    msg.push(item[product_id + '__r']);
                    data.splice(len, 1);
                    if (apiDel) param.dataUpdater.del(param.objApiName, item.rowId);
                }
            }
        }

        function _filterRepeatData(data, apiDel) {
            let len = data.length;
            while (len--) {
                let item = data[len];
                if (filterData.includes(item[product_id])) {
                    repeatData.push(item[product_id + '__r']);
                    data.splice(len, 1);
                    if (apiDel) param.dataUpdater.del(param.objApiName, item.rowId);
                } else {
                    filterData.push(item[product_id])
                }
            }
            return repeatData;
        }

        if (param.pasteContent.changeFields.includes(product_id)) {
            let details = this._details;
            let addData = param.pasteContent.addDatas,
                changeData = param.pasteContent.changeData;
            // 过滤粘贴数据中的重复数据
            if (addData && addData.length) {
                let rd = _filterRepeatData(addData, true);
                msg = msg.concat(rd);
            }
            if (changeData && changeData.length) {
                let rd = _filterRepeatData(changeData);
                msg = msg.concat(rd);
            }
            if (msg.length) {
                this.alert(this.i18n('产品') + ': 【 ' + msg.join() + ' 】' + this.i18n('重复，已过滤'));
                msg = [];
            }
            // 过滤和已有数据重复的数据
            let ids = details.map(item => item[product_id]);
            ids = ids.filter(id => id);
            if (!ids.length) return;
            if (addData && addData.length) _spliceRepeatData(addData, ids, true);
            if (changeData && changeData.length) _spliceRepeatData(changeData, ids);
            if (msg.length) this.alert(this.i18n('产品') + ': 【 ' + msg.join() + ' 】' + this.i18n('和现有数据重复，不允许粘贴'))
        }
    }

    // 开了价目表，粘贴产品名称，change字段需要加上 价目表明细、价格，需要计算相关字段
    addChangeField(param) {
        let mdApiName = param.objApiName;
        let allFields = this.getPluginFields('price-service', mdApiName);
        let {product_id, price_book_product_id, product_price} = allFields;
        if (param.pasteContent.changeFields && param.pasteContent.changeFields.includes(product_id) && this.getConfig('openPriceList')) {
            param.pasteContent.changeFields = param.pasteContent.changeFields.concat([price_book_product_id, product_price]);
        }
    }

    /**
     * 导入数据回填明细前
     *
     * @param {object} plugin
     * @param {object} param
     * @returns
     */
    async excelImportAfter(plugin, param) {
        let addDatas = Array.concat([], param.pasteContent.changeData || [], param.pasteContent.addDatas || []);
        let r1 = await this.runPlugin('excelpaste.parseData.before', {
            addDatas,
            param,
        });
        if(r1 && r1.noRunBusiness){
            return;
        }
        param.lookupField = param.lookupField || {
            api_name: this.lookupField.api_name,
            target_api_name: this.lookupField.target_api_name,
            target_related_list_name: this.lookupField.target_related_list_name,
            __action_from: this.lookupField.__action_from
        };
        let mdApiName = param.objApiName;
        this.addChangeField(param);
        this.delAddData(param, addDatas);
        this.delAddDatForBom(param, addDatas);
        this.filterRepeatData(param);

        let {product_id} = this.getAllFields(param.objApiName);
        await this.calculateSomeField(addDatas, param);
        addDatas = addDatas.filter(item => item[product_id]);
        if (!addDatas.length) return await this.runOtherPlugin(param);

        this.addPriceBookId(addDatas, param);
        addDatas = await this.filterMultiData(addDatas, param);
        if (!addDatas.length) return ;
        let {actual_unit, unit} = this.getMultiUnitPlugin() ? this.getPluginField(this.getMultiUnitPlugin(), ['actual_unit', 'unit'], param) : {
            actual_unit: 'unit',
            unit: 'unit'
        };
        let lookupData = addDatas.map(item => {
            let obj = item[product_id + '__ro'] || item;
            // 添加字段
            obj._selected_num = item.quantity;
            // 没开价目表强制优先级，单位保留导入单位
            if (!this.pluginParam.bizStateConfig.priceBookPriority && typeof item[actual_unit] !== 'undefined') {
                obj[unit] = item[actual_unit];
            }
            return obj;
        });
        let masterData = param.dataGetter.getMasterData();
        // 没有不计算的字段
        param.filterFields = param.filterFields || {};
        param.filterFields[param.objApiName] = [];
        param = Object.assign(param, {
            addDatas: addDatas,
            lookupDatas: lookupData,
            master_data: masterData,
            newDataIndexs: addDatas.map(item => item.rowId)
        });
        await this.runPlugin('md.batchAdd.after', param);
    }

    // 粘贴单独列，执行其他业务逻辑；多单位改数量
    async runOtherPlugin(param) {
        let cds = param.pasteContent.changeData;
        let details = param.dataGetter.getDetail(param.objApiName);
        if(!details.length) return;
        let {product_id} = this.getAllFields(param.objApiName);
        let changeData = [];
        cds.forEach(item => {
            let f = details.find(c => c.rowId === item.rowId);
            if(f) changeData.push(Object.assign({}, f, item))
        });
        changeData = changeData.filter(item => item[product_id]);
        if(!changeData.length) return;
        let changeFields = param.pasteContent.changeFields;

        if(this.getMultiUnitPlugin()){
            let {quantity} = this.getPluginField(this.getMultiUnitPlugin(), ['quantity'], param);
            if(changeFields.includes(quantity)){
                let list = await this.runPlugin('multiunit.batchTodoMultiunit', {
                    list: changeData,
                    objectApiName: param.masterObjApiName,
                    detailObjectApiName: param.objApiName,
                    objApiName: param.objApiName,
                    pluginParam: param,
                    updateData: true,
                    ignoreFields: [quantity]
                });
            }
        }
    }

    /**
     * 粘贴数据执行UI事件后
     *
     * @param {object} context
     * @param {object} param
     * @returns
     */
    async excelImportEnd(context, param) {
        let addDatas = Array.concat([], param.pasteContent.changeData || [], param.pasteContent.addDatas || []);
        if(!addDatas.length) return;
        await this.runPlugin('md.batchAdd.end', param);
    }

    // 如果主对象价目表必填，需要给粘贴的数据价目表赋值
    addPriceBookId(addData, param) {
        let {product_id, form_price_book_id, price_book_id} = this.getAllFields();
        if (param.pasteContent.changeFields && param.pasteContent.changeFields.includes(product_id) && this.getConfig('openPriceList')) {
            let des = param.dataGetter.getDescribe(param.masterObjApiName).fields;
            let isRequire = des[form_price_book_id] && des[form_price_book_id].is_required;
            if (!isRequire) return;
            let masterData = param.dataGetter.getMasterData();
            let pId = masterData[form_price_book_id];
            if (pId) {
                addData.forEach(item => {
                    item[price_book_id] = pId;
                })
            }
        }
    }

    // 处理多单位数据，不符合规则的数据需要删除，并提示
    async filterMultiData(addDatas, param) {
        let msg = [];
        let res = await this.excelImportCheckMultiUnit(addDatas, param);
        let {product_id} = this.getPluginField(this.getMultiUnitPlugin(), ['product_id'], param);

        // 删除数据
        function _delData(rowId) {
            let changeData = param.pasteContent.changeData,
                addData = param.pasteContent.addDatas;
            if (changeData) {
                let f = changeData.find(item => item.rowId === rowId);
                if (f) {
                    let keys = Object.keys(f);
                    keys.forEach(k => delete f[k]);
                }
            }
            if (addData) {
                let f = addData.find(item => item.rowId === rowId);
                if (f) param.dataUpdater.del(param.objApiName, rowId);
            }
        }

        if (res.unitErr && res.unitErr.length) {
            res.unitErr.forEach(item => {
                msg.push(item[product_id + '__r']);
                _delData(item.rowId);
            });
            if (msg.length) {
                this.alert(this.i18n('产品') + ': 【 ' + msg.join() + ' 】 ' + this.i18n('单位不匹配，粘贴失败'))
            }
        }
        return res.data;
    }

    /**
     * 是否执行取价
     *
     * @param {object} plugin
     * @param {object} opts
     * @returns
     */
    priceServiceBatchAddAfterBefore(plugin, {param} = opts) {
        if (param.lookupField.__action_from === this.lookupField.__action_from) {
            // param.lookupField.__action_from = null;
            return {
                isDoPriceService: true
            };
        }
    }

    getMultiUnitPlugin() {
        return util.getPlugin(this._cachePlugins, 'multi-unit');
    }

    getPluginField(plugin = {}, fields = [], param = {}) {
        return util.getPluginField.apply(this, arguments);
    }

    getHook() {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            },
            {
                event: 'md.excelpaste.before',
                functional: this.excelImportBefore.bind(this)
            },
            {
                event: 'md.excelpaste.after',
                functional: this.excelImportAfter.bind(this)
            },
            {
                event: 'md.excelpaste.end',
                functional: this.excelImportEnd.bind(this)
            },
            {
                event: 'price-service.batchAddAfter.before',
                functional: this.priceServiceBatchAddAfterBefore.bind(this)
            },
            // {
            //     event: 'price-service.batchAddAfter.after',
            //     functional: this.priceServiceBatchAddAfterAfter.bind(this)
            // }
        ]
    }
}

