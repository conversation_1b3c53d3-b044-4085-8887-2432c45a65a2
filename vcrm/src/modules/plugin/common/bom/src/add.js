/*
 * @Descripttion:
 * @Author: LiAng
 * @Date: 2022-02-17 15:09:57
 * @LastEditors: LiAng
 * @LastEditTime: 2023-08-17 19:20:47
 */
/**
 * @desc: bom
 * @author: wangshaoh
 * @date: 2/16/22
 */
import PPM from 'plugin_public_methods'

export default class BeforeRender {

    constructor(pluginService, pluginParam, parent) {
        this.pluginService = pluginService;
        this.parent = parent;
        this.BomBase = this.parent.BomBase;
    }

    // 是否灰度了不回填根结点
    isGrayDeleteRoot(){
        return this.parent.getConfig('bom_delete_root') == '1';
    }

    // 从对象渲染之前，处理按钮展示；
    _mdRenderBefore(plugin, param) {
        let _this = this;
        let allFields = this.getAllFields(param.objApiName);
        const {product_id, bom_id, bom_core_id, is_package,  } = allFields;
        let mdApiName = param.objApiName;
        this.mdDescribe = param.dataGetter.getDescribe(mdApiName).fields || {};

        let _fn = (trData) => {
            // todo: 提供隐藏配置按钮字段 hideConfigBtn，以后UI事件支持了控制自定义按钮后要去掉
            if (PPM.isBom(trData, allFields).isPackage && !trData.parent_rowId && !trData.hideConfigBtn && !this.isGrayDeleteRoot()) {
                return {
                    add: [{
                        action: "bomConfigHandle",
                        label: $t("配置"),
                        callBack: this.bomConfigHandle.bind(this),
                    }],
                    del: ["insertHandle"]
                }
            }
            if (trData.parent_rowId) {
                return {
                    retain: null
                }
            }
        };
        if (!plugin.preData) plugin.preData = {};
        let isOpen = this.pluginService.api.getLocal('isOpenTree');
        if (isOpen === null || isOpen === undefined) {
            this.pluginService.api.setLocal('isOpenTree', false);
            isOpen = false;
        }
        let isOpenBomFirstColumn = CRM.util.getConfigStatusByKey('bom_first_column_with_layout') === '1';

        return {
            __execResult:{
                treeConfig: {
                    expend: !!isOpen,  // 展开收起
                    mainFieldName: isOpenBomFirstColumn ? '' : product_id
                },
                operateBtns:[_fn],
                operateBtnsDepends:[bom_id, bom_core_id, product_id],
                headerSlot:[this.initOpenTreeBtn.bind(this, plugin, param), this.initTotalMoney.bind(this, plugin, param)],

                tableOptions: {
                    renderComplete(event) {
                        // event.on('update', () => event.getCheckedData());
                        event.on('check.change', checkDatas => {
                            _this.onTableChecked(mdApiName)
                        })
                    }
                }
            },
            __mergeDataType:{
                array: 'concat'
            }
        }
    }

    // 表格勾选事件
    onTableChecked(mdApiName){
        let param = this.pluginService.api.pluginServiceFactory({
            pluginApiName:'tableChecked'
        });
        let cd = param.dataGetter.getCheckedDatas(mdApiName);
        if(cd?.length){
            this.showTotalMoney(cd, param)
        }else{
            this.hideTotalMoney();
        }
        param.end();
    }

    // 初始化总金额
    initTotalMoney(plugin, param, $wrapper) {
        this.initTotalMoneyElement($wrapper);
        this.initSubPriceTips($wrapper);
        return {
            destroy(){
               
            }
        }
    }

    // 初始化总金额元素
    initTotalMoneyElement(wrapper) {
        let dom = `<div class="crm-bom-md-totalmoney">
                        <span class="crm-bom-md-totalmoney-name">${$t('总金额')}:</span>
                        <span class="crm-bom-md-totalmoney-value"></span>
                        <span class="crm-bom-md-totalmoney-currency"></span>
	                </div>`;
        $(wrapper).append(dom);
    }

    // 子件定价+母件定价提示
    initSubPriceTips(wrapper) {
        let tips = `<div class="crm-bom-md-totalmoney-tips"></div>`;
        $(wrapper).append(tips);
        this.setTipWrap();
    }

    // 子件定价+母件定价提示
    setTipWrap() {
        this.tipWrap && this.tipWrap.destroy && this.tipWrap.destroy(); 
        this.tipWrap = FxUI.create({
            wrapper: $('.crm-bom-md-totalmoney-tips')[0],
            template: `
                <fx-alert
                    title=""
                    :show-icon="true"
                    :closable="false"
                    is-mini
                    type="warning"
                    >
                        <template #title>
                            <span v-html="msg"></span>
                            <span class="crm-bom-md-totalmoney-tips-btn" @click="showDetail">{{ $t("查看") + $t("说明") }}</span>
                          
                        </template>
                    </fx-alert>
            `,
            computed: {
                msg() {
                    return $t('sfa.bom.sub_price_tips1', null, '当前明细存在不同售卖方式的产品组合')
                }
            },
            data() {
                return {
                     
                }
            },
            methods: {
                showDetail() {
                    let msg = `<div>
                        <p>${$t('sfa.bom.sub_price_tips2', null, '售卖方式是【按子件灵活折扣售卖】时，允许修改所有母子件的价格、折扣、销售金额小计等信息')}</p>
                        <p>${$t('sfa.bom.sub_price_tips3', null, '售卖方式是【按整套价格折扣售卖】时，允许修改单套的价格、折扣、销售金额小计，子件的折扣、销售金额小计等不展示且不可修改')}</p>
                    </div>`;
                    this.$alert(msg, $t('提示'), {
                        confirmButtonText: $t('我知道了'),
                        dangerouslyUseHTMLString: true,
                  
                      });
                }
            }
        })
    }

    // 展示提示信息，用于子件定价+母件定价提示
    showSubPriceTips(param) {
        let mdApiName = param.objApiName;
        let allFields = this.getAllFields(mdApiName);
        let details = param.dataGetter.getDetail(mdApiName);
        let subPriceBom = details.find(item => PPM.isBom(item, allFields).isPackage && this.parent.BomBase.Add.isSubPrice(item, param));
        let rootBom = details.find(item => PPM.isBom(item, allFields).isPackage && !this.parent.BomBase.Add.isSubPrice(item, param));
        if(subPriceBom && rootBom){
            $('.crm-bom-md-totalmoney-tips').show();
        }
    }

    // 展示总金额
    showTotalMoney(selectData, param){
        let totalMoney = this.getSelectedTotalMoney(selectData, param);
        $('.crm-bom-md-totalmoney').show();
        $('.crm-bom-md-totalmoney-value').html(totalMoney);
    }

    // 隐藏总金额
    hideTotalMoney(selectData){
        $('.crm-bom-md-totalmoney').hide();
    }

    // 统计勾选行的小计字段金额
    getSelectedTotalMoney(selectData, param){
        let {subtotal, form_mc_currency} = this.getAllFields();
        let field = subtotal;
        let res = this.parent.runPluginSync('bom.showTotalMoney.before', {});
        if(res && res.calField) field = res.calField;
        let dp = this.mdDescribe[field]?.decimal_places;
        let m = 0;
        selectData.forEach(item => {
            if(PPM.hasValue(item[field])){
                m = PPM.accAdd(m, item[field]);
            }
        })
        if(CRM._cache.currencyStatus){
            let currency = param.dataGetter.getMasterData()[form_mc_currency] || '';
            return CRM.util.formatMoneyForCurrency(m, currency, dp);
        }else{
            return CRM.util.formatMoney(m, dp)
        }
    }

    // 初始化展开按钮；
     initOpenTreeBtn(plugin, param, $wrapper) {
        $wrapper = $($wrapper);
        $wrapper.append('<div class="newmd_openTree_md2"><span class="newmd_openTree_title">' + this.parent.i18n('默认展开多级结构') + '</span><span class="newmd_openTree_switch"></span></div>');
        this.renderSwitch(param.objApiName);
        $wrapper.find('.newmd_openTree_md2').show();
        return {
            destroy(){
                this.openTreeSwitch && this.openTreeSwitch.destroy && this.openTreeSwitch.destroy();
                this.openTreeSwitch = null
            }
        }
    }

    // 渲染开关组件；
    renderSwitch(mdApiName) {
        let _this = this;
        let isOpen = this.pluginService.api.getLocal('isOpenTree');
        this.openTreeSwitch && this.openTreeSwitch.destroy && this.openTreeSwitch.destroy();
        this.openTreeSwitch = FxUI.create({
            wrapper: '.newmd_openTree_switch',
            template: `<fx-switch
								  v-model="value"
								  size="mini"
								  @change="change"
							  >
						</fx-switch>`,
            data() {
                return {
                    value: isOpen
                }
            },
            methods: {
                change(data, node, tree) {
                    _this.pluginService.api.setLocal('isOpenTree', data);
                    let factory = _this.pluginService.api.pluginServiceFactory({
                        pluginApiName:'bom'
                    });
                    factory.dataUpdater.updateTreeExpend(data, mdApiName);
                    factory.end();
                },
            }
        })
    }

    getAllPluginFields(mdApiName){
        return this.parent.getSomePluginFields(['bom', 'attribute', 'period_product', 'multi-unit'], mdApiName);
    }

    getAllFields() {
        return this.parent.getAllFields.apply(this.parent, arguments);
    }

    getMdFields(mdApiName) {
        return this.parent.getMdFields.apply(this.parent, arguments);
    }

    // 如果是自定义对象，需要初始化下数据，对齐dialog_selectbom组件需要的字段
    initExtendData(extendData = [], mdApiName = ''){
        if(!mdApiName.includes('__c')) return;
        let allMdFields = this.getMdFields(mdApiName);
        let key__r = ['product_id'];
        PPM.forEachTreeData(extendData, item => {
            for (let [key, value] of Object.entries(allMdFields)) {
                if(PPM.hasValue(item[value])) {
                    item[key] = item[value];
                    if(key__r.includes(key)){
                        item[key + '__r'] = item[value + '__r'];
                    }
                }
            }
        })
    }

    // 二次配置按钮触发
    async bomConfigHandle(rowData = {}, opts = {}) {
        let allFields = this.getAllPluginFields();
        const {bom_core_id, bom_type, bom_version, product_id, price_book_id, product_price, form_account_id, attribute_json, form_price_book_id, form_partner_id, form_mc_currency, pricing_period} = allFields;

        let rootData = this.currentRow = rowData;
        // let param = plugin.api.factory();
        let recordType = rowData.record_type;
        let des = this.mdDescribe[product_price];
        let param = this.pluginService.api.pluginServiceFactory({
            pluginApiName:'bom'
        });
        let masterData = param.dataGetter.getMasterData();
        let id = rowData[product_id];
        let mdApiName = rowData.object_describe_api_name;
        let details = param.dataGetter.getDetail(mdApiName);
        const priceBookDesc = param.dataGetter.getDescribe(mdApiName)?.fields?.price_book_id;
        let children = PPM.getChildren({rowId: rowData.rowId, details});
        let treeData = PPM.parseDataToTree(PPM.deepClone(children.concat([rowData])));
        let extendData = treeData[0].children || [];
        this.initExtendData(extendData, mdApiName);
        this.addModifyPrice(extendData, mdApiName);
        // if(!masterData[form_account_id]){  // 880去掉的
        //     this.parent.alert(this.parent.i18n('请先选择客户'));
        //     return
        // }
        let r1 = await this.parent.runPlugin('bom.renderSelectBom.before', {
            param,
            rootData,
            childrenData: extendData
        });
        let hasPeriod = this.parent.hasPlugin('period_product', mdApiName);
        await this.renderSelectBom({
            bom_version: rowData[bom_version],
            bom_type: rowData[bom_type + '__v'],
            bom_core_id: rowData[bom_core_id],
            rootId: id,
            extendData: extendData,
            rowData: rowData,
            isFrom: 'twiceConfig',
            title: this.parent.i18n('配置产品') + '：' + rowData[product_id + '__r'],
            priceBookId: rowData[price_book_id] || masterData[form_price_book_id] || '',
            accountId: masterData[form_account_id],
            partnerId: masterData[form_partner_id] || '',
            mcCurrency: masterData[form_mc_currency] || '',
            attribute: rowData[attribute_json] || {},
            apinameFrom: mdApiName,
            object_data: masterData,
            isHideTotalMoney: !this.isShowPrice(product_price, mdApiName, recordType, param),
            decimal_places: des ? des.decimal_places : 2,
            mapFields: allFields,
            extendRootData: rootData,
            masterData,
            details: PPM.parsePriceBookDataRangeDetails({[mdApiName]: details}, priceBookDesc),
            extendParam: opts.extendParam || {},
            root_pricing_period: rootData[pricing_period] || 1,
            isSupportPeriod: hasPeriod,

        }, param);
    }

    /**
     * @desc 添加统一字段 modified_adjust_price、lookup_product_id、lookup_product_id__r
     * @param data
     */
    addModifyPrice(data, mdApiName) {
        const {product_price, product_id} = this.getAllFields(mdApiName);
        function _fn(list) {
            list.forEach(item => {
                if(!PPM.isObject(item)) return;
                item.modified_adjust_price = item[product_price];
                if (!item.hasOwnProperty('lookup_product_id')) item.lookup_product_id = item[product_id];
                if (!item.hasOwnProperty('lookup_product_id__r')) item.lookup_product_id__r = item[product_id + '__r'];
                if (item.children) _fn(item.children);
            })
        }
        _fn(data)
    }

    // 价格是否展示；
    isShowPrice(filedName, mdApiName, recordType, param) {
        return param.assertInLayouts(filedName, mdApiName, recordType);
    }

    // 选配bom组件
    async renderSelectBom(opt, param) {
        let _this = this;
        let recordType = _this.currentRow.recordType || _this.currentRow.record_type;
        let masterData = param.dataGetter.getMasterData();
        let masterApiName = param.masterObjApiName;
        let mdApiName = opt.apinameFrom;
        let extendBomParam = {};
        let res = await this.parent.runPlugin('bom.twiceConfig.before', {
            param
        });
        if(res && res.extendBomParam) extendBomParam = res.extendBomParam;
        const SelectBOM = await this.getSelectBomComponent(opt)
        _this.SelectBom = new SelectBOM(Object.assign(opt, {
            onSelect: dialogEnterHandle
        }, extendBomParam));

        async function dialogEnterHandle (obj) {
            await _this.parent.BomBase.Add.updateBomForTwiceConfig({
                obj,
                rootData: _this.currentRow,
                mdApiName,
                recordType,
                masterData,
                masterApiName
            }, param);
            param.end();
        }

        _this.SelectBom.on && _this.SelectBom.on('dialogEnter', dialogEnterHandle);
    }

    async getSelectBomComponent(opts) {
        let comp = await this.pluginService.api.import('crm-modules/components/dialog_selectbom/dialog_selectbom');

        if (opts.extendParam.getSelectBomComponent) {
            comp = await opts.extendParam.getSelectBomComponent(opts.rowData, comp);
        }

        return comp;
    }

    // 添加参数，是否展示价格;
    _batchAddBefore(plugin, param) {
        let mdApiName = param.objApiName;
        let {product_price, form_account_id, form_partner_id, form_price_book_id, form_mc_currency, quantity} = this.parent.getAllFields();
        let recordType = param.recordType;
        if (!plugin.preData) plugin.preData = {};
        if (!plugin.preData.extendParam) plugin.preData.extendParam = {};
        plugin.preData.extendParam.isHideTotalMoney = !this.isShowPrice(product_price, mdApiName, recordType, param);
        let masterData = param.dataGetter.getMasterData();
        let lookUpApiName = param.lookupField.api_name;
        let pathConfig = this.getPickSelfPath(plugin, param);

        // 补充字段给server、底层组件用
        masterData.account_id = masterData[form_account_id] || '';
        masterData.partner_id = masterData[form_partner_id] || '';
        masterData.price_book_id = masterData.pricebook_id = masterData[form_price_book_id] || '';
        masterData.price_book_id__r = masterData[form_price_book_id + '__r'] || '';
        let hasPeriod = this.parent.hasPlugin('period_product', mdApiName);

        return Object.assign({
            fieldName:lookUpApiName,
            master_data: masterData,
            accountId: masterData[form_account_id],
            extendParam:{
                fieldName:lookUpApiName,
                master_data: masterData,
                accountId: masterData[form_account_id],
                fieldMapping: {
                    account_id: form_account_id,
                    partner_id: form_partner_id,
                    mc_currency: form_mc_currency,

                },
                is_from_bom:true,
                quantityDes: this.mdDescribe[quantity],
                quantityLayout: param.dataGetter.getFieldLayoutAttr(quantity, param.objApiName, param.recordType),
                isSupportPeriod: hasPeriod,

            }
        },pathConfig);
    }

    // 是否开了价格政策
    isUsePriceService() {
        let allPlugins = this.pluginService.api.getPlugins() || [];
        return allPlugins.find(item => item.pluginApiName === 'price-service');
    }

    /**
     * @desc 从xx添加，改组件路径；
     * @param param
     * @returns {{path: string}}
     * @private
     */
    getPickSelfPath(plugin, param) {
        let path;
        let {price_book_product_id, product_id} = this.getAllFields(param.objApiName);
        let lookUpApiName = param.lookupField.api_name;
        if (lookUpApiName === price_book_product_id) {
            // path = 'crm-modules/components/pickselfobject_cpq_npb/pickselfobject_cpq_npb';
            if(this.isUsePriceService()){
                path = 'crm-modules/components/pickselfobject_cpq_npb/pickselfobject_cpq_npb';
            }else {
                path = 'crm-modules/components/pickselfobject_cpq/pickselfobject_cpq';
            }
        }
        if (lookUpApiName === product_id) {
            path = 'crm-modules/components/pickselfobject_cpq/pickselfobject_cpq';
        }
        if([price_book_product_id, product_id].includes(lookUpApiName) && this._isModule2()){
            path = 'crm-modules/buscomponents/pickselfobject_cpq_style2/pickselfobject_cpq_style2';
        }

        if(path) return {modulePath: path}
    }

    // 模式3
    _isModule2(){
        // return true;
        return CRM.util.getConfigStatusByKey('cpq_ui_mode') === '1';
    }

    // 批量添加结束，如果有混合 bom 下单，展示提示信息
    _batchAddEnd(plugin, param) {
        this.showSubPriceTips(param);
    }

    _mdRenderAfter(plugin, param) {
        this.showSubPriceTips(param);
    }

    getHook() {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            }, {
                event: 'md.batchAdd.before',
                functional: this._batchAddBefore.bind(this)
            }, {
                event: 'md.batchAdd.end',
                functional: this._batchAddEnd.bind(this)
            }, {
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this)
            }

        ];
    }

    destroy() {
        this.openTreeSwitch && this.openTreeSwitch.destroy && this.openTreeSwitch.destroy();
        this.tipWrap && this.tipWrap.destroy && this.tipWrap.destroy(); 
        this.openTreeSwitch = null;
        this.tipWrap = null;
    }
}

