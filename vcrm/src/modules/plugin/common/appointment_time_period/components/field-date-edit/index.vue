<template>
    <div class="field-edit">
        <div
            class="field-edit-warpper"
            @mouseenter.capture="handleMouseEnter"
            @mouseleave.capture="handleMouseLeave"
        >
            <div class="field-edit-warpper-left">
                <span>{{ dateTime }}</span>
            </div>
            <div
                class="field-hoveredit"
                @click.stop="handleEdit"
                v-show="isShowEdit && hoverEdit"
                :style="style"
            >
                <span
                    :title="$t('编辑')"
                    class="star-ico-edit el-icon-edit-outline"
                ></span>
            </div>
        </div>
        <single-edit v-if="visible" :field="field" :values="values" :dLabel="dLabel" :size="dSize" :disabled="disabled" :format="sFormat" :useLocaleFormat="true" 
        :isRequired="isRequired" :start-placeholder="$t('开始日期')" :end-placeholder="$t('结束日期')" 
        :pickerOptions="pickerOptions" :appointmentTimePeriod="appointmentTimePeriod" :selectDateRange="selectDateRange" @save="handleSave" @close="handleClose"></single-edit>
        <!-- <div class="field-edit-singleedit" v-show="visible">
            <div class="edit-innder">
                <div class="view-wrap">
                    <div class="field-action-nfield">
                        <div class="view-box">
                            <div class="view-box-name">
                                <fx-tooltip placement="top">
                                    <div slot="content">{{ dLabel }}</div>
                                    <span class="view-box-name-tit">{{
                                        dLabel
                                    }}</span>
                                </fx-tooltip>
                            </div>
                            <div class="view-box-content">
                                <el-date-picker
                                    style="width: 224px"
                                    ref="datepick"
                                    clearable
                                    v-model="values"
                                    type="datetimerange"
                                    value-format="timestamp"
                                    :size="dSize"
                                    :disabled="disabled"
                                    :format="sFormat"
                                    :useLocaleFormat="true"
                                    :start-placeholder="$t('开始日期')"
                                    :end-placeholder="$t('结束日期')"
                                    @change="changeHandle"
                                    @blur="blurHandle"
                                    :picker-options="pickerOptions"
                                    :appointment_time_period="
                                        appointmentTimePeriod
                                    "
                                    :select_date_range="selectDateRange"
                                ></el-date-picker>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tit-wrap">
                    <span
                        class="tit-left fx-icon-close"
                        @click="handleClose"
                    ></span>
                    <fx-tooltip placement="top">
                        <div slot="content">{{ $t("保存") }}</div>
                        <span
                            class="tit-right fx-icon-ok-2 main"
                            @click="handleSave"
                        ></span>
                    </fx-tooltip>
                </div>
            </div>
        </div> -->
    </div>
</template>
<script>
import ElDatePicker from "../date-picker/index";
import SingleEdit from "../singleEdit/index";
const util = FS.crmUtil;

export default {
    name: "FieldDateEdit",
    components: {
        ElDatePicker,
        SingleEdit,
    },
    props: {
        field: {
            type: Object,
            required: true,
        },
        data: {
            type: Object,
            required: true,
        },
        values: {
            type: Array,
            default: () => [],
        },
        value: {
            type: String,
            default: "",
        },
        dLabel: {
            type: String,
            value: "",
        },
        isShowEdit: {
            type: Boolean,
            value: false,
        },
        style: {
            type: Object,
            value: {},
            default: () => ({
                position: "absolute",
                top: "2px",
                right: "-14px",
                width: "18px",
                zIndex: 506,
            }),
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        dSize: {
            type: String,
            default: "small",
        },
        appointmentTimePeriod: {
            type: Array,
            default: () => [],
        },
        sFormat: {
            type: String,
            default: "yyyy-MM-dd HH:mm",
        },
        selectDateRange: {
            type: Number,
            default: 7,
        },
        pickerOptions: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            visible: false,
            hoverEdit: false,
            dateTime: "",
            changeValues: [],
            isRequired: false,
        };
    },
    computed: {},
    methods: {
      initData() {
        this.dateTime = this.formatDateTime(this.field, this.values);
      },
      formatDateTime(field, val) {
          let isConverTimeTZ8 =
              field && field.not_use_multitime_zone == true ? true : false;
          let dateFormat = field && field.date_format,
              dateObj = { QQQ: "[Q]Q", ":MM": ":mm", ":SS": ":ss" };
          let f =
              (dateFormat || "YYYY-MM-DD HH:mm") +
              (field && field.pay_type === "payment" ? ":ss" : "");
          f = (f.toUpperCase() || "").replace(
              new RegExp(Object.keys(dateObj).join("|"), "g"),
              function ($1) {
                  return dateObj[$1] || $1;
              }
          );
          let date = this._fdate(val, f, isConverTimeTZ8);
          if (_.isArray(val) && val.length == 2) {
              let startT = this._fdate(val[0], f, isConverTimeTZ8),
                  endT = this._fdate(val[1], f, isConverTimeTZ8);
              if (_.isNaN(startT) && _.isNaN(endT)) {
                  date = "--";
              } else {
                  startT = _.isNaN(startT) ? "--" : startT;
                  endT = _.isNaN(endT) ? "--" : endT;
                  date = startT + " ~ " + endT;
              }
          } else if (
              _.isArray(val) &&
              val.length == 1 &&
              (val[0] + "").length < 3
          ) {
              let timeShortcuts = [
                  [$t("本年度"), 6],
                  [$t("上一年度"), 7],
                  [$t("下一年度"), 10],
                  [$t("本季度"), 13],
                  [$t("上一季度"), 14],
                  [$t("下一季度"), 15],
                  [$t("本月"), 4],
                  [$t("上月"), 5],
                  [$t("下月"), 8],
                  [$t("本周"), 2],
                  [$t("上周"), 3],
                  [$t("下周"), 9],
                  [$t("今天"), 11],
                  [$t("昨天"), 1],
                  [$t("明天"), 12],
              ];
              timeShortcuts.forEach((item) => {
                  if (_.indexOf(item, val[0] * 1) > -1) {
                      date = item[0];
                  }
              });
          }
          return date;
      },
      _fdate: function (val, reg, isConverTimeTZ8) {
          val = val === null ? void 0 : val; // 兼容值为null
          let time = +val;
          if (_.isNaN(time)) {
              return time;
          } else if (_.isArray(val) && val.length == 0) {
              return "";
          }

          // 开启多区域的话，不显示秒
          let currentEmployee = FS.contacts.getCurrentEmployee();
          let supportRegion = !!currentEmployee.region;
          if (supportRegion && reg === "YYYY-MM-DD HH:mm:ss") {
              reg = "YYYY-MM-DD HH:mm";
          }

          if (!isConverTimeTZ8) {
              // 日期时间类型不需要转时区
              return FS.moment
                  .unix(time / 1000, true)
                  .format(reg || "YYYY-MM-DD");
          } else {
              return FS.moment
                  .unix(FS.util.convertTimestampFromTZ8(time) / 1000, true)
                  .format(reg || "YYYY-MM-DD");
          }
      },
      handleEdit() { 
          this.fetchFields(this.data, this.field).then((fields)=> {
            this.visible = true;
            this.hoverEdit = false;
            if (fields && fields.length > 0) {
              this.isRequired = fields.find(item => item.api_name === this.field).is_required;
            }
          }).catch((res)=> {
            var msg = res.Result.FailureMessage;
            if(FxUI && FxUI.Message) {
                FxUI.Message({
                    isMiddler: true,
                    duration: 1500,
                    message: msg,
                    type: 'warning'
                })
            } else {
                util.alert(msg);
            }
          });
      },
      handleMouseEnter() {
          this.hoverEdit = true;
      },
      handleMouseLeave() {
        this.hoverEdit = false;
      },
      handleClose() {
        this.visible = false;
      },
      handleSave(values) {
        this.visible = false;
        this.$emit("onSave", values);
      },
      focus() {
        this.$refs.datepick && this.$refs.datepick.focus();
      },
      blurHandle() {
      },
      fetchFields(obj, field) {
        return new Promise((resolve, reject) => {
            if(obj.fieldList) return resolve(obj.fieldList);
            util.waiting('  ');
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + obj.object_describe_api_name + '/controller/QuickEditLayout',
                data: {
                    dataId: obj._id,
                    fieldApiName: field
                },
                success: function(res) {
                    if(res.Result.StatusCode === 0) {
                        resolve(res.Value.fields);
                    } else {
                        reject(res);
                    }
                },
                complete: function() {
                    util.waiting(false);
                }
            }, {
                errorAlertModel: 1
            })
        })
      },
      dataIsNull(val) {
        return val === void 0 || val === '' || val === null || (_.isObject(val) && _.isEmpty(val));
      },
    },
    mounted() {
      this.initData();
    },
};
</script>

<style lang="less" scoped>
.field-edit {
    position: relative;
}
.field-hoveredit {
    position: absolute;
    top: 2px;
    right: -14px;
    width: 18px;
    .star-ico-edit.el-icon-edit-outline {
        padding: 2px;
        height: 18px;
        font-size: 18px;
        font-weight: 200;
        color: #7b7e86;
        border-radius: 3px;
        background: var(--color-special01);
        cursor: pointer;
        &:hover {
            color: var(--color-primary06);
        }
    }
}

</style>
