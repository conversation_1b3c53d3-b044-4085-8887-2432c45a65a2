<template>
  <div class="field-edit-singleedit" @click="handleClick">
    <div class="edit-innder" :style="innerStyle" @click.stop>
      <div class="view-wrap">
        <div class="field-action-nfield">
          <div class="view-box">
            <div class="view-box-name" :class="{'required': isRequired}">
              <fx-tooltip placement="top">
                <div slot="content">{{ dLabel }}{{ isRequired }}</div>
                <span class="view-box-name-tit">{{dLabel}}{{ isRequired }}</span>
              </fx-tooltip>
            </div>
            <div class="view-box-content">
              <el-date-picker
                style="width: 224px"
                ref="datepick"
                clearable
                v-model="values"
                type="datetimerange"
                value-format="timestamp"
                :size="dSize"
                :disabled="disabled"
                :format="sFormat"
                :useLocaleFormat="true"
                :start-placeholder="$t('开始日期')"
                :end-placeholder="$t('结束日期')"
                @change="changeHandle"
                @blur="blurHandle"
                :picker-options="pickerOptions"
                :appointment_time_period="appointmentTimePeriod"
                :select_date_range="selectDateRange"
              ></el-date-picker>
              <span v-if="isRequired && showRequired" class="fm-error crm-ico-error">{{ $t('请填写') + dLabel }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="tit-wrap">
          <span
              class="tit-left fx-icon-close"
              @click="handleClose"
          ></span>
          <fx-tooltip placement="top">
              <div slot="content">{{ $t("保存") }}</div>
              <span
                  class="tit-right fx-icon-ok-2 main"
                  @click="handleSave"
              ></span>
          </fx-tooltip>
      </div>
    </div>
  </div>
</template>
<script>
import ElDatePicker from "../date-picker/index";
const util = FS.crmUtil;

export default {
  name: "FieldDateEdit",
  components: {
    ElDatePicker,
  },
  props: {
    dLabel: {
      type: String,
      default: "",
    },
    field: {
      type: Object,
      default: () => ({}),
    },
    values: {
      type: Array,
      default: [],
    },
    dSize: {
      type: String,
      default: "small",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    sFormat: {
      type: String,
      default: "yyyy-MM-dd HH:mm:ss",
    },
    pickerOptions: {
      type: Object,
      default: () => ({}),
    },
    appointmentTimePeriod: {
      type: Object,
      default: () => ({}),
    },
    selectDateRange: {
      type: Object,
      default: () => ({}),
    },
    isRequired: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      innerStyle: {},
      showRequired: false,
      dateValues: [],
    };
  },
  methods: {
    getPosition() {
      const { top, left, height } = this.$parent.$el.getBoundingClientRect();
      const pLeft = left - 104;
      let $top = 8;
      if(height < 32) {
				$top = (32 - height) / 2 + 8;
      }
      this.innerStyle = {
        ...this.innerStyle,
        left: `${pLeft}px`,
        top: `${top - $top}px`,
      };
    },
    handleSave() {
      if (this.isRequired && (!this.dateValues || this.dateValues.length <= 0)) {
        this.showRequired = true;
        return;
      }
      this.$emit("save", this.dateValues);
    },
    handleClose() {
      this.$emit("close");
    },
    handleClick() {
      this.handleClose();
    },
    changeHandle(value) {
      const me = this;
      let zone = this.field.not_use_multitime_zone;
      let val = value || [];
      if (!me.dataIsNull(val) && zone) {
          val = [
              FS.util.convertTimestampToTZ8(val[0]),
              FS.util.convertTimestampToTZ8(val[1]),
          ]; //从个人时区转到东八
      }
      this.dateValues = val;
      this.showRequired = false;
    },
    blurHandle(value) {
      this.$emit("blurHandle", value);
    },
    initData() {
      this.dateValues = this.values;
    },
    dataIsNull(val) {
      return val === void 0 || val === '' || val === null || (_.isObject(val) && _.isEmpty(val));
    },
  },
  mounted() {
    this.initData();
    this.getPosition();
  },
  beforeDestroy() {
  },
};
</script>
<style lang="less" scoped>
.field-edit-singleedit {
    z-index: 505;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    .edit-innder {
        display: flex;
        align-items: baseline;
        justify-content: space-between;
        width: 400px;
        position: absolute;
        border-radius: 3px;
        box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.15);
        background-color: var(--color-neutrals01);
        .view-wrap {
            display: flex;
            flex: 1;
            .field-action-nfield {
                padding: 8px 0 8px 8px;
                max-height: 250px;
                display: flex;
            }
            .view-box {
                position: relative;
                display: flex;
                align-items: baseline;
                max-width: 1300px;
                min-width: 200px;
                .view-box-name {
                    display: flex;
                    align-items: baseline;
                    width: 88px;
                    min-width: 88px;
                    line-height: 1.2em;
                    margin: 0 8px 0 0;
                    &::before {
                        content: "*";
                        font-size: 14px;
                        margin: 0 4px 0 0;
                        transform: translateY(3px);
                        visibility: hidden;
                    }
                    &.required {
                      &::before {
                        visibility: visible;
                        color: red;
                      }
                    }
                    .view-box-name-tit {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                }
                .view-box-content {
                    width: 100%;
                    display: flex;
                    align-items: baseline;
                    flex-direction: column;
                    .crm-ico-error {
                      padding-left: 0px;
                      color: rgb(245, 113, 95);
                      display: block;
                    }
                }
            }
        }
        .tit-wrap {
            display: flex;
            align-items: center;
            width: 72px;
            span {
                height: 24px;
                line-height: 24px;
                width: 24px;
                text-align: center;
                margin-left: 8px;
                border-radius: 4px;
                cursor: pointer;
                background-color: var(--color-neutrals07);
                color: var(--color-neutrals01);
                &::before {
                    color: var(--color-neutrals01);
                }
            }
            .main {
                background-color: var(--color-primary06);
            }
        }
    }
}
</style>
