/**
 * 手工赠品 - web插件
 * @author: lingj
 * @date: 8/2/23
 */

import Base from "plugin_base";
import PPM from "plugin_public_methods";
export default class ChangePrice extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);

    }

    getHook() {
        return [
            {
                event: "md.render.after",
                functional: this.changePrice_renderAfter.bind(this),
            }, {
                event: "form.dataChange.after",
                functional: this.changePrice_masterChangeAfter.bind(this)
            }, {
                event: "md.edit.after",
                functional: this.changePrice_detailChangeAfter.bind(this)
            }, {
                event: "form.change.end",
                functional: this.changePrice_changeEnd.bind(this)
            }, {
                event: "md.edit.end",
                functional: this.changePrice_changeEnd.bind(this)
            }, {
                event: "md.batchAdd.end",
                functional: this.changePrice_changeRowEnd.bind(this)
            }, {
                event: "md.del.end",
                functional: this.changePrice_changeRowEnd.bind(this)
            }, {
                event: "md.copy.end",
                functional: this.changePrice_changeRowEnd.bind(this)
            }, {
                event: "bom.twiceConfig.after",  //bom二次配置
                functional: this.changePrice_bomPriceChange.bind(this)
            }
        ];
    }
    /**
     * 渲染之前
     */
    async changePrice_renderAfter(plugin, param) {
        this.masterApi = param.masterObjApiName;
        this.detailApi = param.objApiName;
        /**
         * 主对象要监控的修改字段：销售单订单金额 ，整单折扣
         * 从对象要监控的修改字段：销售单价 ，折扣，小计
         * 变更要监控的字段：价目表价格，单位，数量
         */
        const { account_id, order_amount, discount: masterDiscount } = this.getMasterFields(),
            { sales_price, discount: detailDiscount, subtotal, price_book_price, actual_unit, quantity } = this.getMdFields(param.objApiName);

        this.masterWatchFields = [order_amount, masterDiscount];
        this.detailWatchFields = [sales_price, detailDiscount, subtotal];
        this.changeWatchFields = [price_book_price, actual_unit, quantity];

        //收集描述fields，计算接口用
        const describeLayout = param.dataGetter.getDescribeLayout(),

            detailObj = describeLayout.detailObjectList.find(d => d.objectApiName == this.detailApi),
            detailFields = detailObj.objectDescribe.fields;
        this.fields = {
            [this.masterApi]: describeLayout.objectDescribe.fields,
            [this.detailApi]: detailFields
        }
        //是否是手工改价模式
        this.adjustedPriceMode  = this.getConfig("change_price_type")=="direct";
    }

    // 主对象数据修改
    async changePrice_masterChangeAfter(plugin, param) {
        if(!this.adjustedPriceMode){
            return;
        }
        const me = this,
            changeFields = Object.keys(param.changeData || {}),
            targetField = changeFields.find(f => me.masterWatchFields.includes(f)),
            { account_id = "account_id", dynamic_amount } = this.getMasterFields();

        //特殊逻辑：更换客户，清空主对象额外调整
        if (changeFields.includes(account_id)) {
            //更新主对象数据 && 补充后续计算要过滤的字段
            param.dataUpdater.updateMaster({
                [dynamic_amount]: 0
            });
            param.filterFields = param.filterFields || {};
            param.filterFields[this.masterApi] = [...(param.filterFields[this.masterApi] || []), dynamic_amount];
        }
        if (!targetField) {
            return;
        }

        const masterData = param.dataGetter.getMasterData(),
            url = "FHH/EM1HNCRM/API/v1/object/price_policy/service/calculateDynamicMaster",
            args = {
                masterObjectApiName: this.masterApi,
                master: {
                    ...masterData,
                    "modified_old_value": param?.oldData[targetField]
                },
                modifiedField: targetField
            };

        await this.handleDataChange(param, url, args, "master");
    }

    // 从对象数据修改
    async changePrice_detailChangeAfter(plugin, param) {  
        if (!this.adjustedPriceMode||!this.detailWatchFields.includes(param.fieldName)) {
            return;
        }
        const url = "FHH/EM1HNCRM/API/v1/object/price_policy/service/calculateDynamicDetail",
            args = {
                masterObjectApiName: this.masterApi,
                detailObjectApiName: this.detailApi,
                details: this.getDetails(param),
                modifiedField: param.fieldName
            };
        await this.handleDataChange(param, url, args, "detail");
    }

    //获取修改的明细行数据
    getDetails(param) {
        const { fieldName, dataGetter, dataIndex = [], changeData } = param;
        return dataGetter.getDetail(this.detailApi).filter(d => dataIndex.includes(d.rowId)).map(data => {
            return {
                ...data,
                ...changeData[data.rowId],
                "modified_old_value": data[fieldName]
            };
        });
    }

    /**
     * 公共逻辑处理函数
     */
    async handleDataChange(param, url, args, changeFrom) {

        args.requestId = param.dataGetter.getRequestId();

        const api = this.detailApi,
            result = await PPM.ajax(this.request, url, args),
            { master = {}, details = [] } = result;

        param.changeData = param.changeData || {};
        param.filterFields = param.filterFields || {};

        //更新主对象数据 && 补充后续计算要过滤的字段
        param.dataUpdater.updateMaster(master);
        param.filterFields[this.masterApi] = [...(param.filterFields[this.masterApi] || []), ...Object.keys(master)];

        //更新从对象数据 && 补充后续计算要过滤的字段
        const detailChangeSet = new Set();
        details.forEach(item => {
            Object.keys(item).forEach(key => detailChangeSet.add(key));
            param.dataUpdater.updateDetail(api, item.rowId, item)
        });
        if (details.length) {
            param.filterFields[this.detailApi] = [...(param.filterFields[this.detailApi] || []), ...detailChangeSet];
        }

        //收集主从修改字段相关的计算字段
        const masterCalFields = param.dataGetter.getCalculateFieldsByFieldName(Object.keys(master), false, this.masterApi),
            detailCalFields = param.dataGetter.getCalculateFieldsByFieldName(Array.from(detailChangeSet), false, this.detailApi),
            calFields = this.mergeChangeFields(masterCalFields, detailCalFields);

        param.extraFields = calFields;

    }

    //合并要计算的字段
    mergeChangeFields(obj1 = {}, obj2 = {}) {
        const result = {};
        for (let key of new Set([...Object.keys(obj1), ...Object.keys(obj2)])) {
            for (let key of new Set([...Object.keys(obj1), ...Object.keys(obj2)])) {
                result[key] = Array.from(new Set([...(obj1[key] || []), ...(obj2[key] || [])]));
            }
        }
        return result;
    }

    // 数据修改计算结束后,处理修改行&主对象
    async changePrice_changeEnd(plugin, param) {
        if(!this.adjustedPriceMode){
            return;
        }

        const editedField = param.operateType === "masterEdit"
            ? param.collectChange()?.blurField
            : param.fieldName;

        // 编辑的非主从相关手动改价字段
        if (!this.masterWatchFields.includes(editedField) && !this.detailWatchFields.includes(editedField)) {
            const changedRes = this.collectChangeInfo(this.changeWatchFields, param,param.dataIndex);
            await this.commonPriceChangeProcedure(param, changedRes);
        }
    }

    //赠删行计算结束后，只处理主对象
    async changePrice_changeRowEnd(plugin, param){
        if(!this.adjustedPriceMode){
            return;
        }
        await this.commonPriceChangeProcedure(param, {
            changeMaster:true, 
            changedRows:[]
        });
    }

    //bom二次配置,处理修改行&主对象
    async changePrice_bomPriceChange(plugins, obj) {
        //开关判断 
        if(!this.adjustedPriceMode){
            return;
        }
        const editRow=obj?.rootData?.rowId?[obj.rootData.rowId]:[];
        const changedRes = this.collectChangeInfo(this.changeWatchFields, obj.param,editRow);
        await this.commonPriceChangeProcedure(obj.param, changedRes);
    }

    //修改的数据
    collectChangeInfo(watchFields, param,editRows=[]) {
        const { mdUpdate = {}, mdAdd = [], mdDel = [], mdInsert = [] } = param.collectChange(),
            changedRows = [],
            updateKeys = Object.keys(mdUpdate);

        const watchFieldSet = new Set(watchFields);
        for (let rowId of editRows) {
            if (Object.keys(mdUpdate[rowId] || {}).some(field => watchFieldSet.has(field))) {
                changedRows.push(rowId);
            }
        }

        const changeMaster = mdAdd.length || mdDel.length || mdInsert.length || changedRows.length;
        return { changeMaster, changedRows };
    }

    //清额外调整逻辑
    async commonPriceChangeProcedure(param, { changeMaster, changedRows }) {
        const api = this.detailApi;
        const { dynamic_amount: masterDynamicAmount } = this.getMasterFields();
        const { dynamic_amount: detailDynamicAmount } = this.getMdFields(this.detailApi);


        for (let rowId of changedRows) {
            param.dataUpdater.updateDetail(api, rowId, { [detailDynamicAmount]: 0 });
        }

        if (changeMaster) {
            param.dataUpdater.updateMaster({ [masterDynamicAmount]: 0 });
        }

        //收集主从修改字段相关的计算字段
        const masterCalFields = changeMaster ? param.dataGetter.getCalculateFieldsByFieldName([masterDynamicAmount], false, this.masterApi) : {},
            detailCalFields = changedRows.length ? param.dataGetter.getCalculateFieldsByFieldName([detailDynamicAmount], false, this.detailApi) : {},
            calFields = this.mergeChangeFields(masterCalFields, detailCalFields);

        await this.calBatch(param, changedRows, calFields);
    }

    async calBatch(param, changeRows, calFields) {
        const result = await this.runPlugin('change_price.cal.before', {
            param: param,
            calIndex: changeRows
        });
       
        if(result?.calIndex){
            changeRows = result.calIndex; 
        }
        
        const calArgs = {
            changeFields: [],
            filterFields: {},
            extraFields: calFields,
            operateType: 'mdEdit',
            dataIndex: changeRows,
            objApiName: this.detailApi,
        };
        await param.triggerCal(calArgs);
    }
}

//
