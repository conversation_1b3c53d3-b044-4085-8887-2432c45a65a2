/**
 * @desc: 返利政策 插件-Web
 * @author: lingj
 * @date: 6/19/22
 */
import PPM from "plugin_public_methods";
import Base from "plugin_base";
import Rebate from "rebate";
import RebateDetail from "./rebate_detail";
import RebateRepel from "./rebate_repel.vue";

export default class RebatePolicy extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.rebatePolicy = new Rebate(pluginService, pluginParam);
        this.userTrigger = false;
        this.cacheChildren([this.rebatePolicy]);
        this.allowByConfirmStatus = false;
        this.allowedRange = this.getConfig('rebate_product_range_shelves') == "1";
    }
    getHook() {
        return [{
            event: "md.render.before",
            functional: this.rebate_mdRenderBefore.bind(this)
        }, {
            event: "form.render.after",
            functional: this.rebate_formRenderAfter.bind(this)
        }, {
            event: 'form.submit.before',
            functional: this.rebate_submitBefore.bind(this)
        }, {
            event: "form.dataChange.after",
            functional: this.rebate_masterChangeAfter.bind(this)
        }, {
            event: "form.change.end",
            functional: this.rebate_dataChange.bind(this)
        }, {
            event: "md.edit.end",
            functional: this.rebate_dataChange.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.rebate_mdRowChange.bind(this)
        }, {
            event: "md.del.end",
            functional: this.rebate_mdRowChange.bind(this)
        }, {
            event: "salesOrderHistory.calculate.before", //从历史订单添加，计算前
            functional: this.rebate_historyDataCalBefore.bind(this)
        }, {
            event: "md.copy.after",
            functional: this.rebate_mdCopyAfter.bind(this)
        }, {
            event: "dhtCustomerAccount.input.rebate",  //输入自动使用
            functional: this.rebate_autoUseRebate.bind(this)
        }, {
            event: "dhtCustomerAccount.click.rebate",  //点击按钮手动使用
            functional: this.rebate_manuallyUseRebate.bind(this)
        }, {
            event: "dhtCustomerAccount.update.rebateQuantity",  //账户组件初始化前，统计返利信息
            functional: this.rebate_generateRebateInfo.bind(this)
        }, {
            event: "dhtCustomerAccount.inputFocus.rebate",  //账户组件使用返利时，是否允许用返利判断
            functional: this.rebate_allowUseRebate.bind(this)
        }, {
            event: "md.copy.end",
            functional: this.rebate_mdRowChange.bind(this)
        }, {
            event: "salesOrderHistory.calculate.end",
            functional: this.rebate_businessRowChange.bind(this)
        }, {
            event: "bom.twiceConfig.after",
            functional: this.rebate_businessRowChange.bind(this)
        }];
    }

    async rebate_mdRenderBefore(plugin, param, $wrapper) {
        this.masterApi = param.masterObjApiName;
        this.detailApi = param.objApiName;
        const me = this,
            res = this.rebatePolicy.init(param,plugin);

        if (!['add', 'edit'].includes(param.formType)) {
            await this.rebatePolicy.initData(param);
        }

        this.fieldMap = res.fieldMap;
        this.decimalMap = res.decimalMap;

        const { product_id, rebate_coupon_id } = this.fieldMap;

        return {
            __execResult: {
                operateBtns: [this.getOperateBtn()],
                tableOptions: {
                    disabledcfg: {
                        idKey: 'is_giveaway',
                        data: [{
                            'is_giveaway': '2'
                        }],
                        filterOr: true
                    }
                },
                columnRenders: [{
                    product_id: {
                        depend_fields: [rebate_coupon_id],
                        render(cellValue, trData) {
                            if (trData[rebate_coupon_id]) {
                                const icon = {
                                    type: "circle",
                                    title: $t("返利产品"),
                                    label: $t("返")
                                };

                                return ` <span class="crm_promotion_icon ${icon.type}" title="${icon.title}" data-action="show_policy_info" style="display:inline-block;line-height:14px;margin-right:3px;border-color:var(--color-primary06);">
                                            ${icon.label}
                                        </span>${cellValue}`;
                            } else {
                                return cellValue;
                            }
                        }
                    }
                }]
            },

            __mergeDataType: {
                array: "concat"
            }
        };
    }
    //form render
    rebate_formRenderAfter(plugin, param) {
        this.rebatePolicy.setRebateProductStatus(param);
        this.initRebateCondition(param);
        return;
    }

    async initRebateCondition(param){
        await this.getRebateCondition(param);
        this.refreshAvailableFund(param);
    }

    //自定义按钮&按钮事件
    getOperateBtn() {
        const me = this;
        return (trData) => {
            if (trData.is_giveaway == "2") {
                return {
                    retain: []
                };
            }
        }
    }
    //从历史添加数据，计算前处理
    rebate_historyDataCalBefore(plugin, obj) {
        const me = this,
            { rebate_amortize_amount, rebate_dynamic_amount } = this.fieldMap,
            cleanData = {
                [rebate_amortize_amount]: 0,
                [rebate_dynamic_amount]: 0,
                "misc_content": null
            },
            { details, detailObjectApiName, param } = obj;

        details.forEach(l => {
            const item = Object.assign({}, cleanData)
            param.dataUpdater.updateDetail(detailObjectApiName, l.rowId, item);
        })
        return [rebate_amortize_amount, rebate_dynamic_amount];
    }

    //行复制
    rebate_mdCopyAfter(plugin, param) {

        const me = this,
            { rebate_amortize_amount, rebate_dynamic_amount } = this.fieldMap,
            detailsMap = (param.dataGetter.getDetail() || []).reduce((accMap, item) => {
                accMap[item.rowId] = item;
                return accMap;
            }, {}),
            copyRowIds = param.copyRowIds;

        (param.newDataIndexs || []).forEach((key, idx) => {
            const oriItem = detailsMap[copyRowIds[idx]],
                cleanData = {
                    [rebate_amortize_amount]: oriItem[rebate_amortize_amount] == undefined ? oriItem[rebate_amortize_amount] : 0,
                    [rebate_dynamic_amount]: oriItem[rebate_dynamic_amount] == undefined ? oriItem[rebate_dynamic_amount] : 0,
                    "misc_content": null
                };
            const item = Object.assign({}, cleanData);
            param.dataUpdater.updateDetail(me.detailApi, key, item);
        })
    }

    //校验是否允许使用返利，返回给客户账户组件
    async rebate_allowUseRebate(plugin, obj) {
        const { param } = obj,
            masterData = param.dataGetter.getMasterData();

        const repelCoupon = this.getUsedRepelCoupon(masterData);

        if (repelCoupon) {
            this.allowByConfirmStatus = true;
            return {
                allowUse: await this.confirmUseRebate(repelCoupon),
                allowByConfirm: true
            };
        } else {
            this.allowByConfirmStatus = false;
            return {
                allowUse: true,
                allowByConfirm: false
            };
        }
    }

    //获取使用了的互斥的优惠券
    getUsedRepelCoupon(masterData) {
        const { coupon = [] } = masterData?.misc_content || {},
            repelCoupon = coupon.find(c => c.repel_rebate);
        return repelCoupon;
    }

    //弹框确认是否强制使用返利
    async confirmUseRebate(data) {
        return new Promise((resolve) => {
            const RebateRepelComp = Vue.extend(RebateRepel);
            const rebateRepelInstance = new RebateRepelComp({
                propsData: {
                    data: data,
                }
            });
            // 挂载组件
            rebateRepelInstance.$mount();
            rebateRepelInstance.$on('useRebate', function () {
                resolve(true);
            });
            //从领券页面直接用券
            rebateRepelInstance.$on('cancelUseRebate', function () {
                resolve(false);
            });
        })
    }
    //客户账户组件输入值，自动使用返利
    async rebate_autoUseRebate(plugin, obj) {

        const me = this,
            { fundAccountId, accountType, amount, param, fundAccountField } = obj,
            fundArgs = {
                fundAccountId: fundAccountId,
                accountType: accountType,
                amount: amount || 0
            };

        const result = await this.rebatePolicy.autoUseRebate(param, fundArgs, this.allowByConfirmStatus),
            useAmount = result?.masterData[fundAccountField] || 0;

        if (!this.areValuesEqual(useAmount, amount)) {
            CRM.util.remind(
                3,
                $t("返利账户的使用额超过限制，将自动调整为目前最大可用额")
            );
        }
        this.afterMatchRebate(param, result);
        this.allowByConfirmStatus = false;
        const rebateInfo = this.getProductRebateInfo(param);
        return rebateInfo;
    }

    areValuesEqual(A, B) {
        const numberA = A === null ? 0 : Number(A);
        const numberB = B === null ? 0 : Number(B);

        if (isNaN(numberA) || isNaN(numberB)) {
            return false;
        }
        return numberA === numberB;
    }


    //手动使用返利
    async rebate_manuallyUseRebate(plugin, { fundAccountId, accountType, param, fundAccountField, accountList }) {
        this.showLoading();

        const fundArgs = {
            fundAccountId,
            accountType,
            fundAccountField
        };

        const rebates = await this.rebatePolicy.queryRebateFromFund(param, fundArgs),
            accountMap = this.getFuncAccountMap(accountList);

        this.hideLoading();

        const res = await new Promise((resolve) => {
            this.showRebateDetail(param, rebates, fundArgs, accountMap, resolve);
        });

        return res;
    }


    //账户组件获取货补返利相信信息
    rebate_generateRebateInfo(plugin, obj) {
        const { param ,rebate_accounts=[]} = obj,
            rebateInfo = this.getProductRebateInfo(param);
            
        const moneyFunds=[],productFunds=[];
        rebate_accounts.forEach(a=>{
            if(a.accountType==='1'){
                moneyFunds.push(a.fundAccountId)
            }else{
                productFunds.push(a.fundAccountId)
            }
        })
        this.fundAccountIds={moneyFunds,productFunds };
        this.refreshAvailableFund(param);
        return rebateInfo;
    }
    //账户信息
    getFuncAccountMap(accountArr) {
        let map = new Map();
        for (let item of accountArr) {
            map.set(item.authorize_account_id, item.account_name);
        }
        return map;
    }

    async showRebateDetail(param, rebateInfo, fundArgs, accountMap, done) {
        const me = this,
            masterData = param.dataGetter.getMasterData(),
            repelCoupon = this.getUsedRepelCoupon(masterData),
            relatedList = this.getRelatedList(param);;

        this.rebateDetail = new RebateDetail({
            $wrapper: $('body')[0],
            fieldMap: me.fieldMap,
            decimalMap: me.decimalMap,
            formType: param.formType,
            rebateInfo: rebateInfo,
            fundAccountId: fundArgs.fundAccountId,
            fundAccountMap: accountMap,
            repelCoupon: repelCoupon,
            allowedRange: me.allowedRange,
            masterData:masterData,
            relatedList:relatedList,
            changeRule: function (ruleId, rangeRules, tempMiscContent) {
                return me.rebatePolicy.queryRebateFromRule(param, fundArgs, ruleId, rangeRules, tempMiscContent);
            },

            parsePickGifts: function (gifts, unit) {
                return me.rebatePolicy.parsePickProducts(param, gifts, unit);
            },
            allowUseRebate: async function () {
                const forceUse = await me.confirmUseRebate(repelCoupon);
                return forceUse;
            },
            submit: async function (res, forceUse) {
                me.showLoading();
                const result = await me.rebatePolicy.useRebate(me.userTrigger, res, param, fundArgs, forceUse);
                me.afterMatchRebate(param, result);
                const productRebateInfo = me.getProductRebateInfo(param);
                done(productRebateInfo);
            },
            cancel: function () {
                const productRebateInfo = me.getProductRebateInfo(param);
                done(productRebateInfo);
            }
        });
    }

    //获取货补返利累计信息
    getProductRebateInfo(param, fundId) {
        const masterData = param.dataGetter.getMasterData(),
            { product_rebate = [] } = masterData.misc_content || {};
        const fundRebateMap = {};
        product_rebate.forEach(pr => {
            const { fund_account_id, quantity, amount, product = [] } = pr;
            let item = fundRebateMap[fund_account_id] || { quantity: 0, kindDetails: new Set() };
            item.quantity = CRM.util.accAdd(item.quantity, quantity);
            product.forEach(p => {
                item.kindDetails.add(p.product_id);
            });
            item.kind = item.kindDetails.size;
            fundRebateMap[fund_account_id] = item;
        })
        return fundRebateMap;
    }

    //保存前提交
    async rebate_submitBefore(plugin, param) {
        //用户无编辑，直接保存
        const isRebateEdit = this.isRebateEdit(param);
        const isCouponEdit = await this.isCouponEdit(param);
        if (!isRebateEdit && !isCouponEdit) {
            return;
        }

        this.showLoading();
        const result = await this.rebatePolicy.checkBeforeSubmit(this.userTrigger, param),
            rebateInfo = this.getProductRebateInfo(param),
            detailData = param.dataGetter.getDetail(this.detailApi),
            subtotalLessZeroData = detailData.filter(d => d.subtotalLessZero == '1');

        await this.runPlugin('crmRebate.match.change', {
            masterData: param.dataGetter.getMasterData(),
            detailData: detailData,
            rebateInfo: rebateInfo,
            param: param
        });

        this.afterMatchRebate(param, result, true);

        if (result.rebateChange || result.productRebateChange) {
            CRM.util.alert($t("vcrm.rebateChangedTip"));
            param.end();
            plugin.skipPlugin();
        } else if (result.couponChange) {
            CRM.util.alert($t("vcrm.couponChangedTip"));
            param.end();
            plugin.skipPlugin()
        } else if (subtotalLessZeroData.length) {
            let str = $t("以下产品行小计为负，请确认，如不合理可手工调整：");
            subtotalLessZeroData.forEach((d, idx) => {
                str += `${idx == 0 ? '' : '、'}【${d.product_id__r}】`
            });
            CRM.util.alert(str);
            param.end();
            plugin.skipPlugin();
        }
    }

    isRebateEdit(param){
        return this.userTrigger && this.isUseRebate(param);
    }

    async isCouponEdit(param){
        const isCouponEdit = await this.runPlugin('coupon.query.isEdit', {
            param
        });
        return isCouponEdit;
    }

    isUseRebate(param){
        const masterData = param.dataGetter.getMasterData();
        const { misc_content } = masterData;
        const { rebate = [] ,rangeRebate = [],product_rebate = []} = misc_content || {};
   
       
        return rebate.length > 0 || rangeRebate.length > 0 || product_rebate.length > 0 
    }

    rebate_masterChangeAfter(plugin, param){
        if (!this.fieldMap) {
            return;
        }
        const changeDataKeys = Object.keys(param.changeData || {}),
            { account_id, rebate_amount, misc_content } = this.fieldMap; 
        if (changeDataKeys.includes(account_id)) {
            const updateData = {
                rebate_rule_id: "",
                product_rebate_rule_id: "",
                range_rebate_rule_ids: [],
                [rebate_amount]: 0,
                [misc_content]: {
                    coupon: [],
                    rebate: [],
                    rangeRebate: [],
                    product_rebate: [],
                }
            }
            param.dataUpdater.updateMaster(updateData);
            this.getRebateCondition(param);
        }
    }

    rebate_dataChange(plugin, param) {
        this.userAction(param);
    }

    rebate_mdRowChange(plugin, param) {
        this.userAction(param);
    }

    rebate_businessRowChange(plugin,obj){
        if(obj.param){
            this.userAction(obj.param);
        } 
    }

    afterMatchRebate(param, result) {
        this.userTrigger = false;
        this.serverCheck = true;  //!result.rebateChange && !result.productRebateChange && !(result.couponChange ?? false);
        this.hideLoading();
    }

    //用户有增删改动作
    userAction(param) {
        // 用户此次修改是否触发
        const isActionTrigger = this.isTriggerCondition(param);
        //用户历史操作是否触发
        this.userTrigger = this.userTrigger || isActionTrigger;
        if (isActionTrigger) {
            this.refreshAvailableFund(param);
        }
    }

    //获取返利条件字段
    async getRebateCondition(param) {
        const result = await this.rebatePolicy.queryRebateCond(param);
    
        const masterCondSet = new Set(result[this.masterApi]||[]),
            detailCondSet = new Set(result[this.detailApi] || []);
            
        this.rebateCondition = {masterCondSet,detailCondSet};
    }

    isTriggerCondition(param) {
        const { mdAdd, mdDel, mdInsert, masterUpdate, mdUpdate } = param.collectChange();
        if (mdAdd?.length || mdDel?.length || mdInsert?.length) {
            return true;
        }
        const {masterCondSet,detailCondSet} =this.rebateCondition||{};
        if (Object.keys(masterUpdate).some(key => masterCondSet?.has(key))) {
            return true;
        }
        return Object.values(mdUpdate).some(item => Object.keys(item).some(key => detailCondSet?.has(key)));
    }

    async refreshAvailableFund(param) {
        const moneyFundIds = this.fundAccountIds?.moneyFunds || [];
        if(moneyFundIds.length==0){
            return;
        }

        const result = await this.rebatePolicy.queryAvailableFund(param, this.fundAccountIds);
        console.log(result);
        this.runPlugin('crmRebate.canUseAmount.change', {
            param,
            allowAmount:result
        });
    }

    getRelatedList(param) {
        if (!this.relatedList) {
            const fieldAttr = param.dataGetter.getFieldAttr("product_id", this.detailApi);
            this.relatedList = fieldAttr.target_related_list_name
        }
        return this.relatedList;
    }

    destroy() {
        this.rebatePolicy = this.rebateDetail = null;
    }

}