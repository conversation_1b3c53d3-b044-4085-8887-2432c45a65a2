/**
 * @desc: 价格政策 插件-Web
 * @author: lingj
 * @date: 6/16/22
 */
import PPM from "plugin_public_methods";
import Base from "plugin_base";
import price_policy from "price_policy";
import PricePolicyDetail from "./price_policy_detail";
export default class PricePolicy extends Base {

    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.PricePolicy = new price_policy(pluginService, pluginParam);
        this.basisPrice = this.getConfig('giftAmortizeBasis') == "product_price"; //赠品分摊价格是否依据产品“标准价格”
        this.cacheChildren([this.PricePolicy]);
        this.promoCalcTriggered = false;
        this.formInit = false;
        this.matchingPolicy = false;
    }
    getHook() {
        return [{
            event: "md.render.before",
            functional: this.policy_mdRenderBefore.bind(this)
        }, {
            event: "form.render.after",
            functional: this.policy_formRenderAfter.bind(this)
        }, {
            event: "form.change.end",
            functional: this.policy_masterDataChange.bind(this)
        }, {
            event: "md.batchAdd.end",
            functional: this.policy_mdDataChange.bind(this)
        }, {
            event: "md.del.end",
            functional: this.policy_mdDataChange.bind(this)
        }, {
            event: "md.edit.end",
            functional: this.policy_mdDataChange.bind(this)
        }, {
            event: "md.copy.after",
            functional: this.policy_mdCopyAfter.bind(this)
        }, {
            event: "md.copy.end",
            functional: this.policy_mdDataChange.bind(this)
        }, {
            event: "bom.twiceConfig.after", //bom二次配置
            functional: this.policy_businessPriceChange.bind(this)
        }, {
            event: "bom.priceChange", //bom 更新配置
            functional: this.policy_businessPriceChange.bind(this)
        }, {
            event: "salesOrderHistory.calculate.before", //从历史订单添加，计算前
            functional: this.policy_historyDataCalBefore.bind(this)
        }, {
            event: "salesOrderHistory.calculate.end", //从历史订单添加，计算结束后
            functional: this.policy_businessPriceChange.bind(this)
        }, {
            event: "form.uievent.after", //按钮ui事件
            functional: this.policy_uiEventAfter.bind(this)
        }, {
            event: "md.tile.before", //平铺事件
            functional: this.policy_mdTileBefore.bind(this)
        }, {
            event: "md.excelpaste.before",
            functional: this.policy_excelPasteBefore.bind(this)
        }, {
            event: 'form.submit.before',
            functional: this.policy_submitBefore.bind(this)
        }, {
            event: 'form.model.destroy.before',
            functional: this.policy_fromDestroyBefore.bind(this)
        }, {
            event: 'bom.filterCalculateFields.before',
            functional: this.policy_bomFilterCalFields.bind(this)
        }, {
            event: 'pricePolicy.showPolicyDetail',
            functional: this.policy_showPolicyDetail.bind(this)
        }, {
            event: 'period-product.mdRenderAfter.before',
            functional: this.policy_periodProductInitBefore.bind(this)
        }
        ];
    }

    /********************************************************/
    /*********************render前dom操作*********************/
    /********************************************************/

    async policy_mdRenderBefore(plugin, param, $wrapper) {
        this.masterApi = param.masterObjApiName;
        this.detailApi = param.objApiName;
        const renderConfig = await this.runPlugin('pricePolicy.render.before', {
            param: param
        });

        this.setupPolicySConfig(renderConfig);

        this.showPolicyLoading($t("政策计算中"), null, "policy");
        const policyInitStartTime = new Date();
        const me = this,
            result = await this.PricePolicy.mdRenderBefore(param, {
                manualMatchMode: this.manualMatchMode,
                allowDrag: this.allowDrag,
                allowedRange: this.allowedRange,
                grayNewCalGiftFun: this.grayNewCalGiftFun
            },plugin);


        this.decimalMap = result.decimalMap;
        this.productNameConfig = result.productNameConfig;
        this.fieldMap = result.fieldMap;
        this.mdDefRecordType = result.mdDefRecordType || "default__c";
        this.updateAfterPolicy(result, param);
        const {
            product_id,
            price_policy_id,
            quantity
        } = this.fieldMap;

        const disableConf = this.getConfig('manual_gift') == '1' ? {
            idKey: 'gift_type',
            data: [{
                'gift_type': "system"
            }],
            filterOr: true
        } : {
            idKey: 'is_giveaway',
            data: [{
                'is_giveaway': "1"
            }],
            filterOr: true
        }

        //
        const now = new Date();

        const policyInitDiffInSeconds = (now - policyInitStartTime) / 1000
        console.log(`价格政策初始化耗时: ${policyInitDiffInSeconds}`);

        const orderInitStartTime = localStorage.getItem("myOrderInitStart");
        const orderInitDiffInSeconds = (now - new Date(orderInitStartTime)) / 1000;
        console.log(`订单初始化耗时: ${orderInitDiffInSeconds}`);
        //
        return {
            __execResult: {
                operateBtns: [this.getOperateBtn()],
                headerSlot: renderConfig?.disableMasterPolicy ? [] : [this.initPolicyDom.bind(this, plugin, param)],
                tableOptions: {
                    isDrag: this.allowDrag,
                    disabledcfg: disableConf,
                },
                columnRenders: [{
                    product_id: {
                        depend_fields: [price_policy_id, "icon_fake_val"],
                        render(cellValue, trData) {
                            if (trData.price_policy_id) {
                                const isGift = trData.parent_gift_key,
                                    icon = {
                                        type: "circle",
                                        title: trData.parent_gift_key ? $t("促销赠品") : $t("促销产品"),
                                        label: trData.parent_gift_key ? $t("赠") : $t("促")
                                    },
                                    overLimitKeys = me.policyInfo && me.policyInfo.limit && me.policyInfo.limit.overLimitKey || null,
                                    isOverLimit = overLimitKeys && overLimitKeys.includes(trData.prod_pkg_key),
                                    overIcon = isOverLimit ? 'el-icon-warning' : '',
                                    overInfo = $t("所选产品超出数量或价格限制，无法使用该政策"),
                                    iconStyle = isGift ? "" : "background:var(--color-primary06);color:#fff;";

                                return ` <span class="crm_policy_limit_icon ${overIcon}" title="${overInfo}" style="color:#FFD22A;font-size:16px;"></span>
                                        <span class="crm_promotion_icon ${icon.type}" title="${icon.title}" style="display:inline-block;line-height:14px;margin-right:3px;border-color:var(--color-primary06); ${iconStyle}" >
                                            ${icon.label}
                                        </span>${cellValue}`;
                            } else {
                                return cellValue;
                            }
                        }
                    }
                }]
            },
            __mergeDataType: {
                array: "concat"
            }
        };
    }

    setupPolicySConfig(renderConfig) {
        //是否手动匹配模式
        this.manualMatchMode = renderConfig?.disableManualMatch ? false : (this.getConfig('match_mode') == "once"); //一次匹配：即通过按钮手动匹配
        
        //是否允许拖拽,开关未在pluginList接口下发，临时兼容
        const allowDragConfig = (this.getConfig('price_policy_change_order')||CRM.util.getConfigStatusByKey('price_policy_change_order')) == "1";
        this.allowDrag = allowDragConfig || CRM.util.isGrayScale('CRM_POLICY_ALLOW_DRAG') || false;
        //是否允许赠品范围
        this.allowedRange = this.getConfig('enable_gift_range_shelves') == "1";
        //周期性产品
        const periodicConfigStr = this.getConfig("periodic_product_plugin");
        const periodicConfig = JSON.parse(periodicConfigStr || "[]");
        this.isPeriod = periodicConfig.includes(this.masterApi);

        //是否灰度新计算赠品
        this.grayNewCalGiftFun = CRM.util.isGrayScale('CRM_GIFT_CAL_BY_NEW_FUN') || false;
    }

    //平铺事件，屏蔽取消和关闭按钮
    policy_mdTileBefore(plugin, param) {
        return {
            __execResult: {
                hideClose: true,
            },
            __mergeDataType: {
                array: "concat"
            }
        }

    }
    //自定义按钮&按钮事件
    getOperateBtn() {
        return (trData) => {
            if (trData.is_giveaway == "1" && trData.parent_gift_key) {
                return {
                    retain: []
                };
            } else {
                return {
                    add: [{
                        action: "showPolicyDetail",
                        label: $t("促销"),
                        callBack: this.showPolicyDetail_d.bind(this)
                    }],
                    del: ["insertHandle"]
                };
            }
        }
    }
    //初始化显示主对象政策信息的dom
    initPolicyDom(plugin, param, $wrapper) {
        const togglePolicyBtn = CRM.util.isGrayScale('CRM_SHOW_POLICY_BTN_BY_DATA');
        const me = this,
            btnShowStyle = togglePolicyBtn ? "none" : "inline-block",
            modeBtnStyle = this.manualMatchMode ? "inline-block" : "none",
            dom = `<div class="policy-btn-wrapper" style="display:flex;align-items: center;gap: 8px;width:100%;">
                    <div class="master-policy-content" style="flex:1;height:28px;line-height:28px;border:1px solid #eee;padding:4px;cursor:pointer;clear:both;">
                        <label class="master-policy-name">
                            ${$t("未选择价格政策")}
                        </label>
                        <span class="j-master-policy crm-btn-primary" style="display:${btnShowStyle};float:right;padding:0 8px;border-radius:4px">
                            ${$t("crm.price_policy.policy_btn")}
                        </span>
                    </div>
                    <span class="j-match-policy-mode crm-btn-primary" style="display:${modeBtnStyle};padding:0 8px;line-height:28px;border-radius:4px;cursor:pointer;">${$t("sfa.crm.price_policy.manual_policy_btn")}</span>
                    </div>`,
            masterData = param.dataGetter.getMasterData();
        $($wrapper).append(dom);
        this.updateMasterPolicyInfo(masterData);

        if (togglePolicyBtn) {
            this.updateMasterPolicyBtn(masterData);
        }

        $('body').delegate('.j-master-policy', 'click', (event) => {
            event.stopPropagation && event.stopPropagation();
            me.showPolicyDetail_m();
        });
        $('body').delegate('.j-match-policy-mode', 'click', (event) => {
            event.stopPropagation && event.stopPropagation();
            me.manualUsePolicy();
        });
        return {
            destroy() {
                me.destroy();
            }
        }
    }

    //周期产品初始化前,过滤价格政策赠品不被周期性插件重置
    policy_periodProductInitBefore(plugin, { data, param }) {
        return {
            data: (data || []).filter(d => !d.parent_gift_key)
        }
    }

    /********************************************************/
    /***********************页面初始化结束**********************/
    /********************************************************/
    //form render后，做数据初始化
    async policy_formRenderAfter(plugin, param) {
        this.showCustomerPolicyLoading($t("政策计算中"), null, "policy");
        try {
            const result = await this.PricePolicy.formRenderAfter(param);
            if(!param.__errorStatus){
                this.hideCustomerPolicyLoading();
            } 
            this.updateAfterPolicy(result, param);
        } catch (err) {
            this.handlePolicyError(err);
        }
        this.formInit = true;
    }

    /********************************************************/
    /********************用户操作触发价格政策********************/
    /********************************************************/

    //主对象字段变化 
    async policy_masterDataChange(plugin, param) {
        if (this.matchingPolicy) {
            console.log("价格政策匹配中，再次被触发");
            console.log(param.collectChange());
            return;
        }
        if (!this.formInit) {
            return;
        }
        if (this.manualMatchMode) {
            const result = this.PricePolicy.checkPromoCalcTriggered(param);
            this.updatePromoStatus(result);
            //更新一下整单政策显示文案
            const masterData = param.dataGetter.getMasterData();
            this.updateMasterPolicyInfo(masterData, param);
        } else {
            this.showPolicyLoading($t("政策计算中"), null, "policy");
            try {
                const result = await this.PricePolicy.masterDataChange(param);
                this.updateAfterPolicy(result, param);
            } catch (err) {
                this.handlePolicyError(err);
            }
        }
    }

    policy_mdCopyAfter(plugin, param) {
        const me = this,
            {
                amortize_amount,
                rowId,
                data_index,
                prod_pkg_key,
                price_policy_id,
                price_policy_id__r,
                price_policy_rule_ids,
                policy_dynamic_amount,
                parent_gift_key
            } = this.fieldMap,
            cleanData = {
                [price_policy_id]: "",
                [price_policy_id__r]: "",
                [price_policy_rule_ids]: [],
                [amortize_amount]: 0,
                gift_map: null
            },
            details = param.dataGetter.getDetail();

        (param.newDataIndexs || []).forEach(key => {
            let data = details.find(d => d.rowId == key);

            const item = Object.assign({}, cleanData, {
                [prod_pkg_key]: key,
                [data_index]: key
            });
            if (data.gift_type !== "manual") {
                item[policy_dynamic_amount] = 0;
            }
            param.dataUpdater.updateDetail(me.detailApi, key, item);
        })
    }
    //从历史添加数据，计算前处理
    policy_historyDataCalBefore(plugin, obj) {
        const me = this,
            {
                amortize_amount,
                rowId,
                data_index,
                prod_pkg_key,
                price_policy_id,
                price_policy_id__r,
                price_policy_rule_ids,
                policy_dynamic_amount,
                parent_gift_key,
                gift_amortize_price
            } = this.fieldMap,
            cleanData = {
                [price_policy_id]: "",
                [price_policy_id__r]: "",
                [price_policy_rule_ids]: [],
                [policy_dynamic_amount]: 0,
                [amortize_amount]: 0,
                [parent_gift_key]: "",
                [gift_amortize_price]: 0,
                gift_map: null
            },
            {
                details,
                detailObjectApiName,
                param
            } = obj;

        details.forEach(l => {
            const curRowId = l[rowId],
                item = Object.assign({}, cleanData, {
                    [prod_pkg_key]: curRowId,
                    [data_index]: curRowId
                })
            param.dataUpdater.updateDetail(detailObjectApiName, l[rowId], item);
        })
        return [amortize_amount, policy_dynamic_amount, gift_amortize_price];
    }

    //从对象数据增删改触发价格政策
    async policy_mdDataChange(plugin, param) {
        await this.mdDataChangeHandle(param);
    }

    //通过其他业务事件触发的价格政策，如从历史数据添加、bom二次配置
    async policy_businessPriceChange(plugin, obj) {
        await this.mdDataChangeHandle(obj.param);
    }

    async mdDataChangeHandle(param) {
        if (this.manualMatchMode) {
            const result = this.PricePolicy.checkPromoCalcTriggered(param);
            this.updatePromoStatus(result);
        } else {
            this.showPolicyLoading($t("政策计算中"), null, "policy");
            try {
                const result = await this.PricePolicy.mdDataChange(param);
                this.updateAfterPolicy(result, param);
            } catch (err) {
                this.handlePolicyError(err);
            }
        }
    }

    //点击按钮【计算促销】，手动使用政策
    async manualUsePolicy(oriParam) {
        const param = oriParam || this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'price_policy'
        });

        this.showPolicyLoading($t("政策计算中"), null, "policy");
        try {
            const result = await this.PricePolicy.manualMatchPolicy(param);
            if (!result?.value && result?.msg) {
                CRM.util.remind(3, result.msg);
            }
            this.updateAfterPolicy(result, param);
            param.end();
            return result;
        } catch (err) {
            this.handlePolicyError(err);
        }
    }

    async policy_uiEventAfter(plugin, param) {
        const res = await this.runPlugin('pricePolicy.match.before', {
            param: param
        });
        if (res && res.triggerMatch) {
            this.showPolicyLoading($t('UI事件执行中') + '...', null, "policy");
            const result = await this.PricePolicy.uiEventAfter(param);
            this.hidePolicyLoading();
            await this.policy_mdDataChange(plugin, param);
        }
    }

    //单元格复制粘贴
    policy_excelPasteBefore(plugin, param) {
        const {
            startPasteDataIndex
        } = param;
        const details = param.dataGetter.getDetail();
        const item = details.find(d => d.rowId == startPasteDataIndex);
        if (item.parent_gift_key && item.is_giveaway === '1') {
            CRM.util.remind(3, $t('价格政策赠品不支持粘贴'));
            return plugin.skipPlugin();
        }
    }

    //
    async policy_submitBefore(plugin, param) {
        //非手动匹配模式 或 用非（使用价格政策且用户又编辑），直接保存
        if (!this.manualMatchMode || !this.promoCalcTriggered) {
            return;
        }
        const result = await this.manualUsePolicy(param);
        if (!result?.value && result?.noUpdatePolicy) {
            return;
        }
        CRM.util.alert($t("crm.price_policy.submitTip"));
        plugin.skipPlugin()
    }

    /********************************************************/
    /*********************** 更新数据 ***********************/
    /********************************************************/
    //通过底层方法更新ui事件返回结果 => collectChange可以收集到变化
    updateUiRes(param) {
        const api = this.detailApi,
            {
                rowId
            } = this.fieldMap,
            {
                uiEventResult,
                masterObjApiName
            } = param,
            details = param.dataGetter.getDetail(api);
        if (!uiEventResult) {
            return;
        }
        const masterRes = uiEventResult[masterObjApiName] || {},
            detailRes = uiEventResult[this.detailApi],
            detailRes_a = detailRes && detailRes.a || [],
            detailRes_u = detailRes && detailRes.u || {};
        //更新主对象数据
        param.dataUpdater.updateMaster(masterRes);
        //更新从对象数据-改&删
        details.forEach((d, idx) => {
            if (detailRes_u[idx]) {
                param.dataUpdater.updateDetail(api, d[rowId], detailRes_u[idx]);
            } else {
                param.dataUpdater.del(api, d[rowId]);
            }
        })
        //更新从对象数据-增
        if (detailRes_a.length >= 1) {
            param.dataUpdater.add(detailRes_a);
        }
    }

    //手动匹配模式,记录用户操作是否需要触发价格政策
    updatePromoStatus(result) {
        this.promoCalcTriggered = this.promoCalcTriggered || result.triggerPolicy;
    }

    updateAfterPolicy(result, param) {
        if(param.__errorStatus){
            CRM.util.remind(3, $t("sfa.crm.pricepolicy.refresh_tip"));
        }else{
            this.handleSuccessPolicyUpdate(result, param);
        }  
        this.sendPolicyLog(result, param);
    }

    handleSuccessPolicyUpdate(result, param){
        const masterData = param.dataGetter.getMasterData(),
            togglePolicyBtn = CRM.util.isGrayScale('CRM_SHOW_POLICY_BTN_BY_DATA');
        if (result) {
           
            const {
                policyInfo,
                value,
                promoteProcessLog
            } = result;
            this.parsePolicyRuleInfo(policyInfo);
            this.updateMasterPolicyInfo(masterData, param);

            if (togglePolicyBtn) {
                this.updateMasterPolicyBtn(masterData);
                this.updateDetailPolicyBtn(param);
            }

            const noPolicy = policyInfo && !policyInfo.hasPricePolicy;
            const executeMatchDetail = promoteProcessLog?.details?.some(d => d.action == 'matchDetail');
            if (noPolicy || executeMatchDetail) {
                this.promoCalcTriggered = false;
            }

            this.runPlugin('price_policy.match.end', {
                param: param
            });
        } else {
            if (togglePolicyBtn) {
                this.updateMasterPolicyBtn(masterData);
            }
        }
        this.hidePolicyLoading();
    }
    sendPolicyLog(result, param) {
        //异步日志校验，不阻塞
        const checkFlag = this.PricePolicy.checkAndLogPolicyResult(result, {
            masterData:param.dataGetter.getMasterData(),
            detailData: param.dataGetter.getDetail(this.detailApi)
        });

        const startTimeStr = result?.promoteProcessLog?.startTime;
        let eventData = {};
        let diffInSeconds = 0;
        if (startTimeStr) {
            const startTime = new Date(startTimeStr);
            const now = new Date();
            diffInSeconds = (now - startTime) / 1000;
            result.promoteProcessLog.durationMs = diffInSeconds;
            eventData = result.promoteProcessLog;
        } else {
            eventData = {
                changeData: param.collectChange(),
                conditions: this.policyInfo.policyCondition
            }
        }
        this.sendLog({
            eventId: 'fs-crm-sfa-plugin-pricepolicy-log',
            eventType: 'PROD',
            eventName: 'pv',
            apiName: 'price_policy',
            eventData: eventData,
            num1: diffInSeconds
        })
        console.log(`价格政策耗时: ${diffInSeconds}`)
        console.log(eventData);
    }

    handlePolicyError(err) {
        CRM.util.remind(3, $t("sfa.crm.pricepolicy.refresh_tip"));
        this.sendLog({
            eventId: 'fs-crm-sfa-plugin-pricepolicy-log',
            eventType: 'PROD',
            eventName: 'pv',
            apiName: 'price_policy',
            eventData: err?.message || '价格政策执行失败', //[ignore-i18n]
        })
    }

    //格式化政策信息
    parsePolicyRuleInfo(policyInfo = {}) {
        policyInfo?.pricePolicies?.forEach(policy => {
            policy?.rules?.forEach(rule => {
                if (typeof rule.executeResult === 'string') {

                    rule.executeResult = JSON.parse(rule.executeResult) || {};
                    rule.type = rule.executeResult.type;

                    if (rule.executeResult.gift_condition && typeof rule.executeResult.gift_condition === 'string') {
                        let giftCondition = CRM.util.parseCondition(rule.executeResult.gift_condition),
                            unitText = rule.executeResult.gift_condition_unit__s;
                        if (giftCondition && giftCondition !== "") {
                            rule.executeResult.conditionGift = `${$t("按范围赠送")}${unitText}: ${giftCondition} ${$t("产品")}`;
                        }
                    }
                }
            });
        });
        this.policyInfo = {
            ...(this.policyInfo || {}),
            ...policyInfo
        }
    }

    updateMasterPolicyInfo(masterData, param) {
        let policyName = $t("未选择价格政策"),
            limitPolicy = this.policyInfo && this.policyInfo.limit && this.policyInfo.limit.overLimitKey || null,
            limitInfo = "";
        if (masterData.price_policy_id) {
            let policy = (this.policyInfo && this.policyInfo.pricePolicies || []).find(p => p.id == masterData.price_policy_id);
            policyName = policy && policy.name || policyName;

            //更新主对象政策名称到数据上，否则草稿箱取出时没有__r导致政策id被清空
            param && param.dataUpdater.updateMaster({
                price_policy_id__r: policyName
            });

        }
        if (limitPolicy && limitPolicy.includes('master')) {
            limitInfo = `<label style="color:#545861"><span class="el-icon-warning" style="color:#FFD22A;font-size:16px;"></span>${$t("所选的部分产品超出数量或价格限制，无法使用该规则")}</label>`
        }
        $("body")
            .find(".master-policy-content .master-policy-name")
            .html(`${policyName}  ${limitInfo}`);
    }

    updateMasterPolicyBtn(masterData) {

        const overLimitKey = this.policyInfo?.limit?.overLimitKey ?? null;
        const hasLimit = overLimitKey && overLimitKey.includes('master');
        const limitInfo = hasLimit ?
            `<label style="color:#545861"><span class="el-icon-warning" style="color:#FFD22A;font-size:16px;"></span>${$t("所选的部分产品超出数量或价格限制，无法使用该规则")}</label>` :
            '';

        const $policyContent = $('.master-policy-content', this.$el);
        const {
            showPolicyBar,
            showPolicyBtn,
            policyMsg
        } = this.getMasterPolicyInfo(masterData);

        // 更新 UI
        //$policyContent.toggle(showPolicyBar);
        if (showPolicyBar) {
            $policyContent.css('visibility', 'visible');
        } else {
            $policyContent.css('visibility', 'hidden');
        }


        if (showPolicyBar) {
            $policyContent.find('.j-master-policy').toggle(showPolicyBtn);
            $policyContent.find('.master-policy-name').html(`${policyMsg}   ${limitInfo}`);
        }
    }

    //获取整单政策条显示内容&是否显示信息
    getMasterPolicyInfo(masterData) {
        const {
            pricePolicies = [], masterPricePolicy = {}
        } = this.policyInfo || {},
            masterPolicy = pricePolicies.find(p => p.id == masterData.price_policy_id);

        const entireOrderPolicy = pricePolicies.filter(({
            modifyType
        }) => modifyType === "master"),
            entireOrderPolicyCount = entireOrderPolicy.length,
            entireUseablePolicyCount = Object.keys(masterPricePolicy).length;

        const policyMsg = masterPolicy ?
            masterPolicy?.name || masterData.price_policy_id__r :
            entireUseablePolicyCount ?
                $t("请选择促销政策") :
                entireOrderPolicyCount ?
                    $t("未满足促销条件") :
                    null;

        return {
            showPolicyBar: Boolean(entireOrderPolicyCount),
            showPolicyBtn: Boolean(entireOrderPolicyCount && entireUseablePolicyCount),
            policyMsg
        };
    }

    updateDetailPolicyBtn(param) {
        const me = this,
            details = param.dataGetter.getDetail(this.detailApi),
            {
                detailPricePolicyMap = {}
            } = this.policyInfo || {};

        let {
            withPolicy,
            withoutPolicy
        } = details.reduce(({
            withPolicy,
            withoutPolicy
        }, item) => {
            const {
                record_type,
                prod_pkg_key,
                parent_gift_key,
                rowId
            } = item;
            // 只检查非赠品，检查detailPricePolicyMap中是否存在有效的prod_pkg_key
            if (!parent_gift_key) {
                let availablePolicy = detailPricePolicyMap[prod_pkg_key];
                let hasPolicy = availablePolicy && Object.keys(availablePolicy).length > 0;
                let target = hasPolicy ? withPolicy : withoutPolicy;

                if (!target[record_type]) {
                    target[record_type] = [];
                }

                target[record_type].push(rowId);
            }

            return {
                withPolicy,
                withoutPolicy
            };
        }, {
            withPolicy: {},
            withoutPolicy: {}
        });

        const toggleDetailButton = (policy, hidden) => {
            for (let rc in policy) {
                param.UI.toggleDetailButton({
                    [me.detailApi]: [{
                        record_type: rc,
                        buttons: [{
                            action: 'showPolicyDetail',
                            rowIds: policy[rc],
                            hidden: hidden
                        }]
                    }]
                })
            }
        };

        toggleDetailButton(withPolicy, false);
        toggleDetailButton(withoutPolicy, true);
    }


    /********************************************************/
    /*******************价格政策详情查看&操作*******************/
    /********************************************************/

    // hook 事件 : 显示数据政策信息
    policy_showPolicyDetail(plugin, obj) {
        const { type, dataId, rowData } = obj;
        if (type == 'master') {
            this.showPolicyDetail_m();
        } else {
            this.showPolicyDetail_d(rowData, dataId);
        }
    }

    showPolicyDetail_m() {
        const param = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'price_policy'
        }),
            masterData = param.dataGetter.getMasterData(),
            detailsData = param.dataGetter.getDetail(this.detailApi),
            repelCoupon = this.PricePolicy.getUsedRepelCoupon(masterData),
            relatedList = this.getRelatedList(param);
        param.end();
        this.showPolicyHandle("master", masterData, detailsData, repelCoupon, masterData, relatedList);
    }

    showPolicyDetail_d(rowData = {}, dataId) {
        const param = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'price_policy'
        }),
            masterData = param.dataGetter.getMasterData(),
            detailsData = param.dataGetter.getDetail(this.detailApi),
            repelCoupon = this.PricePolicy.getUsedRepelCoupon(masterData),
            relatedList = this.getRelatedList(param);

        rowData = rowData && Object.keys(rowData).length ? rowData : detailsData.find(d => d.rowId == dataId);
        param.end();
        this.showPolicyHandle("detail", rowData, detailsData, repelCoupon, masterData, relatedList);
    }


    getRelatedList(param) {
        if (!this.relatedList) {
            const fieldAttr = param.dataGetter.getFieldAttr("product_id", this.detailApi);
            this.relatedList = fieldAttr.target_related_list_name
        }
        return this.relatedList;
    }

    showPolicyHandle(type, data, detailsData, repelCoupon, masterData, relatedList) {
        const me = this;
        this.policyDetail = null;
        this.policyDetail = new PricePolicyDetail({
            $wrapper: $('body')[0],
            policyInfo: this.getAvailablePolicies(type, data, detailsData),
            data: data,
            decimalMap: this.decimalMap,
            limitInfo: this.policyInfo && this.policyInfo.limit,
            fieldMap: this.fieldMap,
            productNameConfig: this.productNameConfig,
            type: type,
            mdDefRecordType: this.mdDefRecordType,
            repelCoupon: repelCoupon,
            masterData,
            relatedList,
            allowedRange: this.allowedRange,
            isPeriod: this.isPeriod,
            policyHandle: async function (handleType, data, policy) {
                me.showPolicyLoading($t("政策计算中"), null, "policy");
                let dataFromPolicy, curData;
                const param = me.pluginService.api.pluginServiceFactory({
                    pluginApiName: 'price_policy'
                }),
                    dataKey = data.prod_pkg_key || "master";
                try {
                    switch (handleType) {
                        case "cancel":
                            dataFromPolicy = await me.PricePolicy.cancelPolicy(param, type, dataKey);
                            break;
                        case "match":
                            dataFromPolicy = await me.PricePolicy.selectPolicy(param, type, dataKey, policy.id);
                            break;
                        default:
                            dataFromPolicy = await me.PricePolicy.changePolicy(param, type, dataKey, policy.id);
                    }
                    return {
                        policies: me.afterDetailHandlePolicy(param, type, data, dataFromPolicy),
                        limitInfo: dataFromPolicy.policyInfo.limit
                    }
                } catch (err) {
                    me.handlePolicyError(err);
                    return {
                        policies: [],
                        limitInfo: null
                    }
                }
            },
            editPolicyGift: async function (gifts) {
                me.showPolicyLoading($t("赠品计算中"), null, "policy");
                const param = me.pluginService.api.pluginServiceFactory({
                    pluginApiName: 'price_policy'
                }),
                    dataFromPolicy = await me.PricePolicy.editGifts(param, gifts, data.prod_pkg_key || "master");
                return {
                    policies: me.afterDetailHandlePolicy(param, type, data, dataFromPolicy),
                    limitInfo: dataFromPolicy.policyInfo.limit
                }
            },
            getGiftByUnit: function (gifts, unitType) {
                return me.PricePolicy.getGiftByUnit(gifts, unitType)
            },
            getGiftPrice: function (gifts) {
                return me.PricePolicy.getGiftPrice(masterData, gifts)
            }
        })
    }

    afterDetailHandlePolicy(param, type, data, dataFromPolicy) {

        this.updateAfterPolicy(dataFromPolicy, param);
        let curData,
            detailsArr = param.dataGetter.getDetail(this.detailApi);
        if (type == 'master') {
            curData = param.dataGetter.getMasterData();
        } else {
            curData = detailsArr.find(d => d.prod_pkg_key == data.prod_pkg_key)
        }
        param.end();
        return this.getAvailablePolicies(type, curData, detailsArr);
    }

    //获取当前数据的全部可用价格政策
    getAvailablePolicies(type, data, detailData) {
        switch (type) {
            case "master":
                return this.masterAvailablePolicies(data, detailData)
            default:
                return this.detailAvailablePolicies(data, detailData)
        }
    }

    //主对象全部匹配政策通过政策modifyType == "master"查找
    masterAvailablePolicies(data, detailData) {
        const me = this,
            pricePolicies = this.policyInfo && this.policyInfo.pricePolicies || [],
            masterPolicyMap = this.policyInfo && this.policyInfo.masterPricePolicy || {};

        return Object.keys(masterPolicyMap).map(policyId => {
            let policyItem = Object.assign({}, pricePolicies.find(p => p.id == policyId));
            policyItem.rules = (policyItem.rules || []).filter(r => masterPolicyMap[policyId].includes(r.id));
            policyItem.fold = false;
            policyItem.loading = false;
            if (policyId == data.price_policy_id) {
                policyItem = me.parseActivePolicy(policyItem, data, detailData)
            }
            return policyItem;
        }).filter(p => p.id);
    }

    //从对象全部匹配政策通过detailPricePolicyMap查找
    detailAvailablePolicies(data, detailData) {

        const me = this,
            pricePolicies = this.policyInfo.pricePolicies || [],
            detailPricePolicyMap = this.policyInfo.detailPricePolicyMap || {},
            curPolicyMap = detailPricePolicyMap[data.prod_pkg_key];

        if (!curPolicyMap) {
            return [];
        }
        return Object.keys(curPolicyMap).map(policyId => {
            let policyItem = Object.assign({}, pricePolicies.find(p => p.id == policyId));
            policyItem.rules = curPolicyMap[policyId].map(ruleId => {
                return (policyItem.rules || []).find(pr => pr.id == ruleId) || {}
            });
            policyItem.fold = false;
            policyItem.loading = false;
            if (policyId == data.price_policy_id) {
                policyItem = me.parseActivePolicy(policyItem, data, detailData)
            }
            return policyItem;
        }).filter(p => p.id);
    }

    parseActivePolicy(policy, data, detailData) {
        policy.is_active = true;
        policy.fold = true;
        const {
            prod_pkg_key,
            parent_gift_key,
            price_policy_rule_ids
        } = this.fieldMap;
        //匹配的价格政策，过滤出匹配的规则
        if (data.price_policy_rule_ids && data.price_policy_rule_ids.length >= 1) {
            policy.rules = policy.rules.filter(r => data.price_policy_rule_ids.includes(r.id))
        }
        const giftTypeRules = ["gift", "combination_gift", "fixed_gift"],
            giftMap = Object.assign({}, data.gift_map || {}),
            dataKey = data.prod_pkg_key || "master",
            policyId = policy.id,
            isCurGift = (item, dataKey, ruleId, giftList = [], groupParentKey) => {
                const isGift = item[parent_gift_key] && (item[parent_gift_key] == dataKey || item[parent_gift_key] == groupParentKey),
                    isCurRule = item.price_policy_rule_ids && item.price_policy_rule_ids[0] == ruleId,
                    isConditionGift = giftList.findIndex(g => g.prod_pkg_key == item.prod_pkg_key) < 0;
                return isGift && isCurRule && isConditionGift;
            };
        policy.rules = (policy.rules || []).map(r => {
            const ruleId = r.id;
            if (!giftTypeRules.includes(r.ruleType)) {
                return r;
            }
            let giftRule = {},
                groupGiftParent = "";
            //数据本身有giftMap且有该规则赠品
            if (giftMap[ruleId]) {
                giftRule = Object.assign({}, giftMap[ruleId]);
            } else if (data.group_key && detailData) {
                //如果是组合规则，规则赠品信息可能在其他数据的gift_map里
                let giftData = (detailData || []).find(d => d.group_key == data.group_key && d.gift_map && d.prod_pkg_key !== dataKey);
                if (giftData && giftData.gift_map[ruleId]?.group_key) {
                    giftRule = {
                        ...giftData.gift_map[ruleId]
                    }
                    groupGiftParent = giftData[prod_pkg_key];
                }
            }
            //补全按范围选到的赠品
            const conditionGift = detailData.filter(d => isCurGift(d, dataKey, ruleId, giftRule.gift_list, groupGiftParent)).map(d => {
                return {
                    ...d,
                    isFromFilter: true,
                    isActive: true,
                    disabled: true,
                    product_id__r: d.product_id__r || d.product_id__s,
                    product_id__s: d.product_id__s || d.product_id__r
                }
            });
            giftRule.gift_list = [].concat(giftRule.gift_list || [], conditionGift);
            return Object.assign({}, r, giftRule);

        })
        return policy;
    }


    //
    policy_bomFilterCalFields(plugin, obj) {
        let tempFieldMap = this.fieldMap
        if (!tempFieldMap) {
            tempFieldMap = this.getAllFields(obj.mdApiName);
        }
        const { policy_dynamic_amount, amortize_amount, policy_subtotal, amortize_subtotal, policy_discount, policy_price } = tempFieldMap;
        return {
            filterFields: [policy_dynamic_amount, amortize_amount, policy_subtotal, amortize_subtotal, policy_discount, policy_price]
        }
    }

    //价格政策的loading事件，改用底层loading，提交前的match可以被监控
    showPolicyLoading(msg = '', el, from) {
        this.beginPolicyLoading = new Date();
        this.pluginService.api.showLoading(msg);
        this.matchingPolicy = true;
    }
    hidePolicyLoading(el) {
        this.pluginService.api.hideLoading();
        this.matchingPolicy = false;
    }

    //价格政策的loading事件
    showCustomerPolicyLoading(msg = '', el, from) {
        let ct = el || $('body');
        if (ct.children('.crm-policy-loading').length) return;
        ct.append(`<div class="crm-policy-loading loading-{{from}}" style="position:fixed;top:0;bottom:0;left:0;right:0;display:flex;align-items:center;justify-content:center;z-index:10000;flex-direction:column"><div style="z-index:100;margin-bottom:8px;font-size:14px;color:var(--color-primary06)" class="crm-waiting-text-msg">${msg}</div><div class="crm-loading-anima"></div></div>`);
        this.matchingPolicy = true;
    }
    hideCustomerPolicyLoading(el) {
        let ct = el || $('body');
        $('.crm-policy-loading', ct).remove();
        this.matchingPolicy = false;
    }

    policy_fromDestroyBefore(plugins, param) {
        this.policyDetail && this.policyDetail.destroy();
        this.policyDetail = null;
        this.destroy();
    }
    destroy() {
        $('body').undelegate('.j-master-policy', 'click');
        $('body').undelegate('.j-match-policy-mode', 'click');
    }
}
