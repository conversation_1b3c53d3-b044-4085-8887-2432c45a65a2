/**
 * @desc: BOM 插件-Web
 * @author: wangshaoh
 * @date: 12/29/21
 */
import Base from 'plugin_base'
import {requireField} from '@common/require';
import BqueryComp from './componets/bquery.js';
import multilangHook from './componets/multilangHook.js'
// import iconConfig from '@detail/view/v3/base/components/headinfo/iconconfig';
// import iconHandle from '@detail/view/v3/base/components/headinfo/iconhandle';
// import iconAuth from '@detail/view/v3/base/components/headinfo/iconauth';

export default class Bquery extends Base{
    constructor(pluginService, pluginParam) {
        super(...arguments);
        console.log(pluginService, 'pluginService-------')
        this.fieldMapping = this.getAllFields();
    }

    _formRenderBeforeOld(plugin, param) {
        let components = {};
        const me = this;
        let customParam = me.pluginParam.describe.params.customParam || {};
        requireField().then((Field) => {
            components[this.fieldMapping.fieldApiName] = function(param){
                return new Field.C.bquery(_.extend(param, {
                    mappingRuleApiname: customParam.mapping_rule_api_name,
                    industryExtFieldApiName: me.fieldMapping.industryExtFieldApiName,
                    bizRegNameFieldApiName: me.fieldMapping.bizRegNameFieldApiName,
                }));
            }
        })

        return {
            components,
        }
    }

    _formRenderBefore(plugin, param) {
        let components = {};
        const me = this;
        const { BaseComponents } = param;
        let customParam = me.pluginParam.describe.params.customParam || {};
        // import('./bquery.js').then((comp) => {
        //     const Comp = comp.default;
            // function compo(options) {
            //     const params = {
            //         pluginService: plugin,
            //         pluginParam: param,
            //         options: _.extend(options, {
            //             mappingRuleApiname: customParam.mapping_rule_api_name,
            //             industryExtFieldApiName: me.fieldMapping.industryExtFieldApiName,
            //             bizRegNameFieldApiName: me.fieldMapping.bizRegNameFieldApiName,
            //         }),
            //         parent: me,
            //     }
            //     me.gongshangComp =  new BqueryComp(params);
            //     return me.gongshangComp
            // }
            // components[me.fieldMapping.fieldApiName] = compo;
        // })
        requireField().then((Field) => {
          const multilang = Field.C.multilang;
          components[me.fieldMapping.fieldApiName] = BaseComponents.base.extend(_.extend({
            async render() {
              const params = {
                pluginService: plugin,
                pluginParam: param,
                options: _.extend(this.options, {
                    mappingRuleApiname: customParam.mapping_rule_api_name,
                    industryExtFieldApiName: me.fieldMapping.industryExtFieldApiName,
                    bizRegNameFieldApiName: me.fieldMapping.bizRegNameFieldApiName,
                }),
                parent: me,
              }
              const gongshangComp = (me.gongshangComp = new BqueryComp(params));
              gongshangComp.$on('name.change', (val) => {
                this.trigger('name.change', val)
              })
              gongshangComp.render && await gongshangComp.render()
              await gongshangComp.$mount();
              this.$el.append(gongshangComp.$el);
              this.$ipt = $('input', this.$el);
              // 适配平台多语级翻译
              this.renderMultilineActionbar && this.renderMultilineActionbar(this.$ipt);
            },
            //输出该字段的值
            getValue(){
              return me.gongshangComp.getValue();
            },
            //当外部修改字段值时会调用
            setValue(val) {
              me.gongshangComp.setValue(val);
            },
            passiveSetValue(obj) {
              // 客户主数据字段自动回填
              me.gongshangComp.passiveSetValue(obj);
            },
            setStatus() {
              // 设置字段编辑状态
              me.gongshangComp.setStatus();
            },
            disable() {
              me.gongshangComp.disable();
            },
            enable() {
              me.gongshangComp.enable && me.gongshangComp.enable();
            },
            hideError() {
              me.gongshangComp.hideError && me.gongshangComp.hideError();
            },
            hideUIError() {
              me.gongshangComp.hideUIError && me.gongshangComp.hideUIError();
            },
            showUIError(tips) {
              me.gongshangComp.showUIError && me.gongshangComp.showUIError(tips);
            },
            setData(value, apiname, noTrigger, isBlur, noCalculate) {
              me.gongshangComp.setData && me.gongshangComp.setData(value, apiname, noTrigger, isBlur, noCalculate);
            },
            showError(el, error, flag) {
              me.gongshangComp.showError && me.gongshangComp.showError(el, error, flag);
            },
            showTips(msg) {
              me.gongshangComp.showTips && me.gongshangComp.showTips(msg);
            },
            show() {
              me.gongshangComp.show && me.gongshangComp.show();
            },
            hide() {
              me.gongshangComp.hide && me.gongshangComp.hide();
            },
            destroy: function() {
              me.gongshangComp && (me.gongshangComp.$destroy(), me.gongshangComp = null);
              this.super.destroy.call(this);
            }
          }, multilang, multilangHook));
        })


        return {
            components,
        }
    }

    _detailHeadinfoRenderBefore(plugin, param) {
        const me = this;
        return Promise.all([
            import('@detail/view/v3/base/components/headinfo/iconconfig'),
            import('@detail/view/v3/base/components/headinfo/iconhandle'),
            import('@detail/view/v3/base/components/headinfo/iconauth'),
        ]).then(([iconConfig, iconHandle, iconAuth]) => {
            iconHandle = iconHandle.default;
            iconAuth = iconAuth.default;
            let item = iconConfig.map.BusinessInquiry;
            let data =  param.dataGetter().getData();
            let apiname = param.objectApiName;
            let context = {
                apiName: apiname,
                data: data,
                industryExtFieldApiName: me.fieldMapping.industryExtFieldApiName,
                fieldApiName: me.fieldMapping.fieldApiName,
            };
            let helper_func = param.component.helper_func || [];
            item = _.extend({}, iconConfig.map.BusinessInquiry, {
                on: {
                    click(e) {
                        me.doLog(data, item.action);
                        iconHandle[`handle${item.action}`](e, item, context);
                    }
                }
            })
            return iconAuth[`check${item.action}`](item, context).then((res) => {
                if (res) {
                    helper_func.push(item);
                    param.component.helper_func = helper_func;
                }
            })
        })
    }

    doLog(data, operationId, eventType) {
        var apiName = data.object_describe_api_name;
        CRM.util.sendLog('crm', 'detail', { // 统计代码
            operationId: operationId || 'view',
            eventType: eventType || 'cl',
            pageData: {
                object_id: data._id,
                object_api_name: apiName,
            }
        });
    }

    _formChangeEnd() {
      let me = this;
      setTimeout(() => {
        me._bquerySuffixIcon()
      }, 0);
    }

    _formRenderAfter() {
      let me = this;
      setTimeout(() => {
        me._bquerySuffixIcon()
      }, 0);
    }

    _bquerySuffixIcon() {
      let me =this;
      if(this.pluginService && this.pluginService.run) {
        this.pluginService.run('form.query.suffix_icon.render.before', {allIcons: ['gongshang', 'dengbaishi']}).then((result) => {
          console.log(result, 'form.query.suffix_icon.render.before')
          if(result.StatusCode == 0 && result.BizCode == 2) {
            if(me.gongshangComp){
              // 调用组件里控制邓白氏icon显示方法
              me.gongshangComp.changeIcon(result.Value)
            }
          }
        })
      }
    }

    _handleQuerySuffixIcon(plugin, param) {
      console.log(param, '_handleQuerySuffixIcon')
      return param.allIcons
    }

    getHook() {
        return [
            {
                event: 'form.render.before',
                functional: CRM.util.isGrayScale('CRM_BQUERY_20') ? this._formRenderBefore.bind(this) : this._formRenderBeforeOld.bind(this)
                // functional: this._formRenderBefore.bind(this),
            },
            {
                event: 'detail.head_info.render.before',
                functional: this._detailHeadinfoRenderBefore.bind(this)
            },
            {
              event: 'form.change.end',
              functional: this._formChangeEnd.bind(this)
            },
            {
              event: 'form.render.after',
              functional: this._formRenderAfter.bind(this)
            },
            {
              event: 'form.query.suffix_icon.render.before',
              functional: this._handleQuerySuffixIcon.bind(this)
            }
        ];
    }
}
