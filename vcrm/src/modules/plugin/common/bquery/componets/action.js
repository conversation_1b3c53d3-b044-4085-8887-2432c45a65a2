export default {
    // 数据发生变更后相关变更
    changeHandle(obj, notrigger) {
        // 校验
        this.validate(obj.value);
        // 查重
        this.checkCustomerDuplicate(obj.value);
        // 有工商，但是没有该数据权限, 则不修改数据不回填不点亮（编辑、复制而来的初始数据存在这种情况）
        // 正常由输入得来的数据有工商肯定有权限，则正常更新
        // 如果数据没有工商，则正常更新
        if (obj.id && !this.getOneRights(this.bqueryRights, obj.datasource)) return;
        // 设置当前展示值的信息
        this.setValue(obj);
        // 更新model层data数据
        this.updateData(obj, notrigger);
        // 更新model层其他数据（工商注册，扩展字段等）
        this.updateExtraData(obj);
        // 是否自动回填
        this.execBackFill(obj, (data, obj) => {
            // 回填结束后检查选择的数据名称是否跟映射数据名称一致，不一致需要重新查重和更新展示值
            // 存在这个问题为啥不先回填完了再进行查重更新呢，有的用户可能操作很快没有等回填接口返回就提交了，故需先更新一次，而且是否自动回填是接口配置控制的
            if (data[this.apiname] && obj.value !== data[this.apiname]) {
                // 更新值
                obj.value = data[this.apiname];
                // 查重
                this.checkCustomerDuplicate(obj.value);
                // 设置当前展示值， 数据层batchupdate已更新
                this.setValue(obj);
            }
        });
        // 插件
        this.parent.runPlugin('bquery.changeHandle.after', {
            bqueryInfo: obj,
            param: this.pluginParam,
        })
        !notrigger && this.$emit('name.change', this.getValue());
        console.log(999, this.inputValue, this.valueObj, this.get('data'));
    },

    // 回填后数据发生变更处理
    backfillChangeHandle(obj, notrigger) {
        const me = this;
        // 回填数据
        this.backfill(obj, (data, obj) => {
            // 用映射的数据名称更新当前数据的名称（防止名称不一致的情况）
            data[this.apiname] && (obj.value = data[this.apiname]);
            if(obj.datasource) {
                // 如果是补充查询返回的数据，后续无需存储datasource
                if(obj.supplyTag === 1) {
                    delete obj.datasource;
                }
                // 无论是否补充查询后续均无需传supplyTag，删除
                delete obj.supplyTag;
            }
            // 查重
            this.checkCustomerDuplicate(obj.value);
            // 设置当前展示值
            this.setValue(obj);
            // 更新model层data数据(映射回填没更新名称时，才需要在这里更新数据层)
            !data[this.apiname] && this.updateData(obj, notrigger);
            // 更新model层其他数据（工商注册，扩展字段等）
            this.updateExtraData(obj);
            // 如果是从高级查询回填需要关闭高级查询
            if (me.advanceBquery) {
                me.advanceBquery.destroy && me.advanceBquery.destroy();
                me.advanceBquery = null;
            }
            // 插件
            this.parent.runPlugin('bquery.backfillChangeHandle.after', {
                backfillData: data,
                bqueryInfo: obj,
                param: this.pluginParam,
            })
            console.log(888, this.inputValue, this.valueObj, this.get('data'));
        })
    },

    handleSelect(item) {
        // 点击item普通节点更新数据
        let _v = {
            value: item.value, 
            id: item.id
        };
        this.changeHandle(_v);
    },

    handleFocus() {
        this.hideError();
    },

    handleBlur(e) {
        const me = this;
        // 如果点击到下拉框中的有效数据失焦，则不执行失焦逻辑
        // 如果通过pwc控制不显示 工商icon，
        const showGongshang = me.showIcon.includes('gongshang');
        if (this.itemClickStatus && this.itemClickStatus === 1 || !showGongshang) {
            this.itemClickStatus = 0;
            return;
        }

        // 如果点击下拉框中其他部分失焦，则只更新当前当前输入框的数据（以及相关扩展字段），不处理工商
        if (this.itemClickStatus && this.itemClickStatus === 2) {
            this.itemClickStatus = 0;
            if(this.inputValue && this.inputValue !== this.valueObj.value) {
                let _v = {
                    value: me.inputValue,
                }
                this.handleSelect(_v);
            }
            return;
        }

        // 如果当前输入框有值inputValue且 (跟存的的值valueObj.value不同 或 存的值没有valueObj.id)
        if (this.inputValue && (this.inputValue !== this.valueObj.value || !this.valueObj.id)){
            // 如果未查询过接口
            if(this.inputValue != this.queryString){
                // 在接口返回之前需要先更新一次数据，防止接口还没返回的情况已经提交了未更新的数据
                let _v = {
                    value: me.inputValue,
                }
                this.handleSelect(_v);
                // 查询接口
                this.doSearch(this.inputValue).then((obj) => {
                    let list = obj && obj.list;
                    doBlur(list);
                })
            } else {  //已查询过则无需重新查询
                doBlur(me.searchList);
            }
        } else {
            // 如果输入框的值和已存的值一样（都没值 || 一样的值），则无需更新
            if (me.inputValue === me.valueObj.value) return;
            doBlur();
        }

        function doBlur(searchList) {
            let _v = {
                value: me.inputValue,
            }
            // 如果值跟下拉列表匹配，则将匹配值作为需要更新的值
            if (searchList && searchList.length && searchList[0].value === me.inputValue) {
                _v = searchList[0];
            }
            // 如果要更新的值跟现有数据完全一致则无需再更新处理
            if (_v.value === me.inputValue && _v.value === me.valueObj.value && _v.id === me.valueObj.id) return;
            me.handleSelect(_v);
        }
    },

    // 处理item点击，主要是为了拦截部分不触发选择数据的情况
    handleSearchItemClick(e, item) {
        // 不可用，失焦效果会延迟到下一次点击
        // e.preventDefault();
        // 下拉框点击标识
        this.itemClickStatus = 1;

        if (this.pluginFns.handleSearchItemClick) {
            this.pluginFns.handleSearchItemClick(e, item, {
                context: this,
            });
            return;
        }
        const $tg = $(e.target);
        // 非item普通内容节点点击（暂无数据、错误提示）
        if (item.type) {
            // 只处理当前输入框里的数据，更新扩展字段等
            this.itemClickStatus = 2;
            // 不选中，需要阻止事件冒泡
            e.stopPropagation();
            this.clickStopPropagation = true;
            // 点击高级查询
            if (item.type == 3) {
                // 走失焦逻辑处理数据
                this.itemClickStatus = 0;
                // 关闭弹层
                this.hideSearchLayer();
                this.toAdvanceBquery(e);
            }
            return;
        }
        // 如果点击到item里的工字，进详情，不选中
        if ($tg.hasClass('j-b-detail')) {
            // 只处理当前输入框里的数据，更新扩展字段等
            this.itemClickStatus = 2;
            // 不选中，需要阻止事件冒泡
            e.stopPropagation();
            this.clickStopPropagation = true;
            // 关闭弹层
            this.hideSearchLayer();
            this.showBusinessDetail(e, {value: item.value, id: item.id})
            return;
        }
        // 其他情况会触发select，走handleSelect的逻辑
    },

    // 补丁，阻止冒泡
    hackHandleSearchItemClick(e, item) {
        if(this.clickStopPropagation) {
            e.stopPropagation();
            this.clickStopPropagation = false;
        }
    },

    handleBqueryPrefixClick(e) {
        if (this.pluginFns.handleBqueryPrefixClick) {
            this.pluginFns.handleBqueryPrefixClick(e, {
                context: this,
            });
            return;
        }
    },
    
    handleBquerySuffixClick(e) {
        if (this.pluginFns.handleBquerySuffixClick) {
            this.pluginFns.handleBquerySuffixClick(e, {
                context: this,
            });
            return;
        }
        const $tg = $(e.target);
        if ($tg.hasClass('j-b-detail')) {
            this.showBusinessDetail(e, this.valueObj);
        }
        if ($tg.hasClass('j-b-dengbaishi')) {
            this.showAdvanceBquery(e);
        }
    },

    // 处理查询为高级查询还是补充查询
    toAdvanceBquery(e) {
        const me = this;
        let $tg = $(e.target);
        let from = $tg.data('from');
        if (!from) return;
        let supplyTag = (from === 'supply') ? 1 : 0;
        let sourceOptions = me.getSourceOptions(supplyTag);
        let title = supplyTag == 1 ? $t('工商信息补充查询') : (sourceOptions.length > 1 ? $t('高级工商查询') : sourceOptions[0].label + $t('高级工商查询'));
        
        me.showAdvanceBquery(e, {
            title: title,
            sourceOptions: sourceOptions,
            supplyTag: supplyTag,
        });
    },

    // 高级查询、补充查询、邓白氏
    showAdvanceBquery(e, options = {}) {
        const me = this;
        import('@components/advancebquery/index.js').then((res) => {
            let Comp = res.default;
            me.advanceBquery = new Comp(_.extend({
                title: $t("邓白氏全球企业信息查询"),
                inputValue: me.inputValue,
                objectApiname: me.obj_apiname,
                apiname: me.apiname,
            }, options));
            me.advanceBquery.$on('showDetail', (data, datasource) => {
                me.showBusinessDetail(null, {
                    id: data.KeyNo,
                    value: data.Name,
                    datasource: datasource,
                    supplyTag: options.supplyTag,
                })
            })
            me.advanceBquery.$on('change', (data, datasource) => {
                let _data = {
                    id: data.KeyNo,
                    value: data.Name,
                    datasource: datasource,
                    supplyTag: options.supplyTag,
                }
                me.backfillChangeHandle(_data);
            })
            me.advanceBquery.$on('destroy', () => {
                me.advanceBquery = null;
            })
        })
    },

    // 工商详情
    showBusinessDetail: function (e, obj) {
        var me = this;

        if (me.pluginFns.showBusinessDetail) {
            me.showBusinessDetail(e, obj, {
                context: this,
            });
            return;
        }

        var obj = obj || me.valueOb;

        // 没有id则返回
        if(!obj.id) return;

        // 有数据id没有权限则提示无权限查看（补充查询进详情无需判断权限）
        if (obj.supplyTag != 1 && !this.getOneRights(this.bqueryRights, obj.datasource)){
            let map = {
                QiChaCha: $t("企查查"),
                DengBaiShi: $t('邓白氏'),
                TianYanCha: $t('天眼查'),
            }
            let datasourceText = map[obj.datasource] || $t('普通');
            CRM.util.alert($t('不具备{{datasourceText}}工商查询功能权限', {
                datasourceText: datasourceText,
            }))
            return;
        }

        me.doLog({
            operationId: 'BusinessInquiryDetail'
        });

        // 区分对象的工商详情埋点
        CRM.util.sendLog(me.obj_apiname, 'newpage', {
            operationId: obj.datasource ? obj.datasource + (obj.supplyTag ? 'Supply': '') + 'Detail' : 'BusinessDetail',
            eventType: 'cl',
        });

        CRM.api.business_detail2({
            apiname: me.obj_apiname,
            id: obj.id,
            name: obj.value,
            datasource: obj.datasource,
            supplyTag: obj.supplyTag,
            zIndex: CRM.util.getzIndex(me.get('zIndex')) + 10,
            isShowBtn: true,
            mappingRuleApiname: me.getOptions().mappingRuleApiname,
            callback: function() {
                me.backfillChangeHandle(obj);
            },
        });
    },

    // 触发回填
    execBackFill: function (obj, callback) {
        // 是否自动回填 
        if (this.isAutoBackfill) {
            this.backfill(obj, callback);
        } 
    },

    // 工商回填
    backfill: function (obj, callback) {
        const me = this;
        if (me.pluginFns.backfill) {
            me.pluginFns.backfill(obj, callback, {
                context: this
            });
            return;
        }

        var obj = obj || this.valueObj;
        if (obj && !obj.id) {
            return;
        }

        CRM.util.sendLog('BizQueryObj', 'Detail', {
            operationId: 'WriteBack',
            eventType: 'cl',
        });

        // 区分对象的工商回填埋点
        CRM.util.sendLog(this.obj_apiname, 'newpage', {
            operationId: obj.datasource ? obj.datasource + (obj.supplyTag ? 'Supply': '') + 'WriteBack' : 'BusinessWriteBack',
            eventType: 'cl',
        });

        CRM.util.waiting(true);
        me.getBackfillData(obj).then(async (data) => {
            CRM.util.waiting(false);
            await me.backfillSuccess(data, obj);
            callback && callback(data, obj);
        }, () => {
            CRM.util.waiting(false);
        })
    },

    // 回填成功
    async backfillSuccess(data, obj = {}) {
        if (this.pluginFns.backfillSuccess) {
            this.pluginFns.backfillSuccess(data, obj, {
                context: this,
            });
            return;
        }
        
        // 处理需要回填的数据
        data = await this.parseBackfillData(data, obj);

        // 回填数据
        this.model.newBatchUpdate(data);
    },

    async parseBackfillData(data, obj = {}) {
        // 负责人不回填
        data.owner = void 0;
        // 字段中对象名不回填
        data.object_describe_api_name = void 0;
        // 映射回填的数据名称和原数据名称一致，则不重新回填名称字段
        if (data[this.apiname] == obj.value) {
            data[this.apiname] = void 0;
        // } else {
            // 若不一致，则用映射数据的名称更新当前数据名称
            // obj.value = data[this.apiname];
            // this.checkCustomerDuplicate(obj.value);
            // this.setValue(obj);
        }

        if (this.pluginFns.parseBackfillData) {
            data = await this.pluginFns.parseBackfillData(data, obj, {
                context: this,
            });
        }
        return data;
    },

    // 查重
    checkCustomerDuplicate(name) {
        const me = this;
        const url = me.getCheckDuplicateUrl(me.obj_apiname);
        
        // 没有值时不进行判重 或 编辑时名称和初始值相同不进行判重
        if (!name || (me.obj_apiname === 'AccountObj' && me.data._id && (me.data.name === name))) { 
            me.hideTips();
            return;
        };

        if (!url) {
            return;
        }

        this.doCheckCustomerDuplicate(url, name).then((res) => {
            var me = this;
            if (res && res.Result && res.Result.StatusCode === 0) {
                if (res.Value.duplicated) {
                    const _nameMap = {
                        AccountObj: $t('crm.客户'),
                        PartnerObj: $t('crm.合作伙伴'),
                    }
                    me.showTips($t("该{{displayName}}已存在", {displayName: _nameMap[me.obj_apiname] || me.fieldAttr.label}));
                } else {
                    me.hideTips();
                }
            } else {
                me.hideTips();
            }
        })
    }
}