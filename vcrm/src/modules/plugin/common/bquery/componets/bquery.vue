<template>
    <div class="crm-bquery">
        <!-- <component class="crm-bquery-prefix" :is="getBqueryPrefixHtml()" @click.native="handleBqueryPrefixClick"></component> -->
        <div class="crm-bquery-prefix" @click="handleBqueryPrefixClick">
            <component :is="getBqueryPrefixHtml()"></component>
        </div>
        <fx-autocomplete
            ref="crmBqueryInput"
            class="crm-bquery-input"
            popper-class="crm-bquery-autocomplete"
            v-model="inputValue"
            :fetch-suggestions="querySearch"
            :trigger-on-focus="true"
            debounce="500"
            :placeholder="placeholder"
            size="small"
            @focus="handleFocus"
            @blur="handleBlur"
            @select="handleSelect"
            >
            <template slot-scope="{ item }">
                <div @mousedown="handleSearchItemClick($event, item)" @click="hackHandleSearchItemClick($event, item)">
                    <component :is="getSearchItemHtml(item, inputValue)" :item="item" :inputValue="inputValue"></component>
                </div>
            </template>
        </fx-autocomplete>
        <!-- <component class="crm-bquery-suffix" :is="getBquerySuffixHtml()" @click.native="handleBquerySuffixClick"></component> -->
        <div class="crm-bquery-suffix" @click="handleBquerySuffixClick">
            <component :is="getBquerySuffixHtml()" ></component>
        </div>
    </div>
</template>

<script>
import base from './base';
import api from './api';
import action from './action';
import util from './util';

export default {
    props: {
        pluginService: Object,
        pluginParam: Object,
        getOptions: Function,
        parent: Object,
    },
    data() {
        return {
            searchList: [],  //查询列表数据
            inputValue: "",  // 输入框数据
            valueObj: {},  // 包含id的数据
            ready: true,
            pluginFns: {},
            queryString: '',  //记录数据查询时用的queryString 作为失焦时是否重查接口的判断
            placeholder: $t("请输入"),
            showIcon: [],
            searchable: true,
        };
    },
    computed: {
        // 当值存在id且有当前datasourc数据的权限的时候，点亮icon的标识为true;
        light() {
            return !!this.valueObj.id && !this.valueObj.noRights;
        },
    },
    created() {

    },
    mounted() {
        this.init();
    },
    methods: {
        changeIcon(param) {
          this.showIcon = param;
          // 处理pwc控制工商插件icon隐藏，输入框focus时下拉列表不能及时隐藏
          if(!param.includes("gongshang")) {
            $(".crm-bquery-autocomplete").addClass("unshow-gongshang");
            this.itemClickStatus = 0;
          }else{
            $(".crm-bquery-autocomplete").removeClass("unshow-gongshang");
          }
        },
        init() {
            this.$elp = $(this.getOptions().el);
            this.ajaxs = {};  // 销毁的时候abort
            this.model = this.getOptions().model;
            // 当前字段apiname
            this.apiname = this.getOptions().apiname;
            // 当前字段描述
            this.fieldAttr = this.getAttr(this.apiname);
            const support_languages = this.fieldAttr.support_languages;
            if (!support_languages || !support_languages?.length) {
                $(".crm-bquery-input .el-input input").css("padding-right", '0px');
            }else{
                $(".crm-bquery-input .el-input input").css("padding-right", '33px');
            }
            // 当前对象apinane
            this.obj_apiname = this.get('apiname');
            this.fieldAttr.help_text && (this.placeholder = this.fieldAttr.help_text)
            // 隐藏的industry_ext字段需要提交
            let _masterNeedSubmit = this.get('_masterNeedSubmit') || [];
            this.industryExtFieldApiName = this.getOptions().industryExtFieldApiName || 'industry_ext';
            this.bizRegNameFieldApiName = this.getOptions().bizRegNameFieldApiName || 'biz_reg_name';
            if (_.indexOf(_masterNeedSubmit, this.industryExtFieldApiName) == -1) {
                _masterNeedSubmit.push(this.industryExtFieldApiName);
                this.set('_masterNeedSubmit', _masterNeedSubmit);
            }
            // 对象数据
            this.data = this.get('data') || {};
        },
        async beforeRender() {
            await this.parent.runPlugin('bquery.render.before', {
                param: this.pluginParam,
            }).then((res) => {
                if (!res) return;
                res.dataEvents && (this.pluginFns.dataEvents = res.dataEvents);
                res.getInitValueObj && (this.pluginFns.getInitValueObj = res.getInitValueObj);
                res.getBackfillConfig && (this.pluginFns.getBackfillConfig = res.getBackfillConfig);
                res.getBqueryRights && (this.pluginFns.getBqueryRights = res.getBqueryRights);
                res.getSearchItemHtml && (this.pluginFns.getSearchItemHtml = res.getSearchItemHtml);
                res.getBqueryPrefixHtml && (this.pluginFns.getBqueryPrefixHtml = res.getBqueryPrefixHtml);
                res.getBquerySuffixHtml && (this.pluginFns.getBquerySuffixHtml = res.getBquerySuffixHtml);
                res.querySearch && (this.pluginFns.querySearch = res.querySearch);
                res.doSearch && (this.pluginFns.doSearch = res.doSearch);
                res.handleSearchItemClick && (this.pluginFns.handleSearchItemClick = res.handleSearchItemClick);
                res.handleBqueryPrefixClick && (this.pluginFns.handleBqueryPrefixClick = res.handleBqueryPrefixClick);
                res.handleBquerySuffixClick && (this.pluginFns.handleBquerySuffixClick = res.handleBquerySuffixClick);
                res.backfill && (this.pluginFns.backfill = res.backfill);
                res.backfillSuccess && (this.pluginFns.backfillSuccess = res.backfillSuccess);
                res.getBackfillData && (this.pluginFns.getBackfillData = res.getBackfillData);
                res.parseBackfillData && (this.pluginFns.parseBackfillData = res.parseBackfillData)
                res.getExtraUpdateData && (this.pluginFns.getExtraUpdateData = res.getExtraUpdateData);
                res.lightIcon && (this.pluginFns.lightIcon = res.lightIcon);
                res.shutIcon && (this.pluginFns.shutIcon = res.shutIcon);
            })
            // 添加事件监听处理
            let dataEvents = this.pluginFns.dataEvents ? this.pluginFns.dataEvents : this.dataEvents;
            this.addEvent(_.isFunction(dataEvents) ? dataEvents() : dataEvents);
            await this.getBqueryRights();
            this.getBackfillConfig();
        },
        async render() {
            await this.beforeRender();
            // this.ready = true;
            // 需等组件渲染完
            const name = this.getData();
            this.inputValue = name;
            // 没有普通工商权限则下拉框不查询
            if (!this.basicRights) {
                this.searchable = false;
            }
            // 查询name是否存在工商id等信息
            let obj = await this.getInitValueObj(name);
            if (obj) {
                // 配置了初始回填
                if (this.get('isInitialCheckBquery')) {
                    this.handleSelect(obj);
                } else {
                    // valueObj变更，则点亮标识变更
                    this.setValueObj(obj);
                    // 更新model层data数据
                    this.updateData(obj);
                    // 更新model层其他数据（工商注册，扩展字段等）
                    this.updateExtraData(obj);
                }
            }
            this.afterRender();
        },
        afterRender() {
            this.setStatus();
            this.parent.runPlugin('bquery.render.after', {
                param: this.pluginParam,
            })
        },
        getSearchItemHtml(item, keyword){
            let itemHtml = '';
            switch(item.type) {
                // 没有数据
                case 1:
                    itemHtml = `<div class="crm-bquery-search-nodata">${item.value}</div>`;
                    break;
                // 错误
                case 2:
                    itemHtml = `<div class="crm-bquery-search-error">${item.value}</div>`;
                    break;
                // 高级查询
                case 3:
                    itemHtml = this.getAdvanceItemHtml(item, keyword)
                    break;
                // 单条数据
                default:
                    const pValue = keyword ? CRM.util.highlight(item.value, keyword) : item.value;
                    itemHtml = `<div class="crm-bquery-search-item">
                        <div class="crm-bquery-search-item-name">${pValue}</div>
                        <div class="crm-bquery-search-item-desc"><span>${$t("法人")}：${item.operName}</span><em>丨</em><span>${$t("经营状态")}:${item.status}</span></div>` //[ignore-i18n]
                        +`<div class="crm-bquery-search-item-desc"><span>${$t("统一社会信用代码")}：${item.creditCode}</span></div>
                        <div class="crm-bquery-search-item-icon j-b-detail">工</div>` //[ignore-i18n]
                    +`</div>`;
                    break;
            }
            let _value;
            if (this.pluginFns.getSearchItemHtml) {
                _value = this.pluginFns.getSearchItemHtml(item, keyword, {
                    itemHtml: itemHtml,
                    context: this,
                })
            }
            if (_value) {
                return _value;
            }
            return {
                template: itemHtml,
            }
        },
        getAdvanceItemHtml(item, keyword) {
            let itemHtml = '';
            let _wrapli1 = `<div class="crm-bquery-search-advance j-search-advance">`;
            let _wrapli2 = `</div>`;
            let _advance = `<a class="" title="${$t("高级工商查询")}" data-from="advance" href="javascript:;">${$t("高级工商查询")}></a>`;
            let _supply = `<a class="" title="${$t("查不到想要的企业？点击这里")}" data-from="supply" href="javascript:;">${$t("查不到想要的企业？点击这里")}</a>`;
            // 灰度了补充查询，且存在普通工商权限，且没有匹配到查询数据 =》则展示补充查询
            if (CRM.util.isGrayScale('CRM_SUPPLY_BQUERY') && this.basicRights && (!this.searchList.length || (this.searchList.length && keyword !== this.searchList[0].value))) {  // 开启灰度并查不到匹配数据显示补充工商查询
                let con = _supply;
                // 存在高级查询权限，则展示高级查询
                if (this.tianyanchaRights || this.qichachaRights) {
                    con = _advance + _supply;
                    con = con.replace(/class=""/g, 'class="cwidth"');
                }
                itemHtml = (_wrapli1 + con + _wrapli2);
            } else if (this.tianyanchaRights || this.qichachaRights) {  // 存在高级查询权限，显示高级查询
                itemHtml = (_wrapli1 + _advance + _wrapli2);
            }
            return itemHtml;
        },
        getBqueryPrefixHtml() {
            let prefixHtml = '';
            let _value;
            if (this.pluginFns.getBqueryPrefixHtml) {
                _value = this.pluginFns.getBqueryPrefixHtml({
                    prefixHtml: prefixHtml,
                    context: this,
                });
            }
            if (_value) {
                return _value;
            }
            return {
                template: prefixHtml,
            };
        },
        getBquerySuffixHtml() {
            let suffixHtml = '';
            let showGongshang = this.showIcon.includes('gongshang');
            if (this.finallyRights && showGongshang) {
                suffixHtml += `<fx-tooltip :effect="effectType" content="${$t('工商查询')}" placement="top">
                <span class="crm-bquery-icon icon-business j-b-detail ${this.light? 'icon-light': ''}">工</span>` //[ignore-i18n]
                +`</fx-tooltip>`;
            }
            let showDengbaishi = this.showIcon.includes('dengbaishi');
            if (this.dengbaishiRights && !this.isReadonly() && showDengbaishi) {
                suffixHtml += `<fx-tooltip :effect="effectType" content="${$t('邓白氏全球企业信息查询')}" placement="top">
                <span class="crm-bquery-icon icon-dengbaishi j-b-dengbaishi"></span>
                </fx-tooltip>`;
            }

            let _value;
            if (this.pluginFns.getBquerySuffixHtml) {
                _value = this.pluginFns.getBquerySuffixHtml({
                    suffixHtml: suffixHtml,
                    context: this,
                });
            }
            if (_value) {
                return _value;
            }
            return {
                template: `<div class="crm-bquery-suffix-inner">${suffixHtml}</div>`,
            }
        },
        // 调用 callback 返回建议列表的数据
        querySearch(queryString, cb) {
            // console.log(new Date());
            if (this.pluginFns.querySearch) {
                this.pluginFns.querySearch(queryString, cb, {
                    context: this,
                });
                return;
            }
            let results = [];

            // 未输入内容不查询，不展示下拉框
            // 没有任何工商权限时，不展示下拉框
            // pwc控制不显示工商icon，不展示下拉框
            let showGongshang = this.showIcon.includes('gongshang');
            if (!queryString || !this.finallyRights || !showGongshang) {
                cb(results);
                return;
            }

            // 没有普通工商权限
            if (!this.basicRights) {
                results.push({
                    value: $t('暂无权限'),
                    type: 2,
                });
                results.push({
                    value: $t('高级查询'),
                    type: 3,
                })
                this.searchList = [];
                this.queryResults = results;
                cb(results);
                return;
            }

            // 如果已查询的queryString跟当前传进来的一样，则不重新查询，只做结果展示
            if (this.queryString === queryString) {
                cb(this.queryResults || []);
                return;
            }

            // 埋点
            this.doLog({
                operationId: 'Search'
            });
            // 区分对象的工商搜索埋点
            CRM.util.sendLog(this.obj_apiname, 'newpage', {
                operationId: 'BusinessInquiry',
                eventType: 'cl',
            });

            this.doSearch(queryString).then((obj) => {
                // 记录数据查询时用的queryString 作为失焦时是否重查接口的判断
                this.queryString = queryString;
                // 查询报错
                if(_.contains(['1', '2'], obj.errorCode)){
                    results.push({
                        class: 'crm-bquery-search-nodata',
                        value: obj.message,
                        type: 1,
                    });
                    results.push({
                        value: $t('高级查询'),
                        type: 3,
                    })
                    this.searchList = [];
                    this.queryResults = results;
                    cb(results);
                    return;
                }
                // 正常查询
                let list = obj.list;
                if(!list.length) {
                    results.push({
                        value: $t('暂无数据'),
                        type: 1,
                    })
                    this.searchList = [];
                } else {
                    results = results.concat(list);
                    this.searchList = list;
                }
                results.push({
                    value: $t('高级查询'),
                    type: 3,
                })
                this.queryResults = results;
                cb(results);
            })

            // if (!results.length) {
            //     results.push({
            //         value: 'test',
            //         operName: '11',
            //         status: '22',
            //         creditCode: '33',
            //     })
            //     results.push({
            //         type: 1,
            //         value: '暂无数据'
            //     })
            //     results.push({
            //         type: 2,
            //         value: '系统异常'
            //     })
            //     results.push({
            //         type: 3,
            //         value: '高级查询',
            //     })
            // }
            // cb(results);
        },

        async getInitValueObj(obj) {
            const me = this;

            if (this.pluginFns.getInitValueObj) {
                return await this.pluginFns.getInitValueObj(obj, {
                    context: this,
                });
            }

            // 值为空，直接返回无需check
            if(!obj || (_.isString(obj) && !obj) || (_.isObject(obj) && !obj.value)){
                return;
            }

            // 处理数据
            let _valueObj = _.isObject(obj) ? obj : this.formatNameValue(obj);

            // 没有当前工商datasource对应的功能权限，则不查询不点亮
            if (!this.getOneRights(this.bqueryRights, _valueObj.datasource)){
                // 没权限时增加没权限标识
                _valueObj.noRights = true;
                return _valueObj;
            }

            // 有id则直接返回数据
            if (_valueObj.id) {
                return _valueObj;
            }

            // 没有id，需要查询接口
            const res = await this.CheckCustomerByBusinessQuery(_valueObj.value);
            return res;
        },

        setValueObj(obj) {
            if (_.isString(obj)) {
                obj = { value: obj };
            }
            this.valueObj = obj || {};
        },

        setValue(obj) {
            // this.hideError();
            if (_.isString(obj)) {
                obj = { value: obj };
            }
            this.inputValue = obj && obj.value;
            this.setValueObj(obj);
        },

        async passiveSetValue(obj) {
            let me = this;
            if (_.isString(obj)) {
                obj = { value: obj };
            }
            // 主数据为空时更新客户名称
            if (!obj.value) {
                me.setValue(obj);
                me.changeHandle(obj, true);
                return;
            }
            let _valueObj = await this.getInitValueObj(obj)
            me.changeHandle(_valueObj, true);
        },

        getValue() {
            if (this.fieldAttr.is_required && !this.inputValue) {
                this.showError();
            }
            return CRM.util.trim(this.inputValue);
        },

        // 更新当前字段值在model中的data数据
        updateData(obj, notrigger) {
            if (obj.value !== (this.getData() || '')) {
                this.setData(obj.value, null, notrigger, true);
            }
        },

        // 更新当前字段之外额外需要更新的字段（跟当前字段值相关需要更新的字段）
        updateExtraData(obj) {
            let _obj = this.getExtraUpdateData(obj);
            this.model.onlyUpdateValue(_obj);
        },

        // 获取需要额外更新的字段
        getExtraUpdateData(obj) {
            let _obj = {};
            _obj[this.industryExtFieldApiName] = !obj.id ? '' :JSON.stringify({
                datasource: obj.datasource,
                companyId: obj.id
            });
            _obj[this.bizRegNameFieldApiName] = !!obj.id;

            if (this.pluginFns.getExtraUpdateData) {
                _obj = this.pluginFns.getExtraUpdateData(obj, {
                    context: this,
                });
            }
            return _obj;
        },

        validate(name){
            if (!name) {
                this.hideTips();
                this.isRequired() && this.showError();
                return;
            }
            this.hideError();
        },

        destroy() {
            this.$destroy();
        },

        ...base,
        ...api,
        ...action,
        ...util,
    },
    beforeDestroy() {
        this.advanceBquery && this.advanceBquery.destroy && this.advanceBquery.destroy();
        _.each(this.ajaxs, (aj) => {
            aj && aj.abort();
        })
    },
    watch: {
        light(val, oldValue) {
            if (val === oldValue) return;
            val ? this.lightIcon() : this.shutIcon();
        }
    }
  };
</script>


<style lang="less">
.crm-bquery{
    display: flex;
    height: 100%;
    max-height: 32px;
    .crm-bquery-prefix,.crm-bquery-suffix{
        display: flex;
        align-items: center;
    }
    .crm-bquery-prefix-inner, .crm-bquery-suffix-inner{
        display: flex;
        align-items: center;
    }
    .crm-bquery-icon {
        margin-left: 10px;
    }
    .icon-dengbaishi{
		width: 24px;
		height: 24px;
		cursor: pointer;
        background: -webkit-image-set(
            url("~@assets/images/icon-dbs.png") 1x,
            url("~@assets/images/<EMAIL>") 2x
        )
	}
    .icon-business {
        width: 24px;
        height: 24px;
        border: 2px solid #eee;
        border-radius: 50%;
        text-align: center;
        box-sizing: border-box;
        font-weight: 700;
        color: #ccc;
        cursor: pointer;
    }
    .icon-light {
        color : #3487e2;
        border-color : #3487e2;
        svg {
            & > path {
                fill: #3487e2;
            }
        }
    }
}
.crm-bquery-input{
    flex: 1;
    .el-input{
        height: 100%;
    }
}
.unshow-gongshang{
    display: none;
}
.crm-bquery-autocomplete {
    min-width:240px;
    .el-autocomplete-suggestion__wrap{
        &:has(.crm-bquery-search-advance) {
            padding-bottom: 0;
        }
        // padding-bottom: 32px;
    }
    li.highlighted, li:hover{
        .crm-bquery-search-nodata, .crm-bquery-search-error, .crm-bquery-search-advance{
            background: #fff;
        }
    }
    li {
        padding: 0;
        &:has(.crm-bquery-search-advance) {
            position: sticky;
            bottom: 0;
            height: 32px;
        }
    }
    .crm-bquery-search-nodata, .crm-bquery-search-error{
        font-size:14px;
        color:#C1C5CE;
        text-align:center;
        box-sizing: border-box;
        padding: 0 16px;
        width: 100%;
		height: 100px;
        line-height:20px;
        display: flex;
        align-items: center;
        justify-content: space-around;
        white-space: normal;
        word-break: break-word;
    }
    .crm-bquery-search-nodata{
        color:#999;
    }
    .crm-bquery-search-advance{
        // position: sticky;
		// bottom:0;
		width: 100%;
		height:32px;
		line-height: 32px;
		box-sizing: border-box;
		border-top: #ddd solid 1px;
        background: #fff;
        padding:0 10px;
        border-radius: 0 0 4px 4px;
		a[data-from="advance"] {
			float: left;
		}
		a[data-from="supply"] {
			float: right;
		}
		.cwidth{
			width: 49%;
			overflow: hidden;
			text-overflow: ellipsis;
		}
    }
    .crm-bquery-search-item{
        line-height: normal;
        padding: 10px 30px 10px 10px;
        border-bottom: 1px dotted #eee;
        position: relative;
        white-space: normal;
        .crm-bquery-search-item-name{
			line-height: 17px;
			font-size: 13px;
		}
		.crm-bquery-search-item-desc{
			margin-top: 6px;
			line-height: 16px;
			font-size: 12px;
			color: #999;
			em{
				margin: 0 10px;
				color : #eee;
			}
		}
		.crm-bquery-search-item-icon{
			position: absolute;
			right: 4px;
			top: 0;
			width: 24px;
			height: 100%;
			display: flex;
			align-items: center;
		}
        .text-highlight{
            color: #ff8837;
        }
    }
  }
</style>
