<template>
  <div class="star-field-singleedit" ref="reteFieldStart" @click="handleClick">
    <div class="edit-innder" :style="innerStyle" @click.stop>
      <div class="view-wrap">
        <div class="start-action-nfield">
          <div class="view-box">
            <div class="view-box-name" :class="{'required': isRequired}">
              <fx-tooltip placement="top">
                <div slot="content"> {{dLabel}}</div>
                <span class="view-box-name-tit">{{dLabel}}</span>
              </fx-tooltip>
            </div>
            <div class="view-box-content">
              <Star :readonly="false" :dValue="dValue" :ratescore="ratescore" @changeValue="handleChangeValue" :style="starStyle" />
              <span v-if="isRequired && showRequired" class="fm-error crm-ico-error">{{ $t('请填写') + dLabel }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="tit-wrap">
        <span class="tit-left fx-icon-close" @click="handleClose"></span>
        <fx-tooltip placement="top">
          <div slot="content"> {{ $t('保存') }} </div>
          <span class="tit-right fx-icon-ok-2 main" @click="handleSave"></span>
        </fx-tooltip>
      </div>
    </div>
  </div>
</template>
<script>
import Star from "../star.vue";
const util = FS.crmUtil;

export default {
  name: "m-single-edit",
  components: {
    Star,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dLabel: {
      type: String,
      default: '',
    },
    dValue: {
      type: Number,
      default: 0,
    },
    ratescore: {
      type: Number,
      default: 10,
    },
    starStyle: {
      type: Object,
      default: () => ({}),
    },
    isRequired: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      innerStyle: {},
      value: '',
      showRequired: false,
    }
  },
  methods: {
    getPosition() {
      const { top, left, height } = this.$parent.$el.getBoundingClientRect();
      const pLeft = left - 104;
      let $top = 8;
      if(height < 32) {
				$top = (32 - height) / 2 + 8;
      }
      this.innerStyle = {
        ...this.innerStyle,
        left: `${pLeft}px`,
        top: `${top - $top}px`,
      };
    },
    handleChangeValue(value) {
      this.value = value;
      this.showRequired = false;
      this.$emit("changeValue", value);
    },
    handleSave() {
      if (this.isRequired && !this.value) {
        this.showRequired = true;
        return;
      }
      this.$emit("save", this.value);
    },
    handleClose() {
      this.$emit("close");
    },
    handleClick() {
      this.handleClose();
    },
    initData() {
      this.value = this.dValue;
    },
  },
  mounted() {
    this.initData();
    this.getPosition();
  },
  beforeDestroy() {
    console.log("beforeDestroy---");
  },
};

</script>
<style lang="less" scoped>
.star-field-singleedit {
  z-index: 505;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  .edit-innder {
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    width: 400px;
    position: absolute;
    border-radius: 3px;
    box-shadow: 0 0 6px 0 rgba(0, 0, 0, .15);
    background-color: var(--color-neutrals01);
    .view-wrap {
      .start-action-nfield {
        padding: 8px 0 8px 8px;
        max-height: 250px;
      }
      .view-box {
        display: flex;
        align-items: baseline;
        flex-wrap: nowrap;
        .view-box-name {
          width: 88px;
          min-width: 88px;
          line-height: 1.2em;
          margin: 0 8px 0 0;
          &::before {
            content: '*';
            font-size: 14px;
            margin: 0 4px 0 0;
            transform: translateY(3px);
            visibility: hidden;
          }
          &.required {
            &::before {
              visibility: visible;
              color: red;
            }
          }
          .view-box-name-tit {
            word-break: break-word;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
        .view-box-content {
          width: 100%;
          display: flex;
          align-items: baseline;
          flex-direction: column;
          .crm-ico-error {
            padding-left: 0px;
            color: rgb(245, 113, 95);
            display: block;
          }
        }
      }
    }
    .tit-wrap {
      display: flex;
      align-items: center;
      width: 72px;
      span {
        height: 24px;
        line-height: 24px;
        width: 24px;
        text-align: center;
        margin-left: 8px;
        border-radius: 4px;
        cursor: pointer;
        background-color: var(--color-neutrals07);
        color: var(--color-neutrals01);
        &::before {
          color: var(--color-neutrals01);
        }
      }
      .main {
        background-color: var(--color-primary06);
      }
    }
  }
}
</style>


