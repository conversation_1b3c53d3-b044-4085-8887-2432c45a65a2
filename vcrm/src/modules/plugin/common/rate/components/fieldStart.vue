<template>
  <div class="rete-field-start">
    <div class="rete-star-warpper" ref="starWrapper" @mouseenter.stop="handleMouseEnter" @mouseleave.stop="handleMouseLeave">
      <Star :readonly="readonly" :dValue="dValue" :ratescore="ratescore" :starStyle="starStyle" />
      <div class="field-start-hoveredit" @click.stop="handleEdit" v-show="isShowEdit && hoverEdit" :style="style">
        <span :title="$t('编辑')" class="star-ico-edit el-icon-edit-outline"></span>
      </div>
    </div>
    <single-edit v-if="visible" :dLabel="dLabel" :dValue="dValue" :ratescore="ratescore" :starStyle="starStyle" :isRequired="isRequired" :innerStyle="innerStyle" @changeValue="handleChangeValue" @save="handleSave" @close="handleClose"></single-edit>
  </div>
</template>
<script>
import Star from "../star.vue";
import SingleEdit from "./singleEdit.vue";
const util = FS.crmUtil;

export default {
  name: "m-star-edit",
  components: {
    Star,
    SingleEdit,
  },
  props: {
    field: {
      type: Object,
      required: true,
    },
    data: {
      type: Object,
      required: true,
    },
    readonly: {
      type: Boolean,
      value: false,
    },
    dValue: {
      type: Number,
      value: 0,
    },
    ratescore: {
      type: Number,
      value: 10,
    },
    dLabel: {
      type: String,
      value: '',
    },
    isShowEdit: {
      type: Boolean,
      value: false,
    },
    style: {
      type: Object,
      value: {},
      default: () => ({
        position: 'absolute',
        top: 0,
        right: 0,
        width: '18px',
        zIndex: 506,
      }),
    },
    starStyle: {
      type: Object,
      value: {},
      default: () => ({}),
    },
  },
  watch: {},
  data() {
    return {
      visible: false,
      hoverEdit: false,
      innerStyle: {},
      isRequired: false,
    };
  },
  methods: {
    handleEdit() {
      this.fetchFields(this.data, this.field).then((fields)=> {  
        this.visible = true;
        this.hoverEdit = false;
        if (fields && fields.length > 0) {
          this.isRequired = fields.find(item => item.api_name === this.field.api_name).is_required;
        }
      }).catch((res)=> {
        console.log("err:", res);
        var msg = res.Result.FailureMessage;
        if(FxUI && FxUI.Message) {
          FxUI.Message({
            isMiddler: true,
            duration: 1500,
            message: msg,
            type: 'warning'
          })
        } else {
          util.alert(msg);
        }
      });
    },
    handleMouseEnter() {
      this.hoverEdit = true;
    },
    handleMouseLeave() {
      this.hoverEdit = false;
    },
    handleClose() {
      this.visible = false;
    },
    handleSave(value) {
      this.visible = false;
      this.$emit("onSave", value);
    },
    fetchFields(obj, field) {
      return new Promise((resolve, reject) => {
        if(obj.fieldList) return resolve(obj.fieldList);
        util.waiting('  ');
        util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/' + obj.object_describe_api_name + '/controller/QuickEditLayout',
            data: {
                dataId: obj._id,
                fieldApiName: field.api_name
            },
            success: function(res) {
                if(res.Result.StatusCode === 0) {
                    resolve(res.Value.fields);
                } else {
                    reject(res);
                }
            },
            complete: function() {
                util.waiting(false);
            }
        }, {
            errorAlertModel: 1
        })
      })
    },
  },
  created() {},
  mounted() {
    console.log("mounted---");
  },
  beforeDestroy() {}
};
</script>
<style lang="less" scoped>
.rete-field-start {
  position: relative;
}
.field-start-hoveredit {
  position: absolute;
  top: 2px;
  right: -14px;
  width: 18px;
  .star-ico-edit.el-icon-edit-outline {
    padding: 2px;
    height: 18px;
    font-size: 18px;
    font-weight: 200;
    color: #7B7E86;
    border-radius: 3px;
    background: var(--color-special01);
    cursor: pointer;
    &:hover {
      color: var(--color-primary06);
    }
  }
}
</style>
