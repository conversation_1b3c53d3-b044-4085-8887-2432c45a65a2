import Base from 'plugin_base';
import Slider from './class/slider';
import Calculate from './class/caculate';
import { filter, take } from 'rxjs/operators';
import { BehaviorSubject, Subject } from 'rxjs';
import { MEMBER_ARRAY, MEMBER_MAP, SHOW_SLIDER_ERROR_CODE } from './etc/const';
// import init, { greet } from './etc/hello_wasm';

//todo 需要处理一下loyalty_detail和 loyaltyMap 之间的关系 可以考虑合并
class Point extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.slider = null;
        this.masterObjApiName = pluginParam.describe.objectApiName;
        const have_partner = CRM._cache.sfa_loyalty_plugin_partner_switch_apply_SalesOrderObj && !this.isDhtApp();
        this.memberArr = have_partner ? MEMBER_ARRAY : MEMBER_ARRAY.slice(0, 1);
    }

    destroy() {
        this.destroyStreamAndSubscribe();
        this.slider?.destroy();
        this.slider = null;
    }

    getHook() {
        const mdEvents = ['edit', 'batchAdd', 'del', 'copy'].map(item => `md.${item}.end`);
        const hooks = [
            {
                event: 'form.submit.before',
                functional: this.formSubmitBefore.bind(this),
            },
            {
                event: 'statistics.showPoint.start',
                functional: this.showSlider.bind(this),
            },
            {
                event: 'form.render.before',
                functional: this.formRenderBefore.bind(this),
            },
            {
                event: 'form.render.after',
                functional: this.formRenderAfter.bind(this),
            },
            {
                event: 'form.dataChange.after',
                functional: this.masterDataChange.bind(this),
            },
        ];
        return hooks.concat(mdEvents.map(item => ({ event: item, functional: this.mdDataChange.bind(this) })));
    }

    /*************  hook  *************/

    // 提交前检查数据
    async formSubmitBefore(plugin, param) {
        const is_user_use_points = this.isUserUsePoints();

        if (!is_user_use_points) {
            return;
        }

        const masterData = param.dataGetter.getMasterData();

        let calculateData = {};

        try {
            calculateData = await this.getCalculateData(masterData);
        } catch (err) {
            calculateData.error = true;
        }

        if (calculateData.error || calculateData?.status?.error) {
            param.end();
            plugin.skipPlugin();
            if (SHOW_SLIDER_ERROR_CODE.has(calculateData?.status?.errorCode?.toString())) {
                this?.slider?.show('submit_error');
            }
            return;
        }

        const is_calculate_data_changed = this.isCalculateDataChanged(calculateData.data, masterData);

        if (is_calculate_data_changed) {
            const value = this.data$.value;
            this.data$.next({
                ...value,
                loyalty_amount: calculateData?.data?.loyalty_amount || 0,
                from: 'plugin',
                last_data: value,
            });
            this.isDistinctWithSubmit$.next({
                dataChange: true,
                showAlert: Number(value.loyalty_amount) - Number(calculateData?.data?.loyalty_amount) < 0,
            });
            // 结算金额变化，积分抵扣额有变化
            this.runPlugin('point.submit.showError', { info: $t('sfa.plugin.usepoint.amount_changed') });
            param.end();
            plugin.skipPlugin();
        }
    }

    initSlider() {
        this.slider = new Slider({
            $wrapper: $('body')[0],
            calculate: this.calculate,
            data: {
                isEdit: this.isEdit,
                account_id_label: this.account_id_label,
                partner_id_label: this.partner_id_label,
            },
            stream: {
                data$: this.data$,
                isDistinctWithSubmit$: this.isDistinctWithSubmit$,
            },
            functions: {
                getParam: this.getParam.bind(this),
                convertArrayToMapByMember: this.convertArrayToMapByMember.bind(this),
            },
        });
    }

    showSlider() {
        if (this.slider) {
            return this.slider.show();
        } else {
            this.initSlider();
            this.slider.show();
        }
    }

    formRenderBefore(plugin, param) {
        this.initData(plugin, param);
        this.updateMasterData(plugin, param);
    }

    async formRenderAfter(plugin, param) {
        this.getFieldsLabel(param);
        await this.#initStream(plugin, param);
        this.#initSubscribe();
        this.initSlider();
    }

    // 需要bizAPI的
    getParam() {
        return (
            this.pluginService.api.pluginServiceFactory({
                pluginApiName: 'point',
            }) || {}
        );
    }

    async masterDataChange(plugin, param) {
        const { changeData, dataGetter } = param;
        const changeDataKeys = Object.keys(changeData);
        const masterData = dataGetter.getMasterData();
        const account_id_changed = changeDataKeys.includes('account_id');
        const partner_id_changed = changeDataKeys.includes('partner_id');
        if (!account_id_changed && !partner_id_changed) {
            return;
        }
        const newMemberMap = this.resetMemberMap(masterData);
        const updatedData = {
            loyalty_detail: this.calculate.getLoyaltyData(newMemberMap),
            loyalty_amount: 0,
        };
        const last_data = this.data$.value;
        this.data$.next({
            ...last_data,
            memberMap: newMemberMap,
            account_id: masterData?.account_id,
            partner_id: masterData?.partner_id,
            order_amount: masterData?.order_amount,
            loyalty_amount: 0,
            point: 0,
            member_amount: 0,
            from: 'plugin',
        });

        this.calculate.mainSubject.next({
            type: 'calculate',
            from: 'point_plugin',
            data: {
                member_data: this.data$.value.memberMap,
                masterData,
                masterObjApiName: this.masterObjApiName,
            },
        });

        param.dataUpdater?.updateMaster(updatedData);
    }

    mdDataChange(plugin, param) {
        // todo 我只能认为order_amount变化了会影响内部 之后研究下怎么处理这块
        const { dataGetter } = param;
        const masterData = dataGetter.getMasterData();
        const is_order_amount_change = this.isOrderAmountChanged(masterData);
        if (!is_order_amount_change) return;

        const data = this.data$.value;
        this.data$.next({
            ...data,
            order_amount: masterData?.order_amount,
            from: 'plugin',
        });
    }

    /************  tool   **************/

    isDhtApp() {
        const appId = CRM.util.getPageCurAppId();
        // 订货通和配件商城
        return ['FSAID_11490c84', 'FSAID_11491427', 'FSAID_11491512'].includes(appId);  
    }

    getFieldsLabel(param) {
        const { dataGetter } = param;
        const { fields } = dataGetter.getDescribe();
        // 客户名称
        this.account_id_label = fields?.account_id?.label || $t('sfa.plugin.usepoint.account_name');
        // 合作伙伴
        this.partner_id_label = fields?.partner_id?.label || $t('sfa.plugin.usepoint.partner_name');
    }

    updateMasterData(plugin, param) {
        const { dataGetter, dataUpdater } = param;
        const masterData = dataGetter.getMasterData();
        if (this.isDraft || this.isClone) {
            dataUpdater?.updateMaster({
                ...masterData,
                loyalty_amount: 0,
                loyalty_detail: [],
            });
            this.triggerCal(param, masterData, { loyalty_amount: 0 });
        }
    }

    isUserUsePoints(masterData) {
        const data = this.data$.value;
        if (Array.from(data.memberMap.values()).every(item => !item.member_id || !item.use_points)) {
            return false;
        }
        return true;
    }

    compareNumber(a = 0, b = 0) {
        return Number(a) === Number(b);
    }

    isCalculateDataChanged(calculateData) {
        const data = this.data$.value;
        if (
            this.pointsDetailsHaveBeenChanged(calculateData.pointsDetails, data.memberMap) ||
            !this.compareNumber(data.point, calculateData.point) ||
            !this.compareNumber(data.loyalty_amount, calculateData.loyalty_amount) ||
            !this.compareNumber(data.member_amount, calculateData.member_amount)
        ) {
            return true;
        }
        return false;
    }

    pointsDetailsHaveBeenChanged(pointsDetails = [], memberMap) {
        /**
         * pointsDetails
         */
        const memberIdMaps = new Map();
        memberMap.forEach(item => {
            memberIdMaps.set(item.member_id, item);
        });
        return pointsDetails.some(item => {
            const { cashExchange, value, memberId } = item;
            const member = memberIdMaps.get(memberId) || {};
            const { cash_exchange, use_points } = member;
            return !this.compareNumber(cashExchange, cash_exchange) || !this.compareNumber(value, use_points);
        });
    }

    initData(plugin, param) {
        this.isEdit = param.formType === 'edit';
        this.isDraft = param.formType === 'draft';
        this.isClone = param.formType === 'clone';
    }

    resetMemberMap(masterData = {}) {
        const { memberMap } = this.data$.value;
        if (memberMap.has('account')) {
            const account = this.resetMemberData(memberMap.get('account'));
            memberMap.set('account', account);
        }
        if (memberMap.has('partner')) {
            const partner = this.resetMemberData(memberMap.get('partner'));
            memberMap.set('partner', partner);
        }
        return memberMap;
    }

    async handleSubmit(data) {
        const { point, loyalty_amount, loyalty_detail } = data;
        const param = this.getParam();
        const newMasterData = {
            loyalty_detail,
            loyalty_amount: loyalty_amount,
        };
        const masterData = param.dataGetter?.getMasterData() || {};

        function end() {
            param.end();
        }

        this.triggerCal(param, masterData, newMasterData, end);
    }

    triggerCal(param, masterData, changedMasterData, end) {
        param.triggerCal
            .call(this, {
                showLoading: true,
                changeFields: ['loyalty_amount'],
                calType: 'masterEdit',
                masterData: {
                    ...masterData,
                    ...changedMasterData,
                },
                objApiName: param.masterObjApiName,
            })
            .then(res => {
                if (res.Result.StatusCode === 0) {
                    const calculateResult = res.Value.calculateResult;
                    const SalesOrderObj = calculateResult.SalesOrderObj[0];
                    const calculatedMasterData = {
                        ...changedMasterData,
                        ...SalesOrderObj,
                    };
                    param.dataUpdater?.updateMaster(calculatedMasterData);
                    this.runPlugin('point.match.end', {
                        param: param,
                        calculatedMasterData,
                    });
                    typeof end === 'function' && end();
                }
            });
    }

    async getCalculateData(masterData) {
        const mainSubject = this.calculate.mainSubject;

        mainSubject.next({
            type: 'calculate',
            from: 'form_submit',
            data: {
                member_data: this.data$.value.memberMap,
                masterData,
                masterObjApiName: this.masterObjApiName,
            },
        });
        const calculateData = await this.calculate.getValueFromSubject(this.calculate.calculate$.pipe(take(1)));

        return calculateData;
    }

    submit(data) {
        this.runPlugin('point.match.end', data);
    }

    isMemberDataChanged(masterData = {}) {
        const { account_id: cache_account_id, partner_id: cache_partner_id } = this.data$.value;
        const { partner_id = '', account_id = '' } = masterData;
        return {
            account_id_changed: cache_account_id !== account_id,
            partner_id_changed: cache_partner_id !== partner_id,
        };
    }

    isOrderAmountChanged(masterData = {}) {
        const cache_order_amount = this.data$.value.order_amount;
        const { order_amount } = masterData;
        return cache_order_amount !== order_amount;
    }

    convertArrayToMapByMember(array) {
        const mapData = new Map();
        array.forEach(item => {
            mapData.set(item.member, this.cloneDeep(item));
        });
        return mapData;
    }

    cloneDeep(data) {
        return CRM.util.deepClone(data);
    }

    resetMemberData(item = {}) {
        const member = item.member;
        item = {
            show_label: MEMBER_MAP[member],
            // 是否使用当前会员
            use: true,
            // 会员的sub_type: account 会员, partner 合作伙伴会员
            member,
            // 会员id
            member_id: '',
            // 会员label
            member_label: '',
            // 1积分=多少金额
            cash_exchange: 0,
            // 最多可用积分
            consumer_points: 0,
            // 此次使用积分
            use_points: null,
            // 此次使用的金额
            amount: 0,
        };
        return item;
    }

    async initMemberMap(loyalty_detail) {
        const temp = new Map();
        if (loyalty_detail?.length) {
            await this.initMemberMapByData(loyalty_detail, temp);
        } else {
            this.memberArr.forEach(member => {
                temp.set(member, this.resetMemberData({ member }));
            });
        }
        return temp;
    }

    initMemberMapByData(loyalty_detail, temp) {
        return Promise.all(
            loyalty_detail.map(detail => {
                const { memberId, value } = detail;
                const search_query_info = {
                    limit: 20,
                    offset: 0,
                    filters: [
                        {
                            field_name: '_id',
                            field_values: [memberId],
                            operator: 'EQ',
                        },
                    ],
                };
                const param = {
                    include_layout: false,
                    include_describe: false,
                    search_query_info: JSON.stringify(search_query_info),
                };
                return CRM.util.fetchObjRelatedList('LoyaltyMemberObj', param, false).then(res => {
                    const member = res.dataList[0];
                    member.value = value;
                    member.amount__t = detail.amount;
                    member.cashExchange__t = detail.cashExchange;
                    member.operate_id = detail.operateId;
                    return member;
                });
            })
        ).then(list => {
            const resMap = new Map();
            list.forEach(item => {
                const { member_sub_type } = item;
                resMap.set(member_sub_type, item);
            });
            ['account', 'partner'].forEach(member => {
                if (resMap.has(member)) {
                    const member_data = resMap.get(member) || {};
                    const { member_sub_type, _id, consumer_points, name, member_name } = member_data;
                    const { value, amount__t, cashExchange__t, operate_id } = member_data;
                    temp.set(member, {
                        show_label: MEMBER_MAP[member_sub_type],
                        member: member_sub_type,
                        member_label: member_name || name,
                        use: true,
                        member_id: _id,
                        cash_exchange: cashExchange__t,
                        consumer_points,
                        use_points: value,
                        amount: amount__t,
                        operate_id,
                    });
                } else {
                    if (this.memberArr.includes(member)) {
                        temp.set(member, this.resetMemberData({ member }));
                    }
                }
            });
        });
    }

    destroyStreamAndSubscribe() {
        this.fromPluginSubscription?.unsubscribe();
        this.distinctDataSubscription?.unsubscribe();
        this.data$.complete();
        this.isDistinctWithSubmit$.complete();
        this.distinctData$ = null;
    }

    /** 私有方法 **/
    async #initStream(plugin, param) {
        this.#initCalculateStream();
        await this.#initDataStream(param);
    }

    async #initDataStream(param) {
        const masterData = param.dataGetter?.getMasterData() || {};
        const loyalty_detail =
            typeof masterData?.loyalty_detail === 'string'
                ? JSON.parse(masterData.loyalty_detail)
                : masterData?.loyalty_detail || [];
        const { account_id, partner_id, loyalty_amount = 0, order_amount = 0 } = masterData;
        const memberMap = await this.initMemberMap(loyalty_detail);
        this.data$ = new BehaviorSubject({
            memberMap,
            account_id,
            partner_id,
            loyalty_amount,
            point: loyalty_detail.reduce((prev, curr) => prev + Number(curr.value || 0), 0),
            order_amount,
            member_amount: loyalty_detail.reduce((prev, curr) => prev + Number(curr.amount || 0), 0),
            from: 'init',
        });
        /** todo 之后需要处理一下 主从对象的变化和提交产生的变化应该会影响到每次提交的数据是否发生改变
         *  这里的distinctUntilChanged 需要判断这次数据和已经计算过并提交到外部的数据是否一致
         *  过滤条件需要重写
         */
        this.distinctData$ = this.data$.pipe(
            filter(data => data.from === 'submit')
            // distinctUntilChanged((prev, curr) => {
            //     return (
            //         //todo 这块有可能出现换了一种积分配置方式 但是结果是一样的情况 之后有空再处理
            //         prev.point === curr.point &&
            //         prev.loyalty_amount === curr.loyalty_amount &&
            //         prev.member_amount === curr.member_amount
            //     );
            // })
        );
        this.isDistinctWithSubmit$ = new Subject(false);
    }

    #initCalculateStream() {
        this.calculate = new Calculate();
        this.fromPlugin$ = this.calculate.calculate$.pipe(filter(item => item.from === 'point_plugin'));
    }

    #initSubscribe() {
        this.fromPluginSubscription = this.fromPlugin$.subscribe(data => {
            this.#triggerStatisticsAction(data);
        });
        this.distinctDataSubscription = this.distinctData$.subscribe(data => {
            const layout = data.layout;
            const loyalty_amount = data.loyalty_amount;
            const last_data = this.data$.value;
            const memberMap = last_data.memberMap;
            layout.forEach((item, index) => {
                memberMap.set(item.member, item);
            });
            this.handleSubmit(data);
        });
    }

    #triggerStatisticsAction(value) {
        console.log('wzTest 外部变化触发计算');
    }

    /**   暂时不需要  */
    compUpdateBefore(plugin, data) {
        //todo 下边判断是否改变的方法之后应该统一使用distinctUntilChanged
        const { masterData } = data;
        const order_amount_changed = this.isOrderAmountChanged(masterData),
            { account_id_changed, partner_id_changed } = this.isMemberDataChanged(masterData);

        if (!order_amount_changed && !account_id_changed && !partner_id_changed) {
            return data.statisticsData;
        }

        this.resetPropsData(masterData, account_id_changed, partner_id_changed);

        this.calculate.mainSubject.next({
            type: 'calculate',
            from: 'point_plugin',
            data: {
                member_data: this.data$.value.memberMap,
                masterData,
                masterObjApiName: this.masterObjApiName,
            },
        });
        return data.statisticsData;
    }
}

export default Point;
