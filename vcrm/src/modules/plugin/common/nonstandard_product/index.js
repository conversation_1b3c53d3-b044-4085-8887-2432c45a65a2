/**
 * @desc: 非标产品
 * @author: wangshaoh
 */
import Base from 'plugin_base'
import PPM from 'plugin_public_methods'

export default class NonStandardProduct extends Base {

    constructor(pluginService, pluginParam) {
        super(...arguments)
    }


    // 从对象渲染之前，格式化自定义对象属性数据
    _mdRenderBefore(plugin, param) {
        let mdApiName = param.objApiName;
        // 预置对象，server 下发添加非标产品按钮，检查布局中是否有添加非标产品按钮
        if(!mdApiName.includes('__c')) {
            let layoutList = param.dataGetter.getDescribeLayout().detailObjectList;
            let addNonProductBtn = layoutList.find(item => item.objectApiName === mdApiName)?.layoutList[0]?.detail_layout?.buttons.find(item => item.api_name === 'AddNonStandardProduct_button_default');
            if(!addNonProductBtn) return;
        };
        let addBtn = [{
            label: this.i18n('sfa-crm-nonstandard-product-add'), // 添加非标产品
            action: 'addFromNonStandardProduct',
            callBack: this.addFromNonStandardProduct.bind(this)
        }];
        return {
            __execResult:{
                 buttons:{  // 处理从xx添加入口
                     add: addBtn,
                     del: ['Add_Non_Standard_Product']

                 }
            },
            __mergeDataType:{
                array: 'concat'
            }
        }
    }

    // 获取产品id的where条件
    getProductIdWheres(param) {
        let mdApiName = param.objectApiName;
        let {product_id} = this.getAllFields(mdApiName);
        let productIdDes = param.dataGetter.getDescribe(mdApiName);
        this._targetApiName = productIdDes.fields[product_id]?.target_related_list_name;
        return productIdDes.fields[product_id]?.wheres;
    }

    // 获取非标产品选择器参数
    getPickParam(param) {
        let me = this;
        let mdApiName = param.objectApiName;
        let {form_account_id} = this.getPluginFields('price-service') || {};

        let masterData = param.dataGetter.getMasterData();
        let attributePlugin = this.getPluginFields('attribute');
        let productIdWheres = this.getProductIdWheres(param);
        let res = {
            // filterIds: filterIds, //过滤掉已选数据
            api_name: 'ProductObj',
            apiname: 'ProductObj',
            fieldName: "product_id",
            isMultiple: true,
            isNet: false,
            isSupportCopy: true,
            isSupportCount: true,
            master_data: masterData,
            object_data: masterData,
            source_api_name: mdApiName,
            _renderAttributeTable: !!attributePlugin,
            // target_related_list_name:'',
            beforeRequest: function (rq) {
                rq.search_query_info = rq.search_query_info || {};
                let sq = JSON.parse(rq.search_query_info);
                sq.filters.push({
                    field_name: "product_type",
                    field_values: ["non_standard"],
                    operator: "EQ",
                });
                if(productIdWheres){
                    sq.wheres = productIdWheres;
                }
                rq.search_query_info = JSON.stringify(sq);
                rq.object_data = rq.object_data || {};
                rq.object_data.not_filter_non_product_flag = true; // 显示非标产品
                if(form_account_id) rq.object_data.account_id = rq.account_id = masterData[form_account_id];
                rq.master_data = masterData;
                rq.associated_object_field_related_list_name = me._targetApiName;
                return rq;
            }
        };

        return res;
    }

    // 获取非标产品
    async fetchNonStandardProduct(param) {
        let productIdWheres = this.getProductIdWheres(param);
        let masterData = param.dataGetter.getMasterData();
        let {form_account_id} = this.getPluginFields('price-service') || {};
        let res = await CRM.util.fetchObjRelatedList('ProductObj', {
            associated_object_describe_api_name: 'ProductObj',
            search_query_info: JSON.stringify({
                limit: 2000,
                offset: 0,
                filters: [
                    {
                        field_name: "product_type",
                        field_values: ["non_standard"],
                        operator: "EQ",
                    }
                ],
                wheres: productIdWheres,
                orders: [
                    {
                        fieldName: 'last_modified_time',
                        isAsc: false
                    }
                ]
            }),
            extractExtendInfo: true,
            ignore_scene_record_type: false,
            include_describe: false,
            serializeEmpty: false,
            master_data: masterData,
            object_data: {
                not_filter_non_product_flag: true,
                account_id: masterData[form_account_id],
            },
            associated_object_field_related_list_name: this._targetApiName,
            account_id: masterData[form_account_id],


        })
        return res?.dataList || [];
    }

    // 判断非标产品是否存在属性
    _hasAttr(data){
        return data.attribute?.length > 0 || data.non_standard_attr?.length > 0;
    }

    // 添加非标产品按钮
    async addFromNonStandardProduct(data, obj) {
        let param = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'nonstandard_product'
        });
        let priceServiceFields = this.getPluginFields('price-service');
        if(priceServiceFields) {
            let {
                form_account_id
            } = priceServiceFields;
            let masterData = param.dataGetter.getMasterData();
            if (!masterData[form_account_id]) {
                this.alert(this.i18n('请先选择客户'));
                param.end();
                return
            }
        }

        // 设置非标产品选择器参数
        param = Object.assign(param, {
            objectApiName: obj.objectApiName,
            objApiName: obj.objectApiName,
            recordType: obj.recordType
        });

        // 获取非标产品
        let nonStandardProduct = await this.fetchNonStandardProduct(param);
        if(!nonStandardProduct?.length){
            this.alert(this.i18n('sfa.crm.no_nonstandard_product')); // 当前没有可用的非标产品！请确认产品信息、可售范围、数据范围的过滤条件等
            param.end();
            return;
        }
        if(nonStandardProduct?.length === 1 && !this._hasAttr(nonStandardProduct[0])) {
            await this.addNonProduct(nonStandardProduct, param);
            return;
        }

        // 获取非标产品选择器参数
        let pikParam = this.getPickParam(param);
        // 渲染非标产品历史表
        this._renderHistoryTable(pikParam, param, obj)
    }

    async _renderHistoryTable(pikParam, param, obj) {
        let me = this;
        let mdApiName = obj.objectApiName;
        param.objApiName = mdApiName;
        let PickSelf = await this.pluginService.api.import('crm-modules/components/pickselfobject/pickselfobject');
        this.pickComp = new PickSelf();
        this.pickComp.on('select', async function (list, remList) {
            me.showLoading();
            Array.isArray(list) || (list = [list]);
            me.addNonProduct(list, param);
            me.hideLoading();
            me.pickComp = null;
        });
        this.pickComp.render(pikParam);
    }

    // 添加非标产品
    async addNonProduct(list, param) {
        let lookupField = {
            api_name: 'product_id',
            target_api_name: 'ProductObj',
            target_related_list_name: 'salesorderproduct_product_list',
            __action_from: 'nonstandard_product'
        };
        let mdApiName = param.objectApiName;
        let masterData = param.dataGetter.getMasterData();
        let {product_id, detail_type} = this.getAllFields(mdApiName);
        let recordType = param.recordType;
        var baseData = param.getRowBasicData(mdApiName, recordType);
        let addDatas = list.map(item => {
            let rowId = PPM.uniqueCode();
            return Object.assign({}, baseData, {
                rowId,
                [product_id]: item._id,
                [product_id + '__r']: item.name,
                [detail_type]: 'non_standard'
            });
        });
        param.dataUpdater.add(addDatas);
        param.lookupField = lookupField;
        let dataIndex = addDatas.map(item => item.rowId);
        param = Object.assign(param, {
            addDatas: addDatas,
            lookupDatas: list,
            master_data: masterData,
            formType: "add",
            dataIndex,
            newDataIndexs: dataIndex,
            __action_from: 'nonstandard_product',
        });
        await this.runPlugin('md.batchAdd.after', param);
        let r = await param.triggerCalAndUIEvent({
            operateType: 'mdAdd',
            dataIndex,
            objApiName: mdApiName,
            masterData: masterData,
            // details: {},
        });
        await this.runPlugin('md.batchAdd.end', param);
        this.setFieldsReadOnly(addDatas, param);
        param.end();
    }

    // 开了 bom 和属性，价目表字段只读；
    disabledPriceBook(){
        return this.getPluginFields('bom') && this.getPluginFields('attribute');
    }

    // 设置非标产品的产品字段为可编辑，价目表字段设为只读
    setFieldsReadOnly(data, param, from = '') {
        let { objApiName, recordType} = param;
        let {detail_type, product_id} = this.getAllFields(objApiName);
        let {price_book_product_id, price_book_id} = this.getPluginFields('price-service') || {};
        let isOpenPriceList = this.getConfig('openPriceList');
        data.forEach(item => {
            if(isOpenPriceList){
                let isEdit = item[detail_type] === 'non_standard' || !item[product_id];
                param.dataUpdater.setReadOnly({
                    fieldName: [product_id],
                    dataIndex: item.rowId,
                    objApiName,
                    recordType,
                    status: !isEdit,
                })
                param.dataUpdater.setReadOnly({
                    fieldName: [price_book_product_id, price_book_id],
                    dataIndex: item.rowId,
                    objApiName,
                    recordType,
                    status: item[detail_type] && item[detail_type] === 'non_standard' || this.disabledPriceBook(),
                })
            }
        })
    }

    // 渲染后设置非标产品为可编辑
    _mdRenderAfter(plugin, param) {
        let mdApiName = param.objectApiName;
        let mdData = param.dataGetter.getDetail(mdApiName);
        this.setFieldsReadOnly(mdData, param);
    }

    // 缓存产品id和产品名称
    cacheProductId(param) {
        let mdApiName = param.objectApiName || param.objApiName;
        let detailData = param.dataGetter.getDetail(mdApiName);
        let {product_id} = this.getAllFields(mdApiName);
        let changeRows = detailData.filter(item => param.dataIndex.includes(item.rowId));
        changeRows.forEach(item => {
            item.__oldProductId = item[product_id];
            item.__oldProductName = item[product_id + '__r'];
        })
    }

    // 编辑产品字段，修改选产品组件，效果和从产品添加入口一样；
    _editBefore(plugin, param) {
        let mdApiName = param.objectApiName;
        let {product_id} = this.getAllFields(mdApiName);
        if(param.fieldName !== product_id) return;
        this.cacheProductId(param);

        let modulePath = 'crm-modules/components/pickselfobject_classification/pickselfobject_classification';
        let bomPlugin = this.getPluginFields('bom');
        if(bomPlugin) {
            modulePath = 'crm-modules/components/pickselfobject_cpq/pickselfobject_cpq';
        }
        let attributePlugin = this.getPluginFields('attribute');
        return {
            __execResult: {
                modulePath,
                _renderAttributeTable: !!attributePlugin,
                skipSearch: bomPlugin || attributePlugin,   // 开了 cpq 或者属性，禁止产品字段模糊搜索，因为不支持 bom 和 属性
            },
            __mergeDataType: {
                array: 'concat'
            }
        }
    }

    // 编辑产品后，更新非标产品类型
    updateDetailType(changeRows, mdApiName, param) {
        let {detail_type, detail_type_changed} = this.getAllFields(mdApiName);
        changeRows.forEach((item, index) => {
            let rowId = item.rowId;
            let lookupData = param.lookupData;
            lookupData = Array.isArray(lookupData) ? lookupData : [lookupData];
            let lookupDataItem = lookupData[index];
            param.dataUpdater.updateDetail(mdApiName, rowId, {
                [detail_type]: lookupDataItem ? lookupDataItem.product_type || 'standard' : '',
                [detail_type_changed]: true,
                __fromNonStandardProduct: true,
            });
        })
    }

    _getUnit(data, unitField){
        return data.hasOwnProperty(unitField + '__v') ? data[unitField + "__v"] : data[unitField];
    }

    // 检查更改后的产品单位是否和原产品单位一致，不一致则提示，如果取消则还原
    async checkUnit(changeRows, mdApiName, lookupData, plugin, param) {
        if(!lookupData) return;
        let r1 = this.runPluginSync('nonstandard_product.checkUnit.before', {changeRows, param});
        if(r1 && r1.stop) return;

        let {product_id, unit} = this.getAllFields(mdApiName);
        let {actual_unit} = this.getPluginFields('multi-unit') || {};
        let productList = [];
        changeRows.forEach(item => {
            let cd = param.changeData[item.rowId];
            if(actual_unit && item.__oldProductName && this._getUnit(item, actual_unit) !== this._getUnit(cd, actual_unit)) {
                productList.push([item.__oldProductName, cd[product_id + '__r']]);
            }else if(unit && item.__oldProductName && this._getUnit(item, unit) !== this._getUnit(lookupData, 'unit')){
                productList.push([item.__oldProductName, cd[product_id + '__r']]);
            }
        })
        if(!productList.length) return;

        return new Promise((resolve, reject) => {
            let msg = '';
            productList.forEach(item => {
                // msg += `${item[0]} 和 ${item[1]} 的单位不一致，是否确认更换产品？`;
                msg += this.i18n('sfa.crm.nonstandard_product.confirm_change_product', {
                    A: item[0],
                    B: item[1],
                }, `{{A}} 和 {{B}} 的单位不一致，是否确认更换产品？`);
                msg += `<br>`;
            })
            CRM.util.confirm20(msg, null, {
                confirmHandle(e, item) {
                    this.hide();
                    resolve();
                },
                cancelHandle(e, item) {
                    this.hide();
                    plugin.skipPlugin();
                    reject();
                }
            })
        })
    }

    // 开了不重新取价
    isOpenNoGetPrice(){
        return this.getConfig('change_product_type_refresh_price') == '1';
    }

      // 把修改的数据更新到数据上
      updateChangeData(changeData = {}, changeDataList = []){
        changeDataList.forEach(item => {
            item = Object.assign(item, changeData[item.rowId]);
        })
    }

    // 编辑产品后，还原非标产品价格等字段
    async _editAfter(plugin, param) {
        let mdApiName = param.objectApiName || param.objApiName;
        let detailData = param.dataGetter.getDetail(mdApiName);
        let {detail_type, product_id, product_price, discount} = this.getAllFields(mdApiName);
        if(param.fieldName !== product_id) return;
        if(!param.changeData) return;
        let changeRows = detailData.filter(item => param.changeData[item.rowId]);
        await this.checkUnit(changeRows, mdApiName, param.lookupData, plugin, param);

        // 把更改的数据合并到明细数据上
        this.updateChangeData(param.changeData, changeRows);

        let priceServiceFields = this.getPluginFields('price-service');
        let bomPlugin = this.getPluginFields('bom') || {};
        // 非标产品改为普通品后，需要还原的字段；
        let ignoreFields = [product_price, discount,];
        if(priceServiceFields){
            let {price_book_price, price_book_discount} = priceServiceFields;
            ignoreFields.push(price_book_price, price_book_discount);
        }
        this.updateDetailType(changeRows, mdApiName, param);
        this.setFieldsReadOnly(changeRows, param, 'edit');

        // 如果后台开关为不重新计算，则不还原价格等字段
        if(!this.isOpenNoGetPrice()) return;

        let nonProductData = [];
        changeRows.forEach(item => {
         // getMDOriginData 获取原始数据
            // 非标产品，且不是套餐，则还原价格等字段
            let rowId = item.rowId;
            if(item && item[detail_type] !== 'non_standard' && !PPM.isBom(item, bomPlugin).isPackage) {
                let val = param.dataGetter.getMDOriginData(rowId, ignoreFields);
                param.dataUpdater.updateDetail(mdApiName, rowId, val);
                nonProductData.push(item);
            }
        })
        if(!nonProductData.length) return;
        this.deleteChangeData(ignoreFields, nonProductData, param);
        this.ignoreCalParam({
            calData: nonProductData,
            mdApiName,
            ignoreFields,
        }, param);
    }

    // 从 change 字段中，删除非标产品要保留的数据，不触发计算
    deleteChangeData(ignoreFields, nonProductData, param) {
        param.beChangeFields = param.beChangeFields.filter(item => !ignoreFields.includes(item));
        nonProductData.forEach(item => {
            ignoreFields.forEach(field => {
                delete param.changeData[item.rowId][field];
            })
        })
    }

    /**
     * 忽略底层计算某些行的某些字段
     * @param calData 需要忽略计算的行数据 []
     * @param mdApiName
     * @param ignoreFields 忽略的计算字段 []
     * @param param
     */
    ignoreCalParam({calData, mdApiName, ignoreFields} = {}, param) {
        let obj = {
            modifiedDataIndexList: [],
            excludedDetailCalculateFields: {}
        };
        function _fn(fieldList = []){
            return fieldList.map(f => {
                return {
                    "fieldName": f,
                    "order": 1
                }
            })
        }
        // 忽略字段，以及忽略字段相关的字段，都不计算
        let allIgnoreFields = param.dataGetter.getCalculateFieldsByFieldName(ignoreFields, true, mdApiName)
        let excludeFields = {};
        PPM.each(allIgnoreFields, (val, apiname) => {
            let filterData = {};
            calData.forEach(item => {
               filterData[item.rowId] = _fn(val);
            })
            excludeFields[apiname] = filterData;
        })

        obj.excludedDetailCalculateFields = excludeFields

        let fn1 = param.parseParam;
        param.parseParam = (p) => {
            if(fn1) p = fn1(p);
            p.modifiedDataIndexList = p.modifiedDataIndexList || [];
            p.modifiedDataIndexList = p.modifiedDataIndexList.concat(obj.modifiedDataIndexList);
            p.excludedDetailCalculateFields = p.excludedDetailCalculateFields || {};
            this.mergeCalculateFields(p.excludedDetailCalculateFields, obj.excludedDetailCalculateFields);
            return p;
        };
    }

    // 合并计算过滤字段；
    mergeCalculateFields(base, self){
        PPM.each(self, (val, key) => {
            base[key] = base[key] || {};
            PPM.each(val, (arr, rowId) => {
                if(base[key][rowId]){
                    base[key][rowId] = base[key][rowId].concat(arr)
                }else{
                    base[key][rowId] = arr;
                }
            })
        })
    }

    // 导入非标产品, 更新非标产品类型
    _excelImportParseFormDataBefore(plugin, obj) {
        let {data, param} = obj;
        let mdApiName = param.objApiName;
        let {product_id, detail_type} = this.getAllFields(mdApiName);
        data.forEach(item => {
            if(item[product_id + '__ro']) {
                let pt = item[product_id + '__ro'].product_type;
                if(pt) item[detail_type] = pt || 'standard';
            }
        })
        return {data};
    }

    // 粘贴非标产品, 更新非标产品类型
    _excelPasteParseDataBefore(plugin, obj) {
        let {addDatas, param} = obj;
        let mdApiName = param.objApiName;
        let {product_id, detail_type} = this.getAllFields(mdApiName);
        addDatas.forEach(item => {
            if(item[product_id + '__ro']) {
                let pt = item[product_id + '__ro'].product_type;
                if(pt) item[detail_type] = pt || 'standard';
            }
        })
        return {addDatas};
    }

    // 导入或者粘贴后，设置非标产品为可编辑
    _batchAddEnd(plugin, param) {
        if(param.lookupField?.__action_from === 'excel_import' || param.lookupField?.__action_from === 'excel_paste'){
            let data = param.addDatas;
            if(data) this.setFieldsReadOnly(data, param);
        }
    }

    // 行复制后，设置非标产品为可编辑
    _mdCopyAfter(plugin, param) {
        let details = param.dataGetter.getDetail(param.objApiName);
        let data = details.filter(item => param.newDataIndexs.includes(item.rowId));
        this.setFieldsReadOnly(data, param);
    }

    /**
     * @desc  添加非标产品，是否走取价
     * @returns {{isDoPriceService: boolean}}
     * @private
     */
    _batchAddAfter_before(plugin, obj) {
        let {param} = obj;
        if(
            param.__action_from === "nonstandard_product"){
            return {
                isDoPriceService: true
            }
        }
    }

    getHook() {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            },{
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this)
            },{
                event: 'md.edit.before',
                functional: this._editBefore.bind(this)
            }, {
                event: 'md.edit.after',
                functional: this._editAfter.bind(this)
            },{
                event: 'excelImport.parseFormData.before',
                functional: this._excelImportParseFormDataBefore.bind(this)
            }, {
                event: 'md.batchAdd.end',
                functional: this._batchAddEnd.bind(this)
            }, {
                event: 'excelpaste.parseData.before',
                functional: this._excelPasteParseDataBefore.bind(this)
            }, {
                event: 'md.copy.after',
                functional: this._mdCopyAfter.bind(this)
            }, {
                event: 'price-service.batchAddAfter.before',
                functional: this._batchAddAfter_before.bind(this)
            }

        ];
    }

}
