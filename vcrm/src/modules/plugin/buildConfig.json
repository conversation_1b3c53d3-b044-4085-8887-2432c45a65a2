{"attribute": "./common/attribute", "backcalculation": "./common/backcalculation", "periodic_product": "./common/periodic_product", "bom": "./common/bom", "fixed_collocation": "./common/fixed_collocation", "mc_currency": "./common/mc_currency", "mulit-unit": "./common/multiunitplugin", "plugin-service": "./common/plugin-service", "plugin-log": "./common/plugin-log", "price-service": "./common/price-service", "price_policy": "./common/price_policy", "price_keyboard": "./common/price_keyboard", "coupon": "./common/coupon", "rebate": "./common/rebate", "statistics": "./common/statistics", "nonstandard_attribute": "./common/nonstandard_attribute", "nonstandard_product": "./common/nonstandard_product", "datasource": "./common/datasource", "excelimport": "./common/excelimport", "excelpaste": "./common/excelpaste", "add_md_by_product": "./common/add_md_by_product", "resource": "./common/resource", "bquery": "./common/bquery", "manual_gift": "./common/manual_gift", "gantt": "./common/gantt", "rate": "./common/rate", "mdscandevice": "./common/mdscandevice", "video": "./common/video", "appointment_time_period": "./common/appointment_time_period", "change_price": "./common/change_price", "selfcheck": "./common/selfcheck", "async_commit": "./common/async_commit", "AIReport": "./crm/ai-report", "quoter": "./common/quoter", "percent": "./common/percent", "biz_field_sfa_category_tree": "./common/biz_field_sfa_category_tree", "accountaddrobj": "./crm/accountobj/accountaddrobj/index", "accountfininfoobj": "./crm/accountobj/accountfininfoobj/index", "accountbquery": "./crm/accountobj/accountbquery/index", "availableaccountobj": "./crm/availablerangeobj/availableaccountobj", "availableproductobj": "./crm/availablerangeobj/availableproductobj", "availablepricebookobj": "./crm/availablerangeobj/availablepricebookobj", "bom_attribute_constraintobj_list": "./crm/bom_attribute_constraintobj/list/index", "bom_attribute_constraintobj_product_id": "./crm/bom_attribute_constraintobj/productid", "bom_attribute_constraintobj_md": "./crm/bom_attribute_constraintobj/md", "pricepolicyaccountobj": "./crm/pricepolicyobj/pricepolicyaccountobj", "pricepolicyproductobj": "./crm/pricepolicyobj/pricepolicyproductobj", "quotelinehistory": "./crm/quoteobj/quotelinehistory", "quoteobjhistory": "./crm/quoteobj/quoteobjhistory", "quotetrialcalculate": "./crm/quoteobj/quotetrialcalculate", "multi_level_order_sfa": "./crm/salesorderobj/multi_level_order/server_empty_entry", "multi_level_order_sfa_inner": "./crm/salesorderobj/multi_level_order", "salesorderobj_history": "./crm/salesorderobj/salesorderobj_history", "salesorderobj_salecontract": "./crm/salesorderobj/salesorderobj_salecontract", "salesorderobj_shopcategory": "./crm/salesorderobj/salesorderobj_shopcategory", "salesorderobj_customer": "./crm/salesorderobj/salesorderobj_customer", "closeorder": "./crm/salesorderobj/close_order", "invoiceapplicationlinesobj": "./crm/invoiceapplicationobj/invoiceapplicationlinesobj/index", "invoiceapplication_address_invoice_backfill": "./crm/invoiceapplicationobj/address_invoice_backfill", "applyaccountrange": "./crm/pricebookobj/applyaccountrange", "attribute_value": "./crm/attributeobj/attribute_value", "specification_value": "./crm/specificationobj/specification_value", "attribute_pricebooklines": "./crm/attributepricebookobj/attribute_pricebooklines", "couponinstanceobj_couponplan": "./crm/couponinstanceobj/couponplan", "bomcore_md": "./crm/bomcore/bomcore_md", "crm_uni_detail": "./crm/crm_uni_detail", "detail_multi_source": "./common/detail_multi_source", "advanced_formula_obj": "./crm/advancedformulaobj/index", "prm_custompage": "./prm/custompage", "dht_orderform": "./dht/orderform", "dht_shoppingcart": "./dht/shoppingcartobj", "dht_newadvertisementobj": "./dht/newadvertisementobj/index", "dht_list_newadvertisementobj": "./dht/newadvertisementobj/dht_list_newadvertisementobj", "dht_detail_newadvertisementobj": "./dht/newadvertisementobj/dht_detail_newadvertisementobj.js", "dht-guide-modal": "./dht/dht-guide-modal/index", "dht-customer-account": "./dht/dht-customer-account/index", "medical-equipment-dms": "./dht/medical-equipment-dms/index", "shop_category": "./dht/shop_category/index", "industryorder_jhyp": "./dht/industryorder/jhyp", "distribution-list": "./dht/distribution/list", "distribution-form": "./dht/distribution/form", "distribution-detail-pricebookobj": "./dht/distribution/detail/pricebookobj", "dms-medical-guide": "./dht/dms-medical-guide/index", "transfer-order-form": "./dht/transfer-order-plugin/form/index", "transfer-order-list": "./dht/transfer-order-plugin/list/index", "onlinestoreobj-form": "./crm/onlinestoreobj/form", "onlinestoreobj-list": "./crm/onlinestoreobj/list", "onlinestoreobj-detail": "./crm/onlinestoreobj/detail", "splugin-individualstockcountingobj": "./stock/individualstockcountingobj", "splugin-outbounddeliverynoteobj": "./stock/outbounddeliverynoteobj", "splugin-purchasereturnnoteobj": "./stock/purchasereturnnoteobj", "splugin-purchaseorderobj": "./stock/purchaseorderobj", "splugin-goodsreceivednoteobj": "./stock/goodsreceivednoteobj", "splugin-stockchecknoteobj": "./stock/stockchecknoteobj", "splugin-requisitionnoteobj": "./stock/requisitionnoteobj", "splugin-dealerreturnapplicationobj": "./stock/dealerreturnapplicationobj", "splugin-returnedgoodsinvoiceobj": "./stock/returnedgoodsinvoiceobj", "splugin-returnedgoodsinvoiceobj-batchsn": "./stock/returnedgoodsinvoiceobj/batch-sn", "splugin-returnedgoodsinvoiceobj-multiunit": "./stock/returnedgoodsinvoiceobj/multi-unit", "splugin-sparepartsapplicationobj": "./stock/sparepartsapplicationobj", "splugin-sparepartsreturnobj": "./stock/sparepartsreturnobj", "splugin-deliverynoteobj": "./stock/deliverynoteobj", "splugin-sparepartsconsumptionobj": "./stock/sparepartsconsumptionobj", "splugin-sparepartsmaintenancetaskobj": "./stock/sparepartsmaintenancetaskobj", "splugin-sparepartsdeliveryobj": "./stock/sparepartsdeliveryobj", "splugin-supplierobj": "./stock/supplierobj", "splugin-batch-sn": "./stock/common-splugin/splugin-batch-sn", "splugin-batch-sn-old": "./stock/common-splugin/splugin-batch-sn-old", "splugin-scan-code": "./stock/common-splugin/splugin-scan-code", "splugin-multi-unit": "./stock/common-splugin/splugin-multi-unit", "refresh-current-available-stock": "./stock/common-splugin/refresh-current-available-stock", "payment-com": "./stock/plugin-units/payment", "opening-required": "./stock/plugin-units/opening-required", "payobj-form": "./stock/plugin-units/payobj-form", "stock-detail-devicemeterreadingsobj": "./stock/devicemeterreadingsobj/detail/index", "stock-list-devicemeterreadingsobj": "./stock/devicemeterreadingsobj/list/index", "stock-detail-devicemeterobj": "./stock/devicemeterobj/detail/index", "splugin-receivematerialbillobj": "./eservice/receivematerialbillobj/splugin-receivematerialbillobj", "accessoryexchangeobj": "./eservice/accessoryexchangeobj/accessoryexchangedetailobj", "deviceplanobj": "./eservice/deviceplanobj/deviceplandetailobj", "feesettlementbillobj": "./eservice/feesettlementbillobj/feesettlementdetailobj", "preventivemaintenanceobj": "./eservice/preventivemaintenanceobj/preventivemaintenancedetailobj", "casesdevice": "./eservice/casesobj/casesdeviceobj", "casesserviceproject": "./eservice/casesobj/casesserviceprojectobj", "casesservicepersonnel": "./eservice/casesobj/casesservicepersonnelobj", "refundmaterialbill": "./eservice/refundmaterialbillobj/refundmaterialbillobj", "checkgroup": "./eservice/checkgroupobj/checkgroupobj", "appraiseobj": "./eservice/appraiseobj/index", "casesobj": "./eservice/casesobj/index", "customsearch": "./eservice/customsearch/index", "servicefaultdetail": "./eservice/servicefaultobj/servicefaultdetailobj", "servicefaultrecordincases": "./eservice/casesobj/servicefaultrecordobj", "servicefaultrecordobj": "./eservice/servicefaultrecordobj/index", "splugin-employeewarehouseobj": "./eservice/employeewarehouseobj", "servicecenterobj": "./eservice/servicecenterobj/index", "servicecenterobj-detail": "./eservice/servicecenterobj/detail/index", "servicecenterobj-list": "./eservice/servicecenterobj/list/index", "knowledgedistributionruleobj": "./eservice/knowledgedistributionruleobj/index", "knowledgedistributionruleobj-detail": "./eservice/knowledgedistributionruleobj/detail/index", "knowledgedistributionruleobj-list": "./eservice/knowledgedistributionruleobj/list/index", "eservicegeneralruleobj": "./eservice/eservicegeneralruleobj/index", "eservicegeneralruleobj-detail": "./eservice/eservicegeneralruleobj/detail/index", "eservicegeneralruleobj-list": "./eservice/eservicegeneralruleobj/list/index", "salecontractobj_md": "./crm/salecontractobj/md", "sale_contract_component": "./crm/salecontractobj/sub_contract_level", "splugin-batch-sn-receivematerialbillobj": "./eservice/receivematerialbillobj/splugin-receivematerialbillobj/batch-sn", "tpmactivityobj": "./crm/tpm/tpmactivityobj", "tpmactivitystorerange": "./crm/tpm/tpmactivityobj/tpmactivitystorerange.js", "tpm-cashing-all": "./crm/tpm/cashing_all/", "tpm-cashing-select-product": "./crm/tpm/cashing_all/select_product", "tpmactivityunifiedcaseobj": "./crm/tpm/tpmactivityunifiedcaseobj", "tpmactivityunifiedcaseobj-filtertabs": "./crm/tpm/tpmactivityunifiedcaseobj/filtertabs", "tpmactivitycashingproductscopeobj": "./crm/tpm/tpmactivityunifiedcaseobj/tpmactivitycashingproductscopeobj", "tpmdealeractivitycostobj": "./crm/tpm/tpmdealeractivitycostobj/index", "tpmactivitycashingproductobj": "./crm/tpm/tpmactivityobj/tpmactivitycashingproductobj.js", "tpmactivityproductrangeobj": "./crm/tpm/tpmactivityobj/tpmactivityproductrangeobj.js", "tpmactivityunifiedcaseproductrangeobj": "./crm/tpm/tpmactivityunifiedcaseobj/tpmactivityunifiedcaseproductrangeobj.js", "tpmactivitydetailobj": "./crm/tpm/tpmactivityobj/tpmactivitydetailobj.js", "tpm-price-rule": "./crm/tpm/price_rule/index.js", "tpm-reward": "./crm/tpm/reward/index.js", "tpmbudgetcarryforwardobj": "./crm/tpm/tpmbudgetcarryforwardobj/index.js", "tpmbudgetcarryforwarddetailobj": "./crm/tpm/tpmbudgetcarryforwardobj/tpmbudgetcarryforwarddetailobj.js", "task-system-reward-metric": "./crm/tpm/rewardmetricobj/index.js", "task-system-designer": "./crm/tpm/rewardtaskobj/index.js", "orderpaymentobj": "./crm/paymentobj/orderpaymentobj", "payment-pay": "./crm/paymentobj/pay", "manualmatchworkpage": "./crm/paymentobj/manualmatchworkpage/index", "detailmanualmatch": "./crm/paymentobj/detailmanualmatch/index", "accountsreceivabledetailobj": "./crm/accountsreceivablenoteobj/accountsreceivabledetailobj", "matchnotedetailobj": "./crm/matchnoteobj/matchnotedetailobj", "salesinvoicedetailobj": "./crm/salesinvoiceobj/salesinvoicedetailobj", "authorizationdetailobj": "./crm/faccountauthorizationobj/authorizationdetailobj", "unfreezeauthdetailobj": "./crm/faccountauthorizationobj/unfreezeauthdetailobj", "creditoccupiedruledetailobj": "./crm/creditoccupiedruleobj/creditoccupiedruledetailobj", "creditoccupiedruledetailobj-detail": "./crm/creditoccupiedruledetailobj/detail.js", "creditoccupiedruledetailobj-list": "./crm/creditoccupiedruledetailobj/list.js", "customercreditauthobj": "./crm/customercreditauthobj/index", "transactionstatementobj": "./crm/transactionstatementobj/index", "plugin-enterpriserelationobj": "./interconnect/enterpriserelationobj/index", "plugin-publicemployeeobj": "./interconnect/publicemployeeobj/index", "simpleorderform": "./crm/salesorderobj/salesorderobj_simpleorder/index", "orderformhly": "./crm/salesorderobj/salesorder_hly/index", "incentivepolicyobj": "./crm/incentivepolicyobj/index", "splugin-assettransferobj": "./stock/assettransferobj", "splugin-assetextensionobj": "./stock/assetextensionobj", "splugin-warehouseobj": "./stock/warehouseobj", "electronicsignobj": "./crm/electronicsignobj/index", "detail-ProjectObj": "./crm/projectobj/index", "mengniu-salesarea": "./crm/accountobj/mengniu-salesarea/index", "selected_list_test": "./crm/selected_list_test/index", "activity_email_corpus": "./eservice/activity_email_corpus/index", "point": "./common/point", "activerecordobj_interactive_records": "./crm/activerecordobj/interactive_records/index", "product-form": "./crm/productobj/form", "spu-form": "./crm/spuobj/form", "shopcategoryobj-form": "./crm/shopcategoryobj/form", "salary-kpi-metric": "./crm/salary/salarykpiobj/index.js", "salary-item": "./crm/salary/salaryitemobj/index.js", "salary-payment-slip": "./crm/salary/salarypaymentslipobj/index.js", "employee-fixed-salary": "./crm/salary/employeefixedsalaryobj/index.js"}