import Base from 'plugin_base'
import QuickArFormDialog from './QuickArFormDialog.vue'
export default class SalesOrderObjDetailQuickAr extends Base {
    apply() {
        return [
            {
                event: 'detail.head_info.render.before',
                functional: this.headInfoRenderBefore.bind(this)
            }
        ]
    }

    headInfoRenderBefore(plugin, param) {
        console.log(plugin, param);
        const {objectApiName, objectDataId} = param; 
        const objectData = param.dataGetter().getData();
        return Promise.resolve({
            buttons: {
                reset: [{
                    action: 'ArQuickCreate',
                    callback: () => {
                        this.handleQuickAr.apply(this, [{objectApiName, objectDataId, objectData}, ...arguments])
                    }
                }],
            }
        });
    }

    handleQuickAr(param, ...args) {
        const {objectApiName, objectDataId, objectData} = param; 
        FxUI.create({
            wrapper: $('body')[0],
            template: `<quick-ar-form-dialog objectApiName="${objectApiName}" objectDataId="${objectDataId}" :objectData="objectData" />`,
            components: {
                QuickArFormDialog
            },
            data() {
                return {
                    objectData
                }
            }
        })
    }
}