<template>
    <fx-dialog
        :visible.sync="visible"
        :title="title"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :append-to-body="true"
        :destroy-on-close="true"
        custom-class="quick-ar-form-dialog"
        size="small"
    >
        <fx-form :model="form" :rules="rules" ref="form" v-loading="initLoading" size="small">
            <div class="form-item-amount-container">
                <p class="step-info">
                    <span class="step-info-item" v-for="(_, i) in Array(allPeriod).fill(true)" :key="i" :class="{'active': i < currentPeriod}"></span>
                </p>
                <p class="step-info-amount">
                    <span>{{ $t('sfa.vcrm.accountreceivable.ar_quick_rule.amount_tip') }}<span class="step-info-amount-value">{{ arAmountProcessPercent }}</span></span>
                    <span>{{ $t('sfa.vcrm.accountreceivable.ar_quick_rule.period_tip') }}<span class="step-info-amount-value">{{ currentPeriod }} / {{ allPeriod }}</span></span>
                </p>
                <fx-input
                    :label="$t('sfa.vcrm.accountreceivable.ar_quick_rule.current_amount')"
                    prop="amount"
                    v-model="form.amount"
                    :placeholder="$t('请输入')"
                    :disabled="isLastPeriod"
                    type="number"
                    :decimal-places="2"
                />
            </div>

            <template v-for="item in accountReceivableFormProps">
                <fx-select
                    v-if="item.type === 'select'"
                    :label="item.label"
                    :prop="item.prop"
                    :key="item.prop"
                    v-model="form[item.prop]"
                    v-bind="item.attr"
                />
                <fx-date-picker
                    v-else-if="item.type === 'date' || item.type === 'date_time'"
                    :label="item.label"
                    :prop="item.prop"
                    :key="item.prop"
                    v-model="form[item.prop]"
                    v-bind="{
                        ...item.attr,
                        'type': item.type === 'date' ? 'date' : 'datetime',
                        'format': item.type === 'date' ? 'yyyy-MM-dd' : 'yyyy-MM-dd HH:mm'
                    }"
                    value-format="timestamp"
                />
                <fx-input
                    v-else
                    :label="item.label"
                    :prop="item.prop"
                    :key="item.prop"
                    :type="item.type"
                    v-model="form[item.prop]"
                    v-bind="item.attr"
                />
            </template>
        </fx-form>

        <div slot="footer">
            <fx-button type="primary" size="mini" @click="handleSubmit" :loading="submitLoading">{{ $t('确定') }}</fx-button>
            <fx-button size="mini" @click="visible = false">{{ $t('取消') }}</fx-button>
        </div>
    </fx-dialog>
</template>

<script>
export default {
    name: 'QuickArFormDialog',
    props: {
        objectApiName: {
            type: String,
            default: ''
        },
        objectDataId: {
            type: String,
            default: ''
        },
        objectData: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            visible: false,
            title: $t('sfa.vcrm.accountreceivable.ar_quick_rule.title'),
            form: {
                amount: '',
                record_type: 'default__c'
            },
            rules: {
                amount: [{
                    required: true,
                    validator: (rule, value, callback) => {
                        if (!value) {
                            callback(new Error($t('sfa.vcrm.accountreceivable.ar_quick_rule.current_amount') + ' ' + $t('不能为空')));
                            return;
                        }
                        if (Number(value) < this.minAmount) {
                            callback(new Error($t('sfa.vcrm.accountreceivable.ar_quick_rule.current_amount_min_tip', {num: this.minAmount})));
                        } else {
                            callback();
                        }
                    },
                    trigger: 'blur'
                }],
            },
            accountReceivableFormProps: [],
            minAmount: 0,
            currentPeriod: 0,
            isLastPeriod: false,
            quickRuleData: {},
            initLoading: false,
            submitLoading: false
        }
    },
    computed: {
        allPeriod() {
            return Number(this.quickRuleData?.max_period_count || 0);
        },
        arAmountProcessPercent() {
            const { ar_tag_amount, no_ar_tag_amount } = this.objectData;
            return `${ar_tag_amount} / ${Number(ar_tag_amount) + Number(no_ar_tag_amount)}`;
        }
    },
    created() {
        this.init();
    },
    methods: {
        async init() {
            this.initLoading = true;
            try {
                await this.handleCheckArQuick();
                await this.fetchAccountReceivableDescribe();
            } finally {
                this.initLoading = false;
            }
        },
        handleCheckArQuick() {
            return CRM.util.ajax_base('/EM1HNCRM/API/v1/object/accounts_receivable/service/check_ar_quick', {
                describe_api_name: this.objectApiName,
                object_id: this.objectDataId,
            }).then((res) => {
                const { quickRuleData, currentPeriod, lastPeriod, minAmount } = res;
                this.minAmount = Number(minAmount);
                this.isLastPeriod = lastPeriod;
                this.currentPeriod = Number(currentPeriod) + 1;
                this.quickRuleData = quickRuleData;
                this.visible = true;
                // 最后一期带入未建立应收金额
                if (lastPeriod) {
                    this.$set(this.form, 'amount', this.objectData.no_ar_tag_amount);
                }
            })
        },
        fetchAccountReceivableDescribe() {
            return CRM.util.getDescribeLayout({
                apiname: 'AccountsReceivableNoteObj',
                include_layout: false,
                include_detail_describe: false
            }).then((res) => {
                const accountReceivableDescribe = res.objectDescribe?.fields;
                const fields = ['record_type', ...(this.quickRuleData?.information_fields || [])];
                this.accountReceivableFormProps = fields.map((field) => {
                    const fieldDescribe = accountReceivableDescribe[field] || {};
                    if (field === 'record_type') {
                        return {
                            label: fieldDescribe.label,
                            prop: field,
                            type: 'select',
                            attr: {
                                options: fieldDescribe.options.filter((item) => item.is_active),
                                required: true,
                                placeholder: $t('请选择')
                            }
                        }
                    }
                    // 允许的类型：['number', 'text', 'date', 'currency']
                    return {
                        label: fieldDescribe.label,
                        prop: field,
                        type: fieldDescribe.type,
                        attr: {
                            'decimal-places': fieldDescribe.decimal_places,
                            maxLength: fieldDescribe.maxlength,
                            placeholder: $t('请输入'),
                            required: true,
                        }
                    }
                })
            })
        },
        
        handleSubmit() {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    this.submitLoading = true;
                    CRM.util.ajax_base('/EM1HNCRM/API/v1/object/accounts_receivable/service/save_ar_quick', {
                        describe_api_name: this.objectApiName,
                        object_id: this.objectDataId,
                        record_type: this.form.record_type,
                        amount: this.form.amount,
                        information_fields: _.pick(this.form, this.accountReceivableFormProps.map((item) => item.prop)),
                        accounts_receivable_quick_rule_id: this.quickRuleData._id
                    }).then((res) => {
                        this.visible = false;
                        this.$message.success($t('操作成功'));
                    }).finally(() => {
                        this.submitLoading = false;
                    })
                }
            })
        }
    }
}
</script>

<style lang="less">
.quick-ar-form-dialog {
    .el-select,
    .el-input,
    .el-date-editor {
        width: 100%;
    }

    .form-item-amount-container {
        background-color: var(--color-neutrals02);
        border-radius: 8px;
        padding: 8px;
        margin-bottom: 12px;

        .el-form-item {
            margin-bottom: 0;
        }

        .step-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 2px;

            .step-info-item {
                flex: 1;
                height: 2px;
                background-color: #DEE1E8;
                &:first-child {
                    border-top-left-radius: 2px;
                    border-bottom-left-radius: 2px;
                }
                &:last-child {
                    border-top-right-radius: 2px;
                    border-bottom-right-radius: 4px;
                }
                &.active {
                    background-color: var(--color-primary06);
                }
            }
        }
        .step-info-amount {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 12px;
            line-height: 18px;
            color: var(--color-neutrals11);
            margin: 4px 0 8px;
            .step-info-amount-value {
                font-weight: 700;
                color: var(--color-neutrals19);
            }
        }
    }
}
</style>