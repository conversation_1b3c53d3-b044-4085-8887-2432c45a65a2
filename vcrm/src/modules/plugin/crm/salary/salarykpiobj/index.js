import Base from "plugin_base";

const IndicatorCalcMethod = "indicator_calc_method"; // 指标计算方法字段api_name

export default class SalaryKPIObj extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    _fetchDescribeAfter(plugin, param) {
        const { layout } = param.describe;
        let form_component = layout.components?.find(
            (item) => item.api_name === "form_component"
        );
        if (form_component) {
            // 查找包含计算公式字段的分组
            const formulaSection = form_component.field_section.find(
                (section) => section.form_fields.some(field => field.field_name === IndicatorCalcMethod)
            );
            
            if (formulaSection) {
                // 修改字段渲染类型
                const formulaField = formulaSection.form_fields.find(field => field.field_name === IndicatorCalcMethod);
                if (formulaField) {
                    formulaField.render_type = IndicatorCalcMethod;
                }
            }
        }
        return param;
    }
    importSalaryComp() {
        return new Promise((resolve) => {
            seajs.use([
                'app-checkin-modules/es6/salary/index.js',
            ], (salary) => {
                resolve(salary.default);
            })
        })
    }
    _formRenderBefore(plugin, param) {
        let { BaseComponents, dataGetter, objApiName } = param;
        const describe = dataGetter.getDescribe(), plugin_this = this, components = {};
        const data = dataGetter.getData();

        components[IndicatorCalcMethod] = BaseComponents.Base.extend({
            render(h) {
                const that = this;
                const default_size = this.getSize();
                plugin_this.$wrapper = this.$wrapper = FxUI.create({
                    wrapper: this.$el.parents(`.f-g-item__${IndicatorCalcMethod}`)[0],
                    replaceWrapper: true,
                    components: {
                        CalcMethodComp: () => plugin_this.importSalaryComp().then(salary => {
                            return salary.salarykpiobj;
                        })
                    },
                    data() {
                        return {
                            size: default_size,
                            data
                        }
                    },
                    template: `<CalcMethodComp ref="calcMethod" :size='size' :data='data' describe='${JSON.stringify(describe)}' @change='onChangeMethod'/>`,
                    methods: {
                        onChangeMethod(changed) {
                            Object.keys(changed).forEach(api_name => {
                                that.setData(changed[api_name], api_name, false, true)
                            })
                        },
                        getValue() {
                            return this.$refs.calcMethod?.getValue();
                        },
                        validate() {
                            return this.$refs.calcMethod?.validate();
                        },
                        resize(size) {
                            this.size = size;
                        },
                    },
                });
            },
            resize(...args) {
                BaseComponents.Base.prototype.resize.apply(this, arguments);
                this.$wrapper?.resize(this.getSize());
            },
            getValue() {
                return this.$wrapper?.getValue();
            },
            asyncValidateData() {
                return this.$wrapper?.validate().then(() => {
                    return false;
                }, () => {
                    return true;
                });
            },
            destroy() {
                BaseComponents.Base.prototype.destroy.apply(this, arguments);
                this.$wrapper?.$destroy();
            },
        });
        return { components };
    }

    _formDataChange(plugin, param) {
        
    }

    getHook() {
        return [
            {
                event: "fetchdescribe.after",
                functional: this._fetchDescribeAfter.bind(this),
            },
            {
                event: "form.render.before",
                functional: this._formRenderBefore.bind(this),
            },
            {
                event: "form.dataChange.after",
                functional: this._formDataChange.bind(this)
            }
        ];
    }
}
