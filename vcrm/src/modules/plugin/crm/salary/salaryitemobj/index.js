import Base from "plugin_base";

const IndicatorCalcMethod = "calculation_formula"; // 指标计算方法字段api_name
const ValueTypeField = "value_type"; // 值类型字段

export default class SalaryItemObj extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    _fetchDescribeAfter(plugin, param) {
        const { layout } = param.describe;
        let form_component = layout.components?.find(
            (item) => item.api_name === "form_component"
        );
        if (form_component) {
            // 查找包含计算公式字段的分组
            const formulaSection = form_component.field_section.find(
                (section) => section.form_fields.some(field => field.field_name === IndicatorCalcMethod)
            );
            
            if (formulaSection) {
                // 修改字段渲染类型
                const formulaField = formulaSection.form_fields.find(field => field.field_name === IndicatorCalcMethod);
                if (formulaField) {
                    formulaField.render_type = IndicatorCalcMethod;
                }
            }
        }
        return param;
    }

    importSalaryComp() {
        return new Promise((resolve) => {
            seajs.use([
                'app-checkin-modules/es6/salary/index.js',
            ], (salary) => {
                resolve(salary.default);
            })
        })
    }

    _formRenderBefore(plugin, param) {
        let { BaseComponents, dataGetter, objApiName } = param;
        const describe = dataGetter.getDescribe(), plugin_this = this, components = {};
        const data = dataGetter.getData();

        components[IndicatorCalcMethod] = BaseComponents.Base.extend({
            render(h) {
                const that = this;
                const default_size = this.getSize();
                plugin_this.$wrapper = this.$wrapper = FxUI.create({
                    wrapper: this.$el.parents(`.f-g-item__${IndicatorCalcMethod}`)[0],
                    replaceWrapper: true,
                    components: {
                        FormulaEditor: () => plugin_this.importSalaryComp().then(salary => {
                            return salary.salaryitemobj;
                        })
                    },
                    data() {
                        return {
                            size: default_size,
                            data,
                            value_type: data[ValueTypeField],
                            formula: data[IndicatorCalcMethod] || ''
                        }
                    },
                    template: `<FormulaEditor ref="formulaEditor" v-show="value_type == '2'" :size='size' :value='formula' :data='data'/>`,
                    watch: {
                        
                    },
                    methods: {
                        getValue() {
                            return this.$refs.formulaEditor?.getValue();
                        },
                        validate() {
                            return this.$refs.formulaEditor?.validate();
                        },
                        resize(size) {
                            this.size = size;
                        },
                        setValue(value) {
                            this.value_type = value
                        },
                        destroy() {
                            this.$wrapper?.$destroy();
                        }
                    },
                });
            },
            resize(...args) {
                BaseComponents.Base.prototype.resize.apply(this, arguments);
                this.$wrapper?.resize(this.getSize());
            },
            getValue() {
                return this.$wrapper?.getValue();
            },
            asyncValidateData() {
                return this.$wrapper?.validate().then(() => {
                    return false;
                }, () => {
                    return true;
                });
            },
            destroy() {
                BaseComponents.Base.prototype.destroy.apply(this, arguments);
                this.$wrapper?.$destroy();
            },
        });
        return { components };
    }

    _formRenderAfter(plugin, param) {
        // 初始化时根据value_type值设置calculation_formula字段的显示状态
        this._toggleFormulaFieldVisibility(param);
    }

    _formDataChange(plugin, param) {
        const { changeData = {}, dataUpdater } = param;
        
        if (ValueTypeField in changeData) {
            this._toggleFormulaFieldVisibility(param);
        }

    }

    // 根据value_type值控制计算公式字段的显示/隐藏
    _toggleFormulaFieldVisibility(param) {
        const { dataGetter, dataUpdater } = param;
        const data = dataGetter.getData();
        
        const isFormulaType = data[ValueTypeField] === '2';
        this.$wrapper.setValue(data[ValueTypeField]);    
        if (isFormulaType) {
            dataUpdater.setHidden({status: false,fieldName: ['rounding_method', 'decimal_places']});
            dataUpdater.setRequired({status: true,fieldName: ['rounding_method', 'decimal_places']});
            this.$wrapper.$el.parentElement.style.display = 'flex'
        } else {
            dataUpdater.setHidden({status: true,fieldName: ['rounding_method', 'decimal_places']});
            dataUpdater.setRequired({status: false,fieldName: ['rounding_method', 'decimal_places']});
            this.$wrapper.$el.parentElement.style.display = 'none'
        }
    }

    getHook() {
        return [
            {
                event: "fetchdescribe.after",
                functional: this._fetchDescribeAfter.bind(this),
            },
            {
                event: "form.render.before",
                functional: this._formRenderBefore.bind(this),
            },
            {
                event: "form.render.after",
                functional: this._formRenderAfter.bind(this),
            },
            {
                event: "form.dataChange.after",
                functional: this._formDataChange.bind(this)
            }
        ];
    }
}
