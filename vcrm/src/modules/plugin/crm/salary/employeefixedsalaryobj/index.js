import Base from "plugin_base";

export default class SalaryItemObj extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    _formDataChange(plugin, param) {
        const { changeData = {}, dataUpdater } = param;
        const salary_method = 'salary_method';
        // 如果value_type字段发生变化
        if (salary_method in changeData) {
            console.log('值类型变更:', changeData[salary_method]);
            dataUpdater.delDetail('EmployeeFixedSalaryDetailObj')
        }
    }


    getHook() {
        return [
            {
                event: "form.dataChange.after",
                functional: this._formDataChange.bind(this)
            }
        ];
    }
}
