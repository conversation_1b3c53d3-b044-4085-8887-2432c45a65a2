import Base from "plugin_base";

const startDate = "start_date"; // 指标计算方法字段api_name

export default class SalaryItemObj extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    _formRenderBefore(plugin, param) {
        let { BaseComponents, dataGetter, objApiName } = param;
        const describe = dataGetter.getDescribe(), plugin_this = this, components = {};
        const data = dataGetter.getData();
        components[startDate] = BaseComponents.Base.extend({
            render(h) {
                const that = this;
                const default_size = this.getSize();
                plugin_this.$wrapper = this.$wrapper = FxUI.create({
                    wrapper: this.$el.parents(`.f__date_time_range`)[0],
                    template: `
                    <div style="width: 100%;">
                        <fx-date-picker ref="picker1" v-model="value" :size="size" v-show="pay_cycle === '1'" type="date" format="yyyy-MM-dd" @change="changeHandler" />
                        <fx-date-picker ref="picker2" v-model="value" :size="size" v-show="pay_cycle === '2'" type="week" format="yyyy 第 WW 周" @change="changeHandler" />
                        <fx-date-picker ref="picker3" v-model="value" :size="size" v-show="pay_cycle === '3'" type="month" @change="changeHandler" />
                    </div>
                    `,
                    replaceWrapper: true,
                    data() {
                        return {
                            value: FS.moment(data[startDate]).format('YYYY-MM-DD') || '',
                            size: default_size,
                            data,
                            pay_cycle: data['pay_cycle_referenced__v'] || '1',
                        }
                    },
                    watch: {
                        
                    },
                    methods: {
                        changeHandler(value){
                            console.log('changeHandler', value);
                            let start_date = FS.moment(value).startOf('day').valueOf();
                            let end_date = '';
                            if(this.pay_cycle == 1){
                                start_date = FS.moment(value).startOf('day').valueOf();   
                                end_date = start_date;
                            }else if(this.pay_cycle == 2){
                                start_date = FS.moment(value).startOf('week').add(1, 'day').valueOf();
                                end_date = FS.moment(start_date).add(6, 'day').startOf('day').valueOf();
                            }else if(this.pay_cycle == 3){
                                start_date = FS.moment(value).startOf('month').valueOf();
                                end_date = FS.moment(value).endOf('month').startOf('day').valueOf();
                            }
                            that.setData(start_date, 'start_date', false, true)
                            that.setData(end_date, 'end_date', false, true)
                        },
                        setValue(value){
                            this.pay_cycle = value || '1';
                            this.changeHandler(this.value);
                        },
                    },
                });
            },
            validate() {
                return this.$wrapper?.validate();
            },
            destroy() {
                BaseComponents.Base.prototype.destroy.apply(this, arguments);
                this.$wrapper?.$destroy();
            },
        });
        return { components };
    }

    _formDataChange(plugin, param) {
        const { changeData = {}, dataGetter } = param;
        const pay_cycle = 'pay_cycle_referenced__v';
        // 如果value_type字段发生变化
        setTimeout(() => {
            const data = dataGetter.getData();
            this.$wrapper.setValue(data[pay_cycle]);  
        }, 500);
        // if (salary_rule in changeData && changeData[salary_rule]) {
        //     dataGetter.getDescribeLayout().objectDescribe.fields.pay_cycle.options[0].disabled = true
        //     FxUI.objectApi.fetch_data('SalaryRuleObj', changeData[salary_rule]).then((data) => {
        //         console.log(data);
        //         this.$wrapper.setValue(data['distribution_cycle']);
        //     })
        // }
    }

    getHook() {
        return [
            {
                event: "form.render.before",
                functional: this._formRenderBefore.bind(this),
            },
            {
                event: "form.dataChange.after",
                functional: this._formDataChange.bind(this)
            }
        ];
    }
}
