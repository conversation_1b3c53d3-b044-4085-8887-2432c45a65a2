/**
 * 回款 - 回款明细
 */
import Base from 'plugin_base';

const DEFAULT_RECORD_TYPE = 'default__c';
const crmUtil = CRM.util;
const DETAIL_API_NAME = 'OrderPaymentObj';
/** 蓝字回款 */
const COLLECTION_TYPE_BLUE = 'Blue';
/** 红字回款 */
const COLLECTION_TYPE_RED = 'Red';
// 回款类型对应来源数据，蓝字 -- 订单、红字 -- 退货单
const COLLECTION_TYPE_MAP = {
    RELATE_DETAIL_ID: {
        [COLLECTION_TYPE_BLUE]: 'order_id',
        [COLLECTION_TYPE_RED]: 'returned_goods_invoice_id'
    },
    RELATE_APINAME: {
        [COLLECTION_TYPE_BLUE]: 'SalesOrderObj',
        [COLLECTION_TYPE_RED]: 'ReturnedGoodsInvoiceObj'
    },
    RELATE_FIELDS: {
        [COLLECTION_TYPE_BLUE]: {
            // 待回款金额
            receivable_amount: 'receivable_amount',
            // 待回款确认金额
            payment_money_to_confirm: 'payment_money_to_confirm',
        },
        [COLLECTION_TYPE_RED]: {
            receivable_amount: 'pending_refund_amount',
            payment_money_to_confirm: 'refund_amount_to_be_confirmed',
        }
    }
}


export default class OrderPaymentPlugin extends Base {
    constructor(...rest) {
        const [pluginService, pluginParam] = rest;
        super(...rest);

        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
        this._isCalculatePayment = true;
        // 是否开启红字回款，根据是否有回款类型字段判断
        this._isOpenRedPayment = false;
    }

    // 根据 collection_type 取明细行对应字段
    parseDetailFields(detailItem, collectionType = COLLECTION_TYPE_BLUE) {
        if (!this._isOpenRedPayment) return detailItem;
        const fieldMap = COLLECTION_TYPE_MAP.RELATE_FIELDS[collectionType];
        return {
            ...detailItem,
            receivable_amount: detailItem[fieldMap.receivable_amount],
            payment_money_to_confirm: detailItem[fieldMap.payment_money_to_confirm] || '0',
        }
    }

    // 退货单立即回款，回款金额获取
    getPaymentAmountForBlue(id) {
        CRM.util.showLoading_tip();
        return new Promise((resolve, reject) => {
            CRM.util.FHHApi({
                url: '/EM1HNCRM/FMCG/API/DMS/Payment/calculateMaxAmount',
                data: {returnGoodsInvoiceId: id},
                success(res) {
                    if (res.Result.StatusCode === 0) {
                        resolve(res.Value?.amount);
                        return;
                    }
                    resolve(null);
                },
                complete() {
                    CRM.util.hideLoading_tip();
                    resolve(null);
                }
            })
        })
    }

    /**
     * 计算回款金额
     * @param {*} masterData
     * @returns {number|undefined} 回款金额
     */
    async _getPaymentAmountForDefaultData(masterData) {
        if (masterData.collection_type === COLLECTION_TYPE_RED) {
            const paymentAmount = await this.getPaymentAmountForBlue(masterData.order_data_id);
            return paymentAmount;
        }
        const paymentAmount = masterData.payment_amount;
        if (paymentAmount) return paymentAmount;

        const orderDetail = masterData.order_detail_data || {};
        const { receivable_amount, payment_money_to_confirm } = orderDetail;
        if (receivable_amount && payment_money_to_confirm) {
            return crmUtil.accSub(receivable_amount, payment_money_to_confirm);
        }
        return null;
    }

    /**
     * 获取从对象引用字段
     * @param {*} hookParam
     * @returns
     */
    _getQuoteFields(hookParam) {
        const { dataGetter, objApiName, recordType } = hookParam;
        const layoutFields = dataGetter.getLayoutFields(
            objApiName,
            recordType || DEFAULT_RECORD_TYPE,
        );

        const fields = [];
        layoutFields.forEach(field => {
            const { quote_field } = field;
            if (quote_field) {
                const { quote_field_type, api_name } = field;
                const [quoteField, quoteValue] = quote_field.split('__r.');
                fields.push({
                    api_name,
                    quote_field_type,
                    quoteField,
                    quoteValue,
                });
            }
        });

        return fields;
    }

    /**
     * 获取引用字段数据
     * @param {*} orderDetailData 订单明细
     * @param {*} hookParam
     * @returns {Record<string, string>} 引用字段数据
     */
    _getQuoteFieldData(orderDetailData, hookParam) {
        const result = {};
        if (!orderDetailData) {
            return result;
        }

        const quoteFields = this._getQuoteFields(hookParam);
        quoteFields.forEach((field) => {
            const { apiname, quoteValue } = field;
            result[apiname] = orderDetailData[quoteValue] || '';
        });

        return result;
    }

    /**
     * 添加默认数据
     * 从销售订单【立即付款】创建的回款
     * @param {*} hookParam
     * @returns
     */
    async _addOneDataToTable(hookParam) {
        const { dataGetter, dataUpdater, objApiName } = hookParam;
        const masterData = dataGetter.getMasterData();
        // 从销售订单下新建的
        const isFromSalesOrder = !!masterData.from_sales_order;
        // 从订单[收款]按钮下新建的(灰度)
        const isFromSalesOrderByCollectBtn = !!masterData.from_salesorder_by_collectbtn;
        // 从退货单添加
        const isFromPayment = !!masterData.from_payment;

        // 不是要添加默认数据的情形直接返回
        if (!(isFromSalesOrder || isFromSalesOrderByCollectBtn || isFromPayment)) {
            return;
        }

        //  获取明细业务类型
        const layout = dataGetter.getDescribeLayout();
        const orderPaymentLayout = layout?.detailObjectList?.find(
            ({ objectApiName }) => objectApiName === DETAIL_API_NAME
        );
        const detailRecordType = orderPaymentLayout?.layoutList[0]?.record_type;

        // 业务类型对不上添加了也没用，直接返回
        if (!detailRecordType) {
            return;
        }

        // 回款类型
        let collection_type = this._isOpenRedPayment ?
            (isFromPayment ? COLLECTION_TYPE_RED : COLLECTION_TYPE_BLUE) :
            null;
        if (collection_type) dataUpdater.updateMaster({collection_type});

        // 组装回款明细数据
        const orderDetailData = masterData.order_detail_data;
        const basicData = hookParam.getRowBasicData(
            objApiName,
            detailRecordType
        );
        const paymentAmount = await this._getPaymentAmountForDefaultData(masterData);
        const quoteFieldData = this._getQuoteFieldData(
            orderDetailData,
            hookParam
        );
        const {
            order_data_id,
            order_id__r,
            order_id,
            account_id,
            account_id__r,
        } = masterData;
        // 回填关联字段：订单、退货单
        const lookupField = collection_type ? COLLECTION_TYPE_MAP.RELATE_DETAIL_ID[collection_type] : 'order_id';
        const formatData = {
            [lookupField]: order_data_id,
            [lookupField + '__r']: order_id__r || order_id,
            payment_amount: paymentAmount,
            account_id,
            account_id__r,
        };
        const item = {
            ...basicData,
            ...quoteFieldData,
            ...formatData,
        };

        // 插入一行默认数据
        dataUpdater.add([item]);

        // 删除从对象数据，防止计算的时候提交无用的大参数
        dataUpdater.updateMaster({ order_detail_data: null });

        // 计算
        await hookParam.triggerCalAndUIEvent({
            newDataIndexs: [basicData.rowId]
        });
    }

    /**
     * 批量添加
     * @param {*} context
     * @param {*} param
     */
    _batchAddDataHandle(unkonw, params) {
        const { objectApiName, recordType } = params;
        const pluginService = this.pluginService.api.pluginServiceFactory({
            pluginApiName: this.pluginParam.pluginApiName,
        });
        const {collection_type} = pluginService.dataGetter.getMasterData();

        pluginService.batchPickData({
            objApiName: objectApiName,
            recordType: recordType,
            fieldName: COLLECTION_TYPE_MAP.RELATE_DETAIL_ID[collection_type || COLLECTION_TYPE_BLUE],
        });
    }

    /**
     * 为批量添加的数据计算回款金额，处理可能重复订单的情况
     * @param {*} orderId 订单编号
     * @param {*} details 从对象数据
     * @returns
     */
    _getSelPaymentAmount(orderId, details) {
        let paymentAmount = 0;
        details.forEach((order) => {
            const { order_id, life_status, payment_amount } = order;
            // 表格里存在相同订单且生命状态非正常，才需要累加已添加的使用金额
            if (orderId === order_id && life_status !== 'normal') {
                paymentAmount = crmUtil.accAdd(paymentAmount, payment_amount || 0)
            }
        });

        return paymentAmount;
    }

    /**
     * 为批量添加的数据计算回款金额
     * @param {*} order 销售订单
     * @param {*} details 从对象
     * @returns
     */
    _getPaymentForMd(order, details, hookParam) {
        let paymentAmount = null;
        if (this._isCalculatePayment) {
            const {collection_type} = hookParam.dataGetter.getMasterData();
            const { receivable_amount, payment_money_to_confirm, _id } = this.parseDetailFields(order, collection_type);
            paymentAmount =
                receivable_amount && payment_money_to_confirm
                    ? crmUtil.accSub(receivable_amount || 0, payment_money_to_confirm || 0)
                    : null;
            // 处理重复订单的情况
            const selPaymentAmount = this._getSelPaymentAmount(_id, details) || 0;
            paymentAmount = crmUtil.accSub(paymentAmount, selPaymentAmount);
            paymentAmount = paymentAmount
                ? paymentAmount.toFixed(2)
                : paymentAmount;
        }

        return paymentAmount;
    }

    // 处理回款金额正负数，红字处理为负数，蓝字处理为正数
    _parsePaymentSign(paymentAmount, hookParam) {
        if (!this._isOpenRedPayment) return paymentAmount;
        const {collection_type} = hookParam.dataGetter.getMasterData();
        paymentAmount = Math.abs(paymentAmount);
        return collection_type === COLLECTION_TYPE_RED ? paymentAmount * -1 : paymentAmount;
    }

    /**
     * 处理客户改变
     * @param {*} hookParam
     * @returns
     */
    async _handleAccountIdChange(hookParam) {
        const { dataGetter } = hookParam;
        const details = dataGetter.getDetail(DETAIL_API_NAME);
        if (!details.length) {
            return;
        }

        // 清空明细
        await hookParam.dataUpdater.delDetailAndTrigger(DETAIL_API_NAME);
    }

    /**
     * 获取销售订单修改后的字段数据
     * @param {*} hookParam
     * @returns
     */
    _getFormatDataForOrderChange(hookParam) {
        const cellData = {
            payment_plan_id: '',
            payment_plan_id__r: '',
            payment_amount: '',
        };
        const { lookupData, objApiName } = hookParam;

        // 只处理lookupData，即选择订单的情况
        if (this._isCalculatePayment && lookupData) {
            const {collection_type} = hookParam.dataGetter.getMasterData();
            const {
                receivable_amount,
                payment_money_to_confirm,
                _id
            } = this.parseDetailFields(lookupData, collection_type);
            const details = hookParam.dataGetter.getDetail(objApiName);

            // 本次回款金额 = 订单的待回款金额 - 订单的待确认回款金额 - 已选订单的回款金额
            const subRes = crmUtil.accSub(receivable_amount || 0, payment_money_to_confirm || 0);
            const selPaymentAmount = this._getSelPaymentAmount(_id, details) || 0;
            cellData.payment_amount = crmUtil.accSub(subRes, selPaymentAmount);
        }

        return cellData;
    }

    /**
     * 获取回款计划修改后的字段数据
     * @param {*} hookParam
     * @returns
     */
    _getFormatDataForPaymentPlanChange(hookParam) {
        const cellData = {};
        const { dataIndex, lookupData, objApiName } = hookParam;

        // 只处理lookupData，即选择计划的情况
        if (!lookupData) {
            return cellData;
        }

        const { plan_payment_amount } = lookupData;
        const oldData = hookParam.dataGetter.getData(
            objApiName,
            dataIndex[0]
        );
        const { _id } = oldData;

        // 新增的明细 && 回款计划金额有值 && 可以赋值
        if (
            !_id &&
            plan_payment_amount &&
            this._isCalculatePayment
        ) {
            cellData.payment_amount = plan_payment_amount;
        }

        return cellData;
    }

    /**
     * 处理回款金额计算方式
     * @param {*} hookParam
     */
    _resolveFields(hookParam) {
        const { dataGetter, objApiName, masterObjApiName } = hookParam;

        const describe = dataGetter.getDescribe(objApiName);
        const { payment_amount } = describe.fields;
        // 本次回款金额配置了默认值或默认公式。或选择了预付款+返利的回款方式？
        const { default_value, default_is_expression } = payment_amount;
        if (default_value || default_is_expression) {
            this._isCalculatePayment = false;
        }

        // 判断是否开启红字回款
        const {collection_type} = dataGetter.getDescribe(masterObjApiName)?.fields;
        if (!_.isUndefined(collection_type)) {
            this._isOpenRedPayment = true;
        }
        console.log('_isOpenRedPayment', this._isOpenRedPayment);
    }

    /**
     * 主对象改变
     * @param {*} context
     * @param {*} hookParam
     */
    async _formChangeEnd(context, hookParam) {
        const changeData = hookParam.collectChange();

        // 处理客户
        const blurField = changeData.blurField;
        if (blurField === 'account_id' || blurField === 'collection_type') {
            await this._handleAccountIdChange(hookParam);
        }

        // 主对象回款金额
        if (blurField === 'amount' && this._isOpenRedPayment) {
            const amount = this._parsePaymentSign(changeData.masterUpdate[blurField], hookParam);
            hookParam.dataUpdater.updateMaster({amount});
            await hookParam.triggerCalAndUIEvent({
                objApiName: hookParam.masterObjApiName,
                changeFields: [blurField]
            })
        }
    }

    _mdRenderBefore(context, hookParam) {
        const addBtns = [
            {
                label: '+' + this.i18n('添加'),
                action: '_batchAddDataHandle',
                callBack: this._batchAddDataHandle.bind(this),
            },
        ];
        const res = {
            // 表格右上角操作按钮
            buttons: {
                retain: [], // 需要保留的按钮
                add: addBtns, // 新增按钮
            },
        };

        this._resolveFields(hookParam);

        return res;
    }

    async _mdRenderAfter(context, hookParam) {
        await this._addOneDataToTable(hookParam);
        this.setCollectionTypeRelateFieldStatus(hookParam);
    }

    _mdBatchAddBefore(context, hookParam) {
        const { account_id, collection_type } = hookParam.dataGetter.getMasterData();
        const {account_id: accountIdField, collection_type: collectionTypeField} = hookParam.dataGetter.getDescribe(hookParam.masterObjApiName)?.fields;

        if (!account_id || (this._isOpenRedPayment && !collection_type)) {
            const {label} = !account_id ? accountIdField : collectionTypeField;
            context.api.alert($t('请先选择') + label);
            context.skipPlugin();
            return;
        }

        return {
            beforeRequest: this._parseBeforeRequest(hookParam, context),
            extendParam: {
                isSupportCount: true
            }
        };
    }

    _mdBatchAddAfter(context, hookParam) {
        const { addDatas = [], lookupDatas = [], lookupField = {}, objApiName } = hookParam;
        const { target_api_name } = lookupField;

        const handleObjs = ['PaymentPlanObj'].concat(Object.values(COLLECTION_TYPE_MAP.RELATE_APINAME));
        // 数据来源不是回款计划和销售订单的直接返回
        if (!handleObjs.includes(target_api_name)) {
            return { newDatas: addDatas };
        }

        const details = hookParam.dataGetter.getDetail(objApiName);
        const newDatas = addDatas.map((item, index) => {
            const order = lookupDatas[index];
            const { account_id, account_id__r } = order;

            // 合并格式化数据
            const paymentAmount = this._getPaymentForMd(order, details, hookParam);
            const formatData = {
                account_id,
                account_id__r,
                payment_amount: paymentAmount,
            };

            return {
                ...item,
                ...formatData,
            };
        });

        return { newDatas };
    }

    _mdBatchAddEnd(context, hookParam) {
        this.setCollectionTypeRelateFieldStatus(hookParam);
    }

    _mdCopyEnd(context, hookParam) {
        this.setCollectionTypeRelateFieldStatus(hookParam);
    }

    // 红单回款，设置订单只读、发货单必填；蓝字回款，设置发货单只读，订单必填；
    // setReadOnly 支持设置所有行，setRequired 不支持，这里需要在所有从对象的数据变化时，更新相应行的状态；
    setCollectionTypeRelateFieldStatus(hookParam, ids) {
        if (!this._isOpenRedPayment) return;
        const {collection_type = COLLECTION_TYPE_BLUE} = hookParam.dataGetter.getMasterData();
        const readOnlyFields = Object.entries(COLLECTION_TYPE_MAP.RELATE_DETAIL_ID)
            .filter(([key, value]) => key !== collection_type)
            .map(([key, value]) => value);

        const dataIndex = ids || hookParam.dataGetter.getDetail().map(({rowId}) => rowId);
        // 红字回款不可选回款计划
        if (collection_type === COLLECTION_TYPE_RED) readOnlyFields.push('payment_plan_id');
        hookParam.dataUpdater.setReadOnly({
            // dataIndex: 'all',
            dataIndex,
            fieldName: readOnlyFields,
            status: true,
            objApiName: hookParam.objApiName
        });
        hookParam.dataUpdater.setRequired({
            dataIndex,
            fieldName: [COLLECTION_TYPE_MAP.RELATE_DETAIL_ID[collection_type]],
            status: true,
            objApiName: hookParam.objApiName
        });
    }

    _mdEditBefore(context, hookParam) {
        return {
            beforeRequest: this._parseBeforeRequest(hookParam, context)
        };
    }

    // 查找订单、退货单，根据客户筛选
    _parseBeforeRequest(hookParam, context) {
        const {account_id} = hookParam.dataGetter.getMasterData();
        const preBeforeRequest = context?.preData?.beforeRequest;

        const filter = {
            field_name: 'account_id',
            field_values: [account_id],
            operator: 'EQ',
        };

        return (rq) => {
            if (preBeforeRequest) {
                rq = preBeforeRequest(rq);
            }
            const search_query_info = JSON.parse(rq.search_query_info);
            const filters = search_query_info.filters;
            if (Array.isArray(filters)) {
                filters.push(filter);
            } else {
                search_query_info.filters = [filter];
            }

            rq.search_query_info = JSON.stringify(search_query_info);

            return rq;
        }
    }

    async _mdEditAfter(context, hookParam) {
        const { fieldName, changeData, dataIndex } = hookParam;
        let cellData = {};

        // 处理修改销售订单和回款计划
        if (Object.values(COLLECTION_TYPE_MAP.RELATE_DETAIL_ID).includes(fieldName)) {
            // 订单、退货单
            cellData = this._getFormatDataForOrderChange(hookParam);
        } else if (fieldName === 'payment_plan_id') {
            cellData = this._getFormatDataForPaymentPlanChange(hookParam);
        } else if (fieldName === 'payment_amount') {
            cellData = {
                [fieldName]: this._parsePaymentSign(changeData[dataIndex[0]][fieldName], hookParam)
            }
        }

        if (!_.isEmpty(cellData)) {
            const index = dataIndex[0];
            const currentChangeData = changeData[index];
            Object.assign(currentChangeData, cellData);
        }
    }

    getHooks() {
        return [
            {
                event: 'form.change.end',
                functional: this._formChangeEnd.bind(this),
            },
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this),
            },
            {
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this),
            },
            {
                event: 'md.batchAdd.before',
                functional: this._mdBatchAddBefore.bind(this),
            },
            {
                event: 'md.batchAdd.after',
                functional: this._mdBatchAddAfter.bind(this),
            },
            {
                event: 'md.batchAdd.end',
                functional: this._mdBatchAddEnd.bind(this),
            },
            {
                event: 'md.copy.end',
                functional: this._mdCopyEnd.bind(this),
            },
            {
                event: 'md.edit.before',
                functional: this._mdEditBefore.bind(this),
            },
            {
                event: 'md.edit.after',
                functional: this._mdEditAfter.bind(this),
            },
        ];
    }

    apply() {
        return this.getHooks();
    }
}
