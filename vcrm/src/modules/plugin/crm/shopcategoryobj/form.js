import Base from 'plugin_base';

/**
 * 产品对象，新建
 */
export default class ShopCategoryForm extends Base{
  constructor(...rest) {
    super(...rest);
  }

  _formRenderBefore(plugin, param) {
    const { dataGetter } = param;
    const fieldEditBeforeCallbacks =  {
      pid: [() => {
          return new Promise(resolve => {
              resolve({
                  Value: {
                    beforeRequest:(rq) => {
                      const masterData = dataGetter.getMasterData();
                      if (masterData.store_id) {
                        let searchQueryInfo = JSON.parse(rq.search_query_info) || {};
                        let filters = searchQueryInfo.filters || [];
                        filters.push({
                          field_name: 'store_id',
                          field_values: [masterData.store_id],
                          operator: 'EQ'
                        });
                        searchQueryInfo.filters = filters;
                        rq.search_query_info = JSON.stringify(searchQueryInfo);
                      }
                      return rq;
                    },//改请求参数
                  }
              });
          });
      }],
    };

    return {
      fieldEditBeforeCallbacks,
      isSkipLayoutFieldStateCheck: true, // 设置这个，必填字段才能设置只读
    };
  }

  _formRenderAfter(plugin, param) {
    const { dataUpdater, dataGetter, formType } = param;
    const actionApiName = dataGetter.getOptions('actionApiName');

    // 新建
    if (formType === 'add') {
      // 新建不能修改商店
      const readOnlyFields = ['store_id'];
      // 新建平级和新建下级，父级类目，顶部新建按钮可以修改
      if (
        actionApiName === 'Add_button_default_child' || 
        actionApiName === 'Add_button_default_same'
      ) {
        readOnlyFields.push('pid');
      }
      dataUpdater.setReadOnly({
        fieldName: readOnlyFields,
        status: true
      });
    }
  }

  getHook() {
    return [
      {
        event: "form.render.before",
        functional: this._formRenderBefore.bind(this)
      },
      {
        event: "form.render.after",
        functional: this._formRenderAfter.bind(this)
      },
    ];
  }
}
