/*
 * @Descripttion: 可售范围-可售客户
 * @Author: chaoxin
 * @Date: 2022-06-22 10:22:26
 * @LastEditors: chaoxin
 * @LastEditTime: 2023-12-14 11:26:52
 */

import Base from 'plugin_base';
import FilterTabs from '../../../common/filtertabs';
import FilterPickData from '../../../common/filter_pickdata';

export default class Plugin extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
        let me = this;
        // 初始化子插件
        const filterTabsConfig = {
            field: 'product_range',
            tabs: {
                filter: {
                    options: {
                        apiname: 'ProductObj',
                        addFilterApiname: CRM.util.isGrayScale('CRM_FILTER_OWNER_DEPARTMENT') ? [] : ['owner_department'],
                        layoutMode: 'flex',
                        getCompStyle(levelIdx) {
                            if (levelIdx === 0) {
                                return {
                                    flex: '0 0 216px'
                                };
                            }
                            if (levelIdx === 1) {
                                return {
                                    flex: '0 0 120px'
                                };
                            }
                            return {
                                flex: '1 0 260px'
                            };
                        },
                        formatFieldType(field) {
                            if (field.api_name === 'category') {
                                field.optionType = 'cascader'
                            }
                            return field.type;
                        },
                        filterCusOptions: {
                            extra: {
                                async setValueBefore(defaultval, options) {
                                    defaultval = await me.initDefaultValue(defaultval);
                                    if (!options.props.multiple) {
                                        defaultval = defaultval[0];
                                    }
                                    return defaultval;
                                },
                                getValueBefore(val, options) {
                                    if (options.props.multiple) {
                                        let res = val.map(item => {
                                            return _.isArray(item) ? item.slice(-1)[0] : item;
                                        });
                                        return res;
                                    } else {
                                        let _val = val;
                                        let len = _val.length;
                                        let res = len > 1 ? _val.slice(-1) : _val;
                                        return res;
                                    }
                                },
                                showAllLevels: false,
                            },
                            setCascaderProps(field, compare) {
                                return {
                                    checkStrictly: true,
                                    expandTrigger: 'click',
                                    label: 'name',
                                    value: 'code',
                                    children: 'children',
                                }
                            },
                            getCascaderOptions(field) {
                                return me.category_options || [];
                            }
                        }
                    }
                },
                md: {
                    label: this.i18n('指定产品')
                }
            }
        };
        this.filterTabs = new FilterTabs(filterTabsConfig, this);
        const filterPickData = new FilterPickData('product_id');
        this.cacheChildren([this.filterTabs, filterPickData]);
    }
    async getCategoryList() {
        let res = await CRM.util.getCategoryDataList()
        let treeData = this.transToTreeData(res);
        return treeData;
    }
    transToTreeData(nodeList) {
        // 创建 ID 到节点的映射表
        const nodeMap = {};
        nodeList.forEach(node => {
            // 复制节点数据，避免修改原始数据
            nodeMap[node._id] = { ...node, children: null, leaf: true };
        });

        // 存储根节点
        const rootNodes = [];

        // 构建树结构
        nodeList.forEach(node => {
            const mappedNode = nodeMap[node._id];
            const parentId = node.pid;

            // 如果父节点不存在或 pid 为空，则视为根节点
            if (!parentId || !nodeMap[parentId]) {
                rootNodes.push(mappedNode);
            } else {
                // 将当前节点添加到父节点的 children 中
                nodeMap[parentId].children ? nodeMap[parentId].children.push(mappedNode) : nodeMap[parentId].children = [mappedNode];
                nodeMap[parentId].leaf = false;
            }
        });

        return rootNodes;
    }
    // 查询当前分类节点父节点的整个树数据
    getCategoryLineageById(codeList) {
        return new Promise((resolve, reject) => {
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/product_category/service/lineage',
                data: {
                    codeList,
                    mode: 'tree',
                    includeBrother: false
                },
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        resolve(res?.Value?.categoryTreeList || [])
                    } else {
                        reject()
                        CRM.util.alert(res.Result.FailureMessage);
                    }
                }
            }, {
                errorAlertModel: 1
            })
        })
    }
    _resetFilterTabsOption(param) {
        let result = {};

        // 判断是否存在价目表从对象
        if (
            param.dataGetter.getDescribe('AvailablePriceBookObj') !== undefined
        ) {
            const _getPriceBookComp = function() {
                return class PriceBook {
                    constructor(options) {
                        this.options = options;
                    }

                    getValue() {
                        return 'PRICE_BOOK';
                    }

                    setValue() {}
                };
            }
            result = {
                tabs: {
                    md: {
                        index: 4,
                    },
                    pricebook: {
                        type: 'PRICE_BOOK',
                        label: this.i18n('跟随适用价目表范围'),
                        index: 3,
                        isHide: !CRM._cache.openAvailablePriceBook, // 灰度价目表改造 && 未开启 才隐藏
                        selected: false,
                        component: _getPriceBookComp()
                    }
                }
            }
        }

        return result;
    }

    _mdRenderBefore(plugin, param) {
        console.log('parent md.render.before ----------------');
        return {
            __execResult:{
                filterBatchEditFields: ['product_id']
            },
            __mergeDataType:{
                array: 'concat'
            }
        }
    }

    async _filtertabsInitBefore(plugin, param) {
        if (param.objApiName === 'AvailableProductObj') {
            const options = this._resetFilterTabsOption(param.param);
            try {
                this.category_options = await this.getCategoryList();
            } catch (error) {
                console.error(error);
            }
            return {
                options
            }
        }
    }
    async initDefaultValue(ids) {
        let default_val = [];
        function _fn(list, path) {
            if (!_.isArray(list)) return;
            list.forEach(item => {
                if (!item || !item.code) return;
                const arr = [...path, item.code];
                if (item.children && item.children.length) {
                    _fn(item.children, arr);
                } else {
                    default_val.push(arr);
                }
            });
        }
        if (ids.length) {
            let res = await this.getCategoryLineageById(ids.flat())
            _fn(res, []);
        }
        return default_val;
    }
    getHook() {
        return [
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            },
            {
                event: 'filtertabs.init.before',
                functional: this._filtertabsInitBefore.bind(this)
            },
        ]
    }
}
