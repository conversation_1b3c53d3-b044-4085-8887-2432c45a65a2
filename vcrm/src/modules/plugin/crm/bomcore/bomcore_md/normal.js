/**
 * @Desc: BOMCore 从对象
 * <AUTHOR>
 * @date 2023/7/1
 */

import Vue from 'vue'
import Base from 'plugin_base'
import LeftTreeCom from './left_tree'
import SelectSearch from './select_search'
import DragSplitter from './dragSplitter/index';
import PPM from 'plugin_public_methods'
import './bomcore_md.less'

export default class MD extends Base {

    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
        this._cacheBasicData = null;
        this._allTableData = {};  // 所有业务类型数据
        this._leftTreeComs = {};  // 左树组件
        this._cacheDel = [];       // 缓存删除的数据
        this.recordType = 'default__c';
        let config = pluginParam.bizStateConfig['multiplexed_bom_mode']
        this.isOpenCpqModeBom = config === '1';
    }

    options() {
        return {
            defMasterFields: {
                form_product_id: 'product_id',
                form_category: 'category'
            },
            defMdFields: {
                product_id: 'product_id',
                order_field: 'order_field',
                price_mode: 'price_mode',
                node_bom_core_version: 'node_bom_core_version',
                node_bom_core_type: 'node_bom_core_type',
                core_id: 'core_id',
                related_core_id: 'related_core_id',

                is_required: 'is_required',
                selected_by_default: 'selected_by_default',
                price_editable: 'price_editable',
                amount_editable: 'amount_editable',
                product_group_id: 'product_group_id',
                is_package: 'is_package',
                pricing_period: 'pricing_period',

            },
        }
    }

    // 缓存表格显示字段
    cacheDescribe(param) {
        let mdApiName = this.mdApiName;
        let des = param.dataGetter.getDescribe(mdApiName).fields;
        let fields = param.dataGetter.getLayoutFields(mdApiName, this.recordType);
        this._mdFields = fields.map(f => des[f]);
    }

    async _mdRenderAfter(plugin, param){
        let {product_group_id} = this.getAllFields();
        // 隐藏产品组合字段
        param.UI.hideColumns([product_group_id], this.mdApiName, this.recordType)
    }

    async _mdRenderBefore(plugin, param) {
        let {product_id, node_bom_core_version} = this.getAllFields();
        this.mdApiName = param.objApiName;
        let power = param.dataGetter.getDescribeLayout().objectDescribe.funButton;
        let createGroupPower = power.includes("CreateGroup");
        let setGroupPower = power.includes("SetGroup");
        let detail = param.dataGetter.getDetail(this.mdApiName);
        this._addLable(detail, this.mdApiName, param);
        // let exportPower = power.includes("Export");
        let _fn = (trData) => {
            let add = [];
            let delBtn = ['insertHandle', 'copyRowHandle'];
            let resetBtn = {
                'delRowHandle': {
                    isFold: !this.isOpenCpqModeBom
                }
            };
            let btns = [{
                show: !trData.isGroup && !trData._isChildBom && !this.isOpenCpqModeBom,
                action: "addChildrenHandle",
                label: $t("添加子产品"),
                callBack: this.addChildrenHandle.bind(this)
            }, {
                show: !trData.isGroup && !trData._isChildBom && createGroupPower && !this.isOpenCpqModeBom,
                action: "addGroupHandle",
                label: $t("创建分组"),
                callBack: this.addGroupHandle.bind(this)
            }, {
                show: !trData.isGroup,
                action: "trInsertHandle",
                label: $t("在下方添加产品"),
                callBack: this.trInsertHandle.bind(this),
            }, {
                show: trData.isGroup && setGroupPower,
                action: "addChildrenToGroupHandle",
                label: $t("向分组添加产品"),
                callBack: this.addChildrenToGroupHandle.bind(this)
            }, {
                show: trData.isGroup && setGroupPower,
                action: "editGroupHandle",
                label: $t("设置"),
                callBack: this.editGroupHandle.bind(this)
            }, {
                show: trData._isChildBom && this.isOpenCpqModeBom,
                action: 'openBomCoreObjDetailHandle',
                label: $t('查看'),
                callBack: this.openBomCoreObjDetailHandle.bind(this)
            }]

            add = btns.filter((item, i) => {
                return item.show && !trData._isChildBomChildren;
            });
            if (trData._isChildBomChildren) {
                delBtn.push('delRowHandle');
            }
            let visibleCount = 2;
            add = add.map((item, idx) => {
                if (idx >= visibleCount) {
                    item.isFold = true;
                }
                delete item.show;
                return item;
            });
            return {
                add,
                del: delBtn,
                reset: resetBtn
            };
        };
        if (!plugin.preData) plugin.preData = {};
        this.getGroupDes();
        let addBtn = [{
            label: this.i18n('添加产品'),
            action: 'addChildrenHandle',
            callBack: this.addChildrenHandle.bind(this)
        },];
        if (createGroupPower) {
            addBtn.unshift({
                label: this.i18n('添加分组'),
                action: 'addGroupHandle',
                callBack: this.addGroupHandle.bind(this)
            })
        }
        if (param.formType === 'edit' || param.formType === 'clone') {
            await this.fetchBomForEdit(param)
        }
        this.cacheDescribe(param);
        let batchButtonsAdd = [];
        // 批量导出按钮
        // if (exportPower) {
        //     batchButtonsAdd = [{
        //         label: this.i18n('导出'),
        //         action: 'batchExportDataHandle',
        //         callBack: this.batchExportDataHandle.bind(this)
        //     }]
        // }
        return {
            __execResult: {
                treeConfig: {
                    showCheckBox: true,  // 子件显示复选框
                    expend: true,  // 展开收起
                    mainFieldName: product_id
                },
                operateBtns: [_fn],
                // headerSlot: [this.initHeader.bind(this, plugin, param)],
                titSlot: {
                    default__c: this.initHeader.bind(this, plugin, param)
                },
                tableLeftSlot: [this.initLeft.bind(this, plugin, param)],
                // footerSlot: this.initFooter.bind(this, plugin, param),
                buttons: { // 处理从xx添加入口
                    retain: [],
                    add: addBtn,
                },
                filterBatchEditFields: [node_bom_core_version],
                batchButtons: {
                    del: ['copySelectedHandle'],
                    add: batchButtonsAdd
                },
                columnRenders: this.formatColumnRender(),
                fakeFields: this.getFakeFields(plugin, param), // 添加packageIcon
            },
            __mergeDataType: {
                array: 'concat'
            }
        }
    }

    // 添加自定义列。
    getFakeFields(plugin, param){
        let me = this;
        let columns = []
        // 点击icon, 查看复用BOM
        columns.push({
            // pos: 'after',
            field_name:'product_id',
            fields:[
                {
                    fixedIndex: 0,
                    is_readonly:true,
                    api_name:'packageIcon',
                    width: 40,
                    dataType: 1,
                    nocolResize: true,
                    label: '',
                    fixed: true,
                    render:function (cellValue, cellType, trData) {
                        return trData._isChildBom ? `<span class="package-icon open-child-bom-btn ${me.isOpenCpqModeBom ? 'disabled' : ''}"  data-action="openChildBom" title="${$t('查看复用BOM')}"></span>` : '';
                    },
                    actionCallBacks: {
                        openChildBom: this.openChildBom.bind(this)
                    }
                }
            ]
        })
        return columns;
    }

    /**
     * 自定义产品名称列的render
     */
    formatColumnRender() {
        return [{
            product_id(cellValue, trData) {
                let r = `<span>${cellValue}</span>`;
                if(trData.isGroup){
                    r = `<span class="fx-icon-fold2"></span> ` + r;
                }
                return r;
            }
        }];
    }

    getGroupDes() {
        let _this = this;
        CRM.util.fetchDescribe("ProductGroupObj", {}, function (res) {
            _this.groupDes = res.objectDescribe.fields;
        });
    }

    // 渲染左树
    initLeft(plugin, param, $wrapper, obj) {
        let mdApiName = param.objApiName;
        let recordType = obj?.recordType || this.recordType;
        this.initData(recordType);
        // let basicData = param.getRowBasicData(mdApiName, recordType);
        // let mdData = param.dataGetter.getDetail(mdApiName);
        $($wrapper).addClass('crm_bomCore_leftTree');
        $($wrapper).append(`<div></div>`);
        let _comp = Vue.extend({
            props: ['clickNodeHook', 'openChildBomHook', 'loadAllDataHook'],
            components: {
                DragSplitter,
                LeftTreeCom
            },
            template: `
                <drag-splitter :width="width" :max-width="maxWidth" :min-width="minWidth">
                    <left-tree-com :disabled="${this.isOpenCpqModeBom}" class="crm-bomcore-md" ref="leftTree" slot="left" :clickNodeHook="clickNodeHook" :openChildBomHook="openChildBomHook" :loadAllDataHook="loadAllDataHook"></left-tree-com>
                </drag-splitter>
            `,
            data() {
                return {
                    width: 260,
                    maxWidth: 400,
                    minWidth: 45
                }
            },

        });

        const comp = new _comp({
            propsData: {
                clickNodeHook: this.clickNodeHook.bind(this),
                openChildBomHook: this.openChildBomHook.bind(this),
                loadAllDataHook: this.loadAllDataHook.bind(this),
            }
        });

        comp.$mount($($wrapper).find('div')[0]);
        this._leftTreeComs[recordType] = comp.$refs.leftTree;
        this.initRoot(param);
        this.initRootChildren(mdApiName, recordType, param);
        this.isOpenCpqModeBom && $($wrapper).hide();
        return {
            destroy() {

            }
        }
    }

    // 查看复用bom
    async openChildBom(currentRow, obj){
        if (this.isOpenCpqModeBom) {
            return;
        }
        let {recordType} = obj;
        let row = this.getDataFromLeft(currentRow.rowId, recordType);
        await this.openChildBomHook(row);
    }

    // 搜索模式 加载更多数据
    async loadAllDataHook(currentRow){
        await this.openChildBomHook(currentRow);
    }

    // 展开子BOM
    async openChildBomHook(currentRow) {
        let {product_id, related_core_id} = this.getAllFields();
        let coreId = currentRow._isChildBom ? currentRow[related_core_id] : this._masterCoreId;
        if (currentRow._isChildBom && !coreId) {
            return this.pluginService.api.alert(this.i18n('请先选择BOM版本'));
        }
        if (currentRow._needReq || currentRow._fromSearch) {
            let mdApiName = this.mdApiName;
            let recordType = currentRow.record_type;
            let childData = await this.addChildBom({row: currentRow, mdApiName, recordType, coreId});
            if (childData) this._setNeedReq(currentRow, false);
        }
        // setTimeout(() => {
        //     this.refreshMd(currentRow);
        // })
    }

    // 左数点击事件
    async clickNodeHook(currentRow) {
        console.log(currentRow);
        this.search.clearSearch();
        this.refreshMd(currentRow);
    }

    // 刷新从对象数据，先删 再加
    refreshMd(data) {

        // 先删掉从对象数据
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'bomcoremd_self'
        });
        let mdApiName = this.mdApiName;
        let curMdData = factory.dataGetter.getDetail(mdApiName);
        this.mergeDataToLeft(curMdData);
        factory.objApiName = mdApiName;
        this._delMdData(mdApiName, factory);
        factory.end();
        // 再加
        setTimeout(() => {
            const factory2 = this.pluginService.api.pluginServiceFactory({
                pluginApiName: 'bomcoremd_self2'
            });
            let saveData = [data];
            if (data.isRoot) {
                saveData = data.children || [];
            }
            saveData = PPM.parseTreeToNormal(saveData);
            factory2.dataUpdater.add(saveData, true);
            let recordType = data.record_type;
            this.initMdData({data: saveData, mdApiName, recordType,}, factory2);
            factory2.end();
        })

        // let saveData = [data];
        // if(data.isRoot){
        //     saveData = data.children || [];
        // }
        // saveData = PPM.parseTreeToNormal(saveData);
        //
        // saveData.forEach(item => {
        //     let f = mdData.find(c => c.rowId === item.rowId);
        //     if(!f) needAdd.push(item)
        // });
        // mdData.forEach(item => {
        //     let f = PPM.getDataByKey(item.rowId, saveData);
        //     if(!f){
        //         factory.dataUpdater.del(mdApiName, item.rowId,)
        //     }
        // });
        // if(needAdd.length) factory.dataUpdater.add(needAdd, true);
        // factory.end();
    }

    getSelectOptions() {
        // let dataType = [1, 101, 31, 32, 34, 38, 39];
        // let dataType = ['LongText', 'Input', 'LookUp', 'Location'];
        let dataType = ['long_text', 'text', 'object_reference', 'location'];
        let fields = this._mdFields.filter(item => dataType.includes(item.type));
        fields.forEach(item => item.value = item.api_name);
        return fields;
    }

    // 渲染搜索组件
    initHeader(plugin, param, $wrapper) {
        $wrapper = $($wrapper);
        // this.initOpenTreeBtn(...arguments);
        $wrapper.append('<div class="crm_bomCore_header"><div class="crm_bomCore_search"></div></div>');
        let selectOptions = this.getSelectOptions();
        const SelectSearchCom = (new (Vue.extend(SelectSearch))({
            propsData: {
                handleSelectHook: this.doSearch.bind(this),
                queryRes: this.queryRes.bind(this),
                selectOptions
            }
        }));
        SelectSearchCom.$mount($wrapper.find('.crm_bomCore_search')[0]);
        this.search = SelectSearchCom;
        return {
            destroy() {
            }
        }
    }

    // 初始化展开按钮；
    initOpenTreeBtn(plugin, param, $wrapper) {
        $wrapper = $($wrapper);
        $wrapper.append(`<div class="crm_bomCore_btnBox"><span class="crm_bomCore_btnLable">${$t('全部展开')}</span><div class="crm_bomCore_openTree"></div></div>`);
        this.renderSwitch(param.objApiName);
        return {
            destroy(){
                this.openTreeSwitch && this.openTreeSwitch.destroy && this.openTreeSwitch.destroy();
                this.openTreeSwitch = null
            }
        }
    }

    // 渲染开关组件；
    renderSwitch(mdApiName) {
        let _this = this;
        this.openTreeSwitch && this.openTreeSwitch.destroy && this.openTreeSwitch.destroy();
        this.openTreeSwitch = FxUI.create({
            wrapper: '.crm_bomCore_openTree',
            template: `<fx-switch
								  v-model="value"
								  size="mini"
								  @change="change"
							  >
						</fx-switch>`,
            data() {
                return {
                    value: true,
                }
            },
            methods: {
                change(data, node, tree) {
                    let factory = _this.pluginService.api.pluginServiceFactory({
                        pluginApiName:'bomcore_openTree'
                    });
                    factory.dataUpdater.updateTreeExpend(data, mdApiName);
                    factory.end();
                },
            }
        })
    }

    doSearch_beforeHook(data) {
        return data;
    }

    // 展示搜索结果
    async doSearch(data) {
        console.log('doSearch=====', data);
        data = await this.doSearch_beforeHook(data);
        this.setLeftSelected(data, data.record_type);
        this.refreshMd(data);
    }

    getSearchDataHook(queryString, currentField) {
        let rootData = this.getRootData(this.recordType);
        return CRM.util.getDataByKeyWord(queryString, currentField, rootData.children || []);
    }

    // 搜索请求匹配数据
    async queryRes(queryString, currentField) {
        if(!this._leftTreeComs || !Object.keys(this._allTableData).length) return;
        let searchData = await this.getSearchDataHook(queryString, currentField);
        let rootData = this.getRootData(this.recordType);
        let allData = rootData.children;
        searchData.forEach(item => {
            item.value = item._isChildBomChildren || !item.crumb_bread_name ? CRM.util.getBomPathFromData(item, allData, 'parent_rowId'): CRM.util.getBomPath(item, true);
        });
        return searchData;
        // if (searchData.length){
        //     // 根据筛选结果，过滤要展示的行；
        //
        //
        // }else{
        //     CRM.util.forEachTreeData([rootData], row => {
        //         row.isShow = false;
        //     })
        // }
    }

    // initFooter(plugin, param, $wrapper) {
    //     let mdApiName = param.objApiName;
    //     $wrapper.append(`<div data-recordtype="${recordType}" class="crm_bom_tree">加载更多</div>`);
    // }

    initData(recordType) {
        this._allTableData[recordType] = {}
    }

    initRoot(param) {
        if(!this._leftTreeComs) return;
        let {form_product_id, product_id} = this.getAllFields();
        let masterData = param.dataGetter.getMasterData();
        PPM.each(this._allTableData, (data, key) => {
            if (!masterData[form_product_id]) return;
            let root = {
                label: masterData[form_product_id + '__r'],
                isRoot: true,
                [product_id]: masterData[form_product_id],
                record_type: key,
                object_describe_api_name: this.mdApiName,
                price_editable: true,
                amount_editable: true,
                enabled_status: true,
                children:[]
            };
            PPM.addRowId(root);
            if (this._rootBomId) {
                root.bom_id = this._rootBomId;
            }
            this._allTableData[key] = root;
            let leftTree = this.getLeftTree(key);
            leftTree.reset(root);
        });
    }

    // 切换根节点
    changeRoot(param){
        let {form_product_id, product_id} = this.getAllFields();
        let masterData = param.dataGetter.getMasterData();
        let mdApiName = this.mdApiName ;
        PPM.each(this._allTableData, (data, key) => {
            if (!masterData[form_product_id]) return;
            let root = {
                label: masterData[form_product_id + '__r'],
                [product_id]: masterData[form_product_id],
            };
            Object.assign(data, root);
            delete data.bom_id;
            this.updateDataToLeft(data, key)

            // let mdData = param.dataGetter.getDetail(this.mdApiName);
            // mdData.forEach(item => {
            //     delete item.parent_bom_id;
            //     delete item.new_bom_path;
            //     delete item._id;
            //     param.dataUpdater.updateDetail(mdApiName, item.rowId, {
            //         parent_bom_id: null,
            //         new_bom_path: null,
            //         _id: null,
            //     })
            // });
            // this.updateDataToLeft(mdData, key)
        });

    }

    // 如果有数据，初始化数据
    initRootChildren(mdApiName, recordType, param) {
        let mdData = param.dataGetter.getDetail(mdApiName);
        if (!mdData.length || !this._leftTreeComs) return;
        let root = this.getRootData(recordType);
        mdData = mdData.filter(c => c.record_type === recordType);
        this._addLable(mdData, mdApiName, param);
        mdData = PPM.parseDataToTree(mdData);
        mdData.forEach(item => {
            item.parent_rowId = root.rowId
            item.enabled_status === null && (item.enabled_status = root.enabled_status)
        });
        mdData = PPM.deepClone(mdData);
        root.children = mdData;
        let leftTree = this.getLeftTree(recordType);
        leftTree.reset(root);
        this.initMdData({data: mdData, mdApiName, recordType,}, param);
    }

    // 重置左树，清空表格
    resetMd(param) {
        this.initRoot(param);
        this.clearMd(param)
    }

    // 清空所有从对象数据并计算
    async clearMd(param) {
        await param.dataUpdater.delDetailAndTrigger(this.mdApiName);
    }

    getMdData() {
        let res = [];
        PPM.each(this._leftTreeComs, lt => {
            let rd = lt.getValue()[0];
            res = res.concat(rd?.children || [])
        });
        return res;
    }

    // 切换父级产品
    async _dataChange_after(plugin, param) {
        let changeData = param.changeData;
        let {form_product_id, form_category} = this.getAllFields();
        let mdData = this.getMdData();
        if (!mdData.length && changeData[form_product_id]) {
            this.resetMd(param);
            return;
        }
        if (mdData.length && (changeData[form_product_id] || changeData[form_category])) {
            // await this.afterChangeProductId(...arguments);
            this.changeRoot(param);
        }
    }

    // 保存草稿，需要把分组数据塞进去一起保存；
    _formRenderBefore(plugin, param){
        let me = this;
        return {
            beforeSaveDraft(obj){
                debugger
                let mdData = me.getMdData();
                let cloneData = PPM.deepClone(mdData);
                cloneData.forEach(item => delete item.parent_rowId);
                obj.details.BOMObj = PPM.parseTreeToNormal(cloneData);
            }
        }
    }

    _formRenderAfter(plugin, param) {
        return new Promise(resolve => {
            // 判断 url 字符串中是否有 eservice_web_serviceBom
            // 有则说明是从服务通的“服务bom”入口新建产品组合对象
            // 此时需要将将 bom类型设置为配置BOM，bom用途设置为服务BOM，并设置为只读
            if (param.formType === 'add' &&
                window.location.hash.indexOf('paasapp/eservice_web_serviceBom') > -1
            ) {
                param.dataUpdater.updateMaster({
                    category: 'configure',
                    purpose: 'service'
                });
                param.dataUpdater.setReadOnly({
                    status:true,
                    fieldName:['category', 'purpose']
                });
            }
            resolve();
        });
    }

    // 切换组合，提示清空明细
    afterChangeProductId(plugin, param) {
        return new Promise(resolve => {
            let changeData = param.changeData;
            let key = Object.keys(changeData);
            let {form_product_id, form_category} = this.getAllFields();
            key = key.find(f => f === form_product_id || f === form_category);
            let flag;
            this.pluginService.api.confirm({
                title: this.i18n("提示"),
                msg: this.i18n("切换父项产品或BOM类型将会清空当前约束明细，确认切换") + '?',
                success: () => {
                    flag = true;
                    this.resetMd(param);
                    resolve();
                },
                cancel: () => {
                    if (flag) return;
                    let oldVal = param.oldData && param.oldData[key];
                    let oldVal__r = param.oldData && param.oldData[key + '__r'];
                    param.dataUpdater.updateMaster({
                        [key]: oldVal || '',
                        [key + '__r']: oldVal__r || '',
                    });
                    resolve();
                }
            });
        })
    }

    // 添加数据
    async _mdAddBefore(plugin, param) {

    }

    // 添加数据
    async _mdAddEnd(plugin, param) {
        await this.parseAddBomData({
            addDatas: param.addDatas,
            lookupDatas: param.lookupDatas
        }, param, plugin);
    }

    _addLable(data, mdApiName, param) {
        let {
            product_id,
        } = this.getAllFields();
        PPM.forEachTreeData(data, item => {
            if(item.isGroup && !item[product_id] && param){
                param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                    [product_id]: item.label,
                    [product_id + '__r']: item.label
                })
            }
            if (item[product_id]) item.label = item[product_id + '__r']
        });
    }

    // 添加子bom标记
    _addBomCore(data, lookupDatas, mdApiName, param) {
        let {
            node_bom_core_version,
            related_core_id,
            node_bom_core_type,
            is_package,
        } = this.getAllFields();
        data.forEach((item, index) => {
            let ld = lookupDatas[index];
            item.pricing_mode = ld.pricing_mode;
            if (PPM.isBom(ld).isPackage) {
                if (ld.core_id) {
                    item[node_bom_core_version] = ld.node_bom_core_version;
                    item[related_core_id] = ld.core_id;
                    item[node_bom_core_type] = ld.node_bom_core_type;
                }else{
                    item[node_bom_core_type] = null;
                }
                item._isChildBom = item[is_package] = true;
                this._setNeedReq(item, true);
                this._addKey(item);
            }
        })
    }

    // 校验子bom成环；只有编辑的时候才校验；
    async checkChildBom(addData, param) {
        if (this._formType !== 'edit') return true;
        let {
            product_id,
            node_bom_core_type,
            related_core_id,
            form_product_id,
            form_category,
        } = this.getAllFields();
        let childBom = addData.filter(item => item[related_core_id]);
        if (!childBom.length) return true;
        let masterData = param.dataGetter.getMasterData();
        let p = {
            masterData: {
                product_id: masterData[form_product_id],
                category: masterData[form_category],
                _id: masterData._id,
            },
            nodeList: childBom.map(item => {
                return {
                    product_id: item[product_id],
                    related_core_id: item[related_core_id],
                    node_bom_core_type: item[node_bom_core_type],
                }
            })
        };
        let res = await this._checkChildBom(p);
        return res === true;
    }

    // server计算产品包价格；
    _checkChildBom(param) {
        let url = `FHH/EM1HNCRM/API/v1/object/bom/service/check_bom_cycle`;
        return PPM.ajax(this.request, url, Object.assign({}, {
            // "masterData": {
            //     "product_id": "5fd8671910299a0001ce6d1e",
            //     "category": "configure",
            //     "_id": "64b78f771ae0f9000127327c"
            // },
            // "nodeList": [
            //     {
            //
            //         "product_id": "64b79c16ff8e01000139ccf4",
            //         "related_core_id": "64b79c16ff8e01000139cc11",
            //         "node_bom_core_type": "configure"
            //     }
            // ]
        }, param))
    }

    // 添加数据时，处理BOM数据
    async parseAddBomData({
                              addDatas = [],
                              lookupDatas = []
                          } = {}, param, plugin) {
        let mdApiName = param.objApiName;
        let recordType = param.recordType;
        this._addLable(addDatas);
        this._addBomCore(addDatas, lookupDatas, mdApiName, param);
        let checkRes = await this.checkChildBom(addDatas, param);
        if (!checkRes) return plugin.skipPlugin();
        this.addActionType(addDatas, 'create');
        let currentRow = this._getCurrentRow(recordType);
        this._currentRow = null;
        let nc;
        // 插入行
        if (this._addType === 'insert') {
            nc = this.insertDataToLeft(addDatas, currentRow.rowId, recordType);
            nc.forEach(item => {
                param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                    parent_rowId: item.parent_rowId,
                    order_field: currentRow.order_field
                })
            });
            this._clearAddType();
        } else {
            let maxNum = this.getMaxNum(currentRow.children || []);
            this.addNumber(addDatas, maxNum, false);
            nc = this.addDataToLeft(addDatas, currentRow.rowId, recordType);
            nc.forEach(item => {
                param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                    parent_rowId: item.parent_rowId
                })
            });
        }
        this.initMdData({data: nc, recordType, mdApiName}, param)
    }

    // 初始化md数据
    initMdData({data = [], recordType, mdApiName} = {}, param) {
        this._addLable(data);
        this.setSomeFieldsVal({changeRow: data, mdApiName, recordType, isForce: true, from: 'add'}, param);
        this.setFieldsReadonly({data, recordType, mdApiName}, param);
        this.setBomValueForStandard({data, recordType, mdApiName}, param)
    }

    getLeftTree(recordType) {
        return this._leftTreeComs[recordType];
    }

    getCurrentRow(recordType) {
        return this._leftTreeComs[recordType].getCurrentRow();
    }

    getRootData(recordType) {
        return this._leftTreeComs[recordType].getValue()[0];
    }

    // 左树添加数据
    addDataToLeft(data, rowId, recordType, clearChildren = false) {
        let leftTree = this.getLeftTree(recordType);
        let r = leftTree.addChildren(data, rowId, clearChildren);
        if (Array.isArray(data)) return r;
        return r[0];
    }

    // 左树选中数据
    setLeftSelected(row, recordType) {
        let leftTree = this.getLeftTree(recordType);
        leftTree.setCurrentKey(row);
    }

    // 左树删除数据
    delDataToLeft(data, recordType) {
        let leftTree = this.getLeftTree(recordType);
        return leftTree.deleteRow(data);
    }

    // 左树插入数据
    insertDataToLeft(data, rowId, recordType) {
        let leftTree = this.getLeftTree(recordType);
        return leftTree.insertChildren(data, rowId);
    }

    // 左树更新数据
    updateDataToLeft(data, recordType) {
        let leftTree = this.getLeftTree(recordType);
        return leftTree.updateRow(data);
    }

    // 左树获取数据
    getDataFromLeft(rowId, recordType, isClone) {
        let leftTree = this.getLeftTree(recordType);
        let res = leftTree.getDataByRowIds(rowId);
        if (isClone) res = PPM.deepClone(res);
        if (Array.isArray(rowId)) {
            return res;
        }
        return res[0];
    }

    // 设置字段只读
    setFieldsReadonly({
                          data = [],
                          mdApiName = '',
                          recordType = ''
                      } = {}, param) {
        let {
            order_field,
            product_id,
            node_bom_core_version,
            is_required,
            selected_by_default,
            price_editable,
            amount_editable,
            pricing_period,

        } = this.getAllFields(mdApiName);
        let isStandard = this.isStandard(param);
        PPM.forEachTreeData(data, item => {
            // 复用bom子件全部字段只读
            if (item._isChildBomChildren) {
                param.dataUpdater.setReadOnly({
                    whiteFieldName: [],
                    dataIndex: item.rowId,
                    objApiName: mdApiName,
                    recordType: recordType,
                    status: true
                });
            } else {
                // 分组只能改序号；
                if (item.isGroup) {
                    param.dataUpdater.setReadOnly({
                        whiteFieldName: [order_field],
                        dataIndex: item.rowId,
                        objApiName: mdApiName,
                        recordType: recordType,
                        status: true
                    });
                } else if (item.parent_rowId) {
                    let f = [product_id];
                    // 普通子件版本只读
                    if (!item._isChildBom) f.push(node_bom_core_version);
                    // 根是标准bom，这些字段只读
                    if (isStandard) {
                        f = f.concat([is_required, selected_by_default, price_editable, amount_editable])
                    }
                    if(item.product_id__ro?.pricing_mode === 'one' || item?.pricing_mode === 'one'){
                        f.push(pricing_period)
                    }
                    param.dataUpdater.setReadOnly({
                        fieldName: f,
                        dataIndex: item.rowId,
                        objApiName: mdApiName,
                        recordType: recordType,
                        status: true
                    });
                }
            }
        });
    }

    // 删除
    _delEnd(plugin, param) {
        if(!param) return;
        let recordType = param.delDatas[0].record_type;
        let leftTree = this.getLeftTree(recordType);
        let delData = param.delDatas.map(item => {
            let r = this.getDataFromLeft(item.rowId, recordType, true);
            delete r.children;
            return r;
        });
        this.updateDelCache(delData);
        leftTree.deleteRow(delData);
    }

    // 更新缓存删除数据
    updateDelCache(delData) {
        this.addActionType(delData, 'delete',);
        PPM.each(delData, item => {
            if (item._isChildBomChildren) return;
            if (item._isChildBom) delete item.children;
            if (item.action_type !== 'create') {
                let f = this._cacheDel.find(c => c.rowId === item.rowId);
                if (item.isGroup) item.delete_child_bom = true;
                if (!f) this._cacheDel.push(item);
            }
        })
    }

    // 插入
    async trInsertHandle(rowData, obj) {
        this._addType = 'insert';
        this._addChildren(rowData, obj);
    }

    // 必须先选父项产品和bom类型才能添加数据
    checkHasRoot(param) {
        let {
            form_product_id,
            form_category
        } = this.getAllFields();
        let masterData = param.dataGetter.getMasterData();
        if (!masterData[form_product_id] || !masterData[form_category]) {
            this.alert(this.i18n('请先选择父项产品和BOM类型'));
            return false;
        }
        return true;
    }
    // 向分组添加产品
    async addChildrenToGroupHandle(rowData, obj) {
        this.addChildrenHandle(rowData, obj)
    }

    openBomCoreObjDetailHandle(rowData, obj) {
        let {product_id, related_core_id} = this.getAllFields();
        CRM.api && CRM.api.show_crm_detail({
            type: 'BomCoreObj',
            data: {
                crmId: rowData[related_core_id],
            }
        });
    }
    _clearAddType() {
        this._addType = null;
    }

    // 添加子产品
    async addChildrenHandle(rowData, obj) {
        this._clearAddType();
        await this._addChildren(...arguments);
    }

    async _addChildren(rowData, obj) {
        let _this = this;
        let {objectApiName, recordType} = obj;
        let mdApiName = this.mdApiName;
        let {
            product_id,
        } = this.getAllFields(mdApiName);
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'bomcoremd_self3'
        });
        if (!this.checkHasRoot(factory)) {
            return factory.end();
        }
        if (rowData) this._currentRow = rowData;
        if (!rowData) rowData = this.getCurrentRow(recordType);
        if (!rowData) return factory.end();
        if ((rowData._isChildBom || rowData._isChildBomChildren) && this._addType !== 'insert') {
            this.pluginService.api.alert(this.i18n('复用BOM不允许添加子件'));
            return factory.end();
        }
        let rootData = this.getRootData(recordType);
        factory.objApiName = mdApiName;
        let masterData = factory.dataGetter.getMasterData();
        factory.pickConfig = {
            beforeRequest: (questParam) => {
                // 添加子件，过滤掉父项产品 
                let ids = [rowData[product_id]];
                let pids = CRM.util.getAllParents(rowData, [rootData], null, null, 'parent_rowId');
                let filterIds = _.union(ids, pids);
                let rq = JSON.parse(questParam.search_query_info);
                rq.filters = rq.filters || [];
                rq.filters.push({
                    field_name: '_id',
                    field_values: filterIds,
                    operator: 'NIN'
                });
                questParam.search_query_info = JSON.stringify(rq);
                return questParam
            },
            master_data: masterData,
            object_data: Object.assign({}, factory.getRowBasicData()),
            extendParam: {
                noAccount: true,
                noButton: true,   // 不显示按钮
                noFetchBom: true, // 不查treev1
                notShowDetail: false,   // 显示详情
                notShowBom: true,   // 不自动弹bom
                notShowAttribute: true,   // 不展示属性
                noCoreVersion: true,   // 不选版本
                bomCoreParams:{
                    beforeRequestBomCore(questParam) {
                          // 标准bom，只能添加标准 bom 子件
                          if (_this.isStandard()) {
                            let f = JSON.parse(questParam.search_query_info);
                            f.filters = f.filters || [];
                            f.filters.push({
                                field_name: 'category',
                                field_values: ["standard"],
                                operator: 'EQ'
                            });
                            questParam.search_query_info = JSON.stringify(f);
                        }
    
                        // 子件定价，只能添加相同定价方式的复用 bom
                        if(masterData.sale_strategy){
                            let f = JSON.parse(questParam.search_query_info);
                            f.filters = f.filters || [];
                            f.filters.push({
                                field_name: 'sale_strategy',
                                field_values: [masterData.sale_strategy],
                                operator: 'EQ'
                            });
                            questParam.search_query_info = JSON.stringify(f);
                        }
                        
                        return questParam;
                    }
                }
            },
           
        };
        if (this._addType === 'insert') {
            factory.insertedRowId = rowData.rowId;
        }
        // 选数据换成cpq组件
        factory.modulePath = 'crm-modules/components/pickselfobject_cpq/pickselfobject_cpq';
        factory.batchPickData({
            fieldName: product_id,
            objApiName: mdApiName,
            recordType,
        });
        factory.end();
    }

    // 添加分组
    async addGroupHandle(rowData, obj, type = 'add') {
        let mdApiName = this.mdApiName;
        let {recordType} = obj;
        this._currentRow = null;
        if (rowData) {
            this._currentRow = rowData
        }
        if (!rowData) rowData = this.getCurrentRow(recordType);
        if (rowData && (rowData._isChildBom || rowData._isChildBomChildren)) return this.pluginService.api.alert(this.i18n('复用BOM不允许添加子件'));
        if (rowData && rowData.isGroup) return this.pluginService.api.alert(this.i18n('只有子件下才能添加分组'));
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'bomcoremd_self4'
        });
        if (!this.checkHasRoot(factory)) {
            return factory.end();
        }
        factory.objApiName = mdApiName;
        this.createPackage({type, recordType, mdApiName}, factory);
    }

    // 编辑分组
    async editGroupHandle(rowData, obj) {
        let mdApiName = this.mdApiName;
        let {recordType} = obj;
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'bomcoremd_self4'
        });
        if (!this.checkHasRoot(factory)) {
            return factory.end();
        }
        factory.objApiName = mdApiName;
        this.createPackage({groupData: rowData, type: 'edit', recordType, mdApiName}, factory);
    }

    // 获取当前行
    _getCurrentRow(recordType) {
        if (this._currentRow) {
            let f = this.getDataFromLeft(this._currentRow.rowId, recordType);
            if (f) return f;
            this._currentRow = null;
        }
        return this.getCurrentRow(recordType);
    }

    /**
     *  @desc 创建分组
     *  @param type: edit/ add
     *  @param from 是否来自监听分组列表编辑或新建
     */
    async createPackage({groupData, type, from, recordType, mdApiName} = {}, param) {
        var _this = this;
        let currentRow = this._getCurrentRow(recordType);
        this._currentRow = null;
        let AddGroup = await this.pluginService.api.import('crm-modules/components/dialog_addgroup/dialog_addgroup');
        this.addGroup = new AddGroup({
            fields: this.groupDes,
            data: groupData,
            parentData: groupData ? param.dataGetter.getData(mdApiName, groupData.parent_rowId) || _this.getDataFromLeft(groupData.parent_rowId, recordType) : currentRow,
            type: type,
            defNumber: this.getMaxNum(currentRow.children || []) + 1,
            // showAddSubBtn: !_this.subData.length,
            showAddSubBtn: true,
            currentRow: currentRow,
            pData: currentRow,
        });
        let _oldGroupData = PPM.deepClone(groupData)
        function _fn(data) {
            data.isGroup = true;
            _this.parseGroupData(data, recordType);
            if (type === 'edit') {
                // 如果修改了分组的启用状态
                if (data.enabled_status !== _oldGroupData.enabled_status || !data.enabled_status) {
                    let rowData = _this.getDataFromLeft(data.rowId, recordType);
                    Object.assign(rowData, {
                        enabled_status: data.enabled_status
                    })
                    _this.setSomeFieldsVal({changeField: 'enabled_status', changeRow: [rowData], mdApiName, recordType, from: 'edit'}, param);
                }
                _this.addActionType(data, 'update', false);
                Object.assign(groupData, data);
                _this.updateGroup(data, mdApiName, param);
                _this.updateDataToLeft(data, recordType);
            } else if (type === 'add') {
                data.parent_rowId = currentRow.rowId;
                _this.addActionType(data, 'create', false);
                _this.parseNewGroup(data, currentRow, param);
                let nc = _this.addDataToLeft([data], currentRow.rowId, recordType);
                param.dataUpdater.add(nc, true);
                _this.setFieldsReadonly({data: nc, recordType, mdApiName}, param)
            }
            param.end();
        }
        // 设置分组
        this.addGroup.on('dialogEnter', function (data) {
            _fn(data);
        });
        // 取消设置
        this.addGroup.on('dialogCancel', function (data) {
            if(groupData){
                _this.parseGroupData(groupData, recordType);
            }
        });
        // 向分组内添加子产品
        this.addGroup.on('addSubObj', function (groupData, dialog) {
            _fn(groupData);
            _this.addChildrenHandle(groupData, {recordType, mdApiName})
        })
    }

    updateGroup(newGroup, mdApiName, param) {
        let {
            product_id,
            order_field,
        } = this.getAllFields();
        param.dataUpdater.updateDetail(mdApiName, newGroup.rowId, {
            [product_id]: newGroup.name,
            [product_id + '__r']: newGroup.name,
            [order_field]: newGroup.order_field,
            enabled_status: newGroup.enabled_status
        });
    }

    // 给数据添加标记
    addActionType(data, type, iclChildren) {
        let list = _.isArray(data) ? data : [data];

        function _fn(datas) {
            _.each(datas, function (item) {
                if (item._isChildBom && item._isChildBomChildren || item._isChildBomChildren) return;
                if (type === 'delete' || type === 'update') {
                    if (item.action_type !== 'create') item.action_type = type;
                } else if (type === 'create') {
                    item.action_type = type;
                }
                if (iclChildren && item.children && item.children.length) {
                    _fn(item.children)
                }
            })
        }

        _fn(list)
    }

    getBasicData(param) {
        if (this._cacheBasicData) return this._cacheBasicData;
        this._cacheBasicData = param.getRowBasicData(this.mdApiName, this.recordType);
        delete this._cacheBasicData.rowId;
        return this._cacheBasicData;
    }

    /**
     * @desc 给分组数据添加树组件所需要的信息
     * @param allData
     */
    parseGroupData(allData, recordType) {
        allData = Array.isArray(allData) ? allData : [allData];
        PPM.forEachTreeData(allData, data => {
            if (data.isGroup) {
                PPM.addRowId(data);
                data.label = data.name;
                data.isGroup = true;
                data.isFake = true;

                data.record_type = recordType;
                data.object_describe_api_name = this.mdApiName;

                // data.trGroupUpIcon = 'crm-md-groupIcon fx-icon-fold2';
                // data.trGroupDownIcon = 'crm-md-groupIcon fx-icon-unfold2';
                // data.bom_id = data.bom_id || data.rowId;
                if (!data.children) data.children = [];
                data.product_id = data.name;
                data.product_id__r = data.name;
            }
        })
    }

    /**
     * @desc 添加新分组数据
     * @param groupData
     * @param parentData
     */
    parseNewGroup(groupData, parentData, param) {
        // Object.assign(groupData, this.getBasicData(param));
    }

    /**
     * @desc 获取当前列表中的最大序号
     * @return {number}
     */
    getMaxNum(data) {
        var datas = data;
        if (!datas.length) return 0;
        var maxNum = _.max(datas, function (item) {
            return Number(item.order_field)
        }).order_field;
        return Number(maxNum || 0)
    }

    /**
     * @desc 添加默认序号
     * @param data
     * @param num
     * @param iclChildren  是否处理children；
     */
    addNumber(data, num = 0, iclChildren = true) {
        var _this = this;
        var datas = data;
        if (!_.isArray(data)) {
            datas = [data]
        }
        _.each(datas, function (item, index) {
            if (num !== undefined) item.order_field = num + index + 1;
            if (item.children && iclChildren) _this.addNumber(item.children);
        })
    }

    // 编辑前事件
    async _editBefore(plugin, param) {
        let mdApiName = param.objApiName;
        let changeField = param.fieldName;
        let recordType = param.recordType;
        let {node_bom_core_version} = this.getAllFields();
        let changeRow = this.getChangeRowFromLeft(recordType, param);
        if (changeField === node_bom_core_version) {
            let masterData = param.dataGetter.getMasterData();
            param.pickConfig = {
                fieldEditCallBack: this.renderSelectCore.bind(this, {
                    row: changeRow[0],
                    mdApiName,
                    recordType,
                    masterData
                })
            }
        }
    }

    // 单元格编辑后；校验数量等字段
    async _editAfter(plugin, param) {
        let mdApiName = param.objApiName;
        let changeField = param.fieldName;
        let recordType = param.recordType;
        let changeRow = this.getChangeRowFromLeft(recordType, param);
        let r1 = this.validAmount(changeField, changeRow, plugin);
        if (!r1) return;
        this.clearCoreVersion({changeField, changeRow, mdApiName, recordType}, param);
        this.setSomeFieldsVal({changeField, changeRow, mdApiName, recordType, from: 'edit'}, param);
        this.addActionType(changeRow, 'update', true);
        this.updateDataToLeft(changeRow, recordType);
        this.setSubQuantity({allData: changeRow, mdApiName, recordType}, param)
    }

    _getDescribe(mdApiName, field, param){
        return param.dataGetter.getDescribe(mdApiName).fields?.[field];
    }

    /**
     * @desc 复用 bom 数量变化时，子产品的数量也要变; 子产品数量 = 默认数量 * 包的数量
     * @param allData
     * @param rootQuantity 更改后的数量
     * @param mdApiName
     */
    setSubQuantity({
                       allData = [],
                       mdApiName = '',
                       recordType,
                   } = {}, param) {

        let des = this._getDescribe(mdApiName, 'amount', param);
        let dp = des?.decimal_places;

        allData.forEach(item => {
            if(item.related_core_id){
                let allData = [item];
                let cloneData = CRM.util.cloneBomData(allData, ['rowId', 'parent_rowId', '__amount', 'amount', 'related_core_id', 'amount_any', 'product_id__r', 'is_package']);
                CRM.util.setChildrenAmount(cloneData, {
                    baseAmount: '__amount',
                    amount: 'amount',
                    amount_any: 'amount_any',
                    related_core_id: 'related_core_id',
                },  (val, row, parentBom) => {
                    val = PPM.formatDecimalPlace(val, dp);  // 格式化小数位
                    let f = PPM.getDataByKey(row.rowId, allData);
                    f.amount = val;
                    param.dataUpdater.updateDetail(mdApiName, f.rowId, {
                        amount: val,
                    });
                    this.updateDataToLeft(f, recordType);
                });
            }
        })
    }


    // 清空版本，需要清掉core_id；切换版本，清掉子件
    clearCoreVersion({changeField = '', changeRow = [], mdApiName, recordType, isForce = false} = {}, param) {
        let {node_bom_core_version, related_core_id, node_bom_core_type} = this.getAllFields();
        if (changeField === node_bom_core_version) {
            changeRow.forEach(item => {
                if (item._isChildBom) {
                    if (!item[node_bom_core_version]) {
                        param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                            [related_core_id]: null,
                            [node_bom_core_type]: null,
                        });
                        item[related_core_id] = null;
                        item[node_bom_core_type] = null;
                    }
                    this._setNeedReq(item, true);
                    this._addKey(item);
                    if (item.children) {
                        let children = PPM.parseTreeToNormal(item.children);
                        this.delDataToLeft(children, recordType);
                        children.forEach(c => {
                            param.dataUpdater.del(mdApiName, c.rowId);
                        });
                    }
                }
            })
        }
    }

    // 是否为标准bom
    isStandard() {
        let {
            form_category
        } = this.getAllFields();
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'bomcoremd_isStandrd'
        });
        let masterData = factory.dataGetter.getMasterData();
        factory.end();
        let category = masterData[form_category];
        return category === 'standard';
    }

    // 设置某些字段值和是否可编辑
    setSomeFieldsVal({changeField = '', changeRow = [], mdApiName, recordType, isForce = false, from} = {}, param) {
        this.changeIsRequired(...arguments);
        this.changePriceMode(...arguments);
        // this.changePriceEditable(...arguments);
        // this.changePrice(...arguments);
        this.changeEnabledStatus(...arguments);
    }

    // 从左树获取更改的数据，clone
    getChangeRowFromLeft(recordType, param) {
        let changeRow = this.getDataFromLeft(param.dataIndex, recordType, true);
        _.each(param.changeData, (val, key) => {
            let f = changeRow.find(c => c.rowId === key);
            Object.assign(f, val);
        });
        return changeRow;
    }

    // 更改数量相关校验
    validAmount(changeField, changeRow, plugin) {
        var validField = ['amount', 'max_amount', 'min_amount', 'increment', 'pricing_period'];
        if (validField.includes(changeField)) {
            let r = CRM.util.validAmountBeforeSubmit(null, changeRow, null, changeField, true);
            if (!r) {
                plugin.skipPlugin();
                return false;
            }
        }
        return true;
    }

    // 改的是必填 必选为是，默认选中也改为是，且禁止编辑；
    changeIsRequired({changeField, changeRow, mdApiName, recordType, isForce} = {}, param) {
        if (changeField === 'is_required' || isForce) {
            PPM.forEachTreeData(changeRow, item => {
                if (item.isGroup || this._isChildBomChildrenFn(item)) return;
                if (item.is_required) {
                    param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                        selected_by_default: true
                    });
                    item.selected_by_default = true;
                }
                param.dataUpdater.setReadOnly({
                    fieldName: ['selected_by_default'],
                    dataIndex: item.rowId,
                    objApiName: mdApiName,
                    recordType: recordType,
                    status: item.is_required
                });
            })
        }
    }

    // 如果改的价格不为0，且父级价格不可编辑，将该行的数量是否可编辑改为否，且置灰；
    // changePrice({changeField, changeRow, mdApiName, recordType, isForce} = {}, param) {
    //     if (changeField === 'adjust_price' || isForce) {
    //         PPM.forEachTreeData(changeRow, (item, pd, realPd) => {
    //             if (item.isGroup) return;
    //             let pData = pd?.isGroup ? realPd : pd || this._getRealParent(item, recordType);
    //             if (pData && !pData.price_editable) {
    //                 let isZero = Number(item.adjust_price) === 0;
    //                 if (!isZero) {
    //                     param.dataUpdater.updateDetail(mdApiName, item.rowId, {
    //                         amount_editable: false
    //                     });
    //                     item.amount_editable = false;
    //                 }
    //                 param.dataUpdater.setReadOnly({
    //                     fieldName: ['amount_editable'],
    //                     dataIndex: item.rowId,
    //                     objApiName: mdApiName,
    //                     recordType: recordType,
    //                     status: !isZero
    //                 });
    //             }
    //         })
    //     }
    // }

    _getRealParent(row, recordType) {
        if (!row.parent_rowId) return;
        let pd = this.getDataFromLeft(row.parent_rowId, recordType);
        if (pd.isGroup) pd = this.getDataFromLeft(pd.parent_rowId, recordType);
        return pd;
    }

    // 是否是子bom的子产品；
    _isChildBomChildrenFn(data) {
        return data._isChildBom && data._isChildBomChildren || data._isChildBomChildren;
    }

    // 如果父级的价格是否可编辑改为否，则其下的所有子节点，如果价格为0，则价格不可编辑，数量可编辑；价格不为0，则价格、数量都设置为不可编辑，且禁止修改；
    // changePriceEditable({changeField, changeRow, mdApiName, recordType, isForce} = {}, param) {
    //     if (changeField === 'price_editable' || isForce) {
    //         changeRow.forEach(item => {
    //             if (item.isGroup) return;
    //             PPM.forEachTreeData([item], (c, pd, realPd) => {
    //                 if (c.isGroup || this._isChildBomChildrenFn(c)) return;
    //                 let pData = pd?.isGroup ? realPd : pd || this._getRealParent(c, recordType);
    //                 let val = pData.price_editable;
    //                 if (!val) {
    //                     param.dataUpdater.updateDetail(mdApiName, c.rowId, {
    //                         price_editable: val
    //                     });
    //                     c.price_editable = val;
    //                     // 子级价格为0，价格不可编辑，数量可编辑；
    //                     if (Number(c.adjust_price) === 0) {
    //                         param.dataUpdater.setReadOnly({
    //                             fieldName: ['price_editable'],
    //                             dataIndex: c.rowId,
    //                             objApiName: mdApiName,
    //                             recordType: recordType,
    //                             status: true
    //                         });
    //                     } else {
    //                         param.dataUpdater.updateDetail(mdApiName, c.rowId, {
    //                             amount_editable: val
    //                         });
    //                         c.amount_editable = val;
    //                         param.dataUpdater.setReadOnly({
    //                             fieldName: ['price_editable', 'amount_editable'],
    //                             dataIndex: c.rowId,
    //                             objApiName: mdApiName,
    //                             recordType: recordType,
    //                             status: true
    //                         });
    //                     }
    //                 } else {
    //                     param.dataUpdater.setReadOnly({
    //                         fieldName: ['price_editable', 'amount_editable'],
    //                         dataIndex: c.rowId,
    //                         objApiName: mdApiName,
    //                         recordType: recordType,
    //                         status: false
    //                     });
    //                 }
    //             });
    //         })
    //     }
    // }

    // 定价模式为价目表价格时，价格 清空且不可编辑；
    // 1：配置价格；2：价目表价格
    changePriceMode({changeField, changeRow, mdApiName, recordType, isForce} = {}, param) {

        function _setPrice(item) {
            if (item.price_mode == '2') {     //  价目表价格
                param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                    adjust_price: null
                });
                item.adjust_price = null;
            }
            param.dataUpdater.setReadOnly({
                fieldName: ['adjust_price'],
                dataIndex: item.rowId,
                objApiName: mdApiName,
                recordType: recordType,
                status: item.price_mode == '2'
            });
        }

        if (changeField === 'price_mode' || isForce) {
            PPM.forEachTreeData(changeRow, item => {
                if (item.isGroup || this._isChildBomChildrenFn(item)) return;
                _setPrice(item);
            })
        }
    }

    changeEnabledStatus({changeField, changeRow, mdApiName, recordType, isForce, from} = {}, param) {
        let _this = this;
        if (changeField === 'enabled_status' || isForce) {
            this.traverseTree(changeRow, (node, pNode, pData) => {
                let parent = pNode;
                if (from === 'add') {
                    parent = param.dataGetter.getData(mdApiName, node.parent_rowId) || _this.getDataFromLeft(node.parent_rowId, recordType);
                }
                if (!parent || this._isChildBomChildrenFn(parent) || (from === 'edit' && this._isChildBomChildrenFn(node))) return;
                if (!parent.enabled_status && !node.enabled_status) return;
                _this.setEnabledStatus(node, parent, from, mdApiName, recordType, param)
            })
        }
    }
    traverseTree(data, callback) {
        if (!Array.isArray(data)) return;
        function traverse(nodes, pNode, pData) {
            nodes.forEach(node => {
                callback(node, pNode, pData);
                if (node.children) {
                    traverse(node.children, node, node.isGroup ? pData : node );
                }
            });
        }
        traverse(data, null, null);
    }
    setEnabledStatus(node, pData, from, mdApiName, recordType, param) {
        if (!pData.enabled_status) {
            param.dataUpdater.updateDetail(mdApiName, node.rowId, {
                enabled_status: pData.enabled_status
            });
            node.enabled_status = pData.enabled_status;
            this.updateDataToLeft(node, recordType);
        }
        param.dataUpdater.setReadOnly({
            fieldName: ['enabled_status'],
            dataIndex: node.rowId,
            objApiName: mdApiName,
            recordType: recordType,
            status: node.isGroup ? true : !pData.enabled_status
        });
    }
    /**
     * @desc 校验分组 ; 分组中的必选子产品个数 <= 分组的最大可选个数；分组中的子产品个数 >= 分组的最少可选个数
     */
    validGroupData(data) {
        var msg = '';

        function _fn(list) {
            _.each(list, function (item) {
                var res = true;
                var rowId = item.rowId;
                if (item.isGroup && item.children) {
                    var isRequired = 0;
                    var isDefaultSelect = 0;
                    _.each(item.children, function (c) {
                        if (c.is_required) isRequired++;
                        if (c.selected_by_default) isDefaultSelect++;
                    });
                    var max = item.max_prod_count;
                    var min = item.min_prod_count;
                    let groupName = '【' + item.name + '】';
                    if (isRequired > max && max !== null) {
                        msg += groupName + '，' + $t('子产品为必选的个数超过该分组的最大子产品个数，请修改') + '<br/>';
                        res = false;
                    }

                    if (isDefaultSelect > max && max !== null) {
                        msg += groupName + '，' + $t('子产品默认选中的个数超过该分组的最大子产品个数，请修改') + '<br/>';
                        res = false;
                    }

                    if (min !== null && min > item.children.length) {
                        msg = groupName + '，' + $t('子产品个数应大于分组的最少子产品个数') + '<br/>';
                        res = false;
                    }
                }
                if (item.children && item.children.length) {
                    _fn(item.children)
                }
            })
        }

        _fn(data);

        if (msg) {
            CRM.util.alert(msg);
        }
        return !msg
    }

    beforeValidTable(record_type) {
        let data = this.getRootData(record_type);
        data = data.children;
        var res1 = this.validGroupData(data);
        var res2 = CRM.util.validAmountBeforeSubmit(null, data, null, null, true);
        var res3 = CRM.util.checkBomShareRate(null, data);
        if (!res3.status) CRM.util.alert(res3.msg);
        return res1 && res2 && res3.status;
    }

    // 选bomcore
    renderSelectCore({row, mdApiName, recordType, masterData} = {}) {
        let {
            node_bom_core_version,
            node_bom_core_type,
            related_core_id,
            product_id,
            form_category
        } = this.getAllFields();

        return new Promise(async resolve => {
            if (!row._isChildBom) return resolve({});
            const PickSelf = await this.pluginService.api.import('crm-modules/components/pickselfobject/pickselfobject');
            let status = false;
            let pickobject = new PickSelf();
            pickobject.on('select', (d) => {
                status = true;
                let coreId = d._id;
                if (coreId === row[related_core_id]) return resolve({});
                resolve({
                    [node_bom_core_version]: d.core_version,
                    [node_bom_core_type]: d.category,
                    [related_core_id]: d._id,
                })
            });
            pickobject.on('destroy', (obj) => {
                if (!status) resolve({})
            });
            pickobject.render({
                apiname: 'BomCoreObj',
                dataId: row[related_core_id] || '',
                // relatedname: me.fieldAttr.target_related_list_name,
                // wheres: me.fieldAttr.wheres,
                // filters: me.getFilters(),
                // object_data: me.getLookupData(),
                // zIndex: me.get('zIndex'),
                hideAdd: true,
                isMultiple: false,
                beforeRequest: function (rq) {
                    var info = JSON.parse(rq.search_query_info);
                    info.filters = info.filters || [];
                    info.filters.push({
                        field_name: 'product_id',
                        field_values: [row[product_id]],
                        operator: 'EQ'
                    });
                    // 标准件，子件bom也只能选标准件
                    if (masterData[form_category] === 'standard') {
                        info.filters.push({
                            field_name: form_category,
                            field_values: ["configure"],
                            operator: 'N'
                        });
                    }

                    // 子件定价，只能添加相同定价方式的复用 bom
                    if(masterData.sale_strategy){
                        info.filters.push({
                            field_name: 'sale_strategy',
                            field_values: [masterData.sale_strategy],
                            operator: 'EQ'
                        });
                    }
                    
                    rq.search_query_info = JSON.stringify(info);
                    return rq;
                }
            })
        })
    }

    // 初始化子bom数据。字段全部设为只读
    initChildBomData({data = [], recordType, mdApiName, row} = {}, param) {
        this._addLable(data);
        let {product_id, related_core_id} = this.getAllFields();
        PPM.forEachTreeData(data, item => {
            item.record_type = recordType;
            if (row._isChildBom || row._isChildBomChildren) {
                item._isChildBomChildren = true;
                item.__hideCheckBox = true;
                if (item[related_core_id]) {
                    item._isChildBom = true;
                }
                param.dataUpdater.setReadOnly({
                    whiteFieldName: [],
                    dataIndex: item.rowId,
                    objApiName: mdApiName,
                    recordType: recordType,
                    status: true
                });
            }
        });
    }

    // 标准bom，设置字段值和只读
    setBomValueForStandard({data = [], recordType, mdApiName} = {}, param) {
        let isStand = this.isStandard(param);
        if (!isStand) return;
        let {
            is_required,
            selected_by_default,
            price_editable,
            amount_editable,
        } = this.getAllFields(mdApiName);
        PPM.forEachTreeData(data, item => {
            if (!item.isGroup) {
                param.dataUpdater.updateDetail(mdApiName, item.rowId, {
                    [is_required]: true,
                    [selected_by_default]: true,
                    [price_editable]: false,
                    [amount_editable]: false,
                });
            }
        });
    }

    /**
     * @desc 添加子bom逻辑
     * @param row 子bom根节点行数据
     * @param mdApiName
     * @param recordType
     * @param coreId
     */
    async addChildBom({row, mdApiName, recordType, coreId} = {}) {
        let {
            product_id,
        } = this.getAllFields();
        let rd = this.getRootData(recordType);
        let rootPD = row._isChildBom ? row[product_id] : rd[product_id];
        let childData = await this.fetchBomCore({coreId, rootProductId: rootPD, fetchAll: true, row});
        childData = this.addChildBom_afterHook(childData, row);
        if (!childData) {
            this._setNeedReq(row, true);
            this._addKey(row);
            return
        }
        ;
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'bomcoremd_addchild'
        });
        this.initChildBomData({data: childData, recordType, mdApiName, row}, factory);
        let nc = this.addDataToLeft(childData, row.rowId, recordType, true);
        this.initAmount(nc, row);
        nc = PPM.parseTreeToNormal(nc);
        factory.dataUpdater.add(nc);
        factory.end();
        return childData;
    }

    addChildBom_afterHook(childData, row) {
        return childData
    }

    // 删除从对象数据
    _delMdData(mdApiName, param) {
        let mdData = param.dataGetter.getDetail(mdApiName);
        mdData.forEach(item => {
            param.dataUpdater.del(mdApiName, item.rowId);
        })
    }

    // 初始化编辑数据
    parseEditData(data, mdApiName) {
        let {related_core_id, is_package} = this.getAllFields();
        data.forEach(c => {
            if (c[related_core_id] || c[is_package]) {
                c._isChildBom = true;
                this._setNeedReq(c, true);
                this._addKey(c);
            }
        });
    }

    _setNeedReq(data, val) {
        data._needReq = val;
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'bomcoremd_self'
        });
        let mdApiName = this.mdApiName;
        factory.dataUpdater.updateDetail(mdApiName, data.rowId, {
            _needReq: val
        });
        factory.end();
    }

    _addKey(data) {
        data._key = PPM.uniqueCode();
    }

    // 二次编辑数据，需要删掉数据，重新查treev1。需要分组信息
    async fetchBomForEdit(param) {
        let {form_product_id, related_core_id} = this.getAllFields();
        this._formType = param.formType;
        let mdApiName = param.objApiName;
        let masterData = param.dataGetter.getMasterData();
        let coreId = this._masterCoreId = this._formType === 'edit' ? masterData._id : param.dataGetter.getOptions('dataId');
        let rootProductId = this._masterProductId = masterData[form_product_id];
        this._delMdData(mdApiName, param);
        let bomData = await this.fetchBomCore({coreId, rootProductId, fetchAll: false, from: 'edit',});
        bomData = PPM.parseTreeToNormal(bomData);
        this.parseEditData(bomData, mdApiName);
        param.dataUpdater.add(bomData);
    }

    async _fetchFn({coreId, rootProductId, fetchAll = false, row, from = ''} = {}) {
        return await CRM.util.fetchBomAndRelatedBomData([rootProductId], {
            child_search_query_info: '{"limit":2000,"offset":0,"filters":[]}',
            bom_core_id: coreId,
            include_all_sub_core_id: fetchAll,
        });
    }

    // 请求从对象数据
    async fetchBomCore({coreId, rootProductId, fetchAll = false, row, from = ''} = {}) {
        let res = await this._fetchFn(...arguments);
        let bomData = CRM.util.flatBomData(res.dataMapList);
        let rootData = res.dataMapList.find(item => item.describeApiName === 'ProductObj');
        if (!rootData) return;
        rootData = rootData.dataList[0];
        if (!rootData) {
            this.pluginService.api.alert(this.i18n('未查到产品包信息'));
            return
        }
        if (!this._rootBomId) this._rootBomId = rootData.bom_id;
        this.parseGroupData(bomData, this.recordType);
        this.addRealCoreId(bomData, row);
        bomData = this.fetchBomCore_afterFetch(bomData, rootData, from);
        bomData = PPM.sortTreeData(bomData, 'order_field');
        this.fetchBomCore_afterHook(bomData, from);
        this.parseDataForClone(bomData, from);
        return bomData;
    }

    // 给复用bom子件 添加复用bom真实的coreid
    addRealCoreId(bomData, row) {
        let {product_id, related_core_id} = this.getAllFields();
        let realRootCoreId, realRootProductId;
        if (row && row._isChildBom) {
            realRootCoreId = row[related_core_id];
            realRootProductId = row[product_id];
        } else if (row && !row._isChildBom && row._realRootCoreId) {
            realRootCoreId = row._realRootCoreId;
            realRootProductId = row._realRootProductId;
        }
        if (realRootCoreId) {
            bomData.forEach(item => {
                item._realRootCoreId = realRootCoreId;
                item._realRootProductId = realRootProductId;
            })
        }
    }

    // 处理复制数据
    parseDataForClone(bomData, from) {
        if (this._formType !== 'clone' || from !== 'edit') return;
        PPM.forEachTreeData(bomData, item => {
            delete item._id;
            delete item.core_id;
            delete item.extend_obj_data_id;
            delete item.parent_bom_id;
            delete item.new_bom_path;
            if (!item.isGroup) item.name = null;
            this.addActionType(item, 'create');
        });
    }

    fetchBomCore_afterFetch(bomData, rootData) {
        return PPM.parseDataToBOM(bomData, rootData.bom_id);
    }

    fetchBomCore_afterHook(data) {
    }

    getCheckData(data) {

    }

    // 初始化复用 bom 的数量
    initAmount(bomData, rootData){
        PPM.forEachTreeData(bomData, c => {
            if(c.isGroup) return;
            c.__amount = c.amount;
        });
        let allData = [].concat(bomData).concat([rootData]);
        let cloneData = CRM.util.cloneBomData(allData, ['rowId', 'parent_rowId', '__amount', 'amount', 'related_core_id', 'amount_any', 'product_id__r', 'is_package']);

        CRM.util.setChildrenAmount(cloneData, {
            baseAmount: '__amount',
            amount: 'amount',
            amount_any: 'amount_any',
            related_core_id: 'related_core_id',
        },  (val, row, parentBom) => {
            let f = PPM.getDataByKey(row.rowId, allData);
            f.amount = val;
        });
    }





    // 保存前处理参数
    _submitBefore(plugin, param) {
        let r = this.beforeValidTable(this.recordType);
        if (!r) return plugin.skipPlugin();
        return {
            parseParam: this.parseSaveData.bind(this)
        }
    }

    // 将 md 数据更新到左侧树；
    mergeDataToLeft(curMdData = []){
        if(!curMdData?.length) return;
        let newData = {};
        curMdData.forEach(item => {
            if(newData[item.record_type]){
                newData[item.record_type].push(item)
            }else{
                newData[item.record_type] = [item];
            }
        });
        PPM.each(newData, (data, rd) => {
            this.updateDataToLeft(data, rd)
        });
    }

    // 处理保存参数
    parseSaveData(res) {
        let allData = this.getMdData();
        let mdApiName = this.mdApiName;
        let curMdData = res.details[mdApiName];
        this.mergeDataToLeft(curMdData);
        let cloneData = PPM.deepClone(allData);
        let addList = [];
        let updateList = [];
        // 找出更新的数据
        PPM.forEachTreeData(cloneData, (item, pd) => {
            if (item._isChildBom) delete item.children;
            if (item.isGroup) item.object_describe_api_name = 'ProductGroupObj';
            if (item.action_type === 'update') {
                let cd = $.extend({}, item);
                delete cd.children;
                delete cd.product_id__ro;
                updateList.push(cd);
            }
        });
        // 找出新增的数据，保留层级结构；
        // 先取出来所有有新增子级的数据，然后去重，过滤出新增子级；
        PPM.forEachTreeData(cloneData, (item, pd) => {
            if (item.action_type === 'create') {
                if (!pd) {
                    addList.push(item);
                } else if (pd && pd.action_type !== 'create') {
                    addList.push(pd)
                }
            }
        });
        addList = _.uniq(addList, c => c.rowId);
        addList.forEach(item => {
            if(item.action_type !== 'create') delete item.action_type;
            if (item.children) item.children = item.children.filter(c => c.action_type === 'create');
        });
        console.log(addList);
        // 改分组的来源
        this._cacheDel.forEach(c => {
            if (c.isGroup) c.object_describe_api_name = 'ProductGroupObj';
        });
        // 合并增删改数据
        res.details[mdApiName] = [...addList, ...updateList, ...this._cacheDel];
        return res
    }

    apply() {
        return [{
            event: 'md.render.before',
            functional: this._mdRenderBefore.bind(this)
        },{
            event: 'md.render.after',
            functional: this._mdRenderAfter.bind(this)
        }, {
            event: 'md.batchAdd.end',
            functional: this._mdAddEnd.bind(this)
        }, {
            event: 'md.add.before',
            functional: this._mdAddBefore.bind(this)
        }, {
            event: 'md.edit.before',
            functional: this._editBefore.bind(this)
        }, {
            event: 'md.edit.after',
            functional: this._editAfter.bind(this)
        }, {
            event: 'md.del.end',
            functional: this._delEnd.bind(this)
        }, {
            event: 'form.render.before',
            functional: this._formRenderBefore.bind(this)
        },{
            event: 'form.render.after',
            functional: this._formRenderAfter.bind(this)
        },{
            event: 'form.dataChange.after',
            functional: this._dataChange_after.bind(this)
        }, {
            event: 'form.submit.before',
            functional: this._submitBefore.bind(this)
        }, {
            event: 'bomAttributeConstraint.getMdData',
            functional: this.getMdData.bind(this)
        }];
    }

    destroy() {
        PPM.each(this._leftTreeComs, com => {
            com && com.destroy && com.destroy();
            com = null;
        });
        this._leftTreeComs = null;
        this._allTableData = null;
        this.search = null;
    }


}
