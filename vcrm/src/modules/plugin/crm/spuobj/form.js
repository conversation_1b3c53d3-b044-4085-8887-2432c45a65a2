import Base from 'plugin_base';
import MallCategoryTree from '../productobj/components/mall-category/mall-category-tree.vue';

/**
 * 产品对象，新建
 */
export default class SpuForm extends Base{
  constructor(...rest) {
    super(...rest);
  }

  async _formRenderBefore(plugin, context) {

    const { BaseComponents, dataGetter} = context;
    const masterData = dataGetter.getMasterData();

    return {
      // 替换组件
      components: {
        'mall_category_id': BaseComponents.Base.extend({
          render() {
            const mallCategoryTree = Vue.extend(MallCategoryTree);
            this.widget = new mallCategoryTree({
              el: this.$el[0],
              propsData: {
                value: masterData.mall_category_id || [],
              }
            });

            this.widget.$on('change', value => {
              this.setData(value, null, null, true);
            });
          },
          //点击提交时会调用此方法
          getValue() {
            //通常直接返回值即可
            const vv = this.getData();
            return vv;
          },
          destroy() {
            //销毁内部组件
            if (this.widget) {
              this.widget.$destroy();
              this.widget = null;
            }
            this.super.destroy.call(this);
          }
        }),
      },
    };
  }

  getHook() {
    return [
      {
        event: 'form.render.before',
        functional: this._formRenderBefore.bind(this),
      },
    ];
  }
}
