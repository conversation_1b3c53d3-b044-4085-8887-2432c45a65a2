<template>
  <fx-popover
    ref="popover"
    v-model="popoverVisible"
    placement="bottom-start"
    :arrowOffset="1"
    width="400"
    :disabled="disabled"
    trigger="click">
    <div>
      <div class="tree-wrapper">
        <div v-if="!isLoading && !categoryData.length">
          {{$t('dht.no.mallcategory.tip')}}
        </div>
        <fx-tree
          v-if="!isLoading"
          ref="fxTree"
          :data="treeData"
          show-checkbox
          node-key="id"
          :default-checked-keys="value"
          :check-strictly="true"
          :props="defaultProps">
        </fx-tree>
      </div>
      <div class="button-wrapper">
        <fx-button size="mini" type="primary" @click="onConfirm">{{$t('el.messagebox.confirm')}}</fx-button>
      </div>
    </div>
    <div class="tab-wrapper" :class="{ disabled }" slot="reference" style="">
      <fx-tag
        class="category-tag"
        v-for="(tag, index) in selectedNodes"
        :key="tag.id"
        :closable="!disabled"
        size="small"
        disableTransitions
        @close="onTagClose(tag, index)"
        type="info">
        {{tag.name}}
      </fx-tag>
    </div>
  </fx-popover>
</template>

<script>
export default {
  name: 'mall-category-tree',

  props: {
    value: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  data: function () {
    return {
      popoverVisible: false, // 控制popover显示
      appList: [], // 商店列表
      categoryData: [], // 所以商店的商城类目数据
      selectedNodes: [], // 当前所选择的类目节点
      treeData: [], // 分类树
      defaultProps: { // 分类树属性设置
        children: 'children',
        label: 'name'
      },
      isLoading: true,
    }
  },

  created () {
    this.init();
  },

  methods: {
    /**
     * 选择分类确认
     */
    onConfirm() {
      this.selectedNodes = this.$refs.fxTree.getCheckedNodes();
      this.emitValue();
      this.popoverVisible = false;
    },
    /**
     * 点击分类tab关闭
     * @param tag
     * @param index
     */
    onTagClose(tag, index) {
      this.selectedNodes.splice(index, 1);
      this.$refs.fxTree.setChecked(tag.id, false);
      this.emitValue();
    },
    /**
     * 获取当前选择的类目的id数组
     * @returns {any[]}
     */
    getValue() {
      return this.selectedNodes.map(item => item.id);
    },
    /**
     * 抛出选择的选择的类目的id数组
     */
    emitValue() {
      this.$emit('change', this.getValue());
    },
    /**
     * 初始化获取数据
     */
    init() {
      const p1 = this._getAppList();
      const p2 = this._getCategoryList();
      Promise.all([p1, p2]).then((result) => {
        this.isLoading = false;
        this.appList = result[0];
        this.categoryData = result[1];
        this.treeData = this.createTreeForStore(this.appList, this.categoryData);
        this.initSelectedNodes(this.categoryData);
      }).catch((err) => {
        console.error(err);
        this.isLoading = false;
      });
    },
    /**
     * 初始化选择的节点
     */
    initSelectedNodes (categoryData) {
      const nodes = [];
      const value = this.value || [];
      const nodeMap = {};

      categoryData.forEach(node => {
        // 复制节点数据，避免修改原始数据
        nodeMap[node.id] = node;
      });

      value.forEach((id) => {
        if (nodeMap[id]) {
          nodes.push(nodeMap[id]);
        }
      });
      this.selectedNodes = nodes;
    },
    /**
     * 创建分类树，单个商城直接显示分类，多个商城需要按商城分类显示
     * @param appList
     * @param categoryData
     * @returns {*[]}
     */
    createTreeForStore(appList, categoryData) {
      if (appList.length === 1) {
        // 只有一个商城
        return this._buildTree(categoryData);
      } else if(appList.length > 1) {
        // 多个商城
        return this.createTreeByStore(appList, categoryData);
      }
    },
    /**
     * 创建多个商城的分类树
     * @param appList
     * @param categoryData
     * @returns {*[]}
     */
    createTreeByStore(appList, categoryData) {
      const treeMap = {};
      const categoryMap = this.splitCategoryByStoreId(categoryData);

      Object.keys(categoryMap).forEach(key => {
        treeMap[key] = this._buildTree(categoryMap[key]);
      });

      const treeData= [];
      appList.forEach(app => {
        const tree = treeMap[app._id]
        if (tree && tree.length) {
          const node = {
            id: app._id,
            name: app.name,
            showCheckbox: false,
            children: treeMap[app._id] || []
          }
          treeData.push(node);
        }
      });

      return treeData;
    },
    /**
     * 根据商城Id分离分类数据
     * @param categoryData
     * @returns {{}}
     */
    splitCategoryByStoreId(categoryData) {
      const categoryMap = {};

      categoryData.forEach(category => {
        const storeId = category.storeId;
        if (!categoryMap[storeId]) {
          categoryMap[storeId] = [];
        }
        categoryMap[storeId].push(category);
      });

      return categoryMap;
    },
    /**
     * 构建分类树的方法
     * @param nodeList
     * @returns {*[]}
     * @private
     */
    _buildTree(nodeList) {
      // 创建 ID 到节点的映射表
      const nodeMap = {};
      nodeList.forEach(node => {
        // 复制节点数据，避免修改原始数据
        nodeMap[node.id] = { ...node, children: [] };
      });

      // 存储根节点
      const rootNodes = [];

      // 构建树结构
      nodeList.forEach(node => {
        const mappedNode = nodeMap[node.id];
        const parentId = node.pid;

        // 如果父节点不存在或 pid 为空，则视为根节点
        if (!parentId || !nodeMap[parentId]) {
          rootNodes.push(mappedNode);
        } else {
          // 将当前节点添加到父节点的 children 中
          nodeMap[parentId].children.push(mappedNode);
        }
      });

      return rootNodes;
    },

    /**
     * 获取所有分类的方法
     * @returns {Promise<unknown>}
     * @private
     */
    _getCategoryList() {
      return new Promise((resolve, reject) => {
        CRM.util.FHHApi({
          url: '/EM1HNCRM/API/v1/object/shop_category/service/query_sub_shop_category_list',
          data: {
            storeId: '',
            pid: '',
          },
          success: res => {
            if (res.Result.StatusCode === 0) {
              const shopCategoryList = res.Value && res.Value.shopCategoryList || [];
              resolve(shopCategoryList);
            } else {
              CRM.util.error(res.Result.FailureMessage);
              reject(res);
            }
          },
          error: (err) => {
            reject(err);
          },
        }, {
          errorAlertModel: 1
        });
      });
    },
    /**
     * 获取所有商城的方法
     * @returns {Promise<unknown>}
     * @private
     */
    _getAppList: function() {
      return new Promise(function(resolve, reject) {
        CRM.util.FHHApi({
          url: '/EM1HNCRM/API/v1/object/OnlineStoreObj/controller/List',
          data: {
            extractExtendInfo: true,
            ignore_scene_record_type: false,
            include_describe: false,
            include_layout: false,
            object_describe_api_name: "OnlineStoreObj",
            search_query_info: "{\"limit\":100,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}",
            search_template_id: '',
            search_template_type: "default",
            serializeEmpty: false,
          },
          success: function (res) {
            if (res.Result.StatusCode === 0) {
              const appList = res.Value && res.Value.dataList || [];
              resolve(appList);
            } else {
              CRM.util.alert(res.Result.FailureMessage);
              reject(res);
            }
          }
        }, {
          errorAlertModel: 1
        });
      })
    },
  }
}
</script>

<style scoped lang="less">
  .tree-wrapper {
    height: 300px; overflow-y: auto
  }

  .button-wrapper {
    display: flex;
    flex-direction: row-reverse;
    padding-top: 8px;
    border-top: 1px solid #eee;
  }

  .tab-wrapper {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 2px 2px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-height: 32px;
    box-sizing: border-box;

    &.disabled {
      background-color: #f2f3f5;
      cursor: not-allowed;
    }
  }

  .category-tag {
    margin-right: 5px;
    margin-bottom: 5px;
  }
</style>
