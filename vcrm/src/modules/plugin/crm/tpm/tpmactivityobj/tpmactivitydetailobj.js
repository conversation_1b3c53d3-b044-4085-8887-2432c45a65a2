/*
 * @Author: <PERSON>
 * @Date: 2025-03-17 11:15:34
 * @LastEditTime: 2025-03-17 14:38:18
 * @LastEditors: <PERSON> Jun
 * @Description: 参与活动项目
 */

import Base from "plugin_base";

const RIO_LOOKUP_FIELD_API_NAME = 'display_project_judgment_standard__c';  // RIO的特殊字段

export default class Plugin extends Base {
    constructor() {
        super(...arguments);
    }

    _mdEditAfter(plugin, param) {
        let { fieldName, dataIndex, changeData, lookupData } = param;
        if (fieldName === RIO_LOOKUP_FIELD_API_NAME && lookupData) {
            changeData[dataIndex[0]].activity_amount_standard = lookupData.floor_number__c || 0;   // 将陈列标准中的层数赋值给数量标准
        }
        return { changeData };
    }

    _batchAddEnd(plugin, param) {
        const { addDatas, lookupDatas, lookupField } = param;
        if(lookupField.api_name === RIO_LOOKUP_FIELD_API_NAME) {
            addDatas.forEach((data, index) => {
                let lookup_data = lookupDatas[index];
                data.activity_amount_standard = lookup_data.floor_number__c || 0;   // 将陈列标准中的层数赋值给数量标准
            })
        }
        return { newDatas: addDatas }
    }

    getHook() {
        return [
            {
                event: "md.edit.after",
                functional: this._mdEditAfter.bind(this),
            },
            {
                event: "md.batchAdd.end",
                functional: this._batchAddEnd.bind(this),
            }
        ];
    }
}
