/*
 * @Author: <PERSON>
 * @Date: 2023-06-27 19:12:17
 * @LastEditTime: 2025-07-31 15:44:29
 * @LastEditors: <PERSON> Jun
 * @Description: 参与活动客户
 */
import Base from "plugin_base";
import FilterTabs from "@plugin/common/filtertabs";

export default class Plugin extends Base {
  constructor(pluginService, pluginParam) {
    super(...arguments);
    const filterTabsConfig = {
      field: 'store_range',
      tabs: {
        filter: {
          options: {
            apiname: 'AccountObj',
            needLookUp: true,
            filterType: [
              // 'object_reference',
              // 'formula',
              'group',
              'image',
              'file_attachment',
              'master_detail',
              'auto_number',
              'signature',
              'quote',
              'embedded_object_list',
              'multi_level_select_one',
              'tree_path',
              'employee_many',
              'department_many',
              'html_rich_text',
            //   'object_reference_many',
              'big_file_attachment',
            ],
            parseCompare(compares, field) {  //排除指定操作符
              if(field && field.type === 'object_reference'){
                return compares.filter(item => !['LIKE', 'NLIKE'].includes(item.value1))
              }
              return compares;
            }
          }
        },
        md: {
          label: this.i18n('指定客户')
        }
      }
    };
    if(FS.util.getUserAttribute('isSmoorecig')){  //麦克韦尔特殊处理名称
      filterTabsConfig.title = this.i18n('客户范围1');
    }
    this.filterTabs = new FilterTabs(filterTabsConfig, this);
    this.cacheChildren([this.filterTabs]);
  }

  _mdRenderBefore() {
    return {
      __execResult: {
        filterBatchEditFields: ["store_id"],
      },
      __mergeDataType: {
        array: "concat",
      },
    };
  }

  async _batchAddBefore(plugin, param) {
    let { lookupField} = param;
    let result = plugin.preData || {};
    if(lookupField.api_name === "store_id"){   //只处理门店（store_id）的查找关联字段
      return Object.assign(result, {
        skipSearch: true,
        url: `/EM1HNCRM/API/v1/object/TPMActivityObj/controller/RelatedActivityStoreList`
      })
    }
    return result;
  }

  _mdEditBefore(plugin, param) {
    let { objApiName, fieldName, dataGetter } = param;
    if(objApiName === "TPMActivityStoreObj" && fieldName === "store_id"){    //改变门店
      return {
        skipSearch:true,
        url: `/EM1HNCRM/API/v1/object/TPMActivityObj/controller/RelatedActivityStoreList`
      }
    }
  }
  _formRenderBefore(plugin, param) {
    console.log('渲染前');
    const { dataGetter } = param, data = dataGetter.getMasterData();
    this.store_range = data.store_range;
  }

  _formRenderAfter(plugin, param) {
    console.log('渲染后');
      if(param.hasChange()){
        const { dataGetter } = param, data = dataGetter.getMasterData();
        if(this.filterTabs && this.store_range != data.store_range){
            this.filterTabs.resetOptions({
                defaultValue: data.store_range,   //大日期活动只能是指定产品
            });
        }
    }
  }

  _mdExcelImportBefore(plugin, param) {
    console.log('[ 导入前 ] >', param);
    return {
        importConfig: {
            parseRelatedListUrl(options) {
                console.log('[ 解析URL ] >', options);
                if(options.fieldName === 'store_id'){
                    return `/EM1HNCRM/API/v1/object/TPMActivityObj/controller/RelatedActivityStoreList`
                }
            }
        }
    }
  }

  _formSubmitBefore(plugin, param) {
    try {
        const store_range = this.store_range ? JSON.parse(this.store_range) : {};
        if(store_range.code){
            return Promise.resolve({
                parseParam(postData) {
                    if(postData.object_data.store_range){
                        const current = JSON.parse(postData.object_data.store_range);
                        postData.object_data.store_range = JSON.stringify(Object.assign({ code: store_range.code }, current));
                    }
                    return postData;
                }
            });
        }
        return Promise.resolve({});
    } catch (error) {
        return Promise.resolve({});
    }
  }

  getHook() {
    return [
      {
        event: "md.edit.before",
        functional: this._mdEditBefore.bind(this),
      },
      {
        event: 'md.batchAdd.before',
        functional: this._batchAddBefore.bind(this)
      },
      {
        event: "md.render.before",
        functional: this._mdRenderBefore.bind(this),
      },
      {
        event: "form.render.before",
        functional: this._formRenderBefore.bind(this)
      },
      {
        event: "form.render.after",
        functional: this._formRenderAfter.bind(this)
      },
      {
        event: "md.excelimport.before",
        functional: this._mdExcelImportBefore.bind(this)
      },
      {
        event: "form.submit.before",
        functional: this._formSubmitBefore.bind(this)
      }
    ];
  }
}
