/*
 * @Author: <PERSON>
 * @Date: 2024-11-21 10:57:38
 * @LastEditTime: 2025-03-18 14:15:09
 * @LastEditors: <PERSON> Jun
 * @Description:  任务管理对象
 */

import Base from "plugin_base";

const RewardTaskRuleField = "__reward_task_config"; //表示任务规则字段api_name
const RewardTaskDesignerField = "__reward_task_designer"; //表示任务设计字段api_name

export default class RewardTaskObj extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    _fetchDescribeAfter(plugin, param) {
        console.log("获取描述后：", arguments);
        const { layout, objectDescribe } = param.describe;
        let form_component = layout.components?.find(
            (item) => item.api_name === "form_component"
        );
        if (form_component) {
            const index = form_component.field_section.findIndex(
                (item) => item.api_name === "base_field_section__c"
            );
            form_component.field_section.splice(index + 1, 0, {
                api_name: "group_task_system_rule__c",
                header: $t('tpm.task.poster.group.title.1'),
                type: "group",
                form_fields: [
                    {
                        field_name: RewardTaskRuleField,
                        is_readonly: false,
                        is_required: false,
                        render_type: RewardTaskRuleField,
                        full_line: true
                    },
                ],
            }, {
                api_name: "group_task_system_designer__c",
                header: '',
                type: "group",
                form_fields: [
                    {
                        field_name: RewardTaskDesignerField,
                        is_readonly: false,
                        is_required: false,
                        render_type: RewardTaskDesignerField,
                        full_line: true
                    },
                ]
            });
        }
        objectDescribe.fields[RewardTaskRuleField] = {
            api_name: RewardTaskRuleField,
        };
        objectDescribe.fields[RewardTaskDesignerField] = {
            api_name: RewardTaskDesignerField,
        };
        if(param.formType == 'edit') {   // 编辑时，修改任务周期字段可选项
            const data = param.dataGetter.getData(),
                isActionMetric = data.task_type == 'action',
                cycle_type_describe = objectDescribe.fields.cycle_type;
            cycle_type_describe.options = cycle_type_describe.options.filter(item => {
                return item.value !== 'single' ^ isActionMetric;    // 事件类任务只能选单次，周期性任务不能选单次
            });
        }
        return param;
    }

    _formRenderBefore(plugin, param) {
        let { BaseComponents, dataGetter, formType } = param;
        const describe = dataGetter.getDescribe(), plugin_this = this, components = {};
        const data = dataGetter.getData();
        // 人员范围
        components.employee_range = BaseComponents.Base.extend({
            render() {
                const { employee_range = [], department_range = [] } = data, that = this;
                plugin_this.$range_selector = FxUI.create({
                    wrapper: this.$el[0],
                    template: `<fx-selector-input-v2 ref='selector' v-bind='options' @change='onChange'></fx-selector-input-v2>`,
                    data() {
                        return {
                            options: FS.selectorParseContactV2.parseContacts({
                                addBtnLabel: $t("选择人员和部门"),
                                member: true,
                                group: { company: true, selectAll: true },
                                defaultSelectedItems: {
                                    member: employee_range,
                                    group: department_range,
                                }
                            })
                        }
                    },
                    methods: {
                        onChange(value) {
                            const { member, group } = value;
                            that.setData(member.map((id) => `${id}`), 'employee_range', false, true)
                            that.setData(group.map((id) => `${id}`), 'department_range', false, true)
                        },
                        getValue() {
                            const result = this.$refs.selector.getValue();
                            return result.member.map((id) => `${id}`);
                        },
                    }
                })
            },
            destroy() {
                BaseComponents.Base.prototype.destroy.apply(this, arguments);
                this.$range_selector?.$destroy();
            },
        })
        // 角色范围
        components.role_range = BaseComponents.Base.extend({
            render() {
                const { role_range } = data, that = this;
                plugin_this.$role_range_selector = FxUI.create({
                    wrapper: this.$el[0],
                    template: `<fx-selector-input-v2 ref='selector' v-bind='options' @change='onChange'></fx-selector-input-v2>`,
                    data() {
                        return {
                            options: FS.selectorParseContactV2.parseContacts({
                                addBtnLabel: $t("选择角色"),
                                member: false,
                                group: false,
                                role: true,
                                defaultSelectedItems: {
                                    role: role_range ? JSON.parse(role_range) : [],
                                }
                            })
                        }
                    },
                    methods: {
                        onChange(value) {
                            const { role } = value;
                            that.setData(JSON.stringify(role.map((id) => `${id}`)), 'role_range', false, true)
                        },
                        getValue() {
                            const result = this.$refs.selector.getValue();
                            return JSON.stringify(result.role.map((id) => `${id}`));
                        },
                    }
                })
            },
            destroy() {
                BaseComponents.Base.prototype.destroy.apply(this, arguments);
                this.$role_range_selector?.$destroy();
            },
        });
        // 任务要求
        components[RewardTaskRuleField] = BaseComponents.Base.extend({
            render(h) {
                const default_size = this.getSize(), that = this;
                plugin_this.$tpm_task_rule = this.$tpm_task_rule = FxUI.create({
                    wrapper: this.$el.parents(`.f-g-item__${RewardTaskRuleField}`)[0],
                    replaceWrapper: true,
                    components: {
                        TaskRule: () => import('paas-tpm/sdk').then(SDK => {
                            return SDK.getRewardTaskRule();
                        })
                    },
                    data() {
                        return { size: default_size, data: Fx.util.deepClone(data) }
                    },
                    template: `<TaskRule
                        ref="taskRule"
                        describe='${JSON.stringify(describe)}'
                        :size='size'
                        :data='data'
                        form-type='${formType}'
                        @change='onChange'
                        @close='onClose'
                    />`,
                    methods: {
                        onChange(changed) {
                            Object.keys(changed).forEach(api_name => {
                                if(api_name == 'cycle_type') {  // 需要重新渲染
                                    that.model.newBatchUpdate({cycle_type: changed[api_name]})
                                    const isActionMetric = (changed.task_type || this.data.task_type) == 'action';   // 是否是事件类任务
                                    const attrs = that.getAttr('cycle_type');
                                     // 周期性任务不能选单次，事件类任务只能选单次
                                    attrs.filterOptions = !isActionMetric ? ['single'] :attrs.options.filter(item => {
                                        return item.value !== 'single';
                                    }).map(item => item.value);
                                    that.model.trigger('resetOptionsByFilterOptions.cycle_type');   // 触发重新设置options

                                }else{
                                    that.setData({value: changed[api_name], apiname: api_name }, api_name, false, true)
                                    if(api_name != '__reward_task_config')
                                        this.$set(this.data, api_name, changed[api_name]);  // 更新数据
                                }
                            })
                        },
                        onClose() {
                            that.model.trigger('destroy');
                        },
                        getValue() {
                            return this.$refs.taskRule?.getValue();
                        },
                        validate() {
                            return this.$refs.taskRule?.validate();
                        },
                        resize(size) {
                            this.size = size;
                        },
                    }
                });
            },
            resize() {
                BaseComponents.Base.prototype.resize.apply(this, arguments);
                this.$tpm_task_rule?.resize(this.getSize());
            },
            getValue() {
                return this.$tpm_task_rule?.getValue();
            },
            asyncValidateData() {
                return this.$tpm_task_rule?.validate().then(() => {
                    return false;
                }, () => {
                    return true;
                });
            },
            destroy() {
                BaseComponents.Base.prototype.destroy.apply(this, arguments);
                this.$tpm_task_rule?.$destroy();
            },
        });
        // 规则设置和海报
        components[RewardTaskDesignerField] = BaseComponents.Base.extend({
            render() {
                const default_size = this.getSize(), that = this;
                const container = $('.action-field-wrap.tpm-task-system-designer')
                plugin_this.$tpm_task_designer = this.$tpm_task_designer = FxUI.create({
                    wrapper: container[0],
                    components: {
                        TaskDesigner: () => import('paas-tpm/sdk').then(SDK => {
                            return SDK.getTaskDesigner();
                        })
                    },
                    template: `<TaskDesigner ref="taskDesigner" :size='size' :data='data' describe='${JSON.stringify(describe)}' @change='onChange'/>`,
                    data() {
                        return {
                            size: default_size,
                            data: Fx.util.deepClone(data)
                        }
                    },
                    methods: {
                        changeData(data) {
                            this.data = Fx.util.deepClone(data);
                        },
                        onChange(changed) {
                            if(Object.keys(changed).length){
                                const plugin = plugin_this.pluginService.api.pluginServiceFactory({ pluginApiName: "Plugin_Task_System_Designer" });
                                plugin.dataUpdater.updateMaster(changed)    // 不会触发 dataChange  hook
                                const data = plugin.dataGetter.getMasterData();
                                plugin.end();
                                this.changeData(data);
                            }
                        },
                        validate() {
                            return this.$refs.taskDesigner?.validate();
                        }
                    }
                })
            },
            getValue() { return; },
            asyncValidateData() {
                return this.$tpm_task_designer?.validate().then(() => {
                    return false;
                }, () => {
                    return true;
                });
            },
            destroy() {
                BaseComponents.Base.prototype.destroy.apply(this, arguments);
                this.$tpm_task_designer?.$destroy();
            },
        })
        return { components };
    }

    __initDesigner(param) {
        let { dataGetter, objApiName } = param;
        const describe = dataGetter.getDescribe(), plugin_this = this, components = {};
        const data = dataGetter.getData();
        const container = $('.action-field-wrap.tpm-task-system-designer')
        const that = this;
        const $tpm_task_designer = FxUI.create({
            wrapper: container[0],
            components: {
                TaskDesigner: () => import('paas-tpm/sdk').then(SDK => {
                    return SDK.getTaskDesigner();
                })
            },
            template: `<TaskDesigner :data='data' describe='${JSON.stringify(describe)}' @change='onChange'/>`,
            data() {
                return {
                    data
                }
            },
            methods: {
                changeData(data) {
                    this.data = data;
                },
                onChange(changed) {
                    if(Object.keys(changed).length){
                        const plugin = that.pluginService.api.pluginServiceFactory({ pluginApiName: "Plugin_Task_System_Designer" });
                        plugin.dataUpdater.updateMaster(changed)
                        plugin.end();
                    }
                },
            }
        })
        this.$tpm_task_designer = $tpm_task_designer;
    }

    _formDataChange(plugin, param) {
        const { changeData, dataUpdater } = param;
        // 判断changeData是否存在task_type
        if(changeData.task_type) {
            dataUpdater.setReadOnly({ fieldName: 'cycle_type', status: changeData.task_type == 'action' ? true : false })   // 事件类任务只读考核周期
        }
        if(this.$tpm_task_designer) {
            const data = param.dataGetter.getMasterData();
            this.$tpm_task_designer.changeData(data);
        }
    }

    _formRenderAfter(plugin, param) {
        const { dataGetter, dataUpdater } = param;
        const data = dataGetter.getMasterData();
        if(data.task_type) {
            dataUpdater.setReadOnly({ fieldName: 'cycle_type', status: data.task_type == 'action' ? true : false })   // 事件类任务只读考核周期
        }
        dataUpdater.setRequired({ fieldName: ['cycle_type'], status: true })   // 考核周期必填
    }

    __onValidateEmployeePermission(data = {}) {
        return new Promise((resolve, reject) => {
            // 调用接口
            FS.util.FHHApi({
                url: "/EM1HNCRM/FMCGReward/API/RewardTask/ValidateEmployeePermission",
                data: {
                    department_range: data.department_range || [],
                    role_range: data.role_range || '[]',
                    employee_range: data.employee_range || []
                }
            }).then(({ Result, Value }) => {
                console.log(Value);
                if(Result.StatusCode === 0) {
                    if(Value.notHaveAccessEmployeeSize > 0) {   // 有未授权人员
                        FxUI.create({
                            data() {
                                return {
                                    visible: true,
                                    innerVisible: false,
                                    tip: $t('tpm.task.designer.license.tip', {
                                        total_count: Value.totalEmployeeSize,
                                        str: `<span style="color: var(--color-danger06); text-decoration: underline;">${$t('num人', { num: Value.notHaveAccessEmployeeSize })}</span>`
                                    })
                                }
                            },
                            template: `
                                <fx-dialog title='${$t('提示')}' size='small2'
                                    :visible.sync='visible' appendToBody
                                    :close-on-click-modal="false" :close-on-click-outside="false"
                                    @closed='onClosed'
                                >
                                    <div class="el-message-box__status fx-icon-f-jingshi"></div>
                                    <div v-html='tip'></div>
                                    <div slot="footer" class="dialog-footer">
                                        <fx-button type="primary" size="small">${$t('tpm.task.designer.license.btn1')}</fx-button>
                                        <fx-button @click="innerVisible = true" size="small">${$t('tpm.task.designer.license.btn2')}</fx-button>
                                        <fx-button @click="visible = false" size="small">${$t('知道了')}</fx-button>
                                    </div>
                                </fx-dialog>
                            `,
                            methods: {
                                onClosed() {
                                    this.$destroy()
                                    resolve();
                                }
                            }
                        })
                        reject();
                    }
                }else {
                    reject();
                }
            })
        })
    }

    _formSubmitBefore(plugin, param) {
        const { dataGetter } = param;
        const data = dataGetter.getMasterData();
        let { role_range, employee_range,  department_range } = data;
        if(typeof role_range === 'string'){
            role_range = JSON.parse(role_range);
        }
        if(!(role_range?.length || employee_range?.length || department_range?.length)){
            FxUI.MessageBox.alert($t('task.system.range.warning'), '', { type: 'warning', confirmButtonText: $t('知道了'), })
            plugin.skipPlugin();
            return Promise.reject();
        }else {   // 校验许可配置
            return import('paas-tpm/sdk').then(SDK => {
                return SDK.validateTaskEmployeePermission(data).catch(() => {
                    plugin.skipPlugin();
                    return Promise.reject();
                });
            })
        }

    }

    getHook() {
        return [
            {
                event: "fetchdescribe.after",
                functional: this._fetchDescribeAfter.bind(this),
            },
            {
                event: "form.render.before",
                functional: this._formRenderBefore.bind(this),
            },
            {
                event: 'form.render.after',
                functional: this._formRenderAfter.bind(this)
            },
            {
                event: 'form.dataChange.after',
                functional: this._formDataChange.bind(this)
            },
            {
                event: 'form.submit.before',
                functional: this._formSubmitBefore.bind(this)
            }
        ];
    }
}
