/*
 * @Descripttion: 高级公式
 * @Author: lingjing
 * @Date: 2022-07-07 16:58:48
 * @LastEditors: lingjing
 * @LastEditTime: 2025-06-09 16:44:53
 */
import Base from 'plugin_base';


export default class Plugin extends Base {
    constructor() {
        super(...arguments);     
    }

    /**
     * 批量添加数据，选数据前
     */
    _batchAddBefore(plugin, param) {
        const data = param.dataGetter.getDetail();
        const lookupFieldApiName = param.lookupField?.api_name || "product_id";
        const filterIds = (data||[]).map(d=>d[lookupFieldApiName]).filter(d=>d);
        
        return {
            beforeRequest(rq) {
                let sq = JSON.parse(rq.search_query_info)||{};
                sq.filters=sq.filters||[];
                if(filterIds.length){
                    sq.filters.push({
                        field_name: "_id",
                        field_values: filterIds,
                        operator: "NIN"
                    });
                }
                rq.search_query_info = JSON.stringify(sq);
                return rq;
            }
        };
    }

    _batchAddAfter(plugin, param) {
        const lookupFieldApiName = param.lookupField?.api_name || "product_id";
        const lookupDataMap = new Map();
        if(lookupFieldApiName === "bom_id"){
            (param.lookupDatas||[]).forEach(item=>{
                lookupDataMap.set(item._id, item);
            });
            (param.addDatas||[]).forEach(item=>{
                const source = lookupDataMap.get(item.bom_id);
                if(source){
                    item.product_id = source.product_id;
                    item.product_id__r = source.product_id__r;
                }
            });
        }
        
    }

    getHook() {
        return [
            {
                event: "md.batchAdd.before",
                functional: this._batchAddBefore.bind(this),
            },
            {
                event: "md.batchAdd.after",
                functional: this._batchAddAfter.bind(this),
            }
        ]
    }
}
