/*
 * @Descripttion: interaction_records 字段钩子
 * @Author: 
 * @Date: 2025-05-06
 * @LastEditors: 
 * @LastEditTime: 2025-05-06
 */
import Base from "plugin_base";
import { requireField } from '@common/require';

export default class InteractionRecordsPlugin extends Base {
    constructor(pluginService, pluginParam) {
        super(...arguments);
    }

    /**
     * interaction_records 字段 render.before 钩子
     * @param {*} plugin 插件上下文
     * @param {*} param 参数
     * @returns 
     */
    async _renderBefore(plugin, context) {
        const { BaseComponents } = context;

        // 在这里处理 interaction_records 字段的渲染前逻辑
        const Attach = BaseComponents.big_file_attachment;
        const license = await this.getLicense();
        // 如果开启了Activity，则获取资源管控信息
        const recording = !!license?.ai_interactive_assistant_app && await this.fetchActivityResourceUsageRecording();
        // 资源超限增加提示
        const exceed = recording?.interaction_recording_duration_limit?.total <= recording?.interaction_recording_duration_limit?.usage
        return {
            components: {
                interaction_records: Attach.extend({
                    render () {
                        // console.log('interaction_records render.before hook 被调用', this, param)
                        Attach.prototype.render.apply(this, arguments)
                        // 资源超限增加提示
                        exceed && this.$el.append(`<div style="color: red;">${$t('sfa.activity.corpus.audio_setting_resource_usage_field_tips')}</div>`)
                    },
                    getUploadAuthParams () {
                        return {
                            ...Attach.prototype.getUploadAuthParams.apply(this, arguments),
                            business: 'SFA_ACTIVITY_SPECIAL'
                        }
                    }
                })
            }
        }
    }

    // 资源管控接口
    fetchActivityResourceUsageRecording() {
      // CRM.util.showLoading_tip();
      return new Promise(function(resolve, reject) {
        CRM.util.FHHApi({
          url: '/EM1HNCRM/API/v1/object/activity_resource_usage/service/recording',
          data: {},
          success: function(res) {
            if (res.Result.StatusCode == 0) {
              resolve(res.Value);
            } else {
              resolve(null);
            }
          },
          complete: function() {
          //   CRM.util.hideLoading_tip();
          }
        }, {
          errorAlertModel: 1
        });
      });
    }

    getLicense() {
        return new Promise(function(resolve, reject) {
          CRM.api.get_licenses({
            key: ['ai_interactive_assistant_app'],
            objectApiName: 'ActiveRecordObj',
            cb: licenses => {
              resolve(licenses)
            }
          })
        })
    }

    getHook() {
        return [
            {
                event: "form.render.before",
                functional: this._renderBefore.bind(this),
            }
        ];
    }
}
