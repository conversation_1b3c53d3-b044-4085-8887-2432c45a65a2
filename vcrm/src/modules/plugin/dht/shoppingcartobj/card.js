let productDesc = null;
// 初始化时 获取产品描述信息并缓存起来
export async function initCardConfig (context, param) { 
  productDesc = await getProductDesc();
  return ;
}

/** 
 * 获取订单产品卡片配置数据 参考https://wiki.firstshare.cn/pages/viewpage.action?pageId=615383108 
 * 模版数据通过 订单产品web端 列表布局中的 卡片视图生成, 
 * 增加的虚拟字段 
 *     标签: 产品中的标签数据, 
 * @param {*} context 
 * @param {*} param 
 * @returns cardConfig 
 */
export function getCardConfig (context, param) { 
  const cardConfig = {
    title: $t('crm.产品'),
    show_image: 'product_image',  //卡片图片对应的字段 field_fujf1__c
    show_image_size: 'large',
    row_sections: [{
        type: 'whole',
        field_section: [{
            fields: [{
                api_name: 'product_id__r',  // 商品名称 product_id__r
                style: {
                    color: '#000',
                    font_size: 16
                },
                type: 'field',
                is_show_label: false
            }]
        }]
    },
    {
          type: 'whole',
          field_section: [{
              fields: [{
                  api_name: 'commodity_label__fake',       // 产品标签         
                  type: 'field',
                  is_show_label: false
              }]
          }]
    },
    {
      type: 'whole',
      field_section: [{
          fields: [
            {
              api_name: 'policy_info_fake',                // 价格政策信息
              type: 'field',
              is_show_label: false,
            },
        ]
      }]
    },
  ]
  }
  return cardConfig;
}

// 根据 policyDetails 获取 活动信息
function getActivePolicyInfo(policyDetails) {
  if (!policyDetails?.length) return null;
  
  const activePolicy = policyDetails.find(policy => policy.isActive);
  // 没有匹配上促销政策时,不显示, 返回null
  if(!activePolicy) return null;
  
  // 遍历activePolicy.rules, 返回 isActive的rule name
  const activeRule = activePolicy?.rules?.find(rule => rule.isActive);
  const ruleNames = activeRule?.name;
  // 没有匹配促销政策中的 规则时,也整行不显示, 返回null
  if(!activeRule) return null;

  /* 显示匹配的全部规则
  const activeRule = activePolicy?.rules?.filter(rule => rule.isActive);
  const ruleNames = activeRule.map(rule => rule.name).join(','); 
  if(!ruleNames) return null;
  */
    
  return {
      name: activePolicy.name,
      ruleNames,
      policy: activePolicy
  };
}

function renderPolicyInfo(cellValue, trData, column) {
  // console.log('renderPolicyInfo', trData);  
  const policy = getActivePolicyInfo(trData.policyDetails || []);
  let html = '';
  if(policy) {
    html = `<span style="color: #FF8000; margin-right: 4px;">${policy.name}</span>
             <span>${policy.ruleNames}</span>`
  }
  return html;  
}

// 渲染 产品标签
function renderProductLabels(trData, column) { 
  let cellValue = trData.commodity_label || [];
  if (!Array.isArray(cellValue)) {
    cellValue = [];
  }
  const labelList = getLabelList(cellValue);
  let html = '';
  for (let index = 0, len = labelList.length; index < len; index++) {
    let item = labelList[index];
    html += `<span style="color:${item.font_color}"> 
          ${item.label}
        </span>`
  }
  return html;
}

/**
 * 异步 获取产品描述信息
 *  
 * */
async function getProductDesc() {
  const isSpuMode = $dht.config.sail.isSpuMode;
  const objectApiName = isSpuMode ? 'SPUObj' : 'ProductObj';  
  const productDesc = await $dht.getService('meta').getSimpleDescribe(objectApiName, 'default__c');
  return productDesc;
}


// 根据订单产品上的产品标签value 获取显示的标签信息
function getLabelList(data) {
  let options = productDesc?.fields?.commodity_label?.options || [];
  if (data && data.length > 0) {
      data = data.map(item => {
          let obj = options.find(option => option.value === item);
          return obj || {};
      })
      return data
  } else {
      return []
  }
}

export function getFakeFieldByCard(cellValue, trData) { 
  return [{
    pos: 'after',
    field_name: 'product_id',
    fields: [      
      {
        api_name: 'commodity_label__fake',
        data: 'commodity_label',
        depend_fields: ['commodity_label'],
        is_readonly: true,
        label: '标签',
        render: function(cellValue, trData, column ) {
          return renderProductLabels(trData, column);
        },
      },      
      {
        api_name: 'policy_info_fake',
        depend_fields: ['policyDetails'],
        is_readonly: true,
        label: '促销优惠',
        render: function(cellValue, trData, column) {
          return renderPolicyInfo(cellValue, trData, column);
        },
      }
    ]
  }]
}

/**
 * 卡片视图下 格式化列渲染, 主要处理增加的虚拟字段
 * @param {*} $data 
 * @returns 
 */
// export function cardformatColumnRender ($data) {
//   return [];
// }

export default  { 
  initCardConfig,
  getCardConfig,
  getFakeFieldByCard,
  // cardformatColumnRender
}