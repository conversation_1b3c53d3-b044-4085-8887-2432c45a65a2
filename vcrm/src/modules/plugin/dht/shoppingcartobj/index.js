/**
 * @desc: 订货通购车
 * @author: chil
 * @date: 5/20/22
 */
import PPM from 'plugin_public_methods';
import Base from 'plugin_base';
import { logService } from '../../../../widgets/services/LogService.ts';
import { CartService } from '../../../../widgets/services/CartService.ts'
import { isEastProduct } from '../../../../widgets/utils/check-ea.ts';
import { initCardConfig, getCardConfig, getFakeFieldByCard } from './card.js';

const OPERATE_TYPE = {
    MD_ADD: 'mdAdd',
};
const cartInstance = CartService.getInstance();
export default class ShoppingCart extends Base {

    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.pluginService = pluginService;
        this.pluginParam = pluginParam;
        this.util = pluginService.api;
        // 存储从对象apiname
        this.mdApiname = 'SalesOrderProductObj';
        // 存储组件
        this.$comp = {};
        const isSimpleCpq = $dht.config.simpleCpq.isEnable;
        const isOpenMiniNum = $dht.config.sail.isOpenMiniNum;
        // 存储数据
        this.$data = {
            currencyFlag: $dht.config.currency.currencyFlag || '',
            // 是否BOM结构： cpq 或 固定搭配
            isBomEnable: $dht.config.bom.isEnable || isSimpleCpq,
            isOpenMiniNum,
            isSimpleCpq,
            cartService: window.$dht.services.cart, // 购物车服务
            cartCheckedData: [], // 记录已勾选的购物车数据（PS：临时存储选中数据，无实时性，所以如果要取最终选中的真实数据时，还是拿id去过滤）
            cartCheckIdMap: {}, // 记录已勾选的购物车数据id
        };
        // 存储可操作表格的实例（目前主要是操作复选框），可用方法在crm的handleCompleteCallback方法里返回
        this.tableExp = null;
        // 标记表格是否渲染完成
        this.isTableRenderComplete = false;
        // 标记下单成功
        this.isOrderSuccess = false;
        // 标记初始化时是否更新底部组件，防止多次计算
        this.isUpdatedFooterOnInit = false;
        // 监听窗口变化
        $(window).on('resize', this.onCartWindowResize);
        // 监听工具栏购物车的删除
        this.$data._onMincartDelete = (cartIds) => {
            this.onMincartDelete(cartIds);
        }
        $dht.store.on('store:mincart.list.delete', this.$data._onMincartDelete);
    }

    options() {
        return {
            defMasterFields: {
                account_id: 'account_id',
            },
            defMdFields: {
                product_price: 'product_price',
                sales_price: 'sales_price',
                discount: 'discount',
                subtotal: 'subtotal',
                is_giveaway: 'is_giveaway',
                price_book_id: 'price_book_id',
                price_book_product_id: 'price_book_product_id',
                product_id: 'product_id',
            },
        }
    }

    async _mdRenderBefore(context, hookParam) {
        // let mdUpdate = hookParam.collectChange().mdUpdate; // 收集之前改过的从对象字段
        // // 主动先调用一次计算接口，拿到最新的从数据(这里需要调用一次计算接口，将一些需要计算的字段或默认值算回来)
        // let mdData = hookParam.dataGetter.getDetail(hookParam.objApiName);
        // let rowIds = mdData.filter(a => !a.parent_prod_pkg_key).map(a => { return a.rowId });
        // let filterFields = ['product_price', 'price_book_price'];
        // if (!PPM.isEmpty(mdUpdate)) {
        //     let mdUpdateFields = Object.keys(mdUpdate[rowIds[0]] || {}) || []; // 之前计算过的字段不参与此次计算
        //     filterFields = filterFields.concat(mdUpdateFields);
        // }
        // await hookParam.triggerCalAndUIEvent({
        //     newDataIndexs: rowIds,
        //     objApiName: hookParam.objApiName,
        //     filterFields: {[this.mdApiname]: filterFields}, // 不参与此次计算的字段
        // });

        const { dataGetter, masterObjApiName } = hookParam;
        const mdApiname = this.mdApiname;
        const details = dataGetter.getDetail(mdApiname);
        const hasPackage = details.some(item => item.is_package__v);

        const me = this;
        const preOperateBtns = context.preData.operateBtns || [];
        const delOperateBtns = ['copyRowHandle', 'dragHandle']; // 去掉复制/拖拽按钮
        // 固定搭配，不能配置子产品，将配置按钮去掉
        if (this.$data.isSimpleCpq) {
            delOperateBtns.push('bomConfigHandle');
        }
        // console.log('_mdRenderBefore on dht_shoppingcart');

        let fakeFields = undefined;
        let cardConfig = undefined;

        // 插件中配置了启用卡片视图, 才执行增加 cardConfig, 否则不增加
        const dhtMdConfig = await this.runPlugin('dht.shoppingcart.mdRenderBefore');
        if(dhtMdConfig?.useCard) {
            cardConfig = getCardConfig(context, hookParam);;
            fakeFields = getFakeFieldByCard(context, hookParam);
            await initCardConfig(context, hookParam);
        }

        // console.log('fakeFields', fakeFields);
        return {
            fakeFields,
            cardConfig,
            // 表格右上角操作按钮
            buttons: {
                retain: [],
                add: [],
            },
            // 表格左上角批量按钮
            batchButtons: {
                retain: ['delSelectedHandle'], // 只保留删除操作
            },
            // 单行操作按钮
            operateBtns: preOperateBtns.concat([() => {
                return {
                    del: delOperateBtns,
                }
            }]),
            // 自定义列的render
            columnRenders: me.formatColumnRender(),
            // 表格配置
            tableOptions: {
                termBatchPos: 'C',
                autoHeight: null,
                maxHeight: null,
                height: null,
                renderComplete(tableIns) {
                    me.tableRenderComplete(tableIns);
                },
            },            
            headerSlot: [],
            //每个表格下面可以追加footer 可用于统计之类的展示
            // tableFooterSlot: {
            //     common(wrapper, list, opts) {
            //         return me.initTableFooter(wrapper, list, opts);
            //     },
            // },
            //从对象底部可以追加footer 可用于统计之类的展示
            footerSlot: [(wrapper) => {
                return me.initTableFooter(wrapper, hookParam);
            }],
        }
    }
    /**
     * 自定义列的render
     */
    formatColumnRender() {
        const me = this;
        // 给值加上币种符号
        let addCurrencyFlag = (val) => {
            if (val === '--' || val === '-') {
                return val;
            }
            return me.$data.currencyFlag + val;
        }        
        let rst = [{
            product_price(cellValue, trData) {
                return addCurrencyFlag(cellValue);
            },
            sales_price(cellValue, trData) {
                return addCurrencyFlag(cellValue);
            },
            subtotal(cellValue, trData) {
                return addCurrencyFlag(cellValue);
            },
            price_book_price(cellValue, trData) {
                return addCurrencyFlag(cellValue);
            },
        }]

        // 卡片视图下 格式化列渲染, 主要处理增加的虚拟字段
        // const cardFormatColumnRender = cardformatColumnRender();
        // if (cardFormatColumnRender) {
        //     Object.assign(rst[0], cardFormatColumnRender[0]);
        // }
        // console.log('formatColumnRender rst', rst);
        return rst;
    }

    // ======================================== 处理表格 checkbox 选中/非选中 状态 ====================================
    /**
     * 表格渲染完成后
     */
    tableRenderComplete(tableIns) {
        this.isTableRenderComplete = true;
        this.tableExp = tableIns;
        this.setDefaultCheckState(tableIns);
        tableIns.on('update', (data) => {
            this.tableUpdateHandle();
        });
        tableIns.on('check.change', (checkList) => {
            this.tableCheckChange(tableIns, checkList);
        });
    }
    /**
     * 表格渲染完成后，设置初始的选中数据
     */
    setDefaultCheckState(tableIns) {
        const mdApiname = (tableIns && tableIns.objectApiName) || this.mdApiname;
        // 表格渲染完成后，初始化选中数据
        let factory = this.util.pluginServiceFactory({pluginApiName: 'dht_shoppingcart'});
        let mdData = factory.dataGetter.getDetail(mdApiname);
        let rowIds = [];
        mdData.forEach(a => {
            if(a.is_settled) {
                rowIds.push(a.rowId);
                this.$data.cartCheckedData.push(a);
                this.$data.cartCheckIdMap[a.rowId] = true;
            }
        });
        if(rowIds.length) {
            if(tableIns) {
                tableIns.check(rowIds); // 设置初始选中
            } else {
                this.pluginService.api.checkDetailsRow(rowIds, true);
            }
        }
    }
    /**
     * 表格更新时执行
     */
    tableUpdateHandle(data) {
        // 对于洁柔定制的表格，数据更新时, 需要更新底部的统计字段
        if(!this.tableExp) {
            if(!this.$comp.footerIns) {
                return;
            }
            const factory = this.util.pluginServiceFactory({pluginApiName: 'dht_shoppingcart'});
            const { checkedStatus } = this.getCheckedListAndStatus(factory.dataGetter);

            // 使用主对象的计算接口，获取最新的统计计算结果
            const result = factory.dataGetter.getMasterData();
            this.$comp.footerIns.updateCartFooter({
                result,
                ...checkedStatus,
            });
            return;
        }

        //  如果是价格政策的更新 或 bom配置改变，则处理选中状态
        if (this.tableExp.isPricePolicyUpdate || this.tableExp.isBomChange || this.tableExp.isTableUpdate) {
            delete this.tableExp.isPricePolicyUpdate;
            delete this.tableExp.isBomChange;
            delete this.tableExp.isTableUpdate;
            let hookParam = this.util.pluginServiceFactory({pluginApiName: 'dht_shoppingcart'});
            let rowIds = Object.keys(this.$data.cartCheckIdMap)
            rowIds.length && this.tableExp.check(rowIds);
        }
    }
    /**
     * 更新选中/非选中状态，并更新底部信息，并将状态落库
     */
    tableCheckChange(tableIns, checkList) {
        // 已选中的购物车产品和UI事件添加的，过滤掉非购物车正规产品(如：赠品、bom子产品、bom分组)
        const checkedCartList = this.getMainProducts(checkList);

        let isCheckChange = this.isCheckChange(this.$data.cartCheckedData, checkedCartList);
        if (!isCheckChange) {// 没有变化，直接返回
            return ;
        }

        const {
            selectCartIds,
            unSelectCartIds,
        } = this.getDiffSelected(this.$data.cartCheckedData, checkedCartList);

        if (selectCartIds.length) {
            this.$data.cartService.batchUpdateSettled(selectCartIds, true); // 选择需要落库
        }
        if (unSelectCartIds.length) {
            this.$data.cartService.batchUpdateSettled(unSelectCartIds, false); // 反选选择需要落库
        }

        this.$data.cartCheckedData = checkedCartList;
        this.$data.cartCheckIdMap = {};
        checkedCartList.forEach(a => this.$data.cartCheckIdMap[a.rowId] = true);

        let factory = this.util.pluginServiceFactory({pluginApiName: 'dht_shoppingcart'});
        this.updateFooterOnCheckChange(factory);
    }

    getMainProducts(list =[]) {
        // 购物车产品，或者不是bom子产品
        return list.filter(item => item.cartId || (/*!(item.is_giveaway === '1') &&*/ !(item.bom_id && !item.cartId)));
    }

    getDataMap(list = []) {
        return list.reduce((obj, item) => {
            const key = item.cartId;
            obj[key] = item;
            return obj;
        }, {})
    }

    isCheckChange(oldCheckData, newCheckData) {
        if (oldCheckData.length !== newCheckData.length) {
            return true;
        }

        let isCheckChange = false;
        const oldKeys = oldCheckData.map(item => item.rowId);
        const newKeys = newCheckData.map(item => item.rowId);

        for (let i = 0; i < newKeys.length; i++) {
            const key = newKeys[i];
            if (!oldKeys.includes(key)) {
                isCheckChange = true;

                break;
            }
        }

        return isCheckChange;
    }

    /**
     * 获取新选的数据和反选的数据
     * @param oldCheckData: 上次选择的数据
     * @param newCheckData: 本次选择的数据
     * @return {
     * selectCartIds: string[], 改变的新选择购物车Id
     * unSelectCartIds string[], 改变的反选的购物车Id
     * selectProducts: any[], 改变的新选择购物车产品
     * unSelectProducts: any[], 改变的反选的购物车产品
     * }
     */
    getDiffSelected(oldCheckData, newCheckData) {
        // 转化为map
        const oldCheckDataMap = this.getDataMap(oldCheckData);
        const newCheckDataMap = this.getDataMap(newCheckData);
        // 获取新选择的购物车id和被反选的购物车Id
        const { selectCartIds, unSelectCartIds } = this.getChangeSelected(oldCheckDataMap, newCheckDataMap);

        return { selectCartIds, unSelectCartIds };
    }
    /**
     * 获取改变的购物车Id，包括新选和反选的
     * @param oldCheckDataMap
     * @param newCheckDataMap
     * @return {
     * selectCartIds: string[], 改变的新选择购物车Id
     * unSelectCartIds string[], 改变的反选的购物车Id
     * }
     */
    getChangeSelected(oldCheckDataMap, newCheckDataMap) {
        const oldKeys = Object.keys(oldCheckDataMap);
        const newKeys = Object.keys(newCheckDataMap);
        const selectCartIds = [];
        const unSelectCartIds = [];
        oldKeys.forEach((key) => {
            if (!newCheckDataMap[key] && oldCheckDataMap[key].cartId) {
                unSelectCartIds.push(key);
            }
        });
        newKeys.forEach((key) => {
            if (!oldCheckDataMap[key] && newCheckDataMap[key].cartId) {
                selectCartIds.push(key);
            }
        });
        return { selectCartIds, unSelectCartIds };
    }
    /**
     * 获取产品的赠品
     * @param products
     * @param allTableData
     */
    getProductsGifts(products, allTableData) {
        let gifts = [];
        let giftRowIds = [];
        if (this.pluginParam.bizStateConfig.advancedPricing) {
            const productKeys = products.map(item => item.prod_pkg_key);
            allTableData.forEach(item => {
                if (item.is_giveaway === '1' && productKeys.includes(item.parent_gift_key)) {
                    gifts.push(item);
                    giftRowIds.push(item.rowId);
                }
            });
        }
        return {gifts, giftRowIds};
    }
    // ====================================================================================================

    // ======================================== 处理表格 footer 底部汇总 ====================================
    /**
     * 初始化表格底部汇总组件
     */
    initTableFooter(wrapper, hookParam, opts) {
        const me = this;
        const $el = document.createElement('div');
        wrapper.append($el);

        me.util.import('vcrm/sdk').then((SDK) => {
            SDK.widgetService.getWidgetApp('cartFooter', {
                el: $el,
                propsData: {}
            }).then((ins) => {
                me.$comp.footerIns = ins;
                me.tableFooterListener();
                me.updateTableFooterOnInit(hookParam);
            })
        });

        return {
            /* update(data) { //res: {add: [{...}], update: {rowId: {....}, del: [rowId, rowId...], insert:[{insertRowId: 'xxx', datas: [{.....}]}]}}
                me.tableUpdateHandle(data);
            },  */
            // 洁柔定制, 对于完全客开的表格, 需要同步选中数据
            checkChange: (val) => {
                console.log('shoppingcart checkChange', val);
                // 未定制表格, 不做处理
                if(this.tableExp) {
                    return;
                }
                // 获取最新的数据, 需要使用factory
                const ps = me.util.pluginServiceFactory({pluginApiName: 'dht_shoppingcart'});
                // 如果底部未初始化过, 则手动触发一次
                if(!this.isUpdatedFooterOnInit) {
                    this.updateTableFooterOnInit(ps, true);
                    return;
                }

                // 正常选中逻辑
                const checkedList = ps.dataGetter.getCheckedDatas(me.mdApiname);
                me.tableCheckChange(null, checkedList);
            },
            destroy() {
                me.$comp.footerIns && me.$comp.footerIns.$destroy && me.$comp.footerIns.$destroy();
                me.$comp.footerIns = null;
            },
        }
    }
    // 获取已选中的数据, 替代this.$data.cartCheckedData 与 this.$data.cartCheckIdMap
    getCheckedList(dataGetter) {
        const me = this;
        // console.log('getCheckedList checkedList', checkedList);
        // 注意 getCheckedDatas是还未回写到视图中的数据, 是旧的数据, 仅rowId有效
        let checkedList = dataGetter.getCheckedDatas(me.mdApiname) || [];
        // 内存中的实时数据
        let mdData = dataGetter.getDetail(me.mdApiname) || [];
        // 深拷贝一份, 避免直接修改checkedList
        checkedList = CRM.util.deepClone(checkedList);
        mdData = CRM.util.deepClone(mdData);

        // 使用mdData中的数据, 更新checkedList中的数据, 保证数据的实时性, 如数量、价格等
        checkedList = checkedList.map(a => mdData.find(b => b.rowId === a.rowId));
        // 过滤掉mdData中被删除的数据
        checkedList = checkedList.filter(a => a);

        mdData = me.getMainProducts(mdData);
        const mdMap = {};
        mdData.forEach(item => {
            mdMap[item.rowId] = {
                rowId: item.rowId,
                cartId: item.cartId,
                parent_gift_key: item.parent_gift_key,
                is_giveaway: item.is_giveaway,
            };
        });
        let checkedIdsMap = {};
        checkedList.forEach(a => {
            checkedIdsMap[a.rowId] = true;
        });
        // 遍历 mdData, 维护赠品跟随父级的选中状态
        mdData.forEach(a => {
            if(a.is_giveaway === '1' && a.parent_gift_key) {
                const parentGiftKey = a.parent_gift_key;
                // 如果父级在选中列表中, 并且当前行不在选中列表中, 则添加到选中列表中
                if(checkedIdsMap[parentGiftKey] && !checkedIdsMap[a.rowId]) {
                    checkedList.push(a);
                    // checkedIdsMap[a.rowId] = true;
                }

                // 如果父级不在选中列表中, 并且当前行在选中列表中, 则从选中列表中移除
                if(!checkedIdsMap[parentGiftKey] && checkedIdsMap[a.rowId]) {
                    checkedList = checkedList.filter(b => b.rowId !== a.rowId);
                    // delete checkedIdsMap[a.rowId];
                }
            }
        });

        // 根据checkedList 更新 checkedIdsMap
        checkedIdsMap = {};
        checkedList.forEach(a => checkedIdsMap[a.rowId] = true);
        // // 向checkedList中添加赠品行
        // checkedList = checkedList.concat(mdData.filter(a => a.is_giveaway === '1' && checkedIdsMap[a.rowId]));

        // // 删除checkedList中不在checkedIdsMap中的行
        // checkedList = checkedList.filter(a => checkedIdsMap[a.rowId]);
        return {checkedList, checkedIdsMap};
    }

    getCheckedListAndStatus(dataGetter) {
        const me = this;
        const {checkedList} = me.getCheckedList(dataGetter);

        const mdData = dataGetter.getDetail(me.mdApiname);
        const mainProducts = me.getMainProducts(mdData);

        const checkedLength = checkedList.length;
        const detailLength = mainProducts.length;
        const isCheckedAll = checkedLength > 0 && checkedLength === detailLength;
        const isIndeterminate = checkedLength > 0 && checkedLength < detailLength;

        return {
            checkedList,
            checkedStatus: {
                isCheckedAll,
                isIndeterminate,
            }
        };
    }

    getCalParams(dataGetter, checkedList, operateType) {
        const params = {
            details: {
                SalesOrderProductObj: checkedList
            },
            objApiName: this.mdApiname,
            noMerge: true,
            noLoading: true,
            operateType,
        };

        // 新增的情况不要主对象
        if (operateType !== OPERATE_TYPE.MD_ADD) {
            const masterData = dataGetter.getMasterData();
            params.masterData = masterData;
        }

        return params;
    }

    /**
     * 购物车初始化更新底部统计信息
     */
    updateTableFooterOnInit(hookParam, isCheckChange = false) {
        if (!this.isUpdatedFooterOnInit && this.$comp.footerIns) {
            this.updateFooterOnAdd(hookParam, isCheckChange);
        }
    }

    /**
     * 更新表格底部统计信息
     */
    async updateTableFooter(hookParam, params, checkedStatus) {
        if (!this.$comp.footerIns) return;

        // console.log('==== updateTableFooter ====');

        this.isUpdatedFooterOnInit = true;

        // 必须用factory，不能用hookParams，hookParams会改变自定义的operateType等参数
        const factory = this.util.pluginServiceFactory({pluginApiName: 'dht_shoppingcart'});
        const res = await factory.triggerCal(params);

        // 需改了不需要计算的字段时，是没有返回值的
        if (!res) {
            return;
        }

        const {
            SalesOrderObj = {},
        } = res?.Value?.calculateResult;
        const result = SalesOrderObj[0];

        this.$comp.footerIns.updateCartFooter({
            result,
            ...checkedStatus,
        });
    }

    async updateFooterOnEdit(hookParam) {
        const {
            dataGetter,
            operateType,
            dataIndex,
            changeData,
            fieldName
        } = hookParam;
        const {
            checkedList,
            checkedStatus,
        } = this.getCheckedListAndStatus(dataGetter);

        // 是否编辑的已选产品
        const isEditCheckedList = checkedList.some(item => dataIndex.includes(item.rowId));
        if (!isEditCheckedList) {
            return;
        }

        const index = dataIndex[0];
        const changeFields = Object.keys(changeData[index]);
        const params = this.getCalParams(dataGetter, checkedList, operateType);
        Object.assign(params, {
            changeFields,
            dataIndex,
            fieldName,
        })

        await this.updateTableFooter(hookParam, params, checkedStatus);
    }

    updateFooterOnAdd(hookParam, isCheckChange = false) {
        const { dataGetter } = hookParam;
        const {
            checkedList,
            checkedStatus,
        } = this.getCheckedListAndStatus(dataGetter);

        // 页面加载时没有选中，或者cartCheckIdMap还没有算出来值的时候，可以忽略计算
        // 如果是因为选中状态则一定要重新计算
        if (!isCheckChange && checkedList.length === 0) {
            return;
        }

        const params = this.getCalParams(dataGetter, checkedList, OPERATE_TYPE.MD_ADD);

        this.updateTableFooter(hookParam, params, checkedStatus);
    }

    async updateFooterOnDel(hookParam) {
        const { dataGetter, operateType, delDatas } = hookParam;
        const {
            checkedList,
            checkedStatus,
        } = this.getCheckedListAndStatus(dataGetter);
        const cartCheckIdMap = this.$data.cartCheckIdMap;

        // 是否删除的已选产品
        const isDelCheckedList = delDatas.some(({ rowId }) => cartCheckIdMap[rowId]);
        if (!isDelCheckedList) {
            // 删除为勾选的产品，有可能会引起底部状态变化，更新已选状态
            this.$comp?.footerIns?.updateCheckStatus(checkedStatus);

            return;
        }

        const params = this.getCalParams(dataGetter, checkedList, operateType);

        await this.updateTableFooter(hookParam, params, checkedStatus);
    }

    updateFooterOnCheckChange(factory) {
        this.updateFooterOnAdd(factory, true);
    }
    /**
     * 监听化底部信息组件抛出的事件，包括全选、结算、全部删除事件
     */
    tableFooterListener() {
        let footer = this.$comp.footerIns;
        // 全选/取消全选
        footer.$on('select-change', (val) => {
            const targets = $('.j-all-checkbox');
            // 模拟md表格的全选按钮点击事件来触发全选或者反选
            if (targets && targets.length > 0) {
                const e = document.createEvent('MouseEvents');
                e.initEvent('click', true, true);
                targets[0].dispatchEvent(e);
            } else {
                this.pluginService.api.checkDetailsRow(null, val);
            }
        });
        // 购物车结算事件
        footer.$on('order-create', () => {
            this.submitHandle();
        });
        // 全部删除事件
        footer.$on('delete-selected', () => {
            // 选择的产品数据，不包括赠品
            this.batchDeleteHandle();
        });
    }
    /**
     * 结算
     */
    async submitHandle() {
        let factory = this.util.pluginServiceFactory({
            pluginApiName: 'dht_shoppingcart'
        });
        let masterData = factory.dataGetter.getMasterData();
        let masterDescribe = factory.dataGetter.getDescribe(masterData.object_describe_api_name);
        let mdData = factory.dataGetter.getDetail(this.mdApiname);
        let checkedList = mdData.filter(a => this.$data.cartCheckIdMap[a.rowId]);

        if (!checkedList || checkedList.length <= 0) {
            this.util.alert($t('i18n.shoppingcart.select_at_least_one_product', '请至少选择一件商品结算'));
            return;
        }

        let {gifts} = this.getProductsGifts(checkedList, mdData);

        if (this.$data.isBomEnable) {
            checkedList = this.getSubmitBomList(checkedList, mdData);
        }

        // 判断是否需要计算客户Id相关的计算字段
        const masterExtraFields = [];
        const accountIdField = masterDescribe.fields.account_id;
        if (
            accountIdField
            && accountIdField.calculate_relation
            && accountIdField.calculate_relation.calculate_fields
            && Array.isArray(accountIdField.calculate_relation.calculate_fields.SalesOrderObj)
        ) {
            masterExtraFields.push('account_id');
        }

        // 结算前，主动先调用一次计算接口，拿到最新的主数据
        let res = await factory.triggerCal({
            noMerge: true,
            operateType: 'mdAdd',
            dataIndex: checkedList.map(a => a.rowId),
            objApiName: this.mdApiname,
            details: {[this.mdApiname]: checkedList.concat(gifts)},
            filterFields: {[this.mdApiname]: ['product_price', 'price_book_price', 'discount', 'quantity', 'unit']}, // 不参与此次计算的字段
            extraFields: {
                SalesOrderObj: masterExtraFields,
            }
        })

        if(!res || res.Result.StatusCode) {
            this.util.alert($t('i18n.shoppingcart.calculation_error_retry', '检测到有计算异常请重试'));
            return;
        }

        let calRes = res.Value.calculateResult;
        masterData = Object.assign(masterData, calRes[factory.masterObjApiName][0]);

        // apiname可能修改需要从新取一下
        let describe = factory.dataGetter.getDescribeLayout();
        let displayName = describe && describe.objectDescribe.display_name || describe.objectDescribe.display_name_r

        // 跳转到销售订单页面
        window.$dht.createOrder({
            apiname: 'SalesOrderObj',
            displayName,
            source: 'cart', // 标识订货通购物车来源
            _from: 'cart', // 标识需要恢复购物车的价格政策
            showDetail: true, // 订单提交成功后显示订单详情
            noPreCalculate: true, // 标识不需要前期的预计算
            isSubmitAndCreate: false, // 不显示提交继续创建按钮
            record_type: masterData.record_type, // 传入主对象的业务类型
            data: masterData, // 主对象数据
            mdData: {[this.mdApiname]: checkedList.concat(gifts)}, // 从对象数据
            success: (action, data, dataId, details) => {
                if (action === 'add') {
                    // 提交成功后需要删除对应的购物车数据
                    const ids = details.map(item => item.cartId || item.__temp_data_id__);
                    const deleteList = checkedList.filter((item) =>
                        ids.includes(item.cartId || item.__temp_data_id__)
                    );
                    this.isOrderSuccess = true;
                    this.batchDeleteHandle(deleteList);
                }
                return Promise.resolve();
            },
        });
    }
    /**
     * 开启了bom，整理结算产品列表
     */
    getSubmitBomList(checkedList, mdData) {
        let bomList = [];
        _.each(checkedList, a => {
            bomList.push(a);
            if (a.is_package__v && a.root_prod_pkg_key) {
                let list = PPM.getChildrenByRootKey({ rootKey: a.root_prod_pkg_key, details: mdData }) || [];
                const children = list.filter(item => !item.isGroup); // 过滤掉group
                bomList = bomList.concat(children);
            }
        })
        return bomList;
    }
    /**
     * 批量删除数据
     * @param {要删除的数据，可选} list
     */
    batchDeleteHandle(list) {
        let factory = this.util.pluginServiceFactory({
            pluginApiName: 'dht_shoppingcart'
        });
        let mdData = factory.dataGetter.getDetail(this.mdApiname);
        let checkedList = mdData.filter(a => this.$data.cartCheckIdMap[a.rowId]);
        let deleteList = list ? list : checkedList;

        if (!(deleteList && deleteList.length)) return;

        // let rowIds = deleteList.map(a => a.rowId);
        // factory.runMDDelService(rowIds);
        this.util.proxyService('runMDDelService', {
            objApiName: this.mdApiname,
            recordType: mdData[0].record_type,
            delDatas: deleteList,
        });
    }
    // ============================================================================================

    async _mdRenderAfter(context, hookParam) {
        $(window).trigger('resize');
        FS.util.addWatermark('.paas-cart-container .table-wrapper');
        this.setTableNav(hookParam);
        this.setFieldsReadonly(hookParam);

        if (this.$data.isBomEnable) {
            // 默认展开bom结构
            hookParam.dataUpdater.updateTreeExpend(true, this.mdApiname);
            // 触发一下bom价格计算
            await this.queryBomAndCalculate(hookParam);
        }

        this.updateTableFooterOnInit(hookParam);
    }

    // 自定义表格渲染完成
    _mdCustTableRenderComplete(hookParam) {
        this.isTableRenderComplete = true;
    }

  /**
   * 触发一下bom价格计算，否则node_subtotal等没有值
   * @param hookParam
   * @return {Promise<*>}
   */
  queryBomAndCalculate(hookParam) {
      let mdData = hookParam.dataGetter.getDetail(hookParam.objApiName);
      let masterData = hookParam.dataGetter.getMasterData();
      if (mdData.length) {
        return this.runPlugin('bom.queryBomAndCalculate', {
          obj: {
            data: mdData,
            masterData,
            masterApiName: hookParam.masterObjApiName,
            mdApiName: this.mdApiname,
            recordType: mdData[0].record_type
          },
          param: hookParam
        });
      }
      return Promise.resolve();
    }

    /**
     * 设置字段只读
     */
    setFieldsReadonly(hookParam) {
        let {
            product_price,
            sales_price,
            discount,
            subtotal,
            is_giveaway,
            price_book_id,
            price_book_product_id,
            product_id,
        } = this.getAllFields(hookParam.objApiName);
        // 不可编辑字段：价格、销售价格、折扣、小计、是否赠品、价目表id、价目表明细id, 产品id
        hookParam.dataUpdater.setReadOnly({
            fieldName: [sales_price, discount, subtotal, is_giveaway, price_book_id, price_book_product_id, product_id],
            dataIndex: 'all',
            objApiName: hookParam.objApiName,
            status: true
        });

        let dataIndex = 'all';
        if (this.$data.isBomEnable) {
            // bom数据结构 子产品价格字段 是否可编辑 根据配置来，这里不处理
            dataIndex = [];
            let mdData = hookParam.dataGetter.getDetail(hookParam.objApiName);
            mdData.forEach(item => {
                if (!item.parent_prod_pkg_key) {
                    dataIndex.push(item.rowId);
                }
            });
        }
        hookParam.dataUpdater.setReadOnly({
            fieldName: [product_price],
            dataIndex: dataIndex,
            objApiName: hookParam.objApiName,
            status: true
        });
    }
    /**
     * 设置表格头部区域的显示内容
     */
    setTableNav(hookParam) {
        $('.md20-layoutitem__wrapper .tit-wrapper').hide(); // 表格头部业务类型、操作等显示区域
        let $titEl = `
            <div class="nav-item active" style="color: var(--color-primary06,#FF8000);border-bottom-color:var(--color-primary06,#FF8000)">
                <span class="item-label">${this.i18n('购物车')}</span>
            </div>
        `
        $('.md20-nav__wrapper .crm-md20-navbar').show();
        $('.md20-nav__wrapper .crm-md20-navbar').html($titEl);

        //只保留第一个表格，其他全部隐藏
        _.each($('.md20-layoutitem__wrapper'), (el, index) => {
            if(index != 0) $(el).hide();
        })

        // 初始化业务类型组件：如果可售范围设置了业务类型，则购物车不需要有切换业务组件的类型
        let rangeFilter = $dht.config.newAvailableRangeFilter;
        if (!(rangeFilter.isEnable && rangeFilter.filter_field === 'record_type')) {
            this.initRecordTypeSelect(hookParam);
        }
    }
    /**
     * 初始化切换业务类型组件
     */
    async initRecordTypeSelect(hookParam) {
        let masterData = hookParam.dataGetter.getMasterData();
        let recordTypefield = hookParam.dataGetter.getFieldAttr('record_type', hookParam.masterObjApiName);
        // 先获取实际可用的业务类型
        const recordTypeList = await $dht.getRecordTypeData();
        if (!Array.isArray(recordTypeList) || recordTypeList.length < 2) {
            // 可选业务类型小于2个，不显示业务类型选择组件
            return;
        }        
        let Sdk = await this.util.import('vcrm/sdk');
        
        const wrapper = document.createElement('div');
        const $target = $('.md20-nav__wrapper .crm-md20-navbar')[0];
        $target.append(wrapper);
        this.$comp.recordtype = await Sdk.widgetService.getWidgetApp('cartRecordType', {
            el: wrapper,
            propsData: {
                defaultVal: masterData.record_type,
                field: recordTypefield,
            }
        })
    }

    _mdEditBefore(context, hookParam) {
        const apiName = hookParam.fieldName;
        const rowId = hookParam.dataIndex[0];
        const rowData = hookParam.dataGetter.getData(hookParam.objApiName, rowId);
        const isCommonUnit = rowData && rowData.is_common_unit;
        // 当是常用单位时，不允许编辑单位相关字段
        if ((apiName === 'actual_unit' || apiName === 'other_unit') && isCommonUnit) {
            context.api.alert($t('i18n.shoppingcart.product_has_common_unit', '该产品有常用单位，单位相关字段不可编辑'));
            context.skipPlugin(); // 不再执行后续插件，返回失败（状态非0）
        }
    }

    async _mdEditAfter(context, hookParam) {
        const apiName = hookParam.fieldName;
        const rowId = hookParam.dataIndex[0];
        const changeData = hookParam.changeData[rowId];
        const rowData = hookParam.dataGetter.getData(hookParam.objApiName, rowId);

        if(!rowData) return;

        const isCartProduct = rowData.cartId;
        const isEditCartField = ['quantity', 'actual_unit'].includes(apiName) || (apiName === 'product_price' && rowData.parent_bom_id);
        if (isCartProduct && isEditCartField) {
            let finalData = Object.assign(rowData, changeData);
            let updateParam = {
                quantity: finalData.quantity,
                unit: finalData.actual_unit || '',
            }

            if (+finalData.quantity <= 0) {
                context.api.alert($t('i18n.shoppingcart.quantity_must_greater_than_zero', '数量必须大于0'));
                context.skipPlugin();
                return;
            }
            // TODO if逻辑优化，类型转换用加号
            // 最小订购量,修改数量
            // TODO 从购物车到销售订单，最小订购量的数据可能丢失
            if (this.$data.isOpenMiniNum) {
                if (apiName === 'quantity') {
                    if (rowData.is_multiple_unit__v) {
                        const curUnitOption = rowData.multiunit__ro.find(item => rowData.actual_unit === item.unit_id);

                        if (curUnitOption && curUnitOption.minimum_order_quantity && +finalData.quantity < +curUnitOption.minimum_order_quantity) {
                            context.api.alert($t('ava.dht.product.minimum.tip') + `${+curUnitOption.minimum_order_quantity}`);
                            updateParam.quantity = curUnitOption.minimum_order_quantity;
                            changeData.quantity = curUnitOption.minimum_order_quantity;
                            rowData.quantity = curUnitOption.minimum_order_quantity;
                        }
                    } else if (+finalData.quantity < +rowData.minimum_order_quantity) {
                        context.api.alert($t('ava.dht.product.minimum.tip') + `${+curUnitOption.minimum_order_quantity}`);
                        updateParam.quantity = rowData.minimum_order_quantity;
                        changeData.quantity = rowData.minimum_order_quantity;
                        rowData.quantity = rowData.minimum_order_quantity;
                    }
                } else if (apiName === 'actual_unit') {
                    const curUnitOption = rowData.multiunit__ro.find(item => item.unit_id === rowData.actual_unit);

                    if (curUnitOption && curUnitOption.minimum_order_quantity) {
                        updateParam.quantity = curUnitOption.minimum_order_quantity;
                        changeData.quantity = curUnitOption.minimum_order_quantity;
                        rowData.quantity = curUnitOption.minimum_order_quantity;
                    }
                }
            }

            // 如果是bom子产品，则更新主产品存bom信息的字段
            if (rowData.parent_bom_id) {
                const t = await this.getMainBomDataBySub(hookParam, rowData.root_prod_pkg_key);
                finalData = t.finalData;
                updateParam = t.updateParam;
            }

            await this.fetchUpdateCart(context, finalData, updateParam);
        }

        await this.updateFooterOnEdit(hookParam);
    }
    /**
     * 子产品改变时，获取主产品及主产品要更新的字段
     * @param {*} hookParam
     * @param {*} rootProdPkgKey 根节点产品的唯一标识
     */
    async getMainBomDataBySub(hookParam, rootProdPkgKey) {
        let SDK = await this.util.import('vcrm/sdk');
        let bomService = await SDK.widgetService.getService('bomService');
        let mdData = hookParam.dataGetter.getDetail(this.mdApiname);
        let rootProduct = mdData.find(a => { return a.root_prod_pkg_key === rootProdPkgKey; });
        let subList = PPM.getChildrenByRootKey({ rootKey: rootProduct.root_prod_pkg_key, details: mdData });
        let reSublist = [];
        _.each(subList, item => {
            if (!item.isGroup) {
                let obj = bomService.getSubProductsObj(item);
                reSublist.push(obj);
            }
        });

        let bomData = {
            key_sub_products_selected_in_product: reSublist, // 产品下选择的子产品明细
            adjust_total_price: rootProduct.sales_price, // 配置后的产品包价格
            bom_id: rootProduct.bom_id,
            bomCoreData: {
              _id: rootProduct.bom_core_id,
              name: rootProduct.bom_core_id__r,
              node_bom_core_type: rootProduct.bom_type,
              node_bom_core_version: rootProduct.bom_version,
            },
        }
        const updateParam = {
            extend_content: { bom_data: bomData }
        }

        if (isEastProduct(rootProduct.product_id__r)) {
            updateParam.attribute_json = rootProduct.attribute_json;
        }

        return {
            finalData: rootProduct,
            updateParam,
        }
    }
    /**
     * 更新购物车-请求
     * @param {*} context 上下文
     * @param {*} product 要更新的购物车产品
     * @param {*} fields 要更新的字段
     */
    fetchUpdateCart(context, product, fields) {
        this.util.showLoading();
        return this.$data.cartService.updateFields(product.cartId, fields).then((res) => {
            this.util.hideLoading();
        }, (error) => {
            console.log(error); // 底层已经弹窗报错了，这里就不需要再弹窗
            this.util.hideLoading();
            context.skipPlugin(); // 不再执行后续插件，返回失败（状态非0）
        });
    }

    async _mdDelBefore(context, hookParam) {
        let cartIds = hookParam.delDatas.filter(a => a.cartId).map(a => a.cartId);

        if (!cartIds.length) {
            return;
        }

        this.util.showLoading();
        await this.$data.cartService.batchRemove(cartIds).then(() => {
            // 更新工具栏购物车飘数
            cartInstance.updateCartNumber();

            this.util.hideLoading();

            // 下单成功返回后删除不要弹框
            !this.isOrderSuccess && CRM.util.remind(1, $t("删除成功"));
            this.isOrderSuccess = false;

            if (this.tableExp) {
                this.tableExp.isTableUpdate = true;
            }
        }, () => {
            this.util.hideLoading();
            context.skipPlugin(); // 不再执行后续插件，返回失败（状态非0）
        })
    }

    async _mdDelAfter(context, hookParam) {
        await this.updateFooterOnDel(hookParam);
    }
    /**
     * 价格政策更新后
     */
    _pricePolicyUpdateAfter(context, params) {
        if (!this.isTableRenderComplete) return // 初始购物车时，价格政策触发了，但renderComplete还没触发，所以直接返回

        if(this.tableExp) {
            this.tableExp.isPricePolicyUpdate = true;
        }
        // 再计算一次
        this.updateFooterOnAdd(params.param);
    }
    /**
     * bom配置更改后
     */
     async _bomUpdateAfter(context, params) {
        if (this.tableExp) this.tableExp.isBomChange = true;
        const hookParam = params.param;
        const rootData = params.rootData;
        const t = await this.getMainBomDataBySub(hookParam, rootData.root_prod_pkg_key);
        await this.fetchUpdateCart(context, t.finalData, t.updateParam);

        await this.updateFooterOnAdd(params.param);
    }
    /**
     * 窗口变化时（window resize），动态计算表格容器高度
     * 这里只是改变表格容器高度，table => resize() => _countHeight() 依赖表格容器高度计算
     */
    onCartWindowResize() {
        // 表格容器高度 = 页面容器高度 - 导航高度 - 底部结算组件高度
        let wrapH = $('.paas-cart-container').height();
        let navH = $('.paas-cart-container .md20-nav__wrapper').outerHeight() || 41;
        let footerH = $('.paas-cart-container .dht-cart-footer2').outerHeight() || 49;

        // 兼容 sfa 订单产品表格底部的汇总区域显示时, 高度不对问题
        // let sfaCountH = $('.paas-cart-container .crm-list-fieldcount').outerHeight() || 0;
        // if ( sfaCountH > 0) {
        //     sfaCountH = sfaCountH + 8;
        // }
        let h = wrapH - navH - footerH;

        $('.paas-cart-container .table-wrapper').height(h);
    };
    /**
     * 工具栏的购物车删除操作时，这里同步修改
     */
    onMincartDelete(cartIds) {
        // 更新工具栏购物车飘数
        cartInstance.updateCartNumber();

        let factory = this.util.pluginServiceFactory({
            pluginApiName: 'dht_shoppingcart'
        });
        let mdData = factory.dataGetter.getDetail(this.mdApiname);
        let rowIds = [];

        cartIds.forEach(cartId => {
            let product = mdData.find(item => item.cartId === cartId);
            product && rowIds.push(product.rowId);
        });

        rowIds.forEach(rowId => {
            factory.dataUpdater.del(this.mdApiname, rowId);
        })
        factory.end(); // 先更新表格数据

        if (this.tableExp) this.tableExp.isTableUpdate = true;
    }

    async _formRenderAfter(context, hookParam) {
      const time = new Date().getTime();
      const uicost = (time - this.pluginParam.params.cartStartTime) / 1000;
    //   console.log(`cart initTime: ${uicost}s`);
      logService.log('cart', 'costTime','ct', {
        uicost,
      });

      if ($dht.cartLoading) {
        $dht.cartLoading.close();
        $dht.cartLoading = null;
      }

      // 兼容赠品触发汇总计算场景, 需等数据更新完成后再更新底部
    //   if(this.tableExp) {
    //     this.updateFooterOnAdd(hookParam);
    //   }

      // 洁柔类 自定义表格场景, 手动触发初始化
    //   console.log('shoppingcartobj  _formRenderAfter');
      if(!this.tableExp) {
        this.setDefaultCheckState(null);
        // this.updateTableFooterOnInit(hookParam);
      }
    }

   /**
   * 切换合作伙伴，不提示清空列表
   * @param context
   * @param hookParam
   * @returns {{isNoTriggerChange: boolean}}
   */
    priceServiceTodoAfterFieldChangeBefore(context, hookParam) {
      return { isNoTriggerChange: true };
    }

   /**
   * 购物车 禁用手动计算促销 功能
   * @param context
   * @param hookParam
   * @returns {{disableManualMatch: boolean}}
   */
    pricePolicyRenderBefore(context, hookParam) {
      return { disableManualMatch: true };
    }

    apply() {
        return [{
            event: 'md.render.before',
            functional: this._mdRenderBefore.bind(this)
        }, {
            event: 'md.render.after',
            functional: this._mdRenderAfter.bind(this)
        }, {
            event: 'md.edit.before',
            functional: this._mdEditBefore.bind(this)
        }, {
            event: 'md.edit.after',
            functional: this._mdEditAfter.bind(this)
        }, {
            event: 'md.del.before',
            functional: this._mdDelBefore.bind(this)
        }, {
            event: 'md.del.after',
            functional: this._mdDelAfter.bind(this)
        }, {
            event: 'md.custable.renderComplete',
            functional: this._mdCustTableRenderComplete.bind(this)
        }, {
            event: 'pricePolicy.executeAndUpdate.end',
            functional: this._pricePolicyUpdateAfter.bind(this)
        }, {
            event: 'bom.twiceConfig.after',
            functional: this._bomUpdateAfter.bind(this)
        }, {
          event: 'form.render.after',
          functional: this._formRenderAfter.bind(this)
        }, {
          event: 'price-service.todoAfterFieldChange.before',
          functional: this.priceServiceTodoAfterFieldChangeBefore.bind(this)
        }, {
          event: 'pricePolicy.render.before',
          functional: this.pricePolicyRenderBefore.bind(this)
        }];
    }

    destroy() {
        $dht.store.off('store:mincart.list.delete', this.$data._onMincartDelete);
        // 销毁vue组件
        PPM.each(this.$comp, comp => {
            if (comp && _.isFunction(comp.$destroy)) {
                comp.$destroy();
            }
        });
        this.tableExp && this.tableExp.destroy && this.tableExp.destroy();
        this.$comp = this.$data = this.util = this.tableExp = null;
        // 取消窗口改变监听
        $(window).off('resize', this.onCartWindowResize);
    }
}

