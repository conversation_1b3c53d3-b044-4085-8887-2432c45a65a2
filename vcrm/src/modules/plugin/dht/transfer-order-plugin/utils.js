/**
 * 缓存处理的通用方法
 */

export const isDhtApp = () => {
    return window.$dht && window.$dht.getService('distribution');
};

export const isChannelDistributionApp = () => {
    return window.distribution && window.distribution.userInfo;
};

/**
 * 获取 URL 中的参数值
 * @param {string} url 完整的url地址
 * @param {string} paramName 参数名
 * @returns {string}
 */
export const getUrlParam = (url, paramName) => {
    const urlObj = new URL(url);
    const params = new URLSearchParams(urlObj.search);
    const hashParams = new URLSearchParams(urlObj.hash.split('?')[1] || '');

    return params.get(paramName) || hashParams.get(paramName) || '';
};

/**
 * 获取渠道分销应用的 appid
 */
export const getChannelDistributionAppId = () => {
    const origin = window.location.origin;

    // 判断是否为测试环境
    let devEnvs = ['ceshi112', 'localhost'];
    const fsaid = devEnvs.some(env => origin.indexOf(env) > -1) ? 'FSAID_114910bc' : 'FSAID_11491173';

    return fsaid;
};

/**
 * 缓存处理的 mixin 工厂函数
 * @param {string} paramName URL 参数名
 */
export const createCacheMixin = (paramName) => {
    const mixin = {
        constructor(...rest) {
            this.paramId = getUrlParam(location.href, paramName);
            if (this.paramId) {
                // 清除以 paramId 为 key 的缓存
                sessionStorage.removeItem(this.paramId);
            }
        },

        /**
         * 检查并设置缓存
         * @returns {boolean} true 表示有缓存，false 表示无缓存
         */
        checkAndSetCache() {
            if (!this.paramId) return false;

            const cache = sessionStorage.getItem(this.paramId);
            if (cache) {
                return true;
            }
            // 设置缓存
            sessionStorage.setItem(this.paramId, '1');
            return false;
        }
    };

    return {
        constructor(...rest) {
            mixin.constructor.call(this, ...rest);
            // 复制 mixin 的方法到实例上
            Object.assign(this, {
                checkAndSetCache: mixin.checkAndSetCache
            });
        }
    };
};

export function isOrgSupply(supplyType) {
    const SUPPLY_TYPE = {
        ORG: '1',
        CHANNEL: '2',
    };

    return supplyType === SUPPLY_TYPE.ORG;
}
