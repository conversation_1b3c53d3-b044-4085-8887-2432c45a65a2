
export default class Plugin {
  apply() {
    return [
      {
        event: "dht.customerAccount.uiConfig",
        functional: this.customUIConfig.bind(this)
      }
    ];
  }
  accountListFilter(iparam, plugin) {
    console.log('call accountListFilter',iparam, plugin);
    let accountList = iparam.accountList;
    const param = iparam.param;
    const { blurField, masterUpdate } = param.collectChange();
    // const changeDataKeys = Object.keys(masterUpdate || {}),
    //     masterData = param.dataGetter.getMasterData();

    // if (changeDataKeys && changeDataKeys.length && changeDataKeys.includes("field_bizhong__c")) {
    //    console.log('币种点击切换');
    // }
    if(blurField && blurField != 'field_bizhong__c') {
      // console.log('非币种点击切换, 不修改');
      return accountList;
    }
    
    // 切换币种时, 更新账户
    const currency = masterData['field_bizhong__c'] || '';
    accountList = accountList.filter(it=>{     
      if(currency == 'option_rmb_amount__c') {
         // 仅显示返利账户
        return it.trade_amount_fieldapiname == 'amount_rebate__c';
      } else {
        // 仅显示现金账户
        return it.trade_amount_fieldapiname == 'field_LczG3__c';
      }      
    }) 
    console.log('filter accountList return', accountList);
    return accountList;
  }
  customUIConfig(plugin, param) {
    return {
      hideCurrencyDesc: true,   // 是否隐藏 账户余额
      blurFieldsForFilter: ['field_bizhong__c'], // 监听的主对象字段,触发过滤函数
      customAccountListFilter: this.accountListFilter.bind(this) // object/account_auth/service/get_outcome_auth_accounts 接口返回后会调用此函数过滤客户账户 
    }
  }
}
