const DHT_MANAGE_APP_ID = 'FSAID_PaaS_6841442477632';
const DHT_APP_ID = 'FSAID_11490c84';
const DISTRIBUTION_APP_IDS = ['FSAID_114910bc', 'FSAID_11491173'];
export const DISTRIBUTION = 'distribution__c';
export const MERCHANT = 'merchant__c';
export const PARTNER_OBJ = 'PartnerObj';
export const FORM_TYPE = {
    ADD: 'add',
    EDIT: 'edit',
};
export const FILTER_TYPES = {
    RECORD_TYPE: 'record_type',
    PARTNER_ID: 'partner_id',
};
const MD_LIST = [
    // 可售范围
    'AvailableAccountObj',
    'AvailableProductObj',
    'AvailablePriceBookObj',
    // 价目表
    'PriceBookAccountObj',
    'PriceBookProductObj',
    'SalesOrderProductObj',
    'DeliveryNoteProductObj',
    'ReturnedGoodsInvoiceProductObj',
    'OrderPaymentObj',
    'InvoiceApplicationLinesObj',
    'GoodsReceivedNoteProductObj',
    'OutboundDeliveryNoteProductObj',
    'InventoryUnfreezingDetailObj',
    'InventoryFreezingDetailObj',
    'RequisitionNoteProductObj',
    'StockCheckNoteProductObj',
];

export function isMd(objApiName) {
    return MD_LIST.includes(objApiName);
}

export function isDhtManageApp() {
    const hash = window.location.hash;

    return hash.includes(DHT_MANAGE_APP_ID);
}

export function isDhtApp() {
    const appId = CRM.util.getPageCurAppId();

    return appId === DHT_APP_ID;
}


export function isChannelDistributionApp() {
    const appId = CRM.util.getPageCurAppId();

    return DISTRIBUTION_APP_IDS.includes(appId);
}

export function formatQuery(rq, list) {
    if (!list) {
        return rq;
    }

    try {
        const search_query_info = JSON.parse(rq.search_query_info);
        let filters = search_query_info.filters;
        if (Array.isArray(filters)) {
            filters.push(...list);
        } else {
            filters = list;
        }

        search_query_info.filters = filters;

        rq.search_query_info = JSON.stringify(search_query_info);
    } catch (error) {
        console.error(error);
    }

    return rq;
}

export function getDistributionRecordType(objApiName) {
    return objApiName === PARTNER_OBJ ? MERCHANT : DISTRIBUTION;
}

export function getDistributionFilter(rq) {
    const objApiName = rq.associated_object_describe_api_name;
    const masterRecordType = rq.master_data?.record_type || rq.object_data?.record_type;
    const recordType = getDistributionRecordType(objApiName);
    const operator = masterRecordType === DISTRIBUTION ? 'EQ' : 'N';
    return {
        field_name: 'record_type',
        field_values: [recordType],
        operator,
    };
}

export function formatQueryByRecordType(rq, objApiName) {
    const filter = getDistributionFilter(rq);

    // 从对象按主对象的字段过滤
    if (isMd(objApiName)) {
        filter.is_master_field = true;
    }

    return formatQuery(rq, [filter]);
}

export function formatQueryById(rq, ids = []) {
    const filters = [];

    if (ids.length) {
        filters.push({
            field_name: '_id',
            field_values: ids,
            operator: 'IN',
        });
    }

    return formatQuery(rq, filters);
}

export function formatQueryByPartner(rq, partnerId) {
    const filter = getDistributionFilter(rq);
    const filters = [filter];

    if (partnerId) {
        filters.push({
            field_name: 'partner_id',
            field_values: [partnerId],
            operator: 'EQ',
        });
    }

    return formatQuery(rq, filters);
}

export function formatQueryByPartnerForList(rq, partnerId) {
    const recordType = getDistributionRecordType(rq.object_describe_api_name);
    const filters = [{
        field_name: 'record_type',
        field_values: [recordType],
        operator: 'EQ',
    }];

    if (partnerId) {
        filters.push({
            field_name: 'partner_id',
            field_values: [partnerId],
            operator: 'EQ',
        });
    }

    if (isMd(rq.object_describe_api_name)) {
        filters.forEach(item => {
            item.is_master_field = true;
        });
    }

    return formatQuery(rq, filters);
}

export function formatQueryByAccount(rq, accountId) {
    const filter = getDistributionFilter(rq);
    const filters = [filter];

    if (accountId) {
        filters.push({
            field_name: 'account_id',
            field_values: [accountId],
            operator: 'EQ',
        });
    }

    return formatQuery(rq, filters);
}

export function formatQueryByAccountForList(rq, accountId, isDistribution) {
    const operator = isDistribution ? 'EQ' : 'N';
    const filters = [{
        field_name: 'record_type',
        field_values: [DISTRIBUTION],
        operator,
    }];
    // 权限改造之后不需要传客户Id
    // if (accountId) {
    //     filters.push({
    //         field_name: 'account_id',
    //         field_values: [accountId],
    //         operator: 'EQ',
    //     });
    // }

    // 从对象按主对象的字段过滤
    if (isMd(rq.object_describe_api_name)) {
        filters.forEach(item => {
            item.is_master_field = true;
        });
    }

    return formatQuery(rq, filters);
}

export function isOrgSupply(supplyType) {
    const SUPPLY_TYPE = {
      ORG: '1',
      CHANNEL: '2',
    };

    return supplyType === SUPPLY_TYPE.ORG;
}

export function formatQueryByRecordTypeForList(rq, isDistribution) {
    const operator = isDistribution ? 'EQ' : 'N';
    const filters = [{
        field_name: 'record_type',
        field_values: [DISTRIBUTION],
        operator,
    }];

    return formatQuery(rq, filters);
}

export function formatQueryForProduct(rq, userInfo = {}) {
    rq.object_data = rq.object_data || {};
    Object.assign(rq.object_data, {
        partner_id: userInfo.partner_id,
    });

    return rq;
}

/**
 * 为订货管理【渠道客户】添加特殊参数
 * @param {Record<string, any>} rq 列表页请求参数
 * @returns rq
 */
export function formatExtraParams(rq, recordType, isDhtManage, isDhtChannelMenu ) {
    rq.extra_params = rq.extra_params || {};

    const multi_level_order = {
        dht_manage: isDhtManage,
        record_type: recordType,
    };
    debugger
    if (isDhtChannelMenu) {
        multi_level_order.is_channel_menu = isDhtChannelMenu;
    }
    rq.extra_params.multi_level_order = multi_level_order;
    return rq;
}
