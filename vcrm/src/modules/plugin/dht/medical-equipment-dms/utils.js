const DISTRIBUTION_APP_IDS = ['FSAID_114910bc', 'FSAID_11491173'];

export function isDhtApp() {
    const appId = CRM.util.getPageCurAppId();
    // 订货通和配件商城
    return ['FSAID_11490c84', 'FSAID_11491427', 'FSAID_11491512'].includes(appId);
}


export function isChannelDistributionApp() {
    const appId = CRM.util.getPageCurAppId();

    return DISTRIBUTION_APP_IDS.includes(appId);
}

export function isOrgSupply(supplyType) {
    const SUPPLY_TYPE = {
      ORG: '1',
      CHANNEL: '2',
    };

    return supplyType === SUPPLY_TYPE.ORG;
}
