import Base from 'plugin_base'
import PPM from 'plugin_public_methods'
import {Business, ScanCode} from "@stock-plugin/web/dist/receivematerialbillobj/index";
import { DevicePicture, flattenTreeNodes } from './devicePicture';
import store from './store';

const BASE_CASES_DEVICE = 'base_cases_device';
export default class SpluginReceiveMaterialBillObj extends Base {

    constructor(pluginService, pluginParam) {
        super(...arguments);
        this.pluginService = pluginService
        this.pluginParam = pluginParam
        this.deviceMap = {}
        this.bomCoreInfo = [] // 工单对应的产品组合对象数据
        this.business = new Business(pluginService)
        this.business.scanCode = new ScanCode(
            "spare",
            this.business.detailApiName,
            this.business.masterApiName,
            this.pluginService
        );
    }

    options() {
        return {
            defMasterFields: {
                form_receive_type: "receive_type", // 领料方式
                form_warehouse_id: "warehouse_id", // 仓库id
                form_cases_id: "cases_id",         // 工单id
                form_device_id: "device_id"        // 设备id
            },
            defMdFields: {
                sn_id: "sn_id",
                new_product_id: "new_product_id",
                stock_id: "stock_id"
            }
        };
    }

    async handleFormRenderBefore(plugin, hookParam) {
        const { BaseComponents, dataUpdater, formType, triggerCalAndUIEvent } = hookParam;
        const object_reference = BaseComponents.object_reference;

        try {
            if (formType !== 'edit') {
                // 调用接口获取个人库的默认值，有值则回填
                const myEmployeeInfo = await this.getEmployeeWarehouse({ querySelf: true }) || {};
                if (myEmployeeInfo._id) {
                    dataUpdater.updateMaster({
                        employee_warehouse_id: myEmployeeInfo._id,
                        employee_warehouse_id__r: myEmployeeInfo.name,
                    });

                    await triggerCalAndUIEvent({
                        objApiName: 'ReceiveMaterialBillObj',
                        changeFields: ['employee_warehouse_id']
                    });
                }
            }
        } catch (err) {
            console.error(err);
        }

        const components = {};
        components['employee_warehouse_id'] = object_reference.extend({
            getFilters() {
                // 过滤掉停用的员工个人库
                return [{
                    field_name: 'warehouse_status',
                    field_values: ['disable'],
                    operator: 'N',
                }];
            },
        });
        return { components };
    }

    getEmployeeWarehouse(options = {}) {
        const params = {
            querySelf: options.querySelf,
        };
        return new Promise((resolve, reject) => {
            CRM.util.FHHApi(
                {
                    url: '/EM1HNCRM/API/v1/object/accessory_exchange/service/get_employee_warehouse',
                    data: params,
                    success: res => {
                        if (res.Result.StatusCode == 0) {
                            const employeeWarehouse = res.Value.employeeWarehouse || {}; // 里面携带了 name、_id 字段
                            resolve(employeeWarehouse);
                        } else {
                            FS.crmUtil.alert(res.Result.FailureMessage);
                            reject(res.Result.FailureMessage);
                        }
                    },
                },
                {
                    errorAlertModel: 1,
                }
            );
        });
    }

    _mdRenderBefore(plugin, param) {
        param.dataUpdater.setHidden({
            status: true,
            fieldName: ['receive_type']
        })

        let _fn = (trData) => {
            if (trData.product_batch_sn__v == 3) {
                return {
                    del: ['copyRowHandle']
                }
            }
        };

        let res = {
            operateBtns: [_fn], // 处理行按钮展示；
        };

        return Promise.resolve().then(() => {
            return this.business.scanCode.getMdRenderBeforeOpts();
        }).then((obj) =>
            this.getAccessoryConfig().then((config) => {
                store.isBomCoreExplosionDiagramOpen = config.bomCoreExplosionDiagramSwitch == 1; // 是否开启爆炸图

                const masterData = param.dataGetter.getMasterData()

                let addBtn = [
                    {
                        label: config.warehouseModel == 3 ? $t("从备件库存添加") : $t("从产品批量添加"),
                        callBack: this.batchAddDataHandle.bind(this, plugin, param),
                    },
                    ...obj.buttons.add,
                ];

                if (config.sparePartsReceiveConfigSwitch == 1) {
                    addBtn.push({
                        action: "handleBatchAddByDeviceBom",
                        label: $t("从设备BOM添加"),
                        callBack: (ignore, params) => this.handleBatchAddByDeviceBom(plugin, params)
                    });

                    // 编辑时，进入页面要拉取设备数据
                    if (masterData.cases_id) {
                        this.getDevice(masterData.cases_id)
                    }
                }

                if (config.sparePartsReceiveForBomCoreConfigSwitch == 1) {
                    addBtn.push({
                        action: "handleBatchAddByServiceBom",
                        label: $t("从服务BOM添加"),
                        callBack: (ignore, params) => this.handleBatchAddByServiceBom(plugin, params)
                    });

                    // 编辑时，进入页面要拉取设备数据
                    if (masterData.cases_id) {
                        this.getDevice(masterData.cases_id)
                    }
                }

                let buttons = {
                    retain: [],
                    add: addBtn,
                };

                try {
                    // 判断是否开启新建编辑页布局，开启了后 buttons 只增加业务需要的这些，不再删除其他的
                    if (param.dataGetter.getDescribeLayout().layout.layout_type === 'edit') {
                        // @ts-ignore
                        buttons = {
                            add: addBtn,
                        };
                    }
                } catch (err) {
                    console.error(err);
                }

                res.buttons = buttons;
                return res;
            })
        )
    }

    getPickBomDataCom() {
        return new Promise((resolve) => {
            seajs.use("vcrm/sdk", (sdk) => {
                sdk
                    .getComponent("pickDeviceBom")
                    .then((pickDeviceBom) => resolve(pickDeviceBom.default));
            });
        });
    }

    // 弹出产品组合（服务BOM）列表页
    showServiceBomListPage(plugin, params, serviceBomIdList) {
        const me = this;
        const recordType = params.recordType
        const factory = plugin.api.pluginServiceFactory({
            pluginApiName: "handleBatchAddByServiceBomPlugin"
        });
        const masterData = factory.dataGetter.getMasterData();

            this.getPickBomDataCom().then(PickBomDataCom => {
                let _pickBomDataCom = new (Vue.extend(PickBomDataCom))({
                        propsData: {
                            apiName: 'ServiceBomObj',
                            masterData,
                                devices: serviceBomIdList,
                                 title: $t("选择服务BOM")
                }
            });
            _pickBomDataCom.on("confirm", (res) => {
                    const selectedData = res.subCheckedData;
                    const productIds = [];
                    selectedData.forEach(d => {
                        productIds.push(d.product_id);
                    });
                    CRM.api.pick_data({
                        apiName: "StockObj",
                        single: false,
                        hideAdd: true,
                        // 过滤条件为：备件业务类型 = 备件库存，可用库存 > 0，产品id 属性 productIds
                        filters: [
                            {
                                field_name: "record_type",
                                field_values: ["spare_part__c"],
                                operator: "EQ"
                            },
                            {
                                field_name: "available_stock",
                                field_values: ["0"],
                                operator: "GT"
                            },
                            {
                                field_name: "product_id",
                                field_values: productIds.length ? productIds : '',
                                operator: productIds.length ? "IN" : "IS"
                            }
                        ],
                        methods: {
                            select: (data) => {

                                const selectedData = data.selected;
                                const addDatas = [];
                                selectedData.forEach((a) => {
                                    // 设置回填数据的字段的
                                    let addItem = {
                                        ...a,
                                        stock_id: a._id,
                                        stock_id__r: a.name,
                                        apply_amount: a.batch_sn__v === "3" ? '1' : null,
                                        life_status: "normal",
                                        lock_rule: "default_lock_rule",
                                        lock_status: "0",
                                        name: "{yyyy}-{mm}-{dd}00001",
                                        object_describe_api_name: "ReceiveMaterialBillProductObj",
                                        record_type: recordType || "default__c",
                                        actual_unit: a.auxiliary_unit ||
                                            a.actual_unit ||
                                            a.unit__v ||
                                            a.unit,
                                        batch_sn: !isNaN(a.batch_sn__v) ? a.batch_sn__v : a.batch_sn,
                                        batch_sn__v: a.batch_sn__v,
                                        product_batch_sn__v: a.batch_sn__v,
                                        new_product_id: a.product_id || a.product_id__id,
                                        new_product_id__r: a.product_id__r
                                    }
                                    delete addItem._id;
                                    addDatas.push(addItem);
                                });
                                const objectDatas = factory.dataUpdater.add(addDatas);
                                const newDataIndexs = objectDatas.map((i) => i.rowId);

                                factory.dataUpdater.setReadOnly({
                                    dataIndex: newDataIndexs,
                                    fieldName: "new_product_id",
                                    status: true
                                });
                                factory.triggerCalAndUIEvent({
                                    newDataIndexs,
                                    objApiName: "ReceiveMaterialBillProductObj"
                                }).then(() => {
                                    factory.end();
                                }).then(() => {
                                    factory.run("batch-sn-handle", objectDatas);
                                });
                            }
                        }
                    })
                });
            _pickBomDataCom.$mount();
        });
    }

    handleBatchAddAfterForBomCoreExplosionDiagram(plugin, param) {
        const { lookupDatas, recordType } = param;
        const leafNodeList = flattenTreeNodes(lookupDatas);
        const factory = plugin.api.pluginServiceFactory({
            pluginApiName: "handleBatchAddByServiceBomPlugin"
        });

        const productIds = [];
        leafNodeList.forEach(d => {
            productIds.push(d.product_id);
        });

        CRM.api.pick_data({
            apiName: "StockObj",
            single: false,
            hideAdd: true,
            // 过滤条件为：备件业务类型 = 备件库存，可用库存 > 0，产品id 属性 productIds
            filters: [
                {
                    field_name: "record_type",
                    field_values: ["spare_part__c"],
                    operator: "EQ"
                },
                {
                    field_name: "available_stock",
                    field_values: ["0"],
                    operator: "GT"
                },
                {
                    field_name: "product_id",
                    field_values: productIds.length ? productIds : '',
                    operator: productIds.length ? "IN" : "IS"
                }
            ],
            methods: {
                select: (data) => {

                    const selectedData = data.selected;
                    const addDatas = [];
                    selectedData.forEach((a) => {
                        // 设置回填数据的字段的
                        let addItem = {
                            ...a,
                            stock_id: a._id,
                            stock_id__r: a.name,
                            apply_amount: a.batch_sn__v === "3" ? '1' : null,
                            life_status: "normal",
                            lock_rule: "default_lock_rule",
                            lock_status: "0",
                            name: "{yyyy}-{mm}-{dd}00001",
                            object_describe_api_name: "ReceiveMaterialBillProductObj",
                            record_type: recordType || "default__c",
                            actual_unit: a.auxiliary_unit ||
                                a.actual_unit ||
                                a.unit__v ||
                                a.unit,
                            batch_sn: !isNaN(a.batch_sn__v) ? a.batch_sn__v : a.batch_sn,
                            batch_sn__v: a.batch_sn__v,
                            product_batch_sn__v: a.batch_sn__v,
                            new_product_id: a.product_id || a.product_id__id,
                            new_product_id__r: a.product_id__r
                        }
                        delete addItem._id;
                        addDatas.push(addItem);
                    });

                    const objectDatas = factory.dataUpdater.add(addDatas);
                    const newDataIndexs = objectDatas.map((i) => i.rowId);

                    factory.dataUpdater.setReadOnly({
                        dataIndex: newDataIndexs,
                        fieldName: "new_product_id",
                        status: true
                    });
                    factory.triggerCalAndUIEvent({
                        newDataIndexs,
                        objApiName: "ReceiveMaterialBillProductObj"
                    }).then(() => {
                        factory.end();
                    }).then(() => {
                        factory.run("batch-sn-handle", objectDatas);
                    });
                }
            }
        })
    }

    // 根据设备id查找服务BOM
    // 如果选择了工单，则需要根据是否有关联设备来判断调用接口的参数
    // 如果关联了设备：则只传设备 id 列表，不传工单 id；
    // 如果没关联：则不传设备 id 列表，只传工单 id。
    getServiceBomIds(deviceIdList, caseId) {
        return new Promise((resolve) => {
            CRM.util.FHHApi(
                {
                    url: "/EM1HNCRM/API/v1/object/bom_core/service/get_bom_core_data",
                    data: {
                        device_id_list: deviceIdList || [],
                        case_id: caseId || ''
                    },
                    success: function (res) {
                        if (res && res.Value && res.Value.dataIdList) {
                            resolve(res.Value.dataIdList);
                        } else {
                          resolve([]);
                        }
                    }
                },
                {
                    errorAlertModel: 1
                }
            );
        });
    }

    handleBatchAddByServiceBom(plugin, params) {
        const me = this;

        const factory = plugin.api.pluginServiceFactory({
            pluginApiName: "handleBatchAddByServiceBomPlugin"
        });

        const masterData = factory.dataGetter.getMasterData();
        const caseId = masterData.case_id || masterData.cases_id;
        if (!caseId) {
            return FS.crmUtil.alert($t("请先选择工单"));
        } else {
            // 如果选择了工单，则需要根据是否有关联设备来判断调用接口的参数
            // 如果关联了设备：则只传设备 id 列表，不传工单 id；
            // 如果没关联：则不传设备 id 列表，只传工单 id。
            const deviceList = this.deviceMap[caseId] || [];
            plugin.api.showLoading();
            me.getServiceBomIds(deviceList, deviceList.length ? '' : caseId).then(res => {
                const serviceBomIds = res;
                if (serviceBomIds.length) {
                    // 是否开启产品爆炸图
                    if (store.isBomCoreExplosionDiagramOpen) {
                        CRM.util.fetchObjList('BomCoreObj', {
                            search_query_info: JSON.stringify({
                                limit: 2000,
                                filters: [
                                    {
                                        field_name: "_id",
                                        field_values: serviceBomIds,
                                        operator: "IN"
                                    }
                                ]
                            })
                        }).then(res => {
                            console.log('查到的产品组合数据', res);
                            if (res && res.dataList) {
                                me.bomCoreInfo = res.dataList;
                                factory.batchPickData({
                                    objApiName: params.objectApiName,
                                    recordType: params.recordType,
                                    fieldName: 'new_product_id',
                                });
                            }
                        });
                    }else{
                        me.showServiceBomListPage.call(me, plugin, params, serviceBomIds);
                    }
                } else {
                    FS.crmUtil.alert($t("未能匹配到对应的服务BOM"));
                }
            }).finally(() => {
                plugin.api.hideLoading();
            });
        }
    }

    handleBatchAddByDeviceBom(plugin, params) {
    const factory = plugin.api.pluginServiceFactory({
        pluginApiName: "handleBatchAddByDeviceBomPlugin"
    });
    const masterData = factory.dataGetter.getMasterData();
    const recordType = params.recordType
    if (!masterData.case_id && !masterData.cases_id) {
        return FS.crmUtil.alert($t("请先选择工单"));
    }

        const deviceList = this.deviceMap[masterData.case_id || masterData.cases_id] || [];

        if (!deviceList.length && !masterData.device_id) {
            return FS.crmUtil.alert($t("ava.objform.eservice.workorder.this.workorder.not.associated.equipment")); // 该工单未关联任何设备
        }

        const devices = deviceList;

        this.getPickBomDataCom().then(PickBomDataCom => {
            let _pickBomDataCom = new (Vue.extend(PickBomDataCom))({
                propsData: {
                    devices: masterData.device_id ? [masterData.device_id] : devices
                }
            });
            _pickBomDataCom.on("confirm", (data) => {
                this.getProductIds(data).then((productIds) => {
                    // if 普通模式 else 高级模式
                    if (this.accessoryConfig.warehouseModel == 2) {
                        this.getProductList(productIds).then((list) => {
                            if (list.length) {
                                const addDatas = [];
                                list.forEach((a) => {
                                    let addItem = {
                                        ...a,
                                        life_status: "normal",
                                        lock_rule: "default_lock_rule",
                                        lock_status: "0",
                                        name: "{yyyy}-{mm}-{dd}00001",
                                        object_describe_api_name: "ReceiveMaterialBillProductObj",
                                        record_type: recordType || "default__c",
                                        actual_unit: a.auxiliary_unit ||
                                            a.actual_unit ||
                                            a.unit__v ||
                                            a.unit,
                                        batch_sn: !isNaN(a.batch_sn__v) ? a.batch_sn__v : a.batch_sn,
                                        batch_sn__v: a.batch_sn__v,
                                        product_batch_sn__v: a.batch_sn__v,
                                        new_product_id: a._id,
                                        new_product_id__r: a.name
                                    };
                                    delete addItem._id;
                                    addDatas.push(addItem);
                                });
                                const objectDatas = factory.dataUpdater.add(addDatas);
                                const newDataIndexs = objectDatas.map((i) => i.rowId);

                                factory.dataUpdater.setReadOnly({
                                    dataIndex: newDataIndexs,
                                    fieldName: "new_product_id",
                                    status: true
                                });
                                factory
                                    .triggerCalAndUIEvent({
                                        newDataIndexs,
                                        objApiName: "ReceiveMaterialBillProductObj"
                                    })
                                    .then(() => {
                                        factory.end();
                                    })
                                    .then(() => {
                                        factory.run("batch-sn-handle", objectDatas);
                                    });
                            }
                        });

                    } else if (this.accessoryConfig.warehouseModel == 3) {
                        CRM.api.pick_data({
                            apiName: "StockObj",
                            single: false,
                            filters: [{
                                field_name: "record_type",
                                field_values: ["spare_part__c"],
                                operator: "EQ"
                            },
                                {
                                    field_name: "init_available_stock",
                                    field_values: ["0"],
                                    operator: "GT"
                                },
                                {
                                    field_name: "product_id",
                                    field_values: productIds,
                                    operator: "IN"
                                }
                            ],
                            methods: {
                                select: (data) => {
                                    const selectedData = data.selected;
                                    const addDatas = [];
                                    selectedData.forEach((a) => {
                                        let addItem = {
                                            ...a,
                                            stock_id: a._id,
                                            stock_id__r: a.name,
                                            apply_amount: a.batch_sn__v === "3" ? '1' : null,
                                            life_status: "normal",
                                            lock_rule: "default_lock_rule",
                                            lock_status: "0",
                                            name: "{yyyy}-{mm}-{dd}00001",
                                            object_describe_api_name: "ReceiveMaterialBillProductObj",
                                            record_type: recordType || "default__c",
                                            actual_unit: a.auxiliary_unit ||
                                                a.actual_unit ||
                                                a.unit__v ||
                                                a.unit,
                                            batch_sn: !isNaN(a.batch_sn__v) ? a.batch_sn__v : a.batch_sn,
                                            batch_sn__v: a.batch_sn__v,
                                            product_batch_sn__v: a.batch_sn__v,
                                            new_product_id: a.product_id || a.product_id__id,
                                            new_product_id__r: a.product_id__r
                                        }
                                        delete addItem._id;
                                        addDatas.push(addItem);
                                    });
                                    const objectDatas = factory.dataUpdater.add(addDatas);
                                    const newDataIndexs = objectDatas.map((i) => i.rowId);

                                    factory.dataUpdater.setReadOnly({
                                        dataIndex: newDataIndexs,
                                        fieldName: "new_product_id",
                                        status: true
                                    });
                                    factory
                                        .triggerCalAndUIEvent({
                                            newDataIndexs,
                                            objApiName: "ReceiveMaterialBillProductObj"
                                        })
                                        .then(() => {
                                            factory.end();
                                        })
                                        .then(() => {
                                            factory.run("batch-sn-handle", objectDatas);
                                        });
                                }
                            }
                        });
                    }
                })
            });
            _pickBomDataCom.$mount();
        })
    }

    _formFieldWatch(plugin, param) {
        const masterData = param.dataGetter.getMasterData();
        if ((this.accessoryConfig.sparePartsReceiveConfigSwitch == 1 ||
            this.accessoryConfig.sparePartsReceiveForBomCoreConfigSwitch == 1
        ) && (param.changeData.case_id || param.changeData.cases_id)
        ) {
            // 选择工单，获取工单关联设备/判断工单是否关联了设备
            return this.getCaseDevicesArrByCasesId(masterData.case_id || masterData.cases_id).then(
                (deviceList) => {
                    if (deviceList.length) param.dataUpdater.delDetail('ReceiveMaterialBillProductObj')
                    return this.deviceMap[masterData.case_id || masterData.cases_id] = deviceList
                }
            );
        }
    }

    getProductList(productIds) {
        const me = this;
        me.pluginService.api.showLoading();
        return new Promise((resolve, reject) => {
            CRM.util.FHHApi(
                {
                    url: "/EM1HNCRM/API/v1/object/ProductObj/controller/RelatedList",
                    data: {
                        associated_object_describe_api_name: "ProductObj",
                        associated_object_field_related_list_name:
                            "target_related_list_spad_product_id",
                        search_query_info: JSON.stringify({
                            offset: 0,
                            filters: [
                                {
                                    field_name: "_id",
                                    field_values: productIds,
                                    operator: "IN"
                                }
                            ],
                            orders: [
                                {
                                    fieldName: "last_modified_time",
                                    isAsc: false
                                }
                            ],
                            wheres: []
                        })
                    },
                    success: function (res) {
                        resolve((res && res.Value && res.Value.dataList) || []);
                    },
                    complete: function () {
                        me.pluginService.api.hideLoading();
                    }
                },
                {errorAlertModel: 1}
            );
        });
    }

    getProductIds(data) {
        const me = this;
        return new Promise((resolve, reject) => {
            const deviceIds = []
            const productIds = []
            data.mainCheckedData && data.mainCheckedData.forEach((obj) => productIds.push(obj.device_product_id));
            data.subCheckedData && data.subCheckedData.forEach((obj) => {
                if (obj.device_part_id) return productIds.push(obj.device_part_id)
                deviceIds.push(obj.accessory_material_id)
            });
            if (deviceIds.length) {
                me.pluginService.api.showLoading();
                CRM.util.FHHApi({
                    url: "/EM1HNCRM/API/v1/object/DeviceObj/controller/List",
                    data: {
                        associated_object_describe_api_name: "DeviceObj",
                        associated_object_field_related_list_name: "target_related_list_cases_id__c",
                        extractExtendInfo: true,
                        ignore_scene_record_type: false,
                        include_describe: false,
                        include_layout: false,
                        serializeEmpty: false,
                        search_query_info: JSON.stringify({
                            limit: 2000,
                            offset: 0,
                            filters: [{
                                field_name: "_id",
                                field_values: deviceIds,
                                operator: "IN",
                            },]
                        }),
                    },
                    success: function (res) {
                        if (res && res.Value && res.Value.dataList) {
                            res.Value.dataList.forEach(item => productIds.push(item.device_product_id))
                        }
                        resolve(productIds)
                    },
                    complete: function () {
                        me.pluginService.api.hideLoading();
                    }
                }, {
                    errorAlertModel: 1,
                });
            } else {
                resolve(productIds)
            }
        })
    }

    // 获取工单关联设备
    getCaseDevicesArrByCasesId(casesId) {
        return new Promise((resolve) => {
            CRM.util.FHHApi(
                {
                    url: "/EM1HNCRM/API/v1/object/eservice_device/service/query_cases_related_device_id_list",
                    data: {
                        casesId: casesId
                    },
                    success: function (res) {
                        const deviceIdList = res && res.Value && res.Value.deviceIdList;
                        if (
                            res.Value &&
                            res.Value.isSuccess &&
                            deviceIdList &&
                            deviceIdList.length
                        ) {
                            resolve(deviceIdList);
                        } else {
                            resolve([]);
                        }
                    }
                },
                {
                    errorAlertModel: 1
                }
            );
        });
    }

    getDevice(caseId) {
        const me = this
        me.pluginService.api.showLoading();
        const factory = me.pluginService.api.pluginServiceFactory({
            pluginApiName: 'splugin-getDevice'
        });
        const FIELD_APINAME_DEVICE_ID = ESERVICE.getCasesFieldApiName('device_id');
        const FIELD_APINAME_DEVICE_ID__R = ESERVICE.getCasesFieldApiName('device_id__r');
        CRM.util.FHHApi({
            url: "/EM1HNCRM/API/v1/object/eservice_device/service/query_cases_related_device_id_list",
            data: {
                casesId: caseId
            },
            success: function (res) {
                if (
                    res &&
                    res.Value &&
                    res.Value.deviceIdList &&
                    res.Value.deviceIdList.length !== 0
                ) {
                    me.pluginService.api.hideLoading();
                    me.deviceMap[caseId] = res.Value.deviceIdList;
                } else {
                    CRM.util.FHHApi({
                        url: "/EM1HNCRM/API/v1/object/CasesObj/controller/RelatedList",
                        data: {
                            associated_object_describe_api_name: "CasesObj",
                            associated_object_field_related_list_name: "target_related_list_cases_id__c",
                            extractExtendInfo: true,
                            ignore_scene_record_type: false,
                            include_describe: false,
                            serializeEmpty: false,
                            search_query_info: JSON.stringify({
                                limit: 2000,
                                offset: 0,
                                filters: [{
                                    field_name: "_id",
                                    field_values: [
                                        caseId,
                                    ],
                                    operator: "IN",
                                },]
                            }),
                        },
                        success: function (res) {
                            me.pluginService.api.hideLoading();
                            if (res && res.Value && res.Value.dataList) {
                                const caseObj = res.Value.dataList.find(i => i._id == caseId)
                                if (caseObj && caseObj[FIELD_APINAME_DEVICE_ID]) {
                                    const masterData = factory.dataGetter.getMasterData()
                                    masterData.device_id = caseObj[FIELD_APINAME_DEVICE_ID]
                                    masterData.device_id__r = caseObj[FIELD_APINAME_DEVICE_ID__R]
                                    factory.dataUpdater.updateMaster(masterData)
                                }
                            }
                        },
                        error: function () {
                            me.pluginService.api.hideLoading();
                        }
                    }, {
                        errorAlertModel: 1,
                    });
                }
            },
            error: function (err) {
                console.error(err);
                // 兼容 未开启多设备
                CRM.util.FHHApi({
                    url: "/EM1HNCRM/API/v1/object/CasesObj/controller/RelatedList",
                    data: {
                        associated_object_describe_api_name: "CasesObj",
                        associated_object_field_related_list_name: "target_related_list_cases_id__c",
                        extractExtendInfo: true,
                        ignore_scene_record_type: false,
                        include_describe: false,
                        serializeEmpty: false,
                        search_query_info: JSON.stringify({
                            limit: 2000,
                            offset: 0,
                            filters: [{
                                field_name: "_id",
                                field_values: [
                                    caseId,
                                ],
                                operator: "IN",
                            },]
                        }),
                    },
                    success: function (res) {
                        me.pluginService.api.hideLoading();
                        if (res && res.Value && res.Value.dataList) {
                            const caseObj = res.Value.dataList.find(i => i._id == caseId)
                            if (caseObj && caseObj[FIELD_APINAME_DEVICE_ID]) {
                                const masterData = factory.dataGetter.getMasterData()
                                masterData.device_id = caseObj[FIELD_APINAME_DEVICE_ID]
                                masterData.device_id__r = caseObj[FIELD_APINAME_DEVICE_ID__R]
                                factory.dataUpdater.updateMaster(masterData)
                            }
                        }
                    },
                    error: function () {
                        me.pluginService.api.hideLoading();
                    }
                }, {
                    errorAlertModel: 1,
                });
            }
        }, {
            errorAlertModel: 1,
        });
    }


    async batchAddDataHandle(plugin, param, undefind, {objectApiName, recordType, $event}) {
        const me = this;
        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'splugin-receivematerialbillobj'
        });
        let masterData = factory.dataGetter.getMasterData();

        const deviceList = this.deviceMap[masterData.case_id || masterData.cases_id];

        let fieldName = this.accessoryConfig.warehouseModel == 2 ? 'new_product_id' : 'stock_id';
        let mdApiName = objectApiName;

        factory.batchPickData({
            objApiName: mdApiName,
            recordType: recordType,
            fieldName: fieldName
        });
    }

    async _mdRenderAfter(plugin, param) {
        const mdApiName = param.objApiName;
        const details = param.dataGetter.getDetail(mdApiName);
        this.setFieldsReadonly(param, details);
    }


    // 不允许编辑的字段；
    setFieldsReadonly(param, arrs) {
        if (!arrs.length) return;
        let hideFields = ['stock_id', 'new_product_id', 'spare_batch_stock_id'];

        param.dataUpdater.setReadOnly({
            fieldName: hideFields,
            dataIndex: 'all',
            objApiName: param.objApiName,
            recordType: param.recordType,
            status: true
        })

        let batchSnRowIds = [];
        let notBatchSnRowIds = [];

        arrs.forEach(rowData => {
            if (rowData.product_batch_sn__v == 3) {
                batchSnRowIds.push(rowData.rowId);
            } else {
                notBatchSnRowIds.push(rowData.rowId)
            }
        })

        if (batchSnRowIds && batchSnRowIds.length > 0) {
            param.dataUpdater.setReadOnly({
                dataIndex: batchSnRowIds,
                fieldName: ['apply_amount'],
                objApiName: param.objApiName,
                recordType: param.recordType,
                status: true
            })
            param.dataUpdater.setRequired({
                dataIndex: batchSnRowIds,
                fieldName: ['sn_id'],
                objApiName: param.objApiName,
                recordType: param.recordType,
                status: true
            })
        }

        if (notBatchSnRowIds && notBatchSnRowIds.length > 0) {
            param.dataUpdater.setReadOnly({
                fieldName: ['sn_id'],
                dataIndex: notBatchSnRowIds,
                objApiName: param.objApiName,
                recordType: param.recordType,
                status: true
            })
        }

    }

    async testHandleServiceBom(plugin, params) {
        let pluginService = this.pluginService.api.pluginServiceFactory({
            pluginApiName:'bom'
        });

        pluginService.batchPickData({
            objApiName: params.objectApiName,
            recordType: params.recordType,
            fieldName: 'new_product_id',
        });
    }

    async _batchAddBeforeHook(plugin, param) {
        let masterData = param.dataGetter.getMasterData();

        // 从服务BOM添加-爆炸图
        console.log('param?.actionDescribe?.from', param?.actionDescribe?.from)
        if (param?.actionDescribe?.from === 'handleBatchAddByServiceBomPlugin') {
            const devicePicture = new DevicePicture();
            return {
                modulePath: 'crm-modules/buscomponents/pickselfobject_cpq_style2/pickselfobject_cpq_style2',
                extendParam: {
                  getApiHook(apis) {
                    devicePicture.initDialogApis(apis);
                    // getApiHook 这个方法是在整个弹框渲染完后执行的，所以在这里挂载“已选清单”按钮
                    devicePicture.mountSelectedListBtn();
                    //
                    devicePicture.hideTableCheckbox()
                  },
                  validResult(...args) {
                    const data = args[0]?.data || [];
                    // 如果没有选择服务bom子产品，不允许确认
                    const allChildrenEmpty = data.every(arg => {
                      return Array.isArray(arg.children) && arg.children.length === 0;
                    });

                    if (allChildrenEmpty) {
                      FS.crmUtil.alert($t('stock.ExplosionDiagram.please_select_sub_product')); // 请选择服务BOM的子产品
                      return true;
                    }
                  },
                  apiname: 'ProductObj',
                  accountId: masterData.account_id,
                  master_data: masterData,
                  title: $t('选择服务Bom'),
                  hideMoney: true, // 隐藏总金额
                  hideQuantity: true, // 隐藏数量
                  hideShoppingCart: true, // 隐藏购物车相关按钮
                  dialogOptions: {
                    btnName: {
                      save: $t('stock.ExplodedView.confirm'), // 确定
                    }
                  },

                  extendBomParam: {
                    hideParentsCheckBox: false, // 隐藏父级复选框
                    search_rich_text_extra: true, // 支持长文本字段返回所有内容
                    notShowSelectAllBtn: true, // 不显示全选按钮
                    hideIconColumn: true, //隐藏右侧的icon列

                    extendLeftBoxHook($el) {
                      // 底层调用 extendLeftBoxHook 方法时，是 this.options.extendLeftBoxHook() 方式调用的；
                      // 所以这里的 this 可以获取到 table 的 options
                      devicePicture.initTableInfo(this);
                      devicePicture.initPicture($el, this.bom_core_id);
                    },

                    renderAfterHook(obj){
                      let {$el_left} = obj;
                      debugger
                      devicePicture.initTableInfo(this);
                      devicePicture.initPicture($el_left, this.bom_core_id);
                    },

                    addBtnHook: (row)=> {
                      if(row && row.related_core_id) {
                        return '<a data-id="' + row.rowId + '" data-action="switchingExplosionPattern" >' + $t("切换爆炸图") + '</a>'
                      } else {
                        return ''
                      }
                    },

                    parseColumn_after(columnList) {
                      const drawingNoColumn = columnList.find(item => item.api_name === 'drawing_no');
                      if (drawingNoColumn) {
                        drawingNoColumn.isHidden = false;
                        drawingNoColumn.fixed = true;
                        drawingNoColumn.fixedIndex = 2;
                        drawingNoColumn.render = (data, type, full, helper, index, item, context) => {
                          return devicePicture.renderdrawingNo(data, type, full, helper, index, item, context)
                        }
                      }

                      const amountColumn = columnList.find(item => item.api_name === 'amount');
                      if (amountColumn) {
                        amountColumn.fixed = true;

                      }

                      const productIdColumn = columnList.find(item => item.api_name === 'product_id');
                      if (productIdColumn) {
                        productIdColumn.fixedIndex = 1;
                      }

                      const operationColumn = columnList.find(item => item.api_name == undefined && item.data == null);
                      if(operationColumn) {
                        operationColumn.width = 140
                      }
                    },

                    initDataEndHook(allData) {
                      devicePicture.initDataEndHook(allData.data);
                    },
                    trHoverHook: devicePicture.handleTrHoverHook(),
                    checkboxClickEndHook(...args) {
                      devicePicture.handleCheckboxClickEndHook(args);
                    },
                    trClickEndHook(...args) {
                      devicePicture.handleTrClickEndHook(args);
                    }
                  },
                  defaultWidth_left: 200,
                },
                beforeRequest: rq => {
                  const search_query_info = rq.search_query_info;
                  if (search_query_info) {
                    const search_query_info_obj = JSON.parse(search_query_info);
                    const filters = search_query_info_obj.filters || [];
                    filters.push({
                      field_name: '_id',
                      field_values: this.bomCoreInfo.map(item => item.product_id),
                      operator: 'IN',
                    });
                    search_query_info_obj.filters = filters;
                    rq.search_query_info = JSON.stringify(search_query_info_obj);
                  }
                  return rq;
                },
              };
        }

        let mdApiName = param.objApiName;

        let {
            form_warehouse_id,
            form_cases_id,
            form_device_id,
            form_receive_type,
            new_product_id,
            stock_id
        } = this.getAllFields(mdApiName);

        let receive_type = masterData[form_receive_type];
        let warehouse = masterData[form_warehouse_id];
        let cases_id = masterData[form_cases_id];
        let device_id = masterData[form_device_id];
        const accessoryConfig = this.accessoryConfig || {};

        let masterApiName = param.masterObjApiName;
        let des = param.dataGetter.getDescribe(masterApiName).fields;

        let lookUpApiName = param.lookupField.api_name;  // 数据来源对象
        let isSpecial = true;

        if (isSpecial) {
            let label = '';
            if (receive_type && receive_type == 1) {
                if (accessoryConfig.baseEmployeeWarehouseDetailFlag && accessoryConfig.baseEmployeeWarehouseDetailFlag != 1 && !warehouse) {
                    label = des[form_warehouse_id].label;
                }
            } else if (receive_type == BASE_CASES_DEVICE) {
                if (!cases_id) {
                    label = des[form_cases_id].label;
                }
                if (!device_id) {
                    label = des[form_device_id].label;
                }
            }
            // 兼容新版根据工单设备领料
            if (accessoryConfig.receiveByCasesDeviceSwitch == 1) {
                if (!cases_id) {
                    label = des[form_cases_id].label;
                }
            }
            if (label) {
                let msg = this.i18n("请先选择{{label}}", {
                    'label': label
                });
                // this.alert(msg);
                FS.util.alert(msg);
                plugin.skipPlugin(); // 不会执行第三个钩子
            }
        }

        let filterIds = [];

        const paramObj = await this._pickProductBom(plugin, param);

        let details = param.dataGetter.getDetail(mdApiName);
        let recordType = param.recordType;

        if (lookUpApiName === 'new_product_id') {
            filterIds = this.getAllFieldIds(details, new_product_id, recordType)
        } else if (lookUpApiName === 'stock_id') {
            filterIds = this.getAllFieldIds(details, stock_id, recordType)
        }

        let btnInfo = {
            fieldname: lookUpApiName
        };

        if (receive_type == BASE_CASES_DEVICE || accessoryConfig.receiveByCasesDeviceSwitch == 1) {
            return {
                filters: paramObj.filters ? paramObj.filters : [],
                extendParam: {
                    filterIds,
                    source_api_name: mdApiName,
                    btnInfo,
                    fieldName: lookUpApiName,
                }
            };
        }


        let filters = this.addFilter(param);

        this.filters = filters;

        let res = {
            skipReadOnlyValidate: true, // 跳过底层只读字段不允许选数据
            filters: filters,
            filterIds: filterIds,
            source_api_name: mdApiName,
            fieldName: lookUpApiName,
            extendParam: {
                filterIds,
                source_api_name: mdApiName,
                btnInfo,
                fieldName: lookUpApiName,
            }
        };
        return res
    }


    async _mdEditAfter(plugin, param) {
        if (param && param.fieldName == 'apply_amount') {
            const changeData = param.changeData;
            for (var prop in changeData) {
                if (hasOwnProperty.call(changeData, prop)) {
                    if (changeData[prop].apply_amount < 0) {
                        let msg = this.i18n("领料单数量不能小于{{num}}", {
                            'num': 0
                        });
                        FS.util.alert(msg);
                        plugin.skipPlugin(); // 不会执行第三个钩子
                    }
                }
            }
        }
    }

    addFilter(param) {
        const me = this;
        let filter = [];
        // 过滤仓库
        const masterData = param.dataGetter.getMasterData();
        const warehouse = masterData.warehouse_id || '';
        const receive_type = masterData.receive_type || ''; // 领料方式
        const accessoryConfig = this.accessoryConfig || {};
        const lookUpApiName = param.lookupField.api_name;  // 数据来源对象
        const recordType = param.recordType;
        const mdApiName = param.objApiName;
        const details = param.dataGetter.getDetail(mdApiName);

        let {
            new_product_id,
            stock_id
        } = this.getAllFields(mdApiName);

        if (param.apiname == 'StockObj' && param.fieldName == 'stock_id' && receive_type && receive_type == 1 && warehouse) {
            filter.push({
                field_name: 'warehouse_id',
                field_values: [warehouse],
                operator: 'EQ'
            })
        }

        // 开启了序列号且领料方式为基于产品领料  过滤开启序列号管理的产品
        if (+receive_type == 2 && accessoryConfig.isOpenBatchSN) {
            filter = filter.concat([{
                field_name: 'batch_sn',
                field_values: [3],
                operator: 'N',
            }])
        }


        let ids = [];
        if (lookUpApiName === 'new_product_id') {
            ids = this.getAllFieldIds(details, new_product_id, recordType)
        } else if (lookUpApiName === 'stock_id') {
            ids = this.getAllFieldIds(details, stock_id, recordType)
        }
        if (ids && ids.length > 0) {
            filter = filter.concat([{
                field_name: '_id',
                field_values: ids,
                operator: 'NIN',
            }])
        }

        return filter;
    }

    // 选数据之后
    async _batchAddAfter(plugin, param) {
        this.showLoading();
        if (param?.actionDescribe?.from === 'handleBatchAddByServiceBomPlugin') {
            this.handleBatchAddAfterForBomCoreExplosionDiagram(plugin, param);
        }
        await this.batchAddAfterTodo(plugin, param);
        this.hideLoading();
    }

    handleBatchAddEnd(plugin, param) {
        const { dataUpdater, newDataIndexs } = param;
        if (param.pluginApiName === 'handleBatchAddByServiceBomPlugin') {
            // 删除根节点产品
            newDataIndexs.forEach(indexItem => {
                dataUpdater.del(store.detailObjApiName, indexItem);
            });
        }
    }

    async batchAddAfterTodo(plugin, param) {
        const lookupDatas = param.lookupDatas;
        const objApiName = param.objApiName;
        const newData = param.addDatas;


        this.addSomeFields(newData, lookupDatas);

        this.setFieldsReadonly(param, newData);
    }

    addSomeFields(addData, lookupData) {
        addData.forEach((item, index) => {
            let lData = lookupData[index];
            if (lData) {
                item = Object.assign(item, {
                    actual_unit: lData.auxiliary_unit ||
                        lData.actual_unit ||
                        lData.unit__v ||
                        lData.unit,
                    batch_sn: !isNaN(lData.batch_sn__v) ? lData.batch_sn__v : lData.batch_sn,
                    batch_sn__v: lData.batch_sn__v,
                    product_batch_sn__v: lData.batch_sn__v,
                    apply_amount: lData.batch_sn__v === "3" ? '1' : null,
                });
            }
        })
    }

    // 清除一些无用字段
    clearData(obj) {
        let keys = Object.keys(obj);
        let delFields = ['_fields', '_origin'];
        keys.forEach(k => {
            if (k.includes('_tpd') || delFields.includes(k)) {
                delete obj[k];
            }
        });
    }

    getMDData(param) {
        return param.dataGetter.getDetail(param.objApiName);
    }

    // 获取已选数据源的id
    getAllFieldIds(details = [], field = '', recordType) {
        let res = [];
        details.forEach(item => {
            if (item.record_type === recordType && field && item[field] && item.batch_sn__v != '3') res.push(item[field]);
        });
        return res;
    }

    getAccessoryConfig() {
        let url = 'FHH/EM1HESERVICE2/eservice/casesAccessoryConfig/getAccessoryConfig';
        return PPM.ajax(this.request, url, {}).then(res => {
            return this.accessoryConfig = res.data
        }).catch(err => {
            console.log('err', err);
        })
    }

    // 查询设备关联产品信息
    queryDeviceRelatedProductInfo(mdApiName, masterData) {
        let me = this;
        let {
            form_device_id,
        } = this.getAllFields(mdApiName);

        let url = 'FHH/EM1HNCRM/API/v1/object/eservice_device/service/query_device_related_product_info';
        let p = Object.assign({}, {
            deviceId: masterData[form_device_id]
        });
        return PPM.ajax(this.request, url, p);
    }

    async pickselfBom(rootId, cb) {
        const me = this;
        let PC = await this.pluginService.api.import('crm-modules/components/pickself_bom/pickself_bom');
        const selectBom = new PC({
            rootId: rootId,
            noNeedCheckParentsAndChildren: true,
        });
        selectBom.on('dialogEnter', function (list) {
            cb && cb(list);
        });
    }

    _pickProductBom(plugin, param) {
        const me = this;

        const factory = this.pluginService.api.pluginServiceFactory({
            pluginApiName: 'splugin-receivematerialbillobj'
        });

        const accessoryConfig = this.accessoryConfig || {};
        let masterData = factory.dataGetter.getMasterData();
        let lookUpApiName = param.lookupField && param.lookupField.api_name ? param.lookupField.api_name : 'new_product_id';  // 数据来源对象

        let filters = [];
        let paramObj = {}

        // 选bom
        return new Promise((resolve1) => {
            this.queryDeviceRelatedProductInfo(factory.objApiName, masterData).then((resInfo) => {
                if (resInfo && resInfo.isPackage && resInfo.receiveByProductBomSwitch) {
                    me.pickselfBom(resInfo.productId, (list, remList) => {
                        // 产品领料筛选_id 库存领料筛选product_id
                        if (accessoryConfig.receiveByCasesDeviceSwitch == 1) {
                            if (lookUpApiName == 'new_product_id') {
                                const ids = list.map((item) => {
                                    return item.product_id;
                                });
                                paramObj.filters = filters.concat([{
                                    field_name: '_id',
                                    field_values: ids,
                                    operator: 'IN',
                                }]);
                            } else if (lookUpApiName == 'stock_id') {
                                const ids = list.map((item) => {
                                    return item.product_id;
                                });
                                paramObj.filters = filters.concat([{
                                    field_name: 'product_id',
                                    field_values: ids,
                                    operator: 'IN',
                                }]);
                            }
                        } else {
                            if (accessoryConfig.accessoryWarehouseStatus == 1) {
                                // 开启库存 当前企业中开启库存且对接了A类库存时，此处需要选择“领料仓库”字段，根据选择的sku校验库存
                                const ids = list.map((item) => {
                                    return item.product_id;
                                });
                                paramObj.filters = filters.concat([{
                                    field_name: 'product_id',
                                    field_values: ids,
                                    operator: 'IN',
                                }]);
                            }
                        }
                        resolve1(paramObj)
                    });
                } else {
                    resolve1(paramObj);
                }
            });
        }).catch(err => {
            console.log('err', err);
        })

    }

    handleAddSnBefore(plugin, hookParam) {
        return {
            beforeRequest: function (rq) {
                const objectData = rq.object_data || {};
                const masterData = rq.master_data || {};
                const details =
                    (rq.details && rq.details.ReceiveMaterialBillProductObj) || [];
                const warehouse_id = objectData.warehouse_id || "";
                const new_product_id = objectData.new_product_id || "";
                const search_query_info = rq.search_query_info ? JSON.parse(rq.search_query_info) : {}
                // 过滤出和当前点击的产品 相同的产品下的  序列号
                let snIds = [];
                if (details.length > 0) {
                    details.forEach((i) => {
                        if (i.new_product_id == new_product_id && i.sn_id) {
                            snIds.push(i.sn_id);
                        }
                    });
                }
                search_query_info.filters = [
                    ...(search_query_info.filters || []),
                    {
                        field_name: "warehouse_id",
                        field_values: [warehouse_id],
                        operator: 'EQ'
                    },
                    {
                        field_name: "stock_id.name",
                        field_values: [""],
                        operator: 'ISN'
                    },
                    {
                        field_name: "product_id",
                        field_values: [new_product_id],
                        operator: 'EQ'
                    },
                    {
                        field_name: "is_using",
                        field_values: [true],
                        operator: 'N'
                    },
                    {
                        field_name: "whether_used",
                        field_values: [true],
                        operator: 'N'
                    },
                    {
                        field_name: "grade",
                        field_values: ["2"],
                        operator: 'N'
                    }
                ];
                if (snIds.length > 0) {
                    search_query_info.filters.push({
                        field_name: "_id",
                        field_values: snIds,
                        operator: 'NIN'
                    });
                }
                rq.search_query_info = JSON.stringify(search_query_info);
                return rq;
            }
        };
    }

    apply() {
        return [
            {
                event: "form.render.before",
                functional: this.handleFormRenderBefore.bind(this),
            },
            {
                event: 'md.render.before',
                functional: this._mdRenderBefore.bind(this)
            },
            {
                event: 'md.render.after',
                functional: this._mdRenderAfter.bind(this)
            },
            {
                event: 'md.batchAdd.before',
                functional: this._batchAddBeforeHook.bind(this)
            },
            {
                event: 'md.batchAdd.after',
                functional: this._batchAddAfter.bind(this)
            },
            {
                event: 'md.batchAdd.end',
                functional: this.handleBatchAddEnd.bind(this)
            },
            {
                event: 'md.edit.after',
                functional: this._mdEditAfter.bind(this)
            },
            {
                event: "form.dataChange.after",
                functional: this._formFieldWatch.bind(this)
            },
            {
                event: "sn.add.before",
                functional: (plugin, hookParam) => {
                    return this.handleAddSnBefore(plugin, hookParam);
                }
            },
        ];
    }
}
