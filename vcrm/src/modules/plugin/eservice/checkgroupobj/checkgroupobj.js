import Base from 'plugin_base'
import PPM from 'plugin_public_methods'
// import filtergroup from "./filtergroup";

export default class CheckGroupObj extends Base {

  constructor(pluginService, pluginParam) {
    super(...arguments);
    this.pluginService = pluginService
    this.pluginParam = pluginParam
  }

  options() {
    return {
      defMasterFields: {
      },
      defMdFields: {
        check_group_item_option_id: 'check_group_item_option_id',
        field_type: 'field_type',
        max_reference: 'max_reference',
        min_reference: 'min_reference',
        numerical_order: 'numerical_order'
      }
    }
  }
  async _fetchDescribeAfter(plugin, param) {
    const { layout, objectDescribe, objectDescribeExt } = param.describe;
    const field = 'target_object';
    const fieldValue = 'DeviceObj';
    const res = await this.queryDeviceConfig();
    // not_usable 设置选择不展示
    const setFieldAttr = (options) => {
      _.each(options, (item) => {
        if (item && item.value == fieldValue) {
          item['not_usable'] = true;
        }
      })
    }
    // 没有开启设备，隐藏设备对象单选项
    if (!_.isEmpty(res.data) && res.data.deviceStatus != 1) {
      if (objectDescribe && objectDescribe.fields[field] && objectDescribe.fields[field].options) {
        let options = objectDescribe.fields[field].options;
        setFieldAttr(options);
        objectDescribe.fields[field].options = options;
      }
      if (objectDescribeExt && objectDescribeExt.fields[field] && objectDescribeExt.fields[field].options) {
        let options = objectDescribeExt.fields[field].options;
        setFieldAttr(options);
        objectDescribeExt.fields[field].options = options;
      }
    }
    return param
  }

  async _beforeRender(plugin, param) {
    const that = this;
    const objectApiName = param.objApiName;
    const masterData = param.dataGetter.getMasterData();
    const BaseComponents = param.BaseComponnets;

    try {
      const res = await this.getReferenceObjectDescribe('CasesObj');
      const deviceRes = await this.getReferenceObjectDescribe('DeviceObj');
      const CasesObjDescribe = res.data;
      const DeviceObjDescribe = deviceRes.data;
      this.conditionsOptions = this.parseConditionsOptions(CasesObjDescribe);
      this.deviceConditionsOptions = this.parseConditionsOptions(DeviceObjDescribe);
    } catch (error) {
      console.log('err', error);
    }
    
    await that.pluginService.api.import(['app-workorder/app.js']);
    await that.pluginService.api.import(['app-workorder/assets/style/all.css']);

    let filtergroup = await that.pluginService.api.import('app-workorder/components/filter-group/filter-group-vue');

    const field = 'applicable_scope';
    const describeFields = param.dataGetter.getDescribe(objectApiName).fields;
    const components = {};

    const fieldAttr = describeFields[field];

    components[field] = BaseComponents.text.extend({
      render() {
        const applicableScopeStr = masterData[field];
        let applicableScope = applicableScopeStr ? JSON.parse(applicableScopeStr) : [];

        let $vm = new Vue({
          render: h => h(filtergroup, {
            props: {
              conditionsOptions: that.conditionsOptions || [],
              deviceConditionsOptions: that.deviceConditionsOptions || [],
              conditions: applicableScope,
              pluginService: that.pluginService,
              targetObject: masterData['target_object'] || 'CasesObj'
            },
            on: {
              changeValue: (val) => {
                const dValue = val && val.length > 0 ? JSON.stringify(val) : '';
                param.dataUpdater.updateMaster({
                  [field]: dValue
                })
                param.end();
              }
            }
          })
        }).$mount();
        BaseComponents.text.prototype.render.apply(this, arguments);
        this.$el.html($vm.$el);
      },
      destroy() {
        //销毁内部组件
        this.widget && (this.widget.destroy(), this.widget = null);
        BaseComponents.text.prototype.destroy.apply(this, arguments);
      }
    })

    return {
      components: components
    }
    
  }

  async _afterRender(plugin, param) {
    const res = await this.queryDeviceConfig()
    if (!_.isEmpty(res.data) && res.data.deviceStatus == 1) {
      //设置主对象字段只读
      param.dataUpdater.setReadOnly({
        fieldName: ["boolean_value"],
        status: true
      });
    }
  }

  parseConditionsOptions(res, options = {}) {
    const { useCustomComponent, needPrefix } = options;
    const fields = {};
    // 对象
    _.forEach(res.objectDescribe.fields, (v, k) => {
      let api_name = `${v.api_name}`;
      if (needPrefix) {
        api_name = `${res.objectDescribe.api_name}.${api_name}`;
      }
      fields[api_name] = _.extend({}, v, {
        api_name,
        label: `${res.objectDescribe.display_name}.${v.label}`,
        useCustomComponent
      });
    });
    // 查找关联字段
    let objFields = _.filter(_.toArray(res.objectDescribe.fields), (item) => {
      return item.type == 'object_reference';
    });
    _.forEach(objFields, (item) => {
      const referenceObject = res.referenceObject[item.target_api_name];
      _.forEach(referenceObject.fields, (v, k) => {
        let api_name = `${item.api_name}.${v.api_name}`;
        if (needPrefix) {
          api_name = `${res.objectDescribe.api_name}.${api_name}`;
        }
        fields[api_name] = _.extend({}, v, {
          api_name,
          label: `${res.objectDescribe.display_name}.${item.label}.${v.label}`,
          useCustomComponent
        });
      });
    });
    return fields;
  }

  async _mdRenderBefore(plugin, param) {
    const me = this;
    this.mdApiName = param.objApiName;

    let factory = this.pluginService.api.pluginServiceFactory({
      pluginApiName: 'checkgroup',
    });

    this.describeLayout = factory.dataGetter.getDescribeLayout(); // 透传describeLayout接口完整返回值
    this.describe = factory.dataGetter.getDescribe(this.mdApiName); // 当前表单下，对象描述
    this.data = factory.dataGetter.getData();
    let res = {};

    let addBtn = [{
      label: '+' + this.i18n('添加检查项'),
      callBack: this.batchAddDataHandle.bind(this)
    }]

    let _fn = (trData) => {
      return {
        add: [{
          label: this.i18n('编辑'),
          action: 'action',//尽量唯一
          callBack(trData,{objectApiName, recordType}) {
            me.handleEditRow(trData, {objectApiName, recordType})
          }
        }],
        del: ["copyRowHandle"]
      }
    };

    res.operateBtns = [_fn];

    //指定字段的宽度
    res.columnWidthConfig = {
      name: 400,
      min_reference: 150,
      max_reference: 150,
    }
    res.buttons = {
      retain: [],
      add: addBtn,
    }
    return res;
  }

  _mdRenderAfter(plugin, param) {
    this.setFieldsReadonly(param);
  }

  async _mdDelBefore(plugin, param) {
    return new Promise(async (resolve, reject) => {
      const isEdit = param.formType === 'edit';
      if (!isEdit) return resolve();
      const { delDatas = [] } = param;
      const delIds = delDatas.filter(item => item._id);
      const res = await this.isCheckGroupItemCanDelete({checkGroupId: this.data._id, checkGroupItemId: delIds[0]._id});
      if (!res.data || res.errCode !== 'C120040000') {
        this.pluginService.api.alert(res.errMsg);
        reject();
      }
      if (res.data && res.data.canDelete) {
        resolve();
      } else {
        const rules = res.data && res.data.useByCheckGroupRules.map(item => {
          return item.label;
        });
        const msg = this.i18n('检查项删除失败，检查项选项被以下检查规则使用：{{rules}}', { rules: rules.join(',') });
        this.pluginService.api.alert(msg);
        reject();
      }
    })
  }

  async _mdDelAfter(plugin, param) {
    const mdApiName = param.objApiName;
    const delDatas = param.delDatas;

    let factory = this.pluginService.api.pluginServiceFactory({
      pluginApiName: 'checkgroup',
    });

    delDatas.forEach(item => {
      factory.dataUpdater.del(mdApiName, item.rowId)
    })
    factory.end();

    factory.triggerCalAndUIEvent({
      delDatas: param.delDatas, //删除的行数据
      objApiName: param.objApiName
    })

    // 执行数据序号刷新逻辑
    this.numericalOrder(param, mdApiName);
  }

  _mdCopyAfter(plugin, param) {
    const objectApiName = param.objApiName;
    const recordType = param.recordType;
    const newDataIndexs = param.newDataIndexs;
    let {
      max_reference,
      min_reference
    } = this.getAllFields(objectApiName);

    // 如果复制的值不是文本或数字不可编辑
    // 获取所有的数据，判断每条数据的字段类型，根据字段类型设置编辑状态
    _.each(newDataIndexs, (dataIndex, index) => {
      let rowData = param.dataGetter.getData(objectApiName, dataIndex);
      const status = ['text', 'number'].indexOf(rowData.field_type) == -1;
      param.dataUpdater.setReadOnly({
        fieldName: [max_reference, min_reference],
        dataIndex: [dataIndex],
        objApiName: objectApiName,
        recordType: recordType,
        status: status
      });
    })
    
    // this.numericalOrder(param, objectApiName);

  }

  _mdEditAfter(plugin, param) {
    // 如果修改数据为字段类型， 参考值最大最小值置为空。
    // 编辑最大值或者最小值设置禁用
    const mdApiName = param.objApiName;
    const details = param.dataGetter.getDetail(mdApiName);
    const changeData = param.changeData;
    for (var prop in changeData) {
      if (hasOwnProperty.call(changeData, prop)) {
        if (changeData[prop].field_type && ['number', 'text'].indexOf(changeData[prop].field_type == -1)) {
          let targetData = details.find(item => {
            return item.rowId === prop;
          })
          if (targetData) {
            targetData.min_reference = '';
            targetData.max_reference = '';
            param.dataUpdater.updateDetail(mdApiName, prop, targetData)
          }
        }
      }
    }
  }

  async batchAddDataHandle(undefind, {objectApiName, recordType, $event}) {
    const me = this;
    me.checkedOptionShow({},{ objectApiName, recordType });
  }

  async checkedOptionShow(trData,{ objectApiName, recordType }) {
    const me = this;

    let factory = this.pluginService.api.pluginServiceFactory({
      pluginApiName: 'checkgroup',
    });
    const describe = this.describe;
    const maxlength = describe?.fields?.name?.max_length || 30;
    const allDetail = factory.dataGetter.getDetail(objectApiName);
    const order = allDetail.length + 1;
    
    me.fieldTypeList = this.getFieldTypeList(objectApiName, 'field_type');
    me.decimalPlacesList = this.getFieldTypeList(objectApiName, 'decimal_places');
    me.numericalLimitList = this.getFieldTypeList(objectApiName, 'numerical_limit');
    me.watermarkList = this.getFieldTypeList(objectApiName, 'is_watermark');
    await me.pluginService.api.import(['app-workorder/app.js']);
    await me.pluginService.api.import(['app-workorder/assets/style/all.css']);
    let CheckedOptionEditor = await me.pluginService.api.import('app-workorder/tpls/newsetting/checked-group/checked-option-editor/checked-option-editor-vue');

    CheckedOptionEditor.$show({
      editCheckedOptionItemId: trData._id || trData.rowId,
      fieldTypeList: me.fieldTypeList,
      decimalPlacesOptions: me.decimalPlacesList,
      numericalLimitOptions: me.numericalLimitList,
      watermarkOptions: me.watermarkList,
      order: trData.numerical_order || order,
      trData: trData,
      maxlength: maxlength,
      checkGroupId: me.data._id
    })
    .then((res)=> {
      let objdata = {};
      if (!trData.rowId) {
        let basicData = factory.getRowBasicData(objectApiName, recordType);
        objdata = Object.assign({}, basicData, res);
        if (['checkbox', 'radio'].indexOf(objdata.field_type) == -1) {
          delete objdata.check_group_item_option_id
          delete objdata.check_group_item_option_id__r
        }
        factory.dataUpdater.add([objdata]);
      } else {
        objdata = Object.assign({}, trData, res);
        if (['checkbox', 'radio'].indexOf(objdata.field_type) == -1) {
          objdata.check_group_item_option_id = '';
          objdata.check_group_item_option_id__r = [];
        }
        factory.dataUpdater.updateDetail(objectApiName, trData.rowId, objdata);
      }
      this.setItemReadonly(factory, objectApiName, recordType, objdata.field_type, objdata.rowId);
      this.numericalOrder(factory, objectApiName);
      factory.end();

    })
    .catch((err) => {
      console.log('err', err)
    })
  }

  // 批量添加选项值
  batchCreateCheckItemOption(data) {
    let url = 'FHH/EM1HESERVICE2/eservice/checkGroup/batchCreateCheckItemOption';
    let p = Object.assign({}, {
      nameList: data
    })
    return PPM.ajax(this.request, url, p)
  }

  // 获取对象关联对象的描述
  getReferenceObjectDescribe(apiName) {
    let url = 'FHH/EM1HESERVICE2/eservice/objectDescribe/getReferenceObjectDescribe';
    let p = Object.assign({}, {
      apiName
    })
    return PPM.ajax(this.request, url, p)
  }

  // 获取开启对象设备状态
  queryDeviceConfig() {
    let url = 'FHH/EM1AESERVICE/DeviceService/queryDeviceConfig';
    return PPM.ajax(this.request, url, {})
  }

  // 删除检查项前判断
  isCheckGroupItemCanDelete({checkGroupId, checkGroupItemId}) {
    let url = 'FHH/EM1HESERVICE2/eservice/checkGroup/isCheckGroupItemCanDelete';
    let p = Object.assign({}, {
      checkGroupId,
      checkGroupItemId
    })
    return PPM.ajax(this.request, url, p)
  }


  getFieldTypeList(objectApiName, fieldName) {
    const { detailObjectList } = this.describeLayout;
    if(!detailObjectList) return;
    const targetDetailObject = detailObjectList.find((item) => {
      return item.objectApiName === objectApiName;
    })
    try {
      const { objectDescribe: { fields: { [fieldName]: { options } } }  } = targetDetailObject;
      if (options) {
        const filterOptions = options.filter(item => {
          return item && item.not_usable !== true;
        })
        return filterOptions;
      }
    } catch (error) {
      return []
    }
  }

  async handleEditRow(trData, {objectApiName, recordType}) {
    const me = this;
    me.checkedOptionShow(trData, {objectApiName, recordType});
  }

  setFieldsReadonly(param) {
    let {
      check_group_item_option_id,
      field_type,
      max_reference,
      min_reference,
      numerical_order
    } = this.getAllFields(param.objApiName);

    // 非文本、数字字段，最大最小参考值不可编辑
    const mdApiName = param.objApiName;
    const details = param.dataGetter.getDetail(mdApiName);
    const ids = details.filter(item => {
      return ['text', 'number'].indexOf(item.field_type) == -1;
    }).map(item => {
      return item.rowId;
    });

    param.dataUpdater.setReadOnly({
      fieldName: [max_reference, min_reference],
      dataIndex: ids,
      objApiName: param.objApiName,
      status: true
    });

    // 不可编辑字段：字段类型，可选值
    param.dataUpdater.setReadOnly({
      fieldName: [field_type, check_group_item_option_id, numerical_order],
      dataIndex: 'all',
      objApiName: param.objApiName,
      status: true
    });
  }

  setItemReadonly(factory, objectApiName, recordType, fieldType, rowId) {
    const status = ['text', 'number'].indexOf(fieldType) == -1;
    let {
      max_reference,
      min_reference
    } = this.getAllFields(objectApiName);

    factory.dataUpdater.setReadOnly({
      fieldName: [max_reference, min_reference],
      dataIndex: [rowId],
      objApiName: objectApiName,
      recordType: recordType,
      status: status
    });
  }

  numericalOrder(param, objectApiName) {

    const details = param.dataGetter.getDetail(objectApiName);

    let factory = this.pluginService.api.pluginServiceFactory({
      pluginApiName: 'checkgroup',
    });

    // 如果序号没有按照顺序排序，更新序号
    details && details.forEach((item, index) => {
      if (item.numericalOrder != index + 1 ) {
        item.numerical_order = index + 1;
        factory.dataUpdater.updateDetail(objectApiName, item.rowId, item);
      }
    })

    factory.end();
  }
  //  当切换了目标对象，需要清空试用范围的值
  _dataChangeAfter(plugin, param) {
    const that = this;
    const changeFields = Object.keys(param.changeData || {});
    const field = 'target_object';
    const tField = 'applicable_scope';
    if (changeFields.includes(field)) {
      const masterData = param.dataGetter.getMasterData();
      const data = masterData[field];
      FS.Events.trigger('change.checkedgroup-targetobject', that.targetObjectChange(data));
      param.dataUpdater.updateMaster({
        [tField]: ''
      })
    }
    param.end();
  }
  targetObjectChange(data) {
    return data
  }

  apply() {
    return [
      {
        event: "fetchdescribe.after",
        functional: this._fetchDescribeAfter.bind(this),
      },
      {
        event: 'form.render.before',
        functional: this._beforeRender.bind(this)
      },
      {
        event: 'form.render.after',
        functional: this._afterRender.bind(this)
      },
      {
        event: 'md.render.before',
        functional: this._mdRenderBefore.bind(this)
      },
      {
        event: 'md.render.after',
        functional: this._mdRenderAfter.bind(this)
      },
      {
        event: 'md.edit.after',
        functional: this._mdEditAfter.bind(this)
      },
      {
        event: 'md.del.before',
        functional: this._mdDelBefore.bind(this)
      },
      {
        event: 'md.del.after',
        functional: this._mdDelAfter.bind(this)
      },
      {
        event: 'md.copy.after',
        functional: this._mdCopyAfter.bind(this)
      },
      {
        event: 'form.dataChange.after',
        functional: this._dataChangeAfter.bind(this)
      }
    ];
  }
}