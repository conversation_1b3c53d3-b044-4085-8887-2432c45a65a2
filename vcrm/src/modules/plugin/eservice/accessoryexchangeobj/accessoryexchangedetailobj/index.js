import Base from 'plugin_base'
import PPM from 'plugin_public_methods'
import { Business, ScanCode } from "@stock-plugin/web/dist/accessoryexchangeobj/index";

export default class AccessoryExchangeObj extends Base {

  constructor(pluginService, pluginParam) {
    super(...arguments);
    this.pluginService = pluginService
    this.pluginParam = pluginParam
    this.business = new Business(pluginService)
    this.business.scanCode = new ScanCode(
      "spare",
      this.business.detailApiName,
      this.business.masterApiName,
      this.pluginService
    );
  }

  options() {
    return {
      defMasterFields: {
      },
      defMdFields: {
      }
    }
  }

  async _mdRenderBefore(plugin, param) {
    await this.getAccessoryConfig();
    const obj = await this.business.scanCode.getMdRenderBeforeOpts()
    param.dataGetter.getMasterData().method == "2" &&
    param.UI.hideDetailsComp("AccessoryExchangeDetailObj", "default__c");
    const res = {}
    res.buttons = {
      add: [
        ...obj.buttons.add
      ],
      retain: ["employee_warehouse_detail_id"],

      reset: {
        employee_warehouse_detail_id: {
          label: '+' + $t('添加')
        }
      }
    }
    return res;
  }


  async _mdEditAfter(plugin, param) {
    if (param && param.fieldName == 'exchange_amount') {
      const changeData = param.changeData;
      for (var prop in changeData) {
        if (hasOwnProperty.call(changeData, prop)) {
          if (changeData[prop].exchange_amount < 0) {
            let msg = this.i18n("调拨数量不能小于{{num}}", {
              'num': 0
            });
            FS.util.alert(msg);
            plugin.skipPlugin();
          }
        }
      }
    }
  }
  // 选数据之后
  async _batchAddAfter(plugin, param) {
    this.param = param;
    this._batchAddAfterTodo(param);
    if(this.accessoryConfig && this.accessoryConfig.warehouseModel != '3'){
      param.skipBatchSnFields = {
        skipRequired: ["serial_number_id"]
      };
    }
  }

  _batchAddAfterTodo(param) {
    const lookupDatas = param.lookupDatas;
    const objApiName = param.objApiName;
    const newData = param.addDatas;


    let ids = [];
    this.addSomeFields(newData, param.lookupDatas);

    newData.forEach(item => {
      if (item.batch_sn__v == 3) {
        item['exchange_amount'] = 1;
        ids.push(item.rowId);
      }
    });

    param.dataUpdater.setReadOnly({
      fieldName: ['exchange_amount'],
      dataIndex: ids,
      objApiName: param.objApiName,
      recordType: param.recordType,
      status: true
    })

    param.dataUpdater.setRequired({
      fieldName: ['exchange_amount'],
      dataIndex: 'all',
      objApiName: param.objApiName,
      recordType: param.recordType,
      status: true
    })
  }

  getMDData(apiName) {
    return this.param.dataGetter.getDetail(apiName);
  }

  addSomeFields(addData, lookupData) {
    addData.forEach((item, index) => {
        let lData = lookupData[index];
        if(lData){
          delete lData._id;
          delete lData._id__tpd;
          delete lData._origin;
          delete lData._origin__tpd;
          delete lData.version;
          delete lData.version__tpd;
          delete lData.__tbIndex;
          delete lData.object_describe_api_name;
          delete lData.object_describe_api_name__tpd;
          item = Object.assign(item, lData, {
            work_order_id: lData.cases_id,
            work_order_id__r: lData.cases_id__r
          });

          item.actual_unit = lData.auxiliary_unit || lData.actual_unit || lData.unit__v || lData.unit;
          item.batch_sn = !isNaN(lData.batch_sn__v)?lData.batch_sn__v:lData.batch_sn
          if(item.batch_sn != '2'){
            item.batch_id = '';
          }
        }
    })
  }

  _batchAddBeforeHook(plugin, hookParam) {
    return {
      beforeRequest(rq) {
        let mdData =
          hookParam.dataGetter.getDetails()["AccessoryExchangeDetailObj"] ||
          [];
        let productId = []; // 序列号
        const search_query_info = JSON.parse(rq.search_query_info);
        // 过滤掉个人库明细 已占用/已使用 的序列号数据
        search_query_info.filters.push({
          field_name: 'occupancy_status',
          field_values: [true],
          operator: 'N'
        })
        search_query_info.filters.push({
          field_name: 'usage_status',
          field_values: [true],
          operator: 'N'
        })
        if (mdData.length > 0) {
          // 针对序列号产品，过滤已选择的数据
          mdData.forEach((i) => {
            (i.batch_sn=='3' || i.batch_sn__v == "3") ? productId.push(i.employee_warehouse_detail_id) : "";
          });
          if (productId.length) {
            search_query_info.filters.push({
              field_name: "_id",
              field_values: productId,
              operator: "NIN"
            });
          }
        }
        rq.search_query_info = JSON.stringify(search_query_info);
        return rq;
      }
    };
  }


  getAccessoryConfig() {
    let url = 'FHH/EM1HESERVICE2/eservice/casesAccessoryConfig/getAccessoryConfig';
    PPM.ajax(this.request, url, {}).then(res => {
      this.accessoryConfig = res.data
    }).catch(err => {
      console.log('err', err);
    })
  }

  _dataChangeAfter(plugin, hookParam){
    let changeData = Object.keys(hookParam.changeData);
    changeData.forEach((key) => {
      switch (key) {
        case "method":
          this.onMethodChange(plugin, hookParam);
      }
    });
  }

  onMethodChange(plugin, hookParam){
    if (hookParam.changeData.method == "2"){
      hookParam.UI.hideDetailsComp("AccessoryExchangeDetailObj", "default__c")
    } else {
      hookParam.UI.showDetailsComp("AccessoryExchangeDetailObj", "default__c")
    }
  }

  _submitBefore(plugin, hookParam){
    if (hookParam.recordType == "default__c") {
      hookParam.dataGetter.getMasterData().method == "2" && hookParam.formType == "add" &&
      hookParam.dataUpdater.delDetail("AccessoryExchangeDetailObj")
    }
  }

  apply() {
    return [
      {
        event: 'md.render.before',
        functional: this._mdRenderBefore.bind(this)
      },
      {
        event: 'md.batchAdd.before',
        functional: this._batchAddBeforeHook.bind(this)
      },
      {
        event: 'md.batchAdd.after',
        functional: this._batchAddAfter.bind(this)
      },
      {
        event: 'form.dataChange.after',
        functional: this._dataChangeAfter.bind(this)
      },
      {
        event: 'md.edit.after',
        functional: this._mdEditAfter.bind(this)
      },
      {
        event: 'form.submit.before',
        functional: this._submitBefore.bind(this)
      },
    ];
  }
}
