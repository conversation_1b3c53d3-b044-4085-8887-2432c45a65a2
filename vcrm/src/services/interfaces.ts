// 组件
export type WidgetType =
    | 'cartQuantityInput'
    | 'quantityInput'
    | 'productList'
    | 'sortSelect'
    | 'recordType'
    | 'cartRecordType'
    | 'cartFooter'
    | 'orderQuickluQuantityInput'
    | 'singleSelect'
    | 'shopCart'
    | 'hotZoneEdit'
    | 'singleSelectList'
    | 'dhtModules'
    | 'selectConfirm'
    | 'goodDetail'
    | 'splitScreen'
    | 'skuDetail'
    | 'spuDetail'
    | 'productDetailMeta'
    | 'attachPreview'
    | 'connectProducts'
    | 'isolatedMall'
    | 'mallCategoryTree'
    | 'mallShopList'
    | 'importCategoryTree'
    | 'fullLifeCard'
    | undefined;
// 组件服务
export type WidgetServiceType =
    | 'imagePreviewService'
    | 'modalService'
    | 'cartService'
    | 'collectionService'
    | 'productDetailService'
    | 'bomService'
    | 'logService'
    | 'unitService';
export type WidgetFnType = () => Promise<typeof import('*.vue')>;
export type WidgetServiceFnType = () => Promise<any>;
