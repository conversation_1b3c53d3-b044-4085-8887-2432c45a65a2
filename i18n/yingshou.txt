970自动核销

sfa.crm_setting.accountreceivable.is_open_ar_quick_rule_title,快捷应收自动化规则
sfa.crm_setting.accountreceivable.is_open_ar_quick_rule_desc1,面向销售人员的极简应收创建，启用后，销售人员可在合同界面一键触发极简的应收创建流程。他们仅需在系统弹出的简易窗口中输入核心应收信息（如金额、分期、日期等）。系统将基于此处设置的规则完成自动化创建。
sfa.crm_setting.accountreceivable.open_ar_quick_rule_mapping_title,设置字段映射
sfa.crm_setting.accountreceivable.open_ar_quick_rule_create_rule_title,创建规则
sfa.crm_setting.accountreceivable.open_ar_quick_rule_sales_order_label,#$#SalesOrderObj.attribute.self.display_name#$#
sfa.crm_setting.accountreceivable.open_ar_quick_rule_contract_label,#$#SaleContractObj.attribute.self.display_name#$#
sfa.crm_setting.accountreceivable.open_ar_quick_rule_add_max_tip,最多创建10条规则
sfa.crm_setting.accountreceivable.open_ar_quick_rule_prop_name,规则名称
sfa.crm_setting.accountreceivable.open_ar_quick_rule_prop_condition,适用条件
sfa.crm_setting.accountreceivable.open_ar_quick_rule_prop_max_period_count,最大分期数
sfa.crm_setting.accountreceivable.open_ar_quick_rule_prop_allow_min_split,允许最小分摊
sfa.crm_setting.accountreceivable.open_ar_quick_rule_prop_add_key_info,添加关键信息
sfa.crm_setting.accountreceivable.open_ar_quick_rule_condition.prop_text,按
sfa.crm_setting.accountreceivable.open_ar_quick_rule_condition.prop_product_id,产品名称
sfa.crm_setting.accountreceivable.open_ar_quick_rule_condition.prop_product_category,产品类别
sfa.crm_setting.accountreceivable.open_ar_quick_rule_condition.prop_min_value_label,不低于合约
sfa.vcrm.accountreceivable.ar_quick_rule.title,快捷应收
sfa.vcrm.accountreceivable.ar_quick_rule.amount_tip,已建立应收：
sfa.vcrm.accountreceivable.ar_quick_rule.period_tip,期数：
sfa.vcrm.accountreceivable.ar_quick_rule.current_amount,本次应收金额
sfa.vcrm.accountreceivable.ar_quick_rule.current_amount_min_tip,本次应收金额最少不能低于{{num}}



930应收核销

sfa.crm.action_accountsreceivablenoteobj.stepview_action_next,下一步
sfa.crm.action_accountsreceivablenoteobj.stepview_action_back,上一步
sfa.crm.action_accountsreceivablenoteobj.stepview_optionalview_title,应收选配
sfa.crm.action_accountsreceivablenoteobj.stepview_baseview_title,基本信息
sfa.crm.action_accountsreceivablenoteobj.SOURCE_TABLE_TITLE,合同已建应收
sfa.crm.action_accountsreceivablenoteobj.TARGET_TABLE_TITLE,应收项目分摊
sfa.crm.action_accountsreceivablenoteobj.SOURCE_TABLE_INPUT_TARGET_AMOUNT_LABEL,本次应收金额
sfa.crm.action_accountsreceivablenoteobj.SOURCE_TABLE_INPUT_TARGET_RATIO_LABEL,收取比例
sfa.crm.action_accountsreceivablenoteobj.SOURCE_TABLE_TIP_MSG,当前合同/订单总应收金额为合同主表金额。若合同/订单已经享受了整单折扣，明细汇总金额可能与合同/订单的总金额不完全一致，请以此金额为应收依据
sfa.crm.action_accountsreceivablenoteobj.TARGET_TABLE_HEADER_SELECT_TIP,选择产品名称，以分摊金额
sfa.crm.action_accountsreceivablenoteobj.TARGET_TABLE_HEADER_SELECT_TIP_AMOUNT,待分摊金额
sfa.crm.action_accountsreceivablenoteobj.TARGET_TABLE_ACTION_ADD_ALL,全部填入
sfa.crm.action_accountsreceivablenoteobj.TARGET_TABLE_ACTION_ADD_LINE_ALL,将余额填入
sfa.crm.action_accountsreceivablenoteobj.TARGET_TABLE_COLUMN_AMOUNT_TITLE,分摊金额
sfa.crm.action_accountsreceivablenoteobj.CHECK_TARGET_AMOUNT_REQUIRED,本次应收金额不能为空
sfa.crm.action_accountsreceivablenoteobj.CHECK_TARGET_AMOUNT_MAX,本次应收金额不能超过
sfa.crm.action_accountsreceivablenoteobj.CHECK_TARGET_RADIO_MAX,收取比例不能超过
sfa.crm.action_accountsreceivablenoteobj.CHECK_LINE_AMOUNT_MAX,输入金额不能超过剩余可分摊金额
sfa.crm.action_accountsreceivablenoteobj.CHECK_REMAINED_AMOUNT_ZERO,待分摊金额不为0，请调整产品分摊金额
sfa.crm.action_accountsreceivablenoteobj.add_from_order,由订单创建应收单
sfa.crm.action_accountsreceivablenoteobj.add_from_contract,由合同创建应收单
sfa.crm.action_accountsreceivablenoteobj.table_column_prodct_id,收费项目
sfa.crm.action_accountsreceivablenoteobj.table_column_subtotal,小计金额
sfa.crm.action_accountsreceivablenoteobj.table_column_ar_tag_amount,已建立应收金额
sfa.crm.action_accountsreceivablenoteobj.table_column_achieve_ratio,达成比例
sfa.crm.action_accountsreceivablenoteobj.table_column_remaind_amount,剩余可分摊
sfa.crm.action_accountsreceivablenoteobj.table_column_total,合计
sfa.crm.setting_accountreceivable.account_receivable_status_title,启用快捷应收：
sfa.crm.setting_accountreceivable.account_receivable_status_opentip,启用应收选配交互，影响用户操作习惯，请慎重开启
sfa.crm.setting_accountreceivable.account_receivable_status_closetip,已启用选配交互
sfa.crm.setting_accountreceivable.create_ar_by_objects_title,启用应收
sfa.crm.setting_accountreceivable.create_ar_by_objects_desc1,应收单可以基于“合同”、“订单”以及“结算单”三种不同的业务来源进行创建。在应收单拉取创建的过程中，系统将提供选项菜单。您可以按需开启或关闭菜单项，以满足具体业务需求
sfa.crm.setting_accountreceivable.create_ar_by_objects_opt_from_order,允许订单创建应收
sfa.crm.setting_accountreceivable.create_ar_by_objects_opt_from_contract,允许合同创建应收
sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_account,基于客户匹配核销规则
sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_order,基于订单匹配核销规则
sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_amount,根据金额进行匹配
sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_currency,根据币种进行匹配
sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_date,根据日期进行匹配
sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_required,请至少保留一条规则
sfa.crm.setting_accountreceivable.account_receivable_status_desc1,启用应收后新增应收单核销单销售发票对象
sfa.crm.setting_accountreceivable.account_receivable_status_desc2,如果已启用客户账户启用应收后回款不再关联账户未关联账户的回款可核销应收
sfa.crm.setting_accountreceivable.account_receivable_status_desc3,当启用返利后返利金额将在应收金额中扣减并在订单中做平均分摊
sfa.crm.detail_accountreceivable.complete_label,完成时间：
sfa.crm.detail_accountreceivable.complete_title,完成应收任务
sfa.crm.detail_accountreceivable.complete_placeholder,请选择完成时间