const path = require('path');
module.exports = {
  account: {
    //spu，开启价目表、促销
    // upstreamEa: '80736',
    // upstreamUsername: '***********',
    // upstreamPassword: '1234qwer',
    // ea: '59769',
    // username: '***********',
    // password: 'b123456'
    // sku多单位
    // upstreamEa: '81028',
    // upstreamUsername: '***********',
    // upstreamPassword: '1234qwer',
    // ea: '59769',
    // username: '***********',
    // password: 'b123456'

    // upstreamEa: 'jbdg168',
    // upstreamUsername: '***********',
    // upstreamPassword: 'jbdg1234',
    // ea: '0',
    // username: '***********',
    // password: '1234567'

    // upstreamEa: 'fktest6012',
    // upstreamUsername: '***********',
    // upstreamPassword: 'fktest6012',
    // ea: '5806699',
    // username: '***********',
    // password: 'yipishang1',

    // upstreamEa: 'fktest1400',
    // upstreamUsername: '***********',
    // upstreamPassword: '123456zxc',
    // ea: '539161',
    // username: '***********',
    // password: 'a123456'

    // upstreamEa: 'fktest6011',
    // upstreamUsername: '***********',
    // upstreamPassword: 'fktest6011',
    // ea: '',
    // username: '***********',
    // password: 'fktest6011'

    // upstreamEa: 'fktest1395',
    // upstreamUsername: '***********',
    // upstreamPassword: '123321qwe',
    // ea: '539161',
    // username: '***********',
    // password: 'jbdg1234',

    // upstreamEa: '81146',
    // upstreamUsername: '***********',
    // upstreamPassword: '123321qwe',
    // ea: '',
    // username: '***********',
    // password: '81146',

    // upstreamEa: '83050',
    // upstreamUsername: '11255555555',
    // upstreamPassword: '123456qwe',
    // ea: '0',
    // username: '***********',
    // password: 'wuzuhu83050'

    // ea: '',
    // username: '***********',
    // password: 'a123456',
  },
  env: 'fxiaoke',
  // env: 'ceshi112',
  developDownstream: true,
  appId: 'FSAID_11490c84',
  macOS: true,
  watchFile: false,
  project: {
    // 'vcrm-dist': {
    //         enable: true,
    //         onlinePath: '/vcrm-dist/',
    //         localPath: path.join(__dirname, '../vcrm/dev')
    //     },
    'crm-dist': {
        enable: true,
        onlinePath: '/crm-dist/',
        localPath: path.join(__dirname, '../crm/crm2')
    },
  },

  // 是否是无租户，无租户企业用不了mock登录，只能登录企业后，把需要的cookie粘贴过来
  // 无需使用时，将其注释或者ERInfo为空
  staticCookie: {
    // 金杯
    // guid: '727b756b-2c3d-9903-7b7a-************',
    // ERInfo: 'er_02dae9aafe724e579786f2bff6ea3afa_0801140837_726805',
    // CRInfo: 'er_02dae9aafe724e579786f2bff6ea3afa_0801140837_726805',
    // ERUpstreamEa: 'jbdg168',

    // //
    // guid: '3006d866-65de-780b-8e3f-e1928e332306',
    // ERInfo: 'er_817ee10d94ce42698a1390fc40e56dcf_1015140743_799841',
    // CRInfo: 'er_817ee10d94ce42698a1390fc40e56dcf_1015140743_799841',
    // ERUpstreamEa: 'fktest6012',

    // guid: 'dff75b13-a466-4091-81b0-e335b5018b02',
    // ERInfo: 'er_ec069fb2bd534591b7b4ec1b082ed28b_1112111609_799839',
    // CRInfo: 'er_ec069fb2bd534591b7b4ec1b082ed28b_1112111609_799839',
    // ERUpstreamEa: 'fktest6011',

    // guid: '01246564-3c57-f66e-eb41-919667ccd55d',
    // ERInfo: 'er_7c0330499e444309816c39aacd2db51e_0424203758_815996',
    // CRInfo: 'er_7c0330499e444309816c39aacd2db51e_0424203758_815996',
    // ERUpstreamEa: '815996_sandbox',

    // guid: '2e690ed5-215c-67cd-104f-************',
    // ERInfo: 'er_690deddb90ba44d28a0ec6039ac46d91_0617184745_83050',
    // CRInfo: 'er_690deddb90ba44d28a0ec6039ac46d91_0617184745_83050',
    // ERUpstreamEa: '83050',

    guid: '9d94074d-c398-4eb9-93be-b584e62b5867',
    ERInfo: 'er_f9946537a6bd416186a93abc0243ed3d_0811115605_801368',
    CRInfo: 'er_f9946537a6bd416186a93abc0243ed3d_0811115605_801368',
    ERUpstreamEa: 'shuhua',
  },

};

