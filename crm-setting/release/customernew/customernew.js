define("crm-setting/customernew/customernew",["crm-modules/common/util","crm-modules/new/common/common"],function(t,e,o){var r=[{text:$t("转移"),className:"j-transfer",index:1},{text:$t("合并"),className:"j-merge",index:2}],n=[{width:234,data:"name",fixed:!0,fixedIndex:1,render:function(t,e,o){return(o.is_remind_recycling?'<span class="crm-tag-berecovery-list" data-title="'+$t("即将收回")+'">'+$t("即将收回")+"</span>":"")+'<a href="javascript:;" title="'+o.name+'">'+t+"</a>"}}],a=(t("crm-modules/common/util"),t("crm-modules/new/common/common")),t=a.extend({options:{apiname:"AccountObj"},getTableConfig:function(){return{batchBtns:r,customColumns:n}},getExtendAttribute:function(){return{extend_attribute:"customercrmmgr"}},getDetailParams:function(){return{detailFrom:3}},getColumns:function(){var t=a.prototype.getColumns.apply(this,arguments),e=_.findWhere(t,{data:"high_seas_id"});return e&&(e.showLookupText=!0),t},events:{"click .j-merge":"onMerge","click .j-transfer":"onTransfer"},onTransfer:function(){this.doAction("transfer",{batch:!0,cids:this.getRemberIds()})},onAdd:function(){this.doAction("add",{showDetail:!0,from:3,show_type:"full"})},onMerge:function(){var t=this;CRM.api.merge_object({$el:t.$el,ids:_.pluck(data,"_id"),type:2,success:function(){t.refresh()}})},getImportInTabs:function(){return[{title:this.get("displayName"),downUrl:"/EM1HCRM/Customer/GetCustomerExcelTemplate",downParam:{CanImportOwner:!0,TargetType:2},importUrl:"/EM1HCRM/Customer/ImportCustomerList",importAgainUrl:"/EM1HCRM/Customer/ImportCustomerListForUpdatePropertys",noChecker:!0,type:"customer",postData:{TargetType:2},showCustomerTip:!0,importObj:"customer"},{title:$t("crm.关联联系人"),downUrl:"/EM1HCRM/Contact/GetContactExcelTemplateRelationCustomer",importUrl:"/EM1HCRM/Contact/ImportContactListRelationCustomer",importAgainUrl:"/EM1HCRM/Contact/ImportContactListForUpdatePropertysRelationCustomer",postData:{IsCRMMgr:!0,TargetType:1},importObj:"key"},{title:$t("销售记录"),downUrl:"/EM1HCRM/SaleEvent/GetSaleEventExcelTemplate",downParam:{Type:1},postData:{Type:1,TargetType:2},importUrl:"/EM1HCRM/SaleEvent/ImportSaleEventList",importAgainUrl:"/EM1HCRM/SaleEvent/ImportSaleEventListForUpdatePropertys",noImportTool:!0},{title:$t("客户和关联联系人"),downUrl:"/EM1HCRM/MultiImport/GetCustomerAndContactExcelTemplate",importUrl:"/EM1HCRM/MultiImport/ImportCustomerListAndContactList",noChecker:!0,type:"customer",importAgainUrl:"/EM1HCRM/MultiImport/ImportCustomerListAndContactListForUpdatePropertys",noImportTool:!0,showCustomerTip:!0}]},getImportOutParams:function(t){return{type:"checkbox",tabs:[{title:$t("客户相关信息"),value:"",checked:!0,disabled:!0},{title:$t("销售记录"),value:"SaleEvent"}]}}});o.exports=t});