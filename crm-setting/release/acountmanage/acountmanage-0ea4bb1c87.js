function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,_toPropertyKey(o.key),o)}}function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/acountmanage/acountmanage",["./components/main"],function(t,e,n){var o=t("./components/main"),r=Vue.extend({template:'\n\t\t\t<div>\n \t\t\t\t<div class="crm-tit">\n\t\t\t\t\t<h2>\n\t\t\t\t\t\t<span class="tit-txt">\n\t\t\t\t\t\t\t{{$t("客户账户管理")}}\n\t\t\t\t\t\t\t<a class="crm-doclink" href="https://www.fxiaoke.com/mob/guide/crmdoc/src/4-4%E5%AE%A2%E6%88%B7%E8%B4%A6%E6%88%B7.html" target="_blank"></a>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</h2>\n\t\t\t\t</div>\n\t        \t<div class="crm-module-con">\n\t\t\t\t\t<Main></Main>\n\t\t\t\t</div>\n\t\t\t</div>'});n.exports=_createClass(function t(e){_classCallCheck(this,t),this.app=new r({name:"App",el:e.wrapper.append("<div></div>").children()[0],components:{Main:o}})},[{key:"destroy",value:function(){this.app.$destroy()}}])});
define("crm-setting/acountmanage/components/authorized",[],function(t,n,e){var i=FS.crmUtil;e.exports={template:'\n\t\t<div class="basic-info">\n\t\t\t<div class="basic-info-wrap" ref="configWrap">\n\t\t\t\t<div class="column-item">\n\t\t\t\t\t<template v-if="!dConfig.account_auth_enable">\n\t\t\t\t\t\t<label class="label">{{$t("账户授权")}}：</label>\n\t\t\t\t\t\t<span class="switch-sec" @click="_onSetAccountAuth"></span>\n\t\t\t\t\t</template>\n\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t<label class="label">{{$t("账户授权")}}：</label>\n\t\t\t\t\t\t{{$t("已开启")}}&nbsp;&nbsp;{{$t("已完成初始化")}}\n\t\t\t\t\t\t<span class="authorized-tip">\n\t\t\t\t\t\t\t(\n\t\t\t\t\t\t\t\t{{ $t(\'crm.acountmanage.authorized.tip\') }} \n\t\t\t\t\t\t\t\t<a href="/XV/UI/manage#crmmanage/=/module-tradeconfigure/key-is_payment_enter_account_enable" target="_blank">{{ $t(\'点击这里\') }}</a>\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</template>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div\n\t\t\t\tv-if="dConfig.account_auth_enable"\n\t\t\t\tclass="account-authorization-list"\n\t\t\t\t:style="{top: listTop + \'px\'}">\n\t\t\t</div>\n\t\t</div>',name:"AuthorizedEntry",props:{config:{type:Object,default:{}}},data:function(){return{dConfig:{},listTop:0}},mounted:function(){var t=this;this.$nextTick(function(){t.renderList()})},created:function(){this.$set(this,"dConfig",this.config)},destroyed:function(){var t,n;null!=(t=this.list)&&null!=(n=t.destroy)&&n.call(t),this.list=null},methods:{renderList:function(){var n=this;this.dConfig.account_auth_enable&&(this.listTop=this.$refs.configWrap.clientHeight,t.async("crm-modules/page/list/list",function(t){n.list=new t({apiname:"FAccountAuthorizationObj",wrapper:$(".account-authorization-list")}),n.list.render()}))},_onSetAccountAuth:function(){var e=this,a=$t("设置失败请联系纷享客服"),t=i.confirm($t("是否确认开启入账授权"),$t("提示"),function(){t.destroy(),i.FHHApi({url:"/EM1HNCRM/API/v1/object/account_auth/service/enable_account_auth",data:{},success:function(t){var n=t.Result;0===n.StatusCode?0===(enableStatus=t.Value.enableStatus)||3===enableStatus?i.alert(t.Value.message||a):(i.remind(1,$t("设置成功")),1===enableStatus&&i.alert($t("crm.系统检测客户太多系统预设在次日凌晨开启")),e.dConfig.account_auth_enable=!0,e.$nextTick(function(){e.renderList()}),e.$emit("updateConfig",{account_auth_enable:!0})):i.alert(n.FailureMessage||a)}},{errorAlertModel:1})})}}}});
define("crm-setting/acountmanage/components/basiccomp",["./basicoldcomp","./basicnewcomp"],function(t,n,o){var c=t("./basicoldcomp"),t=t("./basicnewcomp");o.exports={template:'\n\t\t<new-basic-comp\n\t\t\tv-if="isNewTpl"\n\t\t\t:config="config"\n\t\t\t@updateConfig="updateConfig">\n\t\t</new-basic-comp>\n\t\t<old-basic-comp\n\t\t\tv-else\n\t\t\t:config="config"\n\t\t\t@updateConfig="updateConfig">\n\t\t</old-basic-comp>',name:"BasicComp",components:{OldBasicComp:c,NewBasicComp:t},props:{config:{type:Object,default:{}}},data:function(){return{}},computed:{isNewTpl:function(){var t=this.config.customer_account_enable,n=this.config.new_customer_account_enable;return n||!t&&!n}},created:function(){},methods:{updateConfig:function(t){this.$emit("updateConfig",t)}}}});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,r=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)),r}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}function asyncGeneratorStep(t,e,n,r,o,c,i){try{var a=t[c](i),l=a.value}catch(t){return void n(t)}a.done?e(l):Promise.resolve(l).then(r,o)}function _asyncToGenerator(a){return function(){var t=this,i=arguments;return new Promise(function(e,n){var r=a.apply(t,i);function o(t){asyncGeneratorStep(r,e,n,o,c,"next",t)}function c(t){asyncGeneratorStep(r,e,n,o,c,"throw",t)}o(void 0)})}}define("crm-setting/acountmanage/components/basicnewcomp",[],function(t,e,n){var c=FS.crmUtil,i="is_customer_account_exceed_enable";n.exports={template:'\n\t\t<div class="basic-info">\n\t\t\t<div class="basic-info-wrap" ref="configWrap">\n\t\t\t\t<div class="crm-intro">\n\t\t\t\t\t<h3>{{$t("说明")}}：</h3>\n\t\t\t\t\t<ul>\n\t\t\t\t\t\t<li>1. {{$t("客户账户管理广泛用于快消制造消费电子等行业")}}</li>\n\t\t\t\t\t\t<li>2. {{$t("启用客户账户后预设账户账户收支流水客户账户余额等对象")}}</li>\n\t\t\t\t\t\t<li>3. {{$t("启用客户账户后可创建多个账户")}}</li>\n\t\t\t\t\t\t<li>4. {{$t("当启用账户校验功能后可配置规则实现账户余额占用和扣减能力")}}</li>\n\t\t\t\t\t\t<li>5. {{$t("一旦开启客户账户将无法关闭")}}</li>\n\t\t\t\t\t\t<li>6. {{$t("对于客户账户的老客户可同时使用新老客户账户能力")}}</li>\n\t\t\t\t\t</ul>\n\t\t\t\t</div>\n\t\t\t\t<template v-if="!dConfig.new_customer_account_enable">\n\t\t\t\t\t<div class="column-item" v-if="dConfig.new_customer_account_status === \'3\'">\n\t\t\t\t\t\t<label class="label">{{$t("账户管理")}}：</label>{{$t("开启中")}}&nbsp;&nbsp;{{$t("次日凌晨1点自动开启")}}\n\t\t\t\t\t</div>\n\t\t\t\t\t<p class="column-item" v-else>\n\t\t\t\t\t\t<label class="label">{{$t("是否开启账户管理")}}</label>\n\t\t\t\t\t\t<span class="switch-sec" @click="_onSetFundAccount"></span>\n\t\t\t\t\t</p>\n\t\t\t\t</template>\n\t\t\t\t<template v-else>\n\t\t\t\t\t<div class="column-item">\n\t\t\t\t\t\t<label class="label">{{$t("账户管理")}}：</label>\n\t\t\t\t\t\t{{$t("已开启")}}&nbsp;&nbsp;{{$t("已完成初始化")}}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="column-item no-padding">\n\t\t\t\t\t\t<label class="label"></label>\n\t\t\t\t\t\t<div class="switch-wrapper">\n\t\t\t\t\t\t\t<fx-switch\n\t\t\t\t\t\t\t\tv-model="customerAccountExceedEnable"\n\t\t\t\t\t\t\t\tactive-value="2"\n\t\t\t\t\t\t\t\tinactive-value="0"\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t\t:disabled="customerAccountExceedEnable === \'2\'"\n\t\t\t\t\t\t\t\t:before-change="_onSetCustomerAccountExceed">\n\t\t\t\t\t\t\t</fx-switch>\n\t\t\t\t\t\t\t<span class="switch-label">{{ $t(\'账户余额支持扣减为负数\') }}</span>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="crm-guide-info-wrap">\n\t\t\t\t        <ul>\n\t\t\t\t            <li>{{$t("已成功启用客户账户，您可在订单相关列表中，可基于订单创建账户支出流水，使用账户余额抵扣货款。")}}</li>\n\t\t\t\t            <li>{{$t("如果需要其他能力，请参考一下指引：")}}</li>\n\t\t\t\t            <li>• {{$t("新建订单时，希望系统按规则自动扣减账户余额，则可启用【校验规则】，并配置“直接扣减”、“校验扣减规则”")}}</li>\n\t\t\t\t            <li>• {{$t("新建订单时，希望手动选择账户金额扣减，则可启用【账户授权】能力，并新建销售订单的“支出授权”并完成初始化，然后在【销售订单】布局中配置【客户账户】组件。")}}</li>\n\t\t\t\t            <li>• {{$t("除了订单对象，其他预设或自定义对象也可以扣减账户余额，或入账到客户账户，请在【账户授权】中，创建对应的支出授权、入账授权。")}}</li>\n\t\t\t\t            <li class="li-flex-wrap">\n\t\t\t\t                {{$t("注：当启用返利时，系统会自动启用【账户授权】并预设订单的支出授权，请在订单布局中配置【客户账户组件】即可。")}}\n\t\t\t\t                <fx-link :underline="false" type="standard" style="font-size: 13px;" @click="jumpPage(\'/XV/UI/manage#crmmanage/=/module-sysobject/api_name-SalesOrderObj/child_type-layout\')">{{$t("去配置订单布局")}}</fx-link>\n\t\t\t\t            </li>\n                        </ul>\n                    </div>\n\t\t\t\t</template>\n\t\t\t</div>\n\t\t</div>',name:"NewBasicComp",props:{config:{type:Object,default:{}}},data:function(){return{dConfig:{},customerAccountExceedEnable:"0"}},created:function(){this.$set(this,"dConfig",this.config),this.getCustomerAccountExceed()},methods:{_onSetFundAccount:function(){var t=this,e=c.confirm($t("是否确认开启该客户账户开启后将无法关闭"),$t("提示"),function(){e.destroy(),t.enableCustomerAccount()})},enableCustomerAccount:function(){var r=this,o=$t("设置失败请联系纷享客服");c.FHHApi({url:"/EM1HNCRM/API/v1/object/customer_account/service/enable_customer_account",data:{},success:function(t){var e,n=t.Result;0===n.StatusCode?2===(e=t.Value.enableStatus)?(c.remind(1,$t("设置成功")),r.dConfig.new_customer_account_enable=!0,r.$emit("updateConfig",{new_customer_account_enable:!0})):3===e?(c.remind(1,$t("设置成功")),c.alert($t("crm.系统检测客户太多系统预设在次日凌晨开启")),r.dConfig.new_customer_account_status=String(e),r.$emit("updateConfig",{new_customer_account_status:String(e)})):c.alert(t.Value.message||o):c.alert(n.FailureMessage||o)}},{errorAlertModel:1})},getCustomerAccountExceed:function(){var o=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){var e,n,r;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e={keys:[i]},t.next=3,o.fetch({url:"/EM1HNCRM/API/v1/object/crm_config/service/get_config_values",data:e});case 3:e=t.sent,n=(null==e||null==(n=e.Value)?void 0:n.values)||[],(r=n.find(function(t){return t.key===i}))&&(o.customerAccountExceedEnable=r.value);case 7:case"end":return t.stop()}},t)}))()},_onSetCustomerAccountExceed:function(){var t=this,e="\n\t\t\t\t\t".concat($t("crm.setting.tradeconfigure.confirm_open",null,"确认开启"),'"').concat($t("账户余额支持扣减为负数"),'"?,\n\t\t\t\t\t').concat($t("crm.setting.tradeconfigure.warn_cannot_closed",null,"一旦启用，将无法停用")),n=$t("提示"),r=c.confirm(e,n,function(){r.destroy(),t.enableCustomerAccountExceed()})},enableCustomerAccountExceed:function(){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e={key:i,value:"2",oldValue:"0"},t.prev=1,t.next=4,n.fetch({url:"/EM1HNCRM/API/v1/object/crm_config/service/set_config_value",data:e});case 4:t.next=10;break;case 6:throw t.prev=6,t.t0=t.catch(1),CRM.util.alert(t.t0.message),new Error(t.t0);case 10:n.customerAccountExceedEnable="2";case 11:case"end":return t.stop()}},t,null,[[1,6]])}))()},fetch:function(t){var r=t.url,o=t.data,c=t.otherOpts;return new Promise(function(e,n){CRM.util.showLoading_tip(),CRM.util.FHHApi({url:r,data:o,success:function(t){CRM.util.hideLoading_tip(),0===t.Result.StatusCode?e(t):n(new Error(null==t||null==(t=t.Result)?void 0:t.FailureMessage))},error:function(){CRM.util.hideLoading_tip(),n(new Error($t("网络异常，请稍后重试")))}},_objectSpread({errorAlertModel:1},c))})},jumpPage:function(t,e){t&&("self"===e?window.location.hash=t:(e=window.location.origin,window.open("".concat(e).concat(t))))}}}});
define("crm-setting/acountmanage/components/basicoldcomp",[],function(t,e,n){var l=FS.crmUtil;n.exports={template:'\n\t\t<div class="basic-info">\n\t\t\t<div class="crm-intro">\n\t\t\t\t<h3>{{$t("说明")}}：</h3>\n\t\t\t\t<ul>\n\t\t\t\t\t<li>{{$t("1客户账户是客户拥有的预存款返利信用等资产的统称用于日常销售订单的校验和结算。")}}</li>\n\t\t\t\t\t<li>{{$t("crm.开通账户后系统增加对象")}}</li>\n\t\t\t\t\t<li>{{$t("crm.开通账户后销售订单增加结算方式")}}</li>\n\t\t\t\t\t<li>{{$t("4开启客户账户后回款增加预存款返利预存款")}}+{{$t("返利等回款方式可通过客户账户余额进行回款。")}}</li>\n\t\t\t\t\t<li>{{$t("crm.开启账户后可启用订单返利")}}</li>\n\t\t\t\t\t<li>{{$t("crm.开通账户后可进行授信管理")}}</li>\n\t\t\t\t\t<li>{{$t("7注意一旦开启客户账户将无法关闭。")}}</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t\t<div class="container">\n\t\t\t\t<template v-if="!dConfig.customer_account_enable">\n\t\t\t\t\t<p class="column-item">\n\t\t\t\t\t\t<label class="label">{{$t("是否开启客户账户")}}</label>\n\t\t\t\t\t\t<span class="switch-sec" @click="_onSetCustomerAccount"></span>\n\t\t\t\t\t</p>\n\t\t\t\t</template>\n\t\t\t\t<template v-else>\n\t\t\t\t\t<div class="column-item">\n\t\t\t\t\t\t<label class="label">{{$t("客户账户")}}：</label>{{$t("已开启")}}&nbsp;&nbsp;{{$t("已完成初始化")}}\n\t\t\t\t\t</div>\n\t\t\t\t\t<p class="tip">{{$t("客户账户已开启建议先去后台设置")}}<a class="crm-link" href="http://www.fxiaoke.com/XV/Home/Index#crm/setting/myobject" target="_blank">{{$t("crm.客户账户")}}</a>{{$t("、")}}<a class="crm-link" href="http://www.fxiaoke.com/XV/Home/Index#crm/setting/myobject" target="_blank">{{$t("crm.预存款")}}</a>{{$t("、")}}<a class="crm-link" href="http://www.fxiaoke.com/XV/Home/Index#crm/crm/setting/myobject" target="_blank">{{$t("crm.返利")}}</a>{{$t("、")}}<a class="crm-link" href="http://www.fxiaoke.com/XV/Home/Index#crm/crm/setting/myobject" target="_blank">{{$t("信用")}}</a>{{$t("等对象的字段布局信息")}}</p>\n\t\t\t\t\t<p class="column-item">\n\t\t\t\t\t\t<label class="label">{{$t("同时新建回款")}}</label>\n\t\t\t\t\t\t<span class="switch-sec" :class="{on: dConfig.create_payment_enable}" @click="_onSetCreatePayment"></span>\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class="tip">{{$t("crm.对预付订单保存成功后默认新建")}}</p>\n\t\t\t\t\t<p class="column-item flex-align-center">\n\t\t\t\t\t\t<label class="label">{{$t("控制强度")}}：</label>\n\t\t\t\t\t\t<fx-radio-group v-model="orderLimit" @change="_onSetLimit">\n\t\t\t\t\t\t\t<fx-radio label="1">{{$t("终止操作")}}</fx-radio>\n\t\t\t\t\t\t\t<fx-radio label="2">{{$t("预警提示")}}</fx-radio>\n\t\t\t\t\t\t\t<fx-radio label="3">{{$t("不予控制")}}</fx-radio>\n\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t</p>\n\t\t\t\t\t<p class="tip">{{$t("结算方式为预付、赊销时，提交订单会判断客户账户是否足够。如果不够，可设置终止操作、预警提示或不予控制。")}}</p>\n\t\t\t\t</template>\n\t\t\t</div>\n\t\t\t<div class="column-item">\n\t\t\t\t<div class="label">{{$t("启用订单返利")}}</div>  \n\t\t\t\t<ul class="font-second">\n\t\t\t\t<li>{{$t("根据以下步骤进行设置")}}</li>\n\t\t\t\t<li>{{$t("1）在【流程管理】-【工作流管理】中，启用“")}}<a class="crm-link" href="#crm/setting/workflow" target="_blank">{{$t("订单自动生成返利")}}</a>{{$t("”并设置条件，默认条件为：订单金额>=2000，订单状态=“已收货”。")}}</li>\n\t\t\t\t<li>{{$t("2）在【流程管理】-【自定义函数】中启用")}}<a class="crm-link" href="#crm/setting/myfunction" target="_blank">{{$t("订单自动生成返利")}}</a>{{$t("”并设置条件，默认为订单金额的5%")}}。</li>\n\t\t\t\t<li>{{$t("3）在【流程管理】-【工作流管理】中启用")}}<a class="crm-link" href="#crm/setting/workflow" target="_blank">{{$t("返利生成通知")}}</a>{{$t("”并设置条件，默认只通知管理员。")}}</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t</div>',name:"OldBasicComp",props:{config:{type:Object,default:{}}},data:function(){return{dConfig:{},orderLimit:"1"}},created:function(){this.$set(this,"dConfig",this.config),this.orderLimit=this.dConfig.order_limit},methods:{_onSetCustomerAccount:function(){var t=this,e=l.confirm($t("是否确认开启该客户账户开启后将无法关闭"),$t("提示"),function(){e.destroy(),t.enableCustomerAccount()})},enableCustomerAccount:function(){var a=this,i=$t("设置失败请联系纷享客服");l.FHHApi({url:"/EM1HNCRM/API/v1/object/customer_account/service/enable_customer_account",data:{},success:function(t){var e,n=t.Result;0===n.StatusCode?0===(e=t.Value.enableStatus)||3===e?l.alert(t.Value.message||i):(l.remind(1,$t("设置成功")),1===e&&l.alert($t("crm.系统检测客户太多系统预设在次日凌晨开启")),a.dConfig.customer_account_enable=!0,a.dConfig.create_payment_enable=!0,a.$emit("updateConfig",{customer_account_enable:!0,create_payment_enable:!0})):l.alert(n.FailureMessage||i)}},{errorAlertModel:1})},_onSetCreatePayment:function(){var e=this,t=this.dConfig.create_payment_enable?"2":"1";l.FHHApi({url:"/EM1HNCRM/API/v1/object/customer_account/service/update_payment_switch",data:{createPaymentSwitch:t},success:function(t){t=t.Result;0===t.StatusCode?(l.remind(1,$t("设置成功")),e.dConfig.create_payment_enable=!e.dConfig.create_payment_enable,e.$emit("updateConfig",{create_payment_enable:e.dConfig.create_payment_enable})):l.alert(t.FailureMessage||t.message||$t("设置失败请联系纷享客服"))}},{errorAlertModel:1})},_onSetLimit:function(e){var n=this;l.FHHApi({url:"/EM1HNCRM/API/v1/object/customer_account/service/update_create_order_limit_config",data:{createOrderLimitConfig:e},success:function(t){t=t.Result;0===t.StatusCode?(n.dConfig.order_limit=e,l.remind(1,$t("设置成功")),n.$emit("updateConfig",{order_limit:e})):(l.alert(t.FailureMessage||t.message||$t("设置失败请联系纷享客服")),n.orderLimit=n.dConfig.order_limit)}},{errorAlertModel:1})}}}});
function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _iterableToArrayLimit(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,r,a,c,l=[],o=!0,s=!1;try{if(a=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;o=!1}else for(;!(o=(i=a.call(n)).done)&&(l.push(i.value),l.length!==e);o=!0);}catch(t){s=!0,r=t}finally{try{if(!o&&null!=n.return&&(c=n.return(),Object(c)!==c))return}finally{if(s)throw r}}return l}}function _arrayWithHoles(t){if(Array.isArray(t))return t}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var n;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(n="Object"===(n={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(t,e):void 0}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=Array(e);n<e;n++)i[n]=t[n];return i}define("crm-setting/acountmanage/components/checkrulecomp",[],function(t,e,n){var a=FS.crmUtil;n.exports={template:'\n\t\t<div class="checkrule-info">\n\t\t\t<div class="checkrule-info-wrap" ref="configWrap">\n\t\t\t\t<template v-if="!dConfig.account_check_enabel">\n\t\t\t\t\t<p class="column-item">\n\t\t\t\t\t\t<label class="label" style="width: 200px;">{{$t("是否启用客户账户校验功能")}}</label>\n\t\t\t\t\t\t<span class="switch-sec" @click="_onSetAccountCheck"></span>\n\t\t\t\t\t</p>\n\t\t\t\t</template>\n\t\t\t\t<template v-else>\n\t\t\t\t\t<div class="column-item checkrule-extra-style">\n\t\t\t\t\t\t<div class="checkrule-info-title">\n\t\t\t\t\t\t\t<label class="label">{{$t("客户账户校验功能")}}：</label>\n\t\t\t\t\t\t\t<div class="function">\n\t\t\t\t\t\t\t\t<span>{{$t("已开启")}}&nbsp;&nbsp;{{$t("已完成初始化")}}</span>\n\t\t\t\t\t\t\t\t<div class="only-cancel-credit">\n\t\t\t\t\t\t\t\t\t<div style="display:flex;align-items: center;">\n\t\t\t\t\t\t\t\t\t\t<div style="margin-right:5px">{{$t(\'account_manage.checkrule.cancel_credit_new\')}}</div>\n\t\t\t\t\t\t\t\t\t\t<fx-button size="small" type="text" @click="handleSettingClick">{{$t(\'设置\')}}</fx-button>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\t\t\t\t\t\t\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="deduction-info-content" v-if="!dConfig.direct_reduce_force_check_amount">\n\t\t\t\t\t\t\t<img class="icon icon-info" src="https://a9.fspage.com/FSR/weex/dht/web/huojian.png">\n\t\t\t\t\t\t\t<p>{{$t("direct_deduction.verification.update_desc")}}</p>\n\t\t\t\t\t\t\t<fx-link type="standard" size="small" :underline="false" @click="handleUpgrade">{{$t("click_to_upgrade")}}</fx-link>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</template>\n\t\t\t</div>\n\t\t\t<div v-if="dConfig.account_check_enabel"\n\t\t\t\tclass="checkrule-list"\n\t\t\t\t:style="{top: listTop + \'px\'}">\n\t\t\t</div>\n\n\t\t\t<fx-dialog\n\t\t\t\t:visible.sync="isShowConfirmPopup"\n\t\t\t\tref="confirmPopup"\n\t\t\t\tsize="small"\n\t\t\t\tshowFullscreenSwitch\n\t\t\t\t:title="$t(\'提示\')"\n\t\t\t\tzIndex="3000"\n\t\t\t\tclass="checkrule-dialog"\n\t\t\t\tclose-on-click-outside="false"\n\t\t\t>\n\t\t\t\t<div class="dialog-content">\n\t\t\t\t\t<p class="dialog-content-title">{{$t(\'direct_deduction.verification.upgrade_title\')}}</p>\n\t\t\t\t\t<ul class="dialog-content-desc">\n\t\t\t\t\t\t<li>• {{$t(\'direct_deduction.verification.upgrade_desc1\')}}</li>\n\t\t\t\t\t\t<li>• {{$t(\'direct_deduction.verification.upgrade_desc2\')}}</li>\n\t\t\t\t\t</ul>\n\t\t\t\t</div>\n\t\t\t\t<span slot="footer" class="dialog-footer">\n\t\t\t\t\t<fx-button type="primary" @click="onConfirmUpgrade" size="small">\n\t\t\t\t\t\t{{$t(\'确 定\')}}\n\t\t\t\t\t</fx-button>\n\t\t\t\t\t<fx-button @click="isShowConfirmPopup = false" size="small">{{$t(\'取 消\')}}</fx-button>\n\t\t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t\t<fx-dialog\n\t\t\t\t:visible.sync="visible"\n\t\t\t\tref="confirmPopup"\n\t\t\t\tsize="small"\n\t\t\t\tshowFullscreenSwitch\n\t\t\t\t:title="$t(\'设置\')"\n\t\t\t\tzIndex="3000"\n\t\t\t\tclass="checkrule-dialog"\n\t\t\t\tclose-on-click-outside="false"\n\t\t\t>\n\t\t\t\t<div class="dialog-content">\n\t\t\t\t\t<div style=\'color: var(--color-neutrals19, #181c25);margin-bottom: 10px;\'>{{$t(\'account_manage.checkrule.cancel_credit_new\')}}:\n\t\t\t\t\t</div>\n\t\t\t\t\t<fx-checkbox-group v-model="initValue" is-vertical>\n\t\t\t\t\t\t<fx-checkbox label="1">{{$t(\'dht_crm_account_income_flow\')}}</fx-checkbox>\n\t\t\t\t\t\t<fx-checkbox label="2">{{$t(\'dht_crm_account_record_detail\')}}</fx-checkbox>\n\t\t\t\t\t</fx-checkbox-group>\n\t\t\t\t</div>\n\t\t\t\t<span slot="footer" class="dialog-footer">\n\t\t\t\t\t<fx-button type="primary" @click="handleSetValue" size="small">\n\t\t\t\t\t\t{{$t(\'确 定\')}}\n\t\t\t\t\t</fx-button>\n\t\t\t\t\t<fx-button @click="visible = false" size="small">{{$t(\'取 消\')}}</fx-button>\n\t\t\t\t</span>\n\t\t\t</fx-dialog>\n\t\t</div>',name:"CheckRuleComp",props:{config:{type:Object,default:{}}},data:function(){return{dConfig:{},listTop:0,isShowConfirmPopup:!1,visible:!1,checked:!1,tooltipContent:"",initValue:[],ruleMap:{0:["2"],1:["1","2"],2:["1"],3:[]}}},mounted:function(){var t=this;this.$nextTick(function(){t.renderList(),t.setInitValue()})},created:function(){this.$set(this,"dConfig",this.config)},destroyed:function(){var t,e;null!=(t=this.list)&&null!=(e=t.destroy)&&e.call(t),this.list=null},methods:{renderList:function(){var e=this;this.dConfig.account_check_enabel&&(this.listTop=this.$refs.configWrap.clientHeight+8,t.async("crm-modules/page/list/list",function(t){e.list=new t({apiname:"AccountCheckRuleObj",wrapper:$(".checkrule-list")}),e.list.render()}))},_onSetAccountCheck:function(t){var r,e;this.config.new_customer_account_enable?(r=this,e=a.confirm($t("启用校验功能后系统预设校验规则确定要启用吗"),$t("提示"),function(){e.destroy();var i=$t("设置失败请联系纷享客服");a.FHHApi({url:"/EM1HNCRM/API/v1/object/customer_account/service/enable_account_check",data:{},success:function(t){var e,n=t.Result;0===n.StatusCode?0===(e=t.Value.enableStatus)||3===e?a.alert(t.Value.message||i):(a.remind(1,$t("设置成功")),1===e&&a.alert($t("crm.系统检测客户太多系统预设在次日凌晨开启")),r.dConfig.account_check_enabel=!0,r.$nextTick(function(){r.renderList()}),r.$emit("updateConfig",{account_check_enabel:!0})):a.alert(n.FailureMessage||i)}},{errorAlertModel:1})})):a.alert($t("该租户未启用客户账户模块请联系管理员开通"))},handleUpgrade:function(){this.isShowConfirmPopup=!0},onConfirmUpgrade:function(){var e=this;a.FHHApi({url:"/EM1HNCRM/API/v1/object/account_check/service/upgrade_direct_reduce_force_check_amount",data:{},success:function(t){t=t.Result;0===t.StatusCode?(e.isShowConfirmPopup=!1,e.$message({message:$t("direct_deduction.verification.upgrade_success"),type:"success"}),e.dConfig.direct_reduce_force_check_amount=!0):a.alert(t.FailureMessage||message)}},{errorAlertModel:1})},handleSettingClick:function(){this.visible=!0},setInitValue:function(){this.initValue=this.ruleMap[this.dConfig.check_rule_flow_whether_invalid_after_cancel||"0"]||[]},handleSetValue:function(){this.visible=!1;var t=this.getKeyByValue();this.handleCheckRuleFlowWhetherInvalidAfterCancel(t)},getKeyByValue:function(){for(var t=_toConsumableArray(this.initValue).sort().join(","),e=0,n=Object.entries(this.ruleMap);e<n.length;e++){var i=_slicedToArray(n[e],2),r=i[0];if(i[1].sort().join(",")===t)return r}return null},handleCheckRuleFlowWhetherInvalidAfterCancel:function(e){var n=this;a.FHHApi({url:"/EM1HNCRM/API/v1/object/crm_config/service/set_config_value",data:{key:"check_rule_flow_whether_invalid_after_cancel",value:e},success:function(t){t=t.Result;0===t.StatusCode?n.dConfig.check_rule_flow_whether_invalid_after_cancel=e:a.alert(t.FailureMessage||message)}},{errorAlertModel:1})}}}});
define("crm-setting/acountmanage/components/main",["./basiccomp","./checkrulecomp","./authorized"],function(e,t,c){var n=e("./basiccomp"),a=e("./checkrulecomp"),e=e("./authorized"),o=FS.crmUtil;c.exports={template:'\n\t\t<div class="accountmanage-wrap" v-if="isReady">\n\t\t\t<fx-tabs\n\t\t\t\tclass="accountmanage-tabs"\n\t\t\t\t:show-tab-content="false"\n\t\t\t\tv-model="activeComp"\n\t\t\t\t@tab-click="handleClick"\n\t\t\t>\n\t\t\t\t<fx-tab-pane \n\t\t\t\t\tv-for="item in tabs"\n\t\t\t\t\t:key="item.comp"\n\t\t\t\t\t:label="item.label" \n\t\t\t\t\t:name="item.comp"\n\t\t\t\t></fx-tab-pane>\n\t\t\t</fx-tabs>\n\t\t\t<component\n\t\t\t\tclass="accountmanage-content"\n\t\t\t\tv-bind:is="activeComp"\n\t\t\t\t:config="config"\n\t\t\t\t@updateConfig="updateConfig">\n\t\t\t</component>\n\t\t</div>',name:"Main",components:{BasicComp:n,CheckRuleComp:a,AuthorizedComp:e},data:function(){return{isReady:!1,tabs:[{label:$t("基本信息"),comp:"BasicComp"}],activeComp:"BasicComp",config:{customer_account_enable:null,new_customer_account_enable:null,new_customer_account_status:null,create_payment_enable:null,order_limit:"1",account_check_enabel:null,account_auth_enable:null,direct_reduce_force_check_amount:null,check_rule_flow_whether_invalid_after_cancel:null}}},watch:{"config.new_customer_account_enable":{handler:function(e){e&&this.tabs.push({label:$t("账户授权"),comp:"AuthorizedComp"},{label:$t("校验规则"),comp:"CheckRuleComp"})}}},created:function(){this.getConfig()},methods:{handleClick:function(e){},getConfig:function(){var a=this;o.waiting(),o.FHHApi({url:"/EM1HNCRM/API/v1/object/customer_account/service/query_config",data:{isAllConfig:!0},success:function(e){var c,n,t=e.Result;0==t.StatusCode?(c={29:"customer_account_enable",is_customer_account_enable:"new_customer_account_enable",create_payment_while_order_created:"create_payment_enable",create_order_limit_config:"order_limit",is_account_check_enable:"account_check_enabel",is_account_auth_enable:"account_auth_enable",direct_reduce_force_check_amount:"direct_reduce_force_check_amount",check_rule_flow_whether_invalid_after_cancel:"check_rule_flow_whether_invalid_after_cancel"},n={29:"1",is_customer_account_enable:"2",create_payment_while_order_created:"1",is_account_check_enable:"2",is_account_auth_enable:"2",direct_reduce_force_check_amount:"1",check_rule_flow_whether_invalid_after_cancel:"1"},_.each(e.Value.values,function(e){var t=c[e.key]||e.key;a.config[t]=n[e.key]?n[e.key]===e.value:e.value,"is_customer_account_enable"===e.key&&(a.config.new_customer_account_status=e.value),"check_rule_flow_whether_invalid_after_cancel"===e.key&&(a.config.check_rule_flow_whether_invalid_after_cancel=e.value)})):o.alert(t.FailureMessage||t.message||$t("设置失败请联系纷享客服")),a.isReady=!0,o.waiting(!1)}},{errorAlertModel:1})},updateConfig:function(e){var c=this;_.map(e,function(e,t){c.config[t]=e})}}}});