define("crm-setting/termbankobj/termbankobj",["crm-modules/page/list/list"],function(t,e,n){var s=t("crm-modules/page/list/list"),t=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper)},render:function(){this.el;this.initDesc()},initDesc:function(){this.initTb()},initTb:function(){var e=this,t=s.extend({operateBtnClickHandle:function(t){t=$(t.target).attr("data-action");e["".concat(t,"Handle")]&&e["".concat(t,"Handle")]()},getOptions:function(){var t=s.prototype.getOptions.apply(this,arguments);return _.extend(t,{})},parseData:function(t){var e=s.prototype.parseData.apply(this,arguments);return _.each(e.data,function(e){_.each(e.operate,function(t){"ChangeStatus"===t.action&&(t.label="1"===e.biz_status?$t("停用"):$t("启用"))})}),e},_operateHandle:function(t,e){var n=this,t=t.api_name,a=e._id,i=e.biz_status;"Edit_button_default"==t?this.edit=CRM.api.edit({apiname:e.object_describe_api_name,id:a,displayName:e.name,success:function(){n.refresh()}}):"ChangeStatus_button_default"==t?(e="1"===i?$t("termbankobj.list.confirm.stop"):$t("termbankobj.list.confirm.open"),(new Vue).$confirm(e).then(function(){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/TermBankObj/action/ChangeStatus",data:{objectDataId:a,args:{biz_status:"1"===i?"0":"1"}},success:function(t){0==t.Result.StatusCode?CRM.util.remind(1,$t("操作成功！")):CRM.util.alert(t.Result.FailureMessage)},complete:function(){n.refresh()}})})):s.prototype._operateHandle.apply(this,arguments)}});e.tb=new t({wrapper:e.$el,apiname:"TermBankObj"}),e.tb.render()}});n.exports=t});