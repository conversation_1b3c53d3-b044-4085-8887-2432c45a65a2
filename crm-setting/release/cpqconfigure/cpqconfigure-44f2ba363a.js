function asyncGeneratorStep(e,t,n,i,a,o,r){try{var s=e[o](r),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(i,a)}function _asyncToGenerator(s){return function(){var e=this,r=arguments;return new Promise(function(t,n){var i=s.apply(e,r);function a(e){asyncGeneratorStep(i,t,n,a,o,"next",e)}function o(e){asyncGeneratorStep(i,t,n,a,o,"throw",e)}a(void 0)})}}define("crm-setting/cpqconfigure/cpqconfigure",["./data"],function(n,e,t){var i=n("./data"),a=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.getLicense()},getLicense:function(){var t=this;CRM.api.get_licenses({key:["cpq_app","cpq_base_app"],cb:function(e){e.cpq_app||e.cpq_base_app||t.setVisible(),t.init()}})},setVisible:function(){i.forEach(function(e){return e.visible=!1})},init:function(e){var t=this;n.async("vcrm/sdk",function(e){e.getComponent("backstage").then(function(e){t.initView(e.default,i)})})},initView:function(e,t){new Vue({el:this.$el[0],template:'\n\t\t\t\t\t<Backstage :data="dataList" :methodList="methodList" @change="change" @click="click"></Backstage>\n\t\t\t\t',components:{Backstage:e},data:function(){return{dataList:t,methodList:{afterGetConfig:this.afterGetConfig,beforeSetConfig:this.beforeSetConfig,afterSetConfig:this.afterSetConfig}}},watch:{},computed:{},created:function(){},mounted:function(){this.initDataList()},methods:{click:function(e,t){},afterGetConfig:function(e){var n=this;"1"===e[0].find(function(e){return"cpq"===e.key}).value&&CRM.util.forEachTreeData(this.dataList[0].moduleList,function(e){var t;e.isShow=!0,"bom_temp_node"===e.key&&(e.isShow=!0),"standard_cpq"===e.key&&(e.isShow=!0),"generate_standard_bom_based_on_order"===e.key&&(e.isShow=n.getConfigByKey("standard_cpq").value),"multiplexed_bom_mode"===e.key&&(e.isShow=!0),"bom_duplicate_check"===e.key&&(t=n.getConfigByKey("generate_standard_bom_based_on_order").value,e.isShow=["1","2"].includes(t))})},showOtherConfig:function(){CRM.util.forEachTreeData(this.dataList[0].moduleList,function(e){e.isShow=!0,["generate_standard_bom_based_on_order","bom_duplicate_check"].includes(e.key)&&(e.isShow=!1)})},beforeSetConfig:function(e,t,n){var i;switch(e){case"bom_print_template_has_sub_node":n.confirmMessage=t?$t("确认开启{{name}}吗",{name:$t("报价单订单销售合同打印时只包含母件产品")}):$t("确认关闭只打印母件开关吗？");break;case"bom_duplicate_check":n.confirmMessage=t?CRM.util.getI18n(["确认开启","BOM","查重","校验","开关"])+"?":CRM.util.getI18n(["确认关闭","BOM","查重","校验","开关"])+"?";break;case"not_show_bom":n.confirmMessage=t?$t("确认开启{{name}}吗",{name:$t("crm.setting.cpq.not_show_bom",null,"选产品组合不进入选配配置页")}):$t("确认关闭{{name}}吗",{name:$t("crm.setting.cpq.not_show_bom",null,"选产品组合不进入选配配置页")})}var a=null;return null!=(i=n.setUrl)&&i.includes("save_module_status")&&(a={param:{moduleCode:e,openStatus:t?"1":"0",tenantId:CRM.enterpriseId}},"generate_standard_bom_based_on_order"===e)&&(a.param.openStatus=t),a},afterSetConfig:function(e,t,n){switch(e){case"generate_standard_bom_based_on_order":var i=this.getConfigByKey("bom_duplicate_check");i&&(i.isShow="0"!=t);break;case"cpq":this.showOtherConfig()}},getConfigByKey:function(e){return this._allConfig[e]},initDataList:function(){var t=this;this._allConfig={},this.dataList.forEach(function(e){CRM.util.forEachTreeData(e.moduleList,function(e){t._allConfig[e.key]=e})})},change:function(e){e.type;return _asyncToGenerator(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:case"end":return e.stop()}},e)}))()}}})},destroy:function(){}});t.exports=a});
define("crm-setting/cpqconfigure/data",[],function(e,t,l){var s=[{domId:"level0",title:$t("CPQ配置"),moduleId:"cpqConfig",autoSetConfig:!0,moduleList:[{type:"switch",title:$t("CPQ开启开关"),key:"cpq",value:!1,displayCount:3,isDisabled:!1,isShow:!0,enableClose:!1,render:null,radioOptions:[],confirmMessage:$t("crm.确认开启CPQ吗"),getUrl:"",setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:$t("该开关一旦开启，不可关闭。"),list:[]},{title:$t("开启该开关后，报价单和订单可以配置复杂产品和价格，其它业务模块暂时不支持。"),list:[]},{title:$t("cpq开关说明3"),list:[{title:$t("cpq开关说明3_1")},{title:$t("cpq开关说明3_2")},{title:$t("cpq开关说明3_2_1")},{title:$t("cpq开关说明3_2_2")}]},{title:$t("开启该开关后，价目表暂时只能设置产品组合的折扣/价格，无法设置其子产品的折扣/价格。"),list:[{title:$t("如果在报价单和订单中选择了产品组合并配置了其子产品，最终产品组合显示价格按照价目表中的折扣折算。")},{title:$t("比如，熊猫电脑 价格5,000元，价目表中折扣设置80%。")+$t("cpq举例说明")}]}]},{isShow:!1,type:"switch",title:$t("BOM套BOM模式"),key:"multiplexed_bom_mode",value:!1,displayCount:3,isDisabled:!1,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[$t("crm.multiplexed_bom_mode.desc1",null,"功能概述：此开关允许用户通过嵌套BOM的方式创建和管理多层级的产品结构。开启后，BOM的层级和结构将支持通过嵌套子BOM来实现复杂组合产品的构建。"),$t("crm.multiplexed_bom_mode.desc2",null,"适用场景：特别适用于那些需要构建由多个子组件组成的复杂产品，而这些子组件本身也是由多个子件构成的BOM结构的情况。"),$t("crm.multiplexed_bom_mode.desc3",null,"操作效果：启用此开关后，用户可以在BOM中添加其他BOM作为子项，从而实现产品结构的多层次嵌套。这允许用户在创建新产品时，复用已有的子BOM，简化设计流程，提高效率。"),{title:$t("优势"),list:[$t("crm.multiplexed_bom_mode.desc4",null,"结构复用：通过嵌套子BOM，可以复用已有的物料结构，减少重复定义相同组件的工作。"),$t("crm.multiplexed_bom_mode.desc5",null,"简化管理：简化了物料和产品结构的管理，使得维护和更新更加高效。"),$t("crm.multiplexed_bom_mode.desc6",null,"灵活性增强：提高了设计和生产的灵活性，便于快速响应市场变化和客户需求。")]}]},{type:"switch",title:$t("报价单订单销售合同打印时只包含母件产品"),key:"bom_print_template_has_sub_node",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[],setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{type:"radio",title:$t("cpq-子产品价目表规则"),key:"bom_adaptation_price_list_rules",value:"0",displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[{label:$t("子件产品跟随母件选择的价目表"),value:"0",warningMessage:$t("主子同步注意1")},{label:$t("子件产品随价目表优先级"),value:"1",warningMessage:$t("主子同步注意2")}],describeList:[]},{type:"radio",title:$t("cpq.calculate.msg"),key:"bom_price_calculation_configuration",value:"0",displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[{label:$t("含默认子件"),value:"0",warningMessage:$t("cpq.calculate.msg1"),secWaringMessage:$t("cpq.calculate.msg2")},{label:$t("不")+$t("含默认子件"),value:"1",warningMessage:$t("cpq.calculate.msg3"),secWaringMessage:$t("cpq.calculate.msg4")}],describeList:[]},{type:"switch",title:$t("cpq.optionalSwitch"),key:"bom_instance",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!1,render:null,radioOptions:[],confirmMessage:$t("cpq.optionalSwitch.msg"),setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{isShow:!1,type:"switch",title:$t("standard_cpq.title",null,"开启标准BOM"),key:"standard_cpq",value:!1,displayCount:3,enableClose:!1,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:[{text:$t("standard_cpq.desc.title",null,"开关开启后，产品组合可以创建BOM类型为【标准BOM】的BOM")}]}]},{type:"radio",title:$t("crm.bom.generate_standard_bom_based_on_order"),key:"generate_standard_bom_based_on_order",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[],radioOptions:[{label:$t("crm.setting.cpqconfig.generate_standard_bom_based_on_order.label1",null,"不生成标准BOM"),value:"0"},{label:$t("crm.setting.cpqconfig.generate_standard_bom_based_on_order.label2",null,"自动生成标准BOM"),value:"1"},{label:$t("crm.setting.cpqconfig.generate_standard_bom_based_on_order.label3",null,"手动生成标准BOM"),value:"2"}]},{type:"switch",title:$t("crm.setting.cpqconfig.bom_duplicate_check.title",null,"BOM查重校验"),key:"bom_duplicate_check",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,radioOptions:[],setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{type:"switch",title:$t("crm.setting.cpq.not_show_bom",null,"选产品组合不进入选配配置页"),key:"not_show_bom",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!0,render:null,describeList:[$t("crm.setting.cpq.not_show_bom_desc",null,"用途说明：报价单/销售合同/销售订单 选择数据页面，选择“产品组合”时，不弹出选配产品页面，默认弹出。")]},{type:"switch",title:$t("开启临时子件"),key:"bom_temp_node",value:!1,displayCount:3,isDisabled:!1,isShow:!1,enableClose:!1,render:null,radioOptions:[],confirmMessage:$t("确认开启临时子件吗？开关开启后不可关闭"),setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[]},{isShow:!1,type:"BomcoreFilterRule",title:$t("crm.bomcoreFilterRule.title",null,"产品组合数据过滤规则"),key:"biz_function",value:"",displayCount:3,isDisabled:!1,enableClose:!0,describeList:[]},{isShow:!1,type:"switch",title:$t("crm.setting.cpq.bom_node_share",null,"CPQ分摊"),key:"bom_node_share",displayCount:3,isDisabled:!1,enableClose:!1,describeList:[],setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status"},{isShow:!1,type:"switch",title:$t("crm.setting.cpq.bom_single_leaf_node_closed",null,"自动收起末级单选子件分组"),key:"bom_single_leaf_node_closed",displayCount:3,isDisabled:!1,enableClose:!0,describeList:[$t("crm.setting.cpq.bom_single_leaf_node_closed.desc1",null,"开关开启后，在组合产品配置的场景下，组内的选配规则为单选（即一次只能选择一个子件），用户勾选某个子件后，系统会自动将包含该子件的分组收起，以简化界面和避免用户进一步操作同一分组内的其他子件。"),$t("crm.setting.cpq.bom_single_leaf_node_closed.desc2",null,"开关关闭后，用户勾选子件后，包含该子件的分组保持展开状态，用户可以继续查看或更改同一分组内的其他子件选项。")]},{isShow:!1,type:"switch",title:$t("crm.bom_detail_layout_closed"),key:"bom_detail_layout_closed",value:!1,displayCount:3,enableClose:!0,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:[{text:$t("crm.bom_detail_layout_closed.msg")+"；"+$t("crm.bom_detail_layout_closed.msg2")}]}]},{isShow:!1,type:"switch",title:$t("sfa.crm.bom_delete_root"),key:"bom_delete_root",value:!1,displayCount:3,enableClose:!0,setUrl:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",describeList:[{title:[{text:$t("sfa.crm.bom_delete_root.msg")}]}]},{isShow:!1,type:"switch",title:$t("sfa.crm.cpq_enabled_latest_version",null,"选择产品组合默认最新版本"),key:"cpq_enabled_latest_version",value:!1,displayCount:3,enableClose:!0,describeList:[$t("sfa.crm.cpq_enabled_latest_version.msg",null,"用途说明：选数据页面，选择“产品组合”时，如果存在多版本，默认取最新版本。")]}]}];l.exports=s});