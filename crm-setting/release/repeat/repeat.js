define("crm-setting/repeat/repeat",["./template/index-html","crm-modules/common/util"],function(e,t,n){var i=e("./template/index-html"),e=(e("crm-modules/common/util"),Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.renderPage()},renderPage:function(){this.$el.html(i)}}));n.exports=e});
define("crm-setting/repeat/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("查重设置")) == null ? "" : __t) + '</span></h2> </div> <div class="repeat-box"> <div class="repeat-img"></div> ';
            var n = '【<a href="#crm/setting/sysobject">' + $t("预设对象管理") + "</a>】";
            __p += " ";
            var text = $t("该功能已迁移请您到{{name}}的相应对象下进行查重设置", {
                name: n
            });
            __p += " <p>" + ((__t = text) == null ? "" : __t) + "</p> </div>";
        }
        return __p;
    };
});