define("crm-setting/callcenter/accessconfig/accessconfig",["crm-modules/common/util","./configuration-mode/configuration-mode"],function(e,n,i){e("crm-modules/common/util");var t=e("./configuration-mode/configuration-mode"),e=Backbone.View.extend({initialize:function(e){this.serviceProvider=e.data.serviceProvider,this.tenantBindId=e.data.id,this.setElement(e.wrapper),this.render()},events:{},render:function(){var e=this.serviceProvider,n=this.tenantBindId;this.accessconfigInstance=FxUI.create({name:"accessconfigpage",wrapper:this.$el[0],template:'<configuration-mode :serviceProvider="serviceProvider" :tenantBindId="tenantBindId"></configuration-mode>',components:{ConfigurationMode:t},data:function(){return{serviceProvider:e,tenantBindId:n}},watch:{},computed:{},methods:{},mounted:function(){},created:function(){}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var e=this;e.trigger("destroy"),e.selectValidTime=null,e.initShareData1=null,e.$queryApiNameSelect=null,e.$addApiNameSelect=null,this.accessconfigInstance&&this.accessconfigInstance.$destroy(),this.accessconfigInstance=null,this.undelegateEvents(),this.off()}});i.exports=e});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var i,a=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),a.push.apply(a,i)),a}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(i),!0).forEach(function(e){_defineProperty(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function _defineProperty(e,t,i){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var i=0;i<t.length;i++){var a=t[i];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,_toPropertyKey(a.key),a)}}function _createClass(e,t,i){return t&&_defineProperties(e.prototype,t),i&&_defineProperties(e,i),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0===i)return("string"===t?String:Number)(e);i=i.call(e,t||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/accessconfig/configuration-mode/bean",["crm-modules/common/util"],function(e,t,i){e("crm-modules/common/util");e=_createClass(P,[{key:"choosableObjOptions",get:function(){return(this.choosableApiNames||[]).map(function(e){return{label:e.label,value:e.apiName}})}},{key:"multiPageChoosableObjOptions",get:function(){return(this.choosableApiNames||[]).map(function(e){return{label:e.label,value:e.objectApiName}})}},{key:"priorityFlag",get:function(){return this.priority?"1":"0"}}],[{key:"formatBEData",value:function(e){var t;return new P(_objectSpread(_objectSpread({},e),{},{searchFieldConf:e.searchFieldConf?JSON.parse(e.searchFieldConf):{},priority:e.priority,temporaryRights:e.temporaryRights,multiStatus:e.multiStatus||0,satisfactionStatus:e.satisfactionStatus,addApiNameConfigs:e.addApiNameConfigs,interfaceConfig:e.interfaceConfig,monitorSeat:e.monitorSeat,addApiNames:e.addApiNames,queryApiNames:e.queryApiNames,choosableApiNames:e.choosableApiNames,shareData:e.shareData||[1,2,3],multiPageType:e.multiPageType||1,cardLayoutApiName:e.cardLayoutApiName||"default__c",advancedMultiPageDataModels:e.advancedMultiPageDataModels&&Array.isArray(e.advancedMultiPageDataModels)&&e.advancedMultiPageDataModels.map(function(e){return a.formatBEData(e||{})})||[],phoenFieldSettingData:e.phoenFieldSettingData||[],phoneUpdateRule:null!=(t=e.phoneUpdateRule)?t:"",phoneUpdateStatus:null!=(t=e.phoneUpdateStatus)?t:0}))}}]);function P(e){var i=this,t=e.searchFieldConf,a=e.priority,o=e.temporaryRights,r=e.multiStatus,r=void 0===r?0:r,n=e.satisfactionStatus,s=e.addApiNameConfigs,p=e.interfaceConfig,l=e.monitorSeat,u=e.addApiNames,c=e.queryApiNames,d=e.choosableApiNames,y=e.shareData,y=void 0===y?[1,2,3]:y,f=e.validTimeShort,f=void 0===f?-1:f,h=e.multiPageType,h=void 0===h?1:h,m=e.advancedMultiPageDataModels,m=void 0===m?[]:m,b=e.cardLayoutApiName,g=e.phoenFieldSettingData,v=e.phoneUpdateStatus,S=e.phoneUpdateRule;_classCallCheck(this,P),_.forEach(e,function(e,t){i[t]=e}),this.searchFieldConf=t,this.status=o.status,this.temporaryRights=o,this.multiStatus=r,this.satisfactionStatus=n?""+n:"0",this.addApiNameConfigs=s,this.interfaceConfig=p,this.monitorSeat=l,this.choosableApiNames=d,this.addApiNames=u,this.queryApiNames=c,this.priority=a,this.searchFieldConf=t,this.shareData=y,this.validTimeShort=f,this.validTime=o.validTime,this.priorityState=a&&"1"===a.status?"1":"0",this.priorityOption=a&&a.options,this.priorityId=a&&a.id,this.multiPageType=h,this.advancedMultiPageDataModels=m,this.cardLayoutApiName=b,this.phoenFieldSettingData=g,this.phoneUpdateStatus=v,this.phoneUpdateRule=S}var a=_createClass(l,null,[{key:"formatBEData",value:function(e){var t;return new l({priority:null!=(t=e.priority)?t:1,objectApiName:null!=(t=e.objectApiName)?t:"",autoPreCreate:null==(t=e.autoPreCreate)||t,dataQueryFlag:null==(t=e.dataQueryFlag)||t,phoneIdentifyFields:e.phoneIdentifyFields||[],recordTypeApiName:e.recordTypeApiName||"default__c",bindFieldApiName:null!=(t=e.bindFieldApiName)?t:"",popScreenByContact:null!=(t=e.popScreenByContact)&&t,phoneUpdateStatus:null!=(t=e.phoneUpdateStatus)?t:0})}}]);function l(e){var t=e.priority,t=void 0===t?0:t,i=e.objectApiName,i=void 0===i?"":i,a=e.autoPreCreate,a=void 0===a||a,o=e.dataQueryFlag,o=void 0===o||o,r=e.phoneIdentifyFields,r=void 0===r?[]:r,n=e.recordTypeApiName,n=void 0===n?"default__c":n,s=e.bindFieldApiName,s=void 0===s?"":s,p=e.popScreenByContact,p=void 0!==p&&p,e=e.phoneUpdateStatus,e=void 0===e?0:e;_classCallCheck(this,l),this.priority=t,this.objectApiName=i,this.autoPreCreate=a,this.dataQueryFlag=o,this.phoneIdentifyFields=r,this.recordTypeApiName=n,this.bindFieldApiName=s,this.popScreenByContact=p,this.phoneUpdateStatus=e}i.exports={ConfigurationMode:e,AdvanceMultiPageDataModel:a}});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var i,a=Object.keys(e);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(e),t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,i)),a}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(i),!0).forEach(function(t){_defineProperty(e,t,i[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))})}return e}function _defineProperty(t,e,i){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0===i)return("string"===e?String:Number)(t);i=i.call(t,e||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/accessconfig/configuration-mode/configuration-mode",["crm-modules/common/util","./template/accessconfig-html","../pop-field-setting/pop-field-setting","../customer-query/customer-query","../multi-page-mode/multi-page-mode","./utils","./bean","../../components/set-item/set-item","../../components/set-item-detail/set-item-detail","../../components/phone-field-setting/phone-field-setting"],function(t,e,i){var o=t("crm-modules/common/util"),a=(t("./template/accessconfig-html"),t("../pop-field-setting/pop-field-setting")),n=t("../customer-query/customer-query"),s=t("../multi-page-mode/multi-page-mode"),r=t("./utils"),l=t("./bean"),c=t("../../components/set-item/set-item"),d=t("../../components/set-item-detail/set-item-detail"),t=t("../../components/phone-field-setting/phone-field-setting"),p=($t("新建线索"),$t("新建客户"),$t("新建联系人"),$t("eservice.crm.mailobj.create_service_requestobj"),$t("查询客户"),$t("eservice.crm.search.contact"),"--");i.exports=Vue.extend({name:"configuration-mode",template:'\n\t <div class="configuration-mode" v-if="readyStatus">\n        <div class="form-content">\n\n\t\t\t<set-item \n\t\t\t\t:comp-name="popupScreenFieldSettingData.name" \n\t\t\t\tclass="screen-list-config-container"\n\t\t\t\t@cancel="onCancel"\n\t\t\t\t@submit="onSubmit">\n\t\t\t\t<div slot="label">\n\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.popup_screen_field_setting\') }}\n\t\t\t\t</div>\n\t\t\t\t<div slot="alone" slot-scope="scope">\n\t\t\t\t\t{{ $t(\'es.callcenter.label.popup_list\') }} \n\t\t\t\t\t<fx-switch v-model="cMultiStatus" :size="size" @change="handleSwitchChange(cMultiStatus, \'multiStatus\', scope)"></fx-switch>\n\t\t\t\t</div>\n\t\t\t\t<div slot="form">\n\t\t\t\t\t<fx-form label-position="top" :size="size">\n\t\t\t\t\t\t<fx-form-item :label="$t(\'es.callcenter.label.popup_display_mode\')">\n\t\t\t\t\t\t\t<fx-radio-group v-model="popupScreenFieldSettingData.basicModel.multiPageType" @change="handleChange($event)" :has-form-item="false">\n\t\t\t\t\t\t\t\t<fx-radio :label="1">{{$t(\'eservice.crm.callcenter.signal_model\')}}</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio :label="2">{{$t(\'eservice.crm.callcenter.multi_mode\')}}</fx-radio>\n\t\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t</fx-form-item>\n\n\t\t\t\t\t\t<template v-if="popupScreenFieldSettingData.basicModel.multiPageType == 1">\n\t\t\t\t\t\t\t<fx-form-item :label="$t(\'eservice.crm.callcenter.popup_screen_field_setting\')">\n\t\t\t\t\t\t\t\t<pop-field-setting \n\t\t\t\t\t\t\t\t\t:key="popupScreenFieldSettingData.popFieldSettingKey"\n\t\t\t\t\t\t\t\t\tref="popFieldSetting" \n\t\t\t\t\t\t\t\t\tv-bind="popFieldSettingProps.propsData" \n\t\t\t\t\t\t\t\t\t:has-form-item="false">\n\t\t\t\t\t\t\t\t</pop-field-setting>\n\t\t\t\t\t\t\t</fx-form-item>\n\n\t\t\t\t\t\t\t<fx-form-item :label="$t(\'新建弹屏列表\')">\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t\tv-bind="addApiNameConfigOpt.attr"\n\t\t\t\t\t\t\t\t\tv-model="popupScreenFieldSettingData.basicModel.addApiNames"\n\t\t\t\t\t\t\t\t\t@change="handleChange($event, \'addApiNames\')"\n\t\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t</fx-form-item>\n\n\t\t\t\t\t\t\t<fx-form-item :label="$t(\'eservice.crm.callcenter.data_query\')">\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t\tv-bind="queryApiNameConfigOpt.attr"\n\t\t\t\t\t\t\t\t\tv-model="popupScreenFieldSettingData.basicModel.queryApiNames"\n\t\t\t\t\t\t\t\t\t@change="handleChange($event, \'queryApiNames\')"\n\t\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t</fx-form-item>\n\n\t\t\t\t\t\t\t<fx-form-item>\n\t\t\t\t\t\t\t    <span slot="label">\n\t\t\t\t\t\t\t    {{$t(\'eservice.crm.callcenter.updatephone.label\')}}\n\t\t\t\t\t\t\t        <fx-tooltip effect="light" :content="$t(\'eservice.crm.callcenter.updatephone.rule.tips.desc\')" placement="bottom-start">\n\t\t\t\t\t\t\t        \t<span class="fx-icon-question"></span>\n\t\t\t\t\t\t\t        </fx-tooltip>\n\t\t\t\t\t\t        </span>\n\t\t\t\t\t\t\t\t<fx-radio-group size="small" v-model="popupScreenFieldSettingData.basicModel.phoneUpdateStatus" @change="handleChange($event, \'phoneUpdateStatus\')" :has-form-item="false">\n\t\t\t\t\t\t\t\t    <fx-radio :label="1">{{$t(\'是\')}}</fx-radio>\n\t\t\t\t\t\t\t\t    <fx-radio :label="0">{{$t(\'否\')}}</fx-radio>\n\t\t\t\t\t\t\t    </fx-radio-group>\n\t\t\t\t\t\t\t</fx-form-item>\n\n\t\t\t\t\t\t\t<fx-form-item v-if="popupScreenFieldSettingData.basicModel.phoneUpdateStatus === 1" :label="$t(\'eservice.crm.callcenter.updatephone.rule.desc\')">\n\t\t\t\t\t\t\t    <phone-field-setting \n\t\t\t\t\t\t\t\t    :key="popupScreenFieldSettingData.phoneFieldSettingKey"  \n\t\t\t\t\t\t\t\t    ref="phoneFieldSetting" \n\t\t\t\t\t\t\t\t    :basicModel="popupScreenFieldSettingData.basicModel"\n\t\t\t\t\t\t\t\t\t:phone-field-setting-opt="phoneFieldSettingOpt"\n\t\t\t\t\t\t\t\t    :has-form-item="false">\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t\t\t</phone-field-setting>\n\t\t\t\t\t\t\t</fx-form-item>\n\t\t\t\t\t\t</template>\n\n\t\t\t\t\t\t<template v-if="popupScreenFieldSettingData.basicModel.multiPageType == 2">\n\t\t\t\t\t\t\t<fx-form-item :label="$t(\'eservice.crm.callcenter.popup_screen_object\')">\n\t\t\t\t\t\t\t\t<multi-page-mode \n\t\t\t\t\t\t\t\t\t:key="popupScreenFieldSettingData.multiPageModeKey"\n\t\t\t\t\t\t\t\t\tref="multiPageMode" \n\t\t\t\t\t\t\t\t\t:basicModel="basicModel"\n\t\t\t\t\t\t\t\t\t:advancedMultiPageDataModels.sync="popupScreenFieldSettingData.basicModel.advancedMultiPageDataModels"\n\t\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\t></multi-page-mode> \n\t\t\t\t\t\t\t</fx-form-item>\n\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\n\t\t\t\t\t</form>\n\t\t\t\t</div>\n\t\t\t\t<div slot="detail">\n\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t{{ $t(\'es.callcenter.label.popup_display_mode\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t{{ multiPageTypeLabel }}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</set-item-detail>\n\n\t\t\t\t\t<template v-if="basicModel.multiPageType == 1">\n\t\t\t\t\t\t<pop-field-setting \n\t\t\t\t\t\t\t:key="popupScreenFieldSettingData.popFieldSettingKey"\n\t\t\t\t\t\t\tref="popFieldSetting" \n\t\t\t\t\t\t\tv-bind="popFieldSettingProps.propsData" \n\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\ttype="detail">\n\t\t\t\t\t\t</pop-field-setting>\n\n\t\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t{{ $t(\'新建弹屏列表\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t{{ addApiNamesLabel }}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</set-item-detail>\n\n\t\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.data_query\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t{{ queryApiNamesLabel }}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</set-item-detail>\n\n\t\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.updatephone.label\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t{{ phoneUpdateStatusLabel }}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</set-item-detail>\n\n\t\t\t\t\t\t<phone-field-setting \n\t\t\t\t\t\t\t:key="popupScreenFieldSettingData.phoneFieldSettingKey"  \n\t\t\t\t\t\t\tref="phoneFieldSetting" \n\t\t\t\t\t\t\t:basicModel="basicModel"\n\t\t\t\t\t\t\t:phone-field-setting-opt="phoneFieldSettingOpt"\n\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\ttype="detail">\t\t\t\t\t\t\t\t\t\n\t\t\t\t\t\t</phone-field-setting>\n\t\t\t\t\t</template>\n\n\t\t\t\t\t<template v-if="basicModel.multiPageType == 2">\n\t\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.popup_screen_object\') }}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<multi-page-mode \n\t\t\t\t\t\t\t\t:key="popupScreenFieldSettingData.multiPageModeKey"\n\t\t\t\t\t\t\t\tref="multiPageMode" \n\t\t\t\t\t\t\t\t:basicModel="basicModel" \n\t\t\t\t\t\t\t\t:advancedMultiPageDataModels.sync="basicModel.advancedMultiPageDataModels"\n\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\ttype="detail">\n\t\t\t\t\t\t\t></multi-page-mode> \n\t\t\t\t\t\t</set-item-detail>\n\t\t\t\t\t</template>\n\t\t\t\t</div>\n\t\t\t</set-item>\n\n\t\t\t<set-item \n\t\t\t\tv-if="basicModel.priorityFlag === \'1\'"\n\t\t\t\t:comp-name="seatListenSettingData.name"\n\t\t\t\tv-bind="seatListenSettingData.compProps"\n\t\t\t\t@cancel="onCancel"\n\t\t\t\t@submit="onSubmit">\n\t\t\t\t<div slot="label">\n\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.seat_listen_setting\') }}\n\t\t\t\t</div>\n\t\t\t\t<div slot-scope="scope">\n\t\t\t\t\t<fx-switch :value="cPriorityState" :size="size" @click.native="onEdit(cPriorityState, \'priorityState\', scope)"></fx-switch>\n\t\t\t\t\t<div class="callcenter-desc">{{$t("开启后，会根据客户对象下的客户级别字段进行电话接听优先级配置，帮助客服人员更快的接听更重要客户的来电")}}</div>\n\t\t\t\t</div>\n\t\t\t\t<div slot="form">\n\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t<div \n\t\t\t\t\t\t\tclass="callcenter-priorityState"\n\t\t\t\t\t\t\tv-for="(item, index) in seatListenSettingData.basicModel.priorityOption" \n\t\t\t\t\t\t\t:key="index">\n\t\t\t\t\t\t\t<div class="callcenter-priorityState__label">\n\t\t\t\t\t\t\t\t{{$t("客户级别")}}-{{item.label}}{{$t(\'：\')}} \n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="callcenter-priorityState__value">\n\t\t\t\t\t\t\t\t{{$t(\'第\')}}\n\t\t\t\t\t\t\t\t<fx-input\n\t\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t\tv-model="item.code"\n\t\t\t\t\t\t\t\t\t:placeholder="$t(\'输入整数1-999\')"\n\t\t\t\t\t\t\t\t\tmaxlength="3"\n\t\t\t\t\t\t\t\t\t@change="handleChange($event, \'priorityOption\')"\n\t\t\t\t\t\t\t\t\ttype="number"\n\t\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\t\tstyle="width: 110px;"\n\t\t\t\t\t\t\t\t></fx-input>\t \n\t\t\t\t\t\t\t\t{{$t("优先级接听")}}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</set-item-detail>\n\t\t\t\t</div>\n\t\t\t\t<div slot="detail" v-if="seatListenSettingData.basicModel.priorityState === \'1\'">\n\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t<div \n\t\t\t\t\t\t\tclass="callcenter-priorityState"\n\t\t\t\t\t\t\tv-for="(item, index) in basicModel.priorityOption" \n\t\t\t\t\t\t\t:key="index">\n\t\t\t\t\t\t\t<div class="callcenter-priorityState__label">\n\t\t\t\t\t\t\t\t{{$t("客户级别")}}-{{item.label}}{{$t(\'：\')}}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="callcenter-priorityState__value">\n\t\t\t\t\t\t\t\t{{$t(\'第\')}}\n\t\t\t\t\t\t\t\t{{item.code || EMPTY_STRING}}\n\t\t\t\t\t\t\t\t{{$t("优先级接听")}}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</set-item-detail>\n\t\t\t\t\t</div>\n\t\t\t</set-item>\n\n\n\t\t\t<set-item \n\t\t\t\t:comp-name="tempPermissionStatusData.name"\n\t\t\t\tv-bind="tempPermissionStatusData.compProps"\n\t\t\t\t@cancel="onCancel"\n\t\t\t\t@submit="onSubmit">\n\t\t\t\t<div slot="label">\n\t\t\t\t\t{{ $t(\'临时权限状态\') }}\n\t\t\t\t</div>\n\t\t\t\t<div slot-scope="scope">\n\t\t\t\t\t<fx-switch :value="cStatus" :size="size" @click.native="onEdit(cStatus, \'status\', scope)"></fx-switch>\n\t\t\t\t\t<div class="callcenter-desc">{{$t("开启后，客户，线索，联系人，数据不用长久开放给客服部门，只有客户来电后，客服部门在设置的有效期内才会有数据权限")}}</div>\n\t\t\t\t</div>\n\t\t\t\t<div slot="form">\n\t\t\t\t\t<fx-form label-position="top" :size="size">\n\t\t\t\t\t\t<fx-form-item :label="$t(\'临时权限有效期\')">\n\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\tv-bind="validTimeOpt.attr"\n\t\t\t\t\t\t\t\tv-model="tempPermissionStatusData.basicModel.validTimeShort"\n\t\t\t\t\t\t\t\t@change="handleChange($event, \'validTimeShort\')"\n\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\tstyle="width: 130px;"\n\t\t\t\t\t\t\t></fx-select>\n\n\t\t\t\t\t\t\t<template v-if="showTempValidTime">\n\t\t\t\t\t\t\t\t<fx-input\n\t\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t\tv-model="tempPermissionStatusData.basicModel.tempValidTime"\n\t\t\t\t\t\t\t\t\tmaxlength="5"\n\t\t\t\t\t\t\t\t\t@change="handleChange($event, \'tempValidTime\')"\n\t\t\t\t\t\t\t\t\ttype="number"\n\t\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\t\tstyle="width: 60px;"\n\t\t\t\t\t\t\t\t></fx-input>\n\n\t\t\t\t\t\t\t\t{{$t("天")}}\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</fx-form-item>\n\n\t\t\t\t\t\t<fx-form-item :label="$t(\'开通临时权限对象\')">\n\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tv-bind="shareDataOpt.attr"\n\t\t\t\t\t\t\t\tv-model="tempPermissionStatusData.basicModel.shareData"\n\t\t\t\t\t\t\t\t@change="handleChange($event, \'sharedata\')"\n\t\t\t\t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t</fx-form-item>\n\t\t\t\t\t</form>\n\t\t\t\t</div>\n\t\t\t\t<div slot="detail" v-if="tempPermissionStatusData.basicModel.status === \'1\'">\n\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t{{ $t(\'临时权限有效期\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t{{ validTimeShortLabel }}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t{{ $t(\'开通临时权限对象\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t{{ shareDataLabel }}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</set-item-detail>\n\t\t\t\t</div>\n\t\t\t</set-item>\n\n\t\t\t<set-item \n\t\t\t\t:comp-name="customerQuerySettingsData.name"\n\t\t\t\tv-bind="customerQuerySettingsData.compProps"\n\t\t\t\t@cancel="onCancel"\n\t\t\t\t@submit="onSubmit">\n\t\t\t\t<div slot="label">\n\t\t\t\t\t{{ $t(\'客服查询接口设置\') }}\n\t\t\t\t</div>\n\t\t\t\t<div slot-scope="scope">\n\t\t\t\t\t<fx-switch :value="customerQuerySettingsData.customerQueryProps.propsData.isCustomerQueryOpened" :size="size" @click.native="onEdit(customerQuerySettingsData.customerQueryProps.propsData.isCustomerQueryOpened, null, scope)"></fx-switch>\n\t\t\t\t\t<div class="callcenter-desc">{{$t("开启后，第三方呼叫中心可以通过接口查询客户")}}</div>\n\t\t\t\t</div>\n\t\t\t\t<div slot="form">\n\t\t\t\t\t<fx-form label-position="top" :size="size">\n\t\t\t\t\t\t<fx-form-item :label="$t(\'可查询客户字段\')">\n\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tv-bind="customerQueryPropsAttr"\n\t\t\t\t\t\t\t\tv-model="customerQuerySettingsData.customerQueryProps.propsData.selectedFieldApiNameList"\n\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t</fx-form-item>\n\t\t\t\t\t</form>\n\t\t\t\t</div>\n\t\t\t\t<div slot="detail" v-if="customerQuerySettingsData.customerQueryProps.propsData.isCustomerQueryOpened">\n\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t{{ $t(\'可查询客户字段\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t{{ selectedFieldApiNameListLabel }}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</set-item-detail>\n\t\t\t\t</div>\n\t\t\t</set-item>\n\n\t\t\t<set-item \n\t\t\t\t:comp-name="callLogCardData.name"\n\t\t\t\t@cancel="onCancel"\n\t\t\t\t@submit="onSubmit">\n\t\t\t\t<div slot="label">\n\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.servicerecord_card\') }}\n\t\t\t\t\t<fx-tooltip effect="dark" :content="$t(\'eservice.crm.callcenter.servicerecord_card_desc\')" placement="top" transition="">\n\t\t\t\t\t  <span class="fx-icon-question" style="fontSize: 14px"></span>\n  \t\t\t\t\t</fx-tooltip>\n\t\t\t\t</div>\n\t\t\t\t<div slot="form">\n\t\t\t\t\t<fx-form label-position="top" :size="size">\n\t\t\t\t\t\t<fx-form-item :label="$t(\'布局\')">\n\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\tv-model="callLogCardData.basicModel.cardLayoutApiName"\n\t\t\t\t\t\t\t\tkey="cardLayoutApiName"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t:el-style="{ width: \'300px\' }"\n\t\t\t\t\t\t\t\tv-bind="serviceRecordOpt.attr"\n\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t</fx-form-item>\n\t\t\t\t\t</form>\n\t\t\t\t</div>\n\t\t\t\t<div slot="detail">\n\t\t\t\t\t<div>\n\t\t\t\t\t\t{{ $t(\'布局\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t{{ cardLayoutApiNameLabel }}\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</set-item>\n\n\t\t\t<set-item \n\t\t\t\tv-if="[\'7\',\'8\'].includes(serviceProvider)"\n\t\t\t\tcomp-name="satisfactionSurvey"\n\t\t\t\t@cancel="onCancel"\n\t\t\t\t@submit="onSubmit">\n\t\t\t\t<div slot="label">\n\t\t\t\t\t{{ $t(\'满意度调查\') }}\n\t\t\t\t</div>\n\t\t\t\t<div slot="alone" slot-scope="scope">\n\t\t\t\t\t<fx-switch v-model="cSatisfactionStatus" :size="size" @change="handleSwitchChange(cSatisfactionStatus, \'satisfactionStatus\', scope)"></fx-switch>\n\t\t\t\t\t<div class="callcenter-desc">{{$t("开启后，座席通话时就可具备发起满意度调查功能")}}</div>\n\t\t\t\t</div>\n\t\t\t</set-item>\n\n\t\t\t<set-item \n\t\t\t\tv-if="serviceProvider === \'8\'"\n\t\t\t\t:comp-name="monitorSeatData.name"\n\t\t\t\tv-bind="monitorSeatData.compProps"\n\t\t\t\t@cancel="onCancel"\n\t\t\t\t@submit="onSubmit">\n\t\t\t\t<div slot="label">\n\t\t\t\t\t{{ $t(\'班长座席配置\') }}\n\t\t\t\t</div>\n\t\t\t\t<div slot-scope="scope">\n\t\t\t\t\t<fx-switch :value="cMonitorSeatStatus" :size="size" @click.native="onEdit(cMonitorSeatStatus, \'monitorSeatStatus\', scope)"></fx-switch>\n\t\t\t\t\t<div class="callcenter-desc">{{$t("eservice.crm.callcenter.seat_")}}</div>\n\t\t\t\t</div>\n\t\t\t\t<div slot="form">\n\t\t\t\t\t<fx-form label-position="top" :size="size">\n\t\t\t\t\t\t<fx-form-item :label="$t(\'es.callcenter.label.squad_leader_seat\')">\n\t\t\t\t\t\t\t<fx-selector-input-v2\n\t\t\t\t\t\t\t\tref="customerSelector"\n\t\t\t\t\t\t\t\tclass="customer-selector"\n\t\t\t\t\t\t\t\t@change="handleChange($event, \'monitorSeat.userIds\')"\n\t\t\t\t\t\t\t\tv-bind="monitorSeatSelectorOpt.attr"\n\t\t\t\t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t></fx-selector-input-v2>\n\t\t\t\t\t\t</fx-form-item>\n\t\t\t\t\t</form>\n\t\t\t\t</div>\n\t\t\t\t<div slot="detail" v-if="monitorSeatData.basicModel.monitorSeat.monitorSeatStatus === 1">\n\t\t\t\t\t<set-item-detail>\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t{{ $t(\'es.callcenter.label.squad_leader_seat\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t{{ monitorSeatLabel }}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</set-item-detail>\n\t\t\t\t</div>\n\t\t\t</set-item>\n\n        </div>\n\t </div>\n\t',props:{serviceProvider:String,tenantBindId:String},components:{SetItem:c,SetItemDetail:d,PopFieldSetting:a,"customer-query":n,MultiPageMode:s,PhoneFieldSetting:t},data:function(){return{size:"small",popupScreenFieldSettingData:{},seatListenSettingData:{},tempPermissionStatusData:{},customerQuerySettingsData:{},callLogCardData:{},monitorSeatData:{},multiStatus:1,readyStatus:!1,options:[],basicModel:{},validTimeOpt:{},shareDataOpt:{},customerQueryProps:{},popFieldSettingProps:{},addApiNameConfigOpt:{},queryApiNameConfigOpt:{},monitorSeatSelectorOpt:{},objectDataMap:{},serviceRecordOpt:{},phoneFieldSettingOpt:[]}},computed:{EMPTY_STRING:function(){return p},cMultiStatus:{get:function(){return!!this.basicModel.multiStatus},set:function(t){this.basicModel.multiStatus=+t}},cPriorityState:{get:function(){return!!+this.seatListenSettingData.basicModel.priorityState},set:function(t){this.seatListenSettingData.basicModel.priorityState=+t+""}},cStatus:{get:function(){return!!+this.tempPermissionStatusData.basicModel.status},set:function(t){this.tempPermissionStatusData.basicModel.status=+t+""}},cSatisfactionStatus:{get:function(){return!!+this.basicModel.satisfactionStatus},set:function(t){this.basicModel.satisfactionStatus=+t+""}},cMonitorSeatStatus:{get:function(){return!!this.monitorSeatData.basicModel.monitorSeat.monitorSeatStatus},set:function(t){this.monitorSeatData.basicModel.monitorSeat.monitorSeatStatus=+t}},multiPageTypeOptions:function(){return[{label:$t("eservice.crm.callcenter.signal_model"),value:1},{label:$t("eservice.crm.callcenter.multi_mode"),value:2}]},multiPageTypeLabel:function(){var e=this;return(this.multiPageTypeOptions.find(function(t){return t.value===e.basicModel.multiPageType})||{}).label||p},addApiNamesLabel:function(){var e=this,t="";try{var i=_.filter(this.addApiNameConfigOpt.attr.options,function(t){return e.basicModel.addApiNames.includes(t.value)}),t=(i=_.map(i,function(t){return t.label})).join($t("，"))}catch(t){}return t||p},queryApiNamesLabel:function(){var e=this,t="";try{var i=_.filter(this.queryApiNameConfigOpt.attr.options,function(t){return e.basicModel.queryApiNames.includes(t.value)}),t=(i=_.map(i,function(t){return t.label})).join($t("，"))}catch(t){}return t||p},validTimeShortLabel:function(){var e=this;return(this.showTempValidTime?"".concat(this.basicModel.tempValidTime||p).concat($t("天")):(_.find(this.validTimeOpt.attr.options,function(t){return e.basicModel.validTimeShort===t.value})||{}).label)||p},shareDataLabel:function(){var e=this,t="";try{var i=_.filter(this.shareDataOpt.attr.options,function(t){return e.basicModel.shareData.includes(t.value)}),t=(i=_.map(i,function(t){return t.label})).join($t("，"))}catch(t){}return t||p},customerQueryPropsAttr:function(){var t=[];try{t=_.map(this.customerQueryProps.propsData.fieldOptions,function(t){return{label:t.label,value:t.key}})}catch(t){}return{multiple:"multiple",width:"500px",options:t}},selectedFieldApiNameListLabel:function(){var e=this,t="";try{var i=_.filter(this.customerQueryPropsAttr.options,function(t){return e.customerQueryProps.propsData.selectedFieldApiNameList.includes(t.value)}),t=(i=_.map(i,function(t){return t.label})).join($t("，"))}catch(t){}return t||p},cardLayoutApiNameLabel:function(){var e=this,t="";try{var i=_.filter(this.serviceRecordOpt.attr.options,function(t){return e.basicModel.cardLayoutApiName.includes(t.value)}),t=(i=_.map(i,function(t){return t.label})).join($t("，"))}catch(t){}return t||p},monitorSeatLabel:function(){var t="";try{t=_.map(this.monitorSeatSelectorOpt.attr.defaultSelectedItems.member,function(t){return(FS.contacts.getEmployeeById(t)||{}).name||p}).join($t("，"))}catch(t){}return t||p},showTempValidTime:function(){return 4===this.basicModel.validTimeShort},showBasedatasource:function(){return"1"===this.basicModel.priorityState},phoneUpdateStatusLabel:function(){return 1===this.basicModel.phoneUpdateStatus?$t("开启"):$t("关闭")}},methods:{initData:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=this;r.initData(this.tenantBindId).then(function(t){i.basicModel=t.bean,i.validTimeOpt=t.validTimeOpt,i.shareDataOpt=t.shareDataOpt,i.customerQueryProps=t.customerQueryProps,i.popFieldSettingProps=t.popFieldSettingProps,i.addApiNameConfigOpt=t.addApiNameConfigOpt,i.queryApiNameConfigOpt=t.queryApiNameConfigOpt,i.monitorSeatSelectorOpt=t.monitorSeatSelectorOpt,i.serviceRecordOpt=t.serviceRecordOpt,i.phoneFieldSettingOpt=t.phoneFieldSettingOpt,i.readyStatus=!0,i.updateTempData(e)})},handleChange:function(t,e){var i,a;["multiStatus","status","priorityState"].includes(e)&&1===t&&(i=(a=r.resetTime(this.basicModel)).validTimeShort,a=a.tempValidTime,this.basicModel.validTimeShort=i,this.basicModel.tempValidTime=a),["satisfactionStatus"].includes(e)&&"1"===t&&CRM.util.alert($t("为了功能正常使用，请确保已在第三方呼叫中心后台的系统对接设置中，配置了“满意度调查”相关的消息推送。")),["priorityOption"].includes(e)&&(""===t?CRM.util.alert($t("优先级不能为空")):/^[1-9]\d*$/.test(t)||CRM.util.alert($t("请输入非0整数数字")))},getSubmitData:function(){var t={},e=(t.tenantBindId=this.tenantBindId,t.satisfactionStatus=+this.basicModel.satisfactionStatus,t.multiStatus=this.basicModel.multiStatus,t.multiPageType=this.basicModel.multiPageType,t.cardLayoutApiName=this.basicModel.cardLayoutApiName,1===t.multiPageType?(t.searchFieldConf=JSON.stringify(this.popupScreenFieldSettingData.basicModel.searchFieldConf),t.addApiNames=this.basicModel.addApiNames,t.queryApiNames=this.basicModel.queryApiNames,t.phoneUpdateRule=this.basicModel.phoneUpdateRule,t.phoneUpdateStatus=this.basicModel.phoneUpdateStatus):2===t.multiPageType&&(t.advancedMultiPageDataModels=this.basicModel.advancedMultiPageDataModels,t.popUpPlugApiName=this.basicModel.popUpPlugApiName,t.popUpFunctionApiName=this.basicModel.popUpFunctionApiName),this.customerQueryProps.propsData);return t.interfaceConfig={fieldList:e.selectedFieldApiNameList,interfaceStatus:+e.isCustomerQueryOpened},"8"===this.serviceProvider&&(t.monitorSeat={monitorSeatStatus:this.basicModel.monitorSeat.monitorSeatStatus,userIds:0===this.basicModel.monitorSeat.monitorSeatStatus?[]:this.basicModel.monitorSeat.userIds}),1==this.basicModel.priorityFlag&&(t.priority={status:this.basicModel.priorityState,id:this.basicModel.priorityId,options:this.basicModel.priorityOption}),0==this.basicModel.status?t.temporaryRights={status:this.basicModel.status}:t.temporaryRights={status:this.basicModel.status,validTime:r.getTime(this.basicModel),apiNames:JSON.stringify([{apiName:"AccountObj",displayName:$t("客户")},{apiName:"LeadsObj",displayName:$t("线索")},{apiName:"ContactObj",displayName:$t("联系人")}])},t},getSubmitDataByModuleName:function(){var t,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=e.compName,e=e.from,a=this.getSubmitData();return i?(t=[],"popupScreenFieldSetting"==i?"switch"==e?t=["multiStatus"]:(t=["multiPageType"],1==a.multiPageType?t=t.concat(["searchFieldConf","addApiNames","queryApiNames","phoneUpdateRule","phoneUpdateStatus"]):2==a.multiPageType&&(t=t.concat(["advancedMultiPageDataModels","popUpPlugApiName","popUpFunctionApiName"]))):"seatListenSetting"==i?t=["priority"]:"tempPermissionStatus"==i?t=["temporaryRights"]:"customerQuerySettings"==i?t=["interfaceConfig"]:"callLogCard"==i?t=["cardLayoutApiName"]:"monitorSeat"==i?t=["monitorSeat"]:"satisfactionSurvey"==i&&(t=["satisfactionStatus"]),t=t.concat(["tenantBindId"]),_.pick(a,t)):a},validate:function(t){var e=this.basicModel.validTimeShort;return!1===t||""===t?(-1!==e?CRM.util.alert($t("请输入临时权限天数！")):CRM.util.alert($t("请选择临时权限有效期！")),!1):/^[1-9]d*/.test(t)?!(99999<t&&(CRM.util.alert($t("临时权限有效期不能超过99999")),1)):(CRM.util.alert($t("请输入非0整数数字")),!1)},checkAllowSubmit:function(t){var e=this.seatListenSettingData.basicModel;if("seatListenSetting"==t&&"1"===e.priorityState&&e.priorityOption&&e.priorityOption.length)for(var i=/^[1-9]\d*$/,a=0;a<e.priorityOption.length;a++){if(""==e.priorityOption[a].code||null==e.priorityOption[a].code)return CRM.util.alert($t("优先级不能为空")),!1;if(!i.test(e.priorityOption[a].code))return CRM.util.alert($t("请输入非0整数数字")),!1}var n=this.popupScreenFieldSettingData.basicModel;if("popupScreenFieldSetting"==t&&1===n.multiPageType){if(this.$refs.popFieldSetting&&!this.$refs.popFieldSetting.checkAllowSubmit())return!1;if(this.$refs.phoneFieldSetting&&!this.$refs.phoneFieldSetting.checkAllowSubmit())return!1;if(!n.addApiNames.length)return o.remind(3,$t("新建弹屏列表不能为空")),!1}else if("popupScreenFieldSetting"==t&&2===n.multiPageType&&this.$refs.multiPageMode&&!this.$refs.multiPageMode.checkAllowSubmit())return!1;var n=this.tempPermissionStatusData.basicModel;return!("tempPermissionStatus"==t&&0!=n.status&&!this.validate(n.tempValidTime)||(n=this.monitorSeatData.basicModel,"monitorSeat"==t&&"8"===this.serviceProvider&&1===n.monitorSeat.monitorSeatStatus&&0===n.monitorSeat.userIds.length&&(o.remind(3,$t("请选择座席人员")),1)))},saveTemporaryInfo:function(e){var i=this;CRM.util.showLoading_tip(),o.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/saveAdvancedSetting",type:"post",data:i.getSubmitDataByModuleName(e),success:function(t){0==t.Value.errorCode?(o.remind(1,$t("操作成功！")),_.isFunction(e.next)&&e.next(),i.initData(e)):o.remind(3,t.Value.errorMessage||$t("操作失败!"))},complete:function(){CRM.util.hideLoading_tip()}},{submitSelector:$(".j-save-access"),errorAlertModel:1})},onCancel:function(){this.updateTempData(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{})},beforeSubmit:function(t){var a=this,n=t.compName,o=t.from;return new Promise(function(t,e){var i;(a.checkAllowSubmit(n)?("popupScreenFieldSetting"==n?(1==a.popupScreenFieldSettingData.basicModel.multiPageType?(a.popupScreenFieldSettingData.basicModel.searchFieldConf=JSON.parse(a.$refs.popFieldSetting.getSubmitData()),a.popupScreenFieldSettingData.basicModel.phoneUpdateRule=a.$refs.phoneFieldSetting&&a.$refs.phoneFieldSetting.getSubmitData()||""):2==a.popupScreenFieldSettingData.basicModel.multiPageType&&(i=a.$refs.multiPageMode.getSubmitData(),Object.assign(a.popupScreenFieldSettingData.basicModel,i)),Object.assign(a.basicModel,a.popupScreenFieldSettingData.basicModel)):"seatListenSetting"==n?("switch"==o&&(a.cPriorityState=!a.cPriorityState),Object.assign(a.basicModel,a.seatListenSettingData.basicModel)):"tempPermissionStatus"==n?("switch"==o&&(a.cStatus=!a.cStatus),Object.assign(a.basicModel,a.tempPermissionStatusData.basicModel)):"customerQuerySettings"==n?("switch"==o&&(a.customerQuerySettingsData.customerQueryProps.propsData.isCustomerQueryOpened=!a.customerQuerySettingsData.customerQueryProps.propsData.isCustomerQueryOpened),Object.assign(a.customerQueryProps,a.customerQuerySettingsData.customerQueryProps)):"callLogCard"==n?Object.assign(a.basicModel,a.callLogCardData.basicModel):"monitorSeat"==n&&("switch"==o&&(a.cMonitorSeatStatus=!a.cMonitorSeatStatus),1===a.monitorSeatData.basicModel.monitorSeat.monitorSeatStatus&&(a.monitorSeatData.basicModel.monitorSeat.userIds=a.$refs.customerSelector.getValue().member||[]),Object.assign(a.basicModel,a.monitorSeatData.basicModel)),t):e)()})},onSubmit:function(){var t=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.beforeSubmit(e).then(function(){t.saveTemporaryInfo(e)})},onEdit:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};t?this.onSubmit(_.extend({},i,{from:"switch"})):(this.handleChange(!t,e),i.openDialog({from:"switch"}))},getTempData:function(){var t=JSON.parse(JSON.stringify(this.$data));return t.basicModel=l.ConfigurationMode.formatBEData(_objectSpread(_objectSpread({},t.basicModel),{},{searchFieldConf:JSON.stringify(t.basicModel.searchFieldConf)})),t},updateTempData:function(t){var t=t.compName,e=this.getTempData();t&&"popupScreenFieldSetting"!=t||(this.popupScreenFieldSettingData={name:"popupScreenFieldSetting",popFieldSettingKey:FS.util.uuid(),phoneFieldSettingkey:FS.util.uuid(),multiPageModeKey:FS.util.uuid(),basicModel:{multiPageType:e.basicModel.multiPageType,addApiNames:e.basicModel.addApiNames,queryApiNames:e.basicModel.queryApiNames,searchFieldConf:e.basicModel.searchFieldConf,advancedMultiPageDataModels:e.basicModel.advancedMultiPageDataModels,phoenFieldSettingData:e.basicModel.phoenFieldSettingData,choosableApiNames:e.basicModel.choosableApiNames,phoneUpdateStatus:e.basicModel.phoneUpdateStatus,phoneUpdateRule:e.basicModel.phoneUpdateRule}}),t&&"seatListenSetting"!=t||(this.seatListenSettingData={name:"seatListenSetting",basicModel:{priorityState:e.basicModel.priorityState,priorityOption:e.basicModel.priorityOption},compProps:{type:"dialog",title:$t("es.crm.callcenter.seat_listen_setting_title")}}),t&&"tempPermissionStatus"!=t||(this.tempPermissionStatusData={name:"tempPermissionStatus",basicModel:{status:e.basicModel.status,validTimeShort:e.basicModel.validTimeShort,tempValidTime:e.basicModel.tempValidTime,shareData:e.basicModel.shareData},compProps:{type:"dialog",title:$t("es.crm.callcenter.temp_permission_status_title")}}),t&&"customerQuerySettings"!=t||(this.customerQuerySettingsData={name:"customerQuerySettings",customerQueryProps:e.customerQueryProps,compProps:{type:"dialog",title:$t("es.crm.callcenter.customer_query_settings_title")}}),t&&"callLogCard"!=t||(this.callLogCardData={name:"callLogCard",basicModel:{cardLayoutApiName:e.basicModel.cardLayoutApiName}}),t&&"monitorSeat"!=t||(this.monitorSeatData={name:"monitorSeat",basicModel:{monitorSeat:e.basicModel.monitorSeat},compProps:{type:"dialog",title:$t("es.crm.callcenter.monitor_seat_title")}})},handleSwitchChange:function(t,e){var i=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{};this.handleChange(t,e),this.saveTemporaryInfo(_.extend({},i,{from:"switch"}))}},mounted:function(){this.readyStatus=!1,this.initData()}})});
define("crm-setting/callcenter/accessconfig/configuration-mode/utils",["crm-modules/common/util","./bean"],function(e,t,i){function s(a){return new Promise(function(e,t){var i=_.map(o,function(e){return{label:e.ObjectName,value:e.OrderIndex}}),i={disabled:!0,width:"500px",multiple:"multiple",placeholder:$t("请选择"),size:"small",options:i};a.shareData=[1,2,3],e({attr:i})})}function c(r){return new Promise(function(e,t){var i=(i=n(r.temporaryRights.validTime)[0])?n(r.temporaryRights.validTime)[0].OrderIndex:4,a={options:_.map(O,function(e){return{label:e.validTime,value:e.OrderIndex,day:e.day}})};r.validTimeShort=i,r.tempValidTime=r.temporaryRights.validTime,e({attr:a})});function n(t){return O.filter(function(e){if(e.day==t)return!0})}}function l(i){return new Promise(function(e,t){e({attr:{options:i.choosableObjOptions,multiple:"multiple",width:"500px"}})})}function u(i){return new Promise(function(e,t){e({attr:{options:i.choosableObjOptions,multiple:"multiple",width:"500px"}})})}function d(e){var a=e.bean,r=e.customDataArr,n=e.LeadsObjDataArr,o=e.ContactDataArr;return new Promise(function(e,t){function i(e){return e.map(function(e){return{name:e.ObjectName,value:e.OrderIndex}})}e({propsData:{originSettingData:a.searchFieldConf,selectableAccountList:i(r),selectableLeadsList:i(n),selectableContactList:i(o)}})})}function m(e){var a=e.bean;return new Promise(function(t,e){var i=a&&a.choosableApiNames&&a.choosableApiNames.map(function(e){return e.objectApiName});n.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeListByApiName",type:"post",data:{describe_apiname_list:i},success:function(e){var a,r;0==e.Result.StatusCode&&t((e=e.Value.objectDescribeList,a={placeholder:$t("请选择"),multiple:!0,clearable:!0,filterable:!0,width:"100%"},r=[],_.each(e,function(e,t){var i=[];_.each(e.fields,function(e,t){"system"!==e.define_type&&"phone_number"===e.type&&i.push({fieldName:t,ObjectName:e.label+"("+e.api_name+")",OrderIndex:t,label:e.label+"("+e.api_name+")",value:t})}),r.push({apiName:e.api_name,attr:Object.assign({},a,{options:i,label:e.display_name})})}),r))}},{errorAlertModel:1})})}function p(n){return new Promise(function(e,t){var i=!!n.interfaceConfig.interfaceStatus,a=[],r=[];n.interfaceConfig.fieldList.forEach(function(e){a.push({key:e.apiName,label:e.label}),e.isShow&&r.push(e.apiName)}),e({propsData:{fieldOptions:a,selectedFieldApiNameList:r,isCustomerQueryOpened:i}})})}function f(i){return new Promise(function(e,t){e({attr:{tabs:[{id:"member",type:"switch",showSelectAll:!0,data:[{title:$t("按字母查看"),type:"sort",showSortNav:!0,data:[]},{title:$t("按组织架构查看"),type:"tree",data:{}}]}],defaultSelectedItems:{member:i.monitorSeat.userIds||[]},"add-btn-label":$t("选择员工"),size:"small"}})})}function b(e){return new Promise(function(t,e){n.FHHApi({url:"/EM1HNCRM/API/v1/object/layout/service/findByObjDescribeApiName",type:"post",data:{objectDescribeApiName:"ServiceRecordObj",layoutType:"detail",sourceInfo:"object_management"},success:function(e){0==e.Result.StatusCode&&(e={options:e.Value.layouts&&e.Value.layouts.map(function(e){return{label:e.display_name,value:e.api_name}}),placeholder:$t("请选择")},t({attr:e}))}},{errorAlertModel:1})})}function a(t){return new Promise(function(r,e){n.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeListByApiName",type:"post",data:t,success:function(e){var t=A(e.Value.objectDescribeList[2].fields),i=A(e.Value.objectDescribeList[0].fields),a=A(e.Value.objectDescribeList[1].fields);0==e.Result.StatusCode&&r({customDataArr:t,ContactDataArr:i,LeadsObjDataArr:a})}},{errorAlertModel:1})})}function r(i){return new Promise(function(t,e){n.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/getAdvancedSetting",type:"post",data:{tenantBindId:i},success:function(e){0==e.Result.StatusCode&&t(e.Value.data)}},{errorAlertModel:1})})}var n=e("crm-modules/common/util"),v=e("./bean"),y={describe_apiname_list:["LeadsObj","ContactObj","AccountObj"]},o=[{apiName:"LeadsObj",ObjectName:$t("销售线索"),OrderIndex:1},{apiName:"AccountObj",ObjectName:$t("客户"),OrderIndex:2},{apiName:"ContactObj",ObjectName:$t("联系人"),OrderIndex:3}],O=[{validTime:$t("请选择"),day:!1,OrderIndex:-1},{validTime:$t("一周"),day:7,OrderIndex:1},{validTime:$t("一个月"),day:30,OrderIndex:2},{validTime:$t("一年"),day:365,OrderIndex:3},{validTime:$t("自定义"),day:!1,OrderIndex:4}],A=function(e){var i=[];return _.each(e,function(e,t){"system"!==e.define_type&&i.push({fieldName:t,ObjectName:e.label+"("+e.api_name+")",OrderIndex:t})}),i};i.exports={initData:function(t){return new Promise(function(o,e){Promise.all([a(y),r(t)]).then(function(e){var t=e[0],i=t.customDataArr,a=t.ContactDataArr,t=t.LeadsObjDataArr,r=e[1],n=(r.searchFieldConf,v.ConfigurationMode.formatBEData(e[1]));Promise.all([s(n),c(n),l(n),u(n),d({bean:n,customDataArr:i,LeadsObjDataArr:t,ContactDataArr:a}),p(n),f(n),b(),m({bean:n})]).then(function(e){o({shareDataOpt:e[0],validTimeOpt:e[1],addApiNameConfigOpt:e[2],queryApiNameConfigOpt:e[3],popFieldSettingProps:e[4],customerQueryProps:e[5],monitorSeatSelectorOpt:e[6],serviceRecordOpt:e[7],phoneFieldSettingOpt:e[8],bean:n})})})})},ValidTimeArr:O,resetTime:function(e){var t=i(e.temporaryRights.validTime)[0];return{validTimeShort:t=t?i(e.temporaryRights.validTime)[0].OrderIndex:4,tempValidTime:e.temporaryRights.validTime};function i(t){return O.filter(function(e){if(e.day==t)return!0})}},getTime:function(e){var t=i(e.validTimeShort)[0];return t&&4!==t.OrderIndex?i(e.validTimeShort)[0].day:Number(e.tempValidTime);function i(t){return O.filter(function(e){if(e.OrderIndex==t)return!0})}},getfindDescribeListByApiName:a,getRecordType:function(i){return new Promise(function(t,e){n.FHHApi({url:"/EM1HNCRM/API/v1/object/".concat(i,"/controller/ValidRecordType"),type:"post",data:{describeApiName:i,is_only_active:!0},success:function(e){0==e.Result.StatusCode&&t(e.Value.record_list)}},{errorAlertModel:1})})},getfindDescribeListByApiName2:function(i){return new Promise(function(t,e){n.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeListByApiName",type:"post",data:i,success:function(e){0==e.Result.StatusCode&&t({objectDescribeList:e.Value.objectDescribeList})}},{errorAlertModel:1})})}}});
define("crm-setting/callcenter/accessconfig/customer-query/customer-query",[],function(e,t,n){n.exports=Vue.extend({name:"customer-query",template:'\n\t\t<div class="callcenter__customer-query">\n      <ul class="setting-list">\n        <li class="setting-item">\n          <div class="setting-item__label-wrap">\n            <p class="setting-item__label">{{$t(\'客服查询接口设置\')}}</p>\n          </div>\n          <div class="setting-item__comp-wrap">\n            <fx-radio-group v-model="isCustomerQueryOpened" class="radio-group  crm-style">\n              <fx-radio :label="true" class="radio-group__item">{{ $t(\'开启\') }}</fx-radio>\n              <fx-radio :label="false" class="radio-group__item">{{ $t(\'关闭\') }}</fx-radio>\n            </fx-radio-group>\n            <p class="setting-desc">{{ $t(\'开启后，第三方呼叫中心可以通过接口查询客户\') }}</p>\n          </div>\n        </li>\n\n        <li class="setting-item" v-show="isCustomerQueryOpened">\n          <div class="setting-item__label-wrap">\n            <p class="setting-item__label">{{$t(\'可查询客户字段\')}}</p>\n          </div>\n          <div class="setting-item__comp-wrap">\n            <fx-transfer\n              filterable\n              v-model="selectedFieldApiNameList"\n              :data="fieldOptions"\n              :titles="[$t(\'全部字段\'), $t(\'已选字段\')]"\n              :filter-placeholder="$t(\'请搜索\')"\n              class="fx-transfer crm-style"\n            >\n            </fx-transfer>\n          </div>\n        </li>\n\n      </ul>\n\t\t</div>\n\t',props:{isCustomerQueryOpened:{type:Boolean,default:!1},fieldOptions:{type:Array,default:function(){return[]}},selectedFieldApiNameList:{type:Array,default:function(){return[]}}},data:function(){return{}},methods:{getSubmitData:function(){return{isCustomerQueryOpened:this.isCustomerQueryOpened,selectedFieldApiNameList:this.selectedFieldApiNameList}}},mounted:function(){},created:function(){}})});
define("crm-setting/callcenter/accessconfig/multi-page-mode/adjust-priority/adjust-priority",[],function(t,e,i){var o=Vue.extend({name:"mutil-page-mode-dialog",template:'\n    <fx-dialog\n        :visible="showPanel"\n        @close="handleDialogClose"\n        custom-class="call-center-multi__selector-dialog"\n\t\twidth="480px"\n        :title="title"\n        append-to-body\n    >\n\t\t<fx-form ref="form" :model="formData" label-width="150px">\n            <fx-form-item :label="$t(\'优先级序号\')" prop="priority" required>\n              <fx-input-number  v-model.trim="formData.priority" controls-position="right" :min="min" :max="max"></fx-input-number>\n              <div class="app-workorder-busy-idle-rule__tips">\n                {{$t(\'提示：数字越小，优先级越高\')}}\n              </div>\n            </fx-form-item>\n        </fx-form>\n        <span slot="footer" class="dialog-footer">\n            <fx-button type="primary" @click="handleSubmit" size="small">\n              {{ $t(\'确定\') }}\n            </fx-button>\n            <fx-button @click="handleDialogClose" size="small">\n\t\t    \t{{ $t(\'取消\') }}\n\t\t    </fx-button>\n        </span>\n    </fx-dialog>\n  ',props:{max:{type:Number,default:1},min:{type:Number,default:1},priority:{type:Number,default:1},title:{type:String,default:$t("调整优先级")}},data:function(){return{showPanel:!1,formData:{priority:0}}},mounted:function(){this.formData.priority=this.priority},computed:{transferTitles:function(){return[$t("全部对象"),$t("已选对象")]}},methods:{handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){this.$emit("submit",{priority:this.formData.priority})},submit:function(t){this.$emit("submit",t)}},created:function(){}});o.$show=function(){var n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(t,e){var i=new o({el:document.createElement("div"),propsData:n});i.$on("hide",function(){i.showPanel=!1,setTimeout(function(){i.$destroy(),i.$el.remove()},1e3)}),i.$on("submit",function(){i.$emit("hide"),t.apply(void 0,arguments)}),i.$on("cancel",function(){i.$emit("hide"),e.apply(void 0,arguments)}),$("body").append(i.$el),setTimeout(function(){i.showPanel=!0},20),$("body").append(i.$el)})},i.exports=o});
define("crm-setting/callcenter/accessconfig/multi-page-mode/multi-page-mode",["crm-modules/common/util","./selector-dialog/selector-dialog","./object-selector/object-selector","./adjust-priority/adjust-priority","../configuration-mode/bean","../configuration-mode/utils","app-standalone/components/help/help-vue"],function(t,e,n){var i=t("crm-modules/common/util"),a=t("./selector-dialog/selector-dialog"),o=t("./object-selector/object-selector"),l=t("./adjust-priority/adjust-priority"),p=t("../configuration-mode/bean"),c=t("../configuration-mode/utils"),t=t("app-standalone/components/help/help-vue");n.exports=Vue.extend({name:"mutil-page-mode",template:'\n\t\t<div class="mutil-page-mode">\n\t\t\t<fx-table\n\t\t\t\t:data="advancedMultiPageDataModels"\n\t\t\t\trow-class-name="tab_row_class"\n\t\t\t\theader-row-class-name="header_row_class"\n\t\t\t\tclass="tab_row_class"\n\t\t\t\t:key="version"\n\t\t\t\tborder\n\t\t\t\tv-if="!loading"\n\t\t\t>\n\t\t\t\t<fx-table-column :label="$t(\'优先级\')" prop="priority" width="60">\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<span>{{ scope.row.priority }}</span>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t\t<fx-table-column prop="objectApiName" :label="$t(\'对象名称\')" width="90">\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<span>{{ getObjectApiName(scope.row.objectApiName) }}</span>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t\t<fx-table-column\n\t\t\t\t\tprop="phoneIdentifyFields"\n\t\t\t\t\t:label="$t(\'eservice.crm.callcenter.phone_identity_field\')"\n\t\t\t\t\twidth="160"\n\t\t\t\t>\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t:value="scope.row.phoneIdentifyFields"\n\t\t\t\t\t\t\t:options="option.fieldsOptions[scope.row.objectApiName]"\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\tmultiple\n\t\t\t\t\t\t\tclearable\n\t\t\t\t\t\t\tdisabled\n\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t\t<fx-table-column\n\t\t\t\t\tprop="bindFieldApiName"\n\t\t\t\t\t:label="$t(\'eservice.crm.callcenter.callrecord_backfill_field\')"\n\t\t\t\t\twidth="140"\n\t\t\t\t>\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t:value="scope.row.bindFieldApiName"\n\t\t\t\t\t\t\t:options="option.bindFieldApiNameOption[scope.row.objectApiName]"\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\tclearable\n\t\t\t\t\t\t\tdisabled\n\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t\t<fx-table-column\n\t\t\t\t\tprop="autoPreCreate"\n\t\t\t\t\t:label="$t(\'eservice.crm.callcenter.auto_pre_create\')"\n\t\t\t\t\twidth="85"\n\t\t\t\t>\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<span>{{ !!scope.row.autoPreCreate ? $t(\'是\') : $t(\'否\') }}</span>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t\t<fx-table-column\n\t\t\t\t\tprop="dataQueryFlag"\n\t\t\t\t\t:label="$t(\'eservice.crm.callcenter.data_query\')"\n\t\t\t\t\twidth="75"\n\t\t\t\t>\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<span>{{ !!scope.row.dataQueryFlag ? $t(\'是\') : $t(\'否\') }}</span>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t\t<fx-table-column\n\t\t\t\t\tprop="recordTypeApiName"\n\t\t\t\t\t:label="$t(\'业务类型\')"\n\t\t\t\t\twidth="120"\n\t\t\t\t>\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<span>{{\n\t\t\t\t\t\t\tgetRecordTypeLabel(\n\t\t\t\t\t\t\t\tscope.row.recordTypeApiName,\n\t\t\t\t\t\t\t\tscope.row.objectApiName\n\t\t\t\t\t\t\t)\n\t\t\t\t\t\t}}</span>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t\t<fx-table-column :label="$t(\'操作\')" width="180" v-if="type != \'detail\'">\n\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\t@click="handleEditObject(scope.row)"\n\t\t\t\t\t\t\ttype="text"\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t>{{ $t(\'编辑\') }}</fx-button\n\t\t\t\t\t\t>\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\ttype="text"\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t@click="handleProipty(scope.row)"\n\t\t\t\t\t\t\tv-if="\n\t\t\t\t\t\t\t\t![\'AccountObj\', \'ContactObj\'].includes(scope.row.objectApiName)\n\t\t\t\t\t\t\t"\n\t\t\t\t\t\t\t>{{ $t(\'调整优先级\') }}</fx-button\n\t\t\t\t\t\t>\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\ttype="text"\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t@click="handleDelete(scope.row)"\n\t\t\t\t\t\t\tv-if="\n\t\t\t\t\t\t\t\t![\'AccountObj\', \'ContactObj\'].includes(scope.row.objectApiName)\n\t\t\t\t\t\t\t"\n\t\t\t\t\t\t\t>{{ $t(\'删除\') }}</fx-button\n\t\t\t\t\t\t>\n\t\t\t\t\t</template>\n\t\t\t\t</fx-table-column>\n\t\t\t</fx-table>\n\t\t\t<div class="append-object" v-if="!loading && type != \'detail\'">\n\t\t\t\t<div class="append-btn" @click="handleAddObject">\n\t\t\t\t\t<span class="fx-icon-add-2 append-icon">{{ $t(\'添加对象\') }}</span>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="obj-detail-plugin-area" v-show="type !== \'detail\'">\n\t\t\t\t<div class="plugin-item">\n\t\t\t\t\t<div class="plugin-item__header">\n\t\t\t\t\t\t<p class="plugin-item__header-text">{{$t(\'es.callcenter-accessconfig.multi-page-mode.plugin-label\') /* 弹屏插件 */}}</p>\n\t\t\t\t\t\t\x3c!-- <sd-help :content="*************"> --\x3e\n\t\t\t\t\t\t<p>:</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="plugin-item__setting-content">\n\t\t\t\t\t\t<fx-select \n\t\t\t\t\t\t\t:options="option.pluginOptions" \n\t\t\t\t\t\t\tv-model="pluginApiName" \n\t\t\t\t\t\t\tclass="plugin-selector"\n\t\t\t\t\t\t\tclearable\n\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t<p class="sd-link-btn setting-content__add-btn" @click="onUploadPlugin">+{{ $t(\'新建\') }}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="plugin-item">\n\t\t\t\t\t<div class="plugin-item__header">\n\t\t\t\t\t\t<p class="plugin-item__header-text">{{$t(\'es.callcenter-accessconfig.multi-page-mode.apl-label\') /* 弹屏函数 */}}</p>\n\t\t\t\t\t\t<p>:</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="plugin-item__setting-content">\n\t\t\t\t\t\t<fx-select  \n\t\t\t\t\t\t\t:options="option.aPLOptions" \n\t\t\t\t\t\t\tv-model="aPLApiName" \n\t\t\t\t\t\t\tclass="plugin-selector"\n\t\t\t\t\t\t\tclearable\n\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t<p class="sd-link-btn setting-content__add-btn" @click="onUploadAPL">+{{ $t(\'新建\') }}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="obj-detail-plugin-area" v-show="type === \'detail\'">\n\t\t\t\t<div class="plugin-item">\n\t\t\t\t\t<div class="plugin-item__header">\n\t\t\t\t\t\t<p class="plugin-item__header-text">{{$t(\'es.callcenter-accessconfig.multi-page-mode.plugin-label\') /* 弹屏插件 */}}</p>\n\t\t\t\t\t\t\x3c!-- <sd-help style="margin-left: 0px;" :content="*************"></sd-help> --\x3e\n\t\t\t\t\t\t<p>：</p>\n\t\t\t\t\t\t<p>{{ (option.pluginOptions.find((d) => d.value === pluginApiName) || {}).label || \'--\' }}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="plugin-item">\n\t\t\t\t\t<div class="plugin-item__header">\n\t\t\t\t\t\t<p class="plugin-item__header-text">{{$t(\'es.callcenter-accessconfig.multi-page-mode.apl-label\') /* 弹屏函数 */}}</p>\n\t\t\t\t\t\t<p>:</p>\n\t\t\t\t\t\t<p>{{ (option.aPLOptions.find((d) => d.value === aPLApiName) || {}).label || \'--\' }}</p>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t',components:{"sd-help":t},props:{type:{type:String,default:""},advancedMultiPageDataModels:{type:Array,default:[]},multiPageChoosableObjOptions:[],basicModel:{}},data:function(){return{option:{fieldsOptions:{},bindFieldApiNameOption:{},selectableObjList:[],recordTypeOption:{},pluginOptions:[],aPLOptions:[]},pluginApiName:"",aPLApiName:"",objectDataMap:{},version:1,loading:!1}},watch:{"basicModel.popUpFunctionApiName":{handler:function(t){this.aPLApiName=t},immediate:!0},"basicModel.popUpPlugApiName":{handler:function(t){this.pluginApiName=t},immediate:!0}},methods:{onUploadPlugin:function(){var t=this;window.open("#customcomponent/=/module-plugins","_blank"),this.$confirm($t("es.callcenter-accessconfig.multi-page-mode.plugin-upload-confirm")).then(function(){t.loadPluginOptions()})},onUploadAPL:function(){var t=this;window.open("#crmmanage/=/module-myfunction","_blank"),this.$confirm($t("es.callcenter-accessconfig.multi-page-mode.plugin-upload-confirm")).then(function(){t.loadAPLOptions()})},loadPluginOptions:function(){var e=this;CRM.util.showLoading_tip(),i.FHHApi({url:"/EM1HCompBuild/Plugin/queryByTypes",type:"post",data:{type:"eservice_callcenter_plugin",isAll:!0,client:"web"},success:function(){var t=((0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).Value||{}).plugins||[];e.option.pluginOptions=t.map(function(t){return{label:t.name,value:t.apiName}})},complete:function(){CRM.util.hideLoading_tip()}},{submitSelector:$(".j-save-access"),errorAlertModel:1})},loadAPLOptions:function(){var e=this;CRM.util.showLoading_tip(),i.FHHApi({url:"/EM1HFUNC/biz/query",type:"post",data:{name_space:["call_center"],binding_object_api_name:"",return_type_list:[],pageSize:1e3,pageNumber:1,pageSizeOption:[20,50,100],_isfilter:!1,is_include_used:!0,is_active:!0},success:function(){var t=((0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).Value||{}).function||[];e.option.aPLOptions=t.map(function(t){return{label:t.function_name,value:t.api_name}})},complete:function(){CRM.util.hideLoading_tip()}},{submitSelector:$(".j-save-access"),errorAlertModel:1})},getObjectApiName:function(e){var t=this.option.selectableObjList&&this.option.selectableObjList.find(function(t){return t.value===e});return t&&t.label?t.label:e},getRecordTypeLabel:function(e,t){t=this.option.recordTypeOption[t]&&this.option.recordTypeOption[t].find(function(t){return t.value===e});return t&&t.label?t.label:e},handleDelete:function(e){["AccountObj","ContactObj"].includes(e.objectApiName)||(this.advancedMultiPageDataModels=this.advancedMultiPageDataModels.filter(function(t){return t.objectApiName!==e.objectApiName}))},handleProipty:function(i){var a=this;["AccountObj","ContactObj"].includes(i.objectApiName)||l.$show({min:3,max:this.advancedMultiPageDataModels.length,priority:i.priority}).then(function(t){var t=t.priority,e=a.advancedMultiPageDataModels.findIndex(function(t){return t.objectApiName===i.objectApiName}),n=a.advancedMultiPageDataModels.find(function(t){return t.objectApiName===i.objectApiName});a.advancedMultiPageDataModels.splice(e,1),a.advancedMultiPageDataModels.splice(t-1,0,n),a.advancedMultiPageDataModels=a.advancedMultiPageDataModels.map(function(t,e){return t.priority=e+1,t})})},handleEditObject:function(i){var t=this;a.$show({selectableList:[],selectedList:[],selectorType:"edit",editDataProps:{autoPreCreate:i.autoPreCreate,dataQueryFlag:i.dataQueryFlag,recordTypeApiName:i.recordTypeApiName,priority:i.priority,bindFieldApiName:i.bindFieldApiName,objectApiName:i.objectApiName,phoneIdentifyFields:i.phoneIdentifyFields,fieldsOptions:t.option.fieldsOptions[i.objectApiName],recordTypeOption:t.option.recordTypeOption[i.objectApiName],bindFieldApiNameOption:t.option.bindFieldApiNameOption[i.objectApiName],popScreenByContact:i.popScreenByContact,phoneUpdateStatus:i.phoneUpdateStatus}}).then(function(n){t.advancedMultiPageDataModels=t.advancedMultiPageDataModels.map(function(t){var e;return t.objectApiName===i.objectApiName?(e=p.AdvanceMultiPageDataModel.formatBEData(n),Object.assign({},t,e)):t})})},handleAddObject:function(t){var i=this,a=this;o.$show({selectableObjList:this.option.selectableObjList,objectApiNameMap:this.objectDataMap,selectedObjList:this.advancedMultiPageDataModels.map(function(t){return t.objectApiName})}).then(function(t){var e=i.advancedMultiPageDataModels.map(function(t){return t.objectApiName}),t=t.filter(function(t){return!e.includes(t.objectApiName)}),n=i.advancedMultiPageDataModels&&i.advancedMultiPageDataModels.length||0,t=t.map(function(t){n+=1;var e=a.option.bindFieldApiNameOption[t.objectApiName]&&a.option.bindFieldApiNameOption[t.objectApiName][0].value;return p.AdvanceMultiPageDataModel.formatBEData({bindFieldApiName:e,objectApiName:t.objectApiName,priority:n})});i.advancedMultiPageDataModels=i.advancedMultiPageDataModels.concat(t)}).catch(function(t){})},handleGetRecordType:function(e){return c.getRecordType(e).then(function(t){return{objectApiName:e,recordTypeOption:t.map(function(t){return Object.assign(t,{value:t.api_name})})}})},checkAllowSubmit:function(){var n=this,i=!0;return this.advancedMultiPageDataModels.forEach(function(t){var e=n.getObjectApiName(t.objectApiName);_.isEmpty(t.recordTypeApiName)?(CRM.util.remind(3,"".concat(e).concat($t("eservice.crm.callcenter.please_select_recordtype"))),i=!1):_.isEmpty(t.bindFieldApiName)?(CRM.util.remind(3,"".concat(e).concat($t("eservice.crm.callcenter.please_select_bind_field"))),i=!1):_.isEmpty(t.phoneIdentifyFields)&&(CRM.util.remind(3,"".concat(e).concat($t("eservice.crm.callcenter.please_select_phone_identify_field"))),i=!1)}),i},getSubmitData:function(){return{advancedMultiPageDataModels:this.advancedMultiPageDataModels,popUpFunctionApiName:this.aPLApiName,popUpPlugApiName:this.pluginApiName}},initData:function(){var e=this,o=(this.loading=!0,CRM.util.showLoading_new(),this),l={},p=[],n={},t=(this.basicModel.choosableApiNames.forEach(function(t){n[t.objectApiName]?n[t.objectApiName].push({label:t.apiName,value:t.apiName}):n[t.objectApiName]=[{label:t.apiName,value:t.apiName}]}),o.option.bindFieldApiNameOption=n,this.basicModel.multiPageChoosableObjOptions.map(function(t){return t.value}));t.push("ServiceRecordObj"),c.getfindDescribeListByApiName2({describe_apiname_list:Array.from(new Set(t))}).then(function(t){t.objectDescribeList.forEach(function(n){var e,t,i,a;l[n.api_name]=Object.assign({},l[n.api_name],{api_name:n.api_name,display_name:n.display_name}),o.option.fieldsOptions[n.api_name]=(e=n.fields,t=Object.keys(e).map(function(t){if("system"!==e[t].define_type)return{label:e[t].label,api_name:e[t].api_name,value:e[t].api_name,type:e[t].type}}).filter(Boolean),i=t.filter(function(t){return"phone_number"===t.type}),t=t.filter(function(t){return"phone_number"!==t.type}),i=i.map(function(t){return t.disabled=!1,t}),t=t.map(function(t){return t.disabled=!0,t}),[].concat(i).concat(t).filter(Boolean)),p.push({label:n.display_name,value:n.api_name}),"ServiceRecordObj"===n.api_name&&(a={},Object.keys(o.option.bindFieldApiNameOption).map(function(t){var e=(e=o.option.bindFieldApiNameOption[t]||[]).map(function(t){return t.label=n.fields[t.value].label,t});a[t]=e}),o.option.bindFieldApiNameOption=a)}),o.objectDataMap=l,o.option.selectableObjList=p,o.version=o.version+1}),Promise.all([c.getfindDescribeListByApiName2({describe_apiname_list:Array.from(new Set(t))})].concat(Array.from(new Set(t)).map(function(t){return o.handleGetRecordType(t)}))).then(function(t){t.forEach(function(t){o.option.recordTypeOption[t.objectApiName]=t.recordTypeOption}),o.version=o.version+1,e.loading=!1,CRM.util.hideLoading_new()})}},created:function(){this.initData(),this.loadPluginOptions(),this.loadAPLOptions()}})});
define("crm-setting/callcenter/accessconfig/multi-page-mode/object-selector/object-selector",[],function(e,t,n){var o=Vue.extend({name:"object-selector",template:'<fx-dialog\n    :visible="showPanel"\n    @close="handleDialogClose"\n    custom-class="object-selector-callcenetr"\n    width="602px"\n    :title="$t(\'选择对象\')"\n    append-to-body\n  >\n    <div v-loading="isPageLoading">\n      <div class="editor-header">\n        <fx-alert\n          :title="$t(\'点击保存后，添加的对象不允许删除，请谨慎选择添加对象。\')"\n          type="warning"\n          :closable="false"\n        >\n        </fx-alert>\n      </div>\n      <div class="editor-body">\n        <fx-transfer\n          filterable\n          :titles="transferTitle"\n          v-model="selectedObjList"\n          :data="selectableObjList"\n          :props="{\n            key: \'value\',\n          }"\n        >\n        </fx-transfer>\n      </div>\n    </div>\n    <span slot="footer" class="dialog-footer">\n      <fx-button type="primary" @click="handleSubmit" size="small">\n        {{ $t(\'确定\') }}\n      </fx-button>\n      <fx-button @click="handleDialogClose" size="small">{{\n        $t(\'取消\')\n      }}</fx-button>\n    </span>\n  </fx-dialog>',props:{selectableObjList:{type:Array,default:function(){return[]}},selectedObjList:{type:Array,default:function(){return[]}},objectApiNameMap:{type:Object,default:function(){return{}}}},data:function(){return{showPanel:!1,isPageLoading:!1,transferTitle:[$t("全部对象"),$t("已选对象")]}},mounted:function(){},methods:{handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){var e,t=this;this.isPageLoading||(this.isPageLoading=!0,e=this.selectedObjList.map(function(e){return{name:t.objectApiNameMap[e].display_name,objectApiName:e}}),this.$emit("submit",e))}},created:function(){}});o.$show=function(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(e,t){var n=new o({el:document.createElement("div"),propsData:i});n.$on("hide",function(){n.showPanel=!1,setTimeout(function(){n.$destroy(),n.$el.remove()},1e3)}),n.$on("submit",function(){n.$emit("hide"),e.apply(void 0,arguments)}),n.$on("cancel",function(){n.$emit("hide"),t.apply(void 0,arguments)}),$("body").append(n.$el),setTimeout(function(){n.showPanel=!0},20),$("body").append(n.$el)})},n.exports=o});
define("crm-setting/callcenter/accessconfig/multi-page-mode/selector-dialog/selector-dialog",[],function(t,e,n){var o=Vue.extend({name:"mutil-page-mode-dialog",template:'\n    <fx-dialog\n        :visible="showPanel"\n        @close="handleDialogClose"\n        custom-class="call-center-multi__selector-dialog"\n\t\twidth="602px"\n        :title="title"\n        append-to-body\n    >\n\t<fx-form ref="formData" size="small" :model="formData" label-width="150px" class="form-class" label-position="left">\n\t<fx-radio-group\n\t   v-model="formData.autoPreCreate"\n\t   prop="autoPreCreate"\n\t   :label="$t(\'eservice.crm.callcenter.auto_pre_create\')"\n\t   required\n\t>\n\t   <fx-radio :label="true">{{$t(\'是\')}}</fx-radio>\n\t   <fx-radio :label="false">{{$t(\'否\')}}</fx-radio>\n   </fx-radio-group>\n\n   <fx-radio-group\n\t   v-model="formData.dataQueryFlag"\n\t   prop="dataQueryFlag"\n\t   :label="$t(\'eservice.crm.callcenter.data_query\')"\n\t   required\n\t>\n\t   <fx-radio :label="true">{{$t(\'是\')}}</fx-radio>\n\t   <fx-radio :label="false">{{$t(\'否\')}}</fx-radio>\n\t</fx-radio-group>\n\n\t<fx-form-item>\n\t\t<span slot="label">\n\t\t{{$t(\'eservice.crm.callcenter.updatephone.label\')}}\n\t\t    <fx-tooltip effect="light" :content="$t(\'eservice.crm.callcenter.updatephone.rule.tips.desc\')" placement="bottom-start">\n\t\t    \t<span class="fx-icon-question"></span>\n\t\t    </fx-tooltip>\n\t\t</span>\n\t\t<fx-radio-group size="small" v-model="formData.phoneUpdateStatus" :has-form-item="false">\n\t\t    <fx-radio :label="1">{{$t(\'是\')}}</fx-radio>\n\t\t    <fx-radio :label="0">{{$t(\'否\')}}</fx-radio>\n\t\t</fx-radio-group>\n\t</fx-form-item>\n\n\t<fx-select\n\t\tprop="phoneIdentifyFields"\n\t\tv-model="formData.phoneIdentifyFields"\n\t\t:options="formData.fieldsOptions"\n\t\t:label="$t(\'eservice.crm.callcenter.phone_identity_field\')"\n\t\tmultiple\n\t\trequired\n\t\twidth="300px"\n\t\tfilterable\n\t></fx-select>\n\n\t<fx-select\n\t\tprop="bindFieldApiName"\n\t\tv-model="formData.bindFieldApiName"\n\t\t:options="formData.bindFieldApiNameOption"\n\t\t:label="$t(\'eservice.crm.callcenter.callrecord_backfill_field\')"\n\t\trequired\n\t\twidth="300px"\n\t\tfilterable\n\t></fx-select>\n\n\t<fx-select\n\t\tprop="recordTypeApiName"\n\t\tv-model="formData.recordTypeApiName"\n\t\t:options="formData.recordTypeOption"\n\t\t:label="$t(\'业务类型\')"\n\t\twidth="300px"\n\t\trequired\n\t></fx-select>\n\n\t<fx-radio-group\n\t   v-model="formData.popScreenByContact"\n\t   prop="popScreenByContact"\n\t   required\n\t   v-if="formData.objectApiName === \'AccountObj\' || formData.objectApiName === \'PartnerObj\'"\n\t>\n\t   <span slot="label">\n\t       {{$t(\'eservice.crm.callcenter.popup_by_contact\')}}\n\t\t   <fx-tooltip effect="light" :content="$t(\'eservice.crm.callcenter.popup_by_contact_desc\')" placement="bottom-start">\n\t\t       <span class="fx-icon-question"></span>\n           </fx-tooltip>\n\t   </span>\n\t   <fx-radio :label="true">{{$t(\'是\')}}</fx-radio>\n\t   <fx-radio :label="false">{{$t(\'否\')}}</fx-radio>\n    </fx-radio-group>\n\n    </fx-form>\n        <span slot="footer" class="dialog-footer">\n            <fx-button type="primary" @click="handleSubmit" size="small">\n              {{ $t(\'确定\') }}\n            </fx-button>\n            <fx-button @click="handleDialogClose" size="small">\n\t\t    \t{{ $t(\'取消\') }}\n\t\t    </fx-button>\n        </span>\n    </fx-dialog>\n  ',props:{selectorType:{type:String,default:"edit"},priority:{type:Number,default:1},title:{type:String,default:$t("编辑")},editDataProps:{type:Object,default:{}}},data:function(){return{showPanel:!1,formData:{autoPreCreate:!1,dataQueryFlag:!1,recordTypeApiName:"",priority:0,bindFieldApiName:"",objectApiName:"",phoneIdentifyFields:[],fieldsOptions:[],recordTypeOption:[],bindFieldApiNameOption:[],popScreenByContact:!1,phoneUpdateStatus:!1}}},mounted:function(){this.formData=this.editDataProps},computed:{transferTitles:function(){return[$t("全部对象"),$t("已选对象")]}},methods:{handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){var n=this;this.$refs.formData.validate(function(t,e){t&&(t=n.formData,n.$emit("submit",t))})},submit:function(t){this.$emit("submit",t)}},created:function(){}});o.$show=function(){var a=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(t,e){var n=new o({el:document.createElement("div"),propsData:a});n.$on("hide",function(){n.showPanel=!1,setTimeout(function(){n.$destroy(),n.$el.remove()},1e3)}),n.$on("submit",function(){n.$emit("hide"),t.apply(void 0,arguments)}),n.$on("cancel",function(){n.$emit("hide"),e.apply(void 0,arguments)}),$("body").append(n.$el),setTimeout(function(){n.showPanel=!0},20),$("body").append(n.$el)})},n.exports=o});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/accessconfig/pop-field-setting/pop-field-setting",["crm-widget/select/select","crm-modules/components/set-item-detail/set-item-detail"],function(t,e,n){var o=t("crm-widget/select/select"),t=t("crm-modules/components/set-item-detail/set-item-detail"),i={data:[],options:[],selectorInstance:null,domId:"callcenter__PopFieldSettingItem__AccountObj",apiName:"AccountObj",label:$t("客户"),priorityShow:!1},a={data:[],options:[],selectorInstance:null,domId:"callcenter__PopFieldSettingItem__LeadsObj",apiName:"LeadsObj",label:$t("线索"),priorityShow:!1},r={data:[],options:[],selectorInstance:null,domId:"callcenter__PopFieldSettingItem__ContactObj",apiName:"ContactObj",label:$t("联系人"),priorityShow:!1},s={AccountObj:i,LeadsObj:a,ContactObj:r};n.exports=Vue.extend({name:"pop-field-setting",template:'\n\t\t<div class="callcenter__pop-field-setting">\n\t\t\t<div v-if="type == \'detail\'">\n\t\t\t\t<set-item-detail>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.popup_screen_field_setting\') }}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div \n\t\t\t\t\t\tv-for="settingItem in settingList"\n\t\t\t\t\t\t:key="settingItem.apiName">\n\t\t\t\t\t\t{{ settingItem.label }}{{ $t(\'：\') }}\n\t\t\t\t\t\t{{ getSelectFieldNames(settingItem) }}\n\t\t\t\t\t</div>\n\t\t\t\t</set-item-detail>\n\t\t\t</div>\n\t\t    <div class="j-basedatasource" v-else>\n\t\t        <fx-draggable :list="settingList" animation="200">\n\t\t        \t<div\n\t\t        \t\tclass="fm-item setting-item"\n\t\t        \t\t:class="settingItem.apiName"\n\t\t        \t\tv-for="settingItem in settingList"\n\t\t        \t\t:key="settingItem.apiName"\n\t\t        \t>\n\t\t        \t\t<label class="fm-lb"><em>*</em>{{settingItem.label}}</label>\n\t\t        \t\t<div class="fm-wrap">\n\t\t        \t\t\t<div\n\t\t        \t\t\t\tclass="j-custom-sharedata"\n\t\t        \t\t\t\t:id="settingItem.domId"\n\t\t        \t\t\t></div>\n\t\t        \t\t</div>\n\t\t        \t\t<div class="drag-item">\n\t\t        \t\t\t<i class="drag-item-icon" />\n\t\t        \t\t</div>\n\t\t        \t\t<div class="sub-setting" v-if="settingItem.apiName === \'ContactObj\'">\n\t\t        \t\t\t<fx-checkbox v-model="settingItem.priorityShow" class="priority-selector">\n\t\t        \t\t\t\t<div class="priority-label">\n\t\t        \t\t\t\t\t<span class="main-text">{{ $t(\'弹屏关联客户信息\') }}</span>\n\t\t        \t\t\t\t\t<fx-tooltip \n\t\t        \t\t\t\t\t\teffect="light"  \n\t\t        \t\t\t\t\t\tplacement="right"\n\t\t        \t\t\t\t\t\t:content="$t(\'选中该选项后，会优先弹出该联系人关联的客户\') ">\n\t\t        \t\t\t\t\t\t<i class="question-icon"></i>\n\t\t        \t\t\t\t\t</fx-tooltip>\n\t\t        \t\t\t\t</div>\n\t\t        \t\t\t</fx-checkbox>\n\t\t        \t\t</div>\n\t\t        \t</div>\n\t\t         </fx-draggable>\n\t       </div>\n\t\t</div>\n\t',props:{originSettingData:{type:Array,default:function(){return[]}},selectableAccountList:{type:Array,default:function(){return[]}},selectableLeadsList:{type:Array,default:function(){return[]}},selectableContactList:{type:Array,default:function(){return[]}},type:{type:String,default:""}},components:{SetItemDetail:t},data:function(){return{settingList:[]}},methods:{getSubmitData:function(){var t=this.settingList.map(function(t){return{apiName:t.apiName,displayName:t.label,field:t.data,priorityShow:t.priorityShow}});return JSON.stringify(t)},checkAllowSubmit:function(){var t=this.settingList.find(function(t){return t.apiName===i.apiName})||{},e=this.settingList.find(function(t){return t.apiName===a.apiName})||{},n=this.settingList.find(function(t){return t.apiName===r.apiName})||{};return t.data.length?e.data.length?n.data.length?!(10<t.data.length+e.data.length+n.data.length&&(CRM.util.alert($t("最多选择10个号码字段")),1)):(CRM.util.remind(3,$t("请至少选择一个联系人")),!1):(CRM.util.remind(3,$t("请至少选择一条线索")),!1):(CRM.util.remind(3,$t("请至少选择一个客户")),!1)},initSettingData:function(){i.options=this.selectableAccountList,a.options=this.selectableLeadsList,r.options=this.selectableContactList,this.settingList=this.originSettingData.map(function(t){var e=_objectSpread({},s[t.apiName]);return e.data=t.field,e.priorityShow=t.priorityShow,e})},createSettingSelectorComp:function(n){var i=this,t=new o({$wrap:$("#"+n.domId,this.$el),zIndex:999,width:500,options:n.options,multiple:"multiple",defaultValue:n.data});t.on("change",function(t,e){n.data=t,i.$emit("change",n)}),n.selectorInstance=t,this.$once("hook:beforeDestroy",function(){t.destroy()})},getSelectFieldNames:function(t){var e=t.data||[],t=_.filter(t.options,function(t){return e.includes(t.value)});return(t=_.map(t,function(t){return t.name})).join($t("，"))||"--"}},mounted:function(){var e=this;this.$nextTick(function(){e.settingList.forEach(function(t){e.createSettingSelectorComp(t)})})},created:function(){this.initSettingData()}})});
define("crm-setting/callcenter/accessconfig/pop-field-setting/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="callcenter__pop-field-setting"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("eservice.crm.callcenter.popup_screen_field_setting")) == null ? "" : __t) + ':</label> <div class="fm-wrap-declare"> <span class="declare" >' + ((__t = $t("当呼叫中心有电话呼入的时候，系统会根据下面配置的字段匹配号码后弹屏")) == null ? "" : __t) + '</span > </div> </div> <div class="j-basedatasource"> <fx-draggable :list="settingList" animation="200"> <div class="fm-item" v-for="settingItem in settingList" :key="settingItem.refName" :ref="settingItem.refName" > <label class="fm-lb"><em>*</em>' + ((__t = settingItem.label) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-custom-sharedata" :id="settingItem.apiName" ></div> </div> </div> </fx-draggable> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/accessconfig/template/accessconfig-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="crm-g-form-content crm-scroll"> <div class="more-cust-wrapper"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("弹屏列表配置")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
            if (moreState == "1") {
                __p += (__t = " mn-selected") == null ? "" : __t;
            }
            __p += '" data-base="open"></span> <span class="radio-lb">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
            if (moreState == "0") {
                __p += (__t = " mn-selected") == null ? "" : __t;
            }
            __p += ' " data-base="close"></span> <span class="radio-lb">' + ((__t = $t("关闭")) == null ? "" : __t) + '</span> </span> <br> </div> </div> <div class="fm-item"> <label class="fm-lb">&nbsp;</label> <div class="fm-wrap-declare"> <span class="declare">' + ((__t = $t("开启后：一个手机号被识别匹配到多个客户，联系人时，会显示客户或者联系人列表。")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="search-field-wrapper" id="popFieldSetting"></div> <div class="search-field-wrapper"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("弹屏新建配置")) == null ? "" : __t) + '</label> <div class="fm-wrap-declare"> <span class="declare">' + ((__t = $t("当呼叫中心有电话呼入，且系统内未识别到对应电话信息后，系统会根据下面配置的选项弹屏。")) == null ? "" : __t) + '</span> </div> </div> <div class="j-basedatasource"> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("新建弹屏列表")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-addApiNameConfig"></div> </div> </div> </div> <div class="j-basedatasource"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("查询弹屏列表")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-queryApiNameConfig"></div> </div> </div> </div> </div> ';
            if (priorityFlag == true) {
                __p += ' <div class="priority-wrapper"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("eservice.crm.callcenter.seat_listen_setting")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (priorityState === "1") {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="open"></span> <span class="radio-lb">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (priorityState === "0") {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += ' " data-base="close"></span> <span class="radio-lb">' + ((__t = $t("关闭")) == null ? "" : __t) + '</span> </span> <br> </div> </div> <div class="fm-item"> <label class="fm-lb">&nbsp;</label> <div class="fm-wrap-declare"> <span class="declare">' + ((__t = $t("开启后，会根据客户对象下的客户级别字段进行电话接听优先级配置，帮助客服人员更快的接听更重要客户的来电")) == null ? "" : __t) + '</span> </div> </div> <div class="j-basedatasource" style="display:none"> ';
                _.each(priorityOption, function(item, index) {
                    __p += ' <div class="fm-item"> <label class="fm-lb">&nbsp;</label> <div class="custom-priority-box"> <span class="custom-priority-item">' + ((__t = $t("客户级别")) == null ? "" : __t) + "-" + ((__t = item.label) == null ? "" : __t) + '</span> <span class="">' + ((__t = $t("第")) == null ? "" : __t) + '</span>&nbsp;&nbsp; <input class="b-g-ipt fm-ipt" type="text" maxlength="3" value="' + ((__t = item.code) == null ? "" : __t) + '" onkeyup="value=value.replace(/[^\\d]/g,\'\')" data-base="' + ((__t = index) == null ? "" : __t) + '" placeholder="' + ((__t = $t("输入整数1-999")) == null ? "" : __t) + '"> <span class="con-item-lb">&nbsp;&nbsp;' + ((__t = $t("优先级接听")) == null ? "" : __t) + "</span> </div> </div> ";
                });
                __p += " </div> </div> ";
            }
            __p += ' <div class="temp-auth-warpper"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("临时权限状态")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
            if (onState === "1") {
                __p += (__t = " mn-selected") == null ? "" : __t;
            }
            __p += '" data-base="open"></span> <span class="radio-lb">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
            if (onState === "0") {
                __p += (__t = " mn-selected") == null ? "" : __t;
            }
            __p += ' " data-base="close"></span> <span class="radio-lb">' + ((__t = $t("关闭")) == null ? "" : __t) + '</span> </span> </div> </div> <div class="fm-item"> <label class="fm-lb">&nbsp;</label> <div class="fm-wrap-declare"> <span class="declare">' + ((__t = $t("开启后，客户，线索，联系人，数据不用长久开放给客服部门，只有客户来电后，客服部门在设置的有效期内才会有数据权限")) == null ? "" : __t) + '</span> </div> </div> <div class="j-basedatasource" ';
            if (onState == "0") {
                __p += ' style="display:none" ';
            }
            __p += '> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("临时权限有效期")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="type-select-box"></div> <div class="custom-time-box" style="display:none"> <input class="b-g-ipt fm-ipt" type="text" maxlength="5" onkeyup="value=value.replace(/[^\\d]/g,\'\')"><span class="con-item-lb">&nbsp;' + ((__t = $t("天")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("开通临时权限对象")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-sharedata"></div> </div> </div> </div> </div> <div class="search-field-wrapper" id="customerQuery"></div> ';
            if ([ "7", "8" ].includes("" + serviceProvider)) {
                __p += ' <div class="satisfaction-wrapper"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("满意度调查")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (satisfactionState == "1") {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="open"></span> <span class="radio-lb">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (satisfactionState == "0") {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += ' " data-base="close"></span> <span class="radio-lb">' + ((__t = $t("关闭")) == null ? "" : __t) + '</span> </span> <br> </div> </div> <div class="fm-item"> <label class="fm-lb">&nbsp;</label> <div class="fm-wrap-declare"> <span class="declare">' + ((__t = $t("开启后，座席通话时就可具备发起满意度调查功能")) == null ? "" : __t) + "</span> </div> </div> </div> ";
            }
            __p += " ";
            if (serviceProvider == "8") {
                __p += ' <div class="monitor-seat-warpper"> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("班长座席配置")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (monitorSeat.monitorSeatStatus === 1) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="open"></span> <span class="radio-lb">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (monitorSeat.monitorSeatStatus === 0) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += ' " data-base="close"></span> <span class="radio-lb">' + ((__t = $t("关闭")) == null ? "" : __t) + '</span> </span> </div> </div> <div class="fm-item"> <label class="fm-lb">&nbsp;</label> <div class="fm-wrap-declare"> <span class="declare">' + ((__t = $t("eservice.crm.callcenter.seat_")) == null ? "" : __t) + '</span> </div> </div> <div class="j-basedatasource" ';
                if (monitorSeat.monitorSeatStatus === 0) {
                    __p += ' style="display:none" ';
                }
                __p += '> <div class="fm-item"> <label class="fm-lb">&nbsp;</label> <div class="fm-wrap"> <div class="monitor-seat-staff-selector"></div> </div> </div> </div> </div> ';
            }
            __p += ' </div> <div class="page-footer"> <div class="footer-btn"> <span class="b-g-btn j-save-access">' + ((__t = $t("保 存")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/accountbinding/accountbind-common/accountbind-common",["crm-modules/common/util","crm-widget/table/table","../component/template/index-html","../component/table-header/table-header","../component/import-dialog/import-dialog","../component/export-dialog/export-dialog","../editbindinginfo-default/editbindinginfo"],function(t,e,n){var a=t("crm-modules/common/util"),o=t("crm-widget/table/table"),i=t("../component/template/index-html"),d=t("../component/table-header/table-header"),r=t("../component/import-dialog/import-dialog"),c=t("../component/export-dialog/export-dialog"),s=function(){},l=t("../editbindinginfo-default/editbindinginfo"),t=Backbone.View.extend({initialize:function(t){s=this.hanleGetBindingComMap();var e=this;e.$$={},e.setElement(t.wrapper),e.opts=t,e.initSeatTypeOption(),e.tenantBindId=t.data.id,e.render()},render:function(){var e=this;this.$el.html(i()),$(this.$el).show(),e.editBindingData={},"function"==typeof s.getEditBindData?s.getEditBindData().then(function(t){e.initTable(),e.editBindingData=t}):e.initTable(),e.initTableHeader()},refresh:function(){this.dt.setParam({},!0)},hanleGetBindingComMap:function(){return l},handleGetTableHeader:function(){return d},initTableHeader:function(){var e=this,t=new(this.handleGetTableHeader())({el:document.createElement("div"),propsData:{}});t.$on("change",function(t){e.tableSearch({keyword:t.searchWord,viewType:t.type})}),this.$(".table-header").append(t.$el),this.$$.tableHeader=t},tableSearch:function(t){this.dt.setParam(t,!0,!0)},events:{"click .j-add":"onAdd","click .j-import":"onImport","click .j-export":"onExport","click .j-del":"onDel"},initSeatTypeOption:function(){var e=this;this.opts.seatType=new Promise(function(t){t([{value:1,name:$t("电话")},{value:2,name:$t("分机")}])}).then(function(t){return e.opts.seatType=t})},initTable:function(){var i=this;$(i.$el).show(),i.dt=new o({$el:i.$(".table-content"),title:"",url:"/EM1HESERVICE2/eservice/callcenter/seat/queryUserBindPage",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!1,trHandle:!1,showPage:!0,stopWheelEvent:!0,postData:{tenantBindId:i.opts.data.id},operate:{pos:"T",btns:[{text:$t("新建"),className:"j-add"}]},batchBtns:[],columns:[{data:"userName",title:$t("员工")},{data:"mobile",title:$t("手机号")},{data:"mainDepartmentName",title:$t("主属部门")},{data:"seatId",title:$t("第三方帐号")},{data:null,title:$t("操作"),lastFixed:!0,render:function(t,e,n){return'<div class="btns"><a class="j-edit">'+$t("编辑")+'</a><a class="j-del-btn">'+$t("解绑")+"</a></div>"}}],formatData:function(t){return t.data&&$.extend(!0,[],t.data.data),{data:t.data?t.data.data:[],totalCount:t.data?t.data.recordSize:0}},initComplete:function(t){}}),i.dt.on("trclick",function(t,e,n){n.hasClass("j-edit")?i.onEdit(t):n.hasClass("j-del-btn")&&i.onDel(t)})},filterAccountData:function(t,e){return t.filter(function(t){if(-1!==t.nickName.indexOf(e)||t.phoneNum===e)return!0})},onImport:function(){r.$show()},onExport:function(){c.$show({selectedDataList:this.dt.getCheckedData()||[]})},onAdd:function(){var t=this;t.EditBindingInfoWidget&&(t.EditBindingInfoWidget.destroy(),t.EditBindingInfoWidget=null),t.EditBindingInfoWidget=new s(_objectSpread({title:$t("新建绑定信息"),type:"add",seatType:t.opts.seatType,tenantBindId:t.opts.data.id},t.editBindingData)),t.EditBindingInfoWidget.on("success",function(){t.refresh()}),t.EditBindingInfoWidget.show({data:{tenantBindId:t.opts.data.id}})},onEdit:function(t){var e=this;e.EditBindingInfoWidget&&(e.EditBindingInfoWidget.destroy(),e.EditBindingInfoWidget=null),e.EditBindingInfoWidget=new s(_objectSpread({title:$t("编辑绑定信息"),type:"edit",groupId:t.id,seatType:e.opts.seatType,tenantBindId:e.opts.data.id},e.editBindingData)),e.EditBindingInfoWidget.on("success",function(){$(".dt-ipt").val(""),$(".dt-sc-reset").hide(),e.refresh()}),e.EditBindingInfoWidget.show({data:Object.assign(t,{tenantBindId:e.opts.data.id})})},onDel:function(t){var e=this,n=e.dt.getCheckedData(),i=[],o=(_.each(n,function(t,e){i.push(t.id)}),a.confirm($t("解绑后，该帐号再无法在第三方平台访问CRM信息，确认解绑吗?"),$t("确定"),function(){a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/deleteUserBind",data:{id:t.id||i.join(",")},success:function(t){0==t.Result.StatusCode&&($(".dt-ipt").val(""),$(".dt-sc-reset").hide(),a.remind(1,$t("操作成功")),o.destroy(),e.refresh())}})}))},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var e=this,t=this;t.dt&&t.dt.destroy(),t.dt=t.$el=t.el=t.events=t.options=null,Object.keys(this.$$||{}).forEach(function(t){t=e.$$[t];t.$destroy&&t.$destroy(),t.destroy&&t.destroy()}),this.$$=null}});n.exports=t});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/accountbinding/accountbinding-tianrun/accountbinding",["crm-modules/common/util","./template/index-html","crm-widget/table/table","./components/table-header/table-header","../component/import-dialog/import-dialog","../component/export-dialog/export-dialog","../../model/model","../editbindinginfo-tianrun/editbindinginfo"],function(t,e,n){var a=t("crm-modules/common/util"),i=t("./template/index-html"),o=t("crm-widget/table/table"),d=t("./components/table-header/table-header"),r=t("../component/import-dialog/import-dialog"),s=t("../component/export-dialog/export-dialog"),l=t("../../model/model"),c=t("../editbindinginfo-tianrun/editbindinginfo"),t=Backbone.View.extend({initialize:function(t){var e=this;e.$$={},e.setElement(t.wrapper),e.opts=t,e.model=new l(Object.assign(t.data,{data:t.data})),e.initSeatTypeOption(),e.render()},refresh:function(){this.dt.setParam({},!0)},render:function(){var e=this;this.$el.html(i()),$(this.$el).show(),this.editBindingData={},"function"==typeof c.getEditBindData?c.getEditBindData().then(function(t){e.initTable(),e.editBindingData=t}):this.initTable(),this.initTableHeader()},events:{"click .j-add":"onAdd","click .j-import":"onImport","click .j-export":"onExport","click .j-del":"onDel"},initSeatTypeOption:function(){var t={};try{t=JSON.parse(this.opts.data.confJson)}catch(t){}this.opts.seatType=[{value:1,name:$t("电话")},{value:2,name:$t("分机")}],"sdk"===t.accessWay&&this.opts.seatType.push({value:3,name:$t("软电话")})},initTableHeader:function(){var e=this,t=new d({el:document.createElement("div"),propsData:{}});t.$on("change",function(t){e.tableSearch({keyword:t.searchWord,viewType:t.type})}),this.$(".table-header").append(t.$el),this.$$.tableHeader=t},tableSearch:function(t){this.dt.setParam(t,!0,!0)},initTable:function(){var i=this;i.dt=new o({$el:i.$(".table-content"),title:"",url:"/EM1HESERVICE2/eservice/callcenter/seat/queryUserBindPage",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!1,trHandle:!1,showPage:!0,stopWheelEvent:!0,postData:{viewType:"all",tenantBindId:i.opts.data.id},operate:{pos:"T",btns:[{text:$t("新建"),className:"j-add"}]},batchBtns:[],columns:[{data:"userName",title:$t("员工")},{data:"mainDepartmentName",title:$t("所属部门")},{data:"seatId",title:$t("第三方账号")},{data:"bindTypeLabel",title:$t("座席话机类型")},{data:"bindTel",title:$t("座席绑定电话")},{data:"thirdQnosLabel",title:$t("所属队列")},{data:"activeLabel",title:$t("激活状态")},{data:"runStatusLabel",title:$t("员工状态")},{data:null,title:$t("操作"),lastFixed:!0,render:function(t,e,n){return'<div class="btns"><a class="j-edit">'+$t("编辑")+'</a><a class="'.concat(n.userId?"j-del-btn":"disabled",'">')+$t("解绑")+"</a></div>"}}],formatData:function(t){var e={1:$t("电话"),2:$t("分机"),3:$t("软电话")},n={0:$t("否"),1:$t("是"),"-1":$t("无效座席")},i={1:$t("正常"),2:$t("已停用"),3:$t("已删除")},t=t.data,o=(t.data||[]).map(function(t){return Object.assign(t,{thirdQnosLabel:(t.thirdQnos||[]).join(","),bindTypeLabel:e[t.bindType],activeLabel:n[t.active],runStatusLabel:i[t.runStatus]})});return $.extend(!0,[],o),{data:o,totalCount:t.recordSize||0}},initComplete:function(t){}}),i.dt.on("trclick",function(t,e,n){n.hasClass("j-edit")?i.onEdit(t):n.hasClass("j-del-btn")&&i.onDel(t)})},filterAccountData:function(t,e){return t.filter(function(t){if(-1!==t.nickName.indexOf(e)||t.phoneNum===e)return!0})},onImport:function(){r.$show()},onExport:function(){s.$show({selectedDataList:this.dt.getCheckedData()||[]})},onAdd:function(){var t=this;t.EditBindingInfoWidget&&(t.EditBindingInfoWidget.destroy(),t.EditBindingInfoWidget=null),t.EditBindingInfoWidget=new c(_objectSpread({title:$t("新建绑定信息"),type:"add",seatType:t.opts.seatType,tenantBindId:this.opts.data.id},t.editBindingData)),t.EditBindingInfoWidget.on("success",function(){t.refresh()}),t.EditBindingInfoWidget.show({data:{tenantBindId:this.opts.data.id}})},onEdit:function(t){var e=this;e.EditBindingInfoWidget&&(e.EditBindingInfoWidget.destroy(),e.EditBindingInfoWidget=null),e.EditBindingInfoWidget=new c(_objectSpread({title:$t("编辑绑定信息"),type:"edit",groupId:t.id,seatType:e.opts.seatType,tenantBindId:this.opts.data.id},e.editBindingData)),e.EditBindingInfoWidget.on("success",function(){$(".dt-ipt").val(""),$(".dt-sc-reset").hide(),e.refresh()}),e.EditBindingInfoWidget.show({data:Object.assign(t,{tenantBindId:this.opts.data.id})})},onDel:function(t){var e=this,n=e.dt.getCheckedData(),i=[],o=(_.each(n,function(t,e){i.push(t.id)}),a.confirm($t("账号解绑后，同步解绑并删除天润绑定侧对应座席的电话号码，确认解绑吗?"),$t("确定"),function(){a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/deleteUserBind",data:{id:t.id||i.join(",")},success:function(t){$(".dt-ipt").val(""),$(".dt-sc-reset").hide(),o.destroy(),e.refresh(),0==t.Result.StatusCode&&0==t.Value.errorCode?a.remind(1,$t("操作成功")):a.remind(3,t.Value.errorMessage||$t("操作失败！"))}})}))},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var e=this,t=this;t.dt&&t.dt.destroy(),t.dt=t.$el=t.el=t.events=t.options=null,Object.keys(this.$$||{}).forEach(function(t){t=e.$$[t];t.$destroy&&t.$destroy(),t.destroy&&t.destroy()}),this.$$=null}});n.exports=t});
define("crm-setting/callcenter/accountbinding/accountbinding-tianrun/components/table-header/table-header",[],function(e,n,t){t.exports=Vue.extend({name:"table-header",template:'\n    <div class="callcenter__table-header">\n      <fx-dropdown trigger="click" @command="handleTypeChange" class="header-type">\n        <span class="el-dropdown-link">\n          {{ currentType.label }}<i class="el-icon-arrow-down el-icon--right"></i>\n        </span>\n        <fx-dropdown-menu slot="dropdown">\n          <fx-dropdown-item \n            v-for="typeData of typeList" \n            :key="typeData.value"\n            :command="typeData.value"\n          >{{ typeData.label }}</fx-dropdown-item>\n        </fx-dropdown-menu>\n      </fx-dropdown>\n      <fx-input\n        v-model="searchWord"\n        class="search-input"\n        :placeholder="$t(\'请输入员工、第三方账号、座席绑定电话\')"\n        prefix-icon="el-icon-search"\n        size="small"\n        @keyup.enter.native="search"\n        @clear="search"\n        clearable\n      >\n      </fx-input>\n    </div>\n  ',props:{},data:function(){return{searchWord:"",currentTypeValue:"all",typeList:[{label:$t("所有"),value:"all"},{label:$t("已绑定座席"),value:"bound"},{label:$t("未绑定座席"),value:"unbind"}]}},watch:{currentTypeValue:function(e){this.emitChange()}},computed:{currentType:function(){var n=this;return this.typeList.find(function(e){return e.value===n.currentTypeValue})}},methods:{emitChange:function(){this.$emit("change",{type:this.currentTypeValue,searchWord:this.searchWord})},handleTypeChange:function(e){this.currentTypeValue=e},search:function(){this.emitChange()}},created:function(){}})});
define("crm-setting/callcenter/accountbinding/accountbinding-tianrun/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="account-binding__tian-run"> <div class="table-header"></div> <div class="table-content"></div> </div>';
        }
        return __p;
    };
});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/accountbinding/accountbinding-udesk/accountbinding",["crm-modules/common/util","crm-widget/table/table","./template/index-html","./component/import-dialog/import-dialog","./component/export-dialog/export-dialog","./component/table-header/table-header","../../model/model","../editbindinginfo-udesk/editbindinginfo"],function(t,e,n){var a=t("crm-modules/common/util"),o=t("crm-widget/table/table"),i=t("./template/index-html"),d=t("./component/import-dialog/import-dialog"),r=t("./component/export-dialog/export-dialog"),s=t("./component/table-header/table-header"),c=t("../../model/model"),l=t("../editbindinginfo-udesk/editbindinginfo"),t=Backbone.View.extend({initialize:function(t){var e=this;e.$$={},e.setElement(t.wrapper),e.opts=t,e.model=new c(Object.assign(t.data,{data:t.data})),e.initSeatTypeOption(),e.render()},render:function(){var e=this;this.$el.html(i()),$(this.$el).show(),e.editBindingData={},"function"==typeof l.getEditBindData?l.getEditBindData().then(function(t){e.initTable(),e.editBindingData=t}):e.initTable(),this.initTableHeader()},initTableHeader:function(){var e=this,t=new s({el:document.createElement("div"),propsData:{}});t.$on("change",function(t){e.tableSearch({keyword:t.searchWord,viewType:t.type})}),this.$(".table-header").append(t.$el),this.$$.tableHeader=t},tableSearch:function(t){this.dt.setParam(t,!0,!0)},refresh:function(){this.dt.setParam({},!0)},events:{"click .j-add":"onAdd","click .j-import":"onImport","click .j-export":"onExport","click .j-del":"onDel"},initSeatTypeOption:function(){var e=this;this.opts.seatType=new Promise(function(t){t([{value:1,name:$t("电话")},{value:2,name:$t("分机")}])}).then(function(t){return e.opts.seatType=t})},initTable:function(){var i=this;$(i.$el).show(),i.dt=new o({$el:i.$(".table-content"),title:"",url:"/EM1HESERVICE2/eservice/callcenter/seat/queryUserBindPage",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!1,trHandle:!1,showPage:!0,stopWheelEvent:!0,postData:{viewType:"all",tenantBindId:i.opts.data.id},operate:{pos:"T",btns:[{text:$t("新建"),className:"j-add"}]},batchBtns:[],columns:[{data:"userName",title:$t("员工")},{data:"mobile",title:$t("手机号")},{data:"mainDepartmentName",title:$t("主属部门")},{data:"seatId",title:$t("第三方帐号")},{data:null,title:$t("操作"),lastFixed:!0,render:function(t,e,n){return'<div class="btns"><a class="j-edit">'+$t("编辑")+'</a><a class="j-del-btn">'+$t("解绑")+"</a></div>"}}],formatData:function(t){t=t.data;return{data:t.data||[],totalCount:t.recordSize||0}},initComplete:function(t){}}),i.dt.on("trclick",function(t,e,n){n.hasClass("j-edit")?i.onEdit(t):n.hasClass("j-del-btn")&&i.onDel(t)})},filterAccountData:function(t,e){return t.filter(function(t){if(-1!==t.nickName.indexOf(e)||t.phoneNum===e)return!0})},onImport:function(){d.$show()},onExport:function(){r.$show({selectedDataList:this.dt.getCheckedData()||[]})},onAdd:function(){var t=this;t.EditBindingInfoWidget&&(t.EditBindingInfoWidget.destroy(),t.EditBindingInfoWidget=null),t.EditBindingInfoWidget=new l(_objectSpread({title:$t("新建绑定信息"),type:"add",seatType:t.opts.seatType,tenantBindId:this.opts.data.id},t.editBindingData)),t.EditBindingInfoWidget.on("success",function(){t.refresh()}),t.EditBindingInfoWidget.show({data:{tenantBindId:t.opts.data.id}})},onEdit:function(t){var e=this;e.EditBindingInfoWidget&&(e.EditBindingInfoWidget.destroy(),e.EditBindingInfoWidget=null),e.EditBindingInfoWidget=new l(_objectSpread({title:$t("编辑绑定信息"),type:"edit",groupId:t.id,seatType:e.opts.seatType,tenantBindId:this.opts.data.id},e.editBindingData)),e.EditBindingInfoWidget.on("success",function(){$(".dt-ipt").val(""),$(".dt-sc-reset").hide(),e.refresh()}),e.EditBindingInfoWidget.show({data:_objectSpread(_objectSpread(_objectSpread({},t.extraData||{}),t),{},{tenantBindId:this.opts.data.id})})},onDel:function(t){var e=this,n=e.dt.getCheckedData(),i=[],o=(_.each(n,function(t,e){i.push(t.id)}),a.confirm($t("解绑后，该帐号再无法在第三方平台访问CRM信息，确认解绑吗?"),$t("确定"),function(){a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/deleteUserBind",data:{id:t.id||i.join(",")},success:function(t){0==t.Result.StatusCode&&($(".dt-ipt").val(""),$(".dt-sc-reset").hide(),a.remind(1,$t("操作成功")),o.destroy(),e.refresh())}})}))},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var e=this,t=this;t.dt&&t.dt.destroy(),t.dt=t.$el=t.el=t.events=t.options=null,Object.keys(this.$$||{}).forEach(function(t){t=e.$$[t];t.$destroy&&t.$destroy(),t.destroy&&t.destroy()}),this.$$=null}});n.exports=t});
define("crm-setting/callcenter/accountbinding/accountbinding-udesk/component/export-dialog/export-dialog",[],function(n,t,e){var i=Vue.extend({name:"export-dialog",template:'\n    <fx-dialog\n      :visible="showPanel"\n      @close="handleDialogClose"\n      custom-class=""\n      width="480px"\n      :title="$t(\'导出文件\')"\n      append-to-body\n    >\n      <div class="editor-header"></div>\n      <div class="editor-body">\n        <div class="btn-operate-area">\n          <fx-button type="primary" @click="exportFile()" size="small">\n            {{ $t(\'导出所有数据\') }}\n          </fx-button>\n          <fx-button type="primary" @click="exportFile(selectedDataList)" size="small">\n            {{ $t(\'导出当前筛选数据\') }}\n          </fx-button>\n        </div>\n      </div>\n      <span slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="handleSubmit" size="small">\n          {{ $t(\'确定\') }}\n        </fx-button>\n        <fx-button @click="handleDialogClose" size="small">\n          {{ $t(\'取消\') }}\n        </fx-button>\n      </span>\n    </fx-dialog>\n\t',props:{selectedDataList:{type:Array,default:function(){return[]}}},data:function(){return{showPanel:!1}},methods:{exportFile:function(n){},handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){this.$emit("submit")}},mounted:function(){},created:function(){}});i.$show=function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(n,t){var e=new i({el:document.createElement("div"),propsData:o});e.$on("hide",function(){e.showPanel=!1,setTimeout(function(){e.$destroy(),e.$el.remove()},1e3)}),e.$on("submit",function(){e.$emit("hide"),n.apply(void 0,arguments)}),e.$on("cancel",function(){e.$emit("hide"),t.apply(void 0,arguments)}),$("body").append(e.$el),setTimeout(function(){e.showPanel=!0},20),$("body").append(e.$el)})},e.exports=i});
define("crm-setting/callcenter/accountbinding/accountbinding-udesk/component/import-dialog/import-dialog",[],function(n,e,t){var i=Vue.extend({name:"import-dialog",template:'\n    <fx-dialog\n      :visible="showPanel"\n      @close="handleDialogClose"\n      custom-class=""\n      width="480px"\n      :title="$t(\'导入文件\')"\n      append-to-body\n    >\n      <div class="editor-header"></div>\n      <div class="editor-body">\n        <fx-upload\n          class="upload"\n          ref="fileUploader"\n          :accept="\'.xls,.xlsx\'"\n        >\n          <fx-button size="small" type="primary">{{$t(\'点击上传\')}}</fx-button>\n        </fx-upload>\n      </div>\n      <span slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="handleSubmit" size="small">\n          {{ $t(\'确定\') }}\n        </fx-button>\n        <fx-button @click="handleDialogClose" size="small">\n          {{ $t(\'取消\') }}\n        </fx-button>\n      </span>\n    </fx-dialog>\n\t',props:{},data:function(){return{showPanel:!1}},methods:{handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){this.$refs.fileUploader.getFiles(),this.$emit("submit")}},mounted:function(){},created:function(){}});i.$show=function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(n,e){var t=new i({el:document.createElement("div"),propsData:o});t.$on("hide",function(){t.showPanel=!1,setTimeout(function(){t.$destroy(),t.$el.remove()},1e3)}),t.$on("submit",function(){t.$emit("hide"),n.apply(void 0,arguments)}),t.$on("cancel",function(){t.$emit("hide"),e.apply(void 0,arguments)}),$("body").append(t.$el),setTimeout(function(){t.showPanel=!0},20),$("body").append(t.$el)})},t.exports=i});
define("crm-setting/callcenter/accountbinding/accountbinding-udesk/component/table-header/table-header",[],function(e,n,a){a.exports=Vue.extend({name:"table-header",template:'\n    <div class="callcenter__table-header">\n      <fx-input\n        v-model="searchWord"\n        class="search-input"\n        :placeholder="$t(\'请输入员工、第三方账号、座席绑定电话\')"\n        prefix-icon="el-icon-search"\n        size="small"\n        @keyup.enter.native="search"\n        @clear="search"\n        clearable\n      >\n      </fx-input>\n    </div>\n  ',props:{},data:function(){return{searchWord:"",currentTypeValue:"all",typeList:[{label:$t("所有"),value:"all"},{label:$t("已绑定座席"),value:"bound"},{label:$t("未绑定座席"),value:"unbind"}]}},watch:{},computed:{},methods:{emitChange:function(){this.$emit("change",{searchWord:this.searchWord})},search:function(){this.emitChange()}},created:function(){}})});
define("crm-setting/callcenter/accountbinding/accountbinding-udesk/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="account-binding__tian-run"> <div class="table-header"></div> <div class="table-content"></div> </div>';
        }
        return __p;
    };
});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/accountbinding/accountbinding",["crm-modules/common/util","crm-widget/table/table","./component/template/index-html","./component/table-header/table-header","./component/import-dialog/import-dialog","./component/export-dialog/export-dialog","./editbindinginfo-default/editbindinginfo","./editbindinginfo-zhichi/editbindinginfo","./editbindinginfo-tianrun/editbindinginfo","./editbindinginfo-heli/editbindinginfo","./editbindinginfo-tianhong/editbindinginfo","./accountbinding-tianrun/accountbinding","./accountbinding-udesk/accountbinding","./accountbinging-heli/accountbinging-heli"],function(t,e,n){var d=t("crm-modules/common/util"),o=t("crm-widget/table/table"),i=t("./component/template/index-html"),a=t("./component/table-header/table-header"),r=t("./component/import-dialog/import-dialog"),c=t("./component/export-dialog/export-dialog"),s=function(){},l=t("./editbindinginfo-default/editbindinginfo"),u=t("./editbindinginfo-zhichi/editbindinginfo"),f=t("./editbindinginfo-tianrun/editbindinginfo"),p=t("./editbindinginfo-heli/editbindinginfo"),g=t("./editbindinginfo-tianhong/editbindinginfo"),b=t("./accountbinding-tianrun/accountbinding"),h=t("./accountbinding-udesk/accountbinding"),t=t("./accountbinging-heli/accountbinging-heli"),m={3:p,7:u,8:f,10:g},y={3:t,8:b,9:h},v=Backbone.View.extend({initialize:function(t){s=m[t.data.serviceProvider]||l;var e=this;e.$$={},e.setElement(t.wrapper),e.opts=t,e.initSeatTypeOption(),e.tenantBindId=t.data.id,e.render()},render:function(){var e=this;this.$el.html(i()),$(this.$el).show(),e.editBindingData={},"function"==typeof s.getEditBindData?s.getEditBindData().then(function(t){e.initTable(),e.editBindingData=t}):e.initTable(),e.initTableHeader()},initTableHeader:function(){var e=this,t=new a({el:document.createElement("div"),propsData:{}});t.$on("change",function(t){e.tableSearch({keyword:t.searchWord,viewType:t.type})}),this.$(".table-header").append(t.$el),this.$$.tableHeader=t},tableSearch:function(t){this.dt.setParam(t,!0,!0)},refresh:function(){this.dt.setParam({},!0)},events:{"click .j-add":"onAdd","click .j-import":"onImport","click .j-export":"onExport","click .j-del":"onDel"},initSeatTypeOption:function(){var e=this;this.opts.seatType=new Promise(function(t){t([{value:1,name:$t("电话")},{value:2,name:$t("分机")}])}).then(function(t){return e.opts.seatType=t})},initTable:function(){var i=this;$(i.$el).show(),i.dt=new o({$el:i.$(".table-content"),title:"",url:"/EM1HESERVICE2/eservice/callcenter/seat/queryUserBindPage",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!1,trHandle:!1,showPage:!0,stopWheelEvent:!0,postData:{tenantBindId:i.opts.data.id},operate:{pos:"T",btns:[{text:$t("新建"),className:"j-add"}]},batchBtns:[],columns:[{data:"userName",title:$t("员工")},{data:"mobile",title:$t("手机号")},{data:"mainDepartmentName",title:$t("主属部门")},{data:"seatId",title:$t("第三方帐号")},{data:null,title:$t("操作"),lastFixed:!0,render:function(t,e,n){return'<div class="btns"><a class="j-edit">'+$t("编辑")+'</a><a class="j-del-btn">'+$t("解绑")+"</a></div>"}}],formatData:function(t){return t.data&&$.extend(!0,[],t.data.data),{data:t.data?t.data.data:[],totalCount:t.data?t.data.recordSize:0}},initComplete:function(t){}}),i.dt.on("trclick",function(t,e,n){n.hasClass("j-edit")?i.onEdit(t):n.hasClass("j-del-btn")&&i.onDel(t)})},filterAccountData:function(t,e){return t.filter(function(t){if(-1!==t.nickName.indexOf(e)||t.phoneNum===e)return!0})},onImport:function(){r.$show()},onExport:function(){c.$show({selectedDataList:this.dt.getCheckedData()||[]})},onAdd:function(){var t=this;t.EditBindingInfoWidget&&(t.EditBindingInfoWidget.destroy(),t.EditBindingInfoWidget=null),t.EditBindingInfoWidget=new s(_objectSpread({title:$t("新建绑定信息"),type:"add",seatType:t.opts.seatType,tenantBindId:t.opts.data.id},t.editBindingData)),t.EditBindingInfoWidget.on("success",function(){t.refresh()}),t.EditBindingInfoWidget.show({data:{tenantBindId:t.opts.data.id}})},onEdit:function(t){var e=this;e.EditBindingInfoWidget&&(e.EditBindingInfoWidget.destroy(),e.EditBindingInfoWidget=null),e.EditBindingInfoWidget=new s(_objectSpread({title:$t("编辑绑定信息"),type:"edit",groupId:t.id,seatType:e.opts.seatType,tenantBindId:e.opts.data.id},e.editBindingData)),e.EditBindingInfoWidget.on("success",function(){$(".dt-ipt").val(""),$(".dt-sc-reset").hide(),e.refresh()}),e.EditBindingInfoWidget.show({data:Object.assign(t,{tenantBindId:e.opts.data.id})})},onDel:function(t){var e=this,n=e.dt.getCheckedData(),i=[],o=(_.each(n,function(t,e){i.push(t.id)}),d.confirm($t("解绑后，该帐号再无法在第三方平台访问CRM信息，确认解绑吗?"),$t("确定"),function(){d.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/deleteUserBind",data:{id:t.id||i.join(",")},success:function(t){0==t.Result.StatusCode&&($(".dt-ipt").val(""),$(".dt-sc-reset").hide(),d.remind(1,$t("操作成功")),o.destroy(),e.refresh())}})}))},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var e=this,t=this;t.dt&&t.dt.destroy(),t.dt=t.$el=t.el=t.events=t.options=null,Object.keys(this.$$||{}).forEach(function(t){t=e.$$[t];t.$destroy&&t.$destroy(),t.destroy&&t.destroy()}),this.$$=null}});n.exports=function(t){return new(y[t.data.serviceProvider]||v)(t)}});
define("crm-setting/callcenter/accountbinding/accountbinging-heli/accountbinging-heli",["crm-modules/common/util","crm-widget/table/table","../editbindinginfo-heli/editbindinginfo","./components/table-header/table-header","../accountbind-common/accountbind-common"],function(t,e,n){t("crm-modules/common/util");var i=t("crm-widget/table/table"),a=t("../editbindinginfo-heli/editbindinginfo"),d=t("./components/table-header/table-header"),t=t("../accountbind-common/accountbind-common").extend({hanleGetBindingComMap:function(){return a},handleGetTableHeader:function(){return d},initTable:function(){var a=this;a.dt=new i({$el:a.$(".table-content"),title:"",url:"/EM1HESERVICE2/eservice/callcenter/seat/queryUserBindPage",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!1,trHandle:!1,showPage:!0,stopWheelEvent:!0,postData:{viewType:"all",tenantBindId:a.opts.data.id},operate:{pos:"T",btns:[{text:$t("新建"),className:"j-add"}]},batchBtns:[],columns:[{data:"userName",title:$t("员工")},{data:"mobile",title:$t("手机号")},{data:"mainDepartmentName",title:$t("主属部门")},{data:"seatId",title:$t("第三方帐号")},{data:"activeLabel",title:$t("座席状态")},{data:null,title:$t("操作"),lastFixed:!0,render:function(t,e,n){return'<div class="btns"><a class="j-edit">'+$t("编辑")+'</a><a class="'.concat(n.userId?"j-del-btn":"disabled",'">')+$t("解绑")+"</a></div>"}}],formatData:function(t){var e={1:$t("电话"),2:$t("分机"),3:$t("软电话")},n={0:$t("停用"),1:$t("启用"),"-1":$t("无效座席")},a={1:$t("正常"),2:$t("已停用"),3:$t("已删除")},t=t.data,i=(t.data||[]).map(function(t){return Object.assign(t,{thirdQnosLabel:(t.thirdQnos||[]).join(","),bindTypeLabel:e[t.bindType],activeLabel:n[t.active],runStatusLabel:a[t.runStatus]})});return tempData=$.extend(!0,[],i),{data:i,totalCount:t.recordSize||0}},initComplete:function(t){}}),a.dt.on("trclick",function(t,e,n){n.hasClass("j-edit")?a.onEdit(t):n.hasClass("j-del-btn")&&a.onDel(t)})}});n.exports=t});
define("crm-setting/callcenter/accountbinding/accountbinging-heli/components/table-header/table-header",[],function(e,n,t){t.exports=Vue.extend({name:"table-header",template:'\n    <div class="callcenter__table-header-heli">\n      <fx-dropdown trigger="click" @command="handleTypeChange" class="header-type">\n        <span class="el-dropdown-link">\n          {{ currentType.label }}<i class="el-icon-arrow-down el-icon--right"></i>\n        </span>\n        <fx-dropdown-menu slot="dropdown">\n          <fx-dropdown-item \n            v-for="typeData of typeList" \n            :key="typeData.value"\n            :command="typeData.value"\n          >{{ typeData.label }}</fx-dropdown-item>\n        </fx-dropdown-menu>\n      </fx-dropdown>\n      <fx-input\n        v-model="searchWord"\n        class="search-input"\n        :placeholder="$t(\'请输入员工、第三方账号、座席绑定电话\')"\n        prefix-icon="el-icon-search"\n        size="small"\n        @keyup.enter.native="search"\n        @clear="search"\n        clearable\n      >\n      </fx-input>\n    </div>\n  ',props:{},data:function(){return{searchWord:"",currentTypeValue:"all",typeList:[{label:$t("所有"),value:"all"},{label:$t("已绑定座席"),value:"bound"},{label:$t("未绑定座席"),value:"unbind"}]}},watch:{currentTypeValue:function(e){this.emitChange()}},computed:{currentType:function(){var n=this;return this.typeList.find(function(e){return e.value===n.currentTypeValue})}},methods:{emitChange:function(){this.$emit("change",{type:this.currentTypeValue,searchWord:this.searchWord})},handleTypeChange:function(e){this.currentTypeValue=e},search:function(){this.emitChange()}},created:function(){}})});
define("crm-setting/callcenter/accountbinding/component/export-dialog/export-dialog",[],function(n,t,e){var i=Vue.extend({name:"export-dialog",template:'\n    <fx-dialog\n      :visible="showPanel"\n      @close="handleDialogClose"\n      custom-class=""\n      width="480px"\n      :title="$t(\'导出文件\')"\n      append-to-body\n    >\n      <div class="editor-header"></div>\n      <div class="editor-body">\n        <div class="btn-operate-area">\n          <fx-button type="primary" @click="exportFile()" size="small">\n            {{ $t(\'导出所有数据\') }}\n          </fx-button>\n          <fx-button type="primary" @click="exportFile(selectedDataList)" size="small">\n            {{ $t(\'导出当前筛选数据\') }}\n          </fx-button>\n        </div>\n      </div>\n      <span slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="handleSubmit" size="small">\n          {{ $t(\'确定\') }}\n        </fx-button>\n        <fx-button @click="handleDialogClose" size="small">\n          {{ $t(\'取消\') }}\n        </fx-button>\n      </span>\n    </fx-dialog>\n\t',props:{selectedDataList:{type:Array,default:function(){return[]}}},data:function(){return{showPanel:!1}},methods:{exportFile:function(n){},handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){this.$emit("submit")}},mounted:function(){},created:function(){}});i.$show=function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(n,t){var e=new i({el:document.createElement("div"),propsData:o});e.$on("hide",function(){e.showPanel=!1,setTimeout(function(){e.$destroy(),e.$el.remove()},1e3)}),e.$on("submit",function(){e.$emit("hide"),n.apply(void 0,arguments)}),e.$on("cancel",function(){e.$emit("hide"),t.apply(void 0,arguments)}),$("body").append(e.$el),setTimeout(function(){e.showPanel=!0},20),$("body").append(e.$el)})},e.exports=i});
define("crm-setting/callcenter/accountbinding/component/import-dialog/import-dialog",[],function(n,e,t){var i=Vue.extend({name:"import-dialog",template:'\n    <fx-dialog\n      :visible="showPanel"\n      @close="handleDialogClose"\n      custom-class=""\n      width="480px"\n      :title="$t(\'导入文件\')"\n      append-to-body\n    >\n      <div class="editor-header"></div>\n      <div class="editor-body">\n        <fx-upload\n          class="upload"\n          ref="fileUploader"\n          :accept="\'.xls,.xlsx\'"\n        >\n          <fx-button size="small" type="primary">{{$t(\'点击上传\')}}</fx-button>\n        </fx-upload>\n      </div>\n      <span slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="handleSubmit" size="small">\n          {{ $t(\'确定\') }}\n        </fx-button>\n        <fx-button @click="handleDialogClose" size="small">\n          {{ $t(\'取消\') }}\n        </fx-button>\n      </span>\n    </fx-dialog>\n\t',props:{},data:function(){return{showPanel:!1}},methods:{handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){this.$refs.fileUploader.getFiles(),this.$emit("submit")}},mounted:function(){},created:function(){}});i.$show=function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(n,e){var t=new i({el:document.createElement("div"),propsData:o});t.$on("hide",function(){t.showPanel=!1,setTimeout(function(){t.$destroy(),t.$el.remove()},1e3)}),t.$on("submit",function(){t.$emit("hide"),n.apply(void 0,arguments)}),t.$on("cancel",function(){t.$emit("hide"),e.apply(void 0,arguments)}),$("body").append(t.$el),setTimeout(function(){t.showPanel=!0},20),$("body").append(t.$el)})},t.exports=i});
define("crm-setting/callcenter/accountbinding/component/table-header/table-header",[],function(e,n,a){a.exports=Vue.extend({name:"table-header",template:'\n    <div class="callcenter__table-header">\n      <fx-input\n        v-model="searchWord"\n        class="search-input"\n        :placeholder="$t(\'请输入员工、第三方账号、座席绑定电话\')"\n        prefix-icon="el-icon-search"\n        size="small"\n        @keyup.enter.native="search"\n        @clear="search"\n        clearable\n      >\n      </fx-input>\n    </div>\n  ',props:{},data:function(){return{searchWord:"",currentTypeValue:"all",typeList:[{label:$t("所有"),value:"all"},{label:$t("已绑定座席"),value:"bound"},{label:$t("未绑定座席"),value:"unbind"}]}},watch:{},computed:{},methods:{emitChange:function(){this.$emit("change",{searchWord:this.searchWord})},search:function(){this.emitChange()}},created:function(){}})});
define("crm-setting/callcenter/accountbinding/component/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="account-binding__tian-run"> <div class="table-header"></div> <div class="table-content"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/accountbinding/editbindinginfo-default/editbindinginfo",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","./template/edit-bindinginfo-html"],function(e,t,i){var r=e("crm-modules/common/util"),s=e("crm-widget/dialog/dialog"),n=e("crm-widget/selector/selector"),d=(e("crm-widget/select/select"),e("./template/edit-bindinginfo-html")),l=s.extend({attrs:{title:$t("新建绑定信息"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-callcenter"},events:{"click .seatId":"_hideError","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=l.superclass.show.call(this);return this.set(e||{data:{}}),this.setContent(d({data:this.get("data")})),this.initSelector(),t},initSelector:function(){this.employeeWidget=new n({$wrap:this.$(".j-selector"),zIndex:+this.get("zIndex")+10,member:!0,single:!0,label:$t("选择员工"),defaultSelectedItems:{member:this.get("data").userId?[this.get("data").userId]:[]}}),this.employeeWidget.on("change",function(){r.hideErrmsg($(".j-selector"))})},onSubmit:function(){var e=this.employeeWidget.getValue(),t=this.$(".j-selector"),i=this.$(".seatId"),s=$.trim(i.val());return e.member.length?s?void this.submit({id:this.get("data").id,userId:e.member[0],seatId:s,tenantBindId:this.get("data").tenantBindId}):(r.showErrmsg(i,$t("请输入第三方帐号！")),!1):(r.showErrmsg(t,$t("请选择员工！")),!1)},_hideError:function(e){e=$(e.target);r.hideErrmsg(e)},submit:function(e){CRM.util.showLoading_tip($t("提交中..."));var t=this;r.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/saveUserBind",data:e,success:function(e){CRM.util.hideLoading_tip(),0==e.Result.StatusCode&&0==e.Value.errorCode?(r.remind(1,$t("操作成功！")),t.trigger("success"),t.hide()):r.remind(3,e.Value.errorMessage||$t("操作失败！"))}},{})},hide:function(){this.destroy()},destroy:function(){return this.employeeWidget&&this.employeeWidget.destroy(),l.superclass.destroy.call(this)}});i.exports=l});
define("crm-setting/callcenter/accountbinding/editbindinginfo-default/template/edit-bindinginfo-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb" title="' + ((__t = $t("选择员工")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("选择员工")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-selector"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方帐号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方帐号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatId" ';
            if (data) {
                __p += 'value="' + ((__t = data.seatId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("eservice.crm.callcenter.need_third_seat_number")) == null ? "" : __t) + '"> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/accountbinding/editbindinginfo-heli/editbindinginfo",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","./template/edit-bindinginfo-html"],function(e,t,i){var d=e("crm-modules/common/util"),s=e("crm-widget/dialog/dialog"),n=e("crm-widget/selector/selector"),r=e("crm-widget/select/select"),a=e("./template/edit-bindinginfo-html"),l=s.extend({attrs:{title:$t("新建绑定信息"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-callcenter"},events:{"click .seatId":"_hideError","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=l.superclass.show.call(this);return this.set(e||{data:{}}),this.setContent(a({data:this.get("data")})),this.initSelector(),this.initSelect(),t},initSelector:function(){this.employeeWidget=new n({$wrap:this.$(".j-selector"),zIndex:+this.get("zIndex")+10,member:!0,single:!0,label:$t("选择员工"),defaultSelectedItems:{member:this.get("data").userId?[this.get("data").userId]:[]}}),this.employeeWidget.on("change",function(){d.hideErrmsg($(".j-selector"))})},initSelect:function(){var e=[{name:$t("直线电话/手机"),value:1},{name:$t("电脑客户端"),value:3},{name:$t("语音网关/ip话机"),value:2}];this.seatTypeWidget=new r({$wrap:this.$(".j-select"),options:e,defaultVal:this.get("data").bindType}),this.seatTypeWidget.on("change",function(){d.hideErrmsg($(".j-select"))})},onSubmit:function(){var e=this.employeeWidget.getValue(),t=this.$(".j-selector"),i=this.$(".seatId"),s=$.trim(i.val()),n=this.seatTypeWidget.getValue(),r=this.$(".bindType");return e.member.length?void 0===n?(d.showErrmsg(r,$t("eservice.crm.callcenter.input_seat_phone_type")),!1):s?void this.submit({id:this.get("data").id,userId:e.member[0],seatId:s,bindType:n,tenantBindId:this.get("data").tenantBindId}):(d.showErrmsg(i,$t("请输入第三方帐号！")),!1):(d.showErrmsg(t,$t("请选择员工！")),!1)},_hideError:function(e){e=$(e.target);d.hideErrmsg(e)},submit:function(e){CRM.util.showLoading_tip($t("提交中..."));var t=this;d.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/saveUserBind",data:e,success:function(e){CRM.util.hideLoading_tip(),0==e.Result.StatusCode&&0==e.Value.errorCode?(d.remind(1,$t("操作成功！")),t.trigger("success"),t.hide()):d.remind(3,e.Value.errorMessage||$t("操作失败！"))}},{})},hide:function(){this.destroy()},destroy:function(){return this.employeeWidget&&this.employeeWidget.destroy(),l.superclass.destroy.call(this)}});i.exports=l});
define("crm-setting/callcenter/accountbinding/editbindinginfo-heli/template/edit-bindinginfo-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb" title="' + ((__t = $t("选择员工")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("选择员工")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-selector"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方帐号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方帐号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatId" ';
            if (data) {
                __p += 'value="' + ((__t = data.seatId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("eservice.crm.callcenter.need_third_seat_number")) == null ? "" : __t) + '"> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("登录方式")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("登录方式")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-select bindType"></div> </div> </div> <!-- <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方账号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方账号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt thirdAccount" ';
            if (data) {
                __p += 'value="' + ((__t = data.thirdAccount) == null ? "" : __t) + '"';
            }
            __p += '> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方密码")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方密码")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt thirdPwd" type="password" ';
            if (data) {
                __p += 'value="' + ((__t = data.thirdPwd) == null ? "" : __t) + '"';
            }
            __p += "> </div> </div> --> </div>";
        }
        return __p;
    };
});
define("crm-setting/callcenter/accountbinding/editbindinginfo-tianhong/editbindinginfo",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","./template/edit-bindinginfo-html"],function(e,t,i){var o=e("crm-modules/common/util"),s=e("crm-widget/dialog/dialog"),n=e("crm-widget/selector/selector"),r=e("crm-widget/select/select"),d=e("./template/edit-bindinginfo-html"),c=s.extend({attrs:{title:$t("新建绑定信息"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-callcenter"},events:{"click .seatId":"_hideError","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=c.superclass.show.call(this);return this.set(e||{data:{}}),this.setContent(d({data:this.get("data")})),this.initSelector(),this.initSelect(),t},initSelector:function(){this.employeeWidget=new n({$wrap:this.$(".j-selector"),zIndex:+this.get("zIndex")+10,member:!0,single:!0,label:$t("选择员工"),defaultSelectedItems:{member:this.get("data").userId?[this.get("data").userId]:[]}}),this.employeeWidget.on("change",function(){o.hideErrmsg($(".j-selector"))})},initSelect:function(){var e,t=this,i=this.get("seatType");Array.isArray(i)?e=i:(e=[],i.then(function(e){e.forEach(function(e){t.seatTypeWidget.addOption(e)})})),this.seatTypeWidget=new r({$wrap:this.$(".j-select"),options:e,defaultVal:this.get("data").bindType}),this.seatTypeWidget.on("change",function(){o.hideErrmsg($(".j-select"))})},onSubmit:function(){var e=this.employeeWidget.getValue(),t=this.$(".j-selector"),i=this.seatTypeWidget.getValue(),s=this.$(".j-select"),n=this.$(".bindTel"),r=$.trim(n.val()),d=this.$(".seatId"),c=$.trim(d.val()),a=this.$(".thirdPwd"),l=$.trim(a.val());return e.member.length?c?void 0===i?(o.showErrmsg(s,$t("eservice.crm.callcenter.input_seat_phone_type")),!1):void 0===r?(o.showErrmsg(n,$t("eservice.crm.callcenter.input_seat_bing_phone")),!1):l?void this.submit({id:this.get("data").id,userId:e.member[0],thirdAccount:c,seatId:c,thirdPwd:l,bindType:i,bindTel:r,tenantBindId:this.get("data").tenantBindId}):(o.showErrmsg(a,$t("请输入第三方密码！")),!1):(o.showErrmsg(d,$t("eservice.crm.callcenter.seat_number")),!1):(o.showErrmsg(t,$t("请选择员工！")),!1)},_hideError:function(e){e=$(e.target);o.hideErrmsg(e)},submit:function(e){CRM.util.showLoading_tip($t("提交中..."));var t=this;o.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/saveUserBind",data:e,success:function(e){CRM.util.hideLoading_tip(),0==e.Result.StatusCode&&0==e.Value.errorCode?(o.remind(1,$t("操作成功！")),t.trigger("success"),t.hide()):o.remind(3,e.Value.errorMessage||$t("操作失败！"))}},{})},hide:function(){this.destroy()},destroy:function(){return this.employeeWidget&&this.employeeWidget.destroy(),c.superclass.destroy.call(this)}});i.exports=c});
define("crm-setting/callcenter/accountbinding/editbindinginfo-tianhong/template/edit-bindinginfo-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb" title="' + ((__t = $t("选择员工")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("选择员工")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-selector"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方帐号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方帐号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatId" ';
            if (data) {
                __p += 'value="' + ((__t = data.seatId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("eservice.crm.callcenter.need_third_seat_number")) == null ? "" : __t) + '"> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("eservice.crm.callcenter.seat_phone_type")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("eservice.crm.callcenter.seat_phone_type")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-select"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("eservice.crm.callcenter.seat_bing_phone")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("eservice.crm.callcenter.seat_bing_phone")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt bindTel" ';
            if (data) {
                __p += 'value="' + ((__t = data.bindTel) == null ? "" : __t) + '"';
            }
            __p += '> </div> </div> <!-- <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("坐席号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("坐席号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatId" ';
            if (data) {
                __p += 'value="' + ((__t = data.seatId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("对应第三方帐号")) == null ? "" : __t) + '"> </div> </div> --> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方密码")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方密码")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt thirdPwd" ';
            if (data) {
                __p += 'value="' + ((__t = data.thirdPwd) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("请输入第三方密码")) == null ? "" : __t) + '"> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/accountbinding/editbindinginfo-tianrun/editbindinginfo",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","./template/edit-bindinginfo-html"],function(t,e,i){var a=t("crm-modules/common/util"),s=t("crm-widget/dialog/dialog"),n=t("crm-widget/selector/selector"),r=t("crm-widget/select/select"),d=t("./template/edit-bindinginfo-html"),o=s.extend({attrs:{title:$t("新建绑定信息"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-callcenter dialog-tianrun"},events:{"click .seatId":"_hideError","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(t){var e=o.superclass.show.call(this);return this.$$||(this.$$={}),this.set(t||{data:{}}),this.setContent(d({data:this.get("data")})),this.initSelector(),this.initSelect(),e},initSelector:function(){this.$$.employeeWidget=new n({$wrap:this.$(".j-selector"),zIndex:+this.get("zIndex")+10,member:!0,single:!0,label:$t("选择员工"),defaultSelectedItems:{member:this.get("data").userId?[this.get("data").userId]:[]}}),this.$$.employeeWidget.on("change",function(){a.hideErrmsg($(".j-selector"))})},initSelect:function(){var t,e=this,i=this.get("seatType");Array.isArray(i)?t=i:(t=[],i.then(function(t){t.forEach(function(t){e.$$.seatTypeWidget.addOption(t)})})),this.$$.seatTypeWidget=new r({$wrap:this.$(".j-select"),options:t,defaultVal:this.get("data").bindType}),this.$$.seatTypeWidget.on("change",function(){a.hideErrmsg($(".j-select"))})},onSubmit:function(){var t=this.$$.employeeWidget.getValue(),e=this.$(".j-selector"),i=this.$$.seatTypeWidget.getValue(),s=this.$(".j-select"),n=this.$(".seatId"),r=$.trim(n.val()),d=this.$(".seatPhone"),o=$.trim(d.val());return t.member.length?r?void 0===i?(a.showErrmsg(s,$t("eservice.crm.callcenter.input_seat_phone_type")),!1):o?void this.submit({id:this.get("data").id,userId:t.member[0],seatId:r,bindType:i,bindTel:o,tenantBindId:this.get("data").tenantBindId}):(a.showErrmsg(d,$t("eservice.crm.callcenter.input_seat_bing_phone")),!1):(a.showErrmsg(n,$t("请输入第三方帐号！")),!1):(a.showErrmsg(e,$t("请选择员工！")),!1)},_hideError:function(t){t=$(t.target);a.hideErrmsg(t)},submit:function(t){CRM.util.showLoading_tip($t("提交中..."));var e=this;a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/saveUserBind",data:t,success:function(t){CRM.util.hideLoading_tip(),0==t.Result.StatusCode&&0==t.Value.errorCode?(a.remind(1,$t("操作成功！")),e.trigger("success"),e.hide()):a.remind(3,t.Value.errorMessage||$t("操作失败！"))}},{})},hide:function(){this.destroy()},destroy:function(){for(var t in this.trigger("destroy"),this.$$){var e=this.$$[t]||{};e.destroy&&"function"==typeof e.destroy?e.destroy():e.$destroy&&"function"==typeof e.$destroy&&e.$destroy(),delete this.$$[t]}return o.superclass.destroy.call(this)}});i.exports=o});
define("crm-setting/callcenter/accountbinding/editbindinginfo-tianrun/template/edit-bindinginfo-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form editbindinginfo-tianrun"> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb" title="' + ((__t = $t("选择员工")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("选择员工")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-selector"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方帐号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方帐号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatId" ';
            if (data) {
                __p += 'value="' + ((__t = data.seatId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("eservice.crm.callcenter.need_third_seat_number")) == null ? "" : __t) + '"> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("eservice.crm.callcenter.seat_phone_type")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("eservice.crm.callcenter.seat_phone_type")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-select"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("eservice.crm.callcenter.seat_bing_phone")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("eservice.crm.callcenter.seat_bing_phone")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatPhone" ';
            if (data) {
                __p += 'value="' + ((__t = data.bindTel) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("请输入坐席绑定电话")) == null ? "" : __t) + '"> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/accountbinding/editbindinginfo-udesk/editbindinginfo",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","./template/edit-bindinginfo-html"],function(e,t,i){var n=e("crm-modules/common/util"),s=e("crm-widget/dialog/dialog"),d=e("crm-widget/selector/selector"),r=(e("crm-widget/select/select"),e("./template/edit-bindinginfo-html")),l=s.extend({attrs:{title:$t("新建绑定信息"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-callcenter"},events:{"click .seatId":"_hideError","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=l.superclass.show.call(this);return this.set(e||{data:{}}),this.setContent(r({data:this.get("data")})),this.initSelector(),t},initSelector:function(){this.employeeWidget=new d({$wrap:this.$(".j-selector"),zIndex:+this.get("zIndex")+10,member:!0,single:!0,label:$t("选择员工"),defaultSelectedItems:{member:this.get("data").userId?[this.get("data").userId]:[]}}),this.employeeWidget.on("change",function(){n.hideErrmsg($(".j-selector"))})},onSubmit:function(){var e=this.employeeWidget.getValue(),t=this.$(".j-selector"),i=this.$(".seatId"),s=$.trim(i.val()),d=this.$(".udeskAgentId"),d=$.trim(d.val());return e.member.length?s?void this.submit({id:this.get("data").id,userId:e.member[0],seatId:s,udeskAgentId:d,tenantBindId:this.get("data").tenantBindId}):(n.showErrmsg(i,$t("请输入第三方帐号！")),!1):(n.showErrmsg(t,$t("请选择员工！")),!1)},_hideError:function(e){e=$(e.target);n.hideErrmsg(e)},submit:function(e){CRM.util.showLoading_tip($t("提交中..."));var t=this;n.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/saveUserBind",data:e,success:function(e){CRM.util.hideLoading_tip(),0==e.Result.StatusCode&&0==e.Value.errorCode?(n.remind(1,$t("操作成功！")),t.trigger("success"),t.hide()):n.remind(3,e.Value.errorMessage||$t("操作失败！"))}},{})},hide:function(){this.destroy()},destroy:function(){return this.employeeWidget&&this.employeeWidget.destroy(),l.superclass.destroy.call(this)}});i.exports=l});
define("crm-setting/callcenter/accountbinding/editbindinginfo-udesk/template/edit-bindinginfo-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb" title="' + ((__t = $t("选择员工")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("选择员工")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-selector"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方帐号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方帐号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatId" ';
            if (data) {
                __p += 'value="' + ((__t = data.seatId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("eservice.crm.callcenter.need_third_seat_number")) == null ? "" : __t) + '"> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("eservice.crm.callcenter.third_seat_id")) == null ? "" : __t) + '"> ' + ((__t = $t("eservice.crm.callcenter.third_seat_id")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt udeskAgentId" ';
            if (data) {
                __p += 'value="' + ((__t = data.udeskAgentId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("对应第三方坐席id")) == null ? "" : __t) + '"> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/accountbinding/editbindinginfo-zhichi/editbindinginfo",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","./template/edit-bindinginfo-html"],function(e,t,i){var r=e("crm-modules/common/util"),s=e("crm-widget/dialog/dialog"),n=e("crm-widget/selector/selector"),d=e("crm-widget/select/select"),l=e("./template/edit-bindinginfo-html"),o=s.extend({attrs:{title:$t("新建绑定信息"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-callcenter"},events:{"click .seatId":"_hideError","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=o.superclass.show.call(this);return this.set(e||{data:{}}),this.setContent(l({data:this.get("data")})),this.initSelector(),this.initSelect(),t},initSelector:function(){this.employeeWidget=new n({$wrap:this.$(".j-selector"),zIndex:+this.get("zIndex")+10,member:!0,single:!0,label:$t("选择员工"),defaultSelectedItems:{member:this.get("data").userId?[this.get("data").userId]:[]}}),this.employeeWidget.on("change",function(){r.hideErrmsg($(".j-selector"))})},initSelect:function(){var e=this,t=[{name:$t("网络电话"),value:1},{name:$t("sip话机"),value:2},{name:$t("手机"),value:3}];this.bindTypeWidget=new d({$wrap:this.$(".j-select"),options:t,defaultVal:this.get("data").bindType}),this.bindTypeWidget.on("change",function(){r.hideErrmsg($(".j-select"))}),this.one("destroy",function(){e.bindTypeWidget.destroy(),e.bindTypeWidget=null})},onSubmit:function(){var e=this.employeeWidget.getValue(),t=this.$(".j-selector"),i=this.$(".seatId"),s=$.trim(i.val()),n=this.bindTypeWidget.getValue(),d=this.$(".bindType");return e.member.length?s?void 0===n?(r.showErrmsg(d,$t("eservice.crm.callcenter.input_seat_phone_type")),!1):void this.submit({id:this.get("data").id,userId:e.member[0],seatId:s,bindType:n,tenantBindId:this.get("data").tenantBindId}):(r.showErrmsg(i,$t("请输入第三方帐号！")),!1):(r.showErrmsg(t,$t("请选择员工！")),!1)},_hideError:function(e){e=$(e.target);r.hideErrmsg(e)},submit:function(e){CRM.util.showLoading_tip($t("提交中..."));var t=this;r.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/seat/saveUserBind",data:e,success:function(e){CRM.util.hideLoading_tip(),0==e.Result.StatusCode&&0==e.Value.errorCode?(r.remind(1,$t("操作成功！")),t.trigger("success"),t.hide()):r.remind(3,e.Value.errorMessage||$t("操作失败！"))}},{})},hide:function(){this.destroy()},destroy:function(){return this.trigger("destroy"),this.employeeWidget&&this.employeeWidget.destroy(),o.superclass.destroy.call(this)}});i.exports=o});
define("crm-setting/callcenter/accountbinding/editbindinginfo-zhichi/template/edit-bindinginfo-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb" title="' + ((__t = $t("选择员工")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("选择员工")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-selector"></div> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("第三方帐号")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("第三方帐号")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <input class="b-g-ipt fm-ipt seatId" ';
            if (data) {
                __p += 'value="' + ((__t = data.seatId) == null ? "" : __t) + '"';
            }
            __p += ' placeholder="' + ((__t = $t("eservice.crm.callcenter.need_third_seat_number")) == null ? "" : __t) + '"> </div> </div> <div class="fm-item"> <label class="fm-lb" title="' + ((__t = $t("登录方式")) == null ? "" : __t) + '"> <em>*</em>' + ((__t = $t("登录方式")) == null ? "" : __t) + ' </label> <div class="fm-wrap"> <div class="j-select bindType"></div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/advanced/advanced",["crm-modules/common/util","crm-setting/callcenter/util","./template/advanced-html","./audio-transfer-setting/audio-transfer-setting","./ai-summary-setting/ai-summary-setting","../model/model"],function(t,e,n){t("crm-modules/common/util");var i=t("crm-setting/callcenter/util"),a=t("./template/advanced-html"),s=t("./audio-transfer-setting/audio-transfer-setting"),o=t("./ai-summary-setting/ai-summary-setting"),r=t("../model/model"),t=Backbone.View.extend({initialize:function(t){this.componentsInstance={},this.model=new r(Object.assign(t.data,{data:t.data})),this.setElement(t.wrapper),this.render()},events:{"click .j-save-access":"saveTemporaryInfo"},render:function(){this.renderPage()},loadConfigData:function(){var t=this;return this._loadConfigPromise||(this._loadConfigPromise=i.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/setting/queryTenantAdvancedSetting",params:0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}}),setTimeout(function(){t._loadConfigPromise=null},2e3)),this._loadConfigPromise},renderPage:function(t){var e=this;e.$el.html(a(this.options.data)),e.initAudioTransferSetting(),e.initAiSummarySetting()},show:function(){this.$el.show()},initAiSummarySetting:function(){var t=this,e=(this.componentsInstance.aiSummarySetting&&(this.componentsInstance.aiSummarySetting.$destroy(),this.componentsInstance.aiSummarySetting.$el.remove()),new o({el:document.createElement("div"),propsData:{loadConfigDataFn:this.loadConfigData.bind(this)}}));e.$on("refresh",function(){t.render()}),e.$on("submit",function(){t.submit()}),this.componentsInstance.aiSummarySetting=e,$("#aiSummarySetting").append(e.$el),this.once("destroy",function(){e.$destroy(),e.$el.remove()})},initAudioTransferSetting:function(){var t=this,e=(this.componentsInstance.audioTransferSetting&&(this.componentsInstance.audioTransferSetting.$destroy(),this.componentsInstance.audioTransferSetting.$el.remove()),new s({el:document.createElement("div"),propsData:{loadConfigDataFn:this.loadConfigData.bind(this)}}));e.$on("refresh",function(){t.render()}),e.$on("submit",function(){t.submit()}),this.componentsInstance.audioTransferSetting=e,$("#audioTransferSetting").append(e.$el),this.once("destroy",function(){e.$destroy(),e.$el.remove()})},submit:function(){},hide:function(){this.$el.hide()},destroy:function(){var t=this;t.trigger("destroy"),t.selectValidTime=null,t.initShareData1=null,t._select=null,this.undelegateEvents(),this.off()}});n.exports=t});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,i=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),i.push.apply(i,n)),i}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/advanced/ai-summary-setting/ai-summary-setting",["crm-modules/setting/callcenter/util"],function(t,e,n){var i,r=t("crm-modules/setting/callcenter/util");t.async("app-standalone/app",function(t){t.getModuleComp("prompt-template-selector/prompt-template-selector-vue.js").then(function(t){i=t})}),n.exports=Vue.extend({name:"ai-summary-setting",template:'\n\t\t<div class="callcenter__ai-summary-setting" v-if="isShow" v-loading="isLoading">\n\t\t\t<div class="setting-label">\n\t\t\t\t<p class="setting-label__text">{{ $t(\'eservice.callcenter.ai-summary-setting.title-desc\') /* AI摘要 */ }}</p>\n\t\t\t</div>\n\t\t\t<div class="setting-comp">\n        <fx-switch \n\t\t\t\t\tsize="small" \n\t\t\t\t\tv-model="isAiSummaryOpen"\n\t\t\t\t\t@change="onChangeData({ aiSummaryStatus: +isAiSummaryOpen })"\n\t\t\t\t></fx-switch>\n        <p class="setting-comp__setting-desc">{{ $t(\'eservice.callcenter.ai-summary-setting.setting-desc\') /* 开启后，AI将根据语音转文字的原始内容生成摘要。 */ }}</p>\n\t\t\t\t<div class="prompt-template-setting" style="margin-top: 11px" v-if="isAiSummaryOpen">\n\t\t\t\t\t{{ $t(\'提示词模板: \') + (promptTemplateMap[promptTemplateId].label || \'--\') }}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<p \n\t\t\t\tclass="sd-link-btn" style="margin-left: 14px; align-self: center;" \n\t\t\t\t@click="onPromptTemplateChange" \n\t\t\t\tv-if="isAiSummaryOpen"\n\t\t\t>{{$t(\'设置\')}}</p>\n\t\t</div>\n\t',props:{loadConfigDataFn:{type:Function,default:function(){return Promise.resolve()}}},data:function(){return{promptTemplateMap:{},promptTemplateId:"",isAiSummaryOpen:!1,isShow:!1,isLoading:0}},watch:{isShow:function(t){t&&$("#aiSummarySetting").css("display","block")}},computed:{},methods:{loadPromptTemplateOptions:function(){var i=this;return this.isLoading++,r.FHHApi({url:"/EM1HAIGC/prompt/query",params:{keyword:"",pageNumber:1,pageSize:1e3}}).finally(function(){i.isLoading--}).then(function(t){var t=(t=t.Value.list||[]).filter(function(t){return"ServiceRecordObj"===t.bindingObjectApiName}),e=[],n={};t.forEach(function(t){t={id:t.apiName,label:t.name,value:t.apiName};e.push(t),n[t.id]=t}),i.promptTemplateMap=n})},onChangeData:function(t){this.isAiSummaryOpen?this.onPromptTemplateChange(t):this.submitSetting(t)},submitSetting:function(t){var e=this;return this.isLoading++,r.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/setting/saveTenantAdvancedSetting",params:t}).finally(function(t){e.isLoading--})},onPromptTemplateChange:function(){var e=this,n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};i.$show({defaultPromptTemplateId:this.promptTemplateId,promptObjectApiName:"ServiceRecordObj"}).then(function(t){return e.submitSetting(_objectSpread({promptApiName:t.promptTemplateId},n))}).finally(function(){e.loadData()})},submit:function(){this.$emit("submit")},changeCalloutInform:function(){this.submit()},loadData:function(){var e=this;this.isLoading++,this.loadConfigDataFn().finally(function(t){e.isLoading--}).then(function(t){t=t.Value.data;e.isAiSummaryOpen=!!t.aiSummaryStatus,e.isShow=-1!==t.aiSummaryStatus,e.promptTemplateId=e.promptTemplateMap[t.promptApiName]?t.promptApiName:""})}},beforeCreate:function(){$("#aiSummarySetting").css("display","none")},mounted:function(){},created:function(){this.loadPromptTemplateOptions().then(this.loadData)}})});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,_toPropertyKey(a.key),a)}}function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/advanced/audio-transfer-setting/audio-transfer-setting",["crm-setting/callcenter/util","./quota-detail/quota-detail","app-standalone/components/help/help-vue"],function(t,e,n){var a=t("crm-setting/callcenter/util"),i=t("./quota-detail/quota-detail"),t=t("app-standalone/components/help/help-vue"),r=_createClass(function t(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=e.orderNumber,n=void 0===n?0:n,a=e.duration,a=void 0===a?0:a,i=e.leftDuration,i=void 0===i?0:i,r=e.effectTime,r=void 0===r?0:r,o=e.dueDate,o=void 0===o?0:o,e=e.status,e=void 0===e?0:e;_classCallCheck(this,t),this.orderNumber=n,this.duration=a,this.leftDuration=i,this.effectTime=r,this.dueDate=o,this.status=e},[{key:"durationLabel",get:function(){return a.formatDurationTime(this.duration)}},{key:"leftDurationLabel",get:function(){return a.formatDurationTime(this.leftDuration)}},{key:"effectTimeLabel",get:function(){return FS.moment(this.effectTime).format("YYYY-MM-DD HH:mm:ss")}},{key:"dueDateLabel",get:function(){return FS.moment(this.dueDate).format("YYYY-MM-DD HH:mm:ss")}},{key:"statusLabel",get:function(){return _defineProperty(_defineProperty({},!0,$t("生效中")),!1,$t("未生效"))[this.status]}}]);n.exports=Vue.extend({name:"audio-transfer-setting",template:'\n\t\t<div class="callcenter__audio-transfer-setting" v-loading="isLoading">\n\t\t\t<div class="setting-label">\n\t\t\t\t<p class="setting-label__text">{{ $t(\'语音转文字\') }}</p>\n\t\t\t</div>\n\t\t\t<div class="setting-comp">\n        <fx-switch \n\t\t\t\t\tsize="small" \n\t\t\t\t\tv-model="isAudioTransferOpen"\n\t\t\t\t\t:before-change="() => this.$confirm(\n\t\t\t\t\t\t$t(\'是否确认{{status}}\', { status: this.isAudioTransferOpen ? $t(\'关闭\') : $t(\'开启\') })\n\t\t\t\t\t)"\n\t\t\t\t\t@change="onChangeData({ voiceToTextStatus: +isAudioTransferOpen })"\n\t\t\t\t></fx-switch>\n        <p class="setting-comp__setting-desc">{{ $t(\'eservice.callcenter.audio-transfer-setting.setting-desc\') /* 开启后，呼叫中心同步到【通话记录】对象的录音文件可以手动或自动转为文字。 */ }}</p>\n\t\t\t\t<div class="setting-comp__auto-transfer" v-show="isAudioTransferOpen">\n\t\t\t\t\t<div class="auto-transfer__switch-wrap">\n\t\t\t\t\t\t<p class="auto-transfer__switch-label">{{ $t(\'eservice.callcenter.audio-transfer-setting.switch-label\') /* 自动语音转文字 */ }}</p>\n\t\t\t\t\t\t<sd-help\n\t\t\t\t\t\t\tstyle="margin-right: 10px;"\n\t\t\t\t\t\t\t:content="$t(\'eservice.callcenter.audio-transfer-setting.help-desc\') /* 开启后，录音文件将自动转为文字。 */"\n\t\t\t\t\t\t></sd-help>\n\t\t\t\t\t\t<fx-switch \n\t\t\t\t\t\t\tstyle="flex: 1;"\n\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\tv-model="isAutoTransferOpen"\n\t\t\t\t\t\t\t:before-change="() => this.$confirm(\n\t\t\t\t\t\t\t\t$t(\'是否确认{{status}}\', { status: this.isAutoTransferOpen ? $t(\'关闭\') : $t(\'开启\') })\n\t\t\t\t\t\t\t)"\n\t\t\t\t\t\t\t@change="onChangeData({ autoTurnTextStatus: +isAutoTransferOpen })"\n\t\t\t\t\t\t></fx-switch>\n\t\t\t\t\t\t<p class="table-header-wrapper">\n\t\t\t\t\t\t\t\x3c!-- <span class="header-btn sd-link-btn" @click="showDetail">{{ $t(\'查看明细\') }}</span> --\x3e\n\t\t\t\t\t\t</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<p class="sd-desc setting-comp__auto-transfer-desc"></p>\n\t\t\t\t</div>\n\t\t\t\t<div \n\t\t\t\t\tclass="setting-comp__quota-statistics" \n\t\t\t\t\tv-show="isAudioTransferOpen && false"\n\t\t\t\t>\n\t\t\t\t\t<fx-table\n\t\t\t\t\t\tclass="quota-statistics-table crm-style-table--new"\n\t\t\t\t\t\t:data="quotaStatisticsList"\n\t\t\t\t\t\tstyle="width: 100%"\n\t\t\t\t\t\t:max-height="500"\n\t\t\t\t\t\tborder\n\t\t\t\t\t>\n\t\t\t\t\t\t<fx-table-column \n\t\t\t\t\t\t\tmin-width="112" \n\t\t\t\t\t\t\tprop="orderNumber"\n\t\t\t\t\t\t\t:label="$t(\'eservice.callcenter.audio-transfer-setting.order-number\') /* 订单号 */"\n\t\t\t\t\t\t\t:formatter="tableFormatter"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column \n\t\t\t\t\t\t\tmin-width="112" \n\t\t\t\t\t\t\tprop="durationLabel"\n\t\t\t\t\t\t\t:label="$t(\'eservice.callcenter.audio-transfer-setting.duration-label\') /* 套餐时长 */"\n\t\t\t\t\t\t\t:formatter="tableFormatter"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column \n\t\t\t\t\t\t\tmin-width="260" \n\t\t\t\t\t\t\tprop="leftDurationLabel"\n\t\t\t\t\t\t\t:label="$t(\'eservice.callcenter.audio-transfer-setting.left-duration-label\') /* 剩余时长 */"\n\t\t\t\t\t\t\t:formatter="tableFormatter"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column \n\t\t\t\t\t\t\tmin-width="260" \n\t\t\t\t\t\t\tprop="effectTimeLabel"\n\t\t\t\t\t\t\t:label="$t(\'eservice.callcenter.audio-transfer-setting.effect-time-label\') /* 生效时间 */"\n\t\t\t\t\t\t\t:formatter="tableFormatter"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column \n\t\t\t\t\t\t\tmin-width="260" \n\t\t\t\t\t\t\tprop="dueDateLabel"\n\t\t\t\t\t\t\t:label="$t(\'eservice.callcenter.audio-transfer-setting.due-date-label\') /* 到期时间 */"\n\t\t\t\t\t\t\t:formatter="tableFormatter"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column \n\t\t\t\t\t\t\tmin-width="260" \n\t\t\t\t\t\t\tprop="statusLabel"\n\t\t\t\t\t\t\t:label="$t(\'状态\')"\n\t\t\t\t\t\t\t:formatter="tableFormatter"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t</fx-table>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t',components:{"sd-help":t},props:{loadConfigDataFn:{type:Function,default:function(){return Promise.resolve()}}},data:function(){return{isLoading:!1,isAudioTransferOpen:!1,isAutoTransferOpen:!1,quotaStatisticsList:[]}},computed:{},methods:{tableFormatter:function(t,e,n,a){return n||"--"},onChangeData:function(t){var e=this;this.isLoading=!0,a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/setting/saveTenantAdvancedSetting",params:t}).finally(function(t){e.isLoading=!1}).then(this.loadData)},showDetail:function(){i.$show()},submit:function(){this.$emit("submit")},changeCalloutInform:function(){this.submit()},loadData:function(){var e=this;this.isLoading=!0,this.loadConfigDataFn().finally(function(t){e.isLoading=!1}).then(function(t){t=t.Value.data;e.isAudioTransferOpen=!!t.voiceToTextStatus,e.isAutoTransferOpen=!!t.autoTurnTextStatus,e.quotaStatisticsList=(t.quotaList||[]).map(function(t){return new r({orderNumber:t.orderNumber,duration:t.quota,leftDuration:t.remainderQuota,effectTime:t.startTime,dueDate:t.expiredTime,status:t.status})})})}},mounted:function(){},created:function(){this.loadData()}})});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var a=0;a<e.length;a++){var n=e[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_toPropertyKey(n.key),n)}}function _createClass(t,e,a){return e&&_defineProperties(t.prototype,e),a&&_defineProperties(t,a),Object.defineProperty(t,"prototype",{writable:!1}),t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var a=t[Symbol.toPrimitive];if(void 0===a)return("string"===e?String:Number)(t);a=a.call(t,e||"default");if("object"!=_typeof(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/advanced/audio-transfer-setting/quota-detail/quota-detail",["crm-setting/callcenter/util"],function(t,e,a){var n=t("crm-setting/callcenter/util"),i=_createClass(c,[{key:"quotaTypeLabel",get:function(){return c.quotaTypeStatusMap[this.quotaType]}},{key:"recordDurationLabel",get:function(){return n.formatDurationTime(this.recordDuration)}},{key:"beforeChangedDurationLabel",get:function(){return n.formatDurationTime(this.beforeChangedDuration)}},{key:"afterChangedDurationLabel",get:function(){return n.formatDurationTime(this.afterChangedDuration)}},{key:"createTimeLabel",get:function(){return FS.moment(this.createTime).format("YYYY-MM-DD HH:mm:ss")}}]);function c(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},e=t.quotaType,e=void 0===e?0:e,a=t.callRecordId,a=void 0===a?0:a,n=t.callRecordName,n=void 0===n?0:n,i=t.recordFileSrc,i=void 0===i?0:i,o=t.recordDuration,o=void 0===o?0:o,r=t.beforeChangedDuration,r=void 0===r?0:r,l=t.afterChangedDuration,l=void 0===l?0:l,t=t.createTime,t=void 0===t?0:t;_classCallCheck(this,c),this.quotaType=e,this.callRecordId=a,this.callRecordName=n,this.recordFileSrc=i,this.recordDuration=o,this.beforeChangedDuration=r,this.afterChangedDuration=l,this.createTime=t}i.quotaTypeStatusMap={consume:$t("eservice.callcenter.audio-transfer-setting.consume"),recharge:$t("充值")};var o=Vue.extend({name:"quota-detail-dialog",template:'\n    <fx-dialog\n      :visible="showPanel"\n      @close="onClose"\n      custom-class="quota-detail-dialog"\n      :title="$t(\'eservice.callcenter.audio-transfer-setting.quota-detail-dialog-title\') /* 配额明细 */"\n      width="910px"\n      append-to-body\n      sliderPanel\n    >\n      <div class="editor-body" ref="quotaDetailTableWrap">\n        <fx-table\n\t\t\t\t\tv-loading="isLoading"\n          ref="quotaDetailTable"\n          :data="quotaStatisticsDetailList"\n          :height="tableHeight"\n          border\n          class="crm-style-table--new quota-detail-table"\n        >\n          <fx-table-column \n\t\t\t\t\t\tprop="quotaTypeLabel" \n\t\t\t\t\t\t:label="$t(\'eservice.callcenter.audio-transfer-setting.quota-type-label\') /* 流量变更类型 */" \n\t\t\t\t\t\tmin-width="152"\n\t\t\t\t\t>\n          </fx-table-column>\n          <fx-table-column :label="$t(\'通话记录\')" min-width="152">\n\t\t\t\t\t\t<template slot-scope="{ row: detailData }">\n\t\t\t\t\t\t\t<fx-link @click="">{{ detailData.callRecordName }}</fx-link>\n\t\t\t\t\t\t</template>\n          </fx-table-column>\n          <fx-table-column \n\t\t\t\t\t\t:label="$t(\'录音文件\')" \n\t\t\t\t\t\tmin-width="152"\n\t\t\t\t\t>\n\t\t\t\t\t\t<template slot-scope="{ row: detailData }">\n\t\t\t\t\t\t\t<fx-link :href="detailData.recordFileSrc">{{ detailData.recordFileSrc }}</fx-link>\n\t\t\t\t\t\t</template>\n          </fx-table-column>\n          <fx-table-column \n\t\t\t\t\t\tprop="recordDurationLabel" \n\t\t\t\t\t\t:label="$t(\'eservice.callcenter.audio-transfer-setting.record-duration-label\') /* 录音时长 */" \n\t\t\t\t\t\tmin-width="152"\n\t\t\t\t\t>\n          </fx-table-column>\n          <fx-table-column prop="beforeChangedDurationLabel" :label="$t(\'eservice.callcenter.audio-transfer-setting.before-changed-duration-label\') /* 消耗前流量 */" min-width="152">\n          </fx-table-column>\n          <fx-table-column prop="afterChangedDurationLabel" :label="$t(\'eservice.callcenter.audio-transfer-setting.after-changed-duration-label\') /* 消耗后流量 */" min-width="152">\n          </fx-table-column>\n          <fx-table-column prop="createTimeLabel" :label="$t(\'时间\')" min-width="152">\n          </fx-table-column>\n        </fx-table>\n      </div>\n      <div slot="footer" class="dialog-footer">\n        <fx-pagination\n          @current-change="handleCurrentChange"\n          layout="total2, jumper2, prev2, next2"\n          :current-page.sync="pageData.currentPage"\n          :page-size.sync="pageData.pageSize"\n          :total="pageData.total"\n        >\n        </fx-pagination>\n      </div>\n    </fx-dialog>\n\t',props:{editingData:{type:Object,default:function(){return{}}}},data:function(){return{quotaStatisticsDetailList:[],pageData:{currentPage:1,pageSize:20,total:100},isLoading:!1,tableHeight:300,isDialogOpened:!1,showPanel:!1,error:{}}},watch:{showPanel:function(t){var e=this;t&&setTimeout(function(){e.tableHeight=e.$refs.quotaDetailTableWrap.offsetHeight},600)}},methods:{onClose:function(){this.$emit("cancel")},loadData:function(){var e=this;this.isLoading=!0,n.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/setting/queryQuotaStatisticsDetailList",params:{currentPage:this.pageData.currentPage,pageSize:this.pageData.pageSize}}).finally(function(t){e.isLoading=!1}).then(function(){var t=((0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).Value||{}).data||{};e.pageData.total=t.pageTotal,e.quotaStatisticsDetailList=(t.data||[]).map(function(t){return new i({quotaType:t.quotaType,callRecordId:t.dataId,callRecordName:t.name,recordFileSrc:t.recordFile,recordDuration:t.changedQuota,beforeChangedDuration:t.beforeChangedQuota,afterChangedDuration:t.afterChangedQuota,createTime:t.createTime})})})}},created:function(){this.loadData()}});o.$show=function(){var n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(t,e){var a=new o({el:document.createElement("div"),propsData:n});a.$on("hide",function(){a.showPanel=!1,setTimeout(function(){a.$destroy(),a.$el.remove()},1e3)}),a.$on("submit",function(){a.$emit("hide"),t.apply(void 0,arguments)}),a.$on("cancel",function(){a.$emit("hide"),e.apply(void 0,arguments)}),$("body").append(a.$el),setTimeout(function(){a.showPanel=!0},20)})},a.exports=o});
define("crm-setting/callcenter/advanced/template/advanced-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="crm-g-form-content crm-scroll"> <div class="crm-g-form-content__setting-item" id="audioTransferSetting"></div> <div class="crm-g-form-content__setting-item" id="aiSummarySetting"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/callcenter",["crm-modules/common/util","./callcentermenu","./configinfo/call-center-config/selector-dialog/selector-dialog"],function(t,e,n){var a=t("crm-modules/common/util"),o=t("./callcentermenu"),l=t("./configinfo/call-center-config/selector-dialog/selector-dialog"),t=Backbone.View.extend({initialize:function(t){this.pages={},this.setElement(t.wrapper)},events:{"click .j-init":"onInit"},getQueryParams:function(t){var e=null;return e=-1!==(t=t||window.location.href).indexOf("/=/")&&0<(t=t.slice(t.indexOf("/=/")+3)).length?t.split("/"):e},getPageTab:function(){var e={};return this.getQueryParams().forEach(function(t){t=t.split("-");e[t[0]]=t[1]}),e},render:function(e){var n=this;CRM.util.showLoading_new(),n.getTenantBindList(function(t){CRM.util.hideLoading_new(),2!=t.initStatus?n.$el.html('<div class="crm-tit"><h2><span class="tit-txt">'+$t("eservice.crm.callcenter.callout_center")+'</span></h2><div class="crm-intro"><h3>'+$t("说明：")+"</h3><ul><li>"+$t("呼叫中心是纷享销客与第三方合作的应用，初始化后即可开放呼入弹屏，通话记录，CRM测外呼能力，帮助客服识别客户，定位问题。")+"</li><li>"+$t("1. 开启呼叫中心后，系统会增加通话记录对象，存储通话信息和录音信息。")+"</li><li>"+$t("2. 开启呼叫中心后，系统会增加角色--呼叫中心客服。")+"</li><li>"+$t("3. 注意：呼叫中心初始化后将无法关闭。")+'</li></ul></div><span class="crm-btn crm-btn-primary j-init ">'+$t("开始初始化")+"</span></div>"):(n.data=t,n.renderPage(e))})},renderPage:function(c){var r=this,s=this.data.tenantBinds,t=this.data.isTelemarkWorkstationGrayed,e=this.data.serviceProviderAttribute,n=!!this.data.allowAddTenantBind;this.cellcenterInstance=FxUI.create({name:"callcenter",template:'<div class="configinfo-wrapper">\n\t\t\t\t<div class="crm-title">\n                 <h2>\n\t             \t<span class="tit-txt">\n\t\t        \t{{$t("eservice.crm.callcenter.callout_center")}}\n\t\t        \t<a class="crm-doclink" href="https://help.fxiaoke.com/1a54/8c78/cdc0" target="_blank"></a>\n\t\t            </span>\n\t           </h2>\n               </div>\n\t\t\t\t<div class="configinfo-list-container">\n\t\t\t\t<div class="configinfo-list" v-for="(item, index) in tenantBindsData" :key="item.uuid">\n\t\t\t\t  <div class="config-list-name">\n\t\t\t\t  \t{{item.name}}\n\t\t\t\t\t<fx-switch :value="!!item.status" size="small" @click.native="onUpdate(item)"></fx-switch>\n\t\t\t\t  </div>\n\t\t\t\t  <div class="config-list-appid-container">\n\t\t\t\t\t<div class="config-list-appid">{{$t(\'eservice.crm.callcenter.callout_manufacturer\')}}{{item.serviceProviderName}}</div> \n\t\t\t\t  </div>\n\t\t\t\t  <div class="btns-container">\n\t\t\t\t\t<fx-button size="small" class="btn-class" type="text" @click="handleSet(item.id)">{{$t(\'配置\')}}</fx-button>\n\t\t\t\t\t<fx-button size="small" class="btn-class" type="text" v-if="item.status == 0" @click="onDelete(item)">{{$t(\'删除\')}}</fx-button>\n\t\t\t\t  </div>\n\t\t\t\t</div>\n\t\t\t\t<div class="configinfo-list-empty" @click="handleAdd" v-if="allowAddTenantBind">\n\t\t\t\t   <span class="fx-icon-add-2" style="fontSize: 24px; marginBottom: 13px; color: #0C6CFF"></span>\n\t\t\t\t   <div class="configinfo-list-empty-title">{{$t(\'eservice.crm.callcenter.callout_center_manufacturer\')}}</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="callcenter-menutab" ref="menutab"></div>\n\t\t\t\t</div>',components:{},data:function(){return{tenantBinds:s,showMenuTab:!1,menuPageInstance:null,isTelemarkWorkstationGrayed:t,serviceProviderAttribute:e,allowAddTenantBind:n}},watch:{},computed:{tenantBindsData:function(){var t=this;return this.tenantBinds.map(function(e){return{status:e.status,serviceProviderName:t.serviceProviderAttribute.find(function(t){return t.id===e.serviceProvider}).name,name:e.name,id:e.id,uuid:FS.util.uuid()}})}},methods:{handleSet:function(e){var t=this,n=this,i=s.find(function(t){return t.id===e}),a=_.filter(n.serviceProviderAttribute,function(t){return+t.optional||0==+t.optional&&i.serviceProvider==t.id});i.param=c,this.$nextTick(function(){n.menuPageInstance=new o(_.extend({wrapper:n.$refs.menutab,data:Object.assign(i,{isTelemarkWorkstationGrayed:t.isTelemarkWorkstationGrayed,serviceProviderAttribute:a})})),n.menuPageInstance.on("back",function(){n.menuPageInstance&&n.menuPageInstance.destroy&&n.menuPageInstance.destroy(),n.menuPageInstance=null,n.$refs.menutab.childNodes[0].remove(),r.render()}),n.$once("hook:beforeDestroy",function(){n.menuPageInstance&&n.menuPageInstance.destroy&&n.menuPageInstance.destroy(),n.menuPageInstance=null})})},handleAdd:function(){var e=this,n=this,i=_.filter(n.serviceProviderAttribute,function(t){return+t.optional});l.$show({serviceProviderOption:i.map(function(t){return{label:t.name,value:t.id}})}).then(function(t){n.menuPageInstance=new o(_.extend({wrapper:n.$refs.menutab,data:Object.assign({name:t.name,serviceProvider:t.serviceProvider,isTelemarkWorkstationGrayed:e.isTelemarkWorkstationGrayed,serviceProviderAttribute:i})})),n.menuPageInstance.on("back",function(){n.menuPageInstance&&n.menuPageInstance.destroy&&n.menuPageInstance.destroy(),n.menuPageInstance=null,n.$refs.menutab.childNodes[0].remove(),r.render()}),n.$once("hook:beforeDestroy",function(){n.menuPageInstance&&n.menuPageInstance.destroy&&n.menuPageInstance.destroy(),n.menuPageInstance=null})})},updateTenantBindStatus:function(t){var e={1:0,0:1}[t.status],n={1:$t("启用成功"),0:$t("停用成功")}[e];a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/updateTenantBindStatus",type:"post",data:{id:t.id,status:e},success:function(t){0==t.Value.errorCode?(CRM.util.remind(1,n),r.render()):a.remind(3,t.Value.errorMessage||$t("操作失败!"))},complete:function(){}})},onUpdate:function(t){var e=this;0==t.status?this.updateTenantBindStatus(t):FxUI.MessageBox.confirm($t("eservice.crm.callcenter.config_update_prompt"),$t("提示"),{type:"warning"}).then(function(){e.updateTenantBindStatus(t)}).catch(function(){})},deleteTenantBind:function(t){a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/deleteTenantBind",type:"post",data:{id:t.id},success:function(t){0==t.Value.errorCode?(CRM.util.remind(1,$t("删除成功")),r.render()):a.remind(3,t.Value.errorMessage||$t("操作失败!"))},complete:function(){}})},onDelete:function(t){var e=this;FxUI.MessageBox.confirm($t("eservice.crm.callcenter.config_delete_prompt"),$t("提示"),{type:"warning"}).then(function(){e.deleteTenantBind(t)}).catch(function(){})}},mounted:function(){},created:function(){}}),r.$el.html(this.cellcenterInstance.$el)},onInit:function(){var e=this;$(".j-init",e.$el);a.FHHApi({url:"/EM1HNCRM/API/v1/object/call_center/service/init_call_center",data:{},success:function(t){0==t.Result.StatusCode&&(2==t.Value.initStatus?(a.remind(1,$t("初始化成功")),e.render()):a.remind(3,t.Value.message||$t("初始化失败")))}})},getTenantCallCenterInfo:function(n){var i=this;a.FHHApi({url:"/EM1HNCRM/API/v1/object/call_center/service/get_tenant_call_center_info",type:"post",data:{},success:function(e){var t;0==e.Result.StatusCode&&(t=[{accountId:e.Value.accountId,confJson:e.Value.confJson,id:"sdfeofhreiuisdwe",name:e.Value.serviceProviderAttribute.find(function(t){return t.id===e.Value.serviceProvider}).name,serviceProvider:e.Value.serviceProvider}],e.Value.tenantBinds=t,i.tenantBinds=t,n(e.Value))}})},getTenantBindList:function(e){a.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/getTenantBindList",type:"post",data:{},success:function(t){0==t.Result.StatusCode&&e&&e(t.Value.data)}})},destroy:function(){this.cellcenterInstance.$destroy(),this.cellcenterInstance=null,this.undelegateEvents(),this.off()}});n.exports=t});
define("crm-setting/callcenter/callcentermenu",["crm-modules/common/util","./template/index-html"],function(i,e,a){var t=i("crm-modules/common/util"),r=i("./template/index-html"),n=Backbone.View.extend({config:{page1:{id:"configinfo",wrapper:".callcenter-configinfo-box"},page2:{id:"accountbinding",wrapper:".callcenter-accountbinding-box"},page3:{id:"accessconfig",wrapper:".callcenter-accessconfig-box"},page4:{id:"telmarketing",wrapper:".callcenter-telmarketing-box"},pageCallout:{id:"callout",wrapper:".callcenter-callout-box"},pageAdvanced:{id:"advanced",wrapper:".callcenter-advanced-box"}},events:{"click .crm-tab a":"onHandle","click .j-init":"onInit","click .back-btn":"handleBack"},initialize:function(e){this.pages={},this.setElement(e.wrapper),this.isTelemarkWorkstationGrayed=e.data.isTelemarkWorkstationGrayed,this.serviceProviderAttribute=e.data.serviceProviderAttribute,this.serviceProvider=e.data.serviceProvider,this.tenantBindId=e.data.id,this.name=e.data.name||$t("eservice.crm.callcenter.callout_center"),this.render(e.data.param)},onHandle:function(e){e=$(e.target);return e.hasClass("page1")?this.switchPage(["page1"]):e.hasClass("page2")?this.switchPage(["page2"]):e.hasClass("page3")?this.switchPage(["page3"]):e.hasClass("page4")?this.switchPage(["page4"]):e.hasClass("pageCallout")?this.switchPage(["pageCallout"]):e.hasClass("pageAdvanced")&&this.switchPage(["pageAdvanced"]),!1},handleBack:function(){this.trigger("back")},getPageTab:function(){var a={};return this.getQueryParams().forEach(function(e){e=e.split("-");a[e[0]]=e[1]}),a},getQueryParams:function(e){var a=null;return a=-1!==(e=e||window.location.href).indexOf("/=/")&&0<(e=e.slice(e.indexOf("/=/")+3)).length?e.split("/"):a},switchPage:function(e){this.render(e)},render:function(e){var n=this,a=this.getPageTab(),a=a&&a.pagetab;n.param=e||a&&[a],this.tenantBindId?n.getTenantBind(function(e){var a=e.tenantBind,t=_.filter(n.serviceProviderAttribute,function(e){return+e.optional||0==+e.optional&&a.serviceProvider==e.id});a.serviceProviderAttribute=t,n.tenantBind=a,n.$el.html(r({name:n.name})),1==n.isTelemarkWorkstationGrayed&&$(".page4").show(),e.voiceToTextStatus&&$(".pageAdvanced").show(),n.renderPage(n.param)}):(e=_.filter(n.serviceProviderAttribute,function(e){return+e.optional}),n.tenantBind={serviceProviderAttribute:e,serviceProvider:n.serviceProvider,name:n.name},n.$el.html(r({name:n.name})),$(".page2").hide(),$(".page3").hide(),$(".pageCallout").hide(),$(".pageAdvanced").hide(),n.renderPage(n.param))},renderPage:function(e){var t,n=this,a=n.$(".crm-tab .item"),a=(a.removeClass("cur"),(e?a.filter("."+e[0]):a.eq(0)).addClass("cur"),n.curId=e?e[0]:"page1"),e=(_.map(n.pages,function(e){e.destroy&&e.destroy()}),[".",n.config[a].id,n.config[a].id].join("/"));t=a,i.async(e,function(e){var a=n.tenantBind||{},e=new e(_.extend({wrapper:n.config[t].wrapper,data:a}));e.on("save",function(){n.render(n.param)}),e.on("saveConfig",function(e){n.tenantBindId=e&&e.id,n.name=e&&e.name,n.render(n.param)}),n.curId===t&&e.show(),n.pages[t]=e})},getTenantCallCenterInfo:function(a){t.FHHApi({url:"/EM1HNCRM/API/v1/object/call_center/service/get_tenant_call_center_info",type:"post",data:{},success:function(e){0==e.Result.StatusCode&&(e.Value.tenantBind=[{accountId:e.Value.accountId,confJson:e.Value.confJson,id:"123456",name:$t("天润"),serviceProvider:e.Value.serviceProvider,callCenterAppId:"callCenterAppId"}],a)&&a(e.Value)}})},getTenantBind:function(a){t.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/getTenantBind",type:"post",data:{id:this.tenantBindId},success:function(e){0==e.Result.StatusCode&&a&&a(e.Value.data)}})},destroy:function(){_.map(this.pages,function(e){e.destroy&&e.destroy()}),this.pages=this.curId=null,this.undelegateEvents(),this.off()}});a.exports=n});
define("crm-setting/callcenter/callout/callout-inform-setting/callout-inform-setting",["../../util"],function(t,n,e){t("../../util");e.exports=Vue.extend({name:"callout-object-setting",template:'\n\t\t<div class="callcenter__callout-inform-setting">\n\t\t\t<div class="setting-label">\n\t\t\t\t<p class="setting-label__text">{{ $t(\'外呼通知\') }}</p>\n\t\t\t</div>\n\t\t\t<div class="setting-comp">\n        <fx-switch size="small" v-model="isCalloutInformOpen" @change="changeCalloutInform"></fx-switch>\n        <p class="setting-comp__setting-desc">{{ $t(\'开启后，通过外呼按钮进行外呼，系统会发送通知信息给员工，点击通知即可跳转客服工作台。\') }}</p>\n\t\t\t</div>\n\t\t</div>\n\t',props:{callOutSettingRes:{type:Object,default:function(){return{}}}},data:function(){return{isCalloutInformOpen:!1}},watch:{},computed:{},methods:{getSubmitData:function(){return{remindStatus:+this.isCalloutInformOpen}},submit:function(){this.$emit("submit")},changeCalloutInform:function(){this.submit()},loadData:function(){var t=this.callOutSettingRes.Value;this.isCalloutInformOpen=!!t.remindStatus}},mounted:function(){},created:function(){this.loadData()}})});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var i;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(i="Object"===(i={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:i)||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(t,e):void 0}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=Array(e);i<e;i++)n[i]=t[i];return n}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_toPropertyKey(n.key),n)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var i=t[Symbol.toPrimitive];if(void 0===i)return("string"===e?String:Number)(t);i=i.call(t,e||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}define("crm-setting/callcenter/callout/callout-object-setting/callout-object-setting",["../../util","./selector-dialog/selector-dialog"],function(t,e,i){var a=t("../../util"),n=t("./selector-dialog/selector-dialog"),r=_createClass(function t(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=e.label,i=void 0===i?"":i,e=e.apiName,e=void 0===e?"":e;_classCallCheck(this,t),this.label=i||e||"--",this.apiName=e}),l=_createClass(function t(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=e.label,i=void 0===i?"":i,n=e.apiName,n=void 0===n?"":n,a=e.fieldList,a=void 0===a?[]:a,l=e.selectableFieldList,l=void 0===l?[]:l,e=e.selectableFieldMap;_classCallCheck(this,t),this.label=i||n||"--",this.apiName=n,this.fieldList=a.map(function(t){return new r(t)}),this.selectableFieldList=l.map(function(t){return new r(t)}),this.selectableFieldMap=e||l.reduce(function(t,e){return e[t.apiName]=t,e},{})},[{key:"recordSelectableFieldList",value:function(t){var e=[],i={};(t||[]).forEach(function(t){t=new r(t);e.push(t),i[t.apiName]=t}),this.selectableFieldList=e,this.selectableFieldMap=i}},{key:"fieldListLabel",get:function(){return this.fieldList.map(function(t){return t.label}).join(",")||"--"}}]);i.exports=Vue.extend({name:"callout-object-setting",template:'\n\t\t<div class="callcenter__callout-object-setting">\n\t\t\t<div class="object-setting-label">\n\t\t\t\t<p class="setting-label__text">{{ $t(\'外呼对象设置\') }}</p>\n\t\t\t</div>\n\t\t\t<div class="object-setting-comp">\n\t\t\t\t<p class="setting-comp__setting-desc">{{ $t(\'设置需要外呼的对象，让通话记录自动关联该对象。默认支持：客户、线索、联系人三个预置对象\') }}</p>\n\t\t\t\t<div class="object-setting-table-wrap">\n\t\t\t\t\t<fx-table :data="settingObjectList" border class="object-setting-table">\n\t\t\t\t\t\t<fx-table-column prop="label" :label="$t(\'对象\')" width="70"></fx-table-column>\n\t\t\t\t\t\t<fx-table-column prop="fieldListLabel" :label="$t(\'匹配字段\')" width="302"></fx-table-column>\n\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\t:label="$t(\'操作\')"\n\t\t\t\t\t\t\twidth="83">\n\t\t\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t\t\t<fx-button \n\t\t\t\t\t\t\t\t\t@click="settingObjField(scope.row)" \n\t\t\t\t\t\t\t\t\ttype="text" \n\t\t\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\t\t\tclass="table-text-btn link-btn"\n\t\t\t\t\t\t\t\t>{{ $t(\'配置字段\') }}</fx-button>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t</fx-table>\n\t\t\t\t\t<div class="table-add-line">\n\t\t\t\t\t\t<fx-button \n\t\t\t\t\t\t\t@click="addObj" \n\t\t\t\t\t\t\ttype="text" \n\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\tclass="link-btn"\n\t\t\t\t\t\t>{{ \'+ \' + $t(\'添加对象\') }}</fx-button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t',props:{callOutSettingRes:{type:Object,default:function(){return{}}}},data:function(){return{originSettingObjectList:[],editingSettingObjectList:[],selectableObjectList:[],selectableObjectMap:{}}},watch:{selectableObjectList:function(t){this.selectableObjectMap=(t||[]).reduce(function(t,e){return t[e.apiName]=e,t},{})}},computed:{settingObjectList:function(){return[].concat(_toConsumableArray(this.originSettingObjectList),_toConsumableArray(this.editingSettingObjectList))}},methods:{addObj:function(){var e=this;n.$show({selectableList:this.selectableObjectList.map(function(t){return{key:t.apiName,label:t.label}}),selectedList:this.editingSettingObjectList.map(function(t){return t.apiName})}).then(function(t){e.editingSettingObjectList=(t.selectedList||[]).map(function(t){return e.selectableObjectMap[t]})}).then(this.submit)},settingObjField:function(e){e instanceof l||(e=new l(e)),this.getObjFieldList(e).then(function(){return n.$show({selectorType:"field",selectedList:e.fieldList.map(function(t){return t.apiName}),selectableList:e.selectableFieldList.map(function(t){return{key:t.apiName,label:t.label}})})}).then(function(t){e.fieldList=(t.selectedList||[]).map(function(t){return e.selectableFieldMap[t]})}).then(this.submit)},getObjFieldList:function(n){return 0<n.selectableFieldList.length?Promise.resolve(n.selectableFieldList):a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeListByApiName",params:{describe_apiname_list:[n.apiName]}}).then(function(t){var e=t.Value.objectDescribeList[0].fields||{},i=[];return Object.keys(e).forEach(function(t){t=e[t];"phone_number"===t.type&&i.push(new r({apiName:t.api_name,label:t.label_r||t.label||t.description}))}),n.recordSelectableFieldList(i),i})},formatObjectSettingItem:function(t){return(t||[]).map(function(t){return new l({label:t.name,apiName:t.id,fieldList:t.fieldList})})},loadObjectList:function(){var n=this;return new Promise(function(t,e){var i=n.callOutSettingRes.Value;n.originSettingObjectList=n.formatObjectSettingItem(i.currentCalloutObjList),n.selectableObjectList=n.formatObjectSettingItem(i.remainingObjList),t()})},getSubmitData:function(){return this.settingObjectList.map(function(t){return{name:t.label,id:t.apiName,fieldList:t.fieldList}})},submit:function(){this.$emit("submit")},loadFieldList:function(){},loadData:function(){this.loadObjectList(),this.loadFieldList()}},mounted:function(){},created:function(){this.loadData()}})});
define("crm-setting/callcenter/callout/callout-object-setting/selector-dialog/selector-dialog",[],function(t,e,n){var l=Vue.extend({name:"selector-dialog",template:'\n    <fx-dialog\n      :visible="showPanel"\n      @close="handleDialogClose"\n      custom-class="call-center__selector-dialog"\n\t\t\twidth="602px"\n      :title="dialogTitle"\n      append-to-body\n    >\n\t\t\t<div class="editor-header" v-if="isObjectSelector">\n\t\t\t\t<fx-alert\n\t\t\t\t\t:title="$t(\'点击保存后，添加的对象不允许删除，请谨慎选择添加对象。\')"\n\t\t\t\t\ttype="warning"\n\t\t\t\t\t:closable="false"\n\t\t\t\t>\n\t\t\t\t</fx-alert>\n\t\t\t</div>\n      <div class="editor-body">\n\t\t\t\t<fx-transfer\n\t\t\t\t\tfilterable\n\t\t\t\t\t:titles="transferTitles"\n\t\t\t\t\tv-model="selectedList"\n\t\t\t\t\t:data="selectableList">\n\t\t\t\t</fx-transfer>\n      </div>\n      <span slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="handleSubmit" size="small">\n          {{ $t(\'确定\') }}\n        </fx-button>\n        <fx-button @click="handleDialogClose" size="small">\n\t\t\t\t\t{{ $t(\'取消\') }}\n\t\t\t\t</fx-button>\n      </span>\n    </fx-dialog>\n  ',props:{selectedList:{type:Array,default:function(){return[]}},selectableList:{type:Array,default:function(){return[]}},selectorType:{type:String,default:"object"}},data:function(){return{showPanel:!1}},computed:{dialogTitle:function(){return this.isObjectSelector?$t("添加对象"):$t("配置字段")},transferTitles:function(){return this.isObjectSelector?[$t("全部对象"),$t("已选对象")]:[$t("全部字段"),$t("已选字段")]},isObjectSelector:function(){return"object"===this.selectorType},isFieldSelector:function(){return"field"===this.selectorType}},methods:{handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){var e=this;this.isObjectSelector?this.$alert($t("1、系统将会在对象下预设自定义按钮，这将会消耗系统的自定义按钮配额。")+"<br />"+$t("2、对象添加后不可删除。是否确认添加？"),$t("提示"),{dangerouslyUseHTMLString:!0,type:"warning",callback:function(t){"confirm"===t&&e.submit({selectedList:e.selectedList})}}):this.submit({selectedList:this.selectedList})},submit:function(t){this.$emit("submit",t)}},created:function(){}});l.$show=function(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(t,e){var n=new l({el:document.createElement("div"),propsData:i});n.$on("hide",function(){n.showPanel=!1,setTimeout(function(){n.$destroy(),n.$el.remove()},1e3)}),n.$on("submit",function(){n.$emit("hide"),t.apply(void 0,arguments)}),n.$on("cancel",function(){n.$emit("hide"),e.apply(void 0,arguments)}),$("body").append(n.$el),setTimeout(function(){n.showPanel=!0},20),$("body").append(n.$el)})},n.exports=l});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(e,t){var n,o=Object.keys(e);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(e),t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),o.push.apply(o,n)),o}function _objectSpread(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(n),!0).forEach(function(t){_defineProperty(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function _defineProperty(t,e,n){return(e=_toPropertyKey(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/callcenter/callout/callout",["crm-modules/common/util","../util","./template/callout-html","./callout-object-setting/callout-object-setting","./callout-inform-setting/callout-inform-setting","./work-phone-setting/work-phone-setting","../model/model"],function(t,e,n){t("crm-modules/common/util");var i=t("../util"),o=t("./template/callout-html"),r=t("./callout-object-setting/callout-object-setting"),c=t("./callout-inform-setting/callout-inform-setting"),s=t("./work-phone-setting/work-phone-setting"),l=t("../model/model"),t=Backbone.View.extend({initialize:function(t){this.componentsInstance={},this.model=new l(Object.assign(t.data,{data:t.data})),this.setElement(t.wrapper),this.render()},events:{"click .j-save-access":"saveTemporaryInfo"},render:function(){var t=this;this.initData().then(function(){t.renderPage()})},initData:function(){return this.loadSettingConfig()},loadSettingConfig:function(){var e=this;return CRM.util.showLoading_tip(),i.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/getCallOutSetting"}).then(function(t){CRM.util.hideLoading_tip(),e.callOutSettingRes=t}).catch(function(){CRM.util.hideLoading_tip()})},renderPage:function(t){var e=this;e.$el.html(o(this.options.data)),e.initCalloutObjectSetting(),e.initCalloutInformSetting(),e.initWorkPhoneSetting()},show:function(){this.$el.show()},initCalloutInformSetting:function(){var t=this,e=(this.componentsInstance.calloutInformSetting&&(this.componentsInstance.calloutInformSetting.$destroy(),this.componentsInstance.calloutInformSetting.$el.remove()),new c({el:document.createElement("div"),propsData:{callOutSettingRes:this.callOutSettingRes,tenantBindId:this.model.get("id")}}));e.$on("submit",function(){t.submit()}),this.componentsInstance.calloutInformSetting=e,$("#calloutInformSetting").append(e.$el),this.once("destroy",function(){e.$destroy(),e.$el.remove()})},initCalloutObjectSetting:function(){var t=this,e=(this.componentsInstance.calloutObjectSetting&&(this.componentsInstance.calloutObjectSetting.$destroy(),this.componentsInstance.calloutObjectSetting.$el.remove()),new r({el:document.createElement("div"),propsData:{callOutSettingRes:this.callOutSettingRes,tenantBindId:this.model.get("id")}}));e.$on("refresh",function(){t.render()}),e.$on("submit",function(){t.submit()}),this.componentsInstance.calloutObjectSetting=e,$("#calloutObjectSetting").append(e.$el),this.once("destroy",function(){e.$destroy(),e.$el.remove()})},initWorkPhoneSetting:function(){var t,e=this;$("#workPhoneSetting").length&&(this.componentsInstance.workPhoneSetting&&(this.componentsInstance.workPhoneSetting.$destroy(),this.componentsInstance.workPhoneSetting.$el.remove()),(t=new s({el:document.createElement("div"),propsData:{callOutSettingRes:this.callOutSettingRes,tenantBindId:this.model.get("id")}})).$on("refresh",function(){e.render()}),t.$on("submit",function(){e.submit()}),this.componentsInstance.workPhoneSetting=t,$("#workPhoneSetting").append(t.$el),this.once("destroy",function(){t.$destroy(),t.$el.remove()}))},submit:function(){var e=this,t=(CRM.util.showLoading_tip(),this.componentsInstance.calloutObjectSetting?this.componentsInstance.calloutObjectSetting.getSubmitData():void 0),n=this.componentsInstance.calloutInformSetting?this.componentsInstance.calloutInformSetting.getSubmitData():{},o=this.componentsInstance.workPhoneSetting?this.componentsInstance.workPhoneSetting.getSubmitData():void 0;i.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/setCallOutSetting",params:_objectSpread({calloutObjList:t,workPhoneStatus:o},n)}).then(function(t){CRM.util.hideLoading_tip(),FS.util.remind(1,$t("操作成功！")),e.render()}).catch(function(){CRM.util.hideLoading_tip(),e.render()})},hide:function(){this.$el.hide()},destroy:function(){this.trigger("destroy"),this.selectValidTime=null,this.initShareData1=null,this._select=null,this.undelegateEvents(),this.off()}});n.exports=t});
define("crm-setting/callcenter/callout/template/callout-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="crm-g-form-content crm-scroll"> <div class="crm-g-form-content__setting-item" id="calloutObjectSetting"></div> <div class="crm-g-form-content__setting-item" id="calloutInformSetting"></div> ';
            if (+serviceProvider === 3) {
                __p += ' <div class="crm-g-form-content__setting-item" id="workPhoneSetting"></div> ';
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,_toPropertyKey(o.key),o)}}function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}define("crm-setting/callcenter/callout/work-phone-setting/work-phone-setting",["../../util"],function(t,e,n){var o=t("../../util"),i=_createClass(function t(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=e.name,n=void 0===n?"":n,o=e.seatNum,o=void 0===o?"":o,e=e.workPhone,e=void 0===e?"":e;_classCallCheck(this,t),this.name=n,this.seatNum=o,this.workPhone=e});n.exports=Vue.extend({name:"work-phone-setting",template:'\n\t\t<div class="callcenter__work-phone-setting">\n\t\t\t<div class="setting-label">\n\t\t\t\t<p class="setting-label__text">{{ $t(\'工作手机外呼设置\') }}</p>\n\t\t\t</div>\n\t\t\t<div class="setting-comp">\n\t\t\t\t<fx-switch size="small" v-model="isWorkPhoneSettingOpen" @change="changeWorkPhoneStatus"></fx-switch>\n\t\t\t\t<p class="setting-comp__setting-desc">{{ $t(\'如需使用工作手机，请先在合力侧完成用户的工作手机绑定，绑定后的座席人员可使用工作手机的外呼功能\') }}</p>\n\t\t\t\t<div class="setting-table-wrap" v-show="isWorkPhoneSettingOpen">\n\t\t\t\t\t<fx-table :data="workPhoneList" border class="setting-table" v-loading="isLoadingWorkPhoneList">\n\t\t\t\t\t\t<fx-table-column prop="name" :label="$t(\'姓名\')" width="102"></fx-table-column>\n\t\t\t\t\t\t<fx-table-column prop="seatNum" :label="$t(\'座席号\')" width="115"></fx-table-column>\n\t\t\t\t\t\t<fx-table-column prop="workPhone" :label="$t(\'工作手机号码\')" width="238"></fx-table-column>\n\t\t\t\t\t</fx-table>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t',props:{callOutSettingRes:{type:Object,default:function(){return{}}},tenantBindId:{type:String,default:""}},data:function(){return{isLoadingWorkPhoneList:!1,isWorkPhoneSettingOpen:!1,workPhoneList:[]}},watch:{isWorkPhoneSettingOpen:function(t){t&&this.loadWorkPhoneList()}},methods:{getSubmitData:function(){return+this.isWorkPhoneSettingOpen},submit:function(){this.$emit("submit")},changeWorkPhoneStatus:function(){this.submit()},loadWorkPhoneList:function(){var e=this;this.isLoadingWorkPhoneList=!0,o.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/queryWorkPhoneSeats",params:{tenantBindId:this.tenantBindId}}).then(function(t){e.isLoadingWorkPhoneList=!1;t=(t.Value||{}).data||[];e.workPhoneList=t.map(function(t){return new i({name:t.userName,seatNum:t.seatId,workPhone:t.workPhone})})}).catch(function(){e.isLoadingWorkPhoneList=!1})},loadData:function(){var o=this;return new Promise(function(t,e){var n=o.callOutSettingRes.Value;o.isWorkPhoneSettingOpen=1===n.workPhoneStatus,t()})}},mounted:function(){},created:function(){this.loadData()}})});
define("crm-setting/callcenter/components/phone-field-setting/phone-field-setting",["../set-item-detail/set-item-detail"],function(t,e,i){t=t("../set-item-detail/set-item-detail");i.exports=Vue.extend({name:"phone-field-setting",template:'\n\t\t<div class="callcenter__phone-field-setting">\n\t\t\t<div v-if="type == \'detail\'">\n\t\t\t\t<set-item-detail>\n\t\t\t\t\t<div>\n\t\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.updatephone.rule.desc\') }}\t\t\t\t\t\t\n\t\t\t\t\t</div>\n\t\t\t\t\t<div v-for="item in detailphoneFieldSetting" :key="item" class="signal-phone-setting-detail-item">\n\t\t\t\t\t  {{item}}\n\t\t\t\t\t</div>\n\t\t\t\t</set-item-detail>\n\t\t\t</div>\n\t\t    <div class="signal-phone-setting-from" v-else>\n\t\t\t\t<div\n\t\t        \tclass="fm-item-wrap"\n\t\t        \tv-for="item in phoneFieldSettingFilterOpt"\n\t\t        \t:key="item.apiName"\n\t\t\t\t\t:class="item.apiName"\n\t\t        \t>\n\t\t        \t\t<label class="fm-lb-wrap"><em>*</em>{{item.attr.label}}</label>\n\t\t        \t\t<div class="fm-value-wrap">\n\t\t\t\t\t\t    <fx-select\n\t\t\t\t\t\t        size="small"\n\t\t\t\t\t\t        v-bind="item.attr"\n\t\t\t\t\t\t        v-model="phoneFieldSettingData[item.apiName]"\n\t\t\t\t\t\t        @change="handleChange($event, item.apiName)"\n\t\t\t\t\t\t        :has-form-item="false"\n\t\t\t\t\t        ></fx-select>\n\t\t        \t\t</div>\n\t\t        </div>\n\t       </div>\n\t\t</div>\n\t',props:{phoneFieldSettingOpt:{type:Array,default:function(){return[]}},basicModel:{},type:{type:String,default:""}},components:{SetItemDetail:t},data:function(){return{phoneFieldSettingData:{}}},computed:{phoneFieldSettingFilterOpt:function(){var e=this,i=this.basicModel.choosableApiNames.map(function(t){if(e.basicModel.queryApiNames.includes(t.apiName))return t.objectApiName}).filter(Boolean);return this.phoneFieldSettingOpt.filter(function(t){return i.includes(t.apiName)})},detailphoneFieldSetting:function(){var a=this,l=[];return _.each(this.phoneFieldSettingData,function(e,i){var t,n=a.phoneFieldSettingFilterOpt.find(function(t){return t.apiName===i});_.isEmpty(n)||(t=n&&n.attr.options.filter(function(t){return e&&e.includes(t.value)}).map(function(t){return t.label}).join("、"),l.push("".concat(n.attr.label,"：  ").concat(t)))}),l}},methods:{getSubmitData:function(){var i=this,t=this.basicModel.choosableApiNames.map(function(t){if(i.basicModel.queryApiNames.includes(t.apiName))return t.objectApiName}).filter(Boolean),n={};return _.each(t,function(t,e){n[t]=i.phoneFieldSettingData[t]}),JSON.stringify(n)},checkAllowSubmit:function(){var i=this,n=!0;return this.basicModel.choosableApiNames.map(function(t){if(i.basicModel.queryApiNames.includes(t.apiName))return t.objectApiName}).filter(Boolean).forEach(function(e){var t;_.isEmpty(i.phoneFieldSettingData[e])&&(t=i.phoneFieldSettingOpt.find(function(t){return t.apiName===e}),FS.util.remind(3,$t("eservice.crm.callcenter.updatephone.rule.error.tips",{label:t.attr.label})),n=!1)}),n},initSettingData:function(){var i={};_.each(this.phoneFieldSettingOpt,function(t,e){i[t.apiName]=[]}),this.phoneFieldSettingData=i},handleChange:function(t){}},mounted:function(){var t,i=this;_.isEmpty(this.basicModel.phoneUpdateRule)||(t=JSON.parse(this.basicModel.phoneUpdateRule),_.each(t,function(t,e){i.phoneFieldSettingData[e]=t}))},created:function(){this.initSettingData()}})});
define("crm-setting/callcenter/components/set-item-detail/set-item-detail",[],function(t,e,n){n.exports=Vue.extend({name:"setItemDetail",template:'\n\t\t<div class="callcenter-set-item-detail">\n\t\t\t<slot></slot>\n\t\t</div>\n\t',props:{},components:{},data:function(){return{}},watch:{},computed:{},methods:{},mounted:function(){}})});
define("crm-setting/callcenter/components/set-item/set-item",[],function(t,i,e){e.exports=Vue.extend({name:"setItem",template:'\n        <div class="callcenter-set-item" :class="{\'callcenter-set-item__bg\': isEditing}">\n\t\t\t<div class="callcenter-set-item__label">\n\t\t\t\t<slot name="label"></slot>\n\t\t\t</div>\n\t\t\t<div class="callcenter-set-item__content">\n\t\t\t\t<div class="callcenter-set-item__alone">\n\t\t\t\t\t<slot name="alone" :exit-edit="onExit" :on-edit="onEdit" :open-dialog="openDialog" :comp-name="compName"></slot>\n\t\t\t\t</div>\n\t\t\t\t<div class="callcenter-set-item__form" v-if="isEditing">\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<slot :exit-edit="onExit" :on-edit="onEdit" :open-dialog="openDialog" :comp-name="compName"></slot>\n\t\t\t\t\t\t<slot name="form"></slot>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="callcenter-set-item__form-btns">\n\t\t\t\t\t\t<fx-button type="primary" :size="size" @click="omSubmit">{{ $t(\'保存\') }}</fx-button>\n\t\t\t\t\t\t<fx-button :size="size" @click="onExit">{{ $t(\'取消\') }}</fx-button>    \n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="callcenter-set-item__detail" v-if="!isEditing">\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<slot :exit-edit="onExit" :on-edit="onEdit" :open-dialog="openDialog" :comp-name="compName"></slot>\n\t\t\t\t\t\t<slot name="detail"></slot>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="callcenter-set-item__detail-btns" @click="onEdit" v-if="$slots.detail">\n\t\t\t\t\t\t<span class="callcenter-set-item__btn">\n\t\t\t\t\t\t\t{{ $t(\'设置\') }}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<fx-dialog\n\t\t\t\t:visible.sync="visible"\n\t\t\t\t@close="onExit"\n\t\t\t\t:size="size"\n\t\t\t\t:title="title"\n\t\t\t\t:custom-class="customClass"\n\t\t\t\tmax-height="500px">\n\t\t\t\t<div>\n\t\t\t\t\t<slot name="form"></slot>\n\t\t\t\t</div>\n\t\t\t\t<div slot="footer" class="dialog-footer">\n\t\t\t\t\t<fx-button type="primary" @click="omSubmit" :size="size">{{ $t(\'保存\') }}</fx-button>\n\t\t\t\t\t<fx-button @click="onExit" :size="size">{{ $t(\'取消\') }}</fx-button>\n\t\t\t\t</div>\n\t\t\t</fx-dialog>\n        </div>\n\t',props:{compName:{type:String,default:""},label:{type:String,default:""},size:{type:String,default:"small"},type:{type:String,default:""},title:{type:String,default:$t("设置")},customClass:{type:String,default:""}},components:{},data:function(){return{isEditing:!1,visible:!1,from:""}},watch:{},computed:{},methods:{onEdit:function(){"dialog"===this.type?this.openDialog():this.isEditing=!0},onExit:function(){(this.isEditing||this.visible)&&(this.isEditing=!1,this.visible=!1,this.$emit("cancel",{compName:this.compName}))},omSubmit:function(){var t=this;this.$emit("submit",{compName:this.compName,from:this.from,next:function(){t.isEditing=!1,t.visible=!1}})},openDialog:function(){this.from=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).from||"",this.visible=!0}},mounted:function(){}})});
define("crm-setting/callcenter/configinfo/call-center-config/call-center-config",["crm-setting/callcenter/util","../login-type-setting/login-type-setting","../call-center-plugin/call-center-plugin","../multi-tenant/multi-tenant","../white-list/white-list","crm-modules/common/util"],function(t,e,n){var i=t("crm-setting/callcenter/util"),r=t("../login-type-setting/login-type-setting"),a=t("../call-center-plugin/call-center-plugin"),o=t("../multi-tenant/multi-tenant"),l=t("../white-list/white-list"),s=t("crm-modules/common/util");Vue.config.devtools=!0,n.exports=Vue.extend({name:"call-center-config",template:'\n\t<div class="configinfo-wrapper" :class="{\'configinfo-wrapper-edit\': editStatus}">\n\t\t<div class="configinfo-inner-context">\n\t\t\t<div class="configinfo-lable">{{ $t("呼叫中心厂商配置") }}</div>\n\t\t\t<div class="configinfo-container">\n\t\t\t\t<div class="configinfo-cont-wrapper">\n\t\t\t\t\t<div class="configinfo-cont crm-g-form">\n\t\t\t\t\t\t<p class="desc">\n\t\t\t\t\t\t\t{{\n\t\t\t\t\t\t\t\t$t(\n\t\t\t\t\t\t\t\t\t"选择呼叫中厂商后，配置厂商关键参数信息，让系统互通"\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t<a\n\t\t\t\t\t\t\t\thref="https://help.fxiaoke.com/1a54/8c78/cdc0/5efa/f638"\n\t\t\t\t\t\t\t\ttarget="_blank"\n\t\t\t\t\t\t\t\tv-if="+serviceProvider === 20"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclass="link-btn"\n\t\t\t\t\t\t\t\t\tstyle="margin-left: 5px"\n\t\t\t\t\t\t\t\t\t>{{\n\t\t\t\t\t\t\t\t\t\t$t("eservice.crm.callcenter.setting.look.article")\n\t\t\t\t\t\t\t\t\t}}</span\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t</a>\n\t\t\t\t\t\t</p>\n\n\t\t\t\t\t\t<template v-if="editStatus">\n\n\t\t\t\t\t\t<fx-form\n\t\t\t\t\t\t\t:model="formServiceProvider.formData"\n\t\t\t\t\t\t\t:rules="formServiceProvider.rules"\n\t\t\t\t\t\t\tref="callcenterServ"\n\t\t\t\t\t\t\tclass="form-class form-class-service-provider"\n\t\t\t\t\t\t\t:disabled="!editStatus"\n\t\t\t\t\t\t\t:label-width="labelWidth"\n\t\t\t\t\t\t\t:label-position="labelPosition"\n\t\t\t\t\t\t\t:size="size"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<fx-form-item\n\t\t\t\t\t\t\t\t:label="$t(\'服务商\')"\n\t\t\t\t\t\t\t\tprop="serviceProvider"\n\t\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\t\tv-model="\n\t\t\t\t\t\t\t\t\t\tformServiceProvider.formData\n\t\t\t\t\t\t\t\t\t\t\t.serviceProvider\n\t\t\t\t\t\t\t\t\t"\n\t\t\t\t\t\t\t\t\t@change="handleServiceProviderChange"\n\t\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\t\t:has-form-item="false"\n\t\t\t\t\t\t\t\t\t:el-style="{width:\'288px\'}"\n\t\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t</fx-form-item>\n\t\t\t\t\t\t</fx-form>\n\n\t\t\t\t\t\t<fx-form\n\t\t\t\t\t\t\tv-if="show"\n\t\t\t\t\t\t\t:model="form.formData"\n\t\t\t\t\t\t\t:rules="form.rules"\n\t\t\t\t\t\t\tref="callcenterFormData"\n\t\t\t\t\t\t\tclass="form-class form-class-form-data"\n\t\t\t\t\t\t\t:label-width="labelWidth"\n\t\t\t\t\t\t\t:label-position="labelPosition"\n\t\t\t\t\t\t\t:size="size"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<template\n\t\t\t\t\t\t\t\tv-for="key in form.fieldKey"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<fx-input\n\n\t\t\t\t\t\t\t\t\t:key="key"\n\t\t\t\t\t\t\t\t\tv-if="form.attr[key].fieldType === \'text\'"\n\t\t\t\t\t\t\t\t\tv-bind="form.attr[key]"\n\t\t\t\t\t\t\t\t\tv-model="form.formData[key]"\n\t\t\t\t\t\t\t\t\t@change="(value) => filedChange(value, key)"\n\t\t\t\t\t\t\t\t\twidth="288px"\n\t\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t\t\t<fx-input\n\n\t\t\t\t\t\t\t\t\t:key="key"\n\t\t\t\t\t\t\t\t\tv-if="form.attr[key].fieldType === \'integer\'"\n\t\t\t\t\t\t\t\t\tv-bind="form.attr[key]"\n\t\t\t\t\t\t\t\t\tv-model="form.formData[key]"\n\t\t\t\t\t\t\t\t\ttype="number"\n\t\t\t\t\t\t\t\t\t@change="(value) => filedChange(value, key)"\n\t\t\t\t\t\t\t\t\twidth="288px"\n\t\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t\t\t<fx-select\n\n\t\t\t\t\t\t\t\t\t:key="key"\n\t\t\t\t\t\t\t\t\tv-if="form.attr[key].fieldType === \'select\'"\n\t\t\t\t\t\t\t\t\tv-bind="form.attr[key]"\n\t\t\t\t\t\t\t\t\tv-model="form.formData[key]"\n\t\t\t\t\t\t\t\t\t:options="form.options[key]"\n\t\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\t\t@change="(value) => filedChange(value, key)"\n\t\t\t\t\t\t\t\t\t:el-style="{width:\'288px\'}"\n\t\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</fx-form>\n\n\t\t\t\t\t\t<fx-form\n\t\t\t\t\t\t:model="formAppId.formData"\n\t\t\t\t\t\t:rules="formAppId.rules"\n\t\t\t\t\t\tref="tenantBindId"\n\t\t\t\t\t\tclass="form-class form-class-app-id"\n\t\t\t\t\t\t:disabled="!editStatus"\n\t\t\t\t\t\t:label-width="labelWidth"\n\t\t\t\t\t\t:label-position="labelPosition"\n\t\t\t\t\t\t:size="size"\n\t\t\t\t\t    >\n\t\t\t\t\t    \t<fx-form-item\n\t\t\t\t\t    \t\t:label="$t(\'eservice.crm.callcenter.callout_center_id\')"\n\t\t\t\t\t\t\t\tprop="serviceProvider"\n\t\t\t\t\t\t\t\tv-if="formAppId.formData.tenantBindId"\n\t\t\t\t\t    \t>\n\t\t\t\t\t\t\t    <fx-input\n\t\t\t\t\t\t\t      v-model="formAppId.formData.tenantBindId"\n\t\t\t\t\t\t\t      :placeholder="$t(\'请输入内容\')"\n\t\t\t\t\t\t\t      @change="onChange"\n\t\t\t\t\t\t\t\t  disabled\n\t\t\t\t\t\t\t\t  :has-form-item="false"\n\t\t\t\t\t\t\t\t  width="288px"\n\t\t\t\t\t\t        >\n\t\t\t\t\t\t        </fx-input>\n\t\t\t\t\t    \t</fx-form-item>\n\t\t\t\t\t\t\t<fx-form-item\n\t\t\t\t\t    \t\t:label="$t(\'eservice.crm.callcenter.showname\')"\n\t\t\t\t\t    \t\trequired\n\t\t\t\t\t\t\t\tprop="name"\n\t\t\t\t\t    \t>\n\t\t\t\t\t\t\t    <fx-input\n\t\t\t\t\t\t\t      v-model="formAppId.formData.name"\n\t\t\t\t\t\t\t      :placeholder="$t(\'请输入内容\')"\n\t\t\t\t\t\t\t      @change="onChange"\n\t\t\t\t\t\t\t\t  :has-form-item="false"\n\t\t\t\t\t\t\t\t  width="288px"\n\t\t\t\t\t\t        >\n\t\t\t\t\t\t        </fx-input>\n\t\t\t\t\t    \t</fx-form-item>\n\t\t\t\t\t    </fx-form>\n\n\t\t\t\t\t\t</template>\n\n\t\t\t\t\t\t<template v-else>\n\t\t\t\t\t\t\t<div class="configinfo-cont__detail">\n\t\t\t\t\t\t\t\t<div class="configinfo-cont__detail-item">\n\t\t\t\t\t\t\t\t\t{{ $t(\'服务商\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t\t{{ serviceProviderLabel }}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<template v-if="show">\n\t\t\t\t\t\t\t\t\t<div class="configinfo-cont__detail-item" v-for="key in form.fieldKey" :key="key">\n\t\t\t\t\t\t\t\t\t\t<template v-if="form.attr[key].fieldType === \'text\' || form.attr[key].fieldType === \'integer\'">\n\t\t\t\t\t\t\t\t\t\t\t{{ form.attr[key].label }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t\t\t\t{{ form.formData[key] }}\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t<template v-if="form.attr[key].fieldType === \'select\'">\n\t\t\t\t\t\t\t\t\t\t\t{{ form.attr[key].label }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t\t\t\t{{ getSelectFieldLabel(form.formData[key], form.options[key]) }}\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t<div class="configinfo-cont__detail-item" v-if="formAppId.formData.tenantBindId">\n\t\t\t\t\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.callout_center_id\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t\t{{ formAppId.formData.tenantBindId }}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div class="configinfo-cont__detail-item">\n\t\t\t\t\t\t\t\t\t{{ $t(\'eservice.crm.callcenter.showname\') }}{{ $t(\'：\') }}\n\t\t\t\t\t\t\t\t\t{{ formAppId.formData.name }}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</template>\n\n\t\t\t\t\t\t<div class="vendor-wrapper" v-if="editStatus">\n\t\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\t\t:size="size"\n\t\t\t\t\t\t\t\tclass="btn-submit"\n\t\t\t\t\t\t\t\ttype="primary"\n\t\t\t\t\t\t\t\t@click="saveSubmit"\n\t\t\t\t\t\t\t\t>{{ $t("保存") }}</fx-button\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\t\t:size="size"\n\t\t\t\t\t\t\t\tclass="btn-cancel"\n\t\t\t\t\t\t\t\t@click="handleCancel"\n\t\t\t\t\t\t\t\t>{{ $t("取消") }}</fx-button\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="configinfo-cont"></div>\n\t\t\t\t<div class="configinfo-btn-box"></div>\n\t\t\t</div>\n\t\t\t<div class="configinfo-btn-wrap" v-if="!editStatus">\n\t\t\t\t<div class="configinfo-btn" @click="handleSet">\n\t\t\t\t\t{{ $t("设置") }}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t\t<login-type-setting v-if="isShowLoginType" :tenant-bind-id="tenantBindId"></login-type-setting>\n\t\t<template v-if="isShowPluginWhiteList">\n\t\t    <call-center-plugin :tenant-bind-id="tenantBindId"></call-center-plugin>\n\t\t\t<white-list :tenant-bind-id="tenantBindId"></white-list>\n\t\t</template>\n\t</div>\n\t',props:{serviceProvider:String,serviceProviderAttribute:Array,selectList:Array,tenantBindId:String,callCenterAppId:String,name:String},components:{LoginTypeSetting:r,CallCenterPlugin:a,MultiTenant:o,WhiteList:l},data:function(){return{size:"small",labelPosition:"left",labelWidth:"135px",editStatus:!1,show:!1,form:{formData:{},rules:{},options:{},attr:{},fieldKey:[]},formServiceProvider:{formData:{serviceProvider:""},rules:{serviceProvider:{required:!0,validator:function(t,e,n){e||n(new Error($t("请输入"))),n()}}}},formAppId:{formData:{tenantBindId:"",name:""},rules:{name:{required:!0,validator:function(t,e,n){e||n(new Error($t("请输入"))),n()}}}}}},mounted:function(){var t=this;this.calcRenderData(this.serviceProvider),this.formServiceProvider.formData.serviceProvider=this.serviceProvider,this.formAppId.formData.tenantBindId=this.tenantBindId,this.formAppId.formData.name=this.name,this.tenantBindId||setTimeout(function(){t.handleSet()},200)},computed:{serviceProviderLabel:function(){var e=this,t="";return(t=this.formServiceProvider&&this.formServiceProvider.formData&&this.formServiceProvider.formData.serviceProvider?(_.find(this.options,function(t){return t.value==e.formServiceProvider.formData.serviceProvider})||{}).label:t)||"--"},options:function(){return this.selectList.map(function(t){return{label:t.name,value:t.value}})},isShowLoginType:function(){if("8"===this.formServiceProvider.formData.serviceProvider)return"sdk"===this.form.formData.accessWay&&this.tenantBindId},isShowPluginWhiteList:function(){return 20==+this.formServiceProvider.formData.serviceProvider&&this.tenantBindId}},methods:{getSelectFieldLabel:function(e){return(_.find(1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],function(t){return e==t.value})||{}).label||"--"},filedChange:function(t,e){},calcRenderData:function(e){var t=this,n=this.serviceProviderAttribute.find(function(t){return e===t.id}),n=n&&n.conf||[],r={},a={},o={},l={},s=[];n&&n.map(function(i){r[i.value]=i.v,a[i.value]={required:!!+i.rules.required,validator:function(t,e,n){t.required&&!e&&n(new Error(i.rules.msg||$t("请输入"))),n()}},l[i.value]={required:!!+i.rules.required,fieldType:i.type,placeholder:i.placeholder,readonly:!!+i.readonly,disabled:!t.editStatus||+i.disabled,label:i.name,prop:i.value},i.rules.maxlength&&(l[i.value].maxlength=i.rules.maxlength),o[i.value]="select"===i.type&&i.options&&i.options.map(function(t){return{label:t.name,value:t.value}}),s.push(i.value)}),this.$set(this.form,"formData",r),this.$set(this.form,"rules",a),this.$set(this.form,"options",o),this.$set(this.form,"attr",l),this.$set(this.form,"fieldKey",s),this.show=!0},handleSet:function(){this.editStatus=!0,this.calcRenderData(this.formServiceProvider.formData.serviceProvider)},handleCancel:function(){this.editStatus=!1,this.calcRenderData(this.formServiceProvider.formData.serviceProvider)},handleServiceProviderChange:function(t){this.calcRenderData(t)},saveSubmit:function(){var r=this,a=_.filter(r.serviceProviderAttribute,function(t){return t.id==r.formServiceProvider.formData.serviceProvider}),t=this.$refs.callcenterServ.validate(),e=this.$refs.callcenterFormData.validate(),n=this.$refs.tenantBindId.validate();Promise.all([t,e,n]).then(function(t,e){var n,i;r.serviceProvider&&r.serviceProvider!=r.formServiceProvider.formData.serviceProvider?(n=a[0].name,i=_.filter(r.serviceProviderAttribute,function(t){return t.id==r.serviceProvider})[0].name,i=$t("当服务商从")+i+$t("切换到了")+n+$t("，保存后，原有的")+i+$t("系统不能再关联客户、联系人、线索。")+"<br>"+$t("您确定要切换服务商为【")+n+$t("】并更新帐户绑定关系吗？"),r.confirm=s.confirm(i,$t("服务商修改提醒"),function(){r.saveConfig(),r.confirm.destroy()}),r.confirm.on("dialogCancel",function(t){r.formServiceProvider.formData.serviceProvider=r.serviceProvider,r.calcRenderData(r.formServiceProvider.formData.serviceProvider),r.confirm.destroy()})):r.saveConfig()})},saveConfig:function(){var e=this,n=this;CRM.util.showLoading_tip($t("提交中...")),i.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/saveTenantBind",params:Object.assign(n.getSubmitData(),{id:this.tenantBindId,name:n.formAppId.formData.name}),config:{errorAlertModel:1}}).then(function(t){return CRM.util.hideLoading_tip(),"2018"==(t.Value||{}).errorCode?o.$showMultiExamineConfirm({submitData:Object.assign(n.getSubmitData(),{id:e.tenantBindId,name:n.formAppId.formData.name})}):t}).then(function(t){CRM.util.hideLoading_tip(),0==t.Value.errorCode?(n.serviceProvider=n.formServiceProvider.formData.serviceProvider,s.remind(1,$t("设置成功！")),n.$emit("saveConfig",{id:t.Value.data,name:n.formAppId.formData.name})):s.remind(3,t.Value.errorMessage||$t("设置失败!"))}).catch(function(){CRM.util.hideLoading_tip()})},getSubmitData:function(){var e=this,n="",i={},t=_.filter(e.serviceProviderAttribute,function(t){return t.id==e.formServiceProvider.formData.serviceProvider});return _.each(t[0].conf,function(t){"accountId"==t.value?n=e.form.formData.accountId:i[t.value]=e.form.formData[t.value]}),4==$.trim(e.formServiceProvider.formData.serviceProvider)?{accountId:i.domain,serviceProvider:$.trim(e.formServiceProvider.formData.serviceProvider),confJson:JSON.stringify(i)}:{accountId:$.trim(n),serviceProvider:$.trim(e.formServiceProvider.formData.serviceProvider),confJson:JSON.stringify(i)}}},created:function(){},watch:{}})});
define("crm-setting/callcenter/configinfo/call-center-config/selector-dialog/selector-dialog",[],function(e,t,n){var i=Vue.extend({name:"service-provider-dialog",template:'\n    <fx-dialog\n        :visible="showPanel"\n        @close="handleDialogClose"\n        custom-class="call-center-multi__selector-dialog"\n\t\twidth="602px"\n        :title="$t(\'eservice.crm.callcenter.add.serviceProvider\')"\n        append-to-body\n    >\n\t    <fx-form ref="formData" size="small" :model="formData" label-width="150px" class="form-class" label-position="left">           \n            <fx-select\n            \tprop="serviceProvider"\n            \tv-model="formData.serviceProvider"\n            \t:options="formDataOption.serviceProvider"\n            \t:label="$t(\'eservice.crm.callcenter.callout_center_manufacturer\')"\n            \trequired\n            \twidth="300px"\n            ></fx-select>\n            \n            <fx-input\n            \tprop="name"\n            \tv-model="formData.name"\n            \t:label="$t(\'eservice.crm.callcenter.showname\')"\n            \twidth="300px"\n\t\t\t\trequired\n            ></fx-input>\n        </fx-form>\n        <span slot="footer" class="dialog-footer">\n            <fx-button type="primary" @click="handleSubmit" size="small">\n              {{ $t(\'确定\') }}\n            </fx-button>\n            <fx-button @click="handleDialogClose" size="small">\n\t\t    \t{{ $t(\'取消\') }}\n\t\t    </fx-button>\n        </span>\n    </fx-dialog>\n  ',props:{serviceProviderOption:{type:Array,default:function(){return[]}}},data:function(){return{showPanel:!1,formData:{name:"",serviceProvider:"-1"},formDataOption:{serviceProvider:[]}}},mounted:function(){this.formDataOption.serviceProvider=this.serviceProviderOption},computed:{},methods:{handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){var t=this;this.$refs.formData.validate().then(function(e){t.submit({serviceProvider:t.formData.serviceProvider,name:t.formData.name})},function(e){})},submit:function(e){this.$emit("submit",e)}},created:function(){}});i.$show=function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(e,t){var n=new i({el:document.createElement("div"),propsData:o});n.$on("hide",function(){n.showPanel=!1,setTimeout(function(){n.$destroy(),n.$el.remove()},1e3)}),n.$on("submit",function(){n.$emit("hide"),e.apply(void 0,arguments)}),n.$on("cancel",function(){n.$emit("hide"),t.apply(void 0,arguments)}),$("body").append(n.$el),setTimeout(function(){n.showPanel=!0},20),$("body").append(n.$el)})},n.exports=i});
define("crm-setting/callcenter/configinfo/call-center-plugin/call-center-plugin",["./components/params-setting/params-setting","./components/callback-fn-setting/callback-fn-setting","./components/callout-fn-setting/callout-fn-setting","crm-setting/callcenter/util"],function(t,n,i){var a=t("./components/params-setting/params-setting"),l=t("./components/callback-fn-setting/callback-fn-setting"),s=t("./components/callout-fn-setting/callout-fn-setting");t("crm-setting/callcenter/util");i.exports=Vue.extend({name:"callcenter-plugin-box",template:'\n\t\t<div class="callcenter-plugin-box">\n\t\t\t<ul class="setting-list">\n\t\t\t\t<li class="setting-item params-setting" v-loading="isParamLoading">\n\t\t\t\t\t<div class="setting-item_label">\n\t\t\t\t\t\t<p class="label">{{$t(\'参数配置\')}}</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="setting-item_comp">\n\t\t\t\t\t\t<params-setting :is-loading.sync="isParamLoading" :tenant-bind-id="tenantBindId"></params-setting>\n\t\t\t\t\t</div>\n\t\t\t\t</li>\n\t\t\t\t<li class="setting-item callback-fn-setting" v-loading="isCallbackFnLoading">\n\t\t\t\t\t<div class="setting-item_label">\n\t\t\t\t\t\t<p class="label">{{$t(\'回调函数配置\')}}</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="setting-item_comp">\n\t\t\t\t\t\t<callback-fn-setting :is-loading.sync="isCallbackFnLoading" :tenant-bind-id="tenantBindId"></callback-fn-setting>\n\t\t\t\t\t</div>\n\t\t\t\t</li>\n\t\t\t\t<li class="setting-item callback-fn-setting" v-loading="isCalloutFnLoading">\n\t\t\t\t\t<div class="setting-item_label">\n\t\t\t\t\t\t<p class="label">{{$t(\'外呼函数配置\')}}</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="setting-item_comp">\n\t\t\t\t\t\t<callout-fn-setting :is-loading.sync="isCalloutFnLoading" :tenant-bind-id="tenantBindId"></callout-fn-setting>\n\t\t\t\t\t</div>\n\t\t\t\t</li>\n\t\t\t\t<li class="setting-item">\n\t\t\t\t\t<div class="setting-item_label">\n\t\t\t\t\t\t<p class="label">{{$t(\'电话条插件集成\')}}</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="setting-item_comp">\n\t\t\t\t\t\t<span>{{$t(\'上传电话条自定义组件供客服工作台使用\')}}</span>\n\t\t\t\t\t\t<a class="link-btn" href="#customcomponent/=/module-component" target="__blank">{{$t(\'去设置\')}}</a>\n\t\t\t\t\t</div>\n\t\t\t\t</li>\n\t\t\t</ul>\n\t\t</div>\n\t',components:{ParamsSetting:a,CallbackFnSetting:l,CalloutFnSetting:s},props:{tenantBindId:String},data:function(){return{isParamLoading:!1,isCallbackFnLoading:!1,isCalloutFnLoading:!1}},watch:{},computed:{},methods:{},mounted:function(){},created:function(){}})});
define("crm-setting/callcenter/configinfo/call-center-plugin/components/callback-fn-setting/callback-fn-setting",["crm-setting/callcenter/util"],function(t,n,e){var i=t("crm-setting/callcenter/util");e.exports=Vue.extend({name:"callback-fn-setting",template:'\n\t\t<div class="callcenter__callback-fn-setting">\n\t\t\t<div class="setting-content">\n\t\t\t\t<p class="desc">\n\t\t\t\t\t{{$t(\'呼叫中心系统对接的所有消息推送，都会触发系统自动调用该函数，必须配置该函数才可以完成呼叫中的对接。\')}}\n\t\t\t\t\t\x3c!-- <span class="link-btn">{{$t(\'查看示例函数\')}}</span> --\x3e\n\t\t\t\t</p>\n\t\t\t\t<p class="setting-label">{{$t(\'Step1：添加自定义函数\')}}</p>\n\t\t\t\t<div class="fn-content" v-if="functionData.functionApiName">\n\t\t\t\t\t<div class="content-header">\n\t\t\t\t\t\t<p class="title">{{$t(\'自定义函数\')}}</p>\n\t\t\t\t\t\t<i class="d-icon el-icon-edit" @click="editAplFunction"></i>\t\n\t\t\t\t\t\t<i class="d-icon el-icon-delete" @click="deleteAplFunction"></i>\t\n\t\t\t\t\t</div>\n\t\t\t\t\t<ul class="fn-msg">\n\t\t\t\t\t\t<li class="msg-line">\n\t\t\t\t\t\t\t<p class="fn-msg__label">{{$t(\'函数名称：\')}}</p>\n\t\t\t\t\t\t\t<p class="fn-msg__value">{{functionData.functionName || \'--\'}}</p>\n\t\t\t\t\t\t</li>\n\t\t\t\t\t\t<li class="msg-line">\n\t\t\t\t\t\t\t<p class="fn-msg__label">{{$t(\'函数描述：\')}}</p>\n\t\t\t\t\t\t\t<p class="fn-msg__value">{{functionData.functionDesc || \'--\'}}</p>\n\t\t\t\t\t\t</li>\n\t\t\t\t\t</ul>\n\t\t\t\t</div>\n\t\t\t\t<div class="fn-content empty" @click="addAplFunction" v-else>\n\t\t\t\t\t<p class="link-btn">{{\'+ \' + $t(\'添加自定义函数\')}}</p>\n\t\t\t\t</div>\n\t\t\t\t<p class="setting-label">{{$t(\'Step2：配置推送地址\')}}</p>\n\t\t\t\t<p class="desc">\n\t\t\t\t\t{{$t(\'需要在呼叫中心厂商内配置正确的推送地址才可以进行函数调用。\')}}\n\t\t\t\t</p>\n\t\t\t\t<p class="desc">\n\t\t\t\t\t{{$t(\'注意：地址中的企业号为纷享的企业号，是必须维护的项，推送类型可为空。\')}}\n\t\t\t\t</p>\n\t\t\t\t<p class="msg">\n\t\t\t\t\t{{$t(\'推送地址：\')}}\n\t\t\t\t\t<span ref="pushLink">{{pushLink || \'--\'}}</span>\n\t\t\t\t\t<span class="link-btn" @click="copyLink">{{$t(\'复制链接\')}}</span>\n\t\t\t\t</p>\n\t\t\t</div>\n\t\t</div>\n\t',props:{isLoading:{type:Boolean,default:!1},tenantBindId:String},data:function(){return{isEditing:!1,pushLink:"",functionData:{functionApiName:"",functionName:"",functionDesc:""}}},watch:{},computed:{},methods:{copyLink:function(){var t=document.createRange(),n=document.getSelection();t.selectNode(this.$refs.pushLink),n.removeAllRanges(),n.addRange(t),document.execCommand("Copy"),n.empty(),this.$toast($t("已复制"),{type:"success",autoCloseTime:650})},submitAplFunction:function(){var t=this,n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.$emit("update:isLoading",!0),i.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/new/saveCallFunction",params:Object.assign({bizType:"callBack",tenantBindId:this.tenantBindId},n)}).then(function(){t.$emit("update:isLoading",!1),t.loadData()}).catch(function(){t.$emit("update:isLoading",!1),t.loadData()})},editAplFunction:function(){var n=this;i.getSdkHelper().then(function(t){t.update({api_name:n.functionData.functionApiName,object_api_name:"ServiceRecordObj"}).on("complete",function(t){t.status&&(t=t.data.function||{},n.submitAplFunction({functionApiName:t.api_name,functionName:t.function_name,functionDesc:t.remark}))})})},deleteAplFunction:function(){this.submitAplFunction()},addAplFunction:function(){var n=this;i.getSdkHelper().then(function(t){t.getCallCenterFunction({object_api_name:"ServiceRecordObj",zIndex:CRM.util.getzIndex()},function(t){t.status&&(t=t.data.function||{},n.submitAplFunction({functionApiName:t.api_name,functionName:t.function_name,functionDesc:t.remark}))})})},loadData:function(){var e=this;this.$emit("update:isLoading",!0),i.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/new/queryNewCallCenterConfig",params:{tenantBindId:this.tenantBindId}}).then(function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=(e.$emit("update:isLoading",!1),(t.Value||{}).data||{}),n=(t.functionConfigs||[]).find(function(t){return"callBack"===t.bizType})||{functionApiName:"",functionName:"",functionDesc:""};e.functionData=n,e.pushLink=t.callBackPushDataUrl}).catch(function(){e.$emit("update:isLoading",!1)})}},mounted:function(){},created:function(){this.loadData()}})});
define("crm-setting/callcenter/configinfo/call-center-plugin/components/callout-fn-setting/callout-fn-setting",["crm-setting/callcenter/util"],function(t,n,i){var e=t("crm-setting/callcenter/util");i.exports=Vue.extend({name:"callout-fn-setting",template:'\n\t\t<div class="callcenter__callout-fn-setting">\n\t\t\t<div class="setting-content">\n\t\t\t\t<p class="desc">\n\t\t\t\t\t{{$t(\'如需使用外呼功能，则需要进行外呼函数的配置，如果不需要在纷享进行外呼，则可忽略此配置项。\')}}\n\t\t\t\t</p>\n\t\t\t\t<p class="setting-label">{{$t(\'添加自定义函数\')}}</p>\n\t\t\t\t<div class="fn-content" v-if="functionData.functionApiName">\n\t\t\t\t\t<div class="content-header">\n\t\t\t\t\t\t<p class="title">{{$t(\'自定义函数\')}}</p>\n\t\t\t\t\t\t<i class="d-icon el-icon-edit" @click="editAplFunction"></i>\t\n\t\t\t\t\t\t<i class="d-icon el-icon-delete" @click="deleteAplFunction"></i>\t\n\t\t\t\t\t</div>\n\t\t\t\t\t<ul class="fn-msg">\n\t\t\t\t\t\t<li class="msg-line">\n\t\t\t\t\t\t\t<p class="fn-msg__label">{{$t(\'函数名称：\')}}</p>\n\t\t\t\t\t\t\t<p class="fn-msg__value">{{functionData.functionName || \'--\'}}</p>\n\t\t\t\t\t\t</li>\n\t\t\t\t\t\t<li class="msg-line">\n\t\t\t\t\t\t\t<p class="fn-msg__label">{{$t(\'函数描述：\')}}</p>\n\t\t\t\t\t\t\t<p class="fn-msg__value">{{functionData.functionDesc || \'--\'}}</p>\n\t\t\t\t\t\t</li>\n\t\t\t\t\t</ul>\n\t\t\t\t</div>\n\t\t\t\t<div class="fn-content empty" @click="addAplFunction" v-else>\n\t\t\t\t\t<p class="link-btn">{{\'+ \' + $t(\'添加自定义函数\')}}</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t',props:{isLoading:{type:Boolean,default:!1},tenantBindId:String},data:function(){return{isEditing:!1,pushLink:"",functionData:{functionApiName:"",functionName:"",functionDesc:""}}},watch:{},computed:{},methods:{submitAplFunction:function(){var t=this,n=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.$emit("update:isLoading",!0),e.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/new/saveCallFunction",params:Object.assign({bizType:"callOut",tenantBindId:this.tenantBindId},n)}).then(function(){t.$emit("update:isLoading",!1),t.loadData()}).catch(function(){t.$emit("update:isLoading",!1),t.loadData()})},editAplFunction:function(){var n=this;e.getSdkHelper().then(function(t){t.update({api_name:n.functionData.functionApiName,object_api_name:"ServiceRecordObj"}).on("complete",function(t){t.status&&(t=t.data.function||{},n.submitAplFunction({functionApiName:t.api_name,functionName:t.function_name,functionDesc:t.remark}))})})},deleteAplFunction:function(){this.submitAplFunction()},addAplFunction:function(){var n=this;e.getSdkHelper().then(function(t){t.getCallCenterFunction({object_api_name:"ServiceRecordObj",zIndex:CRM.util.getzIndex()},function(t){t.status&&(t=t.data.function||{},n.submitAplFunction({functionApiName:t.api_name,functionName:t.function_name,functionDesc:t.remark}))})})},loadData:function(){var i=this;this.$emit("update:isLoading",!0),e.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/new/queryNewCallCenterConfig",params:{tenantBindId:this.tenantBindId}}).then(function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=(i.$emit("update:isLoading",!1),(t.Value||{}).data||{}),n=(t.functionConfigs||[]).find(function(t){return"callOut"===t.bizType})||{functionApiName:"",functionName:"",functionDesc:""};i.functionData=n,i.pushLink=t.calloutPushDataUrl}).catch(function(){i.$emit("update:isLoading",!1)})}},mounted:function(){},created:function(){this.loadData()}})});
define("crm-setting/callcenter/configinfo/call-center-plugin/components/params-setting/params-setting",["crm-setting/callcenter/util"],function(t,i,n){function a(){return{paramErr:""}}var e=t("crm-setting/callcenter/util");n.exports=Vue.extend({name:"params-setting",template:'\n\t\t<div class="callcenter__params-setting">\n\t\t\t<div class="setting-content">\n\t\t\t\t<p class="desc">\n\t\t\t\t\t{{$t(\'对接呼叫中心，需要先进行各项参数配置。\')}}\n\t\t\t\t</p>\n\t\t\t\t<div class="params-table">\n\t\t\t\t\t<div class="table-row">\n\t\t\t\t\t\t<p class="table-col request">{{$t(\'参数名称\')}}</p>\n\t\t\t\t\t\t<p class="table-col request">{{$t(\'api name\')}}</p>\n\t\t\t\t\t\t<p class="table-col request">{{$t(\'参数值\')}}</p>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div \n\t\t\t\t\t\tclass="table-row" \n\t\t\t\t\t\tv-for="(param, index) of (isEditing ? editingParamList : paramList)" \n\t\t\t\t\t\t:key="index"\n\t\t\t\t\t>\n\t\t\t\t\t\t<div class="table-col">\n\t\t\t\t\t\t\t<fx-input \n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t:placeholder="$t(\'请输入参数名称\')"\n\t\t\t\t\t\t\t\tv-model="param.name" \n\t\t\t\t\t\t\t\t:disabled="!isEditing"\n\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="table-col">\n\t\t\t\t\t\t\t<fx-input \n\t\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\t\t:placeholder="$t(\'请输入apiname\')"\n\t\t\t\t\t\t\t\tv-model="param.apiName" \n\t\t\t\t\t\t\t\t:disabled="!isEditing"\n\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="table-col">\n\t\t\t\t\t\t\t<fx-input\n\t\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\t\t:placeholder="$t(\'请输入参数值\')"\n\t\t\t\t\t\t\t\tv-model="param.value" \n\t\t\t\t\t\t\t\t:disabled="!isEditing"\n\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<i \n\t\t\t\t\t\t\tclass="d-icon icon el-icon-delete"\n\t\t\t\t\t\t\tv-show="isEditing" \n\t\t\t\t\t\t\t@click="deleteParam(index)"\n\t\t\t\t\t\t></i>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<p class="error-msg" v-show="error.paramErr" style="position: relative">\n\t\t\t\t\t<i class="el-icon-warning"></i>\n\t\t\t\t\t{{ error.paramErr }}\n\t\t\t\t</p>\n\n\t\t\t\t<div class="params-table-btn" v-show="isEditing" @click="addParam">\n\t\t\t\t\t<p class="link-btn">{{\'+ \' + $t(\'继续添加\')}}</p>\n\t\t\t\t</div>\n\t\t\t\t\n\t\t\t\t<div class="setting-submitting-area" v-if="isEditing">\n\t\t\t\t\t<fx-button size="mini" type="primary" @click="submit">{{$t(\'保存\')}}</fx-button>\n\t\t\t\t\t<fx-button size="mini" @click="isEditing = false">{{$t(\'取消\')}}</fx-button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="setting-btn-area" v-show="!isEditing">\n\t\t\t\t<p class="setting-btn link-btn" @click="isEditing = true">{{$t(\'设置\')}}</p>\n\t\t\t</div>\n\t\t</div>\n\t',props:{isLoading:{type:Boolean,default:!1},tenantBindId:String},data:function(){return{isEditing:!1,editingParamList:[],paramList:[{name:"",apiName:"",value:""}],error:a()}},watch:{editingParamList:{handler:function(t,i){this.error.paramErr=""},deep:!0},isEditing:function(t){t?this.editingParamList=JSON.parse(JSON.stringify(this.paramList)):this.error=a()}},computed:{},methods:{deleteParam:function(t){this.editingParamList.splice(t,1)},checkAllowSubmit:function(){var t=!0,i=this.editingParamList,n=this.error;return i.some(function(t){return!t.name||!t.apiName||!t.value})&&(t=!1,n.paramErr=$t("请完善参数设置")),t},submit:function(){var t=this;this.checkAllowSubmit()&&(this.$emit("update:isLoading",!0),e.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/new/saveCallMapping",params:{paramMappings:this.editingParamList,tenantBindId:this.tenantBindId}}).then(function(){t.$emit("update:isLoading",!1),t.loadData()}).catch(function(){t.$emit("update:isLoading",!1),t.loadData()}))},addParam:function(){this.editingParamList.push({name:"",apiName:"",value:""})},loadData:function(){var i=this;this.isEditing=!1,this.$emit("update:isLoading",!0),e.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/new/queryNewCallCenterConfig",params:{tenantBindId:this.tenantBindId}}).then(function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};i.$emit("update:isLoading",!1);t=((t.Value||{}).data||{}).paramMappings||[{name:"",apiName:"",value:""}];i.paramList=t}).catch(function(){i.$emit("update:isLoading",!1)})}},mounted:function(){},created:function(){this.loadData()}})});
define("crm-setting/callcenter/configinfo/call-center-plugin/components/provider-setting/provider-setting",[],function(t,n,i){i.exports=Vue.extend({name:"provider-setting",template:'\n\t\t<div class="callcenter__provider-setting">\n\t\t\t<div class="setting-content">\n\t\t\t\t<p class="desc">\n\t\t\t\t\t{{$t(\'选择呼叫中心厂商后，配置厂商关键参数信息，让系统互通。\')}}\n\t\t\t\t</p>\n\t\t\t\t<p class="setting-label request">\n\t\t\t\t\t{{$t(\'服务商\')}}\n\t\t\t\t</p>\n\t\t\t\t<fx-select \n\t\t\t\t\tstyle="width: 423px" \n\t\t\t\t\tsize="small" \n\t\t\t\t\t:disabled="!isEditing"\n\t\t\t\t></fx-select>\n\t\t\t\t<div class="setting-submitting-area" v-if="isEditing">\n\t\t\t\t\t<fx-button size="mini" type="primary">{{$t(\'保存\')}}</fx-button>\n\t\t\t\t\t<fx-button size="mini" @click="isEditing = false">{{$t(\'取消\')}}</fx-button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="setting-btn-area" v-show="!isEditing">\n\t\t\t\t<p class="setting-btn link-btn" @click="isEditing = true">{{$t(\'设置\')}}</p>\n\t\t\t</div>\n\t\t</div>\n\t',props:{},data:function(){return{isEditing:!1}},watch:{},computed:{},methods:{},mounted:function(){},created:function(){}})});
define("crm-setting/callcenter/configinfo/configinfo",["crm-modules/common/util","./template/configinfo-html","crm-widget/select/select","../model/model","crm-setting/callcenter/util","./login-type-setting/login-type-setting","./call-center-plugin/call-center-plugin","./multi-tenant/multi-tenant","./white-list/white-list","./call-center-config/call-center-config"],function(e,t,i){var n=e("crm-modules/common/util"),r=(e("./template/configinfo-html"),e("crm-widget/select/select"),e("../model/model")),a=(e("crm-setting/callcenter/util"),e("./login-type-setting/login-type-setting"),e("./call-center-plugin/call-center-plugin"),e("./multi-tenant/multi-tenant"),e("./white-list/white-list"),e("./call-center-config/call-center-config")),e=Backbone.View.extend({initialize:function(i){var e=this,n=(e.setElement(i.wrapper),e.model=new r(Object.assign(i.data,{data:i.data})),e.opts=i,e.$$={},e.selectList=_.map(i.data.serviceProviderAttribute,function(e){return{name:e.name,value:e.id}}),{});i.data.confJson&&(n=JSON.parse(i.data.confJson)),e.model.set("serviceProvider",e.model.get("data").serviceProvider),e.model.get("data").serviceProvider&&-1!=e.model.get("data").serviceProvider&&(e.oldServiceprovider=e.model.get("data").serviceProvider),_.each(i.data.serviceProviderAttribute,function(t){e.model.get("serviceProvider")||e.model.set("serviceProvider",t.id),_.each(t.conf,function(e){e.rules=e.rules||{},t.id==i.data.serviceProvider?"accountId"==e.value?e.v=i.data.accountId||"":e.v=n[e.value]||"":e.v=""})}),e.render()},events:{"click .j-save":"onSubmit","click .j-cancel":"cancelVendorSetting","input .configinfo-cont .fm-ipt":"setText","click .configinfo-cont .b-g-ipt":"_hideError","click .configinfo-btns .j-cancel-add":"cancelObjSetting","click .telmarketing-btn-setting-obj":"vendorSetting"},render:function(){var t=this,e=t.selectList,i=this.model.get("serviceProvider"),n=this.model.get("serviceProviderAttribute"),r=this.model.get("id"),c=this.model.get("callCenterAppId"),o=this.model.get("name");this.dataInstance=FxUI.create({template:'<call-center-config :tenantBindId="tenantBindId" :serviceProvider="serviceProvider" :serviceProviderAttribute="serviceProviderAttribute" :selectList="selectList" :callCenterAppId="callCenterAppId" :name="name" @saveConfig="handleSaveConfig"></call-center-config>',components:{CallCenterConfig:a},data:function(){return{serviceProvider:i,serviceProviderAttribute:n,selectList:e,tenantBindId:r,callCenterAppId:c,name:o}},watch:{},computed:{},methods:{handleSaveConfig:function(e){t.trigger("saveConfig",e)}},mounted:function(){},created:function(){}}),this.$el.html(this.dataInstance.$el)},initCallCenterConfig:function(){var e,t=this,i=this.$el.find("#configinfo-inner-container");i&&i.length&&((e=new a({el:document.createElement("div"),propsData:{serviceProvider:serviceProvider,serviceProviderAttribute:serviceProviderAttribute,selectList:t.selectList||[]}})).$on("save",function(e){t.trigger("save")}),i.append(e.$el),this.$$.callCenterConfig=e)},getServiceRecordRefObjList:function(){var t=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/getServiceRecordRefObjList",data:{},success:function(e){e.Value&&1==e.Value.isSucc?(t.remainingObjList=e.Value.remainingObjList,t.render()):n.remind(3,e.Result.FailureMessage)}},{errorAlertModel:1})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},_hideError:function(e){e=$(e.target);n.hideErrmsg(e)},setText:function(e){var e=e.target,t=this.model.get("data")||{};t.serviceProviderAttribute[$(e).data("index")].conf[$(e).data("index2")].v=$.trim(e.value),this.model.set("data",t)},destroy:function(){this.confirm&&this.confirm.destroy(),this.dateInstance&&this.dateInstance.destroy(),this.dateInstance=null}});i.exports=e});
define("crm-setting/callcenter/configinfo/login-type-setting/account-setting-dialog/account-setting-dialog",[],function(t,n,i){var e=Vue.extend({name:"account-setting-dialog",template:'\n    <fx-dialog\n      :visible="showPanel"\n      @close="handleDialogClose"\n      custom-class="call-center__account-setting-dialog"\n\t\t\twidth="480px"\n      :title="dialogTitle"\n      append-to-body\n    >\n\t\t\t<div class="editor-header">\n\t\t\t</div>\n      <div class="editor-body">\n\t\t\t\t<ul class="setting-list">\n\t\t\t\t\t<li class="setting-item">\n\t\t\t\t\t\t<div class="setting-item__label">\n\t\t\t\t\t\t\t<p class="setting-item__label-text">{{$t(\'天润管理员账号ID\')}}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="setting-item__comp">\n\t\t\t\t\t\t\t<fx-input \n\t\t\t\t\t\t\t\tclass="password-input"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\tv-model="editingData.accountId"\n\t\t\t\t\t\t\t\t:disabled="isEditing"\n\t\t\t\t\t\t\t\t@change="error.accountIdErr = \'\'"\n\t\t\t\t\t\t\t\t:placeholder="$t(\'请输入\')"\n\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t\t<p class="error-msg" v-show="error.accountIdErr">{{ error.accountIdErr }}</p>\n\t\t\t\t\t\t</div>\t\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class="setting-item">\n\t\t\t\t\t\t<div class="setting-item__label">\n\t\t\t\t\t\t\t<p class="setting-item__label-text">{{$t(\'密码\')}}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="setting-item__comp">\n\t\t\t\t\t\t\t<fx-input \n\t\t\t\t\t\t\t\tclass="password-input"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\tshow-password\n\t\t\t\t\t\t\t\tv-model="editingData.password"\n\t\t\t\t\t\t\t\t@change="error.passwordErr = \'\'"\n\t\t\t\t\t\t\t\t:placeholder="$t(\'请输入\')"\n\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t\t<p class="error-msg" v-show="error.passwordErr">{{ error.passwordErr }}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class="setting-item">\n\t\t\t\t\t\t<div class="setting-item__label">\n\t\t\t\t\t\t\t<p class="setting-item__label-text">{{$t(\'确认密码\')}}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="setting-item__comp">\n\t\t\t\t\t\t\t<fx-input \n\t\t\t\t\t\t\t\tclass="password-input"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\tshow-password\n\t\t\t\t\t\t\t\tv-model="editingData.confirmPassword"\n\t\t\t\t\t\t\t\t@change="error.confirmPasswordErr = \'\'"\n\t\t\t\t\t\t\t\t:placeholder="$t(\'请输入\')"\n\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t\t<p class="error-msg" v-show="error.confirmPasswordErr">{{ error.confirmPasswordErr }}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n      </div>\n      <span slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="handleSubmit" size="small">\n          {{ $t(\'确定\') }}\n        </fx-button>\n        <fx-button @click="handleDialogClose" size="small">\n\t\t\t\t\t{{ $t(\'取消\') }}\n\t\t\t\t</fx-button>\n      </span>\n    </fx-dialog>\n  ',props:{accountId:{type:String,default:""},haveSetAccountList:{type:Array,default:function(){return[]}}},computed:{isEditing:function(){return!!this.accountId}},data:function(){return{showPanel:!1,error:{accountIdErr:"",passwordErr:"",confirmPasswordErr:""},editingData:{accountId:"",password:"",confirmPassword:""},dialogTitle:this.isEditing?$t("新建Token登录"):$t("编辑Token登录")}},methods:{loadData:function(){this.editingData.accountId=this.accountId},formatEditingData:function(){this.editingData.accountId=this.editingData.accountId.trim()},checkAllowSubmit:function(){var t=!0,n=this.editingData,i=this.error;return n.accountId||(i.accountIdErr=$t("请输入账号ID"),t=!1),n.accountId&&this.haveSetAccountList.includes(n.accountId)&&!this.isEditing&&(i.accountIdErr=$t("账号ID已存在"),t=!1),n.password||(i.passwordErr=$t("请输入密码"),t=!1),n.confirmPassword||(i.confirmPasswordErr=$t("请输入确认密码"),t=!1),n.confirmPassword&&n.password&&n.password!==n.confirmPassword&&(i.confirmPasswordErr=$t("两次输入密码不一致"),t=!1),t},handleDialogClose:function(){this.$emit("cancel")},handleSubmit:function(){this.formatEditingData(),this.checkAllowSubmit()&&this.submit({id:this.editingData.accountId,password:this.editingData.password,edited:!0})},submit:function(t){this.$emit("submit",t)}},created:function(){this.loadData()}});e.show=function(){var s=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(t,n){var i=new e({el:document.createElement("div"),propsData:s});i.$on("hide",function(){i.showPanel=!1,setTimeout(function(){i.$destroy(),i.$el.remove()},1e3)}),i.$on("submit",function(){i.$emit("hide"),t.apply(void 0,arguments)}),i.$on("cancel",function(){i.$emit("hide"),n.apply(void 0,arguments)}),$("body").append(i.$el),setTimeout(function(){i.showPanel=!0},20),$("body").append(i.$el)})},i.exports=e});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ownKeys(n,t){var e,i=Object.keys(n);return Object.getOwnPropertySymbols&&(e=Object.getOwnPropertySymbols(n),t&&(e=e.filter(function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})),i.push.apply(i,e)),i}function _objectSpread(n){for(var t=1;t<arguments.length;t++){var e=null!=arguments[t]?arguments[t]:{};t%2?ownKeys(Object(e),!0).forEach(function(t){_defineProperty(n,t,e[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(e)):ownKeys(Object(e)).forEach(function(t){Object.defineProperty(n,t,Object.getOwnPropertyDescriptor(e,t))})}return n}function _defineProperty(t,n,e){return(n=_toPropertyKey(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,n){if("object"!=_typeof(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0===e)return("string"===n?String:Number)(t);e=e.call(t,n||"default");if("object"!=_typeof(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,n){var e;if(t)return"string"==typeof t?_arrayLikeToArray(t,n):"Map"===(e="Object"===(e={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:e)||"Set"===e?Array.from(t):"Arguments"===e||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(e)?_arrayLikeToArray(t,n):void 0}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayLikeToArray(t,n){(null==n||n>t.length)&&(n=t.length);for(var e=0,i=Array(n);e<n;e++)i[e]=t[e];return i}define("crm-setting/callcenter/configinfo/login-type-setting/login-type-setting",["../../util","crm-modules/common/util","./account-setting-dialog/account-setting-dialog"],function(t,n,e){var i=t("../../util"),o=t("crm-modules/common/util"),a=t("./account-setting-dialog/account-setting-dialog");e.exports=Vue.extend({name:"login-type-setting",template:'\n\t\t<div class="callcenter__login-type-setting">\n\t\t\t<div class="setting-label">\n\t\t\t\t<p class="setting-label__text">{{$t("登录选择设置")}}</p>\n\t\t\t</div>\n\t\t\t<div class="setting-comp">\n\t\t\t\t<p class="comp-desc">{{ \n\t\t\t\t\t$t(\'座席登录客服工作台电话条时，天润侧要求用户进行登录验证。提供以下两种验证方式，建议同时启用两种验证方式，当均启用时，系统会优先采取token登录方式验证，token登录失败时，允许座席通过密码登录。\') \n\t\t\t\t}}</p>\n\t\t\t\t<ul class="login-setting-area" :class="{\'is-editing\': isEditing}">\n\t\t\t\t\t<li class="setting-area-item">\n\t\t\t\t\t\t<div class="setting-area__setting-label">\n\t\t\t\t\t\t\t<p class="setting-area__setting-label__text">{{$t("登录选择设置:")}}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="setting-area__setting-comp" v-if="isEditing">\n\t\t\t\t\t\t\t<fx-checkbox-group v-model="editingData.loginTypeList">\n\t\t\t\t\t\t\t\t<fx-checkbox :label="1">\n\t\t\t\t\t\t\t\t\t{{ $t(\'token登录\') }}\n\t\t\t\t\t\t\t\t\t<fx-tooltip \n\t\t\t\t\t\t\t\t\t\teffect="light"  \n\t\t\t\t\t\t\t\t\t\tplacement="right"\n\t\t\t\t\t\t\t\t\t\t:content="$t(\'启用此种登录方式，需要在后台维护一个管理员的账号、密码信息，座席登录电话条时，系统会在后台自动完成密码校验，无需座席手动输入信息。注意:修改天润侧管理员账号密码后，会导致登录失效,如需更改账号密码，尽量在不影响业务时进行快速修改。\') ">\n\t\t\t\t\t\t\t\t\t\t<i class="question-icon"></i>\n\t\t\t\t\t\t\t\t\t</fx-tooltip>\t\n\t\t\t\t\t\t\t\t</fx-checkbox>\n\t\t\t\t\t\t\t\t<fx-checkbox :label="2">\n\t\t\t\t\t\t\t\t\t{{ $t(\'密码登录\') }}\n\t\t\t\t\t\t\t\t\t<fx-tooltip \n\t\t\t\t\t\t\t\t\t\teffect="light"\n\t\t\t\t\t\t\t\t\t\tplacement="right"\n\t\t\t\t\t\t\t\t\t\t:content="$t(\'启用此种登录方式，座席需要在首次登录电话条时输入密码进行校验。\') ">\n\t\t\t\t\t\t\t\t\t\t<i class="question-icon"></i>\n\t\t\t\t\t\t\t\t\t</fx-tooltip>\n\t\t\t\t\t\t\t\t</fx-checkbox>\n\t\t\t\t\t\t\t</fx-checkbox-group>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="setting-area__setting-showing" v-else>\n\t\t\t\t\t\t\t<p class="setting-area__setting-showing__text">{{ loginTypeLabel }}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</li>\n\t\t\t\t\t<li class="setting-area-item token-type">\n\t\t\t\t\t\t<div class="setting-area__setting-label">\n\t\t\t\t\t\t\t<p class="setting-area__setting-label__text">{{$t("Token登录:")}}</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div class="setting-area__setting-comp">\n\t\t\t\t\t\t\t<div class="object-setting-table-wrap" :class="{\'is-editing\': isEditing}">\n\t\t\t\t\t\t\t\t<fx-table \n\t\t\t\t\t\t\t\t\t:data="isEditing ? editingData.accountList : accountList" \n\t\t\t\t\t\t\t\t\tborder \n\t\t\t\t\t\t\t\t\tclass="object-setting-table"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<fx-table-column prop="id" :label="$t(\'天润管理员账号ID\')" width="171"></fx-table-column>\n\t\t\t\t\t\t\t\t\t<fx-table-column prop="password" :label="$t(\'密码\')" width="202"></fx-table-column>\n\t\t\t\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\t\t\t\tv-if="isEditing"\n\t\t\t\t\t\t\t\t\t\t:label="$t(\'操作\')"\n\t\t\t\t\t\t\t\t\t\twidth="83">\n\t\t\t\t\t\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t\t\t\t\t\t<fx-button \n\t\t\t\t\t\t\t\t\t\t\t\t@click="editAccount(scope.row)" \n\t\t\t\t\t\t\t\t\t\t\t\ttype="text" \n\t\t\t\t\t\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\t\t\t\t\t\tclass="table-text-btn link-btn"\n\t\t\t\t\t\t\t\t\t\t\t>{{ $t(\'编辑\') }}</fx-button>\n\t\t\t\t\t\t\t\t\t\t\t<fx-button \n\t\t\t\t\t\t\t\t\t\t\t\t@click="deleteAccount(scope.row)" \n\t\t\t\t\t\t\t\t\t\t\t\ttype="text" \n\t\t\t\t\t\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\t\t\t\t\t\tclass="table-text-btn link-btn"\n\t\t\t\t\t\t\t\t\t\t\t>{{ $t(\'删除\') }}</fx-button>\n\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t\t\t</fx-table>\n\t\t\t\t\t\t\t\t<div class="table-add-line" v-if="isEditing">\n\t\t\t\t\t\t\t\t\t<fx-button \n\t\t\t\t\t\t\t\t\t\t@click="editAccount" \n\t\t\t\t\t\t\t\t\t\ttype="text" \n\t\t\t\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\t\t\t\tclass="link-btn"\n\t\t\t\t\t\t\t\t\t>{{ \'+ \' + $t(\'新建\') }}</fx-button>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</li>\n\t\t\t\t</ul>\n\t\t\t\t<div class="submit-area" v-show="isEditing">\n\t\t\t\t\t<fx-button type="primary" @click="saveSetting" size="small">\n\t\t\t\t\t\t{{ $t(\'保存\') }}\n\t\t\t\t\t</fx-button>\n\t\t\t\t\t<fx-button @click="hideSetting" size="small">\n\t\t\t\t\t\t{{ $t(\'取消\') }}\n\t\t\t\t\t</fx-button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="setting-operate">\n\t\t\t\t<fx-button \n\t\t\t\t\t@click="openSetting" \n\t\t\t\t\ttype="text" \n\t\t\t\t\tsize="small" \n\t\t\t\t\tclass="link-btn"\n\t\t\t\t>{{ $t(\'设置\') }}</fx-button>\n\t\t\t</div>\n\t\t</div>\n\t',props:{tenantBindId:String},data:function(){return{loginTypeLabel:"",loginTypeList:[],accountList:[],editingData:{loginTypeList:[],accountList:[]},isEditing:!1}},methods:{editAccount:function(n){var e=this;a.show({accountId:n.id,haveSetAccountList:this.editingData.accountList.map(function(t){return t.id})}).then(function(t){n&&n.id?Object.assign(n,t):e.editingData.accountList.push(t)})},deleteAccount:function(n){var t=this.editingData.accountList.findIndex(function(t){return t.id===n.id});this.editingData.accountList.splice(t,1)},saveSetting:function(){var t=this;i.FHHApi({url:"/eservice/callcenter/config/saveLoginSetting",params:{adminConfigs:this.editingData.accountList.map(function(t){return{account:t.id,password:t.password,edited:t.edited}}),loginTypes:this.editingData.loginTypeList,tenantBindId:this.tenantBindId},config:{autoPrependPath:!1}}).then(function(){o.remind(1,$t("设置成功！")),t.hideSetting(),t.loadData()})},hideSetting:function(){this.isEditing=!1,this.editingData={}},openSetting:function(){this.isEditing=!0,this.editingData={loginTypeList:_toConsumableArray(this.loginTypeList),accountList:this.accountList.map(function(t){return _objectSpread({},t)})}},loadLoginTypeData:function(){var e=this;i.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/getLoginSetting",params:{tenantBindId:this.tenantBindId}}).then(function(t){var t=t.Value.data,n={1:$t("token登录"),2:$t("密码登录")};e.loginTypeList=t.loginTypes,e.loginTypeLabel=e.loginTypeList.map(function(t){return n[t]}).join(",")||"--",e.accountList=t.adminConfigs.map(function(t){return{id:t.account,password:t.password||"--"}})})},loadData:function(){this.loadLoginTypeData()}},created:function(){this.loadData()}})});
define("crm-setting/callcenter/configinfo/multi-tenant/multi-tenant",["crm-setting/callcenter/util"],function(n,t,e){var i=n("crm-setting/callcenter/util"),o=Vue.extend({template:'\n      <fx-dialog\n        :visible="showPanel"\n        @close="handleDialogClose"\n        custom-class="cct-multi-tenant__company-examine-dialog"\n        :title="$t(\'企业信息验证\')"\n        width="480px"\n        append-to-body\n      >\n        <div class="editor-body">\n          <fx-alert\n            :title="$t(\'如果已经绑定了多个历史企业，则需要对所有的历史企业验证通过，中间用;分隔。\')"\n            type="warning"\n            show-icon    \n            :closable="false"\n          >\n          </fx-alert>\n          <ul class="setting-list">\n            <li class="setting-list__setting-item">\n              <div class="setting-item__label-wrap">\n                <p class="label request" :title="$t(\'纷享企业号\')">\n                  {{ $t(\'纷享企业号\') }}\n                </p>\n              </div>\n              <div class="setting-item__comp-wrap">\n                <fx-input \n                  v-model="editingData.fxCompany" \n                  :placeholder="$t(\'请输入\')"\n                  @change="error.fxCompanyErr = \'\'"\n                  size="small" \n                  clearable\n                ></fx-input>\n                <p class="error-msg" v-show="error.fxCompanyErr">\n                  <i class="el-icon-warning"></i>\n                  {{ error.fxCompanyErr }}\n                </p>\n              </div>\n            </li>\n          </ul>\n        </div>\n        <span slot="footer" class="dialog-footer">\n          <fx-button type="primary" @click="handleSubmit" size="small">\n            {{ $t(\'确定\') }}\n          </fx-button>\n          <fx-button @click="handleDialogClose" size="small">{{\n            $t(\'取消\')\n          }}</fx-button>\n        </span>\n      </fx-dialog>\n    ',name:"cct-multi-tenant__company-examine-dialog",props:{submitData:{type:Object,default:function(){return{}}},editingData:{type:Object,default:function(){return{fxCompany:""}}}},data:function(){return{showPanel:!1,error:{fxCompanyErr:""}}},methods:{handleDialogClose:function(){this.$emit("cancel")},checkAllowSubmit:function(){var n=!0,t=this.error;return this.editingData.fxCompany||(t.fxCompanyErr=$t("请输入"),n=!1),n},reexamine:function(n){this.$confirm(n||$t("企业号校验失败，请重新校验"),{confirmButtonText:$t("重新校验"),type:"error"}).then(function(){})},handleSubmit:function(){var t,e=this;this.checkAllowSubmit()&&(CRM.util.showLoading_tip($t("校验中...")),t={},i.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/saveTenantBind",params:Object.assign({},this.submitData,{checkFsEa:this.editingData.fxCompany}),config:{errorAlertModel:0}}).then(function(n){t=n,CRM.util.hideLoading_tip();n=n.Value||{};return"0"==n.errorCode?e.$alert($t("校验成功！"),{type:"success"}):("300"==n.errorCode?e.reexamine(n.errorMessage):e.$alert(n.errorMessage||"error",{type:"error"}),Promise.reject())}).then(function(){e.$emit("submit",t)}).catch(function(){CRM.util.hideLoading_tip()}))}}});o.$show=function(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(n,t){var e=new o({el:document.createElement("div"),propsData:i});e.$on("hide",function(){e.showPanel=!1,setTimeout(function(){e.$destroy(),e.$el.remove()},1e3)}),e.$on("submit",function(){e.$emit("hide"),n.apply(void 0,arguments)}),e.$on("cancel",function(){e.$emit("hide"),t.apply(void 0,arguments)}),$("body").append(e.$el),setTimeout(function(){e.showPanel=!0},20),$("body").append(e.$el)})};e.exports.$showMultiExamineConfirm=function(t){var n=$t("呼叫中心厂商账户已经存在于纷享销客系统中，请确认此账户是否正在您其他的纷享企业租户中应用。"),e=$t("如果是您的企业正在应用，并且想实现一个呼叫中心对接多个纷享租户的功能，请继续验证已绑定企业账号信息，验证通过后可继续设置；如果不是您的企业正在应用，请联系呼叫中心客服人员确认账户信息是否正确。");return Vue.prototype.$confirm('\n          <p style="color: var(--color-neutrals15)">'.concat(n,'</p>\n          <p style="color: var(--color-neutrals11)">').concat(e,"</p>\n        "),{dangerouslyUseHTMLString:!0,confirmButtonText:$t("继续验证"),type:"warning"}).then(function(n){return o.$show(t)})}});
define("crm-setting/callcenter/configinfo/template/configinfo-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="configinfo-wrapper"> <div id="configinfo-inner-container"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/configinfo/white-list/white-list",["../../util","crm-modules/common/util"],function(t,i,n){var e=t("../../util");t("crm-modules/common/util");n.exports=Vue.extend({name:"white-list-setting",template:'\n\t\t<div class="callcenter__white-list-setting" :class="{ edit: isEditing }" v-loading="isLoading">\n\t\t\t<div class="setting-label">\n\t\t\t\t<p class="setting-label__text">{{$t("IP白名单")}}</p>\n\t\t\t</div>\n\t\t\t<div class="setting-comp">\n\t\t\t\t<p class="comp-desc">{{ $t(\'为保证系统安全性，需要配置第三方系统IP，纷享只接受白名单内IP发送的请求。如果有多个IP地址，每个 IP地址用；间隔。\') }}</p>\n\t\t\t\t<div class="display-area" v-show="!isEditing">\n\t\t\t\t\t<p>{{ $t(\'IP地址\') + \'：\' + (ipStr || \'--\') }}</p>\n\t\t\t\t</div>\n\t\t\t\t<div class="editing-area" v-show="isEditing">\n\t\t\t\t\t<div class="editing-line">\n\t\t\t\t\t\t<p class="editing-line__label required" :title="$t(\'IP地址\')">{{ $t(\'IP地址\') }}</p>\n\t\t\t\t\t\t<div class="editing-line__comp">\n\t\t\t\t\t\t\t<fx-input \n\t\t\t\t\t\t\t\tsize="small" \n\t\t\t\t\t\t\t\t:placeholder="$t(\'请输入\')" \n\t\t\t\t\t\t\t\tv-model="editingData.ipStr"\n\t\t\t\t\t\t\t\t@change="error.ipStrErr = \'\'"\n\t\t\t\t\t\t\t\tstyle="width: 272px"\n\t\t\t\t\t\t\t></fx-input>\n\t\t\t\t\t\t\t<p class="error-msg" v-show="error.ipStrErr">\n\t\t\t\t\t\t\t\t<i class="el-icon-warning"></i>\n\t\t\t\t\t\t\t\t{{ error.ipStrErr }}\n\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<div class="submit-area" v-show="isEditing">\n\t\t\t\t\t<fx-button type="primary" @click="saveSetting" size="small">\n\t\t\t\t\t\t{{ $t(\'保存\') }}\n\t\t\t\t\t</fx-button>\n\t\t\t\t\t<fx-button @click="hideSetting" size="small">\n\t\t\t\t\t\t{{ $t(\'取消\') }}\n\t\t\t\t\t</fx-button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="setting-operate" v-show="!isEditing">\n\t\t\t\t<fx-link \n\t\t\t\t\t@click="openSetting" \n\t\t\t\t\ttype="standard" \n\t\t\t\t\t:underline="false"\n\t\t\t\t\tclass="link-btn"\n\t\t\t\t>{{ $t(\'设置\') }}</fx-link>\n\t\t\t</div>\n\t\t</div>\n\t',props:{tenantBindId:String},data:function(){return{ipStr:"",editingData:{ipStr:""},error:{ipStrErr:""},isLoading:!1,isEditing:!1}},methods:{checkAllowToSubmit:function(){var t=!0,i=this.editingData,n=this.error;return i.ipStr||(n.ipStrErr=$t("请输入"),t=!1),t},saveSetting:function(){var i=this;this.checkAllowToSubmit()&&(this.isLoading=!0,e.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/saveIpWhiteList",params:{ip:this.editingData.ipStr,tenantBindId:this.tenantBindId}}).then(function(t){i.hideSetting(),i.loadData(),i.isLoading=!1}).catch(function(){i.isLoading=!1}))},hideSetting:function(){this.isEditing=!1,this.editingData={}},openSetting:function(){this.isEditing=!0,this.editingData={ipStr:this.ipStr}},loadData:function(){var i=this;this.isLoading=!0,e.FHHApi({url:"/EM1HESERVICE2/eservice/callcenter/config/getIpWhiteList",params:{tenantBindId:this.tenantBindId}}).then(function(t){i.ipStr=t.Value.data.ip,i.isLoading=!1}).catch(function(){i.isLoading=!1})}},created:function(){this.loadData()}})});
define("crm-setting/callcenter/model/model",[],function(e,t,l){FS.crmUtil;l.exports=Backbone.Model.extend({defaults:{}})});
define("crm-setting/callcenter/telmarketing/addtelobj/addtelobj",["crm-modules/common/util","crm-widget/dialog/dialog","../template/addtelobj-html","base-modules/select/select"],function(e,t,a){var o=e("crm-modules/common/util"),l=e("crm-widget/dialog/dialog"),i=e("../template/addtelobj-html"),n=e("base-modules/select/select"),s=l.extend({attrs:{title:$t("新增外呼工作台"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-callcenter"},events:{"click .seatId":"_hideError","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=s.superclass.show.call(this);return this.set(e||{data:{}}),this.addPostData={nextFollowFieldAPIName:"",nextFollowFieldLabelName:"",objAPIName:"",objFollowAPIName:"",objFollowLabelName:"",objLabelName:"",sortAsc:null,sortFieldAPIName:null,sortFieldLabelName:null},this.objAPINameAndLabelList="",this.allFieldList="",this.allObjList="",this.getRemainingCallOutObjList(),t},getRemainingCallOutObjList:function(){var t=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/getRemainingCallOutObjList",data:{},success:function(e){1==e.Value.isSucc&&(t.objAPINameAndLabelList=e.Value.objAPINameAndLabelList,t.setContent(i({data:t.get("data")})),t.initobj())}},{})},onSubmit:function(){var e=this.changeObj.getValue(),t=this.$(".add-obj-select"),a=this.$(".add-next-follow-field"),l=this.$(".add-follow-apiname");return 0==e?(o.showErrmsg(t,$t("请选择外呼对象！")),!1):0==this.nextfollowfield.getValue()?(o.showErrmsg(a,$t("请选择下次跟进时间字段！")),!1):0==this.objFollowAPIName.getValue()?(o.showErrmsg(l,$t("请选择电话跟进记录对象！")),!1):void this.submit(this.get("data").concat(this.addPostData))},initobj:function(){var a=this,e=[{id:0,name:$t("请选择")}].concat(a.objAPINameAndLabelList);a.changeObj=new n({wrapper:a.$(".add-obj-select"),options:_.map(e,function(e){return{name:e.name,value:e.id}}),zIndex:+a.get("zIndex")+10,defaultValue:0}),a.changeObj.on("change",function(e,t){o.hideErrmsg($(".add-obj-select")),a.addPostData.objAPIName=e.value,a.addPostData.objLabelName=e.name,a.getAllFieldList()})},_hideError:function(e){e=$(e.target);o.hideErrmsg(e)},getAllFieldList:function(e){var t=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/getAllFieldList",type:"post",data:{objAPINameList:[t.addPostData.objAPIName]},success:function(e){t.allFieldList=t.fliterDateField(e.Value.fieldAPINameAndLabelMap[t.addPostData.objAPIName]),t.getRelateObjAPINameList()}},{errorAlertModel:1})},fliterDateField:function(e){return _.filter(e,function(e){return"date_time"==e.type})},getRelateObjAPINameList:function(){var t=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/getRelateObjAPINameList",type:"post",data:{objAPINameList:[t.addPostData.objAPIName]},success:function(e){t.allObjList=e.Value.objAPINameAndLabelMap[t.addPostData.objAPIName],t.initFollowField(),t.initObjFollowAPIName()}},{errorAlertModel:1})},initFollowField:function(){var t=this,e=[{id:0,name:$t("请选择")}].concat(t.allFieldList);t.nextfollowfield&&t.nextfollowfield.destroy(),t.nextfollowfield=new n({wrapper:t.$(".add-next-follow-field"),options:_.map(e,function(e){return{name:e.name,value:e.id}}),zIndex:+t.get("zIndex")+10,defaultValue:0}),t.nextfollowfield.on("change",function(e){o.hideErrmsg($(".add-next-follow-field")),t.addPostData.nextFollowFieldAPIName=e.value,t.addPostData.nextFollowFieldLabelName=e.name})},initObjFollowAPIName:function(){var a=this,e=[{id:0,name:$t("请选择")}].concat(a.allObjList);a.objFollowAPIName&&a.objFollowAPIName.destroy(),a.objFollowAPIName=new n({wrapper:a.$(".add-follow-apiname"),options:_.map(e,function(e){return{name:e.name,value:e.id}}),zIndex:+a.get("zIndex")+10,defaultValue:0}),a.objFollowAPIName.on("change",function(e,t){o.hideErrmsg($(".add-follow-apiname")),a.addPostData.objFollowAPIName=e.value,a.addPostData.objFollowLabelName=e.name})},submit:function(e){var t=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/setTelemarketingSetting",data:{calloutObjList:e,validTalkTimeInSecond:null},success:function(e){1==e.Value.isSucc&&(t.trigger("success"),o.remind(1,$t("添加成功！")),t.hide())}},{})},hide:function(){this.destroy()},destroy:function(){return this.employeeWidget&&this.employeeWidget.destroy(),s.superclass.destroy.call(this)}});a.exports=s});
define("crm-setting/callcenter/telmarketing/telmarketing",["crm-modules/common/util","./template/telmarketing-html","./config/tableconfig","base-modules/select/select","base-modules/ui/newtable/newtable","./addtelobj/addtelobj"],function(e,t,a){var l=e("crm-modules/common/util"),i=e("./template/telmarketing-html"),n=(e("./config/tableconfig"),e("base-modules/select/select")),o=e("base-modules/ui/newtable/newtable"),s=e("./addtelobj/addtelobj"),e=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.opts=e,this.objsetFlag=!1,this.allFieldSelectArr={},this.apinames=[],this.allFieldList=[],this.allObjList=[],this.calloutObjList=[],this.validTalkTimeInSecond="",this.render(),this.showTable()},render:function(){this.$el.html(i())},events:{"click .j-add":"onAdd","click .j-del":"onDel","click .j-del-btn":"delCallObj","click .go-leads-seting":"leadSetting","click .go-account-seting":"accountSetting","click .batch-button":"saveBatchManager","click .set-call-time .j-save":"callTimeSave","click .set-call-time .j-cancel":"callTimeCancel","click .telmarketing-btn-setting-obj":"changeObjSetFlag","click .telmarketing-btn-setting-date":"changeDateSetFlag","click .telmarketing-workbench-btns .j-save":"workbenchSave","click .telmarketing-workbench-btns .j-cancel":"workbenchCancel"},show:function(){this.$el.show()},onAdd:function(){var e=this;e.AddTelObj&&(e.AddTelObj.destroy(),e.AddTelObj=null),e.AddTelObj=new s({title:$t("新增外呼工作台"),type:"add"}),e.AddTelObj.on("success",function(){e.showTable()}),e.AddTelObj.show({data:e.calloutObjList})},delCallObj:function(e,t){this.calloutObjList.splice(t,1),this.allSave()},upCallObj:function(e,t){var a=this,l=a.calloutObjList[t-1];a.calloutObjList[t]=l,a.calloutObjList[t-1]=e,a.allSave()},downCallObj:function(e,t){var a=this,l=a.calloutObjList[t+1];a.calloutObjList[t]=l,a.calloutObjList[t+1]=e,a.allSave()},changeObjSetFlag:function(){this.objsetFlag=!this.objsetFlag,1==this.objsetFlag?($(".telmarketing-workbench-btns").show(),$(".add-btn-wrapper").hide(),$(".telmarketing-btn-setting-obj").hide()):($(".telmarketing-workbench-btns").hide(),$(".telmarketing-btn-setting-obj").show(),$(".add-btn-wrapper").show()),this.showTable()},nochangeObjSetFlag:function(){1==this.objsetFlag?($(".telmarketing-workbench-btns").show(),$(".add-btn-wrapper").hide(),$(".telmarketing-btn-setting-obj").hide()):($(".telmarketing-workbench-btns").hide(),$(".telmarketing-btn-setting-obj").show(),$(".add-btn-wrapper").show()),this.showTable()},changeDateSetFlag:function(){$(".telmarketing-maxlimit-wrap .fm-ipt").val(this.validTalkTimeInSecond),$(".set-call-time").show(),$(".view-call-time").hide()},callTimeCancel:function(){$(".set-call-time").hide(),$(".view-call-time").show()},workbenchCancel:function(){$(".telmarketing-workbench-btns").hide(),$(".add-btn-wrapper").show(),$(".telmarketing-btn-setting-obj").show(),this.objsetFlag=!1,this.showTable()},workbenchSave:function(){var t=this;0!=t.checkPostData()&&l.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/setTelemarketingSetting",type:"post",data:{calloutObjList:t.calloutObjList,validTalkTimeInSecond:null},success:function(e){1==e.Value.isSucc?(l.remind(1,$t("设置成功！")),t.changeObjSetFlag()):l.remind(3,e.Value.errMsg||$t("设置失败!"))}},{errorAlertModel:1})},allSave:function(){var t=this;0!=t.checkPostData()&&l.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/setTelemarketingSetting",type:"post",data:{calloutObjList:t.calloutObjList,validTalkTimeInSecond:null},success:function(e){1==e.Value.isSucc?(l.remind(1,$t("设置成功！")),t.nochangeObjSetFlag()):l.remind(3,e.Value.errMsg||$t("设置失败!"))}},{errorAlertModel:1})},checkPostData:function(){for(var e=this,t=!0,a=0;a<e.calloutObjList.length;a++){if(0==e.calloutObjList[a].nextFollowFieldAPIName){l.alert($t("请选择下次跟进时间关联字段")),t=!1;break}if(0==e.calloutObjList[a].objFollowAPIName){l.alert($t("请选择电话跟进记录关联字段")),t=!1;break}0==e.calloutObjList[a].sortFieldAPIName&&(e.calloutObjList[a].sortFieldAPIName=null,e.calloutObjList[a].sortFieldLabelName=null)}return t},callTimeSave:function(){var t=this,e=$(".telmarketing-maxlimit-wrap .fm-ipt").val();""==e?CRM.util.alert($t("有效通话时长不能为空")):(t.validTalkTimeInSecond=e,l.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/setTelemarketingSetting",type:"post",data:{calloutObjList:null,validTalkTimeInSecond:t.validTalkTimeInSecond},success:function(e){1==e.Value.isSucc?(l.remind(1,$t("设置成功！")),$(".telmarketing-no-edite-label").html(t.validTalkTimeInSecond),t.callTimeCancel()):l.remind(3,e.Value.errMsg||$t("设置失败!"))}},{errorAlertModel:1}))},showTable:function(e){var n=this;n.$dt=n.$dt,n.$dt&&n.$dt.destroy(),FS.tpl.event.one("table_loaded",function(){1==n.objsetFlag&&n.initFilter()}),!1===n.objsetFlag?n.$dt=new o({$el:$(".telmarketing-data-table",n.$el),url:"/EM1HNCRM/API/v1/object/telemarketing/service/getTelemarketingSetting",requestType:"FHHApi",method:"post",height:"auto",showPage:!1,columns:[{data:"objLabelName",title:$t("外呼任务对象"),width:150},{data:"nextFollowFieldLabelName",title:$t("下次跟进时间"),width:150,render:function(e,t,a){return!1===n.objsetFlag?"<span>"+e+"</span>":'<div class="inline-select-nextFollowFieldLabelName"></div>'}},{data:"objFollowLabelName",title:$t("电话跟进记录"),width:150,render:function(e,t,a){return e=a.objFollowLabelName||"--",!1===n.objsetFlag?"<span>"+e+"</span>":'<div class="inline-select-objFollowLabelName "></div>'}}],initComplete:function(e){FS.tpl.event.trigger("table_loaded")},paramFormat:function(e){var t={pageSize:e.pageSize,pageNo:e.pageNumber};return _.extend(t,e)},rowCallBack:function(e,t){0===t.ei&&e.addClass("no-edit")},formatData:function(e){return n.apinames=[],_.each(e.calloutObjList,function(e){n.apinames.push(e.objAPIName)}),n.validTalkTimeInSecond=e.validTalkTimeInSecond,$(".telmarketing-no-edite-label").html(e.validTalkTimeInSecond),n.calloutObjList=[].concat(e.calloutObjList),{data:e.calloutObjList}}}):(n.$dt=new o({$el:$(".telmarketing-data-table",n.$el),url:"/EM1HNCRM/API/v1/object/telemarketing/service/getTelemarketingSetting",requestType:"FHHApi",method:"post",showPage:!1,height:"auto",columns:[{data:"objLabelName",title:$t("外呼任务对象"),width:150},{data:"nextFollowFieldLabelName",title:$t("下次跟进时间"),width:150,render:function(e,t,a){return!1===n.objsetFlag?"<span>"+e+"</span>":'<div class="inline-select-nextFollowFieldLabelName"></div>'}},{data:"objFollowLabelName",title:$t("电话跟进记录"),width:150,render:function(e,t,a){return e=a.objFollowLabelName||"--",!1===n.objsetFlag?"<span>"+e+"</span>":'<div class="inline-select-objFollowLabelName "></div>'}},{data:null,title:$t("操作"),lastFixed:!0,render:function(e,t,a,l,i){return 0==i&&1==n.calloutObjList.length?'<div class="btns"><a class="j-del-btn">'+$t("移除")+'</a><span">'+$t("上移")+'</span><span style="margin-right: 7px>'+$t("下移")+"</span></div>":i==n.calloutObjList.length-1?'<div class="btns"><a class="j-del-btn">'+$t("移除")+'</a><a class="j-up-btn">'+$t("上移")+"</a><span>"+$t("下移")+"</span></div>":0==i&&1<n.calloutObjList.length?'<div class="btns"><a class="j-del-btn">'+$t("移除")+'</a><span style="margin-right: 7px">'+$t("上移")+'</span><a class="j-down-btn">'+$t("下移")+"</a></div>":'<div class="btns"><a class="j-del-btn">'+$t("移除")+'</a><a class="j-up-btn">'+$t("上移")+'</a><a class="j-down-btn">'+$t("下移")+"</a></div>"}}],initComplete:function(e){FS.tpl.event.trigger("table_loaded")},paramFormat:function(e){var t={pageSize:e.pageSize,pageNo:e.pageNumber};return _.extend(t,e)},rowCallBack:function(e,t){0===t.ei&&e.addClass("no-edit")},formatData:function(e){return n.apinames=[],_.each(e.calloutObjList,function(e){n.apinames.push(e.objAPIName)}),n.validTalkTimeInSecond=e.validTalkTimeInSecond,$(".telmarketing-no-edite-label").html(e.validTalkTimeInSecond),n.calloutObjList=[].concat(e.calloutObjList),{data:e.calloutObjList}}}),n.$dt.on("trclick",function(e,t,a){t=$(t).index();a.hasClass("j-del-btn")?n.delCallObj(e,t):a.hasClass("j-up-btn")&&n.upCallObj(e,t),a.hasClass("j-down-btn")&&n.downCallObj(e,t)}))},initFilter:function(){this.getAllFieldList()},initAllFilter:function(){this.initSortAsc(),this.initSortFieldAPIName(),this.initObjFollowLabelName(),this.initnextFollowFieldLabelName()},initSortAsc:function(){var t,i=this;setTimeout(function e(){var l;0<(t=$(".inline-select-sortAsc")).length?(l=i.$dt.curData.data,t.each(function(e,t){var t=$(t).closest("tr").data("index"),t=l[t],a="",a=void 0===t.sortAsc?0:!1===t.sortAsc?2:1;new n({wrapper:$(this),height:28,options:i.allSortOptions,defaultValue:a}).on("change",function(e){var t=$(this.$el).closest("tr").data("index"),a=(l[t],""),a=0==e.value?null:1==e.value;i.calloutObjList[t].sortAsc=a})})):setTimeout(e,100)},100)},initSortFieldAPIName:function(){var t,i=this;setTimeout(function e(){var l;0<(t=$(".inline-select-sortFieldLabelName")).length?(l=i.$dt.curData.data,t.each(function(e,t){var t=$(t).closest("tr").data("index"),a=[{id:0,name:$t("请选择")}].concat(i.allFieldList[l[t].objAPIName]),t=l[t];new n({wrapper:$(this),options:_.map(a,function(e){return{name:e.name,value:e.id}}),defaultValue:t.sortFieldAPIName||0}).on("change",function(e){var t=$(this.$el).closest("tr").data("index");i.calloutObjList[t].sortFieldAPIName=e.value,i.calloutObjList[t].sortFieldLabelName=e.name})})):setTimeout(e,100)},100)},initnextFollowFieldLabelName:function(){var t,i=this;setTimeout(function e(){var l;0<(t=$(".inline-select-nextFollowFieldLabelName")).length?(l=i.$dt.curData.data,t.each(function(e,t){var t=$(t).closest("tr").data("index"),a=[{id:0,name:$t("请选择")}].concat(i.fliterDateField(i.allFieldList[l[t].objAPIName])),t=l[t];new n({wrapper:$(this),options:_.map(a,function(e){return{name:e.name,value:e.id}}),defaultValue:t.nextFollowFieldAPIName||0}).on("change",function(e){var t=$(this.$el).closest("tr").data("index");i.calloutObjList[t].nextFollowFieldAPIName=e.value,i.calloutObjList[t].nextFollowFieldLabelName=e.name})})):setTimeout(e,100)},100)},initObjFollowLabelName:function(){var t,i=this;setTimeout(function e(){var l;0<(t=$(".inline-select-objFollowLabelName")).length?(l=i.$dt.curData.data,t.each(function(e,t){var t=$(t).closest("tr").data("index"),a=[{id:0,name:$t("请选择")}].concat(i.allObjList[l[t].objAPIName]),t=l[t];new n({wrapper:$(this),options:_.map(a,function(e){return{name:e.name,value:e.id}}),defaultValue:t.objFollowAPIName||0}).on("change",function(e){var t=$(this.$el).closest("tr").data("index");i.calloutObjList[t].objFollowAPIName=e.value,i.calloutObjList[t].objFollowLabelName=e.name})})):setTimeout(e,100)},100)},fliterDateField:function(e){return _.filter(e,function(e){return"date_time"==e.type})},getAllFieldList:function(e){var t=this;l.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/getAllFieldList",type:"post",data:{objAPINameList:t.apinames},success:function(e){t.allFieldList=e.Value.fieldAPINameAndLabelMap,t.getRelateObjAPINameList()}},{errorAlertModel:1})},getRelateObjAPINameList:function(){var t=this;l.FHHApi({url:"/EM1HNCRM/API/v1/object/telemarketing/service/getRelateObjAPINameList",type:"post",data:{objAPINameList:t.apinames},success:function(e){t.allObjList=e.Value.objAPINameAndLabelMap,t.initAllFilter()}},{errorAlertModel:1})},createPostArr:function(e){var t=this;_.each(e,function(e){t.calloutObjList.push(e)})},leadSetting:function(){window.open("#crmmanage/=/module-clue","_blank")},accountSetting:function(){window.open("#crmmanage/=/module-customerrule","_blank")},hide:function(){this.$el.hide()},destroy:function(){}});a.exports=e});
define("crm-setting/callcenter/telmarketing/template/addtelobj-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb">' + ((__t = $t("外呼对象")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="add-obj-select"></div> </div> </div> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("下次跟进时间")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="add-next-follow-field"></div> </div> </div> <div class="fm-item"> <label class="fm-lb">' + ((__t = $t("跟进记录")) == null ? "" : __t) + "</label> <div class=\"fm-wrap\"> <div class='add-follow-apiname'></div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/callcenter/telmarketing/template/telmarketing-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="telmarketing-wrapper"> <div class="telmarketing-rulesetting-section"> <div class="telmarketing-section-content"> <div class="telmarketing-label">' + ((__t = $t("外呼工作台")) == null ? "" : __t) + '</div> <div class="telmarketing-view-wrap"> <div class="telmarketing-view"> <div class="telmarketing-data-wrapper"> <div class="telmarketing-data-table"></div> </div> <div class="add-btn-wrapper"> <a class="j-add">+&nbsp;' + ((__t = $t("新增")) == null ? "" : __t) + '</a> </div> <div class="telmarketing-workbench-btns" style="display:none"> <span class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span class="b-g-btn j-cancel">' + ((__t = $t("取消")) == null ? "" : __t) + '</span> </div> </div> <a class="telmarketing-btn-setting-obj">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> </div> </div> <div class="telmarketing-rulesetting-section"> <div class="telmarketing-section-content"> <div class="telmarketing-label">' + ((__t = $t("统计设置")) == null ? "" : __t) + '</div> <div class="telmarketing-edite-wrap set-call-time" style="display:none"> <div class="telmarketing-maxlimit-wrap"> <span class="telmarketing-edite-label" >' + ((__t = $t("有效通话时长：")) == null ? "" : __t) + '</span> <input class="b-g-ipt fm-ipt" onkeyup="value=value.replace(/[^\\d]/g,\'\')"></input>&nbsp;&nbsp;' + ((__t = $t("秒")) == null ? "" : __t) + ' </div> <div class="telmarketing-desc"> <span class="telmarketing-edite-label">' + ((__t = $t("当日电销工作台统计字段：")) == null ? "" : __t) + '</span> <span class="display-field">' + ((__t = $t("外呼数、接通数、有效通话数、有效通话时长")) == null ? "" : __t) + '</span> </div> <div class="telmarketing-btns"> <span class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span class="b-g-btn j-cancel">' + ((__t = $t("取消")) == null ? "" : __t) + '</span> </div> </div> <div class="telmarketing-view-wrap view-call-time"> <div class="telmarketing-view"> <div class="telmarketing-note">' + ((__t = $t("有效通话时长：")) == null ? "" : __t) + '<span class="telmarketing-no-edite-label"></span>&nbsp;&nbsp;' + ((__t = $t("秒")) == null ? "" : __t) + '</div> <div class="telmarketing-desc"> ' + ((__t = $t("当日电销工作台统计字段：外呼数、接通数、有效通话数、有效通话时长")) == null ? "" : __t) + ' </div> </div> <a class="telmarketing-btn-setting-date">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> </div> </div> </div> <div class="telmarketing-rulesetting-section"> <div class="telmarketing-section-content"> <div class="telmarketing-label">' + ((__t = $t("任务分配/回收配置")) == null ? "" : __t) + '</div> <div class="telmarketing-view-wrap"> <div class="telmarketing-view"> <div class="telmarketing-note"> <span class="seting-title">' + ((__t = $t("线索与线索池管理")) == null ? "" : __t) + '</span> <a class="go-leads-seting">' + ((__t = $t("前往设置")) == null ? "" : __t) + '</a> </div> <div class="telmarketing-desc"> <span class="seting-title">' + ((__t = $t("客户和公海管理")) == null ? "" : __t) + '</span> <a class="go-account-seting">' + ((__t = $t("前往设置")) == null ? "" : __t) + "</a> </div> </div> </div> </div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/callcenter/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="callcenter"> <div class="crm-tit"> <div class="back-btn"><i class="header-btn-icon el-icon-arrow-left"></i><p class="header-btn-text">' + ((__t = $t("返回")) == null ? "" : __t) + '</p></div> <h2> <span class="tit-txt"> ' + ((__t = name) == null ? "" : __t) + ' <a class="crm-doclink" href="https://help.fxiaoke.com/1a54/8c78/cdc0" target="_blank"></a> </span> </h2> </div> <div class="crm-module-con-callcenter"> <div class="crm-tab"> <a class="item page1" href="#crm/setting/callcenter/=/page1">' + ((__t = $t("配置信息")) == null ? "" : __t) + '</a> <a class="item page2" href="#crm/setting/callcenter/=/page2">' + ((__t = $t("帐号绑定")) == null ? "" : __t) + '</a> <a class="item page3" href="#crm/setting/callcenter/=/page3">' + ((__t = $t("客服设置")) == null ? "" : __t) + '</a> <a class="item page4" href="#crm/setting/callcenter/=/page4" style="display: none">' + ((__t = $t("电销设置")) == null ? "" : __t) + '</a> <a class="item pageCallout" href="#crm/setting/callcenter/=/pageCallout">' + ((__t = $t("外呼设置")) == null ? "" : __t) + '</a> <a class="item pageAdvanced" href="#crm/setting/callcenter/=/pageAdvanced" style="display: none">' + ((__t = $t("高级设置")) == null ? "" : __t) + '</a> </div> <div class="tab-con-callcenter"> <div class="item callcenter-configinfo-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item callcenter-accountbinding-box" style="display:none;"> <div class="dt-term-batch nocaption j-account-header"> </div> <div class="crm-loading"></div> </div> <div class="item callcenter-accessconfig-box" style="display:none;"> <!-- <div class="dt-term-batch nocaption j-account-header"> </div> <div class="crm-loading"></div> --> </div> <div class="item callcenter-telmarketing-box" style="display:none;"> <!-- <div class="dt-term-batch nocaption j-account-header"> </div> <div class="crm-loading"></div> --> </div> <div class="item callcenter-callout-box" style="display:none;"> </div> <div class="item callcenter-advanced-box" style="display:none;"> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/callcenter/util",["crm-modules/common/util"],function(t,r,e){t("crm-modules/common/util");var u={};u.FHHApi=function(r){var e=r.url,t=r.params,s=r.config;return new Promise(function(u,o){FS.util.FHHApi({url:e,data:t||{},success:function(){var r=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},e=function(r){var r=0<arguments.length&&void 0!==r?r:{},e=0,t="";return r.Value&&void 0!==r.Value.isSucc?(e=r.Value.isSucc?0:1,t=r.Value.errMsg):void 0!==r.errorCode?(e=r.errorCode,t=r.errorMessage):r.Result&&(e=r.Result.StatusCode,t=r.Result.FailureMessage),{errorCode:e,errorMessage:t}}(r),t=e.errorCode,e=e.errorMessage;0===t?u(r):o(e)},error:function(r){o(r)}},s)}).catch(function(r){return FS.util.alert(r,$t("提示"),{dangerouslyUseHTMLString:!0}),Promise.reject("error from crm2/modules/setting/callcenter/util.js  ",r)})},u.getSdkHelper=function(){return new Promise(function(e,r){CRM.util.showLoading_tip(),t.async("paas-function/sdk",function(r){CRM.util.hideLoading_tip(),e(r)})})},u.formatDurationTime=function(r){var r=FS.moment.duration(r),e=[];return r.days()&&(e.push(r.days()+$t("天")),r.subtract(r.days(),"days")),r.hours()&&(e.push(r.hours()+$t("小时")),r.subtract(r.hours(),"hours")),r.minutes()&&(e.push(r.minutes()+$t("分钟")),r.subtract(r.minutes(),"minutes")),r.seconds()&&e.push(r.seconds()+$t("秒")),e.join("")},e.exports=u});