define("crm-setting/common/tabs/tabs",["./template/tpl-html"],function(t,e,i){var a=t("./template/tpl-html"),n="active",s="hide",t=CRM.Widget.extend({_cls:"crm-w-tabs",options:{$wrap:null,activeKey:1,tabBarGutter:20,showContent:!1,className:"",options:[{tab:'<a href="#crm/setting/rolemanage/=/page1">'+$t("员工角色分配")+"</a>",content:'<div style="color: red;">1</div>',callback:function(t){}},{tab:$t("员工角色"),content:"<div>2</div>",callback:function(t){}}]},setup:function(){var t=this.options,e=t.activeKey-1,i=(this.$el.html(a(t)),this.get("$wrap").append(this.$el),this.$navbar=this.$(".navbar"),this.$container=this.$(".container-wrapper"),t.className&&this.$el.addClass(t.className),t.activeKey>t.length&&(this.options.activeKey=1),this.$navbar.children(".item").eq(e));i.addClass(n),this.$navbar.children(".label").css({width:i.width(),left:i.position().left}),t.options[e].callback(this.$container.children(".item").eq(e))},events:{"click .navbar .item":"onChange"},onChange:function(t){this.render($(t.currentTarget).index()+1)},render:function(t){t-=1;var e=this.$navbar.children(".item").eq(t),i=this.$container.children(".item").eq(t),a=e.width();e.hasClass(n)||(this.$navbar.children(".label").css({width:a,left:e.position().left}).siblings().removeClass(n),e.addClass(n),i.removeClass(s).siblings().addClass(s),this.options.options[t].callback(i))},destroy:function(){CRM.Widget.prototype.destroy.apply(this,arguments)}});i.exports=t});
define("crm-setting/common/tabs/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-w-tabs"> <div class="navbar"> ';
            _.each(options, function(item, index) {
                __p += ' <span class="item" style="margin-right: ' + ((__t = tabBarGutter) == null ? "" : __t) + 'px">' + ((__t = item.tab) == null ? "" : __t) + "</span> ";
            });
            __p += ' <label class="label"></label> </div> ';
            if (showContent) {
                __p += ' <div class="container-wrapper"> ';
                _.each(options, function(item, index) {
                    __p += ' <div class="item ';
                    if (index != activeKey - 1) {
                        __p += " hide";
                    }
                    __p += " " + ((__t = item.contentClassName) == null ? "" : __t) + '">' + ((__t = item.content) == null ? "" : __t) + "</div> ";
                });
                __p += " </div> ";
            }
            __p += " </div>";
        }
        return __p;
    };
});