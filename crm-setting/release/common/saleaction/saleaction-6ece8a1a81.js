define("crm-setting/common/saleaction/banner/banner",["../template/banner-html"],function(t,e,n){var i=t("../template/banner-html"),t=Backbone.View.extend({options:{maxNum:9,width:500},initialize:function(){this.$el.parents("crm-c-saleaction")[0]||this.$el.addClass("crm-c-saleaction")},events:{"click .go-back":"backHandle","click .go-ahead":"aheadHandle","click .circle":"circleClickHandle","mouseenter .circle span":"hoverHandle","mouseleave .circle span":"leaveHandle"},show:function(t){this.bannerData=t;var e,n=this.doIndex(t);this.$el.html(i({info:this.caculate(t.length,n),data:t})),(e=this.$(".current"))[0]?e.click():99!=t[t.length-1].active&&(t[0].active?this.$(".circle:first"):this.$(".circle:last")).click(),this.setCount(t.length,n)},doIndex:function(t){var n,i=!1;return $.each(t,function(t,e){if(n=t,1===e.active)return!(i=!0)}),i?n:t[0].active?0:t.length-1},setCount:function(t,e){var n,i=this.options.maxNum;i<t?(n=Math.floor(i/2),this.count=n<e?t-e-(n+1)<0?0:t-e-(n+1):t-i):this.count=0},caculate:function(t,e){var n,i,a,s,r,c,o=t-(e+1),h=this.options.maxNum;return h<t?(c=1,i=0,(n=((s=this.options.width-(this.options.width-60-36*h)%h)-60-36*h)/h)%2&&(n--,s-=h),i-=n,(r=Math.floor(h/2))<e&&(i=r<o?-(e-r)*(36+n)-n/2:-(e+o-(h-1))*(36+n)),r=0,a=s-60):(c=2,i="0",a=(n=((s=this.options.width-(this.options.width-36*t)%(t-1))-36*t)/(t-1))+s,r=-n),{status:c,margLeft:(this.margLeft=n)+"px",width:s+"px",wrapLeft:r+"px",wrapWidth:a+"px",offsetLeft:i+"px",solidWidth:(n+36)*e+n+"px"}},setSolidLine:function(){},getMargleft:function(){var t=this.bannerData.length,e=this.options.maxNum,n=this.margLeft,i=this.count;return t-e===i?n:(t-e-i)*(36+n)+(this.count?n/2:0)},backHandle:function(){this.count+this.options.maxNum+1>this.bannerData.length||(this.count++,this.$(".aid-wrap").css("margin-left",-this.getMargleft()+"px"))},aheadHandle:function(){this.count<1||(this.count--,this.$(".aid-wrap").css("margin-left",-this.getMargleft()+"px"))},circleClickHandle:function(t){t=$(t.currentTarget);this.$(".current").removeClass("current"),t.addClass("current"),this.trigger("click",this.bannerData[t.index()],t)},isFirst:function(t){t=t.index();return!t||this.bannerData.length-this.count-this.options.maxNum===t},isLast:function(t){var t=t.index(),e=this.bannerData.length;return t+1===e||this.count===e-t-1},hoverHandle:function(t){var e,n,i=$(t.currentTarget).parent(),a=this.$(".sale-tip").show(),s=a.find("p"),r=this.bannerData[i.index()];s.text($t("第{{order}}阶段：{{{{-name}}}}",{order:r.order,name:r.name})),r=this.isFirst(i)?(e=!0,"first"):this.isLast(i)?(n=!0,"last"):"middle",s=s.width(),a.attr("class","sale-tip "+r).width(s+16).css("left",i.offset().left-this.$(".sale-con").offset().left+(e?4:n?14-s:10-s/2)),this.trigger("mouseenter",this.bannerData[$(t.currentTarget).parent().index()])},leaveHandle:function(t){this.$(".sale-tip").hide(),this.trigger("mouseleave",this.bannerData[$(t.currentTarget).parent().index()])},destroy:function(){var t=this;t.stopListening(),t.undelegateEvents(),t.$el.empty(),t.$el=t.el=t.options=t.events=t.bannerData=null}});n.exports=t});
define("crm-setting/common/saleaction/detail/detail",["../template/tpl-html","../template/nosale-html","../template/noexist-html","../template/feedback-html","./fielddetail","crm-modules/common/util","../banner/banner","../model/saleaction","../view/view"],function(o,e,t){var n=o("../template/tpl-html"),i=o("../template/nosale-html"),l=(o("../template/noexist-html"),o("../template/feedback-html")),a=o("./fielddetail"),s=o("crm-modules/common/util"),r=o("../banner/banner"),c=o("../model/saleaction"),d=o("../view/view"),u=a.extend({options:{detailType:9,detailFrom:s.DETAIL_FROM_LIST,from:1,isBelongOpportunity:!1,saleType:1},events:{"click .j-fb-startSaleAfter":"_startHandle","click .j-fb-edit":"_editHandle","click .j-fb-complete":"_completeHandle","click .j-fb-next":"_nextHandle","click .j-fb-revoke":"_reovkeHandle","click .j-fb-confirm":"_confirmHandle","click .j-fb-reject":"_rejectHandle","click .j-sale-del":"_delHandle","click .j-set-oppstatus":"_setOppStatusHandle"},initialize:function(){this.collect=new c,this.listenTo(this.collect,{ready:this._readyHandle,success:this._success,noSale:this._noSale,delSuc:this._delSale,startAfterSuc:this._startAfterSuc,handleSuc:this._handleSuc,singlefetch:this._fbDetail,singlefetcherror:this._fbError,datachange:this._dataChange}),a.prototype.initialize.apply(this,arguments)},fetchAttachList:function(){},getImgDownToken:function(e,t){var n=this.currentModel;~e.fieldname.indexOf("__")?(e.fieldname=e.fieldname.replace("__",""),e.id=n.get("customerID"),e.type=2):(e.id=n.get("attachID"),e.type=7),a.prototype.getImgDownToken.apply(this,[e,t])},render:function(e){this.oppId=e,this.collect.fetch(e,this.options.saleType)},refresh:function(){this.render(this.oppId),this.trigger("refresh")},_success:function(e){this.render(this.oppId),this.trigger("refresh",e)},_dataChange:function(){this.trigger("datachange")},changeStage:function(){var e=_.map(this.collect.toJSON(),function(e){return{name:e.name,value:e.stageID}});e[0]&&this._doOppAction(e)},switchSale:function(e){var t=this.options.saleType;e&&t!==e&&(this.options.saleType=e,this.refresh())},_doOppAction:function(e,t){var n=this,i=n.collect.getCurrent(),a=n.collect.oppInfo,l={oList:e||[],defaultValue:i.get("stageID"),oppId:i.get("oppId"),oppName:i.get("oppName"),needAdd:i.get("isAllowTradeIfWin"),isConfirm:i.get("isConfirm"),isHasLead:!!~i.get("leaderID"),customerName:a.CustomerName,customerId:a.CustomerID,edealtime:a.ExpectedDealTime};t&&(l.title=t),o.async("crm-modules/action/opportunityobj/opportunityobj",function(e){n._oppAction||(n._oppAction=new e,n._oppAction.on("refresh",function(){n._handleSuc()})),n._oppAction.modifyStage(l)})},_readyHandle:function(){this.$el.html(n({from:this.options.from||2,type:this.options.saleType,oppStatus:this.collect.oppInfo.Status})),this._renderBanner(),this.trigger("list:ready",this.collect.toJSON())},_renderBanner:function(){var e,t,n,i=this;i.banner&&(i.banner.destroy(),i.banner=null),this.collect.length&&(i.banner=new r({el:i.$(".banner-wrap"),from:this.options.from,maxNum:this.options.maxNum||6,width:i.$(".banner-wrap").width()}),i.banner.on("click",i._bannerClickHandle,i),e=i.collect.toJSON(),t=i.collect.oppInfo.Status,(n=1===i.options.saleType&&["","","s-win","s-invalid","s-lose"][t])&&e.push({active:99,order:["","",$t("赢单"),$t("无效"),$t("输单")][t],stageID:"",className:n}),i.banner.show(e))},_bannerClickHandle:function(e,t){e=this.collect.get(e.stageID);this._loadingDetail(),e&&(this.currentModel=e).fetch()},_startAfterSuc:function(){this.trigger("startAfterSuc")},_handleSuc:function(){this.trigger("handleSuc"),this.refresh()},_delSale:function(){this.$el.html(i({type:this.options.saleType}))},_noSale:function(){this.$el.html(i({type:this.options.saleType}))},_setTipInfo:function(e){e&&this.$(".j-item-desc").text(e)},_loadingDetail:function(){this.$(".con-wrap").html('<div class="crm-loading "></div>')},_fbDetail:function(n){var i=this,a=n.toJSON();i.options.dataId=a.stageID,n.fetchAttach(function(e){i.attachList=e;var t,e=i._parse("sale",{UserDefinedFields:a.fieldList,UserDefineFieldDatas:a.fieldData});i.collect&&(t={},_.each(e.Custom,function(e){t[e.FieldName]=e.FieldValue}),_.each(e.PresetArr,function(e){t[e.FieldName]=e.FieldValue}),e.isLast=i.collect.isLast(n),e.isFirst=i.collect.isFirst(n),e.btn=i._doBtn(a),e.valObj=t,i.$(".con-wrap").html(l(_.extend(e,a))),i._setTipInfo(a.targetInfo))})},_fbError:function(){this.$(".con-wrap").html('<div class="s-nodata">'+$t("暂时无法获取到阶段反馈数据")+","+$t("请稍后重试")+"</div>")},_startHandle:function(){this.collect.startSaleAfter(this.oppId,this.$(".j-fb-startSaleAfter"))},_nextHandle:function(e){var t,n=this,i=n.currentModel,a=n.collect.next(i),l=i.get("leaderID");i.validate()?i.get("isConfirm")&&-1!==i.get("leaderID")?l?i.toUpperNext(a.get("stageID"),$(e.currentTarget)):n._setLeader(function(){i.toUpperNext(a.get("stageID"),$(e.currentTarget))}):i.next(a,$(e.currentTarget)):t=s.confirm($t("你有未填写的阶段反馈请完善后再进入下一阶段"),"",function(){t.hide(),t=null,n._editHandle()},{btnLabel:{confirm:$t("去完善")}})},_reovkeHandle:function(){this.currentModel&&this.currentModel.revoke(this.$(".j-fb-revoke"))},_completeHandle:function(e){var t,n=this,i=n.currentModel,a=i.get("leaderID");if(i.validate())return 2===i.get("saleType")?i.get("isConfirm")&&-1!==i.get("leaderID")?void(a?i.setStatus(1,$(e.currentTarget)):n._setLeader(function(){i.setStatus(1,$(e.currentTarget))})):void i.completeAfter($(e.currentTarget)):void n._doOppAction([],$t("商机状态"));t=s.confirm($t("你有未填写的阶段反馈请完善后才能完成销售流程"),"",function(){t.hide(),n._editHandle()},{btnLabel:{confirm:$t("去完善")}})},_delHandle:function(){var e=this,t=s.confirm($t("确定要删除该销售流程"),$t("删除"),function(){t.hide(),e.collect.del()})},_doBtn:function(e){var t={};return _.each(e.functionNo,function(e){t[e.FunctionNo]=!!e.Right}),t},_editHandle:function(){var t=this,n=_.pick(this.currentModel.attributes,["stageID","oppId","saleactionID","fieldList","fieldData"]);n.isValid=this.currentModel.isValid(),o.async("crm-modules/common/field/field",function(e){t.editHandle||(t.editHandle=new e.Edit({className:"crm-c-field-edit crm-c-field-one",formView:d,title:$t("填写阶段反馈")}),t.editHandle.on("refresh",function(){t._loadingDetail(),t.currentModel.eidtSuccess()})),t.editHandle.show(n)})},_confirmHandle:function(e){this.currentModel.setStatus(2,this.$(e.currentTarget))},_rejectHandle:function(){var t=this.currentModel,e=t.attributes,n=['<div class="crm-g-form">','<div class="fm-item">','<label style="width:80px;padding-right:0" class="fm-lb">',"<span>"+$t("拒绝原因")+"</span>","</label>",'<textarea text-type="textarea" maxlength="20" class="b-g-ipt fm-ipt normal-ipt" placeholder='+$t("请填写拒绝的原因（20字以内）")+' data-isnotnull="false" style="width:330px;"></textarea>',"</div>","</div>"].join(""),i=s.confirm(n,$t("驳回"),function(){s.FHHApi({url:"/EM1HCRM/SaleAction/SetSaleActionStageUpperStatus",data:{OpportunityID:e.oppId,SaleActionStageID:e.stageID,RejectReason:i.element.find('[text-type="textarea"]').val()||"",Status:3},success:function(e){0==e.Result.StatusCode?(s.remind(1,$t("驳回成功")),t.trigger("success","reject")):s.alert(e.Result.FailureMessage),i.hide()}},{submitSelector:$(".b-g-btn",i.element),errorAlertModel:1})})},_setLeader:function(n){var i=this;o.async("crm-modules/components/setleader/setleader",function(e){i.leader||(i.leader=new e,i.leader.on("setLeader.success",function(t){t&&_.each(i.collect,function(e){e.set("leaderID",t)}),n&&n.call(i,t)})),i.leader.show()})},destroy:function(){var t=this;_.each(["leader","banner","editHandle","_oppAction"],function(e){t[e]&&t[e].destroy&&t[e].destroy(),t[e]=null}),this.collect.reset(),this.collect=null,this.currentModel=null,a.prototype.destroy.call(this)}});t.exports=u});
define("crm-setting/common/saleaction/detail/fielddetail",[],function(s,e,t){var l=CRM.util,n=Backbone.View.extend({options:{isDownAllImg:!0},events:{"click .field-img div":"_previewImgHandle","click .j-attach-preview":"_previewAttachHandle"},initialize:function(){var t=this,a=(t.events=_.extend({},n.prototype.events,t.events),t.options=_.extend({},n.prototype.options,t.options),t.show),i=(t.show=function(e){t.options.dataId=e,_.isFunction(a)&&a.apply(t,Array.prototype.slice.call(arguments,0))},t.renderPage);t.renderPage=function(){var e=Array.prototype.slice.call(arguments,0);t.fetchAttachList("",function(){_.isFunction(i)&&i.apply(t,e)})},t.imgList={},t.options.fieldImgWidth=t.options.fieldImgWidth||(this.$el.width()<600?410:510),Backbone.View.prototype.initialize.call(t)},show:function(e){},renderPage:function(){},setContent:function(e){this.$el.html(e)},fetchAttachList:function(e,t){var a=this;l.FHHApi({url:"/EM1HCRM/Attach/GetAttachList",data:{DataID:e||a.options.dataId,PageSize:200,PageNumber:1,Source:a.options.detailType||1},success:function(e){0===e.Result.StatusCode&&(a.attachList=e.Value.AttachInfoList),t&&t.call(a,e.Value.AttachInfoList||[])}})},fetchAttachSnapshotList:function(e,t){var a=this;l.FHHApi({url:"/EM1HCRM/Attach/GetAttachSnapshotList",data:{ModifyRecordID:e},success:function(e){0===e.Result.StatusCode&&(a.attachList=e.Value.AttachInfoList),t&&t.call(a)}})},getAttachFieldData:function(t){var e=_.filter(this.attachList,function(e){return e.FieldName===t.FieldName}),a=_.pick(t,["FieldName","FieldProperty","FieldType"]);return a.FieldValue={},a.FieldValue.Value="",a.FieldValue.Values=[],a.FieldValue.UploadAttachs=e||[],a},getImgDownToken:function(e,t){e.id&&l.FHHApi({url:"/EM1HCRM/DownloadFile/BatchDownloadImage",data:{OwnerType:e.type,DataID:e.id,FieldName:e.fieldname},success:function(e){0===e.Result.StatusCode?t&&t(e.Value.TokenID):t&&t()},error:function(){t&&t()}})},getImgDownUrlByToken:function(e){return FS.BASE_PATH+"/FSC/EM/File/DownloadByToken?FileToken="+e},_previewImgHandle:function(e){var a=this,t=$(e.currentTarget),i=t.parent().data(),n=t.index();return s.async("base-imagepreview",function(e){new e({data:_.map(a.imgList[i.id],function(e,t){var a=l.getImgPath(e);return{smallUrl:l.getImgPath(e,1),bigUrl:a,originUrl:a,originDownUrl:l.getFscLink(e,e,!0)}}),activeIndex:n,mainType:"crm",navType:"crm",customOpts:{batchDownloadHandle:function(t){var e={id:a.options.dataId,fieldname:i.fieldname,type:i.type};a.getImgDownToken(e,function(e){e&&(e=a.getImgDownUrlByToken(e),t)&&t(e)})}}}).show()}),e.preventDefault(),!1},_previewAttachHandle:function(e){var t=$(e.target).data();return s.async("base-modules/file-preview/index",function(e){e.preview({fileId:t.id,fileName:t.name,filePath:t.path})}),e.preventDefault(),!1},_parse:function(e,s,l){var r=this,c={},o=[],d=[],u=this._parseFieldData(s.UserDefineFieldDatas);return _.each(s.UserDefinedFields,function(e){var t,a=e.FieldName,i=u[a],n=e.FieldType;e.IsVisible&&(17!==n||l||(t=r.getAttachFieldData(e),i?i.FieldValue=t.FieldValue:s.UserDefineFieldDatas.push(t)),(e=_.extend({},e)).FieldCaption=_.escape(e.FieldCaption),1!==n&&(e.FieldValue=i||17===n&&!l?(r["parse_"+n]||r.parse_2).apply(r,[e,i&&i.FieldValue]):"--",e.IsVisible)&&(1===e.FieldProperty?c[a]=e.FieldValue:o.push(e)),e.IsVisible)&&1==e.FieldProperty&&(c[a]=e.FieldValue,16==n&&(e.FieldID=i?i.FieldValue.Value:"",e.FieldValue=i?_.escape(i.FieldValue.Names.join(",")):""),d.push(e))}),{Sys:s[e]||{},Preset:c,PresetArr:d,Custom:o}},_parseFieldData:function(e){var t={};return _.each(e,function(e){t[e.FieldName]=e}),t},parse_2:function(e,t){t=t.Value;return""===t||void 0===t?"--":_.escape(t)},parse_3:function(e,t){return this.parse_2(e,t)},parse_4:function(e,t){return this.parse_2(e,t)},parse_5:function(e,t){return this.parse_2(e,t)},parse_6:function(e,t){return t.Value||"--"},parse_7:function(e,t){t=t.Value;return t&&""!==t&&"946656000000"!=t&&"631123200000"!=t?FS.moment.unix(t/1e3).format("YYYY-MM-DD"):"--"},parse_8:function(e,t){t=t.Value,e=_.findWhere(e.EnumDetails,{ItemCode:t});return e?_.escape(e.ItemName):"--"},parse_9:function(e,t){var a=t.Values,i=[];return _.each(e.EnumDetails,function(e){_.contains(a,e.ItemCode)&&i.push(e.ItemName)}),_.escape(i.join(",")||"--")},parse_10:function(e,t){var i,a=(this.options&&this.options.fieldImgWidth||0)+"px",n=['<div data-id="'+e.UserDefinedFieldID+'" data-type="'+e.OwnerType+'" data-fieldname="'+e.FieldName+'" style="width:'+a+'" class="field-img fn-clear">'];return t.Values[0]&&this.imgList?(i=this.imgList[e.UserDefinedFieldID]=[],_.each(t.Values,function(e,t){var a=l.getImgPath(e,1);i.push(e),t<10&&n.push('<div><img src="'+a+'"/></div>')}),10<(a=t.Values.length)&&(n[10]="<div>"+$t("更多")+(a-9)+$t("张")+"</div>"),n.push("</div>"),n.join("")):"--"},parse_11:function(e,t){return _.escape(t.Value.split("#%$")[2]||"--")},parse_12:function(e,t){return t.Value.replace("0000-00-00","--")},parse_13:function(e,t){return""!==t.Value?t.Value?$t("是"):$t("否"):"--"},parse_14:function(e,i){var n,s=[];return i.Names&&i.Names[0]?_.escape(i.Names[0]):(function t(e){var a;e&&e[0]?(a=_.find(e,function(e){return t(e.Children&&e.Children[0]?e.Children:e),n&&e}))&&s.push(a.ItemName):n=e.ItemCode===i.Value}(e.EnumDetails||[]),_.escape(s.reverse().join("/")))},parse_15:function(e,t){return t.Value},parse_16:function(e,t){return""},parse_17:function(e,t){e=_.where(this.attachList,{FieldName:e.FieldName});return e&&e[0]?this._attachTpl({attachs:e}):"--"},parse_18:function(e,t){t=t.Value;return""===t||void 0===t?"--":_.escape(t)},parse_19:function(e,t){return this.parse_2(e,t)},parse_20:function(e,t){return this.parse_2(e,t)},_attachTpl:_.template(['<div class="crm-table">',"<table>","<thead>","<tr>",'<th class="name"><div class="tb-cell">'+$t("附件名称")+"</div></th>",'<th style="width:90px" class="size"><div class="tb-cell">'+$t("文件大小")+"</div></th>",'<th style="width:90px" class="handle"><div class="tb-cell">'+$t("操作")+"</div></th>","</tr>","</thead>",'<tbody class="tbody">',"##_.each(attachs,function(annex){##","##var __downUrl = CRM.util.getFscLink(annex.AttachPath, annex.AttachName, true);##","<tr>",'<td class="name"><div class="tb-cell"><span style="margin-right:10px;float:left" class="{{CRM.util.getFileIco(annex.AttachName)}}"></span>{{{{-annex.AttachName}}}}</div></td>','<td class="size"><div class="tb-cell">{{CRM.util.getFileSize(annex.AttachSize)}}</div></td>','<td class="handle">','<div class="tb-cell">','##if(annex.CanPreview){## <a style="margin-right:10px" class="j-attach-preview" data-id="{{annex.AttachID}}" data-path="{{annex.AttachPath}}" data-name="{{annex.AttachName}}" data-url="{{__downUrl}}" href="javascript:;">'+$t("预览")+"</a> ## } ##",'<a href="{{__downUrl}}">'+$t("下载")+"</a>","</div>","</td>","</tr>","##})##","</tbody>","</table>","</div>"].join("")),destroy:function(){var e=this;e.stopListening(),e.undelegateEvents(),e.$el.empty(),e.imgPreview&&e.imgPreview.destroy(),e.previewAttach&&e.previewAttach.destroy(),e.imgList=e.attachList=null,e.show=e.renderPage=null,e.previewAttach=e.imgPreview=null,e.$el=e.el=e.options=e.events=null}});t.exports=n});
define("crm-setting/common/saleaction/model/saleaction",[],function(t,e,a){function o(t){var e=t.TargetSaleActionStageInfo,a=t.SaleActionConfirmInfo;return{stageID:t.SaleActionStageID,name:t.Name,description:t.Description,stageRules:t.StageRules,order:t.StageOrder,saleactionID:t.SaleActionID,leaderID:t.LeaderID,customerId:t.Opportunity&&t.Opportunity.CustomerID,fieldList:i(t.UserDefinedFields,t.CustomerFields,t.OppoFields),fieldData:i(t.UserDefineFieldDatas,t.CustomerFieldDatas,t.OppoFieldDatas),isTimeRemind:!!t.IsTimeoutRemind,isOverTime:!!t.IsOverTime,isConfirm:!(!t.IsLeaderConfirm||-1===t.LeaderID),status:t.ConfirmStatus,stageStatus:t.StageStatus||1,functionNo:t.FunctionRights,attachID:t.SaleActionStageUDefID,remindDays:t.RemainDays,saleType:t.Type,weight:t.Weight,targetInfo:a&&a.TargetOpportunityStatus&&1===t.ConfirmStatus?$t("商机即将{{result}},",{result:["","",$t("赢单"),$t("无效"),$t("输单，输单原因：{{lose}}",{lose:a.LoseTypeName})][a.TargetOpportunityStatus]}):e&&e.SaleActionStageID&&1===t.ConfirmStatus?$t('即将进入  "第{{order}}阶段:{{name}}"',{order:e.StageOrder,name:e.Name}):""};function i(t,e,a){return _.each(e,function(t){t.realFieldName=t.FieldName,t.__type="customer",t.FieldName="__"+t.FieldName}),_.each(a,function(t){t.realFieldName=t.FieldName,t.__type="opportunity",t.FieldName="___"+t.FieldName}),[].concat(t||[],e||[],a||[])}}var n=CRM.util,s=n.json,i=Backbone.Model.extend({defaults:function(){return{isPosting:!1,oppId:"",stageID:"",name:"",description:"",order:"",saleactionID:"",stageStatus:"",leaderID:"",active:0,weight:0,fieldList:[],fieldData:[],isComplete:!1,stageRules:[],isTimeRemind:!1,isOverTime:!1,isStartAfterSale:!1,isConfirm:!1,remainDays:0,isShowTipByContact:!1,status:0,functionNo:[],attachID:null,isAllowTradeIfWin:!1,opportunityStatus:0,saleActionName:"",loseTypeName:"",saleType:1,isCanStartAfter:!1,isCanChangeStage:!1}},idAttribute:"stageID",submit:function(t,e){var a=this,i=this.attributes;n.FHHApi({url:"/EM1HCRM/SaleAction/AddSaleActionStage",data:{SaleActionID:i.saleactionID,SaleActionStageID:i.stageID,OpportunityID:i.oppId,UDFieldDatas:t.UDFieldDatas,NeedCheckValid:!1,CustomerFieldDatas:t.CustomerFieldDatas,OppoFieldDatas:t.OppoFieldDatas},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("操作成功！")),a.trigger("datachange"),1===i.active?a.trigger("success"):a.fetch(!0)):n.alert(t.Result.FailureMessage)}},{submitSelector:e,errorAlertModel:1})},isValid:function(){var t=this.get("status"),e=this.get("isConfirm");return!this.get("active")||1===t&&e||2===t},eidtSuccess:function(){this.trigger("datachange"),1===this.get("active")?this.trigger("success"):this.fetch(!0)},submitIllegal:function(t,e){var a=this,i=a.get("status"),r=a.get("isConfirm");!a.get("active")||1===i&&r||2===i||a.submit(t,e)},fetch:function(t){var r=this,s=this.attributes,o=s.active;!t&&s.fieldList.length?r.trigger("singlefetch",r):(r.fetchAjax&&(r.fetchAjax.abort(),r.fetchAjax=null),r.fetchAjax=n.FHHApi({url:"/EM1HCRM/SaleAction/GetSaleActionStageInfoByID",data:{OpportunityID:s.oppId,SaleActionStageID:s.stageID},success:function(a){var t,e,i;0==a.Result.StatusCode?(a.Value.active=o,t=a.Value.CustomerFields,e=a.Value.OppoFields,i=[],t[0]&&i.push(t),e[0]&&i.push(e),n.handleFieldPrivilege(i,function(t,e){r.set(r.parse(_.extend({active:a.Value.IsCompleted?0:o,oppId:s.oppId},a.Value,a.Value.SaleActionStageInfo))),e?r.trigger("singlefetch",r):r.trigger("singlefetcherror")})):r.trigger("singlefetcherror")}},{errorAlertModel:1}))},fetchAttach:function(i,r){var s=this;s.fetchSaleActionAttach(function(a){s.fetchCustomerAttach(function(e){_.each(e,function(t){t.FieldName="__"+t.FieldName}),s.fetchOpportunityAttach(function(t){_.each(t,function(t){t.FieldName="___"+t.FieldName}),i&&i.call(r||s,[].concat(a,e,t))})})})},_fetchAttach:function(t,e,a){var i=this;t?n.FHHApi({url:"/EM1HCRM/Attach/GetAttachList",data:{DataID:t,PageSize:200,PageNumber:1,Source:e},success:function(t){a&&a.call(i,0===t.Result.StatusCode?t.Value.AttachInfoList:[])},error:function(){a&&a.call(i,[])}}):a&&a.call(i,[])},fetchSaleActionAttach:function(t){_.find(this.get("fieldList"),function(t){return 17===t.FieldType&&!t.__type})?this._fetchAttach(this.get("attachID"),9,t):t.call(this,[])},fetchCustomerAttach:function(t){_.find(this.get("fieldList"),function(t){return 17===t.FieldType&&"customer"===t.__type})?this._fetchAttach(this.get("customerId"),1,t):t.call(this,[])},fetchOpportunityAttach:function(t){_.find(this.get("fieldList"),function(t){return 17===t.FieldType&&"opportunity"===t.__type})?this._fetchAttach(this.get("oppId"),7,t):t.call(this,[])},parse:o,unmergeFieldData:function(t){},unmergeFieldList:function(){},validate:function(){var i=[],r=this.get("fieldData");return _.each(this.get("fieldList"),function(t){var e=_.isString(t.ExtendProp)?s.parse(t.ExtendProp):t.ExtendProp||{},a=_.findWhere(r,{FieldName:t.FieldName});!e.Modify_IsVisible||!t.IsNotNull||a&&(a.FieldValue.Value||a.FieldValue.Values&&a.FieldValue.Values[0]||a.FieldValue.UploadAttachs&&a.FieldValue.UploadAttachs[0])||i.push(t)}),!i[0]||(this.trigger("validate.error",i),!1)},next:function(t,e){var a,i;t&&t.get("stageID")&&(i=(a=this).attributes,a.validate())&&(i.isPosting=!0,n.FHHApi({url:"/EM1HCRM/SaleAction/MoveNextSaleActionStage",data:{OpportunityID:i.oppId,CurrentSaleActionStageID:i.stageID},success:function(t){i.isPosting=!1,0==t.Result.StatusCode?(n.remind(1,$t("进入下一阶段成功")),a.trigger("success")):n.alert(t.Result.FailureMessage||"server error")},error:function(){i.isPosting=!1}},{submitSelector:e,errorAlertModel:1}))},completeAfter:function(t){var e=this,a=this.attributes;a.isPosting=!0,n.FHHApi({url:"/EM1HCRM/SaleAction/MoveNextSaleActionStage",data:{OpportunityID:a.oppId,CurrentSaleActionStageID:a.stageID,IsCompleted:!0},success:function(t){a.isPosting=!1,0===t.Result.StatusCode?(n.remind(1,$t("已完成")),e.trigger("success")):n.alert(t.Result.FailureMessage||"server error")},error:function(){a.isPosting=!1}},{submitSelector:t,errorAlertModel:1})},toUpperNext:function(t,e){var a=this;n.FHHApi({url:"/EM1HCRM/SaleAction/SetSaleActionStageUpperStatus",data:{OpportunityID:a.get("oppId"),SaleActionStageID:a.get("stageID"),Status:1,TargetSaleActionStageID:t},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("操作成功")),a.trigger("success")):n.alert(t.Result.FailureMessage||"server error")}},{submitSelector:e,errorAlertModel:1})},win:function(){var e=this;n.FHHApi({url:"/EM1HCRM/SaleAction/MoveNextSaleActionStage",data:{OpportunityID:e.get("oppId"),CurrentSaleActionStageID:e.get("stageID"),NextSaleActionStageID:e.get("stageID"),OppoStatus:2,IsCompleted:!0},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("操作成功")),e.trigger("handleSuc")):n.alert(t.Result.FailureMessage||"server error")}},{errorAlertModel:1})},toUpperWin:function(t){var e=this;n.FHHApi({url:"/EM1HCRM/SaleAction/SetSaleActionStageUpperStatus",data:{OpportunityID:e.get("oppId"),SaleActionStageID:e.get("stageID"),Status:1,TargetSaleActionStageID:e.get("stageID"),OpportunityStatus:2,DataID:t||void 0},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("操作成功")),e.trigger("success")):n.alert(t.Result.FailureMessage||"server error")}},{errorAlertModel:1})},novalid:function(){var e=this;n.FHHApi({url:"/EM1HCRM/SaleAction/MoveNextSaleActionStage",data:{OpportunityID:e.get("oppId"),CurrentSaleActionStageID:e.get("stageID"),NextSaleActionStageID:e.get("stageID"),OppoStatus:3,IsCompleted:!0},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("操作成功")),e.trigger("handleSuc")):n.alert(t.Result.FailureMessage||"server error")}},{errorAlertModel:1})},toUpperNovalid:function(){var e=this;n.FHHApi({url:"/EM1HCRM/SaleAction/SetSaleActionStageUpperStatus",data:{OpportunityID:e.get("oppId"),SaleActionStageID:e.get("stageID"),Status:1,TargetSaleActionStageID:e.get("stageID"),OpportunityStatus:3},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("操作成功")),e.trigger("success")):n.alert(t.Result.FailureMessage||"server error")}},{errorAlertModel:1})},revoke:function(t){var e=this;n.FHHApi({url:"/EM1HCRM/SaleAction/BackSaleActionConfirmStatus",data:{OpportunityID:this.get("oppId"),SaleActionStageID:this.get("stageID")},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("撤回成功")),e.trigger("success","revoke")):(n.alert(t.Result.FailureMessage||"server error"),e.trigger("success"))}},{submitSelector:t,errorAlertModel:1})},checkStage:function(t){var e=this;n.FHHApi({url:"/EM1HCRM/SaleAction/CheckChangeStage",data:{OpportunityID:e.get("oppId"),CurrentSaleActionStageID:e.get("stageID"),NextSaleActionStageID:t},success:function(t){0===t.Result.StatusCode?e.trigger("checkSuc",e):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},setStatus:function(e,t){var a=this,i=a.attributes;i.isPosting||(i.isPosting=!0,n.FHHApi({url:"/EM1HCRM/SaleAction/SetSaleActionStageUpperStatus",data:{OpportunityID:i.oppId,SaleActionStageID:i.stageID,Status:e},success:function(t){i.isPosting=!1,0==t.Result.StatusCode?(n.remind(1,["",$t("提交成功")+","+$t("待上级确认"),$t("确认成功"),$t("驳回成功")][e]),a.trigger("success",2==e?"confirm":"")):n.alert(t.Result.FailureMessage)},error:function(){i.isPosting=!1}},{submitSelector:t,errorAlertModel:1}))}}),i=Backbone.Collection.extend({model:i,fetch:function(a,t){var i=this;i.oppId=a,i.reset(),n.FHHApi({url:"/EM1HCRM/SaleAction/GetSaleActionStageListByID",data:{OpportunityID:a,Type:t},success:function(t){var e;if(0==t.Result.StatusCode)return(e=t.Value).OpportunityID=a,i.oppInfo=e.Opportunity,i.tradeDesc=e.TradeRuleDesc,i.currentStageId=e.SaleActionStageID,i.isStartAfterSale=e.IsStartAfterSale,i.isAllowTradeIfWin=e.IsAllowTradeIfWin,e.SaleActionStageInfoList[0]?(i.add(i.parse(e),{silent:!0}),void i.trigger("ready",i.toJSON())):void i.trigger("noSale");6==t.Result.StatusCode?i.trigger("noSale"):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},startSaleAfter:function(t,e){var a;t&&(a=this,n.FHHApi({url:"/EM1HCRM/SaleAction/StartAfterSaleAction",data:{OpportunityID:t},success:function(t){0===t.Result.StatusCode?(a.isStartAfterSale=!0,a.fetch(a.oppId,2),a.trigger("startAfterSuc")):n.alert(t.Result.FailureMessage)}},{submitSelector:e,errorAlertModel:1}))},del:function(){var e=this,t=this.at(0),t=t&&t.get("oppId");t&&n.FHHApi({url:"/EM1HCRM/SaleAction/ChangeSaleAction",data:{OpportunityID:t,SaleActionID:""},success:function(t){0===t.Result.StatusCode?(n.remind(1,$t("删除成功")),e.reset(),e.trigger("delSuc")):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},isFirst:function(t){return this.at(0)===t},isLast:function(t){return this.at(this.length-1)===t},next:function(t){t=this.indexOf(t);return this.at(t+1)},getCurrent:function(){return this.get(this.currentStageId)},parse:function(a){var i=0,t=_.findWhere(a.FunctionRights,{FunctionNo:13005}),e=_.findWhere(a.FunctionRights,{FunctionNo:13007}),r=a.Opportunity,s={customer:a.Customer||{},customerId:r.CustomerID,customerName:r.CustomerName,expectedDealTime:r.ExpectedDealTime,expectedSalesAmount:r.ExpectedSalesAmount,oppName:r.Name,oppId:r.OpportunityID,isAllowTradeIfWin:a.IsAllowTradeIfWin,isStartAfterSale:a.IsStartAfterSale,loseTypeName:a.LoseTypeName,opportunityStatus:r.Status,saleActionName:a.SaleActionName,isComplete:2===a.SaleActionStatus,isCanStartAfter:t&&!!t.Right,isCanChangeStage:e&&e.Right};return _.map(a.SaleActionStageInfoList,function(t){var e=_.extend({},o(t),s);return 2!==t.Type||e.isStartAfterSale?(e.active=i,t.SaleActionStageID===a.SaleActionStageID&&(e.active=2===t.StageStatus?0:1,i=2)):e.active=2,e})}});a.exports=i});
define("crm-setting/common/saleaction/saleaction",["./detail/detail","./banner/banner"],function(n,e,a){var t=n("./detail/detail"),n=n("./banner/banner");a.exports={Detail:t,Banner:n}});
define("crm-setting/common/saleaction/selectaction/selectaction",["crm-modules/common/util","crm-widget/select/select","crm-widget/dialog/dialog"],function(t,e,s){var l=t("crm-modules/common/util"),i=t("crm-widget/select/select"),c=t("crm-widget/dialog/dialog").extend({attrs:{content:'<div class="crm-g-form"><div  class="fm-item"><label style="width:40px" class="fm-lb">'+$t("更换为")+'</label><div style="float:left;width:320px" class="select-wrap"></div></div></div>',title:$t("更换销售流程"),width:500,showBtns:!0,showScroll:!1,zIndex:600},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"submit"},show:function(t,e){this.cusid=e;e=c.superclass.show.call(this);return this.getAction(t,this.initSelect),e},getAction:function(e,s){var i=this;l.FHHApi({url:"/EM1HCRM/SaleAction/GetAllSimpleSaleActionList",success:function(t){0===t.Result.StatusCode&&s&&s.call(i,t.Value.SaleActionSimpleInfoList,e)}})},initSelect:function(t,e){var s=[{value:0,name:$t("请选择")}];_.each(t,function(t){t.SaleActionID!==e&&s.push({value:t.SaleActionID,name:_.escape(t.Name)})}),this.sb=new i({$wrap:this.$(".select-wrap"),zIndex:1002,options:s,defaultValue:0})},submit:function(t){var e,s=this.sb.getValue(),i=this;s&&(e=l.confirm($t("确定更换该客户的销售流程吗"),$t("更换"),function(){l.FHHApi({url:"/EM1HCRM/SaleAction/ChangeSaleAction",data:{CustomerID:i.cusid,SaleActionID:s},success:function(t){e.hide(),0===t.Result.StatusCode?(l.remind(1,$t("更换成功")),i.trigger("success",i.cusid,s)):l.alert(t.Result.FailureMessage)}},{submitSelector:e.$(".b-g-btn"),errorAlertModel:1})}),i.hide())},hide:function(){return this.sb&&this.sb.destroy(),this.sb=null,c.superclass.hide.call(this)},destroy:function(){return this.sb&&this.sb.destroy(),this.sb=null,c.superclass.destroy.call(this)}});s.exports=c});
define("crm-setting/common/saleaction/template/banner-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="sale-con" style="width:' + ((__t = info.width) == null ? "" : __t) + '"> <div class="sale-tip"><p></p></div> <div class="stage ' + ((__t = [ , "too-much", "too-little" ][info.status]) == null ? "" : __t) + '"> <div class="go-back"></div> <div class="circle-wrap" style="width:' + ((__t = info.wrapWidth) == null ? "" : __t) + ";margin-left:" + ((__t = info.wrapLeft) == null ? "" : __t) + '"> <div class="aid-wrap" style="margin-left:' + ((__t = info.offsetLeft) == null ? "" : __t) + '"> ';
            _.each(data, function(a) {
                __p += " ";
                if (a.active == 99) {
                    __p += ' <div style="margin-left:' + ((__t = info.margLeft) == null ? "" : __t) + '" class="s-status ' + ((__t = a.className) == null ? "" : __t) + '"><span><em>' + ((__t = a.order) == null ? "" : __t) + "</em></span></div> ";
                } else {
                    __p += ' <div style="margin-left:' + ((__t = info.margLeft) == null ? "" : __t) + '" class="circle ' + ((__t = [ "grey", "orange current", "hollow" ][a.active || 0]) == null ? "" : __t) + '"><span><em>' + ((__t = a.order) == null ? "" : __t) + "</em></span></div> ";
                }
                __p += " ";
            });
            __p += ' <div class="dotted-line"></div> <div style="width:' + ((__t = info.solidWidth) == null ? "" : __t) + '" class="solid-line"></div> </div> </div> <div class="go-ahead"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/common/saleaction/template/feedback-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<h2> $t("第' + ((__t = order) == null ? "" : __t) + "阶段：" + __e(name) + '") ';
            if (saleType === 1) {
                __p += ' <span class="s-weight">' + ((__t = $t("赢率")) == null ? "" : __t) + "" + ((__t = weight) == null ? "" : __t) + "%</span> ";
            }
            __p += " ";
            if (isOverTime && remainDays) {
                __p += " ";
                var days = $t("该阶段已停留{{remainDays}}天", {
                    remainDays: remainDays
                });
                __p += " <span>" + ((__t = days) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (isConfirm && status) {
                __p += " <span>" + ((__t = [ , $t("待确认"), $t("已确认"), $t("驳回") ][status]) == null ? "" : __t) + "</span> ";
            }
            __p += ' </h2> <div class="item-caption">' + ((__t = $t("阶段要求")) == null ? "" : __t) + '</div> <p class="item-con">' + __e(description) + "</p> ";
            if (stageRules.length > 0) {
                __p += ' <div class="item-caption">' + ((__t = $t("规则")) == null ? "" : __t) + '</div> <p class="item-con" style="line-height: 24px;">' + ((__t = stageRules.join("<br />")) == null ? "" : __t) + "</p> ";
            }
            __p += ' <div class="item-caption">' + ((__t = $t("阶段反馈")) == null ? "" : __t) + "</div> ";
            _.each(fieldList, function(item) {
                __p += " ";
                if (item.IsVisible && !item.noVisible) {
                    __p += " ";
                    if (item.FieldType === 1) {
                        __p += ' <h5 class="item-tit"><span>' + ((__t = item.FieldCaption) == null ? "" : __t) + "</span></h5> ";
                    } else {
                        __p += ' <div class="item-con"> <span>' + ((__t = item.FieldCaption) == null ? "" : __t) + "</span> <div>" + ((__t = valObj[item.FieldName] || "--") == null ? "" : __t) + "</div> </div> ";
                    }
                    __p += " ";
                }
                __p += " ";
            });
            __p += ' <div class="fb-btns"> ';
            if (fieldList[0] && btn[13001] && active !== 2) {
                __p += ' <span class="s-btn j-fb-edit">' + ((__t = [ $t("编辑"), $t("填写阶段反馈") ][active]) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (isFirst && isCanStartAfter && saleType === 2 && !isStartAfterSale) {
                __p += ' <span class="s-btn j-fb-startSaleAfter">' + ((__t = $t("启用售后流程")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (btn[13003]) {
                __p += ' <span class="s-btn j-fb-confirm">' + ((__t = $t("确 认")) == null ? "" : __t) + '</span> <span class="s-btn reject-btn j-fb-reject">' + ((__t = $t("驳 回")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (btn[13002]) {
                __p += ' <span class="s-btn j-fb-revoke">' + ((__t = $t("撤回")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (btn[13004]) {
                __p += ' <span class="s-btn ' + ((__t = isLast ? "j-fb-complete" : "j-fb-next") == null ? "" : __t) + '">' + ((__t = isLast ? $t("完成销售流程") : $t("进入下一阶段")) == null ? "" : __t) + "</span> ";
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/common/saleaction/template/noexist-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (from === 2) {
                __p += ' <div style="color:#515766;font-size:32px;margin-top:180px;text-align:center"> ';
            } else {
                __p += ' <div style="color:#e6e6e6;font-size:20px;text-align:center"> ';
            }
            __p += " " + ((__t = $t("尚未启用")) == null ? "" : __t) + "" + ((__t = [ $t("流程"), $t("售前流程"), $t("售后流程") ][saleType]) == null ? "" : __t) + " </div>";
        }
        return __p;
    };
});
define("crm-setting/common/saleaction/template/nosale-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-c-saleaction"> <div class="no-sale no-sale-big"> <h1>' + ((__t = $t("商机未启用")) == null ? "" : __t) + " [$t('销售'), " + ((__t = $t("售前销售")) == null ? "" : __t) + ", " + ((__t = $t("售后销售")) == null ? "" : __t) + "][type]</h1> <h1>" + ((__t = $t("您可以向CRM管理员建议启用")) == null ? "" : __t) + "</h1> <h2>[$t('销售'), " + ((__t = $t("售前销售")) == null ? "" : __t) + ", " + ((__t = $t("售后销售")) == null ? "" : __t) + "][type] " + ((__t = $t("有什么好处？")) == null ? "" : __t) + "</h2> <p>" + ((__t = $t("格式化内容提高赢单机率")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("数据可统计过程管理更精细")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("按部门配置销售节奏更规范")) == null ? "" : __t) + '</p> <div class="bg-img"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/common/saleaction/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-c-saleaction"> <div class="banner-wrap"></div> <div class="con-wrap"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/common/saleaction/view/view",["crm-modules/common/field/field"],function(e,t,i){var s=e("crm-modules/common/field/field"),l=FS.crmUtil,r=s.View.extend({render:function(){this.$el.html('<div style="margin-bottom:15px" class="j-sale-wrap"></div><div style="margin-bottom:15px" class="j-cus-wrap"></div><div class="j-opp-wrap"></div>'),this.count=0,this.renderSale(),this.renderCus(),this.renderOpp()},renderSale:function(){var e=this,t=e._getModelData(7);!e.saleView&&t&&(t.el=e.$(".j-sale-wrap"),e.saleView=new s.View(t),e.saleView.on("render.after",e.renderAfter,e),e.saleView.render())},renderCus:function(){var t=this,i=t._getModelData(2);!t.cusView&&i&&(i.el=t.$(".j-cus-wrap"),e.async("crm-modules/action/customer/customer",function(e){t.cusView=new e.View(i),t.cusView.on("render.after",t.renderAfter,t),t.cusView.render()}))},renderOpp:function(){var t=this,i=t._getModelData(8);!t.oppView&&i&&(i.el=t.$(".j-opp-wrap"),e.async("crm-modules/action/opportunityobj/opportunityobj",function(e){t.oppView=new e.View(i),t.oppView.on("render.after",t.renderAfter,t),t.oppView.render()}))},_getModelData:function(i){var s=[],l=[],e=this.model.get("fieldList"),r=this.model.get("fieldData");return _.each(e,function(e){var t;e.OwnerType===i&&(s.push(_.extend({},e,{FieldName:e.realFieldName||e.FieldName})),t=_.findWhere(r,{FieldName:e.FieldName}))&&l.push(_.extend({},t,{FieldName:e.realFieldName||e.FieldName}))}),s[0]?{isEdit:!0,fieldList:s,fieldData:l}:this.renderAfter()},renderAfter:function(){3==++this.count&&this.trigger("resizedialog")},collect:function(){return{CustomerFieldDatas:this.cusView&&this.cusView.collect(),UDFieldDatas:this.saleView&&this.saleView.collect(),OppoFieldDatas:this.oppView&&this.oppView.collect()}},submit:function(){var t=this,e=this.collect();this.model.get("isValid")&&!this.validate(e)?this.scrollToError():l.FHHApi({url:"/EM1HCRM/SaleAction/AddSaleActionStage",data:{SaleActionID:this.model.get("saleactionID"),SaleActionStageID:this.model.get("stageID"),OpportunityID:this.model.get("oppId"),NeedCheckValid:!1,UDFieldDatas:e.UDFieldDatas,CustomerFieldDatas:e.CustomerFieldDatas,OppoFieldDatas:e.OppoFieldDatas},success:function(e){0===e.Result.StatusCode?(l.remind(1,$t("操作成功")),t.model.trigger("submit.success",e.Value,t.model.toJSON())):l.alert(e.Result.FailureMessage||$t("操作失败"))}},{submitSelector:this.model.get("btn"),errorAlertModel:1})},destroy:function(){this.off(),this.$el.empty(),this.saleView&&this.saleView.destroy(),this.cusView&&this.cusView.destroy(),this.oppView&&this.oppView.destroy(),this.oppView=this.cusView=this.saleView=null,this.$el=this.el=this.options=null}});i.exports=r});