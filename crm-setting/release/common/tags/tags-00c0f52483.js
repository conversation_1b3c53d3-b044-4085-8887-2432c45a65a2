define("crm-setting/common/tags/tags",["crm-modules/common/util","./template/tpl-html","./template/field-html"],function(e,t,a){var n=e("crm-modules/common/util"),i=e("./template/tpl-html"),s=e("./template/field-html"),m={1001:{name:"sale",type:1,desc:$t("销售记录类型"),showRequire:!0},1002:{name:"service",type:2,desc:$t("服务记录类型"),showRequire:!1},1003:{enumName:"EnumCRMAddressType",name:"address",type:4,desc:$t("地址类型"),showRequire:!1},1004:{enumName:"EnumProductLine",name:"productline",type:4,desc:$t("产品线"),showRequire:!1}},e=Backbone.View.extend({typeObj:null,isCheckout:"0",initialize:function(e){var t=this;t.typeObj=m[e.type],t._bindEvents(),t.getConfig(),t.render()},render:function(){var e=this;$t("在此调整{{desc}}的选项",{desc:"obj.desc"}),e.$el.html(i({obj:e.typeObj})),e.renderField()},renderField:function(){var t=this;t.typeObj&&4==t.typeObj.type?t.getEnumData(function(e){$(".crm-g-form",t.$el).html(s(_.extend(e,{type:t.typeObj?t.typeObj.type:""})))}):t.getData(function(e){$(".crm-g-form",t.$el).html(s(_.extend(e,{type:t.typeObj?t.typeObj.type:""})))})},getData:function(t){var a=this.typeObj;n.FHHApi({url:"/EM1HCRM/CustomTag/GetCustomTagList",data:{Type:a.type},success:function(e){0==e.Result.StatusCode&&t&&t({list:e.Value.CustomTagList||[],obj:a,maxlength:20})}})},getEnumData:function(t){n.FHHApi({url:"/EM1HEBL/EnumInfo/GetEnumByNames",data:{EnumNames:[this.typeObj.enumName]},success:function(e){0==e.Result.StatusCode&&(e=_.map(e.Value.Items,function(e){return{CustomTagID:e.EnumDetailID,Code:e.ItemCode,Name:e.ItemName,TagOrder:e.ItemCode,Type:4}}),t)&&t({list:e||[],obj:this.typeObj,maxlength:10})}})},_bindEvents:function(){n.fixInputEvent(".tags-wrap .b-g-ipt",$.proxy(this.changeTagName,this),this.$el)},changeTagName:function(e){e=$(e.target).closest(".tags-wrap");$(".crm-btn",e).removeClass("crm-btn-disabled")},events:{"click .add-tag":"addField","click .del-tag":"delField","click .fm-item":"focusField","click .btns .crm-btn":"saveField","click .mn-radio-item":"checkField"},checkField:function(e){this.showSubmitBtn(),this.isCheckout=$(e.currentTarget).attr("data-value")},showSubmitBtn:function(){$(".crm-btn",this.$el).removeClass("crm-btn-disabled")},addField:function(e){var t,a=this,i=$(".crm-g-form",a.$el);100<=$(".fm-item",i).length?n.alert($t("标签最多创建100项")):(t=$(s({list:[{CustomTagID:"",Code:"",Name:"",EI:"",Type:a.typeObj.type}],type:a.typeObj?a.typeObj.type:""})),i.append(t),t[0].click(),$(".b-g-ipt",t).focus(),a.showSubmitBtn())},delField:function(e){var t=this,e=$(e.currentTarget),a=e.closest(".fm-item"),i=$(".crm-g-form",t.$el),e=e.data("type");$(".fm-item",i).length<=2?n.alert($t("最少保留2个选项")):a.data("id")&&a.data("id")<7?n.alert($t("预设地址类型值不可删除")):(a.remove(),t["delele"+t.typeObj.name]=t["delele"+t.typeObj.name]||[],a.attr("data-id")&&t["delele"+t.typeObj.name].push({EditFlag:3,Code:a.data("code")||"",CustomTagID:a.data("id"),Type:e}),t.showSubmitBtn())},focusField:function(e){$(e.currentTarget).addClass("fm-item-edite").siblings().removeClass("fm-item-edite")},saveConfig:function(e,t){n.setConfigValue({key:"38",value:this.isCheckout}).then(function(){t()})},getConfig:function(e){var t=this;n.getConfigValue("38").then(function(e){t.isCheckout=e,"1"==t.isCheckout&&(t.$(".mn-radio-item").removeClass("mn-selected"),t.$(".mn-radio-item-y").addClass("mn-selected"))})},saveField:function(e){var t=this;t.typeObj.showRequire?t.saveConfig(e,function(){t.saveChange(e)}):t.saveChange(e)},saveChange:function(e){var t=this,a=$(e.currentTarget),e=$(".crm-g-form",t.$el),i=e.data("type"),e=t.getSubmitData(e,i);a.hasClass("crm-btn-disabled")||n.FHHApi({url:"/EM1HCRM/CustomTag/SaveCustomTag",data:{Requests:e},success:function(e){0==e.Result.StatusCode?(n.remind("1",$t("保存成功")),t.renderField(),t["delele"+t.typeObj.name]=[],a.addClass("crm-btn-disabled")):n.alert(e.Result.FailureMessage)}},{submitSelector:a,errorAlertModel:1})},getSubmitData:function(e,n){var t=this,s=[],m=[];return $(".fm-item",e).each(function(e,t){var t=$(t),a=t.attr("data-id"),i=t.attr("data-code"),t=$(".b-g-ipt",t).val();""!=t&&(s.push({EditFlag:a?2:1,CustomTagID:a||"",Type:n,Name:t,TagOrder:e}),m.push({EditFlag:a?2:1,EnumDetailID:a||"",ItemCode:i||e+1,ItemName:t,ItemOrder:e}))}),t.typeObj&&4==t.typeObj.type?(e=_.map(t["delele"+t.typeObj.name],function(e){return{EditFlag:e.EditFlag,ItemCode:e.Code,EnumDetailID:e.CustomTagID}}))?m.concat(e):m:(e=t["delele"+t.typeObj.name])?s.concat(e):s},destroy:function(){this.$el.empty(),this.typeObj=this.events=null,this.$el.off()}});a.exports=e});
define("crm-setting/common/tags/template/field-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (list.length > 0) {
                __p += " ";
                _.each(list, function(item) {
                    __p += ' <div class="fm-item" data-id="' + ((__t = item.CustomTagID) == null ? "" : __t) + '" data-code="' + ((__t = item.Code) == null ? "" : __t) + '" data-type="' + ((__t = item.Type) == null ? "" : __t) + '" data-ei="' + ((__t = item.EI) == null ? "" : __t) + '"> <input type="text" value="' + __e(item.Name) + '" ' + ((__t = type == 4 ? 'maxlength="6"' : "") == null ? "" : __t) + ' class="b-g-ipt" placeholder="' + ((__t = $t("填写选项内容")) == null ? "" : __t) + '" /> <span class="del-tag old-del-tag">×</span> </div> ';
                });
                __p += " ";
            } else {
                __p += ' <div class="no-data">' + ((__t = $t("暂无任何标签")) == null ? "" : __t) + "</div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/common/tags/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="tags-wrap ' + ((__t = obj.name) == null ? "" : __t) + '-box"> ';
            if (obj.showRequire) {
                __p += ' <div class="crm-title"> <span class="crm-titleIcon"></span> <span class="crm-titleName">' + ((__t = $t("属性设置")) == null ? "" : __t) + '</span> </div> <div class="crm-salestype-radioBox mn-radio-box"> <span>' + ((__t = $t("是否必填")) == null ? "" : __t) + '</span> <span class="mn-radio-item mn-radio-item-y" data-value="1"></span> <span class="label">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item mn-radio-item-n mn-selected" data-value="0"></span> <span class="label">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += ' <div class="crm-title marginBottom0"> <span class="crm-titleIcon"></span> <span class="crm-titleName">' + ((__t = $t("选项设置")) == null ? "" : __t) + "</span> </div> ";
            var item = $t("在此调整{{desc}}的选项", {
                desc: obj.desc
            });
            __p += ' <p class="intro">' + ((__t = item) == null ? "" : __t) + '</p> <div class="crm-g-form" data-type="' + ((__t = obj.type) == null ? "" : __t) + '"> </div> <span class="add-tag">+' + ((__t = $t("添加选项")) == null ? "" : __t) + '</span> <div class="btns"> <span class="crm-btn crm-btn-primary crm-btn-disabled">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});