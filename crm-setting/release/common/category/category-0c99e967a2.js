function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var n;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(n="Object"===(n={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(t,e):void 0}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,i=Array(e);n<e;n++)i[n]=t[n];return i}function _iterableToArrayLimit(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var i,a,o,r,c=[],l=!0,d=!1;try{if(o=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;l=!1}else for(;!(l=(i=o.call(n)).done)&&(c.push(i.value),c.length!==e);l=!0);}catch(t){d=!0,a=t}finally{try{if(!l&&null!=n.return&&(r=n.return(),Object(r)!==r))return}finally{if(d)throw a}}return c}}function _arrayWithHoles(t){if(Array.isArray(t))return t}define("crm-setting/common/category/category",["crm-modules/common/util","base-ztree","./setname/setname","./template/tpl-html","./template/tplold-html"],function(a,t,e){var r=a("crm-modules/common/util"),o=(a("base-ztree"),a("./setname/setname")),n=a("./template/tpl-html"),i=a("./template/tplold-html"),c=Backbone.View.extend({initialize:function(t){var e=this;CRM._cache.close_old_category?e.$el.html(n()):(CRM.util.getProductWithAttr().then(function(t){e.$el.html(i({configAttr:t}))}),e.$el.addClass("category-s-crm-wrapper"),e.categoryId="",e.categoryName="",$(document).on("click.hideEditList",function(t){t=$(t.target);t.closest(".set-btn")[0]||t.closest(".edite-list")[0]||e.hideEditList()}),e.render())},events:{"mouseleave .edite-list":"hideEditList","click .j-del":"_onDel","click .j-edit":"_onEdit","click .j-add-inner":"_onAddInner","click .j-add-root":"_onAddRoot","click .j-add-level":"_onAddLevel","click .j-config-attribute":"_onConfigAttribute"},render:function(){var e=this;e.getData(function(t){e.initZtree(t),e.showNoDataTip(),e.createSearchInput(t)})},showNoDataTip:function(){var t=this;t.$(".no-data-li").remove(),0==t.$(".ztree-box .ui-check-ztree li").length&&t.$(".ztree-box .ui-check-ztree").html('<li class="no-data-li"><span class="no-data">'+$t("暂未添加分类")+"</span></li>")},getData:function(e){CRM.util.getCategoryDataList().then(function(t){e(t)})},createSearchInput:function(t){var o=this,e=this.createStructureStyle();o.searchInput=FxUI.create({wrapper:o.$(".search-container")[0],template:'\n\t\t\t\t\t<div>\n\t\t\t\t\t\t<fx-autocomplete\n\t\t\t\t\t\t\tpopper-class="my-autocomplete-category"\n\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\tv-model="state"\n\t\t\t\t\t\t\t:fetch-suggestions="querySearch"\n\t\t\t\t\t\t\t:placeholder="$t(\'搜索分类\')"\n\t\t\t\t\t\t\t:trigger-on-focus="true"\n\t\t\t\t\t\t\t:popper-append-to-body="false"\n\t\t\t\t\t\t\t@select="handleSelect">\n\t\t\t\t\t\t\t<i\n\t\t\t\t\t\t\t\tclass="el-icon-search el-input__icon"\n\t\t\t\t\t\t\t\tslot="suffix"\n\t\t\t\t\t\t\t\t@click="handleIconClick">\n\t\t\t\t\t\t\t</i>\n\t\t\t\t\t\t\t<template slot-scope="{ item }">\n\t\t\t\t\t\t\t\t<div class="name">\n\t\t\t\t\t\t\t\t\t<span v-for ="(one, index) in item" :key="index">\n\t\t\t\t\t\t\t\t\t{{one.name}}\n\t\t\t\t\t\t\t\t\t<i class="el-icon-arrow-right"></i>\n\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t</fx-autocomplete>\n                        <category-structure-style></category-structure-style>\n\t\t\t\t\t\t<fx-button type="primary" size="mini" @click="handleImport">{{ $t(\'导入\') }}</fx-button>\n\t\t\t\t\t\t<fx-button type="primary" size="mini" @click="handleExport">{{ $t(\'导出\') }}</fx-button>\n                    </div>\n\t\t\t\t',components:{categoryStructureStyle:e},data:function(){return{categories:t,state:""}},methods:{updateCategories:function(){var e=this;o.getData(function(t){e.categories=t})},querySearch:function(i,t){var a,o,r;""===i?t([]):(o=(a=this).categories,r=[],i=(this.queryString=i).toLowerCase(),_.each(o,function(t,e,n){-1<t.name.toLowerCase().indexOf(i)&&(t.pid?r.push(a.upstream(o,t.pid,[t])):r.push([t]))}),t(r))},upstream:function e(n,i,a){return i&&_.each(n,function(t){t._id===i&&(a.unshift(t),e(n,t.pid,a))}),a},handleSelect:function(t){var a=o.zTree.getNodeByParam("_id",t[0]._id,t[0].pid);o.zTree.selectNode(a,!0,!1,!0),_.each(t,function(t,e,n){var i=a&&a.children,i=_.find(i||[],function(t){return n[e+1]&&n[e+1]._id===t._id});i&&o.zTree.selectNode(i,!0,!1,!0),a=i}),_.find(this.$children,function(t){return"fx-autocomplete"===(t.$vnode&&t.$vnode.componentOptions.tag)}).dItemValue=this.queryString},handleIconClick:function(t){},handleImport:function(){CRM.api.import_async({zIndex:900,pageApiname:"ProductCategoryObj"})},handleExport:function(){CRM.api.export({apiname:"ProductCategoryObj",displayName:$t("产品分类"),showUniqueID:!1,showExportfield:!1,queryParam:{object_describe_api_name:"ProductCategoryObj",search_query_info:{limit:0,offset:0,filters:[{field_name:"pid",field_values:"",operator:"IS"}],orders:[]},search_template_type:"default"}})}}})},createStructureStyle:function(){var t=this.createMiniTreeSkeleton(),e=this.createMiniFlatSkeleton();return{template:'\n                    <div style="display:inline-block">\n                        <fx-button size="mini" @click="dialogVisible=true">{{ $t(\'分类结构样式\') }}</fx-button>\n                        <fx-dialog\n                            width="60%"\n                            :visible.sync="dialogVisible"\n                            :append-to-body="true"\n                            :title="$t(\'分类结构样式\')"\n                            :z-index="3000"\n                            >\n                            <div class="category-structure-style">\n                                <section class="structure-style-left">\n                                    <div class="style-left-skeleton">\n                                        <mini-tree-skeleton :radioVal="radio" @radio-change="radio=$event"></mini-tree-skeleton>\n                                        <mini-flat-skeleton :radioVal="radio" @radio-change="radio=$event"></mini-flat-skeleton>\n                                    </div>\n                                    <div class="style-left-recent" v-if="display">\n                                        <span>{{ $t(\'分类导航显示“最近”下单产品\') }}</span>\n                                        <fx-switch\n                                            v-model="recent"\n                                            size="small">\n                                        </fx-switch>\n                                    </div>\n                                    <div class="style-left-collect" v-if="display">\n                                        <span>{{ $t(\'分类导航显示“收藏”产品\') }}　　</span>\n                                        <fx-switch\n                                            v-model="collect"\n                                            size="small">\n                                        </fx-switch>\n                                    </div>\n                                </section>\n                                <section class="structure-style-right">\n                                    <p>{{ radio===\'1\'? $t("树形结构-适合分类级别、条目较少的业务场景, 示例") : $t("平铺结构-适合分类级别、条目较多的业务场景, 示例") }}:</p>\n                                    <div class="style-right-skeleton">\n                                        <div class="right-skeleton-inner">\n                                            <component :is="(radio===\'1\'? \'treeSkeleton\' : \'flatSkeleton\')">\n                                                <create-recent-collect \n                                                    :recentStatus="recent"\n                                                    :collectStatus="collect"\n                                                ></create-recent-collect>\n                                            </component>\n                                        </div>\n                                    </div>\n                                </section>\n                            </div>\n                            <span slot="footer" class="dialog-footer">\n                                <fx-button type="primary" @click="onSave" size="small">{{$t(\'保存\')}}</fx-button>\n                                <fx-button @click="dialogVisible = false" size="small">{{$t(\'取消\')}}</fx-button>\n                            </span>\n                        </fx-dialog>\n                    </div>\n                ',data:function(){return{dialogVisible:!1,radio:"2",display:!1,recent:!1,collect:!1}},components:{treeSkeleton:this.createTreeSkeleton(),flatSkeleton:this.createFlatSkeleton(),miniTreeSkeleton:t,miniFlatSkeleton:e,createRecentCollect:this.createRecentCollect()},watch:{dialogVisible:function(t){t&&(this.radio=CRM._cache.category_model_type?"1":"2")}},created:function(){this.getConfig()},methods:{getConfig:function(){var e=this;CRM.util.getConfigValues(["category_model_type"]).then(function(t){t=_.reduce(t,function(t,e,n,i){return t[e.key]=e.value.split(","),t},{});e.radio=t.category_model_type[0],e.cacheSwitch(t)},function(t){})},cacheSwitch:function(t){CRM._cache.category_model_type=t.category_model_type&&"1"==t.category_model_type[0]},radioSaveSuccess:function(){CRM._cache.category_model_type="1"==this.radio},radioSaveFail:function(){"1"===this.radio?this.radio="2":this.radio="1"},onSave:function(){var t=[],e=[],n=[];t.push({key:"category_model_type",value:this.radio}),e.push(this.radioSaveSuccess),n.push(this.radioSaveFail),this.setConfig({config:t,successQueue:e,failQueue:n})},setConfig:function(){var e=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},n=t.config,i=t.successQueue,a=void 0===i?[]:i,i=t.failQueue,o=void 0===i?[]:i;CRM.util.setConfigValues(n).then(function(){_.each(a,function(t){t.apply(e)}),r.remind(1,$t("设置成功")),e.dialogVisible=!1},function(){_.each(o,function(t){t.apply(e)}),r.remind(3,$t("设置失败"))})}}}},createMiniTreeSkeleton:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"2";return{template:'\n                    <div>\n                        <div :class="{checked: radio===\'1\'}" class="mini-tree-skeleton mini-skeleton" @click="updateRadio(\'1\')">\n                            <ul class="mini-tree-aside">\n                                <li v-for="n in 8"></li>\n                            </ul>\n                            <section></section>\n                        </div>\n                        <fx-radio v-model="radio" @change="updateRadio" label="1">{{$t(\'树形结构\')}}</fx-radio>\n                    </div>\n                ',data:function(){return{radio:t}},props:{radioVal:String},created:function(){this.radioVal&&(this.radio=this.radioVal)},watch:{radioVal:function(t){this.radio=t}},methods:{updateRadio:function(t){this.$emit("radio-change",t)}}}},createMiniFlatSkeleton:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"2";return{template:'\n                    <div>\n                        <div :class="{checked: radio===\'2\'}" class="mini-flat-skeleton mini-skeleton" @click="updateRadio(\'2\')">\n                            <ul class="mini-flat-aside">\n                                <li v-for="n in 5"></li>\n                            </ul>\n                            <section>\n                                <div class="mini-flat-main">\n                                    <h6></h6>\n                                    <ul v-for="m in 2">\n                                        <li v-for="n in 3"></li>\n                                    </ul>\n                                </div>\n                                <div class="mini-flat-main">\n                                    <h6></h6>\n                                    <ul v-for="x in 2">\n                                        <li v-for="y in 3"></li>\n                                    </ul>\n                                </div>\n                            </section>\n                        </div>\n                        <fx-radio v-model="radio" @change="updateRadio" label="2">{{$t(\'平铺结构\')}}</fx-radio>\n                    </div>\n                ',data:function(){return{radio:t}},props:{radioVal:String},created:function(){this.radioVal&&(this.radio=this.radioVal)},watch:{radioVal:function(t){this.radio=t}},methods:{updateRadio:function(t){this.$emit("radio-change",t)}}}},createTreeSkeleton:function(){return{template:'\n                    <div class="right-tree-skeleton">\n                        <aside>\n                            <slot></slot>\n                            <ul>\n                                <li>'.concat($t("一级分类"),'<i class="el-icon-arrow-down"></i></li>\n                                <li class="sub" v-for="n in 4">').concat($t("二级分类"),"</li>\n                                <li>").concat($t("一级分类"),"</li>\n                                <li>").concat($t("一级分类"),"</li>\n                            </ul>\n                        </aside>\n                        <main>\n                            <ul>\n                                <li>").concat($t("产品列表"),'</li>\n                                <li v-for="n in 6"></li>\n                            </ul>\n                        </main>\n                    </div>\n                ')}},createFlatSkeleton:function(){return{template:'\n                    <div class="right-flat-skeleton">\n                        <aside>\n                            <slot></slot>\n                            <ul>\n                                <li class="selected">'.concat($t("一级分类"),'<span class="customization-icon"><i></i></span></li>\n                                <li v-for="n in 6">').concat($t("一级分类"),'</li>\n                            </ul>\n                        </aside>\n                        <main>\n                            <ul>\n                                <li v-for="m in 2">\n                                    <h6>').concat($t("二级分类"),'</h6>\n                                    <ul class="sub-bgc">\n                                        <li v-for="n in 3" title=').concat($t("三级分类"),">").concat($t("三级分类"),"</li>\n                                        <li title=").concat($t("四级分类"),">").concat($t("四级分类"),"</li>\n                                        <li title=").concat($t("五级分类"),">").concat($t("五级分类"),"</li>\n                                        <li title=").concat($t("六级分类"),">").concat($t("六级分类"),"</li>\n                                    </ul>\n                                </li>\n                            </ul>\n                        </main>\n                    </div>\n                ")}},createRecentCollect:function(){return{template:'\n                    <ul class="right-skeleton-recent" v-show="recentStatus || collectStatus">\n                        <li v-show="recentStatus">\n                            <i class="fx-icon-clock2"></i>\n                            <span>'.concat($t("最近"),'</span>\n                        </li>\n                        <li v-show="collectStatus">\n                            <i class="fx-icon-collect"></i>\n                            <span>').concat($t("收藏"),"</span>\n                        </li>\n                    </ul>\n                "),props:["recentStatus","collectStatus"]}},initZtree:function(t){var o=this;_.each(t,function(t){t.nameArray=t.category_code?"（"+t.category_code+"）"+t.name:t.name}),o.zTree=$.fn.zTree.init($("#category-ztree",o.$el),{data:{key:{name:"nameArray"},simpleData:{enable:!0,idKey:"_id",pIdKey:"pid",rootPId:""}},edit:{enable:!0,showRenameBtn:!1,showRemoveBtn:!1,editNameSelectAll:!0},view:{showLine:!1,showIcon:!1,autoCancelSelected:!1,selectedMulti:!1,dblClickExpand:!1,addHoverDom:function(t,e){o.hoverHandle(t,e)},removeHoverDom:function(t,e){o.leaveHandle(t,e)}},callback:{onClick:function(t,e,n){o.zTree.expandNode(n,!0),o.categoryId=n._id,o.categoryName=n.name},beforeDrop:function(t,e,n,i){return o.remberDropNode(e),null!=n},onDrop:function(t,e,n,i,a){i&&(o.dragCategory(n[0],i,a),$("#"+n[0].tId+"_a").trigger("click"))}}},t)},remberDropNode:function(t){t=t[0];t.getPreNode()?(this.originTargetNode=t.getPreNode(),this.originTargetType="next"):t.getNextNode()?(this.originTargetNode=t.getNextNode(),this.originTargetType="prev"):(this.originTargetNode=t.getParentNode(),this.originTargetType="inner")},dragCategory:function(e,n,i){var t,a=this,o=n.getParentNode();switch({prev:1,next:2,inner:3}[i]){case 1:t=+n.order_field;break;case 2:t=+n.order_field+1;break;default:t=n.children?+n.children.length+1:1}r.FHHApi({url:"/EM1HNCRM/API/v1/object/product_category/service/update",data:{code:e.code||"",category_code:e.category_code,pid:"inner"==i?n._id:o?o._id:"",object_describe_api_name:n.object_describe_api_name,name:e.name,order_field:t,_id:e._id},success:function(t){0!=t.Result.StatusCode?(a.cancleDrog(e),r.alert(t.Result.FailureMessage)):a.updateOrderField(n,i,(t=t.Value)&&t.data)},error:function(){a.cancleDrog(e)}},{errorAlertModel:1})},updateOrderField:function(t,e,n){var i=[],i="inner"==e?t.children:t.getParentNode()?t.getParentNode().children:this.zTree.getNodesByFilter(function(t){return!t.pid});_.each(i,function(t){var e=_.findWhere(n,{_id:t._id});t.order_field=e&&e.order_field})},cancleDrog:function(t){this.zTree.moveNode(this.originTargetNode,t,this.originTargetType)},hoverHandle:function(t,e){var n=$("#"+e.tId+"_a"),i=e.tId+"_edite-b";0<$("#"+i).length||(this.categoryId=e._id,this.categoryName=e.name,this.createEditList(n))},leaveHandle:function(t,e){$("#"+e.tId+"_edite-b").off().remove()},createEditList:function(t){var e=t.position(),n=$(".edite-list",this.$el);n.css({left:e.left+280,top:e.top}),n.show(),this.$el.find(".hoverd").removeClass("hoverd"),t.addClass("hoverd")},hideEditList:function(){$(".edite-list",this.$el).hide(),this.$el.find(".hoverd").removeClass("hoverd")},_onDel:function(t){var e,n=this,i=n.zTree.getNodesByParam("_id",n.categoryId)[0],a=$t("你确认要删除该分类");n.checkIsUsed(i.code,function(t){t.Value.result?(i.isParent&&(a=$t("当前分类下还有子分类请确认是否一起删除")),e=r.confirm(a,$t("删除"),function(){r.FHHApi({url:"/EM1HNCRM/API/v1/object/product_category/service/delete",data:{code:i.code||"",id:i._id,category_code:i.category_code},success:function(t){e.hide(),0===t.Result.StatusCode?(r.remind($t("删除分类成功")),n.zTree.removeNode(i),n.showNoDataTip()):r.alert(t.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:$(".b-g-btn",e.element)})})):r.alert($t("该分类或分类下的子分类已被使用，无法删除"))})},checkIsUsed:function(t,e){r.FHHApi({url:"/EM1HNCRM/API/v1/object/spu_sku_object/service/check_delete_category",data:{categoryCodes:[t]},success:function(t){0===t.Result.StatusCode?e&&e(t):r.alert(t.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:$(".b-g-btn",confirm.element)})},_onEdit:function(){var i=this,t=i.zTree.getNodesByParam("_id",i.categoryId)[0],t=new o({title:$t("编辑"),tip:$t("修改分类 {{categoryName}} 的名称",{categoryName:i.categoryName}),categoryId:i.categoryId,categoryCode:t.code,parentId:t.pid,node:t});t.on("refresh",function(t,e){var n=i.zTree.getNodesByParam("_id",i.categoryId)[0];n.name=t,n.category_code=e,n.nameArray="（"+n.category_code+"）"+n.name,i.zTree.updateNode(n),i.searchInput.updateCategories()}),t.show()},_onAddRoot:function(){var e=this,t=e.zTree.getNodes(),t=new o({title:$t("新建"),tip:$t("新增一级分类"),type:"new",categoryId:"",categoryCode:"",parentId:"",orderField:+t.length+1});t.on("refresh",function(t){e.zTree.addNodes(null,[t]),e.showNoDataTip(),e.searchInput.updateCategories()}),t.show()},_onAddInner:function(){var t,e=this,n=e.zTree.getNodesByParam("_id",e.categoryId)[0];19<=n.level?r.alert($t("最多可建20级分类")):((t=new o({title:$t("新建子分类"),tip:$t("为{{categoryName}} 新增子分类",{categoryName:e.categoryName}),type:"new",categoryId:n._id,categoryCode:n.code,parentId:n._id,orderField:n.children&&n.children.length?+n.children[n.children.length-1].order_field+1:1})).on("refresh",function(t){e.zTree.addNodes(n,[t]),e.showNoDataTip(),e.searchInput.updateCategories()}),t.show())},_onAddLevel:function(){var e=this,n=e.zTree.getNodesByParam("_id",e.categoryId)[0],t=new o({title:$t("新建平级分类"),tip:$t("新增{{categoryName}}的平级分类",{categoryName:e.categoryName}),type:"new",categoryId:n._id,categoryCode:n.code,parentId:n.pid,orderField:+n.order_field+1});t.on("refresh",function(t){e.zTree.addNodes(n.getParentNode(),[t]),e.showNoDataTip(),e.searchInput.updateCategories()}),t.show()},_onConfigAttribute:function(){var n=this,i=this.categoryId,t=this.getAttributeParameter(i);this.ajaxAttribute(t).then(function(t){var e=n.getAttributeById(t);a.async("crm-modules/components/pickattribute/pickattribute",function(t){new t({apiName:"AttributeObj",type:"edit",isEdit:!1,editData:[].concat(e),success:function(t){var e=t.data;n.setAttribute(i,t.disRelatedIds,e)}}).render()})})},getAttributeById:function(t){return t&&t.attributes?t.attributes.map(function(t){return t.attribute}):[]},getAttributeParameter:function(t){return{url:"getAttributeAndValueByCategoryIdForManager",data:{categoryId:t}}},setAttribute:function(t,e,n){n={categoryId:t,related:!0,attributeIds:this.getRelatedIds(n)},t={url:"productCategoryRelateAttribute",data:{categoryId:t,related:!1,attributeIds:e}};Promise.all([this.ajaxAttribute({url:"productCategoryRelateAttribute",data:n}),this.ajaxAttribute(t)]).then(function(t){t=_slicedToArray(t,2);t[0];r.remind($t("操作成功"))})},getRelatedIds:function(t){return[].concat(t).map(function(t){return t._id})},ajaxAttribute:function(n){return new Promise(function(e,t){r.FHHApi({url:"/EM1HNCRM/API/v1/object/attribute/service/"+n.url,data:n.data,success:function(t){0===t.Result.StatusCode?e(t.Value):r.alert(t.Result.FailureMessage)}},{errorAlertModel:1})})},destroy:function(){var t;$(document).off(".hideEditList"),$.fn.zTree.destroy("category-ztree"),(this.zTree=null)!=(t=this.searchInput)&&t.$destroy(),this.undelegateEvents()}});e.exports=c});
define("crm-setting/common/category/setname/setname",["crm-modules/common/util","crm-widget/dialog/dialog"],function(e,t,i){var s=e("crm-modules/common/util"),r=e("crm-widget/dialog/dialog").extend({attrs:{width:500,title:$t("标题"),showScroll:!1,tip:$t("提示文本"),type:"edit",categoryId:"",showBtns:!0,content:'<div class="crm-g-form" style="padding: 10px 0 25px;"><div class="fm-item"><p class="tip" style="color: #999; padding-bottom: 15px;"></p><div style="padding-bottom: 15px;overflow:hidden;"><label class="fm-lb" style="width:80px;">'+$t("分类名称")+'</label><input style="width:325px;"class="b-g-ipt fm-ipt" placeholder='+$t("请填写分类名称（100字以内）")+' maxlength="100" /></div><div style="padding-bottom: 15px;overflow:hidden;"><label class="fm-lb" style="width:80px;">'+$t("分类编码")+'</label><input style="width:325px;"class="b-g-ipt fm-ipt" placeholder='+$t("请填写分类编码（50字以内）")+' maxlength="50" /></div></div></div>'},render:function(){var e=r.superclass.render.call(this);return this._setTip(),this._setContent(),e},_setTip:function(e){$(".tip",this.element).html(this.get("tip"))},_setContent:function(){var e=this.get("node")||{};"edit"===this.get("type")&&($(".b-g-ipt",this.element).first().val(e.name),$(".b-g-ipt",this.element).last().val(e.category_code))},events:{"click .b-g-btn-cancel":"hide","focus .b-g-ipt":"hideError","click .b-g-btn":"onEnter"},onEnter:function(e){var t=this,i=$.trim($(".b-g-ipt",t.element).val()),r=$.trim($(".b-g-ipt",t.element).last().val());""==i&&s.showErrmsg($(".b-g-ipt",t.element).first(),$t("请填写分类名称")),""==r&&s.showErrmsg($(".b-g-ipt",t.element).last(),$t("请填写分类编码")),""!=i&&""!=r&&("edit"==t.get("type")?t._edit(i,r):t._add(i,r))},_edit:function(t,i){var r=this,e=r.get("node");s.FHHApi({url:"/EM1HNCRM/API/v1/object/product_category/service/update",data:{pid:r.get("parentId"),name:t,_id:r.get("categoryId"),order_field:e.order_field,object_describe_api_name:e.object_describe_api_name,category_code:i},success:function(e){0==e.Result.StatusCode?(s.remind($t("编辑成功！")),r.trigger("refresh",t,i),r.hide()):s.alert(e.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:$(".b-g-btn",r.element)})},_add:function(e,t){var i=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/product_category/service/add",data:{name:e,category_code:t,pid:i.get("parentId"),order_field:i.get("orderField")},success:function(e){var t;0==e.Result.StatusCode?(s.remind($t("新建成功！")),t=e.Value.result,_.extend(t,{nameArray:"（"+t.category_code+"）"+t.name}),i.trigger("refresh",t),i.hide()):s.alert(e.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:$(".b-g-btn",i.element)})},hideError:function(){s.hideErrmsg($(".b-g-ipt",this.element))},hide:function(){var e=r.superclass.hide.call(this);return this.destroy(),e}});i.exports=r});
define("crm-setting/common/category/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-category crm-p20"> <p>' + ((__t = $t("产品分类已实现分类对象化，已迁移至“产品分类对象”进行配置")) == null ? "" : __t) + '<a style="margin-left: 6px;" href="/XV/UI/Home#crm/list/=/ProductCategoryObj">' + ((__t = $t("点击配置")) == null ? "" : __t) + "&gt;</a></p> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/category/template/tplold-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-s-category crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("产品每个分类下可设置20级子分类")) == null ? "" : __t) + '</p> </div> <div class="category"> <span class="add-new j-add-root" data-type="root">+' + ((__t = $t("新增一级分类")) == null ? "" : __t) + '</span> <div class="search-container"></div> <div class="ztree-box"> <ul class="ui-check-ztree ui-product-dragul" id="category-ztree"> <li> <div class="crm-loading "></div> </li> </ul> </div> </div> <div class="edite-list b-g-hide"> <div class="edite-list-wrapper"> ';
            if (configAttr) {
                __p += '<span class="j-config-attribute">' + ((__t = $t("配置属性")) == null ? "" : __t) + "</span>";
            }
            __p += ' <span class="j-edit">' + ((__t = $t("编辑")) == null ? "" : __t) + '</span> <span class="j-del">' + ((__t = $t("删除")) == null ? "" : __t) + '</span> <span class="j-add-inner" data-type="inner">' + ((__t = $t("新建子级")) == null ? "" : __t) + '</span> <span class="j-add-level" data-type="level">' + ((__t = $t("新建平级")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});