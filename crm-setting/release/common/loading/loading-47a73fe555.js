define("crm-setting/common/loading/loading-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var count = obj.count || 10;
            __p += " ";
            var width = obj.width;
            __p += ' <div class="crm-s-loading" ';
            if (width) {
                __p += ' style="width:' + ((__t = width) == null ? "" : __t) + 'px"';
            }
            __p += '> <div class="wrap"> ';
            while (count) {
                __p += ' <div class="item-wrap"> <div class="item"> <label class="title"></label> <div class="content"></div> </div> </div> ';
                count--;
                __p += " ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/loading/loading",["./loading-html"],function(n,o,i){n=n("./loading-html");i.exports=n});