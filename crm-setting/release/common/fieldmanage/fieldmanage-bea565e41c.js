define("crm-setting/common/fieldmanage/collection/_get",[],function(e,t,i){i.exports={getCascadeFields:function(e){var i={};return _.each(e,function(t){8==t.FieldType&&_.each(t.CascadeFields,function(e){i[e]=t.FieldName})}),i},getLookupFields:function(){return _.filter(this.toJSON(),function(e){return 16==e.FieldType})},getCalculableFields:function(){var l=this,a=[],o={4:["Status"]};return l.each(function(e){var t=+e.get("FieldType"),i=e.get("FieldName"),n=e.get("FieldCaption"),d=e.get("UserDefinedFieldID"),e=e.get("RelationField");!d||~_.indexOf(o[l.options.ownerType],i)||e||-1<[4,5,6,23].indexOf(t)&&a.push({FieldName:i,FieldCaption:n})}),a},getNumberFields:function(){return _.filter(this.toJSON(),function(e){e=e.FieldType;return 4==e||5==e||6==e||21==e})},_getExitFormula:function(n){var d={};return this.each(function(e){var t=e.get("RenderFieldType"),i=e.get("DefaultValue");-1<[2,3].indexOf(n)&&-1<[2,3].indexOf(t)&&i&&(d[e.get("FieldName")]=i),-1<[4,5,6].indexOf(n)&&-1<[4,5,6].indexOf(t)&&i&&(d[e.get("FieldName")]=i)}),d}}});
define("crm-setting/common/fieldmanage/collection/collection",["../fetch","../config/config","crm-modules/common/util","../editmod/editmod","./_get"],function(e,i,t){var p=e("../fetch"),c=e("../config/config"),d=e("crm-modules/common/util"),n=e("../editmod/editmod"),e=e("./_get"),e=Backbone.Collection.extend(_.extend({options:{ownerType:2,fieldData:null},initialize:function(e,i){var t=this;this.options=_.extend({},this.options,i||{}),this.preFormatRelateData(),this.getFieldLayout(function(){t.updateCollection()})},getFieldLayout:function(i){var t=this,n=[];1<t.options.from?i&&i():p.getLayoutByApiName(CRM.config.objDes[this.options.ownerType].apiName).done(function(e){0===e.Result.StatusCode?(_.each(e.Value.listLayout,function(e){n.push({layout_api_name:e.api_name,layout_label:e.label,is_show:!0,is_required:!1,is_readonly:!1})}),t.options.layoutConfig=n,i&&i()):d.alert(e.Result.FailureMessage)})},updateCollection:function(){var i=this,e=i.options.fieldData;i.delFields=[],i.reset(),e?(e=i.clone(e),setTimeout(function(){e=i.format(e),i.addList(e)},100)):i.fetch(function(e){e=i.format(e),i.addList(e)})},preFormatRelateData:function(){var e=this.options.relateData,t=this.options.fieldData;e&&_.each(e,function(e,i){switch(i){case"customer":_.each(e,function(e,i){e.FieldProperty=3,e.EditFlag=2,t.push(e)});break;case"oppo":_.each(e,function(e,i){e.FieldProperty=4,e.EditFlag=2,t.push(e)})}})},fetch:function(i){var t=this;t._ajax&&t._ajax.abort(),t._ajax=p.fetchFieldsByOwnertype(t.options.ownerType,function(e){e=t.clone(e),t._ajax=0,i&&i(e)},!0)},addList:function(e){var t=this;_.each(e,function(e){var i=n.getModel(e.RenderFieldType);t.add(new i(e))})},addData:function(i){if(!_.isEmpty(i)){var e=this,t=e.options,n=(i.FieldProperty,"number"==typeof i.FieldType?{type:i.FieldType}:{strType:i.FieldType});if(1==t.from){var n=_.findWhere(c.fields,n),o={FieldProperty:2,FieldType:i.RenderFieldType||i.FieldType},o=(20===n.type&&(o={FieldProperty:2,FieldType:2,IsCodeField:!0}),this.where(o));if(-1<n.max&&o.length>=n.max)return void d.alert($t("最多创建{{max}}个{{name}}类型的字段",{max:n.max,name:n.name}))}(i=e.format([i])[0]).isNew=!0;var o=$.extend(!0,{},c.fieldinfo,{OwnerType:t.ownerType},i),a=[];return a.push(o),25!=i.FieldType&&"group"!=i.FieldType||_.each(c.areafields,function(e){a.unshift($.extend(!0,{},i,c.fieldinfo,{OwnerType:t.ownerType,FieldCaption:e.label,FieldType:e.type,FieldProperty:2,RenderFieldType:e.type,UsedIn:"component",IsShow:!1}))}),e.addList(a),!0}},deleteModel:function(e){this.remove(e),e&&this.delFields.push(e.toJSON()),this.trigger("btn.active")},collect:function(o,a){var d=this;if(p.fetchFieldsByOwnertype("AccountObj",function(e){d.accountFileds=e}),this.checkField())return null;a=a||!1;var l=[],r=[],s=[];return this.each(function(i){var t=i.getData(),e=i.get("FieldProperty"),n=o[i.cid];a&&!t.EditFlag&&(t.EditFlag=2),-1==_.indexOf([26,27,28,29,30,31],t.FieldType)&&(t.FieldOrder!=n&&(t.FieldOrder=n,t.UserDefinedFieldID)&&(t.EditFlag=2),t.UserDefinedFieldID||(t.EditFlag=1),1==e||2==e?(25==(t=i.get("isNew")?t:_.omit(t,"LayoutConfig")).FieldType&&_.each(c.areafields,function(e){e=i[e.apiName+"Model"];e&&(e.get("UserDefinedFieldID")&&e.set("EditFlag",2),t.EditFlag)&&l.push(_.omit(e.toJSON(),"LayoutConfig"))}),t.EditFlag&&l.push(t)):(i.get("isNew")&&(t.EditFlag=1),[r,s][e-3].push(t)),"country"==t.FieldType)&&d._accountCpdHandle(t,t.EditFlag,r)}),_.each(this.delFields,function(e){var i=e.FieldProperty;e.EditFlag=3,e.ModifyEnums=e.EnumName,e=_.pick(e,c.saveparam),"country"==(e=_.omit(e,"LayoutConfig")).FieldType&&d._accountCpdHandle(e,3,r),1==i||2==i?25==e.FieldType?l.push(_.extend(e,{IsVisible:!1})):l.push(e):3==i?r.push(e):4==i&&s.push(e)}),{fields:l,customer:r,oppo:s}},_accountCpdHandle:function(i,t,n){var o=this;_.each(["province","city","district"],function(e){e=_.findWhere(o.accountFileds,{api_name:e}),e={FieldApiName:e.api_name,FieldCaption:e.label,FieldName:e.api_name,FieldOrder:i.FieldOrder+1,FieldProperty:3,FieldType:e.type,render_type:e.type,UserDefinedFieldID:e._id,EditFlag:t};n.push(e)})},formatLayoutData:function(e,i){var t=[];return _.each(e,function(e){t.push(_.omit(e,"layout_label"))}),t},save:function(e,i){var t=this,e=t.collect(e);e&&!t._ajax&&(t._ajax=d.FHHApi({url:"/EM1HCRM/UserDefinedField/SaveUserDefinedField",data:{OwnerType:t.options.ownerType,UserDefinedFieldRequests:e.fields},success:function(e){(t._ajax=0)==e.Result.StatusCode?(d.remind("1",$t("保存成功")),FS.setAppStore("crm-fieldlist-"+t.options.ownerType,null),t.updateCollection(),t.trigger("save.suc")):d.alert(_.escape(e.Result.FailureMessage))}},{errorAlertModel:1,submitSelector:i}))},checkField:function(){var i=0;return this.each(function(e){e.check()&&i++}),0<i},clone:function(e){var t=[];return _.each(e,function(e){var i={};$.extend(!0,i,e),t.push(i)}),t},format:function(e){var i=this.options,t=this.getCascadeFields(e),n=(c.hidefields[i.ownerType],this);return _.map(e,function(e){return"string"==typeof e.ExtendProp&&(e.ExtendProp=JSON.parse(e.ExtendProp)),e.LayoutConfig=JSON.stringify(n.options.layoutConfig),_.contains([1,2],e.FieldProperty)&&(e.IsAllowNumToIpt=_.contains(c.autofields,e.FieldName)),2==e.FieldType&&e.IsCodeField||e.IsAllowNumToIpt?e.RenderFieldType=20:e.RelationField?e.RenderFieldType=24:11==e.FieldType?e.RenderFieldType=2:12==e.FieldType?e.RenderFieldType=7:"Employee|0"==e.ReferRule?e.RenderFieldType=2:e.RenderFieldType=e.FieldType,e.relationship=t,e.ParentCascadeName=t[e.FieldName],e.IsShow=n.checkShowField(e),e.InitFrom=i.from,e})},checkShowField:function(e){var i=c.hidefields[e.OwnerType];return!("component"==e.UsedIn||i&&_.contains(i,e.FieldName))}},e));t.exports=e});
define("crm-setting/common/fieldmanage/collection/smartformhandle",["crm-modules/common/util","crm-widget/dialog/dialog"],function(t,e,n){t("crm-modules/common/util");var c=t("crm-widget/dialog/dialog").extend({attrs:{width:500,title:$t("提示"),showBtns:!0,content:'<div class="crm-c-fieldmanage-smartform-dialog"><p class="title">'+$t("智能表单提示1")+'</p><p class="sub-title"></p><p>'+$t("智能表单提示2")+"</p></div>",btnName:{save:$t("同步并保存"),cancel:$t("不同步,仅保存")}},render:function(){return c.superclass.render.call(this)},events:{"click .b-g-btn-cancel":"onCancel","click .b-g-btn":"onSave"},show:function(t){var e=c.superclass.show.call(this);return this.$(".sub-title").html(_.map(t,function(t){return"["+t.FieldCaption+"] "})),e},onCancel:function(){this.trigger("cancel")},onSave:function(t){this.trigger("save")},hide:function(){return c.superclass.hide.call(this)},destroy:function(){return c.superclass.destroy.call(this)}});n.exports=c});
define("crm-setting/common/fieldmanage/config/_apiname",[],function(e,n,i){i.exports={getApiName:function(e){return(e=e?e+"_":"")+CRM.util.getUUId(5)+"__c"}}});
define("crm-setting/common/fieldmanage/config/_areafields",[],function(a,e,t){t.exports={country:{apiName:"country",label:$t("国家"),isStart:!0,isMust:!1,fieldLabel:$t("国家字段名字"),level:1,type:26,param:"area_country"},province:{apiName:"province",label:$t("省"),isStart:!0,isMust:!1,fieldLabel:$t("省字段名字"),level:2,type:27,param:"area_province"},city:{apiName:"city",label:$t("市"),isStart:!0,isMust:!1,fieldLabel:$t("市字段名字"),level:3,type:28,param:"area_city"},district:{apiName:"district",label:$t("区"),isStart:!0,isMust:!1,fieldLabel:$t("区字段名字"),level:4,type:29,param:"area_district"},location:{apiName:"location",label:$t("定位"),isStart:!0,isMust:!1,fieldLabel:$t("定位字段名字"),type:30,param:"area_location"},address:{apiName:"address",label:$t("详细地址"),isStart:!0,isMust:!1,fieldLabel:$t("详细地址字段名字"),type:31,param:"area_detail_address"}}});
define("crm-setting/common/fieldmanage/config/_autofields",[],function(e,o,d){d.exports=["ContractNo","TradeCode","CustomerNo","ReturnOrderCode","TradePaymentCode","TradeRefundCode","TradeBillCode"]});
define("crm-setting/common/fieldmanage/config/_compare",[],function(e,a,t){var n=[{value:"EQ",name:$t("等于"),math:"=",index:1},{value:"N",name:$t("不等于"),math:"!=",index:2},{value:"GT",name:$t("大于"),math:">",index:3},{value:"GTE",name:$t("大于等于"),math:">=",index:4},{value:"LT",name:$t("小于"),math:"<",index:5},{value:"LTE",name:$t("小于等于"),math:"<=",index:6},{value:"LIKE",name:$t("包含"),math:$t("包含"),index:7},{value:"NLIKE",name:$t("不包含"),math:$t("不包含"),index:8},{value:"IS",name:$t("为空"),math:$t("为空"),index:9},{value:"ISN",name:$t("不为空"),math:$t("不为空"),index:10},{value:11,name:$t("开始于"),math:$t("开始于"),index:11},{value:12,name:$t("结束于"),math:$t("结束于"),index:12},{value:"LT",name:$t("早于"),math:$t("早于"),index:13},{value:"GT",name:$t("晚于"),math:$t("晚于"),index:14},{value:"IN",name:$t("属于"),math:$t("属于"),index:15},{value:"NIN",name:$t("不属于"),math:$t("不属于"),index:15}];t.exports=n});
define("crm-setting/common/fieldmanage/config/_fieldinfo",[],function(e,l,i){i.exports={OwnerType:0,CodeRule:null,EnumDetails:[],EnumName:"",ExtendProp:{IsAllowHide:!0},FieldCaption:"",FieldName:"",FieldOrder:0,FieldProperty:2,FieldType:0,RenderFieldType:0,Formula:"",CalFormula:"",IsAllowEditNotNull:!0,IsAllowEditOption:!1,IsCodeField:!1,IsNotNull:!1,IsVisible:!0,IsWatermark:!1,IsShow:!0,UserDefinedFieldID:"",EditFlag:1,ParentCascadeName:"",CascadeFields:[],DecimalDigits:0,ReturnValueType:"",RelationField:"",DefaultValue:"",DefaultIsZero:!0,ObjectRelation:null,ObjectAggregate:null,InitFrom:0}});
define("crm-setting/common/fieldmanage/config/_fields",[],function(t,e,n){var c=[{name:$t("单行文本"),strType:"text",type:2,max:50,length:100,icon:"crm-ico-ipt",isInput:!0,desc:$t("适用于填写简短的文字如姓名")},{name:$t("多行文本"),strType:"long_text",type:3,max:50,length:2e3,icon:"crm-ico-area",isInput:!0,desc:$t("适用于填写大段的文字如备注建议")},{name:$t("整数"),strType:"number",type:4,max:20,length:-1,icon:"crm-ico-init",isInput:!0,desc:$t("适用于填写数字如年龄订购数量")},{name:$t("小数"),strType:"number",type:5,max:20,length:-1,icon:"crm-ico-float",isInput:!0,desc:$t("适用于填写数字如度量单位折扣信息")},{name:$t("金额"),strType:"currency",type:6,max:20,length:-1,icon:"crm-ico-float",isInput:!0,desc:$t("适用于填写数字带千分位")},{name:$t("日期"),strType:"date",type:7,max:20,length:100,icon:"crm-ico-date",isInput:!1,desc:$t("适用于选择特定日期")},{name:$t("单选选择"),strType:"select_one",type:8,max:50,length:-1,icon:"crm-ico-radio",isInput:!1,desc:$t("适用于在几个选项里选一个如投票男")},{name:$t("多选选择"),strType:"select_many",type:9,max:50,length:-1,icon:"crm-ico-check",isInput:!1,desc:$t("适用于几个选项里选多个，如投票")},{name:$t("图片"),strType:"image",type:10,max:20,length:-1,icon:"crm-ico-img",isInput:!1,desc:$t("图片类型适用于作为信息的一个补充如合同附件等")},{name:$t("地址"),strType:11,type:11,max:-1,length:-1,icon:"",isInput:!0,desc:""},{name:$t("生日"),strType:12,type:12,max:-1,length:-1,icon:"",isInput:!1,desc:""},{name:$t("布尔值"),strType:"true_or_false",type:13,max:-1,length:-1,icon:"",isInput:!1,desc:$t("适用于状态的判断")},{name:$t("二级级联"),strType:"multi_level_select_one",type:14,max:20,length:-1,icon:"crm-ico-check",isInput:!1,desc:$t("二级单选用于选择多层级信息")+","+$t("如国民经济行业分类等")},{name:$t("日期时间类型"),strType:"date_time",type:15,max:-1,length:-1,icon:"",isInput:!1,desc:""},{name:$t("附件"),strType:"file_attachment",type:17,max:10,length:-1,icon:"crm-ico-annex",isInput:!1,desc:$t("附件类型适用于上传文件")+","+$t("最多上传10个附件")},{name:$t("电话"),strType:"phone_number",type:18,max:10,length:100,icon:"crm-ico-tel",isInput:!1,desc:$t("用于输入电话号码")},{name:$t("邮件"),strType:"email",type:19,max:10,length:100,icon:"crm-ico-email",isInput:!1,desc:$t("用于输入邮件地址")},{name:$t("计算字段"),strType:"formula",type:21,max:10,length:-1,icon:"crm-ico-caculate",isInput:!0,desc:$t("用于对指定的字段通过计算字段计算出结果")+'<span style="color:#ff7663;margin-top: 20px;display: block;">'+$t("调整公式会造成历史数据中该公式计算的结果发生变化请慎重调整")+"</span>"},{name:$t("分割线"),type:1,max:-1,length:-1,icon:"crm-ico-line",isInput:!1,desc:$t("分割线功能说明")},{name:$t("自增编号"),strType:"auto_number",type:20,max:10,length:-1,icon:"crm-ico-auto",isInput:!1,desc:$t("系统按照规则自动生成不可修改")},{name:$t("业务类型"),strType:"record_strType",type:22,max:-1,length:-1,icon:"",isInput:!1,desc:""},{name:$t("统计字段"),strType:23,type:23,max:-1,length:-1,icon:"crm-ico-statistics",isInput:!1,desc:$t("支持在主从关系的主对象中新建，对从属对象本身或某个字段进行统计运算")},{name:$t("引用字段"),strType:24,type:24,max:-1,length:-1,icon:"crm-ico-quote",isInput:!1,desc:$t("关联对象功能描述")},{name:$t("查找关联"),strType:"object_reference",type:16,max:10,length:-1,icon:"crm-ico-refobj",isInput:!1,desc:$t("用于将当前对象与其他对象（或自身）建立关联关系")},{name:$t("地区定位"),strType:"group",type:25,max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("可以使用该字段记录业务执行的时间、地点及时长")},{name:$t("国家"),strType:"country",type:26,max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("可以使用该字段记录业务执行的时间、地点及时长")},{name:$t("省"),strType:"province",type:27,max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("可以使用该字段记录业务执行的时间、地点及时长")},{name:$t("市"),strType:"city",type:28,max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("可以使用该字段记录业务执行的时间、地点及时长")},{name:$t("区"),strType:"district",type:29,max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("可以使用该字段记录业务执行的时间、地点及时长")},{name:$t("定位"),strType:"location",type:30,max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("可以使用该字段记录业务执行的时间、地点及时长")},{name:$t("详细地址"),strType:"address",type:31,max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("可以使用该字段记录业务执行的时间、地点及时长")},{name:$t("人员"),strType:"employee",max:1,length:-1,isInput:!1,icon:"crm-ico-location",desc:$t("用于选择公司内指定人员")}];n.exports=c});
define("crm-setting/common/fieldmanage/config/_fieldtype",[],function(e,t,n){n.exports={text:2,long_text:3,select_one:8,select_many:9,currency:6,date:7,date_time:15,phone_number:18,email:19,percentile:5,number:5,object_reference:16,true_or_false:13,auto_number:20,formula:21,location:30,quote:24,count:23,country:26,province:27,city:28,district:29}});
define("crm-setting/common/fieldmanage/config/_func",[],function(l,e,a){$t("显示方式"),$t("crm.NUMBERSTRING显示方式"),$t("存储规则"),$t("大写数字的金额形式最多显示到小数点两位数据存储按实际位数保存"),$t("显示方式"),$t("crm.NUMBERSTRINGRMB显示方式");var t=[{label:$t("时间函数"),list:[{label:"DATE",value:"DATE(year,month,day)",tip:[{label:$t("用法"),value:"DATE(year,month,day)"},{label:$t("描述"),value:$t("通过三个数值类型参数创建一个日期类型的值")},{label:$t("示例"),value:"DATE(2013,5,21)，"+$t("返回")+"：2013-05-21"}]},{label:"DATEVALUE",value:"DATEVALUE(string)",tip:[{label:$t("用法"),value:"DATEVALUE(string)"},{label:$t("描述"),value:$t("通过一个字符串类型的参数创建一个日期类型的值")},{label:$t("示例"),value:'DATEVALUE("2013-05-21")，'+$t("返回")+"：2013-05-21"}]},{label:"DATETIMEVALUE",value:"DATETIMEVALUE(string)",tip:[{label:$t("用法"),value:"DATETIMEVALUE(string)"},{label:$t("描述"),value:$t("通过一个字符串类型参数返回日期时间字段中的日期时间")},{label:$t("示例"),value:'DATETIMEVALUE("2013-05-21 13:14:25")，'+$t("返回")+"：2013-05-21 13:14:25"}]},{label:"DATETIMETOTIME",value:"DATETIMETOTIME(data_time)",tip:[{label:$t("用法"),value:"DATETIMETOTIME(data_time)"},{label:$t("描述"),value:$t("通过一个日期时间类型参数返回日期时间字段中的时间")},{label:$t("示例"),value:'DATETIMETOTIME(DATETIMEVALUE("2013-05-21 13:14:25"))，'+$t("返回")+"：13:14:25"}]},{label:"DATETIMETODATE",value:"DATETIMETODATE(date_time)",tip:[{label:$t("用法"),value:"DATETIMETODATE(data_time)"},{label:$t("描述"),value:$t("通过一个日期时间类型参数返回日期时间字段中的日期")},{label:$t("示例"),value:'DATETIMETODATE(DATETIMEVALUE("2013-05-21 13:14:25"))，'+$t("返回")+"：2013-05-21"}]},{label:"YEARS",value:"YEARS(number)",tip:[{label:$t("用法"),value:"YEARS(number)"},{label:$t("描述"),value:$t("crm.YEARS描述")},{label:$t("示例"),value:"TODAY()+YEARS(1)，"+$t("返回")+"：2019-06-05"}]},{label:"MONTHS",value:"MONTHS(number)",tip:[{label:$t("用法"),value:"MONTHS(number)"},{label:$t("描述"),value:$t("crm.MONTHS描述")},{label:$t("示例"),value:"TODAY()+MONTHS(2)，"+$t("返回")+"：2018-08-05"}]},{label:"DAYS",value:"DAYS(number)",tip:[{label:$t("用法"),value:"DAYS(number)"},{label:$t("描述"),value:$t("crm.DAYS描述")},{label:$t("示例"),value:"TODAY()+DAYS(6)，"+$t("返回")+"：2018-06-11"}]},{label:"YEAR",value:"YEAR(date)",tip:[{label:$t("用法"),value:"YEAR(date)"},{label:$t("描述"),value:$t("crm.YEAR描述")},{label:$t("示例"),value:"YEAR(日期)，"+$t("返回")+"：2018"}]},{label:"MONTH",value:"MONTH(date)",tip:[{label:$t("用法"),value:"MONTH(date)"},{label:$t("描述"),value:$t("crm.MONTH描述")},{label:$t("示例"),value:"MONTH(日期)，"+$t("返回")+"：6"}]},{label:"DAY",value:"DAY(date)",tip:[{label:$t("用法"),value:"DAY(date)"},{label:$t("描述"),value:$t("crm.DAY描述")},{label:$t("示例"),value:"DAY(日期)，"+$t("返回")+"：5"}]},{label:"NOW",value:"NOW()",tip:[{label:$t("用法"),value:"NOW()"},{label:$t("描述"),value:$t("返回当前的日期时间")},{label:$t("示例"),value:"NOW()，"+$t("返回")+"：2018-06-05 19:31:54"}]},{label:"TODAY",value:"TODAY()",tip:[{label:$t("用法"),value:"TODAY()"},{label:$t("描述"),value:$t("返回当前的日期")},{label:$t("示例"),value:"TODAY()，"+$t("返回")+"：2018-06-05"}]},{label:"HOURS",value:"HOURS(number)",tip:[{label:$t("用法"),value:"HOURS(number)"},{label:$t("描述"),value:$t("设定一个数值作为一个小时的数值应用在指定日期时间的运算中")},{label:$t("示例"),value:"NOW()+HOURS(3)，"+$t("返回")+"：2018-06-05 22:37:15"}]},{label:"MINUTES",value:"MINUTES(number)",tip:[{label:$t("用法"),value:"MINUTES(number)"},{label:$t("描述"),value:$t("设定一个数值作为一个小时的数值应用在指定日期时间的运算中")},{label:$t("示例"),value:"NOW()+MINUTES(50)，"+$t("返回")+"：2018-06-05 20:28:48"}]}]},{label:$t("逻辑函数"),list:[{label:"AND",value:"AND(boolean1,boolean2,boolean3,...)",tip:[{label:$t("用法"),value:"AND(boolean1,boolean2,boolean3,...)"},{label:$t("描述"),value:$t("逻辑与若多个条件运算结果都为true则结果为true")},{label:$t("示例"),value:"AND(2>1,5>3,7>5)，"+$t("返回")+"：true"}]},{label:"OR",value:"OR(boolean1,boolean2,...)",tip:[{label:$t("用法"),value:"OR(boolean1,boolean2,...)"},{label:$t("描述"),value:$t("逻辑或若其中任何一个条件运算结果为true则结果为true")},{label:$t("示例"),value:"OR(2>1,5<3)，"+$t("返回")+"：true"}]},{label:"NOT",value:"NOT(boolean)",tip:[{label:$t("用法"),value:"NOT(boolean)"},{label:$t("描述"),value:$t("若条件为true则结果为false反之结果为true")},{label:$t("示例"),value:"NOT(5<3)，"+$t("返回")+"：true"}]},{label:"CASE",value:"CASE(expression, value1, result1, value2, result2,...,else_result)",tip:[{label:$t("用法"),value:"CASE(expression, value1, result1, value2, result2,...,else_result)"},{label:$t("描述"),value:$t("crm.CASE描述")},{label:$t("示例"),value:"CASE(3, 2, 2, 3, 33, 6)，"+$t("返回")+"：33"}]},{label:"IF",value:"IF(logical_test, value_if_true, value_if_false)",tip:[{label:$t("用法"),value:"IF(logical_test, value_if_true, value_if_false)"},{label:$t("描述"),value:$t("判断条件结果返回")},{label:$t("示例"),value:"IF(true, 34, 52)，"+$t("返回")+"：34"}]},{label:"ISNULL",value:"ISNULL(expression)",tip:[{label:$t("用法"),value:"ISNULL(expression)"},{label:$t("描述"),value:$t("判断表达式结果是否为空如果为空返回true反之为false")},{label:$t("示例"),value:"ISNULL(6)，"+$t("返回")+"：false"}]},{label:"ISNUMBER",value:"ISNUMBER(string)",tip:[{label:$t("用法"),value:"ISNUMBER(string)"},{label:$t("描述"),value:$t("判断字符串是否可以转为数字如果可以返回true反之为false")},{label:$t("示例"),value:'ISNUMBER("6")，'+$t("返回")+"：true"}]},{label:"NULLVALUE",value:"NULLVALUE(expression, substitute_expression)",tip:[{label:$t("用法"),value:"NULLVALUE(expression, substitute_expression)"},{label:$t("描述"),value:$t("判断条件空与否")},{label:$t("示例"),value:"NULLVALUE(null, 6)，"+$t("返回")+"：6"}]}]},{label:$t("计算函数"),list:[{label:"MIN",value:"MIN(number1,number2)"},{label:"MAX",value:"MAX(number1,number2)"},{label:"MULTIPLE",value:"MULTIPLE(number1,number2)"},{label:"MOD",value:"MOD(number1,number2)"},{label:"ADDS",value:"ADDS(number1,number2)"},{label:"SUBTRACTS",value:"SUBTRACTS(number1,number2)"},{label:"ROUNDUP",value:"ROUNDUP(number, decimal_digits)"}]},{label:$t("文本函数"),list:[{label:"STARTWITH",value:"STARTWITH(string1, string2)"},{label:"ENDWITH",value:"ENDWITH(string1, string2)"},{label:"EQUALS",value:"EQUALS(string1, string2)"},{label:"LEN",value:"LEN(string)"},{label:"CONTAINS",value:"CONTAINS(string, compare_string)"},{label:"VALUE",value:"VALUE(string)"},{label:"NUMBERSTRING",value:"NUMBERSTRING(number)"},{label:"NUMBERSTRINGRMB",value:"NUMBERSTRINGRMB(number)",tip:[{label:$t("存储规则"),value:$t("crm.NUMBERSTRINGRMB存储规则")},{label:$t("显示方式"),value:$t("crm.NUMBERSTRINGRMB显示方式")}]},{label:"TRIM",value:"TRIM(string)"}]}];a.exports=t});
define("crm-setting/common/fieldmanage/config/_hidefields",[],function(e,o,n){n.exports={11:["ReturnMoney","PaymentMoney","RefundMoney","BillMoney","WaitPaymentMoney","RecordType","LogisticsStatus"],28:["RecordType"],4:["RecordType"],2:["RecordType","OutEI"],1:["RecordType"],3:["RecordType"],8:["RecordType"]}});
define("crm-setting/common/fieldmanage/config/_layout",[],function(o,n,e){e.exports={20:{no_show:0,no_required:1,no_readonly:2},21:{no_show:0,no_required:1,no_readonly:2},23:{no_show:0,no_required:1,no_readonly:1},24:{no_show:0,no_required:1,no_readonly:2}}});
define("crm-setting/common/fieldmanage/config/_nulltype",[],function(e,n,a){var t=[{name:$t("默认为零"),value:0},{name:$t("默认为空"),value:1}];a.exports=t});
define("crm-setting/common/fieldmanage/config/_object",[],function(e,a,n){var t=[{type:1,apiname:"LeadsObj",name:$t("crm.销售线索")},{type:2,apiname:"AccountObj",name:$t("crm.客户")},{type:3,apiname:"ContactObj",name:$t("crm.联系人")},{type:6,apiname:"RefundObj",name:$t("crm.退款")},{type:8,apiname:"OpportunityObj",name:$t("crm.商机")},{type:9,apiname:"InvoiceApplicationObj",name:$t("crm.开票申请")},{type:11,apiname:"SalesOrderObj",name:$t("crm.销售订单")},{type:12,apiname:"ReturnedGoodsInvoiceObj",name:$t("crm.退货单")},{type:13,apiname:"VisitingObj",name:$t("crm.拜访")},{type:16,apiname:"ContractObj",name:$t("crm.合同")},{type:20,apiname:"MarketingEventObj",name:$t("crm.市场活动")},{type:27,apiname:"ReturnedGoodsInvoiceProductObj",name:$t("crm.退货单产品")},{type:28,apiname:"SalesOrderProductObj",name:$t("crm.销售订单产品")}];n.exports=t});
define("crm-setting/common/fieldmanage/config/_operator",[],function(e,a,n){var c=[{name:$t("加"),value:"+",desc:"+"},{name:$t("减"),value:"-",desc:"-"},{name:$t("乘"),value:"*",desc:"*"},{name:$t("除"),value:"/",desc:"/"},{name:$t("括号"),value:"()",desc:"()"},{name:$t("大于"),value:">",desc:">"},{name:$t("小于"),value:"<",desc:"<"},{name:$t("大于等于"),value:">=",desc:">="},{name:$t("小于等于"),value:"<=",desc:"<="},{name:$t("不等于"),value:"!=",desc:"≠"},{name:$t("等于"),value:"==",desc:"="},{name:$t("逻辑非"),value:"!",desc:"!"},{name:$t("逻辑或"),value:"||",desc:"||"},{name:$t("逻辑与"),value:"&&",desc:"&&"},{name:"And",value:"&",desc:"&"}];n.exports=c});
define("crm-setting/common/fieldmanage/config/_point",[],function(e,a,n){n.exports=[{name:0,value:0},{name:1,value:1},{name:2,value:2},{name:3,value:3},{name:4,value:4},{name:5,value:5},{name:6,value:6},{name:7,value:7},{name:8,value:8},{name:9,value:9}]});
define("crm-setting/common/fieldmanage/config/_retype",[],function(e,t,a){var n=[{name:$t("数值"),value:"number",desc:$t("数值类型描述")},{name:$t("百分比"),value:"percentage",desc:$t("百分比类型描述")},{name:$t("文本"),value:"text",desc:$t("文本类型描述")},{name:$t("日期"),value:"date",desc:$t("日期类型描述")},{name:$t("布尔"),value:"bool",desc:$t("布尔类型描述")},{name:$t("金额"),value:"currency",desc:$t("金额类型描述")}];a.exports=n});
define("crm-setting/common/fieldmanage/config/_saveparam",[],function(e,l,i){i.exports=["EditFlag","EnumName","FieldCaption","FieldProperty","FieldName","FieldOrder","FieldType","Formula","CalFormula","IsCodeField","IsNotNull","IsVisible","IsWatermark","UserDefinedFieldID","FieldOrder","CodeRule","DefaultValue","AggregateType","AggregateFieldName","AggregateObject","DecimalDigits","ReturnValueType","RelationField","EnumName","DefaultIsZero","ObjectRelation","ObjectAggregate","LayoutConfig","UsedIn","fields","FieldApiName","IsAllowEditNotNull"]});
define("crm-setting/common/fieldmanage/config/config",["./_func","./_nulltype","./_operator","./_point","./_retype","./_fields","./_compare","./_autofields","./_fieldinfo","./_fieldtype","./_hidefields","./_object","./_saveparam","./_apiname","./_layout","./_areafields"],function(e,a,i){var p=e("./_func"),t=e("./_nulltype"),o=e("./_operator"),f=e("./_point"),n=e("./_retype"),l=e("./_fields"),d=e("./_compare"),r=e("./_autofields"),y=e("./_fieldinfo"),m=e("./_fieldtype"),s=e("./_hidefields"),c=e("./_object"),u=e("./_saveparam"),g=e("./_apiname"),h=e("./_layout"),e=e("./_areafields"),v={},b={},j={};_.each(c,function(e){v[e.apiname]=e.type,b[e.type]=e.name,j[e.type]=e.apiname}),i.exports={func:p,nulltype:t,operator:o,point:f,retype:n,fields:l,fieldtype:m,compare:d,autofields:r,fieldinfo:y,hidefields:s,object:c,saveparam:u,api2type:v,type2name:b,type2api:j,apiname:g,layout:h,areafields:e}});
define("crm-setting/common/fieldmanage/config/field_config",[],function(e,_,i){i.exports={SHOW_SYSTEM:["name","created_by","create_time","last_modified_by","last_modified_time","out_owner"],HIDE_PACKAGE:["relevant_team","lock_rule","life_status_before_invalid","lock_user","extend_obj_data_id"],PRESET_OBJECT_HIDE_PACKAGE:{AccountObj:["high_seas_id"]}}});
define("crm-setting/common/fieldmanage/config/filter_config",["./field_config.js"],function(e,t,n){function a(e,t){return(e=$.extend(!0,{config:{}},e)).config.display=+r(e,t.api_name),e.object_define_type=t.define_type,e.object_api_name=t.api_name,e}function r(e,t){var n=!1,r=e.api_name;return"system"==(e=e.define_type)?n=!_.contains(o.SHOW_SYSTEM,r):"package"===e&&(n=_.contains(o.HIDE_PACKAGE,r)),t&&!n&&(e=o.PRESET_OBJECT_HIDE_PACKAGE[t]||[],n=_.contains(e,r)),!n}function i(e,t,n,r){var a,i,o,c=[];return c="custom"==n?c.concat(u(e)):(n=l.object[t],t=u(e),n&&(a=n.filter_fields||[],i=n[e]||[],o=[],n.current_object&&n.current_object[r]&&(n=n.current_object[r])[e]&&(o=n[e]),c=c.concat(a,i,o)),c.concat(t))}function u(e){var t=[];return _.contains(["formula","rule","default_value","object_reference_filter","count_filter"],e)?t=["owner_department","last_modified_time","last_modified_by"]:_.contains(["button_filter","quote"],e)?t=["owner_department","lock_status"]:_.contains(["button_send_crm_remind"],e)?t=["last_modified_by","created_by","owner_department"]:_.contains(["button_add_field","layout_rule_result","layout_rule"],e)&&(t=["owner","owner_department","life_status","lock_status"]),t}var o=e("./field_config.js"),c={quote:["text","long_text","number","currency","date","select_one","select_many","phone_number","email","object_reference","true_or_false","percentile","time","date_time"],formula:{number:[4,5,6,7,"percentile","number","currency","date","time","date_time"],percentage:[4,5,6,7,"percentile","number","currency","date","time","date_time"],text:[2,3,4,5,6,7,8,14,18,19,20,"location","auto_number","url","true_or_false","email","phone_number","percentile","number","currency","date","time","date_time","select_one","text","long_text"],date:[4,5,6,7,"percentile","number","currency","date"],bool:[2,3,4,5,6,"url","true_or_false","percentile","email","date","time","date_time","number","currency","select_one","text","long_text"],phone:[18,"phone_number"],currency:[4,5,6,7,"percentile","number","currency","date"]}},l={common:{},object:{AccountObj:{formula:["Area"]},ProductObj:{formula:["Status"]}}};n.exports={getByQuote:function(e,t){var n=a(e,t);return!(!n.config.display||!1===n.is_active||!_.contains(c.quote,n.type)||_.contains(i("quote",t.api_name,t.define_type),e.api_name))},getByFormula:function(e,t,n,r,a){return this[_.isUndefined(e.api_name)?"_getByFormulaInPreObject":"_getByFormulaInMyObject"](e,t,n,r,a)},_getByFormulaInPreObject:function(e,t,n,r,a){if(e.IsVisible&&!e.RelationField&&!_.contains(i("formula",n.api_name,""),e.FieldName)){if(_.contains(c.formula[t],e.FieldType))return!0;if("text"==t){if(!r&&_.contains([21,23],e.FieldType))return!0}else if("date"==t){if(23==e.FieldType&&_.contains(["date","time","date_time"],e.ObjectAggregate.ReturnValueType)&&(r&&"SalesOrderObj"==n.api_name||!r))return!0}else if("number"==t||"percentage"==t){if(23==e.FieldType&&"number"==e.ObjectAggregate.ReturnValueType&&(r&&"SalesOrderObj"==n.api_name||!r))return!0;if(21==e.FieldType&&!r&&!a)return!0}}return!1},_getByFormulaInMyObject:function(e,t,n){var r;return e.notCheck?!!_.contains(c.formula[t],e.type):!(!(r=a(e,n)).config.display||!1===r.is_active||!_.contains(c.formula[t],r.type)||_.contains(i("formula",n.api_name,n.define_type),e.api_name))}}});
define("crm-setting/common/fieldmanage/config/relate_filter",[],function(e,n,t){var i=["VisitingObj"];t.exports={getByFormula:function(e,n){return e.RelationObjectType!=n.api_name&&!_.contains(i,e.RelationObjectType)}}});
define("crm-setting/common/fieldmanage/crumbpanel/crumbpanel",["./tpl-html"],function(t,i,s){var e=t("./tpl-html"),t=Backbone.View.extend({events:{"click .parent":"_onSub","click .son":"_onChoose","click .all":"_onAll","hover .ul-padding":"showTip"},initialize:function(t){this.opts=_.extend({wrapper:"",offset:[0,0],pos:"top",width:180,isNav:!0,zIndex:10},t),this.isInit=!1,this.setElement(t.wrapper),this.bindEvent()},show:function(){this.isInit||(this.$el.append(e({list:this.opts.options,isAll:!0,isNav:this.opts.isNav})),this._setPosition(),this._setSize(),this.isInit=!0),$(".crm-field-panel",this.$el).show()},hide:function(){$(".crm-field-panel",this.$el).hide()},bindEvent:function(){var i=this;$(document).on("click.panel"+i.cid,function(t){i.hide()})},showTip:function(t){},_setPosition:function(){"top"==this.opts.pos?$(".crm-field-panel",this.$el).css({bottom:this.opts.offset[0],left:this.opts.offset[1]}):"bottom"==this.opts.pos&&$(".crm-field-panel",this.$el).css({top:this.opts.offset[0],left:this.opts.offset[1]})},_setSize:function(){$(".crm-field-panel",this.$el).css({width:this.opts.width,"z-index":this.opts.zIndex})},_onSub:function(t){var i=$(t.currentTarget).index();i<0||(i=this.opts.options[i],this._list=i.list,$(".crm-field-panel ul",this.$el).html(e({list:this._list,isAll:!1})),$(".crm-field-panel .all",this.$el).addClass("all-withsub"),$(".crm-field-panel .nav",this.$el).append('<span class="left-arrow">'+i.label+"</span>"),t.stopPropagation())},_onAll:function(t){$(".crm-field-panel .left-arrow",this.$el).remove(),$(".crm-field-panel ul",this.$el).html(e({list:this.opts.options,isAll:!1})),$(".crm-field-panel .all",this.$el).removeClass("all-withsub"),this._list=this.opts.options,t.stopPropagation()},_onChoose:function(t){var t=$(t.currentTarget).data("value"),i=_.findWhere(this._list,{value:t});this.trigger("change",t,i)},destroy:function(){this.$el.off(),$(".crm-field-panel",this.$el).remove(),$(document).off("click.panel"+this.cid)}});s.exports=t});
define("crm-setting/common/fieldmanage/crumbpanel/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (isAll) {
                __p += ' <div class="crm-field-panel" > ';
                if (isNav) {
                    __p += ' <div class="nav"> <span class="all">' + ((__t = $t("全部")) == null ? "" : __t) + "</span> </div> ";
                }
                __p += ' <ul class="' + ((__t = isNav ? "ul-padding" : "") == null ? "" : __t) + '"> ';
            }
            __p += " ";
            if (list.length > 0) {
                __p += " ";
                _.each(list, function(item, index) {
                    __p += " <!-- ";
                    if (item.tip) {
                        __p += ' <div class="tip"> <div class="arrow"></div> <div class="content"> ';
                        _.each(item.tip, function(tip) {
                            __p += " <div>" + ((__t = tip.label) == null ? "" : __t) + ":" + ((__t = tip.value) == null ? "" : __t) + "</div> ";
                        });
                        __p += " </div> </div> ";
                    }
                    __p += ' --> <li class="' + ((__t = item.list ? "parent" : "son") == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '" data-content=""> ';
                    if (item.valueIsLabel) {
                        __p += " <span>" + ((__t = item.value) == null ? "" : __t) + "</span> ";
                    }
                    __p += " " + ((__t = item.label) == null ? "" : __t) + " <!-- ";
                    if (item.tip) {
                        __p += ' <div class="tip"> <div class="arrow"></div> <div class="content"> ';
                        _.each(item.tip, function(tip) {
                            __p += " <div>" + ((__t = tip.label) == null ? "" : __t) + ":" + ((__t = tip.value) == null ? "" : __t) + "</div> ";
                        });
                        __p += " </div> </div> ";
                    }
                    __p += " --> </li> ";
                });
                __p += " ";
            } else {
                __p += ' <div class="no-data">' + ((__t = $t("暂无选项")) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (isAll) {
                __p += " </ul> </div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/area/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="area-box-wrapper"> <div class="area-box"> <table class="mn-checkbox-box" width="490px"> <thead> <tr> <th width="236px"> <span>' + ((__t = $t("字段名称")) == null ? "" : __t) + '</span> </th> <th width="130px"> <span>' + ((__t = $t("启用")) == null ? "" : __t) + '</span> </th> <th width="130px"> <span>' + ((__t = $t("必填")) == null ? "" : __t) + "</span> </th> </tr> </thead> <tbody> ";
            _.each(areaConfig, function(item) {
                __p += ' <tr class="label" data-apiname="' + ((__t = item.apiName) == null ? "" : __t) + '"> <td> <span class="label">' + ((__t = item.label) == null ? "" : __t) + '</span> </td> <td> <span class="' + ((__t = item.isStart ? "mn-selected" : "") == null ? "" : __t) + ' mn-checkbox-item start" data-level="' + ((__t = item.level) == null ? "" : __t) + '"></span> </td> <td> <span class="' + ((__t = item.isMust ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = item.isStart ? "" : "disabled-selected") == null ? "" : __t) + ' mn-checkbox-item must" data-level="' + ((__t = item.level) == null ? "" : __t) + '"></span> </td> </tr> ';
            });
            __p += ' </tbody> </table> </div> <div class="area-field"> <div class="area-title">' + ((__t = $t("字段管理")) == null ? "" : __t) + "</div> ";
            _.each(areaConfig, function(item) {
                __p += ' <div class="af ' + ((__t = item.apiName) == null ? "" : __t) + '"> <div class="af-label">' + ((__t = item.fieldLabel) == null ? "" : __t) + '</div> <div class="input-wrapper"> <input class="b-g-ipt area-set-input" value="' + ((__t = item.label) == null ? "" : __t) + '" data-param="' + ((__t = item.apiName) == null ? "" : __t) + '"> <div class="input-err"></div> </div> </div> ';
            });
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/area/view",["../common/view","./tpl-html","../../config/config"],function(e,t,a){var s=e("../common/view"),i=e("./tpl-html"),l=e("../../config/config").areafields,e=s.Model.extend({initialize:function(){s.Model.prototype.initialize.apply(this,arguments);var t=this;_.each(l,function(e){t.set(e.apiName,JSON.stringify(e))})},preFormatData:function(){var a,s=this,e=_.keys(l);_.each(e,function(e){var t=JSON.parse(s.get(e));(a=s.collection.get(s.get(e+"Id")))&&a.set("IsVisible",t.isStart),a&&a.set("IsNotNull",t.isMust),a&&a.set("FieldCaption",t.label)})},getOtherModelByField:function(t,a){return _.filter(this.collection.models,function(e){return e.get(t)==a})[0]},getData:function(){return this.preFormatData(),s.Model.prototype.getData.call(this)}}),n=s.extend({events:{"click .area-box .mn-checkbox-item.start":"_checkStartHandle","click .area-box .mn-checkbox-item.must":"_checkMustHandle","input .area-set-input":"_changInput"},renderCallback:function(){var e=this;this.model.areaConfig=$.extend(!0,{},l),this._bindOtherModel(),this.$specialBox.html(i({areaConfig:{country:JSON.parse(e.model.get("country")),province:JSON.parse(e.model.get("province")),city:JSON.parse(e.model.get("city")),district:JSON.parse(e.model.get("district")),location:JSON.parse(e.model.get("location")),address:JSON.parse(e.model.get("address"))}})),this.listenTo(this.model,"change",this._changeFieldLabel)},_bindOtherModel:function(){var t,a=this.model;a.get("isNew")?_.each(a.areaConfig,function(e){a[e.apiName+"Model"]=a.getOtherModelByField("FieldCaption",e.label),a.set(e.apiName+"Id",a[e.apiName+"Model"].cid)}):(t=a.get("Fields")&&JSON.parse(a.get("Fields")))&&_.each(a.areaConfig,function(e){a[e.apiName+"Model"]=a.getOtherModelByField("FieldName",t[e.param]),a[e.apiName+"Model"]&&(a.set(e.apiName+"Id",a[e.apiName+"Model"].cid),a.set(e.apiName,JSON.stringify(_.extend(JSON.parse(a.get(e.apiName)),{label:a[e.apiName+"Model"].get("FieldCaption"),isMust:a[e.apiName+"Model"].get("IsNotNull"),isStart:a[e.apiName+"Model"].get("IsVisible")}))))})},_changInput:function(e){var e=$(e.currentTarget),t=e.data("param"),e=e.val(),a=this.$(".area-field .af."+t+" .input-err");e?(this.model.areaConfig[t].label=e,this.model.set(t,JSON.stringify(_.extend(JSON.parse(this.model.get(t)),{label:e}))),a.text(""),this.editBeforeHandle()):a.text($t("必填属性不可为空"))},_changeFieldLabel:function(){var e=_.keys(l),t=this;_.each(e,function(e){this.$("tr[data-apiname="+e+"] .label").text(JSON.parse(t.model.get(e)).label)}),t.model.get("FieldCaption")&&this.editBeforeHandle()},_checkStartHandle:function(e){var e=$(e.currentTarget),t=e.data("level"),a=this;if(!e.hasClass("disabled-selected"))return t?a._checkLowerLevelStatus(t,"start",!e.hasClass("mn-selected")):(e.toggleClass("mn-selected"),e.hasClass("mn-selected")||a._getBrotherCheck(e,"must").addClass("disabled-selected").removeClass("mn-selected"),e.hasClass("mn-selected")&&a._getBrotherCheck(e,"must").removeClass("disabled-selected"),this.editBeforeHandle()),!1},editBeforeHandle:function(){var t=this;_.each(_.keys(l),function(e){t.model.set(e,JSON.stringify(_.extend(JSON.parse(t.model.get(e)),{label:$(".area-box tr[data-apiname="+e+"] .label").text(),isMust:!!$(".area-box tr[data-apiname="+e+"] .must").hasClass("mn-selected"),isStart:!!$(".area-box tr[data-apiname="+e+"] .start").hasClass("mn-selected")})))}),this.editAfterHandle()},_checkLowerLevelStatus:function(t,a,s){var e=_.filter($(".area-box .mn-checkbox-item."+a),function(e){return $(e).data("level")}),i=this;t&&(_.each(e,function(e){s&&$(e).data("level")<=t&&($(e).addClass("mn-selected"),"start"==a)&&i._getBrotherCheck($(e),"must").removeClass("disabled-selected"),!s&&$(e).data("level")>=t&&($(e).removeClass("mn-selected"),"start"==a)&&i._getBrotherCheck($(e),"must").addClass("disabled-selected").removeClass("mn-selected")}),this.editBeforeHandle())},_checkMustHandle:function(e){var e=$(e.currentTarget),t=e.data("level");return e.hasClass("disabled-selected")||(t?this._checkLowerLevelStatus(t,"must",!e.hasClass("mn-selected")):(e.toggleClass("mn-selected"),this.editBeforeHandle())),!1},_getBrotherCheck:function(e,t){return $(e).closest("td").siblings("").find("."+t)}});n.Model=e,a.exports=n});
define("crm-setting/common/fieldmanage/editmod/autonum/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="autonum-ipt ' + ((__t = IsAllowNumToIpt ? "" : "b-g-hide") == null ? "" : __t) + " " + ((__t = IsCodeField ? "iscodefield" : "") == null ? "" : __t) + " " + ((__t = NotAllowChange ? "disabled" : "") == null ? "" : __t) + '"> <p class="change-btn"></p> <p class="tip">' + ((__t = $t("切换自增编号")) == null ? "" : __t) + '</p> </div> <div class="auto-num-box ' + ((__t = IsCodeField ? "" : "b-g-hide") == null ? "" : __t) + '"> <!-- <h3 class="set-tit autonum-tit">编号设置</h3> --> <!-- <div class="autonum-line"></div> --> <div class="autonum-prew autonum-item"> <label class="set-field-name" style="display:inline-block;">' + ((__t = $t("编号预览")) == null ? "" : __t) + '</label> <div class="crm-ico-qus qus-box" style="display:inline-block; vertical-align:top;"> <div class="qus-box-item"> <em class="sanjiao"></em> <span class="qus-desc">' + ((__t = $t("自动编号前后缀描述")) == null ? "" : __t) + '</span> </div> </div> <p title="01">01</p> </div> <div class="autonum-item b-g-clear"> <label class="set-field-name" style="display:inline-block;">' + ((__t = $t("前缀")) == null ? "" : __t) + '</label> <div class="insert-time" style="display:inline-block"> <span class="btn">' + ((__t = $t("插入时间")) == null ? "" : __t) + '</span> <ul style="z-index:10"> <li data-value="-{yyyy}">' + ((__t = $t("年份")) == null ? "" : __t) + '<em>{yyyy}</em></li> <li data-value="-{mm}">' + ((__t = $t("月份")) == null ? "" : __t) + '<em>{mm}</em></li> <li data-value="-{dd}">' + ((__t = $t("日期")) == null ? "" : __t) + '<em>{dd}</em></li> </ul> </div> <div class="insert-field" style="display:inline-block;" data-widgets="panel1"> <span class="btn">' + ((__t = $t("插入字段")) == null ? "" : __t) + '</span> <div class="panel"></div> </div> <input type="text" value="' + ((__t = CodeRule.Prefix) == null ? "" : __t) + '" class="b-g-ipt prefix-ipt" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" data-param="CodeRule.Prefix"/> <div class="input-err hide"></div> </div> <div class="autonum-item b-g-clear"> <label class="set-field-name" style="display:inline-block;">' + ((__t = $t("后缀")) == null ? "" : __t) + '</label> <div class="insert-time" style="display:inline-block;"> <span class="btn">' + ((__t = $t("插入时间")) == null ? "" : __t) + '</span> <ul style="z-index:10"> <li data-value="-{yyyy}">' + ((__t = $t("年份")) == null ? "" : __t) + '<em>{yyyy}</em></li> <li data-value="-{mm}">' + ((__t = $t("月份")) == null ? "" : __t) + '<em>{mm}</em></li> <li data-value="-{dd}">' + ((__t = $t("日期")) == null ? "" : __t) + '<em>{dd}</em></li> </ul> </div> <div class="insert-field" style="display:inline-block;" data-widgets="panel2"> <span class="btn">' + ((__t = $t("插入字段")) == null ? "" : __t) + '</span> <div class="panel"></div> </div> <input type="text" value="' + ((__t = CodeRule.Postfix) == null ? "" : __t) + '" class="b-g-ipt postfix-ipt" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" data-param="CodeRule.Postfix"/> <div class="input-err hide"></div> </div> <div class="autonum-item" > <label class="set-field-name auto-num-digit" style="display:inline-block;">' + ((__t = $t("编号位数")) == null ? "" : __t) + '</label> <div class="crm-ico-qus qus-box" style="display:inline-block; vertical-align:top;"> <div class="qus-box-item"> <em class="sanjiao"></em> <span class="qus-tit">' + ((__t = $t("编号位数")) == null ? "" : __t) + '</span> <span class="qus-desc">' + ((__t = $t("当编号最大值超过编号位数限制时编号支持继续增加不受位数控制")) == null ? "" : __t) + '</span> </div> </div> <input type="text" maxlength="2" value="' + ((__t = isEmpty(CodeRule) ? 2 : CodeRule.MinLength) == null ? "" : __t) + '" class="b-g-ipt minlength-ipt" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" data-param="CodeRule.MinLength"/> </div> <div class="autonum-item" > <label class="set-field-name auto-num-start" style="display:inline-block;">' + ((__t = $t("编号起始值")) == null ? "" : __t) + '</label> <div class="crm-ico-qus qus-box" style="display:inline-block; vertical-align:top;"> <div class="qus-box-item"> <em class="sanjiao"></em> <span class="qus-tit">' + ((__t = $t("编号起始值生效规则")) == null ? "" : __t) + '</span> <span class="qus-desc">' + ((__t = $t("编号起始值只在重新计算时生效")) == null ? "" : __t) + '</span> </div> </div> <input type="text" maxlength="9" value="' + ((__t = isEmpty(CodeRule) ? 1 : CodeRule.StartNumber) == null ? "" : __t) + '" class="b-g-ipt startnumber-ipt" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" data-param="CodeRule.StartNumber"/> </div> <div class="autonum-item" > <label class="set-field-name" style="display:inline-block;">' + ((__t = $t("编号重新计算规则")) == null ? "" : __t) + '</label> <div class="crm-ico-qus qus-box" style="display:inline-block; vertical-align:top;"> <div class="qus-box-item"> <em class="sanjiao"></em> <span class="qus-tit">' + ((__t = $t("重新计算规则")) == null ? "" : __t) + '</span> <span class="qus-desc">' + ((__t = $t("1选项发生改变时会触发重新计算2按选择的选项条件进行重新计算")) == null ? "" : __t) + '</span> <span class="qus-desc">' + ((__t = $t("自增编号自增说明")) == null ? "" : __t) + '</span> </div> </div> <div class="auto-rule-select"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/autonum/view",["../common/view","crm-modules/common/util","./tpl-html","../../crumbpanel/crumbpanel","../../fetch","crm-widget/select/select"],function(e,t,i){var n=e("../common/view"),l=e("crm-modules/common/util"),s=e("./tpl-html"),o=e("../../crumbpanel/crumbpanel"),r=e("../../fetch"),u=e("crm-widget/select/select"),e=n.Model.extend({setDefault:function(){this.set({CodeRule:{Prefix:"",StartNumber:1,MinLength:2,Postfix:"",ResettingRuleType:""},IsCodeField:!0,FieldType:2,ResetRuleType:"",InputError:""})},cusCheck:function(){var e;return this.get("InputError")?$t("请修改前缀或后缀中的相应错误"):""===(e=this.get("CodeRule")).MinLength||""===e.StartNumber?$t("编号起始值与编号位数必填"):"0"==e.StartNumber||"0"==e.MinLength?$t("编号起始值与编号位数不可为0"):""},getData:function(){var e=n.Model.prototype.getData.call(this),t=(e.CodeRule||(e.CodeRule={}),e.CodeRule);return t.MinLength||(t.MinLength=2),t.StartNumber||(t.StartNumber=1),_.isUndefined(t.Prefix)&&(t.Prefix=""),_.isUndefined(t.Postfix)&&(t.Postfix=""),(t.ResettingRuleType=this.get("ResetRuleType"))||t.ResettingRuleType,e}}),a=n.extend({timeRulesOpts:{0:{name:$t("不重计算"),value:"NONE"},1:{name:$t("按年月日重新计算"),value:"DAY"},2:{name:$t("按年月重新计算"),value:"MONTH"},3:{name:$t("按年重新计算"),value:"YEAR"}},events:{"click .autonum-ipt .change-btn":"_setAutoNumHandle","click .insert-time li":"_onInserTime","mouseenter .insert-field":"_onShowFieldPanel","mouseleave .insert-field":"_onHideFieldPanel"},renderCallback:function(){var i;2<this.prop||(i=this,CRM.util.getConfigValues(["23"]).then(function(e){var t=i.model;i.$specialBox.html(s({isEmpty:_.isEmpty,CodeRule:t.get("CodeRule"),IsCodeField:t.get("IsCodeField"),IsAllowNumToIpt:t.get("IsAllowNumToIpt"),NotAllowChange:!!e[0].value&&-1<_.indexOf([5,9,11],t.get("OwnerType"))})),i._initReCalRuleSelect(),i._resetReCalRuleOpts(t.get("CodeRule").ResettingRuleType),i._initFieldPanel(),$(".set-desc",i.$el).html(t.get("IsCodeField")?$t("系统按照规则自动生成不可修改"):$t("适用于填写简短的文字，如")+$t("姓名")).prev().html(t.get("IsCodeField")?$t("自增编号"):$t("单行文本")),i._setAutoNumPrew()}))},bindCusEvents:function(){l.fixInputEvent(".prefix-ipt, .postfix-ipt",$.proxy(this.setActionByInput,this),this.$el),l.onlyAllowNum(".minlength-ipt",this.$el,!1,20,$.proxy(this.setActionByInput,this)),l.onlyAllowNum(".startnumber-ipt",this.$el,!1,null,$.proxy(this.setActionByInput,this))},unbindCusEvents:function(){l.offInputEvent(".prefix-ipt, .postfix-ipt, .minlength-ipt, .startnumber-ipt",this.$el)},clearError:function(e){var t=this.$(".input-err");t&&t.addClass("hide").html(""),this.model.set("InputError","")},setActionByInput:function(e){var t=e instanceof $?e:$(e.currentTarget);this.clearError(t),n.prototype.setActionByInput.call(this,e),this._checkTimeAction(t),this._checkFieldAction(t),this._checkMaxLength(t),this._setAutoNumPrew()},_checkTimeAction:function(e){this._preCheckTime(e),(e.hasClass("prefix-ipt")||e.hasClass("postfix-ipt"))&&this._resetReCalRuleOpts(this._widgets.ruleSelect&&this._widgets.ruleSelect.getValue())},_checkFieldAction:function(e){this._preCheckField(e)},_checkMaxLength:function(e){var t=e.val(),e=e.siblings(".input-err");40<t.replace(/\$[a-zA-Z]*\$|\{[yyyy|mm|dd]*\}/g,function(e){return""}).length&&(e.html($t("最多可填写40个字符")).removeClass("hide"),this.model.set("InputError",!0))},_resetReCalRuleOpts:function(e){var t=this,i=this.model.get("CodeRule"),i=i.Prefix+i.Postfix||"",n=[t.timeRulesOpts[0]],l=[];this.$(".input-err[class$=input-err]").length||(0<=i.indexOf("{yyyy}")&&l.push("{yyyy}"),0<=i.indexOf("{mm}")&&l.push("{mm}"),0<=i.indexOf("{dd}")&&l.push("{dd}"),l.sort(),_.values(l).join("").replace(/{yyyy}/g,function(e){return n.push(t.timeRulesOpts[3]),e}).replace(/{mm}{yyyy}/g,function(e){return n.push(t.timeRulesOpts[2]),e}).replace(/{dd}{mm}{yyyy}/g,function(e){return n.push(t.timeRulesOpts[1]),e}),t._widgets.ruleSelect.resetOptions(n),i=_.findWhere(n,{value:e})?e:"NONE",t._widgets.ruleSelect.setValue(i,!0))},_preCheckTime:function(e){var t=this.model.get("CodeRule"),t=t.Prefix+t.Postfix||"",e=e.closest(".autonum-item").find(".input-err");/{yyyy}.*{yyyy}|{mm}.*{mm}|{dd}.*{dd}/g.test(t)&&(e.html($t("存在相同的时间变量")).removeClass("hide"),this.model.set("InputError",!0))},_preCheckField:function(e){var t=this.model.get("CodeRule"),t=t.Prefix+t.Postfix||"",i=e.closest(".autonum-item").find(".input-err"),n=t.match(/(\$[^.-]*?\$)/g)||[];n.sort(),n&&3<n.length&&(i.html($t("字段变量不能超过3个")).removeClass("hide"),this.model.set("InputError",!0));for(var l=0;n&&l<n.length-1;l++)n[l]==n[l+1]&&(i.html($t("存在相同的字段变量")).removeClass("hide"),this.model.set("InputError",!0))},_onShowFieldPanel:function(e){e=$(e.currentTarget).data("widgets");this._widgets[e]&&this._widgets[e].show()},_onHideFieldPanel:function(e){e=$(e.currentTarget).data("widgets");this._widgets[e]&&this._widgets[e].hide()},_initFieldPanel:function(){var i=this;this._getFieldSelectOptions(function(e){i.model.set("fieldPanelOpts",i._formatPanelOpts(e)),i._widgets.panel1||(i._widgets.panel1=new o({wrapper:$(".insert-field .panel",i.$el)[0],options:i.model.get("fieldPanelOpts"),offset:[32,0],pos:"bottom",zIndex:20}),i._widgets.panel1.on("change",function(e,t){i._insertField(this.$el,e)})),i._widgets.panel2||(i._widgets.panel2=new o({wrapper:$(".insert-field .panel",i.$el)[1],options:i.model.get("fieldPanelOpts"),offset:[32,0],pos:"bottom",zIndex:20}),i._widgets.panel2.on("change",function(e,t){i._insertField(this.$el,e)}))})},_getFieldSelectOptions:function(t){r.fetchFieldsByOwnertype(this.model.get("OwnerType"),function(e){e=_.filter(e,function(e){return 8==+e.FieldType&&!e.RelationField});t&&t(e)})},_formatPanelOpts:function(e){return _.map(e,function(e){return{label:e.FieldCaption,value:"-$"+e.FieldName+"$"}})},_initReCalRuleSelect:function(e){var i=this;i._widgets.ruleSelect=new u({$wrap:$(".auto-rule-select",this.$el),options:_.values(i.timeRulesOpts),defaultVal:e||"NONE"}),i._widgets.ruleSelect.on("change",function(e,t){i.model.set("ResetRuleType",e),i.editAfterHandle()}),i._widgets.ruleSelect.show()},_insertField:function(e,t){var e=e.closest(".autonum-item").find("input"),i=e.val(),t=i?t:t.replace("-","");e.val(i+t),this.setActionByInput(e)},_onInserTime:function(e){var e=$(e.currentTarget),t=e.attr("data-value"),e=e.closest(".autonum-item").find("input"),i=e.val(),t=i?t:t.replace("-","");e.val(i+t),this.setActionByInput(e)},_setAutoNumHandle:function(e){var t,i,e=$(e.currentTarget).closest(".autonum-ipt");e.hasClass("disabled")||(t=e.hasClass("iscodefield"),i={},this.model.set("IsCodeField",!t),e.toggleClass("iscodefield",!t),$(".set-desc",this.$el).html(t?$t("适用于填写简短的文字如姓名"):$t("系统按照规则自动生成不可修改")).prev().html(t?$t("单行文本"):$t("自增编号")),$(".auto-num-box",this.$el).toggle(!t),this.model.set("CodeRule",i=t?i:{Prefix:"",StartNumber:1,MinLength:2,Postfix:"",ResettingRuleType:""}),this.editAfterHandle())},_setAutoNumPrew:function(){var e=this.model.get("CodeRule"),t=this._formatAutoNum(e.Prefix||""),i=this._formatAutoNum(e.Postfix||""),n=e.MinLength||2,t=(t?t+"":"")+l.addZero(e.StartNumber||1,+n)+(i?""+i:"");this.$(".autonum-prew p").html(t).attr("title",t)},_formatAutoNum:function(e){var t=this,i=new Date;return e.replace(new RegExp("{yyyy}","g"),function(){return l.moment(i).get("year")}).replace(new RegExp("{mm}","g"),function(){return l.addZero(l.moment(i).get("month")+1)}).replace(new RegExp("{dd}","g"),function(){return l.addZero(l.moment(i).get("date"))}).replace(/\$(.*)\$/g,function(e){return t.getFieldOptName(e)})},getFieldOptName:function(e){var t=this.model.get("fieldPanelOpts"),i="",e=e.split("-");return _.each(e,function(e){var e=e.match(/\$(.*)?\$/g);e&&(e=e&&e[0]&&_.findWhere(t,{value:"-"+e[0]}),i+=e&&e.label+"-")}),i&&i.substr(0,i.length-1)}});a.Model=e,i.exports=a});
define("crm-setting/common/fieldmanage/editmod/calculate/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="calculate-box"> <!-- <div class="line"></div> --> <h4>' + ((__t = $t("计算设置")) == null ? "" : __t) + '<a href="javascript:;" class="j-setFormula btn-floatright">' + ((__t = $t("设置")) == null ? "" : __t) + "</a></h4> ";
            if (!isFromOther) {
                __p += ' <div class="field-item"> <label>' + ((__t = $t("返回值类型")) == null ? "" : __t) + '</label> <div class="return-select"></div> </div> <div class="field-item ' + ((__t = _.contains([ "number", "percentage" ], returnType) ? "" : "crm-hide") == null ? "" : __t) + '"> <label>' + ((__t = $t("小数位数")) == null ? "" : __t) + '</label> <div class="point-select"></div> </div> ';
            }
            __p += ' <div class="field-item"> <label><em>*</em>' + ((__t = $t("计算公式")) == null ? "" : __t) + '</label> <textarea readonly="readonly">' + ((__t = Formula) == null ? "" : __t) + "</textarea> </div> ";
            if (!isFromOther) {
                __p += ' <div class="field-item"> <label>' + ((__t = $t("公式中字段为空值时")) == null ? "" : __t) + '</label> <div class="null-select"></div> </div> ';
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/calculate/view",["./tpl-html","../common/view","crm-widget/select/select","../../formula/formula","../../config/config"],function(e,t,l){var i=e("./tpl-html"),n=e("../common/view"),a=e("crm-widget/select/select"),o=e("../../formula/formula"),r=e("../../config/config"),e=n.Model.extend({setDefault:function(){this.set({ReturnValueType:"number",DefaultIsZero:!0,DecimalDigits:2})},cusCheck:function(){return this.get("CalFormula")?"":$t("请填写计算公式")}}),n=n.extend({events:{"click .j-setFormula":"showDialog"},renderCallback:function(){var e,t;2<this.prop||(e=this.model,t=this.getRenderData(),this.$specialBox.html(i(t)),1==e.get("InitFrom")&&this._initSelects())},_initSelects:function(){var n=this,e=[{name:"_return",value:$t("数值"),param:"ReturnValueType",configKey:"retype"},{name:"_point",value:"0",param:"DecimalDigits",configKey:"point"},{name:"_null",value:$t("默认为零"),param:"DefaultIsZero",configKey:"nulltype"}];_.each(e,function(e){var t=e.name.slice(1),l=$("."+t+"-select",n.$el),i=n.model.get(e.param),l=new a({$wrap:l,appendBody:!1,options:r[e.configKey],disabled:!0,defaultValue:"null"==t?i?0:1:i});n._widgets[e.name]=l})},showDialog:function(){var t=this,l=t.model,e=t.getDialogOpts(),e=new o(e);e.show(),e.on("enter",function(e){t.model.set(e),$("textarea",t.$specialBox).val(e.CalFormula),1==l.get("InitFrom")&&($(".point-select",t.$specialBox).closest(".field-item").toggle(_.contains(["number","percentage"],e.ReturnValueType)),t._widgets._return.setValue(e.ReturnValueType),t._widgets._point.setValue(e.DecimalDigits),t._widgets._null.setValue(e.DefaultIsZero?0:1)),t.editAfterHandle()}),t._widgets.setformula=e},getRenderData:function(){var e=this.model;return{Formula:e.get("CalFormula"),returnType:e.get("ReturnValueType"),isFromOther:1<e.get("InitFrom")}},getDialogOpts:function(){var e=this.model;return{title:$t("计算设置"),isAdd:!e.get("UserDefinedFieldID"),isTip:1==e.get("InitFrom"),isHelp:1==e.get("InitFrom"),isResultSet:1==e.get("InitFrom"),isNullHandle:1==e.get("InitFrom"),isCheckBtn:1==e.get("InitFrom"),isSupportGlobalVar:1==e.get("InitFrom"),objectType:e.get("OwnerType"),calformula:e.get("CalFormula"),formula:e.get("Formula"),defaultIsZero:e.get("DefaultIsZero"),decimalDigits:e.get("DecimalDigits"),returnValueType:e.get("ReturnValueType"),fields:1<e.get("InitFrom")?e.collection.toJSON():null,curField:e.toJSON(),filterSelf:!0}}});n.Model=e,l.exports=n});
define("crm-setting/common/fieldmanage/editmod/cascade/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="cascade-box"> <label>' + ((__t = $t("设置选项")) == null ? "" : __t) + "</label> ";
            canRemoveFather = canRemove(options);
            __p += " ";
            _.each(options, function(item, pIndex) {
                __p += " ";
                if (!item.isDeleted) {
                    __p += ' <div class="cascade-item ' + ((__t = item.isnofold ? "" : "nofold") == null ? "" : __t) + '" data-id="' + ((__t = item.EnumDetailID) == null ? "" : __t) + '"> <div class="item-p"> <span data-index="' + ((__t = pIndex) == null ? "" : __t) + '" class="ico-fold j-fold"><em class="tip-fold">-</em><em class="tip-nofold">+</em></span> <input data-index="' + ((__t = pIndex) == null ? "" : __t) + '" maxlength="20" type="text" data-code="' + ((__t = item.ItemCode) == null ? "" : __t) + '" value="' + ((__t = item.ItemName || "") == null ? "" : __t) + '" class="b-g-ipt" placeholder="' + ((__t = $t("一级选项")) == null ? "" : __t) + '"/> <span data-index="' + ((__t = pIndex) == null ? "" : __t) + '" class="add-option">+</span> ';
                    if (canRemoveFather) {
                        __p += ' <span data-index="' + ((__t = pIndex) == null ? "" : __t) + '" class="del-option">-</span> ';
                    }
                    __p += " </div> ";
                    var canRemoveChild = canRemove(item.Children);
                    __p += " ";
                    _.each(item.Children, function(cItem, cIndex) {
                        __p += " ";
                        if (!cItem.isDeleted) {
                            __p += ' <div class="item-c ' + ((__t = item.isNew ? "new-item" : "") == null ? "" : __t) + '" data-id="' + ((__t = cItem.EnumDetailID) == null ? "" : __t) + '"> <span data-index="' + ((__t = pIndex + "-" + cIndex) == null ? "" : __t) + '" class="ico-line"></span> <input data-index="' + ((__t = pIndex + "-" + cIndex) == null ? "" : __t) + '" maxlength="20" type="text" data-code="' + ((__t = cItem.ItemCode) == null ? "" : __t) + '" value="' + ((__t = cItem.ItemName || "") == null ? "" : __t) + '" class="b-g-ipt" placeholder="' + ((__t = $t("二级选项")) == null ? "" : __t) + '"/> <span data-index="' + ((__t = pIndex + "-" + cIndex) == null ? "" : __t) + '" class="add-option">+</span> ';
                            if (canRemoveChild) {
                                __p += ' <span data-index="' + ((__t = pIndex + "-" + cIndex) == null ? "" : __t) + '" class="del-option">-</span> ';
                            }
                            __p += " </div> ";
                        }
                        __p += " ";
                    });
                    __p += " </div> ";
                }
                __p += " ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/cascade/view",["./tpl-html","../common/view","crm-modules/common/util"],function(e,t,a){var i=e("./tpl-html"),n=e("../common/view"),c=e("crm-modules/common/util"),e=n.Model.extend({setDefault:function(){this.set({IsAllowEditOption:!0})},cusCheck:function(){var t=0,e=this.get("EnumDetails");return this.get("IsAllowEditOption")&&(_.each(e,function(e){!e.ItemName||0==e.Children.length?t++:_.each(e.Children,function(e){e.ItemName||t++})}),0<t)?$t("有选项未填写"):""},getData:function(){var e=n.Model.prototype.getData.call(this);return this.get("IsAllowEditOption")&&(e.ModifyEnums=this.getEnumDetails()),e},getEnumDetails:function(){var i=1,n=[],e=this.toJSON();return _.each(e.EnumDetails,function(e){var t={EditFlag:e.isNew?1:e.isDeleted?3:2,ItemCode:e.ItemCode||0,ItemOrder:i++,EnumDetailID:e.EnumDetailID||"",ItemName:e.ItemName||"",isNew:e.isNew},a=[];_.each(e.Children,function(e,t){e.ItemName&&a.push({EditFlag:e.isNew?1:e.isDeleted?3:2,ItemCode:e.ItemCode||0,ItemOrder:100+t,EnumDetailID:e.EnumDetailID||"",ItemName:e.ItemName,isNew:e.isNew})}),t.Children=a,n.push(t)}),this._filterCascadeSubmitData(n,e.BackupEnumDetails||[])},_filterCascadeSubmitData:function(e,t){return function i(e,n,d){return _.filter(e,function(e){var t,a;return!(!e.isNew&&(3==e.EditFlag?(d&&_.each(e.Children,function(e){e.EditFlag=3}),0):!(t=_.findWhere(n,{EnumDetailID:e.EnumDetailID}))||(d?(a=i(e.Children,t.Children),e.Children=a,e.ItemName===t.ItemName&&!a[0]):t.ItemName===e.ItemName)))})}(e,t,!0)}}),d=n.extend({events:{"click .cascade-item .add-option":"_addCascadeItem","click .cascade-item .del-option":"_removeCascade","click .j-fold":"_foldCascadeHandle","blur .cascade-item input":"_setCascadeValue"},renderCallback:function(){2<this.prop||(this._cloneCascadeEnumDetails(),this.model.get("IsAllowEditOption")&&this.renderCascade())},renderCascade:function(){var e=this.model.get("EnumDetails");_.each(e,function(e,t){e.Children&&0!=e.Children.length||(e.Children=[{isNew:!0}])}),this.$specialBox.html(i({options:e||this._getDefaultCascadeEnumDetails(),canAdd:this._canAddCascade,canRemove:this._canRemoveCascade}))},_canRemoveCascade:function(e){var t;return!!_.isArray(e)&&(t=_.where(e,{isDeleted:!0}),1<e.length-t.length)},_canAddCascade:function(e){var t;return!!_.isArray(e)&&(t=_.where(e,{isDeleted:!0}),e.length-t.length<300)},_getDefaultCascadeEnumDetails:function(){return[{isNew:!0,isnofold:!0,Children:[{isNew:!0}]}]},_cloneCascadeEnumDetails:function(){var i,e=this.model.get("EnumDetails");e&&e[0]?(i=[],_.each(e,function(e){var t=_.pick(e,"ItemCode","EnumDetailID","ItemName"),a=[];_.each(e.Children,function(e){a.push(_.pick(e,"ItemCode","EnumDetailID","ItemName"))}),t.Children=a,i.push(t)}),this.cascadeDataBackup=i,this.model.set("BackupEnumDetails",i)):(e=this._getDefaultCascadeEnumDetails(),this.model.set("EnumDetails",e))},_getActiveCascadeObj:function(e){var e=($(e).data("index")+"").split("-"),t=+e[0],a=e[1]?+e[1]:0,i=this.model.get("EnumDetails");return{pindex:t,cindex:a,pData:i[t],cData:e[1]?i[t].Children:[],type:e[1]?"child":"father",data:i}},_addCascadeItem:function(e){var e=this._getActiveCascadeObj(e.target),t=$t("最多添加50条选项")+"！",a=this._getDefaultCascadeEnumDetails()[0];if("father"===e.type){if(!this._canAddCascade(e.data))return void c.alert(t);_.each(e.data,function(e){e.isnofold=!1}),e.data.splice(e.pindex+1,0,a)}else{if(!this._canAddCascade(e.cData))return void c.alert(t);e.cData.splice(e.cindex+1,0,a.Children[0])}this.renderCascade(),this.editAfterHandle()},_removeCascade:function(e){var e=this._getActiveCascadeObj(e.target),t=$t("至少保留一条选项");if("father"===e.type){if(!this._canRemoveCascade(e.data))return void c.alert(t);var a=e.data[e.pindex];a.isNew?e.data.splice(e.pindex,1):a.isDeleted=!0}else{if(!this._canRemoveCascade(e.cData))return void c.alert(t);a=e.cData[e.cindex];a.isNew?e.cData.splice(e.cindex,1):a.isDeleted=!0}this.renderCascade(),this.editAfterHandle()},_foldCascadeHandle:function(e){var t=this._getActiveCascadeObj(e.currentTarget);_.each(t.data,function(e){e!==t.pData&&(e.isnofold=!1)}),t.pData.isnofold=!t.pData.isnofold,this.renderCascade()},_setCascadeValue:function(e){var t,a=this,i=a._getActiveCascadeObj(e.target),n=$.trim(e.target.value),d=$t("选项名称已存在");"father"===i.type?(t=n&&_.findWhere(i.data,{ItemName:n}))&&t!==i.pData?(c.alert(d),e.target.value=i.pData.ItemName||""):i.pData.ItemName=n:(t=n&&_.findWhere(i.cData,{ItemName:n}))&&t!==i.cData[i.cindex]?(c.alert(d),e.target.value=i.cData[i.cindex].ItemName||""):i.cData[i.cindex].ItemName=n,a.editAfterHandle()}});d.Model=e,a.exports=d});
define("crm-setting/common/fieldmanage/editmod/common/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var fieldCaption = _.escape(FieldCaption);
            __p += ' <div class="set-box"> <h3 class="set-tit">' + ((__t = FieldName === "data_own_department" ? $t("部门") : field.name) == null ? "" : __t) + '</h3> <p class="set-desc">' + ((__t = FieldName === "data_own_department" ? "" : field.desc) == null ? "" : __t) + '</p> <div class="field-item field-item-name ' + ((__t = FieldProperty != 2 ? "disabled" : "") == null ? "" : __t) + '"> <label class="set-field-name">' + ((__t = $t("字段名称")) == null ? "" : __t) + '</label> <input type="text" maxlength="100" class="b-g-ipt set-name set-input ' + ((__t = FieldProperty != 2 ? "b-g-ipt-disabled" : "") == null ? "" : __t) + '" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" value="' + ((__t = fieldCaption) == null ? "" : __t) + '" ' + ((__t = FieldProperty != 2 ? "disabled" : "") == null ? "" : __t) + ' data-param="FieldCaption"/> </div> <div class="field-item field-item-default ' + ((__t = isWithDefault ? "" : "crm-hide") == null ? "" : __t) + '"> <label class="set-field-name">' + ((__t = $t("默认值")) == null ? "" : __t) + '</label> <textarea data-param="DefaultValue" class="set-default" readonly="readonly">' + ((__t = DefaultValue) == null ? "" : __t) + '</textarea> <a href="javascript:;" class="j-insert i-default-btn ' + ((__t = isDefaultDisabled ? "insert-disabled" : "") == null ? "" : __t) + '">' + ((__t = $t("插入字段或计算公式")) == null ? "" : __t) + "</a> </div> ";
            if (_.indexOf([ 25, "group" ], FieldType) == -1) {
                __p += ' <div class="field-item mn-radio-box set-editnull-radio ' + ((__t = RenderFieldType != 1 && RenderFieldType != 21 && RenderFieldType != 24 && !RelationField ? "" : "b-g-hide") == null ? "" : __t) + '" set-attr="IsNotNull"> <label>' + ((__t = $t("是否必填")) == null ? "" : __t) + '</label> <span class="mn-radio-item ' + ((__t = IsAllowEditNotNull ? "" : "disabled-selected") == null ? "" : __t) + " " + ((__t = IsNotNull ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span><span class="radio-lb">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item ' + ((__t = IsAllowEditNotNull ? "" : "disabled-selected") == null ? "" : __t) + " " + ((__t = IsNotNull ? "" : "mn-selected") == null ? "" : __t) + '" data-value="0"></span><span class="radio-lb">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </div> <div class="field-item mn-radio-box set-editvisible-radio ' + ((__t = RenderFieldType != 1 && showHideSet ? "" : "b-g-hide") == null ? "" : __t) + '" set-attr="IsVisible"> <label>' + ((__t = $t("是否禁用")) == null ? "" : __t) + '</label> <span class="mn-radio-item ' + ((__t = !IsAllowHide || IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + " " + ((__t = IsVisible ? "" : "mn-selected") == null ? "" : __t) + '" data-value="0"></span><span class="radio-lb">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item ' + ((__t = !IsAllowHide || IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + " " + ((__t = IsVisible ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span><span class="radio-lb">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += ' <div class="field-item mn-radio-box set-watermark-radio ' + ((__t = RenderFieldType == 10 ? "" : "b-g-hide") == null ? "" : __t) + '" set-attr="IsWatermark"> <label>' + ((__t = $t("是否仅水印拍照")) == null ? "" : __t) + '</label> <span class="mn-radio-item ' + ((__t = IsWatermark ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span><span class="radio-lb">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item ' + ((__t = IsWatermark ? "" : "mn-selected") == null ? "" : __t) + '" data-value="0"></span><span class="radio-lb">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </div> <div class="special-field-box"></div> <div class="set-layout-box"> <!-- .b-g-crm .mn-checkbox-box .mn-selected, .b-g-crm .mn-checkbox-box .mn-selected:hover --> ';
            if (isNew && setFieldLayout && fieldType != 1) {
                __p += ' <table class="mn-checkbox-box set-layout-table" style="width:490px;"> <thead> <tr> <th width="160px" style="text-align: center;"> <span class="th-tit">' + ((__t = $t("布局")) == null ? "" : __t) + '</span> </th> <th width="110px"> <span class="th-tit"> ';
                if (EditFlag === 1) {
                    __p += ' <span class="mn-checkbox-item mn-selected show j-checkAll-type " data-con="show"></span> ';
                } else if (EditFlag === 2) {
                    __p += ' <span class="mn-checkbox-item ' + ((__t = !allShow ? "" : "mn-selected") == null ? "" : __t) + " show j-checkAll-type " + ((__t = IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + '" data-con="show"></span> ';
                }
                __p += " <span>" + ((__t = $t("是否展示")) == null ? "" : __t) + '</span> </span> </th> <th width="110px"> <span class="th-tit"> ';
                if (_.indexOf([ 20, 21, 24, 23, 25 ], RenderFieldType) != -1) {
                    __p += ' <span class="mn-checkbox-item required j-checkAll-type ' + ((__t = !allRequired ? "" : "mn-selected") == null ? "" : __t) + ' disabled-selected " data-con="required"></span> ';
                } else {
                    __p += ' <span class="mn-checkbox-item required j-checkAll-type ' + ((__t = !allRequired ? "" : "mn-selected") == null ? "" : __t) + " " + ((__t = IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + ' " data-con="required"></span> ';
                }
                __p += " <span>" + ((__t = $t("是否必填")) == null ? "" : __t) + '</span> </span> </th> <th width="110px"> <span class="th-tit"> ';
                if (_.indexOf([ 20, 21, 24 ], RenderFieldType) != -1) {
                    __p += ' <span class="mn-checkbox-item readonly j-checkAll-type ' + ((__t = !allReadOnly ? "" : "mn-selected") == null ? "" : __t) + ' disabled-selected mn-selected" data-con="readonly"></span> ';
                } else if (_.indexOf([ 23, 25 ], RenderFieldType) != -1) {
                    __p += ' <span class="mn-checkbox-item readonly j-checkAll-type ' + ((__t = !allReadOnly ? "" : "mn-selected") == null ? "" : __t) + ' disabled-selected " data-con="readonly"></span> ';
                } else {
                    __p += ' <span class="mn-checkbox-item readonly j-checkAll-type ' + ((__t = !allReadOnly ? "" : "mn-selected") == null ? "" : __t) + " " + ((__t = IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + ' " data-con="readonly"></span> ';
                }
                __p += " <span>" + ((__t = $t("是否只读")) == null ? "" : __t) + "</span> </span> </th> </tr> </thead> <tbody> ";
                _.each(LayoutConfig, function(layout, index) {
                    __p += ' <tr data-apiname="' + ((__t = layout.layout_api_name) == null ? "" : __t) + '" data-num="' + ((__t = index) == null ? "" : __t) + '" data-name="' + ((__t = layout.layout_label) == null ? "" : __t) + '"> <td width="160px"> <span class="th-tit">' + ((__t = layout.layout_label) == null ? "" : __t) + '</span> </td> <td width="110px" class="left-td"> <span class="th-tit"> ';
                    if (EditFlag === 1) {
                        __p += ' <span class="mn-checkbox-item mn-selected show j-check " data-con="show" data-num="' + ((__t = index) == null ? "" : __t) + '"></span> ';
                    } else if (EditFlag === 2) {
                        __p += ' <span class="mn-checkbox-item ' + ((__t = !layout.is_show ? "" : "mn-selected") == null ? "" : __t) + " td-style j-check show " + ((__t = IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + '" data-con="show" data-num="' + ((__t = index) == null ? "" : __t) + '" ></span> ';
                    }
                    __p += ' </span> </td> <td width="110px" class="left-td"> <span class="th-tit"> ';
                    if (_.indexOf([ 20, 21, 24, 23, 25 ], RenderFieldType) != -1) {
                        __p += ' <span class="mn-checkbox-item required j-check ' + ((__t = !layout.is_required ? "" : "mn-selected") == null ? "" : __t) + ' disabled-selected " data-con="required"></span> ';
                    } else {
                        __p += ' <span class="mn-checkbox-item required j-check ' + ((__t = !layout.is_required ? "" : "mn-selected") == null ? "" : __t) + " " + ((__t = IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + ' " data-con="required"></span> ';
                    }
                    __p += ' </span> </td> <td width="110px" class="left-td"> <span class="th-tit"> ';
                    if (_.indexOf([ 20, 21, 24 ], RenderFieldType) != -1) {
                        __p += ' <span class="mn-checkbox-item readonly j-check ' + ((__t = !layout.is_readonly ? "" : "mn-selected") == null ? "" : __t) + ' disabled-selected mn-selected" data-con="readonly"></span> ';
                    } else if (_.indexOf([ 23, 25 ], RenderFieldType) != -1) {
                        __p += ' <span class="mn-checkbox-item readonly j-check ' + ((__t = !layout.is_readonly ? "" : "mn-selected") == null ? "" : __t) + ' disabled-selected " data-con="readonly"></span> ';
                    } else {
                        __p += ' <span class="mn-checkbox-item readonly j-check ' + ((__t = !layout.is_readonly ? "" : "mn-selected") == null ? "" : __t) + " " + ((__t = IsNotNull ? "disabled-selected" : "") == null ? "" : __t) + '" data-con="readonly" data-num="' + ((__t = index) == null ? "" : __t) + '"></span> ';
                    }
                    __p += " </span> </td> </tr> ";
                });
                __p += " </tbody> </table> ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/common/view",["./tpl-html","../../config/config","crm-modules/common/util","../../formula/formula"],function(e,t,s){var l=e("./tpl-html"),n=e("../../config/config"),a=e("crm-modules/common/util"),o=e("../../formula/formula"),e=Backbone.Model.extend({initialize:function(){var e;Backbone.Model.prototype.initialize.apply(this,arguments),this.get("UserDefinedFieldID")||this.setDefault&&this.setDefault(),this.get("OwnerType")&&(e=CRM.config.objDes[this.get("OwnerType")])&&(this.apiname=e.apiName)},check:function(){var e="",t=this.get("FieldProperty");return e+=this._checkCaption(),t<3&&(e+=this.cusCheck()),e&&this.set("error",e),e},_checkCaption:function(){var e=this.get("FieldCaption");return 0==e.length?$t("请填写字段名称"):/\[|\]/g.test(e)?$t("字段名称有非法字符"):""},getData:function(){var e=this.toJSON(),e=_.pick(e,n.saveparam);return e.ModifyEnums=[],e},cusCheck:function(){return""},getWhoLookUpMe:function(){var e,t=this.get("FieldName");return!!t&&((e=this.collection.filter(function(e){return 0<=e.get("RelationField").indexOf(t)})).length&&e[0]||0)}}),i=Backbone.View.extend({events:{"click [set-attr] .mn-radio-item":"setActionByRadio","click .j-insert":"_showDialog","click .j-checkAll-type":"_checkHeadBtn","click .j-check":"_checkBtn"},initialize:function(e){this.setElement(e.$wrapper),this.super=i.prototype,this.events=_.extend({},this.super.events,this.events),this.options=_.extend({},this.super.options,this.options)},_checkHeadBtn:function(e){var e=$(e.currentTarget),t=e.data("con"),s=this.$(".set-layout-table .mn-checkbox-item."+t),i="show"!=t?"required"!=t?"required":"readonly":"",l=this.$(".set-layout-table .mn-checkbox-item.show"),i=i&&this.$(".set-layout-table .mn-checkbox-item."+i);return e.hasClass("disabled-selected")||(e.hasClass("mn-selected")?s.removeClass("mn-selected"):s.addClass("mn-selected"),"show"!=t?(e.hasClass("mn-selected")&&l.addClass("mn-selected"),e.hasClass("mn-selected")&&i.removeClass("mn-selected")):e.hasClass("mn-selected")||this.$(".set-layout-table .mn-checkbox-item").removeClass("mn-selected"),this._setLayoutValue(),this.editAfterHandle()),!1},_checkBtn:function(e){var t=this,e=$(e.currentTarget),s=e.data("con"),i=this.$(".j-checkAll-type."+s),l=this.$(".j-checkAll-type"),n=e.data("num"),a=e.closest("tr"),a=$(".mn-checkbox-item ",a),o=this.$(".set-layout-table td .mn-checkbox-item."+s),d="show"!=s?"required"!=s?"required":"readonly":"",r=d&&this.$(".set-layout-table .mn-checkbox-item."+d),c=$(".set-layout-table .mn-checkbox-item.show"),h=$(".set-layout-table .mn-checkbox-item.j-check.show"),u=d&&this.$(".set-layout-table td .mn-checkbox-item."+d).eq(n),d=d&&this.$(".set-layout-table th .mn-checkbox-item."+d),n=$(".set-layout-table .mn-checkbox-item.show.j-check").eq(n);return e.hasClass("disabled-selected")||(e.toggleClass("mn-selected"),"show"==s?!e.hasClass("mn-selected")&&a.removeClass("mn-selected")&&l.removeClass("mn-selected"):(t._hasSameClass(o)&&r.removeClass("mn-selected")&&c.addClass("mn-selected"),e.hasClass("mn-selected")&&u.removeClass("mn-selected")&&d.removeClass("mn-selected")&&n.addClass("mn-selected"),e.hasClass("mn-selected")||i.removeClass("mn-selected"),t._hasSameClass(h)&&c.addClass("mn-selected")),t._hasSameClass(o)&&i.addClass("mn-selected"),t._setLayoutValue(),t.editAfterHandle()),!1},_hasSameClass:function(e){return _.every(e,function(e){return $(e).hasClass("mn-selected")})},_setNoTable:function(e){e?(this.$(".set-layout-table .mn-checkbox-item.show").addClass("disabled-selected mn-selected"),this.$(".set-layout-table .mn-checkbox-item.required").addClass("disabled-selected mn-selected"),this.$(".set-layout-table .mn-checkbox-item.readonly").removeClass("mn-selected"),this.$(".set-layout-table .mn-checkbox-item.readonly").addClass("disabled-selected")):this.$(".set-layout-table .mn-checkbox-item").removeClass("disabled-selected")},_setLayoutValue:function(){var e=this.$(".set-layout-table tbody tr"),t=[];_.each(e,function(e){t.push({layout_api_name:$(e).data("apiname"),is_show:!!$(".mn-checkbox-item.show",e).hasClass("mn-selected"),is_required:!!$(".mn-checkbox-item.required",e).hasClass("mn-selected"),is_readonly:!!$(".mn-checkbox-item.readonly",e).hasClass("mn-selected"),layout_label:$(e).data("name")})}),this.model.set("LayoutConfig",JSON.stringify(t))},render:function(){var e=this,t=this.model,s=t.toJSON(),i=t.get("RenderFieldType"),i="number"==typeof i?{type:i}:{strType:i};e.$el.html(l(_.extend({},s,{isWithDefault:e.isAllDefault(),isDefaultDisabled:e.isDefaultDisabled(),showHideSet:e.options.isShowHideSet,field:_.findWhere(n.fields,i),IsAllowHide:!t.get("ExtendProp")||t.get("ExtendProp").IsAllowHide,EditFlag:t.get("EditFlag"),isNew:t.get("isNew"),allShow:e.checkAllType("is_show"),allRequired:e.checkAllType("is_required"),allReadOnly:e.checkAllType("is_readonly"),isRequired_one:!1,setFieldLayout:e.options.setFieldLayout,fieldType:e.model.get("RenderFieldType"),config:n.layout[e.model.get("RenderFieldType")],LayoutConfig:e.model.get("LayoutConfig")&&JSON.parse(e.model.get("LayoutConfig"))}))),e._widgets={},e._ajaxs={},e.prop=t.get("FieldProperty"),e.$specialBox=$(".special-field-box",e.$el),e.renderCallback&&e.renderCallback(),e.bindEvents()},checkAllType:function(e){e=_.pluck(this.model.get("LayoutConfig")&&JSON.parse(this.model.get("LayoutConfig")),e);return _.every(e,function(e){return e})},isAllDefault:function(){var e=this.model,t=e.get("RenderFieldType");return"ReceiverAddress"!=e.get("FieldName")&&"Employee|0"!=e.get("ReferRule")&&!(_.contains([6,13,9,16,20],e.get("OwnerType"))&&_.contains([7,18],t)||(!_.contains([11,28],e.get("OwnerType"))||!_.contains([2,3,4,5,6],t))&&(!_.contains([18],t)||2!=e.get("FieldProperty"))&&(!_.contains([7],t)||2!=e.get("FieldProperty")))},isDefaultDisabled:function(){var e=this.model;return 28==e.get("OwnerType")&&"Amount"==e.get("FieldName")},bindEvents:function(){this.listenTo(this.model,"destroy",this.destroy),a.fixInputEvent(".set-input",$.proxy(this.setActionByInput,this),this.$el),a.fixInputEvent(".set-default",$.proxy(this.setActionByInput,this),this.$el),this.bindCusEvents&&this.bindCusEvents()},unbindEvents:function(){a.offInputEvent(".set-name",this.$el),a.offInputEvent(".set-default",this.$el),this.unbindCusEvents&&this.unbindCusEvents()},setActionByRadio:function(e){var t=this.model,s=$(e.currentTarget),i=1==+s.attr("data-value"),l=s.closest(".field-item").attr("set-attr");if(l){if(s.hasClass("disabled-selected"))return!1;"IsNotNull"==l&&(this._setVisibleBtn(i),this._setNoTable(i),this._setLayoutValue()),"IsVisible"==l&&!i&&(t.get("ParentCascadeName")||0<t.get("CascadeFields").length)?(a.alert($t("该字段存在级联关系无法禁用")),e.preventDefault(),e.stopPropagation()):"IsVisible"===l&&!i&&16==t.get("RenderFieldType")&&t.getWhoLookUpMe()?(s=$t("不能禁用引用字段提示")+"：<br>"+t.getWhoLookUpMe().get("FieldCaption"),a.alert(s,function(){e.stopPropagation()}),e.stopPropagation()):this.setActionAfterByRadio(l,i)}},setActionAfterByRadio:function(e,t){this._setValueByKey(e,t),this.editAfterHandle()},setActionByInput:function(e){e=e instanceof $?e:$(e.currentTarget);this._setValueByKey(e.attr("data-param"),e.val().trim()),this.editAfterHandle()},_setValueByKey:function(e,s){var i,l,n=this,e=e.split("."),a=e.length;1==a?i=s:_.each(e,function(e,t){0==t?i=l=n.model.get(e):t==a-1?l[e]=s:l=l[e]||{}}),n.model.set(e[0],i)},editAfterHandle:function(){var e=this.model;e.set("EditFlag",2),e.trigger("error.hide"),e.trigger("btn.active")},_setVisibleBtn:function(e){var t=this.model,s=$(".set-editvisible-radio .mn-radio-item",this.$el);(t.get("ExtendProp")?t.get("ExtendProp").IsAllowHide:t.get("IsAllowHide"))&&t.get("FieldProperty")<3&&(e?(s.removeClass("mn-selected disabled-selected"),s.eq(1).trigger("click"),s.addClass("disabled-selected")):s.removeClass("disabled-selected"))},_showDialog:function(e){var t=this,s=t.model,i="";$(e.target).hasClass("insert-disabled")||(-1<[2,3].indexOf(s.get("RenderFieldType"))?i="text":-1<[4,5,6].indexOf(s.get("RenderFieldType"))?i="number":-1<[18].indexOf(s.get("RenderFieldType"))?i="phone":[7].indexOf(-1<s.get("RenderFieldType"))&&(i="date"),t._insert=new o({title:$t("插入设置"),isTip:!1,isHelp:!1,isResultSet:!1,isCheckBtn:!0,objectType:this.options.ownerType,calformula:t.model.get("DefaultValue"),returnValueType:i,isInsertSymbol:"phone"!=i,isInsertFunc:"phone"!=i,isDefault:!0,isSupportGlobalVar:"date"===i,isNullHandle:"date"==i,objectApiname:this.model.apiname,curField:s.toJSON(),lookupFields:t.model.collection.getLookupFields(),filterSelf:!0}),t._insert.show(),t._insert.on("enter",function(e){$(".field-item-default textarea").val(e.Formula),t.model.set("DefaultValue",e.CalFormula),t.editAfterHandle()}))},abortAjax:function(e){e=this._ajaxs[e];e&&e.abort&&e.abort()},destroy:function(){var s=this;s.unbindEvents(),s.stopListening(),_.each(this._widgets,function(e,t){e.destory&&e.destroy()}),_.each(this._ajaxs,function(e,t){s.abortAjax(t)}),s.cache=null,s._insert&&s._insert.destroy&&s._insert.destroy(),s.$el.off(),s.$el.empty()}});i.Model=e,s.exports=i});
define("crm-setting/common/fieldmanage/editmod/editmod",["./common/view","./radio/view","./quote/view","./number/view","./lookup/view","./cascade/view","./autonum/view","./calculate/view","./statistics/view","./area/view"],function(e,c,t){var a=e("./common/view"),i=e("./radio/view"),r=e("./quote/view"),n=e("./number/view"),s=e("./lookup/view"),u=e("./cascade/view"),o=e("./autonum/view"),w=e("./calculate/view"),v=e("./statistics/view"),m=e("./area/view");t.exports={getModel:function(e){return this.getView(e).Model},getView:function(e){switch(e){case 5:case 6:case"number":case"currency":return n;case 8:case 9:case"select_one":case"select_many":return i;case 14:case"multi_level_select_one":return u;case 16:return s;case 20:return o;case 21:case"count":return w;case 23:return v;case 24:return r;case 25:case"group":return m;default:return a}}}});
define("crm-setting/common/fieldmanage/editmod/lookup/config",[],function(e,o,i){i.exports={filterRelateBlack:{LeadsObj:["account_id"]}}});
define("crm-setting/common/fieldmanage/editmod/lookup/list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<ul> ";
            _.each(list, function(item) {
                __p += ' <li> <span class="l" title="' + ((__t = item[0]) == null ? "" : __t) + '">' + ((__t = item[0]) == null ? "" : __t) + '</span> <span class="c">' + ((__t = item[1]) == null ? "" : __t) + "</span> ";
                if (item[2]) {
                    __p += ' <span class="r" title="' + ((__t = item[2]) == null ? "" : __t) + '">' + ((__t = item[2]) == null ? "" : __t) + "</span> ";
                }
                __p += " </li> ";
            });
            __p += " </ul>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/lookup/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="lookup-box"> <div class="field-item mn-radio-box lookup-copy" set-attr="ObjectRelation.IsUnique"> <label>' + ((__t = $t("是否允许重复")) == null ? "" : __t) + '</label> <span class="mn-radio-item disabled-selected ' + ((__t = ObjectRelation.IsUnique ? "mn-selected" : "") == null ? "" : __t) + '" data-value="0"></span> <span class="radio-lb">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item disabled-selected ' + ((__t = ObjectRelation.IsUnique ? "" : "mn-selected") == null ? "" : __t) + '" data-value="1"></span> <span class="radio-lb">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </div> <div class="field-item"> <label><em>*</em>' + ((__t = $t("查找关联对象")) == null ? "" : __t) + '</label> <div class="lookup-select"></div> </div> <div class="field-item"> <label><em>*</em>' + ((__t = $t("相关列表标题")) == null ? "" : __t) + '</label> <input type="text" class="lookup-list-title b-g-ipt set-input ' + ((__t = FieldProperty == 1 ? "b-g-ipt-disabled" : "") == null ? "" : __t) + '" data-param="ObjectRelation.TargetRelationListLabel" value="' + ((__t = ObjectRelation.TargetRelationListLabel) == null ? "" : __t) + '" ' + ((__t = FieldProperty == 1 ? "disabled" : "") == null ? "" : __t) + '> </div> <div class="field-item mn-radio-box lookup-range range-box"> <label>' + ((__t = $t("可选择的数据范围")) == null ? "" : __t) + '</label> <div> <span class="mn-radio-item ' + ((__t = ObjectRelation.Wheres.length == 0 ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = isAllowFilter ? "" : "disabled-selected") == null ? "" : __t) + '" data-value="1"></span> <span class="radio-lb">' + ((__t = $t("全部")) == null ? "" : __t) + '</span> </div> <div> <span class="mn-radio-item ' + ((__t = ObjectRelation.Wheres.length == 0 ? "" : "mn-selected") == null ? "" : __t) + " " + ((__t = isAllowFilter ? "" : "disabled-selected") == null ? "" : __t) + '" data-value="0"></span> <span class="radio-lb">' + ((__t = $t("符合以下条件")) == null ? "" : __t) + '</span> <a href="javascript:;" class="j-setRange ' + ((__t = ObjectRelation.Wheres.length == 0 || !ObjectRelation.allowFilter ? "crm-hide" : "") == null ? "" : __t) + '">' + ((__t = $t("设置条件")) == null ? "" : __t) + '</a> </div> <div class="range-list"></div> </div> ';
            if (isDefault) {
                __p += ' <div class="field-item lookup-default"> <label>' + ((__t = $t("默认值")) == null ? "" : __t) + '</label> <div style="position: relative;padding-right: 33px;"> <input type="text" class="b-g-ipt" data-param="DefaultValue" value="' + ((__t = DefaultValue) == null ? "" : __t) + '"> <span class="insert j-insert">+</span> </div> </div> ';
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/lookup/view",["./tpl-html","./list-html","../common/view","../../fetch","../../config/config","./config","crm-modules/common/util","crm-widget/select/select","../../setfilter/setfilter","crm-modules/common/fieldfilter/fieldfilter","../../crumbpanel/crumbpanel"],function(e,t,i){var a=e("./tpl-html"),l=e("./list-html"),n=e("../common/view"),c=e("../../fetch"),o=e("../../config/config"),s=e("./config"),r=e("crm-modules/common/util"),p=e("crm-widget/select/select"),d=e("../../setfilter/setfilter"),u=e("crm-modules/common/fieldfilter/fieldfilter"),f=e("../../crumbpanel/crumbpanel"),e=n.Model.extend({setDefault:function(){this.set("ObjectRelation",{RelationObjectType:"",TargetRelationListName:o.apiname.getApiName("target_related_list"),TargetRelationListLabel:"",IsUnique:!0,Wheres:[]})},cusCheck:function(){var e,t;return 2!==this.get("FieldProperty")?"":(e=this.getObjectRelation("RelationObjectType"),t=this.getObjectRelation("TargetRelationListLabel"),e&&"--"!=e?t?"":$t("请填写列表标题"):$t("请选择关联对象"))},setObjectRelation:function(e,t){var i=this.get("ObjectRelation");i[e]=t,this.set("ObjectRelation",i)},getObjectRelation:function(e){return this.get("ObjectRelation")[e]}}),n=n.extend({events:{"click .j-setRange":"showDialog","click .range-box .mn-radio-item":"chooseHandle","click .j-insert":"_onPanel"},bindCusEvents:function(){r.fixInputEvent(".lookup-default input",$.proxy(this._clearDefault,this),this.$el)},renderCallback:function(){var t,i=this,l=i.model;2<i.prop||l.get("ObjectRelation")&&(t=CRM.config.objDes[i.model.get("OwnerType")],c.fetchFieldsByApiname(t.apiName,function(e){i.fields=e.fields||{},i.apiname=t.apiName;e=i.isAllowFilter(t.apiName,i.model.get("FieldName"));i.$specialBox.html(a({ObjectRelation:_.extend(l.get("ObjectRelation"),{allowFilter:e}),FieldProperty:l.get("FieldProperty"),UserDefinedFieldID:l.get("UserDefinedFieldID"),DefaultValue:l.get("DefaultValue"),isAllowFilter:e,isDefault:2==l.get("FieldProperty")})),i._initSelect(),i._setSelectOptions(),i._prevewDefault()},!0))},notAllowFields:["PriceBookID","PriceBookProductID","PromotionID"],notAllowObject:["InvoiceApplicationObj","ContractObj","VisitingObj"],isAllowFilter:function(e,t){return!_.contains(this.notAllowObject,e)&&!_.contains(this.notAllowFields,t)},_initSelect:function(){var i=this,l=i.model,e=$(".lookup-select",i.$el),e=new p({$wrap:e,appendBody:!1,placeHolder:$t("请选择"),options:[],disabled:!!l.get("UserDefinedFieldID")});e.on("change",function(e,t){0!=e.length&&(t?(l.getObjectRelation("RelationObjectType")!==t.value&&(i._widgets.panel&&(i._widgets.panel.destroy(),i._widgets.panel=null),$(".lookup-default input",i.$el).val(""),l.set("DefaultValue",""),l.setObjectRelation("RelationObjectType",t.value),l.setObjectRelation("Wheres",[])),i.preview(),i.editAfterHandle()):0<e.length&&i._setSelectTitle(e,this))}),i._widgets.lookup=e},_setSelectTitle:function(t,i){this.fetchAll(function(e){$(".tit-con input",i.$el).attr("placeholder",_.findWhere(e,{api_name:t}).display_name)})},_setSelectOptions:function(){var e=this,l=e.model,a=e._widgets.lookup;e.fetchAll(function(e){var t=[],i=l.getObjectRelation("RelationObjectType");_.each(e,function(e){_.contains(["object_bz6k6__c","object_ycWqJ__c","customer_account__c","PriceBookObj","PriceBookProductObj"],e.api_name)||1==l.get("OwnerType")&&_.contains(["OpportunityObj","ContactObj","AccountObj","PriceBookObj","NewOpportunityObj"],e.api_name)||11==l.get("OwnerType")&&"SalesOrderProductObj"==e.api_name||28==l.get("OwnerType")&&"SalesOrderObj"==e.api_name||"ProductObj"==l.apiname&&o.api2type[e.api_name]||t.push({value:e.api_name,name:e.display_name})}),a.resetOptions(t),a.setValue(i||"",!0),"PriceBookObj"!=i&&"PriceBookProductObj"!=i||a.$("input").val(o[i])})},fetchAll:function(t){var e=FS.getAppStore("crm-allobject");e?t&&t(e):r.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeList",data:{isDraft:!0,isIncludeFieldDescribe:!0,isIncludeSystemObj:!0,isIncludeUnActived:!1,packageName:"CRM"},success:function(e){0==e.Result.StatusCode&&(FS.setAppStore("crm-allobject",e.Value.objectDescribeList),t)&&t(e.Value.objectDescribeList)}},{errorAlertModel:1})},showDialog:function(){var i,e,t,l=this,a=this._widgets.lookup.getValue();a&&"--"!=a?(e=(i=l.model).getObjectRelation("Wheres"),t=new d,_.each(s.filterRelateBlack[l.apiname],function(e){delete l.fields[e]}),t.show({apiname:a,defaultValue:0<e.length?e[0].Filters:[],isRelate:!0,origin:{apiname:l.apiname,fields:l.fields},isSupportCascadingCheck:!1}),t.on("success",function(e,t){i.setObjectRelation("Wheres",[{Connector:"OR",Filters:e}]),l.preview(t),l.editAfterHandle()}),l._widgets.setfilter=t):r.alert($t("请先选择关联对象"))},preview:function(e){var t=this;e?$(".range-list",this.$el).html(l({list:e})):0<t.model.getObjectRelation("Wheres").length&&c.fetchFieldsByApiname(t.model.getObjectRelation("RelationObjectType"),function(e){e=new u({$wrapper:$(".hide-box",t.$el),apiname:t.model.getObjectRelation("RelationObjectType"),fields:e.fields,isInitFilter:!1,origin:{apiname:t.apiname,fields:t.fields}});$(".range-list",t.$el).html(l({list:e.getPreviewData(t.formatWheres())}))},!0)},chooseHandle:function(e){var t=$(e.currentTarget),i=1==+t.data("value");t.hasClass("disabled-selected")?e.stopPropagation():i||this.model.getObjectRelation("RelationObjectType")?(i&&(this.model.setObjectRelation("Wheres",[]),$(".range-list",this.$el).html("")),$(".range-box a",this.$el).toggle(!i),this.editAfterHandle()):(r.alert($t("请选择查找关联对象")),e.stopPropagation())},formatWheres:function(){var e=this.model.getObjectRelation("Wheres");return 0==e.length?[]:(e=e[0].Filters,_.map(e,function(e){return[e.FieldName,e.Operator,e.FieldValues]}))},_onPanel:function(t){var i=this;t.stopPropagation(),i.model.getObjectRelation("RelationObjectType")?i._widgets.panel?this._widgets.panel.show():i._getPanelOptions(function(e){e=new f({wrapper:$(t.currentTarget).closest(".field-item"),options:e,pos:"bottom",offset:[85,300<$(".set-box",this.$el).width()?129:58]});e.on("change",function(e,t){$(".lookup-default input",i.$el).val("["+t.label1+"]"),i.model.set("DefaultValue","["+e+"]")}),e.show(),i._widgets.panel=e}):r.alert($t("请先选择关联的对象"))},_getPanelOptions:function(l){var a=[],n=this.model,o=n.getObjectRelation("RelationObjectType");c.fetchFieldsByApiname(n.apiname,function(e){var t=[],i=[];_.each(e.fields,function(e){"object_reference"==e.type&&e.target_api_name!==n.apiname&&!1!==e.is_active&&(t.push(e.target_api_name),i.push(e))}),t=_.uniq(t),c.fetchFieldsByApinames(t,function(e){_.each(i,function(t){var i=[],l=_.findWhere(e,{api_name:t.target_api_name});_.each(l.fields,function(e){"object_reference"==e.type&&e.target_api_name==o&&e.target_api_name!=l.api_name&&i.push({label:e.label,label1:t.label+"."+e.label,value:[l.api_name,t.api_name+"__r",e.api_name].join(".")})}),0<i.length&&a.push({label:t.label,list:i})}),l&&l(a)})},!0)},_clearDefault:function(){this.model.set("DefaultValue","")},_prevewDefault:function(){var i=this,l=i.model.get("DefaultValue");l&&((l=l.slice(1,-1).split("."))[1]=l[1].replace("__r",""),l[1].charCodeAt(0)<97&&!/__c/g.test(l[1])?i._prevewOldDefault(l):c.fetchFieldsByApinames([i.model.apiname,l[0]],function(e){var t=_.findWhere(e,{api_name:i.model.apiname}),e=_.findWhere(e,{api_name:l[0]}),t=t.fields[l[1]],e=e.fields[l[2]];$(".lookup-default input",i.$el).val(t.label+"."+e.label)}))},_prevewOldDefault:function(t){var e=CRM.config.objDes[t[0].toLowerCase()],i=_.findWhere(this.model.collection.toJSON(),{FieldName:t[1]})||{FieldCaption:e.name};c.fetchFieldsByOwnertype(e.objectType,function(e){e=_.findWhere(e,{FieldName:t[2]});$(".lookup-default input",this.$el).val(i.FieldCaption+"."+e.FieldCaption)},!0)}});n.Model=e,i.exports=n});
define("crm-setting/common/fieldmanage/editmod/number/insert-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="insert-box"> <div class="insert-error err"></div> <div class="insert-box-left"> <a class="btn j-show-insert" href="javascript: ;">' + ((__t = $t("插入字段或运算符")) == null ? "" : __t) + "</a> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/number/view",["./insert-html","../common/view","crm-modules/common/util","crm-widget/select/select"],function(e,i,t){e("./insert-html");var l=e("../common/view"),s=(e("crm-modules/common/util"),e("crm-widget/select/select")),e=l.Model.extend({setDefault:function(){this.set("DecimalDigits",2)}}),l=l.extend({events:{"click .j-show-insert":"showInsert","click .insert-box .select-item":"_insertSymbolHandle","click .insert-box .check-btn":"_checkHandle"},renderCallback:function(){var e;"Discount"==this.model.get("FieldName")||null!=this&&null!=(e=this.options)&&e.formCusOrOppField||this._initPointSelect()},_initPointSelect:function(){var t=this,e=[],l=t.model,i='<div class="field-item field-point"><label>'+$t("小数位数")+'</label><div class="field-point-select"></div></div>';$(".field-item-name",t.$el).after(i),null==l.get("DecimalDigits")&&(l.set("DecimalDigits",2),t.editAfterHandle());for(var n=0;n<10;n++)e.push({value:n,name:n});i=new s({$wrap:$(".field-point-select",t.$el),options:e,defaultValue:l.get("DecimalDigits"),zIndex:2e3});i.on("change",function(e,i){l.set("DecimalDigits",+i.value),t.editAfterHandle()}),l.get("DecimalDigits")||i.setValue(l.get("DecimalDigits")),t._widgets.number_select=i}});l.Model=e,t.exports=l});
define("crm-setting/common/fieldmanage/editmod/quote/config",[],function(e,n,i){i.exports={getByQuote:function(e,n){i=e,o=n,(i=$.extend(!0,{config:{}},i)).config.display=+!void o.api_name,i.object_define_type=o.define_type,i.object_api_name=o.api_name;var i,o}}});
define("crm-setting/common/fieldmanage/editmod/quote/list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<ul class="obj-list cur-list crm-scroll" data-parent="' + ((__t = parentId) == null ? "" : __t) + '"> ';
            _.each(list, function(item, index) {
                __p += ' <li class="item ' + ((__t = parentId ? "son-item" : "parent-item") == null ? "" : __t) + '" data-relate="' + ((__t = item.relate) == null ? "" : __t) + '" data-value="' + ((__t = item.value) == null ? "" : __t) + '">' + __e(item.name) + "</li> ";
            });
            __p += " ";
            if (list.length == 0) {
                __p += " <p>" + ((__t = $t("没有可以引用的字段")) == null ? "" : __t) + "</p> ";
            }
            __p += " </ul>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/quote/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="quote-box"> <div class="quote-item"> <label><em>*</em>' + ((__t = $t("引用字段")) == null ? "" : __t) + '</label> <div class="select-quote"> <div class="title ' + ((__t = idDisabled ? "disabled-title" : "") == null ? "" : __t) + '">' + ((__t = defaultValue ? defaultValue : $t("请选择")) == null ? "" : __t) + '</div> <ul class="quote-list"> <li class="quote-list-first"><span style="color:#4080ff;" class="j-all all">' + ((__t = $t("全部")) == null ? "" : __t) + "</span></li> <li></li> </ul> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/quote/view",["./tpl-html","./list-html","../common/view","crm-modules/common/util","../../config/config","../../config/filter_config","../../fetch"],function(e,t,i){var s=e("./tpl-html"),d=e("./list-html"),l=e("../common/view"),c=(e("crm-modules/common/util"),e("../../config/config")),r=e("../../config/filter_config"),o=e("../../fetch"),e=l.Model.extend({cusCheck:function(){return this.get("RelationField")?"":$t("请选择引用字段")}}),l=l.extend({events:{"click .parent-item":"showSon","click .son-item":"sureHandle","click .select-quote .title":"toggleSelect","click .j-all":"showAllObjtype"},renderCallback:function(){var t,i,l,n,a=this,o=this.model,e=o.get("RelationField");2<a.prop||(e?(e=(t=e.split("."))[1].slice(0,-3),i=!!o.get("UserDefinedFieldID"),l={AccountObj:$t("客户"),OpportunityObj:$t("商机"),ContactObj:$t("联系人")},n=_.findWhere(o.collection.getLookupFields(),{FieldName:e}),"SalesOrderObj"==t[0]&&(n={FieldCaption:$t("订单")}),a._getFields(t[0],function(e){e=c.api2type[t[0]]?_.findWhere(e,{FieldName:t[2]}):e.fields[t[2]],e=n&&e?n.FieldCaption+" > "+(e.FieldCaption||e.label):e&&"LeadsObj"==o.apiname?l[t[0]]+" > "+(e.FieldCaption||e.label):"";$(".special-field-box",a.$el).html(s({idDisabled:i,defaultValue:e}))}),i||a.renderList(),$(".set-editnull-radio .mn-radio-item",this.$el).addClass("disabled-selected")):($(".special-field-box",a.$el).html(s({idDisabled:!1,defaultValue:""})),a.renderList()))},renderList:function(){var t=this,n=t.model,a=n.collection.getLookupFields();o.fetchRelationConfig(n.get("OwnerType"),function(e){var l=[];_.each(e,function(e){var t,i;-1<["PriceBookObj","PriceBookProductObj","QuoteObj","QuoteLinesObj"].indexOf(e.RelationObjectType)||c.api2type[e.RelationObjectType]!=n.get("OwnerType")&&(t=_.findWhere(a,{FieldName:e.RelationFieldName}),i=CRM.config.objDes[e.RelationObjectType.toLowerCase()],l.push({relate:e.RelationObjectType,name:t?t.FieldCaption:i?i.name:"--",value:e.RelationFieldName}))}),$(".quote-list li",t.$el).eq(1).append(d({list:l,parentId:""})),t.relateObj=e},!0)},bindCusEvents:function(){var t=this;$("body").on("click.quotelist",function(e){0==$(e.target).closest(".select-quote").length&&$(".quote-list",t.$el).hide()})},unbindCusEvents:function(){$("body").off("click.quotelist")},showSon:function(e){var l=this,n=$(e.currentTarget),a=n.attr("data-relate"),o=(c.fieldtype,["DataOwnDepartment"]);l._getFields(a,function(t){var i=[],e=$(".quote-list li",this.$el);c.api2type[a]?_.each(t,function(e){!e.IsVisible||e.RelationField||!_.contains([2,3,4,5,6,7,8,9,16,18,19],e.FieldType)||_.contains(o,e.FieldName)||i.push({value:e.FieldName,name:e.FieldCaption})}):_.each(t.fields,function(e){r.getByQuote(e,t)&&i.push({name:e.label,value:e.api_name})}),$(".cur-list",this.$el).removeClass("cur-list"),e.eq(1).append(d({list:i,parentId:[a,n.data("value")].join(".")})),e.eq(0).append("<em>></em><span>"+n.html()+"</span>"),l.relateField=t})},_getFields:function(e,t){e||t&&t([]),c.api2type[e]?o.fetchFieldsByOwnertype(c.api2type[e],t):o.fetchFieldsByApiname(e,t)},sureHandle:function(e){var e=$(e.currentTarget),t=e.attr("data-value"),i=e.closest("ul").data("parent").split(".")[0],i=_.findWhere(this.relateField,{FieldName:t})||o.getFieldsImmediate(i).fields[t],l=$(".quote-list-first span",this.$el).last().html()+" > "+e.html();$(".quote-list",this.$el).hide(),$(".select-quote .title",this.$el).html(l),this.model.set("RelationField",e.closest("ul").attr("data-parent")+"__r."+t),this.model.set("FieldType",i.FieldType||c.fieldtype[i.type]||2),this.model.set("EnumName",i.EnumName||""),this.editAfterHandle()},toggleSelect:function(e){$(e.currentTarget).hasClass("disabled-title")||$(".quote-list",this.$el).toggle()},showAllObjtype:function(e){$(".obj-list",this.$el).removeClass("cur-list").eq(0).addClass("cur-list").siblings().remove(),$(".quote-list-first .all",this.$el).siblings().remove()}});l.Model=e,i.exports=l});
define("crm-setting/common/fieldmanage/editmod/radio/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (IsAllowEditOption) {
                __p += " " + ((__t = addStatus ? "" : '<div class="options-box">') == null ? "" : __t) + " ";
                if (!addStatus) {
                    __p += ' <div class="options-box-tit"> <label>' + ((__t = $t("选项设置")) == null ? "" : __t) + '</label> <div class="batch-add"> <a href="javascript: ;">' + ((__t = $t("批量新建")) == null ? "" : __t) + '</a> <div class="batch-add-box"> <span class="batch-add-tit">' + ((__t = $t("每行可输入一个选项")) == null ? "" : __t) + '</span> <textarea class="batch-textarea"></textarea> <div class="batch-add-btns"> <span class="b-g-btn">' + ((__t = $t("完 成")) == null ? "" : __t) + '</span> <span class="b-g-btn-cancel">' + ((__t = $t("取 消")) == null ? "" : __t) + "</span> </div> </div> </div> </div> ";
                }
                __p += " ";
                _.each(EnumDetailsOption, function(item) {
                    __p += ' <div class="field-item"> ';
                    if (!item.IsDelete) {
                        __p += " ";
                        if (item.IsSysItem) {
                            __p += ' <input disabled="disabled" data-code="' + ((__t = item.ItemCode) == null ? "" : __t) + '" type="text" class="b-g-ipt" value="' + ((__t = item.ItemName) == null ? "" : __t) + '" placeholder="' + ((__t = item.placeholder) == null ? "" : __t) + '"/> ';
                        } else {
                            __p += ' <input data-code="' + ((__t = item.ItemCode) == null ? "" : __t) + '" type="text" class="b-g-ipt" value="' + ((__t = item.ItemName) == null ? "" : __t) + '" placeholder="' + ((__t = item.placeholder) == null ? "" : __t) + '"/> ';
                        }
                        __p += ' <span class="add-option">+</span> ';
                        if (!item.IsSysItem) {
                            __p += ' <span class="del-option">-</span> ';
                        }
                        __p += " ";
                        if (item.ItemName.length > 50) {
                            __p += ' <div class="error crm-ico-error">' + ((__t = $t("字数超过限制")) == null ? "" : __t) + "</div> ";
                        }
                        __p += " ";
                    }
                    __p += " </div> ";
                });
                __p += " " + ((__t = addStatus ? "" : "</div>") == null ? "" : __t) + " ";
            }
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/radio/view",["./tpl-html","../common/view","crm-modules/common/util"],function(t,e,i){var l=t("./tpl-html"),n=t("../common/view"),d=t("crm-modules/common/util"),t=n.Model.extend({initialize:function(){n.Model.prototype.initialize.call(this),this.cacheEnums()},cacheEnums:function(){var i={},t=this.get("EnumDetails");(!t||t.length<=0)&&this.setDefault(),_.each(t,function(t,e){t.ItemOrder=e+1;e=t.EnumDetailID||t.ItemOrder;i[e]={ItemName:t.ItemName||t.label,ItemCode:t.ItemCode||t.value,ItemOrder:t.ItemOrder||t.resource_bundle_key}}),this.cache_enums=i},setDefault:function(){var t=this.get("EnumDetails")||this.get("options");t&&t.length<=0?this.set({EnumDetails:[{ItemName:"",ItemCode:999,EditFlag:1,placeholder:$t("选项一")},{ItemName:"",ItemCode:999,EditFlag:1,placeholder:$t("选项二")}],IsAllowEditOption:!0}):this.set({EnumDetails:t})},cusCheck:function(){return this.get("IsAllowEditOption")?this.checkOptions():""},checkOptions:function(){var i,n,t="",e=this.get("EnumDetails");function l(t){switch(i=i||[],0==(n=n||[]).length&&_.each(e,function(t){var e=t.ItemName;3!==t.EditFlag&&(n.push(e),e)&&i.push(e)}),t){case 0:return i.length<2;case 1:return i.length!==n.length;case 2:return _.uniq(i).length!==i.length;case 3:return 0<_.filter(n,function(t){return 50<t.length})}}return l(0)?t+=$t("选项至少填写两项"):l(1)?t+=$t("有选项未填写"):l(2)?t+=$t("选项中有重复项"):l(3)&&(t+=$t("选项字数超出限制")),t},getData:function(){var t,e=n.Model.prototype.getData.call(this);return this.get("IsAllowEditOption")&&(t=this.getEnumDetails(),e.ModifyEnums=t.list,e.ModifyEnums2=t.list2),e},getEnumDetails:function(){var n=[],l=[],t=this.toJSON(),o=this.cache_enums,e=_.filter(t.EnumDetails,function(t){return 3==t.EditFlag&&t.EnumDetailID}),t=_.filter(t.EnumDetails,function(t){return 3!==t.EditFlag});return _.each(t,function(t,e){var i=o[t.EnumDetailID||t.ItemOrder];i&&i.ItemName==t.ItemName&&1!=t.EditFlag&&i.ItemOrder==e+1||n.push({EditFlag:i&&!t.EditFlag?2:t.EditFlag,ItemCode:t.ItemCode,ItemName:t.ItemName,EnumDetailID:t.EnumDetailID,ItemOrder:e+1}),l.push({EditFlag:t.EnumDetailID?2:t.EditFlag,ItemCode:t.ItemCode,ItemName:t.ItemName,EnumDetailID:t.EnumDetailID,ItemOrder:e+1})}),{list:n.concat(e),list2:l.concat(e)}},getEnumOptions:function(){var e=[],i=this.get("UserDefinedFieldID"),t=this.get("EnumDetails");return _.each(t,function(t){e.push({ItemName:t.ItemName,ItemCode:t.ItemCode,placeholder:i?$t("选项"):t.placeholder,IsSysItem:!!i&&t.IsSysItem,IsDelete:3==t.EditFlag,Children:_.map(t.Children,function(t){return _.pick(t,"ItemCode","EnumDetailID","ItemName")})})}),0==e.length&&(e=[{ItemName:"",ItemCode:999,EditFlag:1,placeholder:$t("选项一")},{ItemName:"",ItemCode:999,EditFlag:1,placeholder:$t("选项二")}],this.set("EnumDetails",e)),e}}),o=n.extend({events:{"click .options-box .add-option":"_addOptionHandle","click .options-box .batch-add a":"_toggleBatchAddHandle","click .options-box .b-g-btn-cancel":"_hideBatchAddHandle","click .options-box .b-g-btn":"_addMoreOptionHandle","click .options-box .del-option":"_delOptionHandle"},renderCallback:function(){var t;2<this.prop||(t=this.model,this.$specialBox.html(l({addStatus:!1,FieldType:t.get("RenderFieldType"),EnumDetailsOption:t.getEnumOptions(),IsAllowEditOption:t.get("IsAllowEditOption")})),this.$optionBox=$(".options-box",this.$el))},bindCusEvents:function(){d.fixInputEvent(".options-box input",$.proxy(this.changeFieldOptionHandle,this),this.$el),$("body").on("click.batchadd",function(t){0<$(t.target).closest(".batch-add").length||$(".batch-add-box",this.$el).hide()})},unbindCusEvents:function(){$("body").off("click.batchadd")},changeFieldOptionHandle:function(t){var t=$(t.currentTarget),e=t.closest(".field-item"),t=t.val().trim(),i=this.model.get("EnumDetails");50<t.length?0==e.find(".error").length&&e.append('<div class="error crm-ico-error">'+$t("字数超过限制")+"</div>"):e.find(".error").remove(),i[e.index()-1].ItemName=t,this.model.set("EnumDetails",i),this.model.trigger("checkbox.update"),this.editAfterHandle()},_addOptionHandle:function(t){t=$(t.currentTarget).closest(".field-item");1e3<=$(".field-item",this.$optionBox).not(".b-g-hide").length?d.alert($t("最多添加1000条选项")+"！"):this._addOptionToModel(t,{ItemName:"",placeholder:$t("新增选项"),EditFlag:1,ItemCode:999})},_addMoreOptionHandle:function(){var e=[],t=$(".batch-textarea",this.$el),i=t.val();i&&(i=_.uniq(i.split("\n")),_.each(i,function(t){(t=t.trim())&&e.push({ItemName:t,placeholder:$t("新增选项"),EditFlag:1,ItemCode:999})}),this._addOptionToModel($(".field-item",this.$el).last(),e)),this._toggleBatchAddHandle(),t.val("")},_addOptionToModel:function(t,e){var i=[],n=this.model.get("EnumDetails");_.isArray(e)?(n=n.concat(e),i=e):(n.splice(t.index(),0,e),i.push(e)),t.after(l({addStatus:!0,IsAllowEditOption:!0,EnumDetailsOption:i})),this.model.set("EnumDetails",n),this.editAfterHandle()},_toggleBatchAddHandle:function(t){$(".batch-add-box",this.$el).toggle(),t&&t.stopPropagation()},_hideBatchAddHandle:function(t){$(".batch-add-box",this.$el).hide(),t&&t.stopPropagation()},_delOptionHandle:function(t){var e,i,n=this,l=$(t.target).closest(".field-item"),o=n.model.get("FieldType");function a(){i[l.index()-1].EditFlag=3,n.model.set("EnumDetails",i),l.addClass("b-g-hide"),9==o&&n.model.trigger("checkbox.update"),n.editAfterHandle()}2==$(".options-box .field-item",n.$el).not(".b-g-hide").length?d.alert($t("选项至少保留两项")):999==(i=n.model.get("EnumDetails"))[l.index()-1].ItemCode||9==o?a():e=d.confirm($t("该选项已经应用到业务数据删除将清空业务数据确认删除"),$t("提示"),function(){a(),e.hide()})}});o.Model=t,i.exports=o});
define("crm-setting/common/fieldmanage/editmod/statistics/config",[],function(t,o,e){e.exports={commonFilter:["version"],objectFilter:{SalesOrderProductObj:["discount"],SalesOrderObj:["plan_payment_amount","payment_amount","receivable_amount","invoice_amount","refund_amount","returned_goods_amount","bill_money_to_confirm","payment_money_to_confirm","confirmed_delivery_date","confirmed_receive_date","delivered_amount_sum","discount"],ContactObj:["day_of_birth","year_of_birth","month_of_birth"]},objRelateFilter:{OpportunityObj:{ProductObj:["opportunity_id"],ContactObj:["opportunity_id"]}}}});
define("crm-setting/common/fieldmanage/editmod/statistics/list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<ul> ";
            _.each(list, function(item) {
                __p += ' <li> <span class="l" title="' + ((__t = item[0]) == null ? "" : __t) + '">' + ((__t = item[0]) == null ? "" : __t) + '</span> <span class="c">' + ((__t = item[1]) == null ? "" : __t) + "</span> ";
                if (item[2]) {
                    __p += ' <span class="r" title="' + ((__t = item[2]) == null ? "" : __t) + '">' + ((__t = item[2]) == null ? "" : __t) + "</span> ";
                }
                __p += " </li> ";
            });
            __p += " </ul>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/statistics/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="statistics-box"> <div class="master">' + ((__t = $t("主对象")) == null ? "" : __t) + "：<em>" + ((__t = name) == null ? "" : __t) + '</em></div> <div class="statis-item"> <label><em>*</em>' + ((__t = $t("关联对象")) == null ? "" : __t) + '</label> <div class="select-slave"></div> </div> <div class="statis-item"> <label><em>*</em>' + ((__t = $t("统计类型")) == null ? "" : __t) + '</label> <div class="select-stype"></div> <div class="tip"></div> </div> <div class="statis-item statis-item-field"> <label><em>*</em>' + ((__t = $t("字段汇总")) == null ? "" : __t) + '</label> <div class="select-field"></div> </div> <div class="statis-item"> <label>' + ((__t = $t("返回值类型")) == null ? "" : __t) + '</label> <div class="select-rtype"></div> </div> <div class="statis-item"> <label>' + ((__t = $t("小数位数")) == null ? "" : __t) + '</label> <div class="select-point"></div> </div> <div class="field-item mn-radio-box range-box"> <label>' + ((__t = $t("可选择的数据范围")) == null ? "" : __t) + '</label> <div> <span class="mn-radio-item ' + ((__t = Wheres.length > 0 ? "" : "mn-selected") == null ? "" : __t) + '" data-value="1"></span> <span class="radio-lb">' + ((__t = $t("全部")) == null ? "" : __t) + '</span> </div> <div> <span class="mn-radio-item ' + ((__t = Wheres.length > 0 ? "mn-selected" : "") == null ? "" : __t) + '" data-value="0"></span> <span class="radio-lb">' + ((__t = $t("符合以下条件")) == null ? "" : __t) + '</span> <a href="javascript:;" class="j-setRange ' + ((__t = Wheres.length == 0 ? "crm-hide" : "") == null ? "" : __t) + '">' + ((__t = $t("设置条件")) == null ? "" : __t) + '</a> </div> <div class="range-list"></div> <div class="hide-box crm-hide"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/editmod/statistics/view",["./tpl-html","./list-html","../../fetch","../common/view","./config","crm-modules/common/util","crm-widget/select/select","../../setfilter/setfilter","crm-modules/common/fieldfilter/fieldfilter"],function(e,t,i){var a=e("./tpl-html"),l=e("./list-html"),n=e("../../fetch"),s=e("../common/view"),g=e("./config"),r=e("crm-modules/common/util"),c=e("crm-widget/select/select"),d=e("../../setfilter/setfilter"),o=e("crm-modules/common/fieldfilter/fieldfilter"),m=["number","currency","percentile"],p=["date","time","date_time"],e=s.Model.extend({setDefault:function(){this.set("ObjectAggregate",{AggregateType:"",AggregateFieldName:"",AggregateObject:"",RelationFieldName:"",RelationListName:"",Wheres:[],ReturnValueType:""}),this.set("DecimalDigits",2),this.set("IsAllowEditNotNull",!1),"currency"==this.getObjectAggregate("ReturnValueType")&&(this.setObjectAggregate("ReturnValueType","number"),this.set("EditFlag",2))},cusCheck:function(){var e;return this.get("ObjectAggregate")?(e=this.getObjectAggregate("AggregateType"))?this.getObjectAggregate("AggregateObject")?this.getObjectAggregate("AggregateFieldName")||"count"==e?"":$t("字段汇总不能为空"):$t("关联对象不能为空"):$t("统计类型不能为空"):""},getObjectAggregate:function(e){return this.get("ObjectAggregate")[e]},setObjectAggregate:function(e,t){var i=this.get("ObjectAggregate");i[e]=t,this.set("ObjectAggregate",i)}}),s=s.extend({events:{"click .j-setRange":"showDialog","click .range-box .mn-radio-item":"chooseHandle"},renderCallback:function(){var e;2<this.prop||(e=this.model,this.$specialBox.html(a(_.extend({name:CRM.config.objDes[e.get("OwnerType")].name},e.get("ObjectAggregate")))),this._initSlaveSelect(),this._initStypeSelect(),this._initFieldSelect(),this._initRtypeSelect(),this._initPointSelect(),this._setSelectDisabled(),$(".statis-item-field em",this.$el).toggle("count"!=e.getObjectAggregate("AggregateType")))},bindCusEvents:function(){this.listenTo(this.model,"change:AggregateObject",this._initFieldSelect)},_initSlaveSelect:function(){var i=this;i._createSelect({$wrap:$(".select-slave",i.$el),parentNode:i.$el,placeHolder:$t("请选择"),disabled:!!i.model.get("UserDefinedFieldID"),options:[]},function(e){(i._widgets.slave=e).on("change",function(e,t){0!=e.length&&(i.model.setObjectAggregate("AggregateFieldName",""),i.model.setObjectAggregate("ReturnValueType",""),i.model.setObjectAggregate("AggregateObject",t.object),i.model.setObjectAggregate("RelationFieldName",t.field),i.model.setObjectAggregate("RelationListName",t.listApiname),i.model.setObjectAggregate("Wheres",[]),i._setFieldOptions(),i.editAfterHandle())}),_.contains([11,12],i.model.get("OwnerType"))&&_.contains(["ShouldReturnMoney","TradeMoney"],i.model.get("FieldName"))&&e.setDisable(!0)}),i._setSlaveOptions()},_setSlaveOptions:function(){var l=this;l.fetchObjectRelation(function(e){l.relation=e;var t=l._widgets.slave,i=l.model.getObjectAggregate("AggregateObject"),a=l.model.getObjectAggregate("RelationFieldName"),e=l._parseObjectList(e);0<e.length&&t.resetOptions(e),i&&t.setValue(i+"-"+a)})},_parseObjectList:function(e){var i=this.model.apiname,a={},l=[],s=g.objRelateFilter[i];return e?(_.each(e.detailDescribeList,function(t){var e;t.api_name!=i&&(e=_.where(t.fields,{type:"master_detail",target_api_name:i}),_.each(e,function(e){void 0!==a[t.api_name]?a[t.api_name]++:a[t.api_name]=1,l.push({name:t.display_name,value:t.api_name+"-"+e.api_name,object:t.api_name,field:e.api_name,listLabel:e.target_related_list_label,listApiname:e.target_related_list_name})}))}),_.each(e.lookupDescribeList,function(t){var e;t.api_name!=i&&(e=_.where(t.fields,{type:"object_reference",target_api_name:i}),_.each(e,function(e){s&&_.contains(s[t.api_name]||[],e.api_name)||(void 0!==a[t.api_name]?a[t.api_name]++:a[t.api_name]=1,l.push({name:t.display_name,value:t.api_name+"-"+e.api_name,object:t.api_name,field:e.api_name,listLabel:e.target_related_list_label,listApiname:e.target_related_list_name}))}))}),_.each(l,function(e){1<a[e.object]&&(e.name+="("+e.listLabel+")")}),l):[]},_initStypeSelect:function(){var a=this,e=(a._createSelect({$wrap:$(".select-stype",a.$el),appendBody:!1,placeHolder:$t("请选择"),disabled:!!a.model.get("UserDefinedFieldID"),options:this._getStatisticType()},function(e){(a._widgets.stype=e).on("change",function(e){var t,i;0!=e.length&&(t=_.findWhere(a._getStatisticType(),{value:e}),a.model.setObjectAggregate("AggregateFieldName",""),a.model.setObjectAggregate("ReturnValueType",""),a.model.setObjectAggregate("AggregateType",e),$(".statis-item .tip").html(t.desc),$(".statis-item-field em",a.$el).toggle("count"!=e),t=a._widgets.field.options.$wrap.find(".crm-widget"),i=a._widgets.point.options.$wrap.find(".crm-widget"),"count"==e?(t.addClass("disabled"),i.addClass("disabled"),a._widgets.point.setValue(0,!0)):(t.removeClass("disabled"),i.removeClass("disabled")),a._setFieldOptions(),a.editAfterHandle())})}),a.model.getObjectAggregate("AggregateType"));e&&a._widgets.stype.setValue(e)},_getStatisticType:function(e){return[{value:"count",name:$t("COUNT（计数）"),desc:$t("统计数据的个数比如客户下面的联系人数量。")},{value:"sum",name:$t("SUM（求和）"),desc:$t("统计某个字段的和比如销售订单下产品的金额合计。")},{value:"min",name:$t("MIN（最小值）"),desc:$t("统计某个字段的最小值比如客户下最低的销售费用")},{value:"max",name:$t("MAX（最大值）"),desc:$t("统计某个字段的最大值比如客户下最大的商机金额。")},{value:"average",name:$t("AVERAGE（平均值）"),desc:$t("统计某个字段的平均值比如订单产品的平均折扣")}]},_setFieldOptions:function(){var a=this,e=this.model.getObjectAggregate("AggregateObject"),l=this.model.getObjectAggregate("AggregateType"),s=a.model.getObjectAggregate("AggregateFieldName");e&&l&&n.fetchFieldsByApiname(e,function(e){var t=[],i=[];a.cachefields=e.fields||{},i=-1<["min","max"].indexOf(l)?m.concat(p):m,_.each(e.fields,function(e){!_.contains(i,e.type)||_.contains(g.commonFilter,e.api_name)||_.contains(g.objectFilter[a.model.getObjectAggregate("AggregateObject")]||[],e.api_name)||t.push({value:e.api_name,name:e.label})}),a._widgets.field.resetOptions(t),s&&a._widgets.field.setValue(s,!0),a.preview()},!0),a._widgets.rtype&&("count"==l?(a._widgets.rtype.resetOptions([{name:$t("数值"),value:"number"}]),a._widgets.rtype.setValue("number",!0)):a._widgets.rtype.resetOptions([]))},_initFieldSelect:function(){var i=this;i._widgets.field||i._createSelect({$wrap:$(".select-field",i.$el),appendBody:!1,placeHolder:$t("请选择"),disabled:!!i.model.get("UserDefinedFieldID"),options:[]},function(e){(i._widgets.field=e).on("change",function(e,t){0!=e.length&&(i.model.setObjectAggregate("AggregateFieldName",e),i._setOptionsAfterFields(),i.editAfterHandle())})}),i._setFieldOptions()},_setSelectDisabled:function(){var t=this,e=t.model;"TotalMoney"==e.get("FieldName")&&11==e.get("OwnerType")&&_.each(["slave","stype","field","rtype","point"],function(e){t._widgets[e]&&t._widgets[e].setDisable(!0)})},_setOptionsAfterFields:function(){var e={date:$t("日期"),time:$t("时间"),date_time:$t("日期时间")},t=this.cachefields[this.model.getObjectAggregate("AggregateFieldName")],i=e[t.type]?t.type:"number";this._widgets.rtype.resetOptions([{name:e[t.type]||$t("数值"),value:i}]),this._widgets.rtype.setValue(i),this.model.setObjectAggregate("ReturnValueType",i)},_initPointSelect:function(){var i=this,t=i.model.get("DecimalDigits"),e=_.map([0,1,2,3,4,5,6,7,8,9],function(e){return{value:e,name:e}});i._createSelect({$wrap:$(".select-point",i.$el),appendBody:!1,placeHolder:$t("请选择"),options:e,disabled:!!i.model.get("UserDefinedFieldID")},function(e){_.isNumber(t)&&e.setValue(t),(i._widgets.point=e).on("change",function(e,t){i.model.set("DecimalDigits",e),i.editAfterHandle()})})},_initRtypeSelect:function(){var i=this;i.model.getObjectAggregate("ReturnValueType");i._createSelect({$wrap:$(".select-rtype",i.$el),appendBody:!1,disabled:!0,placeHolder:$t("请选择"),options:[]},function(e){(i._widgets.rtype=e).on("change",function(e,t){0!=e.length&&(i.model.setObjectAggregate("ReturnValueType",e),i.editAfterHandle())})})},_createSelect:function(e,t){e=new c(e);t&&t(e)},fetchObjectRelation:function(t){r.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findRelatedObjectList",data:{describeApiName:this.model.apiname,includeDetailList:!0,includeRefList:!0},success:function(e){0==e.Result.StatusCode?t&&t(e.Value):t&&t()}},{errorAlertModel:1})},showDialog:function(){var e,t,i=this,a=this._widgets.slave.getValue();a?(i._widgets.setfilter&&i._widgets.setfilter.destroy(),a=a.split("-")[0],e=new d,t=i.model.getObjectAggregate("Wheres"),_.findWhere(i.relation.detailDescribeList,{api_name:a})&&CRM.config.objDes[a.toLowerCase()]&&(delete i.cachefields.created_by,delete i.cachefields.create_time,delete i.cachefields.last_modified_time,delete i.cachefields.last_modified_by),"ReturnedGoodsInvoiceProductObj"==a&&delete i.cachefields.record_type,e.show({apiname:a,defaultValue:t[0]?t[0].Filters:[],fields:i.cachefields,isRelate:!0,from:"count",tip:"<div>"+$t("1有条件的统计字段将不出现在对象的新建和编辑界面详情页正常展示。")+"</div><div>"+$t("2过滤条件变更系统需对所有数据重新计算请勿频繁修改过滤条件。")+"</div>"}),e.on("success",function(e,t){i.model.setObjectAggregate("Wheres",[{Connector:"OR",Filters:e}]),i.preview(t),i.editAfterHandle()}),i._widgets.setfilter=e):r.alert($t("请先选择对象"))},preview:function(e){var t=this;e?$(".range-list",this.$el).html(l({list:e})):0<t.model.getObjectAggregate("Wheres").length&&(e=new o({$wrapper:$(".hide-box",t.$el),apiname:t.model.getObjectAggregate("AggregateObject"),fields:t.cachefields,isInitFilter:!1}),$(".range-list",t.$el).html(l({list:e.getPreviewData(t.formatWheres())})))},chooseHandle:function(e){var t=$(e.currentTarget),i=1==+t.data("value");t.hasClass("disabled-selected")?e.stopPropagation():i||this.model.getObjectAggregate("AggregateObject")?(i&&(this.model.setObjectAggregate("Wheres",[]),$(".range-list",this.$el).html("")),$(".range-box a",this.$el).toggle(!i),this.editAfterHandle()):(r.alert($t("请选择关联对象")),e.stopPropagation())},formatWheres:function(){var e=this.model.getObjectAggregate("Wheres");return 0==e.length?[]:(e=e[0].Filters,_.map(e,function(e){return[e.FieldName,e.Operator,e.FieldValues]}))}});s.Model=e,i.exports=s});
define("crm-setting/common/fieldmanage/fetch",["crm-modules/common/util"],function(e,t,i){var n=e("crm-modules/common/util");i.exports={cache:{fields:{},cpcd:{},relate:{}},getDefferd:function(e,t){return n.FHHApi({url:e,data:t},{errorAlertModel:1})},getDefferdByOwnertype:function(e){return this.getDefferd("/EM1HNCRM/API/v1/object/"+e+"/controller/DescribeLayout",{include_detail_describe:!0,include_layout:!1,apiname:e,recordType_apiName:"default__c"})},fetchFieldsByOwnertype:function(t,i,e){var n=FS.getAppStore("crm-fieldlist-"+t);if(e||!n)return this.getDefferdByOwnertype(t).done(function(e){0==e.Result.StatusCode&&(e=e.Value.objectDescribe.fields,FS.setAppStore("crm-fieldlist-"+t,e),i)&&i(e)});i&&i(n)},getLayoutByApiName:function(e){return this.getDefferd("/EM1HNCRM/API/v1/object/layout/service/findDetailLayoutList",{objectDescribeApiName:e})},getDefferdByApiname:function(e){return this.getDefferd("/EM1HNCRM/API/v1/object/describe/service/findDraftByApiName",{draft_apiname:e,include_layout:!1,layout_type:"detail"})},fetchFieldsByApiname:function(t,i,e){var n=this.cache.fields;if(!n[t]||e)return this.getDefferdByApiname(t).done(function(e){0==e.Result.StatusCode&&(n[t]=e.Value.objectDescribeDraft,i)&&i(n[t])});i&&i(n[t])},getDefferdByApinames:function(e){return this.getDefferd("/EM1HNCRM/API/v1/object/describe/service/findDescribeListByApiName",{describe_apiname_list:e})},fetchFieldsByApinames:function(e,t){var n=this.cache.fields;return this.getDefferdByApinames(e).done(function(i){0==i.Result.StatusCode&&(_.each(e,function(e,t){n[e]=i.Value.objectDescribeList[t]}),t)&&t(i.Value.objectDescribeList)})},getDefferdRelation:function(e){return this.getDefferd("/EM1HCRM/CRMRelation/GetObjectRelationConfigByObjectType",{ObjectType:e})},fetchRelationConfig:function(t,i,e){var n=this.cache.relate;n[t]&&!e?i&&i(n[t]):this.getDefferdRelation(t).done(function(e){0==e.Result.StatusCode&&(n[t]=_.reject(e.Value.ObjectRelationList,function(e){return-1<[2,11].indexOf(t)&&-1<["PaymentPlanObj","OrderPaymentObj","SaleActionObj"].indexOf(e.RelationObjectType)}),i)&&i(n[t])})},getFieldsImmediate:function(e){return isNaN(+e)?this.cache.fields[e]:FS.getAppStore("crm-fieldlist-"+e)}}});
define("crm-setting/common/fieldmanage/fieldmanage",["base-dnd","./view/field","./view/add","./editmod/editmod","./template/tpl-html","crm-modules/common/util","./collection/collection","base-modules/ui/scrollbar/scrollbar","./formula/formula"],function(e,t,i){var o=e("base-dnd"),s=e("./view/field"),l=e("./view/add"),n=e("./editmod/editmod"),r=e("./template/tpl-html"),d=e("crm-modules/common/util"),a=e("./collection/collection"),c=e("base-modules/ui/scrollbar/scrollbar"),e=(e("./formula/formula"),Backbone.View.extend({defaults:{from:0,ownerType:0,isSetRelate:!0,isSaveBtn:!0,fieldData:null,relateData:null,isRelateObj:!1,relateObjPos:1,addFilter:[],onlyInput:!1,setFieldLayout:!1,isShowHideSet:!0},events:{"click .j-fmitem":"showEditView","click .btns-box .b-g-btn":"saveFields"},initialize:function(e){this.options=$.extend(!0,{},this.defaults,e),this.$el.html(r(this.options)),this._renderAddView(),this._initScroll(),this._initProp(),this._initCollection()},_renderAddView:function(){var t=this,i=(t.options,t._add);i||((i=new l(_.pick(t.options,["isRelateObj","addFilter","onlyInput","relateObjPos"]))).on("add.field",function(e){t._coll.addData(e)&&(i.hide(),t.activeBtn()),t.trigger("add")}),i.render(),$(".fields-type-box",t.$el).html(i.$el),t._add=i)},_initScroll:function(){this._scrollbar=new c($(".scroll-el",this.$el))},_initProp:function(){this.fieldViews=[],this.changeNum=0,this.$presetBox=$(".preset-box",this.$el),this.$definedBox=$(".defined-box",this.$el),this.$customerBox=$(".cus-box",this.$el),this.$oppoBox=$(".oppo-box",this.$el),this.$editBox=$(".right .scroll-el-con",this.$el)},_initCollection:function(){this._coll=new a([],this.options),this.listenTo(this._coll,{add:this.addField,remove:this.removeEditView,reset:this.emptyView,"btn.active":this.activeBtn,"save.suc":this.clearChangeNum,"change:error":this._scrollToError})},addField:function(e){var t=+e.get("FieldProperty"),i=[this.$presetBox,this.$definedBox,this.$customerBox,this.$oppoBox][t-1],o=new s({model:e,isRelateObj:this.options.isRelateObj,from:this.options.from});e.get("UsedIn")&&!e.get("IsShow")||(o.render(),_.contains([3,4],t)&&i.find("h2").show(),i.find(".inner").append(o.$el),e.get("UserDefinedFieldID")||(o.$el.trigger("click"),$(".left .scroll-content").scrollTop(0).scrollTop(o.$el.offset().top-250)),1!=t&&this._createDnd(o.$el),this.fieldViews.push(o))},activeBtn:function(){this.changeNum++,$(".btns-box .b-g-btn",this.$el).removeClass("b-g-btn-disabled")},removeEditView:function(e){var t=this._edit;this.changeNum++,t&&e==t.model&&(t.destroy(),this._edit=null)},clearChangeNum:function(){this.changeNum=0},saveFields:function(e){e=$(e.currentTarget);e.hasClass("b-g-btn-disabled")||this._coll.save(this._getFieldOrder(),e)},showEditView:function(e){var t,i=this,e=$(e.currentTarget),o=e.attr("data-cid"),o=i._coll.get(o),s=!1,l=n.getView(o.get("RenderFieldType"));e.hasClass("fm-item-edite")||(e.parent()&&e.parent().siblings(".form-tit")&&(s="cus-type"==(t=e.parent().siblings(".form-tit").attr("data-type"))||"oppo-type"==t),$(".fm-item",i.$el).removeClass("fm-item-edite"),e.addClass("fm-item-edite"),i._edit&&i._edit.destroy(),i._edit=new l({model:o,isShowHideSet:i.options.isShowHideSet,setFieldLayout:i.options.setFieldLayout,ownerType:i.options.ownerType,$wrapper:i.$editBox,formCusOrOppField:s}),i._edit.render())},emptyView:function(){var e=this;_.each(e.fieldViews,function(e){e&&e.destroy&&e.destroy()}),e.fieldViews=[],e._edit&&e._edit.destroy(),$(".btns-box .b-g-btn",this.$el).addClass("b-g-btn-disabled")},_createDnd:function(e){var s=this,t=d.uuid(),i=null;e.attr("data-dragid",t),(i=new o(e,{proxyParent:e.closest(".inner"),drag:e,drop:".fm-item:not([data-dragid="+t+"])",containment:e.closest(".inner"),axis:"y",revert:!0,dropCursor:"move",dragIgnore:2,zIndex:999999})).on("dragenter",function(e,t){s._exchangeTwoNavItem(i.get("drag"),t)}),i.on("dragstart",function(e,t,i){s.draggingTopOrgin=t.offset().top||0,t.addClass("dragging")}),i.on("dragend",function(e,t,i){var o=null,o=(t?$(t):$(e)).closest(".inner");s.changeNum+=1,$(".fm-item",o).attr("data-change",2),$(".btns-box .b-g-btn-disabled",s.$el).removeClass("b-g-btn-disabled")}),i.on("drag",function(e,t){s._setScrollTop(e)})},_exchangeTwoNavItem:function(e,t){e=e.closest(".fm-item"),t=t.closest(".fm-item");e.index()>t.index()?t.before(e):t.after(e)},_setScrollTop:function(e){var t=$(".left .scroll-content",this.$el),i=(t[0].scrollHeight,t.scrollTop()),o=$(".scroll-el",this.$el).height(),e=e.offset().top;this.draggingTopOrgin;e<175&&t.scrollTop(i-(175-e)),o+125<e&&t.scrollTop(i+(e-(o+125)))},_getFieldOrder:function(){var i={};return $(".fm-item",this.$el).each(function(e,t){t=$(t);i[t.data("cid")]=e+1}),i},_scrollToError:function(e,t){var i;""!=t&&(t=this.$(".left .field-error"),i=this.$(".left .scroll-content"),t[0])&&i[0]&&i.scrollTop(0).scrollTop(t.offset().top-i.offset().top-150)},getSubmitData:function(e){var t=this._getFieldOrder();return this._coll.collect(t,e)},destroy:function(){this.emptyView(),this._coll&&this._coll.destroy&&this._coll.destroy(),_.each(["_scrollbar","_dep","_add"],function(e){this[e]&&this[e].destroy&&this[e].destroy(),this[e]=null}),FS.setAppStore("crm-sysobject-lookup",null),this.undelegateEvents(),this.$el.empty()}}));i.exports=e});
define("crm-setting/common/fieldmanage/formula/formula",["../fetch","./template/tpl-html","./template/list-html","../config/config","../config/relate_filter","../config/filter_config","crm-modules/common/util","crm-widget/select/select","../crumbpanel/crumbpanel","crm-widget/dialog/dialog"],function(e,t,i){var c=e("../fetch"),l=e("./template/tpl-html"),o=e("./template/list-html"),u=e("../config/config"),s=e("../config/relate_filter"),r=e("../config/filter_config"),n=e("crm-modules/common/util"),a=e("crm-widget/select/select"),d=e("../crumbpanel/crumbpanel"),p=e("crm-widget/dialog/dialog").extend({attrs:{title:$t("计算设置"),showBtns:!0,showScroll:!0,content:"",width:620,zIndex:1300,className:"crm-c-fieldmanage-setformula",returnValueType:u.retype[0].value,calformula:"",formula:"",decimalDigits:2,defaultIsZero:!0,objectType:0,fields:null,isAdd:!0,isTip:!1,isHelp:!1,isResultSet:!1,isNullHandle:!1,isCheckBtn:!1,isSupportGlobalVar:!0,isInsertSymbol:!0,isInsertFunc:!0,filterSelf:!1,curField:"",isDefault:!1},events:{"click .b-g-btn":"sureHandle","click .b-g-btn-cancel":"cancelHandle","click .btn-insert .crm-btn":"_toggleList","click .btn-iFunction":"_showFuncPanel","click .btn-iSymbol":"_showSymbolPanel","click .list-item-parent":"lookSub","click .menu .all":"resetList","click .list-item":"insertField","click .btn-check":"checkHandle","click .update-tip .title":"_toggleTip","click .tab-wrap span":"tabHandle"},show:function(){var e=this;e._widgets={},p.superclass.show.call(e),e.setContent(l(e._getTplData())),e.get("isResultSet")&&(e._initReturnSelect(),e._initPointSelect()),e.get("isNullHandle")&&e._initNullSelect(),e.resetList(null,e.get("returnValueType")),e.resizedialog(),e.bindEvent(),this._preFetch(_.bind(this.preview,this))},_getTplData:function(){return{calformula:this.get("calformula"),formula:this.get("formula"),isTip:this.get("isTip"),isHelp:this.get("isHelp"),isResultSet:this.get("isResultSet"),isNullHandle:this.get("isNullHandle"),isCheckBtn:this.get("isCheckBtn"),isSupportGlobalVar:this.get("isSupportGlobalVar"),isInsertSymbol:this.get("isInsertSymbol"),isInsertFunc:this.get("isInsertFunc")}},_initReturnSelect:function(){var n=this,e=new a({$wrap:$(".return-select",n.element),appendBody:!0,width:125,options:u.retype,disabled:!n.get("isAdd"),stopPropagation:!0,defaultVal:n.get("returnValueType"),zIndex:+n.get("zIndex")+10});e.on("change",function(e,t){var i=_.contains(["number","percentage","currency"],t.value),l=_.contains(["text","date"],t.value);n.resetList(null,t.value),$("textarea",n.element).val(""),$(".desc-tip",n.element).html(t.desc),$(".set-item .r",n.element).toggle(i),$(".result-tip span",n.element).html(""),l?(n._widgets._null.setValue(1),n._widgets._null.setDisable(!0)):(n._widgets._null.setValue(0),n._widgets._null.setDisable(!1))}),n._widgets._return=e},_initPointSelect:function(){var e=this,t=new a({$wrap:$(".point-select",e.element),appendBody:!0,width:125,options:u.point,stopPropagation:!0,zIndex:+e.get("zIndex")+10,defaultVal:e.get("decimalDigits")});e._widgets._point=t},_initNullSelect:function(){var e=this,t=new a({$wrap:$(".null-select",e.element),appendBody:!0,width:240,options:u.nulltype,disabled:!_.contains(["number","percentage"],this.get("returnValueType")),stopPropagation:!0,defaultVal:e.get("defaultIsZero")&&!_.contains(["text","date"],this.get("returnValueType"))?0:1,zIndex:+e.get("zIndex")+10});e._widgets._null=t},bindEvent:function(){var t=this;n.fixInputEvent($("textarea",t.element),_.bind(t.preview,t)),$(document).on("click.crm-c-fieldmanage-setformula",function(e){$(".panel",t.element).hide()})},_preFetch:function(t){var i=this,e=i._getPreFetchAjax(),l=_.keys(e);$.when.apply($,_.values(e)).done(function(){var e=arguments;1==l.length&&(e=[[arguments[0],arguments[1],arguments[2]]]),_.each(e,function(e,t){t=l[t];"globalVars"==t?i._globalVar=e[0].Value.globalVariableList:isNaN(+t)?c.cache.fields[t]=e[0].Value.objectDescribeDraft:FS.setAppStore("crm-fieldlist-"+t,e[0].Value.Items)}),t&&t()})},_getPreFetchAjax:function(){var i={},e=this.get("calformula").match(/\[(.*?)\]/g)||[];return _.each(e,function(e){var t,e=(e=e.slice(1,-1)).split(".");e.length<3||(t=u.api2type[e[0]],c.getFieldsImmediate(t||e[0]))||(t?i[t]=c.fetchFieldsByOwnertype(t):i[e[0]]=c.fetchFieldsByApiname(e[0]))}),i.globalVars=this.getGlobalVariable(),i},_showFuncPanel:function(e){var t=this;this._widgets.funcPanel||((e=new d({wrapper:$(e.currentTarget).closest(".btn-insert"),options:u.func,offset:[44,0]})).on("change",function(e){t.inputVal(e),t.preview()}),this._widgets.funcPanel=e),this._widgets.funcPanel.show()},_showSymbolPanel:function(e){var t=this;this._widgets.symbolPanel||((e=new d({wrapper:$(e.currentTarget).closest(".btn-insert"),options:t._getSymbols(),offset:[44,0],width:120,isNav:!1})).on("change",function(e){t.inputVal(e),t.preview()}),this._widgets.symbolPanel=e),this._widgets.symbolPanel.show()},_getSymbols:function(){var e=_.filter(u.operator,function(e,t){return _.contains([0,1,2,3,4,5,6,7,8,9,10,14],t)});return _.map(e,function(e){return{label:e.name,value:e.value,valueIsLabel:!0}})},getGlobalVariable:function(e){return n.FHHApi({url:"/EM1HNCRM/API/v1/object/global_variable/service/findGlobalVariableList",data:{}})},resetList:function(e,i){var l=this,n=+l.get("objectType");e&&e.stopPropagation(),i=i||l._getReturnValueType(),c.fetchRelationConfig(n,function(t){l._fetchFields(function(e){l.get("filterSelf")&&(e=_.filter(e,function(e){return e.FieldName!=l.get("curField").FieldName})),$(".menu .all",l.element).siblings().remove(),$(".panel-iField ul",l.element).html(o({list:l._formatRelateObject(t,e)})),l.get("fields")?$(".panel-iField ul",l.element).append(o({list:_.filter(l.get("fields"),function(e){return _.contains([4,5,6],e.FieldType)&&2==e.FieldProperty&&e.UserDefinedFieldID})})):$(".panel-iField ul",l.element).append(o({list:l._formatFieldsByRtype(e,i,CRM.config.objDes[n],!1)}))})})},_toggleList:function(e){$(".panel",this.element).hide(),$(e.currentTarget).siblings().toggle(),e.stopPropagation()},_toggleTip:function(e){$(e.currentTarget).closest(".update-tip").toggleClass("update-tip-hover"),this.resizedialog()},_formatRelateObject:function(e,l){var n=[],a=CRM.config.objDes[this.get("objectType")];return _.each(e,function(e){var t,i;s.getByFormula(e,{api_name:a.apiName,display_name:a.name,fields:l})&&(t=_.findWhere(l,{FieldName:e.RelationFieldName}),i=CRM.config.objDes[e.RelationObjectType.toLowerCase()],n.push({name:t?t.FieldCaption:i?i.name:"--",value:e.RelationObjectType,id:e.RelationFieldName,isParent:!0}))}),n},_formatFieldsByRtype:function(e,t,i,l){var n=this,a=[];return i&&(i.api_name||(i.api_name=i.apiName),i.display_name||(i.display_name=i.name),i.fields||(i.fields=e)),_.each(e,function(e){r.getByFormula(e,t,i,l,n.defaults.isDefault)&&a.push(e)}),a},_fetchFields:function(t){this.get("fields")?t&&t(this.get("fields")):c.fetchFieldsByOwnertype(this.get("objectType"),function(e){t&&t(e)})},preview:function(){var r=this,e=$("textarea",r.element).val().trim(),o=r.get("fields")||c.getFieldsImmediate(r.get("objectType")),e=e.replace(/\[(.*?)\]/g,function(e,t){var i,l,n,a=t.split("."),s=null;return 3==a.length?(i=+u.api2type[a[0]],l=u.type2name[i],i?(n=c.getFieldsImmediate(i),(s=_.findWhere(n,{FieldName:a[2]}))?"["+l+"."+s.FieldCaption+"]":e):(n=c.getFieldsImmediate(a[0]),(s=_.findWhere(n.fields,{api_name:a[2]}))?"["+n.display_name+"."+s.label+"]":e)):/__g/g.test(t)?(s=_.findWhere(r._globalVar,{api_name:t}),t&&s?"["+s.label+"]":e):(s=_.findWhere(o,{FieldName:t}),t&&s?"["+s.FieldCaption+"]":e)});$(".result-tip span",this.element).html(_.escape(e)),$(".error-tip",this.element).html("")},lookSub:function(e){e.stopPropagation();var t=this,i=[],e=$(e.currentTarget),l=e.data("value"),n=e.data("id"),a=u.api2type[l],s=l+"."+n+"__r.",r=t._getReturnValueType();$(".panel-iField ul",t.element).html('<div class="loading"></div>'),a?c.fetchFieldsByOwnertype(a,function(e){i=t._formatFieldsByRtype(e,r,CRM.config.objDes[a],!0),i=$.extend(!0,[],i),i=_.map(i,function(e){return e.FieldName=s+e.FieldName,e}),$(".panel-iField .field-list",t.element).html(o({list:i}))},!0):c.fetchFieldsByApiname(l,function(e){i=t._formatFieldsByRtype(e.fields,r,e,!0),i=$.extend(!0,[],i),i=_.map(i,function(e){return{FieldName:s+e.api_name,FieldCaption:e.label,UserDefinedFieldID:e.id||e.api_name}}),$(".panel-iField .field-list",t.element).html(o({list:i}))},!0),$(".panel-iField .menu",t.element).append("<span>"+e.data("name")+"</span>")},insertField:function(e){var e=$(e.currentTarget),t=e.data("value");e.data("id")&&-1<(t="["+t+"]").indexOf("Discount")&&!this.get("isDefault")&&(t+="/100"),this.inputVal(t),this.preview()},inputVal:function(e){var t=$("textarea",this.element),i=t.val().trim(),l=(l=n.getCursorPos(t))||i?l:i.length;t.val(i.slice(0,l)+e+i.slice(l)),n.setCursorPos(t,"()"==e?l+e.length-1:l+e.length),this.preview()},tabHandle:function(e){var t=$(e.currentTarget),i=t.index();t.addClass("active").siblings().removeClass("active"),$(".tab-content",this.element).addClass("crm-hide").eq(i).removeClass("crm-hide"),1==i&&this.renderGlobalvarsList(),e.stopPropagation()},renderGlobalvarsList:function(){var e=this._getReturnValueType(),t=_.map(this._globalVar,function(e){return e.notCheck=!0,e.FieldName=e.api_name,e.FieldCaption=e.label,e.UserDefinedFieldID=e._id,e}),t=this._formatFieldsByRtype(t,e,null,!1);$(".allvar-list",this.element).html(o({list:t}))},checkHandle:function(t){var e=this.getData();n.FHHApi({url:"/EM1HCRM/CRMCommon/CheckExpression",data:{ObjectType:+this.get("objectType"),ReturnType:e.ReturnValueType,Expression:e.CalFormula,DefaultIsZero:e.DefaultIsZero},success:function(e){0==e.Result.StatusCode?_.isFunction(t)?t():n.remind($t("语法检验通过")):n.alert($t("公式语法错误"))}},{errorAlertModel:1})},getData:function(){return{ReturnValueType:this._getReturnValueType(),DecimalDigits:this._getDecimalDigits(),DefaultIsZero:this._getDefaultIsZero(),Formula:$(".result-tip span",this.element).html(),CalFormula:$("textarea",this.element).val().trim()}},_getReturnValueType:function(){return this.get("isResultSet")?this._widgets._return.getValue():this.get("returnValueType")},_getDecimalDigits:function(){return this.get("isResultSet")?this._widgets._point.getValue():this.get("decimalDigits")},_getDefaultIsZero:function(){return this.get("isNullHandle")?0==this._widgets._null.getValue():this.get("defaultIsZero")},validate:function(e){var t="";return(t=e.CalFormula?t:$t("表达式不能为空"))&&$(".error-tip",this.element).html(t),!t},sureHandle:function(){var e=this,t=e.getData();(e.get("isDefault")||e.validate(t))&&(!e.get("isCheckBtn")||e.get("isDefault")&&!t.CalFormula?(e.trigger("enter",t),e.destroy()):e.checkHandle(function(){e.trigger("enter",t),e.destroy()}))},cancelHandle:function(){this.trigger("cancel"),this.destroy()},hide:function(){this.destroy()},destroy:function(){_.each(this._widgets,function(e){e.destroy&&e.destroy()}),$(document).off("click.crm-c-fieldmanage-setformula"),p.superclass.destroy.call(this)}});i.exports=p});
define("crm-setting/common/fieldmanage/formula/template/list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(list, function(item) {
                __p += ' <li class="' + ((__t = item.isParent ? "list-item-parent" : "list-item") == null ? "" : __t) + '" data-value="' + ((__t = item.FieldName || item.value) == null ? "" : __t) + '" data-name="' + __e(item.FieldCaption || item.name) + '" data-id="' + ((__t = item.UserDefinedFieldID || item.id) == null ? "" : __t) + '"> ';
                if (item.desc) {
                    __p += " <em>" + ((__t = item.desc) == null ? "" : __t) + "</em> ";
                }
                __p += " " + __e(item.FieldCaption || item.name) + " ";
                if (item.tip) {
                    __p += ' <div class="crm-tip"> <tip class="tip-btn"></tip> <div class="tip-text"> ';
                    _.each(item.tip, function(tip_item) {
                        __p += " <label>" + ((__t = tip_item.label) == null ? "" : __t) + "</label> <p>" + ((__t = tip_item.value) == null ? "" : __t) + "</p> ";
                    });
                    __p += " </div> </div> ";
                }
                __p += " </li> ";
            });
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/formula/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="inner"> ';
            if (isTip) {
                __p += ' <div class="update-tip update-tip-hover"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="title">' + ((__t = $t("注意")) == null ? "" : __t) + '</div> <div class="text">' + ((__t = $t("由于计算字段增加了多种返回值类型和函数新增的类型和函数只适用于6.1及以后的版本请通知您公司内的同事将App升级到最新版本")) == null ? "" : __t) + "</div> </div> </div> ";
            }
            __p += " ";
            if (isResultSet) {
                __p += ' <div class="set-item"> <div class="l"> <label>' + ((__t = $t("返回值类型")) == null ? "" : __t) + '</label> <div class="return-select"></div> </div> <div class="r"> <label>' + ((__t = $t("小数单位数")) == null ? "" : __t) + '</label> <div class="point-select"></div> </div> <div class="desc-tip crm-clearfix">' + ((__t = $t("计算字段小数单位提示")) == null ? "" : __t) + "</div> </div> ";
            }
            __p += ' <div class="set-item"> <label> <em>*</em>' + ((__t = $t("计算公式")) == null ? "" : __t);
            if (isHelp) {
                __p += ' <a href="https://www.fxiaoke.com/mob/guide/crmdoc/src/7-2-6计算字段.html" target="_black">' + ((__t = $t("使用帮助")) == null ? "" : __t) + "</a> ";
            }
            __p += ' </label> <div class="result-tip">' + ((__t = $t("等于")) == null ? "" : __t) + "：<span>" + __e(formula) + "</span></div> <textarea class=" + ((__t = isSupportGlobalVar && !isResultSet ? "maxHeight" : "") == null ? "" : __t) + ">" + ((__t = calformula) == null ? "" : __t) + '</textarea> <div class="error-tip crm-hide"></div> <div class="btn-box"> <div class="btn-insert"> <span class="crm-btn btn-iField">' + ((__t = $t("插入字段")) == null ? "" : __t) + '</span> <div class="panel panel-iField crm-hide"> ';
            if (isSupportGlobalVar) {
                __p += ' <div class="tab-wrap"> <span class="active" type="">' + ((__t = $t("字段")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("全局变量")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += ' <div class="tab-content"> <div class="menu"><span class="all">' + ((__t = $t("全部")) == null ? "" : __t) + '</span></div> <ul class="crm-scroll field-list"></ul> </div> <ul class="tab-content crm-scroll crm-hide allvar-list"></ul> </div> </div> ';
            if (isInsertSymbol) {
                __p += ' <div class="btn-insert"> <span class="crm-btn btn-iSymbol">' + ((__t = $t("插入运算符")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += " ";
            if (isInsertFunc) {
                __p += ' <div class="btn-insert"> <span class="crm-btn btn-iFunction">' + ((__t = $t("插入函数")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += " ";
            if (isCheckBtn) {
                __p += ' <div class="btn-check crm-btn crm-btn-primary">' + ((__t = $t("语法检查")) == null ? "" : __t) + "</div> ";
            }
            __p += " </div> </div> ";
            if (isNullHandle) {
                __p += ' <div class="set-item"> <label>' + ((__t = $t("公式中字段为空值时")) == null ? "" : __t) + '</label> <div class="null-select"></div> </div> ';
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/setfilter/setfilter",["../fetch","../config/config","./template/tpl-html","crm-modules/common/util","crm-widget/dialog/dialog","crm-modules/common/fieldfilter/fieldfilter"],function(e,t,i){e("../fetch"),e("../config/config");var a=e("./template/tpl-html"),r=e("crm-modules/common/util"),n=e("crm-widget/dialog/dialog"),s=e("crm-modules/common/fieldfilter/fieldfilter"),o=n.extend({attrs:{title:$t("设置条件"),showBtns:!0,showSroll:!0,className:"crm-s-sysobject-setfilter",width:800},events:{"click .b-g-btn-cancel":"destroy","click .b-g-btn":"sureHandle","click .update-tip .title":"_toggleTip"},show:function(e){var t=this;n.superclass.show.call(t),t.setContent(a({tip:e.tip})),t.data=e,t._initFilter(),t.resetPosition()},_initFilter:function(){var t=this,i=t.data,a=new s({$wrapper:$(".filter",t.$el),title:$t("且(AND)"),max:20,level:3,width:750,isRelate:i.isRelate,filterType:t.getFilterType(),filterApiname:t.getFilterApiname(),apiname:i.apiname,origin:i.origin,fields:i.fields,isSupportCascadingCheck:i.isSupportCascadingCheck});a.on("render",function(){var e;i.defaultValue&&(e=_.map(i.defaultValue,function(e){return[e.FieldName,e.Operator,e.FieldValues]}),a.setData(e)),t.resizedialog()}),a.on("add.item",function(){t.resizedialog()}),a.on("del.item",function(){t.resizedialog()}),t.filter=a},getFilterType:function(){var e=this.data,t=["formula","group","image","file_attachment","master_detail","auto_number","signature","quote","embedded_object_list","multi_level_select_one"];return"count"==e.from&&t.push("count"),e.origin&&e.apiname!=e.origin.apiname||t.push("object_reference"),t},getFilterApiname:function(){return["version","package","tenant_id","object_describe_api_name","object_describe_id","_id","extend_obj_data_id","sales_process_id","oppo_stage_id","is_deleted","pin_yin","account_pin_yin","filling_checker_id","high_seas_id","total_refund_amount","is_remind_recycling","Address","owner_department","SalesOrderProductObj","lock_user","lock_rule","life_status_before_invalid","active_status","relevant_team","delivery_comment","bill_money_to_confirm","payment_money_to_confirm","out_owner","out_tenant_id","opportunity_id","after_sale_stage_count","after_sale_stage_name","after_sale_stage_order","after_sale_stage_status","before_sale_stage_count","before_sale_stage_name","before_sale_stage_order","is_bind_after_sale","is_start_after_sale","oppo_after_stage_id","day_of_birth","year_of_birth","month_of_birth","discount"].concat("count"==this.data.from&&-1<["OrderPaymentObj","PaymentObj"].indexOf(this.data.apiname)?[]:["life_status"]).concat(_.contains(["ContactObj"],this.data.apiname)?["mobile","tel"]:[])},sureHandle:function(){var e=this.filter.getData(),t=this.validate(e);t?r.alert(t):(e=this._parse(e),t=this.filter.getPreviewData(),this.trigger("success",e,t),this.destroy())},validate:function(e){for(var t=$t("请输入或插入字段"),i=$t("数字金额百分比等字段不允许比较非数字类型的常量"),a=0;a<e.length;a++){var r=e[a];if(!r[0]||!r[1])return t;if(!_.contains(["IS","ISN"],r[1])){if(_.isArray(r[2])){if(0==r[2].length)return t}else if(!r[2])return t;if(_.contains(["number","currency","percentile","count"],r[3])&&!/^\$(.*)\$$/g.test(r[2])&&isNaN(+r[2]))return i}}return""},_parse:function(e){var t=this;return _.map(e,function(e){return{FieldName:t._parseField(e),Operator:t._parseOperator(e),FieldValues:t._parseValues(e),ValueType:t._parseValueType(e)}})},_parseField:function(e){return e[0]},_parseOperator:function(e){return e[1]},_parseValues:function(e){return _.isArray(e[2])?e[2]:e[2]?[e[2]]:[]},_parseValueType:function(e){return"object_reference"==e[3]?2:/^\$(.*?)\$$/g.test(e[2])?1:0},_toggleTip:function(e){$(e.currentTarget).closest(".update-tip").toggleClass("update-tip-hover")},hide:function(){this.destroy()},destroy:function(){this.off(),this.filter&&this.filter.destroy(),o.superclass.destroy.call(this)}});i.exports=o});
define("crm-setting/common/fieldmanage/setfilter/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (tip) {
                __p += ' <div class="update-tip update-tip-hover"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="title">' + ((__t = $t("注意")) == null ? "" : __t) + '</div> <div class="text">' + ((__t = tip) == null ? "" : __t) + "</div> </div> </div> ";
            }
            __p += ' <div class="filter"></div>';
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/template/add-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<span class="add-field">+' + ((__t = $t("添加字段")) == null ? "" : __t) + '</span> <div class="fields-type"> <div class="fields-type-tit"> <div class="j-tab tab b-g-clear"> ';
            if (isRelateObj) {
                __p += ' <span data-type="Cus" class="cur">' + ((__t = $t("关联客户字段")) == null ? "" : __t) + '</span> <span data-type="Oppo">' + ((__t = $t("关联商机字段")) == null ? "" : __t) + "</span> <!-- ";
                if (CRM.util.isGrayScale("CRM_OPPORTUNITY_SALEACTION")) {
                    __p += " <span>" + ((__t = $t("添加字段")) == null ? "" : __t) + "</span> ";
                }
                __p += " --> ";
            } else {
                __p += ' <span class="cur">' + ((__t = $t("添加字段")) == null ? "" : __t) + '</span> <div class="crm-tip"> <span class="tip-btn"></span> <div class="tip-text"> </div> </div> ';
            }
            __p += ' </div> <span class="close-type">×</span> </div> <div class="j-tab-con f-tab-con"> ';
            if (isRelateObj) {
                __p += ' <div class="f-tab-item cus-item"> <p class="tip">' + ((__t = $t("提示 选用关联字段后信息与客户同步仅能选用一次。")) == null ? "" : __t) + '</p> <div class="fields-type-con cus-field b-g-clear" data-render="0"></div> </div> <div class="f-tab-item oppo-item b-g-hide"> <p class="tip">' + ((__t = $t("提示 选用关联字段后信息与商机同步仅能选用一次。")) == null ? "" : __t) + '</p> <div class="fields-type-con oppo-field" data-render="0"></div> </div> <!-- ';
                if (CRM.util.isGrayScale("CRM_OPPORTUNITY_SALEACTION")) {
                    __p += ' <div class="fields-type-con f-tab-item b-g-clear b-g-hide"> <p class="crm-ico-error" style="padding: 10px 10px 8px 10px;color: #fff;background-color: #ff522a;margin-right: 10px;">' + ((__t = $t("提示销售阶段下直接添加字段功能会在将来版本废止请利用商机本身字段或客户字段")) == null ? "" : __t) + "</p> ";
                    _.each(additem, function(item, index) {
                        __p += " ";
                        if (!item.icon) return;
                        __p += ' <span data-type="' + ((__t = item.type) == null ? "" : __t) + '" class="add-type ' + ((__t = item.icon) == null ? "" : __t) + '">' + ((__t = item.name) == null ? "" : __t) + "</span> ";
                    });
                    __p += " </div> ";
                }
                __p += " --> ";
            } else {
                __p += ' <div class="fields-type-con f-tab-item b-g-clear"> ';
                _.each(additem, function(item, index) {
                    __p += " ";
                    if (!item.icon) return;
                    __p += ' <span data-type="' + ((__t = item.type) == null ? "" : __t) + '" class="add-type ' + ((__t = item.icon) == null ? "" : __t) + '">' + ((__t = item.name) == null ? "" : __t) + "</span> ";
                });
                __p += " </div> ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/template/field-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (FieldType == 1) {
                __p += ' <h3 class="fm-line"><span class="lb-name">' + __e(FieldCaption ? FieldCaption : $t("分割线名称")) + "</span></h3> ";
            } else {
                __p += ' <label class="fm-lb"><em>' + ((__t = IsNotNull ? "*" : "") == null ? "" : __t) + '</em><span class="lb-name">' + __e(FieldCaption ? FieldCaption : $t("字段名称")) + "</span></label> ";
                if (FieldType == 3 || FieldType == "long_text") {
                    __p += ' <div class="fm-ipt ipt-box"> <div class="b-g-ipt fm-item-textarea">' + ((__t = FieldValue) == null ? "" : __t) + "</div> </div> ";
                } else if (FieldType == 8 || FieldType == "select_one") {
                    __p += ' <div class="fm-ipt"> <div class="select-box"></div> <div class="select-box-mask"></div> </div> ';
                } else if (FieldType == 9 || FieldType == "select_many") {
                    __p += ' <div class="fm-ipt mn-checkbox-box fn-clear"> ';
                    _.each(EnumDetails, function(item, index) {
                        __p += ' <p class="mn-checkbox-item-wrapper"> <span class="mn-checkbox-item"></span> <span class="fm-radio-lb" data-isSys="' + ((__t = item.IsSysItem ? 1 : 2) == null ? "" : __t) + '" data-code="' + ((__t = item.ItemCode) == null ? "" : __t) + '">' + __e(item.ItemName || $t("选项") + (index + 1)) + "</span> </p> ";
                    });
                    __p += " </div> ";
                } else if (FieldType == 10 || FieldType == "image") {
                    __p += ' <div class="fm-ipt fn-clear"> <div class="fm-img-place"></div> <p class="img-place-tit">' + ((__t = $t("上传图片")) == null ? "" : __t) + '</p> <p class="img-place-intro">' + ((__t = $t("支持jpggifpng格式的图片")) == null ? "" : __t) + "</p> </div> ";
                } else if (FieldType == 17 || FieldType == "file_attachment") {
                    __p += ' <div class="fm-ipt fn-clear annex-box"> <p>+' + ((__t = $t("添加附件")) == null ? "" : __t) + "</p> </div> ";
                } else if (FieldType == 14 || FieldType == "multi_level_select_one") {
                    __p += ' <div class="fm-ipt mul-select fn-clear"> <div class="select">' + ((__t = $t("请选择")) == null ? "" : __t) + '<em class="crm-ico-arrow2"></em></div> <div class="select">' + ((__t = $t("请选择")) == null ? "" : __t) + '<em class="crm-ico-arrow2"></em></div> </div> ';
                } else {
                    __p += ' <div class="fm-ipt ipt-box"> <div class="b-g-ipt"></div> </div> ';
                }
                __p += " ";
            }
            __p += " ";
            if (FieldProperty != 1) {
                __p += ' <span class="del-field">×</span> ';
            }
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/template/objtab-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(fields, function(item) {
                __p += " ";
                if (item.FieldType != 1 && !(item.FieldName == "Name" && type == "cus")) {
                    __p += ' <span class="add-c-type" data-id="' + ((__t = item._id) == null ? "" : __t) + '" data-apiname="' + ((__t = item.api_name) == null ? "" : __t) + '" data-caption="' + ((__t = item.label) == null ? "" : __t) + '" data-isnotnull="' + ((__t = item.is_required ? 1 : 2) == null ? "" : __t) + '" data-type="' + ((__t = item.type) == null ? "" : __t) + '" data-isallow="' + ((__t = item.is_allow) == null ? "" : __t) + '">' + __e(item.label) + "</span> ";
                }
                __p += " ";
            });
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/template/page-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-s-fieldmanage ' + ((__t = isSetRelate ? "crm-c-isRelationship" : "") == null ? "" : __t) + '"> <div class="layout-box"> <div class="left"> <div class="scroll-el"> <div class="preset-box crm-g-form"> <h2 class="form-tit">' + ((__t = $t("预设字段")) == null ? "" : __t) + '<span style="color: #999;font-size:12px;margin-left: 10px;">(' + ((__t = $t("预设字段不可删除")) == null ? "" : __t) + ')</span></h2> <div class="inner"></div> </div> <div class="defined-box my-field-box crm-g-form" style="padding-bottom: 20px;"> <h2 class="form-tit">' + ((__t = $t("自定义字段")) == null ? "" : __t) + '</h2> <div class="inner"></div> </div> <div class="cus-box my-field-box crm-g-form ' + ((__t = isRelateObj ? "" : "b-g-hide") == null ? "" : __t) + '" style="padding-bottom: 20px;"> <h2 class="form-tit">' + ((__t = $t("以下为关联客户字段")) == null ? "" : __t) + '</h2> <div class="inner"></div> </div> <div class="oppo-box my-field-box crm-g-form ' + ((__t = isRelateObj ? "" : "b-g-hide") == null ? "" : __t) + '" style="padding-bottom: 20px;"> <h2 class="form-tit">' + ((__t = $t("以下为关联商机字段")) == null ? "" : __t) + '</h2> <div class="inner"></div> </div> </div> </div> <div class="right"> <div class="inner"> <div class="scroll-el"> <div class="scroll-el-con"></div> </div> </div> </div> <div class="fields-type-box"></div> ';
            if (isSaveBtn) {
                __p += ' <div class="btns-box"> <span class="b-g-btn b-g-btn-disabled">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/template/table-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<table> <thead> <tr> <th class="first">' + ((__t = $t("字段类型")) == null ? "" : __t) + '</th> <th class="second">' + ((__t = $t("字段个数")) == null ? "" : __t) + '</th> <th class="third">' + ((__t = $t("字段值长度")) == null ? "" : __t) + "</th> </tr> </thead> <tbody> ";
            _.each(left, function(item, index) {
                __p += ' <tr> <td class="first">' + ((__t = item.name) == null ? "" : __t) + '</td> <td class="second">' + ((__t = item.max) == null ? "" : __t) + '</td> <td class="third">' + ((__t = item.length < 0 ? "" : item.length) == null ? "" : __t) + "</td> </tr> ";
            });
            __p += ' </tbody> </table> <table> <thead> <tr> <th class="first">' + ((__t = $t("字段类型")) == null ? "" : __t) + '</th> <th class="second">' + ((__t = $t("字段个数")) == null ? "" : __t) + '</th> <th class="third">' + ((__t = $t("字段值长度")) == null ? "" : __t) + "</th> </tr> </thead> <tbody> ";
            _.each(right, function(item, index) {
                __p += ' <tr> <td class="first">' + ((__t = item.name) == null ? "" : __t) + '</td> <td class="second">' + ((__t = item.max) == null ? "" : __t) + '</td> <td class="third">' + ((__t = item.length < 0 ? "" : item.length) == null ? "" : __t) + "</td> </tr> ";
            });
            __p += " </tbody> </table>";
        }
        return __p;
    };
});
define("crm-setting/common/fieldmanage/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-c-fieldmanage ' + ((__t = isSetRelate ? "crm-c-isRelationship" : "") == null ? "" : __t) + '"> <div class="layout-box"> <div class="left"> <div class="scroll-el"> <div class="preset-box crm-g-form"> <h2 class="form-tit">' + ((__t = $t("预设字段")) == null ? "" : __t) + '<span style="color: #999;font-size:12px;margin-left: 10px;">(' + ((__t = $t("预设字段不可删除")) == null ? "" : __t) + ')</span></h2> <div class="inner"></div> </div> <div class="defined-box my-field-box crm-g-form" style="padding-bottom: 20px;"> <h2 class="form-tit">' + ((__t = $t("自定义字段")) == null ? "" : __t) + '</h2> <div class="inner"></div> </div> <div class="cus-box my-field-box crm-g-form ' + ((__t = isRelateObj ? "" : "b-g-hide") == null ? "" : __t) + '" style="padding-bottom: 20px;"> <h2 class="form-tit" data-type="cus-type">' + ((__t = $t("以下为关联客户字段")) == null ? "" : __t) + '</h2> <div class="inner"></div> </div> <div class="oppo-box my-field-box crm-g-form ' + ((__t = isRelateObj ? "" : "b-g-hide") == null ? "" : __t) + '" style="padding-bottom: 20px;"> <h2 class="form-tit" data-type="oppo-type">' + ((__t = $t("以下为关联商机字段")) == null ? "" : __t) + '</h2> <div class="inner"></div> </div> </div> </div> <div class="right"> <div class="inner"> <div class="scroll-el"> <div class="scroll-el-con"></div> </div> </div> </div> <div class="fields-type-box"></div> ';
            if (isSaveBtn) {
                __p += ' <div class="btns-box"> <span class="b-g-btn b-g-btn-disabled">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
var field=require("../../../../action/field/field");define("crm-setting/common/fieldmanage/view/add",["crm-modules/common/util","../fetch","../template/add-html","../template/table-html","../config/config","../template/objtab-html"],function(e,t,a){e("crm-modules/common/util");var n=e("../fetch"),i=e("../template/add-html"),d=e("../template/table-html"),l=e("../config/config"),s=e("../template/objtab-html"),e=Backbone.View.extend({tagName:"div",defaults:{isRelateObj:!1,addFilter:[],relateObjPos:1},events:{"click .j-tab span":"onTab","click .add-field":"show","click .close-type":"hide","click .add-type":"addHandle","click .add-c-type":"addCTypeField"},render:function(){var e=this,t=e.getRenderData();return e.fieldConfig=l.fields,e.$el.append(i(t)),e.$el.toggleClass("c-fields-box",t.isRelateObj),t.isRelateObj?(e.addType="cus",e.renderObjectField(e.addType)):e.renderTip(),e.fieldsCusCach=[],e},renderTip:function(){var e=this.getFieldLimit();$(".crm-tip .tip-text ",this.$el).html(d(e))},renderObjectField:function(t){var a,i,d=this;t&&(1==+(a=$("."+t+"-item",d.$el)).data("render")?d.renderDisabled():n.fetchFieldsByOwnertype((i={cus:{ownerType:"AccountObj",blackList:["claimed_time","deal_status","expire_time","filling_checker_id","last_deal_closed_amount","last_deal_closed_time","last_followed_time","life_status_before_invalid","lock_rule","lock_user","owner","owner_modified_time","pin_yin","recycled_reason","total_refund_amount","returned_time","transfer_count","relevant_team","data_own_department","completed_field_quantity","high_seas_name"]},oppo:{ownerType:"OpportunityObj",blackList:["account_id","sales_process_name","lost_reason","owner","biz_status","last_followed_time","life_status","life_status_before_invalid","lock_rule","lock_status","lock_user","owner_department","relevant_team","status","data_own_department","sales_stg_changed_time"]}}[t]).ownerType,function(e){e=_.filter(e,function(t){var e;return!_.contains(["record_type","formula","object_reference","quote"],t.type)&&!(_.contains(i.blackList,t.api_name)||_.contains(["system"],t.define_type)&&("OpportunityObj"!=i.ownerType||"name"!=t.api_name)||t.is_abstract||_.contains(["signature"],t.type)||_.contains(["group"],t.type)||t.hasOwnProperty("used_in")&&"component"==t.used_in&&("AccountObj"!=i.ownerType||"country"!=t.api_name)||null==d||null==(e=d.fieldConfig)||!e.find(function(e){return e.strType==t.type||e.type==t.type}))});"AccountObj"==i.ownerType&&(d.fieldsCusCach=e),_.map(e,function(e){e.config&&e.config.attrs&&("false"==e.config.attrs.is_required||0==e.config.attrs.is_required)?e.is_allow=0:e.is_allow=1,"country"==e.api_name&&(e.label=$t("国家/省/市/区"))}),$(".fields-type-con",a).append(s({type:t,fields:e})),a.attr("data-render",1),d.renderDisabled()}))},renderDisabled:function(){var e,a;this.addType&&(e=$("."+this.addType+"-box"),(a=$("."+this.addType+"-field",this.$el)).find("span").removeClass("disabled"),e.find(".fm-item").each(function(e,t){t=$(t).data("apiname");a.find("span[data-apiname="+t+"]").addClass("disabled")}))},show:function(){this.$el.addClass("fields-type-box-add"),this.renderDisabled()},hide:function(){this.$el.removeClass("fields-type-box-add")},addHandle:function(e){e=$(e.currentTarget).data("type");this.trigger("add.field",{FieldType:e,RenderFieldType:e})},addCTypeField:function(e){var t,a,e=$(e.currentTarget);e.hasClass("disabled")||(a=FS.getAppStore("crm-fieldlist-"+("cus"==this.addType?"AccountObj":"OpportunityObj")),t=[],"select_many"==e.data("type")&&(a=(a=_.findWhere(a,{api_name:e.data("apiname")}))?a.options:t,_.each(a,function(e){e.not_usable||(e=_.extend(e,{ItemName:e.label}),t.push(e))})),this.trigger("add.field",{FieldType:e.data("type"),FieldCaption:e.data("caption"),FieldApiName:e.data("apiname"),FieldName:e.data("apiname"),IsNotNull:1==+e.data("isnotnull"),UserDefinedFieldID:e.data("id"),IsAllowEditNotNull:1==+e.data("isallow"),FieldProperty:"cus"==this.addType?3:4,EnumDetails:t}),e.addClass("disabled"))},onTab:function(e){e=$(e.target);e.addClass("cur").siblings().removeClass("cur"),this.$(".f-tab-item").eq(e.index()).show().siblings().hide(),this.addType=e.data("type")?e.data("type").toLowerCase():"",this.renderObjectField(this.addType)},getRenderData:function(){var t=this.options,e=_.filter(l.fields,function(e){return!(t.onlyInput&&!e.isInput||-1<_.indexOf(t.addFilter,e.type)||22==e.type||!e.type)});return _.extend({additem:e},t)},getFieldLimit:function(){var a=[],i=[];return _.each(l.fields,function(e,t){e.max<0||(i.length<a.length?i:a).push(e)}),{left:a,right:i}}});a.exports=e});
define("crm-setting/common/fieldmanage/view/field",["crm-modules/common/util","crm-widget/select/select","../config/config","../template/field-html"],function(e,t,i){var n=e("crm-modules/common/util"),l=e("crm-widget/select/select"),d=e("../config/config"),o=e("../template/field-html"),e=Backbone.View.extend({tagName:"div",className:"fm-item j-fmitem",template:o,isRendered:!1,events:{"click .del-field":"delHandle"},initialize:function(){this.bindEvent()},bindEvent:function(){this.listenTo(this.model,"error.hide",this.hideErrorHandle),this.listenTo(this.model,"change:error",this.errorHandle),this.listenTo(this.model,"change:IsNotNull",this.changeNotNullHandle),this.listenTo(this.model,"checkbox.update",this.changeCheckboxHandle),this.listenTo(this.model,"change:FieldCaption",this.changeFieldNameHandle)},render:function(){var e=this,t=e.model,i=e.getRenderData();return e.$el.html(o(i)),e.$el.attr({"data-cid":t.cid,"data-type":t.get("FieldType"),"data-fid":t.get("UserDefinedFieldID"),"data-apiname":t.get("FieldApiName")}),e.$el.toggleClass("b-g-hide",!t.get("IsShow")),"select_one"!=t.get("FieldType")&&8!=t.get("FieldType")||e.initSelect(),e},getRenderData:function(){var e=this.model;return{FieldType:e.get("FieldType"),IsNotNull:e.get("IsNotNull"),FieldValue:e.get("FieldValue"),EnumDetails:e.get("EnumDetails"),FieldCaption:e.get("FieldCaption"),FieldProperty:e.get("FieldProperty"),FieldApiName:e.get("FieldApiName")}},initSelect:function(){this._select=new l({$wrap:$(".select-box",this.$el),appendBody:!1,width:245,zIndex:1e3,options:[{name:$t("请选择"),value:"0"}]})},delHandle:function(e){var t,i=this,l=i.model;e.stopPropagation(),25==l.get("FieldType")&&l.set("IsVisible",!1),i.isDisabeld()?n.alert($t("该字段未被禁用不允许删除。")):i.isRelated()?n.alert($t("该字段已设置依赖关系请先解除依赖关系再删除")):l.get("UserDefinedFieldID")?t=n.confirm($t("预设对象字段管理删除字段提示"),$t("删除"),function(){i.beforeDeleteModel(l),l.collection.deleteModel(l),i.destroy(),t.hide()}):(25==l.get("FieldType")&&i.deleteComponentsModel(l),l.destroy(),i.destroy())},beforeDeleteModel:function(t){25==t.get("FieldType")&&JSON.parse(t.get("Fields"))&&_.each(d.areafields,function(e){t[e.apiName+"Model"]&&t[e.apiName+"Model"].set("IsVisible",!1),t.collection.deleteModel(t[e.apiName+"Model"])})},deleteComponentsModel:function(t){_.each(d.areafields,function(e){t[e.apiName+"Model"].destroy()})},isDisabeld:function(){var e=this.model;return 1==this.options.from&&e.get("UserDefinedFieldID")&&1!==e.get("FieldType")&&e.get("IsVisible")},isRelated:function(){var e=this.model;return!(2<e.get("FieldProperty"))&&(e.get("ParentCascadeName")||0<e.get("CascadeFields").length)},_activeBtn:function(){this.model.trigger("btn.active")},changeFieldNameHandle:function(e,t){t=t||(1!=e.get("FieldType")?$t("字段名称"):$t("分割线名称")),this.$(".lb-name").html(_.escape(t))},changeNotNullHandle:function(e,t){this.$(".fm-lb em").html(t?"*":"")},changeCheckboxHandle:function(){var e=this.model,t=_.template('## _.each(EnumDetails, function(item) { ##<p class="mn-checkbox-item-wrapper {{item.EditFlag == 3?"b-g-hide":""}}"><span class="mn-checkbox-item"></span><span class="fm-radio-lb" data-isSys="{{item.IsSysItem ? 1 : 2}}" data-code="{{item.ItemCode}}">{{{{-item.ItemName}}}}</span></p>## }) ##');$(".mn-checkbox-box",this.$el).html(t({EnumDetails:e.get("EnumDetails")}))},errorHandle:function(e,t){this.$el.append('<div class="field-error crm-ico-error">'+t+"</div>")},hideErrorHandle:function(){this.model.set("FieldError",""),this.model.set("error",""),$(".field-error",this.$el).remove()},destroy:function(){this.stopListening(),this.remove()}});i.exports=e});