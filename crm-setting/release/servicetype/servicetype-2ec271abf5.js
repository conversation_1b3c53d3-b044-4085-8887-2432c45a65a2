define("crm-setting/servicetype/servicetype",[],function(e,i,n){var t=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){var i=this.el;e.async("vcrm/sdk",function(e){e.getServiceTypeModule(i)})}});n.exports=t});
define("crm-setting/servicetype/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("服务记录类型")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> </div>';
        }
        return __p;
    };
});