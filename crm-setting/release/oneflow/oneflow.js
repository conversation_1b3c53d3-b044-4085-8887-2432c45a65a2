define("crm-setting/oneflow/oneflow",[],function(e,n,t){var i=Backbone.View.extend({initialize:function(e){var n=this;n.setElement(e.wrapper),n.addFlowTag=!1,n.doing=!1,n.viewtype="list",n.$el.addClass("crm-business-table")},render:function(){var t=this;e.async(["paas-paasui/vui","paas-bpm/secondDev"],function(e,n){n.getComponent("manageOneFlowList").then(function(e){t.inst=FxUI.create({wrapper:t.$el[0],template:"\n              <list></list>",components:{list:e}})})})},destroy:function(){this.inst&&this.inst.$destroy()}});t.exports=i});