define("crm-setting/visitorder/notify/notify",["./template/tpl-html","crm-widget/select/select","crm-modules/common/util"],function(e,t,i){var a=e("./template/tpl-html"),l=e("crm-widget/select/select"),n=e("crm-modules/common/util"),e=Backbone.View.extend({events:{"click .j-confirm":"_saveConfig","click .j-cancel":"_cancelConfig","click .j-set-notify":"_setNotifyPage","click .j-confirm2":"_saveConfig2","click .j-cancel2":"_cancelConfig2","click .j-set-notify2":"_setNotifyPage2"},initialize:function(e){this.setElement(e.wrapper),this.$$data={}},render:function(){var e=this;e.$el.html(a()),e.$$data.create={},e.$$data.confirm={},e.getRecordTypeDec(),e.getRecordTypeDecOfConfirm()},getRecordTypeDec:function(e){var t=this;n.api({url:"/FHH/EM1HSailAdmin/sail-admin/config/listApplicableNoticeOrderRecordTypes",success:function(e){var i,a,n,l;e.Error||(e=e.Value||[],i=[],a=[],n=[],l={},e.forEach(function(e){var t={name:e.label,value:e.apiName};e.chosen&&a.push(e.apiName)&&n.push(e.label),l[e.apiName]=e.label,i.push(t)}),t.$$data.create={notifyOpts:i,notifyVal:a,notifyValTemp:a,notifyOpsMap:l},n.length?$(".notify-value",t.$el).html(n.join("、")):$(".notify-value",t.$el).html("-"))}},{autoPrependPath:!1})},getRecordTypeDecOfConfirm:function(e){var t=this;n.api({url:"/FHH/EM1HSailAdmin/sail-admin/config/listExamineApproveSalesOrderNoticeRecordTypes",success:function(e){var i,a,n,l;e.Error||(e=e.Value||[],i=[],a=[],n=[],l={},e.forEach(function(e){var t={name:e.label,value:e.apiName};e.chosen&&a.push(e.apiName)&&n.push(e.label),l[e.apiName]=e.label,i.push(t)}),t.$$data.confirm={notifyOpts:i,notifyVal:a,notifyValTemp:a,notifyOpsMap:l},n.length?$(".notify-value2",t.$el).html(n.join("、")):$(".notify-value2",t.$el).html("-"))}},{autoPrependPath:!1})},initSelect:function(e){var t=this,n=t.$$data.create;t.select||(t.select=new l({$wrap:t.$(".notify-select"),multiple:"multiple",allCheck:!0,options:e,defaultValue:n.notifyVal}),t.select.on("change",function(e,t,i,a){n.notifyValTemp=e}))},initSelect2:function(e){var t=this,n=t.$$data.confirm;t.select2||(t.select2=new l({$wrap:t.$(".notify-select2"),multiple:"multiple",allCheck:!0,options:e,defaultValue:n.notifyVal}),t.select2.on("change",function(e,t,i,a){n.notifyValTemp=e}))},_setNotifyPage:function(){var e=this.$$data.create;$(".notify-wrap",this.$el).addClass("column-item__active"),$(".notify-wrap-text",this.$el).hide(),$(".notify-wrap-set",this.$el).show(),this.initSelect(e.notifyOpts),this.select&&this.select.setValue(_.clone(e.notifyVal))},_setNotifyPage2:function(){var e=this.$$data.confirm;$(".notify-wrap",this.$el).addClass("column-item__active"),$(".notify-wrap-text2",this.$el).hide(),$(".notify-wrap-set2",this.$el).show(),this.initSelect2(e.notifyOpts),this.select2&&this.select2.setValue(_.clone(e.notifyVal))},_cancelConfig:function(){"none"===$(".notify-wrap-set2",this.$el).css("display")&&$(".notify-wrap",this.$el).removeClass("column-item__active"),$(".notify-wrap-text",this.$el).show(),$(".notify-wrap-set",this.$el).hide()},_cancelConfig2:function(){"none"===$(".notify-wrap-set",this.$el).css("display")&&$(".notify-wrap",this.$el).removeClass("column-item__active"),$(".notify-wrap-text2",this.$el).show(),$(".notify-wrap-set2",this.$el).hide()},_saveConfig:function(e){var i=this,e=$(e.target),a=i.$$data.create;n.api({url:"/FHH/EM1HSailAdmin/sail-admin/config/createOrUpdateApplicableNoticeOrderRecordType",data:{orderRecordTypes:a.notifyValTemp},success:function(e){var t;e.Error?n.alert(e.Error.Message):(a.notifyVal=_.clone(a.notifyValTemp),t=[],a.notifyVal.forEach(function(e){t.push(a.notifyOpsMap[e])}),t.length?$(".notify-value",i.$el).html(t.join("、")):$(".notify-value",i.$el).html("-"),i._cancelConfig())}},{submitSelector:e,autoPrependPath:!1,errorAlertModel:1})},_saveConfig2:function(e){var i=this,e=$(e.target),a=i.$$data.confirm;n.api({url:"/FHH/EM1HSailAdmin/sail-admin/config/createOrUpdateExamineApproveSalesOrderNoticeRecordType",data:{orderRecordTypes:a.notifyValTemp},success:function(e){var t;e.Error?n.alert(e.Error.Message):(a.notifyVal=_.clone(a.notifyValTemp),t=[],a.notifyVal.forEach(function(e){t.push(a.notifyOpsMap[e])}),t.length?$(".notify-value2",i.$el).html(t.join("、")):$(".notify-value2",i.$el).html("-"),i._cancelConfig2())}},{submitSelector:e,autoPrependPath:!1,errorAlertModel:1})},destroy:function(){this.select&&this.select.destroy(),this.select2&&this.select2.destroy(),this.$$data=null}});i.exports=e});
define("crm-setting/visitorder/notify/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="column-item notify-wrap"> <div class="column-lb" title="' + ((__t = $t("微信通知设置")) == null ? "" : __t) + '">' + ((__t = $t("微信通知设置")) == null ? "" : __t) + '</div> <!-- 1 --> <div class="column-right"> <div class="column-con notify-wrap-text"> <div>' + ((__t = $t("订单创建成功发送")) == null ? "" : __t) + '<span class="notify-value"></span></div> <div class="column-tip">' + ((__t = $t("crm.访销微信通知设置说明")) == null ? "" : __t) + '</div> </div> <div class="column-con notify-wrap-set" style="display: none;"> <p>' + ((__t = $t("订单创建成功发送")) == null ? "" : __t) + '</p> <div class="select notify-select"></div> <!-- 指定哪些订单业务类型需要在创建访销订单后，微信通知CRM客户的联系人 --> <div class="column-tip">' + ((__t = $t("crm.访销微信通知设置说明")) == null ? "" : __t) + '</div> <div class="btn-box"> <button class="b-g-btn j-confirm">' + ((__t = $t("保存")) == null ? "" : __t) + '</button> <button class="b-g-btn-cancel j-cancel">' + ((__t = $t("取消")) == null ? "" : __t) + '</button> </div> </div> <span class="set-btn j-set-notify notify-wrap-text">' + ((__t = $t("设置")) == null ? "" : __t) + '</span> </div> <!-- 2 --> <div class="column-right" style="margin-top: 30px;"> <div class="column-con notify-wrap-text2"> <div>' + ((__t = $t("订单确认发送")) == null ? "" : __t) + '<span class="notify-value2"></span></div> <!-- 指定哪些订单业务类型需要在访销订单确认后，微信通知CRM客户的联系人 --> <div class="column-tip">' + ((__t = $t("crm.访销微信通知设置说明二")) == null ? "" : __t) + '</div> </div> <div class="column-con notify-wrap-set2" style="display: none;"> <p>' + ((__t = $t("订单确认发送")) == null ? "" : __t) + '</p> <div class="select notify-select2"></div> <div class="column-tip">' + ((__t = $t("crm.访销微信通知设置说明二")) == null ? "" : __t) + '</div> <div class="btn-box"> <button class="b-g-btn j-confirm2">' + ((__t = $t("保存")) == null ? "" : __t) + '</button> <button class="b-g-btn-cancel j-cancel2">' + ((__t = $t("取消")) == null ? "" : __t) + '</button> </div> </div> <span class="set-btn j-set-notify2 notify-wrap-text2">' + ((__t = $t("设置")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/visitorder/promotion/promotion",["./template/tpl-html","crm-widget/select/select","crm-modules/common/util"],function(t,e,o){var l=t("./template/tpl-html"),a=t("crm-widget/select/select"),i=t("crm-modules/common/util"),t=Backbone.View.extend({events:{"click .b-g-btn":"_saveConfig","click .b-g-btn-cancel":"_cancelConfig","click .j-set-promotion":"_setPromotionPage"},initialize:function(t){this.setElement(t.wrapper),this.$$data={}},render:function(){var e=this;e.getRecordTypeDec(function(t){var o=[],a=[],i=[],n={};t.forEach(function(t){var e={name:t.label,value:t.apiName};t.chosen&&a.push(t.apiName)&&i.push(t.label),n[t.apiName]=t.label,o.push(e)}),e.$$data.promotionOpts=o,e.$$data.promotionVal=a,e.$$data.promotionValTemp=a,e.$$data.promotionOpsMap=n,e.$el.html(l({label:i.join("、")}))})},getRecordTypeDec:function(e){i.api({url:"/FHH/EM1HSailAdmin/sail-admin/config/listApplicablePromotionOrderRecordTypes",success:function(t){t.Error||e&&e(t.Value)}},{autoPrependPath:!1})},initSelect:function(t){var e=this;e.select||(e.select=new a({$wrap:e.$(".promotion-select"),multiple:"multiple",allCheck:!0,options:t,defaultValue:e.$$data.promotionVal}),e.select.on("change",function(t){e.$$data.promotionValTemp=t}))},_setPromotionPage:function(){$(".promotion-wrap",this.$el).addClass("column-item__active"),$(".promotion-wrap-text",this.$el).hide(),$(".promotion-wrap-set",this.$el).show(),this.initSelect(this.$$data.promotionOpts),this.select&&this.select.setValue(_.clone(this.$$data.promotionVal))},_cancelConfig:function(){$(".promotion-wrap",this.$el).removeClass("column-item__active"),$(".promotion-wrap-text",this.$el).show(),$(".promotion-wrap-set",this.$el).hide()},_saveConfig:function(t){var o=this,t=$(t.target);o.$$data.promotionValTemp&&o.$$data.promotionValTemp.length&&i.api({url:"/FHH/EM1HSailAdmin/sail-admin/config/createOrUpdateApplicablePromotionOrderRecordType",data:{orderRecordTypes:o.$$data.promotionValTemp},success:function(t){var e;t.Error?i.alert(t.Error.Message):(o.$$data.promotionVal=_.clone(o.$$data.promotionValTemp),e=[],o.$$data.promotionVal.forEach(function(t){e.push(o.$$data.promotionOpsMap[t])}),$(".promotion-value",o.$el).html(e.join("、")),o._cancelConfig())}},{submitSelector:t,autoPrependPath:!1,errorAlertModel:1})},destroy:function(){this.select&&this.select.destroy(),this.$$data=null}});o.exports=t});
define("crm-setting/visitorder/promotion/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="column-item promotion-wrap"> <div class="column-lb" title="' + ((__t = $t("适用促销设置")) == null ? "" : __t) + '">' + ((__t = $t("适用促销设置")) == null ? "" : __t) + '</div> <div class="column-right"> <div class="column-con promotion-wrap-text"> <div class="promotion-value">' + ((__t = label) == null ? "" : __t) + '&nbsp;</div> <div class="column-tip">' + ((__t = $t("crm.访销适用促销设置说明")) == null ? "" : __t) + '</div> </div> <div class="column-con promotion-wrap-set" style="display: none;"> <div class="select promotion-select"></div> <div class="column-tip">' + ((__t = $t("crm.访销适用促销设置说明")) == null ? "" : __t) + '</div> <div class="btn-box"> <button class="b-g-btn">' + ((__t = $t("保存")) == null ? "" : __t) + '</button> <button class="b-g-btn-cancel">' + ((__t = $t("取消")) == null ? "" : __t) + '</button> </div> </div> <span class="set-btn j-set-promotion promotion-wrap-text">' + ((__t = $t("设置")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/visitorder/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("访销订单管理")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-column promotion"></div> <div class="crm-column notify"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/visitorder/visitorder",["./template/tpl-html","./promotion/promotion","./notify/notify","crm-modules/common/util"],function(t,o,n){var i=t("./template/tpl-html"),e=t("./promotion/promotion"),r=t("./notify/notify"),s=t("crm-modules/common/util"),t=Backbone.View.extend({events:{},initialize:function(t){this.$$components={},this.setElement(t.wrapper)},render:function(){var o=this;o.getConfig(function(t){t?(o.$el.html(i()),o.initPromotion(),o.initNotify()):o.$el.html('<div class="crm-tit"><h2><span class="tit-txt">'+$t("访销订单管理")+'</span></h2></div><div class="crm-module-con crm-scroll"><div class="crm-warn-bar"> '+$t("该模块未开通如有需要请咨询纷享客服400-1122-778")+"</div></div>")})},getConfig:function(o){s.api({url:"/FHH/EM1HSailAdmin/sail-admin/config/isVisitOrderNeedSetting",success:function(t){t.Error||o&&o(t.Value)}},{autoPrependPath:!1})},initPromotion:function(){var t=this;t.$$components.promotion=new e({wrapper:$(".promotion",t.$el)}),t.$$components.promotion.render()},initNotify:function(){var t=this;t.$$components.notify=new r({wrapper:$(".notify",t.$el)}),t.$$components.notify.render()},destroyComponents:function(){var t,o=this;for(t in o.$$components)o.$$components.hasOwnProperty(t)&&(o.$$components[t].destroy(),delete o.$$components[t]);o.$$dt&&(o.$$dt.destroy(),delete o.$$dt)},destroy:function(){var t=this;t.$$state={},t.destroyComponents(),t.$el.html("")}});n.exports=t});