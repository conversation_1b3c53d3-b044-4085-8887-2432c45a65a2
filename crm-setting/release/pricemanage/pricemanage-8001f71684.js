function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function asyncGeneratorStep(e,t,n,i,r,l,o){try{var a=e[l](o),c=a.value}catch(e){return void n(e)}a.done?t(c):Promise.resolve(c).then(i,r)}function _asyncToGenerator(a){return function(){var e=this,o=arguments;return new Promise(function(t,n){var i=a.apply(e,o);function r(e){asyncGeneratorStep(i,t,n,r,l,"next",e)}function l(e){asyncGeneratorStep(i,t,n,r,l,"throw",e)}r(void 0)})}}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,_toPropertyKey(i.key),i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/pricemanage/availablerangefilter",[],function(e,t,n){var c='<fx-dialog\n        :visible.sync="dialogVisible"\n        :title="title"\n        size="small"\n        appendToBody\n        class="available-range-filter-dialog"\n        :zIndex='.concat(+FxUI.Utils.getPopupZIndex()+50,'>\n            <div class="filter-content" :style="contentStyle">\n                <div :style="styleObj" >\n                    <label :style="labelStyle"><i style="color:#f56c6c;margin-right:4px;">*</i>').concat($t("过滤字段"),'</label>\n                    <fx-select\n                        ref="field"\n                        v-model="field"\n                        :options="fieldOptions"\n                        filterable\n                        size="small"\n                        :style="formStyle"\n                    ></fx-select>\n                </div>\n                <div :style="styleObj" class="func-form">\n                    <label :style="labelStyle"><i style="color:#f56c6c;margin-right:4px;">*</i>').concat($t("关联函数"),'</label>\n                    <fx-select\n                        @click.native="clickFuncHandle"\n                        ref="func"\n                        v-model="func"\n                        :options="funcOptions"\n                        disabled\n                        size="small"\n                        :style="[formStyle]"\n                    ></fx-select>\n                </div>\n            </div>\n            <span slot="footer" class="dialog-footer">\n                <fx-button type="primary" @click="submitFilter" size="small">').concat($t("确 定"),'</fx-button>\n                <fx-button @click="cancelFilter" size="small">').concat($t("取 消"),"</fx-button>\n            </span>\n    </fx-dialog>"),i=_createClass(function e(t){_classCallCheck(this,e),this.dialog,this.clickPromise,this.result={},this.initView(t)},[{key:"initView",value:function(){var e,n=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=this,r=t.isEdit,l=null!=(e=t.apiName)?e:"SalesOrderObj",o="",a="";r&&(o=t.field,a=t.func),this.dialog&&(this.dialog=null),this.clickPromise=new Promise(function(e,t){n.dialog=FxUI.create({template:c,data:function(){return{dialogVisible:!0,isEdit:r,field:o,fieldOptions:[],func:a,funcOptions:[],contentStyle:{display:"flex",flexDirection:"column",justifyContent:"space-around",height:"120px"},styleObj:{display:"flex",justifyContent:"space-between"},labelStyle:{lineHeight:"32px"},formStyle:{width:0,flex:1,marginLeft:"2em"}}},computed:{title:function(){return this.isEdit?$t("编辑可售范围过滤条件"):$t("启用过滤可售范围")}},created:function(){this.getObjField(),this.getFilterFunction(this.func)},methods:{clickFuncHandle:function(){var t=this;CRM.util.waiting(),new Promise(function(t,n){seajs.use("paas-function/sdk",function(e){CRM.util.waiting(!1),e&&e.getScopeRuleFunction({object_api_name:"AvailableRangeObj",zIndex:+FxUI.Utils.getPopupZIndex()+60},function(e){e.status?t(e.data.function):n()})})}).then(function(e){t.updateFuncOptions(e),t.func=e.api_name})},submitFilter:function(){i.result={filter_field:this.field,filter_function:this.func},this.validateFilter()?(e(i.result),this.dialogVisible=!1):t()},validateFilter:function(){return this.field?!!this.func||(CRM.util.remind(3,$t("请填写")+$t("关联函数"),void 0,2500),!1):(CRM.util.remind(3,$t("请填写")+$t("过滤字段"),void 0,2500),!1)},cancelFilter:function(){t("cancel"),this.dialogVisible=!1},getObjField:function(){var i=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i.fieldOptions&&0!==i.fieldOptions.length)return e.abrupt("return");e.next=2;break;case 2:return t="/EM1HNCRM/API/v1/object/"+l+"/controller/DescribeLayout",n={apiname:l,include_detail_describe:!1,include_layout:!1,layout_type:"add",recordType_apiName:"default__c"},e.next=6,CRM.util.ajax_base(t,n);case 6:t=e.sent,n=t.objectDescribe,i.fieldOptions=i._parseData(n);case 9:case"end":return e.stop()}},e)}))()},_parseData:function(){var e=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).fields,t=[];return _.each(e,function(e){if(!e.is_active)return!1;"object_reference"!=e.type&&"select_one"!=e.type&&"record_type"!=e.type||t.push(_.extend(e,{label:e.label,value:e.api_name}))}),t},getFilterFunction:function(n){var i=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=2;break}return e.abrupt("return");case 2:return t={api_name:n,binding_object_api_name:"AvailableRangeObj"},e.next=6,CRM.util.ajax_base("/EM1HNCRM/API/v1/object/function/service/find",t,$.noop,!0);case 6:t=e.sent,i.updateFuncOptions(t.function);case 8:case"end":return e.stop()}},e)}))()},updateFuncOptions:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};e.id;this.funcOptions=[_.extend({value:e.api_name,label:e.function_name},e)]}}})})}},{key:"getClickPromise",value:function(){return this.clickPromise}},{key:"getValue",value:function(){return this.result}},{key:"setValue",value:function(){}},{key:"destroy",value:function(){var e,t;null!=(e=this.dialog)&&null!=(t=e.destroy)&&t.call(e)}}]);n.exports=i});
define("crm-setting/pricemanage/config",[],function(e,t,i){var r=this&&this.__awaiter||function(e,a,n,p){return new(n=n||Promise)(function(i,t){function r(e){try{o(p.next(e))}catch(e){t(e)}}function c(e){try{o(p.throw(e))}catch(e){t(e)}}function o(e){var t;e.done?i(e.value):((t=e.value)instanceof n?t:new n(function(e){e(t)})).then(r,c)}o((p=p.apply(e,a||[])).next())})},c=(Object.defineProperty(t,"__esModule",{value:!0}),t.CONFIG_DATA=t.KEY_CONFIG=void 0,CRM.util);t.KEY_CONFIG={28:{cache_key:"openPriceList",value:!1},enforce_priority:{cache_key:"priceBookPriority",value:!1},ignore_price_book_valid_period:{cache_key:"ignore_price_book_valid_period",value:!1},match_price_book_valid_field:{cache_key:"match_price_book_valid_field",value:{SalesOrderObj:"",QuoteObj:"",NewOpportunityObj:"",SaleContractObj:""}},price_book_product_valid_period:{cache_key:"priceBookProductValidPeriod",value:!1},price_book_product_tiered_price:{cache_key:"priceBookProductTieredPrice",value:!1},stratified_pricing:{cache_key:"stratifiedPricing",value:!1},whether_filter_price_book_select_product:{cache_key:"priceBookSelectProduct",value:!1},available_price_book:{cache_key:"openAvailablePriceBook",value:!1},available_range:{cache_key:"openAvailablerange",value:!1},is_open_available_range_priority:{cache_key:"openAvailableRangePriority",value:!1},available_range_duplicated_check:{cache_key:"openAvailableRangeDuplicatedCheck",value:!1},available_range_filter:{cache_key:"available_range_filter",value:{status:"0",filter_field:"",filter_function:""}}},t.CONFIG_DATA=[{domId:"level0",moduleId:"pricebook",visible:!0,title:$t("crm.价目表"),moduleList:[{isShow:!0,type:"switch",title:$t("价目表开启开关"),key:"28",value:!1,displayCount:3,isDisabled:!1,enableClose:!1,confirmInfo:function(){return $t("确认开启价目表吗")},describeList:[{title:$t("价目表一旦开启不可关闭。"),list:[]},{title:$t("crm.价目表会影响订单里的内容")},{title:$t("crm.价目表开启后会默认给租客一个标准价目表")},{title:$t("crm.开启价目表后需提前调整OpenAPI")}]},{isShow:!0,title:$t("是否强制执行价目表优先级最优价格"),type:"switch",key:"enforce_priority",value:!1,enableClose:!0,confirmInfo:function(){return CRM._cache.priceBookPriority?$t("确认关闭强制执行价目表优先级最优价格吗"):$t("确认开启强制执行价目表优先级最优价格吗")},valid:function(){return!!CRM._cache.openPriceList||(CRM.util.alert($t("请先开启价目表")),!1)},describeList:[]},{isShow:CRM.util.isGrayScale("CRM_PRICEBOOK_PERIOD"),title:$t("价目表适配单据时间配置"),type:"switch",key:"ignore_price_book_valid_period",value:!1,confirmInfo:function(){return CRM._cache.ignore_price_book_valid_period?$t("确认关闭时间配置吗"):$t("确认开启时间配置吗")},describeList:[{title:$t("价目表适配时间说明1")},{title:$t("价目表适配时间说明2")},{title:$t("配置的时间"),list:[$t("价目表适配时间说明3_1"),$t("价目表适配时间说明3_2")]}],children:[{isShow:function(){return CRM.util.isGrayScale("CRM_PRICEBOOK_PERIOD")&&CRM._cache.ignore_price_book_valid_period},title:$t("适配价目表有效期"),key:"match_price_book_valid_field",type:"matchpricebookvalid",value:{SalesOrderObj:"",QuoteObj:"",NewOpportunityObj:"",SaleContractObj:""},describeList:[],selectList:[{title:$t("订单")+$t("对象"),type:"select",key:"SalesOrderObj",options:[]},{title:$t("报价单")+$t("对象"),type:"select",key:"QuoteObj",options:[]},{title:$t("商机2.0")+$t("对象"),type:"select",key:"NewOpportunityObj",options:[]},{isShow:CRM._cache.sale_contract,title:$t("销售合同.0")+$t("对象"),type:"select",key:"SaleContractObj",options:[]}]}]},{isShow:!0,title:$t("crm.ppmanage.price_book_product_valid_period"),type:"switch",key:"price_book_product_valid_period",value:!1,enableClose:!1,confirmInfo:function(){return CRM._cache.priceBookProductValidPeriod?$t("确认关闭{{name}}吗",{name:$t("crm.ppmanage.price_book_product_valid_period")}):$t("确认开启{{name}}吗",{name:$t("crm.ppmanage.price_book_product_valid_period")})},describeList:[$t("crm.ppmanage.price_book_product_valid_period.intro_1"),$t("crm.ppmanage.price_book_product_valid_period.intro_2"),$t("crm.ppmanage.price_book_product_valid_period.intro_3")]},{isShow:!0,title:$t("crm.ppmanage.price_book_product_tiered_price"),type:"switch",key:"price_book_product_tiered_price",value:!1,enableClose:!1,confirmInfo:function(){return CRM._cache.priceBookProductTieredPrice?$t("确认关闭{{name}}吗",{name:$t("crm.ppmanage.price_book_product_tiered_price")}):$t("确认开启{{name}}吗",{name:$t("crm.ppmanage.price_book_product_tiered_price")})},describeList:[$t("crm.ppmanage.price_book_product_tiered_price.intro_1"),$t("crm.ppmanage.price_book_product_tiered_price.intro_2",{start_count:$t("crm.ppmanage.price_book_product_tiered_price.start_count"),end_count:$t("crm.ppmanage.price_book_product_tiered_price.end_count")}),$t("crm.ppmanage.price_book_product_tiered_price.intro_3"),$t("crm.ppmanage.price_book_product_tiered_price.intro_4")]},{isShow:!0,title:$t("crm.ppmanage.stratified_pricing"),type:"switch",key:"stratified_pricing",value:!1,enableClose:!1,confirmInfo:function(){return CRM._cache.stratifiedPricing?$t("确认关闭{{name}}吗",{name:$t("crm.ppmanage.stratified_pricing")}):$t("确认开启{{name}}吗",{name:$t("crm.ppmanage.stratified_pricing")})},valid:function(){return r(void 0,void 0,void 0,regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return CRM.util.showLoading_tip(),e.next=3,c.getConfigValues(["cpq","periodic_product","price_policy","rebate","rebate"]);case 3:if(t=e.sent,CRM.util.hideLoading_tip(),t.find(function(e){return"1"===e.value}))return CRM.util.alert($t("crm.ppmanage.stratified_pricing.tip1",null,"开启价格政策、返利、优惠券、周期性产品、CPQ的企业不允许开启分层定价")),e.abrupt("return",!1);e.next=9;break;case 9:return e.abrupt("return",!0);case 10:case"end":return e.stop()}},e)}))},describeList:[$t("crm.ppmanage.stratified_pricing.intro_1",null,"开启开关后，价目表支持按照分层定价，根据购买数量所在不同区间确定价格，最终金额等于各区间的小计之和。举例：移动流量包定价，0-100M：3元/M，100M-200M：2元/M，200M以上1元/M，购买130M的最终金额=100*3 + 30*2 = 360元"),$t("crm.ppmanage.stratified_pricing.intro_2",null,"开启后不可关闭"),function(e){return[e("span",{class:"crm-intro-warning"},$t("crm.ppmanage.stratified_pricing.intro_3",null,"注意：分层定价暂不支持多单位场景。"))]}]},{isShow:function(){return CRM._cache.priceBookProductValidPeriod||CRM._cache.priceBookProductTieredPrice||CRM._cache.stratifiedPricing},title:$t("crm.ppmanage.price_book_product_selected"),type:"switch",key:"whether_filter_price_book_select_product",value:!1,confirmInfo:function(){return CRM._cache.priceBookSelectProduct?$t("确认关闭{{name}}吗",{name:$t("crm.ppmanage.price_book_product_selected")}):$t("确认开启{{name}}吗",{name:$t("crm.ppmanage.price_book_product_selected")})},describeList:[$t("crm.ppmanage.price_book_product_selected.intro_1"),$t("crm.ppmanage.price_book_product_selected.intro_2"),$t("crm.ppmanage.price_book_product_selected.intro_3"),$t("crm.ppmanage.price_book_product_selected.intro_4")]}]},{domId:"level1",moduleId:"availableRange",visible:!0,title:$t("可售范围设置"),moduleList:[{isShow:!0,title:$t("可售范围开启开关"),type:"switch",key:"available_range",value:"",enableClose:!1,confirmInfo:function(){return $t("确认开启可售范围吗")},describeList:[$t("开启价目表后不开启可售范围"),$t("开启可售范围"),$t("可售范围规定"),$t("开启可售范围需要设置"),$t("如果开启可售范围且没有设置范围规则")+$t("没有可售产品"),$t("当可售产品范围取不到价目表价格时"),function(e){return[e("span",{class:"crm-intro-warning"},$t("客户名称只按照单独添加算"))]}]},{isShow:function(){return CRM._cache.openAvailablerange},title:$t("是否执行可售范围优先级"),type:"switch",key:"is_open_available_range_priority",value:!1,confirmInfo:function(){return CRM._cache.openAvailableRangePriority?$t("确认关闭执行可售范围优先级吗"):$t("确认")+$t("开启执行可售范围优先级")+$t("吗？")},describeList:[{title:$t("开启执行可售范围优先级")+"。"+$t("客户的适用价目表获取逻辑为")+":",list:[function(e){return["1.1 ",$t("不同类型可售范围文案"),e("fx-popover",{props:{placement:"bottom",width:"300",trigger:"hover",content:$t("可售范围取价举例")}},[e("span",{slot:"reference",class:"crm-doclink ",style:{display:"inline-block",width:"16px",height:"16px"}},null)])]},$t("同类型可售范围文案")]},{title:$t("不开执行可售范围优先级文案")}]},{isShow:function(){return CRM._cache.openAvailablerange},title:$t("可售范围查重开关"),type:"switch",key:"available_range_duplicated_check",value:!1,confirmInfo:function(){return CRM._cache.openAvailableRangeDuplicatedCheck?$t("确认关闭可售范围查重吗"):$t("确认开启可售范围查重吗")},describeList:[{title:$t("客户相同条件的可售范围开启查重校验"),list:[$t("客户相同条件的可售范围为"),$t("客户相同条件的可售范围中")]},{title:$t("可售范围创建者")},{title:$t("此开关可根据业务需要反复开启和关闭")}]},{isShow:function(){return CRM._cache.openAvailablerange},title:$t("基于订单类型、项目过滤可售范围"),type:"availableRangeFilter",key:"available_range_filter",value:{status:"0",filter_field:"",filter_function:""},confirmInfo:function(e){return"1"===e.status?null:$t("确定要停用可售范围的过滤吗？如果启用了订货通，可能会影响经销商的正常使用，请谨慎操作。")},describeList:[{title:$t("功能开启，CRM销售订单可根据过滤字段过滤客户的可售产品")},{title:$t("过滤字段仅支持“销售订单业务类型”、“单选”和“查找关联”字段，通过自定义函数编写过滤逻辑")},{title:$t("此功能只作用于“销售订单”对象")},{title:$t("功能开启，当启用订货通后，商城也会基于过滤条件展示对应的商品数据，同时"),list:[$t("订货通所有WEB页面→组件列表：增加“过滤可售范围”组件，管理员可以在页面中，删除此组件，也可以编辑“组件名称”、“组件ICON”"),$t("订货通移动端，个人中心，增加“商品范围”菜单，同时支持配置“菜单显示位置”、“菜单名称”、“显示ICON”等"),$t("用户首次进入WEB渠道门户或APP小程序时，根据后台的过滤条件，要求客户选择过滤条件，选择好数据后，根据函数过滤可售范围，加载商品列表、购物车等数据"),$t("用户非首次进入，WEB端可点击右上角的“过滤可售范围”进行切换，移动端可在个人中心/商品列表/购物车中切换")]},{title:$t("此功能开关默认为“关闭”状态，启用后可再次关闭"),list:[function(e){return[e("div",{class:"crm-intro-warning"},[$t("注：该插件支持按各种条件过滤可售范围，具体能力可在自定义函数中实现。当变更过滤条件时，请保持过滤字段与函数同时变更")])]}]}]}]}]});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function asyncGeneratorStep(e,t,n,r,o,i,a){try{var c=e[i](a),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function _asyncToGenerator(c){return function(){var e=this,a=arguments;return new Promise(function(t,n){var r=c.apply(e,a);function o(e){asyncGeneratorStep(r,t,n,o,i,"next",e)}function i(e){asyncGeneratorStep(r,t,n,o,i,"throw",e)}o(void 0)})}}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/pricemanage/pricemanage",["./config","crm-modules/common/util"],function(n,e,t){var r=n("./config"),s=n("crm-modules/common/util"),o=Backbone.View.extend({el:".crm-s-pricemanage",initialize:function(e){var t=this;this.Comp=null,n.async("vcrm/sdk",function(e){t._destroy||e.getComponent("backstage").then(function(e){t.initView(e.default,r)})})},initView:function(e,t){var n=t.CONFIG_DATA,r=t.KEY_CONFIG;this.Comp=FxUI.create({wrapper:this.$el[0],template:'\n                    <Backstage ref="backstage" class="crm-setting price-manage-wrapper" :data="newDataList" @change="change"></Backstage>\n               ',components:{Backstage:e},data:function(){return{modelValues:r,baseConfig:n}},watch:{},computed:{newDataList:function(){var r=this;return this.baseConfig.map(function(e){var t=_.isFunction(e.visible)?e.visible():e.visible,n=r.formData(e.moduleList);return _objectSpread(_objectSpread({},e),{},{visible:t,moduleList:n})})},radioModules:function(){var t=[];return this.baseConfig.forEach(function(e){e=e.moduleList.filter(function(e){return"radio"===e.type});t=t.concat(e)}),t}},created:function(){},mounted:function(){this.init()},methods:{formData:function(e){var i=this;return e.map(function(e){var t=null,n=e.key,r=_.isFunction(e.isShow)?e.isShow():e.isShow,o=null,o=n instanceof Array?n.map(function(e){return i.modelValues[e].value}):i.modelValues[n].value;return e.children&&e.children.length&&(t=i.formData(e.children)),_objectSpread(_objectSpread({},e),{},{children:t,value:o,isShow:r})})},init:function(){var t=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t.getConfig();case 1:case"end":return e.stop()}},e)}))()},change:function(e,n){var r=this,o=e.type,i=e.key,a=e.value;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return"switch"!==o&&"checkbox"!==o||(a=a?"1":"0"),e.next=3,r.beforeSetConfig({key:i,value:a,type:o},n);case 3:if(t=e.sent){e.next=6;break}return e.abrupt("return");case 6:t.confirmInfo?r.confirm=s.confirm(t.confirmInfo,$t("提示"),r.setConfig.bind(null,t,o)):r.setConfig(t,o);case 7:case"end":return e.stop()}},e)}))()},getConfig:function(){var i=this,e=Object.keys(this.modelValues),a=this.radioModules.map(function(e){return e.key});CRM.util.showLoading_tip(),CRM.util.getConfigValues(e).then(function(e){e&&e.forEach(function(e){var t,n=e.key,e=e.value,r=i.modelValues[n].cache_key,o=null;"available_range_filter"===n?((t=JSON.parse(e||"{}")).filter_field,o=t,CRM._cache[r]="1"===t.status):a.includes(n)?(o=e,CRM._cache[r]=o):"match_price_book_valid_field"===n?(CRM._cache[r]=e,o=JSON.parse(e||"{}")):(o="1"===e,CRM._cache[r]=o),i.$set(i.modelValues[n],"value",o)}),CRM.util.hideLoading_tip()}).fail(function(e){CRM.util.alert(e),CRM.util.hideLoading_tip()})},beforeSetConfig:function(e,n){var r=e.key,o=e.value;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=null,n&&n.valid)return e.next=4,n.valid();e.next=7;break;case 4:if(e.sent){e.next=7;break}return e.abrupt("return");case 7:return t="available_range"===r||"available_price_book"===r||"is_open_available_range_priority"===r?{url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{tenantId:CRM.enterpriseId,moduleCode:r,openStatus:o}}:{url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:r,value:o},confirmInfo:null},"available_range_filter"===r&&(t.data.value=JSON.stringify(o)),n.confirmInfo&&(t.confirmInfo=n.confirmInfo(o)),e.abrupt("return",t);case 11:case"end":return e.stop()}},e)}))()},setConfig:function(o,e){var i=this,a=this,t=o.data,n=t.key,c=t.value,u=n||t.moduleCode;CRM.util.showLoading_tip(),a.$refs.backstage.commonSetConfig({url:o.url,data:o.data,complete:function(){var e;null!=(e=a.confirm)&&e.hide()}}).then(function(e){var t,n,r;CRM.util.hideLoading_tip(),_.isEmpty(e.Value)||e.Value.success&&0===e.Value.errCode?(t=null,n=i.modelValues[u].cache_key,r=i.radioModules.map(function(e){return e.key}),o.data.moduleCode?(t="1"===e.Value.value.openStatus,CRM._cache[n]=t):r.includes(u)?(t=c,CRM._cache[n]=t):"available_range_filter"===u?(t=JSON.parse(o.data.value),CRM._cache[n]="1"===t.status):"match_price_book_valid_field"===u?(t=JSON.parse(o.data.value),CRM._cache[n]=o.data.value):(t=!CRM._cache[n],CRM._cache[n]=t),i.$set(i.modelValues[u],"value",t),a.initProduct(),CRM.control.refreshAside(),s.remind(1,$t("操作成功"))):s.alert(e.Value.errMessage)}).catch(function(e){CRM.util.hideLoading_tip(),s.alert(e||$t("操作失败!"))})},initProduct:function(){s.FHHApi({url:"/EM1HNCRM/API/v1/object/pricebook_standard/service/check_or_init_standard_pricebook",data:{}},{errorAlertModel:1})}}})},destroy:function(){this.Comp&&this.Comp.destroy&&this.Comp.destroy(),this.Comp=null,this._destroy=!0}});t.exports=o});