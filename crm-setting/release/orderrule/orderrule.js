define("crm-setting/orderrule/orderrule",["crm-modules/common/util","crm-modules/components/tabs/tabs","./template/tpl-html","./template/orderrule-html","./template/invoice-html","./template/salecontract-html","./template/payment-html","crm-widget/dialog/dialog","crm-modules/components/filter/filter"],function(c,e,t){var s=c("crm-modules/common/util"),n=c("crm-modules/components/tabs/tabs"),o=c("./template/tpl-html"),i=(c("./template/orderrule-html"),c("./template/invoice-html"),c("./template/salecontract-html"),c("./template/payment-html"),c("crm-widget/dialog/dialog")),a=c("crm-modules/components/filter/filter"),r=Backbone.View.extend({initialize:function(e){this.tabIndex=0,this.setElement(e.wrapper)},events:{"click .j-switch-tab":"switchTab","click .mn-checkbox-item":"checkHandle","click .mn-radio-item:not(.product-search-conf, .quick-action-rule)":"_onSet","click .mn-radio-item.product-search-conf":"productSearchHandler","click .mn-radio-item.quick-action-rule":"quickActionHandler","click .j-switch-summary":"dataSummaryHandler","click .label":"_onLabel","click .j-radio-copy":"relationHandler","click .j-switch-invoice":"invoiceSwitch","click .j-switch-salecontract":"salecontractSwitch","click .j-switch-history-order":"historyOrderSwitch","click .j-switch-virtual-field":"virtualFieldSwitch","click .j-switch-paymentonline":"paymentOnlineSwitch","click .j-switch-enteraccount":"enterAccountSwitch","click .js-clickSwitch":"clickSwitch"},render:function(){var t=this;this.cacheConfig={},this.$el.html(o()),this._getConfig(function(e){switch(t.cacheConfig=e,n.prototype.getParameters()){case"tabs-promotion":t.tabIndex=0;break;case"tabs-payment":t.tabIndex=1;break;case"tabs-invoice":4==$(".j-switch-tab").length?t.tabIndex=2:t.tabIndex=1;break;case"tabs-salecontract":4==$(".j-switch-tab").length?t.tabIndex=3:t.tabIndex=2}t.renderTpl(e)})},creatDocLink:function(e,t){e&&(this.docLink=FxUI.create({replaceWrapper:!0,wrapper:e,template:'\n                <fx-popover\n                    placement="right"\n                    width="300"\n                    trigger="hover">\n                    <div class="custom-fields-popover">\n                        <strong>'.concat($t("开启后，会将必填字段展示在选产品卡片"),'</strong>\n                        <div style="margin-top:10px;">').concat($t("示例"),':</div>\n                        <ul class="example-wrapper">\n                            <li class="input-number-demo"><fx-input-number size="mini" value=1></fx-input-number></li>\n                            <li>\n                                <div>').concat($t("单选"),'</div>\n                                <div class="custom-fields-demo">\n                                    <span class="item-hint">').concat($t("选项"),"1</span>\n                                    <span>").concat($t("选项"),'2</span>\n                                </div>\n                            </li>\n                            <li class="muti-demo">\n                                <div>').concat($t("多选"),'</div>\n                                <div class="custom-fields-demo">\n                                    <span v-for="n in 6">').concat($t("选项"),"{{n}}</span>\n                                </div>\n                            </li>\n                            <li>\n                                <div>").concat($t("是否"),'</div>\n                                <div class="custom-fields-demo">\n                                    <span>').concat($t("开启"),'</span>\n                                    <span class="item-hint">').concat($t("结束"),'</span>\n                                </div>\n                            </li>\n                        </ul>\n                    </div>\n                    <span class="crm-doclink" slot="reference"></span>\n                </fx-popover>\n                ')}))},renderTpl:function(e){var t=this,n=this,o={"order-rule":{type:"tradeconfigure_business",module:"tradeconfigure"},"invoice-setting":{type:"tradeconfigure_business_invoice",module:"tradeconfigure"},"salecontract-setting":{type:"tradeconfigure_business_salecontract",module:"tradeconfigure"},"payment-setting":{type:"tradeconfigure_business_payment",module:"tradeconfigure"}};["order-rule","invoice-setting","salecontract-setting","payment-setting"].forEach(function(e){t.renderGuide(Object.assign(o[e],{wrapper:e}))}),n.$(".tab-con .render-box").eq(n.tabIndex).show(),n.$(".j-switch-tab").removeClass("cur"),n.$(".j-switch-tab").eq(n.tabIndex).addClass("cur")},renderGuide:function(e){var t=e.type,n=e.module,o=e.wrapper,i=this;c.async("crm-modules/components/biz_manage/biz_manage",function(e){this.guide=new e({$el:$("."+o,i.$el),module:n,type:t})})},renderMobileSelectShowPrice:function(){var t=this;c.async("vcrm/sdk",function(e){e.getComponent("MobileSelect").then(function(e){e=e.default,new Vue({el:t.$(".mobile-select-showPrice")[0],template:'<div class="mobile-select-showPrice-old">\n                                <MobileSelect></MobileSelect>\n                          </div>',components:{MobileSelect:e},data:function(){return{}},mounted:function(){},methods:{}})})})},renderRadioIgnoreCheckCeilingFloorPrice:function(){var t=this;this.ignoreCheckCeilingFloorPrice&&this.ignoreCheckCeilingFloorPrice.destroy(),this.ignoreCheckCeilingFloorPrice=FxUI.create({wrapper:t.$el.find(".ignore_check_ceiling_floor_price")[0],template:'<fx-radio-group v-model="value" @change="change">\n                    <fx-radio label="0">{{label0}}</fx-radio>\n                    <fx-radio label="1">{{label1}}</fx-radio>\n                </fx-radio-group>',data:function(){return{value:CRM._cache.ignore_check_ceiling_floor_price?"1":"0",label0:$t("crm.orderrule.ignore_check_ceiling_floor_price.label0"),label1:$t("crm.orderrule.ignore_check_ceiling_floor_price.label1")}},methods:{change:function(e){this.value=e,t._setConfig("ignore_check_ceiling_floor_price",e),CRM._cache.ignore_check_ceiling_floor_price="1"===e}}})},renderSelectProductCheckBox:function(){var t=this;this.selProCheckBox&&this.selProCheckBox.destroy(),this.selProCheckBox=FxUI.create({wrapper:t.$el.find(".owner-select-product")[0],template:"<fx-checkbox v-model=\"checked\" @change = 'changeHandle' :disabled=\"disabled\" >{{$t('选择数据页面，支持选择本单已选产品')}}</fx-checkbox>",data:function(){return{checked:CRM._cache.tenant_whether_filter_order_select_product,disabled:"1"!==t.cacheConfig[43][0]}},methods:{changeHandle:function(e){t._setConfig("tenant_whether_filter_order_select_product",e?"1":"0"),CRM._cache.tenant_whether_filter_order_select_product=e}}})},renderMobileShowSummaryField:function(){var o=this,t="order_mobile_edit_page_summary_setting";c.async("vcrm/sdk",function(e){e.getComponent("backstage").then(function(e){o.mobileShowFieldCheckBox&&o.mobileShowFieldCheckBox.destroy&&o.mobileShowFieldCheckBox.destroy();e=e.MobileSelectNew;o.mobileShowFieldCheckBox=new Vue({el:o.$(".mobile-select-showSummaryField")[0],template:'<MobileSelectNew :listData="listData" @change="handleChange" />',components:{MobileSelectNew:e},data:function(){return{listData:{title:$t("crm.setting.tradeconfigure.order_mobile_edit_page_summary_setting.title",null,"销售订单移动端：新建编辑页面左下角【合计】字段显示配置"),subTitle:$t("crm.setting.tradeconfigure.title_execution_object")+$t("crm.SalesOrderObj"),showTitleLine:!0,type:"MobileSelectNew",key:[t],value:[CRM._cache[t]],options:[{label:$t("crm.setting.tradeconfigure.order_mobile_edit_page_summary_setting.options.summary",null,"显示【合计】字段"),key:t}]}}},methods:{handleChange:function(e){e.type;var t=e.key,e=e.value,n=e?"1":"0";data=Object.assign(this.listData,{value:[e]}),this.listData=data,o._setConfig(t,n),CRM._cache[t]=e}}})})})},_getConfig:function(o){var i=this,c=[{key:"16",value:"0,1,0"},{key:"43",value:"0"},{key:"46",value:"0"},{key:"49",value:"0"},{key:"50",value:"0"},{key:"23",value:"0"},{key:"new_invoice",value:"0"},{key:"invoice_is_allowed_overflow",value:"0"},{key:"invoice_mode",value:"normal"},{key:"clone_history_order_product",value:"0"},{key:"product_keyword_search_mode",value:""},{key:"price_policy",value:"0"},{key:"get_price_when_convert",value:"0"},{key:"get_price_when_copy",value:"0"},{key:"order_to_quote_default_value_cover",value:"0"},{key:"invoice_show_quick_op_rule",value:"amount"},{key:"invoce_show_sum_data",value:"1"},{key:"order_enlarge_edit_privilege",value:"0"},{key:"sale_contract",value:"0"},{key:"get_price_when_copy_quote",value:"0"},{key:"get_price_when_copy_contract",value:"0"},{key:"input_custom_fields",value:"0"},{key:"is_payment_pay_enable",value:"0"},{key:"is_customer_account_enable",value:"0"},{key:"is_payment_enter_account_enable",value:"0"},{key:"virtual_extension",value:"0"},{key:"tenant_whether_filter_order_select_product",value:"0"},{key:"get_price_when_copy_newopportunity",value:"0"},{key:"ignore_check_ceiling_floor_price",value:"0"}];s.getConfigValues(["16","43","46","49","50","23","new_invoice","invoice_is_allowed_overflow","invoice_mode","clone_history_order_product","product_keyword_search_mode","price_policy","get_price_when_convert","get_price_when_copy","order_to_quote_default_value_cover","invoice_show_quick_op_rule","invoce_show_sum_data","order_enlarge_edit_privilege","sale_contract","get_price_when_copy_quote","get_price_when_copy_contract","input_custom_fields","is_payment_pay_enable","is_customer_account_enable","is_payment_enter_account_enable","virtual_extension","tenant_whether_filter_order_select_product","get_price_when_copy_newopportunity","ignore_check_ceiling_floor_price","order_mobile_edit_page_summary_setting","manual_gift"]).then(function(e){o&&o(i.parseData(e))},function(e){var t,n;null!=(t=i._alert)&&null!=(n=t.destroy)&&n.call(t),CRM.util.alert(e),o&&o(i.parseData(c))})},parseData:function(e){var t={};return e=_.map(e,function(e){return e.value=e.value.split(","),t[e.key]=e.value,e}),this.cachePricePolicy(t),t},cachePricePolicy:function(e){CRM._cache.sale_contract=!!e.sale_contract&&"1"==e.sale_contract[0],CRM._cache.openPricePolicy=e.price_policy&&"1"==e.price_policy[0],CRM._cache.tenant_whether_filter_order_select_product=e.tenant_whether_filter_order_select_product&&"1"==e.tenant_whether_filter_order_select_product[0],CRM._cache.ignore_check_ceiling_floor_price="1"===(e.ignore_check_ceiling_floor_price||["0"])[0],CRM._cache.order_mobile_edit_page_summary_setting="1"==e.order_mobile_edit_page_summary_setting},historyOrderSwitch:function(e){var t=$(e.currentTarget),n=t.hasClass("on")?"0":"1";this._setConfig("clone_history_order_product",n,function(){"0"==n?t.removeClass("on"):t.addClass("on")})},virtualFieldSwitch:function(e){var t=$(e.currentTarget);t.hasClass("on")?s.alert($t("虚拟字段开启后，不可关闭")):this.saveModuleStatus({url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"virtual_extension",tenantId:CRM.enterpriseId,openStatus:1},confirmInfo:$t("确定要开启虚拟字段吗"),confirmTit:$t("提示")},function(){t.addClass("on")})},salecontractSwitch:function(e){var t=$(e.currentTarget),n=this;t.hasClass("on")?s.alert($t("销售合同开启后，不可关闭")):s.getEditLayoutStatus("SalesOrderObj").then(function(e){e?n.saveModuleStatus({url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"sale_contract",tenantId:CRM.enterpriseId,openStatus:1},confirmInfo:$t("确定要开启销售合同吗"),confirmTit:$t("提示")},function(){t.addClass("on"),CRM._cache.sale_contract=!0}):s.alert($t("请先开启{{apiName}}新建布局",{apiName:CRM.config.objDes["SalesOrderObj".toLowerCase()].name}))})},saveModuleStatus:function(t,n){var e=null,e=s.confirm(t.confirmInfo,t.confirmTit,function(){s.FHHApi({url:t.url,data:t.data,success:function(e){0==e.Result.StatusCode?(s.remind(1,t.successText||$t("启用成功")),n&&n()):s.alert(e.Result.FailureMessage)},complete:function(){e.hide()}},{errorAlertModel:1,submitSelector:e.$(".b-g-btn")})})},invoiceSwitch:function(e){var t,n=$(e.currentTarget),o=this,e=$t("开票明细开启后，不可关闭");n.hasClass("on")?s.alert(e):(e=$t("确定要开启开票明细吗"),t=s.confirm(e,"",function(){o.setInvoice(n,"1"),t.hide()}))},setInvoice:function(e,t){var n=this;"1"==t&&this._setConfig("new_invoice",t,function(){"1"!=t&&"2"!=t||(n.$(".setting-item-invoice-status").attr("data-status","2"),e.addClass("on"),CRM.util.sendLog("setting","invoice",{operationId:"openMode2"}))})},paymentOnlineSwitch:function(e){var n=$(e.currentTarget);n.hasClass("on")||this.getOnlineAccountEnable().then(function(e){var t;t=e&&e.length?s.confirm($t("确定要开启显示支付能力吗"),"",function(){s.FHHApi({url:"/EM1HNCRM/API/v1/object/payment_pay/service/enable_payment_pay",data:{},success:function(e){e=e.Result;0===e.StatusCode?(n.toggleClass("on"),s.remind(1,$t("设置成功"))):s.alert(e.FailureMessage||e.message||$t("设置失败请联系纷享客服"))},complete:function(){t.destroy(),t=null}},{errorAlertModel:1,submitSelector:t.$(".b-g-btn")})}):s.confirm($t("企业钱包暂未绑定支付宝或微信账号请先绑定后在启用"),$t("提示"),function(e){t.destroy(),t=null,window.location.hash="#app/entwallet/wallet"},{btnLabel:{confirm:$t("现在绑定"),cancel:$t("知道了")},stopPropagation:!0})})},enterAccountSwitch:function(e){var t,n=$(e.currentTarget);n.hasClass("on")||("2"!==this.cacheConfig.is_customer_account_enable[0]?s.alert($t("该租户未启用客户账户模块请联系管理员开通")):t=s.confirm($t("启用回款入账将在对象上新增等字段并预设按钮确定要启用吗"),$t("提示"),function(){s.FHHApi({url:"/EM1HNCRM/API/v1/object/fund_account/service/payment_enter_account_init",data:{},success:function(e){e=e.Result;0===e.StatusCode?(n.toggleClass("on"),s.remind(1,$t("设置成功"))):s.alert(e.FailureMessage||e.message||$t("设置失败请联系纷享客服"))},complete:function(){t.destroy(),t=null}},{errorAlertModel:1,submitSelector:t.$(".b-g-btn")})}))},getOnlineAccountEnable:function(){return s.waiting(),new Promise(function(t,n){s.FHHApi({url:"/EM1HNCRM/API/v1/object/payment_pay/service/query_valid_isv",data:{},success:function(e){0==e.Result.StatusCode?t(e.Value.valid_isv_list):(s.alert(e.Result.FailureMessage),n(e.Result.FailureMessage))},complete:function(){s.waiting(!1)}},{errorAlertModel:1})})},switchTab:function(e){var n=this,e=$(e.target);e.parent().children().removeClass("cur"),e.addClass("cur"),n.tabIndex=e.index(),_.each($(".render-box",n.$el),function(e,t){t===n.tabIndex?$(e).show():$(e).hide()})},_initFilter:function(){var e=[{FieldType:22,FieldName:"RecordType",FieldCaption:$t("业务类型"),EnumDetails:{Child:[],EnumDetailID:"71a8dd3539d1433380a17d6214edf42d",IsDeleted:!1,IsSysItem:!1,ItemCode:"default__c",ItemName:$t("预设业务类型")}}];this.filter=new a({el:this.$(".rule-wrap").eq(0),data:0==e.length?[{}]:e,fieldlist:e}),this.filter.render()},_onSet:function(e){var t,e=$(e.currentTarget);return e.hasClass("disabled-selected")||e.hasClass("mn-selected")||(this.$(t=".j-radio-item").removeClass("mn-selected"),e.addClass("mn-selected"),this._setConfig("16",this._getParam(t))),!1},productSearchHandler:function(e){var e=this.$(e.currentTarget);return e.hasClass("mn-selected")||(this.$(".product-search-conf").removeClass("mn-selected"),e.addClass("mn-selected"),e=this.getProductSearchConf(e.index(".product-search-conf")),this._setConfig("product_keyword_search_mode",e)),!1},getProductSearchConf:function(e){return["","and","or"][e]},quickActionHandler:function(e){var t=this,n=this.$(e.currentTarget),o=this.$(".quick-action-rule");return this._setConfig("invoice_show_quick_op_rule",n.data("value"),function(){t.radioGroupHandler(n,o)}),!1},dataSummaryHandler:function(e){var t=$(e.currentTarget),n=t.hasClass("on")?"0":"1";return this._setConfig("invoce_show_sum_data",n,function(){"0"==n?t.removeClass("on"):t.addClass("on")}),!1},radioGroupHandler:function(e,t,n){if(e.hasClass("disabled-selected")||e.hasClass("mn-selected"))return!1;t.removeClass("mn-selected"),e.addClass("mn-selected"),n&&n()},checkHandle:function(e){e.stopPropagation();var t,n,o=this,i=$(e.target),c=this.$(".j-invoice-count");i.hasClass("disabled-selected")||("invoice_mode"==(t=i.attr("data-key"))?i.hasClass("mn-selected")||(n=s.confirm($t("确定要启用订单产品为开票明细吗"),"",function(){o._setConfig(t,"sales_order_product",function(){i.addClass("mn-selected disabled-selected"),c.removeClass("hide"),CRM.util.sendLog("setting","invoice",{operationId:"openMode3"})}),n.hide()})):(i.toggleClass("mn-selected"),this._setConfig(t,i.hasClass("mn-selected")?"1":"0")))},relationHandler:function(e){var t,n=this,o=$(e.target);if(o.hasClass("on")&&o.hasClass("disabled-selected"))return s.alert($t("企业已开通订货通不允许关闭")),!1;o.hasClass("on")?((t=new i({content:'<div class="confirm-message">'+$t("crm.确认是否关闭该功能")+"</div>",classPrefix:"crm-c-dialog crm-c-dialog-confirm",title:$t("提示"),showBtns:!0})).on("dialogEnter",function(e){o[o.hasClass("on")?"removeClass":"addClass"]("on"),n._setConfig("43","0"),n.cacheConfig[43][0]="0",n.renderSelectProductCheckBox(),t.destroy()}),t.on("dialogCancel",function(e){t.destroy()}),t.on("hide",function(e){t.destroy()}),t.show()):(o[o.hasClass("on")?"removeClass":"addClass"]("on"),this._setConfig("43","1"),n.cacheConfig[43][0]="1",n.renderSelectProductCheckBox())},updateButtonUrl:function(e){var t=window.location.origin||window.location.protocol+"//"+window.location.host;s.FHHApi({url:"/EM1HNCRM/API/v1/object/button/service/updateButtonUrl",data:{0:{buttonURL:{},buttonApiNames:["Add_button_default","Edit_button_default"],objectDescribeApiName:"SalesOrderObj"},1:{buttonURL:{urlInfos:[{clientType:"mobile",url:t+"/gamma/auth/connect?resourceUrl=fs-sail-order-common&_hash=/visitsales/&source=common"},{clientType:"web",url:t+"/gamma/auth/connect?resourceUrl=fs-sail-order-common&_hash=/visitsales/&source=common"}]},buttonApiNames:["Add_button_default","Edit_button_default"],objectDescribeApiName:"SalesOrderObj"}}[e]},{errorAlertModel:1})},_setConfig:function(e,t,n){var o=this,t={ConfigInfoList:[{key:e,value:t}]};_.isArray(e)&&(t.ConfigInfoList=e);["manual_gift"].includes(e)?o.saveModuleStatus({url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:e,tenantId:CRM.enterpriseId,openStatus:1},confirmInfo:$t("确定要开启临时赠品吗"),confirmTit:$t("提示")},function(){n&&n()}):s.setConfigValues(t).then(function(){s.remind(1,$t("设置成功")),n&&n()},function(e){o._alert=s.alert(e),o.render()})},_getParam:function(e){var n,o=[0,0,0];return".j-radio-item"===e?(this.$(e).each(function(e,t){o[e]=$(t).hasClass("mn-selected")?1:0}),o.join(",")):(n="0",this.$(e).each(function(e,t){0==e&&(n=$(t).hasClass("on")?"1":"0")}),n)},_onLabel:function(e){$(e.currentTarget).closest("p").find(".mn-radio-item").trigger("click")},clickSwitch:function(e){var t,n,o,i,c=this,a=$(e.currentTarget),r=a.data("key"),l=a.hasClass("on")?"0":"1",e={input_custom_fields:"".concat("1"==l?$t("确认要启用吗"):$t("确认要关闭吗"),"?")};this.beforeSetConfig(r,l)||(t=_.bind(function(){c._setConfig(r,l,function(){"0"==l?a.removeClass("on"):a.addClass("on")})},c),(e=e[r])?(o="",_.isObject(e)?(n=e.info,o=e.title):n=e,i=s.confirm(n,o,function(){t(),i.hide()})):t())},beforeSetConfig:function(e,t){return"get_price_when_copy_contract"!==e||CRM._cache.sale_contract?"manual_gift"==e&&("1"==t?!!CRM._cache.promotionStatus&&(s.alert($t("已开启促销，无法开启临时赠品")),!0):(s.alert($t("企业已开通临时赠品不允许关闭")),!0)):(s.alert($t("请先开启销售合同开关")),!0)},destroy:function(){var e,t;(this.cacheConfig=null)!=(e=this.docLink)&&null!=(t=e.destroy)&&t.call(e),this.selProCheckBox&&this.selProCheckBox.destroy(),this.selProCheckBox=null,this.mobileShowFieldCheckBox&&this.mobileShowFieldCheckBox.destroy&&this.mobileShowFieldCheckBox.destroy(),this.mobileShowFieldCheckBox=null}});t.exports=r});
define("crm-setting/orderrule/template/invoice-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<!-- * @Description: * @Author: sunsh * @Date: 2021-01-04 10:59:26 * @LastEditors: sunsh * @LastEditTime: 2021-01-18 10:35:43 --> <div class="setting-invoice"> <div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <ul> <!-- <li>1." + ((__t = $t("开启开票明细后，字段变化")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("增加从对象，支持快捷操作")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("“销售订单&订单产品”增加开票相关字段")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("“销售订单”的“销售订单金额（元）”不可编辑")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("“销售订单”&“客户”增加开票页签")) == null ? "" : __t) + "</li> <li>6." + ((__t = $t("开票申请导入和打印模版增加开票明细")) == null ? "" : __t) + "</li> <li>7." + ((__t = $t("历史开票自动创建明细")) == null ? "" : __t) + "</li> <li>8." + ((__t = $t("无订单明细的历史开票不自动创建明细")) == null ? "" : __t) + "</li> <li>9." + ((__t = $t("BI报表需重新配置")) == null ? "" : __t) + "</li> <li>10." + ((__t = $t("开启时确保OpenAPI已升级")) == null ? "" : __t) + "</li> <li>11." + ((__t = $t("打印模板调整")) == null ? "" : __t) + "</li> --> <li>1." + ((__t = $t("开票明细开启后模式变化")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("支持多订单合并开票")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("销售订单详情页可查看状态")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("客户详情页可查看开票申请信息")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("按需调整打印模板")) == null ? "" : __t) + "</li> <li>6." + ((__t = $t("特别注意")) == null ? "" : __t) + "</li> <li>① " + ((__t = $t("确保OpenAPI已升级")) == null ? "" : __t) + "</li> <li>② " + ((__t = $t("按客户需求配置BI分析")) == null ? "" : __t) + "</li> <li>③ " + ((__t = $t("开启中暂无法使用开票申请")) == null ? "" : __t) + '</li> </ul> </div> <div class="setting-item setting-item-invoice"> ';
            var status = data["new_invoice"] && data["new_invoice"][0];
            __p += ' <div class="setting-item-dl setting-item-invoice-status" data-status="' + ((__t = status) == null ? "" : __t) + '"> <span class="setting-item-dt">' + ((__t = $t("开票明细")) == null ? "" : __t) + '：</span> <span class="setting-item-dd" data-status="1">' + ((__t = $t("已开启 已完成初始化")) == null ? "" : __t) + '</span> <span class="setting-item-dd" data-status="2">' + ((__t = $t("开启中 正在初始化")) == null ? "" : __t) + '</span> </div> <div class="setting-item-dl"> <span class="setting-item-dt">' + ((__t = $t("开票明细开启开关")) == null ? "" : __t) + '：</span> <span class="switch-sec switch-invoice-detail ' + ((__t = status == "1" || status == "2" ? "on" : "") == null ? "" : __t) + ' j-switch-invoice "></span> </div> <p class="setting-item-hint">' + ((__t = $t("开票明细开启后，不可关闭")) == null ? "" : __t) + '</p> <div class="setting-checkbox mn-checkbox-box ' + ((__t = status == "1" ? "" : "hide") == null ? "" : __t) + '"> <p class="setting-check-item"> <span data-key="invoice_is_allowed_overflow" class="mn-checkbox-item j-checkbox-item ' + ((__t = data["invoice_is_allowed_overflow"] && data["invoice_is_allowed_overflow"][0] == "1" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="mn-label">' + ((__t = $t("支持超额开票")) == null ? "" : __t) + '</span> </p> <div class="setting-item-explain"> <p>1.' + ((__t = $t("开启可超额开票后，开票金额允许大于销售订单金额")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("未开启/关闭时，开票金额不允许大于销售订单金额")) == null ? "" : __t) + "</p> <p>3." + ((__t = $t("关闭后，已超额的开票不可编辑修改")) == null ? "" : __t) + '</p> </div> </div> <div class="setting-checkbox mn-checkbox-box ' + ((__t = status == "1" ? "" : "hide") == null ? "" : __t) + '"> <p class="setting-check-item"> <span class="mn-checkbox-item j-checkbox-item ' + ((__t = data["invoice_mode"] && data["invoice_mode"][0] == "sales_order_product" ? "mn-selected disabled-selected" : "") == null ? "" : __t) + '" data-key="invoice_mode"></span> <span class="mn-label">' + ((__t = $t("启用订单产品为开票明细")) == null ? "" : __t) + '</span> </p> <div class="setting-item-explain"> <p>1.' + ((__t = $t("未启用时，销售订单作为开票明细")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("启用后，订单产品作为开票明细")) == null ? "" : __t) + '</p> </div> </div> <div class="setting-quickaction ' + ((__t = status == "1" ? "" : "hide") == null ? "" : __t) + '"> <p class="setting-check-item">' + ((__t = $t("开票明细快捷操作显示规则")) == null ? "" : __t) + '<p/> <div class="mn-radio-box"> <p style="line-height:32px; margin-bottom:4px;"> <span data-value="amount" class="mn-radio-item quick-action-rule ' + ((__t = data["invoice_show_quick_op_rule"] && data["invoice_show_quick_op_rule"]["0"] == "amount" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("按发票金额开票")) == null ? "" : __t) + '</span> </p> <p style="line-height:32px; margin-bottom:4px;" class="j-invoice-count ' + ((__t = data["invoice_mode"] && data["invoice_mode"][0] == "sales_order_product" ? "" : "hide") == null ? "" : __t) + '"> <span data-value="count" class="mn-radio-item quick-action-rule ' + ((__t = data["invoice_show_quick_op_rule"] && data["invoice_show_quick_op_rule"]["0"] == "count" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("按待开票数量比例开票")) == null ? "" : __t) + '</span> </p> <p style="line-height:32px; margin-bottom:4px;"> <span data-value="none" class="mn-radio-item quick-action-rule ' + ((__t = data["invoice_show_quick_op_rule"] && data["invoice_show_quick_op_rule"]["0"] == "none" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("隐藏快捷操作")) == null ? "" : __t) + '</span> </p> </div> </div> <div class="setting-summary ' + ((__t = status == "1" ? "" : "hide") == null ? "" : __t) + '"> <div class="setting-item-dl"> <span class="setting-item-dt">' + ((__t = $t("数据汇总显示")) == null ? "" : __t) + '：</span> <span class="switch-sec ' + ((__t = data["invoce_show_sum_data"] && data["invoce_show_sum_data"][0] == "0" ? "" : "on") == null ? "" : __t) + ' j-switch-summary"></span> </div> <div class="setting-item-explain"> <p>1.' + ((__t = $t("数据汇总显示开启")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("数据汇总显示关闭")) == null ? "" : __t) + "</p> <p>3." + ((__t = $t("数据汇总开关可反复操作")) == null ? "" : __t) + "</p> </div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/orderrule/template/orderrule-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<!-- * @Descripttion: * @Author: LiAng * @Date: 2020-08-20 14:27:30 * @LastEditors: wangzhao * @LastEditTime: 2024-03-29 14:03:37 --> <div class=" crm-scroll setting-orderrule"> ';
            var serialNumber = 0;
            __p += ' <div class="mn-radio-box crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("整单折扣与产品")) == null ? "" : __t) + '</div> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item j-radio-item ' + ((__t = data["16"][0] == "1" ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = isOrder ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor: pointer;">[' + ((__t = $t("整单折扣")) == null ? "" : __t) + "] = [" + ((__t = $t("crm.SalesOrderObj.field.销售订单金额")) == null ? "" : __t) + "] / [" + ((__t = $t("crm.SalesOrderObj.field.产品合计")) == null ? "" : __t) + "]" + ((__t = $t("产品必填")) == null ? "" : __t) + '</span> </p> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item j-radio-item ' + ((__t = data["16"][1] == "1" ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = isOrder ? "disabled-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor:pointer;">[' + ((__t = $t("整单折扣")) == null ? "" : __t) + "]" + ((__t = $t("不跟随")) == null ? "" : __t) + "[" + ((__t = $t("crm.SalesOrderObj.field.销售订单金额")) == null ? "" : __t) + "]" + ((__t = $t("变化产品选填")) == null ? "" : __t) + '</span> </p> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item j-radio-item ' + ((__t = data["16"][2] == "1" ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = isOrder ? "disabled-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor:pointer;">[' + ((__t = $t("整单折扣")) == null ? "" : __t) + "]" + ((__t = $t("不跟随")) == null ? "" : __t) + "[" + ((__t = $t("crm.销售订单金额")) == null ? "" : __t) + "]" + ((__t = $t("变化产品必填")) == null ? "" : __t) + '</span> </p> </div> <div class="mn-radio-box crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("销售订单报价单选产品产品明细支持复制")) == null ? "" : __t) + "</div> ";
            var msg = $t("请注意:移动端使用该功能，需升级至{{num}}以上版本", {
                num: "6.3.5"
            });
            __p += ' <p class="mn-radio-hint">' + ((__t = msg) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >' + ((__t = $t("crm.场景举例")) == null ? "" : __t) + '</p> <span class="switch-sec ' + ((__t = data["43"][0] == "1" ? "on" : "") == null ? "" : __t) + " j-radio-copy " + ((__t = isOrder ? "disabled-selected" : "") == null ? "" : __t) + '"></span> <div> <span class="owner-select-product"></span><span data-pos="bottom" class="crm-doclink crm-ui-title" data-title="' + ((__t = $t("支持已选说明4")) == null ? "" : __t) + '"></span> <p class="mn-radio-hint">' + ((__t = $t("注")) == null ? "" : __t) + "：· " + ((__t = $t("支持已选说明1")) == null ? "" : __t) + '</p> <p class="mn-radio-hint">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;· ' + ((__t = $t("支持已选说明2")) == null ? "" : __t) + '</p> <p class="mn-radio-hint">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;· ' + ((__t = $t("支持已选说明3")) == null ? "" : __t) + '</p> </div> </div> <div class="crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("订单产品开启“从历史订单”添加")) == null ? "" : __t) + '</div> <p class="mn-radio-explain" >' + ((__t = $t("开启后可添加所选客户下的全部历史订单产品")) == null ? "" : __t) + '</p> <span class="switch-sec ' + ((__t = data["clone_history_order_product"] && data["clone_history_order_product"][0] == "1" ? "on" : "") == null ? "" : __t) + ' j-switch-history-order "></span> </div> <div class="crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("订单等对象选产品列表扩展字段")) == null ? "" : __t) + '</div> <ol style="line-height: 26px;"> <li>' + ((__t = $t("说明")) == null ? "" : __t) + ":</li> <li>1. " + ((__t = $t("虚拟字段显示由开关控制，开启后不支持关闭")) == null ? "" : __t) + "!</li> <li> <div>2. " + ((__t = $t("开关开启")) == null ? "" : __t) + ':</div> <ol style="text-indent: 1em;"> <li>(1) ' + ((__t = $t("选择数据页面增加扩展字段显示")) == null ? "" : __t) + ";</li> <li>(2) " + ((__t = $t("不显示扩展字段")) == null ? "" : __t) + ";</li> <li> (3) " + ((__t = $t("扩展字段如下")) == null ? "" : __t) + ': <ol style="text-indent: 2em;"> <li>a. ' + ((__t = $t("产品对象增加：价目表售价、价目表价格、可用库存虚拟字段")) == null ? "" : __t) + ";</li> <li>b. " + ((__t = $t("商品对象增加：规格属性、产品编码、产品条形码、产品价格、价目表售价、价目表价格、可用库存虚拟字段")) == null ? "" : __t) + ";</li> <li>c. " + ((__t = $t("价目表明细对象增加：可用库存虚拟字段")) == null ? "" : __t) + ";</li> <li>" + ((__t = $t("注：可用库存字段是否显示，依据库存开关是否开启判断")) == null ? "" : __t) + ";</li> </ol> </li> <li>(4) " + ((__t = $t("扩展字段，不支持筛选、搜索、排序、布局规则、验证规则")) == null ? "" : __t) + ";</li> <li>(5) " + ((__t = $t("Web端，通过 “场景设置→全部场景” 中配置扩展字段显示")) == null ? "" : __t) + ";</li> <li>(6) " + ((__t = $t("移动端，通过 “移动端摘要布局” 中配置扩展字段显示")) == null ? "" : __t) + ';</li> </ol> </li> </ol> <span class="switch-sec switch-virtual-field ' + ((__t = data["virtual_extension"] && data["virtual_extension"][0] == "1" ? "on" : "") == null ? "" : __t) + ' j-switch-virtual-field "></span> </div> <div class="mn-radio-box crm-p20" style="line-height: 32px;"> <div class="mn-radio-title"><span>' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("新建订单移动端选产品支持输入数量")) == null ? "" : __t) + '</span><span class="crm-doclink-wrapper" style="display: none;"><i class="docklink-pop-content"></i></span></div> <p class="mn-radio-explain" >a. ' + ((__t = $t("启用该开关后，选择产品/商品列表不支持展开步进器，而是显示+号。用户点击后，系统根据是否有订单产品必填字段显示步进器，还是浮窗")) == null ? "" : __t) + '。</p> <p class="mn-radio-explain" >b. ' + ((__t = $t("除了数量字段，还支持订单产品必填的自定义字段，字段类型仅支持")) == null ? "" : __t) + ": <strong>" + ((__t = $t("单行文本、单选、多选、数字、金额、日期、时间、日期时间、手机、布尔、百分比共11种")) == null ? "" : __t) + '</strong>。</p> <p class="mn-radio-explain" >c. ' + ((__t = $t("此能力支持：“销售订单”对象，选择数据页面，选择数据为产品的模式")) == null ? "" : __t) + '。</p> <p class="mn-radio-explain" >d. ' + ((__t = $t("数据选择区域显示+图标不支持默认展开步进器效果")) == null ? "" : __t) + '。</p> <p class="mn-radio-hint" > ' + ((__t = $t("注")) == null ? "" : __t) + '：</p> <p class="mn-radio-explain" >e. ' + ((__t = $t("不管新老客户，默认该参数都为关闭")) == null ? "" : __t) + '。</p> <p class="mn-radio-explain" >f. ' + ((__t = $t("由于自定义字段是拉取订单产品的必填字段，尽管其他对象也有选择产品的场景，但先支持订单对象")) == null ? "" : __t) + '。</p> <span data-key="input_custom_fields" class="switch-sec ' + ((__t = data["input_custom_fields"] && data["input_custom_fields"][0] == "1" ? "on" : "") == null ? "" : __t) + ' js-clickSwitch "></span> </div> <div class="mn-radio-box crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("订单报价单选产品产品名称搜索支持选择")) == null ? "" : __t) + '</div> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item product-search-conf ' + ((__t = data["product_keyword_search_mode"] && data["product_keyword_search_mode"][0] == "" || data["product_keyword_search_mode"] && data["product_keyword_search_mode"][0] == null ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("单关键词搜索")) == null ? "" : __t) + '</span> </p> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item product-search-conf ' + ((__t = data["product_keyword_search_mode"] && data["product_keyword_search_mode"][0] == "and" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("多关键词且的搜索")) == null ? "" : __t) + '</span> <span class="mn-radio-explain" style="display: inline-block;margin-left: 1em;">' + ((__t = $t("搜索结果为")) == null ? "" : __t) + "：" + ((__t = $t("关键词且")) == null ? "" : __t) + '</span> </p> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item product-search-conf ' + ((__t = data["product_keyword_search_mode"] && data["product_keyword_search_mode"][0] == "or" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("多关键词或的搜索")) == null ? "" : __t) + '</span> <span class="mn-radio-explain" style="display: inline-block;margin-left: 1em;">' + ((__t = $t("搜索结果为")) == null ? "" : __t) + "：" + ((__t = $t("关键词或")) == null ? "" : __t) + '</span> </p> <p class="mn-radio-explain">' + ((__t = $t("产品名称搜索举例")) == null ? "" : __t) + '</p> <p class="mn-radio-explain">1. ' + ((__t = $t("单关键词搜索")) == null ? "" : __t) + "：" + ((__t = $t("单关键词举例")) == null ? "" : __t) + '</p> <p class="mn-radio-explain">2. ' + ((__t = $t("多关键词且的搜索")) == null ? "" : __t) + "，" + ((__t = $t("多关键词且举例")) == null ? "" : __t) + '</p> <p class="mn-radio-explain">3. ' + ((__t = $t("多关键词或的搜索")) == null ? "" : __t) + "，" + ((__t = $t("多关键词或举例")) == null ? "" : __t) + '</p> </div> <div class="mn-radio-box crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("是否重新取价开关")) == null ? "" : __t) + '</div> <div class="on-off setting-switch-style"> <label class="title">' + ((__t = $t("报价单转订单重新取价")) == null ? "" : __t) + ':</label> <span data-key="get_price_when_convert" class=" js-clickSwitch switch-sec ' + ((__t = data["get_price_when_convert"][0] == "1" ? "on" : "") == null ? "" : __t) + ' "></span> </div> <p class="mn-radio-explain" >' + ((__t = $t("报价单转订单")) == null ? "" : __t) + ':</p> <p class="mn-radio-explain" >' + ((__t = $t("报价单转订单开关说明")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >a.' + ((__t = $t("报价单转订单开关说明1")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >b.' + ((__t = $t("报价单转订单开关说明2")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >c.' + ((__t = $t("报价单转订单开关说明3")) == null ? "" : __t) + '</p> <p class="mn-radio-hint">' + ((__t = $t("报价单转订单开关注意")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >' + ((__t = $t("销售合同转订单")) == null ? "" : __t) + ':</p> <p class="mn-radio-explain" >' + ((__t = $t("销售合同转订单开关说明")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >a.' + ((__t = $t("销售合同转订单开关说明1")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >b.' + ((__t = $t("销售合同转订单开关说明2")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >c.' + ((__t = $t("销售合同转订单开关说明3")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" style="margin-top:12px;" >' + ((__t = $t("报价单转销售合同")) == null ? "" : __t) + ':</p> <p class="mn-radio-explain" >' + ((__t = $t("报价单转销售合同开关说明")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >a.' + ((__t = $t("报价单转销售合同开关说明1")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >b.' + ((__t = $t("报价单转销售合同开关说明2")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >c.' + ((__t = $t("报价单转销售合同开关说明3")) == null ? "" : __t) + '</p> <div class="on-off setting-switch-style"> <label class="title">' + ((__t = $t('订单复制、订单产品"从历史添加"重新取价')) == null ? "" : __t) + ':</label> <span data-key="get_price_when_copy" class="js-clickSwitch switch-sec ' + ((__t = data["get_price_when_copy"][0] == "1" ? "on" : "") == null ? "" : __t) + ' "></span> </div> <p class="mn-radio-explain" >' + ((__t = $t("订单历史添加重新取价开关说明")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >a.' + ((__t = $t("历史添加重新取价开关说明2")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >b.' + ((__t = $t("历史添加重新取价开关说明3")) == null ? "" : __t) + '</p> <p class="mn-radio-hint">' + ((__t = $t("历史添加重新取价开关注意")) == null ? "" : __t) + '</p> <div class="on-off setting-switch-style"> <label class="title">' + ((__t = $t("报价单复制、报价单明细“从历史报价”添加重新取价")) == null ? "" : __t) + ':</label> <span data-key="get_price_when_copy_quote" class="js-clickSwitch switch-sec ' + ((__t = data["get_price_when_copy_quote"][0] == "1" ? "on" : "") == null ? "" : __t) + ' "></span> </div> <p class="mn-radio-explain" >' + ((__t = $t("报价单历史添加重新取价开关说明")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >a.' + ((__t = $t("报价单历史历史添加重新取价开关说明1")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >b.' + ((__t = $t("报价单历史历史添加重新取价开关说明2")) == null ? "" : __t) + '</p> <div class="on-off setting-switch-style"> <label class="title">' + ((__t = $t("销售合同复制重新取价")) == null ? "" : __t) + ':</label> <span data-key="get_price_when_copy_contract" class="js-clickSwitch switch-sec ' + ((__t = data["get_price_when_copy_contract"][0] == "1" ? "on" : "") == null ? "" : __t) + ' "></span> </div> <p class="mn-radio-explain" >' + ((__t = $t("销售合同复制重新取价开关说明")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >a.' + ((__t = $t("销售合同复制重新取价开关说明1")) == null ? "" : __t) + '</p> <div class="on-off setting-switch-style"> <label class="title">' + ((__t = $t("商机2.0") + $t("复制重新取价")) == null ? "" : __t) + ':</label> <span data-key="get_price_when_copy_newopportunity" class="js-clickSwitch switch-sec ' + ((__t = data["get_price_when_copy_newopportunity"][0] == "1" ? "on" : "") == null ? "" : __t) + ' "></span> </div> </div> <div class="crm-p20" style="line-height: 32px;"> <div class="mn-radio-title ">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("报价单转订单：收货人、收货人电话、收货人地址、仓库，默认值不覆盖映射值：")) == null ? "" : __t) + '<span data-key="order_to_quote_default_value_cover" class="switch-inline js-clickSwitch switch-sec ' + ((__t = data["order_to_quote_default_value_cover"][0] == "1" ? "on" : "") == null ? "" : __t) + ' "></span></div> <p class="mn-radio-explain" >a.' + ((__t = $t("开关默认关闭；订单对象中，收货人、收货人电话、收货人地址、仓库的默认值覆盖映射值")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >b.' + ((__t = $t("若开关开启，报价单转订单后，订单对象中收货人、收货人电话、收货人地址、仓库的默认值")) == null ? "" : __t) + ' <span class="mn-radio-explain" style="color:red">' + ((__t = $t("不会覆盖")) == null ? "" : __t) + '</span> <span class="mn-radio-explain" >' + ((__t = $t("报价单转订单中此字段的映射值")) == null ? "" : __t) + '</span></p> </div> <div class="crm-p20" style="line-height: 32px;"> <div class="mn-radio-title ">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("已确认的销售订单/退款，取消特定角色可编辑和可作废的权限")) == null ? "" : __t) + ': <span data-key="order_enlarge_edit_privilege" class="switch-inline js-clickSwitch switch-sec ' + ((__t = data["order_enlarge_edit_privilege"][0] == "1" ? "on" : "") == null ? "" : __t) + ' "></span></div> <p class="mn-radio-explain" >a.' + ((__t = $t("已确认订单说明a")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >b.' + ((__t = $t("已确认订单说明b")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >c.' + ((__t = $t("已确认订单说明c")) == null ? "" : __t) + '</p> <p class="mn-radio-explain" >d.' + ((__t = $t("已确认订单说明d")) == null ? "" : __t) + '</p> </div> <div class="crm-p20 mn-radio-box" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("销售订单浮动上下限校验配置开关")) == null ? "" : __t) + '</div> <div class="ignore_check_ceiling_floor_price"></div> </div> <div class="crm-p20 mn-radio-box" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = ++serialNumber) == null ? "" : __t) + "." + ((__t = $t("临时赠品开关")) == null ? "" : __t) + ': <span data-key="manual_gift" class="switch-inline js-clickSwitch switch-sec ' + ((__t = data["manual_gift"] == "1" ? "on" : "") == null ? "" : __t) + ' "></span></div> <div class="manual_gift_config"> <ol style="line-height: 26px;"> <li>1. ' + ((__t = $t("开关默认关闭，若开关开启，订单产品可通过【添加临时赠品】直接增加赠品行")) == null ? "" : __t) + "!</li> <li>2. " + ((__t = $t("手工赠品行的取价与普通产品行相同，包括【价格】、【价目表价格】等；【是否赠品】为“是”")) == null ? "" : __t) + "</li> <li> <div>3. " + ((__t = $t("手工赠品行的计算逻辑")) == null ? "" : __t) + ':</div> <ol style="text-indent: 1em;"> <li>3.1 ' + ((__t = $t("如果没有开启价格政策，【销售单价】、【小计】、【折扣】默认为0，可编辑")) == null ? "" : __t) + ";</li> <li>3.2 " + ((__t = $t("如果开启了价格政策，视为整单促销的赠品，【销售单价】、【小计】、【折扣】强制为0")) == null ? "" : __t) + ';</li> </ol> </li> </ol> </div> </div> <div class="crm-p20 mn-radio-box " > <div class="mobile-select-showSummaryField-wrapper"> <div class="mobile-select-showSummaryField"></div> </div> </div> <div class="crm-p20 mn-radio-box " > <div class="mobile-select-showPrice"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/orderrule/template/ordersort-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="item crm-order-sort active"> <div class="order-sort-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("crm.按字段筛选排序")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.筛选排序")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.原排序被清空")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("可筛选对应字段的相关条件进行排序")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("每次只能选择一个排序规则")) == null ? "" : __t) + "</li> <li>6." + ((__t = $t("crm.按范围删选排序")) == null ? "" : __t) + '</li> </ul> <p class="notice">' + ((__t = $t("状态价目表未开启")) == null ? "" : __t) + "／" + ((__t = $t("价目表已开启（待接口数据填充）")) == null ? "" : __t) + '</p> </div> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="mn-radio-title">' + ((__t = $t("排序规则")) == null ? "" : __t) + '</div> <p> <span class="mn-radio-item j-radio-item "></span> <span class="label" style="margin-left:10px;cursor: pointer;">' + ((__t = $t("无规则")) == null ? "" : __t) + '</span> </p> </div> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="mn-radio-title">' + ((__t = $t("按条件范围排序")) == null ? "" : __t) + '</div> <p> <span class="mn-radio-item j-radio-item "></span> <div class="rule-wrap"></div> </p> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/orderrule/template/payment-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-payment"> <div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("回款默认为客户的回款记录当企业开通企业钱包后可启用线上支付能力")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("启用客户账户2.0回款可入账到客户账户")) == null ? "" : __t) + '</li> </ul> </div> <div class="setting-item flex flex-align-center"> <span class="setting-item-label">' + ((__t = $t("开启线上支付能力")) == null ? "" : __t) + '：</span> <span class="switch-sec ' + ((__t = data.is_payment_pay_enable[0] == "1" ? "on" : "") == null ? "" : __t) + ' j-switch-paymentonline"></span> </div> <p class="setting-item-hint">' + ((__t = $t("注意") + ":" + $t("该开关一旦开启，不可关闭。")) == null ? "" : __t) + '</p> <div class="setting-item flex flex-align-center"> <span class="setting-item-label">' + ((__t = $t("开启入账到客户账户")) == null ? "" : __t) + '：</span> <span class="switch-sec ' + ((__t = data.is_payment_enter_account_enable[0] == "2" ? "on" : "") == null ? "" : __t) + ' j-switch-enteraccount"></span> </div> <p class="setting-item-hint">' + ((__t = $t("注意") + ":" + $t("该开关一旦开启，不可关闭。")) == null ? "" : __t) + "</p> </div>";
        }
        return __p;
    };
});
define("crm-setting/orderrule/template/salecontract-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-salecontract"> <div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("先开订单新建布局")) == null ? "" : __t) + '</li> </ul> </div> <div class="setting-sc"> <span class="setting-sc-label">' + ((__t = $t("开启销售合同开关")) == null ? "" : __t) + '：</span> <span class="switch-sec ' + ((__t = data["sale_contract"] && data["sale_contract"][0] == "1" ? "on" : "") == null ? "" : __t) + ' j-switch-salecontract"></span> </div> <p class="setting-item-hint">' + ((__t = $t("注意") + ":" + $t("该开关一旦开启，不可关闭。")) == null ? "" : __t) + "</p> </div>";
        }
        return __p;
    };
});
define("crm-setting/orderrule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("售中交易规则")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-tab"> <span data-render="promotion-box" class="j-switch-tab">' + ((__t = $t("交易相关配置")) == null ? "" : __t) + '</span> <span data-render="payment-box" class="j-switch-tab">' + ((__t = $t("回款配置")) == null ? "" : __t) + '</span> <span data-render="invoice-box" class="j-switch-tab">' + ((__t = $t("开票申请配置")) == null ? "" : __t) + '</span> <span data-render="salecontract-box" class="j-switch-tab">' + ((__t = $t("开启销售合同配置")) == null ? "" : __t) + '</span> </div> <div class="tab-con crm-scroll"> <div class="item render-box order-rule" style="display:none"></div> <div class="item render-box payment-setting" style="display:none"></div> <div class="item render-box invoice-setting" style="display:none"></div> <div class="item render-box salecontract-setting" style="display:none"></div> </div> </div>';
        }
        return __p;
    };
});