define("crm-setting/logistics/api/api",[],function(e,r,t){var s=FS.crmUtil;t.exports={_operateApi:function(e){var r=_.extend({url:"",type:"post"},e||{}),e={submitSelector:r.submitSelector};return e.errorAlertModel=r.errorAlertModel||2,r.successCb&&!r.success&&(r.success=function(e){0==e.Result.StatusCode&&r.successCb(e.Value)}),s.FHHApi(_.omit(r,"errorAlertModel","submitSelector","successCb"),e)},apiGetQuotaStatistics:function(e){var s=this;return new Promise(function(r,t){s._operateApi({url:"/EM1HNCRM/API/v1/object/logistics_query_front/service/query_statistics?",data:e||{},errorAlertModel:2,successCb:function(e){r(e)},errorCb:function(e){t(e)}})})},apiGetQuotaTable:function(e){var s=this;return new Promise(function(r,t){s._operateApi({url:"/EM1HNCRM/API/v1/object/logistics_query_front/service/query_purchase_list",data:e||{},errorAlertModel:2,successCb:function(e){r(e)},errorCb:function(e){t(e)}})})},apiGetConsumeTable:function(e){var s=this;return new Promise(function(r,t){s._operateApi({url:"/EM1HNCRM/API/v1/object/logistics_query_front/service/query_consume_list",data:e||{},errorAlertModel:2,successCb:function(e){r(e)},errorCb:function(e){t(e)}})})}}});
define("crm-setting/logistics/components/consume",["../api/api"],function(t,e,a){var n=CRM.util.moment,i=t("../api/api");a.exports={template:'\n\t\t<div class="logistics-main__content-manage">\n\t\t\t<div class="logistics-main__content-manage__wrapper">\n\t\t\t\t<fx-table\n\t\t\t\t\tclass="logistics-main__content-manage__wrapper__table"\n      \t\t\t\t:data="table.data"\n      \t\t\t\tv-loading="table.loading"\n      \t\t\t>\n      \t\t\t\t<fx-table-column\n      \t\t\t\t\tv-for="col in table.column"\n      \t\t\t\t\t:key="col.prop"\n        \t\t\t\t:prop="col.prop"\n        \t\t\t\t:label="col.label"\n        \t\t\t>\n      \t\t\t\t</fx-table-column>\n    \t\t\t</fx-table>\n    \t\t\t<fx-pagination\n    \t\t\t\tclass="logistics-main__content-manage__wrapper__page"\n      \t\t\t\t@current-change="handleCurrentChange"\n      \t\t\t\t:current-page="pagination.currentPage"\n      \t\t\t\t:page-sizes="pagination.pageSizes"\n      \t\t\t\t:page-size="pagination.pageSize"\n      \t\t\t\tlayout="prev, pager, next, total, jumper, sizes"\n      \t\t\t\t:total="pagination.total"\n      \t\t\t>\n    \t\t\t</fx-pagination>\n\t\t\t</div>\n\t\t</div>',name:"Manage",props:{currentType:{type:Boolean,default:""}},data:function(){return{table:{column:[{prop:"consume_object_name",label:"query"===this.currentType?$t("stock.logistics.consume_object_name"):$t("stock.logistics.consume_object_data")},{prop:"consume_object_describe_api_name",label:$t("stock.logistics.consume_object_describe_api_name")},{prop:"consume_time",label:$t("stock.logistics.consume_time")},{prop:"created_by_name",label:$t("stock.logistics.created_by_name")},{prop:"consumeTypeLabel",label:$t("stock.logistics.consume_type_label")},{prop:"consume_quota",label:$t("stock.logistics.consume_quota")},{prop:"remark",label:$t("stock.logistics.remark")}],data:[],loading:!1},pagination:{currentPage:1,pageSizes:[20,50,100],pageSize:20,total:0}}},watch:{currentType:{handler:function(t){t&&this.getTable()},immediate:!0}},created:function(){},methods:{handleCurrentChange:function(t){this.pagination.currentPage=t,this.getTable()},getTable:function(){var a=this,t=this.pagination,e=t.currentPage,t=t.pageSize,e={searchQueryInfo:JSON.stringify({limit:t,offset:(e-1)*t,filters:[{field_name:"product_type",operator:"EQ",field_values:[this.currentType]}],orders:[{fieldName:"last_modified_time",isAsc:!1}]})};this.table.loading=!0,i.apiGetConsumeTable(e).finally(function(){a.table.loading=!1}).then(function(t){var e=t.dataList,t=t.total;0<e.length?a.table.data=a.format(e):a.table.data=[],a.pagination.total=t,a.table.loading=!1})},format:function(t){return _.map(t,function(t){var e=FS.contacts.getEmployeesByIds(JSON.parse(t.created_by))[0],a=$t("系统");return e&&(a=e.fullName),_.extend(t,{created_by_name:a,consume_time:n(t.consume_time).format("YYYY-MM-DD HH:mm:ss")})})}}}});
define("crm-setting/logistics/components/main",["./quota","./consume"],function(t,n,e){var a=t("./quota"),s=t("./consume");e.exports={template:'\n\t\t<div class="logistics-main">\n\t\t\t<div\n\t\t\t\tv-if="loading"\n\t\t\t\tclass="crm-loading tab-loading"\n\t\t\t></div>\n\t\t\t<div\n\t\t\t\tv-else\n\t\t\t\tclass="logistics-main__content"\n\t\t\t>\n\t\t\t\t<fx-tabs\n\t\t\t\t\tclass="logistics-main__type-list"\n\t\t\t\t\tv-model="currentType"\n\t\t\t\t\t@tab-click="tabs.active = $t(\'stock.logistics.quota\')"\n\t\t\t\t>\n\t\t\t\t\t<fx-tab-pane\n\t\t\t\t\t\tv-for="typeItem in typeOptions"\n\t\t\t\t\t\t:key="typeItem.value"\n\t\t\t\t\t\t:label="typeItem.label"\n\t\t\t\t\t\t:name="typeItem.value"\n\t\t\t\t\t></fx-tab-pane>\n\t\t\t\t</fx-tabs>\n\t\t\t\t<fx-tabs\n\t\t\t\t\tclass="logistics-main__content-tabs"\n\t\t\t\t\tv-model="tabs.active"\n\t\t\t\t\t@tab-click="handleTabsClick"\n\t\t\t\t>\n\t\t\t\t\t<fx-tab-pane\n\t\t\t\t\t\tv-for="tab in tabs.tabs"\n\t\t\t\t\t\t:key="tab"\n\t\t\t\t\t\t:label="tab"\n\t\t\t\t\t\t:name="tab"\n\t\t\t\t\t></fx-tab-pane>\n\t\t\t\t</fx-tabs>\n\t\t\t\t<keep-alive>\n\t\t\t\t\t<component v-bind:is="currentTabComponent" :current-type="currentType"></component>\n\t\t\t\t</keep-alive>\n\t\t\t</div>\n\t\t</div>',name:"Main",components:{Quota:a,Consume:s},computed:{currentTabComponent:function(){return this.tabs.active===$t("stock.logistics.quota")?a:s}},data:function(){var t=[{value:"query",label:$t("stock.logistics.type-options.query")},{value:"order",label:$t("stock.logistics.type-options.order")}];return{currentType:t[0].value,typeOptions:t,tabs:{tabs:[$t("stock.logistics.quota"),$t("stock.logistics.consume_detail")],active:$t("stock.logistics.quota")},loading:!1}},methods:{handleTabsClick:function(t){t=t.paneName;this.tabs.active=t}}}});
define("crm-setting/logistics/components/quota",["../api/api"],function(t,e,i){var a=CRM.util.moment,n=t("../api/api");i.exports={template:'\n\t\t<div class="logistics-main__content-quota" v-loading="isLoading">\n\t\t\t<div class="logistics-main__content-quota__statistics">\n\t\t\t\t<div\n\t\t\t\t\tclass="logistics-main__content-quota__statistics-item"\n\t\t\t\t\tv-for="(value,key) in statistics"\n\t\t\t\t\t:key="key"\n\t\t\t\t>\n\t\t\t\t\t<p>{{value}}</p>\n\t\t\t\t\t<p>{{statisticsMap[key]}}</p>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t<div class="logistics-main__content-quota__wrapper">\n\t\t\t\t\x3c!-- 购买记录 --\x3e\n\t\t\t\t<p class="logistics-main__content-quota__wrapper__title">{{$t("stock.logistics.purchase_record")}}</p>\n\t\t\t\t<fx-table\n\t\t\t\t\tclass="logistics-main__content-quota__wrapper__table"\n      \t\t\t\t:data="table.data"\n      \t\t\t\tv-loading="table.loading"\n      \t\t\t>\n      \t\t\t\t<fx-table-column\n      \t\t\t\t\tv-for="col in table.column"\n      \t\t\t\t\t:key="col.prop"\n        \t\t\t\t:prop="col.prop"\n        \t\t\t\t:label="col.label"\n        \t\t\t>\n      \t\t\t\t</fx-table-column>\n    \t\t\t</fx-table>\n    \t\t\t<fx-pagination\n    \t\t\t\tclass="logistics-main__content-quota__wrapper__page"\n      \t\t\t\t@current-change="handleCurrentChange"\n      \t\t\t\t:current-page="pagination.currentPage"\n      \t\t\t\t:page-sizes="pagination.pageSizes"\n      \t\t\t\t:page-size="pagination.pageSize"\n      \t\t\t\tlayout="prev, pager, next, total, jumper, sizes"\n      \t\t\t\t:total="pagination.total"\n      \t\t\t>\n    \t\t\t</fx-pagination>\n\t\t\t</div>\n\t\t</div>',name:"Quota",props:{currentType:{type:Boolean,default:""}},data:function(){return{isLoading:!1,statistics:{remainingQuota:0,allPurchasedQuota:0,allUsedQuota:0},statisticsMap:{remainingQuota:$t("stock.remaining_query_times"),allPurchasedQuota:$t("stock.total_purchase_times"),allUsedQuota:$t("stock.total_consumption_times")},table:{column:[{prop:"license_order_number",label:$t("stock.logistics.license_order_number")},{prop:"license_create_time",label:$t("stock.logistics.license_create_time")},{prop:"license_start_time",label:$t("stock.logistics.license_start_time")},{prop:"license_expire_time",label:$t("stock.logistics.license_expire_time")},{prop:"moduleLabel",label:$t("stock.logistics.module_label")},{prop:"license_quota",label:$t("stock.logistics.license_quota")},{prop:"license_consume_quota",label:$t("stock.logistics.license_consume_quota")}],data:[],loading:!1},pagination:{currentPage:1,pageSizes:[20,50,100],pageSize:20,total:0}}},watch:{currentType:{handler:function(t){t&&(this.getStatistics(),this.getTable())},immediate:!0}},created:function(){},methods:{handleCurrentChange:function(t){this.pagination.currentPage=t,this.getTable()},getStatistics:function(){var e=this;this.isLoading=!0,n.apiGetQuotaStatistics({productType:this.currentType}).finally(function(){e.isLoading=!1}).then(function(t){return e.statistics=t})},getTable:function(){var i=this,t=this.pagination,e=t.currentPage,t=t.pageSize,e={searchQueryInfo:JSON.stringify({limit:t,offset:(e-1)*t,filters:[{field_name:"product_type",operator:"EQ",field_values:[this.currentType]}],orders:[{fieldName:"last_modified_time",isAsc:!1}]})};this.table.loading=!0,n.apiGetQuotaTable(e).finally(function(){i.table.loading=!1}).then(function(t){var e=t.dataList,t=t.total;0<e.length?i.table.data=i.format(e):i.table.data=[],i.pagination.total=t})},format:function(t){return _.map(t,function(t){return _.extend(t,{license_create_time:a(t.license_create_time).format("YYYY-MM-DD HH:mm:ss"),license_start_time:a(t.license_start_time).format("YYYY-MM-DD HH:mm:ss"),license_expire_time:a(t.license_expire_time).format("YYYY-MM-DD HH:mm:ss")})})}}}});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,_toPropertyKey(i.key),i)}}function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/logistics/logistics",["./components/main"],function(t,e,n){var i=t("./components/main"),o=Vue.extend({template:'\n\t\t\t<div id="app-logistics">\n \t\t\t\t<div class="crm-tit">\n\t\t\t\t\t\x3c!--\n\t\t\t\t\t<h2 class="crm-s-logistics">\n\t\t\t\t\t\t<span class="tit-txt j-setting-title">{{$t("stock.logistics.logistics_inquiry_service")}}</span>\n\t\t\t\t\t</h2>\n\t\t\t\t\t--\x3e\n\t\t\t\t</div>\n\t\t\t\t<div class="crm-module-con">\n\t\t\t\t\t<Main></Main>\n\t\t\t\t</div>\n\t\t\t</div>'});n.exports=_createClass(function t(e){_classCallCheck(this,t),this.app=new o({name:"App",el:e.wrapper.append("<div></div>").children()[0],components:{Main:i}})},[{key:"destroy",value:function(){this.app.$destroy()}}])});