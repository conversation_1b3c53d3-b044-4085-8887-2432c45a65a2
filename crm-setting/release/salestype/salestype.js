define("crm-setting/salestype/salestype",[],function(e,t,n){var i=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){var t=this.el;e.async("vcrm/sdk",function(e){e.getSalestypeModule(t)})}});n.exports=i});
define("crm-setting/salestype/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("销售记录设置")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-tab"> <span class="j-crm-set-type cur" data-id="1">' + ((__t = $t("销售记录类型")) == null ? "" : __t) + '</span> <span class="j-crm-set-type" data-id="2">' + ((__t = $t("销售记录关联设置")) == null ? "" : __t) + '</span> </div> <div class="crm-module-con crm-scroll crm-set-salesType" style="margin-top:50px;"> <div class="crm-salesType"> <p class="desc-text">' + ((__t = $t("说明：")) == null ? "" : __t) + "</p> <span>" + ((__t = $t("销售记录类型设置已迁移到【销售记录】对象中，通过【跟进类型】字段进行设置")) == null ? "" : __t) + '</span> </div> <div class="crm-relevance mn-checkbox-box hide"> <div class="mn-checkbox-box" style="line-height:32px; margin:20px 0 0 30px;"> ';
            var msg = $t("请注意:移动端使用该功能，需升级至{{num}}以上版本", {
                num: 6.5
            });
            __p += ' <p class="mn-radio-hint" style="color:#ff8837;">' + ((__t = msg) == null ? "" : __t) + '</p> <span class="mn-checkbox-item j-select-base"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("联系人发布销售记录时必须关联到客户下")) == null ? "" : __t) + '</span> </div> <div class="mn-checkbox-box" style="line-height:32px; margin:20px 0 0 30px;"> <span class="mn-checkbox-item j-select-opp"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("商机发布销售记录时必须关联到客户下")) == null ? "" : __t) + "</span> </div> </div> </div> </div>";
        }
        return __p;
    };
});