define("crm-setting/guidepage/config/detail",[],function(e,c,t){Object.defineProperty(c,"__esModule",{value:!0});var n={spuConfig:{api_name:"SPUObj",icon:"fx-icon-app15.svg",tag:$t("用于维护和管理基础档案",null,"用于维护和管理基础档案"),desc:$t("crm.guidepage.spuConfig.desc",null,"是一组可复用、易检索的 标准化信息的集合，该集合描述了一个产品的特性"),supportScene:[{icon:"fx-icon-obj-spuobj",title:$t("crm.guidepage.spuConfig.supportScene.0.title",null,"商品档案"),desc:[$t("crm.guidepage.spuConfig.supportScene.0.desc.0",null,"简介：规格、特性相同的商品就可以称为一个SPU，其中基础内容包括：商品名称、分类等信息。对于消费者来说，一个商品的档案可以帮助他们更好地了解该商品，便于选择和购买")]},{icon:"fx-icon-diaobodanchanpin",title:$t("crm.guidepage.spuConfig.supportScene.1.title",null,"基于商品选数据交互应用"),desc:[$t("crm.guidepage.spuConfig.supportScene.1.desc.0",null,"简介：通过精确选择具体的产品规格参数，可以帮助消费者提高购买体验、精准匹配需求和降低购买风险，从而提升了交互价值")]}],functionalRelation:{desc:[$t("crm.guidepage.spuConfig.functionalRelation.desc.0",null,"与属性互斥")]}},attributeConfig:{icon:"fx-icon-obj-app13.svg",api_name:"AttributeObj",desc:$t("crm.guidepage.attributeConfig.desc",null,"某类产品上的特殊对象，属性作为相关对象属于某些（个）产品。主要是指一些足以反映商品品质的主要指标，如颜色、容量、纯度、内存大小、长短、粗细等。"),tag:$t("用于维护和管理基础档案",null,"用于维护和管理基础档案"),supportScene:[{icon:"fx-icon-app12",title:$t("crm.guidepage.attributeConfig.supportScene.0.title",null,"属性"),desc:[$t("crm.guidepage.attributeConfig.supportScene.0.desc.0",null,"简介：某类产品上的特殊对象，属性作为相关对象属于某些（个）产品。主要是指一些足以反映商品品质的主要指标，如颜色、容量、纯度、内存大小、长短、粗细等。")]},{icon:"fx-icon-obj-app48",title:$t("crm.guidepage.attributeConfig.supportScene.1.title",null,"属性值"),desc:[$t("crm.guidepage.attributeConfig.supportScene.1.desc.0",null,"简介：属性的具体参数指标。属性值作为属性的子对象，作为明细存在，只适用于枚举型属性。如：属性：颜色，包含红、黄、蓝等多个属性值。")]},{icon:"fx-icon-obj-app12",title:$t("crm.guidepage.attributeConfig.supportScene.2.title",null,"属性价目表"),desc:[$t("crm.guidepage.attributeConfig.supportScene.2.desc.0",null,"简介：主要解决的场景为不同的属性值可以定义不同的价格。")]},{icon:"fx-icon-obj-stockdetailsobj",title:$t("crm.guidepage.attributeConfig.supportScene.3.title",null,"属性与规格的区别"),desc:[$t("crm.guidepage.attributeConfig.supportScene.3.desc.0",null,"简介：在于规格规格值用于快消行业，属性属性值与主流ERP结构一致，用于制造等通用行业。更适用于与ERP的标准BOM对接的客户。")]}],functionalRelation:{desc:[$t("crm.guidepage.attributeConfig.functionalRelation.desc.0",null,"与多单位/商品（规格）互斥")]}},product:{icon:"fx-icon-obj-app15.svg",title:$t("产品"),api_name:"ProductObj",tag:$t("用于维护和管理基础档案",null,"用于维护和管理基础档案"),desc:$t("crm.guidepage.product.desc",null,"产品管理作为企业的统一产品库，提供清洁统一的产品数据，为企业员工的日常工作提供产品查询服务"),supportScene:[{icon:"fx-icon-obj-marketing_gscp24",title:$t("crm.guidepage.product.supportScene.0.title",null,"产品档案"),desc:[$t("crm.guidepage.product.supportScene.0.desc.0",null,"简介：企业中所有产品的详细信息记录，包括名称、规格、价格、单位、产品分类、生产商等信息。可以帮助企业管理产品信息；")]},{icon:"fx-icon-obj-app18",title:$t("crm.guidepage.product.supportScene.1.title",null,"单位档案"),desc:[$t("crm.guidepage.product.supportScene.1.desc.0",null,"简介：企业中使用的所有计量单位的管理和维护")]},{icon:"fx-icon-obj-app98",title:$t("crm.guidepage.product.supportScene.2.title",null,"产品分类档案"),desc:[$t("crm.guidepage.product.supportScene.2.desc.0",null,"简介：它可以帮助企业对产品进行分类和管理，通过对产品分类档案的维护和管理，企业可以更好地了解产品的销售情况和市场需求，从而更好地制定营销策略；")]},{icon:"fx-icon-obj-app57",title:$t("crm.guidepage.product.supportScene.3.title",null,"多单位产品"),desc:[$t("crm.guidepage.product.supportScene.3.desc.0",null,"简介：它可以帮助企业对不同计量单位的产品进行管理和销售，通过对多单位产品的维护和管理，企业可以更好地满足不同客户的需求，提高销售额和客户满意度")]},{icon:"fx-icon-mgt-applist",title:$t("crm.guidepage.product.supportScene.4.title",null,"多单位产品移动端显示样式控制配置"),desc:[$t("crm.guidepage.product.supportScene.4.desc.0",null,"简介：可以帮助用户更好地了解产品的计量单位，同时也可以更方便地选择不同的计量单位，从而提高用户体验和效率；")]}]},cpqConfig:{title:$t("CPQ配置"),api_name:"BOMObj",tag:$t("crm.guidepage.cpqConfig.tag",null,"主要用于产品组合与产品选配场景"),desc:$t("crm.guidepage.cpqConfig.desc",null,"CPQ代表配置，定价和报价，旨在帮助你执行一个完整、准确、专业的高质量报价。"),supportScene:[{icon:"fx-icon-obj-productobj",title:$t("crm.guidepage.cpqConfig.supportScene.0.title",null,"产品选配明细配置"),desc:[$t("crm.guidepage.cpqConfig.supportScene.0.desc.0",null,"简介：配置产品组合的选配明细，分组，参数（是否必选等）。")]},{icon:"fx-icon-obj-inventoryreport",title:$t("crm.guidepage.cpqConfig.supportScene.1.title",null,"报价与下单时选配产品子件"),desc:[$t("crm.guidepage.cpqConfig.supportScene.1.desc.0",null,"简介：报价单，订单选配产品子件，并遵循参数规则（必选等）。")]},{icon:"fx-icon-obj-pricepolicylimitaccountobj",title:$t("crm.guidepage.cpqConfig.supportScene.2.title",null,"子件定价与母子件价格计算 "),desc:[$t("crm.guidepage.cpqConfig.supportScene.2.desc.0",null,"简介：母件的价格受子件价格影响，也可通过价目表定义母子件价格 。")]},{icon:"fx-icon-obj-projecttaskobj",title:$t("crm.guidepage.cpqConfig.supportScene.3.title",null,"CPQ约束规则"),desc:[$t("crm.guidepage.cpqConfig.supportScene.3.desc.0",null,"简介：通过配置产品子件之间的依赖互斥关系，实现各个子件在选配过程的约束依赖 。")]}],functionalRelation:{desc:[$t("crm.guidepage.cpqConfig.functionalRelation.desc.0",null,"与多单位/固定搭配互斥")]},icon:"fx-icon-obj-app67.svg"},deliveryNote:{visible:!0,router:"#crmmanage/=/module-shiporder",moduleList:[{title:$t("stock.stock_management.stock_management"),router:"#crmmanage/=/module-shiporder"}],title:$t("库存"),api_name:"object_oOUWd__c",tag:$t("crm.guidepage.deliveryNote.tag",null,"全渠道库存流向管理"),desc:$t("crm.guidepage.deliveryNote.desc",null,"围绕商品等资源，对进货、销售、库存进行管理，将信息流、资金流、物流贯穿库存管理的过程之中，达到资源优化配置的目的"),supportScene:[{icon:"fx-icon-obj-deliverynoteobj",title:$t("crm.guidepage.deliveryNote.supportScene.0.title",null,"发货单的创建和维护"),desc:[$t("crm.guidepage.deliveryNote.supportScene.0.desc.0",null,"简介：通过创建发货单及相应的物流信息，完成订单的交付，形成客户---销售—产品的闭环，是售中交易正向流程的核心单据之一")]},{icon:"fx-icon-obj-purchasereturnnoteobj",title:$t("crm.guidepage.deliveryNote.supportScene.1.title",null,"退货单的创建和维护"),desc:[$t("crm.guidepage.deliveryNote.supportScene.1.desc.0",null,"简介：通过创建退货单，可基于订单或发货单进行退货，退货后会更新订单的剩余可发货数量、退货数量等关键信息，是售中交易逆向流程的重要业务单据。")]},{icon:"fx-icon-kucunmingxi",title:$t("crm.guidepage.deliveryNote.supportScene.2.title",null,"库存模块的创建和维护"),desc:[$t("crm.guidepage.deliveryNote.supportScene.2.desc.0",null,"简介：对库存的管理，包括仓库数据的维护，以及基于仓库的出入库、调拨、盘点等业务单据处理。精细化的库存管理，为售中交易的准确性提供了可靠的保障。")]}],functionalRelation:{desc:[$t("crm.guidepage.deliveryNote.functionalRelation.desc.0",null,"依赖销售订单、多单位。"),$t("crm.guidepage.deliveryNote.functionalRelation.desc.1",null,"对于单个产品，序列号与多单位互斥。")]},icon:"fx-icon-obj-app90.svg"},receivableNote:{icon:"fx-icon-obj-app51.svg",tag:$t("crm.guidepage.receivableNote.tag",null,"主流ERP的应收管理模式，回款核销应收"),desc:$t("crm.guidepage.receivableNote.desc",null,"针对大中型或财务要求较高的企业，用回款核销应收，而非扣减订单。 实现订单、应收、回款、发票、业绩等业务闭环，实现简化的业财一体化方案。"),supportScene:[{icon:"fx-icon-obj-paas_fmcg_tpm_activity_budget_type",title:$t("crm.guidepage.receivableNote.supportScene.0.title",null,"应收核销"),desc:[$t("crm.guidepage.receivableNote.supportScene.0.desc.0",null,"简介：支持回款核销应收单，支持应收单、回款单的核销情况查询")]},{icon:"fx-icon-obj-rebateuseruleobj",title:$t("crm.guidepage.receivableNote.supportScene.1.title",null,"销售发票"),desc:[$t("crm.guidepage.receivableNote.supportScene.1.desc.0",null,"简介：支持基于应收单创建销售发票，支持在应收单中查看开票数据")]},{icon:"fx-icon-obj-quotelinesobj",title:$t("crm.guidepage.receivableNote.supportScene.2.title",null,"ERP财务对接"),desc:[$t("crm.guidepage.receivableNote.supportScene.2.desc.0",null,"简介：提供ERP对接能力，实现简化的业财一体化")]}],functionalRelation:{desc:[$t("crm.guidepage.receivableNote.functionalRelation.desc.0",null,"启用应收后，回款不带回款明细，也不支持针对订单回款"),$t("crm.guidepage.receivableNote.functionalRelation.desc.1",null,"如果已启用客户账户，启用应收后，回款不再关联账户，未关联账户的回款可核销应收。原来已关联账户的回款可继续扣减订单")]}},InvoiceApplication:{api_name:"InvoiceApplicationObj",icon:"fx-icon-obj-app35.svg",tag:$t("crm.guidepage.InvoiceApplication.tag",null,"发票"),desc:$t("crm.guidepage.InvoiceApplication.desc",null,"发票是企业做账的依据，也是缴税的费用凭证。"),supportScene:[{icon:"fx-icon-obj-invoiceapplicationobj",title:$t("crm.guidepage.InvoiceApplication.supportScene.0.title",null,"在销售订单详情页进行开票申请操作"),desc:[$t("crm.guidepage.InvoiceApplication.supportScene.0.desc.0",null,"简介：企业人员可以通过具体的某个订单，根据订单信息及业务需要在销售订单详情页发起开票申请")]},{icon:"fx-icon-obj-app35",title:$t("crm.guidepage.InvoiceApplication.supportScene.1.title",null,"支持在【开票申请】菜单新建开票信息"),desc:[$t("crm.guidepage.InvoiceApplication.supportScene.1.desc.0",null,"简介：在开票申请菜单下维护客户信息，销售订单信息，开票金额等信息，创建和维护开票信息")]}]},Payment:{icon:"fx-icon-obj-app61.svg",api_name:"PaymentObj",tag:$t("crm.guidepage.Payment.tag",null,"简易版客户回款管理，回款核销订单"),desc:$t("crm.guidepage.Payment.desc",null,"针对中小企业的简单回款模式，回款核销订单（类似于C端电商，及B2B订货系统），适合部分快消企业"),supportScene:[{icon:"fx-icon-obj-paymentobj",title:$t("crm.guidepage.Payment.supportScene.0.title",null,"基本回款能力"),desc:[$t("crm.guidepage.Payment.supportScene.0.desc.0",null,"简介：支持订单直接核销回款"),$t("crm.guidepage.Payment.supportScene.0.desc.1",null,"支持单独新建回款，覆盖预存款充值/定金/先收款后认领核销等场景"),$t("crm.guidepage.Payment.supportScene.0.desc.2",null,"支持下游渠道商查看回款记录等")]},{icon:"fx-icon-obj-rebateincomedetailobj",title:$t("crm.guidepage.Payment.supportScene.1.title",null,"线上支付"),desc:[$t("crm.guidepage.Payment.supportScene.1.desc.0",null,"简介：支持下游经销商/客户订单在线支付（需开通企业钱包）"),$t("crm.guidepage.Payment.supportScene.1.desc.1",null,"支持业务员面对面二维码收款"),$t("crm.guidepage.Payment.supportScene.1.desc.2",null,"支持线上支付记录查看")]},{icon:"fx-icon-obj-rebateuseruleobj",title:$t("crm.guidepage.Payment.supportScene.2.title",null,"回款入账"),desc:[$t("crm.guidepage.Payment.supportScene.2.desc.0",null,"简介：启用客户账户2.0，回款可通过入账，将余额入账至到客户账户，用户可选择入账到现金、预存款等账户")]}],functionalRelation:{desc:[$t("crm.guidepage.Payment.functionalRelation.desc.0",null,"回款入账需启用客户账户2.0")]}},PaymentPlan:{icon:"fx-icon-obj-app42.svg",api_name:"PaymentPlanObj",tag:$t("crm.guidepage.PaymentPlan.tag",null,"记录销售业务回款的回款计划时间信息"),desc:$t("crm.guidepage.PaymentPlan.desc",null,"通过回款计划模块记录到客户到订单的计划回款金额，计划回款方式，计划回款日期等信息，实现对销售回款有计划的管理和监控。"),supportScene:[{icon:"fx-icon-obj-orderpaymentobj",title:$t("crm.guidepage.PaymentPlan.supportScene.0.title",null,"回款计划的新建和维护"),desc:[$t("crm.guidepage.PaymentPlan.supportScene.0.desc.0",null,"简介：通过创建客户信息，销售订单信息，计划回款金额，计划回款方式，计划回款日期等信息完成回款计划的新建和维护")]},{icon:"fx-icon-obj-paymentplanobj",title:$t("crm.guidepage.PaymentPlan.supportScene.1.title",null,"批量创建回款计划"),desc:[$t("crm.guidepage.PaymentPlan.supportScene.1.desc.0",null,"简介：通过日期循环或者自定义日期的方式批量创建回款计划")]}]},Quote:{icon:"fx-icon-obj-app12.svg",api_name:"QuoteObj",tag:$t("crm.guidepage.Quote.tag",null,"用于报价环节的报价管理业务"),desc:$t("crm.guidepage.Quote.desc",null,"企业在销售过程当中，向客户提供的产品报价清单，也为销售订单提供了一个基本的价格信息。"),supportScene:[{icon:"fx-icon-obj-quotelinesobj",title:$t("crm.guidepage.Quote.supportScene.0.title",null,"报价管理"),desc:[$t("crm.guidepage.Quote.supportScene.0.desc.0",null,"简介：作为企业销售单元在销售过程中，利用价目表对商品价格差异化的灵活控制，与报价单相结合，实现为客户提供快速精准报价、满足复杂报价需求。建立报价单后，可以利用打印或发邮件的方式，将报价单更快捷、全面的展示给客户。")]},{icon:"fx-icon-obj-purchaseorderproductobj",title:$t("crm.guidepage.Quote.supportScene.1.title",null,"一键转订单"),desc:[$t("crm.guidepage.Quote.supportScene.1.desc.0",null,"简介：报价单作为销售订单的基本价格信息，最终业务形态发展为销售订单，报价单一键转换订单功能，实现快速灵活的新建销售订单，实现业务需求。")]}]},SaleContract:{icon:"fx-icon-obj-app48.svg",api_name:"SaleContractObj",tag:$t("crm.guidepage.SaleContract.tag",null,"用于销售环节的合同管理业务"),desc:$t("crm.guidepage.SaleContract.desc",null,"能够快速发起销售合同的业务管理，实现合同过程的有效控制和跟踪，实时了解合同业务各项数据，为企业提供全面的销售合同管理服务"),supportScene:[{icon:"fx-icon-obj-contractobj",title:$t("crm.guidepage.SaleContract.supportScene.0.title",null,"销售合同的创建和维护"),desc:[$t("crm.guidepage.SaleContract.supportScene.0.desc.0",null,"简介：销售合同包括物料产品销售，服务类产品销售。通过在销售合同中录入客户信息，销售信息，财务信息，完成对销售合同的创建和维护。")]},{icon:"fx-icon-obj-costadjustmentnoteobj",title:$t("crm.guidepage.SaleContract.supportScene.1.title",null,"销售合同关联生成下游单据"),desc:[$t("crm.guidepage.SaleContract.supportScene.1.desc.0",null,"简介：支持通过销售合同创建销售订单。可以通过销售合同一键操作转销售订单。")]}]},SalesOrder:{icon:"fx-icon-obj-app69.svg",api_name:"SaleOrderObj",tag:$t("crm.guidepage.SalesOrder.tag",null,"销售业务流程的核心单据"),desc:$t("crm.guidepage.SalesOrder.desc",null,"记录企业销售组织与客户之间进行交易的依据，企业根据订单进行生产，并按照订单信息进行履约交付。"),supportScene:[{icon:"fx-icon-obj-inventoryreport",title:$t("crm.guidepage.SalesOrder.supportScene.0.title",null,"销售订单的创建和维护"),desc:[$t("crm.guidepage.SalesOrder.supportScene.0.desc.0",null,"简介：通过创建基本信息，客户信息，财务信息，订单明细信息完成销售订单的创建和维护")]},{icon:"fx-icon-obj-dealerorderobj",title:$t("crm.guidepage.SalesOrder.supportScene.1.title",null,"销售订单的执行和履约"),desc:[$t("crm.guidepage.SalesOrder.supportScene.1.desc.0",null,"简介：企业通过销售订单安排生产，通过销售订单安排出库，发货，收款，开票等业务环节")]}]},advancedPricing:{icon:"fx-icon-obj-app30.svg",api_name:"object_mk7Lt__c",tag:$t("crm.guidepage.advancedPricing.tag",null,"主要用于促销场景"),desc:$t("crm.guidepage.advancedPricing.desc",null,"以规则引擎的模式实现促销效果。让系统对目标单据上指定字段按一定规则进行修改的一套体系。"),supportScene:[{icon:"fx-icon-obj-pricepolicyobj",title:$t("crm.guidepage.advancedPricing.supportScene.0.title",null,"单品促"),tags:[$t("满赠"),$t("买赠"),$t("打折"),$t("一口价")],desc:[$t("crm.guidepage.advancedPricing.supportScene.0.desc.0",null,"简介：针对具体产品，在指定时间范围内，可以配置满减/买赠/打折/一口价的促销")]},{icon:"fx-icon-obj-projectobj",title:$t("crm.guidepage.advancedPricing.supportScene.1.title",null,"自由组合促"),tags:[$t("满赠"),$t("买赠"),$t("打折")],desc:[$t("crm.guidepage.advancedPricing.supportScene.1.desc.0",null,"简介：针对某个品类下所有产品、某个或多个SPU下所有SKU、指定多个SKU、或这些范围的组合，任意搭配 在指定时间范围内，可以配置满减/买赠/打折/一口价的促销")]},{icon:"fx-icon-obj-projectresourceobj",title:$t("crm.guidepage.advancedPricing.supportScene.2.title",null,"固定组合促"),tags:[$t("满赠"),$t("买赠"),$t("打折"),$t("一口价")],desc:[$t("crm.guidepage.advancedPricing.supportScene.2.desc.0",null,"简介：对指定的多个SKU（其中SKU的数量可配置）打一个包，这个包在指定时间范围内，可以配置是满减/买赠/打折/一口价的促销")]},{icon:"fx-icon-obj-pricepolicylimitaccountobj",title:$t("crm.guidepage.advancedPricing.supportScene.3.title",null,"整单促"),tags:[$t("满赠"),$t("买赠"),$t("打折")],desc:[$t("crm.guidepage.advancedPricing.supportScene.3.desc.0",null,"简介：在指定时间范围内，直接按整单金额满减/买赠或打折。单品促与整单促可以同时执行，共同生效 ")]},{icon:"fx-icon-obj-tpmbudgetaccountobj",title:$t("crm.guidepage.advancedPricing.supportScene.4.title",null,"限额限量"),tags:[],desc:[$t("crm.guidepage.advancedPricing.supportScene.4.desc.0",null,"简介：用于限定促销的优惠发放，把企业的促销费用成本控制在一定范围内，也把促销带来的优惠合理地分给各个客户")]}],functionalRelation:{desc:[$t("crm.guidepage.advancedPricing.functionalRelation.desc.0",null,"依赖价目表")]}},rebate:{icon:"fx-icon-obj-app73.svg",tag:$t("crm.guidepage.rebate.tag",null,"用于企业的返利业务"),desc:$t("crm.guidepage.rebate.desc",null,"基于规则引擎支持企业自定义返利产生和返利消耗规则，实现企业计算返利、经销商使用返利的场景。"),supportScene:[{icon:"fx-icon-obj-rebateincomedetailobj",title:$t("crm.guidepage.rebate.supportScene.0.title",null,"产生返利"),desc:[$t("crm.guidepage.rebate.supportScene.0.desc.0",null,"简介：根据来源对象的单个单据或历史累计单据，按照一定规则，在指定的时间，产生相应面额的返利单，并限制返利单的可使用时间。如季返、年返、返一定面额后续抵扣应付金额、返一定产品后续可以免费提货"),{title:$t("支持场景："),desc:[$t("crm.guidepage.rebate.supportScene.0.desc.1.desc.0",null,"依据：当单/历史累计"),$t("crm.guidepage.rebate.supportScene.0.desc.1.desc.1",null,"时机：逐单/手动/按指定时间/周期性"),$t("crm.guidepage.rebate.supportScene.0.desc.1.desc.2",null,"结果：金额返利/产品返利"),$t("crm.guidepage.rebate.supportScene.0.desc.1.desc.3",null,"阶梯：单阶梯/多阶梯")]}]},{icon:"fx-icon-obj-rebateuseruleobj",title:$t("crm.guidepage.rebate.supportScene.1.title",null,"使用返利"),desc:[$t("crm.guidepage.rebate.supportScene.1.desc.0",null,"简介：在单据上按指定的使用规则消耗返利，减少待支付金额或随单带走返利品"),{title:$t("支持场景："),desc:[$t("crm.guidepage.rebate.supportScene.1.desc.1.desc.0",null,"金额返利：冲抵回款/分摊折价"),$t("crm.guidepage.rebate.supportScene.1.desc.1.desc.1",null,"产品返利：按数量返/按金额返"),$t("crm.guidepage.rebate.supportScene.1.desc.1.desc.2",null,"返利限制：单据上使用返利的条件及额度/返利本身的适用")]}]}],functionalRelation:{desc:[$t("crm.guidepage.rebate.functionalRelation.desc.0",null,"依赖价目表")]}},coupon:{icon:"fx-icon-obj-app25.svg",tag:$t("crm.guidepage.coupon.tag",null,"用于企业的优惠券业务"),desc:$t("crm.guidepage.coupon.desc",null,"可设置优惠券方案并分发优惠券，客户可在交易中使用优惠券，实现企业的优惠券场景。"),supportScene:[{icon:"fx-icon-obj-paas_fmcg_tpm_activity_budget_consume_rule",title:$t("crm.guidepage.coupon.supportScene.0.title",null,"金额满减券"),desc:[$t("crm.guidepage.coupon.supportScene.0.desc.0",null,"简介：根据优惠券方案手工录入或按条件批量产生的方式形成优惠券，并能在订单上使用优惠券"),{title:$t("支持场景："),desc:[$t("电子券"),$t("纸质券"),$t("使用类型：冲抵回款/分摊折价"),$t("分发方式：企业发放/客户领取")]}]}],functionalRelation:{desc:[$t("crm.guidepage.coupon.functionalRelation.desc.0",null,"依赖价目表，发放优惠券需要启用营销通，领取优惠券是在订货通场景")]}},pricebook:{icon:"fx-icon-obj-app42.svg",api_name:"PriceBookObj",tag:$t("crm.guidepage.pricebook.tag",null,"在销售产品时定义不同的价格"),desc:$t("crm.guidepage.pricebook.desc",null,"清晰定义价目表应用的客户范围以及产品价格"),supportScene:[{icon:"fx-icon-obj-pricebookobj",title:$t("crm.guidepage.pricebook.supportScene.0.title",null,"价目表产品价格"),desc:[$t("crm.guidepage.pricebook.supportScene.0.desc.0",null,"简介：在销售产品时定义不同的价格，主要目标是能够让用户快速的维护企业产品的销售价格")]},{icon:"fx-icon-obj-pricepolicyexcludeaccountobj",title:$t("crm.guidepage.pricebook.supportScene.1.title",null,"价目表适用客户"),desc:[$t("crm.guidepage.pricebook.supportScene.1.desc.0",null,"简介：制定不同客户的价目表")]},{icon:"fx-icon-obj-pricepolicylimitaccountobj",title:$t("crm.guidepage.pricebook.supportScene.2.title",null,"价目表优先级"),desc:[$t("crm.guidepage.pricebook.supportScene.2.desc.0",null,"简介：支持定义价目表优先级，影响客户取价最终价格带出")]},{icon:"fx-icon-obj-pricebookproductobj",title:$t("crm.guidepage.pricebook.supportScene.3.title",null,"[ 价目表明细 ] 支持有效期"),desc:[$t("crm.guidepage.pricebook.supportScene.3.desc.0",null,"简介：解决产品不同有效期的价格定义场景")]},{icon:"fx-icon-obj-pricebookproductobj",title:$t("crm.guidepage.pricebook.supportScene.4.title",null,"[ 价目表明细 ] 支持阶梯定价"),desc:[$t("crm.guidepage.pricebook.supportScene.4.desc.0",null,"简介：解决产品不同梯度的阶梯价的场景")]},{icon:"fx-icon-obj-pricebookproductobj",title:$t("crm.guidepage.pricebook.supportScene.5.title",null,"[ 价目表明细 ] 支持多单位定价"),desc:[$t("crm.guidepage.pricebook.supportScene.5.desc.0",null,"简介：解决产品不同单位价格不同的场景")]}]},availableRange:{icon:"fx-icon-obj-app46.svg",api_name:"AvailableRangeObj",tag:$t("crm.guidepage.availableRange.tag",null,"用于定义客户的允销范围"),desc:$t("crm.guidepage.availableRange.desc",null,"可售范围是企业的不同销售组织，对于不同类型客户或不同类型合作伙伴，可销售的产品不同所定义的列表"),supportScene:[{icon:"fx-icon-obj-app46",title:$t("crm.guidepage.availableRange.supportScene.0.title",null,"可售范围"),desc:[$t("crm.guidepage.availableRange.supportScene.0.desc.0",null,"简介：定义哪些客户可售卖哪些产品")]},{icon:"fx-icon-obj-app46",title:$t("crm.guidepage.availableRange.supportScene.1.title",null,"可售范围优先级"),desc:[$t("crm.guidepage.availableRange.supportScene.1.desc.0",null,"简介：客户可售产品优先级定义，帮助客户解决按照优先级带出可售卖产品")]},{icon:"fx-icon-obj-app46",title:$t("crm.guidepage.availableRange.supportScene.2.title",null,"可售范围查重"),desc:[$t("crm.guidepage.availableRange.supportScene.2.desc.0",null,"简介：解决维护可售范围涉及重叠客户，不清楚选择客户后，客户具体在适用哪个可售范围的场景")]}]},customerAccount:{icon:"fx-icon-obj-app78.svg",tag:$t("品牌商营销资金管理套件"),desc:$t("crm.guidepage.customerAccount.desc",null,"广泛用于快消、制造、消费电子等行业，用于管理客户的预存款、返利、费用、积分等资产。"),supportScene:[{icon:"fx-icon-obj-app8",title:$t("crm.guidepage.customerAccount.supportScene.0.title",null,"基础能力"),desc:[$t("crm.guidepage.customerAccount.supportScene.0.desc.0",null,"自定义现金、返利、费用、积分等账户类型"),$t("crm.guidepage.customerAccount.supportScene.0.desc.1",null,"查看、管理客户账户余额"),$t("crm.guidepage.customerAccount.supportScene.0.desc.2",null,"通过收支流水记录账户的收支明细"),$t("crm.guidepage.customerAccount.supportScene.0.desc.3",null,"账户支付、回款入账")]},{icon:"fx-icon-obj-app9",title:$t("crm.guidepage.customerAccount.supportScene.1.title",null,"高级能力"),desc:[$t("crm.guidepage.customerAccount.supportScene.1.desc.0",null,"自定义对象入账、TPM入账"),$t("crm.guidepage.customerAccount.supportScene.1.desc.1",null,"账户余额直接扣减、检验扣减"),$t("crm.guidepage.customerAccount.supportScene.1.desc.2",null,"账户红冲"),$t("crm.guidepage.customerAccount.supportScene.1.desc.3",null,"渠道对账单"),$t("crm.guidepage.customerAccount.supportScene.1.desc.4",null,"已和返利模块整合打通")]}]}},i={productClassification:Object.assign(Object.assign({},n.product),{icon:"fx-icon-obj-app57.svg"}),multipleUnit:Object.assign(Object.assign({},n.product),{icon:"fx-icon-obj-app18.svg"}),simpleCpq:Object.assign(Object.assign({moduleId:"advancedPricing"},n.advancedPricing),{icon:"fx-icon-obj-app30.svg",desc:$t("crm.guidepage.simpleCpq.desc",null,"对指定的多个SKU（其中SKU的数量可配置）打一个包，这个包在指定时间范围内，可以配置是满减/买赠/打折/一口价的促销")}),productPortfolio:Object.assign(Object.assign({moduleId:"cpqConfig"},n.cpqConfig),{icon:"fx-icon-obj-app98.svg"}),productSelection:Object.assign(Object.assign({moduleId:"cpqConfig"},n.cpqConfig),{icon:"fx-icon-obj-app96.svg"}),outboundOrder:Object.assign(Object.assign({},n.deliveryNote),{icon:"fx-icon-obj-app55.svg"}),exchangeGoods:Object.assign(Object.assign({},n.deliveryNote),{icon:"fx-icon-obj-app92.svg"})};n=Object.assign(Object.assign({},n),i);c.default=(e=>{for(var c in e){c=e[c];c.icon&&(c.icon="https://a0.fspage.com/FSR/fs-qixin/static/appicon/svg/"+c.icon)}return e})(n)});
define("crm-setting/guidepage/guidepage",[],function(e,n,t){var i=Backbone.View.extend({el:".crm-s-guidepage",initialize:function(e){this.Comp=null},render:function(){var t=this;e.async("vcrm/sdk",function(e){e.getComponent("configGuide").then(function(n){t.Comp=new Vue({el:t.$el[0],mounted:function(){},methods:{destroy:function(){this.$refs.configGuide.destroy()}},render:function(e){return e(n.default,{ref:"configGuide"})}})})})},destroy:function(){this.Comp&&this.Comp.destroy&&this.Comp.destroy(),this.Comp=null}});t.exports=i});