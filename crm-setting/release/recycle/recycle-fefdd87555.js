define("crm-setting/recycle/recycle",["crm-modules/common/util","./template/batch-html","crm-widget/table/table","crm-widget/select/select","base/assets/plugins/sparkui/spark","crm-widget/dialog/dialog","crm-widget/selector/selector"],function(n,e,t){var d=n("crm-modules/common/util"),a=n("./template/batch-html"),s=n("crm-widget/table/table"),i=n("crm-widget/select/select"),o=(n("base/assets/plugins/sparkui/spark"),n("crm-widget/dialog/dialog")),r=n("crm-widget/selector/selector"),l=Backbone.Model.extend({defaults:{typeList:[{value:"key",name:"name"},{value:"key1",name:"name1"}],QueryInfo:{},type:"",name:"",person:"",startTime:"",endTime:"",blackList:["NewOpportunityObj","SalesOrderObj","QuoteObj","SPUObj","ProductObj","DeliveryNoteObj","ScheduleObj"]},getEmployee:function(){return FS.contacts.getAllEmployees()},getQueryInfo:function(){return{name:this.get("name").name||"",invalidOperator:this.get("person").person||"",startDate:parseInt(this.get("startTime").startTime)||0,endDate:parseInt(this.get("endTime").endTime)||0}},getTypeList:function(){return d.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeList",data:{isDraft:!1,isIncludeSystemObj:!0,isIncludeFieldDescribe:!1,includeBigObject:!0,packageName:"CRM",objectType:"all"}},{errorAlertModel:1})},setTypeList:function(e){var t=[],a=null;_.each(e,function(e){e.is_deleted||_.contains(["LeadsPoolObj","SalesOrderProductObj"],e.api_name)||(t.push({value:e.api_name,name:e.display_name,id:e._id}),e.isDefault&&(a=t[t.length-1]))}),this.set("typeList",t),this.set("type",(a||t[0]||{}).value,{silent:!0}),this.set("objname",(a||t[0]||{}).name,{silent:!0})},getTbFirstHead:function(){switch(this.get("type")){case"ContractObj":case"SalesOrderObj":case"ReturnedGoodsInvoiceObj":case"PaymentObj":case"RefundObj":case"InvoiceApplicationObj":return $t("编号");default:return $t("名称")}}}),c=Backbone.View.extend({events:{"click .j-g-recover":"_onRecoverGroup","click .j-g-delete":"_onDeleteGroup","keydown .r-search-ipt":"_onFilterName","click .h-search-btn":"_onSearch"},initialize:function(e){var t=this;t.setElement(e.wrapper),t.widgets={},t.ajax={},t.model=new l,t.listenTo(t.model,"change:type",t._refreshTB),t.listenTo(t.model,"change:person",t._refreshTB),t.listenTo(t.model,"change:name",t._refreshTB),t.listenTo(t.model,"change:startTime",t._refreshTB),t.listenTo(t.model,"change:endTime",t._refreshTB)},render:function(){return this.initTable(),this._getTypeList(),this},initWidgets:function(){this.initSelect(),this.initSelectRange(),this.initDate()},initTable:function(){var i=this;i.widgets.Tb&&i.widgets.Tb.destroy(),i.widgets.Tb=new s({$el:i.$el,title:$t("回收站"),url:"/EM1HNCRM/API/v1/object/recycle_bin/service/findInvalidDataList",requestType:"FHHApi",searchTip:$t("请输入名称"),showMultiple:!0,openStart:!0,noSupportLock:!0,batchBtns:[{text:$t("恢复"),className:"j-g-recover"},{text:$t("删除"),className:"j-g-delete"}],checked:{maxNum:400,idKey:"_id",data:[]},postData:{describe_api_name:i.model.get("type"),endDate:0,startDate:0,invalidOperator:"",name:""},page:{pageSize:20,pageNumber:1},columns:[{data:"name",title:$t("姓名"),dataType:1,width:200,isOrderBy:!0,orderValues:[1,0]},{data:"object_describe_api_name",title:$t("类型"),dataType:1,width:200,render:function(e){var t=i.model.get("typeList");return"<div>"+_.findWhere(t,{value:e}).name+"</div>"}},{data:"last_modified_by",title:$t("操作人"),dataType:1,width:200,isOrderBy:!0,orderValues:[1,0],render:function(e,t,a){e=d.getEmployeeById(e),a=a.last_modified_by__r;return a?a.name:e&&e.name||CRM.config.TEXT_DEFAULT}},{data:"last_modified_time",title:$t("操作时间"),dataType:1,width:200,isOrderBy:!0,orderValues:[1,0],render:function(e){e=new Date(e).getTime();return FS.moment.unix(e/1e3,!0).format("YYYY-MM-DD HH:mm")}},{title:$t("操作"),dataType:1,width:200,render:function(e,t,a){return'<div class="operate">'+((i.model.get("blackList")||[]).includes(a.object_describe_api_name)?"":'<a href="javascript:;" data-action="recover">'+$t("恢复")+"</a>")+'<a href="javascript:;" data-action="delete">'+$t("删除")+"</a></div>"}}],formatData:function(e){return i._trueTatal?i._trueNumQuery===i.__getTrueQueryParam(i.widgets.Tb.getParam())?i.widgets.Tb.__total=i._trueTatal:(i._trueTatal=0,i.widgets.Tb.__total=e.totalCount):i.widgets.Tb.__total=e.totalCount,i.__bulkButtons=e.bulkButtons,{totalCount:i.widgets.Tb.__total,data:e.dataList}}}),$(".batch-term",i.$el).append(a({})),i.widgets.Tb.on("trclick",function(e,t,a){0<a.closest(".operate").length?i._doAjax(a.data("action"),e._id):i.showDetail(e)}),i.widgets.Tb.on("checkbox.click",function(){(i.model.get("blackList")||[]).includes(i.model.get("type"))?$(".j-g-recover",i.$el).hide():$(".j-g-recover",i.$el).show()}),i.widgets.Tb.on("renderListComplete",_.bind(function(){i.__initAboutNum()},i))},__initAboutNum:function(){var t=this,e=(this._tatal=this.widgets.Tb.__total,this._totalNumLimit||this.options._totalNumLimit||1e3),a=this._tatal>=e&&!this._trueTatal,i=this.widgets.Tb.$el.find(".dt-page"),n=i.find(".about-num-wrap");n.length||(i.find(".dt-page-right").before('<span class="about-num">'.concat($t("约{{num}}条",{num:e+"+"}),'</span><div class="about-num-wrap"><span class="look-num j-look-num">').concat($t("精确总数"),"</span></div>")),i.find(".page-num").after('<span class="about-num">'.concat($t("+"),"</span>")),n=i.find(".about-num-wrap"),i.find(".j-look-num").click(function(e){t._onLookNum(e)})),i.find(".total-num").toggle(!a),i.find(".j-look-num").html($t("精确总数")).removeClass("coding"),i.find(".about-num").toggle(a),n.toggle(a)},_onLookNum:function(e){var t=this,e=$(e.target);e.hasClass("coding")||(e.addClass("coding").html($t("计算中")),this._getAbountNum(function(e){t.widgets.Tb&&(t.widgets.Tb.setPaginationTotal(e),(e=t.widgets.Tb.$el.find(".dt-page")).find(".about-num,.about-num-wrap").hide(),e.find(".total-num").show())}))},_getAbountNum:function(t){var a=this,i=this.widgets.Tb.getParam();CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/recycle_bin/service/findInvalidDataList",data:_.extend({find_explicit_total_num:!0},i),success:function(e){a.widgets.Tb&&(0===e.Result.StatusCode?(a._trueNumQuery=a.__getTrueQueryParam(i),t(a._trueTatal=e.Value.totalCount)):CRM.util.alert(e.Result.FailureMessage))}},{errorAlertModel:1})},__getTrueQueryParam:function(e){e=CRM.util.parseJson(CRM.util.stringify(e));return delete e.pageNumber,delete e.pageSize,CRM.util.stringify(e)},showDetail:function(t){var a=this,e=t.object_describe_api_name,i=t._id;FS.util.getUserAttribute("feedObjs")&&0<=FS.util.getUserAttribute("feedObjs").indexOf(e)?FS.MEDIATOR.trigger("fs.feed2019.slide",{url:"FsFeed/getFeedByObjectId",data:{apiName:e,dataId:i},options:{zIndex:CRM.util.getzIndex(),success:function(){},beforeDestroy:function(){}}}):n.async(d.getCrmFilePath(t.object_describe_api_name,"detail","recycle"),function(e){a.widgets["myObjDetail"+t.object_describe_api_name]||(a.widgets["myObjDetail"+t.object_describe_api_name]=new e({entry:"recycle",apiName:t.object_describe_api_name,top:56,noTopping:!0}),a.widgets["myObjDetail"+t.object_describe_api_name].on("refresh",function(){a.widgets.Tb.setParam({},!0)})),a.widgets["myObjDetail"+t.object_describe_api_name].show(t._id,t.object_describe_api_name)})},_refreshTB:_.debounce(function(){this.widgets.Tb&&this.widgets.Tb.setParam(_.extend(this.model.getQueryInfo(),{describe_api_name:this.model.get("type")}),!0)},20),_getTypeList:function(){var a=this;a.ajax.getListXhr=a.model.getTypeList(),a.ajax.getListXhr.then(function(e){var t;0===e.Result.StatusCode?(t=e.Value.objectDescribeList,a.model.setTypeList(t),a.initWidgets(),a.widgets.Tb.setParam({describe_api_name:a.model.get("type"),endDate:0,startDate:0,invalidOperator:"",name:""}),a.widgets.Tb.start()):d.alert(e.Result.FailureMessage)})},initSelect:function(){var a=this;a.widgets.select&&a.widgets.select.destory(),a.widgets.select=new i({$wrap:a.$(".r-select"),width:150,size:1,options:a.model.get("typeList")}),a.toggleWarnMessage(a.widgets.select.getValue()),a.widgets.select.on("change",function(e,t){a.model.set("type",t.value),a.model.set("objname",t.name),a.updateFirstHead(),a.toggleWarnMessage(t.value),a.widgets.Tb.clearRemberData()})},toggleWarnMessage:function(e){var t=(this.model.get("blackList")||[]).includes(e),e=_.findWhere(this.model.get("typeList"),{value:e})||{};this.$(".r-batch-warn").toggle(t).text(t?"".concat(e.name,"，").concat($t("不允许恢复")):"")},updateFirstHead:function(){var e=this,t=$(".header .th-name .tb-cell .icon-title",e.widgets.Tb.$el);e.ajax.head&&e.ajax.head.abort(),e.ajax.head=d.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDraftByApiName",data:{draft_apiname:e.model.get("type"),include_layout:!1,layout_type:"detail"},success:function(e){0==e.Result.StatusCode&&(e=e.Value.objectDescribeDraft.fields.name,t.text(e?e.label:$t("名称")))}})},initSelectRange:function(){var t=this;t.widgets.selectRange||(t.widgets.selectRange=new r({$wrap:t.$el.find(".r-search-person"),zIndex:1e3,size:1,v2Size:"mini",selectedAfterHideLabel:!0,tabs:[{id:"member",title:$t("同事"),type:"sort",data:FS.contacts.sortEmployeesByLetter(t.model.getEmployee())}],single:!0,label:$t("选择操作人")}),t.widgets.selectRange.on("addItem",function(e){t.model.set("person",{person:e.member[0]})}),t.widgets.selectRange.on("cancelItem",function(e){t.model.set("person",{person:""})}))},initDate:function(){var t=this;try{t.widgets.selectDate&&t.widgets.selectDate.destroy(),t.widgets.selectDate=FxUI.create({wrapper:t.$el.find(".r-search-date")[0],template:'<fx-date-picker unlink-panels :default-time="defaulttime" :disabled="disabled" v-model="value" value-format="timestamp" :format="sFormat" :picker-options="options" @change="changeHandle" :type="type" :start-placeholder="splaceholder" :end-placeholder="eplaceholder" size="mini"></fx-date-picker>',data:function(){return{value:"",defaulttime:["","23:59:59"],disabled:!1,type:"datetimerange",sFormat:"yyyy-MM-dd HH:mm",splaceholder:$t("开始时间"),eplaceholder:$t("结束时间")}},methods:{changeHandle:function(){var e=this.value||["",""];t.model.set("startTime",{startTime:e[0]}),t.model.set("endTime",{endTime:e[1]})}}})}catch(e){}},_getCheckedItem:function(){var t=[];return this.widgets.Tb&&(this.widgets.Tb.getCheckedData(),_.map(this.widgets.Tb.getCheckedData(),function(e){t.push(e._id)})),t},config:{recover:{name:$t("恢复"),url:"/EM1HNCRM/API/v1/object/apiname/action/BulkRecover",tip:$t("确定恢复到作废之前的状态")},delete:{name:$t("删除"),url:"/EM1HNCRM/API/v1/object/apiname/action/BulkDelete",tip:$t("确定要删除吗")}},_doAjax:function(e,t,a){var i,n,s=this,o=s.widgets.Tb,r=s.config[e],l=o.getRemberData()||[];t||l.length?(i=t?[t]:_.pluck(l,"_id"),n=d.confirm(r.tip.replace(/\{\}/g,s.model.get("objname")),$t("提示"),function(){a?(s.__doAsyncRecover(i,o),n.hide(),s._trueTatal=void 0,o._pagination.set("activePageNumber",1)):$(".b-g-btn",n.element).hasClass("state-requesting")||(s.widgets[e+"Ajax"]&&s.widgets[e+"Ajax"].abort(),s.widgets[e+"Ajax"]=d.FHHApi({url:r.url.replace("apiname",s.model.get("type")),data:{describe_api_name:s.model.get("type"),idList:i},success:function(e){var t;0===e.Result.StatusCode?(t=e.Value.success,d.remind(t?$t("操作成功！"):$t("操作失败！")),s._trueTatal=void 0,o._pagination.set("activePageNumber",1),o.clearRemberData(),t&&(t={},s._toFirstPage(o,i.length)&&(t.pageNumber=1),o.setParam(t,!0))):d.alert(e.Result.FailureMessage),n.hide()}},{errorAlertModel:1,submitSelector:$(".b-g-btn",n.element)}))})):d.remind(2,$t("请勾选要恢复的数据"))},__doAsyncRecover:function(t,a){var i=this;CRM.api.list_batchbtn_operate({apiname:i.model.get("type"),async_button_apiname:"AsyncBulkRecover",button_api_name:_.findWhere(i.__bulkButtons,{action:"AsyncBulkRecover"}).api_name,dataIds:t,success:function(){a.clearRemberData();var e={};i._toFirstPage(a,t.length)&&(e.pageNumber=1),a.setParam(e,!0)}})},_toFirstPage:function(e,t){var a=e.__total;if(a)return(a-=t)<=(t=e.getParam()).pageSize*(t.pageNumber-1)},_onRecoverGroup:function(e,t){this._doAjax("recover",t,this.__bulkButtons&&this.__bulkButtons.length)},_onDeleteGroup:function(e,t){this._doAjax("delete",t)},initDialog:function(){this.widgets.dialogR&&this.widgets.dialogR.destory(),this.widgets.dialogR=new o({title:$t("提示"),content:$t("确定要恢复这条数据么")})},_onClearDialog:function(e){var t=this,e=$(e.target);t.widgets.dialogC&&t.widgets.dialogC.destory&&t.widgets.dialogC.destory(),t.widgets.dialogC=new o({title:$t("提示"),content:$t("您确定要永久删除回收站中的数据吗此操作不能恢复清空回收站中所有记录。"),showBtns:!0}),t.widgets.dialogC.show(),t.widgets.dialogC.on("dialogEnter",function(){t._onClearAll(),this.destroy()}),t.widgets.dialogC.on("dialogCancel",function(){this.destroy()}),e.toggleClass("cur")},_onClearAll:function(){d.FHHApi({url:"",success:function(e){e.Result.StatusCode},error:function(){d.alert($t("网络异常，请重试！"))}},{errorAlertModel:1})},_onFilterName:function(e){e=e||window.event;$(e.target||e.srcElement);13===e.which&&this.$el.find(".h-search-btn").trigger("click")},_onSearch:function(e){var t=$.trim(this.$el.find(".r-search-ipt").val());this.model.set("name",{name:t})},destroy:function(){_.each(this.widgets,function(e){e&&e.destory&&e.destory()}),_.each(this.ajax,function(e){e&&e.abort&&e.abort()}),this.$el.off().remove()}});t.exports=c});
define("crm-setting/recycle/template/batch-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="item r-batch-search"> <span class="item-tit">' + ((__t = $t("对象：")) == null ? "" : __t) + '</span> <div class="item-con r-select"></div> <div class="cus-line"></div> <div class="item-con r-search"> <div class="item-con r-ipt-wrap"> <input placeholder="' + ((__t = $t("请输入名称")) == null ? "" : __t) + '" class="r-search-ipt"/> <button class="h-search-btn"></button> </div> </div> <div class="cus-line"></div> <div class="item-con r-search-person"></div> <div class="cus-line"></div> <div class="item-con r-search-date"></div> </div> <div class="item r-batch-warn"></div>';
        }
        return __p;
    };
});