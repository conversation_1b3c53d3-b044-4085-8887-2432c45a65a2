define("crm-setting/robot/robot",[],function(t,n,e){var i=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper)},render:function(){this.initDefaultData(),this.initRobot()},initDefaultData:function(){this.comps={}},initRobot:function(n){var e=this;n=n||this.el,t.async("app-standalone/app",function(t){t.getModuleComp("intelligent-robot/robot-index/robot-index-vue.js").then(function(t){t=new t({el:document.createElement("div"),propsData:{entrance:"manage"}});e.comps.robotInstance=t,n.append(t.$el)})})}});e.exports=i});