define("crm-setting/rolemanageenterpriseinterconnect/permissionsset/fieldprivilege/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="rolemanage-dialog-info">' + ((__t = $t("crm.设置字段权限提示")) == null ? "" : __t) + '</div> <div class="search-wrap"><span>' + ((__t = $t("搜索")) == null ? "" : __t) + ':</span><input class="b-g-ipt j-s-field" placeholder="' + ((__t = $t("搜索字段名")) == null ? "" : __t) + '"/></div> <dl class="rolemanage-dialog-head crm-g-form"> <dt class="fm-lb"><em></em>' + ((__t = $t("全选")) == null ? "" : __t) + '</dt> <dd class="mn-radio-box radio-box-head"> <span class="radio-item"> <span data-permissiontype="2" class="mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + '"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span data-permissiontype="1" class="mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + '"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span data-permissiontype="0" class="mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + '"></span><span class="radio-lb">' + ((__t = $t("不可见")) == null ? "" : __t) + '</span> </span> </dd> </dl> <div class="rolemanage-dialog-body crm-g-form"> ';
            _.each(fieldInfoList, function(item, index) {
                __p += " ";
                var fieldName = item.fieldName;
                __p += ' <dl> <dt class="fm-lb" title="' + ((__t = item.fieldCaption) == null ? "" : __t) + '"><em>';
                if (item.isRequire) {
                    __p += "*";
                }
                __p += '</em><div class="filed-caption">' + __e(item.fieldCaption) + '</div></dt> <dd class="mn-radio-box radio-box-body ';
                if ([ "product_amount", "order_amount", "discount" ].indexOf(fieldName) > -1) {
                    __p += "saleitem";
                }
                __p += " ";
                if ([ "ProductPrice", "Discount", "Price" ].indexOf(fieldName) > -1) {
                    __p += "orderitem";
                }
                __p += '" data-fieldname=' + ((__t = fieldName) == null ? "" : __t) + '> <span class="radio-item"> <span class="mn-radio-item ';
                if (item.status === 2) {
                    __p += "mn-selected";
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfWrite) {
                    __p += "disabled-selected";
                }
                __p += '" data-permissiontype="2"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="mn-radio-item ';
                if (item.status === 1) {
                    __p += "mn-selected";
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfReadOnly || item.isRequire && readonly) {
                    __p += "disabled-selected";
                }
                __p += '" data-permissiontype="1"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="mn-radio-item ';
                if (item.status === 0) {
                    __p += "mn-selected";
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfInvisible || item.isRequire && readonly) {
                    __p += "disabled-selected";
                }
                __p += '" data-permissiontype="0"></span><span class="radio-lb">' + ((__t = $t("不可见")) == null ? "" : __t) + "</span> </span> </dd> </dl> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/rolemanageenterpriseinterconnect/permissionsset/fieldprivilege/index",["crm-modules/common/util","crm-widget/dialog/dialog"],function(e,i,s){var t=e("crm-modules/common/util"),e=e("crm-widget/dialog/dialog"),r="mn-selected",n="disabled-selected",l="."+r,a=e.extend({attrs:{title:$t("字段权限"),content:'<div class="crm-loading "></div>',showScroll:!0,showBtns:!0,datas:null,className:"crm-s-rolemanage"},events:{"click .radio-box-body .mn-radio-item":"onCheck","click .radio-box-head .mn-radio-item":"onCheckAll","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy","input .j-s-field":"onInput","keypress .j-s-field":"onKeyPress"},replaceRegExp:function(e){return(e+="").replace(/\(/g,"\\(").replace(/\-/g,"\\-").replace(/\)/g,"\\)").replace(/\[/g,"\\[").replace(/\]/g,"\\]").replace(/\{/g,"\\{").replace(/\}/g,"\\}").replace(/\+/g,"\\+").replace(/\./g,"\\.")},highligh:function(e,i){var s;return i&&(i=_.escape(i),s=new RegExp(this.replaceRegExp(i),"gi"),i)?(e+="").replace(s,function(e){return'<span class="dt-mark">'+e+"</span>"}):e},onInput:function(){var o=this;o.searchTimer&&(clearTimeout(o.searchTimer),o.searchTimer=null),o.searchTimer=setTimeout(function(){var a=o.$(".j-s-field").val();o.$(".filed-caption").each(function(e,i){var s=$(i).text();$(i).html(o.highligh(s,a))})},100)},onKeyPress:function(e){var i=this;13==e.which&&0<i.$(".dt-mark").length&&(e=i.$(".dt-mark").offset().top-i.$(".dialog-scroll").offset().top,i.$(".dialog-scroll-el").scrollTop(e-30))},render:function(){return a.superclass.render.call(this)},show:function(e){return a.superclass.show.call(this)},onCheck:function(e){var s,a,i=$(e.currentTarget),o=this.$(".radio-box-head .mn-radio-item");return i.hasClass(n)?(e.stopPropagation(),!1):i.closest(".radio-box-body").find(l).hasClass(n)?(t.remind(3,$t("crm.已选中项不可编辑")),void e.stopPropagation()):(i.closest(".radio-box-body").find(".mn-radio-item").each(function(e,i){$(i).removeClass(r)}),i.toggleClass(r),s=i.data("permissiontype"),i.closest(".radio-box-body").hasClass("saleitem")&&this.$(".saleitem").each(function(e,i){(a=$(i).find(".mn-radio-item")).hasClass(n)||($(i).find(".mn-radio-item").removeClass(r),a.eq(2-s).addClass(r))}),i.closest(".radio-box-body").hasClass("orderitem")&&this.$(".orderitem").each(function(e,i){(a=$(i).find(".mn-radio-item")).hasClass(n)||($(i).find(".mn-radio-item").removeClass(r),a.eq(2-s).addClass(r))}),e=_.every(this.$(".radio-box-body"),function(e,i){return $(e).find(l).data("permissiontype")===s}),o.removeClass(r),e&&o.eq(2-s).addClass(r),!1)},onCheckAll:function(e){var s,i=$(e.currentTarget),a=i.data("permissiontype");if(i.hasClass(n))return e.stopPropagation(),!1;_.each(this.$(".radio-box-body"),function(e,i){(s=$(e).find(".mn-radio-item")).eq(2-a).hasClass(n)||(s.removeClass(r),s.eq(2-a).addClass(r))})},onSubmit:function(){var s={};_.each(this.$(".radio-box-body"),function(e,i){s[$(e).data("fieldname")]=$(e).find(l).data("permissiontype")}),this.trigger("submit",s)},destroy:function(){return a.superclass.destroy.call(this)}});s.exports=a});
define("crm-setting/rolemanageenterpriseinterconnect/permissionsset/roleprivilege/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<!-- * @Descripttion: * @Author: LiAng * @Date: 2020-05-14 23:41:23 * @LastEditors: LiAng * @LastEditTime: 2020-05-14 23:49:21 --> ";
            _.each(list, function(item, index) {
                __p += ' <div class="lazyload-item" ';
                if (_.contains([ "AccountObj", "AccountAddrObj" ], item.descApiName)) {
                    __p += ' data-functionnumber="' + ((__t = item.descApiName) == null ? "" : __t) + '" ';
                }
                __p += ' data-descapiname="' + ((__t = item.descApiName) == null ? "" : __t) + '" data-descapidisplayname="' + ((__t = item.descApiDisplayName) == null ? "" : __t) + '"> <!-- <script type="text/lazyload"> --> <h3 class="checkbox-head checkbox-head-toggle current"> <span class="ico-arrow"></span> <span class=\'desc-api-display-name\'>' + ((__t = item.descApiDisplayName) == null ? "" : __t) + "</span> ";
                var titleDesc = "";
                if (item.descApiName === "BIPreConfigedReport") {
                    titleDesc = $t("系统预置报表全员可查看，导出功能走此处配置，数据权限走预置报表的主业务模块的数据权限.");
                } else if (item.descApiName === "BIBlankTemplate") {
                    titleDesc = $t("基于自选主题建立的图表的功能权限走此处配置，数据权限走主业务模块的数据权限.");
                } else if (item.descApiName === "BIProduct") {
                    titleDesc = $t("基于产品分析主题建立的图表的功能权限走此处配置，数据权限按主业务模块产品的数据权限进行控制。产品模块通常为公开只读，请谨慎设置.");
                } else if ([ "BICustomer", "BIOrder", "BIOpportunity", "BISalesClue", "BIContractAchievement", "BIEmployeeAchievement", "BICheckinsAnalyse", "BIGoalAnalysis" ].indexOf(item.descApiName) != -1) {
                    titleDesc = $t("基于{{descApiDisplayName}}主题建立的图表的功能权限走此处配置，数据权限按主业务模块{{descApiDisplayName}}的数据权限进行控制.", {
                        descApiDisplayName: item.descApiDisplayName
                    });
                }
                __p += " ";
                if (titleDesc) {
                    __p += ' <span class="crm-ui-title bi-object-title-desc" data-title="' + ((__t = titleDesc) == null ? "" : __t) + '" data-pos="top"></span> ';
                }
                __p += " ";
                var roleFunctionInfos = item.roleFunctionInfos;
                var allEnabled = _.every(roleFunctionInfos, function(ite) {
                    return ite.enabled;
                });
                var anyEnabled = _.some(roleFunctionInfos, function(ite) {
                    return ite.enabled;
                });
                var isNoEditable = _.every(roleFunctionInfos, function(ite) {
                    return ite.isEditable === false;
                });
                __p += " ";
                if (item.isHaveFieldPrivilege) {
                    __p += " ";
                    if (item.descApiName === "SalesOrderObj") {
                        __p += ' <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="SalesOrderObj" data-name="' + ((__t = $t("crm.销售订单")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置销售订单字段权限")) == null ? "" : __t) + '</span> <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="SalesOrderProductObj" data-name="' + ((__t = $t("crm.订单产品")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置订单产品字段权限")) == null ? "" : __t) + "</span> ";
                    } else if (item.descApiName === "ReturnedGoodsInvoiceObj") {
                        __p += ' <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="ReturnedGoodsInvoiceObj" data-name="' + ((__t = $t("crm.退货单")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置退货单字段权限")) == null ? "" : __t) + '</span> <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="ReturnedGoodsInvoiceProductObj" data-name="' + ((__t = $t("crm.退货单产品")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置退货单产品字段权限")) == null ? "" : __t) + "</span> ";
                    } else if (item.descApiName === "Inventory") {
                        __p += " ";
                    } else {
                        __p += ' <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="' + ((__t = item.descApiName) == null ? "" : __t) + '" data-name="' + ((__t = item.descApiDisplayName) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("设置字段权限")) == null ? "" : __t) + "</span> ";
                    }
                    __p += " ";
                } else {
                    __p += ' <span class="j-set set-btn" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + "></span> ";
                }
                __p += ' </h3> <ul class="mn-checkbox-box b-g-clear ' + ((__t = item.descApiName) == null ? "" : __t) + '"> ';
                if (item.descApiName !== "DuplicateCheckObj") {
                    __p += ' <li class="checkbox-item"> <span class="mn-checkbox-item j-check-all ';
                    if (!item.isEditable || isNoEditable) {
                        __p += "disabled-selected";
                    }
                    __p += " ";
                    if (allEnabled) {
                        __p += "mn-selected";
                    }
                    __p += '"></span>' + ((__t = $t("全选")) == null ? "" : __t) + "</li> ";
                }
                __p += " ";
                _.each(roleFunctionInfos, function(ite, ind) {
                    __p += ' <li class="checkbox-item"> <span class="mn-checkbox-item ';
                    if (ite.isFiledReadOnlyRequired) {
                        __p += " readonly-item";
                    }
                    __p += " ";
                    if (!ite.isEditable) {
                        __p += " disabled-selected";
                    }
                    __p += " ";
                    if (ite.enabled) {
                        __p += " mn-selected";
                    }
                    __p += " ";
                    if (item.viewListFuncCode === ite.functionNumber) {
                        __p += " look-item";
                    }
                    __p += " ";
                    if (item.isBIObject) {
                        __p += " bi-item";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||BeforeSaleAction") {
                        __p += " edit-presale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||AfterSaleAction") {
                        __p += " edit-aftersale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||ViewAfterSaleAction") {
                        __p += " view-presale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||ViewBeforeSaleAction") {
                        __p += " view-aftersale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "ContactObj||ImportFromAddressBook") {
                        __p += " import-contacts";
                    }
                    __p += " ";
                    if (ite.functionNumber === "PriceBookObj||Abolish") {
                        __p += " pricebook-abolish";
                    }
                    __p += " ";
                    if (ite.functionNumber === "PriceBookObj||Delete") {
                        __p += " pricebook-delete";
                    }
                    __p += " ";
                    if (ite.isIntelligentForm) {
                        __p += " smartform-item";
                    }
                    __p += " ";
                    if (ite.isClone) {
                        __p += " clone-item";
                    }
                    __p += " ";
                    if (ite.isAdd) {
                        __p += " add-item";
                    }
                    __p += '" data-functionnumber=' + ((__t = ite.functionNumber) == null ? "" : __t) + "></span> " + ((__t = ite.displayName) == null ? "" : __t) + " </li> ";
                });
                __p += " </ul> <!-- </script> --> </div> ";
            });
        }
        return __p;
    };
});
define("crm-setting/rolemanageenterpriseinterconnect/permissionsset/roleprivilege/index",["../fieldprivilege/index","../fieldprivilege/index-html","./index-html","./link","./lazyload-any"],function(e,i,l){var s=e("../fieldprivilege/index"),d=e("../fieldprivilege/index-html"),t=e("./index-html"),n=e("./link"),o=e("./lazyload-any"),a="mn-selected";l.exports=Backbone.View.extend({initialize:function(e){this.roleCode=e.roleCode,this.$el.html(t({list:e.data})),this.link=new n(e.linkData),this.link.init(e.data)},events:{"click .mn-checkbox-item":"onCheck","click .j-set":"onSet","click .checkbox-head-toggle":"onClickArrow"},onClickArrow:function(e){var e=$(e.currentTarget),i=e.next();"none"==i.css("display")?e.addClass("current"):e.removeClass("current"),i.slideToggle()},onCheck:function(e){var i=$(e.currentTarget),l=i.closest(".mn-checkbox-box"),t=null;return i.hasClass("disabled-selected")?(-1!=["AccountAddrObj||Add","AccountAddrObj||Edit"].indexOf(i.data("functionnumber"))&&CRM.util.remind(3,$t("该权限受客户的【编辑】权限配置影响")),e.stopPropagation()):(this.trigger("check"),i.toggleClass(a),i.hasClass(a)?i.hasClass("j-check-all")?((t=$(".mn-checkbox-item",l).not(".disabled-selected")).addClass(a),this.link.update(this.$el,t)):this.link.update(this.$el,i):i.hasClass("j-check-all")||i.hasClass("look-item")?this.link.unCheckedAll(this.$el,i):this.link.update(this.$el,i),this.link.updateAllStatus(l),this.link.updateSetDisabledStatus(l),this.link.updateLookStatus(l),e.stopPropagation()),!1},onSet:function(e){e.stopPropagation();var i,l,t=this,e=$(e.currentTarget),n=e.data();e.hasClass("disabled-btn")||(i=t.roleCode===CRM.config.MANAGER_ROLE_CODE,l=_.some(e.parent().next().find(".readonly-item"),function(e){return $(e).hasClass(a)}),t.fieldprivilege&&(t.fieldprivilege.destroy(),t.fieldprivilege=null),t.fieldprivilege=new s({title:$t("设置")+n.name+$t("字段权限"),showBtns:!i,className:"crm-s-rolemanage"}),t.fieldprivilege.on("submit",function(e){CRM.util.FHHApi({url:"/EM1HPAASUdobj/roleApi/updateRoleObjectFieldPrivilege",data:{descApiName:n.objectid,roleCode:t.roleCode,fieldPermission:e},success:function(e){0==e.Result.StatusCode&&(CRM.util.remind(1,$t("操作成功")),t.fieldprivilege.destroy())}},{submitSelector:t.fieldprivilege.element.find(".b-g-btn")})}),t.fieldprivilege.show(),CRM.util.FHHApi({url:"/EM1HPAASUdobj/roleApi/getRoleObjectFieldPrivilege",data:{roleCode:t.roleCode,descApiName:n.objectid},success:function(e){"0"==e.Result.StatusCode?(t.fieldprivilege.setContent(d({roleCode:t.roleCode,fieldInfoList:e.Value,disabled:i,readonly:l})),t.fieldprivilege.resizedialog()):CRM.util.remind(3,$t("操作失败"))}},{errorAlertModel:1}))},initLazyload:function(){new o(this.$(".lazyload-item"))},destroy:function(){this.$el.off(),this.fieldprivilege&&(this.fieldprivilege.destroy(),this.fieldprivilege=null),this.link=null}})});
define("crm-setting/rolemanageenterpriseinterconnect/permissionsset/roleprivilege/lazyload-any",[],function(t,n,e){function i(){var t,n,e,i,a,r=h(this);r.is(":visible")&&(n=(t=r)[0].getBoundingClientRect(),t=-t.data(x).threshold,i=v-(e=t),a=p-t,n.top>=e&&n.top<=i||n.bottom>=e&&n.bottom<=i)&&(n.left>=t&&n.left<=a||n.right>=t&&n.right<=a)&&r.trigger(I)}function a(){v=u.innerHeight||f.documentElement.clientHeight,p=u.innerWidth||f.documentElement.clientWidth,r()}function r(){H=H.filter(S),(1==this.nodeType?h(this).find(S):H).each(i)}function o(){var t=h(this),n=t.data(x),e=t.data("lazyload"),i=(e||(i=t.children().filter('script[type="text/lazyload"]').get(0),e=h(i).html()),e||(e=(i=t.contents().filter(function(){return 8===this.nodeType}).get(0))&&h.trim(i.data)),k.html(e).contents());t.replaceWith(i),h.isFunction(n.load)&&n.load.call(i,i)}function d(){var t=h(this);(t=>{var n;return!(t.data(E)||"scroll"!=(n=t.css("overflow"))&&"auto"!=n||(t.data(E,1),t.bind("scroll",r),0))})(t)|(t=>{if(!t.data(W)){var n=t.css("display");if("none"==n)return t.data(W,1),t._bindShow(r),!0}})(t)&&(t.data(j)||(t.data(j,1),t.bind(I,s)))}function s(){var t=h(this);0===t.find(S).length&&(t.removeData(E).removeData(W).removeData(j),t.unbind("scroll",r).unbind(I,s)._unbindShow(r))}function l(){var t=h(this),n="none"!=t.css("display");t.data(m)!=n&&(t.data(m,n),n)&&t.trigger(y)}function c(){(z=z.filter(b)).each(l),0===z.length&&(g=clearInterval(g))}var h,u,f,v,p,g,y,m,b,w,z,x,I,D,S,E,W,j,k,B,H;function T(t){this.$el=t,this.init()}h=jQuery,u=window,f=document,S=":"+(D=(x="jquery-lazyload-any")+"-"+(I="appear")),E=x+"-scroller",W=x+"-display",j=x+"-watch",k=h("<div/>"),B=!1,H=h(),h.expr[":"][D]=function(t){return void 0!==h(t).data(D)},h.fn.lazyload=function(t){var n={threshold:0,trigger:I},t=(h.extend(n,t),n.trigger.split(" "));return this.data(D,-1!=h.inArray(I,t)).data(x,n),this.bind(n.trigger,o),this.each(i),this.parents().each(d),this.each(function(){H=H.add(this)}),B||(B=!0,a(),h(f).ready(function(){h(u).bind("resize",a).bind("scroll",r)})),this},h.lazyload={check:r,refresh:function(t){(void 0===t?H:h(t)).each(function(){var t=h(this);t.is(S)&&t.parents().each(d)})},show:o},b=":"+(m=x+"-"+(y="show")),w=50,z=h(),h.expr[":"][m]=function(t){return void 0!==h(t).data(m)},h.fn._bindShow=function(t){this.bind(y,t),this.data(m,"none"!=this.css("display")),z=z.add(this),w&&!g&&(g=setInterval(c,w))},h.fn._unbindShow=function(t){this.unbind(y,t),this.removeData(m)},h.lazyload.setInterval=function(t){t==w||!h.isNumeric(t)||t<0||(w=t,g=clearInterval(g),0<w&&(g=setInterval(c,w)))},T.prototype=_.extend({constructor:T,init:function(){this.$el.lazyload({load:function(t){var n=t.not("text");n.fadeOut(0,function(){n.fadeIn(0)})}})}},Backbone.Events),e.exports=T});
define("crm-setting/rolemanageenterpriseinterconnect/permissionsset/roleprivilege/link",[],function(e,t,a){var s="mn-selected";function n(e){this.global=_.extend({},e.allPositiveLinkage,this.reverseLinkData(e.allReverseLinkage)),this.data=_.extend({},e.specialPositive,this.reverseLinkData(e.specialReverse))}n.prototype.reverseLinkData=function(e){var a={};return _.each(e,function(e,t){e=_.map(e,function(e){return"!"+e}),a[t="!"+t]=e}),a},n.prototype.getObjectName=function(e){return e.closest(".mn-checkbox-box").prev().find(".j-set").data("functionnumber")},n.prototype.unCheckedAll=function(e,t){t.closest(".mn-checkbox-box").find(".mn-checkbox-item").removeClass(s);var t=this.getObjectName(t),a=this.data["!"+t];if(a)for(var n=0;n<a.length;n++){var i=a[n],i=(/^\!/.test(i)&&(i=i.replace("!","")),e.find('span[data-functionnumber="'+i+'"].mn-checkbox-item'));this.unCheckedAll(e,i)}},n.prototype.update=function(e,t){for(var a=0;a<t.length;a++){var n=t.eq(a),i=n.data("functionnumber"),n=n.hasClass(s)?"":"!";this.data[n+i]&&this.active(e,this.data[n+i])}},n.prototype.active=function(e,t){for(var a=0;a<t.length;a++){var n=t[a],i=/^\!/.test(n),n=(i&&(n=n.replace("!","")),e.find('span[data-functionnumber="'+n+'"]')),i=(i?n.removeClass(s):n.addClass(s),n.closest(".mn-checkbox-box"));this.updateAllStatus(i),this.updateSetDisabledStatus(i),this.updateLookStatus(i),this.update(e,n)}},n.prototype.init=function(e){for(var t=0;t<e.length;t++){var a,n=e[t].roleFunctionInfos[0].functionNumber.replace(/\*||/,"");for(a in this.global){var i=this.replace(n,a);this.data[i]?this.data[i]=this.data[i].concat(this.replace(n,this.global[a])):this.data[i]=this.replace(n,this.global[a])}}},n.prototype.replace=function(t,e){return _.isArray(e)?e.map(function(e){return e.replace("{obj}",t)}):_.isString(e)?e.replace("{obj}",t):void 0},n.prototype.updateAllStatus=function(e){var t=e.find("span:not(.j-check-all)"),a=t.filter("span:not(.disabled-selected)").length;t.filter("span:not(.disabled-selected)").filter(".mn-selected").length==a?e.find(".j-check-all").addClass(s):e.find(".j-check-all").removeClass(s)},n.prototype.updateSetDisabledStatus=function(e){var t=e.prev().find(".j-set");_.some($(".mn-checkbox-item",e),function(e){return $(e).hasClass(s)})?t.removeClass("disabled-btn"):t.addClass("disabled-btn")},n.prototype.updateLookStatus=function(e){var t=e.find(".look-item");e.find("span:not(.look-item)").filter(".mn-selected").length&&t.addClass(s)},a.exports=n});
define("crm-setting/rolemanageenterpriseinterconnect/rolemanageenterpriseinterconnect",["./permissionsset/fieldprivilege/index","./permissionsset/fieldprivilege/index-html","./permissionsset/roleprivilege/index"],function(e,i,r){var s=e("./permissionsset/fieldprivilege/index"),n=e("./permissionsset/fieldprivilege/index-html"),e=e("./permissionsset/roleprivilege/index"),l={};l.FieldPrivilege=s,l.RolePrivilege=e,l.FieldPrivilegeTpl=n,r.exports=l});