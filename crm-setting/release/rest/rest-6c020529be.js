define("crm-setting/rest/rest",["crm-modules/common/util","./template/tpl-html"],function(e,t,a){function r(e){this.wrapper=e,this.id=n.uuid(),this.init()}var n=e("crm-modules/common/util"),i=e("./template/tpl-html"),e=(r.prototype={init:function(){var e=this,t=n.getShareGroupQxSessionId();t?e.create(t):FS.tpl.event.one("shareGroupAdd",e.create,e)},create:function(a){a&&(this.wrapper.each(function(e,t){$('<div class="share-group"><em class="crm-ico-bee"></em>'+$t("在线客服")+"</div>").appendTo($(t)).on("click",function(e){return FS.tpl.event.trigger("qxOpenChat",a,"session"),e&&e.stopPropagation(),!1})}),this.rendered=!0)},destroy:function(){var e=this;e.rendered||FS.tpl.event.off("shareGroupAdd",e.create,e)}},Backbone.View.extend({initialize:function(e){var a=this;a.setElement(e.wrapper),a.getCrmResetStatus(1,function(t){CRM.util.getPermissionByApiName(["NewOpportunityObj"]).done(function(){var e=1==CRM.get("permission").NewOpportunityObj;a.$el.html(i({data:t,newoppo:e,fxLabelRemoval:FS.util.getUserAttribute("fxLabelRemoval"),companyName:FS.contacts.getCurrentEmployee()&&FS.contacts.getCurrentEmployee().companyName})),a.shareGroup=new r($(".share-group-box",a.$el)),FS.util.getUserAttribute("fxLabelRemoval")&&$(".share-group-box[data-title='crmrest']").hide()})})},events:{"click .crm-btn-danger":"dialogToSubmitHandle"},getCrmResetStatus:function(t,a){var r=this;r.ajax=n.FHHApi({url:"/EM2HORG/Management/Enterprise/GetApplicationInitialStatus",data:{},success:function(e){0==e.Result.StatusCode?1==t?a&&a(e.Value):0!=e.Value.isOpen?r.timer=setTimeout(function(){r.getCrmResetStatus(t,a)},3e3):a&&a({isOpen:1}):a&&a({isOpen:0})},complete:function(){r.ajax=null}},{errorAlertMode:1})},dialogToSubmitHandle:function(e){var t,a=this;$(e.currentTarget).hasClass("disabled")||(t=n.confirm($t("确定开始执行CRM初始化吗"),$t("CRM数据初始化"),function(){a.resetCrm(t)})).on("dialogCancel",function(){a.ajax&&a.ajax.abort()})},resetCrm:function(t){var a=this;$(".confirm-message",t.element).html($t("crm.正在进行CRM初始化")),n.FHHApi({url:"/EM1HORG/Management/Enterprise/ResetApplicationInitialData",data:{},success:function(e){0==e.Result.StatusCode?a.getCrmResetStatus(2,function(){t.hide(),n.remind(2,$t("CRM初始化成功")),$(".b-g-btn-del",a.$el).addClass("disabled")}):n.alert(e.Result.FailureMessage)}},{errorAlertMode:1,submitSelector:$(".b-g-btn")})},destroy:function(){var e=this;clearTimeout(e.timer),e.ajax&&e.ajax.abort(),e.shareGroup.destroy()}}));a.exports=e});
define("crm-setting/rest/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("CRM初始化")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="wrap"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("crm.CRM初始化很危险")) == null ? "" : __t) + '</p> <p style="margin-left:10px;">' + ((__t = newoppo ? $t("crm.预设对象新商机和自定义对象的所有数据全部删除") : $t("crm.预设对象和自定义对象的所有数据全部删除")) == null ? "" : __t) + '</p> <p style="margin-left:10px;">' + ((__t = $t("crm.数据触发的流程数据")) == null ? "" : __t) + "</p> <br/> <p>" + ((__t = $t("crm.将于后续开放的初始化内容")) == null ? "" : __t) + '</p> <p style="margin-left:10px;">' + ((__t = $t("crm.配置信息")) == null ? "" : __t) + '</p> <p style="margin-left:10px;">' + ((__t = $t("crm.销售记录以及角色权限数据")) == null ? "" : __t) + '</p> <div class="share-group-box" data-title="crmrest"></div> </div> <div class="item"> <h3>' + ((__t = $t("适用场景：")) == null ? "" : __t) + "</h3> <p>" + ((__t = fxLabelRemoval ? $t("name如何管理CRM信息", {
                name: companyName
            }, "试用纷享一段时间后想正式管理CRM信息，需要清除测试数据。") : $t("crm.如何管理CRM信息")) == null ? "" : __t) + '</p> </div> <div class="item"> <h3>' + ((__t = $t("操作步骤：")) == null ? "" : __t) + "</h3> <p>" + ((__t = fxLabelRemoval ? $t("联系name服务中心", {
                name: companyName
            }, "联系纷享服务中心") : $t("联系纷享服务中心")) == null ? "" : __t) + "<strong> 400-1122-778</strong>，" + ((__t = $t("crm.审核后可以开启高危操作权限")) == null ? "" : __t) + '</p> </div> <div class="btn-box"> <div class="crm-btn crm-btn-danger ';
            if (data.isOpen == 0) {
                __p += "crm-btn-disabled";
            }
            __p += '">' + ((__t = $t("CRM数据初始化")) == null ? "" : __t) + "</div> </div> </div> </div>";
        }
        return __p;
    };
});