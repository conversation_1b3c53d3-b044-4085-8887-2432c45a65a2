function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var r,i=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,r)),i}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/workflowhistory/detail",["./template/detail-html","./template/detailData-html","crm-modules/common/slide/slide"],function(a,e,t){var n=CRM.util,l=a("./template/detail-html"),r=a("./template/detailData-html"),o=a("crm-modules/common/slide/slide"),i=o.extend({events:{"click .d-g-btns .h-btn":"_hide"},options:{showMask:!1,width:800,zIndex:500,className:"crm-d-detail crm-nd-detail workhistory-crm-detail loading",entry:"crm"},show:function(e,t,r,i){var o=this;o.$el.html(l({objname:e})),a.async("paas-paasui/ui",function(){o.fetchFlowData(t,r).then(function(){o.fetchAfterActionImplementation(i).then(function(){CRM.util.fetchCountryAreaOptions().then(function(e){o.areaOptions=e,PaasUI.utils.fetchDescribesByFilter([o.data.entityId]).then(function(e){o.objAndRefObjList=e[o.data.entityId],o.render()})})})})})},fetchFlowData:function(e,r){var i=this;return new Promise(function(t){n.FHHApi({url:"/EM1HPROCESS/WorkflowAction/GetDetailByWorkflowIdAndSourceWorkflowId",data:{workflowId:r,sourceWorkflowId:e},success:function(e){0===e.Result.StatusCode&&(o.prototype.show.apply(i),i.data=e.Value.workflow,e=i.data.workflow.activities,_.each(e,function(e){e.itemList&&e.itemList.length&&_.each(e.itemList,function(e,t){"updates"==e.taskType&&(e.updateFieldJson=JSON.parse(e.updateFieldJson))})}),t())}},{errorAlertModel:1})})},fetchAfterActionImplementation:function(e){var a=this;return new Promise(function(o){n.FHHApi({url:"/EM1HPROCESS/InstanceAction/Detail",data:{workflowInstanceId:e},success:function(e){var t,r,i;0===e.Result.StatusCode&&(a.triggerType=e.Value.triggerType,a.data.triggerType=e.Value.triggerType,t=e.Value.executionList,r=e.Value.exclusiveGateways,a.variable=e.Value.variable,t&&!_.isEmpty(t)&&(e=a.data.workflow.activities,_.each(e,function(r){var i;r.itemList&&r.itemList.length&&(i=t[r.id])&&(_.each(r.itemList,function(e,t){t=i[t];t&&t.executionState&&("success"==t.executionState?t.executionStateLabel=$t("成功"):"error"==t.executionState&&(t.executionStateLabel=$t("异常"),t.isError=!0,r.hasActionResultError=!0),e.actionResult=t)}),r.hasActionResultError||_.every(r.itemList,function(e){return e.actionResult})&&(r.hasActionResultError=!1))})),r&&!_.isEmpty(r)&&(i=a.data.workflow.transitions,a.data.workflow.activities.forEach(function(t){var e=r[t.id];e&&(i.find(function(e){return e.fromId===t.id&&e.condition}).conditionData=e)})),o())},complete:function(){o()}},{errorAlertModel:1})})},render:function(){var t=this;t.initTrigger(),a.async("paas-workprocess/flowdetail",function(e){t.cavas=new e({el:t.$el.find(".svg"),data:t.data,useToolbar:!0,showDetailData:!0}),t.cavas.show(),t.$el.removeClass("loading")})},initTrigger:function(){var n=this;n.propertyView&&n.propertyView.destroy(),n.data.rule&&n.data.rule.conditions&&n.data.rule.conditions.length?window.PaasUI.getComponent("FilterAnalyze").then(function(e){var a;n.propertyView=new e({model:new Backbone.Model({fromApp:"workprocess",fromModule:"filter",originalConditions:n.data.rule,apiName:n.data.entityId,forDetail:!0,triggerNames:n.data.triggerNames})}),n.$el.find(".workprocess-trigger-list").html(n.propertyView.$el),n.variable&&!_.isEmpty(n.variable)&&(a=[],e=_objectSpread(_objectSpread({},n.variable.before),n.variable.after),Object.keys(e).forEach(function(e){var t,r=PaasUI.utils.replaceRichTextV2Async({content:"${".concat(n.data.entityId,".").concat(e,"}"),useHtmlVarWrapper:!1,objAndRefObjList:n.objAndRefObjList}).content,i=PaasUI.utils.findFieldDescribeByVar({variable:"${".concat(n.data.entityId,".").concat(e,"}"),objAndRefObjList:n.objAndRefObjList}),o=[];o.push(((t={}).value=r.replace(/\$\{/g,"").replace(/\}/g,""),t.title=t.value,t)),"2"===n.triggerType&&o.push(((r={}).value=PaasUI.utils.formatFieldValue({value:n.variable.before[e],fieldDescribe:i,areaOptions:n.areaOptions,notShowAttach:!0}),r.title=r.value&&r.value.replace&&r.value.replace(/<\/?[^>]*>/g,""),r)),o.push(((t={}).value=PaasUI.utils.formatFieldValue({value:n.variable.after[e],fieldDescribe:i,areaOptions:n.areaOptions,notShowAttach:!0}),t.title=t.value&&t.value.replace&&t.value.replace(/<\/?[^>]*>/g,""),t)),a.push(o)}),n.$el.find(".workprocess-detail-data").html(r({data:{isUpdate:"2"===n.triggerType,values:a}})))}):n.$el.find(".flow-detail").hide()},_hide:function(){this.hide()},destroy:function(){o.prototype.destroy.call(this)}});t.exports=i});
define("crm-setting/workflowhistory/template/detail-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-d-layout"> <div class="header d-top"> <div class="crm-d-comp-tit"> <span class="d-obj-icon"></span> <span class="obj-name">' + ((__t = $t("工作流执行日志")) == null ? "" : __t) + '</span> <div class="tit"> <div title="' + __e(objname) + '">' + __e(objname) + '</div> </div> </div> <div class="d-g-btns"> <span data-action="hide" class="h-btn" title="' + ((__t = $t("关闭")) == null ? "" : __t) + '"></span> </div> </div> <div class="b-content"> <div class="flow-detail" data-name="flow-detail"> <div class="sec-tit"> <h3><span class="icon icon-info"></span>' + ((__t = $t("工作流触发条件")) == null ? "" : __t) + '</h3> </div> <div class="workprocess-trigger-list"></div> <div class="workprocess-detail-data"></div> </div> <div class="flow-map" data-name="flow-map"> <div class="sec-tit"> <h4><span class="new-icon"></span>' + ((__t = $t("流程配置")) == null ? "" : __t) + '</h4> </div> <article class="field-items b-g-clear svg"></article> </div> </div> <div class="detail-loading"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/workflowhistory/template/detailData-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<table class="detail-table"> <tr> <th>' + ((__t = $t("变量名称")) == null ? "" : __t) + "</th> ";
            if (data.isUpdate) {
                __p += " <th>" + ((__t = $t("变更前")) == null ? "" : __t) + "</th> <th>" + ((__t = $t("变更后")) == null ? "" : __t) + "</th> ";
            } else {
                __p += " <th>" + ((__t = $t("变量值")) == null ? "" : __t) + "</th> ";
            }
            __p += " </tr> ";
            _.each(data.values, function(e) {
                __p += " <tr> ";
                _.each(e, function(f) {
                    __p += " ";
                    var title = f.title;
                    __p += ' <td title="' + ((__t = title) == null ? "" : __t) + '">' + ((__t = f.value) == null ? "" : __t) + "</td> ";
                });
                __p += " </tr> ";
            });
            __p += " </table>";
        }
        return __p;
    };
});
define("crm-setting/workflowhistory/template/filter-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="workflow-history"> <input type="text" name="account" style="display:none;" autocomplete="off"/> <input type="password" name="password" style="display:none;" autocomplete="off"/> <div class="workflow-history-filter"> <div class="api-name-box"></div> <div class="data-id-box"></div> <div class="time"> <span class="title required">' + ((__t = $t("触发时间范围")) == null ? "" : __t) + '：</span> <span class="time-box"></span> </div> <div class="trigger-person"> <span class="title">' + ((__t = $t("工作流发起人")) == null ? "" : __t) + '：</span> <span class="trigger-person-box"></span> </div> <div class="history-search-btn">' + ((__t = $t("查询")) == null ? "" : __t) + '</div> </div> <div class="workflow-history-tip"> <span class="title">' + ((__t = $t("说明")) == null ? "" : __t) + "：</span> <span>" + ((__t = $t("由于数据量较大，现工作流执行日志可存放两个月数据")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/workflowhistory/workflowhistory",[],function(e,t,o){var n=CRM.util,i=Backbone.View.extend({events:{"click .history-search-btn":"onSearch"},initialize:function(e){this.setElement(e.wrapper),n.uploadLog("paasflow","workflow",{operationId:"workflowhistory",eventType:"pv"})},startInstanceId:"",endInstanceId:"",pageNumber:1,render:function(){var t=this;e.async("paas-paasui/lib",function(e){e.getModule("flowLog").then(function(e){t.log=e.default.render({el:t.$el[0]})})})},destroy:function(){this.log.destroy()}});o.exports=i});