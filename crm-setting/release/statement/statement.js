define("crm-setting/statement/statement",["./template/tpl-html","crm-modules/common/util"],function(e,t,n){var i=e("./template/tpl-html"),o=e("crm-modules/common/util"),e=Backbone.View.extend({events:{"click .mn-radio-item":"onChoose","click .switch-sec":"switchOn","click .crm-statement_save button":"save"},initialize:function(e){this.options=_.extend({opened:!1,receivableBy:"SalesOrderObj",needCrmNotice:!1},e),this.setElement(e.wrapper)},render:function(){var e=this;e.getConfig(function(){e.$el.html(i(e.options))})},getConfig:function(t){var n=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/statement/service/get_statement_config",success:function(e){0===e.Result.StatusCode&&(n.options.opened=e.Value.statementOpened,n.options.receivableBy=e.Value.receivableBy||n.options.receivableBy,n.options.needCrmNotice=e.Value.needCrmNotice||n.options.needCrmNotice,t())}})},onChoose:function(e){var t=$(e.currentTarget).data("type"),e=$(e.currentTarget).data("value");"receivableBy"===t?this.options.receivableBy=e:"needCrmNotice"===t&&(this.options.needCrmNotice=Boolean(e))},save:function(){var e={receivableBy:this.options.receivableBy,needCrmNotice:this.options.needCrmNotice};o.FHHApi({url:"/EM1HNCRM/API/v1/object/statement/service/update_statement_config",data:e,success:function(e){0===e.Result.StatusCode&&o.remind(1,$t("设置成功"))}})},switchOn:function(e){var t=$(e.currentTarget);t.hasClass("on")||o.confirm($t("确认要启用对账单吗启用后将无法停用"),$t("启用对账单"),function(){this.destroy(),o.FHHApi({url:"/EM1HNCRM/API/v1/object/statement/service/enable_statement",data:{},success:function(e){0===e.Result.StatusCode&&o.remind(1,$t("启用成功"),function(){t.closest(".on-off").addClass("first-time-on").end().addClass("on"),$(".crm-statement_switches").addClass("opened")},1500)}})})}});n.exports=e});
define("crm-setting/statement/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("对账单设置")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll crm-statement"> <div class="crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("对账单是基于一定周期统计客户代理商的交易往来的数据包括订单退货单回款退款等数据。")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("当开启互联后代理商经销商可在线对账确认")) == null ? "" : __t) + '</p> </div> </div> <div class="crm-statement_content"> <div class="on-off"> <div class="crm-statement_title">' + ((__t = $t("启用对账单")) == null ? "" : __t) + '</div> <div class="switch-sec' + ((__t = opened ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="crm-statement_switches ' + ((__t = opened ? "opened" : "") == null ? "" : __t) + '"> <p class="mn-radio-box"> <span class="crm-statement_name"> <span class="crm-statement_label">' + ((__t = $t("应收确认")) == null ? "" : __t) + '</span> <span class="crm-ico-qus" title="' + ((__t = $t("开启发货单后，才能按发货单确认应收")) == null ? "" : __t) + '"></span> </span> <span class="crm-statement_radio"> <span class="mn-radio-item ' + ((__t = receivableBy === "SalesOrderObj" ? "mn-selected" : "") == null ? "" : __t) + '" data-value="SalesOrderObj" data-type="receivableBy"> </span> <span class="crm-statement_label">' + ((__t = $t("按订单")) == null ? "" : __t) + '</span> </span> <span class="crm-statement_radio"> <span class="mn-radio-item ' + ((__t = receivableBy === "DeliveryNoteObj" ? "mn-selected" : "") == null ? "" : __t) + '" data-value="DeliveryNoteObj" data-type="receivableBy"> </span> <span class="crm-statement_label">' + ((__t = $t("按发货单")) == null ? "" : __t) + '</span> </span> </p> <p class="mn-radio-box"> <span class="crm-statement_name"> <span class="crm-statement_label">' + ((__t = $t("CRM通知")) == null ? "" : __t) + '</span> <span class="crm-ico-qus" title="' + ((__t = $t("对账单新建后系统将在后台获取数据生成对账单明细当等待较长时间时可开启CRM通知。待明细创建完成后系统将会发送通知给创建人")) == null ? "" : __t) + '"></span> </span> <span class="crm-statement_radio"> <span class="mn-radio-item ' + ((__t = needCrmNotice ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1" data-type="needCrmNotice"></span> <span class="crm-statement_label">' + ((__t = $t("需要")) == null ? "" : __t) + '</span> </span> <span class="crm-statement_radio"> <span class="mn-radio-item ' + ((__t = needCrmNotice ? "" : "mn-selected") == null ? "" : __t) + '" data-value="0" data-type="needCrmNotice"></span> <span class="crm-statement_label">' + ((__t = $t("不需要")) == null ? "" : __t) + '</span> </span> </p> <div class="crm-statement_save"> <button>' + ((__t = $t("保存")) == null ? "" : __t) + "</button> </div> </div> </div> </div>";
        }
        return __p;
    };
});