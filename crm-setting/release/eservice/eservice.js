define("crm-setting/eservice/eservice",[],function(s,e,n){return Backbone.View.extend({initialize:function(s){this.setElement(s.wrapper)},render:function(){this.initEserviceConfigView()},initEserviceConfigView:function(){var e=this;s.async("app-workorder/app.js",function(){s.async(["app-common-assets/style/common.css","app-standalone-assets/style/_ui.css","app-standalone-assets/style/all.css","app-workorder-assets/style/all.css"],function(){s.async("app-workorder/tpls/systemsettings/systemsettings-vue",function(s){e.instance=new s({el:e.$el[0],propsData:{isPaasMenu:!0,fromManage:!0}})})})})},destroy:function(){this.instance&&this.instance.$destroy&&this.instance.$destroy()}})});