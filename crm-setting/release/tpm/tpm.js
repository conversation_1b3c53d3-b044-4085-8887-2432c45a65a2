define("crm-setting/tpm/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("营销活动管理")) == null ? "" : __t) + '<a class="crm-doclink" href="javascript:;"></a></span></h2> <span class="tpm-VLD"></span> </div> <div class="crm-module-con crm-scroll"> <div class="crm-tab tpm-tab"> <span class="item cur" data-tab="0">' + ((__t = $t("营销活动管理")) == null ? "" : __t) + '</span> <span class="item" data-tab="1">' + ((__t = $t("费用预算表设置")) == null ? "" : __t) + '</span> </div> <div class="tab-con tpm-tab-con"> <div class="mn-radio-box crm-p20"> ';
            if (obj.license_status === "expired") {
                __p += ' <div class="tpm-expired-box"> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/tpm-expired-icon-6a5027a632.png") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '"> <h3>' + ((__t = $t("营销活动管理功能过期")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("功能过期后，之前产生的数据仍可查看")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("如需续费请联系您的企业营销顾问或拨打客服电话phone进行咨询", {
                    phone: "<span>400-1122-778</span>"
                })) == null ? "" : __t) + "</p> </div> ";
            }
            __p += ' <div class="tpm-container"> <div class="tpm-introduction"> <h2>' + ((__t = $t("功能介绍")) == null ? "" : __t) + "：</h2> <p>" + ((__t = $t("精细化管理企业营销活动，根据活动落地的执行情况合理核销活动经费")) == null ? "" : __t) + '</p> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/tpm-introduction-img-e135e01e02.png") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + '：</h3> <ul> <li class="level-1">' + ((__t = $t("营销活动管理说明1")) == null ? "" : __t) + " <ul> <li>" + ((__t = $t("营销活动管理说明1.1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("营销活动管理说明1.2")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("营销活动管理说明1.3")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("营销活动管理说明1.4")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("营销活动管理说明1.5")) == null ? "" : __t) + '</li> </ul> </li> <li class="level-1">' + ((__t = $t("营销活动管理说明2")) == null ? "" : __t) + " <ul> <li>" + ((__t = $t("营销活动管理说明2.1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("营销活动管理说明2.2")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("营销活动管理说明2.3")) == null ? "" : __t) + '</li> </ul> </li> <li class="level-1">' + ((__t = $t("营销活动管理说明3")) == null ? "" : __t) + '</li> </ul> </div> </div> <div id="tpm-switch-box"></div> </div> ';
            if (obj.license_status !== "expired") {
                __p += ' <div class="tpm-budget"> <div id="tpm-budget-switch-box"></div> </div> ';
            }
            __p += ' </div> </div> </div> <div class="tpm-marketing-img"> <div class="shadow-box"> ';
            if (!obj.license_status) {
                __p += ' <p class="tpm-marketint-tip">' + ((__t = $t("贵公司未购买该模块，请联系您的企业销售人员购买，或拨打客服电话phone进行咨询", {
                    phone: "<span>400-1122-778</span>"
                })) == null ? "" : __t) + "</p> ";
            }
            __p += " ";
            if (obj.language === "zh-CN" || obj.language === "zh-TW") {
                __p += ' <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> ';
            } else if (obj.language === "ja-JP") {
                __p += ' <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> ';
            } else {
                __p += ' <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> <img src="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" srcset="' + ((__t = CRMSETTING.ASSETS_PATH + "/images/<EMAIL>") == null ? "" : __t) + '" alt=""> ';
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/tpm/tpm",["./tpl-html"],function(t,e,i){var a=CRM.util;return FS.util.getUserAttribute("isTPMVersion1")?Backbone.View.extend({template:t("./tpl-html"),events:{"click .crm-doclink":"onClickToggleMarketing","click .tpm-tab .item":"onSwitchTab"},initialize:function(t){this.setElement(t.wrapper),this.getLicense()},onClickToggleMarketing:function(){FS.tpl.navRouter.navigate("#crmmanage/=/module-tpm/image-show",{trigger:!1}),this.toggleMarketing()},onSwitchTab:function(t){var t=t.target,e=+t.dataset.tab;this.$(t).addClass("cur").siblings(".cur").removeClass("cur"),this.$(".tpm-tab-con").removeClass("active-tab".concat(+!e)).addClass("active-tab".concat(e))},renderTpl:function(t){var e=FS.contacts.getCurrentEmployee().language;(t=t||{}).language=e,this.$el.html(this.template(t)),this.$marketing=this.$(".tpm-marketing-img"),"invalid"!==this.license_status&&(this.$marketing.hide().appendTo("body"),this.toggleMarketing())},toggleMarketing:function(){var t;"invalid"!==this.license_status&&"tpm"===(t=FS.util.getTplQueryParams()).module&&("show"===t.image?this.$marketing.show():this.$marketing.hide())},renderSwitch:function(t){this.$switch?this.$switch.value=t:this.$switch=FxUI.create({wrapper:"#tpm-switch-box",template:'\n          <div>\n            <div class="tpm-switch-box">\n              <div class="tpm-row">\n                <span>{{$t(\'启用营销活动管理\')}}</span>\n                <fx-switch size="medium" :disabled="value" :value="value" inactive-color="#D7DBE2" @change="onChange"></fx-switch>\n                <span v-if="isActiving">{{$t(\'正在初始化\')}}...</span>\n                <span v-if="isActiveError && !isActiving" style="color:#FF522A;">{{$t(\'开启失败，请重试\')}}...</span>\n              </div>\n              <span class="tpm-tip">{{$t(\'营销活动管理开启后，不可关闭\')}}</span>\n            </div>\n          </div>',data:function(){return{isActiving:!1,isActiveError:!1,value:t,auditType:"0"}},methods:{getAuditType:function(){var e=this;a.FHHApi({url:"/EM1HFMCGService/config/get",timeout:15e3,data:{key:"TPM_AUDIT_MODE"},success:function(t){0===t.Result.StatusCode&&(e.auditType=t.Value.value)}},{errorAlertModel:1})},onSaveAuditType:function(){var e=this;a.FHHApi({url:"/EM1HFMCGService/config/set",timeout:15e3,data:{key:"TPM_AUDIT_MODE",value:this.auditType},success:function(t){0===t.Result.StatusCode?e.$message({message:$t("保存成功"),type:"success"}):e.$message.error($t("保存失败"))}},{errorAlertModel:1})},onChangeAuditType:function(t){var e=this;this.$confirm([$t("抽检切换为全部检核"),$t("全部检核切换为抽检")][+t]).then(function(t){t.value;e.onSaveAuditType()}).catch(function(){e.auditType=+!+t+""})},onChange:function(t){var e=this;t&&(this.isActiving=!0,a.FHHApi({url:"/EM1HFMCGService/license/active",timeout:15e3,data:{appCode:"FMCG.TPM.2"},success:function(t){t=t.Result;e.isActiving=!1,0!==t.StatusCode?(e.isActiveError=!0,e.value=!1):(e.getAuditType(),e.renderBudgetSwitch(!1,!0))}},{errorAlertModel:1}))}},created:function(){this.value&&this.getAuditType()}})},renderBudgetSwitch:function(t,e){e&&t?this.$budgetSwitch?(this.$budgetSwitch.value=t,this.$budgetSwitch.isTPMActive=e):this.$budgetSwitch=FxUI.create({wrapper:"#tpm-budget-switch-box",template:'\n          <div class="tpm-switch-box">\n            <div class="tpm-row">\n              <span class="switch-label">{{$t(\'开启【费用预算表】对象\')}}</span>\n              <fx-switch size="medium" :disabled="value" v-model="value" inactive-color="#D7DBE2" @change="onChange"></fx-switch>\n              <span v-if="errorMsg" style="color:#FF522A;">{{errorMsg}}</span>\n            </div>\n            <p class="tpm-tip-default">{{$t(\'开启费用预算表对象，对营销活动费用进行合理管控，经销商业务员在申请活动时受相关费用预算表约束\')}}</p>\n            <span class="tpm-tip">{{$t(\'费用预算表对象开启后，不可关闭\')}}</span>\n            <p class="form-title">{{$t(\'规则设置\')}}</p>\n            <div class="budget-start-month">\n              <div class="tpm-row">\n                <span>{{$t(\'选择预算起始月\')}}</span>\n                <div class="tpm-row-content">\n                  <fx-select v-model="month" :options="options"></fx-select>\n                  <p class="tpm-tip-default">{{$t(\'说明：选择起始月，默认为起始月1日至尾月最后一日\')}}</p>\n                </div>\n              </div>\n            </div>\n            <fx-button type="primary" :disabled="!value" @click="onSaveMonth">{{$t(\'保存\')}}</fx-button>\n          </div>',data:function(){return{errorMsg:"",isTPMActive:e,value:t,month:1,options:[1,2,3,4,5,6,7,8,9,10,11,12].map(function(t){return{label:$t("".concat(t,"月")),value:t}})}},methods:{getMonth:function(){var e=this;a.FHHApi({url:"/EM1HFMCGService/config/get",timeout:15e3,data:{key:"TPM_BUDGET_START_MONTH"},success:function(t){0===t.Result.StatusCode&&(e.month=+(t.Value.value||1))}},{errorAlertModel:1})},onSaveMonth:function(){var e=this;a.FHHApi({url:"/EM1HFMCGService/config/set",timeout:15e3,data:{key:"TPM_BUDGET_START_MONTH",value:this.month},success:function(t){t=t.Result;0===t.StatusCode?e.$message({message:$t("保存成功"),type:"success"}):e.$message.error($t("保存失败"))}},{errorAlertModel:1})},onChange:function(t){var e=this;t&&(this.isTPMActive?(this.errorMsg="",a.FHHApi({url:"/EM1HFMCGService/license/active",timeout:15e3,data:{appCode:"FMCG.TPM_BUDGET.2"},success:function(t){0!==t.Result.StatusCode&&(e.errorMsg=$t("开启失败，请重试"),e.value=!1)}},{errorAlertModel:1})):(this.errorMsg=$t("预开启【费用预算表】，请先开启营销活动管理"),this.value=!1))}},created:function(){this.getMonth()}}):this.$('.item[data-tab="1"]').hide()},getLicense:function(){var s=this;a.FHHApi({url:"/EM1HFMCGService/license/get",data:{appCode:"FMCG.TPM.2"},success:function(t){var e=t.Result,t=t.Value;if(0===e.StatusCode){var e=t.externalData,i=e.begin_time,n=e.end_time,e=e.license_status;if("invalid"!==(s.license_status=e))return s.renderTpl({license_status:e}),s.$el.find(".tpm-VLD").text("".concat($t("有效期"),"：").concat(FS.moment(i).format("YYYY-MM-DD")," ").concat($t("至")," ").concat(FS.moment(n).format("YYYY-MM-DD"))),void("expired"!==e&&(s.renderSwitch(!_.isEmpty(t.license)),s.getBudgetLicense(!_.isEmpty(t.license))))}s.renderTpl()}})},getBudgetLicense:function(i){var n=this;i?a.FHHApi({url:"/EM1HFMCGService/license/batch_validate",data:{appCodeList:["FMCG.TPM_BUDGET","FMCG.TPM_BUDGET.2"]},success:function(t){var e=t.Result.StatusCode,t=t.Value.data,e=0===e&&(void 0===t?[]:t).some(function(t){return t.activated});n.renderBudgetSwitch(e,i)}}):this.renderBudgetSwitch(!1,!1)},destroy:function(){this.$marketing.remove(),this.remove()}}):Backbone.View.extend({initialize:function(e){t.async("paas-tpm/sdk",function(t){(0,t.getLicenseView)().then(function(t){t.init(e.wrapper[0])})})}})});