define("crm-setting/batchchangeapprover/batchchangeapprover",[],function(e,n,t){var i=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){var t=this;e.async(["paas-paasui/lib"],function(e){e.getModule("handover").then(function(n){t.inst=new Vue({el:t.$el[0],render:function(e){return e(n.default)}})})})},destroy:function(){this.inst&&this.inst.$destroy()}});t.exports=i});
define("crm-setting/batchchangeapprover/columns",[],function(e,d,t){t.exports={SalesOrderObj:[{data:"TradeCode",width:150,fixed:!0,fixedIndex:2,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.TradeCode)||"--")+'">'+e+"</a>"}},{data:"CustomerName",width:200,fixed:!0,fixedIndex:3}],ReturnedGoodsInvoiceObj:[{data:"ReturnOrderCode",width:150,fixed:!0,fixedIndex:1,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.ReturnOrderCode)||"--")+'">'+e+"</a>"}},{data:"CustomerName",width:200,fixed:!0,fixedIndex:2}],AccountObj:[{width:234,data:"Name",fixed:!0,fixedIndex:3,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.Name)||"--")+'">'+e+"</a>"}},{data:"Address",render:function(e,d,t){return t.Address.split("#%$")[2]||"--"}}],InvoiceApplicationObj:[{data:"TradeBillCode",width:150,fixed:!0,fixedIndex:1,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.TradeBillCode)||"--")+'">'+e+"</a>"}},{data:"CustomerName",width:200,fixed:!0,fixedIndex:2}],ContractObj:[{data:"ContractNo",width:150,fixed:!0,fixedIndex:1,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.ContractNo)||"--")+'">'+e+"</a>"}},{data:"CustomerName",width:200,fixed:!0,fixedIndex:3}],PaymentObj:[{data:"TradePaymentCode",fixed:!0,fixedIndex:1,width:150,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.TradePaymentCode)||"--")+'">'+e+"</a>"}},{data:"CustomerName",width:200,fixed:!0,fixedIndex:2}],RefundObj:[{data:"TradeRefundCode",width:150,fixed:!0,fixedIndex:1,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.TradeRefundCode)||"--")+'">'+e+"</a>"}},{data:"CustomerName",width:200,fixed:!0,fixedIndex:2}],OpportunityObj:[{width:150,data:"Name",fixed:!0,fixedIndex:1,render:function(e,d,t){return'<a href="javascript:;" title="'+(_.escape(t.Name)||"--")+'">'+e+"</a>"}},{width:200,data:"CustomerName",fixedIndex:2,fixed:!0},{data:"AfterSaleActionStageName",render:function(e,d,t){return t.IsBindAfterSale&&!t.IsStartAfterSale?'<span title="'+e+'">'+$t("未开启")+"</span>":'<span title="'+e+'">'+e+"</span>"}}]}});
define("crm-setting/batchchangeapprover/template/changehandler-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<!-- * @Descripttion: * @Author: LiAng * @Date: 2021-07-14 12:39:03 * @LastEditors: LiAng * @LastEditTime: 2021-07-14 12:44:06 --> <div class="batchchangeapprover-box"> <div class="batch-change"> <span class="title object-list-title">' + ((__t = $t("流程关联对象")) == null ? "" : __t) + ':</span> <div class="object-list-content"></div> <span class="title">' + ((__t = $t("变更处理人")) == null ? "" : __t) + ':</span> <div> <div class="select-input-box"> <div class="current-select-input"></div> <div class="icon-mappingto"></div> <div class="target-select-input"></div> </div> <div class="batch-change-total"> ';
            var message = $t("当前处理人是{{name}}的流程有{{num}}条", {
                name: '<span class="current-name">--</span>',
                num: '<span class="current-num">--</span>'
            });
            __p += " <span>" + ((__t = message) == null ? "" : __t) + '</span> </div> </div> </div> <div class="batch-change-btn disable">' + ((__t = $t("批量更换处理人")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});