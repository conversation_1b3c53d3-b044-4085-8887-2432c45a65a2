define("crm-setting/smartforms/detail/datastatistic",["crm-modules/common/util","./template/datastatistic-html","crm-widget/table/table"],function(e,t,a){var i=e("crm-modules/common/util"),r=e("./template/datastatistic-html"),o=e("crm-widget/table/table"),s={text:1,long_text:1,number:2,currency:3,date:10,date_time:4,select:6,select_one:6,record_type:6,select_many:7,checkbox:7,image:9,signature:9,true_or_false:5,employee:8,department:8,object_reference:100,master_detail:100,file_attachment:17,quote:100,url:1,location:39,auto_number:1,comment:1,time:30,percentile:33,multi_level_select_one:11,embedded_object_list:100},n={1:"EQ",2:"N",3:"GT",4:"GTE",5:"LT",6:"LTE",7:"LIKE",8:"NLIKE",9:"IS",10:"ISN",11:"LIKE"},e=(_.invert(n).LIKE=7,Backbone.View.extend({CRM_SELFOBJECT:s,options:{display_name:"",error_code:"",queryParam:null,form_fields:[]},events:{"click .status-item":"getDataByStatusHandle","click .j-import-out":"importOutHandle"},initialize:function(e){this.el.innerHTML=r(),this.data=e.data,this.render()},render:function(){var t=this;t.$el&&t.fetchColunms(function(){t.table&&t.table.destroy(),t.table=new o(t.getOptions()),t.table.on("trclick",function(e){t.showDetail(e)})})},showDetail:function(e){var t,a=e.object_data_id,e=e.object_describe_api_name;a&&e&&(i.isCrmPreObject(e)?t={AccountObj:"customer",LeadsObj:"saleclue",ContactObj:"contact"}[e]:(t="myobject",a=a+","+e),t)&&FS.MEDIATOR.trigger("crm.detail",{type:t,data:{crmId:a},callback:function(){}})},fetchColunms:function(a){var r=this;r.columnsAjax&&r.columnsAjax.abort(),r.columnsAjax=i.FHHApi({url:"/EM1HCRMSMARTFORM/smartform/findFormDataListHeader",data:{formDescribeId:r.data.formDescribeId},success:function(e){var t=e.Value;0===e.Result.StatusCode?(r.options.form_fields=r.parseColumns(t.describe.fields),r.options.display_name=t.describe.display_name,r.options.isMyObject=!/Obj$/.test(t.describe.ref_object_api_name),a()):i.alert(e.Result.FailureMessage||$t("暂时无法获取相关数据请稍后重试"))},complete:function(){r.columnsAjax=null},error:function(){r.columnsAjax=null}},{errorAlertModel:1})},parseColumns:function(e){var r=[];return _.each(e,function(e){var t=e.type,a=s[t]||1;"group"!==t&&(e={title:e.label,data:e.api_name,dataType:a,type:t,not_use_multitime_zone:e.not_use_multitime_zone||!1,options:_.map(e.options||[],function(e){return{ItemCode:e.value,ItemName:e.label,Children:_.map(e.child_options,function(e){return{ItemCode:e.value,ItemName:e.label}})}}),referRule:8===a&&"department"!==t&&"Employee",isId:!0},1===a&&(e.filterCompare=[11,12]),r.push(e))}),r},getOptions:function(){var t=this;return{$el:t.$(".statistic-table"),url:"/EM1HCRMSMARTFORM/smartform/findFormDataList",requestType:"FHHApi",zIndex:501,showSize:!1,showFilerBtn:!1,showTerm:!1,showManage:!1,postData:{},isMyObject:t.options.isMyObject,columns:t.options.form_fields,formatData:function(e){return t.parseData(e)},paramFormat:function(e){return t.parseParam(e)}}},parseData:function(e){var r,i=this,t=e.dataList;return _.each(t,function(a){_.each(a,function(e,t){switch((r=_.findWhere(i.options.form_fields,{data:t})||{}).type){case"select_many":e&&(a[t]=e.join("|"));break;case"image":case"signature":e&&(a[t]=_.map(e,function(e){return e.path+"."+e.ext}).join("|"));break;case"master_detail":case"object_reference":a[t]=a[t+"__r"];break;case"date":case"date_time":r.not_use_multitime_zone?e&&_.isNaN(+e)&&(e=new Date(FS.util.convertTimestampFromTZ8(+e)).getTime()):e&&_.isNaN(+e)&&(e=new Date(e).getTime()),a[t]=-1==e?0:e;break;case"true_or_false":a[t]=_.isUndefined(e)?"--":e?"true":"false"}})}),{totalCount:e.total,data:t}},parseParam:function(e){var t={formDescribeId:this.data.formDescribeId},a={page:{pageSize:e.pageSize,pageNumber:e.pageNumber},filters:[]},r=this.options.error_code;return e.SortField&&(a.orders=[{fieldName:e.SortField,isAsc:2==e.SortType}]),e.QueryInfo&&e.QueryInfo.Conditions&&(a.filters=_.map(e.QueryInfo.Conditions,function(e){return{field_name:e.FieldName,field_values:[e.FilterValue],operator:n[e.Comparison]}})),_.isNumber(r)&&a.filters.push({field_name:"error_code",field_values:[0],operator:r?"N":"EQ"}),t.searchQueryInfo=JSON.stringify(a),this.options.queryParam=t},getDataByStatusHandle:function(e){e=$(e.currentTarget);e.hasClass("cur")||(e.addClass("cur").siblings().removeClass("cur"),this.options.error_code=e.data("status"),this.table.setParam({},!0,!0))},importOutHandle:function(e){CRM.api.export({apiname:this.data.apiname,displayName:this.options.display_name,queryParam:this.options.queryParam,showUniqueID:!1,showExportfield:!1,async:!1,getTokenUrl:"/EM1HCRMSMARTFORM/smartform/exportFormData"}),CRM.util.uploadLog("smartform","detail",{eventId:"dataimportout",eventType:"cl"}),e.preventDefault()},destroy:function(){this.$el.off(),this.table&&this.table.destroy(),this.importOut&&this.importOut.destroy(),this.table=this.importOut=null}}));a.exports=e});
define("crm-setting/smartforms/detail/detail",["crm-modules/common/util","crm-modules/common/slide/slide","./template/index-html","./summary"],function(a,e,t){var i=a("crm-modules/common/util"),r=a("crm-modules/common/slide/slide"),s=a("./template/index-html"),n=a("./summary");t.exports=r.extend({options:{showMask:!1,top:61,zIndex:500,className:"crm-d-smartform crm-d-detail crm-nd-detail",entry:"crm"},events:{"click .j-nav-item":"toggleContent","click [data-action]":"actionHandle"},show:function(e){var t=this;r.prototype.show.apply(this),this.formDescribeId=e,this.render(function(){t.renderPage(t.$selectedTarget||t.$(".nav-selected"))})},render:function(t){var a,i=this;this.el.innerHTML=s(),this.$container=this.$(".container"),this.getFormDetail(function(e){i.formDetailData=e,a=e.formDescribe,i.$(".display_name").html(a.display_name),i.$(".display_name").attr({title:a.display_name}),i.$(".status").addClass(a.is_active?"status1":"status2").html(a.is_active?$t("已启用"):$t("已停用")),i.$(".status3").html(a.expire_time&&0<(new Date).getTime()-a.expire_time?$t("已过期"):""),a.ref_object_display_name&&!1!==a.ref_object_is_active||i.$(".b-item").addClass("disabled"),i.page1=new n({el:i.$container,data:i.formDetailData}),t&&t()})},getFormDetail:function(t){i.FHHApi({url:"/EM1HCRMSMARTFORM/smartform/findFormDetail",data:{formDescribeId:this.formDescribeId,includeStatistics:!0,includeCard:!0},success:function(e){0==e.Result.StatusCode?t(e.Value):i.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},actionHandle:function(e){var t,a=$(e.currentTarget).data("action");"hide"==a?this.hide():(t={formDescribeId:(t=this.formDetailData.formDescribe)._id,ref_object_api_name:t.ref_object_api_name,record_type:t.record_type,type:a},"refresh"==a?this.refresh():$(e.currentTarget).hasClass("disabled")||this.createAction(t))},createAction:function(e){var t=this;CRM.api.smart_form(_.extend({api:"Editor",refresh:function(){t.trigger("refresh")}},e))},toggleContent:function(e){e=this.$selectedTarget=$(e.currentTarget);e.hasClass("nav-selected")||this.renderPage(e)},renderPage:function(e){var t=this,e=e.data("name");switch(t.$(".nav-item").eq(e[4]-1).addClass("nav-selected").siblings().removeClass("nav-selected"),t.page1&&(t.page1.destroy(),t.page1=null),t.page2&&(t.page2.destroy(),t.page2=null),t.page3&&(t.page3.destroy(),t.page3=null),e){case"page2":a.async("./flowstatistic",function(e){t.page2=new e({el:t.$container,data:{formDescribeId:t.formDescribeId,display_name:t.formDetailData.formDescribe.display_name,apiname:"smartform"}})});break;case"page3":a.async("./datastatistic",function(e){t.page3=new e({el:t.$container,data:{formDescribeId:t.formDescribeId,apiname:"smartform"}})});break;default:t.page1=new n({el:t.$container,data:t.formDetailData})}},refresh:function(){var e=this;this.page1&&this.page1.destroy(),this.page2&&this.page2.destroy(),this.page3&&this.page3.destroy(),this.render(function(){e.renderPage(e.$selectedTarget||e.$(".nav-selected"))})},destroy:function(){var t=this;_.each(["action","page1","page2","page3"],function(e){t[e]&&t[e].destroy(),t[e]&&(t[e]=null)}),r.prototype.destroy.call(t)}})});
define("crm-setting/smartforms/detail/flowstatistic",["base-echarts","crm-widget/table/table","./template/flowstatistic-html"],function(t,e,a){var c=CRM.util,d=c.moment,f=t("base-echarts"),i=t("crm-widget/table/table"),r=t("./template/flowstatistic-html");a.exports=Backbone.View.extend({initialize:function(t){this.data=t.data,this.el.innerHTML=r(),this.initEcharts(),this.initTable()},events:{"click .j-import-out":"importOutHandle"},initEcharts:function(){var i,r=f.init(this.el.querySelector(".flow-chart")),s=new Date(d().format("YYYY-MM-DD")+"T00:00:00").getTime()+864e5,o=s-6048e5,n=[],l=[],u=[],m=[];c.FHHApi({url:"/EM1HCRMSMARTFORM/smartform/findFormStatisticsByDate",data:{beginDate:o,endDate:s,formDescribeId:this.data.formDescribeId},success:function(t){if(0==t.Result.StatusCode){i=t.Value.statisticsList;for(var e,a=o;a<s;a+=864e5)e=_.findWhere(i,{access_date:a})||{},n.push(e.pv_count||0),l.push(e.uv_count||0),u.push(e.submit_count||0),m.push(d(a).format("YYYY-MM-DD"));t={color:["#00b4f1","#00cfcd","#fc923f"],tooltip:{trigger:"axis"},legend:{data:[$t("浏览量")+" (PV)",$t("独立访客")+" (UV)",$t("提交数")]},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:{type:"category",data:m},yAxis:{type:"value"},series:[{name:$t("浏览量")+" (PV)",type:"line",data:n},{name:$t("独立访客")+" (UV)",type:"line",data:l},{name:$t("提交数"),type:"line",data:u}]};r.setOption(t)}}},{errorAlertModel:1})},initTable:function(){var e=this;e.dt?e.dt.setParam({},!0):e.dt=new i({$el:e.$(".flow-table"),requestType:"FHHApi",url:"/EM1HCRMSMARTFORM/smartform/findFormStatistics",showMultiple:!1,columns:[{data:"access_date",title:$t("访问时间"),render:function(t){return t?d(t).format("YYYY-MM-DD"):"--"}},{data:"pv_count",title:$t("浏览量")+" (PV)"},{data:"uv_count",title:$t("独立访客")+" (UV)"},{data:"submit_count",title:$t("提交数")}],paramFormat:function(t){return e.parseParam(t)},formatData:function(t){return{totalCount:t.total,data:t.statisticsList}}})},parseParam:function(t){var e={formDescribeId:this.data.formDescribeId};return e.searchQueryInfo=JSON.stringify({page:{pageSize:t.pageSize,pageNumber:t.pageNumber}}),this.queryParam=e},importOutHandle:function(t){var e=this;CRM.api.export({apiname:e.data.apiname,displayName:e.data.display_name,queryParam:e.queryParam,showUniqueID:!1,showExportfield:!1,async:!1,getTokenUrl:"/EM1HCRMSMARTFORM/smartform/exportFormStatistics"}),CRM.util.uploadLog("smartform","detail",{eventId:"flowimportout",eventType:"cl"}),t.preventDefault()},destroy:function(){this.$el.off(),this.dt&&this.dt.destroy(),this.importOut&&this.importOut.destroy(),this.dt=this.importOut=null}})});
define("crm-setting/smartforms/detail/summary",["crm-widget/select/select","crm-modules/common/util","./template/summary-html"],function(e,t,a){var n=e("crm-widget/select/select"),o=e("crm-modules/common/util"),c=e("./template/summary-html");a.exports=Backbone.View.extend({initialize:function(e){var e=e.data,t=(this.el.innerHTML=c(e),this.initSelect(e.card.form_url),this.canvas=this.$("canvas")[0],this.canvas.getContext("2d")),a=new Image;this.displayName=e.formDescribe&&e.formDescribe.display_name,a.src=e.card.qrcode_url,a.onload=function(){t.drawImage(a,0,0,1e3,1e3)}},events:{"click .j-downloadimg":"downloadQrcode","click .j-copyurl":"copyUrl","click .j-copycode":"copyCode"},initSelect:function(e){var a=this.$(".content3"),o=["<script>",'var iframe = document.createElement("iframe")','iframe.src = "'+e+'"',"document.body.appendChild(iframe)","<\/script>"].join(";"),c="<iframe src="+e+"></iframe>";a.text(o),this.select=new n({$wrap:this.$(".j-select"),zIndex:1010,stopPropagation:!0,options:[{value:"js",name:"JavaScript"},{value:"iFrame",name:"iFrame"}]}),this.select.on("change",function(e,t){switch(t.value){case"js":a.text(o);break;case"iFrame":a.text(c)}})},downloadQrcode:function(e){var t=new Image,t=(t.src=this.canvas.toDataURL("image/png"),$("<a></a>").attr("href",t.src).attr("download",this.displayName||$t("paas.object.smartform.code",null,"智能表单二维码")).appendTo(this.el));return t[0].click(),t.remove(),CRM.util.uploadLog("smartform","detail",{eventId:"downloadqrcode",eventType:"cl"}),e.preventDefault(),e.stopPropagation(),!1},copyUrl:function(e){var t=document.createRange(),e=(t.selectNode($(e.target).prev().children()[0]),window.getSelection());0<e.rangeCount&&e.removeAllRanges(),e.addRange(t),document.execCommand("Copy"),o.remind(1,$t("复制成功！")),CRM.util.uploadLog("smartform","detail",{eventId:"copyurl",eventType:"cl"})},copyCode:function(e){$(e.target).prev()[0].select(),document.execCommand("Copy"),o.remind(1,$t("复制成功！")),CRM.util.uploadLog("smartform","detail",{eventId:"copycode",eventType:"cl"})},destroy:function(){this.$el.off(),this.select&&(this.select.destroy(),this.select=null)}})});
define("crm-setting/smartforms/detail/template/datastatistic-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="statistic-header"> <div class="items"> <span class="item-tit">' + ((__t = $t("状态：")) == null ? "" : __t) + '</span> <span class="status-item cur">' + ((__t = $t("全部")) == null ? "" : __t) + '</span> <span class="status-item" data-status="0">' + ((__t = $t("同步成功")) == null ? "" : __t) + '</span> <span class="status-item" data-status="1">' + ((__t = $t("同步失败")) == null ? "" : __t) + '</span> </div> <span class="crm-btn crm-btn-primary j-import-out">' + ((__t = $t("导出")) == null ? "" : __t) + '</span> </div> <div class="statistic-table"></div>';
        }
        return __p;
    };
});
define("crm-setting/smartforms/detail/template/flowstatistic-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="sec-tit"> <h3>' + ((__t = $t("最近7天流量统计")) == null ? "" : __t) + '</h3> </div> <div class="flow-chart"></div> <div class="sec-tit flowstatistic-header"> <h3>' + ((__t = $t("流量数据")) == null ? "" : __t) + '</h3> <span class="crm-btn crm-btn-primary j-import-out">' + ((__t = $t("导出")) == null ? "" : __t) + '</span> </div> <div class="flow-table"></div>';
        }
        return __p;
    };
});
define("crm-setting/smartforms/detail/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-d-layout"> <div class="header d-top"> <div class="crm-d-comp-tit"> <span class="d-obj-icon"></span> <span class="obj-name">' + ((__t = $t("智能表单")) == null ? "" : __t) + '</span> <div class="tit"> <div> <p class="display_name"></p> <span class="status"></span> <span class="status3"></span> </div> </div> </div> <div class="operate"> <div class="crm-d-btn-operate"> <div class="b-item b-main-btn crm-btn crm-btn-primary" data-action="edit">' + ((__t = $t("编辑")) == null ? "" : __t) + '</div> <div class="b-item b-main-btn crm-btn" data-action="preview">' + ((__t = $t("预览")) == null ? "" : __t) + '</div> </div> </div> <div class="d-g-btns"> <span class="r-btn" title="' + ((__t = $t("刷新")) == null ? "" : __t) + '" data-action="refresh"></span> <span class="h-btn" title="' + ((__t = $t("关闭")) == null ? "" : __t) + '" data-action="hide"></span> </div> </div> <div class="d-navigation"> <div class="crm-d-navigation"> <div class="nav-items j-nav-items"> <span data-name="page1" class="nav-item j-nav-item nav-selected">' + ((__t = $t("概览")) == null ? "" : __t) + '</span> <span data-name="page2" class="nav-item j-nav-item">' + ((__t = $t("流量统计")) == null ? "" : __t) + '</span> <span data-name="page3" class="nav-item j-nav-item">' + ((__t = $t("数据统计")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="container crm-scroll"> <div class="crm-loading"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/smartforms/detail/template/summary-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var moment = CRM.util.moment;
            __p += ' <div class="sec-tit"><h3>' + ((__t = $t("统计信息")) == null ? "" : __t) + '</h3></div> <div class="summary-statistic"> <div class="item"> <div class="icon icon1"></div> <div> <span class="title">' + ((__t = $t("浏览量")) == null ? "" : __t) + '(PV)</span> <div class="amount"><em>' + ((__t = statistics.pv_count) == null ? "" : __t) + '</em></div> </div> </div> <div class="item"> <div class="icon icon2"></div> <div> <span class="title">' + ((__t = $t("独立访客")) == null ? "" : __t) + '(UV)</span> <div class="amount"><em>' + ((__t = statistics.uv_count) == null ? "" : __t) + '</em></div> </div> </div> <div class="item"> <div class="icon icon3"></div> <div> <span class="title">' + ((__t = $t("提交总数")) == null ? "" : __t) + '</span> <div class="amount"><em>' + ((__t = statistics.submit_count) == null ? "" : __t) + '</em></div> </div> </div> </div> <div class="sec-tit"><h3>' + ((__t = $t("发布信息")) == null ? "" : __t) + '</h3></div> <canvas height="1000" width="1000" class="hide"></canvas> <div class="field-item b-g-clear"> <div class="pulish-item"> <p>' + ((__t = $t("二维码")) == null ? "" : __t) + '</p> <img src="' + ((__t = card.qrcode_url) == null ? "" : __t) + '" class="content" style="width: 100px;"> <span class="crm-btn crm-btn-cancel j-downloadimg" style="margin-right: 16px;">' + ((__t = $t("下载二维码")) == null ? "" : __t) + '</span> </div> <div class="pulish-item"> <p>' + ((__t = $t("访问地址")) == null ? "" : __t) + '</p> <div class="content"> <a href="' + ((__t = card.form_url) == null ? "" : __t) + '" class="url-text" target="_blank"> ' + ((__t = card.form_url) == null ? "" : __t) + ' </a> </div> <span class="crm-btn crm-btn-cancel j-copyurl">' + ((__t = $t("复制网址")) == null ? "" : __t) + '</span> </div> <div class="pulish-item"> <p class="pulish-item-title">' + ((__t = $t("网页嵌入代码")) == null ? "" : __t) + '<span class="j-select select-container"></span></p> <textarea class="content content3"></textarea> <span class="crm-btn crm-btn-cancel j-copycode">' + ((__t = $t("复制代码")) == null ? "" : __t) + '</span> </div> </div> <div class="sec-tit"><h3>' + ((__t = $t("详细信息")) == null ? "" : __t) + "</h3></div> ";
            var getEmployeeById = function(id) {
                var emp = CRM.util.getEmployeeById(id);
                return emp ? emp.fullName : "--";
            };
            __p += ' <div class="field-table b-g-clear"> <div class="crm-d-comp-infolist"> <div class="i-item b-g-clear"> <div class="i-l"> <div class="i-wrap"> <div class="tit">' + ((__t = $t("截止时间")) == null ? "" : __t) + '</div> <div class="con">' + ((__t = formDescribe.expire_time ? moment(formDescribe.expire_time).format("YYYY-MM-DD HH:mm:ss") : "--") == null ? "" : __t) + '</div> </div> </div> <div class="i-r"> <div class="i-wrap"> <div class="tit">' + ((__t = $t("创建人")) == null ? "" : __t) + '</div> <div class="con">' + ((__t = getEmployeeById(formDescribe.created_by)) == null ? "" : __t) + '</div> </div> </div> </div> <div class="i-item b-g-clear"> <div class="i-l"> <div class="i-wrap"> <div class="tit">' + ((__t = $t("创建时间")) == null ? "" : __t) + '</div> <div class="con">' + ((__t = formDescribe.create_time ? moment(formDescribe.create_time).format("YYYY-MM-DD HH:mm:ss") : "--") == null ? "" : __t) + '</div> </div> </div> <div class="i-r"> <div class="i-wrap"> <div class="tit">' + ((__t = $t("最后修改人")) == null ? "" : __t) + '</div> <div class="con">' + ((__t = getEmployeeById(formDescribe.last_modified_by)) == null ? "" : __t) + '</div> </div> </div> </div> <div class="i-item b-g-clear"> <div class="i-l"> <div class="i-wrap"> <div class="tit">' + ((__t = $t("最后跟进时间")) == null ? "" : __t) + '</div> <div class="con">' + ((__t = formDescribe.last_modified_time ? moment(formDescribe.last_modified_time).format("YYYY-MM-DD HH:mm:ss") : "--") == null ? "" : __t) + '</div> </div> </div> <div class="i-r"><div class="line-white"></div></div> </div> <div class="i-line"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/smartforms/form-dialog",["crm-modules/common/util","crm-widget/select/select","crm-widget/dialog/dialog"],function(e,t,l){var a=e("crm-modules/common/util"),r=e("crm-widget/select/select"),o=e("crm-widget/dialog/dialog").extend({attrs:{width:500,height:390,title:$t("新建表单"),showBtns:!0,content:'<div class="crm-g-form crm-g-form-smart-form"><div class="fm-item fm-title" style="line-height:18px;">'+$t("为了使表单数据能自动生成CRM对象请先选择对象")+'</div><div class="fm-item"><label class="fm-lb" style="width:80px;line-height:18px">'+$t("请选择对象")+'</label><div class="object-select" style="float: left;width:325px;"></div></div><div class="fm-item"><label class="fm-lb" style="width:80px;">'+$t("业务类型")+'</label><div class="type-select" style="float: left;width:325px;"></div></div><div class="fm-item fm-language"><label class="fm-lb" style="width:80px;">'+$t("选择语言")+'</label><div class="language-select" style="float: left;width:325px;"></div></div></div>'},render:function(){return o.superclass.render.call(this)},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"onEnter"},onEnter:function(e){$(e.currentTarget).hasClass("b-g-btn-disabled")||(e=this.typeSelect?{ref_object_api_name:this.objectSelect.getValue(),record_type:this.typeSelect.getValue(),language:this.languegeSelect.getValue()}:{ref_object_api_name:this.objectSelect.getValue()},this.isCopy&&(e.formDescribeId=this.formDescribeId),this.trigger("success",e))},show:function(e){var t=o.superclass.show.call(this),l=this,s=e.formDescribeId,a=e.isCopy,i=e.defaultRecordType,c=e.defaultLang,s=(l.objectSelect&&(l.objectSelect.destroy(),l.objectSelect=null),this.formDescribeId=s,this.isCopy=a,this.defaultRecordType=i,this.defaultLang=c,l.objectSelect=new r({$wrap:this.$(".object-select"),zIndex:1010,options:e.options,defaultValue:e.defaultValue,disabled:a}),l.objectSelect.on("change",function(e,t){l.getRecordTypeList(t.value)}),l.getRecordTypeList(e.defaultValue),a?$t("复制表单"):$t("新建表单"));return $(".dialog-tit",this.element).html(s),t},getRecordTypeList:function(e){var s=this;s.$(".fm-error").remove(),a.FHHApi({url:"/EM1HCRMSMARTFORM/smartform/findValidRecordTypeList",data:{objectApiName:e},success:function(e){var t,l;s.$(".fm-item").eq(2).hide(),s.$(".fm-item").eq(3).hide(),_.contains([304601010,400],e.Result.FailureCode)?(a.showErrmsg(s.$(".object-select"),e.Result.FailureMessage),s.$(".b-g-btn").addClass("b-g-btn-disabled")):e.Result.StatusCode||(e.Value.recordTypeList.length&&(s.$(".b-g-btn").removeClass("b-g-btn-disabled"),s.$(".fm-item").eq(2).show(),s.$(".fm-item").eq(3).show(),s.typeSelect&&(s.typeSelect.destroy(),s.typeSelect=null),s.languegeSelect&&(s.languegeSelect.destroy(),s.languegeSelect=null),t=s.defaultRecordType||e.Value.recordTypeList&&e.Value.recordTypeList[0]&&e.Value.recordTypeList[0].api_name,l=s.defaultLang||"zh-CN",_.find(e.Value.languages,function(e){return e.value===l})||(l=e.Value.languages[0].value),s.typeSelect=new r({$wrap:s.$(".type-select"),zIndex:1010,defaultValue:t,options:_.map(e.Value.recordTypeList,function(e){return{value:e.api_name,name:e.label}})}),e.Value.enableI18N||s.$(".fm-language").hide(),s.languegeSelect=new r({$wrap:this.$(".language-select"),zIndex:1010,options:_.map(e.Value.languages,function(e){return{value:e.value,name:e.displayName}}),defaultValue:l})),s.resizedialog())}},{errorAlertModel:1})},hide:function(){return o.superclass.hide.call(this)},destroy:function(){var e=o.superclass.destroy.call(this);return this.objectSelect&&this.objectSelect.destroy(),this.typeSelect&&this.typeSelect.destroy(),this.languegeSelect&&this.languegeSelect.destroy(),e}});l.exports=o});
define("crm-setting/smartforms/smartforms",["crm-modules/common/util","crm-widget/table/table","./form-dialog"],function(e,t,a){var s=e("crm-modules/common/util"),i=e("crm-widget/table/table"),d=e("./form-dialog");a.exports=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.createEl()},createEl:function(){this.$el.html('<div class="list-view"><div class="crm-loading"></div></div>')},render:function(){var e=this;e.getAvailableObjectList(function(){e.initTable(),window.$st=function(e,t){return e&&t}})},events:{"click .j-add":"onAdd"},getAvailableObjectList:function(t){var a=this;a._ajax&&a._ajax.abort(),a._ajax=s.FHHApi({url:"/EM1HCRMSMARTFORM/smartform/findAvailableObjectList",data:{},success:function(e){0==e.Result.StatusCode&&(a.objectList=_.map(e.Value.objectDescribeList,function(e){return{id:e.api_name,name:e.display_name}}),a.objectApiName=a.objectList[0].id,t&&t(),a._ajax=null)}})},initTable:function(){var r=this;r.dt?r.refresh():(r.dt=new i({$el:r.$(".list-view"),title:$t("智能表单"),requestType:"FHHApi",url:"/EM1HCRMSMARTFORM/smartform/findFormList",showMultiple:!1,operate:{pos:"C",btns:[{text:$t("新建"),className:"j-add"}]},search:{placeHolder:$t("搜索智能表单"),type:"Keyword",highFieldName:"display_name"},searchTerm:{pos:"C",type:"objectApiName",options:[{id:"",name:$t("全部")}].concat(r.objectList)},columns:[{data:"display_name",title:$t("表单名称"),fixed:!0,render:function(e,t,a){return'<a href="javascript:;">'+e+"</a>"}},{data:"ref_object_display_name",title:$t("所属对象"),width:200,render:function(e,t,a){return a.ref_object_display_name?a.ref_object_display_name+(!1===a.ref_object_is_active?'<span style="color: #fc923f">('+$t("已禁用")+")</span>":""):'-- <span style="color: #fc923f">('+$t("对象已删除")+")</span>"}},{data:"record_type_label",title:$t("业务类型")},{data:"",title:$t("截止时间"),render:function(e,t,a){return a.expire_time?FS.moment(a.expire_time).format("YYYY-MM-DD HH:mm"):"--"}},{data:"created_by",title:$t("创建人"),render:function(e){return r.getEmployeeById(e)}},{data:"create_time",title:$t("创建日期"),dataType:4},{data:"last_modified_time",title:$t("修改日期"),dataType:4},{data:"last_modified_by",title:$t("修改人"),render:function(e){return r.getEmployeeById(e)}},{data:"is_active",title:$t("状态"),render:function(e){return e?$t("已启用"):$t("已停用")}},{data:"",title:$t("操作"),lastFixed:!0,width:220,render:function(e,t,a){var i=a.ref_object_display_name&&!1===a.ref_object_is_active||!a.ref_object_display_name&&!a.ref_object_is_active?"disabled":"",a=a.is_active;return['<div class="ops-btns">','<a data-operate="edit" class='+i+">"+$t("编辑")+"</a>",'<a data-operate="preview" class='+i+">"+$t("预览")+"</a>",'<a data-operate="toggle">'+(a?$t("停用"):$t("启用"))+"</a>",'<a data-operate="copy" class='+i+">"+$t("复制")+"</a>",a?"":'<a data-operate="del">'+$t("删除")+"</a>","</div>"].join("")}}],paramFormat:function(e){return r.parseParam(e)},formatData:function(e){return{totalCount:e.total,data:e.formList}},initComplete:function(){this.$el.find(".dt-tit").append(['<div class="crm-tip">','<span class="tip-btn"></span>','<div class="tip-text">',"<p>&nbsp;&nbsp;"+$t("由CRM对象生成的表单以二维码或访问链接的形式开放给外部用户通过扫码或链接的方式打开表单提交表单数据后可自动生成CRM对象的记录数据。")+"</p>","<a href='https://help.fxiaoke.com/dbde/33de/b4bb/1186' target='_blank'>"+$t("查看帮助文档")+"</a>","</div>","</div>"].join(""))}}),r.dt.on("term.change",function(e){r.objectApiName=e}),r.dt.on("trclick",function(e,t,a){var i=a.data("operate");a.hasClass("disabled")||(i?r.operateHandle(i,e):r.showDetail(e._id))}))},parseParam:function(e){var t={objectApiName:e.objectApiName},a=e.Keyword?[{field_name:"display_name",field_values:[e.Keyword],field_value_type:"text",operator:"LIKE"}]:[];return t.searchQueryInfo=JSON.stringify({filters:a,page:{pageSize:e.pageSize,pageNumber:e.pageNumber}}),t},getEmployeeById:function(e){return(e=e&&s.getEmployeeById(e))?e.fullName:"--"},showDetail:function(t){var a=this;e.async("./detail/detail",function(e){a.detail||(a.detail=new e,a.detail.on("refresh",function(){a.refresh()})),a.detail.show(t)})},createAction:function(e){var t=this;e.api="Editor",CRM.api.smart_form(_.extend({refresh:function(e){t.refresh(),e&&t.showDetail(e)}},e))},operateHandle:function(e,t){var a={formDescribeId:t._id,ref_object_api_name:t.ref_object_api_name,record_type:t.record_type,lang:t.lang,type:e};switch(e){case"edit":case"preview":this.createAction(a);break;case"copy":this.onCopy(a);break;case"toggle":this.onToggle(t);break;case"del":this.onDel(t)}},onToggle:function(t){var a=this;s.FHHApi({url:t.is_active?"/EM1HCRMSMARTFORM/smartform/disableForm":"/EM1HCRMSMARTFORM/smartform/enableForm",data:{formDescribeId:t._id},success:function(e){0==e.Result.StatusCode&&(s.remind(1,$t("操作成功！")),a.refresh()),t.is_active&&s.uploadLog("smartform","list",{eventId:"disableform",eventType:"cl"})}})},onCopy:function(e){this.onAdd("copy",e)},onDel:function(e){var t=this,a=s.confirm("<p>"+$t("确认删除该表单")+"</p>",$t("删除"),function(){s.FHHApi({url:"/EM1HCRMSMARTFORM/smartform/deleteForm",data:{formDescribeId:e._id},success:function(e){0==e.Result.StatusCode&&(s.remind(1,$t("操作成功！")),t.refresh(),a.hide())}})})},onAdd:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},a=this,i=(a.type=t="string"==typeof t?t:"add",e.formDescribeId),r=e.ref_object_api_name,o=e.record_type,n=e.lang;s.FHHApi({url:"/EM1HCRMSMARTFORM/smartform/checkFormLicense",success:function(e){0===e.Result.StatusCode&&e.Value.success&&(a.formdialog||(a.formdialog=new d,a.formdialog.on("success",function(e){a.formdialog.hide(),a.createAction(_.extend({},e,{type:a.type}))})),a.formdialog.show({options:_.map(a.objectList,function(e){return{value:e.id,name:e.name}}),defaultValue:("copy"===t?r:a.objectApiName)||a.objectList[0].id,isCopy:"copy"===t,formDescribeId:i,defaultRecordType:o,defaultLang:n}),s.uploadLog("smartform","list",{eventId:"add",eventType:"cl"}))}})},refresh:function(){this.dt.setParam({},!0)},destroy:function(){var t=this;t._ajax&&t._ajax.abort(),_.each(["dt","action","detail","formdialog"],function(e){t[e]&&t[e].destroy&&t[e].destroy()})}})});