define("crm-setting/servicesearch/servicesearch",["crm-modules/common/util","./template/tpl-html"],function(e,t,n){var i=e("crm-modules/common/util"),o=e("./template/tpl-html"),e=Backbone.View.extend({key:"9",initialize:function(e){this.setElement(e.wrapper)},events:{"click .mn-radio-item":"_onSet","click .label":"_onLabel"},render:function(){var t=this;t._getConfig(function(e){t.$el.html(o({val:e}))})},_getConfig:function(t){i.getConfigValue(this.key).then(function(e){t&&t(e||"1")},function(){t&&t("1")})},_onSet:function(e){e=$(e.currentTarget);if(e.hasClass("mn-selected"))return!1;this._setConfig(e.attr("data-value"))},_setConfig:function(e){var t=this;i.setConfigValue({key:t.key,value:e}).then(function(){i.remind(1,$t("设置成功"))},function(e){t.render(),i.remind(3,$t("设置失败"))})},_onLabel:function(e){$(e.currentTarget).closest("p").find(".mn-radio-item").trigger("click")}});n.exports=e});
define("crm-setting/servicesearch/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("服务人员查询类型")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="mn-radio-box crm-p20"> <p> <span class="mn-radio-item ' + ((__t = val == 1 ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span> <span class="label" style="margin-left:10px; cursor:pointer;">' + ((__t = $t("模糊查询")) == null ? "" : __t) + '</span> </p> <p class="crm-gray-9" style="margin:2px 0 20px 25px;">' + ((__t = $t("输入超过两个关键字即可开始查询最多提供10个结果")) == null ? "" : __t) + '</p> <p> <span class="mn-radio-item ' + ((__t = val == 2 ? "mn-selected" : "") == null ? "" : __t) + '" data-value="2"></span> <span class="label" style="margin-left:10px; cursor:pointer;">' + ((__t = $t("精确查询")) == null ? "" : __t) + '</span> </p> <p class="crm-gray-9" style="margin:2px 0 12px 25px;">' + ((__t = $t("只有完全匹配到输入的客户名称后才能查询到结果")) == null ? "" : __t) + "</p> </div> </div>";
        }
        return __p;
    };
});