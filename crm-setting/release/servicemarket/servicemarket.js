define("crm-setting/servicemarket/servicemarket",["./template/tpl-html","crm-modules/page/list/list"],function(t,e,i){var a=t("./template/tpl-html"),s=t("crm-modules/page/list/list"),t=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper),this.apiname="ServiceMarketObj",this.displayName=$t("crm.setting.servicemarket.edit.title"),this.render()},render:function(){this.$el.html(a()),this.renderTable()},renderTable:function(){var n=this,t=s.extend({getColumns:function(){var t=s.prototype.getColumns.apply(this,arguments);return _.findWhere(t,{dataType:"operate"}).width=100,t},getCustomOperate:function(t,e){return _.each(t,function(t){t.render_type="not_fold",t.data=e}),t},operateBtnClickHandle:function(t){var e,i=$(t.target),a=i.data("action"),s=i.closest(".tb-cell").data("id");a&&(_.contains(["Delete"],a)?(i={Delete:$t("删除")},e=CRM.util.confirm($t("确定{{actionName}}吗？",{actionName:i[a]||$t("执行操作")}),null,function(){e.hide(),a="__"+a.split("_").join("")+"Handle",n[a]&&n[a](t,s)})):(a="__"+a.split("_").join("")+"Handle",n[a]&&n[a](t,s)))},trclickHandle:function(t,e,i,a,s){n.showDetail(t,this.table.getCurData())}});this.list=new t({wrapper:$(".crm-qir-table",this.$el),apiname:this.apiname,isRenderRecordType:!1,tableOptions:{searchTerm:null,refreshCallBack:n.refresh()}}),this.list.render&&this.list.render()},showDetail:function(t,e){var i=this;CRM.api.show_crm_detail({apiName:this.apiname,id:t._id,idList:_.pluck(e,"_id"),showMask:!1,top:56,callback:function(){i.refresh()}})},__EditHandle:function(t,e){var i=this;this.edit=CRM.api.edit({apiname:this.apiname,id:e,displayName:this.displayName,success:function(){i.refresh()}})},__DeleteHandle:function(t,e){var i=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/".concat(this.apiname,"/action/Delete"),data:{objectDataId:e,trigger_info:{trigger_page:"List"}},success:function(t){0===t.Result.StatusCode?(CRM.util.remind(1,$t("删除成功")),i.refresh()):CRM.util.alert(t.Result.FailureMessage||DEFAULT_ERROR_MSG)},error:function(t){CRM.util.alert(DEFAULT_ERROR_MSG)}})},refresh:function(){this.list&&this.list.refresh&&this.list.refresh()},destroy:function(){this.list&&this.list.destroy&&this.list.destroy()}});i.exports=t});
define("crm-setting/servicemarket/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-qir-wrapper"> <div class="crm-qir-table"></div> </div>';
        }
        return __p;
    };
});