define("crm-setting/esignature/esignature",["crm-widget/table/table","./model/model","./template/tpl-html","./template/content-html"],function(a,t,e){var n=a("crm-widget/table/table"),s=a("./model/model"),i=FS.crmUtil;return Backbone.View.extend({template:a("./template/tpl-html"),contentTpl:a("./template/content-html"),initialize:function(t){this.setElement(t.wrapper),this.model=new s},events:{"click .j-esigninit":"esgininitHandle","click .j-esignature":"switchStatusHandle","click .j-alarm":"switchAlarmHandle","click .esign-edit-setalarm":"setAlarmHandle","click .esign-edit-setalarm_confirm":"setAlarmConfirmHandle","click .esign-edit-setalarm_cancel":"setAlarmCancelHandle","click .j-show-buyrecode":"showAllBuyRecodeHandle","click .j-set-group":"setGroupHandle"},render:function(){var e=this;e.model.fetchInfo(function(t){1!=t.canAccess?e.renderTpl('<div class="crm-warn-bar">'+$t("该模块未开通如有需要请咨询纷享客服400-1122-778")+"</div>"):(e.renderTpl(t),e.model.parseQuota(t.quotaData,e.renderQuotaTable,e),t.buyRecodeData&&t.buyRecodeData.length&&e.model.parseBuyRecode(t.buyRecodeData,e.renderBuyRecodeTable,e))})},showTip:function(t,e){e?i.remind(e,t||$t("操作成功")):i.alert(t||$t("操作失败请稍后尝试或联系纷享客服"))},renderTpl:function(t){var e=t;_.isObject(t)&&(e=this.contentTpl(_.extend({costdoc:"/",setAccountUrl:"#crm/internalsigncertifyobj"},t))),this.$el.html(this.template({content:e}))},renderQuotaTable:function(t){var e=this,a=[{title:$t("类型"),data:"type",render:function(t){return 1==t?$t("个人"):$t("企业")}},{title:$t("剩余配额"),data:"remained"},{title:$t("已用配额"),data:"used"},{title:$t("购买配额"),data:"buy"},{title:$t("购买金额"),data:"payCost",render:function(t){t=+t;return _.isNaN(t)?"--":(t/=100).toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g,"$&,")}}],a=(e.quotaTable&&e.quotaTable.destroy(),new n({$el:e.$(".esign-quota_table"),className:"crm-table crm-table-open",showMultiple:!1,openStart:!0,doStatic:!0,showPage:!1,columns:a}));a.on("buyquote",function(t){t.attr("data-type")}),a.render(),a.doStaticData(t),e.quotaTable=a},renderBuyRecodeTable:function(t){var e=this,a=[{title:$t("类型"),data:"type",render:function(t){return 1==t?$t("个人"):$t("企业")}},{title:$t("购买配额"),data:"buy"},{title:$t("购买金额"),data:"payCost",render:function(t){t=+t;return _.isNaN(t)?"--":(t/=100).toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g,"$&,")}},{title:$t("操作人"),data:"operator"},{title:$t("充值时间"),data:"buyDate",dataType:4}],a=(e.buyRecodeTable&&e.buyRecodeTable.destroy(),new n({$el:e.$(".esign-buyrecode_table"),className:"crm-table crm-table-open",showMultiple:!1,openStart:!0,doStatic:!0,showPage:!1,columns:a}));a.render(),a.doStaticData(t),e.buyRecodeTable=a},updateStatus:function(t){var e=this.$(".esign-status"),a=e.find(".esign-switch");t?(e.addClass("on"),a.addClass("on")):(e.removeClass("on"),a.removeClass("on"))},esgininitHandle:function(t){var e=this,t=$(t.target);e.model.esignInit(function(t){2==t.initStatus?(e.showTip($t("初始化成功"),1),e.render()):e.showTip(t.message||$t("初始化失败请稍后尝试或联系纷享客服"))},t)},switchStatusHandle:function(t){var e=this;$(t.currentTarget).hasClass("on")?e.model.setStatus(1,2,function(t){1==t.status?(e.model.set("switchStatus",2),e.updateStatus(!1),i.remind(1,$t("设置成功"))):i.remind(t.message||$t("设置失败请稍后尝试或联系纷享客服"))}):e.model.setStatus(1,1,function(t){1==t.status?(e.model.set("switchStatus",1),e.updateStatus(!0),i.remind(1,$t("设置成功"))):i.remind(t.message||$t("设置失败请稍后尝试或联系纷享客服"))})},switchAlarmHandle:function(t){1!==this.model.get("switchStatus")?i.alert($t("请先开启电子签章")):($(t.currentTarget).hasClass("on")?(this.model.set("remainderAlarm",2),this.updateAlarmStatus(!1)):(this.model.set("switchStatus",1),this.updateAlarmStatus(!0)),i.remind(1,$t("设置成功")))},setAlarmHandle:function(t){t=$(t.target);t.hasClass("disabled")||t.closest(".esign-edit").addClass("editing")},setAlarmConfirmHandle:function(t){var t=$(t.target).closest(".esign-edit"),e=t.find("input"),a=+t.attr("data-type"),n=e.val();n?(n=+n,1==a?this.model.set("individualAlarmNum",n):this.model.set("enterpriseAlarmNum",n),i.remind(1,$t("设置成功")),e.siblings("b").text(n),t.removeClass("editing")):i.alert($t("请先设置配额数量"))},setAlarmCancelHandle:function(t){$(t.target).closest(".esign-edit").removeClass("editing")},showAllBuyRecodeHandle:function(t){t.stopPropagation();var e=this;a.async("./recode/recode",function(t){e.recode||(e.recode=new t({})),e.recode.show()})},setGroupHandle:function(){var e=this;a.async("./groupsdialog/groupsdialog",function(t){(new t).show(e.model)})},destroy:function(){var e=this;_.each(["quotaTable","buyRecodeTable","recode"],function(t){e[t]&&e[t].destroy&&e[t].destroy(),e[t]=null})}})});
define("crm-setting/esignature/groupsdialog/groupsdialog",["crm-widget/dialog/dialog","vue-selector-input","app-common-modules/icSelector/filter","./tpl-html"],function(t,e,n){var s=t("crm-widget/dialog/dialog"),r=t("vue-selector-input"),o=t("app-common-modules/icSelector/filter"),i=t("./tpl-html"),a=FS.crmUtil,l=s.extend({attrs:{className:"crm-d-esign-group",width:760,height:510,title:$t("使用部门设置"),showScroll:!0,showBtns:!0,btnName:{save:$t("保存")}},render:function(){var n=this,t=l.superclass.render.call(this);return this.on("dialogCancel",function(t){n.hide()}),this.on("dialogEnter",function(t){var e;n._canSubmmit&&(e=n.getValue(),n._validGroups(e.groups))&&(n._canSubmmit=!1,n.model.setTenantGroups({settingMap:e.data},function(t){1==t.status?(a.remind(1,$t("设置成功")),n.hide()):a.alert(t.message||$t("操作失败请稍后尝试或联系纷享客服"))},function(){n._canSubmmit=!0}))}),t},show:function(t){var e=this;this.model=t,l.superclass.show.call(e);e.showLoading(),this._canSubmmit=!1,t.getTenantSettings(function(t){t=e.parseData(t.signAccountDataList||[]);e.hideLoading(),e.$(".dialog-con").html(i({accounts:t})),e.renderSelectors(t),e._canSubmmit=!0})},hide:function(){var t=l.superclass.hide.call(this);return this.destroy(),t},parseData:function(t){var e=[];return _.each(t,function(t){e.push({id:t.internalSignCertifyId,name:t.enterpriseName,group:t.internalSignCertifyUseRange&&t.internalSignCertifyUseRange.departmentIds||[]})}),this._list=e},renderSelectors:function(s){var i=this,t=$(".j-esgin-selector",i.element);this._destroySelf(),this.selectors=[],t.each(function(t){var e,t=s[t].group||[],n={el:$(this),parentNode:$(this),group:{company:!0},label:$t("选择使用部门"),zIndex:9,defaultSelectedItems:{group:t}},t=o.getFilterGroup(t),t=(t.length&&(n.tabs=[{id:"stopgroup",hidden:!0,type:"list",data:t}],e=[],_.each(t,function(t){e.push(t.id)}),n.defaultSelectedItems.stopgroup=e),new r(n));i.selectors.push(t)})},getValue:function(){var s=this._list||[],i={},r=[];return _.each(this.selectors,function(t,e){var t=t.getValue(),n=t.group;t.stopgroup&&(n=n.concat(t.stopgroup)),_.each(n,function(t,e){n[e]=t+""}),i[s[e].id]=n.length?n:null,r=r.concat(n)}),{data:i,groups:r}},_validGroups:function(t){return _.uniq(t).length===t.length||(a.alert($t("一个部门只能设置在一个实名账户中")),!1)},_destroySelf:function(){_.each(this.selectors,function(t){t.destroy()}),this.selectors=null},destroy:function(){return this._destroySelf(),this.model=null,l.superclass.destroy.call(this)}});n.exports=l});
define("crm-setting/esignature/groupsdialog/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="esign-groups"> <table class="esign-object_table"> <thead> <tr> <th width="48px"><div class="tb-cell">' + ((__t = $t("序号")) == null ? "" : __t) + '</div></th> <th width="190px"><div class="tb-cell">' + ((__t = $t("实名账户")) == null ? "" : __t) + '</div></th> <th><div class="tb-cell">' + ((__t = $t("使用部门")) == null ? "" : __t) + "</div></th> </tr> </thead> <tbody> ";
            _.each(obj.accounts, function(account, index) {
                __p += ' <tr> <td><div class="tb-cell">' + ((__t = index + 1) == null ? "" : __t) + '</div></td> <td><div class="tb-cell">' + __e(account.name) + '</div></td> <td> <div class="tb-cell"> <div class="j-esgin-selector"></div> </div> </td> </tr> ';
            });
            __p += " </tbody> ";
            if (!obj.accounts || obj.accounts.length === 0) {
                __p += ' <tfoot> <tr> <td colspan="3"> <div class="no-data">' + ((__t = $t("暂无数据")) == null ? "" : __t) + "</div> </td> </tr> </tfoot> ";
            }
            __p += " </table> </div>";
        }
        return __p;
    };
});
define("crm-setting/esignature/model/model",[],function(e,t,a){var n=FS.crmUtil;a.exports=Backbone.Model.extend({defaults:{canAccess:1,switchStatus:2,remainderAlarm:2,individualAlarmNum:0,enterpriseAlarmNum:0,quotaData:null,buyRecodeData:null,isLockView:!1},parseQuota:function(e,t,a){var n=[];_.each(e,function(e){n.push({type:e.quotaType,remained:e.remainedQuota,used:e.usedQuota,buy:e.buyQuota,payCost:e.payMoney})}),t&&t.call(a||this,n)},parseBuyRecode:function(e,t,a){var n=[];_.each(e,function(e){n.push({type:e.quotaType,operator:e.operatorName,buy:e.buyQuota,buyDate:e.buyTime,payCost:e.payMoney})}),t&&t.call(a||this,n)},fetchInfo:function(t){var a=this;return n.FHHApi({url:"/EM1HNCRM/API/v1/object/elec_sign/service/get_tenant_elec_sign_info",type:"post",success:function(e){0===e.Result.StatusCode&&(t&&t(e.Value),1!=(e=e.Value).isGrayed?(a.set({canAccess:2}),t&&t(a.toJSON())):(e.remainedQuotaAlarm||(e.remainedQuotaAlarm={}),a.set({canAccess:+e.isGrayed,switchStatus:+e.tenantElecSignSwitch||2,remainderAlarm:+e.remainderAlarmSwitch||2,individualAlarmNum:+e.remainedQuotaAlarm.individualAlarmNum||0,enterpriseAlarmNum:+e.remainedQuotaAlarm.enterpriseAlarmNum||0,quotaData:e.tenantQuotas,buyRecodeData:e.buyRecords}),t&&t(_.extend(e,a.toJSON()))))}},{errorAlertModel:2})},esignInit:function(t,e){var a={errorAlertModel:2};return e&&(a.submitSelector=e),n.FHHApi({url:"/EM1HNCRM/API/v1/object/elec_sign/service/init_elec_sign",type:"post",success:function(e){0===e.Result.StatusCode&&t&&t(e.Value)}},a)},setStatus:function(e,t,a){return n.FHHApi({url:"/EM1HNCRM/API/v1/object/elec_sign/service/enable_or_disable_tenant_switch",type:"post",data:{switchType:e,status:t},success:function(e){0===e.Result.StatusCode&&a&&a(e.Value)}},{errorAlertModel:2})},getTenantSettings:function(t){return n.FHHApi({url:"/EM1HNCRM/API/v1/object/elec_sign_internal_sign_certify_use_range/service/get_internal_sign_certify_use_range_setting_list",type:"post",success:function(e){0===e.Result.StatusCode&&t&&t(e.Value)}},{errorAlertModel:2})},setTenantGroups:function(e,t,a){return n.FHHApi({url:"/EM1HNCRM/API/v1/object/elec_sign_internal_sign_certify_use_range/service/set_use_range",type:"post",data:e,success:function(e){0===e.Result.StatusCode&&t&&t(e.Value)},complete:function(){a&&a()}},{errorAlertModel:2})}})});
define("crm-setting/esignature/recode/recode",["crm-widget/table/table","crm-modules/common/slide/slide","../model/model","./select-html","./tpl-html"],function(e,t,s){var a=e("crm-widget/table/table"),i=e("crm-modules/common/slide/slide"),l=e("../model/model"),n=e("./select-html");s.exports=Backbone.View.extend({options:{className:"crm-nd-esignature",top:61,width:665,showMask:!1,zIndex:500,entry:"crm",recodetype:""},template:e("./tpl-html"),events:{"click .j-btn-colse":"hide","click .esign-select_list li":"selectChangeHandle"},initialize:function(){this.model=new l,this._setElement()},get:function(e){return this.options&&this.options[e]},showLoading:function(){this.$el.html('<div style="position:absolute;top:50%;margin-top:-50px;" class="crm-loading"></div>')},render:function(){var t=this;this.showLoading(),t.getRecodeTypes(function(e){t.$el.html(t.template({title:$t("购买记录")})),t.addSelect({label:$t("类型："),filter:"recodetype",options:e,defaultValue:t.get("recodetype")}),t.renderTable()})},show:function(){this.slide.show(),this.render()},hide:function(){this.slide.hide()},renderTable:function(){var t=this;t.fetchColumns(function(e){t.table&&t.table.destroy(),t.table=new a({$el:t.$(".esign_table"),showMultiple:!1,columns:e,showPage:!0,url:"/EM1HNCRM/API/v1/object/elec_sign_buy_record/service/query_by_page",requestType:"FHHApi",method:"post",postData:{quotaType:null},paramFormat:function(e){return e.currentPage=e.pageNumber,delete e.pageNumber,e},formatData:function(e){return{totalCount:e.pager.recordSize,data:e.pager.data}}})})},getRecodeTypes:function(e){this._recodeTypes=[{name:$t("全部"),value:""},{name:$t("个人"),value:"1"},{name:$t("企业"),value:"2"}],e&&e(this._recodeTypes)},fetchColumns:function(e){var t=[{title:$t("类型"),data:"quotaType",render:function(e){return 1==e?$t("个人"):$t("企业")}},{title:$t("购买配额"),data:"buyQuota"},{title:$t("购买金额"),data:"payMoney",render:function(e){e=+e;return _.isNaN(e)?"--":(e/=100).toFixed(2).replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g,"$&,")}},{title:$t("操作人"),data:"operatorName"},{title:$t("充值时间"),data:"buyTime",dataType:4}];e&&e(t)},addSelect:function(e){e=_.extend({$target:this.$(".esign-filters"),pos:"after",className:"",label:"",width:135,options:[],defaultValue:""},e);var t=_.findWhere(e.options,{value:e.defaultValue}),t=(e.defaultName=t.name,$(n(e)));"after"==e.pos?e.$target.append(t):e.$target.prepend(t)},selectChangeHandle:function(e){e=$(e.target);e.hasClass("select")||(e.addClass("select").siblings(".select").removeClass("select"),e.closest(".esign-select").find(".esign-select_tit").text(e.text()),this.updateType(e.attr("data-value")))},updateType:function(e){this.table&&this.table.setParam({quotaType:+e||null},!0,!0)},_setElement:function(){var e=this,t=this.options,s=new i({theme:t.theme||"gray",top:t.top||61,width:t.width,showMask:t.showMask,zIndex:t.zIndex,entry:t.entry});s.on("hide",function(){e.trigger("hide")}),this.slide=s,this.setElement(s.$el),this.$el.addClass(t.className)},destroy:function(){this.remove(),this.slide&&this.slide.destroy(),this.table&&this.table.destroy(),this.slide=this.table=null}})});
define("crm-setting/esignature/recode/select-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="esign-filter ' + ((__t = obj.className) == null ? "" : __t) + '"> <span class="esign-filter_tit">' + __e(obj.label) + '</span> <div class="esign-filter_con esign-select" style="width:' + ((__t = obj.width) == null ? "" : __t) + 'px;"> <div class="esign-select_tit" data-filter="' + ((__t = obj.filter) == null ? "" : __t) + '">' + __e(obj.defaultName) + '</div> <ul class="esign-select_list"> ';
            _.each(obj.options, function(option) {
                __p += ' <li class="' + ((__t = obj.defaultValue === option.value ? "select" : "") == null ? "" : __t) + '" data-value="' + __e(option.value) + '">' + __e(option.name) + "</li> ";
            });
            __p += " </ul> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/esignature/recode/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="esign_hd"> <h3>' + __e(obj.title) + '</h3> <i class="esign-icon_close j-btn-colse"></i> </div> <div class="esign_bd"> <div class="esign-filters"> </div> <div class="esign_table"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/esignature/template/content-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>1." + ((__t = $t("crm.电子签章使用第三方说明")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.电子签章费用说明")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.电子签章认证须知")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("crm.电子签章必须实名认证说明")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("crm.电子签章业务说明")) == null ? "" : __t) + '</li> </ul> </div> <div class="esign-content"> ';
            if (obj.initStatus != 2) {
                __p += ' <div class="esign-info esign-init"> <div class="esign-info_label" style="line-height: 28px;">' + ((__t = $t("电子签章：")) == null ? "" : __t) + '</div> <div class="esign-info_bd"> <a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary j-esigninit">' + ((__t = $t("开始初始化")) == null ? "" : __t) + "</a> </div> </div> ";
            } else {
                __p += ' <div class="esign-info esign-account"> <div class="esign-info_label">' + ((__t = $t("企业实名账户：")) == null ? "" : __t) + '</div> <div class="esign-info_bd"> <div> <a href="' + ((__t = obj.setAccountUrl) == null ? "" : __t) + '" target="_blank">' + ((__t = $t("申请认证")) == null ? "" : __t) + '</a> <a class="j-set-group" href="javascript:;" style="margin-left: 30px;">' + ((__t = $t("使用部门设置")) == null ? "" : __t) + '</a> </div> <p class="esign-tip">' + ((__t = $t("设置本企业的实名账户，这是一步必须步骤。")) == null ? "" : __t) + '</p> </div> </div> <div class="esign-info esign-status ' + ((__t = obj.switchStatus === 1 ? "on" : "") == null ? "" : __t) + '"> <div class="esign-info_label" style="line-height: 24px;">' + ((__t = $t("电子签章：")) == null ? "" : __t) + '</div> <div class="esign-info_bd"> <i class="j-esignature esign-switch ' + ((__t = obj.switchStatus === 1 ? "on" : "") == null ? "" : __t) + '"></i> <p class="esign-tip">' + ((__t = $t("关闭后电子签章业务不可用，但不影响其他业务的正常使用。")) == null ? "" : __t) + "</p> </div> </div> ";
                if (false) {
                    __p += ' <div class="esign-info esign-alarm"> <div class="esign-info_label">' + ((__t = $t("余额告警")) == null ? "" : __t) + '</div> <div class="esign-info_bd"> <i class="j-alarm esign-switch ' + ((__t = obj.remainderAlarm === 1 ? "on" : "") == null ? "" : __t) + '"></i> <p class="esign-tip esign-edit" data-type="1">' + ((__t = $t("个人用户配额低于")) == null ? "" : __t) + '<span class="esign-edit_text"> <b>' + __e(obj.individualAlarmNum) + '</b> <input type="number" value="' + __e(obj.individualAlarmNum) + '"> </span>' + ((__t = $t("时提醒")) == null ? "" : __t) + '<span class="esign-edit_actions"> <a href="javascript:;" class="esign-edit-setalarm ' + ((__t = obj.remainderAlarm === 1 ? "" : "disabled") == null ? "" : __t) + '">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> <a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary esign-edit-setalarm_confirm">' + ((__t = $t("确认")) == null ? "" : __t) + '</a> <a href="javascript:;" class="crm-btn crm-btn-sm esign-edit-setalarm_cancel">' + ((__t = $t("取 消")) == null ? "" : __t) + '</a> </span> </p> <p class="esign-tip esign-edit" data-type="2">' + ((__t = $t("企业用户配额低于")) == null ? "" : __t) + '<span class="esign-edit_text"> <b>' + __e(obj.enterpriseAlarmNum) + '</b> <input type="number" value="' + __e(obj.enterpriseAlarmNum) + '"> </span>' + ((__t = $t("时提醒")) == null ? "" : __t) + '<span class="esign-edit_actions"> <a href="javascript:;" class="esign-edit-setalarm ' + ((__t = obj.remainderAlarm === 1 ? "" : "disabled") == null ? "" : __t) + '">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> <a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary esign-edit-setalarm_confirm">' + ((__t = $t("确认")) == null ? "" : __t) + '</a> <a href="javascript:;" class="crm-btn crm-btn-sm esign-edit-setalarm_cancel">' + ((__t = $t("取 消")) == null ? "" : __t) + "</a> </span> </p> </div> </div> ";
                }
                __p += ' <div class="esign-info esign-quota"> <div class="esign-info_label">' + ((__t = $t("业务配额：")) == null ? "" : __t) + '</div> <div class="esign-info_bd"> <div class="esign-quota_table"></div> </div> </div> <div class="esign-info esign-buyrecode"> <div class="esign-info_label">' + ((__t = $t("最近购买配额：")) == null ? "" : __t) + '</div> <div class="esign-info_bd"> ';
                if (obj.buyRecodeData && obj.buyRecodeData.length) {
                    __p += ' <div class="esign-buyrecode_table"></div> <div class="esign-buyrecode_more"> <a href="javascript:;" class="j-show-buyrecode">' + ((__t = $t("查看所有记录")) == null ? "" : __t) + "</a> </div> ";
                } else {
                    __p += ' <p class="esign-tip" style="margin-top: 0;">' + ((__t = $t("暂无购买记录")) == null ? "" : __t) + "</p> ";
                }
                __p += " </div> </div> ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/esignature/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("电子签章")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con crm-scroll"> ' + ((__t = obj.content) == null ? "" : __t) + " </div>";
        }
        return __p;
    };
});