define("crm-setting/saleaction2/api",["crm-modules/common/util"],function(e,n,t){var o=e("crm-modules/common/util");function i(e,n){return o.FHHApi({url:e.url,data:e.data,success:function(e){0===e.Result.StatusCode&&n&&n(e.Value)}})}t.exports={list:function(e,n){return i({url:"/EM1HSTAGE/Definition/GetDefinitionList",data:{name:e.name,enable:e.enable,page:e.page,pageSize:e.pageSize}},n)},enable:function(e,n){return i({url:"/EM1HSTAGE/Definition/Enable",data:{enable:e.enable,sourceWorkflowId:e.flowid}},n)},delete:function(e,n){return i({url:"/EM1HSTAGE/Definition/Delete",data:{sourceWorkflowId:e}},n)},quota:function(e){return i({url:"/EM1HSTAGE/Tenant/GetQuota"},e)},get:function(e,n){return i({url:"/EM1HSTAGE/Definition/GetBySourceWorkflowId",data:{sourceWorkflowId:e}},n)}}});
define("crm-setting/saleaction2/approve/approve-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-page-config"> <div class="config-li all-stage"> <div style="color: #545861;margin-bottom: 16px"> ' + ((__t = $t("修改阶段变更触发审批类型，对所有阶段推进器定义、实例都生效")) == null ? "" : __t) + ' </div> <div> <span data-value="STAGE_CHANGE" class="config-radio" >' + ((__t = $t("阶段变更审批")) == null ? "" : __t) + '</span> <span data-value="EDIT" class="config-radio ">' + ((__t = $t("编辑审批")) == null ? "" : __t) + '</span><span id="fortooltips"></span> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/saleaction2/approve/approve",["crm-widget/dialog/dialog","./approve-html"],function(t,e,a){var i=CRM.util,n=t("crm-widget/dialog/dialog"),s=t("./approve-html"),o=n.extend({attrs:{width:510,title:$t("阶段变更触发审批类型设置"),showBtns:!0,showScroll:!1,className:"crm-d-page-config setting-saleaction2"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","click .config-radio":"triggerRadio"},initialize:function(){return this.attrs=$.extend(!0,{},o.prototype.attrs,this.attrs),o.superclass.initialize.apply(this,arguments)},show:function(t){var e=o.superclass.show.call(this);return this.getPageConfig(),e},triggerRadio:function(t){t&&t.stopPropagation();var e=this,t=$(t.target).closest(".config-radio");t.hasClass("readOnly")||(t.hasClass("active")||(t.siblings().removeClass("active"),t.addClass("active"),e.changed=!e.changed),e.setting=t.data("value"))},getPageConfig:function(){var a=this;a.element.find(".dialog-con").html(s()),new Vue({template:' <fx-tooltip effect="dark" :content="text" placement="top-start">\n                            <span class="setting-tooltip">?</span>\n                           </fx-tooltip>',data:{text:$t("切换为「编辑审批」时，请先定义对象的编辑触发审批流程。")}}).$mount("#fortooltips"),i.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"stage",types:["stageChangeApproval"],terminal:"ALL"},success:function(t){0==t.Result.StatusCode&&t.Value.values&&(a.setting=t.Value.values.stageChangeApproval,a.changed=!1,a.editables=t.Value.editables&&t.Value.editables.stageChangeApproval,!1===a.editables&&$(".config-radio",a.$el).each(function(t,e){$(e).data("value")!==a.setting&&$(e).addClass("readOnly")}),$(".config-radio",a.$el).each(function(t,e){$(e).data("value")===a.setting&&$(e).addClass("active")}))}},{errorAlertModel:1})},hide:function(){return o.superclass.hide.call(this)},confirmHandle:function(){var e=this;e.changed?FxUI.MessageBox.confirm("EDIT"===e.setting?$t("flow.stage.changeApproval.confirm_content","切换至「编辑审批」后， 不支持再修改为 「阶段变更审批」，是否确定修改？"):$t("flow.stage.changeApproval.confirm_content2",{isEdit:"EDIT"===e.setting?"编辑审批":"阶段变更审批",isChange:"EDIT"===e.setting?"阶段变更审批":"编辑审批"},"确定要将「{{isChange}}」修改为「{{isEdit}}」吗?"),$t("提示")).then(function(){i.FHHApi({url:"/EM1AFLOW/Config/Save",data:{flowType:"stage",flowConfigs:[{type:"stageChangeApproval",value:e.setting,terminal:"ALL"}]},success:function(t){0==t.Result.StatusCode&&(FxUI.Message.success($t("成功")),e.hide())}},{errorAlertModel:1})}).catch(function(){}):e.hide()},cancelHandle:function(){this.hide()},destroy:function(){n.prototype.destroy.call(this)}});a.exports=o});
define("crm-setting/saleaction2/feature/feature-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<ul class="crm-rocket-feature"> <li class="feature_1"> <div class="feature_1img"></div> <span>' + __e($t("第一步：维护基础信息")) + '</span> <span class="feature-content">' + __e($t("填写流程名称、流程描述、适用范围、选择阶段")) + '</span> </li> <li class="feature-ground"></li> <li class="feature_2"> <div class="feature_2img"></div> <span class="feature-content1">' + __e($t("第二步：维护阶段/任务")) + '</span> <span class="feature-content">' + __e($t('设置：阶段的"完成条件"和"审批配置" 以及任务的"基础信息"和"完成条件"')) + "</span> </li> </ul>";
        }
        return __p;
    };
});
define("crm-setting/saleaction2/feature/feature",["base-modules/dialog/dialog","./feature-html"],function(e,t,i){var a=e("base-modules/dialog/dialog"),o=e("./feature-html"),e=Backbone.View.extend({initialize:function(){var e=this;this.featureView=o({}),this.featureDialog=new a({title:$t("阶段推进器说明"),content:this.featureView,btns:[{label:$t("我知道了"),action:"iknow",type:"default"}],width:712,zIndex:999}),this.featureDialog.show(),this.featureDialog.on("hide",function(){e.trigger("featureClose"),e.featureDialog.destroy()}),this.featureDialog.on("iknow",function(){e.trigger("featureClose"),e.featureDialog.hide(),e.featureDialog.destroy()})}});i.exports=e});
define("crm-setting/saleaction2/linkApp/linkApp-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-page-config"> <div class="config-li all-stage"> <div style="color: #545861;margin-bottom: 16px"> ' + ((__t = $t("下游发起时，仅匹配该应用下启用的互联流程")) == null ? "" : __t) + '<span id="fortooltips"></span> </div> <div> <span data-value="true" class="config-radio" >' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span data-value="false" class="config-radio ">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/saleaction2/linkApp/linkApp",["crm-widget/dialog/dialog","./linkApp-html"],function(t,e,i){var a=CRM.util,n=t("crm-widget/dialog/dialog"),s=t("./linkApp-html"),o=n.extend({attrs:{width:510,title:$t("互联流程设置"),showBtns:!0,showScroll:!1,className:"crm-d-page-config setting-saleaction2"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","click .config-radio":"triggerRadio"},initialize:function(){return this.attrs=$.extend(!0,{},o.prototype.attrs,this.attrs),o.superclass.initialize.apply(this,arguments)},show:function(t){var e=o.superclass.show.call(this);return this.getPageConfig(),e},triggerRadio:function(t){t&&t.stopPropagation();var e=this,t=$(t.target).closest(".config-radio");t.hasClass("active")||(t.siblings().removeClass("active"),t.addClass("active"),e.changed=!e.changed),e.setting=t.data("value")},getPageConfig:function(){var i=this;i.element.find(".dialog-con").html(s()),new Vue({template:' <fx-tooltip effect="dark" :content="text" placement="top-start">\n                            <span class="setting-tooltip">?</span>\n                           </fx-tooltip>',data:{text:$t("选择「是」，下游发起流程时，只能使用为当前「互联应用」创建的阶段推进器；选择「否」，下游发起流程时，可以使用与该对象相关的所有阶段推进器")}}).$mount("#fortooltips"),a.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"stage",type:"outerUserTriggerScopeFilterLinkApp",terminal:"ALL"},success:function(t){0==t.Result.StatusCode&&(i.setting=!!t.Value.value,i.changed=!1,$(".config-radio",i.$el).each(function(t,e){$(e).data("value")===i.setting&&$(e).addClass("active")}))}},{errorAlertModel:2})},hide:function(){return o.superclass.hide.call(this)},confirmHandle:function(){var e=this;e.changed?a.FHHApi({url:"/EM1AFLOW/Config/Save",data:{flowType:"stage",flowConfigs:[{type:"outerUserTriggerScopeFilterLinkApp",value:e.setting,terminal:"ALL"}]},success:function(t){0==t.Result.StatusCode&&(FxUI.Message.success($t("成功")),e.hide())}},{errorAlertModel:2}):e.hide()},cancelHandle:function(){this.hide()},destroy:function(){n.prototype.destroy.call(this)}});i.exports=o});
define("crm-setting/saleaction2/saleaction2",[],function(e,n,t){var a=Backbone.View.extend({initialize:function(e){var n=this,e=(n.setElement(e.wrapper),n.$el.addClass("crm-stage-table"),document.createElement("div"));e.className="stage-manage",n.$el[0].appendChild(e)},render:function(){var t=this;e.async(["paas-paasui/vui","paas-rocket/secondDev"],function(e,n){n.getComponent("manage").then(function(n){t.inst=new Vue({el:t.$el.children(".stage-manage")[0],render:function(e){return e(n)}})})})},destroy:function(){this.inst&&this.inst.$destroy()}});t.exports=a});
define("crm-setting/saleaction2/setting/setting-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-page-config"> <div class="config-li stage-scope"> <div>' + ((__t = $t("阶段定义查看范围")) == null ? "" : __t) + '</div> <div> <span class="config-radio see-all active" >' + ((__t = $t("查看全部")) == null ? "" : __t) + '</span> <span class="config-radio see-scope">' + ((__t = $t("查看适用范围内")) == null ? "" : __t) + '</span> </div> </div> <div class="config-li all-stage"> <div>' + ((__t = $t("是否支持展示全部阶段")) == null ? "" : __t) + '</div> <div> <span class="config-radio true active" >' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio false">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/saleaction2/setting/setting",["crm-widget/dialog/dialog","./setting-html"],function(e,t,i){var s=CRM.util,a=e("crm-widget/dialog/dialog"),n=e("./setting-html"),l=a.extend({attrs:{width:510,title:$t("阶段视图查看设置"),showBtns:!0,showScroll:!1,className:"crm-d-page-config setting-saleaction2"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","click .config-radio":"triggerRadio"},initialize:function(){return this.attrs=$.extend(!0,{},l.prototype.attrs,this.attrs),l.superclass.initialize.apply(this,arguments)},show:function(e){var t=l.superclass.show.call(this);return this.getPageConfig(),t},triggerRadio:function(e){e&&e.stopPropagation();var t=$(e.target).closest(".config-radio"),e=$(e.target).closest(".config-li");t.hasClass("active")||(t.siblings().removeClass("active"),t.addClass("active"),e.hasClass("stage-scope")?this.stageDefinitionViewScope=t.index()?"SCOPE":"ALL":e.hasClass("all-stage")&&(this.showAllStages=!t.index()))},getPageConfig:function(){var t=this;s.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"stage",types:["showAllStages","stageDefinitionViewScope"],terminal:"ALL"},success:function(e){0==e.Result.StatusCode&&e.Value.values&&(t.element.find(".dialog-con").html(n()),(e=e.Value.values).stageDefinitionViewScope&&(t.element.find(".stage-scope .config-radio").removeClass("active"),"ALL"==e.stageDefinitionViewScope?t.element.find(".stage-scope .see-all").addClass("active"):"SCOPE"==e.stageDefinitionViewScope&&t.element.find(".stage-scope .see-scope").addClass("active"),t.stageDefinitionViewScope=e.stageDefinitionViewScope),_.isBoolean(e.showAllStages))&&(t.element.find(".all-stage .config-radio").removeClass("active"),(e.showAllStages?t.element.find(".all-stage .true"):t.element.find(".all-stage .false")).addClass("active"),t.showAllStages=e.showAllStages)}},{errorAlertModel:1})},hide:function(){return l.superclass.hide.call(this)},confirmHandle:function(e){var t=this,i=_.map(["showAllStages","stageDefinitionViewScope"],function(e){return{type:e,value:t[e],terminal:"ALL"}});s.FHHApi({url:"/EM1AFLOW/Config/Save",data:{flowType:"stage",flowConfigs:i},success:function(e){0==e.Result.StatusCode&&(FxUI.Message.success($t("成功")),t.hide())}},{errorAlertModel:1})},cancelHandle:function(){this.hide()},destroy:function(){a.prototype.destroy.call(this)}});i.exports=l});
define("crm-setting/saleaction2/sort/sort",["crm-widget/dialog/dialog","crm-widget/select/select","./template/tpl-html","./template/list-html"],function(t,e,i){var s=CRM.util,r=t("crm-widget/dialog/dialog"),n=t("crm-widget/select/select"),a=t("./template/tpl-html"),o=t("./template/list-html"),l=r.extend({options:{objectType:200022},attrs:{width:1150,title:$t("排列优先级"),showBtns:!0,showScroll:!1,className:"crm-d-sort"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","dragstart tbody tr":"startHandle","dragend tbody tr":"endHandle","dragenter tbody tr":"enterHandle","dragover tbody tr":"overHandle","drop tbody tr":"dropHandle"},initialize:function(){return l.superclass.initialize.apply(this,arguments)},initSelect:function(t){var i=this;i._select&&(i._select.destroy(),i._select=null),i._select=new n({$wrap:$(".select-con",this.element),zIndex:+this.get("zIndex")+10,defaultValue:null,size:1,options:_.map(t,function(t){return{name:t.entityName,value:t.entityId}})}),this._select.on("change",function(t,e){i.renderList()})},getWorkFlowList:function(i){s.FHHApi({url:"/EM1HSTAGE/Definition/GetDefinitionList",data:{entityId:this._select.getValue(),supportPagingQuery:!1,enable:0},success:function(t){var e;0==t.Result.StatusCode?((e=t.Value||{}).result=_.sortBy(e.data||[],"priority"),i&&i(e)):s.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},createMask:function(){var t=$('<div  class="tr drag-mask" draggable="true"></div>'),e=(this.draging,this.draged),i=this.wrap.offset(),r=e.offset();this.mask&&this.mask.remove(),t.css({width:e.width(),height:e.height()-1,position:"absolute",left:r.left-i.left,top:r.top-i.top,background:"#ffffff"}),t.on({dragover:this.overHandle,drop:function(t){return t.stopPropagation(),t.preventDefault(),!1}}),this.mask=t,this.wrap.append(t)},startHandle:function(t){var e=t.originalEvent;e.dataTransfer&&e.dataTransfer.setData("text",""),this.draging=$(t.currentTarget),this.trigger("dragstart",e)},endHandle:function(t){t=t.originalEvent;return this.mask&&this.mask.remove(),t.dataTransfer.clearData("text"),!1},enterHandle:function(t){t=$(t.currentTarget);this.draging.attr("drag_id")!==t.attr("drag_id")&&(this.draged=t,this.createMask(),this.draging.index()>this.draged.index()?this.draged.before(this.draging):this.draged.after(this.draging),this.mask.css({top:this.draging.offset().top-this.wrap.offset().top+1}))},overHandle:function(t){var e=t.originalEvent;return e.dataTransfer&&(e.dataTransfer.dropEffect="move"),t.preventDefault(),!1},dropHandle:function(t){var e=t.originalEvent;e.dataTransfer&&e.dataTransfer.clearData(),t.preventDefault()},show:function(t){var e=this,i=l.superclass.show.call(this);return this.isRender||(this.setContent(a()),this.wrap=$("table",this.element),this.isRender=!0),e.roles=t.roles||[],e.groups=t.groups||[],e.initSelect(t.list),t.list&&t.list.length&&e.renderList(),i},renderList:function(){var e=this;this.getWorkFlowList(function(t){e.element.find("tbody").html(o(_.extend({},t,{util:s,getScope:e.getScope.bind(e)}))),e.resizedialog()})},getScope:function(t,e,i,r){var n=this,a=[];return _.each(t,function(t){t=s.getCircleById(t);a.push(t?t.name:"--")}),_.each(e,function(t){t=s.getEmployeeById(t);a.push(t?t.name:"--")}),_.each(i,function(t){t=_.findWhere(n.groups,{id:t});a.push(t?t.name:"--")}),_.each(r||[],function(t){t=_.findWhere(n.roles,{id:t});a.push(t?t.name:"--")}),a.length?a.join($t("，")):"--"},hide:function(){return l.superclass.hide.call(this)},setWorkFlowPriorities:function(t){var e=this;s.FHHApi({url:"/EM1HSTAGE/Definition/SetPriorities",data:{priorities:t},success:function(t){0==t.Result.StatusCode?(e.trigger("refresh","sort"),e.hide()):s.alert(t.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:$(".b-g-btn",this.element)})},confirmHandle:function(t){var i=[];$("tbody .tr",this.element).each(function(t,e){i.push({sourceWorkflowId:$(e).data("sourceworkflowid"),priority:t})}),i.length<2?this.hide():this.setWorkFlowPriorities(i)},cancelHandle:function(){this.hide()},destroy:function(){var e=this;_.each(["_select"],function(t){e[t]&&e[t].destroy&&(e[t].destroy(),e[t]=null)}),e.undelegateEvents()}});i.exports=l});
define("crm-setting/saleaction2/sort/template/list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(result, function(item, index) {
                __p += ' <tr draggable=\'true\' class="tr" data-sourceworkflowid="' + ((__t = item.sourceWorkflowId) == null ? "" : __t) + '" drag_id="' + ((__t = index) == null ? "" : __t) + '"> <td class="tr-name" title="' + ((__t = item.name || "--") == null ? "" : __t) + '"><span class="cursor"></span>' + ((__t = item.name || "--") == null ? "" : __t) + '</td> <td class="tr-entityName" title="' + ((__t = item.entityName || "--") == null ? "" : __t) + '">' + ((__t = item.entityName || "--") == null ? "" : __t) + '</td> <td class="tr-stageFieldDisplayName" title="' + ((__t = item.stageFieldDisplayName || "--") == null ? "" : __t) + '">' + ((__t = item.stageFieldDisplayName || "--") == null ? "" : __t) + " </td> ";
                var scope = getScope(item.rangeCircleIds, item.rangeEmployeeIds, item.rangeGroupIds, item.rangeRoleIds);
                __p += ' <td class="tr-scope" title="' + ((__t = scope) == null ? "" : __t) + '">' + ((__t = scope) == null ? "" : __t) + "</td> <td>" + ((__t = util.getEmployeeById(item.creator) ? util.getEmployeeById(item.creator).name : "--") == null ? "" : __t) + "</td> <td>" + ((__t = FS.moment(item.createTime).format("YYYY-MM-DD HH:mm:ss")) == null ? "" : __t) + "</td> <td>" + ((__t = util.getEmployeeById(item.modifier) ? util.getEmployeeById(item.modifier).name : "--") == null ? "" : __t) + "</td> <td>" + ((__t = FS.moment(item.modifyTime).format("YYYY-MM-DD HH:mm:ss")) == null ? "" : __t) + "</td> <td>" + ((__t = item.enable ? $t("启用") : $t("停用")) == null ? "" : __t) + "</td> </tr> ";
            });
        }
        return __p;
    };
});
define("crm-setting/saleaction2/sort/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="sort-wrap"> <div class="select-wrap clearfix"> <label class="select-label">' + ((__t = $t("对象：")) == null ? "" : __t) + '</label> <div class="select-con"></div> </div> <table class="tb"> <thead> <tr> <th class="th-name"> <span class="th-tit">' + ((__t = $t("阶段推进器名称")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("流程发起对象")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("对象字段")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("适用范围")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("创建人")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("创建时间")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("最后修改人")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("最后修改时间")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("状态")) == null ? "" : __t) + "</span> </th> </tr> </thead> <tbody> </tbody> </table> </div>";
        }
        return __p;
    };
});
define("crm-setting/saleaction2/stageIn/stageIn-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-page-config"> <div class="config-li stage-scope"> <div>' + ((__t = $t("是否忽略提示语，直接进入？")) == null ? "" : __t) + '</div> <div> <span class="config-radio see-all active" >' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio see-scope">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/saleaction2/stageIn/stageIn",["crm-widget/dialog/dialog","./stageIn-html"],function(e,t,i){var s=CRM.util,a=e("crm-widget/dialog/dialog"),n=e("./stageIn-html"),o=a.extend({attrs:{width:510,title:$t("进入阶段提示语配置"),showBtns:!0,showScroll:!1,className:"crm-d-page-config setting-saleaction2"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","click .config-radio":"triggerRadio"},initialize:function(){return this.attrs=$.extend(!0,{},o.prototype.attrs,this.attrs),o.superclass.initialize.apply(this,arguments)},show:function(e){var t=o.superclass.show.call(this);return this.getPageConfig(),t},triggerRadio:function(e){e&&e.stopPropagation();e=$(e.target).closest(".config-radio");e.hasClass("active")||(e.siblings().removeClass("active"),e.addClass("active"),this.showSkipInTip=!!e.index())},getPageConfig:function(){var t=this;s.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"stage",types:["showSkipInTip"],terminal:"ALL"},success:function(e){0==e.Result.StatusCode&&e.Value.values&&(t.element.find(".dialog-con").html(n()),e=e.Value.values,t.element.find(".stage-scope .config-radio").removeClass("active"),(e.showSkipInTip?t.element.find(".stage-scope .see-scope"):t.element.find(".stage-scope .see-all")).addClass("active"),t.showSkipInTip=e.showSkipInTip)}},{errorAlertModel:1})},hide:function(){return o.superclass.hide.call(this)},confirmHandle:function(e){var t=this,i=_.map(["showSkipInTip"],function(e){return{type:e,value:t[e],terminal:"ALL"}});s.FHHApi({url:"/EM1AFLOW/Config/Save",data:{flowType:"stage",flowConfigs:i},success:function(e){0==e.Result.StatusCode&&(FxUI.Message.success($t("成功")),t.hide())}},{errorAlertModel:1})},cancelHandle:function(){this.hide()},destroy:function(){a.prototype.destroy.call(this)}});i.exports=o});
define("crm-setting/saleaction2/task/task-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-page-config"> <div class="config-li"> <div class="title">' + ((__t = $t("数据详情-阶段任务内容区的展示顺序（不区分是否为当前处理人）")) == null ? "" : __t) + '</div> <div class="config-radio order-radio" >' + ((__t = $t("默认顺序")) == null ? "" : __t) + ' <span class="fortooltips"></span> </div> <div class="config-radio order-radio" >' + ((__t = $t("优先展示未完成任务")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/saleaction2/task/task",["crm-widget/dialog/dialog","./task-html"],function(t,e,a){var s=CRM.util,i=t("crm-widget/dialog/dialog"),o=t("./task-html");a.exports=i.extend({attrs:{title:$t("阶段任务展示顺序"),width:500,showBtns:!0,className:"crm-d-page-config setting-saleaction2"},events:{"click .order-radio":"radioHandleShow","click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"_closeHandle"},radioHandleShow:function(t){t&&t.stopPropagation();var e=$(t.target).closest(".order-radio"),a=this.element.find(".config-li .order-radio"),t=$(t.target).closest(".order-radio").index();e.hasClass("active")||(a.removeClass("active"),e.addClass("active"),this.showed=t-1)},confirmHandle:function(){var t=this,e={flowType:"stage",flowConfigs:[{type:"stageTaskDisplaySequence",value:0===t.showed?"default":"prioritizeShowUnfinishedTasks",terminal:"ALL"}]};s.FHHApi({url:"/EM1AFLOW/Config/Save",data:e,success:function(){t._closeHandle()}},{errorAlertModel:2})},show:function(){var e=this;return s.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"stage",type:"stageTaskDisplaySequence",terminal:"ALL"},success:function(t){t=t.Value.value;e.showed="default"===t?0:1,e.initBtnStatus()}},{errorAlertModel:2}),result=a.exports.superclass.show.call(this),e.setContent(o()),FxUI.create({wrapper:e.element.find(".config-li .fortooltips")[0],template:' <fx-tooltip effect="dark" :content="text" placement="top-start">\n                                <span class="setting-tooltip" >?</span>\n                               </fx-tooltip>',data:function(){return{text:$t("flow.stage.taskDisplaySequence.tooltip","按阶段任务的配置顺序")}}}),e.resizedialog(),result},initBtnStatus:function(){this.element.find(".config-li .order-radio")[this.showed].classList.add("active")},_closeHandle:function(){this.hide()},destroy:function(){return a.exports.superclass.destroy.call(this)}})});