define("crm-setting/opportunity/datemanage/datemanage",["crm-modules/common/util","../template/dateManage-html"],function(e,t,c){var n=e("crm-modules/common/util"),i=e("../template/dateManage-html"),o=0,e=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.tenant_id=CRM.enterpriseId,this.getConfig()},events:{"click .j-save":"setConfig","click .j-checkbox":"change"},render:function(){this.comps={},this.$el.html(i({})),this.renderCheckBox()},renderCheckBox:function(){this.comps.checkbox=FxUI.create({wrapper:this.$el.find(".j-checkbox")[0],template:"<fx-checkbox v-model=\"checked\" >{{$t('商机赢单时修改结单日期（预计成交日期）为赢单日期')}}</fx-checkbox>",data:function(){return{checked:!1}}}),this.comps.button=FxUI.create({wrapper:this.$el.find(".j-save")[0],template:'<fx-button type="primary" :disabled="disabled">{{$t(\'crm.form_save_btn\')}}</fx-button>',data:function(){return{disabled:!0}}})},getConfig:function(){var t=this;t._getAjax&&(t._getAjax.abort(),t._getAjax=null),t._getAjax=n.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"config_change_close_date_if_win"},success:function(e){t.show(),t.render(),0===e.Result.StatusCode?"1"==(o=e.Value.value)&&(t.comps.checkbox.checked=!0):n.alert(e.Result.FailureMessage)},complete:function(){t._getAjax=null}},{errorAlertModel:1})},setConfig:function(){var t=0,t=this.comps.checkbox.checked?1:0,e=o,c=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"config_change_close_date_if_win",oldValue:e,value:t},success:function(e){0==e.Result.StatusCode?(n.remind(1,$t("保存成功")),o=t,c.comps.button.disabled=!0):n.alert(e.Result.FailureMessage)},complete:function(){c.isSetting=!1}},{errorAlertModel:1})},change:function(){this.comps.button.disabled=!1},show:function(){this.$el.toggle(!0)},hide:function(){this.$el.toggle(!1)},destory:function(){for(var e in this.comps)this.comps[e].destory()}});c.exports=e});
define("crm-setting/opportunity/oppoleads/oppoleads",["crm-modules/common/util","../template/oppoleads-html"],function(e,t,a){var o=e("crm-modules/common/util"),i=e("../template/oppoleads-html");return Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){var t=this;t.$el.html(i()),this.getConfig().done(function(e){t.oppoLeads=e,t.renderRadioTpl(e)})},events:{"click .j-save":"saveHandle"},renderRadioTpl:function(e){var t=this;window.FxUI.create({wrapper:".leads-wrapper",template:'<fx-radio-group  v-model="value"><fx-radio v-for="data in oppoLeads" :key="data.value" :label="data.value" v-on:change="change(value)">{{data.label}}</fx-radio></fx-radio-group>',data:function(){return{value:e,oppoLeads:[{label:$t("自动关联第一个转客户且未转为商机的线索"),value:"1"},{label:$t("自动关联最近一个转客户且未转为商机的线索"),value:"2"},{label:$t("不自动关联线索"),value:"0"}]}},methods:{change:function(e){t.oppoLeads=e}}})},getConfig:function(){return new Promise(function(a,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_values",data:{isAllConfig:!1,keys:["new_opportuntiy_leads_setting"]},success:function(e){var t;0===e.Result.StatusCode?e.Value&&e.Value.values&&(t=(t=e.Value.values[0])&&t.value,a(t)):o.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},saveHandle:function(){this.oppoLeads?this.setConfig(this.oppoLeads):CRM.util.alert($t("请选择一项"))},setConfig:function(e){var t=this,a=null,o="0"==e?$t("确认不自动关联？"):$t("确认自动关联？"),a=CRM.util.confirm(o,"",function(){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"new_opportuntiy_leads_setting",value:e},success:function(e){0===e.Result.StatusCode?CRM.util.remind(1,$t("设置成功")):(t.render(),CRM.util.alert(e.Result.FailureMessage))},complete:function(){a.hide()}},{errorAlertModel:1})})},show:function(){this.render(),this.$el.show()},hide:function(){this.$el.hide()}})});
define("crm-setting/opportunity/opportunity",["crm-modules/common/util","./template/index-html"],function(o,e,a){var t=o("crm-modules/common/util"),n=o("./template/index-html"),i=Backbone.View.extend({options:{pageConfig:{page1:{path:"./datemanage/datemanage",el:".date-manage"},page2:{path:"crm-setting/ownership/ownership",el:".oppo-box"},page3:{path:"./oppoleads/oppoleads",el:".oppoleads-manage"},saleactionmanage:{path:"./saleactionmanage/saleactionmanage",el:".saleaction-manage"}}},initialize:function(e){this.setElement(e.wrapper),t.getFieldsByApiName("NewOpportunityObj",!0),t.getFieldsByApiName("PersonnelObj",!0),CRM.get("isOpenPRM")&&t.getFieldsByApiName("PartnerObj",!0)},events:{"click .crm-tab a":"onTab"},render:function(e){var a=this;a.$el.html(n({})),this.renderPage(e),CRM.api.get_licenses({key:"accounts_leads_limit_app",objectApiName:"NewOpportunityObj",cb:function(e){e.accounts_leads_limit_app||a.$el.find(".page2").hide()}})},onTab:function(e){var a=$(e.target).attr("href");e.preventDefault(),a=a.split("/=/"),this.renderPage([a[1]])},renderPage:function(e){var a=this,t=a.$(".crm-tab .item"),t=(t.removeClass("cur"),(e?t.filter("."+e[0]):t.eq(0)).addClass("cur"),a.page1&&a.page1.hide(),a.page2&&a.page2.hide(),a.page3&&a.page3.hide(),a.saleactionmanage&&a.saleactionmanage.hide(),e&&e[0]||"page1"),a=this.options.pageConfig[t];this.getPage(t,a.path,a.el)},getPageData:function(e){e&&e({})},getPage:function(t,e,n){var i=this;this.getPageData(function(a){i[t]?i[t].show():o.async(e,function(e){i[t]=new e(Object.assign({wrapper:n},a,{apiname:"NewOpportunityObj"})),i[t].show()})})},destory:function(){var t=this;["page1","page2","page3","saleactionmanage"].forEach(function(e,a){t[e]&&(t[e].destroy(),t[e]=null)})}});a.exports=i});
define("crm-setting/opportunity/saleactionmanage/saleactionmanage",["crm-modules/common/util","../template/saleactionManage-html"],function(e,t,a){var o=e("crm-modules/common/util"),n=e("../template/saleactionManage-html");return Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){var t=this;t.checkboxComp={},t.$el.html(n()),this.getConfig().done(function(e){t.checkboxComp.checked="1"==e,t.renderCheckBox(e)})},events:{"click .j-save":"saveHandle"},renderCheckBox:function(e){var t=this;this.checkboxComp=FxUI.create({wrapper:".saleactionmange-wrapper",template:"<fx-checkbox v-model=\"checked\" >{{$t('crm.opportunity.setting.saleaction.radio2')}}</fx-checkbox>",data:function(){return{checked:t.checkboxComp.checked}}})},renderRadioTpl:function(e){var t=this;window.FxUI.create({wrapper:".saleactionmange-wrapper",template:'<fx-radio-group is-vertical v-model="value"><fx-radio v-for="data in saleactionOptions" :key="data.value" :label="data.value" v-on:change="change(value)">{{data.label}}</fx-radio></fx-radio-group>',data:function(){return{value:e,saleactionOptions:[{label:$t("crm.opportunity.setting.saleaction.radio1"),value:"0"},{label:$t("crm.opportunity.setting.saleaction.radio2"),value:"1"}]}},methods:{change:function(e){t.saleactionValue=e}}})},getConfig:function(){return new Promise(function(a,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"NewOpportunityObj.add.sales_process.trigger.mode"},success:function(e){var t;0===e.Result.StatusCode?e.Value&&e.Value.value&&(t=e.Value.value,a(t)):o.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},saveHandle:function(){this.setConfig()},setConfig:function(e){var t=this;"0"==e?$t("确认不自动关联？"):$t("确认自动关联？");CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"NewOpportunityObj.add.sales_process.trigger.mode",value:t.checkboxComp.checked?"1":"0"},success:function(e){0===e.Result.StatusCode?CRM.util.remind(1,$t("设置成功")):(t.render(),CRM.util.alert(e.Result.FailureMessage))},complete:function(){}},{errorAlertModel:1})},show:function(){this.render(),this.$el.show()},hide:function(){this.$el.hide()}})});
define("crm-setting/opportunity/template/dateManage-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div style="line-height: 35px;"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li> 1、" + ((__t = $t("crm.opportunity.management.statement_date.tip")) == null ? "" : __t) + ' </li> </ul> </div> <div class="partner-allowmodify mn-checkbox-box"> ' + ((__t = $t("结单日期（预计成交日期）设置")) == null ? "" : __t) + ' </div> <div class="partner-allowmodify mn-checkbox-box"> <span class="j-checkbox"></span> </div> <div class="partner-allowmodify mn-checkbox-box j-button"> <span class="j-save"></span> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/opportunity/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-setting-newoppo"> <div class="crm-tit"> <h2> <span class="tit-txt ">' + ((__t = $t("商机2.0管理")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con"> <div class="crm-tab customer-rule-tab"> <a class="item page1" href="#crm/setting/customerrule/=/page1">' + ((__t = $t("结单日期设置")) == null ? "" : __t) + '</a> <a class="item page2" href="#crm/setting/customerrule/=/page2">' + ((__t = $t("商机保有量")) == null ? "" : __t) + '</a> <a class="item page3" href="#crm/setting/customerrule/=/page3">' + ((__t = $t("商机源线索设置")) == null ? "" : __t) + "</a> ";
            if (CRM.util.isGrayScale("CRM_NEWOPPORTUNITY_SETTING_SALEACTION")) {
                __p += ' <a class="item saleactionmanage" href="#crm/setting/customerrule/=/saleactionmanage">' + ((__t = $t("crm.opportunity.trigger.stage.advancer")) == null ? "" : __t) + "</a> ";
            }
            __p += ' </div> <div class="tab-con"> <div class="crm-p20 crm-scroll"> <div class="item date-manage crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item oppo-box crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item oppoleads-manage crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item saleaction-manage crm-p20" style="display:none;"> <div class="crm-loading"></div> </div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/opportunity/template/oppoleads-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-setting-oppoleads"> <div> <h3 class="title">' + ((__t = $t("商机归因")) == null ? "" : __t) + '</h3> <div class="leads-wrapper"></div> <div class="crm-btn crm-btn-primary j-save">' + ((__t = $t("crm.form_save_btn")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/opportunity/template/saleactionManage-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-setting-saleactionmange"> <div class="crm-intro" style="margin-bottom: 10px;"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <div>" + ((__t = $t("crm.opportunity.setting.saleaction.msg")) == null ? "" : __t) + "：</div> <ol> <li>1、" + ((__t = $t("crm.opportunity.setting.saleaction.msg.tip1")) == null ? "" : __t) + "</li> <!-- <li>2、" + ((__t = $t("crm.opportunity.setting.saleaction.msg.tip2")) == null ? "" : __t) + "</li> --> <li>2、" + ((__t = $t("crm.opportunity.setting.saleaction.msg.tip3")) == null ? "" : __t) + '</li> </ol> </div> <div> <div style="margin-bottom: 10px;"> <div class="saleactionmange-tip1" style="color: #181C25;font-size: 14px;">' + ((__t = $t("crm.opportunity.setting.saleaction.option1")) == null ? "" : __t) + '</div> <div class="saleactionmange-tip2" style="color: #91959E;font-size: 12px;">' + ((__t = $t("crm.opportunity.setting.saleaction.option2")) == null ? "" : __t) + '</div> </div> <div class="saleactionmange-wrapper" style="margin-bottom: 10px;"></div> <div class="crm-btn crm-btn-primary j-save">' + ((__t = $t("crm.form_save_btn")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});