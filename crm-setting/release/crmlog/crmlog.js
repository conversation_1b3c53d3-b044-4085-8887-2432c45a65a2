define("crm-setting/crmlog/config",[],function(e,t,m){m.exports={actionOpts_common:[{ItemName:$t("新建"),ItemCode:1},{ItemName:$t("编辑"),ItemCode:2},{ItemName:$t("作废"),ItemCode:3},{ItemName:$t("恢复"),ItemCode:4},{ItemName:$t("删除"),ItemCode:5},{ItemName:$t("更换负责人"),ItemCode:6},{ItemName:$t("添加相关团队"),ItemCode:23},{ItemName:$t("删除相关团队"),ItemCode:24},{ItemName:$t("编辑相关团队"),ItemCode:27},{ItemName:$t("导出"),ItemCode:36},{ItemName:$t("导入"),ItemCode:13}],actionOpts_special:[{ItemName:$t("新建对象"),ItemCode:"create_obj"},{ItemName:$t("新建字段"),ItemCode:"create_field"},{ItemName:$t("新建布局"),ItemCode:"create_layout"},{ItemName:$t("新建类型"),ItemCode:"create_record_type"},{ItemName:$t("编辑对象"),ItemCode:"update_obj"},{ItemName:$t("编辑字段"),ItemCode:"update_field"},{ItemName:$t("编辑布局"),ItemCode:"update_layout"},{ItemName:$t("编辑相关团队数据权限"),ItemCode:"update_related_team_data_permission"},{ItemName:$t("编辑类型"),ItemCode:"update_record_type"},{ItemName:$t("停用对象"),ItemCode:"disable_obj"},{ItemName:$t("停用字段"),ItemCode:"disable_field"},{ItemName:$t("停用布局"),ItemCode:"disable_layout"},{ItemName:$t("停用类型"),ItemCode:"disable_record_type"},{ItemName:$t("启用对象"),ItemCode:"enable_obj"},{ItemName:$t("启用字段"),ItemCode:"enable_field"},{ItemName:$t("启用布局"),ItemCode:"enable_layout"},{ItemName:$t("启用类型"),ItemCode:"enable_record_type"},{ItemName:$t("删除对象"),ItemCode:"delete_obj"},{ItemName:$t("删除字段"),ItemCode:"delete_field"},{ItemName:$t("删除布局"),ItemCode:"delete_layout"},{ItemName:$t("删除类型"),ItemCode:"delete_record_type"}],actionOpts_function:[{ItemName:$t("创建函数"),ItemCode:"create_function"},{ItemName:$t("更新函数"),ItemCode:"update_function"},{ItemName:$t("删除函数"),ItemCode:"delete_function"},{ItemName:$t("禁用函数"),ItemCode:"disable_function"},{ItemName:$t("启用函数"),ItemCode:"enable_function"}],options:[{isdef:!0,name:$t("线索日志"),id:1,typeName:"clue",eventName:"saleclue"},{name:$t("客户日志"),id:2,typeName:"customer",subPath:"detail/detail",eventName:"customer"},{name:$t("联系人日志"),id:3,typeName:"contacts",eventName:"contact"},{name:$t("产品日志"),id:4,typeName:"product",eventName:"product"},{name:$t("回款日志"),id:5,typeName:"cb",eventName:"backmoney"},{name:$t("退款日志"),id:6,typeName:"gb",eventName:"refund"},{name:$t("商机日志"),id:8,typeName:"opportunity",eventName:"opportunity"},{name:$t("开票日志"),id:9,typeName:"invoice",eventName:"invoice"},{name:$t("订单日志"),id:11,typeName:"orderform",eventName:"order"},{name:$t("退货单日志"),id:12,typeName:"returnorder",eventName:"returnorder"},{name:$t("拜访日志"),id:13,typeName:"visit",eventName:"visit"},{name:$t("合同日志"),id:16,typeName:"contract",eventName:"contract"},{name:$t("竞争对手日志"),id:19,typeName:"rival",eventName:"rival"},{name:$t("市场活动日志"),id:20,typeName:"market",eventName:"marketingevent"}]}});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var i;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(i="Object"===(i={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:i)||"Set"===i?Array.from(e):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var i=0,n=Array(t);i<t;i++)n[i]=e[i];return n}function _iterableToArrayLimit(e,t){var i=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=i){var n,a,r,o,s=[],l=!0,d=!1;try{if(r=(i=i.call(e)).next,0===t){if(Object(i)!==i)return;l=!1}else for(;!(l=(n=r.call(i)).done)&&(s.push(n.value),s.length!==t);l=!0);}catch(e){d=!0,a=e}finally{try{if(!l&&null!=i.return&&(o=i.return(),Object(o)!==o))return}finally{if(d)throw a}}return s}}function _arrayWithHoles(e){if(Array.isArray(e))return e}function ownKeys(t,e){var i,n=Object.keys(t);return Object.getOwnPropertySymbols&&(i=Object.getOwnPropertySymbols(t),e&&(i=i.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),n.push.apply(n,i)),n}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(i),!0).forEach(function(e){_defineProperty(t,e,i[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function _defineProperty(e,t,i){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0===i)return("string"===t?String:Number)(e);i=i.call(e,t||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/crmlog/crmlog",["base-modules/utils","crm-widget/select/select","./config","base-moment","crm-widget/table/table"],function(e,t,i){var s=e("base-modules/utils"),a=e("crm-widget/select/select"),r=e("./config"),o=(e("base-moment"),e("crm-widget/table/table")),n=Backbone.Model.extend({defaults:{objectType:2,objects:[],group:0,pageSize:20,pageNumber:1,selectStatus:0},getAllObjects:function(){var a=this;return s.FHHApi({url:"/EM1HNCRM/API/v1/object/modifyLog/service/getLogModuleGroup",data:{},success:function(e){var n;0===e.Result.StatusCode&&(e=e.Value||{},n=[],_.each(e.moduleGroupList,function(t){var i=[];_.each(t.logModuleList||[],function(e){i.push({name:_.escape(e.name),value:e.id}),e.isDefault&&a.set({selectedGroup:t.groupId,selectedItem:_.isArray(e.id)?"2":e.id})}),n.push({name:t.groupName,value:t.groupId,subOpts:i})}),a.set("groupOpts",n,{silent:!0}),a.set("selectStatus",a.get("selectStatus")+1))}})}}),e=Backbone.View.extend({initialize:function(e){this.logInfo={objectOpts:[],behaviorOpts:[],ids:{},totalCount:0,hasMore:!1,pageSize:20,indexPage:1,prePage:!1,isLoading:!0,isActualCount:!1},this.setElement(e.wrapper),this.widgets={},this.model=new n,this.listenTo(this.model,"change:selectStatus",this._initSelect),this.listenTo(this.model,"change:groupOpts",this._resetGroupOpts),this.listenTo(this.model,"change:itemOpts",this._resetItemOpts),this.listenTo(this.model,"change:selectedItem",this._refreshDt)},render:function(){this.initTable2()},showTips:function(e){this.$el.find(".crm-tip").remove(),this.$(".dt-tit").append(['<div class="crm-tip">','<span class="tip-btn"></span>','<div class="tip-text">',"<p>"+$t("此处显示企业{{num}}个月内的修改记录",{num:e||18})+"</p>","</div>","</div>"].join(""))},events:{"click .log-export":"exportHandle"},exportHandle:function(){var e,t=this;t.logInfo.totalCount?(e=0<navigator.userAgent.indexOf("Mac OS X"),t.widgets.exportDialog=FxUI.create({template:'\n                      <fx-dialog\n                          :title="$t(\'导出\')"\n                          :visible="visible"\n                          :modal="false"\n                          size="small"\n                          :modal-append-to-body="true"\n                          min-height="48px"\n                          custom-class="crmlog-export-dialog"\n                          @close="handleClose"\n      \n                      >\n                        <div class="setting-item"  v-if="showContent==0">\n                            <label style="display: inline-block; margin-bottom:8px;">{{ $t(\'请选择导出的编码格式\') }}</label>\n                            <fx-radio-group v-model="fileFormat"  >\n                                <fx-radio label="UTF-8">UTF-8</fx-radio>\n                                <fx-radio label="GBK">GBK</fx-radio>\n                                <fx-radio label="GB2312">GB2312</fx-radio>\n                            </fx-radio-group>\n                    \n                    \n                        </div>\n                         <div v-else-if="showContent==1">\n                         <p style="text-align: center">'.concat($t("导出中"),'...</p>\n                         <fx-progress  :percentage="percent" status="exception" ></fx-progress>\n                         </div>\n                          \n                         <div v-else v-html="resultContent" style="text-align: center;"></div>\n                          <span slot="footer" class="dialog-footer" v-if="showBtn">\n                              <fx-button type="primary" @click="handleExport" size="small" :loading="btnLoding">').concat($t("确定"),'</fx-button>\n                              <fx-button type="primary" @click="handleClose" size="small">').concat($t("取消"),"</fx-button>\n                          </span>\n                      </fx-dialog>\n                             \n                              "),data:function(){return{showContent:0,visible:!0,percent:0,status:"exception",count:0,processTimer:null,resultContent:"",btnLoding:!1,queryData:{},showBtn:!0,ajaxs:{},fileFormat:e?"UTF-8":"GBK"}},computed:{},methods:{handleClose:function(){this.visible=!1,t.widgets.exportDialog.destroy&&t.widgets.exportDialog.destroy(),t.widgets.exportDialog=null},query:function(){var e,i;this.visible&&(e=$t("导出数据可能需要较长时间，已自动转入后台导出，完成后系统会在企信的文件助手中提示"),(i=this).count++,10<this.count?this.showResult("tips",e):this.ajaxs.query=this.findJobState().then(function(e){var t;2==e.status?(t=window.CRM.util.getFscLink(e.url,$t("CRM日志")+FS.moment.unix(e.startTime/1e3).format("YYYYMMDD")+"."+e.fileExt,!0),i.showResult("success",{path:e.url,file_name:$t("CRM日志")+FS.moment.unix(e.startTime/1e3).format("YYYYMMDD"),ext:e.fileExt,value:t})):e.status<2?setTimeout(i.query,5e3):i.showResult("error",$t("生成失败"))},function(e){FxUI.MessageBox.alert(e.FailureMessage,$t("错误提示"),{type:"error",dangerouslyUseHTMLString:!0}),i.showResult("error",$t("生成失败"))}))},showLoading:function(){var e=this;this.processTimer=setInterval(function(){e.percent++,95==e.percent&&(clearInterval(e.processTimer),e.processTimer=null)},50)},hideLoading:function(){this.processTimer&&clearInterval(t.processTimer),this.processTimer=null},handleExport:function(){var i=this;this.btnLoding=!0,this.ajaxs.export=this.fetchCreateExport({apiFullName:$t("CRM日志"),templateId:"crm_common_002",apiName:"async.job.crm.log",queryParam:{exportBizType:"crmLog",searchQuery:JSON.stringify(t.filterTerms.getFilterData()||{}),fileFormat:this.fileFormat},jobType:2,timezone:"Asia/Shanghai"}).then(function(e){var t;i.showBtn=!1,i.btnLoding=!1,i.showContent=1,i.queryData={jobId:e.id||""},e.status<2?(i.showLoading(),i.query()):2==e.status?(t=window.CRM.util.getFscLink(e.url,$t("企业管理日志")+FS.moment.unix(e.startTime/1e3).format("YYYYMMDD")+"."+e.fileExt,!0),i.showResult("success",{path:e.url,file_name:$t("CRM日志")+FS.moment.unix(e.startTime/1e3).format("YYYYMMDD"),ext:e.fileExt,value:t})):i.showResult("error",$t("生成失败"))},function(e){FxUI.MessageBox.alert(e.FailureMessage,$t("错误提示"),{type:"error",dangerouslyUseHTMLString:!0}),i.showResult("error",$t("生成失败")),i.showBtn=!1,i.visible=!1})},showResult:function(e,t){this.hideLoading(),this.showContent=2,this.resultContent="success"===e?'<div style=" display: flex;\n                                      justify-content: center;\n                                      flex-direction: column;\n                                      align-items: center;">\n                                          <span>'.concat($t("生成成功"),'</span> \n                                          <a href="').concat(t.value,'">').concat($t("下载文件"),"</a>\n                                      </div>"):"<div>\n                                          <span>".concat(t,"</span> \n                                      </div>")},fetchCreateExport:function(e){return new Promise(function(i,n){return s.FHHApi({url:"/EM1HJobCenter/inputJobCenter/createJob",data:e,success:function(e){var t;0===e.Result.StatusCode?(t=e.Value||{},i(t||{})):n(e.Result)},error:function(e){n(e.Result.FailureMessage||$t("服务器返回错误!"))}})})},findJobState:function(e){return e=this.queryData,new Promise(function(i,n){return s.FHHApi({url:"/EM1HJobCenter/inputJobCenter/queryJobState",data:e,success:function(e){var t;0===e.Result.StatusCode?(t=e.Value||{},i(t)):n(e.Result)}})})}},destroy:function(){Object.keys(this.ajaxs).forEach(function(e){e&&e.abort&&e.abort()})}})):FxUI.MessageBox.alert($t("暂无导出数据"),$t("错误提示"),{type:"error",dangerouslyUseHTMLString:!0})},initTable2:function(){var a=this,t=o.prototype._renderPage,i=a.renderPage,e=o,e=o.extend({_renderPage:function(e){e=i.call(a,e,t);e&&this.$el.find(".dt-page .page-box").html(e)}});a.widgets.dt||(a.widgets.dt=new e({$el:a.$el,title:$t("CRM日志"),className:"crm-table crm-table-noborder crm-table-open crm_log_table_new",url:"/EM1HNCRM/API/v1/object/modifyLog/service/getAuditLogList",requestType:"FHHApi",trHandle:!1,isOrderBy:!0,openStart:!0,sortField:"QueryInfo.SortField",sortType:"QueryInfo.SortType",operate:{btns:[{text:$t("导出"),className:"log-export"}],pos:"T"},columns:[{data:"userId",title:$t("操作人"),isOrderBy:!1,dataType:1,filterCompare:[8,1,2,9,10,11,12,13,14,22,23],width:200,isFilter:!0,render:function(e,t,i){var n=i.userId;return n?"-10000"==n?$t("系统"):(i=i.userName||(FS.contacts.getEmployeeById(n)||{}).name||"--",'<span title="'+FS.util.encodeHTML(i)+'">'+FS.util.encodeHTML(i)+"</span>"):"--"}},{data:"operationTime",title:$t("操作时间"),dataType:4,isOrderBy:!1,isFilter:!0,filterCompare:[1,2,3,5,9,10,17,18,19,20,21]},{data:"bizOperationName",title:$t("操作行为"),dataType:6,isFilter:!0,render:function(e,t,i){var n=i.bizOperationName,i=(a.logInfo.behaviorOpts.find(function(e){return e.value.split("/").includes(n)})||{}).label||"--";return'<span class="table-td-nowrap" title="'+FS.util.encodeHTML(i)+'">'+FS.util.encodeHTML(i)+"</span>"}},{data:"objectName",title:$t("操作对象"),render:function(t,e,i){t=i.objectName,i=(a.logInfo.objectOpts.find(function(e){return e.value==t})||{}).label||"";return'<span class="table-td-nowrap" title="'+FS.util.encodeHTML(i)+'">'+FS.util.encodeHTML(i)+"</span>"}},{data:"objectId",title:$t("操作name",{name:$t("数据")}),render:function(e,t,i){for(var e=i.objectId,n="",a=i.messageList||[],r=0;r<a.length;r++)if(n=a[r].text,a[r].type){n=a[r].text;break}return n=n||"--",'<a href="javascript:void(0)" class="j-detail" data-type="'+i.objectName+'"  title="'+FS.util.encodeHTML(n)+'" data-id="'+FS.util.encodeHTML(e)+'">'+FS.util.encodeHTML(n)+"</a> "}},{data:"textMessage",title:$t("操作name",{name:$t("详情")}),isOrderBy:!1,dataType:1,filterCompare:[8,1,2,9,10,11,12,13,14,22,23],isFilter:!0,render:function(e,t,i){var n=i.messageList||[],i=i.objectName||"--",a="",r="";return n.forEach(function(e){e.type?a+='<a href="javascript:void(0)" class="j-detail" data-type="'+e.objectApiName+'" data-id="'+e.dataID+'">'+FS.util.encodeHTML(e.text)+"</a> ":a+=FS.util.encodeHTML(e.text)+" ",r+=e.text}),a='<div class="crm-log-msg" title="'+r+'">'+(a=a||i)+"</div>"}}],paramFormat:function(e){return e},formatData:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=e.results||[],i=t.length?{searchAfterS:t[0].searchAfter,searchAfterE:t[t.length-1].searchAfter,firstId:t[0].logId||"",endId:t[t.length-1].logId||""}:{firstId:"",endId:""};return a.logInfo=_objectSpread(_objectSpread({},a.logInfo),{},{totalCount:(a.logInfo.isActualCount&&a.logInfo.totalCount?a.logInfo:e).totalCount,hasMore:e.hasMore,ids:i,pageSize:e.pageSize,isLoading:!1}),a.filterTerms&&a.filterTerms.unLoading(),{totalCount:e.totalCount,data:t}},initComplete:function(){a.widgets.dt&&a.renderFilter()}}),a.widgets.dt&&a.widgets.dt.on("trclick",function(e,t,i){i.hasClass("j-detail")&&a.renderDetail(i.data("type"),i.data("id"))}))},initTable:function(){var n=this;n.widgets.dt||(n.widgets.dt=new o({$el:n.$el,title:$t("CRM日志"),url:"/EM1HNCRM/API/v1/object/modifyLog/service/getManageLogList",requestType:"FHHApi",trHandle:!1,isOrderBy:!0,showFilerBtn:!0,showPage:!0,openStart:!0,sortField:"QueryInfo.SortField",sortType:"QueryInfo.SortType",columns:[{data:"UserName",title:$t("操作人"),isOrderBy:!1,dataType:1,filterCompare:[8,1,2,9,10,11,12,13,14,22,23],width:200,isFilter:!0,render:function(e){return e}},{data:"operationTime",title:$t("操作时间"),dataType:4,isOrderBy:!0,isFilter:!0,filterCompare:[1,2,3,5,9,10,17,18,19,20,21]},{data:"bizOperationName",title:$t("行为"),dataType:6,isFilter:!0,notExtend:!0,filterCompare:[2,9,10,13,14,22,23],options:_.union([{ItemName:$t("全部"),ItemCode:-9999}],r.actionOpts_special,r.actionOpts_common,r.actionOpts_function),render:function(e,t,i){return i.OperName}},{data:"textMessage",title:$t("操作内容"),isOrderBy:!1,dataType:1,filterCompare:[8,1,2,9,10,11,12,13,14,22,23],isFilter:!0,render:function(e,t,i){var n="",a="";return _.each(i.MsgList,function(e){e.type?n+='<a href="javascript:void(0)" class="j-detail" data-type="'+e.objectApiName+'" data-id="'+e.dataID+'">'+FS.util.encodeHTML(e.text)+"</a> ":n+=FS.util.encodeHTML(e.text)+" ",a+=e.text}),n='<div class="crm-log-msg" title="'+a+'">'+n+"</div>"}}],formatData:function(e){var t=e.msgs||[];return _.each(t,function(e){e.operationTime=e.OperTime,e.bizOperationName=e.OperTime}),{data:t,totalCount:e.pageInfo?e.pageInfo.totalCount:0}},termBatchComplete:function(){n.$(".last-target-item").after('<div class="item"><span class="line"></span><span class="item-tit">'+$t("类别：")+'</span><div class="item-con group-select" style="width: 100px;"></div><div class="item-con item-select" style="width: 130px;"></div></div>'),n.model.set("selectStatus",n.model.get("selectStatus")+1)}}),n.widgets.dt.on("trclick",function(e,t,i){i.hasClass("j-detail")&&n.renderDetail(i.data("type"),i.data("id"))}))},renderFilter:function(){var o=this,e=Vue.extend({template:'    <div class="crm_log_filters">\n                <div class="filter-item">\n                <label>{{ $t("操作对象") }}:</label>\n                <fx-select\n                  :placeholder="$t(\'请选择\')"\n                  v-model="filters.module"\n                  :options="objectOpts"\n                  size="small"\n                  filterable\n                ></fx-select>\n              </div>\n                <div class="filter-item">\n                  <label>{{ $t("操作人") }}:</label>\n                  <fx-selector-input-v2\n          ref="selectorInputLine"\n            class="operator-name__line"\n                    :foldInput="true"\n          :value="filters.userIds"\n                    :single="false"\n                    :member="true"\n                    :tabs="tabs"\n                    style="width:318px"\n                    :defaultSelectedItems="{\n                        member:[]\n                    }"\n          add-btn-label="'.concat($t("选择操作人"),'"\n          @change="handleSelector">\n            </fx-selector-input-v2>\n                </div>\n                <div class="filter-item">\n                  <label>{{ $t("操作时间") }}:</label>\n                  <fx-date-picker\n                    v-model="filters.operationTime"\n                    type="datetimerange"\n                    size="small"\n                    value-format="timestamp"\n                    :default-time="[\'00:00:00\', \'23:59:59\']"\n                    format=\'yyyy-MM-dd HH:mm\'\n                    :useLocaleFormat="true"\n                    :start-placeholder="$t(\'开始日期\')"\n                    :end-placeholder="$t(\'结束日期\')"\n                    :picker-options="pickerOptions"\n                  >\n                  </fx-date-picker>\n                </div>\n               \n                <div class="filter-item">\n                  <label>{{ $t("操作行为") }}:</label>\n                  <fx-select\n                    :placeholder="$t(\'请选择\')"\n                    v-model="filters.bizOperationNames"\n                    :options="behaviorOpts"\n                    size="small"\n                    clearable\n                    filterable\n                  ></fx-select>\n                </div>\n                <div class="filter-item">\n                <label>{{ $t("操作name",{name: $t("详情")}) }}:</label>\n                <fx-input\n                  v-model="filters.textMessage"\n                  size="small"\n                  clearable\n                ></fx-input>\n              </div>\n                <fx-button @click="handleSearch" type="primary" size="small" class=" filter-item filter-button" :disabled="isLoading">{{ $t(\'查询\')}}</fx-button>\n              </div>'),data:function(){var n=this;return{filters:{userIds:{},operationTime:[],bizOperationNames:"",module:""},objectOpts:[],behaviorOpts:[],tabs:[],dateObj:{minDate:"",maxDate:""},queryInterval:void 0,grayCHRead:!1,pickerOptions:{onPick:function(e){var t=e.minDate,e=e.maxDate;n.dateObj.minDate=t,n.dateObj.maxDate=e},disabledDate:function(e){var t,i;e.getTime();return!!n.queryInterval&&(e=e.getTime(),t=2592e6*n.queryInterval,n.dateObj.minDate&&!n.dateObj.maxDate?t<e-(i=n.dateObj.minDate.getTime())||t<i-e:n.dateObj.maxDate&&!n.dateObj.minDate?t<(i=n.dateObj.maxDate.getTime())-e||t<e-i:Date.now()-e>t)}},isLoading:!0}},methods:{handleSearch:function(){if(this.grayCHRead){var e=this.filters.operationTime;if(e&&2==e.length){var e=_slicedToArray(e,2),t=e[0],e=e[1];if(this.queryInterval){var i=2592e6*this.queryInterval,n=31536e6;if(this.filters.textMessage){var a=Math.max(i,n),r=n<i?this.queryInterval:"12";if(6048e5<e-t||t<Date.now()-a)return void this.$message.warning($t("paas.crm.log.month.days.tip",{month:r}))}else{a=Math.max(i,n),r=n<i?$t("paas.crm.log.months.year.tip",{month:this.queryInterval}):$t("paas.crm.log.one.month.tip",{month:this.queryInterval});if(n<e-t||t<Date.now()-a)return void this.$message.warning(r)}}else if(this.filters.textMessage){if(6048e5<e-t||t<Date.now()-31536e6)return void this.$message.warning($t("paas.crm.log.month.days.tip",{month:"12"}))}else if(t<Date.now()-31536e6)return void this.$message.warning($t("paas.crm.log.one.year.tip"))}}o.logInfo.isActualCount=!1,o.widgets.dt.setParamByKey("auditLogCondition",this.getFilterData()),o.refresh(),o.logInfo.indexPage=1,this.isLoading=!0},unLoading:function(){this.isLoading=!1},getFilterData:function(){var t=this,i={};return["userIds","operationTime","bizOperationNames","textMessage","module"].forEach(function(e){"userIds"==e&&Object.keys(t.filters[e]).length?i[e]=t.filters[e].member||[]:"operationTime"==e&&Array.isArray(t.filters.operationTime)&&2==t.filters.operationTime.length?(i.operationTimeFrom=t.filters.operationTime[0],i.operationTimeTo=t.filters.operationTime[1]):"bizOperationNames"==e&&t.filters[e]&&(i[e]=t.filters[e].split("/")),["textMessage","module"].includes(e)&&t.filters[e]&&(i[e]=t.filters[e])}),i},handleSelector:function(e){this.filters.userIds=e},getEmployeesByIds:function(e){return FS.contacts.getEmployeesByIds(e).filter(function(e){return!!e})},initTab:function(){this.tabs=[{id:"member",title:$t("同事"),type:"sort",data:FS.contacts.sortEmployeesByLetter(this.getEmployeesByIds([])),searchKeys:["name","nameSpell"]}]},getDescribeList:function(){var n=this;return new Promise(function(t,e){s.FHHApi({url:"/EM1HNCRM/API/v1/object/modifyLog/service/getLogModuleGroup",data:{},success:function(e){0===e.Result.StatusCode&&((e.Value||{}).moduleGroupList.forEach(function(e){var t=[],t=4==e.groupId?e.logModuleList.map(function(e){return{value:e.idList.join("/"),label:e.name}}):e.logModuleList.map(function(e){return{value:e.id,label:e.name}}),i={1:"objectOpts",4:"behaviorOpts"};o.logInfo[i[e.groupId]]=n[i[e.groupId]]=t,1==e.groupId&&(n.filters.module=(t[0]||{}).value)}),o.widgets.dt.options&&o.widgets.dt.start({auditLogCondition:n.getFilterData(),pageSize:20}),t())}})})},fetchQueryInterval:function(){var n=this;return new Promise(function(i,e){s.FHHApi({url:"/EM1HNCRM/API//v1/object/modifyLog/service/getTenantLogInterval",data:{logType:"audit_log"},success:function(e){var t=(null==e?void 0:e.Value.queryInterval)||void 0,e=(null==e?void 0:e.Value.grayCHRead)||!1;n.grayCHRead=e,t&&(o.showTips(t),n.queryInterval=+t),n.filters=_objectSpread(_objectSpread({},n.filters),{},{operationTime:[(new Date).getTime()-2592e6,(new Date).getTime()]}),i()}})})}},created:function(){this.initTab(),Promise.all([this.getDescribeList({isIncludeSystemObj:!0,isIncludeFieldDescribe:!0,isIncludeUnActived:!1}),this.fetchQueryInterval()]),this.isLoading=o.logInfo.isLoading}}),e=o.filterTerms=(new e).$mount();o.$el.find(".crm-table .dt-term-batch").append(e.$el)},refresh:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=this,i=t.filterTerms.getFilterData();t.widgets.dt.setParamByKey("searchAfter",e.searchAfter||[]),t.widgets.dt&&t.widgets.dt.start(Object.assign({},{pageSize:t.logInfo.pageSize||20,logId:"",isPrePage:!1,isChangePageSize:!1},e,{auditLogCondition:i}))},renderPage:function(){var o=this,e=Vue.extend({template:'<div class="manage_log_page">\n                      <label class="page_item page_totalcount">{{  !isActualCount ? $t("约") : $t("共") }} {{ totalCount }} {{ $t("条") }} </label>\n                    <label class="page_item look-num" v-if="cIsCountTenThousand&& !isActualCount" @click="accurateCal"> {{ $t("精确总数") }}</label>\n                      <fx-button  type="text"  @click="goFrontPage" :disabled="cDisabled.front" :class="[\'page_item\' ,\'page_button_front_page\', cDisabled.front ? \'is_disabled\' : \'\'  ]">{{ $t("首页")}}</fx-button>\n                      <fx-button  type="text"  @click="preClick" :disabled="cDisabled.prev" :class="[\'page_item\' ,\'page_button_front_page\', cDisabled.prev ? \'is_disabled\' : \'\'  ]">{{ $t("上一页")}}</fx-button>\n                      <fx-pagination\n                      ref="pagination"\n                      layout="sizes,total"\n                      :page-size="pageSize"\n                      :page-sizes="sizeList"\n                      \n                      :disabled="isLoading||totalCount ==0"\n                      :current-page.sync="indexPage"\n                      @size-change="updatePageSize" \n                    >\n                    </fx-pagination>\n                    <fx-button  type="text"  @click="nextClick" :disabled="cDisabled.next" :class="[\'page_item\' , cDisabled.next ? \'is_disabled\' : \'\'  ]">{{ $t("下一页")}}</fx-button>\n                      </div> ',data:function(){return{hasMore:!1,prePage:!1,disabled:!0,indexPage:1,pageSize:20,sizeList:[20,50,100],totalCount:0,isLoading:!0,limitNum:1e4,isActualCount:!1}},computed:{cDisabled:function(){return{front:1==this.indexPage||this.isLoading,prev:this.isLoading||1==this.indexPage||!this.hasMore&&this.prePage,next:this.isLoading||this.totalCount<=this.pageSize||!this.hasMore&&!this.prePage}},cIsCountTenThousand:function(){return this.totalCount>=this.limitNum}},methods:{accurateCal:function(){var t=this;this.fetchAuditLogCount().then(function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};t.totalCount=e.totalCount,o.logInfo.totalCount=e.totalCount,o.logInfo.isActualCount=!0,t.isActualCount=!0})},fetchAuditLogCount:function(){return new Promise(function(t,e){s.FHHApi({url:"/EM1HNCRM/API/v1/object/modifyLog/service/getAuditLogCount",data:{auditLogCondition:o.filterTerms.getFilterData()},success:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};t(e.Value)}})})},goFrontPage:function(){o.refresh({pageSize:this.pageSize}),o.logInfo.indexPage=this.indexPage=1,o.logInfo.prePage=this.prePage=!1,this.isLoading=!0},parseLogParam:function(e){var t=(o.logInfo||{}).ids,t=void 0===t?{}:t,i={},n=t.firstId,a=t.endId,r=t.searchAfterE,t=t.searchAfterS;return"start"==e?t?i.searchAfter=t:i.logId=n:r?i.searchAfter=r:i.logId=a,i},updatePageSize:function(e){this.pageSize=e,o.logInfo.pageSize=this.pageSize,this.prePage=!1,1==this.indexPage?this.goFrontPage():o.refresh(_objectSpread(_objectSpread({},this.parseLogParam("start")),{},{pageSize:this.pageSize,isChangePageSize:!0})),this.isLoading=!0},nextClick:function(){this.$refs.pagination.next(),o.logInfo.indexPage=this.indexPage=this.$refs.pagination.internalCurrentPage||1,o.refresh(_objectSpread(_objectSpread({},this.parseLogParam("end")),{},{pageSize:this.pageSize})),o.logInfo.prePage=this.prePage=!1,this.isLoading=!0},preClick:function(){this.$refs.pagination.prev(),o.logInfo.indexPage=this.indexPage=this.$refs.pagination.internalCurrentPage||1,o.refresh(_objectSpread(_objectSpread({},this.parseLogParam("start")),{},{pageSize:this.pageSize,isPrePage:!0})),o.logInfo.prePage=this.prePage=!0,this.isLoading=!0}},created:function(){this.totalCount=o.logInfo.totalCount||0,this.hasMore=o.logInfo.hasMore||!1,this.pageSize=o.logInfo.pageSize,this.indexPage=o.logInfo.indexPage,this.prePage=o.logInfo.prePage,this.isLoading=o.logInfo.isLoading,this.isActualCount=!!o.logInfo.isActualCount||!this.cIsCountTenThousand}});return(o.widgets.pagination=(new e).$mount()).$el},getGrayByMod:function(r){var o="[object String]"===toString.call(r);return r=o?[r]:r,new Promise(function(n,a){if(!window.paasBasicBusinessGray)return s.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findWebConfig",data:{},success:function(e){var t,i;0===e.Result.StatusCode?(t=(e.Value||{}).result,window.paasBasicBusinessGray=t,window.paasBasicBusinessGray=t,i=r.map(function(e){return t[e]}),n(o?i[0]:i)):a(e.Result)}});var t=window.paasBasicBusinessGray,e=r.map(function(e){return t[e]});n(o?e[0]:e)})},renderDetail:function(t,e){var i,e=isNaN(this.model.get("selectedItem"))?(i="myobject",{crmId:e+","+t}):(i=_.find(r.options,function(e){return e.id==t}).eventName,{crmId:e});FS.MEDIATOR.trigger("crm.detail",{type:i,data:e})},_initSelect:function(){var e,n=this;n.model.get("selectStatus")<2||((e=new a({$wrap:n.$(".group-select"),width:130,size:1,options:n.model.get("groupOpts")||[],defaultValue:-1})).on("change",function(e,t){var i=_.find(n.model.get("groupOpts"),function(e){return e.value===t.value});n.model.set({selectedGroup:t.value,itemOpts:i.subOpts})}),e.setValue(n.model.get("selectedGroup"),!0),n.widgets.groupSelect=e)},_resetGroupOpts:function(e,t){this.widgets.groupSelect.resetOptions(t),this.model.set("objectOpts",t.subOpts)},_resetItemOpts:function(e,t){var i,n=this;n.widgets.itemSelect?(n.widgets.itemSelect.resetOptions(n.model.get("itemOpts")||[]),n.widgets.itemSelect.setValue(n.model.get("itemOpts")[0].value,!0)):((i=new a({$wrap:n.$(".item-select"),width:130,size:1,options:n.model.get("itemOpts")||[],defaultValue:n.model.get("selectedItem")})).on("change",function(e,t){n.model.set("selectedItem",_.isArray(e)?"2":e)}),n.widgets.dt.start({QueryInfo:{FilterMainID:this.model.get("selectedItem")}}),n.widgets.itemSelect=i)},_refreshDt:function(){this.widgets.dt.setParam({QueryInfo:{FilterMainID:this.model.get("selectedItem")}},!0)},destroy:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy(),e&&e.$destroy&&e.$destroy()}),this.off(),this.$el.remove()}});i.exports=e});
define("crm-setting/crmlog/template/detail-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += "";
        }
        return __p;
    };
});