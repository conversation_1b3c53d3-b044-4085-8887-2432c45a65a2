define("crm-setting/proresource/proresource",["./template/tpl-html","./template/gantt-html","./template/status-complete-html","./template/project-budget-html","./template/task-notice-html","crm-modules/common/util"],function(t,e,n){var a=t("./template/tpl-html"),i=t("./template/gantt-html"),o=t("./template/status-complete-html"),c=t("./template/project-budget-html"),r=t("./template/task-notice-html"),s=t("crm-modules/common/util"),u=Backbone.View.extend({initialize:function(e){this.widgets={},this.setElement(e.wrapper)},render:function(){this.$el.html(a()),this.idx=0,this.renderCon()},events:{"click .j-tab span":"tabHandle"},renderCon:function(){var e=this,n=this;({1:function(){return $(".tab-con",e.$el).html('<div class="work-wrapper"></div>'),e.createForm()},2:function(){return $(".tab-con",e.$el).html(o()),e.initStatus()},3:function(){return $(".tab-con",e.$el).html(c()),e.addProjectSwitch()},4:function(){return $(".tab-con",e.$el).html(r()),e.initTaskNotice()}}[this.idx]||function(){$(".tab-con",n.$el).html(i()),n.getGanttConfig(function(e){n.$(".crm-radio").empty();var t=(e.find(function(e){return"project_detail_redirect"===e.key})||{}).value,e=(e.find(function(e){return"gantt_allow_update_date"===e.key})||{}).value;n.setProject(t),n.setGantt(e)})}).call(this)},getDataConfig:function(t){s.FHHApi({url:"/EM1HNCRM/API/v1/object/project_resource/service/queryWorkingHoursThreshold",data:{},success:function(e){0==e.Result.StatusCode?t(e.Value.data||{}):s.alert(e.Result.FailureMessage)},complete:function(){}},{errorAlertModel:1})},getGanttConfig:function(t){s.getConfigValues(["gantt_allow_update_date","project_detail_redirect"]).then(function(e){t&&t(e)},function(e){s.alert(e)})},createForm:function(){this.getDataConfig(function(e){var n=e.clocking_in_rule_id_conf_key,a=e.project_resource_working_hours_threshold;t.async("vcrm/sdk",function(e){e.getComponent("WorkHourComponent").then(function(t){var e=new Vue({render:function(e){return e(t.default,{props:{workHour:a,ruleId:n}})}}).$mount();$(".work-wrapper").append(e.$el)})})})},setProject:function(e){this.widgets.projectVal=FxUI.create({wrapper:this.$(".crm-radio")[0],template:'<fx-radio-group v-model="radio" @change="onChange">\n                <fx-radio :label="1">'.concat($t("项目的甘特图页面"),'</fx-radio>\n                <fx-radio :label="2">').concat($t("项目的详情页"),"</fx-radio>\n              </fx-radio-group>"),data:function(){return{radio:parseInt(e)||1}},methods:{onChange:function(e){s.showLoading_tip(),s.FHHApi({url:"/EM1HNCRM/API/v1/object/project_config/service/save_gantt_chart_config",data:{detail_redirect:e},success:function(e){0==e.Result.StatusCode?s.remind(1,$t("设置成功")):s.alert(e.Result.FailureMessage)},complete:function(){s.hideLoading_tip()}},{errorAlertModel:1})}}})},setGantt:function(e){this.widgets.ganttVal=FxUI.create({wrapper:this.$(".crm-gantt")[0],template:'<fx-radio-group v-model="ganttVal" @change="onChange">\n                <fx-radio :label="1">'.concat($t("允许"),"(").concat($t("默认"),')</fx-radio>\n                <fx-radio :label="0">').concat($t("不允许"),"</fx-radio>\n              </fx-radio-group>"),data:function(){return{ganttVal:parseInt(e)}},methods:{onChange:function(e){s.showLoading_tip(),s.setConfigValues([{key:"gantt_allow_update_date",value:e}]).then(function(){s.hideLoading_tip(),s.remind(1,$t("设置成功"))},function(e){s.hideLoading_tip(),s.alert(e)})}}})},initStatus:function(e){var t=this;this._getConfig(["project_biz_status_edit","config_proj_stage_auto_calculate_complete"],function(){t.addSwitch(0<arguments.length&&void 0!==arguments[0]?arguments[0]:[])})},initTaskNotice:function(){this.initTaskSwitch(),this.initTaskDate()},initTaskSwitch:function(){this._getConfig(function(){var e=this.$(".crm-wrapper .notice").find(".switch");_.each(e,function(e,t){FxUI.create({wrapper:e,template:'<fx-switch v-model="value" @change="taskChangeFun" size="small" :before-change="beforeChanage"></fx-switch>',data:function(){return{value:0}},methods:{beforeChanage:function(e){},taskChangeFun:function(e){this.value=!0}}})})})},initTaskDate:function(){var e=this.$(".crm-wrapper .card").find(".date-select-wrapper");_.each(e,function(e,t){this.date_select_box=FxUI.create({wrapper:e,template:'<fx-select\n                        v-model="selectedRange"\n                        :placeholder="$t(\'请选择时间段\')"\n                        size="mini"\n                        class="select-box"\n                        filterable\n                        clearable\n                        :options="dateRanges"\n                    >\n                    </fx-select>',data:function(){return{selectedRange:null,dateRanges:[]}},created:function(){this.dateRanges=this.generateRangeOptions(24)},methods:{generateRangeOptions:function(e){for(var t=[],n=0;n<e;n++)t.push({value:"".concat(n,"  -  ").concat(n+1),label:"".concat(n,"  -  ").concat(n+1)});return t}}}),this.date_select_box.$el.before($t("当天")),this.date_select_box.$el.after($t("时"))})},addSwitch:function(e){var t=this.$(".crm-wrapper .card").find(".switch"),n=0===parseInt(e.find(function(e){return"project_biz_status_edit"===e.key}).value),a=0!==parseInt(e.find(function(e){return"config_proj_stage_auto_calculate_complete"===e.key}).value);_.each(t,function(e,t){FxUI.create({wrapper:e,template:'<fx-switch v-model="value" @change="changeFun" size="small" :before-change="beforeChanage"></fx-switch>',data:function(){return{value:0===t?n:a}},methods:{beforeChanage:function(e){var t=this;if(!this.value)return!0;this.$confirm($t("确定要关闭此设置吗？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){t.value=!1,t.changeFun(!1)}).catch(function(){return Promise.reject()})},changeFun:function(e){s.setConfigValue({key:0===t?"project_biz_status_edit":"config_proj_stage_auto_calculate_complete",value:0===t?e?"0":"1":e?"1":"0"}).then(function(){},function(e){s.alert(e)})}}})})},addProjectSwitch:function(){var i=this;this._getConfig(["enable_project_settlement","enable_expense_cost_collection"],function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],t=0!==parseInt(e.find(function(e){return"enable_project_settlement"===e.key}).value),a=0!==parseInt(e.find(function(e){return"enable_expense_cost_collection"===e.key}).value),e=this.$(".crm-wrapper .budget").find(".switch");_.each(e,function(e,n){FxUI.create({wrapper:e,template:'<fx-switch v-model="value" @change="budgetChangeFun" size="small" :before-change="beforeChanage" :disabled="switchDisabled"></fx-switch>',data:function(){return{value:0===n?t:a,is_disabled_budget:t,is_disabled_const:a}},computed:{switchDisabled:function(){return n?this.is_disabled_const:this.is_disabled_budget}},watch:{value:function(e){this[n?"is_disabled_const":"is_disabled_budget"]=e}},methods:{setConfirm:function(e,t,n){var a=this;this.$confirm('<div class="reimbur-wrapper"><p class="reimbur-title">'.concat(e,'</p><p class="reimbur-title ').concat(t?"":"sub-tip",'">').concat(t,'</p><a class="reimbur-operate ').concat(n?"":"sub-tip",'" href="').concat(n,'" target="_blank">').concat($t("sfa.crm.goto.enable.cost.manage"),"</a></div>"),$t("提示"),{confirmButtonText:$t("知道了"),showCancelButton:!1,dangerouslyUseHTMLString:!0}).then(function(){a.value=!1}).catch(function(){return Promise.reject()})},getReceivableStatus:function(){var t=this;CRM.api.get_licenses({key:"accounts_receivable_app",cb:function(e){if(!e.accounts_receivable_app)return t.setConfirm($t("sfa.open.receivable.payable.management"));i._getConfig(["accounts_receivable_status"],function(){if(2===parseInt((0<arguments.length&&void 0!==arguments[0]?arguments[0]:[]).find(function(e){return"accounts_receivable_status"===e.key}).value))return t.changeFun();t.setConfirm($t("sfa.crm.receivables.payables.manage"),$t("sfa.crm.open.receivables.payables.manage.step"),"#crmmanage/=/module-accountreceivable")})}})},beforeChanage:function(e){var t=this;if(!this.value)return n?void i.getLicense("FMCG.EXPENSE_LICENSE").then(function(e){if(!!e.license)return t.changeFun();t.setConfirm($t("sfa.crm.project.budget.reimbur.manage"),$t("sfa.crm.open.project.reimbursement.manage.step"),"#crmmanage/=/module-tpm")}):this.getReceivableStatus()},changeFun:function(e){this.value=!0;var t=this;s.setConfigValue({key:0===n?"enable_project_settlement":"enable_expense_cost_collection",value:"1"}).then(function(){t.value=!0},function(e){t.value=!1,s.alert(e)})}}})})})},_getConfig:function(e,t){s.getConfigValues(e).then(function(e){t&&t(e)},function(e){s.alert(e)})},getLicense:function(n){return new Promise(function(t,e){s.FHHApi({url:"/EM1HFMCGService/license/get",data:{appCode:n},success:function(e){0==e.Result.StatusCode?t(e.Value):s.alert(e.Result.FailureMessage)},complete:function(){s.hideLoading_tip()}},{errorAlertModel:1})})},tabHandle:function(e){e=$(e.currentTarget);e.addClass("cur").siblings().removeClass("cur"),this.idx=e.data("idx"),this.renderCon()},destroy:function(){_.each(this.widgets,function(e){e&&e.destory&&e.destory()})}});n.exports=u});
define("crm-setting/proresource/template/gantt-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-main crm-day-main crm-gantt"> <p>' + ((__t = $t("点击项目列表进入的页面")) == null ? "" : __t) + '：</p> <div class="crm-radio"></div> <p class="gantt">' + ((__t = $t("drag_time_line_gantt")) == null ? "" : __t) + '：</p> <div class="crm-radio crm-gantt"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/proresource/template/project-budget-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-wrapper"> <ul> <li class="card budget"> <div class="header"> <h3>' + ((__t = $t("crm.project.budget.enable")) == null ? "" : __t) + '</h3> <div class="project-switch-text">' + ((__t = $t("sfa.crm.project.switch.status")) == null ? "" : __t) + '<span class="switch"></span></div> </div> <p>' + ((__t = $t("sfa.crm.project.budget.info")) == null ? "" : __t) + '</p> </li> <li class="card budget"> <div class="header"> <h3>' + ((__t = $t("crm.project.cost.enable")) == null ? "" : __t) + '</h3> <div class="project-switch-text">' + ((__t = $t("sfa.crm.project.switch.status")) == null ? "" : __t) + '<span class="switch"></span></div> </div> <p>' + ((__t = $t("sfa.crm.project.cost.info")) == null ? "" : __t) + "</p> </li> </ul> </div>";
        }
        return __p;
    };
});
define("crm-setting/proresource/template/status-complete-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-wrapper"> <ul> <li class="card status"> <div class="header"> <h3>' + ((__t = $t("crm.project.stage_status.setting")) == null ? "" : __t) + "</h3> <div>" + ((__t = $t("crm.project.status.update")) == null ? "" : __t) + '<span class="switch"></span></div> </div> <p>' + ((__t = $t("crm.project.stage_status.setting.info")) == null ? "" : __t) + '</p> </li> <li class="card complete"> <div class="header"> <h3>' + ((__t = $t("crm.project.stage_complete.setting")) == null ? "" : __t) + "</h3> <div>" + ((__t = $t("crm.project.complete.update")) == null ? "" : __t) + '<span class="switch"></span></div> </div> <p>' + ((__t = $t("crm.project.stage_complete.setting.info")) == null ? "" : __t) + "</p> </li> </ul> </div>";
        }
        return __p;
    };
});
define("crm-setting/proresource/template/task-notice-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-wrapper"> <ul> <li class="card notice"> <div class="header"> <h3>' + ((__t = $t("sfa.crm.task.todo.timeout.reminder")) == null ? "" : __t) + '</h3> <div class="project-switch-text"><span class="switch"></span></div> </div> <p>' + ((__t = $t("sfa.crm.task.todo.timeout.info")) == null ? "" : __t) + "" + ((__t = $t("预览")) == null ? "" : __t) + '</p> <div class="notice-text time">' + ((__t = $t("通知时间")) == null ? "" : __t) + '</div> <div class="notice-text date"> ' + ((__t = $t("ProjectTaskObj.field.plan_start_date.label")) == null ? "" : __t) + ' <div class="date-select-wrapper"></div> </div> </li> <li class="card notice"> <div class="header"> <h3>' + ((__t = $t("sfa.crm.task.timeout.reminder")) == null ? "" : __t) + '</h3> <div class="project-switch-text"><span class="switch"></span></div> </div> <p>' + ((__t = $t("sfa.crm.task.timeout.info")) == null ? "" : __t) + "" + ((__t = $t("预览")) == null ? "" : __t) + '</p> <div class="notice-text time">' + ((__t = $t("通知时间")) == null ? "" : __t) + '</div> <div class="notice-text date"> ' + ((__t = $t("ProjectTaskObj.field.plan_end_date.label")) == null ? "" : __t) + ' <div class="date-select-wrapper"></div> </div> </li> </ul> </div>';
        }
        return __p;
    };
});
define("crm-setting/proresource/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("项目管理设置")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con"> <div class="crm-tab b-g-clean j-tab"> <span class="cur gantt" data-idx="0">' + ((__t = $t("甘特图设置")) == null ? "" : __t) + '</span> <span data-idx="1">' + ((__t = $t("工作时长设置")) == null ? "" : __t) + '</span> <span data-idx="2">' + ((__t = $t("状态与完成度设置")) == null ? "" : __t) + '</span> <span data-idx="3">' + ((__t = $t("sfa.crm.project.budget.setting")) == null ? "" : __t) + '</span> <!-- <span data-idx="4">' + ((__t = $t("sfa.crm.task.notification.settings")) == null ? "" : __t) + '</span> --> </div> <div class="tab-con"></div> </div>';
        }
        return __p;
    };
});