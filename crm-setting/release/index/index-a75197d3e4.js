define("crm-setting/index/index",["./template/index-html"],function(e,t,n){var i=e("./template/index-html"),c=CRM.control.getSettingData(),e=Backbone.View.extend({initialize:function(e){var t=this,n=0;t.setElement(e.wrapper);t.$el.html(i({day:168,date:"2016-04-01",buyers:18,users:8,verCode:CRM.control.verCode,data:c})),t.percent1=12,t.percent2=34,t.percent3=85,t.percent4=36,c[0]&&(_.each(c[0].top||[],function(e){CRM.control.verCode[e.code]&&(n+=1)}),n<3)&&t.$(".con-top li").width(100/n+"%")},render:function(){},events:{"click li":"clickHandle"},clickHandle:function(e){var e=e||window.event,t="",e=$(e.currentTarget).attr("data-type");e&&(t="#crm/setting/",-1==location.hash.indexOf("setting")&&(t="#crmmanage/=/module-"),CRM.control.navigate(t+e))}});n.exports=e});
define("crm-setting/index/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("后台主页")) == null ? "" : __t) + '</span></h2> </div> <!-- <div class="crm-email-detail__wrapper"></div> --> <div class="crm-module-con crm-scroll crm-new"> <ul class="con-top"> ';
            if (data[0] && data[0].top) {
                __p += " ";
                _.each(data[0].top, function(item, index) {
                    __p += " ";
                    if (verCode[item.code] && index != 2) {
                        __p += ' <li class="top-box" data-type=' + ((__t = item.hash) == null ? "" : __t) + '> <div class="top-box' + ((__t = index + 1) == null ? "" : __t) + '"> <div class="top-tit" >' + ((__t = item.text) == null ? "" : __t) + "</div> </div> </li> ";
                    }
                    __p += " ";
                    if (index == 2) {
                        __p += ' <li class="top-box "> <a href="' + ((__t = item.hash) == null ? "" : __t) + '" target="_blank" style="display: block;"> <div class="top-box' + ((__t = index + 1) == null ? "" : __t) + '"> <div class="top-tit">' + ((__t = item.text) == null ? "" : __t) + "</div> </div> </a> </li> ";
                    }
                    __p += " ";
                });
                __p += " ";
            }
            __p += ' </ul> <div class="con-bottom"> <div class="bottom-box right-box"> <!--权限管理--> ';
            if (data[1]) {
                __p += ' <div class="right-box-tit box-tit">' + ((__t = data[1].text) == null ? "" : __t) + '</div> <ul class="box-con"> ';
                for (var i = 0; i < data[1].children.length; i++) {
                    __p += " ";
                    if (verCode[data[1].children[i].code]) {
                        __p += ' <li data-type="' + ((__t = data[1].children[i].hash) == null ? "" : __t) + '"><a >' + ((__t = data[1].children[i].text) == null ? "" : __t) + "</a></li> ";
                    }
                    __p += " ";
                }
                __p += " </ul> ";
            }
            __p += " <!--业务定制--> ";
            if (data[2]) {
                __p += ' <div class="customer-box-tit box-tit">' + ((__t = data[2].text) == null ? "" : __t) + '</div> <ul class="box-con"> ';
                for (var i = 0; i < data[2].children.length; i++) {
                    __p += " ";
                    if (verCode[data[2].children[i].code]) {
                        __p += ' <li data-type="' + ((__t = data[2].children[i].hash) == null ? "" : __t) + '"><a >' + ((__t = data[2].children[i].text) == null ? "" : __t) + "</a></li> ";
                    }
                    __p += " ";
                }
                __p += " </ul> ";
            }
            __p += ' </div> <!--业务规则设置--> <div class="bottom-box rule-box"> ';
            if (data[3]) {
                __p += ' <div class="rule-box-tit box-tit">' + ((__t = data[3].text) == null ? "" : __t) + '</div> <ul class="box-con"> ';
                for (var i = 0; i < data[3].children.length; i++) {
                    __p += " ";
                    if (verCode[data[3].children[i].code]) {
                        __p += ' <li data-type="' + ((__t = data[3].children[i].hash) == null ? "" : __t) + '"><a >' + ((__t = data[3].children[i].text) == null ? "" : __t) + "</a></li> ";
                    }
                    __p += " ";
                }
                __p += " </ul> ";
            }
            __p += ' </div> <!--流程管理--> <div class="bottom-box flow-box"> ';
            if (data[4]) {
                __p += ' <div class="flow-box-tit box-tit">' + ((__t = data[4].text) == null ? "" : __t) + '</div> <ul class="box-con"> ';
                for (var i = 0; i < data[4].children.length; i++) {
                    __p += " ";
                    if (verCode[data[4].children[i].code]) {
                        __p += ' <li data-type="' + ((__t = data[4].children[i].hash) == null ? "" : __t) + '"><a >' + ((__t = data[4].children[i].text) == null ? "" : __t) + "</a></li> ";
                    }
                    __p += " ";
                }
                __p += " </ul> ";
            }
            __p += ' </div> <!--数据维护--> <div class="bottom-box data-box"> ';
            if (data[5]) {
                __p += ' <div class="data-box-tit box-tit">' + ((__t = data[5].text) == null ? "" : __t) + '</div> <ul class="box-con"> ';
                for (var i = 0; i < data[5].children.length; i++) {
                    __p += " ";
                    if (verCode[data[5].children[i].code]) {
                        __p += ' <li data-type="' + ((__t = data[5].children[i].hash) == null ? "" : __t) + '"><a >' + ((__t = data[5].children[i].text) == null ? "" : __t) + "</a></li> ";
                    }
                    __p += " ";
                }
                __p += " </ul> ";
            }
            __p += " <!--插件及API--> ";
            if (data[6]) {
                __p += ' <div class=" plugins-box-tit box-tit">' + ((__t = data[6].text) == null ? "" : __t) + '</div> <ul class="box-con"> ';
                for (var i = 0; i < data[6].children.length; i++) {
                    __p += " ";
                    if (verCode[data[6].children[i].code]) {
                        __p += ' <li data-type="' + ((__t = data[6].children[i].hash) == null ? "" : __t) + '"><a >' + ((__t = data[6].children[i].text) == null ? "" : __t) + "</a></li> ";
                    }
                    __p += " ";
                }
                __p += " </ul> ";
            }
            __p += ' </div> <!--<div class="bottom-box invoice-box">--> <!--<div class="invoice-box-tit box-tit">产品公告</div>--> <!--<ul class="box-con">--> <!--<li data-type="saleaction">销售流程管理销售流程管理销售流程管理</li>--> <!--<li data-type="businessflow">业务流程管理业务流程管理业务流程管理</li>--> <!--<li data-type="workflow">审批流描述描述描述描述描述描述描述描述描述描述描述描述</li>--> <!--<li data-type="">工作流描述描述描述描述描述描述描述描述描述描述描述描述</li>--> <!--</ul>--> <!--</div>--> </div> <!--<div class="con-footer">--> <!--<div class="footer-tit">--> <!--<span class="title">专业版</span><span class="footer-tip">扩展包描述</span>--> <!--</div>--> <!--<div class="footer-nav">--> <!--<div class="footer-box">--> <!--<div class="circle-box circle-box1"></div>--> <!--<div class="circle-tip">--> <!--<div class="circle-top-tip">剩余可用</div>--> <!--<div class="bottom-tip">' + ((__t = day) == null ? "" : __t) + '天</div>--> <!--</div>--> <!--</div>--> <!--<div class="footer-box">--> <!--<div class="circle-box circle-box2"></div>--> <!--<div class="circle-tip">--> <!--<div class="circle-top-tip">产品到期日期</div>--> <!--<div class="bottom-tip">' + ((__t = date) == null ? "" : __t) + '天</div>--> <!--</div>--> <!--</div>--> <!--<div class="footer-box">--> <!--<div class="circle-box circle-box3"></div>--> <!--<div class="circle-tip">--> <!--<div class="circle-top-tip">购买用户数</div>--> <!--<div class="bottom-tip">' + ((__t = buyers) == null ? "" : __t) + '个</div>--> <!--</div>--> <!--</div>--> <!--<div class="footer-box">--> <!--<div class="circle-box circle-box4"></div>--> <!--<div class="circle-tip">--> <!--<div class="circle-top-tip">当前已使用账户</div>--> <!--<div class="bottom-tip">' + ((__t = users) == null ? "" : __t) + "个</div>--> <!--</div>--> <!--</div>--> <!--</div>--> <!--</div>--> </div>";
        }
        return __p;
    };
});