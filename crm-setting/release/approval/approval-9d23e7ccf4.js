define("crm-setting/approval/action/action",["./sort/sort","./switchflow/switchflow","./switchflow/old"],function(e,t,n){e("./sort/sort");function o(e){this.manageScope=e.manageScope}var r=e("./switchflow/switchflow"),i=(e("./switchflow/old"),CRM.util);_.extend(o.prototype,{constructor:o,add:function(t){var n=this;e.async("paas-workflow/sdk",function(e){n._wf&&n._wf.destroy(),n._wf=new e(_.extend(t||{})),n._wf.on("refresh",function(e){n.manageScope.hasOwnProperty(e.entityId)?n.trigger("refresh","add"):n.trigger("refreshManageScope","add")})})},edit:function(t){var n=this;e.async("paas-workflow/sdk",function(e){n._wfedit&&n._wfedit.destroy(),n._wfedit=new e(_.extend(t.param||{})),n._wfedit.on("refresh",function(){n.trigger("refresh","edit")})})},copyadd:function(t){var n=this;e.async("paas-workflow/sdk",function(e){n._wfedit&&n._wfedit.destroy(),n._wfedit=new e(_.extend(t.param||{})),n._wfedit.on("refresh",function(e){n.manageScope.hasOwnProperty(e.entityId)?n.trigger("refresh","copyadd"):n.trigger("refreshManageScope","copyadd")})})},sort:function(t){var n=this;e.async("paas-workflow/lib",function(e){e.getModule("sortConfig").then(function(e){e.default.render({list:t.datas}).$on("refresh",function(e){n.trigger("refresh",e)})})})},switchflow:function(){this._switchflow||(this._switchflow=new r),this._switchflow.show()},del:function(e){var t=this,n=i.confirm($t("确认删除这个流程"),$t("删除"),function(){n.hide(),t.delPost(e.ids)});FS.MEDIATOR.trigger("paas.workflowoperate.load")},delPost:function(e){var t=this;i.FHHApi({url:"/EM1AAPPROVAL/Definition/Delete",data:{sourceWorkflowIds:e},success:function(e){0==e.Result.StatusCode?t.trigger("refresh","del"):i.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},enableConfirm:function(e){var t=this,n=e.enable?$t("启用"):$t("停用"),o=e.enable?$t("确认启用这个流程？"):$t("确认停用这个流程？"),r=i.confirm(o,n,function(){r.hide(),t.enablePost(e)})},enable:function(e){var t=this;"AccountObj"==e.apiName?t.fetchReport(function(){t.enableConfirm(e)}):t.enableConfirm(e),FS.MEDIATOR.trigger("paas.workflowoperate.load")},enablePost:function(t){var n=this;i.FHHApi({url:"/EM1AAPPROVAL/Definition/Enable",data:t,success:function(e){0==e.Result.StatusCode?n.trigger("refresh",t.enable?"enable":"unable"):i.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},fetchReport:function(t){var n=this;i.getConfigValue("3").then(function(e){1==e?i.alert($t("crm.客户报备开启提示")+">"+$t("规则设置 关闭客户报备。")):t&&t.call(n,e)},function(e){i.alert(e)})},destroy:function(){var t=this;_.each(["_wf"],function(e){t[e]&&t[e].destroy&&(t[e].destroy(),t[e]=null)})}},Backbone.Events),n.exports=o});
define("crm-setting/approval/action/sort/sort",["crm-widget/dialog/dialog","crm-widget/select/select","./template/tpl-html","./template/list-html"],function(t,e,r){var n=CRM.util,i=t("crm-widget/dialog/dialog"),a=t("crm-widget/select/select"),s=t("./template/tpl-html"),o=t("./template/list-html"),l=i.extend({options:{objectType:200022},attrs:{width:920,title:$t("排列优先级"),showBtns:!0,showScroll:!1,className:"crm-d-sort"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","dragstart tbody tr":"startHandle","dragend tbody tr":"endHandle","dragenter tbody tr":"enterHandle","dragover tbody tr":"overHandle","drop tbody tr":"dropHandle"},initialize:function(){var t=l.superclass.initialize.apply(this,arguments);return this.doLog("list","priorityconfirm"),t},initSelect:function(t){var r=this;r._select&&(r._select.destroy(),r._select=null),r._select=new a({$wrap:$(".select-con",this.element),zIndex:+this.get("zIndex")+10,defaultValue:null,size:1,options:_.map(t.datas,function(t){return{name:t.entityName,value:t.entityId}})}),this._select.on("change",function(t,e){r.renderList()})},getWorkFlowList:function(r){n.FHHApi({url:"/EM1AAPPROVAL/Definition/GetDefinitions",data:{entityId:this._select.getValue(),pageSize:100,subProcess:!1},success:function(t){var e;0==t.Result.StatusCode?((e=t.Value||{}).result=_.sortBy(e.result||[],"priority"),r&&r(e)):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},createMask:function(){var t=$('<div  class="tr drag-mask" draggable="true"></div>'),e=(this.draging,this.draged),r=this.wrap.offset(),i=e.offset();this.mask&&this.mask.remove(),t.css({width:e.width(),height:e.height()-1,position:"absolute",left:i.left-r.left,top:i.top-r.top,background:"#ffffff"}),t.on({dragover:this.overHandle,drop:function(t){return t.stopPropagation(),t.preventDefault(),!1}}),this.mask=t,this.wrap.append(t)},startHandle:function(t){var e=t.originalEvent;e.dataTransfer&&e.dataTransfer.setData("text",""),this.draging=$(t.currentTarget),this.trigger("dragstart",e)},endHandle:function(t){t=t.originalEvent;return this.mask&&this.mask.remove(),t.dataTransfer.clearData("text"),!1},enterHandle:function(t){t=$(t.currentTarget);this.draging.attr("drag_id")!==t.attr("drag_id")&&(this.draged=t,this.createMask(),this.draging.index()>this.draged.index()?this.draged.before(this.draging):this.draged.after(this.draging),this.mask.css({top:this.draging.offset().top-this.wrap.offset().top+1}))},overHandle:function(t){var e=t.originalEvent;return e.dataTransfer&&(e.dataTransfer.dropEffect="move"),t.preventDefault(),!1},dropHandle:function(t){var e=t.originalEvent;e.dataTransfer&&e.dataTransfer.clearData(),t.preventDefault()},show:function(t){var e=l.superclass.show.call(this);return this.isRender||(this.setContent(s()),this.wrap=$("table",this.element),this.isRender=!0),this.initSelect(t),this.renderList(),e},renderList:function(){var e=this;this.getWorkFlowList(function(t){e.element.find("tbody").html(o(_.extend({},t,{util:n,getTrigger:e.getTrigger}))),e.resizedialog()})},getTrigger:function(t){return t&&t.length?t.join(","):"--"},hide:function(){return l.superclass.hide.call(this)},setWorkFlowPriorities:function(t){var e=this;n.FHHApi({url:"/EM1AAPPROVAL/Definition/SetPriorities",data:{priorities:t},success:function(t){0==t.Result.StatusCode?(e.trigger("refresh","sort"),e.hide()):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:$(".b-g-btn",this.element)})},confirmHandle:function(t){var r=[];$("tbody .tr",this.element).each(function(t,e){r.push({workflowId:$(e).data("workflowid"),priority:t})}),r.length<2?this.hide():this.setWorkFlowPriorities(r)},cancelHandle:function(){this.hide()},doLog:function(t,e,r){var i=this.options;n.uploadLog(i.objectType,t||"list",{eventId:e||"view",eventType:r||"cl"})},destroy:function(){var e=this;_.each(["_select"],function(t){e[t]&&e[t].destroy&&(e[t].destroy(),e[t]=null)}),e.undelegateEvents()}});r.exports=l});
define("crm-setting/approval/action/sort/template/list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(result, function(item, index) {
                __p += ' <tr draggable=\'true\' class="tr" data-workflowid="' + ((__t = item.workflowId) == null ? "" : __t) + '" drag_id="' + ((__t = index) == null ? "" : __t) + '"> <td><span class="cursor"></span>' + ((__t = item.name || "--") == null ? "" : __t) + "</td> <td>" + ((__t = item.entityName || "--") == null ? "" : __t) + "</td> <td>" + ((__t = getTrigger(item.triggerNames)) == null ? "" : __t) + " </td> <td>" + ((__t = util.getEmployeeById(item.modifier) ? util.getEmployeeById(item.modifier).name : "--") == null ? "" : __t) + "</td> <td>" + ((__t = FS.moment(item.modifyTime).format("YYYY-MM-DD HH:mm:ss")) == null ? "" : __t) + "</td> <td>" + ((__t = item.enable ? $t("启用") : $t("停用")) == null ? "" : __t) + "</td> </tr> ";
            });
        }
        return __p;
    };
});
define("crm-setting/approval/action/sort/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="sort-wrap"> <div class="select-wrap clearfix"> <label class="select-label">' + ((__t = $t("对象：")) == null ? "" : __t) + '</label> <div class="select-con"></div> </div> <table class="tb"> <thead> <tr> <th class="th-name"> <span class="th-tit">' + ((__t = $t("审批流名称")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("关联对象")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("触发动作")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("最后修改人")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("最后修改时间")) == null ? "" : __t) + '</span> </th> <th class="th-name"> <span class="th-tit">' + ((__t = $t("状态")) == null ? "" : __t) + "</span> </th> </tr> </thead> <tbody> </tbody> </table> </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/action/switchflow/old-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form crm-g-form-switchflow"> <h3 class="title discript-title">' + ((__t = $t("说明：")) == null ? "" : __t) + '</h3> <p class="text-item">' + ((__t = $t("crm.审批关联对象")) == null ? "" : __t) + '</p> <p class="text-item">' + ((__t = $t("crm.触发审批")) == null ? "" : __t) + '</p> <p class="text-item">' + ((__t = $t("crm.开启审批")) == null ? "" : __t) + '</p> <p class="text-item">' + ((__t = $t("crm.切换审批流类型说明")) == null ? "" : __t) + '</p> <div class="separated-line"></div> ';
            _.each(list, function(item) {
                __p += ' <div class="radio-wrap fn-clear"> <h3 class="title radio-title">' + ((__t = item.title) == null ? "" : __t) + '</h3> <div class="crm-g-radio ' + ((__t = item.value != "0" ? "disabled-selected" : "") == null ? "" : __t) + " " + ((__t = item.value === "0" ? "disabled" : "") == null ? "" : __t) + '" data-key="' + ((__t = item.Key) == null ? "" : __t) + '" data-value="1" > <span class="text">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> </div> <div class="crm-g-radio ' + ((__t = item.value == "0" ? "state-active" : "") == null ? "" : __t) + '" data-key="' + ((__t = item.key) == null ? "" : __t) + '" data-value="0" > <span class="text">' + ((__t = $t("关闭")) == null ? "" : __t) + "</span> </div> </div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/action/switchflow/old",["./old-html","crm-widget/dialog/dialog"],function(t,e,n){var a=CRM.util,s=t("./old-html"),t=t("crm-widget/dialog/dialog");n.exports=t.extend({options:{objectType:200022},attrs:{title:$t("自由审批设置"),width:500,showBtns:!0,config:{21:{title:$t("销售订单 流程类型强制切换为 自由审批")},22:{title:$t("退货单 流程类型强制切换为 自由审批")}}},events:{"click .crm-g-radio":"radioHandle","click .b-g-btn":"_submitHandle","click .b-g-btn-cancel":"_closeHandle"},radioHandle:function(t){var t=$(t.target),e=t.data("key"),i=t.data("value"),e="21"==e?"1"==i?"SalesOrderOnConfirm":"SalesOrderOffConfirm":"1"==i?"ReturnedGoodsInvoiceOnConfirm":"ReturnedGoodsInvoiceOffConfirm";t.hasClass("disabled")||t.hasClass("disabled-selected")||(t.addClass("state-active"),t.siblings().removeClass("state-active disabled-selected"),this.doLog("unrestrained",e))},show:function(t){var e=this,i=n.exports.superclass.show.call(this);return this.getConfigList(function(t){e.setContent(s({list:t})),e.resizedialog()}),i},getConfigList:function(e){var i=this;a.getConfigValues(_.keys(this.get("config"))).then(function(t){e&&e(i.formatData(t))},function(t){a.alert(t)})},formatData:function(t){var e=this.get("config");return _.each(t,function(t){_.extend(t,e[t.key])}),t},_submitHandle:function(){var i=[],t=this.element.find(".state-active");_.each(t,function(t){var e={},t=$(t);e.key=t.data("key"),e.value=t.attr("data-value"),e.key&&e.value&&i.push(e)}),this.setConfigList(i)},setConfigList:function(t){var e=this;a.setConfigValues(t).then(function(){e.hide(),a.remind(1,$t("切换成功"))},function(t){a.alert(t)})},_closeHandle:function(){this.hide()},doLog:function(t,e,i){var n=this.options;a.uploadLog(n.objectType,t||"list",{eventId:e||"view",eventType:i||"cl"})},destroy:function(){return n.exports.superclass.destroy.call(this)}})});
define("crm-setting/approval/action/switchflow/switchflow",["./tpl-html","crm-widget/dialog/dialog"],function(t,e,s){var o=CRM.util,a=t("./tpl-html"),t=t("crm-widget/dialog/dialog");s.exports=t.extend({options:{objectType:200022},attrs:{title:$t("自由审批设置"),width:500,showBtns:!0,config:{SalesOrderObj:{title:$t("销售订单 流程类型强制切换为 自由审批")},ReturnedGoodsInvoiceObj:{title:$t("退货单 流程类型强制切换为 自由审批")}}},events:{"click .crm-g-radio":"radioHandle","click .b-g-btn":"_submitHandle","click .b-g-btn-cancel":"_closeHandle"},radioHandle:function(t){var t=$(t.target),e=t.data("key"),i=t.data("value"),e="SalesOrderObj"==e?"free_approvalflow"==i?"SalesOrderOnConfirm":"SalesOrderOffConfirm":"free_approvalflow"==i?"ReturnedGoodsInvoiceOnConfirm":"ReturnedGoodsInvoiceOffConfirm";t.hasClass("disabled")||t.hasClass("disabled-selected")||(t.addClass("state-active"),t.siblings().removeClass("state-active disabled-selected"),this.doLog("unrestrained",e))},show:function(t){var e=this,i=s.exports.superclass.show.call(this);return this.getConfigList(function(t){e.setContent(a({list:t})),e.resizedialog()}),i},getConfigList:function(e){var i=this;CRM.util.FHHApi({url:"/EM1AFLOW/Config/GetApprovalType",data:{entityIds:["SalesOrderObj","ReturnedGoodsInvoiceObj"]},success:function(t){0==t.Result.StatusCode&&e&&e(i.formatData(t.Value.result))}},{errorAlertModel:1})},formatData:function(t){var e,i=this.get("config"),s=[];for(e in t){var o=i[e];o.Key=e,o.value=t[e],s.push(o)}return s},_submitHandle:function(){var e={},t=this.element.find(".state-active");_.each(t,function(t){t=$(t);e[t.data("key")]=t.attr("data-value")}),this.setConfigList(e)},setConfigList:function(t){var e=this;CRM.util.FHHApi({url:"/EM1AFLOW/Config/SaveApprovalType",data:{entityFlowType:t},success:function(t){0==t.Result.StatusCode&&(e.hide(),o.remind(1,$t("切换成功")))}},{errorAlertModel:1})},_closeHandle:function(){this.hide()},doLog:function(t,e,i){var s=this.options;o.uploadLog(s.objectType,t||"list",{eventId:e||"view",eventType:i||"cl"})},destroy:function(){return s.exports.superclass.destroy.call(this)}})});
define("crm-setting/approval/action/switchflow/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form crm-g-form-switchflow"> <h3 class="title discript-title">' + ((__t = $t("说明：")) == null ? "" : __t) + '</h3> <p class="text-item">' + ((__t = $t("crm.审批关联对象")) == null ? "" : __t) + '</p> <p class="text-item">' + ((__t = $t("crm.触发审批")) == null ? "" : __t) + '</p> <p class="text-item">' + ((__t = $t("crm.开启审批")) == null ? "" : __t) + '</p> <p class="text-item">' + ((__t = $t("crm.切换审批流类型说明")) == null ? "" : __t) + '</p> <div class="separated-line"></div> ';
            _.each(list, function(item) {
                __p += ' <div class="radio-wrap fn-clear"> <h3 class="title radio-title">' + ((__t = item.title) == null ? "" : __t) + '</h3> <div class="crm-g-radio ' + ((__t = item.value != "approvalflow" ? "state-active" : "") == null ? "" : __t) + " " + ((__t = item.value === "approvalflow" ? "disabled" : "") == null ? "" : __t) + '" data-key="' + ((__t = item.Key) == null ? "" : __t) + '" data-value="free_approvalflow" > <span class="text">' + ((__t = $t("开启")) == null ? "" : __t) + '</span> </div> <div class="crm-g-radio ' + ((__t = item.value == "approvalflow" ? "state-active" : "") == null ? "" : __t) + '" data-key="' + ((__t = item.Key) == null ? "" : __t) + '" data-value="approvalflow" > <span class="text">' + ((__t = $t("关闭")) == null ? "" : __t) + "</span> </div> </div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/approval-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="workflow-content"> <div class="workflow-content-title">' + ((__t = $t("审批流程管理")) == null ? "" : __t) + ' <a class="crm-doclink fx-icon-question" href="https://help.fxiaoke.com/dbde/a539/509f/d455" target="_blank"></a> <a href="http://www.fxiaoke.com/mob/guide/crmdoc/video/?id=4" class="crm-ico-play el-link fx-link el-link--standard" target="_blank"> <span class="el-link--inner">' + ((__t = $t("了解更多")) == null ? "" : __t) + '</span> </a> </div> <div class="fix-free-switch"></div> <div class="fix-table-content"></div> <div class="free-table-content"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/approval/approval",[],function(e,n,t){var a=Backbone.View.extend({initialize:function(e){var n=this,e=(n.setElement(e.wrapper),n.$el.addClass("crm-workflow-table"),document.createElement("div"));e.className="workflow-manage",n.$el[0].appendChild(e)},render:function(){var t=this;e.async(["paas-paasui/vui","paas-workflow/lib"],function(e,n){n.getModule("manageList").then(function(n){t.inst=new Vue({el:t.$el.children(".workflow-manage")[0],render:function(e){return e(n.default)}})})})},destroy:function(){this.inst&&this.inst.$destroy()}});t.exports=a});
define("crm-setting/approval/approvalDetailDialog/approvalDetailDialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="workflow-detail"> <div class="config-li"> <div class="workflow-detail-comment">' + ((__t = $t("数据详情页，默认展示最新审批详情")) == null ? "" : __t) + '</div> <span class="config-radio required-show">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio required-show">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/approvalDetailDialog/approvalDetailDialog",["./approvalDetailDialog-html","crm-widget/select/select","crm-widget/dialog/dialog"],function(e,t,l){var i=CRM.util,n=e("./approvalDetailDialog-html");Select=e("crm-widget/select/select"),Dialog=e("crm-widget/dialog/dialog"),l.exports=Dialog.extend({attrs:{title:$t("审批详情展示配置"),width:500,showBtns:!0},events:{"click .required-show":"radioHandleShowed","click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"_closeHandle"},radioHandleShowed:function(e){e&&e.stopPropagation();var t=this,l=$(e.target).closest(".required-show"),i=t.element.find(".config-li .required-show"),e=$(e.target).closest(".required-show").index();l.hasClass("active")||(i.removeClass("active"),l.addClass("active"),t.showed=e-1),0===t.showed?t.getObject():t.element.find(".selectBox").remove()},renderSelect:function(e){var t,l,i=this;i.element.find(".selectBox").length||(t=$('<div class="config-li selectBox">\n                                <div class="workflow-detail-comment" >\n                                <label class="select-label">'.concat($t("关联对象"),'</label>\n                                <div class="select-con"></div>\n                                </div>\n                                </div>')),i.element.find(".workflow-detail").append(t),l=[],i.entityList.forEach(function(t){e.find(function(e){return e.value===t})&&l.push(t)}),i.selectView=FxUI.create({wrapper:t.find(".select-con")[0],template:'<fx-select\n                           :el-style="{width:\'350px\'}"\n                            :placeholder="$t(\'请选择\')"\n                            size="small"\n                            v-model="value"\n                            :options="options"\n                            multiple\n                            clearable\n                            filterable\n                            collapse-tags\n                            @change="onChange">\n                          </fx-select>',data:function(){return{value:l,options:e}},methods:{onChange:function(e){var t=e.indexOf("ALL");"ALL"===e[e.length-1]?this.value=["ALL"]:-1<t&&this.value.splice(t,1),i.entityList=this.value}}}))},confirmHandle:_.debounce(function(){var t=this,e=(1===t.showed&&delete t.entityList,{flowType:"approvalflow",flowConfigs:[{type:"showLatestApprovalflow",value:{enable:0===t.showed,entityList:t.entityList||[]},terminal:"ALL"}]});t.element.find(".b-g-btn").prepend('<i class="el-icon-loading debounce"></i>'),i.FHHApi({url:"/EM1AFLOW/Config/Save",data:e,success:function(e){t._closeHandle()},complete:function(){t.element.find(".debounce").remove()}},{errorAlertModel:2})},1e3,!0),show:function(){var t=this;return i.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"approvalflow",types:["showLatestApprovalflow"],terminal:"ALL"},success:function(e){e=e.Value.values.showLatestApprovalflow,t.showed=e&&e.enable?0:1,e=e&&e.entityList;t.entityList=e||[],0===t.showed&&t.getObject(),t.initBtnStatus()}},{errorAlertModel:2}),result=l.exports.superclass.show.call(this),t.setContent(n()),t.resizedialog(),result},getObject:function(){var t=this;i.FHHApi({url:"/EM1AAPPROVAL/Metadata/GetSupportEntity",success:function(e){0==e.Result.StatusCode&&((e=_.map(e.Value.apiAndDisplayNames,function(e){return{label:e.displayName,value:e.entityId}})).unshift({label:$t("全部对象"),value:"ALL"}),t.renderSelect(e))}},{errorAlertModel:2})},initBtnStatus:function(){this.element.find(".config-li .required-show")[this.showed].classList.add("active")},_closeHandle:function(){this.hide()},destroy:function(){return l.exports.superclass.destroy.call(this)}})});
define("crm-setting/approval/approvalRejectDialog/approvalRejectDialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="workflow-reject-reason"> <div class="config-li"> <div class="workflow-reject-comment">' + ((__t = $t("重新提交或指定节点处理的默认选项为")) == null ? "" : __t) + '</div> <span class="config-radio required-reason">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio required-reason">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </div> <div class="config-li"> <div class="workflow-reject-comment"> ' + ((__t = $t("驳回方式，默认选项为")) == null ? "" : __t) + ' </div> <span class="config-radio reject-method">' + ((__t = $t("驳回并结束此流程")) == null ? "" : __t) + '</span> <span class="config-radio reject-method">' + ((__t = $t("驳回至指定节点")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/approvalRejectDialog/approvalRejectDialog",["./approvalRejectDialog-html","crm-widget/dialog/dialog"],function(e,t,i){var a=CRM.util,o=e("./approvalRejectDialog-html"),e=e("crm-widget/dialog/dialog");i.exports=e.extend({attrs:{title:$t("审批驳回配置"),width:500,showBtns:!0},events:{"click .required-reason":"radioHandleRequired","click  .reject-method":"radioHandleRejected","click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"_closeHandle"},radioHandleRequired:function(e){e&&e.stopPropagation();var t=$(e.target).closest(".required-reason"),i=this.element.find(".config-li .required-reason"),e=$(e.target).closest(".required-reason").index();t.hasClass("active")||(i.removeClass("active"),t.addClass("active"),this.required=e-1)},radioHandleRejected:function(e){e&&e.stopPropagation();var t=$(e.target).closest(".reject-method"),i=this.element.find(".config-li .reject-method"),e=$(e.target).closest(".reject-method").index();t.hasClass("active")||(i.removeClass("active"),t.addClass("active"),this.rejected=e-1)},confirmHandle:function(){var t=this,e={flowType:"approvalflow",flowConfigs:[{type:"REJECT_TO_RETURN_CURRENT_ACTIVITY",value:0===t.required,terminal:"ALL"},{type:"defaultOptionOfRejectionMethod",value:0===t.rejected?"DIRECT_REJECTION":"REJECT_TO_THE_NODE",terminal:"ALL"}]};a.FHHApi({url:"/EM1AFLOW/Config/Save",data:e,success:function(e){t._closeHandle()}},{errorAlertModel:2})},show:function(e){var t=this;return a.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"approvalflow",types:["REJECT_TO_RETURN_CURRENT_ACTIVITY","defaultOptionOfRejectionMethod"],terminal:"ALL"},success:function(e){e=e.Value.values;t.required=e.REJECT_TO_RETURN_CURRENT_ACTIVITY?0:1,t.rejected="DIRECT_REJECTION"===e.defaultOptionOfRejectionMethod?0:1,t.initBtnStatus()}},{errorAlertModel:2}),result=i.exports.superclass.show.call(this),t.setContent(o()),t.resizedialog(),result},initBtnStatus:function(){this.element.find(".config-li .required-reason")[this.required].classList.add("active"),this.element.find(".config-li .reject-method")[this.rejected].classList.add("active")},_closeHandle:function(){this.hide()},destroy:function(){return i.exports.superclass.destroy.call(this)}})});
define("crm-setting/approval/approvalRequiredDialog/approvalRequiredDialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="todo-config-config workflow-approval-config"> <div class="config-li"> <div class="workflow-approval-comment">' + ((__t = $t("审批意见必填")) == null ? "" : __t) + '</div> <div> <span class="title">' + ((__t = $t("同意")) == null ? "" : __t) + '</span> <span class="config-radio agree-approval ">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio agree-approval">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </div> <div> <span class="title">' + ((__t = $t("驳回")) == null ? "" : __t) + '</span> <span class="config-radio reject-approval">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio reject-approval">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </div> </div> <div class="config-li"> <div class="workflow-approval-comment">' + ((__t = $t("手动处理审批时，默认填充意见（同意、不同意）")) == null ? "" : __t) + '</div> <span class="config-radio fill-approval">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio fill-approval">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </div> <div class="config-li"> <div class="workflow-approval-comment">' + ((__t = $t("已处理审批意见，默认全部展开")) == null ? "" : __t) + '</div> <span class="config-radio expand-approval">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio expand-approval">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/approvalRequiredDialog/approvalRequiredDialog",["./approvalRequiredDialog-html","crm-widget/dialog/dialog"],function(e,a,i){var l=CRM.util,t=e("./approvalRequiredDialog-html"),e=e("crm-widget/dialog/dialog");i.exports=e.extend({attrs:{title:$t("审批意见配置"),width:500,showBtns:!0},events:{"click .agree-approval":"radioHandleAgree","click .reject-approval":"radioHandleReject","click .fill-approval":"radioHandleFill","click .expand-approval":"radioHandleExpand","click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"_closeHandle"},radioHandleAgree:function(e){e&&e.stopPropagation();var a=$(e.target).closest(".agree-approval"),i=this.element.find(".config-li .agree-approval"),e=$(e.target).closest(".agree-approval").index();a.hasClass("active")||(i.removeClass("active"),a.addClass("active"),this.agree=e-1)},radioHandleReject:function(e){e&&e.stopPropagation();var a=$(e.target).closest(".reject-approval"),i=this.element.find(".config-li .reject-approval"),e=$(e.target).closest(".reject-approval").index();a.hasClass("active")||(i.removeClass("active"),a.addClass("active"),this.reject=e-1)},radioHandleFill:function(e){e&&e.stopPropagation();var a=$(e.target).closest(".fill-approval"),i=this.element.find(".config-li .fill-approval"),e=$(e.target).closest(".fill-approval").index();a.hasClass("active")||(i.removeClass("active"),a.addClass("active"),this.default=e-1)},radioHandleExpand:function(e){e&&e.stopPropagation();var a=$(e.target).closest(".expand-approval"),i=this.element.find(".config-li .expand-approval"),e=$(e.target).closest(".expand-approval").index();a.hasClass("active")||(i.removeClass("active"),a.addClass("active"),this.expand=e-1)},confirmHandle:function(){var a=this,e={flowType:"approvalflow",flowConfigs:[{type:"agreeOpinionRequired",value:0===a.agree,terminal:"ALL"},{type:"rejectOpinionRequired",value:0===a.reject,terminal:"ALL"},{type:"fillOpinionByDefault",value:0===a.default,terminal:"ALL"},{type:"defaultExpandAllOpinion",value:0===a.expand,terminal:"ALL"}]};l.FHHApi({url:"/EM1AFLOW/Config/Save",data:e,success:function(e){a._closeHandle(),FxUI.Message({message:$t("保存成功"),type:"success"})}},{errorAlertModel:2})},show:function(e){var a=this;return l.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"approvalflow",types:["opinionRequired","agreeOpinionRequired","rejectOpinionRequired","fillOpinionByDefault","defaultExpandAllOpinion"],terminal:"ALL"},success:function(e){e=e.Value.values;a.agree=void 0!==e.agreeOpinionRequired?Number(!e.agreeOpinionRequired):e.opinionRequired?0:1,a.reject=void 0!==e.rejectOpinionRequired?Number(!e.rejectOpinionRequired):e.opinionRequired?0:1,a.default=e.fillOpinionByDefault?0:1,a.expand=e.defaultExpandAllOpinion?0:1,a.initBtnStatus()}},{errorAlertModel:1}),result=i.exports.superclass.show.call(this),a.setContent(t()),a.resizedialog(),result},initBtnStatus:function(){var e=this;e.element.find(".config-li .agree-approval")[e.agree].classList.add("active"),e.element.find(".config-li .reject-approval")[e.reject].classList.add("active"),e.element.find(".config-li .fill-approval")[e.default].classList.add("active"),e.element.find(".config-li .expand-approval")[e.expand].classList.add("active")},_closeHandle:function(){this.hide()},destroy:function(){return i.exports.superclass.destroy.call(this)}})});
define("crm-setting/approval/detail/detail",["crm-modules/common/util","crm-modules/common/slide/slide","../ndetail/ndetail","../flowconfig/flowconfig","./template/updateinfo-tpl-html","./template/tpl-html","base-modules/ui/scrollbar/scrollbar","crm-widget/table/table"],function(e,t,o){var a=e("crm-modules/common/util"),i=e("crm-modules/common/slide/slide"),n=e("../ndetail/ndetail"),l=e("../flowconfig/flowconfig"),r=(e("./template/updateinfo-tpl-html"),e("./template/tpl-html")),s=(ScrollBar=e("base-modules/ui/scrollbar/scrollbar"),e("crm-widget/table/table")),c=i.extend({options:{showMask:!1,width:800,zIndex:500,className:"crm-d-workflow crm-d-detail crm-nd-detail",entry:"crm"},events:{"click .b-list":"toggleBtns","mouseleave .b-list":"toggleBtns","click .nav-item":"switchHandle","click [data-action]":"actionHandle"},_doAction:function(t,o){var i=this;if("edit"==t){if(0==i.options.entityActive)return void a.alert($t("对象已禁用不能编辑流程"));if(1==i.options.entityDeleted)return void a.alert($t("对象已删除不能编辑流程"))}e.async("../action/action",function(e){i._action||(i._action=new e({manageScope:i.options.manageScope}),i._action.on("refresh",function(e){"del"==e?(a.remind(1,$t("删除成功")),i.hide()):("enable"!=e&&"unable"!=e||(i.options.enable="enable"==e),i.refresh()),i.trigger("refresh",e)}),i._action.on("refreshManageScope",function(e){i.refresh(),i.trigger("refreshManageScope",e)})),i._action[t]&&i._action[t](o)})},showDetail:function(e){this.initConfig(e.workflowId,!1,!0)},showTriggerCondition:function(e){var t=this,o=e.ruleId;if(t.ruleList[o])return t.showUnConditionDialog(t.ruleList[o],t.options.entityId),!1;a.FHHApi({url:"/EM1AFLOW/PaaS/GetHistoryRule",data:{flowType:"approvalflow",ruleId:o},success:function(e){e.Value.rule&&(t.ruleList[o]=e.Value.rule,t.showUnConditionDialog(t.ruleList[o],t.options.entityId))}},{errorAlertModel:1})},showUnConditionDialog:function(o,i){var n=this;e.async(["paas-paasui/ui","paas-workflow/sdk"],function(e,t){window.PaasUI.getComponent("FilterAnalyze").then(function(e){n.logAnalyze=new e({model:new Backbone.Model({apiName:i,fromApp:"bpm",fromModule:"filter",originalConditions:o,localVariables:t.Utils.getLocalVariables()})}),n.logAnalyze.renderFilterLabel({callback:function(){a.alert(n.logAnalyze.$el[0].outerHTML,"",{width:575,title:$t("流程触发条件")})}})})})},actionHandle:function(e){var t=this,o={},i=this.options,n=$(e.currentTarget).attr("data-action");if("hide"==n)return this.hide(),!1;switch(n){case"edit":o={param:{flowId:i.workflowId,entityId:i.entityId},zIndex:1e3};break;case"enable":o={sourceWorkflowId:i.srcWorkflowId,enable:!i.enable},i.enable||this._doAction("fetchReport");break;case"del":o={ids:[i.srcWorkflowId]};break;case"copyadd":o={param:{flowId:i.workflowId,entityId:i.entityId,actionType:"copyadd"},zIndex:1e3}}this.checkWorkFlowNum(n).then(function(){t._doAction(n,o)},function(){})},checkWorkFlowNum:function(i){var n=!1;return new Promise(function(t,o){a.FHHApi({url:"/EM1AAPPROVAL/Definition/GetNumberCheck",success:function(e){(0===e.Result.StatusCode?(n=!0,CRM.setLocal("workflowSubProcessSupport",e.Value.subProcess),["copyadd"].includes(i)&&!e.Value.allowable?(a.alert($t("crm.购买企业版或申请扩展包提醒")),o):t):(a.alert(e.Result.FailureMessage),o))()},complete:function(){n||o()}},{errorAlertModel:1})})},switchHandle:function(e){var e=$(e.currentTarget),t=e.attr("data-name");this.$(".layout-scroll [data-name="+this.getSelected()+"]").hide(),e.addClass("nav-selected").siblings().removeClass("nav-selected"),this.setSelected(t),this.renderComponents()},toggleBtns:function(e){"click"==e.type?$(e.currentTarget).toggleClass("active"):$(e.currentTarget).removeClass("active")},get:function(e){var t=this.options;return e=e.split("."),_.each(e,function(e){t=t[e]}),t},set:function(e,o){var i,n=this.options;e=e.split("."),i=e.length,_.each(e,function(e,t){t==i-1?n[e]=o:n=n[e]||{}})},setBatch:function(e){var o=this;_.each(e,function(e,t){o.set(t,e)})},show:function(e){this.setBatch(e),i.prototype.show.apply(this),1!=this.page&&(this.page=1,this.toPage)&&this.toPage.reset(),this.render(),this.initScroll()},refresh:function(){this.render()},render:function(){var e=this.options;e.selected=this.getSelected(),this.$el.html(r(_.extend({},e))),this.$(".nav-selected").click()},renderComponents:function(){var e=this.getSelected().toLocaleLowerCase(),t=e.slice(0,1).toLocaleUpperCase()+e.slice(1);this.$(".layout-scroll [data-name="+e+"]").show(),this["init"+t]&&this["init"+t]()},renderToPage:function(e){},initInfo:function(){this.initNdetail(),this.initTrigger()},initNdetail:function(){var e=this.options;this._info&&this._info.destroy(),this._info=new n({el:this.$(".info-con")}),this._info.render(e)},initUpdateinfo:function(){this.ruleList={},this.renderHistoryTable()},renderHistoryTable:function(){var i=this;i.historyTable=new s({$el:i.$el.find(".flow-update-info .content"),requestType:"FHHApi",url:"/EM1HFLOW/DefinitionConfig/GetDefinitionHistoryBySrcId",showPage:!0,showMultiple:!1,hideIconSet:!0,search:{placeHolder:$t("flow.enter.version"),type:"workflowId",pos:"T"},paramFormat:function(e){return e=Object.assign(e,{type:"approvalflow",sourceWorkflowId:i.options.srcWorkflowId})},formatData:function(e){return{totalCount:e.total,data:e.data}},columns:[{data:"workflowId",title:$t("版本号"),width:200},{data:"modifyTime",title:$t("修改时间"),width:200,dataType:4},{data:"modifier",title:$t("修改人"),width:160,render:function(e,t,o){return FS.contacts.getEmployeeById(e,{includeStop:!0,includeStopByRequest:!0})?FS.contacts.getEmployeeById(e,{includeStop:!0,includeStopByRequest:!0}).name:"--"}},{data:null,lastFixed:!0,width:180,title:$t("操作"),render:function(e,t,o,i,n){var a=FS.util.getUserAttribute("paasFlowEditHistory")?'<a href="javascript:void(0)" class="edit-history">'.concat($t("编辑"),"</a>&nbsp;"):"";return(a+=o.history?'<a href="javascript:void(0)" class="view-recover">'.concat($t("恢复"),"</a>&nbsp;"):"")+'<a href="javascript:void(0)" class="view-detail">'.concat($t("流程配置"),"</a>&nbsp;")+(o.ruleId?'<a href="javascript:void(0)" class="view-rule">'.concat($t("触发条件"),"</a>&nbsp;"):"")+(o.modifyRecord&&o.modifyRecord.length?'<a href="javascript:void(0)" class="log-detail">'.concat($t("修改记录"),"</a>&nbsp;"):"")}}]}),i.historyTable.on("trclick",function(e,t,o){o.hasClass("view-detail")?(window.Fx&&window.Fx.log&&window.Fx.log("paas-flow-flow-configuration","cl",{module:"approvalflow"}),i.showDetail(e)):o.hasClass("edit-history")?(window.Fx&&window.Fx.log&&window.Fx.log("edit_history","cl",{module:"approvalflow"}),i.historyEditHandler(e)):o.hasClass("view-recover")?(window.Fx&&window.Fx.log&&window.Fx.log("paas-flow-definitionLog-revert","cl",{module:"approvalflow"}),i.restoreVersion(e)):o.hasClass("log-detail")?(window.Fx&&window.Fx.log&&window.Fx.log("view_history","cl",{module:"approvalflow"}),i.historyLogHandler(e)):o.hasClass("view-rule")&&(window.Fx&&window.Fx.log&&window.Fx.log("view_rule","cl",{module:"approvalflow"}),i.showTriggerCondition(e))}),i.historyTable._search.on("search",function(){window.Fx&&window.Fx.log("paas-flow-searchversion","cl",{module:"approvalflow"})})},restoreVersion:function(t){var o=this;e.async("../action/action.js",function(e){o._action=new e({manageScope:o.options.manageScope}),o._action.edit&&o._action.edit({zIndex:1e3,param:{flowId:t.workflowId,entityId:o.options.entityId,isRestore:!0}}),o._action.on("refresh",function(e){o.trigger("refresh",e)})})},historyEditHandler:function(e){this.initConfig(e.workflowId,!0,!1,!0)},historyLogHandler:function(e){var t=this;this.logTable=FxUI.create({template:'<fx-dialog\n          :visible="true"\n          size="small"\n          :append-to-body="true"\n          draggable\n          title="'.concat($t("修改记录"),'"\n          @close="close">\n          <div ref="table" style="height: 500px;width: 100%"></div>\n          </fx-dialog>'),mounted:function(){var e=this;setTimeout(function(){e.init()},0)},methods:{init:function(){this.table=new s({$el:$(this.$refs.table),showPage:!1,showMultiple:!1,doStatic:!0,hideIconSet:!0,columns:[{data:"modifyTime",title:$t("修改时间"),dataType:4},{data:"userId",title:$t("修改人"),render:function(e,t,o){return a.getEmployeeById(e)?a.getEmployeeById(e).name:"--"}}]}),this.table.doStaticData(e.modifyRecord)},close:function(){this.table&&this.table.destroy(),t.logTable.$destroy()}}})},getEmployeeById:function(e){e=a.getEmployeeById(e);return e||{name:$t("停用员工"),profileImage:""}},formatTime:function(e){return(FS.moment().isSame(FS.moment(e),"day")?$t("今天"):FS.moment(e).format("YYYY-MM-DD"))+" "+FS.moment(e).format("HH:mm:ss")},initTrigger:function(){var i=this;i.fetchDetail(i.options.workflowId,function(o){i.propertyView&&i.propertyView.destroy(),e.async(["paas-paasui/ui","paas-workflow/sdk"],function(e,t){window.PaasUI.getComponent("FilterAnalyze").then(function(e){i.propertyView=new e({model:new Backbone.Model({apiName:o.workflow.entityId,originalConditions:o.workflowRule,dataLockType:o.dataLockType,triggerNames:o.triggerNames,forDetail:!0,fromApp:"workflow",fromModule:"filter",localVariables:t.Utils.getLocalVariables(),customVariables:o.workflow.customVariableTable})}),i.$el.find(".trigger-sec").empty().append(i.propertyView.$el)})})})},fetchDetail:function(e,t){return a.FHHApi({url:"/EM1AAPPROVAL/Definition/Detail",data:{workflowId:e},success:function(e){0===e.Result.FailureCode&&t(e.Value)}})},initConfig:function(e){var t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],o=2<arguments.length&&void 0!==arguments[2]&&arguments[2],i=3<arguments.length?arguments[3]:void 0,n=this;n._fconfig&&n._fconfig.destroy(),void 0===e&&(e=n.options.workflowId),n.fetchDetail(e,function(e){e={pendingEl:n.$(".flow-config"),id:n.get("workflowId"),entityId:n.get("entityId"),data:e,isEdit:i,showFlowChartFullScreen:t,showFlowFullScreen:o};n._fconfig=new l(e)})},initScroll:function(){this.scroll&&this.scroll.destroy&&this.scroll.destroy(),this.scroll=new ScrollBar($(".layout-scroll",this.$el.find(".layout-scroll")))},setSelected:function(e){this.set("selected",e)},getSelected:function(){return this.get("selected")?this.get("selected"):"info"},destroy:function(){i.prototype.destroy.call(this)}});o.exports=c});
define("crm-setting/approval/detail/template/agreeitem-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<h3 class="detail-sub-tit">' + ((__t = $t("全部确认后")) == null ? "" : __t) + '</h3> <div class="detail-item agree-item"> <h3 class="detail-title">' + ((__t = $t("CRM提醒")) == null ? "" : __t) + "</h3> ";
            if (!pass.send_qixin.length) {
                __p += ' <div class="empty-con">' + ((__t = $t("无操作")) == null ? "" : __t) + "</div> ";
            } else {
                __p += ' <div class="con-wrap"> ';
                _.each(pass.send_qixin, function(qixin) {
                    __p += ' <div class="con-sec"> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = $t("提醒人")) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = qixin.recipients.join("、")) == null ? "" : __t) + '</div> </div> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = $t("提醒标题")) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = qixin.title) == null ? "" : __t) + '</div> </div> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = $t("提醒内容")) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = qixin.content) == null ? "" : __t) + "</div> </div> </div> ";
                });
                __p += " </div> ";
            }
            __p += ' <h3 class="detail-title">' + ((__t = $t("字段变更")) == null ? "" : __t) + "</h3> ";
            if (!pass.updates.length) {
                __p += ' <div class="empty-con">' + ((__t = $t("无变更")) == null ? "" : __t) + "</div> ";
            } else {
                __p += ' <div class="con-wrap"> ';
                _.each(pass.updates, function(data) {
                    __p += ' <div class="con-sec"> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = data.field) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = $t("变更为")) == null ? "" : __t) + "" + ((__t = data.value) == null ? "" : __t) + "</div> </div> </div> ";
                });
                __p += " </div> ";
            }
            __p += ' </div> <h3 class="detail-sub-tit">' + ((__t = $t("全部驳回后")) == null ? "" : __t) + '</h3> <div class="detail-item reject-item"> <h3 class="detail-title">' + ((__t = $t("CRM提醒")) == null ? "" : __t) + "</h3> ";
            if (!reject.send_qixin.length) {
                __p += ' <div class="empty-con">' + ((__t = $t("无操作")) == null ? "" : __t) + "</div> ";
            } else {
                __p += ' <div class="con-wrap"> ';
                _.each(reject.send_qixin, function(qixin) {
                    __p += ' <div class="con-sec"> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = $t("提醒人")) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = qixin.recipients.join("、")) == null ? "" : __t) + '</div> </div> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = $t("提醒标题")) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = qixin.title) == null ? "" : __t) + '</div> </div> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = $t("提醒内容")) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = qixin.content) == null ? "" : __t) + "</div> </div> </div> ";
                });
                __p += " </div> ";
            }
            __p += ' <h3 class="detail-title">' + ((__t = $t("字段变更")) == null ? "" : __t) + "</h3> ";
            if (!reject.updates.length) {
                __p += ' <div class="empty-con">' + ((__t = $t("无变更")) == null ? "" : __t) + "</div> ";
            } else {
                __p += ' <div class="con-wrap"> ';
                _.each(reject.updates, function(data) {
                    __p += ' <div class="con-sec"> <div class="sec-item fn-clear"> <label class="sec-item-label">' + ((__t = data.field) == null ? "" : __t) + '</label> <div class="sec-item-con">' + ((__t = $t("变更为")) == null ? "" : __t) + "" + ((__t = data.value) == null ? "" : __t) + "</div> </div> </div> ";
                });
                __p += " </div> ";
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/detail/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-d-layout"> <div class="header d-top"> <div class="crm-d-comp-tit"> <span class="d-obj-icon"></span> <span class="obj-name">' + ((__t = $t("审批流程")) == null ? "" : __t) + '</span> <div class="tit"> <div title="' + __e(name) + '">' + __e(name) + '</div> </div> </div> <div class="operate"> <div class="crm-d-btn-operate"> ';
            if (currentFlowType !== "free_approvalflow" && showEdit && controlStatus !== "controlled") {
                __p += ' <div class="crm-btn crm-btn-primary b-item b-edit" data-action="edit">' + ((__t = $t("编辑")) == null ? "" : __t) + "</div> ";
            }
            __p += ' <div class="b-item b-list"> <span class="crm-btn show-more">...</span> <ul class="ops-list"> ';
            if (currentFlowType !== "free_approvalflow" || currentFlowType === "free_approvalflow" && enable) {
                __p += ' <li class="opts-list-item" data-action="enable">' + ((__t = enable ? $t("停用") : $t("启用")) == null ? "" : __t) + "</li> ";
            }
            __p += " ";
            if (currentFlowType !== "free_approvalflow" && showCopy && controlStatus !== "controlled") {
                __p += ' <li class="opts-list-item" data-action="copyadd">' + ((__t = $t("复制并新建")) == null ? "" : __t) + "</li> ";
            }
            __p += " ";
            if (!enable && controlStatus !== "controlled") {
                __p += ' <li class="opts-list-item" data-action="del">' + ((__t = $t("删除")) == null ? "" : __t) + "</li> ";
            }
            __p += ' </ul> </div> </div> </div> <div class="d-g-btns"> <span data-action="hide" class="h-btn" title=\'' + ((__t = $t("关闭")) == null ? "" : __t) + '\'></span> </div> </div> <div class="workflow-slide-navigation"> <div class="nav-item-group"> <span data-name="info" class="nav-item ' + ((__t = selected == "info" ? "nav-selected" : "") == null ? "" : __t) + '">' + ((__t = $t("详细信息")) == null ? "" : __t) + '</span> <span data-name="config" class="nav-item ' + ((__t = selected == "config" ? "nav-selected" : "") == null ? "" : __t) + '">' + ((__t = $t("流程配置")) == null ? "" : __t) + '</span> <span data-name="updateinfo" class="nav-item ' + ((__t = selected == "updateinfo" ? "nav-selected" : "") == null ? "" : __t) + '">' + ((__t = $t("历史版本")) == null ? "" : __t) + '</span> </div> </div> <div class="layout-scroll" style="padding-bottom: 0;"> <div class="flow-info" data-name="info"> <div class="info-con"></div> <div class="trigger-con"></div> </div> <div class="flow-config" data-name="config"> </div> <div class="flow-update-info" data-name="updateinfo"> <div class="crm-d-ndetail"> <h3 class="head-title">' + ((__t = $t("历史版本")) == null ? "" : __t) + '</h3> <div class="content"> </div> </div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/approval/detail/template/updateinfo-tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(data, function(item) {
                __p += ' <div class="o-item"> <span class="o-circle"></span> <div class="o-time">' + ((__t = formatTime(item.modifyTime)) == null ? "" : __t) + "</div> ";
                if (getName(item.modifier).profileImage) {
                    __p += ' <div class="img-wrap" data-cardid="' + ((__t = item.modifier) == null ? "" : __t) + '"> <img src="' + ((__t = getName(item.modifier).profileImage) == null ? "" : __t) + '"> </div> ';
                }
                __p += ' <div class="o-name">' + ((__t = getName(item.modifier).name) == null ? "" : __t) + '</div> <a class="view-detail" href="#" data-workflowId="' + ((__t = item.workflowId) == null ? "" : __t) + '">' + ((__t = $t("查看流程图")) == null ? "" : __t) + "</a> ";
                if (item.ruleId) {
                    __p += ' <a class="view-trigger-condition" href="#" data-ruleid="' + ((__t = item.ruleId) == null ? "" : __t) + '">' + ((__t = $t("查看流程触发条件")) == null ? "" : __t) + "</a> ";
                }
                __p += " </div> ";
            });
        }
        return __p;
    };
});
define("crm-setting/approval/flowconfig/flowconfig",["./tpl-html"],function(o,n,e){o("./tpl-html");var i=Backbone.View.extend({initialize:function(){this.render()},render:function(){var e=this;o.async("paas-workflow/sdk",function(o){var n={model:new Backbone.Model(e.options.data),el:e.options.pendingEl,readonly:!0,isEdit:e.options.isEdit,showViewSubProcessor:!0,showFlowChartFullScreen:e.options.showFlowChartFullScreen,showFlowFullScreen:e.options.showFlowFullScreen};new o.Engine(n)})},destroy:function(){this.remove()}});e.exports=i});
define("crm-setting/approval/flowconfig/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-d-ndetail"> <h3 class="head-title">' + ((__t = $t("流程配置")) == null ? "" : __t) + '</h3> <div class="flow-config-sec"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/approval/jumpfeed/jumpfeed-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="jump-feed-config"> <div class="config-li"> <div> ';
            if (data.skipPageFormToDo == "feedPage") {
                __p += ' <span class="config-radio active" >' + ((__t = $t("默认进入审批feed详情页")) == null ? "" : __t) + "</span> ";
            } else {
                __p += ' <span class="config-radio" >' + ((__t = $t("默认进入审批feed详情页")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </div> </div> <div class="config-li"> <div> ';
            if (data.skipPageFormToDo == "dataPage") {
                __p += ' <span class="config-radio active" >' + ((__t = $t("默认进入数据详情页")) == null ? "" : __t) + "</span> ";
            } else {
                __p += ' <span class="config-radio">' + ((__t = $t("默认进入数据详情页")) == null ? "" : __t) + "</span> ";
            }
            __p += " </div> <p>" + ((__t = $t("当有数据查看权限时进入数据详情页，当没有数据权限时进入审批feed详情页")) == null ? "" : __t) + "</p> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/jumpfeed/jumpfeed",["crm-widget/dialog/dialog","crm-widget/select/select","./jumpfeed-html"],function(e,t,a){var i=CRM.util,o=e("crm-widget/dialog/dialog"),s=(e("crm-widget/select/select"),e("./jumpfeed-html")),l=o.extend({attrs:{width:510,title:$t("待办默认进入页面设置"),showBtns:!0,showScroll:!1,className:"crm-d-page-config"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","click .config-radio":"triggerRadio"},initialize:function(){return this.attrs=$.extend(!0,{},l.prototype.attrs,this.attrs),l.superclass.initialize.apply(this,arguments)},show:function(e){var t=l.superclass.show.call(this);return this.skipPageFormToDo="feedPage",this.getPageConfig(),t},triggerRadio:function(e){e&&e.stopPropagation();var t=this,a=$(e.target).closest(".config-radio"),i=t.element.find(".config-li .config-radio"),e=$(e.target).closest(".config-li").index();a.hasClass("active")||(i.removeClass("active"),a.addClass("active"),0==e&&(t.skipPageFormToDo="feedPage"),1==e&&(t.skipPageFormToDo="dataPage"))},getPageConfig:function(){var t=this;i.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"approvalflow",type:"dispatchFromTodoList",terminal:"MOBILE"},success:function(e){0==e.Result.StatusCode&&(e.Value&&e.Value.value&&(t.skipPageFormToDo="OBJECT_DETAIL_PAGE"==e.Value.value?"dataPage":"feedPage"),t.element.find(".dialog-con").html(s({data:{skipPageFormToDo:t.skipPageFormToDo}})))}},{errorAlertModel:1})},confirmHandle:function(e){var t=this;i.FHHApi({url:"/EM1AFLOW/Config/Save",data:{flowType:"approvalflow",type:"dispatchFromTodoList",terminal:"MOBILE",value:"dataPage"==t.skipPageFormToDo?"OBJECT_DETAIL_PAGE":"FEED_DETAIL_PAGE"},success:function(e){0==e.Result.StatusCode&&t.hide()}},{errorAlertModel:1})},hide:function(){return l.superclass.hide.call(this)},cancelHandle:function(){this.hide()},destroy:function(){o.prototype.destroy.call(this)}});a.exports=l});
define("crm-setting/approval/ndetail/ndetail",["crm-modules/common/util","./tpl-html"],function(e,t,r){var i=e("crm-modules/common/util"),n=e("./tpl-html");r.exports=Backbone.View.extend({initialize:function(){},render:function(e){e.creator||(e.creator=""),e.triggerNames&&e.triggerNames.length?e.triggerTypesLabel=e.triggerNames.join(",")||"--":e.triggerTypes&&(e.triggerTypesLabel=_.map(e.triggerTypes,function(e){return{1:$t("新增"),2:$t("编辑"),3:$t("作废"),4:$t("删除")}[e]})),this.$el.html(n(_.extend({getName:i.getEmployeeById,formatTime:CRM.util.formatTime},e)))},destroy:function(){}})});
define("crm-setting/approval/ndetail/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-d-ndetail"> <h3 class="head-title">' + ((__t = $t("审批详情")) == null ? "" : __t) + '</h3> <div class="content"> <div class="flow-list fn-clear"> <div class="clear-line"> <div class="item item-l"> <div class="item-con"> <label class="title">' + ((__t = $t("审批流程名称")) == null ? "" : __t) + '</label> <span class="text">' + __e(name || "--") + '</span> </div> </div> <div class="item item-r"> <div class="item-con"> <label class="title">' + ((__t = $t("API名称")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = srcWorkflowId || "--") == null ? "" : __t) + '</span> </div> </div> <div class="cell-line"></div> </div> <div class="clear-line"> <div class="item item-l"> <div class="item-con"> <label class="title">' + ((__t = $t("关联对象")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = entityName || "--") == null ? "" : __t) + '</span> </div> </div> <div class="item item-r"> <div class="item-con"> <label class="title">' + ((__t = $t("触发动作")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = triggerTypesLabel) == null ? "" : __t) + '</span> </div> </div> <div class="cell-line"></div> </div> <div class="clear-line"> <div class="item item-l"> <div class="item-con"> <label class="title">' + ((__t = $t("审批流程描述")) == null ? "" : __t) + '</label> <span class="text">' + __e(description || "--") + '</span> </div> </div> <div class="item item-r"> <div class="item-con"> <label class="title">' + ((__t = $t("审批流程状态")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = enable ? $t("启用") : $t("停用")) == null ? "" : __t) + '</span> </div> </div> <div class="cell-line"></div> </div> <div class="clear-line"> <div class="item item-l"> <div class="item-con"> <label class="title">' + ((__t = $t("创建人")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = creator && getName(creator) ? getName(creator).name : "--") == null ? "" : __t) + '</span> </div> </div> <div class="item item-r"> <div class="item-con"> <label class="title">' + ((__t = $t("创建时间")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = formatTime(createTime)) == null ? "" : __t) + '</span> </div> </div> <div class="cell-line"></div> </div> <div class="clear-line"> <div class="item item-l"> <div class="item-con"> <label class="title">' + ((__t = $t("最后修改人")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = modifier && getName(modifier) ? getName(modifier).name : "--") == null ? "" : __t) + '</span> </div> </div> <div class="item item-r"> <div class="item-con"> <label class="title">' + ((__t = $t("最后修改时间")) == null ? "" : __t) + '</label> <span class="text">' + ((__t = formatTime(modifyTime)) == null ? "" : __t) + '</span> </div> </div> <div class="cell-line"></div> </div> </div> </div> <h3 class="head-title">' + ((__t = $t("审批流触发条件")) == null ? "" : __t) + '</h3> <div class="trigger-sec"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/approval/todoConfig/todoConfig-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="todo-config-config"> <div class="config-li"> <div> ';
            if (data.skipPageFormToDo == "CLOSE_PAGE") {
                __p += ' <span class="config-radio active" >' + ((__t = $t("处理后，关闭当前详情页")) == null ? "" : __t) + "</span> ";
            } else {
                __p += ' <span class="config-radio" >' + ((__t = $t("处理后，关闭当前详情页")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </div> </div> <div class="config-li"> <div> ';
            if (data.skipPageFormToDo == "CURRENT_PAGE") {
                __p += ' <span class="config-radio active" >' + ((__t = $t("处理后，停留在当前详情页")) == null ? "" : __t) + "</span> ";
            } else {
                __p += ' <span class="config-radio">' + ((__t = $t("处理后，停留在当前详情页")) == null ? "" : __t) + "</span> ";
            }
            __p += " </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/approval/todoConfig/todoConfig",["crm-widget/dialog/dialog","./todoConfig-html"],function(e,o,t){var i=CRM.util,a=e("crm-widget/dialog/dialog"),l=e("./todoConfig-html"),s=a.extend({attrs:{width:510,title:$t("网页待办处理设置"),showBtns:!0,showScroll:!1,className:"crm-d-page-config"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","click .config-radio":"triggerRadio"},initialize:function(){return this.attrs=$.extend(!0,{},s.prototype.attrs,this.attrs),s.superclass.initialize.apply(this,arguments)},show:function(e){var o=s.superclass.show.call(this);return this.skipPageFormToDo="CLOSE_PAGE",this.getPageConfig(),o},triggerRadio:function(e){e&&e.stopPropagation();var o=this,t=$(e.target).closest(".config-radio"),i=o.element.find(".config-li .config-radio"),e=$(e.target).closest(".config-li").index();t.hasClass("active")||(i.removeClass("active"),t.addClass("active"),0==e&&(o.skipPageFormToDo="CLOSE_PAGE"),1==e&&(o.skipPageFormToDo="CURRENT_PAGE"))},getPageConfig:function(){var o=this;i.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"approvalflow",type:"closeApprovalWebPageWhenCompleted",terminal:"ALL"},success:function(e){0==e.Result.StatusCode&&(e.Value&&e.Value.value&&(o.skipPageFormToDo=e.Value.value),o.element.find(".dialog-con").html(l({data:{skipPageFormToDo:o.skipPageFormToDo}})))}},{errorAlertModel:1})},confirmHandle:function(e){var o=this;i.FHHApi({url:"/EM1AFLOW/Config/Save",data:{flowType:"approvalflow",terminal:"ALL",type:"closeApprovalWebPageWhenCompleted",value:o.skipPageFormToDo},success:function(e){0==e.Result.StatusCode&&(FxUI.Message.success($t("成功")),o.hide())}},{errorAlertModel:1})},hide:function(){return s.superclass.hide.call(this)},cancelHandle:function(){this.hide()},destroy:function(){a.prototype.destroy.call(this)}});t.exports=s});