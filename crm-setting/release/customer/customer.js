define("crm-setting/customer/customer",["crm-modules/common/util","crm-modules/page/list/list"],function(t,e,n){t("crm-modules/common/util");var o=t("crm-modules/page/list/list"),t=o.extend({options:{apiname:"AccountObj"},getExtendAttribute:function(){return{extend_attribute:"customercrmmgr"}},parseActionParam:function(t,e){var n=this;return"add"===t?_.extend(e,{data:{leads_pool_id:this.leads_pool_id},logType:10007}):"import"==t&&(e.importSuccssCb=function(){CRM.util.alert($t("导入数据转入后台")),n.trigger("success")}),e},getDetailParams:function(){return{detailFrom:3}},parseBatchBtns:function(t){var e=["receive_accountobj","return_accountobj","assign_accountobj","revoke_accountobj","receive_accountobj","mail"];return _.filter(t,function(t){return!_.contains(e,t.action)})},getOtherBtns:function(){return[]},initComplete:function(){},getColumns:function(){var t=o.prototype.getColumns.apply(this,arguments),e=_.findWhere(t,{data:"high_seas_id"});return e&&(e.showLookupText=!0),t}});n.exports=t});