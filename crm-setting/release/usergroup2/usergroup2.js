define("crm-setting/usergroup2/selectsearch/selectsearch",["base-modules/utils"],function(e,t,i){var o=e("base-modules/utils");i.exports=Backbone.View.extend({options:{wrapper:null,trigger:"click",position:"absolute",width:null,height:240,zIndex:100,spacing:5,defaultValue:null,parentNode:$("body"),scrollNode:$(window),disabled:!1,options:[],titleTemplate:"{{name}}",optionTemplate:'<li data-value="{{value}}" title="{{name}}">{{name}}</li>'},initialize:function(){var i=this,e=i.options;i.setElement(['<div class="f-g-select" style="width:110px;">','<div class="g-select-title-wrapper" style="border-right:0;border-top-right-radius: 0;border-bottom-right-radius: 0;">','<div class="select-title"></div><i></i>',"</div>","</div>",'<div class="dt-ipt-wrap" style="flex:1;display: flex;border-left: 0;border-top-left-radius: 0;border-bottom-left-radius: 0;padding:2px 5px;">','<span class="line" style="display:block;margin: 3px 10px 3px 0;height: 16px;"></span>',"<input placeholder="+$t("请输入")+' class="dt-ipt"></div><span class="dt-sc-ico j-sc-btn"></span>'].join("")),i.$titleWrapper=i.$(".g-select-title-wrapper"),i.$title=i.$titleWrapper.find(".select-title"),e.wrapper.append(i.$el),i.uuid=_.uniqueId("select_"),i.disabled=e.disabled||!1,i.disabled&&i.$el.addClass("g-select-disabled"),"mouseenter"==e.trigger||"mouseover"==e.trigger?(i.hideTimer=null,i.showTimer=null,i.$el.on("mouseenter",function(){i.disabled||i._hoverShow()}),i.$el.on("mouseleave",function(){i.disabled||i._hoverHide()})):(i.$el.filter(".f-g-select").on(e.trigger,function(e){i.disabled||("show"==i.status?i._hide():i._show(),o.stopPropagation(e,i.uuid))}),$("body").on("click.select"+i.uuid,function(e,t){e=$(e.target);t&&t.target==i.uuid||0==e.closest(i.$el.filter(".f-g-select")).length&&i._hide()})),i.optionTemplate=_.template(e.optionTemplate),i.titleTemplate=_.template(e.titleTemplate),null!==e.defaultValue?i.setValue(e.defaultValue):e.options.length&&i.setValue(e.options[0].value),i.rendered=!1},_render:function(){var t,i=this,e=i.options;i.rendered||(t=[],i.$optionsWrapper||(i.$optionsWrapper=$('<div class="'+e.prefix+'options-wrapper" style="display:none;"></div>'),i.$options=$('<div style="display: inline-block;width: 130px;height: 32px;line-height:32px;color: #999;margin: 0 15px;border-bottom: 1px solid #eee;box-shadow: inset 0 1px 0 0 #eee;">'+$t("选择搜索范围")+'</div><ul class="g-select-options"></ul>').appendTo(i.$optionsWrapper),i._bindEvent()),_.each(i.options.options,function(e){t.push(i.optionTemplate(e))}),i.$optionsWrapper.find(".g-select-options").html(t.join("")),i.$optionsWrapper.appendTo($(i.options.parentNode)),i.$options.find('[data-value="'+i.value+'"]').addClass("state-selected"),i.rendered=!0)},_bindEvent:function(){var e,i=this,t=i.options;i.$optionsWrapper.on("click","li",function(e){var t=$(this).attr("data-value");i.setValue(t),i._hide(),e.stopPropagation()}),$(window).on("resize.select"+i.uuid,function(){clearTimeout(e),e=setTimeout(function(){i.$optionsWrapper.is(":hidden")||i._setPosition()},100)}),"mouseenter"!=t.trigger&&"mouseover"!=t.trigger||(i.$optionsWrapper.on("mouseenter",function(){i._hoverShow()}),i.$optionsWrapper.on("mouseleave",function(){i._hoverHide()}))},_hoverShow:function(){var e=this;clearTimeout(e.hideTimer),e.showTimer=setTimeout(function(){e._show()},250)},_hoverHide:function(){var e=this;clearTimeout(e.showTimer),e.hideTimer=setTimeout(function(){e._hide()},250)},_show:function(){var e=this;e._render(),e._setPosition(),e.$titleWrapper.addClass("g-select-expand"),e.$optionsWrapper.slideDown(200),e.status="show"},_hide:function(){var e=this;"show"==e.status&&(e.$titleWrapper.removeClass("g-select-expand"),e.$optionsWrapper.hide(),e.status="hide")},setValue:function(e,t){var i=this,o=i.options,s=-1;if(e!=i.value){i.$options&&i.$options.find('[data-value="'+e+'"]').addClass("state-selected").siblings().removeClass("state-selected");for(var n,l=0;l<o.options.length;l++)if(o.options[l].value==e){s=l;break}-1<s&&(n=o.options[s],i.$title.html(i.titleTemplate(n)),i.value=e,t||i.trigger("change",n))}},getReturnValue:function(){return{selectValue:this.getValue(),searchValue:this.$el.find(".dt-ipt").val()}},reset:function(){this.$el.find(".dt-ipt").val(""),this.setValue(this.options.options[0]&&this.options.options[0].value,!0)},getValue:function(){return this.value},resetOptions:function(e){var t=this;t.rendered=!1,t.options.options=e,"show"==t.status&&t._render();for(var i=0;i<e.length;i++)if(e[i].value==t.value){t.$title.html(t.titleTemplate(e[i]));break}},addOption:function(e){var t=this;t.options.options.push(e),"show"==t.status?t.$optionsWrapper.find(".g-select-options").append(t.optionTemplate(e)):t.rendered=!1},removeOption:function(e){for(var t=this,i=t.options.options,o=-1,s=0;s<i.length;s++)if(i[s].value==e){o=s;break}-1<o&&(e==t.value&&(1<i.length?t.setValue(i[(o+1)%i.length].value):t.setValue(null)),i.splice(o,1),"show"==t.status?t.$options.find('[data-value="'+e+'"]').remove():t.rendered=!1)},setDisable:function(e){(this.disabled=e=e||!1)?this.$el.addClass("g-select-disabled"):this.$el.removeClass("g-select-disabled")},_setPosition:function(){var e,t,i=this,o=i.options,s=o.position,n=o.spacing,l=o.parentNode.closest(".ui-scrollbar"),a=l&&l[0]?l:o.scrollNode,r=a.offset()?a.offset().top:0,p=$(o.parentNode).offset(),d=i.$el,u=d.offset(),h=i.$optionsWrapper.height();"fixed"==s?(t=u.left-a.scrollLeft()-p.left,(e=u.top-a.scrollTop()+d.height()+n-p.left)<0&&(e=u.top-a.scrollTop()-h-n)):(s="absolute",e=u.top+d.height()+n-p.top,t=u.left-p.left,d=h>o.height?o.height:h,-10<e+p.top-r-a.height()&&(p.top-r<d?l&&0<l.length&&setTimeout(function(){$(".scroll-content",l.parent()).scrollTop(40)},200):e=u.top-d-n-p.top)),i.$optionsWrapper.css({position:s,top:e,left:t,zIndex:o.zIndex}),i.$options.css({"max-height":o.height})},destroy:function(){var e=this.uuid;this.trigger("destroy"),this.undelegateEvents(),this.$optionsWrapper&&this.$optionsWrapper.remove(),$("body").off(".select"+e),$(window).off("resize.select"+e),this.remove()}})});
define("crm-setting/usergroup2/usergroup2",["crm-modules/common/util","crm-widget/table/table","./selectsearch/selectsearch"],function(t,e,s){t("crm-modules/common/util");var r=t("crm-widget/table/table");t("./selectsearch/selectsearch");s.exports=Backbone.View.extend({initialize:function(t){this.userid=t.props.userId,this.widgets={},t.wrapper.append('<div class="crm-s-usergroup2"><div class="table-wrap table-wrap1 table-wrap1-0"></div></div>'),this.tableWrapper1=t.wrapper.find(".table-wrap1"),this.setElement(this.tableWrapper1),this.initTable()},events:{},initTable:function(){var s=this;s.dt=new r({$el:s.$el,url:"/EM1HCRMUdobj/groupApi/queryUserGroup",requestType:"FHHApi",trHandle:!1,showSize:!1,noSupportLock:!0,postData:{status:null,id:this.userid},columns:[{data:"name",title:$t("组名"),width:240,fixed:!1,isFilter:!1,render:function(t,e,s){return 1===s.status?'<span style="color:#ccc;">'+t+"</span>":t||"--"}},{data:"description",title:$t("备注"),isFilter:!1,render:function(t,e,s){return 1===s.status?'<span style="color:#ccc;">'+t+"</span>":t||"--"}},{data:"status",title:$t("状态"),width:120,isFilter:!1,render:function(t,e,s){return 1===t?'<span style="color:#ccc;">'+$t("已停用")+"</span>":$t("启用中")}}],search:{pos:"C",placeHolder:$t("搜索"),type:"searchKey",filterColumns:[{title:$t("组名"),data:"name",isFilter:!0,dataType:1}]},sortField:"orderKey",sortType:"isAsc",paramFormat:function(t){var e=null==(e=s.dt)||null==(e=e._search)||null==(e=e.searchComp)||null==(e=e.selectIntance)?void 0:e.getValue();return e&&(t.searchType="name"==e?"0":"1"),t.isAsc&&("1"==t.isAsc&&(t.isAsc=!0),"2"==t.isAsc)&&(t.isAsc=!1),s.tableParam=t},formatData:function(t){return{data:t.list,totalCount:t.page?t.page.totalCount:0}}}),s.dt.on("term.change",function(t){s.dt.setParam({status:"9999"!=t?t:null},!0,!0)})},refresh:function(){FS.MEDIATOR.trigger("selector.usergroup.update"),this.dt.setParam({},!0)},destroy:function(){_.each(["dt","selectSearch","EditUserGroupWidget"],function(t){this[t]&&this[t].destroy(),this[t]&&(this[t]=null)},this)}})});