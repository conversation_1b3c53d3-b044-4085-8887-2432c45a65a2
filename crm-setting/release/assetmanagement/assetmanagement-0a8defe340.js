function asyncGeneratorStep(e,t,n,s,a,r,i){try{var o=e[r](i),c=o.value}catch(e){return void n(e)}o.done?t(c):Promise.resolve(c).then(s,a)}function _asyncToGenerator(o){return function(){var e=this,i=arguments;return new Promise(function(t,n){var s=o.apply(e,i);function a(e){asyncGeneratorStep(s,t,n,a,r,"next",e)}function r(e){asyncGeneratorStep(s,t,n,a,r,"throw",e)}a(void 0)})}}define("crm-setting/assetmanagement/assetmanagement",["crm-modules/common/util"],function(s,e,t){var a=s("crm-modules/common/util");return Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){this.initDeviceSettingView()},initDeviceSettingView:function(){var s=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=s,e.next=3,t.queryDeviceConfig();case 3:1!=+(t=e.sent)?(n={isPaasMenu:!0,fromManage:!0,hasBack:!"app-workorder/tpls/devicesetting/devicesettingv2/devicesettingv2-vue",propTitle:$t("stock_asset_management"),propStatus:t,isSubModule:!0},s.requirePage("app-workorder/tpls/devicesetting/devicesettingv2/devicesettingv2-vue",n)):(n={isPaasMenu:!0,fromManage:!0,isSubModule:!0,bReady:!0,propMenuCode:"da_basic_setting",hasBack:!"app-workorder/tpls/components/components-vue",propTitle:$t("stock_asset_management")},s.requirePage("app-workorder/tpls/components/components-vue",n));case 5:case"end":return e.stop()}},e)}))()},queryDeviceConfig:function(){return new Promise(function(t,n){a.FHHApi({url:"/EM1AESERVICE/DeviceService/queryDeviceConfig",data:{},success:function(e){e.Value&&"C120060000"==e.Value.errorCode?t(e.Value.data.deviceStatus):(a.remind(3,$t("获取状态失败")),n())}},{errorAlertModel:1})})},requirePage:function(e,t){var n=this;s.async("app-workorder/app.js",function(){s.async(["app-common-assets/style/common.css","app-standalone-assets/style/_ui.css","app-standalone-assets/style/all.css","app-workorder-assets/style/all.css"],function(){s.async(e,function(e){n.instance=new e({el:n.$el[0],propsData:t,hasBack:!1,propTitle:$t("stock.crm.setting.assetmanagement.assets_management")})})})})},destroy:function(){this.instance&&this.instance.$destroy&&this.instance.$destroy()}})});