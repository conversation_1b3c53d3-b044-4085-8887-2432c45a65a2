define("crm-setting/marketingactivity/marketingactivity",["crm-modules/common/util","./template/index-html"],function(n,e,i){n("crm-modules/common/util");var t=n("./template/index-html"),a=Backbone.View.extend({config:{page1:{id:"marketingactivity",wrapper:".marketingactivity-box",path:"crm-modules/page/marketingactivity/marketingactivity"}},events:{"click .crm-tab a":"onHandle","click .clue-manageguide":"showGuide"},initialize:function(e){this.setElement(e.wrapper)},onHandle:function(e){var i=$(e.target);e.preventDefault(),i.hasClass("page1")&&this.switchPage(["page1"])},showGuide:function(){this.manageGuide||(this.manageGuide=manageGuide()),this.manageGuide.show()},switchPage:function(e){this.renderPage(e)},render:function(e){var i=this;this.pages={},this.$el.html(t()),this.renderPage(e),CRM.api.get_licenses({key:"accounts_leads_limit_app",objectApiName:"LeadsObj",cb:function(e){e.accounts_leads_limit_app||i.$el.find(".page4").hide()}})},renderPage:function(e){var i,t=this,a=t.$(".crm-tab .item"),a=(a.removeClass("cur"),(e&&e[0]?a.filter("."+e[0]):a.eq(0)).addClass("cur"),t.curId=e&&e[0]?e[0]:"page1");_.map(t.pages,function(e){e.hide()}),t.pages[a]?t.pages[a].show():(i=a,n.async(t.config[i].path,function(e){e=new e(_.extend({wrapper:t.config[i].wrapper,apiname:"LeadsObj"}));t.curId===i&&e.show(),t.pages[i]=e}))},destroy:function(){_.map(this.pages,function(e){e.destroy&&e.destroy()}),this.pages=this.curId=null}});i.exports=a});
define("crm-setting/marketingactivity/marketingactivity/marketingactivity",["crm-modules/page/marketingactivity/marketingactivity"],function(t,i,a){t=t("crm-modules/page/marketingactivity/marketingactivity");a.exports=t});
define("crm-setting/marketingactivity/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit crm-manageclue-title"> <h2><span class="tit-txt">' + ((__t = $t("市场活动管理")) == null ? "" : __t) + '</span></h2> <!-- <div class="clue-manageguide">' + ((__t = $t("线索配置向导")) == null ? "" : __t) + '</div> --> </div> <div class="crm-module-con"> <div class="crm-tab"> <a class="item page1" href="#crm/setting/marketingactivity/=/page1">' + ((__t = $t("营销归因")) == null ? "" : __t) + '</a><a class="crm-doclink guide-link" href="https://www.fxiaoke.com/open/imgtext/?appId=FSAID_bec6dbd&messageId=df681fcc-26a4-4cd6-b767-5ec72e76af15&imageTextParamId=4d6dc070462a4405b6f7eea2a8dbc578&fsEa=5C775DAB77C1B5D5CC805D0292B0FB75" target="_blank"></a> <!-- <a class="item page2" href="#crm/setting/marketingactivity/=/page2">' + ((__t = $t("线索转换设置")) == null ? "" : __t) + '</a> <a class="item page4" href="#crm/setting/marketingactivity/=/page4">' + ((__t = $t("线索保有量")) == null ? "" : __t) + '</a> <a class="item page3" href="#crm/setting/marketingactivity/=/page3">' + ((__t = $t("线索跟进规则")) == null ? "" : __t) + '</a> --> </div> <div class="tab-con"> <div class="crm-p20 crm-scroll"> <div class="item marketingactivity-box" style="display:none;"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("营销归因主要是基于模型算法")) == null ? "" : __t) + '</p> </div> <div class="item marketingattribution-box"> <div class="crm-loading"></div> </div> </div> <!-- <div class="item market-xxx1-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item market-xxx2-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item market-xxx3-box" style="display:none;"> <div class="crm-loading"></div> </div> --> </div> </div> </div>';
        }
        return __p;
    };
});