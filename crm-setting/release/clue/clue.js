define("crm-setting/clue/behavior/behavior-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="follow-behavior"> <div class="crm-intro"> <h3>' + ((__t = $t("跟进行为")) == null ? "" : __t) + "</h3> <ol> <li>" + ((__t = $t("线索跟进行为更改影响")) == null ? "" : __t) + '</li> </ol> </div> <div class="behavior-btn-box"> <a href="javascript:;" class="fx-btn fx-btn-primary setting-btn">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> <a href="javascript:;" class="fx-btn fx-btn-primary add-btn">' + ((__t = $t("添加")) == null ? "" : __t) + '</a> </div> <div class="behavior-table-box"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/clue/behavior/behavior",["crm-modules/common/util","./followbehaviordialog/followbehaviordialog","./behavior-html","./config","crm-widget/table/table"],function(t,e,i){var n=t("crm-modules/common/util"),a=t("./followbehaviordialog/followbehaviordialog"),o=t("./behavior-html"),r=t("./config"),s=t("crm-widget/table/table");i.exports=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper),this.isActiveArr=[],this.hasComplete=!1,this.initCustomerBehavior()},events:{"click .crm-g-radio":"toggleHandle","click .setting-btn":"fbHandle","click .add-btn":"fbHandle"},initCustomerBehavior:function(){var t=this;this.getAllObject(function(){t.getFollowDealSetting(function(){t.resetData(),t.renderCustomerBehavior()})})},resetData:function(){var n=this,s=this.CustomerFollowDealSetting.CustomerFollowSetting,l=[];this.checkCustomer(this.allObj),_.each(this.allObj,function(t,e){var i=t.api_name,t={apiName:i,displayName:t.display_name,actionList:[]},o=$.extend(!0,{},r),a=(o.hasOwnProperty(i)?t.actionList=_.map(o[i],function(t){return t.source_object_api_name=i,t}):(o=$.extend(!0,{},o.otherObj),t.actionList=_.map(o,function(t){return t.source_object_api_name=i,t})),_.find(s,function(t){return t.apiName===i}));a&&_.each(t.actionList,function(e){var t=_.find(a.actionList,function(t){return t.action_code===e.action_code});t&&(e=_.extend(e,t))}),t.timestamp=n.getTime()+e,n.checkAction(t)&&l.push(t)}),this.CustomerFollowDealSetting.CustomerFollowSetting=l,this.currentFollowObj=_.findWhere(this.CustomerFollowDealSetting.CustomerFollowSetting,{apiName:"LeadsObj"})||this.CustomerFollowDealSetting.CustomerFollowSetting[0]||{}},getTime:function(){return(new Date).getTime()},checkCustomer:function(t){_.find(t,function(t){return"LeadsObj"===t.api_name})||t.push({api_name:"LeadsObj",display_name:$t("销售线索")})},checkAction:function(t){if(_.contains(["AccountObj","ContactObj","OpportunityObj","NewOpportunityObj"],t.apiName)){var e=_.findWhere(this.allObj,{api_name:t.apiName});if(e&&e.fields){var i=!0;if(_.map(e.fields,function(t,e){"object_reference"===t.type&&"LeadsObj"===t.target_api_name&&"leads_id"!==e&&(i=!1)}),i)return!1}}if(!_.contains(["LeadsTransferLogObj"],t.apiName)){var e=_.find(t.actionList,function(t){return t.is_visible}),o=_.find(t.actionList,function(t){return t.is_enable});if(e)return e;o&&this.isActiveArr.push(t)}return!1},getAllObject:function(e){var i=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findRelatedObjectList",data:{describeApiName:"LeadsObj",includeRefList:!0,includeDetailList:!0,excludeInvalid:!0},success:function(t){0==t.Result.StatusCode?t.Value.lookupDescribeList.length?(i.allObj=t.Value.lookupDescribeList,e&&e()):n.alert($t("没有关联对象")):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},getFollowDealSetting:function(e){var i=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/object_follow_deal_setting/service/get_object_follow_deal_setting",data:{objectApiName:"LeadsObj",followDealSettingType:1},success:function(t){0==t.Result.StatusCode?(i.CustomerFollowDealSetting={},t.Value.dataList.length?i.CustomerFollowDealSetting.CustomerFollowSetting=_.find(t.Value.dataList,function(t){return 1==t.setting_type}).settingList:i.CustomerFollowDealSetting.CustomerFollowSetting=[],e()):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},toggleHandle:function(t){t=$(t.target);t.is(".state-active")||(t.closest(".trade-behavior-wrap").find(".crm-g-radio").removeClass("state-active"),t.addClass("state-active"))},transformData:function(t){var t=$.extend(!0,{},t),e=[],i={},o=[];return this.isActiveArr.length&&(t.CustomerFollowSetting=t.CustomerFollowSetting.concat(this.isActiveArr)),i.setting_type=1,_.each(t.CustomerFollowSetting,function(t){var e=_.filter(t.actionList,function(t){return t.is_enable});e&&e.length&&(t.actionList=e,o.push(t))}),i.settingList=o,e.push(i),e},setCustomerFollowDealSetting:function(e){var i=this,t=this.transformData(e);i.hasComplete||(i.hasComplete=!0,n.FHHApi({url:"/EM1HNCRM/API/v1/object/object_follow_deal_setting/service/save_object_follow_deal_setting",data:{objectApiName:"LeadsObj",dataList:t,followDealSettingType:1},success:function(t){i.hasComplete=!1,0==t.Result.StatusCode?(i.CustomerFollowDealSetting=e,i.renderCustomerBehavior(),n.remind(1,$t("设置成功"))):n.remind(3,t.Result.FailureMessage||$t("设置失败，请联系客服"))},error:function(){n.remind(3,$t("设置失败，请联系客服")),i.hasComplete=!1}},{errorAlertModel:1}))},fbHandle:function(){this.createDialog("add")},createDialog:function(t){var i=this,e=this.CustomerFollowDealSetting,o="add"===t?$t("添加跟进行为"):$t("编辑{{name}}下的跟进行为",{name:i.currentFollowObj.displayName});this.fb=new a({title:o}),this.fb.on("suc",function(t,e){i.CustomerFollowDealSetting=$.extend(!0,{},t),i.currentFollowObj=$.extend(!0,{},e),i.setCustomerFollowDealSetting(i.CustomerFollowDealSetting)}),this.fb.show(e,this.currentFollowObj,t)},renderCustomerBehavior:function(){this.$el.html(o(this.CustomerFollowDealSetting)),this.calcHeight(),this.createBehaviorTable()},calcHeight:function(){var t=this.$el.parents(".tab-con").height()-200;this.$(".behavior-table-box").height(t)},createBehaviorTable:function(){var a=this,t=this.formatDataToTable(this.CustomerFollowDealSetting.CustomerFollowSetting);this.table&&this.table.destroy(),t.length?(this.$(".setting-btn").hide(),this.$(".add-btn").show(),this.table=new s({$el:this.$(".behavior-table-box"),showPage:!1,doStatic:!0,colResize:!0,columns:[{data:"objName",title:$t("对象名称")},{data:"behavior",title:$t("跟进行为")},{data:null,title:$t("操作"),width:"100px",render:function(t,e,i){return'<span class="js-edit">'+$t("编辑")+'</span><span class="js-del">'+$t("删除")+"</span>"}}]}),this.table.on("trclick",function(e,t,i){var o;i.hasClass("js-edit")?(a.currentFollowObj=_.find(a.CustomerFollowDealSetting.CustomerFollowSetting,function(t){return t.apiName===e.apiName}),a.createDialog("edit")):i.hasClass("js-del")&&(o=n.confirm("<p>"+$t("确定要删除该对象下的所有跟进行为吗")+"?</p>",$t("删除"),function(){o.hide(),a.actionSetting(e.apiName),a.setCurrentObj(e.apiName),a.setCustomerFollowDealSetting(a.CustomerFollowDealSetting)}))}),this.table.doStaticData(t)):(this.$(".setting-btn").show(),this.$(".add-btn").hide())},actionSetting:function(t){for(var e=this.CustomerFollowDealSetting.CustomerFollowSetting,i=e.length;i--;)if(e[i].apiName===t)return void _.each(e[i].actionList,function(t){t.is_enable=!1})},setCurrentObj:function(e){this.currentFollowObj=_.find(this.CustomerFollowDealSetting.CustomerFollowSetting,function(t){return t.apiName===e})},formatDataToTable:function(t){var o=[];return t=this.sortData(t),_.forEach(t,function(t){var e="",i=!1;_.forEach(t.actionList,function(t){t.is_enable&&t.is_visible&&(i=!0,e+=t.action_label+"、")}),i&&(e=e.substring(0,e.length-1),o.push({objName:t.displayName,apiName:t.apiName,behavior:e}))}),o},sortData:function(t){return t.sort(function(t,e){return e.timestamp-t.timestamp})},setBasicHandle:function(t){var t=$(t.target),e=t.attr("data-key"),i=t.hasClass("mn-selected")?"1":"0";this.setConfig({key:e,value:"11"==e?"0"==i?"1":"0":i},t)},setConfig:function(t,e){n.setConfigValues(t).then(function(){n.remind(1,$t("设置成功"))},function(){n.remind(3,$t("设置失败")),e.toggleClass("mn-selected")})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.fb&&(this.fb.destroy(),this.fb=null)}})});
define("crm-setting/clue/behavior/checkboxComponent/checkboxComponent",["./template/checkboxComponent-html","crm-modules/common/util"],function(t,e,n){var o=t("./template/checkboxComponent-html");t("crm-modules/common/util");n.exports=Backbone.View.extend({options:{el:$("body"),title:$t("复选框"),data:[{name:$t("新建"),id:"add",status:!0}]},events:{"click .mn-checkbox-item":"selectEve","click .checkboxComponent-selectAll":"selectAll","click .checkboxComponent-clear":"clearAll"},initialize:function(){this.$el=this.options.el,this.$el.append(o({title:this.options.title})),this.updateBtn(),this.render()},render:function(){var e,n="";this.options.data.length&&(_.forEach(this.options.data,function(t){this._hasValue(t.name)&&this._hasValue(t.id)&&_.isBoolean(t.status)&&(e=t.status?"mn-selected":"",n+='<span class="mn-checkbox-box"><span class="mn-checkbox-item '+e+'" data-id="'+t.id+'"></span><span class="checkbox-lable" title="'+t.name+'">'+t.name+"</span></span>")},this),this.$(".checkboxComponent-main").html(n))},_hasValue:function(t){return!(!_.isString(t)||""===t)},updateBtn:function(){var e=!0;_.each(this.options.data,function(t){t.status||(e=!1)}),e?(this.$(".checkboxComponent-selectAll").hide(),this.$(".checkboxComponent-clear").show()):(this.$(".checkboxComponent-selectAll").show(),this.$(".checkboxComponent-clear").hide())},selectAll:function(){_.each(this.options.data,function(t){t.status=!0}),this.updateBtn(),this.render()},clearAll:function(){_.each(this.options.data,function(t){t.status=!1}),this.updateBtn(),this.render()},selectEve:function(t){var t=$(t.currentTarget),e=t.data().id;t.hasClass("mn-selected")?this.updateData(e,!1):this.updateData(e,!0),this.updateBtn()},updateData:function(t,e){for(var n=this.options.data.length;n--;)if(this.options.data[n].id===t)return void(this.options.data[n].status=e)},getValue:function(){return this.options.data},destroy:function(){this.events=null,this.$(".checkboxComponent").remove()}})});
define("crm-setting/clue/behavior/checkboxComponent/template/checkboxComponent-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="checkboxComponent b-g-crm"> <div class="checkboxCom-title"> <span class="checkboxCom-titleName"> ' + ((__t = title) == null ? "" : __t) + ' </span> <span class="checkboxComponent-selectAll">' + ((__t = $t("选择全部")) == null ? "" : __t) + '</span> <span class="checkboxComponent-clear">' + ((__t = $t("清空")) == null ? "" : __t) + '</span> </div> <div class="checkboxComponent-main"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/clue/behavior/config",[],function(e,i,s){s.exports={LeadsObj:[{is_enable:!1,setting_type:"1",action_code:"ChangeOwner",action_label:$t("更换负责人"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"FollowUp",action_label:$t("跟进中"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"ChangePartner",action_label:$t("更换合作伙伴"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"ChangePartnerOwner",action_label:$t("更换外部负责人"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Merge",action_label:$t("合并"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"SendMail",action_label:$t("发邮件"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Deal",action_label:$t("转换"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Close",action_label:$t("无效"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Move",action_label:$t("转移"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Return",action_label:$t("退回"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"TakeBack",action_label:$t("收回"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Allocate",action_label:$t("分配"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Choose",action_label:$t("领取"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"Edit",action_label:$t("编辑"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1,source_object_api_name:"LeadsObj"}],CheckinsObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建外勤签到"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"FinishCheckin",action_label:$t("完成外勤"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1}],otherObj:[{is_enable:!1,setting_type:"1",action_code:"Add",action_label:$t("新建"),is_visible:!0,is_system:!1},{is_enable:!1,setting_type:"1",action_code:"AddEvent",action_label:$t("发布销售记录"),is_visible:!0,is_system:!1}]}});
define("crm-setting/clue/behavior/followbehaviordialog/followbehaviordialog",["crm-modules/common/util","crm-widget/dialog/dialog","../checkboxComponent/checkboxComponent","crm-widget/select/select"],function(t,e,i){var o=t("crm-modules/common/util"),s=t("crm-widget/dialog/dialog"),c=t("../checkboxComponent/checkboxComponent"),a=t("crm-widget/select/select"),l=s.extend({attrs:{width:640,className:"crm-s-customer-follow",title:$t("添加跟进行为"),showBtns:!0,content:'<div class="follow-box"><div class="follow-objTitle">'+$t("所属对象")+'</div><div class="follow-objSelect" style="width:280px;"></div><div class="follow-wrap"></div></div>'},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"setData"},setData:function(){this.saveSelectBox(),0===this.data.CustomerFollowSetting.length?o.alert($t("crm.edit.chouse")):(this.trigger("suc",this.data,this.currentObj),this.hide())},createObjSelect:function(){var t=this;this.select&&this.select.destroy(),this.select=new a({$wrap:this.$(".follow-objSelect"),zIndex:999,width:280,options:_.map(t.data.CustomerFollowSetting,function(t){return{name:t.displayName,value:t.apiName}}),defaultValue:t.currentObj.apiName}),this.select.on("change",function(e){t.saveSelectBox(),t.currentObj=_.find(t.data.CustomerFollowSetting,function(t){return t.apiName===e}),t.createCheckbox()})},saveSelectBox:function(){var t=this.checkboxCom.getValue(),e=(this.currentObj.actionList=_.map(t,function(t){return t.is_enable=t.status,delete t.name,delete t.id,delete t.status,t}),_.findWhere(this.data.CustomerFollowSetting,{apiName:this.currentObj.apiName}));e&&("add"===this.type&&(e.timestamp=this.getTime()),e.actionList=t)},getTime:function(){return(new Date).getTime()},show:function(t,e,i){var o=l.superclass.show.call(this);return this.type=i,this.data=t,this.currentObj=e,"add"===i&&(this.$(".follow-objTitle").show(),this.createObjSelect()),this.createCheckbox(i),this.resizedialog(),o},createCheckbox:function(){this.checkboxCom&&this.checkboxCom.destroy(),this.checkboxCom=new c({el:this.$(".follow-wrap"),data:this.formatDataToCheckbox(this.currentObj.actionList),title:$t("跟进行为")})},formatDataToCheckbox:function(t){var e=[];return _.each(t,function(t){t.is_visible&&(t.name=t.action_label,t.id=t.action_code,t.status=t.is_enable,e.push(t))}),e},hide:function(){var t=l.superclass.hide.call(this);return this.destroy(),t},destroy:function(){var t=l.superclass.destroy.call(this);return this.select&&this.select.destroy(),this.checkboxCom&&this.checkboxCom.destroy(),t}});i.exports=l});
define("crm-setting/clue/clue",["crm-modules/common/util","./manageguide/manageguide","./template/index-html"],function(s,e,a){var t=s("crm-modules/common/util"),i=s("./manageguide/manageguide"),n=s("./template/index-html"),r=Backbone.View.extend({config:{page1:{id:"clue",wrapper:".clue-pool-box",path:"./clue/clue"},page2:{id:"transform",wrapper:".clue-transform-box",path:"./transform/transform"},page3:{id:"behavior",wrapper:".clue-behavior-box",path:"./behavior/behavior"},page4:{id:"ownership",wrapper:".clue-ownership-box",path:"crm-setting/ownership/ownership"},page5:{id:"systemsetting",wrapper:".clue-scene-box",path:"./systemsetting/systemsetting"}},events:{"click .crm-tab a":"onHandle","click .clue-manageguide":"showGuide"},initialize:function(e){this.setElement(e.wrapper)},onHandle:function(e){var a=$(e.target);e.preventDefault(),a.hasClass("page1")?this.switchPage(["page1"]):a.hasClass("page2")?this.switchPage(["page2"]):a.hasClass("page3")?this.switchPage(["page3"]):a.hasClass("page5")?this.switchPage(["page5"]):this.switchPage(["page4"])},showGuide:function(){this.manageGuide||(this.manageGuide=i()),this.manageGuide.show()},switchPage:function(e){this.renderPage(e)},render:function(e){var a=this,i=t.getTplQueryParams(),i=_.values(i);e=e||[i[1]],this.pages={},this.$el.html(n({param:e})),this.renderPage(e),CRM.api.get_licenses({key:"accounts_leads_limit_app",objectApiName:"LeadsObj",cb:function(e){e.accounts_leads_limit_app||a.$el.find(".page4").hide()}})},renderPage:function(e){var a,i=this,t=i.$(".crm-tab .item"),t=(t.removeClass("cur"),(e&&e[0]?t.filter("."+e[0]):t.eq(0)).addClass("cur"),i.curId=e&&e[0]?e[0]:"page1");_.map(i.pages,function(e){e.hide()}),i.pages[t]?i.pages[t].show():(a=t,s.async(i.config[a].path,function(e){e=new e(_.extend({wrapper:i.config[a].wrapper,apiname:"LeadsObj"}));i.curId===a&&e.show(),i.pages[a]=e}))},destroy:function(){_.map(this.pages,function(e){e.destroy&&e.destroy()}),this.manageGuide&&this.manageGuide.destroy&&this.manageGuide.destroy(),this.pages=this.curId=null}});a.exports=r});
function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var a;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(a="Object"===(a={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:a)||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function _iterableToArrayLimit(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var i,n,o,l,r=[],c=!0,s=!1;try{if(o=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;c=!1}else for(;!(c=(i=o.call(a)).done)&&(r.push(i.value),r.length!==t);c=!0);}catch(e){s=!0,n=e}finally{try{if(!c&&null!=a.return&&(l=a.return(),Object(l)!==l))return}finally{if(s)throw n}}return r}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/clue/clue/clue",["crm-modules/common/util","crm-modules/common/slide/slide","crm-modules/components/objecttable/objecttable","crm-modules/common/cluepool/cluepool","crm-modules/common/filtergroup/util","./template/detail-html"],function(o,e,t){var l=o("crm-modules/common/util"),a=o("crm-modules/common/slide/slide"),n=o("crm-modules/components/objecttable/objecttable"),i=o("crm-modules/common/cluepool/cluepool").rulesFieldStatic,u=o("crm-modules/common/filtergroup/util"),r=o("./template/detail-html"),c=Backbone.View.extend({initialize:function(e){var i=this;i.setElement(e.wrapper),i.opts=e,i.apiname="LeadsPoolObj",i.slide=new a({width:600,className:"crm-s-clue"}),i.slide.on("action",function(e){i.excuteAction(e,i.tableData)}),i.cluepoolNum=0,i.setUsersInfo(),Promise.all([CRM.util.getAuthority("LeadsPoolObj"),i.getEnterpriseQuotaStatistics(),i.getGrayByServerForLeads("graySfaOutTenantPoolBatchAdd")]).then(function(e){var e=_slicedToArray(e,3),t=e[0],a=e[1],e=e[2];i.authority=t||!1,i.quotaStatic=a&&e||!1,i.getPRMRight(function(){i.initTable()})}).catch(function(){i.quotaStatic=!1,i.getPRMRight(function(){i.initTable()})})},MAX_NUM:500,refresh:function(){this.table.table._clearChecked(),this.table.refresh()},events:{"click .j-add":"addHandle"},getEnterpriseQuotaStatistics:function(){return new Promise(function(t,e){CRM.util.FHHApi({url:"/EM1HER2/admin/enterpriseMeta/getEnterpriseQuotaStatistics",data:{},success:function(e){0===e.Result.StatusCode?t(e.Value.data.hasLicense):CRM.util.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},getGrayByServerForLeads:function(){var i=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"LeadsPoolObj";return new Promise(function(t,a){i||a();try{CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit",data:{api_name:e},success:function(e){0===e.Result.StatusCode?t(null==(e=e.Value)?void 0:e[i]):t(!1)}},{errorAlertModel:1})}catch(e){a()}})},addHandle:function(){if(this.cluepoolNum>=this.MAX_NUM)return l.remind(3,$t("贵公司可创建的线索池数量已达上限")),!1;this.doAction("add")},addfromdpmHandle:function(){var a=this;if(this.cluepoolNum>=this.MAX_NUM)return l.remind(3,$t("贵公司可创建的线索池数量已达上限")),!1;l.sendLog("".concat(this.apiname,"Manage"),"batchnew",{operationId:"create"}),o.async("crm-modules/components/dpmdialog/dpmdialog",function(e){e({objname:a.apiname,selectHandle:function(t){CRM.util.waiting(),o.async("vcrm/sdk",function(e){e.getComponent("poolBatchnew").then(function(e){e=e.default;a.batchnewDialog=new e({apiname:a.apiname,dData:t,authority:a.authority,type:"DepartmentObj"}),a.batchnewDialog.$on("success",function(){a.refresh()})})})}})})},batcheditCheck:function(){var o=this;return new Promise(function(e){var t,a,i=o.table.getRemberData(),n=i?i.length:0;n?20<n?CRM.util.remind(3,$t("单次批量编辑不能超过20条")):(t=[],a=[],_.each(i,function(e){("private"==e.pool_type?t:a).push(e)}),t.length?CRM.util.confirm($t("独家伙伴类型的数据不允许批量编辑"),null,function(){o.table.setUncheckedRow("_id",t),o.table.reduceRemberData(t,!0),this.hide(),a.length?e(a):CRM.util.remind(3,$t("请至少选择一条数据"))},{btnLabel:{confirm:$t("继续保存")}}):e(a)):CRM.util.remind(3,$t("请至少选择一条数据"))})},batcheditHandle:function(){var a=this;a.batcheditCheck().then(function(t){o.async("vcrm/sdk",function(e){e.getComponent("poolBatchedit").then(function(e){e=e.default;a.batcheditDialog=new e({apiname:a.apiname,model:{isOpenPRM:a.isOpenPRM,outer:CRM.get("outer")||{},dataList:t},isValid:!0,isSubmit:!0,authority:a.authority}),a.batcheditDialog.$on("success",function(){a.refresh()})})})})},initTable:function(){var i=this;if(i.table)return this;var e=[{action:"add",attrs:"data-action=add",className:"j-action",text:$t("新建")},{action:"addfromdpm",attrs:"data-action=addfromdpm",className:"j-action",isFold:!1,text:"".concat($t("从部门新建"),'<i class="crm-ui-title btn-addfromdpm-tip" data-pos="bottom" data-title="').concat($t("借助部门快速生成对应{{name}}",{name:$t("crm.线索池")}),'">?</i>')},{action:"batchedit",attrs:"data-action=batchedit ",className:"j-action",isFold:!1,text:$t("批量编辑")}],t=(i.quotaStatic&&(e=e.filter(function(e){return"addfromdpm"!==e.action}),t={action:"addfromOther",attrs:"data-action=addfromOther",className:"j-action",isFold:!1,text:"".concat($t("批量新建"))},e.splice(e.length-1,0,t)),n.extend({trclickHandle:function(e,t,a){a.hasClass("action-btn")?(a=a.data("action"),i.excuteAction(a,e,!0)):i.showDetail(e._id,e)},operateBtnClickHandle:function(e){e=$(e.target).attr("data-action");i["".concat(e,"Handle")]&&i["".concat(e,"Handle")]()},getOptions:function(){var e=n.prototype.getOptions.apply(this,arguments);return _.extend(e,{custom_className:"crm-cluepool-table",searchTerm:!1,isOrderBy_allColumn:!1,showFilerBtn:!1,showMultiple:!0,checked:{idKey:"_id",data:[]}})},getColumns:function(){var e=this.options.columns;return e.push({data:null,width:200,title:$t("操作"),lastFixed:!0,render:function(e,t,a){var i="";return i+='<a data-id="'.concat(a._id,'" class="action-btn" data-action="edit" href="javascript:;">').concat($t("编辑"),"</a>"),0<a.leads_count&&(i+='<a data-id="'.concat(a._id,'" class="action-btn" data-action="shift" href="javascript:;">').concat($t("转移"),"</a>")),i=(i+='<a data-id="'.concat(a._id,'" class="action-btn" data-action="del" href="javascript:;">').concat($t("删除"),"</a>"))+'<a data-id="'.concat(a._id,'" class="action-btn" data-action="clone" href="javascript:;">').concat($t("复制"),"</a>")}}),_.each(e,function(e){"name"===e.data&&(e.render=function(e){return'<a href="javascript:;">'.concat(e,"</a>")}),"allocate_overtime"===e.data&&(e.render=function(e,t,a){var i=[];return a.allocate_overtime_hours&&(i.push(a.allocate_overtime_hours),i.push($t("小时"))),a.allocate_overtime_minutes&&(i.push(a.allocate_overtime_minutes),i.push($t("分钟"))),i.length||i.push("--"),i.join("")}),"follow_overtime"===e.data&&(e.render=function(e,t,a){var i=[];return a.overtime_hours&&(i.push(a.overtime_hours),i.push($t("小时"))),a.overtime_minutes&&(i.push(a.overtime_minutes),i.push($t("分钟"))),i.length||i.push("--"),i.join("")})}),e},getExtendAttribute:function(){var e=this.get("baseScenes"),e=e&&e[0]?e.find(function(e){return"All"===e.api_name}):{};return{scene_id:e._id,scene_type:e.type,scene_api_name:e.api_name}}}));i.table=new t({el:i.$el,apiname:i.apiname,showTitle:!1,showTerm:!1,showTermBatch:!1,search:{placeHolder:$t("搜索"),type:"Keyword",highFieldName:"name",pos:"T"},operate:{pos:"T",btns:e}}),i.table.render()},_getAdminList:function(e){var t=[];return _.each(e,function(e){e.Name&&t.push(e.Name)}),t.join($t("，"))},parseObj:function(e){var t=e.SalesCluePoolAllocateRuleList||[];return e.SalesCluePoolAllocateRuleList=t,e},parseRecObj:function(e){var t=e.RecyclingRuleList||[];return t.length&&(t=_.map(t,function(e){return e.RecyclingFilterList.length&&(e.RecyclingFilterList=_.map(e.RecyclingFilterList,function(e){return 8!=e.FieldType&&9!=e.FieldType||!e.FieldValue.includes("|")||(e.FieldValue=e.FieldValue.split("|")),e})),e})),e.RecyclingRuleList=t,e},getScopeHtml:function(e){var t=[];if(e.length)for(var a=e,i=0;i<a.length;i++){for(var n=a[i],o=[],l=0;l<n.filters.length;l++){var r,c=n.filters[l],s=CRM.get("fields.PersonnelObj")[c.field_name];s&&(r=u.formatFieldValue(s,c.field_values,c.operator),o.push("".concat(s.label," ").concat(c.operator_name," ").concat(r?""+r:"")))}t.push((0==i?'<p style="color:#333;">':'<p style="color:#333;">'.concat($t("或")," ")).concat(o.join(" ".concat($t("且")," ")),"</p>"))}e=t.join(" ")||$t("成员已被全部禁用");return'<div class="item-rule">'.concat(e,"</div>")},showDetail:function(e,t){var a=this,i=t;a.getDetailById(e,function(e){a.slide.show(),a.parseData(e.SalesCluePool,i).then(function(e){a.slide.setDetailCon(r(a.formatPostData(e)))})})},parseData:function(a,e){var i=this;return new Promise(function(t){a.isOpenPRM=CRM.get("isOpenPRM"),a=i.formatObj(a,e),i.parseObj(a),i.parseRecObj(a),a=_.extend({display_name:$t("crm.线索池"),object_display_name:$t("crm.销售线索")},a),(i.tableData=a).UpdateTime=FS.moment(a.UpdateTime||a.LastModifiedTime,null,null,null,!0).format("YYYY-MM-DD HH:mm"),a.adminList=i._getAdminList(a.Employees),a.SalesCluePoolAllocateRuleList=_.sortBy(a.SalesCluePoolAllocateRuleList||[],function(e){return e.Priority}),i.getFieldList(function(e){i.fieldlist=e,a.ownerRuleContent=CRM.util.parseFiltersRule(a.PoolOwnerRule,e),a.rules=_.map(a.SalesCluePoolAllocateRuleList,function(e){var t=CRM.util.parseAllocateRule(e,i.fieldlist,CRM.get("fields.PersonnelObj"),a.isOpenPRM);return _.extend({},e,{ruleText:t.ruleText,content:3==e.AllocatePattern?"":t.memberText})}),a.RecyclingCondition=_.map(a.RecyclingRuleList,function(e){return CRM.util.parseRecycleRule(e,i.fieldlist,"LeadsObj")}),t(a)})})},addfromOtherHandle:function(){this.selectDpmDialog()},selectDpmDialog:function(){var t=this;this.getDataDialog&&this.getDataDialog.$destroy(),this.getDataDialog=FxUI.create({template:'    <fx-dialog\n      :title="$t(\'sfa.cluepool.addSelectType\')"\n      size="small"\n      :visible.sync="dialogSelectType"\n      custom-class="select-dpm-dialog"\n    >\n      <ul>\n        <li\n          v-for="item in retentionCordType"\n          :key="item.apiName"\n          @click="nowType = item.apiName"\n          :class="{\n            \'red-border\': item.apiName === nowType,\n          }"\n        >\n          {{ item.label }}\n          <span class="fx-icon-question" v-show="item.description" :title="item.description"></span>\n          <span class="fx-icon-ok-2"></span>\n        </li>\n      </ul>\n      <div slot="footer" class="dialog-footer">\n        <fx-button type="primary" @click="addTable" size="small"\n          >{{$t(\'确定\')}}</fx-button\n        >\n        <fx-button @click="dialogSelectType = false" size="small"\n          >{{$t(\'取消\')}}</fx-button\n        >\n      </div>\n    </fx-dialog>',data:function(){return{dialogSelectType:!0,nowType:"DepartmentObj",retentionCordType:[{apiName:"DepartmentObj",label:$t("从部门新建"),description:$t("借助部门快速生成对应{{name}}",{name:$t("crm.线索池")})},{apiName:"EnterpriseRelationObj",label:$t("sfa.cluepool.addfrom.enterpriseRelationObj"),description:"LeadsPoolObj"===t.apiname?$t("sfa.crm.leadsPoolAddFromRelation"):$t("sfa.crm.highseasAddFromRelation")}]}},mounted:function(){},methods:{addTable:function(){var e=this.nowType;"DepartmentObj"===e&&(l.sendLog("".concat(t.apiname,"Manage"),"batchnew",{operationId:"create"}),t.renderDpmDialog(e)),"EnterpriseRelationObj"===e&&(t.isOpenPRM?t.renderDpmDialog(e):CRM.util.alert($t("sfa.noHavePrm")))}}})},renderDpmDialog:function(a){var i=this;o.async("crm-modules/components/dpmdialog/dpmdialog",function(e){e({objname:i.apiname,type:a,selectHandle:function(t){i.getDataDialog.dialogSelectType=!1,CRM.util.waiting(),o.async("vcrm/sdk",function(e){e.getComponent("poolBatchnew").then(function(e){e=e.default;i.batchnewDialog=new e({apiname:i.apiname,dData:t,authority:i.authority,type:a}),i.batchnewDialog.$on("success",function(){i.refresh()})})})}})})},formatObj:function(e,t){_.isString(e.RolePool)&&""==e.RolePool&&(e.RolePool=0),_.isString(e.LimitType)&&""==e.LimitType&&(e.LimitType=1),_.isString(e.RolePool)&&(e.RolePool={private:1,normal:0,0:0,1:1}[e.RolePool]),_.isString(e.LimitType)&&(e.LimitType={personal:0,enterprise:1,0:0,1:1}[e.LimitType]);var a=t.leads_count||t.SalesClueCount||0;return _.isUndefined(t.leads_count)&&_.isUndefined(t.SalesClueCount)||(e.LeadsCount=e.SalesClueCount=a),e},formatPostData:function(i){var e=i.RecyclingRuleType,t=(2==e?i.DealDays:3==e&&i.FollowUpDays,i.RemindRuleWords=[],_.each(i.RemindRuleList,function(e,t){var a=2==e.RuleType?e.DealDays:e.FollowUpDays,e=[$t("不收回"),$t("未成交"),$t("未跟进")][e.RuleType-1];i.RemindRuleWords.push(a+$t("天")+e+$t("提醒负责人"))}),i.RemindRuleWords=i.RemindRuleWords.join($t("；"))||$t("未设置"),[]),a=[],n=(_.each(i.AdminList,function(e){1==e.Type?t.push(e.DataID):2==e.Type&&a.push(e.DataID)}),l.getNameByIds(a,"g",!0)&&l.getNameByIds(t,"p",!0)?i.Administrator=l.getNameByIds(a,"g",!0)+" , "+l.getNameByIds(t,"p",!0):l.getNameByIds(a,"g",!0)?i.Administrator=l.getNameByIds(a,"g",!0):l.getNameByIds(t,"p",!0)&&(i.Administrator=l.getNameByIds(t,"p",!0)),[]),o=[];return _.each(i.EmployeeList,function(e){1==e.Type?o.push(e.DataID):2==e.Type&&n.push(e.DataID)}),l.getNameByIds(n,"g",!0)&&l.getNameByIds(o,"p",!0)?i.EmployeeName=l.getNameByIds(n,"g",!0)+" , "+l.getNameByIds(o,"p",!0):l.getNameByIds(n,"g",!0)?i.EmployeeName=l.getNameByIds(n,"g",!0):l.getNameByIds(o,"p",!0)&&(i.EmployeeName=l.getNameByIds(o,"p",!0)),i.UpdateTime=i.UpdateTime||(new Date).getTime(),i.ClaimIntervalDays=i.ClaimIntervalDays||0,this.authority.grayAllocateLimitFlag&&(e={day:$t("每天"),month:$t("每月")},i.GrayAllocateLimitFlag=this.authority.grayAllocateLimitFlag,i.TimeAllocateLimitText=i.TimeAllocateLimitNum?"".concat(e[i.TimeAllocateLimitType]).concat(i.TimeAllocateLimitNum).concat($t("条")):$t("crm.leads.rule_timeAllocation_nohave"),i.TimeClaimLimitText=i.TimeClaimLimitNum?"".concat(e[i.TimeClaimLimitType]).concat(i.TimeClaimLimitNum).concat($t("条")):$t("crm.leads.rule_timeClaim_nohave")),this.authority.graySfaAllocateContinue&&(e={0:$t("crm.leads.rule_allocation_over"),1:$t("crm.leads.rule_allocation_next")},i.GraySfaAllocateContinue=this.authority.graySfaAllocateContinue,i.AllocateChoice="".concat($t("crm.leads.rule_allocation_fail")).concat(e[+i.AllocateChoice||0])),i},formatDetail:function(e){return _.map(e,function(e){return{ItemCode:e.value,ItemName:e.label}})},getCountryComponents:function(e){var a=[],i=this,n=_.findWhere(e,{FieldType:25})||[].Fields;return l.getCountryAreaOptions().then(function(t){return n?(t&&n&&_.each(e,function(e){-1!=_.indexOf([26,27,28,29],e.FieldType)&&(26==e.FieldType?e.EnumDetails=i.formatDetail(t.country.options):27==e.FieldType?e.EnumDetails=i.formatDetail(t.province.options):28==e.FieldType?e.EnumDetails=i.formatDetail(t.city.options):29==e.FieldType&&(e.EnumDetails=i.formatDetail(t.district.options))),a.push(e)}),a):e})},filterFieldList:function(e){return e=e.concat(i),_.filter(e,function(e){return-1==_.indexOf(["SalesCluePoolID","PicturePath","MarketingEventID"],e.FieldName)})},getFieldList:function(a){var i=this,e=FS.getAppStore("crm-fieldlist-cluepool");e?a&&a(e):l.FHHApi({url:"/EM1HNCRM/API/v1/object/LeadsObj/controller/DescribeLayout",data:{include_detail_describe:!1,include_layout:!1,apiname:"LeadsObj",layout_type:"add",recordType_apiName:"record_sKbe4__c"},success:function(e){var t;0==e.Result.StatusCode?(t=i.parseFieldData(e.Value.objectDescribe.fields),FS.setAppStore("crm-fieldlist-cluepool",t),a&&a(t)):l.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},parseFieldData:function(e){var t,a=[];for(t in e)e[t]&&e[t]instanceof Object&&a.push(e[t]);return a},parseField:function(e){return _.filter(e||[],function(e){var t="formula"==e.type&&_.contains(["date_time","date","time"],e.return_type)||"formula"==e.type&&!e.is_index;return"formula"==e.type&&(e.type=e.return_type||e.type),!t})},addStaticField:function(e){return e=e.concat(i),_.filter(e,function(e){return"package"===e.define_type?-1==_.indexOf(["back_reason","owner_department","high_seas_name","last_modified_by","claimed_time","out_owner","out_tenant_id","leads_id","created_by","lock_status","remaining_time","partner_id","out_resources","life_status","returned_time","account_status","address","biz_status","high_seas_id","owner","expire_time","last_deal_closed_time","completion_rate","data_own_department","account_no","location","fax","url","recycled_reason","total_refund_amount","last_deal_closed_amount","filling_checker_id","is_remind_recycling","owner_modified_time"],e.api_name)&&!0===e.is_active:-1==_.indexOf(["group","true_or_false","url","time","image","file_attachment","percentile","signature","formula","department","count","employee","location","master_detail"],e.type)&&!0===e.is_active})},getDetailById:function(e,t){l.getCluePoolByID(e,!0).then(function(e){t&&t(e)})},setUsersInfo:function(){CRM.util.getUserGroups2(),CRM.util.getUserRoles2()},getPRMRight:function(a){var i=this;void 0===i.isOpenPRM?$.when(CRM.util.getPRMRight(),CRM.util.getFieldsByApiName("PersonnelObj")).then(function(e,t){i.isOpenPRM=e,i.PersonnelObj=t,a&&a()}):a&&a()},excuteAction:function(t,e,a){var i,n=this,o=(_.contains(["edit","clone"],t)&&(i="new"),e._id||e.SalesCluePoolID),l=e.leads_count||e.SalesClueCount;CRM.util.getCountryAreaOptions().then(function(){CRM.util.getCluePoolByID(o,!0,i).then(function(e){e=e.SalesCluePool||e;e[_.isUndefined(e.SalesClueCount)?"leads_count":"SalesClueCount"]=l,n.doAction(t,e,a)})})},doAction:function(t,a,i){var n=this;o.async("crm-modules/action/cluepool/cluepool",function(e){n.action||(n.action=new e,n.listenTo(n.action,{success:n.doSuccess,error:n.doError})),n.action[t]&&n.action[t](a,i,n.authority)})},doSuccess:function(e,t,a){if(!a){var i=(t=t.data||t).SalesCluePoolID||t._id||t.id;switch(e){case 2:this.showDetail(i,t);break;case 3:t.UpdateTime=$.now(),t.SalesClueCount=0,this.showDetail(i,t);break;case 4:this.slide&&this.slide.hide(),this.cluepoolNum--;break;case 1:this.cluepoolNum++}}this.refresh()},doError:function(e,t){},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var e=this;e.stopListening(),e.remove(),e.batchnewDialog&&e.batchnewDialog.cusDestroy(),e.action&&e.action.destroy(),e.slide&&e.slide.destroy(),e.table&&e.table.destroy(),e.table=e.action=e.$el=e.el=e.events=e.options=null}});t.exports=c});
define("crm-setting/clue/clue/template/detail-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div SalesCluePoolID="' + ((__t = SalesCluePoolID) == null ? "" : __t) + '" class="crm-d-detail"> <div class="detail-tit"> <h2>' + __e(obj.Name) + '</h2> <div class="detail-btns"> <span class="crm-ico-clone" action-type="clone">' + ((__t = $t("复制")) == null ? "" : __t) + "</span> ";
            if (obj.SalesClueCount > 0) {
                __p += ' <span class="crm-ico-shift" action-type="shift">' + ((__t = $t("转移")) == null ? "" : __t) + "</span> ";
            }
            __p += ' <span class="crm-ico-del" action-type="del">' + ((__t = $t("删除")) == null ? "" : __t) + '</span> <span class="crm-ico-edite" action-type="edit">' + ((__t = $t("编辑")) == null ? "" : __t) + '</span> </div> <!-- <div class="detail-status"> ';
            var overTimeHours_t = $t("crm超时提醒", {
                OverTimeHours: OverTimeHours,
                OverTimeMinutes: OverTimeMinutes
            });
            __p += ' <span style="max-width: 200px;" class="maxwidth">' + ((__t = overTimeHours_t) == null ? "" : __t) + '</span> </div> --> </div> <div class="detail-con"> <h3 class="detail-sec-tit base-tit"><span>' + ((__t = $t("基础信息")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("线索池名称")) == null ? "" : __t) + '</span> <div class="item-con">' + __e(obj.NameR || obj.Name) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("描述")) == null ? "" : __t) + '</span> <div class="item-con">' + __e(obj.DescribeInfo) + "</div> </div> ";
            if (obj.isOpenPRM) {
                __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("线索池类型")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.PoolType == 1 ? $t("伙伴独家") : $t("常规")) == null ? "" : __t) + "</div> </div> ";
            }
            __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("管理员")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.EmployeeDisplayText || "--") == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("sfa.pool.field.collaborator_pool_permissions")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.CollaboratorPoolPermissionsText || "--") == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("线索池成员")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.MemberName || "--") == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("分配领取规则")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.IsVisibleToMember ? $t("员工可见可领取管理员可分配") : $t("员工不可见管理员可分配")) == null ? "" : __t) + '</div> </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("规则设置")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("负责人规则")) == null ? "" : __t) + '</span> <div class="item-con" style="display:' + ((__t = obj.PoolOwnerRule && obj.PoolOwnerRule.length ? "" : "none") == null ? "" : __t) + '"> ' + ((__t = obj.ownerRuleContent) == null ? "" : __t) + ' </div> <div class="item-con" style="display:' + ((__t = !obj.PoolOwnerRule || !obj.PoolOwnerRule.length ? "" : "none") == null ? "" : __t) + '"> ' + ((__t = $t("无规则")) == null ? "" : __t) + ' </div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("新线索提醒")) == null ? "" : __t) + '</span> <div class="item-con" style="display:' + ((__t = obj.IsNewToNotifyAdmin ? "" : "none") == null ? "" : __t) + '">' + ((__t = $t("为管理员推送待办")) == null ? "" : __t) + '</div> <div class="item-con" style="display:' + ((__t = !obj.IsNewToNotifyAdmin ? "" : "none") == null ? "" : __t) + '">' + ((__t = $t("无规则")) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("线索领取上限")) == null ? "" : __t) + "</span> ";
            var LimitCount_t = $t("每个成员最多领取{{LimitCount}}条线索", {
                LimitCount: obj.LimitCount
            });
            __p += ' <div class="item-con">' + ((__t = LimitCount_t) == null ? "" : __t) + "" + ((__t = obj.IsChooseToNotify ? "，" + $t("领取时通知线索池管理员") : "") == null ? "" : __t) + "</div> </div> ";
            var ClaimIntervalDaysValue = obj.ClaimIntervalDays == -1 || obj.ClaimIntervalDays == 0 ? "" : obj.ClaimIntervalDays;
            var ClaimIntervalDayText = ClaimIntervalDaysValue ? ClaimIntervalDaysValue + $t("天内不能连续领取同一个线索") : $t("随时");
            __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("领取规则")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = ClaimIntervalDayText) == null ? "" : __t) + "</div> </div> ";
            if (obj.GrayAllocateLimitFlag) {
                __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("crm.time_allocate_limi")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.TimeAllocateLimitText || "--") == null ? "" : __t) + "</div> </div> ";
            }
            __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("线索池分配规则")) == null ? "" : __t) + '</span> <div class="item-con"> ';
            if (obj.GraySfaAllocateContinue) {
                __p += " <div>" + ((__t = obj.AllocateChoice) == null ? "" : __t) + "</div> ";
            }
            __p += " ";
            if (rules.length < 1) {
                __p += " " + ((__t = $t("无规则")) == null ? "" : __t) + " ";
            } else {
                __p += " ";
                _.each(rules, function(item, num) {
                    __p += " <!-- ";
                    var html = "";
                    __p += " ";
                    _.each(item.datas, function(data, index) {
                        __p += " ";
                        var strVal = data.FieldValue ? '"' + data.FieldValue + '"' : "";
                        __p += " ";
                        if (index == item.datas.length - 1) {
                            __p += " ";
                            html += '"' + data.FieldName + '" ' + data.Compare + strVal;
                            __p += " ";
                        } else {
                            __p += " ";
                            html += '"' + data.FieldName + '" ' + data.Compare + strVal + " ," + $t("且");
                            __p += " ";
                        }
                        __p += " ";
                    });
                    __p += ' --> <div class="assign-rule-container"> <div class="assign-rule-label"> ' + ((__t = $t("优先级")) == null ? "" : __t) + "" + ((__t = num + 1) == null ? "" : __t) + ' </div> <div class="assign-rule"> <div class="assign-rule-dl"> <div class="assign-rule-dt">' + ((__t = $t("筛选条件")) == null ? "" : __t) + "</div> ";
                    if (!item.IsAllSalesClue) {
                        __p += ' <div class="assign-rule-dd">' + ((__t = item.ruleText) == null ? "" : __t) + "</div> ";
                    } else {
                        __p += ' <div class="assign-rule-dd">' + ((__t = $t("全部线索")) == null ? "" : __t) + "</div> ";
                    }
                    __p += ' </div> <div class="assign-rule-dl"> <div class="assign-rule-dt">' + ((__t = [ $t("仅分配给"), $t("循环分配给"), $t("权重分配") ][item.AllocatePattern - 1]) == null ? "" : __t) + '</div> <div class="assign-rule-dd">' + ((__t = item.content) == null ? "" : __t) + "</div> </div> </div> </div> ";
                });
                __p += " ";
            }
            __p += " </div> </div> ";
            if (obj.Authority) {
                __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("crm.time_claim_limit")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.TimeClaimLimitText || "--") == null ? "" : __t) + "</div> </div> ";
            }
            __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("超时提醒时间")) == null ? "" : __t) + "</span> ";
            if (obj.AllocateOvertimeHours || obj.AllocateOvertimeMinutes) {
                __p += ' <div class="item-con"> ' + ((__t = $t("超过")) == null ? "" : __t) + " ";
                if (obj.AllocateOvertimeHours && parseInt(obj.AllocateOvertimeHours) > 0) {
                    __p += " " + ((__t = obj.AllocateOvertimeHours) == null ? "" : __t) + "" + ((__t = $t("小时")) == null ? "" : __t) + " ";
                }
                __p += " ";
                if (obj.AllocateOvertimeMinutes && parseInt(obj.AllocateOvertimeMinutes) > 0) {
                    __p += " " + ((__t = obj.AllocateOvertimeMinutes) == null ? "" : __t) + "" + ((__t = $t("分钟")) == null ? "" : __t) + " ";
                }
                __p += " " + ((__t = $t("未处理提醒管理员")) == null ? "" : __t) + " </div> ";
            }
            __p += " ";
            if (obj.OverTimeHours || obj.OverTimeMinutes) {
                __p += ' <div class="item-con"> ' + ((__t = $t("超过")) == null ? "" : __t) + " ";
                if (obj.OverTimeHours && parseInt(obj.OverTimeHours) > 0) {
                    __p += " " + ((__t = obj.OverTimeHours) == null ? "" : __t) + "" + ((__t = $t("小时")) == null ? "" : __t) + " ";
                }
                __p += " ";
                if (obj.OverTimeMinutes && parseInt(obj.OverTimeMinutes) > 0) {
                    __p += " " + ((__t = obj.OverTimeMinutes) == null ? "" : __t) + "" + ((__t = $t("分钟")) == null ? "" : __t) + " ";
                }
                __p += " " + ((__t = $t("未处理提醒负责人")) == null ? "" : __t);
                if (obj.isOpenPRM) {
                    __p += " " + ((__t = $t("和外部负责人")) == null ? "" : __t) + " ";
                }
                __p += " </div> ";
            }
            __p += " ";
            if (!obj.AllocateOvertimeHours && !obj.AllocateOvertimeMinutes && !obj.OverTimeHours && !obj.OverTimeMinutes) {
                __p += ' <div class="item-con">' + ((__t = $t("无超时提醒")) == null ? "" : __t) + "</div> ";
            }
            __p += ' </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("收回规则")) == null ? "" : __t) + '</span> <div class="item-con"> ';
            if (RecyclingCondition.length < 1) {
                __p += " " + ((__t = $t("无收回规则")) == null ? "" : __t) + " ";
            } else {
                __p += " ";
                _.each(RecyclingCondition, function(item, num) {
                    __p += " <!-- ";
                    var html = "";
                    __p += " ";
                    _.each(item.datas, function(data, index) {
                        __p += " ";
                        var strVal = data.FieldValue ? '"' + data.FieldValue + '"' : "";
                        __p += " ";
                        if (index == item.datas.length - 1) {
                            __p += " ";
                            html += '"' + data.FieldName + '" ' + data.Compare + strVal;
                            __p += " ";
                        } else {
                            __p += " ";
                            html += '"' + data.FieldName + '" ' + data.Compare + strVal + " ," + $t("且");
                            __p += " ";
                        }
                        __p += " ";
                    });
                    __p += ' --> <div class="assign-rule-container"> <div class="assign-rule-label"> ' + ((__t = $t("优先级")) == null ? "" : __t) + "" + ((__t = num + 1) == null ? "" : __t) + ' </div> <div class="assign-rule"> <div class="assign-rule-dl"> <div class="assign-rule-dt">' + ((__t = $t("线索范围")) == null ? "" : __t) + "</div> ";
                    if (item.ruleText) {
                        __p += ' <div class="assign-rule-dd">' + ((__t = item.ruleText) == null ? "" : __t) + "</div> ";
                    } else {
                        __p += ' <div class="assign-rule-dd">' + ((__t = $t("全部线索")) == null ? "" : __t) + "</div> ";
                    }
                    __p += ' </div> <div class="assign-rule-dl"> <div class="assign-rule-dt">' + ((__t = $t("收回规则")) == null ? "" : __t) + '</div> <div class="assign-rule-dd">' + ((__t = item.scopeText) == null ? "" : __t) + "</div> </div> </div> </div> ";
                });
                __p += " ";
            }
            __p += ' </div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("转移退回规则")) == null ? "" : __t) + '</span> <div class="item-con" style="display:' + ((__t = obj.AllowMemberMove ? "" : "none") == null ? "" : __t) + '">' + ((__t = $t("成员领取后可以转移该线索池线索到别的线索池")) == null ? "" : __t) + '</div> <div class="item-con" style="display:' + ((__t = obj.OnlyAllowMemberMove ? "" : "none") == null ? "" : __t) + '">' + ((__t = $t("只允许本线索池成员转移线索到该线索池")) == null ? "" : __t) + '</div> <div class="item-con" style="display:' + ((__t = obj.OnlyAllowMemberReturn ? "" : "none") == null ? "" : __t) + '">' + ((__t = $t("只允许本线索池成员退回线索到该线索池")) == null ? "" : __t) + '</div> <div class="item-con" style="display:' + ((__t = !obj.OnlyAllowMemberMove && !obj.OnlyAllowMemberReturn && !obj.AllowMemberMove ? "" : "none") == null ? "" : __t) + '">' + ((__t = $t("无规则")) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("清空规则")) == null ? "" : __t) + '</span> <div style="display:' + ((__t = obj.IsRecyclingTeamMember ? "" : "none") == null ? "" : __t) + '" class="item-con">' + ((__t = $t("线索负责人变化，清空相关团队成员")) == null ? "" : __t) + "</div> ";
            var txt1 = $t("{{pool}}{{object}}负责人清空时，清空{{object}}外部负责人", {
                pool: obj.display_name,
                object: obj.object_display_name
            });
            __p += ' <div style="display:' + ((__t = obj.IsRecyclingOutOwner ? "" : "none") == null ? "" : __t) + '" class="item-con">' + ((__t = txt1) == null ? "" : __t) + "</div> ";
            var txt2 = $t("清空{{object}}外部负责人时，清空{{object}}外部相关团队", {
                pool: obj.display_name,
                object: obj.object_display_name
            });
            __p += ' <div style="display:' + ((__t = obj.IsRecyclingOutOrdinaryTeamMember ? "" : "none") == null ? "" : __t) + '" class="item-con">' + ((__t = txt2) == null ? "" : __t) + '</div> <div style="display:' + ((__t = obj.IsCleanOwner ? "" : "none") == null ? "" : __t) + '" class="item-con">' + ((__t = $t("线索转移到该线索池时，清空线索负责人")) == null ? "" : __t) + "</div> ";
            var txt3 = $t("{{object}}转移到该{{pool}}时，清空{{object}}外部负责人", {
                pool: obj.display_name,
                object: obj.object_display_name
            });
            __p += ' <div style="display:' + ((__t = obj.IsCleanOutOwner ? "" : "none") == null ? "" : __t) + '" class="item-con">' + ((__t = txt3) == null ? "" : __t) + '</div> <div style="display:' + ((__t = !obj.IsRecyclingTeamMember && !obj.IsRecyclingOutOwner && !obj.IsRecyclingOutOrdinaryTeamMember && !obj.IsCleanOwner && !obj.IsCleanOutOwner ? "" : "none") == null ? "" : __t) + '" class="item-con">' + ((__t = $t("无规则")) == null ? "" : __t) + '</div> </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("显示设置")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("公开字段")) == null ? "" : __t) + '</span> <div class="item-con"> ';
            var num = 0;
            __p += " ";
            _.each(PoolPermissionList, function(item, index) {
                __p += " ";
                if (item.IsVisible) {
                    num++;
                    __p += " " + ((__t = num == 1 ? item.FieldCaption : "、" + item.FieldCaption) == null ? "" : __t) + " ";
                }
                __p += " ";
            });
            __p += " " + ((__t = num == 0 ? "--" : "") == null ? "" : __t) + ' </div> </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("其它")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("当前线索数量")) == null ? "" : __t) + '</span> <div class="item-con sale-count">' + ((__t = obj.SalesClueCount) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("最后修改时间")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = obj.UpdateTime) == null ? "" : __t) + "</div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/clue/manageguide/data",[],function(t,e,a){var i=$t("联系顾问了解功能");return[{title:$t("多渠道获取线索"),list:[{title:$t("营销通"),link:"#/app/marketing/index",tag:$t("独立售卖"),tagTip:i,desc:$t("营销通是完整的获客及培育工具")},{title:$t("智能表单"),link:"#crmmanage/=/module-smartforms",desc:$t("快速获取用户信息")},{title:$t("数据初始化"),link:"#datamaintenancetools/initializationandbackup",tag:$t("灰度功能"),tagTip:i,desc:$t("批量导入历史业务数据")},{title:"OpenAPI",link:"#crmmanage/=/module-openapi",desc:$t("打通与第三方的数据通路")},{title:$t("名片扫描"),tag:$t("移动端"),desc:$t("快速录入纸质名片的利器")}]},{title:$t("清洗、识别与评估"),list:[{title:$t("自动识别重复线索"),link:"#crmmanage/=/module-sysobject",desc:$t("自动识别是否存在重复数据"),productLink:"https://www.fxiaoke.com/open/imgtext/?appId=FSAID_bec6dbd&messageId=fd946595-c368-4b2f-9664-18d8d9011979&imageTextParamId=15c91e2dae4b4f77906cd1caee76553c&fsEa=5C775DAB77C1B5D5CC805D0292B0FB75"},{title:$t("属性评分"),link:"#crmmanage/=/module-davinci",desc:$t("基于静态信息评估线索质量"),productLink:"https://www.fxiaoke.com/open/imgtext/?appId=FSAID_bec6dbd&messageId=5459226d-a7cb-4854-b7f4-345a0ae0bfe4&imageTextParamId=45e827c20ffd419aa37fb9c381b42c60&fsEa=5C775DAB77C1B5D5CC805D0292B0FB75"},{title:$t("行为积分"),link:"#crmmanage/=/module-integral",tag:$t("灰度功能"),tagTip:i,desc:$t("基于动态行为评估线索意向"),productLink:"https://www.fxiaoke.com/open/imgtext/?appId=FSAID_bec6dbd&messageId=5459226d-a7cb-4854-b7f4-345a0ae0bfe4&imageTextParamId=45e827c20ffd419aa37fb9c381b42c60&fsEa=5C775DAB77C1B5D5CC805D0292B0FB75"},{title:$t("工商回填"),desc:$t("回填工商系统信息")},{title:$t("手机号归属地"),desc:$t("自动识别手机号归属区域")}]},{title:$t("分配与流转"),list:[{title:$t("线索池"),link:"#crmmanage/=/module-clue",desc:$t("线索分群分级维护管理")},{title:$t("分配规则"),link:"#crmmanage/=/module-clue",desc:$t("根据规则自动化指定负责人")},{title:$t("保有量"),link:"#crmmanage/=/module-clue",desc:$t("控制员工可负责的线索量"),productLink:"https://www.fxiaoke.com/open/imgtext/?appId=FSAID_bec6dbd&messageId=5c39b607-7cc9-4f34-b167-7bfc712bdb11&imageTextParamId=13cc64cdbff64f3880c6c726f6074ea0&fsEa=5C775DAB77C1B5D5CC805D0292B0FB75"},{title:$t("回收规则"),link:"#crmmanage/=/module-clue",desc:$t("根据规则自动回收线索到线索池"),productLink:"https://www.fxiaoke.com/open/imgtext/?appId=FSAID_bec6dbd&messageId=7609e6a5-789a-464c-9741-5880e6f9f172&imageTextParamId=1783f17de8e54ea199f028fbb21aee0b&fsEa=5C775DAB77C1B5D5CC805D0292B0FB75"},{title:$t("工作流"),link:"#crmmanage/=/module-workflow",desc:$t("让系统自动化协助业务处理")}]},{title:$t("跟进与转化"),list:[{title:$t("跟进规则"),link:"#crmmanage/=/module-clue",desc:$t("制定可算跟进动作的操作项")},{title:$t("转换规则"),link:"#crmmanage/=/module-clue",desc:$t("定义转换过程中的各类规则")},{title:$t("审批流"),link:"#crmmanage/=/module-approval",desc:$t("关键动作自动触发审批")},{title:$t("外勤"),link:"#app/checkin/statistics/=/param-datalist",tag:$t("移动端"),desc:$t("可记录拜访时间地址方便追踪跟进")}]},{title:$t("数据分析与决策"),list:[{title:$t("列表洞察"),link:"#crm/list/=/LeadsObj",tag:$t("灰度功能"),tagTip:i,desc:$t("洞察线索数据趋势")},{title:$t("线索驾驶舱"),link:"#bi/dashboard/=/dashboardId-BI_DSB_5de787ccf5cee90001c4b095",desc:$t("大屏展示线索动态")},{title:$t("预设报表/统计图"),link:"#bi/list",desc:$t("一站式了解线索本身及员工效能")}]}]});
define("crm-setting/clue/manageguide/manageguide",["./tpl-html","./data"],function(i,n,t){var e=i("./tpl-html"),l=i("./data");return function(){return FxUI.create({template:'<fx-dialog\n                        :visible.sync="dialogVisible"\n                        fullscreen\n                        appendToBody\n                        hasScroll\n                        :title="$t(\'线索配置向导\')">\n                        <div v-html="content" @click="toLink"></div>\n                    </fx-dialog>',data:function(){return{dialogVisible:!1,content:e(l)}},methods:{show:function(){this.dialogVisible=!0},hide:function(){this.dialogVisible=!1},toLink:function(i){var i=$(i.target),n=i.closest(".cm-guide-linkitem");!i.hasClass("cm-guide-linkitem")&&!n.length||i.hasClass("cm-guide-item-product")||window.open(window.location.href.replace(/(#.*)/,n.data("link")),"_blank")}}})}});
define("crm-setting/clue/manageguide/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-cluemanage-guide"> ';
            _.each(obj, function(item) {
                __p += ' <div class="cm-guide-column"> <div class="cm-guide-dt" title="' + ((__t = item.title) == null ? "" : __t) + '">' + ((__t = item.title) == null ? "" : __t) + '</div> <div class="cm-guide-list"> ';
                _.each(item.list, function(sitem) {
                    __p += " ";
                    if (sitem.link) {
                        __p += ' <div class="cm-guide-item cm-guide-linkitem" data-link="' + ((__t = sitem.link) == null ? "" : __t) + '"> ';
                    } else {
                        __p += ' <div class="cm-guide-item"> ';
                    }
                    __p += ' <div class="cm-guide-item-hd"> <h3 class="cm-guide-item-title">' + ((__t = sitem.title) == null ? "" : __t) + "</h3> ";
                    if (sitem.productLink) {
                        __p += ' <a target="_blank" href="' + ((__t = sitem.productLink) == null ? "" : __t) + '" class="cm-guide-item-product">' + ((__t = $t("产品说")) == null ? "" : __t) + "</a> ";
                    }
                    __p += ' </div> <div class="cm-guide-item-desc">' + ((__t = sitem.desc) == null ? "" : __t) + "</div> ";
                    if (sitem.tag) {
                        __p += ' <span class="cm-guide-item-tag ' + ((__t = sitem.tagTip && "crm-ui-title") == null ? "" : __t) + '" data-title="' + ((__t = sitem.tagTip) == null ? "" : __t) + '" data-pos="top"><i>' + ((__t = sitem.tag) == null ? "" : __t) + "</i></span> ";
                    }
                    __p += " </div> ";
                });
                __p += " </div> </div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/clue/systemsetting/systemsetting",[],function(e,t,n){var i=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.render()},render:function(){var t=this,n=document.createElement("div");document.getElementsByClassName("clue-scene-box")[0].appendChild(n),e.async("paas-vui/sdk",function(e){e.getObject().then(function(e){e.getScene().then(function(e){t.$el.find(".crm-loading").remove(),t.scene=e.init({el:n,object_api_name:"LeadsObj",handler_type:"interconnect",extend_attribute:"salesclueformember,salesclueformgr",from:"dlt"})})})})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var e=this;e.scene&&e.scene.$destroy(),e.remove()}});n.exports=i});
define("crm-setting/clue/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit crm-manageclue-title"> <h2><span class="tit-txt">' + ((__t = $t("线索和线索池管理")) == null ? "" : __t) + '<a class="crm-doclink" href="http://www.fxiaoke.com/mob/guide/crmdoc/src/7-3-1线索和线索池管理.html" target="_blank"></a></span></h2> <div class="clue-manageguide">' + ((__t = $t("线索配置向导")) == null ? "" : __t) + '</div> </div> <div class="crm-module-con"> <div class="crm-tab"> <a class="item page1" href="#crm/setting/clue/=/page1">' + ((__t = $t("线索池管理")) == null ? "" : __t) + "</a> ";
            if (param.indexOf("system_setting") > -1) {
                __p += ' <a class="item page5" href="#crm/setting/clue/=/page5">' + ((__t = $t("线索池场景管理")) == null ? "" : __t) + "</a> ";
            }
            __p += ' <a class="item page2" href="#crm/setting/clue/=/page2">' + ((__t = $t("线索转换设置")) == null ? "" : __t) + '</a> <a class="item page4" href="#crm/setting/clue/=/page4">' + ((__t = $t("线索保有量")) == null ? "" : __t) + '</a> <a class="item page3" href="#crm/setting/clue/=/page3">' + ((__t = $t("线索跟进规则")) == null ? "" : __t) + '</a> </div> <div class="tab-con"> <div class="crm-p20 crm-scroll"> <div class="item clue-pool-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item clue-scene-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item clue-transform-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item clue-behavior-box" style="display:none;"> <div class="crm-loading"></div> </div> <div class="item clue-ownership-box" style="display:none;"> <div class="crm-loading"></div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _defineProperty(t,n,e){return(n=_toPropertyKey(n))in t?Object.defineProperty(t,n,{value:e,enumerable:!0,configurable:!0,writable:!0}):t[n]=e,t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,n){if("object"!=_typeof(t)||!t)return t;var e=t[Symbol.toPrimitive];if(void 0===e)return("string"===n?String:Number)(t);e=e.call(t,n||"default");if("object"!=_typeof(e))return e;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/clue/transform/filter-group-setting",["crm-modules/common/filtergroup/filtergroup"],function(t,n,e){function a(t){return t&&null!=(t=JSON.parse(t))&&t.length?"WHERES":"ALL"}var s=t("crm-modules/common/filtergroup/filtergroup");return function(t,n){e=n;var e,r,o=(t=t)?(r={account:{mustTransfer:(null==t?void 0:t.accountValidateMustTransfer)||!0,allowTransferCondition:null==t?void 0:t.accountAllowTransferCondition,mustTransferCondition:null==t?void 0:t.accountMustTransferCondition,allowDataScope:a(null==t?void 0:t.accountAllowTransferCondition),mustDataScope:a(null==t?void 0:t.accountMustTransferCondition)},contact:{mustTransfer:t.contactValidateMustTransfer,allowTransferCondition:null==t?void 0:t.contactAllowTransferCondition,mustTransferCondition:null==t?void 0:t.contactMustTransferCondition,allowDataScope:a(null==t?void 0:t.contactAllowTransferCondition),mustDataScope:a(null==t?void 0:t.contactMustTransferCondition)},oppor:{allowTransferCondition:null==t?void 0:t.opportunityAllowTransferCondition,allowDataScope:a(null==t?void 0:t.opportunityAllowTransferCondition),group:{opportunity:{mustTransfer:t.opportunityValidateMustTransfer||!1,mustTransferCondition:null==t?void 0:t.opportunityMustTransferCondition,mustDataScope:a(null==t?void 0:t.opportunityMustTransferCondition)},newOpportunity:{mustTransfer:t.newOpportunityValidateMustTransfer||!1,mustTransferCondition:null==t?void 0:t.newOpportunityMustTransferCondition,mustDataScope:a(null==t?void 0:t.newOpportunityMustTransferCondition)}}}},"open"!==e.config_newopportunity_open&&delete r.oppor.newOpportunity,!1===CRM.get("permission").OpportunityObj&&delete r.oppor.opportunity,_.contains(["kis_edition","dealer_edition","promotion_sales_edition"],CRM.control.crmVersion)&&delete r.oppor,Object.keys(r).map(function(o){return"oppor"!=o?Object.assign({},r[o],{apiName:o,filterGroup:[],title:$t("crm.LeadsObj.transform.setting.item.{{objectName}}",{objectName:$t("crm.".concat(o))})}):Object.assign({},r[o],{apiName:o,title:$t("crm.LeadsObj.transform.setting.item.{{objectName}}",{objectName:$t("crm.opportunity")}),group:Object.keys(r[o].group).map(function(t){var n=r[o].group[t],e=1==(null==(e=Object.keys(r[o].group))?void 0:e.length);return Object.assign({},n,{apiName:t,title:e?$t("crm.必须转换的条件"):"opportunity"==t?$t("crm.必须转换为商机的条件"):$t("crm.必须转换为商机2.0的条件")})})})})):[],i={};return FxUI.create({wrapper:n.wrapper,template:'\n        <div class="transform-setting-inner">\n          <template v-for="item in filterGroupData">\n            <div \n              class="transform-setting-item"\n              :key="item.apiName"\n            >\n              <p class="rule-item-title">{{ item.title }}</p>\n              <div class="rule-item-content">\n                <div class="title-wrap">\n                  <span class="title">'.concat($t("crm.允许转换的条件"),'</span>\n                </div>\n                <div class="filter-group-wrap">\n                  <fx-radio-group\n                    v-model="item.allowDataScope"\n                    size="mini"\n                    :disabled="item.apiName === \'account\'"\n                  >\n                    <fx-radio label="ALL">').concat($t("crm.LeadsObj.transform.setting.content.radio"),'</fx-radio>\n                    <fx-radio label="WHERES">').concat($t("crm.LeadsObj.transform.setting.content.radio1"),'</fx-radio>\n                  </fx-radio-group>\n                  <div :class="\'content-filter-\'+item.apiName+\'__allow\'" v-show="item.allowDataScope == \'WHERES\'"></div>\n                </div>\n                <template v-if="item.apiName != \'oppor\'">\n                  <div class="title-wrap">\n                    <span class="title">').concat($t("crm.必须转换的条件"),'</span>\n                    <fx-switch :disabled="item.apiName === \'account\'" size="mini" v-model="item.mustTransfer"></fx-switch>\n                  </div>\n                  <div class="filter-group-wrap" v-show="item.mustTransfer">\n                    <fx-radio-group\n                      v-model="item.mustDataScope"\n                      size="mini"\n                      :disabled="item.apiName === \'account\'"\n                    >\n                      <fx-radio label="ALL">').concat($t("crm.LeadsObj.transform.setting.content.radio"),'</fx-radio>\n                      <fx-radio label="WHERES">').concat($t("crm.LeadsObj.transform.setting.content.radio1"),'</fx-radio>\n                    </fx-radio-group>\n                    <div :class="\'content-filter-\'+item.apiName+\'__must\'" v-show="item.mustDataScope == \'WHERES\'"></div>\n                  </div>\n                </template>\n                <template v-else>\n                  <template v-for="groupItem in item.group">\n                    <div class="title-wrap">\n                    <span class="title">{{groupItem.title}}</span>\n                      <fx-switch size="mini" v-model="groupItem.mustTransfer"></fx-switch>\n                    </div>\n                    <div class="filter-group-wrap" v-show="groupItem.mustTransfer">\n                      <fx-radio-group\n                        v-model="groupItem.mustDataScope"\n                        size="mini"\n                      >\n                        <fx-radio label="ALL">').concat($t("crm.LeadsObj.transform.setting.content.radio"),'</fx-radio>\n                        <fx-radio label="WHERES">').concat($t("crm.LeadsObj.transform.setting.content.radio1"),"</fx-radio>\n                      </fx-radio-group>\n                      <div :class=\"'content-filter-'+groupItem.apiName+'__must'\" v-show=\"groupItem.mustDataScope == 'WHERES'\"></div>\n                    </div>\n                  </template>\n                </template>\n              </div>\n            </div>\n          </template>\n        </div>\n      "),data:function(){return{filterGroupData:o}},computed:{oppMustDisabled:function(){var t=this.filterGroupData.find(function(t){return"newOpportunity"==t.apiName});return!(null==t||!t.mustTransfer)},newOppMustDisabled:function(){var t=this.filterGroupData.find(function(t){return"opportunity"==t.apiName});return!(null==t||!t.mustTransfer)}},mounted:function(){this.$nextTick(this.initFilterGroup)},methods:{initFilterGroup:function(){var n=this;Object.keys(i).length&&(i={}),this.filterGroupData.forEach(function(t){"account"!=t.apiName&&(n.createFilterGroup({wrapper:"content-filter-".concat(t.apiName,"__allow"),defaultValue:t.allowTransferCondition}),t.group?t.group.forEach(function(t){n.createFilterGroup({wrapper:"content-filter-".concat(t.apiName,"__must"),defaultValue:t.mustTransferCondition})}):n.createFilterGroup({wrapper:"content-filter-".concat(t.apiName,"__must"),defaultValue:t.mustTransferCondition}))})},createFilterGroup:function(t){var n=t.wrapper,t=t.defaultValue;i[n]=new s({$wrapper:$(".".concat(n)),title:$t("且(AND)"),width:800,addBtnName:$t("添加条件"),apiname:"LeadsObj",defaultValue:t?JSON.parse(t):[]})},getValue:function(){var r={};return this.filterGroupData.forEach(function(t){var n=t.apiName,e="account"==n||"ALL"==t.allowDataScope?null:i["content-filter-".concat(n,"__allow")].getValue(),o="account"!=n&&"oppor"!=n&&"ALL"!=t.mustDataScope&&t.mustTransfer?i["content-filter-".concat(n,"__must")].getValue():null;"oppor"!=n?r=Object.assign({},r,_defineProperty(_defineProperty(_defineProperty({},"".concat(n,"AllowTransferCondition"),e),"".concat(n,"ValidateMustTransfer"),t.mustTransfer),"".concat(n,"MustTransferCondition"),o)):(r.opportunityAllowTransferCondition=e,t.group.forEach(function(t){var n="ALL"!=t.mustDataScope&&t.mustTransfer?i["content-filter-".concat(t.apiName,"__must")].getValue():null;r=Object.assign({},r,_defineProperty(_defineProperty({},"".concat(t.apiName,"ValidateMustTransfer"),t.mustTransfer),"".concat(t.apiName,"MustTransferCondition"),n))}))}),Object.assign({},r)},validator:function(){return!0},destroy:function(){for(key in i)i[key].destroy&&i[key].destroy()}}})}});
define("crm-setting/clue/transform/mustsetting",[],function(e,t,n){return function(e,t){n=t,o=[{label:$t("线索的业务类型"),key:"record_type"},{label:$t("转换为{{objectName}}",{objectName:$t("crm.客户")}),key:"convert_accountobj",checkAll:!0,isIndeterminate:!1},{label:$t("转换为{{objectName}}",{objectName:$t("crm.联系人")}),key:"convert_contactobj",checkAll:!1,isIndeterminate:!1},{label:$t("转换为{{objectName}}",{objectName:$t("crm.商机")}),key:"convert_opportunityobj",checkAll:!1,isIndeterminate:!1,oppositeKey:"convert_newopportunityobj"},{label:$t("转换为{{objectName}}",{objectName:$t("crm.商机2")}),key:"convert_newopportunityobj",checkAll:!1,isIndeterminate:!1,oppositeKey:"convert_opportunityobj"}],i=[],"open"!==n.config_newopportunity_open&&i.push("convert_newopportunityobj"),!1===CRM.get("permission").OpportunityObj&&i.push("convert_opportunityobj"),_.contains(["kis_edition","dealer_edition","promotion_sales_edition"],CRM.control.crmVersion)&&i.push("convert_newopportunityobj","convert_opportunityobj");var n,o,i,c=_.filter(o,function(e){return!_.contains(i,e.key)});return FxUI.create({wrapper:t.wrapper,template:'<div class="mustsetting-wrapper">\n                <fx-table\n                :height="height"\n                :data="tableData"\n                :border="true"\n                style="width: 100%">\n                    <fx-table-column v-for="item in columns"\n                    :label="item.label">\n                        <template slot="header" slot-scope="scope">\n                            <fx-checkbox v-if="item.key!=\'record_type\'" :disabled="item.key==\'convert_accountobj\'" :indeterminate="item.isIndeterminate" :value="item.checkAll" @change="handleCheckAllChange(item, scope)"></fx-checkbox>\n                            <div class="mustsetting-table-label">{{item.label}}</div>\n                        </template>\n                        <template slot-scope="scope">\n                            <div v-if="item.key==\'record_type\'" class="mustsetting-table-con">{{scope.row[item.key + \'__s\']}}</div>\n                            <div v-else class="mustsetting-table-con">\n                                <fx-checkbox v-if="item.key!=\'record_type\'" :disabled="item.key==\'convert_accountobj\'" :value="scope.row[item.key]" @change="handleChange(item, scope)"></fx-checkbox>\n                            </div>\n                        </template>\n                    </fx-table-column>\n                </fx-table>\n                <div v-if="showMore" class="mustsetting-toggle" @click="toggleHeight()">{{moreBtnLabel}}<i class="mustsetting-toggle-icon el-input__icon el-icon-arrow-down" :class="{\'mustsetting-toggle-reverse\': isAuto}"></i></div>\n                </div>',data:function(){return{columns:c,tableData:e,height:null,showMore:4<e.length,moreBtnLabel:$t("展开全部业务类型"),isAuto:!0}},mounted:function(){this.setColumnsInfo(this.columns),this.toggleHeight()},methods:{toggleHeight:function(){this.isAuto=!this.isAuto;var e=this.tableData.length+1;this.isAuto?this.moreBtnLabel=$t("收起全部业务类型"):(e=4<e?4:e,this.moreBtnLabel=$t("展开全部业务类型")),this.height=41*e},handleCheckAllChange:function(t,e){t.checkAll=!t.checkAll,t.isIndeterminate=!1,this.tableData.forEach(function(e){e[t.key]=t.checkAll}),t.oppositeKey&&t.checkAll&&this.setOpposite(t,e,"all")},handleChange:function(e,t){t.row[e.key]=!t.row[e.key],e.oppositeKey&&t.row[e.key]&&this.setOpposite(e,t),this.setColumnInfo(e)},setColumnInfo:function(e){var t=this.tableData.length,n=this.getCheckedCount(this.tableData,e.key);e.isIndeterminate=0<n&&n<t,e.checkAll=n==t},setColumnsInfo:function(e){var t=this;_.each(e,function(e){t.setColumnInfo(e)})},getCheckedCount:function(e,t){var n=0;return e.forEach(function(e){e[t]&&n++}),n},setOpposite:function(e,t,n){var o=_.findWhere(this.columns,{key:e.oppositeKey});o&&("all"==n?(o.checkAll=!e.checkAll,o.isIndeterminate=!1,this.tableData.forEach(function(e){e[o.key]=o.checkAll})):(t.row[e.oppositeKey]=!t.row[e.key],this.setColumnInfo(o)))},getValue:function(){return this.tableData}}})}});
define("crm-setting/clue/transform/template/transform-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<!-- * @Descripttion: * @Author: LiAng * @Date: 2020-02-03 17:33:30 * @LastEditors: LiAng * @LastEditTime: 2020-03-18 18:47:57 --> <div class="transform-container"> <div class="transform-cont"> <div class="transform-title">1.' + ((__t = $t("允许线索转换成已有客户")) == null ? "" : __t) + '</div> <div class="transform-info">' + ((__t = $t("crm.客户无法关联")) == null ? "" : __t) + '</div> <span class="j-allow-relation-switch"></span> </div> ';
            var isGray = CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE");
            __p += " ";
            if (!isGray) {
                __p += ' <div class="transform-cont"> <div class="transform-title">2.' + ((__t = $t("销售线索转换时，必须转换为")) == null ? "" : __t) + '</div> <div class="transform-info">' + ((__t = isNewOppo ? $t("crm.操作人无商机2.0权限说明") : $t("crm.操作人无权限说明")) == null ? "" : __t) + '</div> <div class="transform-content transform-mustcf"></div> </div> <div class="transform-team-sale-cont"> <div class="transform-title">3.' + ((__t = $t("销售记录和相关团队自动带入")) == null ? "" : __t) + '</div> <div class="transform-content team-sale-setting"></div> </div> <div class="transform-action-change"> <div class="transform-title">4.' + ((__t = $t("线索转换触发线索阶段变更设置")) == null ? "" : __t) + '</div> <div class="leads-transform-content"> <div class="leads-transform-header"> <span>' + ((__t = $t("转换操作")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("是否变更到指定阶段")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("线索阶段")) == null ? "" : __t) + '</span> </div> <div class="leads-transform-customer"> <span class="action-change-label">' + ((__t = $t("线索转客户")) == null ? "" : __t) + '</span> <span class="j-customer-switch"></span> <span class="j-customer-select"></span> </div> <div class="leads-transform-oppor"> <span class="action-change-label">' + ((__t = $t("线索转商机")) == null ? "" : __t) + '</span> <span class="j-oppor-switch"></span> <span class="j-oppor-select"></span> </div> </div> </div> ';
            } else {
                __p += ' <div class="transform-cont"> <div class="transform-title">2.' + ((__t = $t("crm.销售线索转换条件")) == null ? "" : __t) + '</div> <div class="transform-info">' + ((__t = $t("crm.配置销售线索允许进行转换的满足条件")) == null ? "" : __t) + '</div> <div class="transform-filter-group-setting"></div> </div> <div class="transform-team-sale-cont"> <div class="transform-title">3.' + ((__t = $t("销售记录和相关团队自动带入")) == null ? "" : __t) + '</div> <div class="transform-content team-sale-setting"></div> </div> <div class="transform-action-change transform-cont transform-filter-group"> <div class="transform-title">4.' + ((__t = $t("线索转换触发线索阶段变更设置")) == null ? "" : __t) + '</div> <div class="leads-transform-content transform-filter-group"> <div class="content-item"> <p class="item-title">' + ((__t = $t("线索转客户时")) == null ? "" : __t) + '</p> <span class="j-customer-checkbox"></span> <span class="j-customer-select item-select-wrap"></span> </div> <div class="content-item"> <p class="item-title">' + ((__t = $t("线索转商机时")) == null ? "" : __t) + '</p> <span class="j-oppor-checkbox"></span> <span class="j-oppor-select item-select-wrap"></span> </div> </div> </div> ';
            }
            __p += ' <div class="transform-cont"> <div class="transform-title">5.' + ((__t = $t("其他设置")) == null ? "" : __t) + '</div> <div class="transform-content mn-checkbox-box mn-checkbox-box-convertRights"> ';
            if (!isGray) {
                __p += ' <span class="mn-checkbox-item ' + ((__t = obj.convertRights ? "mn-selected" : "") == null ? "" : __t) + ' j-convertrights"></span> <span class="mn-label">' + ((__t = $t("允许用户在转换时关联无查看权限客户")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </div> <div class="transform-content mn-checkbox-box mn-checkbox-box-prm"> ';
            if (obj.openPrm) {
                __p += ' <span class="mn-checkbox-item ' + ((__t = obj.convertPRM ? "mn-selected" : "") == null ? "" : __t) + ' j-convertrights-prm"></span> <span class="mn-label">' + ((__t = $t("sfa.crm.transform.prmtitle")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </div> </div> <div class="transform-btn-box"> <span class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
function asyncGeneratorStep(t,e,n,s,a,i,c){try{var o=t[i](c),r=o.value}catch(t){return void n(t)}o.done?e(r):Promise.resolve(r).then(s,a)}function _asyncToGenerator(o){return function(){var t=this,c=arguments;return new Promise(function(e,n){var s=o.apply(t,c);function a(t){asyncGeneratorStep(s,e,n,a,i,"next",t)}function i(t){asyncGeneratorStep(s,e,n,a,i,"throw",t)}a(void 0)})}}define("crm-setting/clue/transform/transform",["crm-modules/common/util","./template/transform-html","./mustsetting","./filter-group-setting"],function(t,e,n){var s,i=t("crm-modules/common/util"),a=t("./template/transform-html"),c=t("./mustsetting"),o=t("./filter-group-setting"),t=Backbone.View.extend({configs:{40:"0",41:"",config_newopportunity_open:"",51:"0,''",52:"0,''",leads_transfer_right_setting:"1",must_transfer_by_type:"",put_crm_feed_select_mode:"1",put_teams_select_mode:"1"},initialize:(s=_asyncToGenerator(regeneratorRuntime.mark(function t(e){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return this.setElement(e.wrapper),this.opts=e,this.comps={},t.next=5,this.beforeGetConfigs();case 5:this.openPrm=t.sent,this.getConfigs(this.render);case 7:case"end":return t.stop()}},t,this)})),function(t){return s.apply(this,arguments)}),events:{"click .j-transform":"clickHandler","click .j-save":"saveConfig"},beforeGetConfigs:function(){return new Promise(function(e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/sfa_license_service/service/check_license_exist",data:{packageName:"prm_app"},success:function(t){0===t.Result.StatusCode?e(t.Value):e(!1)}},{errorAlertModel:1})})},render:function(){var t=this,e=this,n="0"!==e.configs[40];e.$el.html(a({isNewOppo:"open"==e.configs.config_newopportunity_open,convertRights:"1"==e.configs.leads_transfer_right_setting,convertPRM:"1"===e.configs.enable_duplicate_account_filter_partners,openPrm:e.openPrm})),CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?this.renderFilterGroupSetting():this.initMustConvert(),e.$item=$(".mn-checkbox-item",e.$el),this.renderSwitchTop(n),i.getFieldsByApiName("LeadsObj").done(function(){t.renderActionChange()}),this.renderTeamSaleSet()},renderTeamSaleSet:function(){var t=this;this.comps.teamSale_select&&this.comps.teamSale_select.destroy(),this.comps.teamSale_select=FxUI.create({wrapper:".team-sale-setting",template:'<div class= "team-sale-comp">\n\t\t\t\t\t\t\t<div class="sale-team-container">\n\t\t\t\t\t\t\t\t<span class = "sale-team-label">'.concat($t("crm.clue.transform.sale"),'</span>\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\t\tv-model="saleValue"\n\t\t\t\t\t\t\t\t\t:options="saleOptions"\n\t\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div class="sale-team-container">\n\t\t\t\t\t\t\t\t<span class="sale-team-label">').concat($t("crm.clue.transform.team"),'</span>\n\t\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\t\tv-model="teamValue"\n\t\t\t\t\t\t\t\t\t:options="teamOptions"\n\t\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t\t></fx-select>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t'),data:function(){return{saleOptions:[{value:"1",label:$t("crm.clue.transform.saleDefault")},{value:"2",label:$t("crm.clue.transform.saleMust")},{value:"3",label:$t("crm.clue.transform.saleNoMust")}],teamOptions:[{value:"1",label:$t("crm.clue.transform.saleDefault")},{value:"2",label:$t("crm.clue.transform.saleMust")},{value:"3",label:$t("crm.clue.transform.saleNoMust")}],saleValue:t.configs.put_crm_feed_select_mode,teamValue:t.configs.put_teams_select_mode}}})},renderFilterGroupSetting:function(){var e=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_transfer_config/service/query",data:{},success:function(t){null!=t&&t.Value&&(t=(null==t?void 0:t.Value).transferConfig,e.filterGroupSetting=o(t,{wrapper:e.$(".transform-filter-group-setting")[0],config_newopportunity_open:e.configs.config_newopportunity_open}))},complete:function(){}},{errorAlertModel:1})},initMustConvert:function(){var e=this;CRM.util.getRecordType({describeApiName:"LeadsObj",is_only_active:!0}).then(function(t){e.renderMustConvert(t)})},renderMustConvert:function(t){t=this.toMustRenderData(t);this.mustInstance=c(t,{wrapper:this.$(".transform-mustcf")[0],config_newopportunity_open:this.configs.config_newopportunity_open})},toMustRenderData:function(t){var n=this.configs.must_transfer_by_type||this.configs[41],s=[];return _.each(t,function(t){var e=_.isArray(n)?n:n[t.api_name]?n[t.api_name].split(","):"1,0,0,0";s.push({record_type:t.api_name,record_type__s:t.label,convert_accountobj:!!+e[0],convert_contactobj:!!+e[1],convert_opportunityobj:!!+e[2],convert_newopportunityobj:!!+e[3]})}),s},toMustSubmitData:function(t){var n={};return _.each(t,function(t){var e=_.values(_.pick(t,["convert_accountobj","convert_contactobj","convert_opportunityobj","convert_newopportunityobj"])),e=_.map(e,function(t){return+t});n[t.record_type]=e.join(",")}),JSON.stringify(n)},renderActionChange:function(){CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?this.renderCheckbox():this.renderSwitch(),this.renderSelect()},renderSwitchTop:function(t){var e=this;this.comps.customer_switch_top=FxUI.create({wrapper:".j-allow-relation-switch",template:'<fx-switch v-model="value" size="small" @change="change"></fx-switch>',data:function(){return{value:t}},methods:{change:function(){e.configs[40]=!0===this.value?"1":"0"}}})},renderCheckbox:function(){var e=this;this.comps.customer_checkbox=FxUI.create({wrapper:".j-customer-checkbox",template:'\n\t\t\t\t\t<fx-checkbox \n\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\tsize="mini" \n\t\t\t\t\t\t@change="change"\n\t\t\t\t\t>'.concat($t("线索阶段变更为"),"</fx-checkbox>\n\t\t\t\t"),data:function(){return{value:!1}},methods:{change:function(t){Vue.nextTick(function(){e.comps.customer_select.disabled=!t})}},created:function(){this.value=e.configs[51].switch_value||!1}}),this.comps.oppor_checkbox=FxUI.create({wrapper:".j-oppor-checkbox",template:'\n\t\t\t\t\t<fx-checkbox \n\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\tsize="mini" \n\t\t\t\t\t\t@change="change"\n\t\t\t\t\t>'.concat($t("线索阶段变更为"),"</fx-checkbox>\n\t\t\t\t"),data:function(){return{value:!1}},methods:{change:function(t){Vue.nextTick(function(){e.comps.oppor_select.disabled=!t})}},created:function(){this.value=e.configs[52].switch_value||!1}}),this.comps.mn_checkbox=FxUI.create({wrapper:".mn-checkbox-box-convertRights",template:'\n\t\t\t\t\t<fx-checkbox \n\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t@change="change"\n\t\t\t\t\t>'.concat($t("允许用户在转换时关联无查看权限客户"),"</fx-checkbox>\n\t\t\t\t"),data:function(){return{value:!1}},methods:{change:function(t){e.configs.leads_transfer_right_setting=t?"1":"0"}},created:function(){this.value="1"==e.configs.leads_transfer_right_setting}})},renderSwitch:function(){var e=this;this.comps.customer_switch=FxUI.create({wrapper:".j-customer-switch",template:'<fx-switch v-model="value" size="small" @change="change"></fx-switch>',data:function(){return{value:!1}},methods:{change:function(){this.is_disabled()},is_disabled:function(){var t=!1;this.value||(t=!0),Vue.nextTick(function(){e.comps.customer_select.disabled=t})}},created:function(){this.value=e.configs[51].switch_value||!1,this.is_disabled()}}),this.comps.appor_switch=FxUI.create({wrapper:".j-oppor-switch",template:'<fx-switch v-model="value" size="small" @change="change"></fx-switch>',data:function(){return{value:!1}},methods:{change:function(){this.is_disabled()},is_disabled:function(){var t=!1;this.value||(t=!0),Vue.nextTick(function(){e.comps.oppor_select.disabled=t})}},created:function(){this.value=e.configs[52].switch_value||!1,this.is_disabled()}})},getLeadsStageOptions:function(){var t=CRM.get("fields.LeadsObj");return t.leads_stage?_.filter(t.leads_stage.options,function(t){return!t.not_usable}):[]},renderSelect:function(){var t=this,e=this.getLeadsStageOptions();this.comps.customer_select=FxUI.create({wrapper:".j-customer-select",template:'<fx-select\n\t\t\t\t\t\t\t\t:placeholder="$t(\'请选择\')"\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t\t:disabled="disabled"\n\t\t\t\t\t\t\t></fx-select>',data:function(){return{options:e,value:"SQL",disabled:!1}},watch:{disabled:function(t){t&&(this.value="")}},created:function(){this.value=t.configs[51].select_value||"SQL"}}),this.comps.oppor_select=FxUI.create({wrapper:".j-oppor-select",template:'<fx-select\n\t\t\t\t\t\t\t\t:placeholder="$t(\'请选择\')"\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t\t:disabled="disabled"\n\t\t\t\t\t\t\t></fx-select>',data:function(){return{options:e,value:"SQL",disabled:!1}},watch:{disabled:function(t){t&&(this.value="")}},created:function(){this.value=t.configs[52].select_value||"SQL"}})},getActionChangeData:function(){var t=[],e=CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?"customer_checkbox":"customer_switch",e=this.comps[e].value?1:0,n=this.comps.customer_select.value,e=(t.push({key:"51",value:"".concat(e,",").concat(n)}),CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?"oppor_checkbox":"appor_switch"),n=this.comps[e].value?1:0,e=this.comps.oppor_select.value;return t.push({key:"52",value:"".concat(n,",").concat(e)}),t},clickHandler:function(t){var n,s,a=this,t=$(t.target);return t.hasClass("disabled-selected")||(t[t.hasClass("mn-selected")?"removeClass":"addClass"]("mn-selected"),n=t.data("name"),s=t.data("index"),n&&_.map(a.$item,function(t){var t=$(t),e=t.data("index");e!==s&&t.data("name")===n&&(t.removeClass("mn-selected"),a.configs[41][e]=0)}),a.configs[41][s]=t.hasClass("mn-selected")?1:0),!1},getConfigs:function(e){var n=this,t=(n.openPrm&&(n.configs=_.extend(n.configs,{enable_duplicate_account_filter_partners:"0"})),_.keys(n.configs));_.map(t,function(t){}),i.getConfigValues(t).then(function(t){n.formatData(t),i.getPermissionByApiName(["OpportunityObj"]).done(function(){e&&e.call(n)})})},formatData:function(t){var e=this;if(_.map(t,function(t){e.configs[t.key]=t.value}),e.configs.must_transfer_by_type)try{e.configs.must_transfer_by_type=JSON.parse(e.configs.must_transfer_by_type)}catch(t){}else e.configs[41]&&(e.configs[41]=e.configs[41].split(","));e.configs[51]&&(t=e.configs[51].split(","),e.configs[51]={switch_value:"1"===t[0],select_value:t[1]}),e.configs[52]&&(t=e.configs[52].split(","),e.configs[52]={switch_value:"1"===t[0],select_value:t[1]})},getSubmitData:function(){var t=this,e=[],n=(_.keys(t.configs),e.push({key:"40",value:t.configs[40]}),CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")||(n=this.mustInstance.getValue(),n=this.toMustSubmitData(n),e.push({key:"must_transfer_by_type",value:n})),e.push({key:"leads_transfer_right_setting",value:CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?t.configs.leads_transfer_right_setting:t.$(".j-convertrights").hasClass("mn-selected")?"1":"0"}),t.openPrm&&e.push({key:"enable_duplicate_account_filter_partners",value:t.$(".j-convertrights-prm").hasClass("mn-selected")?"1":"0"}),e.push({key:"put_crm_feed_select_mode",value:this.comps.teamSale_select&&this.comps.teamSale_select.saleValue||"1"}),e.push({key:"put_teams_select_mode",value:this.comps.teamSale_select&&this.comps.teamSale_select.teamValue||"1"}),this.getActionChangeData());return{ConfigInfoList:e=e.concat(n)}},saveFilterGroup:function(){var t=this;return CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?new Promise(function(e,n){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_transfer_config/service/save",data:{transferConfig:t.filterGroupSetting.getValue()},success:function(t){0==t.Result.StatusCode?e(t.Value):n(t.Result.FailureMessage)}},{errorAlertModel:1})}):Promise.resolve()},saveAll:function(){return Promise.all([this.saveFilterGroup(),i.setConfigValues(this.getSubmitData())])},saveConfig:function(){this.checkValid()&&this.saveAll().then(function(){i.remind(1,$t("设置成功"))}).catch(function(t){i.remind(3,t||$t("设置失败"))})},checkValid:function(){var t=!0,e=CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?"customer_checkbox":"customer_switch",n=CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")?"oppor_checkbox":"appor_switch",e=this.comps[e].value,n=this.comps[n].value,s=this.comps.customer_select.value,a=this.comps.oppor_select.value;return e&&!s&&(i.remind($t("请选择线索阶段")),t=!1),n&&!a&&(i.remind($t("请选择线索阶段")),t=!1),t},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){for(var t in this.comps)this.comps[t].destroy&&this.comps[t].destroy();this.comps=null,CRM.util.isGrayScale("CRM_LEADS_CONVERT_RULE")&&this.filterGroupSetting.destroy()}});n.exports=t});