define("crm-setting/transactionstatement/components/edit-transaction",["./step-title","./mixins"],function(t,n,e){t={name:"EditTransaction",template:'\n\t\t\t<div\n\t\t\t\tclass="transaction-step-item"\n\t\t\t\t:class="stepItemClass"\n\t\t\t>\n\t\t\t\t<step-title\n\t\t\t\t\t:title="$t(\'crm.manage.transaction.edit\')"\n\t\t\t\t\t:show-tip="isDisable"\n\t\t\t\t\t:tip-content="$t(\'crm.manage.planobj.tip2\')"\n\t\t\t\t\t@on-click="onTitleClick"\n\t\t\t\t/>\n\t\t\t\t<div class="transaction-step-content">\n\t\t\t\t\t<div class="transaction-step-desc">\n\t\t\t\t\t\t{{ $t(\'crm.manage.planobj.init_desc2\') }}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="transaction-table">\n\t\t\t\t\t\t<fx-table\n\t\t\t\t\t\t\t:data="tableData"\n\t\t\t\t\t\t\tborder\n\t\t\t\t\t\t\theader-row-class-name="transaction-table-header"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\t\tprop="name"\n\t\t\t\t\t\t\t\t:label="$t(\'对象名称\')"\n\t\t\t\t\t\t\t\twidth="300">\n\t\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\t\t:label="$t(\'操作\')"\n\t\t\t\t\t\t\t\twidth="150">\n\t\t\t\t\t\t\t\t<template slot-scope="scope">\n\t\t\t\t\t\t\t\t\t<fx-link\n\t\t\t\t\t\t\t\t\t\tv-if="scope.$index === 0"\n\t\t\t\t\t\t\t\t\t\ttype="primary"\n\t\t\t\t\t\t\t\t\t\thref="/XV/UI/manage#crmmanage/=/module-sysobject/api_name-TransactionStatementObj"\n\t\t\t\t\t\t\t\t\t\ttarget="_blank"\n\t\t\t\t\t\t\t\t\t\t:underline="false"\n\t\t\t\t\t\t\t\t\t>{{ $t(\'去配置\') }}</fx-link>\n\t\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\t\tv-else\n\t\t\t\t\t\t\t\t\t\tclass="plan-setting-btn"\n\t\t\t\t\t\t\t\t\t\t@click="onSettingBtnClick(scope.$index)"\n\t\t\t\t\t\t\t\t\t>{{ $t(\'crm.manage.planobj.edit_field\') }}</span>\n\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t</fx-table>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t',components:{StepTitle:t("./step-title")},mixins:[t("./mixins")],props:{tableData:{type:Array,default:function(){return[]},required:!0},planObj:{type:Object,required:!0,default:function(){return{}}}},data:function(){return{isCollapse:2!==this.curStep}},methods:{onEditSuccess:function(t){this.$emit("refresh")},onSettingBtnClick:function(t){var n=this.planObj._id,t=this.tableData[t].api_name;CRM.api.edit({id:n,apiname:"ReconciliationPlanObj",destObjectApiName:t,isInitialized:!0,success:this.onEditSuccess.bind(this)})}}};e.exports=t});
define("crm-setting/transactionstatement/components/init-plan",["./step-title","./mixins","../global"],function(t,n,i){var e=CRM.util,a=t("./step-title"),s=t("./mixins"),t=t("../global"),c=t.API_NAME,o=t.STATUS_KEY,t={name:"InitPlan",template:'\n\t\t\t<div\n\t\t\t\tclass="transaction-step-item"\n\t\t\t\t:class="stepItemClass"\n\t\t\t>\n\t\t\t\t<step-title\n\t\t\t\t\t:title="$t(\'crm.manage.planobj.init_title\')"\n\t\t\t\t\t:show-tip="isDisable"\n\t\t\t\t\t:tip-content="$t(\'crm.manage.planobj.tip1\')"\n\t\t\t\t\t@on-click="onTitleClick"\n\t\t\t\t/>\n\t\t\t\t<div class="transaction-step-content">\n\t\t\t\t\t<div class="transaction-step-desc">\n\t\t\t\t\t\t{{ $t(\'crm.manage.planobj.init_desc1\') }}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="transaction-step-operations">\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\ttype="primary"\n\t\t\t\t\t\t\t:disabled="isInitializing"\n\t\t\t\t\t\t\t@click="openEditDesigner"\n\t\t\t\t\t\t>{{ $t(\'crm.manage.planobj.edit\') }}</fx-button>\n\t\t\t\t\t\t<span\n\t\t\t\t\t\t\tv-if="isInit"\n\t\t\t\t\t\t\tstyle="color: #b4b6c0; margin-left: 10px;"\n\t\t\t\t\t\t>{{ $t(\'已完成初始化\') }}</span>\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\tv-else\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t:loading="isInitializing"\n\t\t\t\t\t\t\t@click="onInitBtnClick"\n\t\t\t\t\t\t>{{ $t(\'初始化\') }}</fx-button>\n\t\t\t\t\t\t<span style="font-size:10px;margin-left:10px" v-if="isInit">\n\t\t\t\t\t\t\t<fx-link :underline="false" type="standard" @click="onReInitBtnClick">{{ $t(\'crm.transactionstatement.reinit\') }}</fx-link>\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t',components:{StepTitle:a},mixins:[s],props:{isInit:{type:Boolean,default:!1},planObj:{type:Object,require:!0},isInitializing:{type:Boolean,default:!1}},data:function(){return{isCollapse:1!==this.curStep}},methods:{onEditSuccess:function(){this.$emit("refresh")},openEditDesigner:function(){var t=this.planObj._id;CRM.api.edit({id:t,isInitialized:this.isInit,apiname:c,success:this.onEditSuccess.bind(this)})},initPlan:function(){var i=this,t=this.planObj._id;e.FHHApi({url:"/EM1HNCRM/API/v1/object/reconciliation/service/init",data:{reconciliation_plan_id:t},success:function(t){var n;0===t.Result.StatusCode?(i.$emit("init",+t.Value[o]),n="".concat($t("crm.transactionstatement.init.complate",null)),i.$confirm(n,$t("提示"),{confirmButtonText:$t("知道了"),type:"info",showCancelButton:!1})):e.error(t.Result.FailureMessage)}})},onReInitBtnClick:function(){var n=this;e.FHHApi({url:"/EM1HNCRM/API/v1/object/transaction_statement/service/has_data",data:{},success:function(t){0===t.Result.StatusCode&&t.Value.hasData?n.hasDataNotice():n.reinitPlan()}})},hasDataNotice:function(){var t="".concat($t("crm.transactionstatement.has.data",null));this.$confirm(t,$t("提示"),{confirmButtonText:$t("知道了"),type:"info",showCancelButton:!1,dangerouslyUseHTMLString:!0})},reinitPlan:function(){var n=this,t={reconciliation_plan_id:this.planObj._id};e.waiting(),e.FHHApi({url:"/EM1HNCRM/API/v1/object/reconciliation/service/re_init",data:t,success:function(t){0===t.Result.StatusCode&&(e.waiting(!1),n.$emit("init",+t.Value[o]),t="".concat($t("crm.transactionstatement.has.reset",null)),n.$confirm(t,$t("提示"),{confirmButtonText:$t("知道了"),type:"info",showCancelButton:!1}))}})},onInitBtnClick:function(){var t=this,n="".concat($t("crm.manage.planobj.init_tip",null,"初始化就是根据对账方家生成对象的对账单数据结构，包括对象、字段等。数据来源按从对象生成，确定现在要初始化吗？"));this.$confirm(n,$t("提示"),{confirmButtonText:$t("开始初始化"),cancelButtonText:$t("取消"),type:"info"}).then(function(){t.initPlan()})}}};i.exports=t});
define("crm-setting/transactionstatement/components/mixins",[],function(s,e,i){var t={props:{curStep:{type:Number,required:!0,default:0},isDisable:{type:Boolean,default:!0}},computed:{stepItemClass:function(){var s=[];return this.isCollapse&&s.push("is-collapse"),this.isDisable&&s.push("is-disable"),s.join(" ")}},methods:{onTitleClick:function(){this.isDisable||(this.isCollapse=!this.isCollapse)}}};i.exports=t});
define("crm-setting/transactionstatement/components/open-signature",["./step-title","./mixins"],function(t,n,e){var i=t("./step-title"),t=t("./mixins");e.exports={name:"OpenSignature",template:'\n\t\t\t<div\n\t\t\t\tclass="transaction-step-item"\n\t\t\t\t:class="stepItemClass"\n\t\t\t>\n\t\t\t\t<step-title\n\t\t\t\t\ttitle="集成电子签章(可选)"\n\t\t\t\t\t@on-click="onTitleClick"\n\t\t\t\t/>\n\t\t\t\t<div class="transaction-step-content">\n\t\t\t\t\t<div class="transaction-step-desc">\n\t\t\t\t\t\t当企业购买电子签章后，可启用电子签章插件，下游经销商就可以在线签署，直接具有法律效力。\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="transaction-step-operations">\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t@click="onOpenBtnClick"\n\t\t\t\t\t\t>启用插件</fx-button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t',components:{StepTitle:i},mixins:[t],data:function(){return{isCollapse:2!==this.curStep}},methods:{onOpenBtnClick:function(){window.open("/XV/UI/manage#crmmanage/=/module-electronicsign")}}}});
define("crm-setting/transactionstatement/components/open-transaction",["./step-title","./mixins","../global"],function(t,n,i){var e=CRM.util,s=t("./step-title"),a=t("./mixins"),o=t("../global").STATUS_KEY,t={name:"OpenTransation",template:'\n\t\t\t<div\n\t\t\t\tclass="transaction-step-item"\n\t\t\t>\n\t\t\t    <div class="transaction-step-title">\n\t\t\t\t\t{{$t(\'crm.manage.transaction.open\')}}\n\t\t\t\t</div>\n\t\t\t\t<div class="transaction-step-content">\n\t\t\t\t\t<div class="transaction-step-operations">\n\t\t\t\t\t\t<fx-switch\n\t\t\t\t\t\t\tv-model="isInit"\n\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t:disabled="isInit"\n\t\t\t\t\t\t\t:before-change="beforeChanage">\n\t\t\t\t\t\t</fx-switch>\n\t\t\t\t\t\t<span class="switch-label">{{ switchText }}</span>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t',components:{StepTitle:s},mixins:[a],props:{isInit:{type:Boolean,default:!1}},data:function(){return{isCollapse:0!==this.curStep}},computed:{switchText:function(){return this.isInit?$t("已启用"):$t("未启用")}},methods:{beforeChanage:function(){var t=this;return this.$confirm($t("crm.manage.transaction.open_tip"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){t.initTransaction()}),!1},initTransaction:function(){var i=this;e.FHHApi({url:"/EM1HNCRM/API/v1/object/reconciliation/service/enable",success:function(t){var n;0===t.Result.StatusCode?(n=t.Value[o],i.$emit("init",+n)):e.error(t.Result.FailureMessage)}})}}};i.exports=t});
define("crm-setting/transactionstatement/components/set-flow",["./step-title","./mixins"],function(t,n,i){var e=t("./step-title"),t=t("./mixins");i.exports={name:"SetFlow",template:'\n\t\t\t<div\n\t\t\t\tclass="transaction-step-item"\n\t\t\t\t:class="stepItemClass"\n\t\t\t>\n\t\t\t\t<step-title\n\t\t\t\t\t:title="$t(\'crm.manage.transaction.flow_tpl\')"\n\t\t\t\t\t:show-tip="isDisable"\n\t\t\t\t\t:tip-content="$t(\'crm.manage.transaction.flow_tip\')"\n\t\t\t\t\t@on-click="onTitleClick"\n\t\t\t\t/>\n\t\t\t\t<div class="transaction-step-content">\n\t\t\t\t\t<div class="transaction-step-desc">\n\t\t\t\t\t\t{{ $t(\'crm.manage.transaction.flow_desc\') }}\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="transaction-step-operations">\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t@click="onPrintBtnClick"\n\t\t\t\t\t\t>{{ $t(\'crm.manage.transaction.tpl_setting\') }}</fx-button>\n\t\t\t\t\t\t<fx-button\n\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t@click="onFlowBtnClick"\n\t\t\t\t\t\t>{{ $t(\'配置流程\') }}</fx-button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t',components:{StepTitle:e},mixins:[t],data:function(){return{isCollapse:2!==this.curStep}},methods:{onFlowBtnClick:function(){window.open("/XV/UI/manage#crmmanage/=/module-businessflow")},onPrintBtnClick:function(){window.open("/XV/UI/manage#crmmanage/=/module-templatemanage")}}}});
define("crm-setting/transactionstatement/components/step-title",[],function(t,n,e){var i={name:"StepTitle",template:'\n\t\t\t<div\n\t\t\t\tclass="transaction-step-title"\n\t\t\t\t@click="onClick"\n\t\t\t>\n\t\t\t\t{{ title }}\n\t\t\t\t<fx-tooltip\n\t\t\t\t\tv-if="showTip"\n\t\t\t\t\teffect="dark"\n\t\t\t\t\t:content="tipContent"\n\t\t\t\t\tplacement="right"\n\t\t\t\t>\n\t\t\t\t\t<i class="el-icon-lock"></i>\n\t\t\t\t</fx-tooltip>\n\t\t\t</div>\n\t\t',props:{showTip:{type:Boolean,default:!1},tipContent:{type:String,default:""},title:{type:String,default:"",required:!0}},methods:{onClick:function(){this.$emit("on-click")}}};e.exports=i});
define("crm-setting/transactionstatement/global",[],function(t,e,i){var o=CRM.util,c="reconciliation_config_status";i.exports={STATUS_KEY:c,STATUS:{UN_OPEN:0,OPENNING:1,OPENED:2,INITIALIZING:3,INITIALIZED:4},API_NAME:"ReconciliationPlanObj",getReconciliationStatus:function(){return new Promise(function(i,n){o.FHHApi({url:"/EM1HNCRM/API/v1/object/reconciliation/service/get_config",success:function(t){var e;0===t.Result.StatusCode?(e=t.Value[c],i(+e)):(o.error(t.Result.FailureMessage),n(t))}})})}}});
define("crm-setting/transactionstatement/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("crm.manage.transaction")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con transaction-content crm-scroll"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + '：</h3> <ul class="transaction-tips"> <li>' + ((__t = $t("crm.manage.transaction.desc1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("crm.manage.transaction.desc2")) == null ? "" : __t) + '</li> </ul> </div> <div ref="j-dht-crmtable" class="transaction-list-wrapper"></div> </div>';
        }
        return __p;
    };
});
function asyncGeneratorStep(t,e,n,r,i,a,s){try{var o=t[a](s),c=o.value}catch(t){return void n(t)}o.done?e(c):Promise.resolve(c).then(r,i)}function _asyncToGenerator(o){return function(){var t=this,s=arguments;return new Promise(function(e,n){var r=o.apply(t,s);function i(t){asyncGeneratorStep(r,e,n,i,a,"next",t)}function a(t){asyncGeneratorStep(r,e,n,i,a,"throw",t)}i(void 0)})}}define("crm-setting/transactionstatement/transactionstatement",["./template/tpl-html","./components/open-transaction","./components/init-plan","./components/edit-transaction","./components/set-flow","./components/open-signature","./components/step-title","./global"],function(t,e,n){var r=t("./template/tpl-html"),i=t("./components/open-transaction"),a=t("./components/init-plan"),s=t("./components/edit-transaction"),o=t("./components/set-flow"),c=t("./components/open-signature"),u=t("./components/step-title"),t=t("./global"),p=t.STATUS,l=t.getReconciliationStatus,m=CRM.util,t=Backbone.View.extend({initialize:function(t){this.status=0,this.curStep=0,this.setElement(t.wrapper)},render:function(){var t=r();this.$el.html(t),this.$stepWrapper=this.$(".transaction-list-wrapper"),this.renderComponent()},renderStep:function(){var t=this.status,e=this.curStep,r=this.$stepWrapper;FxUI.create({name:"TransactionSteps",wrapper:this.$stepWrapper[0],components:{OpenTransation:i,InitPlan:a,EditTransaction:s,SetFlow:o,OpenSignature:c,StepTitle:u},template:'\n\t\t\t\t\t<open-transation\n\t\t\t\t\t\t:is-disable="false"\n\t\t\t\t\t\t:is-init="isInitTransaction"\n\t\t\t\t\t\t:cur-step="curStep"\n\t\t\t\t\t\t@init="initTransactionHandle"\n\t\t\t\t\t/>\n\t\t\t\t',data:function(){return{curStep:e,status:t,planObj:Object.freeze({_id:"",name:""}),tableData:[],initPlanTimer:null}},computed:{isInitPlan:function(){return this.status===p.INITIALIZED},isInitTransaction:function(){return this.status>=p.OPENED},isInitializingPlan:function(){return this.status===p.INITIALIZING},isPlanDisable:function(){return!this.isInitTransaction||!this.planObj._id}},created:function(){this.isInitTransaction&&this.getPlan(),this.isInitializingPlan&&this.checkStatus()},beforeDestroy:function(){this.clearInitPlanTimer()},methods:{clearInitPlanTimer:function(){this.initPlanTimer&&(clearTimeout(this.initPlanTimer),this.initPlanTimer=null)},initTransactionHandle:function(t){this.status=t,this.isInitTransaction&&this.renderList()},getCrmList:function(){return new Promise(function(e,t){seajs.use("crm-modules/page/list20/list20",function(t){e(t)})})},renderList:function(){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,n.getCrmList();case 2:e=t.sent,new e({wrapper:r,jsPath:"crm-modules/page/list/list",listOptions:{apiname:"ReconciliationPlanObj"}}).render(["ReconciliationPlanObj"]);case 6:case"end":return t.stop()}},t)}))()},initPlanHandle:function(e){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n.status=e,n.checkStatus();case 2:case"end":return t.stop()}},t)}))()},checkStatus:function(){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:n.clearInitPlanTimer(),n.isInitializingPlan&&(n.initPlanTimer=setTimeout(_asyncToGenerator(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,l();case 2:e=t.sent,n.status=e,n.checkStatus();case 5:case"end":return t.stop()}},t)})),1e3));case 2:case"end":return t.stop()}},t)}))()},getPlan:function(){var i=this;m.FHHApi({url:"/EM1HNCRM/API/v1/object/ReconciliationPlanObj/controller/List",data:{search_query_info:'{"limit":1,"offset":0,"filters":[],"orders":[{"fieldName":"last_modified_time","isAsc":false}]}'},success:function(t){var e,n,r;0===t.Result.StatusCode?(e={_id:(n=t.Value.dataList[0])._id,name:n.name,api_name:n.object_describe_api_name},n=(void 0===(n=n.reconciliation_data_source)?[]:n).map(function(t){return{name:t.dest_object_display_name,api_name:t.dest_object_api_name}}),r={name:$t("交易对账单"),api_name:"TransactionStatementObj"},n.unshift(r),i.planObj=Object.freeze(e),i.tableData=Object.freeze(n)):m.error(t.Result.FailureMessage)}})}}})},getCrmList:function(){return new Promise(function(e,t){seajs.use("crm-modules/page/list20/list20",function(t){e(t)})})},renderList:function(){var r=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){var e,n;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r.getCrmList();case 2:e=t.sent,n={wrapper:r.$(".transaction-list-wrapper"),jsPath:"crm-modules/page/list/list",listOptions:{apiname:"ReconciliationPlanObj"}},new e(n).render(["ReconciliationPlanObj"]);case 6:case"end":return t.stop()}},t)}))()},renderComponent:function(){var e=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,e.initStatus();case 3:t.next=8;break;case 5:t.prev=5,t.t0=t.catch(0);case 8:e.status>=p.OPENED?e.renderList():e.renderStep();case 9:case"end":return t.stop()}},t,null,[[0,5]])}))()},initStatus:function(){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function t(){var e;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,l();case 3:e=t.sent,n.status=e,t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0);case 10:case"end":return t.stop()}},t,null,[[0,7]])}))()}});n.exports=t});