define("crm-setting/sysemail/bind-dialog/bind-dialog",["crm-modules/common/util"],function(n,e,s){var t=n("crm-modules/common/util"),a=Vue.extend({name:"account-editor",template:'\n    <fx-dialog\n      :visible.sync="showPanel"\n      :title="$t(\'验证邮箱帐号\')"\n      custom-class="account-editor"\n      width="480px"\n      @close="handleDialogClose"\n      :close-on-click-modal="false"\n      has-scroll\n      append-to-body\n    >\n      <div class="panel-body" v-loading="isLoading">\n        <ul class="setting-list">\n\n          <li class="setting-item l__form-line__item">\n            <div class="item__label">\n              <div class="label required">{{$t(\'邮箱账号\')}}</div>\n            </div>\n            <div class="item__comp">\n              <fx-input\n                size="small"\n                :placeholder="$t(\'请输入\')"\n                v-model="account"\n                @change="error.mailAccountErr = \'\'"\n                disabled\n                autocomplete="off"\n              ></fx-input>\n              <p class="error-msg" v-show="error.mailAccountErr">\n                <i class="el-icon-warning"></i>\n                {{ error.mailAccountErr }}\n              </p>\n            </div>\n          </li>\n\n          <li class="setting-item l__form-line__item">\n            <div class="item__label">\n              <div class="label required">{{$t(\'邮箱密码\')}}</div>\n            </div>\n            <div class="item__comp">\n              <fx-input\n                size="small"\n                show-password\n                :placeholder="$t(\'请输入\')"\n                v-model="password"\n                @change="error.password = \'\'"\n                autocomplete="new-password"\n                @input.native="handlePswInput"\n              ></fx-input>\n              <p class="error-msg" v-show="error.password">\n                <i class="el-icon-warning"></i>\n                {{ error.password }}\n              </p>\n            </div>\n          </li>\n\n          <li class="setting-item l__form-line__item" v-show="useUsername">\n            <div class="item__label">\n              <div class="label required">{{$t(\'用户名称\')}}</div>\n            </div>\n            <div class="item__comp">\n              <fx-input\n                size="small"\n                :placeholder="$t(\'请输入\')"\n                v-model="username"\n                @change="error.username = \'\'"\n              ></fx-input>\n              <p class="error-msg" v-show="error.username">\n                <i class="el-icon-warning"></i>\n                {{ error.username }}\n              </p>\n            </div>\n          </li>\n        </ul>\n      </div>\n      <span slot="footer" class="dialog-footer">\n  \t\t\t<fx-checkbox style="flex: 1" v-model="useUsername">{{$t(\'使用用户名验证\')}}</fx-checkbox>\n        <fx-button type="primary" @click="handleSubmit" size="small">\n          {{ $t(\'保存\') }}\n        </fx-button>\n        <fx-button @click="hidePanel" size="small">{{ $t(\'取消\') }}</fx-button>\n      </span>\n    </fx-dialog>\n  ',props:{account:{type:String,default:""},pUsername:{type:String,default:""}},data:function(){return{showPanel:!0,isLoading:!1,password:"",username:"",useUsername:!1,error:{password:"",username:""}}},methods:{handleDialogClose:function(){this.$emit("cancel")},allowSubmit:function(){var n=!0;return this.password||(this.error.password=$t("请输入邮箱密码"),n=!1),this.useUsername&&!this.username&&(this.error.username=$t("请输入用户名称"),n=!1),n},submitEditingData:function(){var i=this;return!this.submittingData&&this.allowSubmit()?(this.isLoading=!0,this.submittingData=!0,new Promise(function(e,s){t.api({url:"/FHH/EM1HEMAILPROXY/emailproxy/changePassword",data:{account:i.account,password:i.password,userName:i.useUsername?i.username:"",isSystemEmail:!0},success:function(n){n.Value&&Object.assign(n,n.Value),i.isLoading=!1,i.submittingData=!1,(n&&0===n.errorCode?e:(FS.util.alert(n.errorMessage),s))()}},{errorAlertModel:1,autoPrependPath:!1})})):Promise.reject()},handleSubmit:function(){var n=this;this.submitEditingData().then(function(){n.$emit("submit"),n.hidePanel()})},hidePanel:function(){this.$emit("cancel")},handlePswInput:_.debounce(function(){var n=this.password,e=/[\u3a00-\ufa99]/g;e.test(n)&&this.$set(this,"password",n.replace(e,""))},200),loadData:function(){this.username=this.pUsername,this.useUsername=!!this.pUsername}},created:function(){this.loadData()}});a.$show=function(i){return new Promise(function(n,e){var s=new a({el:document.createElement("div"),propsData:i});s.$on("hide",function(){s.showPanel=!1,setTimeout(function(){s.$destroy(),s.$el.remove()},1e3)}),s.$on("submit",function(){s.$emit("hide"),n.apply(void 0,arguments)}),s.$on("cancel",function(){s.$emit("hide"),e.apply(void 0,arguments)}),$("body").append(s.$el),setTimeout(function(){s.showPanel=!0},20),$("body").append(s.$el)})},s.exports=a});
function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var s;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(s="Object"===(s={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:s)||"Set"===s?Array.from(e):"Arguments"===s||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var s=0,a=Array(t);s<t;s++)a[s]=e[s];return a}function _iterableToArrayLimit(e,t){var s=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=s){var a,i,n,o,r=[],l=!0,u=!1;try{if(n=(s=s.call(e)).next,0===t){if(Object(s)!==s)return;l=!1}else for(;!(l=(a=n.call(s)).done)&&(r.push(a.value),r.length!==t);l=!0);}catch(e){u=!0,i=e}finally{try{if(!l&&null!=s.return&&(o=s.return(),Object(o)!==o))return}finally{if(u)throw i}}return r}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/sysemail/sysemail",["crm-modules/common/util","./template/tpl-html","./bind-dialog/bind-dialog"],function(e,t,s){var c=e("crm-modules/common/util"),a=e("./template/tpl-html"),i=e("./bind-dialog/bind-dialog"),n={0:$t("不可查看下级邮件"),1:$t("所有下级邮件"),2:$t("直属下级邮件")},o=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},initOptionData:function(){this.options.emailConfig={isForceSSL:!0}},render:function(){this.initDefaultData(),this.initSystemMail()},initDefaultData:function(){this.comps={}},initSystemMail:function(t){var s=this;t=t||this.el,e.async("app-standalone/app",function(e){e.getModuleComp("system-mail/system-mail-vue.js").then(function(e){e=new e({el:document.createElement("div")});s.comps.systemMailInstance=e,t.append(e.$el)})})},uploadHtml:function(e){this.$el.html(a(_.extend({account:"",nickname:"",signature:"",passwordErr:!1},this.options,e))),$(".crm-see-sub-email .setting-result__value").html(n[this.options.subordinatesStatus])},uploadData:function(){var s=this;return Promise.all([s._getEmailSet(),s.getViewStatus(),s.loadEmailConfig()]).then(function(e){var e=_slicedToArray(e,1)[0],t=[5,2].includes(+e.status);s.options.passwordErr=2==e.status,s.options.username=e.userName,s.uploadHtml(),$(".flowemail-show-box",s.$el).toggle(t),$(".flowemail-set-box",s.$el).toggle(!t),$('input:radio[name="crm-email-see"]').change(function(){s.changeValue=this.value,s.$el.find(".crm-see-email-save").removeClass("b-g-btn-disabled")})})},events:{"click .j-set":"onManualSet","click .j-unbind":"onUnbindEmail","click .j-account-setting":"onAccountSetting","click .j-setItem":"onItemSet","click .j-cancelItem":"onItemCancel","click .j-sureItem":"onItemSure","click .j-validate":"onSubmit","click .j-crm-set-email":"onSetEmailFun","click .j-use-username":"onUseUsernameChange","click .sedi-mes__lt":"onClickSediChoice","click .crm-see-email-save":"onSaveFun","click .change-official__link-btn":"showOfficialSetting","click .crm-see-sub-email__submit-setting":"submitOfficialSetting","click .crm-see-sub-email__cancel-setting":"exitOfficialSetting",'blur .flowemail-set-box input[name="account"]':"checkAccount",'blur .flowemail-set-box input[name="password"]':"checkPassword",'blur .flowemail-set-box input[name="username"]':"checkUsername",'blur .flowemail-set-box input[name="host"]':"checkManualSet",'blur .flowemail-set-box input[name="port"]':"checkManualSet"},onUseUsernameChange:function(){this.options.useUsername?($(".j-use-username .checkbox-input").attr("checked",!1),$(".set-item.username").addClass("hide")):($(".j-use-username .checkbox-input").attr("checked",!0),$(".set-item.username").removeClass("hide")),this.options.useUsername=!this.options.useUsername,this.checkUsername()},showOfficialSetting:function(){this.renderRadioGroup(),$(".crm-see-sub-email").hasClass("setting-official")||$(".crm-see-sub-email").addClass("setting-official")},submitOfficialSetting:function(){var e=this;this.setViewStatus(this.comps["customer_radio-group"].subordinatesStatus).then(function(){e.options.subordinatesStatus=e.comps["customer_radio-group"].subordinatesStatus,$(".crm-see-sub-email .setting-result__value").html(n[e.options.subordinatesStatus]),e.exitOfficialSetting()})},exitOfficialSetting:function(){$(".crm-see-sub-email").removeClass("setting-official")},renderRadioGroup:function(){var e=this;this.comps||(this.comps={}),this.comps["customer_radio-group"]&&this.comps["customer_radio-group"].destroy(),this.comps["customer_radio-group"]=FxUI.create({wrapper:".fx-radio-group",template:'\n\t\t\t\t\t<fx-radio-group \n\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\tv-model="subordinatesStatus"\n\t\t\t\t\t>\n\t\t\t\t\t\t<fx-radio :label="0">{{$t("不可查看下级邮件")}}</fx-radio>\n\t\t\t\t\t\t<fx-radio :label="1">{{$t("所有下级邮件")}}</fx-radio>\n\t\t\t\t\t\t<fx-radio :label="2">{{$t("直属下级邮件")}}</fx-radio>\n\t\t\t\t\t</fx-radio-group>\n\t\t\t\t',data:function(){return{subordinatesStatus:e.options.subordinatesStatus}}})},renderSwitch:function(){var t=this;this.comps||(this.comps={}),this.comps.customer_switch=FxUI.create({wrapper:".fx-switch",template:'\n\t\t\t\t\t<fx-switch \n\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t:before-change="beforeChange"\n\t\t\t\t\t\t:disabled="options.isOpenedAutoAssociation"\n\t\t\t\t\t\tv-model="options.isOpenedAutoAssociation"\n\t\t\t\t\t></fx-switch>\n\t\t\t\t',data:function(){return{options:t.options}},methods:{beforeChange:function(){var e='\n\t\t\t\t\t\t\t<p class="sys-email__auto-association__open-tips__header">'.concat($t("确定要开启吗？"),'<p>\n\t\t\t\t\t\t\t<p class="sys-email__auto-association__open-tips__desc">').concat($t("开启后将创建邮件对象，且此功能不能关闭"),"</p>\n\t\t\t\t\t\t");return this.$alert(e,$t("提示"),{type:"warning",showClose:!1,showCancelButton:!0,dangerouslyUseHTMLString:!0}).then(t.autoSedimentFun.bind(t)).then(t.queryEmailSedimentFun.bind(t))}},beforeCreate:function(){t.options.isOpenedAutoAssociation||(t.options.isOpenedAutoAssociation=!1)}})},onManualSet:function(e){$(e.currentTarget).closest(".flowemail-set-box").toggleClass("all-set")},getViewStatus:function(){var i=this;return new Promise(function(s,a){c.FHHApi({url:"/EM1HCRMTemplate/emailAttributesStatusApi/getViewSubordinatesStatus",success:function(e){var t;0==e.Result.StatusCode?0==e.Value.code&&(t=+(e.Value.result||0),i.options.subordinatesStatus=t,s()):(a(),c.alert(e.Result.FailureMessage))}},{errorAlertModel:1})})},loadEmailConfig:function(){var s=this;return new Promise(function(t,e){c.api({url:"/FHH/EM1HEMAILPROXY/emailproxy/isBindEmailForceBySsl",success:function(e){e.Value&&Object.assign(e,e.Value),s.options.emailConfig.isForceSSL=!!e.data,t()}},{errorAlertModel:1,autoPrependPath:!1})})},setViewStatus:function(e){var a=this;return new Promise(function(t,s){c.FHHApi({url:"/EM1HCRMTemplate/emailAttributesStatusApi/setViewSubordinatesStatus",data:{openStatus:Number(e)},success:function(e){0==e.Result.StatusCode?0===e.Value.code&&(t(),c.remind(1,e.Value.msg),a.$el.find(".crm-see-email-save").addClass("b-g-btn-disabled")):(s(),c.alert(e.Result.FailureMessage))}},{errorAlertModel:1})})},onClickSediChoice:function(e){var t,s=this;$(e.target).hasClass("disabled-selected")||(t=c.confirm($t("开启后将无法关闭确定要开启吗"),$t("提示"),function(){t.hide(),$(e.target).addClass("disabled-selected"),$(".sedi-mes__P1",s.$el).addClass("crm-choiced-iden"),s.$el.find(".crm-see-email-save").removeClass("b-g-btn-disabled"),s.ifRequestEmailSediment=!0}))},onSaveFun:function(){var e=this;e.changeValue&&e.setViewStatus(e.changeValue),e.ifRequestEmailSediment&&e.autoSedimentFun()},onAccountSetting:function(){var e=this;i.$show({account:this.options.account,pUsername:this.options.username}).then(function(){e.render()})},autoSedimentFun:function(){var a=this;return new Promise(function(t,s){c.FHHApi({url:"/EM1HCRMTemplate/emailAttributesStatusApi/setAutoAssociationStatus",success:function(e){0==e.Result.StatusCode?0===e.Value.code?(t(),c.remind(1,$t("设置成功！")),a.ifRequestEmailSediment=!1,a.$el.find(".crm-see-email-save").addClass("b-g-btn-disabled")):(s(),c.remind(3,$t("设置失败!")),$(".sedi-mes__lt",a.$el).removeClass("disabled-selected"),$(".sedi-mes__P1",a.$el).removeClass("crm-choiced-iden")):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},onSetEmailFun:function(e){var t=this;$(e.target).width();$(".crm-set-mail-email",t.$el).addClass("hide"),$(".j-crm-set-email",t.$el).removeClass("cur"),$(e.target).addClass("cur"),$(e.target).hasClass("crm-set-sedi__email")?($(".crm-email-sediment",t.$el).removeClass("hide"),t.queryEmailSedimentFun()):($(".crm-p20",t.$el).removeClass("hide"),this.exitOfficialSetting())},queryEmailSedimentFun:function(){var t=this;c.FHHApi({url:"/EM1HCRMTemplate/emailAttributesStatusApi/getAutoAssociationStatus",success:function(e){0==e.Result.StatusCode?e.Value.result&&(t.options.isOpenedAutoAssociation=!!e.Value.result,$(".sedi-mes__lt",t.$el).addClass("disabled-selected"),$(".sedi-mes__P1",t.$el).addClass("crm-choiced-iden")):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},onUnbindEmail:function(){var t=this;c.FHHApi({url:"/EM1HCRMTemplate/workflowEmailSet/unBind",success:function(e){0==e.Result.StatusCode?e.Value?(c.remind(1,$t("解绑成功！")),t.render()):c.remind(3,$t("解绑失败")+"！"):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},onItemSet:function(e){var e=$(e.currentTarget).closest(".set-item"),t=e.find(".text").html(),s=e.find(".text").html();e.addClass("set-item-edit"),e.find("input").val(t==$t("无")?"":t),e.find("textarea").val(s==$t("无")?"":s)},onItemSure:function(e){var t=$(e.currentTarget).closest(".set-item"),e=t.data("type"),s=null,a=null;"nickname"==e?s=t.find("input").val():"signature"==e&&(a=t.find("textarea").val()),c.FHHApi({url:"/EM1HCRMTemplate/workflowEmailSet/updateAccountInfo",data:{nickname:s,signature:a},success:function(e){0==e.Result.StatusCode?1==e.Value&&(t.removeClass("set-item-edit"),t.find(".text").html(s||a||$t("无"))):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},onItemCancel:function(e){$(e.currentTarget).closest(".set-item").removeClass("set-item-edit")},checkAccount:function(){var e=$(".flowemail-set-box",this.$el),e=$(".account",e);$("[name=account]",e).val()?($(".error-msg",e).hide(),e.removeClass("has-error")):($(".error-msg",e).show(),$(".error-msg span",e).text($t("请输入邮箱账号")),e.addClass("has-error"))},checkPassword:function(){var e=$(".flowemail-set-box",this.$el),e=$(".password",e);$("[name=password]",e).val()?($(".error-msg",e).hide(),e.removeClass("has-error")):($(".error-msg",e).show(),$(".error-msg span",e).text($t("请输入邮箱密码")),e.addClass("has-error"))},checkUsername:function(){var e=$(".flowemail-set-box",this.$el),e=$(".username",e);this.options.useUsername&&!$("[name=username]",e).val()?($(".error-msg",e).show(),$(".error-msg span",e).text($t("请输入用户名称")),e.addClass("has-error")):($(".error-msg",e).hide(),e.removeClass("has-error"))},checkManualSet:function(){var e=$(".flowemail-set-box",this.$el),t=$(".manual-set",e),s=$(".flowemail-set-box",this.$el).hasClass("all-set"),a=$("[name=host]",e).val(),e=$("[name=port]",e).val();!s||a&&e?($(".error-msg",t).hide(),t.removeClass("has-error")):($(".error-msg",t).show(),$(".error-msg span",t).text($t("请输入完整SMTP服务器信息")),t.addClass("has-error"))},checkAllowSubmit:function(){this.checkAccount(),this.checkPassword(),this.checkUsername(),this.checkManualSet();var e=$(".flowemail-set-box .has-error");return!e||0===e.length},onSubmit:function(){var t,e,s,a,i,n,o,r,l,u;this.checkAllowSubmit()&&(t=this,e="",s={},l=$(".flowemail-set-box",this.$el),a=$("[name=account]",l).val(),i=$("[name=password]",l).val(),n=$(".is-ssl",l).hasClass("mn-selected"),o=this.options.useUsername?$("[name=username]",l).val():"",r=$("[name=host]",l).val(),l=$("[name=port]",l).val(),(u=$(".flowemail-set-box",this.$el).hasClass("all-set"))?(e="/EM1HCRMTemplate/workflowEmailSet/emailManualBind",s.account=a,s.password=i,s.userName=o,s.isSendSsl=n?1:0,s.smtpHost=r,s.smtpPort=+l):(e="/EM1HCRMTemplate/workflowEmailSet/emailBind",s.account=a,s.password=i,s.userName=o),c.FHHApi({url:e,data:s,success:function(e){if(0==e.Result.StatusCode)return 2==e.Value?void $(".j-set").click():void t.render();c.alert(e.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:u?$(".j-set",this.$el):$(".j-validate",this.$el)}))},_getEmailSet:function(a){var i=this;return new Promise(function(t,s){c.FHHApi({url:"/EM1HCRMTemplate/workflowEmailSet/getAccountInfo",success:function(e){0==e.Result.StatusCode&&(t(_.extend(i.options,e.Value,e.Value.data||{})),a)&&a(_.extend(e.Value,e.Value.data||{})),s()}})})},destroy:function(){for(var e in this.comps){e=this.comps[e];e.$destroy&&e.$destroy(),e.destroy&&e.destroy()}this.comps=null}});s.exports=o});
define("crm-setting/sysemail/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("邮件管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-tab"> <span class="crm-set-system__email j-crm-set-email cur" >' + ((__t = $t("系统邮箱设置")) == null ? "" : __t) + '</span > <span class="crm-set-sedi__email j-crm-set-email" >' + ((__t = $t("邮件沉淀设置")) == null ? "" : __t) + '</span > <!-- <label class="crm-set-active__email"></label> --> </div> <div class="crm-module-con crm-scroll"> <!-- <div class="crm-tab"> <span data-render="databoard-box" class="cur">流程邮箱设置</span> </div> --> <div class="crm-email-sediment crm-set-mail-email hide"> <div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <p>1." + ((__t = $t("支持邮件沉淀的CRM对象销售线索客户联系人商机2.0。")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("crm.默认沉淀规则")) == null ? "" : __t) + '</p> <p class="hide"> 3.' + ((__t = $t("上级可见下级邮件数据范围默认为当前员工仅可见自己与客户的往来邮件。")) == null ? "" : __t) + ' </p> </div> <div class="crm-obj-sedi"> <div class="crm-obj-sedi__title"> ' + ((__t = $t("CRM对象邮件沉淀")) == null ? "" : __t) + ' <div class="crm-obj-sedi__title__switch fx-switch"></div> </div> <div class="crm-sedi-mes clearfixed"> <p> ' + ((__t = $t("开启后，在对象（销售线索，客户，联系人，商机2.0）下将沉淀纷享邮箱中员工与客户的往来邮件。")) == null ? "" : __t) + ' </p> <!-- <span class="sedi-mes__lt"></span> <div class="sedi-mes__rg"> <p class="sedi-mes__P1">' + ((__t = $t("自动沉淀所有往来邮件")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("勾选后在对象（销售线索客户联系人）下将沉淀纷享邮箱中所有员工与客户的往来邮件。")) == null ? "" : __t) + '</p> </div> --> </div> </div> <!-- <div class="crm-see-sub-email"> <div class="crm-see-sub-email-content"> <div class="crm-see-sub__title">' + ((__t = $t("上级可见下级邮件数据范围")) == null ? "" : __t) + '</div> <div class="crm-see-change-official"> <p class="change-official__desc">' + ((__t = $t("用于设置销售线索客户联系人对象下的邮件沉淀数据权限。当设置上级可查看下级邮件时系统将自动发送CRM通知给相应员工。")) == null ? "" : __t) + '</p> <p class="change-official__link-btn">' + ((__t = $t("设置")) == null ? "" : __t) + '</p> </div> <div class="crm-see-sub__setting-result"> <span class="setting-result__label">' + ((__t = $t("上级可见的邮件数据范围：")) == null ? "" : __t) + '</span> <span class="setting-result__value"></span> <span class="setting-result__comp fx-radio-group"></span> </div> <div class="setting-btn-area"> <span class="crm-btn crm-btn-primary crm-see-sub-email__submit-setting">' + ((__t = $t("确定")) == null ? "" : __t) + '</span> <span class="crm-btn manual-set-btn crm-see-sub-email__cancel-setting">' + ((__t = $t("取消")) == null ? "" : __t) + '</span> </div> </div> </div> --> </div> <div class="crm-p20 crm-set-mail-email"> <div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <p> " + ((__t = $t("系统邮箱是用于流程中（工作流审批流业务流程）后动作发送邮件")) == null ? "" : __t) + ' </p> </div> <div class="flowemail-box"> <div class="flowemail-show-box"> <h3>' + ((__t = $t("验证邮箱帐号")) == null ? "" : __t) + '</h3> <div class="set-item"> <label>' + ((__t = $t("邮箱账号")) == null ? "" : __t) + '</label> <div class="text">' + ((__t = account) == null ? "" : __t) + '</div> <input type="text" name="account" style="display: none" /> <a href="javascript:;" class="j-account-setting" ';
            if (!passwordErr) {
                __p += ' style="display: none" ';
            }
            __p += "> " + ((__t = $t("设置")) == null ? "" : __t) + ' </a> <p class="error-msg" ';
            if (!passwordErr) {
                __p += ' style="margin-top: -4px; display: none;" ';
            } else {
                __p += ' style="margin-top: -4px;" ';
            }
            __p += ' > <i class="el-icon-warning"></i> ' + ((__t = $t("密码或用户名称错误，邮箱无法正常发送邮件，请更新设置")) == null ? "" : __t) + ' </p> </div> <div class="set-item" data-type="nickname"> <label>' + ((__t = $t("发信昵称")) == null ? "" : __t) + '</label> <div class="text">' + __e(nickname || $t("无")) + '</div> <a href="javascript:;" class="j-setItem">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> <div class="edit-item"> <input type="text" name="" /> <div class="set-item-btns"> <span class="crm-btn crm-btn-primary j-sureItem" >' + ((__t = $t("确认")) == null ? "" : __t) + '</span > <span class="crm-btn j-cancelItem" >' + ((__t = $t("取 消")) == null ? "" : __t) + '</span > </div> </div> </div> <div class="set-item" data-type="signature"> <label>' + ((__t = $t("个性签名")) == null ? "" : __t) + '</label> <div class="text">' + ((__t = signature || $t("无")) == null ? "" : __t) + '</div> <a href="javascript:;" class="j-setItem">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> <div class="edit-item"> <textarea></textarea> <div class="set-item-btns"> <span class="crm-btn crm-btn-primary j-sureItem" >' + ((__t = $t("确认")) == null ? "" : __t) + '</span > <span class="crm-btn j-cancelItem" >' + ((__t = $t("取 消")) == null ? "" : __t) + '</span > </div> </div> </div> <div class="msg-btn-area"> <span class="crm-btn j-unbind unbind-btn"> ' + ((__t = $t("解除绑定")) == null ? "" : __t) + ' </span> </div> </div> <div class="flowemail-set-box"> <div class="left"> <h3>' + ((__t = $t("验证邮箱帐号")) == null ? "" : __t) + '</h3> <div class="base-set"> <div class="set-item account"> <label>' + ((__t = $t("邮箱账号")) == null ? "" : __t) + '</label> <input type="text" placeholder="<EMAIL>" name="account" /> <p class="error-msg" style="display: none"> <i class="el-icon-warning"></i> <span class="text"></span> </p> </div> <div class="set-item password"> <label>' + ((__t = $t("邮箱密码")) == null ? "" : __t) + '</label> <input type="password" placeholder=\'' + ((__t = $t("请输入邮箱密码")) == null ? "" : __t) + '\' name="password" /> <p class="error-msg" style="display: none"> <i class="el-icon-warning"></i> <span class="text"></span> </p> </div> <div class="set-item username hide"> <label>' + ((__t = $t("用户名称")) == null ? "" : __t) + '</label> <input type="text" placeholder=\'' + ((__t = $t("用户名称")) == null ? "" : __t) + '\' name="username" /> <p class="error-msg" style="display: none"> <i class="el-icon-warning"></i> <span class="text"></span> </p> </div> <div class="set-item flex" style="margin: -10px 0 14px"> <label style="margin-right: 3px;"></label> <div class="check-box mn-checkbox-box j-use-username"> <input type="checkbox" value="1" class="checkbox-input mn-checkbox-item" /> <span>' + ((__t = $t("使用用户名验证")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="manual-set"> <div class="set-item"> <div class="set-item-item"> <label>' + ((__t = $t("SMTP服务器")) == null ? "" : __t) + '</label> <input type="text" name="host" /> </div> <div class="mn-checkbox-box set-item-item"> <!-- 应向达要求写死不可关闭，如果需要重新开启选择 则去除 input 开放span --> <!-- <span class="mn-checkbox-item mn-selected" disabled></span> --> <input type="checkbox" name="is-outgoing-ssl" value="1" checked="checked" ';
            if (emailConfig.isForceSSL) {
                __p += " disabled ";
            }
            __p += ' class="mn-checkbox-item mn-selected is-ssl" /> <span>SSL</span> </div> <div class="set-item-item port"> <label>' + ((__t = $t("端口")) == null ? "" : __t) + '</label> <input type="text" name="port" class="port" /> </div> <p class="error-msg" style="display: none"> <i class="el-icon-warning"></i> <span class="text"></span> </p> </div> </div> <div class="btns-box"> <span class="crm-btn crm-btn-primary j-validate" >' + ((__t = $t("验证")) == null ? "" : __t) + '</span > <span class="crm-btn manual-set-btn j-set" >' + ((__t = $t("手动设置")) == null ? "" : __t) + '</span > </div> </div> <div class="right"> <div class="r-item">' + ((__t = $t("常见问题")) == null ? "" : __t) + '</div> <div class="r-item"> <a href="http://open.fxiaoke.com/support.html?articleId=7#faq11" target="_blank" >' + ((__t = $t("可以绑定个人邮箱吗")) == null ? "" : __t) + '</a > </div> <div class="r-item"> <a href="http://open.fxiaoke.com/support.html?articleId=7#faq6" target="_blank" >' + ((__t = $t("为什么密码总是错误")) == null ? "" : __t) + '</a > </div> <div class="r-item"> <a href="http://open.fxiaoke.com/support.html?articleId=7#artiId=7" target="_blank" >' + ((__t = $t("如何填写IMAP")) == null ? "" : __t) + "/" + ((__t = $t("SMTP邮箱地址")) == null ? "" : __t) + "</a > </div> </div> </div> </div> </div> </div>";
        }
        return __p;
    };
});