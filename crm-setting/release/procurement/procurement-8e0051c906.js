define("crm-setting/procurement/automatch/automacthresult/automacthresult",["crm-modules/common/util","../bquery/bquery","../bquery/tpl-html","./template/matchresult-html"],function(e,t,n){e("crm-modules/common/util");var c=e("../bquery/bquery"),i=(e("../bquery/tpl-html"),e("./template/matchresult-html")),e=Backbone.View.extend({events:{"click .add-btn":"onAdd"},initialize:function(e){this.comps={},this.setElement(e.wrapper)},render:function(e){e=JSON.parse(e.join_info);this.$el.html(i()),this.createCheckBox(e),this.bquery=new c({wrapper:$(".bquery-comp")}),this.bquery.render(e)},createCheckBox:function(e){var t=this;this.comps.checkbox=FxUI.create({wrapper:$(".result-chk")[0],template:'<div class="result-item">\n                                <div><fx-checkbox v-model="isChecked" :disabled="isDisabled" @change="changeHandle">'.concat($t("crm.procurement.automatchResult.comparisonWithBidderAndWinner"),'</fx-checkbox></div>\n                                <span class="text">').concat($t("crm.autoHint"),"</span>\n                           </div>"),data:function(){return{isChecked:0<e.length,isDisabled:!1}},mounted:function(){this.changeHandle(this.isChecked)},methods:{changeHandle:function(e){e?$(".bquery-comp",t.$el).show():$(".bquery-comp",t.$el).hide(),e?$(".result-title",t.$el).show():$(".result-title",t.$el).hide()}}})},getValue:function(){return this.bquery.getResultValue()},clear:function(){this.$(".conditon-list").html("")},destroy:function(){this.data=null,this.clear()}});n.exports=e});
define("crm-setting/procurement/automatch/automacthresult/template/matchresult-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="result-wrapper"> <div class="result-chk"></div> <h3 class="result-title">' + ((__t = $t("crm.procurement.automatchRule.proposedReference")) == null ? "" : __t) + '</h3> <div class="bquery-comp"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/procurement/automatch/automacthrule/automacthrule",["crm-modules/common/util","./template/map-html"],function(e,t,i){var l=e("crm-modules/common/util"),n=e("./template/map-html"),e=Backbone.View.extend({events:{"click .add-btn":"onAdd"},initialize:function(e){this.comps={},this.setElement(e.wrapper)},render:function(e,t){this.Rulelist=[],this.saveId=t;t=JSON.parse(e.caller_info||"{}"),e=JSON.parse(e.winner_info||"{}");this.Rulelist.push({name:t.name},{name:e.name}),this.$el.html(n({data:this.Rulelist})),this.renderList(t,0),this.renderList(e,1)},renderList:function(e,t){var i=this;0==t?i.caller_info=e:i.winner_info=e,_.each(e.infos,function(e){i._createItem(e,t)})},_createItem:function(t,e){this.comps.fieldList=FxUI.create({wrapper:$(".conditon-list")[e],template:'<div class="a-l-item crm-clearfix">\n                                <div class="gutter g-field">\n                                    <div class="f-box"><fx-checkbox v-model="checked" :disabled="isDisabled" @change="handleChange">{{label}}</fx-checkbox><fx-select v-model="value"  disabled></fx-select></div>\n                                </div>\n                           </div>',data:function(){return{checked:t.select,value:t.value,label:t.label,isDisabled:0==e&&"AccountObj"==t.api_name}},methods:{handleChange:function(e){t.select=e}}})},getValue:function(){return{id:this.saveId,caller_info:this.caller_info,winner_info:this.winner_info}},getRuleList:function(){var n=this,e=JSON.stringify({limit:10,offset:0,filters:[]});n._getRule&&(n._getRule.abort(),n._getRule=null),n._getRule=l.FHHApi({url:"/EM1HNCRM/API/v1/object/ProcurementComparisonObj/controller/List",data:{object_describe_api_name:"ProcurementComparisonObj",search_template_id:null,search_query_info:e},success:function(e){var t,i;0==e.Result.StatusCode?(n.RuleInfo=e.Value.dataList[0],n.saveId=e.Value.dataList[0]._id,t=JSON.parse(n.RuleInfo.caller_info),i=JSON.parse(n.RuleInfo.winner_info),n.Rulelist.push({name:t.name},{name:i.name})):l.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},clear:function(){this.$(".conditon-list").html("")},destroy:function(){this.data=null,this.clear()}});i.exports=e});
define("crm-setting/procurement/automatch/automacthrule/template/map-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<!--[ignore-i18n-file]--> ";
            _.each(data, function(item, idx) {
                __p += ' <div class="map-wrapper"> <div class="main-wrapper"> <div class="main-line"> <div class="left-wrapper"> <div class="title">' + ((__t = $t("crm.procurement.automatchRule.AnnouncementRelationField")) == null ? "" : __t) + '</div> <div class="left-select"> <input disabled type="text" value="' + ((__t = item.name == "招标单位" ? $t("procurement.caller_names") : $t("procurement.winner_names")) == null ? "" : __t) + '" class="unit-input"> </div> </div> <div class="mapto-icon"><span class="mapto-text">' + ((__t = $t("crm.procurement.automatchRule.goToCompare")) == null ? "" : __t) + '</span></div> <div class="half right-wrapper"> <div class="title">' + ((__t = $t("crm.procurement.automatchRule.comparisonRelatedFields")) == null ? "" : __t) + '</div> <div class="right-select"> <div class="conditon-item"> <div class="conditon-list"></div> </div> </div> </div> </div> </div> </div> ';
            });
        }
        return __p;
    };
});
define("crm-setting/procurement/automatch/automatch",["crm-modules/common/util","../automatch/automacthrule/automacthrule","../automatch/automacthresult/automacthresult","../automatch/relevancesetting/relevanceset","./template/tpl-html"],function(e,t,a){var u=e("crm-modules/common/util"),r=e("../automatch/automacthrule/automacthrule"),l=e("../automatch/automacthresult/automacthresult"),o=e("../automatch/relevancesetting/relevanceset"),e=(template=e("./template/tpl-html"),Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},events:{"click .j-saverule":"save"},render:function(){this.comps={},this.$el.html(template({})),this.automacthrule=new r({wrapper:$(".match-rule")}),this.automacthresult=new l({wrapper:$(".match-result")}),this.relevanceSetting=new o({wrapper:$(".relevance-setting")}),this.getRuleList()},getRuleList:function(){var t=this,e=JSON.stringify({limit:10,offset:0,filters:[]});t._getRule&&(t._getRule.abort(),t._getRule=null),t._getRule=u.FHHApi({url:"/EM1HNCRM/API/v1/object/ProcurementComparisonObj/controller/List",data:{object_describe_api_name:"ProcurementComparisonObj",search_template_id:null,search_query_info:e},success:function(e){0==e.Result.StatusCode?(t.RuleInfo=e.Value.dataList[0],t.saveId=e.Value.dataList[0]._id,t.automacthrule.render(t.RuleInfo,t.saveId),t.automacthresult.render(t.RuleInfo),t.relevanceSetting.render(t.RuleInfo.default_rule)):u.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},save:function(){var e=this,t=e.automacthresult.comps.checkbox.isChecked,a=e.automacthrule.getValue(),r=e.automacthresult.getValue(),l=e.relevanceSetting.getRelevanceValue();0===r.length&&t?u.remind(3,$t("我方参标方为必填项")):(t||(r=[]),e._getAjax&&(e._getAjax.abort(),e._getAjax=null),e._getAjax=u.FHHApi({url:"/EM1HNCRM//API/v1/object/ProcurementComparisonObj/action/Edit",data:{object_data:{_id:a.id,object_describe_api_name:"ProcurementComparisonObj",caller_info:JSON.stringify(a.caller_info),winner_info:JSON.stringify(a.winner_info),join_info:JSON.stringify(r),default_rule:1==l?1:0}},success:function(e){0==e.Result.StatusCode?u.remind(1,$t("保存成功")):u.alert(e.Result.FailureMessage)}},{errorAlertModel:1}))},show:function(){this.$el.toggle(!0)},hide:function(){this.$el.toggle(!1)},destory:function(){for(var e in this.comps)this.comps[e].destory()}}));e.AutoMacthRule=r,e.AutoMacthResult=l,a.exports=e});
function asyncGeneratorStep(e,t,a,s,i,r,n){try{var c=e[r](n),u=c.value}catch(e){return void a(e)}c.done?t(u):Promise.resolve(u).then(s,i)}function _asyncToGenerator(c){return function(){var e=this,n=arguments;return new Promise(function(t,a){var s=c.apply(e,n);function i(e){asyncGeneratorStep(s,t,a,i,r,"next",e)}function r(e){asyncGeneratorStep(s,t,a,i,r,"throw",e)}i(void 0)})}}define("crm-setting/procurement/automatch/bquery/bquery",["crm-modules/common/util","crm-modules/action/field/field","./tpl-html","crm-modules/components/search/search","./item-html"],function(n,e,t){var i=n("crm-modules/common/util"),a=n("crm-modules/action/field/field").C.Base,s=n("./tpl-html");t.exports=a.extend(_.extend({events:{"click .j-b-detail":"showBusinessDetail","click .j-delete":"deleteItem","click .j-delete-all":"deleteAll","click .j-list-add":"addItem","click .j-btn-add":"addItem"},initialize:function(e){this.TEMPOBJ="",this.setElement(e.wrapper)},render:function(e){this.datalist=e,this.obj_apiname=this.fieldAttr&&this.fieldAttr.describe_api_name,this.doRender(e)},doRender:function(e){this.$el.html(s({list:e})),this.renderSearch(this.$(".j-b-search")),this.asyncCheckNameByBusiness()},deleteItem:function(e){e=$(e.target).parent(".list-item").index();this.datalist.splice(e,1),$(".list-item").eq(e).remove(),this.datalist.length==[]&&this.doRender([])},deleteAll:function(){var e=this,t=CRM.util.confirm($t("crm.procument.isDelAllFirm"),null,function(){t.destroy(),e.datalist=[],e.doRender([])},{hideFn:function(){},stopPropagation:!0})},addItem:function(e){var t=this,a=$(".j-search-ipt").val(),s=this.TEMPOBJ.id?this.datalist.findIndex(function(e){return t.TEMPOBJ.id==e.id}):this.datalist.findIndex(function(e){return t.TEMPOBJ.value==e.name});if(-1==s&&""!=a){if(99<this.datalist.length)return void CRM.util.remind(3,$t("crm.autoHint"));this.datalist.push({id:this.TEMPOBJ.id,name:this.TEMPOBJ.value}),1==$(".list-result").find(".showTip").length?this.doRender(this.datalist):$(".row-result").append('<li class="list-item">'.concat(a,'<span class="fx-iconfont fx-icon-close delete-icon j-delete"></span></li>'))}else this.showError($(".j-search-ipt"),$t("crm.procurement.thisFirmIsAdd"));$(".j-search-ipt").val("")},getResultValue:function(){return this.datalist},getSearch:function(){return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=3,n("crm-modules/components/search/search");case 3:return a=e.sent,t={_iptFocusHandle:function(){this.searchable=!0,this.fetchAjax&&this.fetchAjax.abort();var e=this.getValue();this.trigger("focus",e.value),e.id||e.value&&this.fetch(e.value)}},a=a.extend(t),e.abrupt("return",a);case 7:case"end":return e.stop()}},e)}))()},renderSearch:function(i){var r=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,a,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=r,e.next=3,r.getSearch();case 3:a=e.sent,s={el:i,placeholder:$t("请输入"),url:"/EM1HDataptIndustry/industryFcpService/getCompanyByName",delay:500,straightway:!0,parseData:function(t){return"1"==t.errorCode||"2"==t.errorCode?[{value:t.message,errorCode:t.errorCode}]:_.map(t.companyEsObjects||[],function(e){return{id:e.KeyNo,value:e.Name,status:e.Status,operName:e.OperName,errorCode:t.errorCode}})},tpl:n("./item-html")},t.search=new a(s),t.search.on("focus",t.hideError,t),t.search.on("blur",t.changeHandle,t),t.search.render();case 9:case"end":return e.stop()}},e)}))()},backfill:function(a){var e,s=this;(a=a||s.search.getValue())&&!a.id||(e={url:s.iconfig.getBackfillUrl(s.obj_apiname),data:{objectDataId:a.id,industryDatasource:a.datasource},success:function(e){var t;0===e.Result.StatusCode?(t=e.Value.objectData,s.backfillSuccess(t,a)):i.alert(e.Result.FailureMessage||$t("操作失败"))},error:function(e){i.alert(e.statusText||$t("系统异常"))}},i.FHHApi(e,{errorAlertModel:1}))},backfillSuccess:function(e,t){this.TEMPOBJ=t,this.addItem()},showBusinessQueryMsg:function(e){e.Result.FailureMessage&&!CRM.getLocal("no_business_query_quota")&&CRM.util.alert(e.Result.FailureMessage,function(){CRM.setLocal("no_business_query_quota",!0)})},hideBusinessQueryMsg:function(){CRM.setLocal("no_business_query_quota",!1)},changeHandle:function(e){this.updateVal(e),this.asyncCheckCustomerName(e.value)},updateVal:function(e){(e=e||this.search.getValue()).value!==(this.getData()||"")&&(this.TEMPOBJ=e,this.setData(e.value,null,null,!0)),this.afterUpdate(e)},afterUpdate:function(e){e.id?this.lightBtn():this.shutBtn()},getCheckDuplicateUrl:function(e){return{AccountObj:"/EM1HNCRM/API/v1/object/sfa_duplicate_search/service/duplicate_check",PartnerObj:"/EM1HNCRM/API/v1/object/sfa_duplicate_search/service/duplicate_check",AccountMainDataObj:"/EM1HNCRM/API/v1/object/sfa_duplicate_search/service/duplicate_check"}[e]},getCheckDuplicateParam:function(e,t){return{api_name:e,object_data:{name:t,_id:this.bobj._id||""}}},asyncCheckCustomerName:function(e){var t=this,a=(0===this.datalist.length&&t.showError($(".j-search-ipt"),$t("拟定参标方不能为空")),t.getCheckDuplicateUrl(t.obj_apiname));e?(t.hideError(),a&&(t.checkAjax&&t.checkAjax.abort(),t.checkAjax=i.FHHApi({url:a,data:t.getCheckDuplicateParam(t.obj_apiname,e),success:function(e){t.afterDuplicateCheck(e)},complete:function(){t.checkAjax=null}},{errorAlertModel:1}))):t.hideTips()},afterDuplicateCheck:function(e){e&&e.Result&&0===e.Result.StatusCode&&e.Value.duplicated?this.showTips($t("该{{displayName}}已存在",{displayName:this.fieldAttr.label})):this.hideTips()},asyncCheckNameByBusiness:function(e,t){var a=this;if(e){if(e.id)return a.hideBusinessQueryMsg(),t?void t(e):(a.setValue(e),void a.updateVal(e));var s=e.value,e={url:"/EM1HDataptIndustry/industryFcpService/CheckCustomerByBusinessQuery",data:{KeyWord:s},success:function(e){0===e.Result.StatusCode&&e.Value.KeyNo?(e={value:s,id:e.Value.KeyNo},t?t(e):(a.setValue(e),a.updateVal(e))):t&&t({value:s})},complete:function(){a.businessAjax=null}};a.businessAjax&&a.businessAjax.abort(),a.businessAjax=i.FHHApi(e,{errorAlertModel:1})}},showBusinessDetail:function(e,t){var a=this,t=t||a.search.getValue();CRM.api.business_detail({apiname:a.obj_apiname,id:t.id,name:t.value,datasource:t.datasource,zIndex:CRM.util.getzIndex(a.get("zIndex"))+10,isShowBtn:!0,success:function(e){e&&a.backfillSuccess(e,t)}})},getValue:function(){var e=this.search.getValue();return this.fieldAttr.is_required&&!e.value&&this.showError(),CRM.util.trim(e.value)},setValue:function(e){this.hideError(),_.isString(e)&&(e={value:e}),this.search&&this.search.setValue(e)},lightBtn:function(){this.$(".j-b-detail").addClass("light-btn").attr("title",$t("查看工商信息"))},shutBtn:function(){this.$(".j-b-detail").removeClass("light-btn").attr("title","")},destroy:function(){this.checkAjax&&this.checkAjax.abort(),this.businessAjax&&this.businessAjax.abort(),this.search&&this.search.destroy(),this.search=this.checkAjax=this.businessAjax=null,this.super.destroy.apply(this,arguments)}}))});
define("crm-setting/procurement/automatch/bquery/item-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<li style="padding:15px 34px 15px 10px" class="b-li-item ' + ((__t = errorCode == "0" ? "j-search-item" : "") == null ? "" : __t) + '"> ';
            if (errorCode == "1" || errorCode == "2") {
                __p += ' <p style="font-size:14px;color:#C1C5CE;text-align:left;line-height:20px;padding: 0 16px;">' + ((__t = value) == null ? "" : __t) + "</p> ";
            } else {
                __p += ' <p class="b-name">' + ((__t = lightValue) == null ? "" : __t) + "</p> ";
                var operName_t = $t("法人{{operName}}", {
                    operName: operName
                });
                var status_t = $t("经营状态{{status}}", {
                    status: status
                });
                __p += ' <p class="b-desc"><span>' + ((__t = operName_t) == null ? "" : __t) + "</span><em>" + ((__t = $t("丨")) == null ? "" : __t) + "</em><span>" + ((__t = status_t) == null ? "" : __t) + '</span></p> <div class="b-btn j-list-add">' + ((__t = $t("添加")) == null ? "" : __t) + "</div> ";
            }
            __p += " </li>";
        }
        return __p;
    };
});
define("crm-setting/procurement/automatch/bquery/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-action-bquery bquery-search-wrapper"> <div class="crm-action-bquery-search j-b-search"></div> <div class="crm-action-bquery-icon j-b-detail"><div class="icon-business">工</div></div> <div class="btn_add j-btn-add">' + ((__t = $t("添加")) == null ? "" : __t) + '</div> </div> <div class="list-wrapper"> ';
            if (list.length > 0) {
                __p += ' <div class="list-title"><span class="selectd-text">' + ((__t = $t("已选企业")) == null ? "" : __t) + '</span> <span class="fx-iconfont fx-icon-delete j-delete-all">' + ((__t = $t("全部删除")) == null ? "" : __t) + '</span></div> <div class="list-result row-result"> ';
                _.each(list, function(item) {
                    __p += ' <li class="list-item">' + ((__t = item.name || item.value) == null ? "" : __t) + '<span class="fx-iconfont fx-icon-close delete-icon j-delete"></span></li> ';
                });
                __p += " </div> ";
            } else {
                __p += ' <div class="list-title"><span class="selectd-text">' + ((__t = $t("已选企业")) == null ? "" : __t) + '</span></div> <div class="list-result"> <div class="showTip">' + ((__t = $t("请先添加企业")) == null ? "" : __t) + "</div> </div> ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/procurement/automatch/relevancesetting/relevanceset",["crm-modules/common/util","./template/tpl-html"],function(e,t,n){e("crm-modules/common/util");var c=e("./template/tpl-html"),e=Backbone.View.extend({initialize:function(e){this.comps={},this.setElement(e.wrapper)},render:function(e){this.$el.html(c()),this.renderRelevanceSetTpl(e)},events:{},renderRelevanceSetTpl:function(e){var t={wrapper:".leads-wrapper",template:'<fx-checkbox v-model="checked">'.concat($t("默认规则（比对成功的标讯数据自动关联比对对象数据）"),"</fx-checkbox>"),data:function(){return{checked:0!=e}}};this.comps.relevanceset=window.FxUI.create(t)},getRelevanceValue:function(){return this.comps.relevanceset.checked},destroy:function(){this.undelegateEvents()}});n.exports=e});
define("crm-setting/procurement/automatch/relevancesetting/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="enterpriselibrary-box"> <div class="relevance-setting-main"> <h3 class="title">' + ((__t = $t("自动关联设置")) == null ? "" : __t) + '</h3> <div class="leads-wrapper"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/procurement/automatch/template/template-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="match-wrapper"> <h3>' + ((__t = $t("比对规则")) == null ? "" : __t) + '</h3> <div class="match-rule"></div> <div class="match-result"></div> <div class="relevance-setting"> </div> <div class="j-button"> <div class="j-saverule">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/procurement/automatch/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="match-wrapper"> <h3>' + ((__t = $t("比对规则")) == null ? "" : __t) + '</h3> <div class="match-rule"></div> <div class="match-result"></div> <div class="relevance-setting"> </div> <div class="j-button"> <div class="j-saverule">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/procurement/opprule/components/afteraction",[],function(t,n,e){e.exports=function(t){var n=document.createElement("div");return $(t.el).html(n),new Vue({el:n,data:function(){return{checkedtwo:t.option.autoRelated,input_percent:t.option.autoRelatedSimilarity}},methods:{destroy:function(){this.$destroy(),$(this.$el).remove()},getValue:function(){return{autoRelated:this.checkedtwo,autoRelatedSimilarity:Number(this.input_percent)}},handleInput:function(t){var n=this;0==/^(30|3[1-9]|[4-8][0-9]|9[0-9]|100)$/.test(t)&&t&&1<t.length&&10!=Number(t)||t&&0===Number(t[0])?this.$nextTick(function(){n.input_percent=""}):this.input_percent=t}},template:'\n            <div class = "afteraction-main">\n                <div class = "afteraction-bottom">\n                <fx-checkbox v-model="checkedtwo">'.concat($t("crm.business.opportunity"),'</fx-checkbox>\n                <div class = "opp-main" v-show = "checkedtwo">\n                    <div class="mate-percentage">\n                        <div class = "mate-percentage-main">\n                        <div class="text-mate"> ').concat($t("匹配度"),'>= </div>\n                        <fx-input\n                        v-model="input_percent"\n                        @input="handleInput"\n                        size="small"\n                        type="percent"\n                        placeholder="').concat($t("crm.bid.thirtytohundred"),'"\n                      >\n                        <span class="el-input__icon" slot="suffix" style="padding-right:8px">\n                          %\n                        </span>\n                      </fx-input>\n                      <div v class="text-mate">  ').concat($t("crm.bid.automaticallynow"),' </div>\n                        </div>\n                    </div>\n                    <span class="text">').concat($t("crm.bid.afteraction.remindercontent"),"</span>\n                </div>\n                </div>\n            </div>")})}});
define("crm-setting/procurement/opprule/components/condition",["crm-modules/common/filtergroup/filtergroup.js","crm-modules/common/util"],function(e,i,t){var o=e("crm-modules/common/filtergroup/filtergroup.js");e("crm-modules/common/util");t.exports=Backbone.View.extend({initialize:function(e){this.initCondition(e.option.defaultValue,e.option.field)},initCondition:function(e,i){var t=this;t.filterGroup=new o({$wrapper:t.$el,title:$t("且"),fields:i,defaultValue:e,apiname:null,width:950,AND_MAX:20,type:"CONDITION"}),t.filterGroup.render()},showError:function(){CRM.util.showErrmsg(this.$el,$t("crm.noSetSameField"))},getValue:function(){return JSON.parse(this.filterGroup.getValue())},hideErrmsg:function(){CRM.util.hideErrmsg(this.$el)}})});
define("crm-setting/procurement/opprule/components/rule",[],function(t,n,e){e.exports=function(t){var n=document.createElement("div");return $(t.el).html(n),new Vue({el:n,data:function(){return{input_percent:t.nameSimilarity,leftInputOne:$t("招标单位"),leftInputTwo:$t("crm.bid.title"),rightInputOne:$t("客户"),rightInputTwo:$t("crm.newopp.title"),oppNameChecked:!0,checkedAccount:t.useAccount}},methods:{destroy:function(){this.$destroy(),$(this.$el).remove()},getValue:function(){return{nameSimilarity:Number(this.input_percent),useAccount:this.checkedAccount}},changeInput:function(){Number(this.input_percent)<30&&(this.input_percent=30)},handleInput:function(t){var n=this;0==/^(30|3[1-9]|[4-8][0-9]|9[0-9]|100)$/.test(t)&&t&&1<t.length&&10!=Number(t)?this.$nextTick(function(){n.input_percent=""}):this.input_percent=t}},template:'\n\t\t\t<div class="opprule-mian">\n\t\t\t\t<div class = "opprule-field">\n\t\t\t\t\t<div class = "left-field">\n\t\t\t\t\t\t<p>'.concat($t("crm.bid.announcement"),'</p>\n\t\t\t\t\t\t<fx-input\n  \t\t\t\t\t\t\tv-model="leftInputOne"\n  \t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t@input="handleInput"\n  \t\t\t\t\t\t\t:disabled="true"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t<div style = "height:37px"></div>\n\t\t\t\t\t\t<fx-input\n  \t\t\t\t\t\t\tv-model="leftInputTwo"\n  \t\t\t\t\t\t\tsize="small"\n  \t\t\t\t\t\t\t:disabled="true"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class="mapto-icon">\n\t\t\t\t\t\t<span class="mapto-text">\n\t\t\t\t\t\t{{$t("crm.procurement.automatchRule.goToCompare")}}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</div>\n\t\t\t\t\t<div class = "right-field">\n\t\t\t\t\t\t<p>').concat($t("crm.bid.opportunity.announcement"),'</p>\n\t\t\t\t\t\t<div class="checkbox-wrapper">\n\t\t\t\t\t\t<fx-checkbox v-model="checkedAccount">').concat($t("比对"),'</fx-checkbox>\n\t\t\t\t\t\t<fx-input\n  \t\t\t\t\t\t\tv-model="rightInputOne"\n  \t\t\t\t\t\t\tsize="small"\n  \t\t\t\t\t\t\t:disabled="true"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div style = "height:37px"></div>\n\t\t\t\t\t\t<div class="checkbox-wrapper">\n\t\t\t\t\t\t<fx-checkbox v-model="oppNameChecked" disabled>').concat($t("比对"),'</fx-checkbox>\n\t\t\t\t\t\t<fx-input\n  \t\t\t\t\t\t\tv-model="rightInputTwo"\n  \t\t\t\t\t\t\tsize="small"\n  \t\t\t\t\t\t\t:disabled="true"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t\t<p style="margin: 10px 0;">').concat($t("crm.bid.matching.text"),'</p>\n\t\t\t<div class = "after-input" >\n\t\t\t\t<fx-input\n\t\t\t\tv-model="input_percent"\n\t\t\t\tsize="small"\n\t\t\t\t@input="handleInput"\n\t\t\t\t@change = "changeInput"\n\t\t\t\ttype="number"\n\t\t\t\tplaceholder="').concat($t("crm.bid.thirtytohundred"),'"\n\t\t\t  \t>\n\t\t\t\t<span class="el-input__icon" slot="suffix" style="padding-right:8px">\n\t\t\t\t  %\n\t\t\t\t</span>\n\t\t\t  </fx-input>\n\t\t\t</div>\n\t\t\t</div>\n\t\t\n\t  ')})}});
function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var r;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(r="Object"===(r={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:r)||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(t,e):void 0}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function _iterableToArrayLimit(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,l,a=[],u=!0,c=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(a.push(n.value),a.length!==e);u=!0);}catch(t){c=!0,i=t}finally{try{if(!u&&null!=r.return&&(l=r.return(),Object(l)!==l))return}finally{if(c)throw i}}return a}}function _arrayWithHoles(t){if(Array.isArray(t))return t}define("crm-setting/procurement/opprule/index",["./components/rule","./components/condition","./components/afteraction"],function(r,t,e){e.exports=function(t){var e=document.createElement("div");return OppRule=r("./components/rule"),Condition=r("./components/condition"),AfterAction=r("./components/afteraction"),$(t.el).html(e),new Vue({el:e,data:function(){return{}},created:function(){var r=this;Promise.all([this.getConfig(),this.getOppField()]).then(function(t){var t=_slicedToArray(t,2),e=t[0];r.initComs(e,t[1])})},methods:{destroy:function(){this.$destroy(),$(this.$el).remove()},getOppField:function(){var e=this;return new Promise(function(r,t){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/NewOpportunityObj/controller/ListHeader",data:{apiname:"NewOpportunityObj",check_edit_permission:!1,include_layout:!0,layout_by_template:!0,layout_type:"list"},success:function(t){var e;0===t.Result.StatusCode?(e=null==(e=t.Value)||null==(e=e.objectDescribe)?void 0:e.fields,r(e)):CRM.util.alert(t.Result.FailureMessage||$t("暂时无法获取相关数据请稍后重试"))},complete:function(){e._fetchAJax=null}},{errorAlertModel:1})})},getConfig:function(){var r=this;return new Promise(function(e,t){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/procurement/service/suspected_opportunity_rule_get",data:{},success:function(t){0===t.Result.StatusCode?e(t.Value||{}):CRM.util.alert(t.Result.FailureMessage||$t("暂时无法获取相关数据请稍后重试"))},complete:function(){r._fetchAJax=null}},{errorAlertModel:1})})},initComs:function(t,e){this.oppRule=new OppRule({el:$(".opprule-rule-before"),nameSimilarity:t.nameSimilarity||"",useAccount:void 0===t.useAccount||t.useAccount}),this.condition=new Condition({el:$(".opprule-rule-filter"),option:{defaultValue:t.opportunityFilters||[],field:e}}),this.afterAction=new AfterAction({el:$(".opprule-rule-after"),option:{autoRelated:t.autoRelated||!1,autoRelatedSimilarity:t.autoRelatedSimilarity||""}})},saveResult:function(){var t=this;(!t.afterAction.getValue().autoRelated||t.afterAction.getValue().autoRelatedSimilarity)&&t.oppRule.getValue()?this.condition.getValue().length?CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/procurement/service/suspected_opportunity_rule_set",data:{nameSimilarity:t.oppRule.getValue().nameSimilarity,useAccount:t.oppRule.getValue().useAccount,autoRelated:t.afterAction.getValue().autoRelated,autoRelatedSimilarity:t.afterAction.getValue().autoRelatedSimilarity,opportunityFilters:t.condition.getValue()},success:function(t){0===t.Result.StatusCode?CRM.util.remind(1,$t("保存成功")):CRM.util.alert(t.Result.FailureMessage||$t("暂时无法获取相关数据请稍后重试"))},complete:function(){t._fetchAJax=null}},{errorAlertModel:1}):CRM.util.remind(3,$t("crm.bid.please.inputNewOppData")):CRM.util.remind(3,$t("crm.bid.please.inputThirtytoHundred"))}},template:'\n\t\t\t<div class="opprule-wrapper">\n\t\t\t\t<h3>'.concat($t("比对规则"),'</h3>\n\t\t\t\t<div class="opprule-rule-before"></div>\n\t\t\t\t<h3>').concat($t("crm.bid.compare.opportunity.scope"),'</h3>\n\t\t\t\t<div class="opprule-rule-filter"></div>\n\t\t\t\t<h3>').concat($t("后动作"),'</h3>\n\t\t\t\t<div class="opprule-rule-after"></div>\n\t\t\t\t<div class="j-button-opprule">\n\t\t\t\t\t<div class="j-saverule-opprule" @click = "saveResult">').concat($t("保存"),"</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t  \t\t\t")})}});
define("crm-setting/procurement/procurement",["crm-modules/common/util","./template/tpl-html","./automatch/automatch","./opprule/index.js","crm-modules/page/procurementruleobj/procurementruleobj","crm-modules/page/biddingsubscriptionrulesobj/biddingsubscriptionrulesobj","crm-modules/page/historicalbiddingimportobj/historicalbiddingimportobj"],function(e,i,t){var a=e("crm-modules/common/util"),s=e("./template/tpl-html"),r=e("./automatch/automatch"),n=e("./opprule/index.js"),e=(Procurementruleobj=e("crm-modules/page/procurementruleobj/procurementruleobj"),BiddingSubscriptionRulesObj=e("crm-modules/page/biddingsubscriptionrulesobj/biddingsubscriptionrulesobj"),HistoricalBiddingImportObj=e("crm-modules/page/historicalbiddingimportobj/historicalbiddingimportobj"),Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){var i=this;this.getPermission().done(function(e){i.judgmentAuthority(e)})},events:{"click .j-crm-set-bid":"onSetBidFun","click .j-save":"saveHandle","click .zhiliao-sub":"showZhiliaoSub","click .qianlima-sub":"showQianlimaSub","click .enterprise-enter":"changeToEnterPrise","click .newOpp-enter":"changeToNewOpp"},judgmentAuthority:function(e){var i=this;i.hasProcurementQlm=e.hasProcurementQlm,i.hasProcurementZl=e.hasProcurementZl,i.hasQlmAiLicense=e.hasQlmAiLicense,this.$el.html(s({param:e,isGray:CRM.util.isGrayScale("CRM_SIGNAL_SUBSCRIPTION_IMPORT")})),this.initView(e)},initView:function(e){var i=this;e.marketAnalysis&&this.initPageView(),e.transferRules&&this.getConfig().done(function(e){i.purchaseBid=e,i.renderRadioTpl(e)}),e.autoComparisonRules&&this.initRuleView()},getConfig:function(){return new Promise(function(t,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_values",data:{isAllConfig:!1,keys:["procurement_transfer_setting"]},success:function(e){var i;0===e.Result.StatusCode?e.Value&&e.Value.values&&(i=(i=e.Value.values[0])&&i.value,t(i)):a.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},getPermission:function(){return new Promise(function(i,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/procurement/service/get_procurement",data:{},success:function(e){0===e.Result.StatusCode?e.Value&&i(e.Value):a.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},renderRadioTpl:function(e){var i="AccountObj"===e?"1":"0",t=this;t.bidRadio=FxUI.create({wrapper:".bid-wrapper",template:'<fx-radio-group  v-model="value"><fx-radio v-for="data in purchases" :key="data.value" :label="data.value" v-on:change="change(data)">{{data.label}}</fx-radio></fx-radio-group>',data:function(){return{value:i,purchases:[{label:$t("crm.销售线索"),name:"LeadsObj",value:"0"},{label:$t("crm.客户"),name:"AccountObj",value:"1"}]}},methods:{change:function(e){t.purchaseBid=e.name}}})},initPageView:function(){this.procurementruleobj=new Procurementruleobj({wrapper:this.$(".list-view"),autoHeight:!0}),this.procurementruleobj.render()},initRuleView:function(){this.automatch=new r({wrapper:this.$(".automatch-wrapper")}),this.automatch.render()},initSignalSubView:function(e){var i=this;this.SignalSub=new BiddingSubscriptionRulesObj({wrapper:"hasProcurementZl"==e?i.$(".signalsub-zhiliao-wrapper"):i.$(".signalsub-qianlima-wrapper"),procurementConfig:e,hasQlmAiLicense:i.hasQlmAiLicense}),i.SignalSub.render()},initHistoryView:function(){this.hisSignaSub=new HistoricalBiddingImportObj({wrapper:this.$(".beaconImport-wrapper")}),this.hisSignaSub.render()},onSetBidFun:function(e){var i=this;$(".crm-set-bid",i.$el).addClass("hide"),$(".j-crm-set-bid",i.$el).removeClass("cur"),$(e.target).addClass("cur"),$(e.target).hasClass("crm-set-system__bid")?$(".crm-set-system",i.$el).removeClass("hide"):$(e.target).hasClass("crm-set-sedi__bid")?$(".crm-bid-sediment",i.$el).removeClass("hide"):$(e.target).hasClass("crm-set-automatch__bid")?$(".crm-bid-automatch",i.$el).removeClass("hide"):$(e.target).hasClass("crm-set-signalsub__bid")?($(".crm-bid-signalsub",i.$el).removeClass("hide"),i.hasProcurementQlm&&!i.hasProcurementZl?i.initSignalSubView("hasProcurementQlm"):i.initSignalSubView("hasProcurementZl")):$(e.target).hasClass("crm-set-beaconImport__bid")&&($(".crm-bid-beaconImport",i.$el).removeClass("hide"),i.initHistoryView())},setConfig:function(e){this._getAjax&&(this._getAjax.abort(),this._getAjax=null),this._getAjax=CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"procurement_transfer_setting",value:e},success:function(e){0===e.Result.StatusCode?CRM.util.remind(1,$t("设置成功")):CRM.util.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},showZhiliaoSub:function(){"none"===$(".zhiliao-sub-main").css("display")&&($(".zhiliao-sub-main").show()&&$(".qianlima-sub-main").hide(),$(".zhiliao-sub").addClass("tab-sub-color")&&$(".qianlima-sub").removeClass("tab-sub-color"),this.initSignalSubView("hasProcurementZl"))},showQianlimaSub:function(){"none"===$(".qianlima-sub-main").css("display")&&($(".qianlima-sub-main").show()&&$(".zhiliao-sub-main").hide(),$(".qianlima-sub").addClass("tab-sub-color")&&$(".zhiliao-sub").removeClass("tab-sub-color"),this.initSignalSubView("hasProcurementQlm"))},changeToEnterPrise:function(){"none"===$(".automatch-enterprise").css("display")&&($(".automatch-enterprise").show()&&$(".newOpp-enterprise").hide(),$(".enterprise-enter").addClass("tab-sub-color"))&&$(".newOpp-enter").removeClass("tab-sub-color")},changeToNewOpp:function(){"none"===$(".newOpp-enterprise").css("display")&&($(".newOpp-enterprise").show()&&$(".automatch-enterprise").hide(),$(".newOpp-enter").addClass("tab-sub-color")&&$(".enterprise-enter").removeClass("tab-sub-color"),this.oppRule=new n({el:$(".automatch-newOpp-wrapper")}))},saveHandle:function(){this.purchaseBid?this.setConfig(this.purchaseBid):CRM.util.alert($t("请选择一项"))},destroy:function(){var i=this;i._ajax&&i._ajax.abort(),_.each(["dt","action","detail","bidRadio"],function(e){i[e]&&i[e].destroy&&i[e].destroy()})}}));t.exports=e});
define("crm-setting/procurement/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (param) {
                __p += ' <div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("crm.procurement.bidding.manage")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-tab"> ';
                if (param.marketAnalysis) {
                    __p += ' <span class="crm-set-system__bid j-crm-set-bid cur">' + ((__t = $t("crm.procurement.market.analysis.setting")) == null ? "" : __t) + "</span> ";
                }
                __p += " ";
                if (param.transferRules) {
                    __p += ' <span class="crm-set-sedi__bid j-crm-set-bid">' + ((__t = $t("crm.procurement.redeposit.rule")) == null ? "" : __t) + "</span> ";
                }
                __p += " ";
                if (param.autoComparisonRules) {
                    __p += " ";
                    var autoStyle = (param.hasProcurementZl || param.hasProcurementQlm) && (!param.marketAnalysis || !param.transferRules);
                    __p += ' <span class="crm-set-automatch__bid j-crm-set-bid ' + ((__t = autoStyle ? "cur" : "") == null ? "" : __t) + '">' + ((__t = $t("crm.procurement.automaticmatching.rule")) == null ? "" : __t) + "</span> ";
                }
                __p += " ";
                if (param.hasProcurementZl || param.hasProcurementQlm) {
                    __p += ' <span class="crm-set-signalsub__bid j-crm-set-bid ">' + ((__t = $t("标讯订阅规则")) == null ? "" : __t) + "</span> ";
                }
                __p += " ";
                if (param.historicalImport && isGray) {
                    __p += ' <span class="crm-set-beaconImport__bid j-crm-set-bid">' + ((__t = $t("历史标讯导入")) == null ? "" : __t) + "</span> ";
                }
                __p += ' </div> <div class="crm-module-con crm-scroll"> ';
                if (param.transferRules) {
                    __p += ' <div class="crm-bid-sediment crm-set-bid hide"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("采购单位转存为指定对象数据")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("转存后的数据进行分配流转")) == null ? "" : __t) + '</p> </div> <div class="crm-setting-bid-main"> <div> <h3 class="title">' + ((__t = $t("crm.procurement.purchase.unit")) == null ? "" : __t) + '</h3> <div class="bid-wrapper"></div> <div class="crm-btn crm-btn-primary j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div> </div> ";
                }
                __p += " ";
                if (param.marketAnalysis) {
                    __p += ' <div class="crm-set-system crm-set-bid"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("分析企业区域市场占有率和拓展开源渠道")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("支持按照后台订阅方式生成市场分析结果")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("自动给成员发送CRM提醒")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("最多生成10组分析结果")) == null ? "" : __t) + '</p> </div> <div class="list-view"> <div class="crm-loading"></div> </div> </div> ';
                }
                __p += " ";
                if (param.autoComparisonRules) {
                    __p += ' <div class="crm-bid-automatch crm-set-bid ' + ((__t = (param.hasProcurementSecond || param.hasProcurementQlm) && !param.hasProcurement ? "" : "hide") == null ? "" : __t) + '"> <div class="sub-title-header"> <span class="enterprise-enter tab-sub-color"> ' + ((__t = $t("crm.bid.comparison.information")) == null ? "" : __t) + ' </span> <span class="newOpp-enter" style="border-left: none;"> ' + ((__t = $t("crm.bid.opportunity.information")) == null ? "" : __t) + ' </span> </div> <div class="automatch-enterprise"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("crm.procurement.automatch.contrastRuleExplainFirst")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.procurement.automatch.contrastRuleExplainSeconed")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.procurement.automatch.contrastRuleExplainThird")) == null ? "" : __t) + '</p> </div> <div class="crm-setting-automatch-main"> <div class="automatch-wrapper"></div> </div> </div> <div class="newOpp-enterprise" style="display: none;"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("crm.procurement.automatch.contrastRuleExplainQlmFirst")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.procurement.automatch.contrastRuleExplainQlmSeconed")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.procurement.automatch.contrastRuleExplainQlmThird")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.procurement.automatch.contrastRuleExplainQlmFourth")) == null ? "" : __t) + '</p> </div> <div class="crm-setting-newOpp-main"> <div class="automatch-newOpp-wrapper"></div> </div> </div> </div> <div class="crm-bid-signalsub crm-set-bid hide"> <div class="crm-setting-signalsub-main"> ';
                    var addStyle = param.hasProcurementZl && param.hasProcurementQlm;
                    __p += " ";
                    if (param.hasProcurementZl && param.hasProcurementQlm) {
                        __p += ' <div class="sub-title-header"> <span class="zhiliao-sub tab-sub-color"> ' + ((__t = $t("crm.subscriber.zl")) == null ? "" : __t) + ' </span> <span class="qianlima-sub" style="border-left: none;"> ' + ((__t = $t("crm.subscriber.qlm")) == null ? "" : __t) + " </span> </div> ";
                    }
                    __p += " ";
                    if (param.hasProcurementZl) {
                        __p += ' <div class="zhiliao-sub-main"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("crm.tencentSubscriptionRulesOne")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.tencentSubscriptionRulesTwo")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.tencentSubscriptionRulesThree")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.tencentSubscriptionRulesFour")) == null ? "" : __t) + '</p> </div> <div class="crm-setting-signalsub-main"> <div class="signalsub-zhiliao-wrapper"></div> </div> </div> ';
                    }
                    __p += " ";
                    if (param.hasProcurementQlm) {
                        __p += ' <div class="qianlima-sub-main" style="' + ((__t = addStyle ? "display: none;" : "") == null ? "" : __t) + '"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("crm.tencentSubscriptionRulesNewOne.qlm")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.tencentSubscriptionRulesNewTwo.qlm")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.tencentSubscriptionRulesNewThree.qlm")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.tencentSubscriptionRulesNewFour.qlm")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.tencentSubscriptionRulesNewFive.qlm")) == null ? "" : __t) + '</p> </div> <div class="crm-setting-signalsub-main"> <div class="signalsub-qianlima-wrapper"></div> </div> </div> ';
                    }
                    __p += " </div> </div> ";
                }
                __p += " ";
                if (param.historicalImport && isGray) {
                    __p += ' <div class="crm-bid-beaconImport crm-set-bid hide"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("crm.bannerImportOne")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.bannerImportTwo")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.bannerImportThree")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.bannerImportFour")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.bannerImportFive")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.bannerImportSix")) == null ? "" : __t) + '</p> </div> <div class="crm-setting-beaconImport-main"> <div class="beaconImport-wrapper"></div> </div> </div> ';
                }
                __p += " </div> ";
            }
        }
        return __p;
    };
});