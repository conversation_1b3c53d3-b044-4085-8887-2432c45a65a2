define("crm-setting/cleanunit/cleanunit",["./template/tpl-html","./template/nav-html","./setcleanunit/setcleanunit","crm-modules/common/util"],function(e,t,a){var n=e("./template/tpl-html"),l=e("./template/nav-html"),c=e("./setcleanunit/setcleanunit"),s=e("crm-modules/common/util"),u=Backbone.View.extend({events:{"click .j-rule":"showRuleHandle","click .j-booking":"bookingHandle","click .j-tab span":"tabHandle","click .j-seeresult":"seeResultHandle","click .j-cancel":"cancelHandle"},initialize:function(e){var t=this;t.setElement(e.wrapper),t.dataCleanInfo=null,t.total=3e4,t.times=0},render:function(){var t=this;t.getLeatestCleanInfo(function(e){t.$el.html(n(e)),t.renderCon()})},renderCon:function(){var e=this;$(".tab-con",e.$el).html(l({data:e.curclean})),"Cleaning"==e.curclean.status&&this.startListen()},getLeatestCleanInfo:function(n){var l=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_clean/service/lastestInfos",data:{},success:function(e){var t,a;0==e.Result.StatusCode?(t=l.dataCleanInfo=e.Value.dataCleanInfos,a={AccountObj:$t("crm.客户"),ContactObj:$t("crm.联系人"),LeadsObj:$t("crm.销售线索")},_.map(l.dataCleanInfo,function(e){e.object_name=a[e.object_api_name]}),l.curclean||(l.curclean=_.findWhere(t,{status:"Cleaning"}))||(l.curclean=_.findWhere(t,{object_api_name:"AccountObj"})),n&&n(l.curclean)):s.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},startListen:function(){var t=this;t.listen=setInterval(function(){t.getDataCleanStatus(function(e){if(t.curclean.status=e,360<++t.times)t.curclean.status="TimeOut",t.setPageStatus(),s.alert($t("网络繁忙请稍后重试"));else switch(e){case"Finished":clearInterval(t.listen),t.listen=null,setTimeout(function(){t.navToCleanResult(),t.setPageStatus()},700);break;case"Failed":case"TimeOut":t.setPageStatus(),s.alert({Failed:$t("清洗失败"),TimeOut:$t("网络繁忙请稍后重试")}[e]);break;default:t.setPageStatus()}})},5e3)},getDataCleanStatus:function(t){var e=this;e.li_ajax&&e.li_ajax.abort(),e.li_ajax=s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_clean/service/status",data:{dataCleanID:e.curclean.data_clean_id},success:function(e){0==e.Result.StatusCode?t&&t(e.Value.status):s.alert(e.Result.FailureMessage)},complete:function(){e.li_ajax=null}},{errorAlertModel:1})},tabHandle:function(e){var t=this,e=$(e.currentTarget);e.addClass("cur").siblings().removeClass("cur"),t.curclean=_.findWhere(t.dataCleanInfo,{object_api_name:e.data("apiname")}),t.setPageStatus()},showRuleHandle:function(){(new c).show({api_name:this.curclean.object_api_name})},bookingHandle:function(){var t=this;t.$(".crm-module-con").attr("data-status"),s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_clean/service/create",data:{objectApiName:t.curclean.object_api_name},success:function(e){if(0==e.Result.StatusCode)return e.Value.data_clean_id?(t.curclean.data_clean_id=e.Value.data_clean_id,t.curclean.create_time=e.Value.create_time,t.curclean.status="Create",t.setPageStatus(),CRM.util.alert($t("已为您预约清洗操作")),void setTimeout(function(){t.getDataCleanStatus(function(e){t.curclean.status=e,t.setPageStatus()})},2e3)):void 0;s.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},cancelHandle:function(){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_clean/service/stop",data:{objectApiName:t.curclean.object_api_name,dataCleanID:t.curclean.data_clean_id},success:function(e){0==e.Result.StatusCode?(t.curclean.status="Stop",t.setPageStatus()):s.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},setPageStatus:function(){var e=this;e.times=0,e.li_ajax&&e.li_ajax.abort(),e.listen&&clearInterval(e.listen),e.listen=null,$(".crm-module-con",e.$el).attr("data-status",e.curclean.status),e.renderCon()},seeResultHandle:function(e){this.curclean.data_clean_id?this.navToCleanResult():s.alert($t("尚未清洗过数据"))},navToCleanResult:function(){var t=this,a=t.curclean.data_clean_id,n={LeadsObj:1,AccountObj:2,ContactObj:3}[t.curclean.object_api_name];e.async("crm-modules/common/cleanresult/cleanresult",function(e){t.result=new e({id:a,type:n,from:1}),t.$el.append(t.result.render().$el)})},destroy:function(){var e=this;e.listen&&clearInterval(e.listen),e.result&&e.result.destroy&&e.result.destroy(),e.curclean=e.result=e.listen=null}});a.exports=u});
define("crm-setting/cleanunit/knob/circle",[],function(t,e,i){o=window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.oRequestAnimationFrame||window.msRequestAnimationFrame||function(t){setTimeout(t,1e3/60)},a.prototype={VERSION:"0.0.6",_generate:function(){return this._svgSize=2*this._radius,this._radiusAdjusted=this._radius-this._strokeWidth/2,this._generateSvg()._generateText()._generateWrapper(),this._el.innerHTML="",this._el.appendChild(this._wrapContainer),this},_setPercentage:function(t){this._movingPath.setAttribute("d",this._calculatePath(t,!0)),this._textContainer.innerHTML=this._getText(this.getValueFromPercent(t))},_generateWrapper:function(){return this._wrapContainer=document.createElement("div"),this._wrapContainer.className=this._wrpClass,this._styleWrapper&&(this._wrapContainer.style.position="relative",this._wrapContainer.style.display="inline-block"),this._wrapContainer.appendChild(this._svg),this._wrapContainer.appendChild(this._textContainer),this},_generateText:function(){if(this._textContainer=document.createElement("div"),this._textContainer.className=this._textClass,this._styleText){var t,e={position:"absolute",top:0,left:0,textAlign:"center",width:"100%",fontSize:.7*this._radius+"px",height:this._svgSize+"px",lineHeight:this._svgSize+"px"};for(t in e)this._textContainer.style[t]=e[t]}return this._textContainer.innerHTML=this._getText(0),this},_getText:function(t){return this._text?(void 0===t&&(t=this._value),t=parseFloat(t.toFixed(2)),"function"==typeof this._text?this._text.call(this,t):this._text):""},_generateSvg:function(){return this._svg=document.createElementNS("http://www.w3.org/2000/svg","svg"),this._svg.setAttribute("xmlns","http://www.w3.org/2000/svg"),this._svg.setAttribute("width",this._svgSize),this._svg.setAttribute("height",this._svgSize),this._generateDefs(),this._generatePath(100,!1,this._colors[0],this._maxValClass)._generatePath(1,!0,this._colors[1],this._valClass),this._movingPath=this._svg.getElementsByTagName("path")[1],this},_generateDefs:function(){var t=document.createElementNS("http://www.w3.org/2000/svg","defs"),e=document.createElementNS("http://www.w3.org/2000/svg","linearGradient"),i=document.createElementNS("http://www.w3.org/2000/svg","stop"),s=document.createElementNS("http://www.w3.org/2000/svg","stop");return e.setAttribute("id","grad"),e.setAttribute("x1","0%"),e.setAttribute("x2","100%"),e.setAttribute("y1","0%"),e.setAttribute("y2","0%"),i.setAttribute("offset","0%"),i.setAttribute("style","stop-color:rgb(175,239,238);stop-opacity:1"),s.setAttribute("offset","100%"),s.setAttribute("style","stop-color:rgb(66,132,211);stop-opacity:1"),e.appendChild(i),e.appendChild(s),t.appendChild(e),this._svg.appendChild(t),this},_generatePath:function(t,e,i,s){var a=document.createElementNS("http://www.w3.org/2000/svg","path");return a.setAttribute("fill","transparent"),a.setAttribute("stroke",i),a.setAttribute("stroke-width",this._strokeWidth),a.setAttribute("d",this._calculatePath(t,e)),a.setAttribute("class",s),this._svg.appendChild(a),this},_calculatePath:function(t,e){t=this._start+t/100*this._circ,t=this._precise(t);return this._arc(t,e)},_arc:function(t,e){var i=t-.001,t=t-this._startPrecise<Math.PI?0:1;return["M",this._radius+this._radiusAdjusted*Math.cos(this._startPrecise),this._radius+this._radiusAdjusted*Math.sin(this._startPrecise),"A",this._radiusAdjusted,this._radiusAdjusted,0,t,1,this._radius+this._radiusAdjusted*Math.cos(i),this._radius+this._radiusAdjusted*Math.sin(i),e?"":"Z"].join(" ")},_precise:function(t){return Math.round(1e3*t)/1e3},htmlifyNumber:function(t,e,i){e=e||"circles-integer",i=i||"circles-decimals";t=(t+"").split("."),e='<span class="'+e+'">'+t[0]+"</span>";return 1<t.length&&(e+='.<span class="'+i+'">'+t[1].substring(0,2)+"</span>"),e},updateRadius:function(t){return this._radius=t,this._generate().update(!0)},updateWidth:function(t){return this._strokeWidth=t,this._generate().update(!0)},updateColors:function(t){this._colors=t;var e=this._svg.getElementsByTagName("path");return e[0].setAttribute("stroke",t[0]),e[1].setAttribute("stroke",t[1]),this},getPercent:function(){return 100*this._value/this._maxValue},getValueFromPercent:function(t){return this._maxValue*t/100},getValue:function(){return this._value},getMaxValue:function(){return this._maxValue},stopUpdate:function(){this.animate&&(clearTimeout(this.animate),this.animate=null)},update:function(t,e){var s,a,r,n,h,u;return!0===t?this._setPercentage(this.getPercent()):this._value==t||isNaN(t)||(void 0===e&&(e=this._duration),a=(s=this).getPercent(),r=1,this._value=Math.min(this._maxValue,Math.max(0,t)),e?(n=s.getPercent(),h=a<n,r+=n%1,t=Math.floor(Math.abs(n-a)/r),u=e/t,function t(e){var i;s.animate&&(clearTimeout(s.animate),s.animate=null),h?a+=r:a-=r,h&&n<=a||!h&&a<=n?o(function(){s._setPercentage(n)}):(o(function(){s._setPercentage(a)}),i=Date.now(),u<=(e=i-e)?t(i):s.animate=setTimeout(function(){t(Date.now())},u-e))}(Date.now())):this._setPercentage(this.getPercent())),this}},a.create=function(t){return new a(t)};var o,s=a;function a(t){var e=t.id;this._el=document.getElementById(e),null!==this._el&&(this._radius=t.radius||10,this._duration=void 0===t.duration?500:t.duration,this._value=0,this._maxValue=t.maxValue||100,this._text=void 0===t.text?function(t){return this.htmlifyNumber(t)}:t.text,this._strokeWidth=t.width||10,this._colors=t.colors||["#EEE","#F00"],this._svg=null,this._movingPath=null,this._wrapContainer=null,this._textContainer=null,this._wrpClass=t.wrpClass||"circles-wrp",this._textClass=t.textClass||"circles-text",this._valClass=t.valueStrokeClass||"circles-valueStroke",this._maxValClass=t.maxValueStrokeClass||"circles-maxValueStroke",this._styleWrapper=!1!==t.styleWrapper,this._styleText=!1!==t.styleText,e=Math.PI/180*270,this._start=-Math.PI/180*90,this._startPrecise=this._precise(this._start),this._circ=e-this._start,this._generate().update(t.value||0))}i.exports=function(t){return s.create(t)}});
define("crm-setting/cleanunit/knob/knob",[],function(t,i,e){function s(t){return this.init(t),this}$.extend(s.prototype,{opts:{el:{},size:100,fgColor:"green",bgColor:"#eee",value:0,barthick:20,bgthick:20,minValue:0,maxValue:100,startPos:"top",clockwise:!0,totaltime:0},init:function(t){this._initOpt(t),this.$canvas=$('<canvas width="'+this.get("size")+'" height="'+this.get("size")+'"></canvas>'),this.ctx=this.$canvas[0].getContext("2d"),this.draw(),this.el.html(this.$canvas)},_initOpt:function(t){this.opts=$.extend(this.opts,t||{}),this.el=this.get("el")instanceof jQuery?this.get("el"):$(this.get("el")),this.posconfig={top:1.5*Math.PI,bottom:.5*Math.PI,left:Math.PI,right:0}},set:function(t,i){this.opts[t]=i},get:function(t){return this.opts[t]},draw:function(){var t=this._getAngle(parseFloat(this.get("value")||0)),i=this.posconfig[this.get("startPos")],e=this.get("clockwise"),t=e?i-t:i+t;this.hasBg||this.drawCircle({thick:this.get("bgthick"),color:this.get("bgColor"),r:this.get("size")/2,start:0,end:2*Math.PI,clockwise:e}),this.drawCircle({thick:this.get("barthick"),color:this.get("fgColor"),r:this.get("size")/2,start:i,end:t,clockwise:e})},_getAngle:function(t){var i=2*Math.PI,e=this.opts.minValue;return(t-e)*i/(this.opts.maxValue-e)},drawCircle:function(t){var i=this.ctx;i.lineWidth=t.thick,i.beginPath(),i.strokeStyle=t.color,i.arc(t.r,t.r,t.r-t.thick/2,t.start,t.end,t.clockwise),i.stroke(),this.hasBg=!0},redraw:function(t){this.set("value",t),this.draw()}}),e.exports=s});
define("crm-setting/cleanunit/setcleanunit/setcleanunit",["crm-widget/dialog/dialog","./tpl-html","paas-vui/sdk","base-vue","crm-widget/table/table"],function(t,e,n){var a=CRM.util,i=t("crm-widget/dialog/dialog"),l=t("./tpl-html"),s=t("paas-vui/sdk"),o=t("base-vue"),u=t("crm-widget/table/table"),c=(s.getObjectApi(),i.extend({attrs:{className:"crm-c-setcleanunit",width:"640px",title:$t("设置清洗规则"),showBtns:!0,showScroll:!0},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"onEnter"},getAllShowFields:function(t){var n=this;return new Promise(function(e){a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName",type:"post",data:{describe_apiname:t,include_layout:!1,include_related_list:!1,get_label_direct:!0},success:function(t){0===t.Result.FailureCode&&(n.objectDescribe=t.Value.objectDescribe,n.allFieldsSet=t.Value.objectDescribe.fields,e(t))},requestStatus:function(){n.hide()}})})},getObjectConfig:function(i){var l=this,t=[i];return new Promise(function(n){a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findObjectConfig",type:"post",data:{apiNameList:t,includeFilters:!0},success:function(t){var e;0===t.Result.FailureCode&&(e=t.Value.config||{},l.objectDescribeConfig=e[i]||{},n(t))},requestStatus:function(){l.hide()}})})},getCleanRules:function(n){var i=this;return new Promise(function(e,t){a.FHHApi({url:"/EM1HNCRM/API/v1/object/clean/service/getCleanRule",type:"post",data:{describe_api_name:n},success:function(t){i.postData={pending_rules:t.Value.clean_rule.pending_rules||{rules:[],show_fields:[]},name:t.Value.clean_rule.name,rule_api_name:t.Value.clean_rule.rule_api_name,effective:!0,enable:t.Value.clean_rule.enable,operator_id:FS.contacts.getCurrentEmployee().id,id:t.Value.id,describe_api_name:n,version:t.Value.version,tenant_id:FS.contacts.getCurrentEmployee().enterpriseAccount,type:"CLEAN"},e(t)},requestStatus:function(){i.hide()}})})},saveCleanRules:function(t){return new Promise(function(e){a.FHHApi({url:"/EM1HNCRM/API/v1/object/clean/service/save",type:"post",data:t,success:function(t){a.remindSuccess(),e(t)}})})},show:function(t){var r=this;r.api_name=t.api_name,Promise.all([r.getCleanRules(r.api_name),r.getAllShowFields(r.api_name),r.getObjectConfig(r.api_name)]).then(function(t){c.superclass.show.call(r),r.$(".dialog-con").html(l({preview_text:""})),s.getComponents().then(function(t){var e=t.Filters.VuiFilters,n=t.VuiTransfer,i=t.Filters.ElementTag,l=t.Filters.convertToServer,a=t.Filters.convertToClient,s=t.FilterConfig;r.filters=new o({el:r.$(".filter-wrap",r.$el)[0],components:{VuiFilters:e,ElementTag:i},template:'<vui-filters :fields="fields" :value="value" :autoCreate="true" @change="handleChange" \n\t\t\t\t\t\t\t:value-model="{value: { field: \'\', value: \'\' },value_meta_data: {}}"\n\t\t\t\t\t\t\t:get-element-tag-props="getElementTagProps" ref="filter">\n\t\t\t\t\t\t\t\t<template slot-scope="{ scope }" :scope="scope">\n\t\t\t\t\t\t\t\t\t\x3c!-- {{ scope }} --\x3e\n\t\t\t\t\t\t\t\t\t<element-tag\n\t\t\t\t\t\t\t\t\t  :item-id="scope.props.itemId"\n\t\t\t\t\t\t\t\t\t  prop="field"\n\t\t\t\t\t\t\t\t\t  key="field"\n\t\t\t\t\t\t\t\t\t  required\n\t\t\t\t\t\t\t\t\t  filterable\n\t\t\t\t\t\t\t\t\t  :rules="scope.rules.field"\n\t\t\t\t\t\t\t\t\t  :value="scope.value.field"\n\t\t\t\t\t\t\t\t\t  v-bind="scope.elementTagProps.field"\n\t\t\t\t\t\t\t\t\t></element-tag>\n\t\t\t\t\t\t\t\t\t<element-tag\n\t\t\t\t\t\t\t\t\t  :item-id="scope.props.itemId"\n\t\t\t\t\t\t\t\t\t  prop="value"\n\t\t\t\t\t\t\t\t\t  key="value"\n\t\t\t\t\t\t\t\t\t  required\n\t\t\t\t\t\t\t\t\t  :rules="scope.rules.value"\n\t\t\t\t\t\t\t\t\t  :value="scope.value.value"\n\t\t\t\t\t\t\t\t\t  v-bind="scope.elementTagProps.value"\n\t\t\t\t\t\t\t\t\t></element-tag>\n\t\t\t\t\t\t\t\t </template>\n\t\t\t\t\t\t\t\t<template slot="add"> +{{ $t("添加清洗条件") }} </template>\n\t\t\t\t\t\t\t</vui-filters>',mounted:function(){this.setPreviewText(r.postData.pending_rules.rules)},methods:{handleChange:function(t){this.value=t;t=l(t,"conditions",function(t){t=t.value||{};return{field_name:t.field,field_value:t.value,connector:"AND"}});r.postData.pending_rules.rules=t,this.setPreviewText(t)},setPreviewText:function(t){var i=this,l="";t.forEach(function(t,e){var n="";t.conditions.forEach(function(t,e){0<e&&(n+=" ".concat(t.connector," ")),n+=(i.getFields(t.field_name)||{label:""}).label}),n="( ".concat(n," )"),0<e&&(l+=" ".concat(t.connector," ")),l+=n,r.$(".preview-text").text(l)})},getFields:function(t){return r.allFieldsSet[t]},getConditionOptions:function(t){var e=(this.getFields(t)||{}).type,n=[{label:$t("精确查询"),value:"PRECISE"},{label:$t("模糊匹配"),value:"FUZZY"}];return["record_type","select_one","phone_number"].includes(e)||"ContactObj"==this.api_name&&["mobile","tel"].includes(t)?[n[0]]:n},getElementTagProps:function(t){var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},t=Object.assign({},t),n=[],i=[],l=(e.value||{}).field,a=t.field.options||[],s=e.filterData||[],r=e.value.field||"";return s.forEach(function(){var t=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).value||{};""!=t.field&&t.field!=r&&n.push(t.field)}),a.forEach(function(t){n.includes(t.value)||i.push(t)}),t.field.options=i,t.value={options:this.getConditionOptions(l),type:"elementSelect"},t}},updated:function(){r.resizedialog()},data:{value:a(r.postData.pending_rules.rules,"conditions",function(t){return{value:{field:t.field_name,value:t.field_value},value_meta_data:{}}}),fields:new s({object:r.objectDescribe,object_describe_config:r.objectDescribeConfig,bussiness_module:"repeat_filter"}).getFilterFields()}}),r.table=new u({$el:r.$(".preview-table"),doStatic:!0,showSize:!1,showMultiple:!1,showFilerBtn:!1,showTerm:!1,showManage:!1,showPage:!1,searchTerm:null,postData:{},columns:[],height:"55px",noSupportLock:!0}),r.table.render(),r.table.doStaticData([]),r.transfer=new o({el:r.$(".transfer-wrap",r.$el)[0],components:{VuiTransfer:n},template:'<vui-transfer v-model="value"\n\t\t\t\t\t\t\t:data="allShowFields"\n\t\t\t\t\t\t\t:titles="[$t(\'全部字段\'), $t(\'显示字段\')]"\n\t\t\t\t\t\t\t:groupable="false"\n\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\tselectAllable\n\t\t\t\t\t\t\tdrag\n\t\t\t\t\t\t\t@change="handleChange"></vui-transfer>',data:{value:r.postData.pending_rules.show_fields},mounted:function(){this.setPreviewTable(r.postData.pending_rules.show_fields)},methods:{handleChange:function(){r.postData.pending_rules.show_fields=this.value,this.setPreviewTable(this.value)},setPreviewTable:function(t){var e=[];t.forEach(function(t){t=r.allFieldsSet[t];e.push({data:t.api_name,dataType:"text",title:$t(t.label),width:100,render:function(){return"XXX"}})}),r.table.renderByColumns(e),r.table.doStaticData([{}])}},computed:{allShowFields:function(){return[{title:"",items:(()=>{var t,e=[],n=r.objectDescribe.fields,i=new s({object:r.objectDescribe,object_describe_config:r.objectDescribeConfig,bussiness_module:"repeat_show_fields"});for(t in n){var l=n[t]||{};!i.getFilterStatus({field:l})||"quote"==l.type&&"quote"==l.type&&["image","file_attachment"].includes(l.quote_field_type)||e.push({key:t,label:l.label||t})}return e})()}]}}})})})},_doLogs:function(t){t.objectIds&&1==t.objectIds.length&&a.uploadLog(t.apiname,this.get("from"),{eventId:"changeowner",eventType:"cl"})},validate:function(){var i=this;return new Promise(function(e,n){i.filters.$refs.filter.validate().then(function(){var t=i.postData.pending_rules;0==t.rules.length?n($t("清洗条件至少一条")):0==t.show_fields.length?n($t("请设置清洗结果显示字段项")):e(!0)}).catch(function(){n($t("清洗条件设置有误"))})})},onEnter:function(t){var e=this,n={duplicate_search:this.postData};this.validate().then(function(){e.saveCleanRules(n)}).catch(function(t){a.remindFail(t)})},hide:function(){var t=c.superclass.hide.call(this);return this.destroy(),t},destroy:function(){return this.initFilters&&this.initFilters.$destroy(),this.initFilters=null,this.initTransfer&&this.initTransfer.$destroy(),this.initTransfer=null,this.table&&this.table.destroy(),this.table=null,c.superclass.destroy.call(this)}}));return c});
define("crm-setting/cleanunit/setcleanunit/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="set-dialog"> <p class="p-title">' + ((__t = $t("清洗数据范围")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("截至到前一天凌晨之前的数据")) == null ? "" : __t) + '</p> <p class="p-title">' + ((__t = $t("设置清洗规则")) == null ? "" : __t) + '</p> <div class="preview"> <p class="label">' + ((__t = $t("预览")) == null ? "" : __t) + '</p> <p class="content preview-text"></p> </div> <div class="filters-area"> <div class="filter-wrap"></div> </div> <p class="p-title">' + ((__t = $t("设置清洗规则")) == null ? "" : __t) + '</p> <div class="preview"> <p class="label">' + ((__t = $t("预览")) == null ? "" : __t) + '</p> <p class="content"><div class="preview-table"></div></p> </div> <div class="filters-area"> <div class="transfer-wrap"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/cleanunit/template/cleanrule-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="rule-item"> <label>' + ((__t = $t("清洗数据范围")) == null ? "" : __t) + "</label> <p>" + ((__t = $t("截止到前一天凌晨之前的数据")) == null ? "" : __t) + '</p> </div> <div class="rule-item"> <label>' + ((__t = $t("客户清洗规则")) == null ? "" : __t) + "</label> <p>" + ((__t = $t("crm.客户名称")) == null ? "" : __t) + " OR " + ((__t = $t("（客户名称 AND 电话）OR（客户名称 AND 地址）OR（网址 AND 电话）OR（网址 AND 地址）")) == null ? "" : __t) + '</p> <table> <thead> <tr> <th class="td-1">' + ((__t = $t("字段")) == null ? "" : __t) + '</th> <th class="td-2">' + ((__t = $t("匹配算法")) == null ? "" : __t) + '</th> <th class="td-4">' + ((__t = $t("说明")) == null ? "" : __t) + '</th> </tr> </thead> <tbody> <tr> <td class="td-1">' + ((__t = $t("crm.客户名称")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("模糊")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("系统自动识别关键信息，用关键词进行模糊匹配。")) == null ? "" : __t) + '</td> </tr> <tr> <td class="td-1">' + ((__t = $t("电话")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("过滤掉特殊符号后，精确匹配。")) == null ? "" : __t) + '</td> </tr> <tr> <td class="td-1">' + ((__t = $t("地址")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("模糊")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("系统自动识别关键信息，用关键词进行模糊匹配。")) == null ? "" : __t) + '</td> </tr> <tr> <td class="td-1">' + ((__t = $t("网址")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("过滤掉前缀“http://”、“https://”，精确匹配。")) == null ? "" : __t) + '</td> </tr> </tbody> </table> </div> <div class="rule-item"> <label>' + ((__t = $t("联系人清洗规则")) == null ? "" : __t) + "</label> <p>" + ((__t = $t("（姓名 AND 客户名称 AND 电话）OR （姓名 AND 客户名称 AND 手机）")) == null ? "" : __t) + '</p> <table> <thead> <tr> <th class="td-1">' + ((__t = $t("字段")) == null ? "" : __t) + '</th> <th class="td-2">' + ((__t = $t("匹配算法")) == null ? "" : __t) + '</th> <th class="td-4">' + ((__t = $t("说明")) == null ? "" : __t) + '</th> </tr> </thead> <tbody> <tr> <td class="td-1">' + ((__t = $t("姓名")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4"></td> </tr> <tr> <td class="td-1">' + ((__t = $t("crm.客户名称")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4"></td> </tr> <tr> <td class="td-1">' + ((__t = $t("电话")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("过滤掉特殊符号后，精确匹配。")) == null ? "" : __t) + '</td> </tr> <tr> <td class="td-1">' + ((__t = $t("手机")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("过滤掉特殊符号后，精确匹配。")) == null ? "" : __t) + '</td> </tr> </tbody> </table> </div> <div class="rule-item"> <label>' + ((__t = $t("销售线索清洗规则")) == null ? "" : __t) + "</label> <p>" + ((__t = $t("（姓名 AND 公司 AND 电话）OR （姓名 AND 公司 AND 手机）")) == null ? "" : __t) + '</p> <table> <thead> <tr> <th class="td-1">' + ((__t = $t("字段")) == null ? "" : __t) + '</th> <th class="td-2">' + ((__t = $t("匹配算法")) == null ? "" : __t) + '</th> <th class="td-4">' + ((__t = $t("说明")) == null ? "" : __t) + '</th> </tr> </thead> <tbody> <tr> <td class="td-1">' + ((__t = $t("姓名")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4"></td> </tr> <tr> <td class="td-1">' + ((__t = $t("公司")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4"></td> </tr> <tr> <td class="td-1">' + ((__t = $t("电话")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("过滤掉特殊符号后，精确匹配。")) == null ? "" : __t) + '</td> </tr> <tr> <td class="td-1">' + ((__t = $t("手机")) == null ? "" : __t) + '</td> <td class="td-2">' + ((__t = $t("精确")) == null ? "" : __t) + '</td> <td class="td-4">' + ((__t = $t("过滤掉特殊符号后，精确匹配。")) == null ? "" : __t) + "</td> </tr> </tbody> </table> </div>";
        }
        return __p;
    };
});
define("crm-setting/cleanunit/template/nav-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="tab-con-start"> <div class="clean-words"> ';
            if (data.data_clean_id && data.status == "Finished") {
                __p += ' <div class="clean-last"> <span>' + ((__t = $t("上次清洗时间")) == null ? "" : __t) + "<em>" + ((__t = FS.moment(data.create_time).format("YYYY-MM-DD HH:mm:ss")) == null ? "" : __t) + '</em></span> <a href="javascript:;" class="j-seeresult see-result">' + ((__t = $t("查看上次清洗结果")) == null ? "" : __t) + "</a> </div> ";
            }
            __p += " ";
            if (data.status == "Cleaning") {
                __p += ' <p class="clean-tip2">' + ((__t = $t("正在执行完毕后以CRM通知的方式告知结果")) == null ? "" : __t) + "</p> ";
            } else {
                __p += ' <p class="clean-tip1">' + ((__t = $t("系统将在第二天00:00-06:00为您清洗所有重复的")) == null ? "" : __t) + " <em>" + ((__t = data.object_name) == null ? "" : __t) + "</em> " + ((__t = $t("数据")) == null ? "" : __t) + " </p> ";
            }
            __p += ' <div class="start-btns"> <a href="javascript:;" class="j-booking crm-btn crm-btn-primary ' + ((__t = data.status == "New" || data.status == "Cleaning" ? "crm-btn-disabled" : "") == null ? "" : __t) + '"> ';
            if (data.status == "New") {
                __p += " " + ((__t = $t("已预约清洗")) == null ? "" : __t) + " ";
            } else if (data.status == "Cleaning") {
                __p += " " + ((__t = $t("清洗中")) == null ? "" : __t) + " ";
            } else {
                __p += " " + ((__t = $t("预约清洗")) == null ? "" : __t) + " ";
            }
            __p += " </a> ";
            if (data.status == "New") {
                __p += ' <a href="javascript:;" class="j-cancel clean-cancel">' + ((__t = $t("取消预约")) == null ? "" : __t) + "</a> ";
            }
            __p += ' </div> </div> <div style="float:right"> <a href="javascript:;" class="clean-rule j-rule">' + ((__t = $t("设置清洗规则")) == null ? "" : __t) + "</a> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/cleanunit/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("清洗工具")) == null ? "" : __t) + '<a class="crm-doclink" href="https://help.fxiaoke.com/dbde/673b/f02d/eb49" target="_blank"></a></span> </h2> </div> <div class="crm-module-con" data-status="' + ((__t = status) == null ? "" : __t) + '"> <div class="crm-tab b-g-clean j-tab"> <span class="' + ((__t = object_api_name == "AccountObj" ? "cur" : "") == null ? "" : __t) + '" data-type="2" data-apiname="AccountObj">' + ((__t = $t("crm.客户")) == null ? "" : __t) + '</span> <span class="' + ((__t = object_api_name == "ContactObj" ? "cur" : "") == null ? "" : __t) + '" data-type="3" data-apiname="ContactObj">' + ((__t = $t("crm.联系人")) == null ? "" : __t) + '</span> <span class="' + ((__t = object_api_name == "LeadsObj" ? "cur" : "") == null ? "" : __t) + '" data-type="1" data-apiname="LeadsObj">' + ((__t = $t("crm.销售线索")) == null ? "" : __t) + '</span> </div> <div class="crm-tab tab-mask"></div> <div class="tab-con"></div> </div>';
        }
        return __p;
    };
});