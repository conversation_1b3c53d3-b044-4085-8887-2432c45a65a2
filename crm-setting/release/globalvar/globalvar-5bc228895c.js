define("crm-setting/globalvar/add/add-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<ul class="field-type-list"> ';
            _.each(fieldTypes, function(fieldType) {
                __p += ' <li data-type="' + ((__t = fieldType.value) == null ? "" : __t) + '" data-prompt="' + ((__t = fieldType.prompt) == null ? "" : __t) + '">' + ((__t = fieldType.label) == null ? "" : __t) + "</li> ";
            });
            __p += ' </ul> <div class="form-container"> <div class="field-type-desc"> <h1>' + ((__t = $t("单行文本")) == null ? "" : __t) + '</h1><p></p> </div> <div class="form-content"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/globalvar/add/add",["./../fieldtype","crm-widget/dialog/dialog","crm-modules/common/util","./add-html","./btns-html"],function(t,e,i){var n=t("./../fieldtype"),a=t("crm-widget/dialog/dialog"),s=t("crm-modules/common/util"),l=t("./add-html"),o=t("./btns-html"),d="text",r=a.extend({attrs:{width:620,title:$t("添加全局变量"),showBtns:!0,showScroll:!1,content:'<div class="content"></div>',className:"crm-s-globalvar add-dialog"},events:{"click .field-type-list li":"onSwitchFieldType"},initialize:function(){r.superclass.initialize.call(this),this.on("save",function(){this.save()}),this.on("saveAgain",this.saveAgain),this.on("cancel",this.hide)},show:function(t){r.superclass.show.call(this),this.initBtns();var e=$(this.element);e.find(".content").html(l({fieldTypes:n})),e.find(".field-type-list li:first").click()},onSwitchFieldType:function(t){var t=$(t.target),e=t.data("type"),i=t.data("prompt"),n=t.text();t.hasClass("active")||(d=e,t.addClass("active").siblings(".active").removeClass("active"),this.renderRightContent({type:e,prompt:i,text:n}))},renderRightContent:function(t){this.setFieldTypeDesc(t),this.initForm(t)},setFieldTypeDesc:function(t){var e=$(this.element).find(".field-type-desc");e.find("h1").html(t.text),e.find("p").html(t.prompt)},initBtns:function(){$(this.element).find(".dialog-btns").html(o())},initForm:function(t){var e,t=t.type,i=this,n=(t="longtext"===(t=t.replace("_",""))?"textarea":t,$(this.element).find(".form-content")),a=(n.html(""),{type:"form."+t,label:$t("默认值"),api_name:"value",is_required:!0,default_value:""});"textarea"===t?(e=FS.contacts.getCurrentEmployee()||{},a.max_length=["74255","obj0509","fktest087","gfkj888"].includes(e.enterpriseAccount)?2e3:500):_.contains(["number","currency"],t)?a=_.extend(a,{length:10,decimal_places:6}):_.contains(["datetime","date","time"],t)&&(a=_.extend(a,{service_ins:{formate:{datetime:"yyyy-MM-dd hh:mm",date:"yyyy-MM-dd",time:"hh:mm"}[t]}})),this.form&&this.form.destroy(),n.Spark({type:"form.componentform",option:{fields:[{type:"form.text",label:$t("变量名称"),api_name:"label",is_required:!0,default_value:""},{type:"form.text",label:"API Name",api_name:"api_name",is_required:!0,pattern:"^[a-zA-Z](_?(?!_)\\w)*__g$",default_value:i.getApiName("var")},a,{type:"form.textarea",label:$t("备注"),api_name:"remarks",max_length:500,default_value:""}]}},["datepicker","form"]).then(function(t){i.form=t,i.resizedialog()})},validate:function(t){t&&this.form.check().then(function(){t(!0)},function(){t(!1)})},getData:function(e){this.form.getValues().done(function(t){t=_.extend(t,{define_type:"custom",type:d}),e&&e(t)})},getApiName:function(t){return(t=t?t+"_":"")+CRM.util.getUUId(5)+"__g"},save:function(e){var i=this;i.adding||i.validate(function(t){t&&i.getData(function(t){i.adding=!0,s.FHHApi({url:"/EM1HNCRM/API/v1/object/global_variable/service/create",data:{json_data:JSON.stringify(t)},success:function(t){0===t.Result.StatusCode&&(i.created=!0,e?i.form.reset():(i.hide(),i.trigger("created")))},error:function(){s.alert($t("网络错误请重试！"))},complete:function(){i.adding=!1}})})})},saveAgain:function(){this.save(!0)},hide:function(){r.superclass.hide.call(this),this.created&&this.trigger("created")}});i.exports=r});
define("crm-setting/globalvar/add/btns-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<span class="b-g-btn-cancel" action-type="cancel">' + ((__t = $t("取 消")) == null ? "" : __t) + '</span> <span class="b-g-btn" action-type="saveAgain">' + ((__t = $t("保存并继续添加")) == null ? "" : __t) + '</span> <span class="b-g-btn" action-type="save">' + ((__t = $t("保存")) == null ? "" : __t) + "</span>";
        }
        return __p;
    };
});
define("crm-setting/globalvar/edit/edit-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="form-container"> <div class="field-type-desc"> <h1>' + ((__t = $t("单行文本")) == null ? "" : __t) + '</h1><p></p> </div> <div class="form-content"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/globalvar/edit/edit",["./../fieldtype","crm-widget/dialog/dialog","crm-modules/common/util","./edit-html"],function(e,t,i){var a=e("./../fieldtype"),n=e("crm-widget/dialog/dialog"),l=e("crm-modules/common/util"),o=e("./edit-html"),r=n.extend({attrs:{width:420,title:$t("编辑全局变量"),showBtns:!0,showScroll:!1,content:'<div class="content"></div>',className:"crm-s-globalvar edit-dialog"},initialize:function(){r.superclass.initialize.call(this),this.on("dialogEnter",function(){this.save()}),this.on("dialogCancel",this.hide)},show:function(e){this.data=e,r.superclass.show.call(this),$(this.element).find(".content").html(o()),this.setFieldTypeDesc(e.type),this.initForm(e)},setFieldTypeDesc:function(e){var e=_.findWhere(a,{value:e})||{},t=$(this.element).find(".field-type-desc");t.find("h1").html(e.label),t.find("p").html(e.prompt)},initForm:function(e){var t,i=e.type,a=this,n=(i="longtext"===(i=i.replace("_",""))?"textarea":i,$(this.element).find(".form-content")),l=(n.html(""),{type:"form."+i,label:$t("默认值"),api_name:"value",is_required:!0,default_value:e.value||""});"textarea"===i?(t=FS.contacts.getCurrentEmployee()||{},l.max_length=["74255","obj0509","fktest087","gfkj888"].includes(t.enterpriseAccount)?2e3:500):_.contains(["number","currency"],i)?l=_.extend(l,{length:10,decimal_places:6}):_.contains(["datetime","date","time"],i)&&(l=_.extend(l,{service_ins:{formate:{datetime:"yyyy-MM-dd hh:mm",date:"yyyy-MM-dd",time:"hh:mm"}[i]}})),a.form&&a.form.destroy(),n.Spark({type:"form.componentform",option:{fields:[{type:"form.text",label:$t("变量名称"),api_name:"label",is_required:!0,default_value:e.label||""},{type:"form.text",label:"API Name",api_name:"api_name",is_required:!0,is_readonly:!0,pattern:"^[a-zA-Z](_?(?!_)\\w)*__g$",default_value:e.api_name||""},l,{type:"form.textarea",label:$t("备注"),api_name:"remarks",max_length:500,default_value:e.remarks||""}]}},["datepicker","form"]).then(function(e){a.form=e,a.resizedialog()})},getData:function(t){var i=this;this.form.getValues().done(function(e){e=_.extend(i.data,e),t&&t(e)})},validate:function(e){e&&this.form.check().then(function(){e(!0)},function(){e(!1)})},save:function(e){var t=this;t.editing||t.validate(function(e){e&&t.getData(function(e){t.editing=!0,l.FHHApi({url:"/EM1HNCRM/API/v1/object/global_variable/service/update",data:{json_data:JSON.stringify(e)},success:function(e){0===e.Result.StatusCode&&(t.hide(),t.trigger("updated"))},error:function(){l.alert($t("网络错误请重试！"))},complete:function(){t.editing=!1}})})})},destroy:function(){$(this.element).off().remove(),r.superclass.destroy.call(this)}});i.exports=r});
define("crm-setting/globalvar/fieldtype",[],function(t,e,l){l.exports=[{label:$t("单行文本"),prompt:$t("适用于填写简短的文字如姓名"),value:"text"},{label:$t("多行文本"),prompt:$t("适用于填写大段的文字如备注建议"),value:"long_text"},{label:$t("数字"),prompt:$t("适用于填写数字如年龄订购数量"),value:"number"},{label:$t("金额"),prompt:$t("适用于填写数字带千分位"),value:"currency"},{label:$t("日期"),prompt:$t("适用于选择特定日期2099-12-31"),value:"date"},{label:$t("时间"),prompt:$t("适用于选择特定时间1631"),value:"time"},{label:$t("日期时间"),prompt:$t("适用于选择特定唯一的时间点2099-12-31 1631"),value:"date_time"}]});
define("crm-setting/globalvar/globalvar",["crm-widget/dialog/dialog","crm-modules/common/util","crm-widget/table/table"],function(t,e,a){var i=t("crm-widget/dialog/dialog"),n=t("crm-modules/common/util"),l=t("crm-widget/table/table"),o={date:$t("日期"),time:$t("时间"),date_time:$t("日期时间"),text:$t("单行文本"),long_text:$t("多行文本"),number:$t("数字"),currency:$t("金额")},d=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper),this.initDesc()},events:{"click .j-add-var":"onAdd"},initDesc:function(){this.$el.html('<div class="globalvar-box"><div class="my-tb-wrap"></div><div class="my-tb-info"></div></div>'),this.initTb()},initTb:function(){var i=this;i.tb=new l({$el:i.$(".my-tb-wrap"),url:"/EM1HNCRM/API/v1/object/global_variable/service/findGlobalVariableList",requestType:"FHHApi",showMoreBtn:!1,showFilerBtn:!1,showPage:!1,searchTip:$t("对象"),title:$t("全局变量")+'<span data-pos="bottom" data-title='+$t("crm.能够被引用的字段类型")+' class="crm-doclink crm-ui-title">222</span>',search:{placeHolder:$t("搜索变量名称"),type:"label",highFieldName:"label"},operate:{btns:[{text:$t("添加变量"),className:"j-add-var"}]},columns:[{data:"label",title:$t("变量名称"),width:250,fixed:!0},{data:"api_name",title:"API Name"},{data:"value",title:$t("变量值")},{data:"type_str",title:$t("变量类型")},{data:"create_time",title:$t("创建时间"),dataType:4},{data:"remarks",title:$t("备注")},{data:"Define_type",title:$t("操作"),lastFixed:!0,render:function(t,e,a){return"system"!==a.define_type?'<a href="javascript:;" class="j-edit">'+$t("编辑")+'</a><a href="javascript:;" class="j-delete">'+$t("删除")+"</a>":""}}],paramFormat:function(t){return _.extend(t,{realTimeTrans:!0}),t},formatData:function(t){t=(t||{}).globalVariableList||[];return{data:_.map(t,function(t){return t.create_time=new Date(t.create_time).getTime(),t.type_str=o[t.type],t})}}}),i.tb.on("trclick",function(t,e,a){a.hasClass("j-delete")?i.onDelete(t):a.hasClass("j-edit")&&i.onEdit(t)})},onAdd:function(){var e=this;t.async("./add/add",function(t){e.addDialog||(e.addDialog=new t,e.addDialog.on("created",function(){e.tb&&e.tb.setParam({},!0)})),e.addDialog.show()})},onEdit:function(e){var a=this;t.async("./edit/edit",function(t){a.editDialog||(a.editDialog=new t(e),a.editDialog.on("updated",function(){a.tb&&a.tb.setParam({},!0)})),a.editDialog.show(e)})},onDelete:function(t){var e=this;var a=new i({title:$t("提示"),showBtns:!0,content:'<div class="content">'+$t("您确定要删除{{label}}吗",{label:t.label.replace(/[&<>"']/g,function(t){return{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}[t]})})+"</div>"});a.on("dialogCancel",function(){a&&a.destroy()}),a.on("dialogEnter",function(){n.FHHApi({url:"/EM1HNCRM/API/v1/object/global_variable/service/delete",data:{apiName:t.api_name},success:function(t){0===t.Result.StatusCode&&e.tb&&e.tb.setParam({},!0)},error:function(){n.alert($t("网络错误请重试！"))}}),a&&a.destroy()}),a.show()},destroy:function(){}});a.exports=d});