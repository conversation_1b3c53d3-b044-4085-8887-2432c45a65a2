define("crm-setting/indexmanage/indexmanage",["./manage/indexmanage"],function(e,n,a){var i=e("./manage/indexmanage"),e=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(e){this.initManage(e)},initManage:function(e){this.manage=new i({wrapper:this.$el})},destroy:function(){this.manage.destroy&&this.manage.destroy()}});a.exports=e});
define("crm-setting/indexmanage/manage/indexmanage",["crm-modules/common/util","./templete/tpl-html","./templete/config-html"],function(e,t,i){var n=e("crm-modules/common/util"),s=e("./templete/tpl-html"),a=e("./templete/config-html"),o=Backbone.View.extend({events:{"click .is-allow":"onChoose","click .j-item":"tabClick","click .is-allow__pspage":"updatePspageStatus"},initialize:function(e){this.setElement(e.wrapper),this.render()},render:function(){this.$el.html(s()),this.initList(),this.initConfig(),this.$item=$(".j-item",this.$el),this.$itemCon=$(".item-con",this.$el)},initList:function(){var t=this,i=document.createElement("div");e.async("paas-appcustomization/sdk",function(e){e.crmIndex().then(function(e){$(".index-manage-list").html(i),t.instance=e.init(i,{})})})},initConfig:function(){var i=this;i._getConfig(function(t){i._getPageConfig(function(e){$(".index-manage-config",i.$el).html(a({isAllow:t,isPersonPage:e}))})})},_getConfig:function(t){n.FHHApi({url:"/EM1HBICRM/billBoardController/getShowConfig",success:function(e){0==e.Result.StatusCode&&t&&t(e.Value.isAllow)}})},_getPageConfig:function(t){n.FHHApi({url:"/EM1HWebPage/Homepage/queryPersonPageConfig",success:function(e){0==e.Result.StatusCode&&t&&t(e.Value.enablePersonPage)}})},myRemind:function(e){0==e.Result.StatusCode?n.remind(1,$t("设置成功")):n.remind(3,$t("设置失败"))},onChoose:function(e){var t=this;n.FHHApi({url:"/EM1HBICRM/billBoardController/saveShowConfig",data:{isAllow:$(e.currentTarget).data("value")},success:function(e){t.myRemind(e)}})},updatePspageStatus:function(e){var t=this;$(e.currentTarget).data("enablepage")?n.FHHApi({url:"/EM1HWebPage/Homepage/enablePersonPage",success:function(e){t.myRemind(e)}}):n.FHHApi({url:"/EM1HWebPage/Homepage/disablePersonPage",success:function(e){t.myRemind(e)}})},tabClick:function(e){e=$(e.currentTarget).data("index");this.$item.removeClass("cur").eq(e).addClass("cur"),this.$itemCon.addClass("hide").eq(e).removeClass("hide")},destroy:function(){this.instance&&this.instance.$destroy()}});i.exports=o});
define("crm-setting/indexmanage/manage/templete/config-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("crm.排行榜说明")) == null ? "" : __t) + '</p> </div> </div> <div class="content"> <div class="title">' + ((__t = $t("排行榜")) == null ? "" : __t) + '</div> <p class="mn-radio-box">' + ((__t = $t("是否允许员工查看排行指标的数值")) == null ? "" : __t) + '<span class="mn-radio-item is-allow ' + ((__t = isAllow ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span><span class="label">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item is-allow ' + ((__t = isAllow ? "" : "mn-selected") == null ? "" : __t) + '" data-value="0"></span><span class="label">' + ((__t = $t("否")) == null ? "" : __t) + '</span> </p> </div> <div class="content person-page"> <div class="title">' + ((__t = $t("个人级首页")) == null ? "" : __t) + '</div> <p class="mn-radio-box">' + ((__t = $t("是否允许员工使用个人级首页")) == null ? "" : __t) + '<span class="mn-radio-item is-allow__pspage ' + ((__t = isPersonPage ? "mn-selected" : "") == null ? "" : __t) + '" data-enablePage="1"></span><span class="label">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item is-allow__pspage ' + ((__t = isPersonPage ? "" : "mn-selected") == null ? "" : __t) + '" data-enablePage="0"></span><span class="label">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </p> </div>";
        }
        return __p;
    };
});
define("crm-setting/indexmanage/manage/templete/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="index-manage-warpper"> <div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("uipaas.CRM.home", null, "CRM首页")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-tab"> <a class="item j-item cur" data-index="0" href="javascript:;">' + ((__t = $t("首页管理")) == null ? "" : __t) + '</a> <a class="item j-item" data-index="1" href="javascript:;">' + ((__t = $t("首页设置")) == null ? "" : __t) + '</a> </div> <div class="tab-con"> <div class="item-con index-manage-list"></div> <div class="item-con index-manage-config hide"></div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/indexmanage/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("首页管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <p>1." + ((__t = $t("crm.排行榜说明")) == null ? "" : __t) + '</p> </div> </div> <div class="content"> <div class="title">' + ((__t = $t("排行榜")) == null ? "" : __t) + '</div> <p class="mn-radio-box">' + ((__t = $t("是否允许员工查看排行指标的数值")) == null ? "" : __t) + '<span class="mn-radio-item ' + ((__t = isAllow ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span><span class="label">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="mn-radio-item ' + ((__t = isAllow ? "" : "mn-selected") == null ? "" : __t) + '" data-value="0"></span><span class="label">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </p> </div> </div>";
        }
        return __p;
    };
});