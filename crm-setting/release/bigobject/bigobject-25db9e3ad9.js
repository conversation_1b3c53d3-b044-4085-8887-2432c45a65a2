define("crm-setting/bigobject/bigobject",["crm-modules/common/util","crm-widget/table/table","paas-object/sdk.js"],function(e,t,i){var a=e("crm-modules/common/util"),n=e("crm-widget/table/table"),s=e("paas-object/sdk.js"),e=Backbone.View.extend({initialize:function(e){var t=a.getTplQueryParams()||{};this.setElement(e.wrapper),this.oObjectSDK=new s,this.useableDescribeCount=1,this.keyword="",t.api_name?this.jumpObjectDetail(t):this.initDesc()},loadingTpl:_.template('<div class="myobject-loading"></div>'),events:{"click .j-designadd":"onDesignAdd"},initDesc:function(){this.$el.html('<div class="myobject-box"><div class="my-tb-wrap"></div><div class="my-tb-info"></div></div>'),this.initTb()},initTb:function(){var s=this,e=[{text:$t("新建"),className:"j-designadd",isFold:!1}];s.tb=new n({$el:s.$(".my-tb-wrap"),tableName:"inventory",url:"/EM1HCRMUdobj/objectDescribe/findByTenantId",postData:{tenant_id:""},requestType:"FHHApi",showMultiple:!1,trHandle:!1,searchTip:$t("对象"),title:$t("object.big.object"),search:{placeHolder:$t("搜索{{getMainTitle}}",{data:{getMainTitle:$t("big.object")}}),type:"Keyword",highFieldName:"Name"},openStart:!0,showPage:!1,operate:{moreBtnClass:"crm-btn",btns:e},columns:[{data:"Name",title:$t("对象名称"),width:250,render:function(e,t,i){e='<a href="javascript:;" class="j-detail" title="'+(_.escape(i.Name)||"--")+'">'+e+"</a>";return i&&"public_big"==i.visibleScope&&(e+='<span class="el-tag fx-tag el-tag--link el-tag--small el-tag--light" style="line-height:19px;height:19px;margin-left:4px;border:0;">Public</span>'),e}},{data:"ApiName",title:"API Name"},{data:"Desc",title:$t("描述")},{data:"CreatedBy",title:$t("创建人"),render:function(e){e=FS.contacts.getEmployeeById(e)||{};return e.fullName||e.name||"--"}},{data:"CreateTime",title:$t("创建时间"),dataType:4},{data:"LastModifiedBy",title:$t("最后修改人"),render:function(e){e=FS.contacts.getEmployeeById(e)||{};return e.fullName||e.name||"--"}},{data:"LastModifiedTime",title:$t("最后修改时间"),dataType:4},{data:"Status",title:$t("状态")},{data:"Define_type",title:$t("操作"),lastFixed:!0,width:170,render:function(){var e=arguments[2]||{},t=["disable","enable"][+!e.IsActive],i=[$t("禁用"),$t("启用")][+!e.IsActive],a="",e=(s.objectConfigs||{})[e.ApiName]||{};return 0==(null===e||null==(e=e.object)?void 0:e.controlLevel)?a="":a+='<a href="javascript:;" class="j-object-btn j-'+t+'">'+i+'</a><a href="javascript:;" class="j-object-btn j-delete">'+$t("删除")+"</a></a>",a}}],formatData:function(e){return null},initComplete:function(){s.keyword?(s.tb.$el.find(".dt-ipt").val(s.keyword),s.tb.$el.find(".dt-sc-btn").trigger("click")):s.searchObjectList()}}),s.tb.on("dt.search",function(e){s.keyword=e,s.searchObjectList()}),s.tb.on("trclick",function(e,t,i){0<i.closest(".j-detail").length?s.showDetail(e):i.hasClass("j-delete")?s.onDelete(e):i.hasClass("j-enable")?s.onEnableOrDisable(e,!0):i.hasClass("j-disable")&&s.onEnableOrDisable(e,!1)})},refresh:function(e){CRM.control.refreshAside(),this.searchObjectList()},searchObjectList:function(){var l=this;l.tb.showLoading(),a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeManageList",data:{onlyVisibleScope:!0,includeBigObject:!0,isIncludeSystemObj:!1,isIncludeFieldDescribe:!1,describeDefineType:"custom",isIncludeUnActived:!0,sourceInfo:"object_management",packageName:"CRM",includeControlLevel:!0},success:function(e){var e=e.Value||{},t=e.objectDescribeList,t=void 0===t?[]:t,i=e.useableDescribeCount,i=void 0===i?0:i,a=e.manageGroup,s=void 0===a?{}:a,a=e.objectConfigs,e=void 0===a?{}:a,n=[],t=t.filter(function(e){return!1===s.all&&s.apiNames&&s.apiNames.includes(e.api_name)||!1!==s.all}),a=(l.objectConfigs=e,l.objectDescribeList=t,_.each(t,function(e){var t={Name:e.display_name,ApiName:e.api_name,OriginalDescribeApiName:e.original_describe_api_name,CreatedBy:e.created_by,CreateTime:new Date(e.create_time).getTime(),Desc:e.description,Status:[$t("禁用"),$t("启用")][+(e.is_active||0)],IsActive:e.is_active,Define_type:e.define_type,LastModifiedBy:e.last_modified_by,LastModifiedTime:e.last_modified_time};(""==l.keyword||-1<(e.display_name||"").toLowerCase().indexOf((l.keyword||"").toLowerCase()))&&n.push(t)}),l.tb.hideLoading(),l.tb.doStaticData(n),$t("paas.current.number",{label:$t("big.object"),length:n.length}));_.isNumber(i)&&(a+="&nbsp&nbsp|&nbsp&nbsp"+$t("paas.remain.available",{label:$t("big.object"),useableDescribeCount:i}),l.useableDescribeCount=i),l.$(".my-tb-info").html(a)}})},jumpObjectDetail:function(e){this.showDetail({Name:e.display_name,ApiName:e.api_name,ChildType:e.child_type,SubChildType:e.sub_child_type,visible_scope:"big"})},showDetail:function(t){var i=this;i.$el.append(this.loadingTpl()),this.oObjectSDK.getDetail({display_name:t.Name,api_name:t.ApiName,visible_scope:t.visible_scope||"big"},function(e){i.$el.find(".myobject-loading").remove();e=e.defaultView({child_type:t.ChildType,sub_child_type:t.SubChildType});e.on("go_back",function(){i.initDesc()}),e.render(),i.$el.html(e.$el),e.resetTab()})},onDelete:function(e){var t=this;t.oObjectSDK.deleteObject({api_name:e.ApiName,is_active:e.IsActive,visible_scope:"big"},function(e){"success"===e&&t.refresh()})},onEnableOrDisable:function(e,t){var i=this;i.oObjectSDK.enableOrDisableObject({api_name:e.ApiName,is_active:t,visible_scope:"big"},function(e){"success"==e&&i.refresh()})},onDesignAdd:function(){var t,i=this;i.useableDescribeCount<=0?a.alert($t("paas.object.license",{name:$t("big.object")})):(t=function(e){e&&(CRM.control.refreshAside(),i.showDetail({Name:e.objectDescribe.display_name,ApiName:e.objectDescribe.api_name,visible_scope:"big"}))},i.$el.append(this.loadingTpl()),this.oObjectSDK.getNewLayoutDesigner().then(function(e){i.$el.find(".myobject-loading").remove(),e.init({visible_scope:"big"}).$on("close",t)}),FS.log&&FS.log("s-paasobj_create_bigobject_designer","cl",{module:"s-paasobj",subModule:"designer"}))},destroy:function(){this.remove()}});i.exports=e});