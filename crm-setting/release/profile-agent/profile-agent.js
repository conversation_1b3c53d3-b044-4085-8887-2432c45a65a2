function asyncGeneratorStep(e,t,i,n,r,o,a){try{var s=e[o](a),u=s.value}catch(e){return void i(e)}s.done?t(u):Promise.resolve(u).then(n,r)}function _asyncToGenerator(s){return function(){var e=this,a=arguments;return new Promise(function(t,i){var n=s.apply(e,a);function r(e){asyncGeneratorStep(n,t,i,r,o,"next",e)}function o(e){asyncGeneratorStep(n,t,i,r,o,"throw",e)}r(void 0)})}}define("crm-setting/profile-agent/profile-agent",[],function(e,t,i){i.exports=Backbone.View.extend({initialize:function(){},render:function(){var e=[{label:$t("sfa.aiCustomerProfileSetting.targetIndustries"),prop:"target_industries",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.targetIndustriesPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.targetIndustriesRequired")}]},{label:$t("sfa.aiCustomerProfileSetting.prosperousIndustries"),prop:"prosperous_industries",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.prosperousIndustriesPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.prosperousIndustriesRequired")}]},{label:$t("sfa.aiCustomerProfileSetting.targetProducts"),prop:"target_products",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.targetProductsPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.targetProductsRequired")}]},{label:$t("sfa.aiCustomerProfileSetting.serviceModels"),prop:"service_models",component:"fx-input",defaultValue:"",placeholder:$t("sfa.aiCustomerProfileSetting.serviceModelsPlaceholder"),rules:[{required:!0,message:$t("sfa.aiCustomerProfileSetting.serviceModelsRequired")}]},{prop:"customer_profile_agent",component:"fx-switch",defaultValue:!1,isShowForm:!1}],t=[$t("sfa.aiCustomerProfileSetting.featureSettingTips1"),$t("sfa.aiCustomerProfileSetting.featureSettingTips2"),$t("sfa.aiCustomerProfileSetting.featureSettingTips3"),$t("sfa.aiCustomerProfileSetting.featureSettingTips4"),$t("sfa.aiCustomerProfileSetting.featureSettingTips5")],i={},n={},r=[];_.each(e,function(e){i[e.prop]=e.rules,n[e.prop]=e.defaultValue,r.push(e.prop)}),FxUI.create({wrapper:this.options.wrapper[0],template:'\n                    <div class="profile-configure-container">\n                        <div class="profile-configure-title">\n                            <span>{{ title }}</span>\n                        </div>\n                        <div class="profile-configure-content">\n                            <div class="profile-configure-item">\n                                <div class="content">\n                                    <div class="feature-setting-tip">\n                                        <span class="fx-icon-f-info icon"></span>\n                                        <div class="feature-setting-tip-content">\n                                            <p v-for="(item, index) in featureSettingTips" :key="index" class="feature-setting-tip-item">\n                                                <span class="index">{{ index + 1 }}.</span>\n                                                <span>{{ item }}</span>\n                                            </p>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                            \n                            <div class="profile-configure-item open-status">\n                                <div class="profile-configure-item-header">\n                                    <span class="profile-configure-item-header-title">{{ title }}</span>\n                                    <div class="profile-configure-item-header-content">\n                                        <span class="status-tip">{{ openProfileAgentTip }}</span>\n                                        <fx-switch size="small" :disabled="openOrOpeningProfileAgentStatus" :value="openOrOpeningProfileAgentStatus" @change="openProfileAgent" />\n                                    </div>\n                                </div>\n                                <div class="content">\n                                    <p>{{ openStatusDesc }}</p>\n                                </div>\n                            </div>\n                            <div class="profile-configure-item" v-if="openProfileAgentStatus">\n                                <div class="profile-configure-item-header">\n                                    <span class="profile-configure-item-header-title">{{ featureSettingFormTitle }}</span>\n                                </div>\n                                <div class="content">\n                                    <fx-form size="mini" lineSize="micro" ref="featureSettingForm" :rules="featureSettingFormRules" :model="featureSettingFormData">\n                                            <fx-form-item v-for="item in featureSettingFormProps" :key="item.prop" :label="item.label" :prop="item.prop">\n                                                <fx-input v-model="featureSettingFormData[item.prop]" :placeholder="item.placeholder" />\n                                            </fx-form-item>\n                                            <fx-button type="primary" size="mini" @click="submitFeatureSettingForm" :loading="featureSettingFormLoading">{{ $t(\'保存\') }}</fx-button>\n                                        </fx-form>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                ',data:function(){return{title:$t("sfa.aiCustomerProfileSetting.title"),openStatusDesc:$t("sfa.aiCustomerProfileSetting.openStatusDesc"),featureSettingTips:t,featureSettingFormTitle:$t("sfa.aiCustomerProfileSetting.featureSettingFormTitle"),featureSettingFormData:n,featureSettingFormProps:e.filter(function(e){return"customer_profile_agent"!==e.prop}),featureSettingFormRules:i,featureSettingFormLoading:!1}},computed:{openOrOpeningProfileAgentStatus:function(){return"0"!==this.featureSettingFormData.customer_profile_agent},openProfileAgentStatus:function(){return"1"===this.featureSettingFormData.customer_profile_agent},openProfileAgentTip:function(){return"2"===this.featureSettingFormData.customer_profile_agent?$t("正在开启中，请耐心等待"):$t("crm.setting.tradeconfigure.warn_cannot_closed")}},created:function(){this.getFeatureSettingValue()},methods:{getFeatureSettingValue:function(){var i=this;CRM.util.showLoading_new(),CRM.util.getConfigValues(r).then(function(e){_.each(e,function(e){var t=e.key,e=e.value;void 0!==e&&(i.featureSettingFormData[t]=e)})},function(e){CRM.util.alert(e)}).always(function(){CRM.util.hideLoading_new()})},submitFeatureSettingForm:function(){var e,i=this;null!=(e=this.$refs.featureSettingForm)&&e.validate&&this.$refs.featureSettingForm.validate(function(e){var t;e&&(t=[],_.each(i.featureSettingFormProps,function(e){t.push({key:e.prop,value:i.featureSettingFormData[e.prop]})}),i.setConfigValues(t))})},openProfileAgent:function(t){var i=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(!0===t)return e.next=3,i.setConfigValues([{key:"customer_profile_agent",value:"2"}]);e.next=4;break;case 3:i.getFeatureSettingValue();case 4:case"end":return e.stop()}},e)}))()},setConfigValues:function(e){return new Promise(function(t,i){CRM.util.showLoading_new(),CRM.util.setConfigValues(e).then(function(e){CRM.util.remind(1,$t("设置成功")),t(!0)},function(e){CRM.util.remind(3,e||$t("设置失败")),i(e)}).always(function(){CRM.util.hideLoading_new()})})}}})}})});