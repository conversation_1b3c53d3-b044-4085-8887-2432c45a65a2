define("crm-setting/promotion/promotion-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<!-- * @Descripttion: * @Author: LiAng * @Date: 2020-08-20 14:27:23 * @LastEditors: LiAng * @LastEditTime: 2020-08-20 14:54:49 --> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>1." + ((__t = $t("crm.促销启用后")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.订单参加促销后")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.促销活动暂时只能在企业互联的订货通中使用")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("促销一旦启用将无法停用。")) == null ? "" : __t) + "</li> </ul> </div> ";
            if (start === "error") {
                __p += ' <p><span class="pp-set-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span><a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + "</a></p> ";
            } else {
                __p += " ";
                if (start) {
                    __p += " <span>" + ((__t = $t("促销开关已开启并已完成初始化")) == null ? "" : __t) + "</span> ";
                }
                __p += ' <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("启用促销")) == null ? "" : __t) + '</label> <div class="switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"></div> </div> ';
                if (start) {
                    __p += ' <div class="pp-tip crm-gray-9">' + ((__t = $t("促销已开启建议先去后台设置")) == null ? "" : __t) + '<a href="#crmmanage/=/module-sysobject">' + ((__t = $t("crm.促销")) == null ? "" : __t) + "</a>" + ((__t = $t("对象的字段布局等信息。")) == null ? "" : __t) + "</div> ";
                }
                __p += " ";
            }
            __p += " ";
            if (isWhiteList) {
                __p += ' <div class="promotion-order"> <div class="mn-radio-title">' + ((__t = $t("PC端启用行业订单")) == null ? "" : __t) + '</div> <p class="mn-radio-explain" >' + ((__t = $t("开启后创建、编辑订单都会适配促销规则")) == null ? "" : __t) + '</p> <div class="pp-switch"> <span class="switch-sec ' + ((__t = data["49"] && data[49][0] == "1" ? "on" : "") == null ? "" : __t) + ' j-switch-promotion "></span> </div> <div class="orderrule-promotion-checkbox mn-checkbox-box ' + ((__t = data["49"] && data[49][0] == "1" ? "" : "hide") == null ? "" : __t) + '"> <p class="orderrule-check-item"> <span data-key="46" class="mn-checkbox-item j-checkbox-item ' + ((__t = data["46"] && data["46"][0] == "1" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor:pointer;">' + ((__t = $t("支持编辑订单产品")) == null ? "" : __t) + '</span> </p> <p class="orderrule-check-item"> <span data-key="50" class="mn-checkbox-item j-checkbox-item ' + ((__t = data["50"] && data["50"][0] == "1" ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="label" style="margin-left:8px;cursor:pointer;">' + ((__t = $t("编辑订单仍适配促销")) == null ? "" : __t) + '</span> </p> </div> </div> <div class="promotion-order"> <div class="mn-radio-title">' + ((__t = $t("移动端启用行业订单")) == null ? "" : __t) + '</div> <p class="mn-radio-hint">' + ((__t = $t("请注意：移动端使用该功能，需升级至6.7版本。开启后，移动端为行业订单，非标准订单")) == null ? "" : __t) + '</p> <div class="pp-switch"> <span class="switch-sec ' + ((__t = data["promotion_mobile_h5"] && data["promotion_mobile_h5"][0] == "1" ? "on" : "") == null ? "" : __t) + ' j-switch-promotion-h5 "></span> </div> </div> ';
            }
        }
        return __p;
    };
});
function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var o;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(o="Object"===(o={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:o)||"Set"===o?Array.from(t):"Arguments"===o||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o)?_arrayLikeToArray(t,e):void 0}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var o=0,n=Array(e);o<e;o++)n[o]=t[o];return n}function _iterableToArrayLimit(t,e){var o=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=o){var n,r,i,a,s=[],l=!0,c=!1;try{if(i=(o=o.call(t)).next,0===e){if(Object(o)!==o)return;l=!1}else for(;!(l=(n=i.call(o)).done)&&(s.push(n.value),s.length!==e);l=!0);}catch(t){c=!0,r=t}finally{try{if(!l&&null!=o.return&&(a=o.return(),Object(a)!==a))return}finally{if(c)throw r}}return s}}function _arrayWithHoles(t){if(Array.isArray(t))return t}define("crm-setting/promotion/promotion",["crm-modules/common/util","./promotion-html","./tpl-html"],function(t,e,o){var i=t("crm-modules/common/util"),r=t("./promotion-html");return Backbone.View.extend({template:t("./tpl-html"),initialize:function(t){this.setElement(t.wrapper),this.isWhiteList=!1},events:{"click .j-set-config":"setConfig","click .mn-checkbox-item":"checkHandle","click .j-set-on":"showTip","click .j-switch-promotion":"promotionSwitch","click .j-switch-promotion-h5":"promotionSwitchH5","click .j-reload-config":"_reloadHandle"},render:function(){this.$el.html(this.template({})),this.getConfig(this.renderTpl)},getKeys:function(){return[{key:"46",value:"0"},{key:"49",value:"0"},{key:"50",value:"0"},{key:"promotion_mobile_h5",value:"0"}]},renderTpl:function(t,e){var o=this,n=o.getKeys();$(".promotion-box",o.$el).html(r({start:t,data:e||n,isWhiteList:o.isWhiteList}))},_reloadHandle:function(t){this.getConfig(this.renderTpl,$(t.currentTarget))},parseData:function(t){var e={};return t=_.map(t,function(t){return t.value=t.value.split(","),e[t.key]=t.value,t}),e},getConfig:function(o,t){var n=this;n._getAjax&&(n._getAjax.abort(),n._getAjax=null),n._getAjax=i.FHHApi({url:"/EM1HNCRM/API/v1/object/promotion/service/is_promotion_enable",success:function(t){var e="error";0===t.Result.StatusCode&&(e=t.Value.enable),Promise.all([n.getWhiteList(),i.getConfigValues(["46","49","50","promotion_mobile_h5"])]).then(function(t){t=_slicedToArray(t,2)[1];o&&o.call(n,e,n.parseData(t))},function(){o&&o.call(n,e,n.parseData(n.getKeys()))})},complete:function(){n._getAjax=null}},{errorAlertModel:1,submitSelector:t})},getWhiteList:function(t){var o=this;return new Promise(function(t,e){i.FHHApi({url:"/EM1HNCRM/API/v1/object/promotion/service/in_promotion_white_list",success:function(t){0===t.Result.StatusCode?o.isWhiteList=t.Value.enable:i.alert(t.Result.FailureMessage)},error:function(){t()},complete:function(){t()}},{errorAlertModel:1})})},_setConfig:function(t,e,o){var n=this,e={ConfigInfoList:[{key:t,value:e}]};_.isArray(t)&&(e.ConfigInfoList=t),i.setConfigValues(e).then(function(){i.remind(1,$t("设置成功")),o&&o()},function(t){i.alert(t),n.render()})},updateButtonUrl:function(t){var e=window.location.origin||window.location.protocol+"//"+window.location.host;i.FHHApi({url:"/EM1HNCRM/API/v1/object/button/service/updateButtonUrl",data:{0:{buttonURL:{},buttonApiNames:["Add_button_default","Edit_button_default"],objectDescribeApiName:"SalesOrderObj"},1:{buttonURL:{urlInfos:[{clientType:"mobile",url:e+"/gamma/auth/connect?resourceUrl=fs-sail-order-common&_hash=/visitsales/&source=common"},{clientType:"web",url:e+"/gamma/auth/connect?resourceUrl=fs-sail-order-common&_hash=/visitsales/&source=common"}]},buttonApiNames:["Add_button_default","Edit_button_default"],objectDescribeApiName:"SalesOrderObj"}}[t]},{errorAlertModel:1})},isEdit_ajax:function(t){var e=this,o=t.hasClass("on")?"0":"1";0==o?this._setConfig([{key:"49",value:o},{key:"46",value:"0"},{key:"50",value:"0"}],"",function(){t.removeClass("on"),e.$(".orderrule-promotion-checkbox .mn-checkbox-item").removeClass("mn-selected"),e.$(".orderrule-promotion-checkbox").addClass("hide")}):this._setConfig("49",o,function(){t.addClass("on"),e.$(".orderrule-promotion-checkbox").removeClass("hide")})},isEdit_ajax_h5:function(t){var e=this,o=t.hasClass("on")?"0":"1";this._setConfig("promotion_mobile_h5",o,function(){"0"==o?(t.removeClass("on"),e.updateButtonUrl(0)):(t.addClass("on"),e.updateButtonUrl(1))})},promotionSwitch:function(t){var e=$(t.currentTarget),o=this,n=$t("关闭后不再适配促销，确定关闭吗");e.hasClass("on")?o._confirm=i.confirm(n,"",function(){o.isEdit_ajax(e),o._confirm.hide()}):(n=$t("开启后，订单将按照促销规则计算，确定开启吗"),this._isPromotion(function(t){t?o._confirm=i.confirm(n,"",function(){o.isEdit_ajax(e),o._confirm.hide()}):i.alert($t("促销应用未启用请启用后再操作"))}))},promotionSwitchH5:function(t){var e=$(t.currentTarget),o=this,n=$t("关闭后不再适配促销，确定关闭吗");e.hasClass("on")?o._confirm=i.confirm(n,"",function(){o.isEdit_ajax_h5(e),o._confirm.hide()}):(n=$t("开启后，订单将按照促销规则计算，确定开启吗"),this._isPromotion(function(t){t?o._confirm=i.confirm(n,"",function(){o.isEdit_ajax_h5(e),o._confirm.hide()}):i.alert($t("促销应用未启用请启用后再操作"))}))},_isPromotion:function(e){i.FHHApi({url:"/EM1HNCRM/API/v1/object/promotion/service/is_promotion_enable",success:function(t){0===t.Result.StatusCode&&(t=t.Value.enable,e)&&e(t)}},{errorAlertModel:1})},checkHandle:function(t){t.stopPropagation();var e,o,n=this,r=$(t.target);r.hasClass("disabled-selected")||("invoice_mode"==(e=r.attr("data-key"))?r.hasClass("mn-selected")||(o=i.confirm($t("确定要启用订单产品为开票明细吗"),"",function(){n._setConfig(e,"sales_order_product",function(){r.addClass("mn-selected disabled-selected"),CRM.util.sendLog("setting","invoice",{operationId:"openMode3"})}),o.hide()})):(r.toggleClass("mn-selected"),this._setConfig(e,r.hasClass("mn-selected")?"1":"0")))},setConfig:function(){var e=this;i.getCrmAllConfig(function(){var t;CRM._cache.cpqStatus?i.alert($t("先关闭CPQ再开启促销")):t=i.confirm($t("crm.确定启用促销么"),$t("提示"),function(){i.FHHApi({url:"/EM1HNCRM/API/v1/object/promotion/service/enable_promotion",success:function(t){0==t.Result.StatusCode?(i.remind(1,$t("启用成功")),e.renderTpl(!0),CRM._cache.promotionStatus=!0,CRM.control.refreshAside()):i.alert(t.Result.FailureMessage)},complete:function(){t.hide(),e.isSetting=!1}},{errorAlertModel:1,submitSelector:t.$(".b-g-btn")})})})},showTip:function(){i.alert($t("促销已开启无法再关闭"))}})});
define("crm-setting/promotion/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("促销设置")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-p20" style="line-height: 35px;"> <div class="tab-con"> <div class="item render-box promotion-box"></div> </div> </div> </div>';
        }
        return __p;
    };
});