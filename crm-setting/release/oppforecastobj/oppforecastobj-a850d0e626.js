define("crm-setting/oppforecastobj/oppforecastobj",["crm-modules/page/list/list","crm-modules/common/util","crm-widget/dialog/dialog","crm-modules/action/oppforecastobj/oppforecastobj","crm-modules/action/oppforecastobj/oppforecastobj","crm-modules/action/oppforecastobj/oppforecastobj","./template/index-html"],function(e,t,a){var o=e("crm-modules/page/list/list"),n=(e("crm-modules/common/util"),e("crm-widget/dialog/dialog")),s=e("crm-modules/action/oppforecastobj/oppforecastobj").View,i=e("crm-modules/action/oppforecastobj/oppforecastobj").Model,r=e("crm-modules/action/oppforecastobj/oppforecastobj").NextView,c=e("./template/index-html"),l=$t("确定启用该规则吗？规则启用后，将不可编辑预测规则相关内容，仅支持编辑商机展示列配置。"),d=$t("确定删除该预测规则？删除后已启用的规则前台预测数据将会被一并删除，且不可恢复"),p=o.extend({apiName:"ForecastRuleObj",initialize:function(e){o.prototype.initialize.apply(this,arguments)},render:function(e){(e=e||[])[0]="ForecastRuleObj",this.$el.html(c()),this.setElement(".oppforecast-box"),o.prototype.render.call(this,e)},renderListCompleteHandle:function(){o.prototype.renderListCompleteHandle.apply(this,arguments)},getColumns:function(){var e=o.prototype.getColumns.apply(this,arguments);return _.each(e,function(e){"forecast_object_amount_api_name"==e.api_name?e.render=function(e,t,a){return"<span>"+a.forecast_object_amount_api_name_lable+"<span/>"}:"forecast_object_date_api_name"==e.api_name?e.render=function(e,t,a){return"<span>"+a.forecast_object_date_api_name_lable+"<span/>"}:"operate"==e.dataType&&(e.width=300,e.render=function(e,t,a){return"2"==a.biz_status?$t("启用中"):'<div class="forecast_rule_operate_btn">'+a.biz_status=="1"?'<a class="forecast_rule_edit" data-action="Edit" src="javascript:;">'+$t("编辑")+"</a>":("1"==a.biz_status?"":'<a class="forecast_rule_enable" data-action="ChangeStatus" src="javascript:;">'+$t("启用")+"</a>")+'<a class="forecast_rule_del" data-action="Delete" src="javascript:;">'+$t("删除")+'</a><a class="forecast_rule_copyadd" data-action="Clone" src="javascript:;">'+$t("复制并新建")+"</a></div>"})}),e},trclickHandle:function(e,t,a,o,n){var s=a.attr("data-action");this["".concat(s,"Handle")]&&this["".concat(s,"Handle")](e,a),$(t).parents(".fix-end-b").length||s||this.onView(e)},CloneHandle:function(e){var t=this;CRM.api.clone({$el:t.$el,apiname:t.apiName,dataId:e._id,success:function(e){t.trigger("refresh","add",e),t.table.refresh()},error:function(){}})},ChangeStatusHandle:function(e,t){var a=this,o=new n({title:$t("提示"),showBtns:!0,content:'<div class="content">'+l+"</div>"});o.on("dialogCancel",function(){o&&o.destroy()}),o.on("dialogEnter",function(){a.oppForcastRuleEnable.call(a,e,t),o&&o.destroy()}),o.show()},EditHandle:function(t){var a=this;e.async("crm-modules/action/field/field",function(e){e.edit({record_type:t.record_type,data:t,apiname:"ForecastRuleObj",className:"crm-action-oppforecast",version:t.version,show_type:t.show_type||"full",dataId:t.dataId||t._id,Model:i,View:s,NextView:r,is_view:!1,success:function(e){a.trigger("refresh","edit",e)}})})},onView:function(a){var o=this;a.is_view=!0,e.async("crm-modules/action/field/field",function(e){var t={record_type:a.record_type,data:a,apiname:"ForecastRuleObj",className:"crm-action-oppforecast",version:a.version,include_detail_describe:!0,show_type:a.show_type||"full",dataId:a.dataId||a._id,Model:i,View:s,NextView:r,is_view:!0,success:function(e){o.table.refresh()}};e.edit(t)})},DeleteHandle:function(e){var t=this,a=new n({title:$t("提示"),showBtns:!0,content:'<div class="content">'+d+"</div>"});a.on("dialogCancel",function(){a&&a.destroy()}),a.on("dialogEnter",function(){t.oppForcastRuleDel(e),a&&a.destroy()}),a.show()},oppForcastRuleEnable:function(e,t){var a=this,o=t.data("action");changestatus=t.closest(".tr").find("[data-action=ChangeStatus]").text(),e.biz_status=changestatus==$t("启用")?"2":"1",CRM.api.change_oppforecast_status(_.extend(e,{_name:o,button_action:o,success:function(e){0===e.Result.StatusCode&&a.table.refresh()}}))},oppForcastRuleDel:function(e){var t=this;CRM.api.abolish({apiname:t.apiName,dataList:[e],success:function(){t.table.refresh()},error:function(){}})},show:function(){this.render(),this.$el.parent(".oppforecast-box").show()},hide:function(){this.$el.hide()},getOptions:function(){var e=o.prototype.getOptions.apply(this,arguments),t=_.findWhere(e.operate.btns,{api_name:"Add_button_default"});return e.operate={btns:t?[t]:[]},_.extend(e,{searchTerm:null,operate:_.extend(e.operate,{pos:"T"}),search:{placeHolder:$t("搜索规则名称"),type:"Keyword",highFieldName:"name",pos:"T"},height:"auto",autoHeight:!0,maxHeight:$(window).height()-240+"px",showMultiple:!1,noAlwaysShowPage:!1,showFilerBtn:!1,showTerm:!0,isOrderBy_allColumn:!1,showBatchBtns:!1,title:!1})},destroy:function(){o.prototype.destroy.call(this)}});a.exports=p});
define("crm-setting/oppforecastobj/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit crm-manageclue-title"> <h2><span class="tit-txt">' + ((__t = $t("crm.opp.forecastname")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-scroll"> <div class="item oppforecast-box"> <div class="crm-loading"></div> </div> </div> </div>';
        }
        return __p;
    };
});