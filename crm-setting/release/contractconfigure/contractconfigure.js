define("crm-setting/contractconfigure/config",[],function(t,e,c){c.exports={CONFIG_DATA:[{domId:"level0_1",moduleId:"SaleContract",title:$t("crm.SaleContractObj",null,"销售合同"),moduleList:[{title:$t("开启销售合同开关"),type:"switch",key:"sale_contract",setConfigParam:2,enableClose:!1,describeList:[{title:$t("先开订单新建布局")}]},{title:$t("crm.salecontractobjconstraint.settings.title",null,"合同约束开关"),type:"switch",key:"contract_constraint_mode",setConfigParam:2,enableClose:!1,isShow:function(t){return t.sale_contract&&CRM.util.isGrayScale("CRM_SALECONTRACT_CONSTRAINT")},describeList:[{title:$t("crm.salecontractobjconstraint.settings.desc1"),list:[{title:$t("crm.salecontractobjconstraint.settings.desc1_1")}]},{title:$t("crm.salecontractobjconstraint.settings.desc2"),list:[{title:$t("crm.salecontractobjconstraint.settings.desc2_1")},{title:$t("crm.salecontractobjconstraint.settings.desc2_2")}]},{title:$t("crm.salecontractobjconstraint.settings.desc3"),list:[{title:$t("crm.salecontractobjconstraint.settings.desc3_1")}]}],children:[{title:"",type:"checkbox",value:[!1],key:["contract_constraint_pricebook_available_range"],enableClose:!1,setConfigParam:2,isShow:function(t){return t.contract_constraint_mode},options:function(t){var e=[],c=t.available_range,t=t[28],c=[c?$t("crm.AvailableRangeObj"):"",t?$t("crm.PriceBookObj"):""].filter(function(t){return t});return c.length&&(t=c.join($t("crm.salecontractobjconstraint.settings.label_opts_and",null,"和")),e.push({key:"contract_constraint_pricebook_available_range",label:$t("crm.salecontractobjconstraint.settings.label_opts_prefix",{name:t},"合同约束")})),e}}]},{title:$t("sfa.crm.setting_tradeconfigure.contract_connector_title"),type:"SaleContractConnector",key:"contract_connector",desc:[$t("sfa.crm.setting_tradeconfigure.contract_connector_desc")]},{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_title"),key:"is_open_additional_contract",enableClose:!1,describeList:[{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_desc1"),list:[{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_desc2")},{title:$t("sfa.crm.setting_tradeconfigure.is_open_additional_contract_desc3")}]}],isShow:function(t){return t.sale_contract}},{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_title"),key:"sale_contract_record_type_mapping",type:"ContractMainSubFlag",desc:[{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc1"),list:[{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc2")},{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc3")},{title:$t("sfa.crm.setting_tradeconfigure.sale_contract_record_type_mapping_desc4")}]}],isShow:function(t){return t.is_open_additional_contract}},{title:$t("sfa.crm.setting_tradeconfigure.contract_mapping_title"),key:"contract_mapping",type:"ContractMapping",desc:[$t("sfa.crm.setting_tradeconfigure.contract_mapping_desc1"),$t("sfa.crm.setting_tradeconfigure.contract_mapping_desc2"),$t("sfa.crm.setting_tradeconfigure.contract_mapping_desc3")],isShow:function(t){return t.is_open_additional_contract}},{title:$t("sfa.crm.setting_tradeconfigure.is_open_contract_progress_title"),describeList:[$t("sfa.crm.setting_tradeconfigure.is_open_contract_progress_desc1")],key:"is_open_contract_progress",type:"switch",value:!1,enableClose:!1,isShow:function(t,e){t=t.sale_contract,e=e.contract_progress_management_app;return t&&e},children:[{title:"",key:"open_contract_progress_rule",type:"OpenContractProgressRule",isShow:function(t){return t.is_open_contract_progress}}]}]}],KEY_CONFIG:{sale_contract:{cache_key:"sale_contract"},contract_connector:{type:"string"},sale_contract_record_type_mapping:{type:"jsonstring"}}}});
function asyncGeneratorStep(e,t,n,r,o,a,i){try{var c=e[a](i),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function _asyncToGenerator(c){return function(){var e=this,i=arguments;return new Promise(function(t,n){var r=c.apply(e,i);function o(e){asyncGeneratorStep(r,t,n,o,a,"next",e)}function a(e){asyncGeneratorStep(r,t,n,o,a,"throw",e)}o(void 0)})}}define("crm-setting/contractconfigure/contractconfigure",["../promotionrebate/promotionrebate","./config"],function(e,t,n){var a=e("../promotionrebate/promotionrebate").Base,e=e("./config"),r=e.CONFIG_DATA,o=e.KEY_CONFIG;n.exports=a.extend({getLicenseKeys:function(){return["contract_progress_management_app"]},getConfigData:function(){return r},getConfigKeyData:function(){return o},beforeSetConfig:function(n,e){var r=arguments,o=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a.prototype.beforeSetConfig.apply(o,r);case 2:if(t=e.sent,"sale_contract"===n)return e.next=6,CRM.util.getEditLayoutStatus("SalesOrderObj");e.next=10;break;case 6:if(e.sent){e.next=9;break}throw new Error($t("请先开启{{apiName}}新建布局",{apiName:CRM.config.objDes["SalesOrderObj".toLowerCase()].name}));case 9:t.confirmInfo=$t("确定要开启销售合同吗");case 10:return e.abrupt("return",t);case 11:case"end":return e.stop()}},e)}))()},afterSetConfig:function(e){e=e.update;"is_open_additional_contract"===this.currentChangeKey&&e(["sale_contract_record_type_mapping"])}}),n.exports.config=e});