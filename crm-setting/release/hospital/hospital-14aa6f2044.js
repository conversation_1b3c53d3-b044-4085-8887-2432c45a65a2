define("crm-setting/hospital/hospital",[],function(t,e,i){var s=Vue.extend({template:'<div class="hospital_setting__container"><h2 class="hospital_setting__title">{{$t("医疗信息设置")}}</h2><div class="hospital_setting__container"><div class="hospital_setting"><h2 class="automatic_backfill__setting">{{$t("医疗信息自动回填设置")}}</h2><p class="automatic_backfill__text" v-html="objmap"> {{objmap}} </p></div><fx-form ref="hospitalForm" :model="hospitalInfo" label-width="auto"><p class="switch_status"><span>{{$t("新建或编辑客户时使用医疗信息开关")}}</span><fx-switch v-model="hospitalInfo.status" size="medium" @change="switchStatus"></fx-switch></p><template v-if="hospitalInfo.status===true"><fx-select prop="recordTypes" :label="$t(\'web-crm-hospitalSetting-recordTypes-label\')" :placeholder="$t(\'web-crm-hospitalSetting-recordTypes-placeholder\')" v-model="hospitalInfo.recordTypes" :options="recordTypeOptions" multiple size="small" :rules="recordTypeRule" ></fx-select></form-item><fx-radio-group class="hospital_backfill__method" prop="backfillInfo" v-model="hospitalInfo.backfillInfo" :label="$t(\'web-crm-hospitalSetting-backfillInfo-label\')" :rules="backfillInfoRule" ><fx-radio label="1">{{$t("新建编辑保存客户时，自动回填医疗信息。")}}</fx-radio><fx-radio label="2">{{$t("新建编辑保存客户时，手动回填医疗信息。")}}</fx-radio></fx-radio-group></template></fx-form><fx-button v-if="hospitalInfo.status===true" class="save" type="primary" @click="save" :loading="saveLoad">{{$t(\'crm.保存\')}}</fx-button></div></div>',data:function(){return{saveLoad:!1,hospitalInfo:{status:!1,backfillInfo:"1",recordTypes:[]},recordTypeOptions:[],recordTypeRule:[{required:!0,message:$t("web-crm-hospitalSetting-recordTypeValid"),trigger:"change"}],backfillInfoRule:[{required:!0,message:$t("web-crm-hospitalSetting-backfillInfoValid")}],objmap:$t("hospital_objmap_backfill",{discrible:'<a href="#crmmanage/=/module-objmap">'+$t("对象映射规则")+"</a>"})}},created:function(){this.getSwitchSettings("refresh"),this.getRecordTypeList()},methods:{switchStatus:function(){!1===this.hospitalInfo.status?this.requestSwitchStatus():(this.getSwitchSettings(),this.getRecordTypeList())},getSwitchSettings:function(s){var a=this;FS.util.api({type:"post",timeout:2e4,url:"/FHH/EM1HHOSPITAL/manager/getSwitchSettings"},{autoPrependPath:!1}).then(function(t){var e,i;t&&0===t.Result.StatusCode?(i=(e=(t||{}).Value.data).status,a.hospitalInfo.recordTypes=(e.recordTypes||[]).map(function(t){return t.api_name}),a.hospitalInfo.backfillInfo=0===i?"1":String(i),a.hospitalInfo.status=!s||0!==i):a.$toast(t.Result.FailureMessage||"Failed to Fetch Configuration",{type:"error"})},function(t){})},getRecordTypeList:function(){var e=this;CRM.util.getRecordType({describeApiName:"AccountObj"}).then(function(t){e.recordTypeOptions=_.map(t,function(t){return t.value=t.api_name,t})})},save:function(){var t=this;this.$refs.hospitalForm.validate().then(function(){t.requestSwitchStatus()}).catch(function(t){})},requestSwitchStatus:function(){var e=this,t=(this.saveLoad=!!this.hospitalInfo.status,this.hospitalInfo.status?Number(this.hospitalInfo.backfillInfo):0),i=this.hospitalInfo.recordTypes,s=(this.recordTypeOptions||[]).filter(function(t){return i.includes(t.api_name)});FS.util.api({type:"post",timeout:2e4,url:"/FHH/EM1HHOSPITAL/manager/updateSwitchSettings",data:{status:t,recordTypes:s}},{autoPrependPath:!1}).then(function(t){t&&0===t.Result.StatusCode?e.$toast(t.Value.errMsg,{type:"success"}):e.$toast(t.Result.FailureMessage,{type:"error"}),e.saveLoad=!1},function(t){e.saveLoad=!1,e.$toast(t||"Failed to Update Configuration",{type:"error"})})}}}),a=Backbone.View.extend({initialize:function(){},render:function(){var t=document.createElement("div");t.setAttribute("class","crm-setting-hospital"),document.querySelector(".crm-s-hospital").appendChild(t),this.hospitalsetting=new s({name:"HospitalSetting",el:document.querySelector(".crm-setting-hospital")})},destory:function(){this.hospitalsetting.destory()}});i.exports=a});
define("crm-setting/hospital/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="hospital_setting__container"> <h2 class="hospital_setting__title">' + ((__t = $t("医疗信息设置")) == null ? "" : __t) + '</h2> <div class="hospital_setting__container"> <div class="hospital_setting"> <h2 class="automatic_backfill__setting">' + ((__t = $t("医疗信息自动回填设置")) == null ? "" : __t) + '</h2> <p class="automatic_backfill__text" v-html="objmap"> ' + ((__t = objmap) == null ? "" : __t) + ' </p> </div> <fx-form ref="hospitalForm" :model="hospitalInfo" label-width="auto"> <p class="switch_status"> <span>' + ((__t = $t("新建或编辑客户时使用医疗信息开关")) == null ? "" : __t) + '</span> <fx-switch v-model="hospitalInfo.status" size="medium" @change="switchStatus"></fx-switch> </p> <template v-if="hospitalInfo.status===true"> <fx-select prop="recordTypes" :label="$t(\'web-crm-hospitalSetting-recordTypes-label\')" :placeholder="$t(\'web-crm-hospitalSetting-recordTypes-placeholder\')" v-model="hospitalInfo.recordTypes" :options="recordTypeOptions" multiple size="small" :rules="recordTypeRule" ></fx-select> </form-item> <fx-radio-group class="hospital_backfill__method" prop="backfillInfo" v-model="hospitalInfo.backfillInfo" :label="$t(\'web-crm-hospitalSetting-backfillInfo-label\')" :rules="backfillInfoRule" > <fx-radio label="1">' + ((__t = $t("新建编辑保存客户时，自动回填医疗信息。")) == null ? "" : __t) + '</fx-radio> <fx-radio label="2">' + ((__t = $t("新建编辑保存客户时，手动回填医疗信息。")) == null ? "" : __t) + '</fx-radio> </fx-radio-group> </template> </fx-form> <fx-button v-if="hospitalInfo.status===true" class="save" type="primary" @click="save" :loading="saveLoad">' + ((__t = $t("crm.保存")) == null ? "" : __t) + "</fx-button> </div> </div>";
        }
        return __p;
    };
});