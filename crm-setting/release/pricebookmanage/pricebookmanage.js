define("crm-setting/pricebookmanage/pricebookmanage",["crm-modules/common/util","./tpl-html"],function(t,e,n){var i=t("crm-modules/common/util");return Backbone.View.extend({template:t("./tpl-html"),initialize:function(t){this.setElement(t.wrapper)},KEY:"28",events:{"click .j-set-config":"setConfig","click .j-set-on":"showTip","click .j-reload-config":"_reloadHandle"},render:function(){this.getConfig(this.renderTpl)},renderTpl:function(t){this.$el.html(this.template({start:t}))},_reloadHandle:function(t){this.getConfig(this.renderTpl,$(t.currentTarget))},getConfig:function(e,t){var n=this;n._getAjax&&(n._getAjax.abort(),n._getAjax=null),i.getConfigValue(n.KEY).then(function(t){e&&e.call(n,"1"==t)},function(){e&&e.call(n,"error")})},setConfig:function(){var t=this,e=i.confirm($t("crm.确认开启价目表么"),$t("提示"),function(){i.setConfigValue({key:t.KEY,value:"1"},{submitSelector:e.$(".b-g-btn")}).then(function(){i.remind(1,$t("启用成功")),t.renderTpl(!0),CRM.control.refreshAside(),t.initProduct()},function(t){i.alert(t||$t("启用失败请稍后重试或联系纷享客服"))}).always(function(){e.hide(),t.isSetting=!1})})},initProduct:function(){i.FHHApi({url:"/EM1HNCRM/API/v1/object/pricebook_standard/service/check_or_init_standard_pricebook",data:{}},{errorAlertModel:1})},showTip:function(){i.alert($t("价目表已开启无法再关闭"))}})});
define("crm-setting/pricebookmanage/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("价目表管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>1." + ((__t = $t("价目表一旦开启不可关闭。")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.价目表会影响订单里的内容")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.价目表开启后会默认给租客一个标准价目表")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("crm.开启价目表后需提前调整OpenAPI")) == null ? "" : __t) + "</li> </ul> </div> ";
            if (start === "error") {
                __p += ' <p><span class="pb-set-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span><a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + "</a></p> ";
            } else {
                __p += " ";
                if (start) {
                    __p += " <span>" + ((__t = $t("价目表已开启并已完成初始化")) == null ? "" : __t) + "</span> ";
                }
                __p += ' <div class="on-off"> <label class="title">' + ((__t = $t("价目表开启开关")) == null ? "" : __t) + '</label> <div class="switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> ';
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});