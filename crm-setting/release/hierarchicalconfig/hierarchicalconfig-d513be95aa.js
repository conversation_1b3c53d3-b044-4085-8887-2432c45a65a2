define("crm-setting/hierarchicalconfig/hierarchicalconfig",["./tpl-html"],function(e,t,i){var a=e("./tpl-html"),n={AccountObj:{KEY:"account_tree_fields",HEAD:$t("客户层级列表上所展示的字段")},PartnerObj:{KEY:"partner_tree_fields",HEAD:$t("伙伴层级列表上所展示的字段"),FILTERS:["partner_path"]}};i.exports=Backbone.View.extend({initialize:function(e){this.apiname=e.apiname,this.$el.html(a(Object.assign(e,{itemHd:n[e.apiname].HEAD}))),this.fetch()},events:{"click .j-save":"onSave"},fetch:function(){var i=this;Promise.all([CRM.util.getDescribeLayout({include_detail_describe:!1,include_layout:!1,apiname:i.apiname,layout_type:"add",recordType_apiName:"default__c"}),CRM.util.getConfigValue(n[i.apiname].KEY)]).then(function(e){i.fieldsObject=e[0].objectDescribe.fields;var t=i.parseData(i.parseData1(i.fieldsObject)),e=e[1].split(","),e=_.filter(e,function(e){return i.fieldsObject[e]&&i.checkField(i.fieldsObject[e])});i.initTransfer(t,e)})},parseData:function(e){var t=[];return _.each(e,function(e){t.push({label:e.label,key:e.api_name,disabled:"name"==e.api_name})}),t},parseData1:function(e){var t=this,i=[],a=/^UD.+__c$/;return _.each(e,function(e){if(!t.checkField(e))return!1;(e=a.test(e.api_name)?_.extend(e,{config:{display:1}},e):e).config||(e.config={display:1}),e.config&&0!==e.config.display&&i.push(e)}),i},checkField:function(e){var t=n[this.apiname].FILTERS,i=["is_overtime","lock_rule","extend_obj_data_id","life_status_before_invalid","package","create_time","version","created_by","relevant_team","data_own_department","_id","is_duplicated","tenant_id","lock_user","is_deleted","object_describe_api_name","last_modified_by","order_by","last_modified_time","leads_pool_id","object_describe_id","account_path","industry_ext","poi_information","pin_yin","completed_field_quantity","remind_days","is_remind_recycling","extend_days","filling_checker_id","last_deal_closed_amount","total_refund_amount","mc_functional_currency","mc_exchange_rate_version","mc_currency","mc_exchange_rate","town","extend_days"];return t&&(i=i.concat(t)),!(!e.is_active||"group"==e.type||!e.is_index&&"formula"==e.type||-1!=i.indexOf(e.api_name))},initTransfer:function(e,t){this.transfer=FxUI.create({wrapper:this.$(".setting-hierarchy-transfer")[0],template:'<fx-transfer :draggable="true" is-item-break :titles="titles" target-order="push" :filterable="true" :filter-placeholder="placeholder" v-model="value" :data="data"></fx-transfer>',data:function(){return{data:e,value:t,titles:[$t("全选"),$t("全选")],placeholder:$t("请输入搜索内容")}}})},onSave:function(){var e=this.transfer.value.join(",");CRM.util.setConfigValue({key:n[this.apiname].KEY,value:e}).then(function(e){CRM.util.remind(1,$t("设置成功"))},function(e){CRM.util.remind(3,e||$t("设置失败"))})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){}})});
define("crm-setting/hierarchicalconfig/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="setting-hierarchy"> <div class="setting-hierarchy-item"> <div class="setting-hierarchy-item-hd">' + ((__t = itemHd) == null ? "" : __t) + '</div> <div class="setting-hierarchy-transfer-hd"> <div class="transfer-title crm-hierarchy-title">' + ((__t = $t("全部字段")) == null ? "" : __t) + '</div> <div class="transfer-title crm-hierarchy-title">' + ((__t = $t("显示字段")) == null ? "" : __t) + '</div> </div> <div class="setting-hierarchy-transfer"></div> </div> <div class="setting-hierarchy-btns"> <div class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});