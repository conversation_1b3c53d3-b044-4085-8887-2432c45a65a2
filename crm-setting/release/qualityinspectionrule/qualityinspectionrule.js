define("crm-setting/qualityinspectionrule/qualityinspectionrule",["./template/tpl-html","crm-modules/components/objecttable/objecttable"],function(t,e,i){var a=t("./template/tpl-html"),n=t("crm-modules/components/objecttable/objecttable"),t=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper),this.apiname="QualityInspectionRuleObj",this.displayName=$t("质检设置")},render:function(){this.$el.html(a()),this.renderTable()},renderTable:function(){var s=this,t=n.extend({initialize:function(t){this.setElement(t.wrapper),n.prototype.initialize.apply(this,arguments)},getColumns:function(){var t=n.prototype.getColumns.apply(this,arguments);return _.findWhere(t,{dataType:"operate"}).width=100,_.findWhere(t,{api_name:"dirty_words"}).render=function(t,e,i){i=CRM.util.parseQIRRule(i.dirty_words);return i?'<span title="'.concat(i,'">').concat(i,"</span>"):"--"},t},getCustomOperate:function(t,e){return _.each(t,function(t){t.render_type="not_fold",t.data=e}),t},operateBtnClickHandle:function(t){var e,i=$(t.target),a=i.data("action"),n=i.closest(".tb-cell").data("id");a&&(_.contains(["Delete"],a)?(i={Delete:$t("删除")},e=CRM.util.confirm($t("确定{{actionName}}吗？",{actionName:i[a]||$t("执行操作")}),null,function(){e.hide(),a="__"+a.split("_").join("")+"Handle",s[a]&&s[a](t,n)})):(a="__"+a.split("_").join("")+"Handle",s[a]&&s[a](t,n)))},trclickHandle:function(t,e,i,a,n){s.showDetail(t,this.table.getCurData())}});this.list=new t({wrapper:$(".crm-qir-table",this.$el),apiname:this.apiname,isFilter:!1,showTitle:!1,showOperate:!0,isRenderRecordType:!1,tableOptions:{showPage:!0,showMultiple:!1,showFilerBtn:!1,showTerm:!1,alwaysShowTermBatch:!0,searchTerm:null,search:{placeHolder:$t("搜索质检名称"),type:"Keyword",highFieldName:"name",pos:"T"},operate:{pos:"T",btns:[{action:"add",attrs:"data-action=add",className:"j-action",text:$t("新建")}]}}}),this.list.render()},showDetail:function(t,e){var i=this;CRM.api.show_crm_detail({apiName:this.apiname,id:t._id,idList:_.pluck(e,"_id"),showMask:!1,top:56,callback:function(){i.refresh()}})},__addHandle:function(){var t=this;50<=(this.list.getCurData()||{}).totalCount?CRM.util.alert($t("可创建的质检设置数量已达上限")):this.add=CRM.api.add({apiname:t.apiname,displayName:t.displayName,success:function(){t.refresh()}})},__EditHandle:function(t,e){var i=this;this.edit=CRM.api.edit({apiname:this.apiname,id:e,displayName:this.displayName,success:function(){i.refresh()}})},__DeleteHandle:function(t,e){var i=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/".concat(this.apiname,"/action/BulkDelete"),data:{describe_api_name:this.apiname,idList:[e]},success:function(t){0===t.Result.StatusCode?(CRM.util.remind(1,$t("删除成功")),i.refresh()):CRM.util.alert(t.Result.FailureMessage||DEFAULT_ERROR_MSG)},error:function(t){CRM.util.alert(DEFAULT_ERROR_MSG)}})},refresh:function(){this.list&&this.list.refresh&&this.list.refresh()},destroy:function(){this.list&&this.list.destroy&&this.list.destroy()}});i.exports=t});
define("crm-setting/qualityinspectionrule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-qir-wrapper"> <div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("质检设置")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-qir-table"></div> </div>';
        }
        return __p;
    };
});