define("crm-setting/salesbrief/salesbrief",["./template/tpl-html","./template/content-html","crm-modules/common/util","crm-widget/selector/selector","./timeselect/timeselect"],function(e,t,a){var n=e("./template/tpl-html"),s=e("./template/content-html"),l=e("crm-modules/common/util"),i=e("crm-widget/selector/selector"),o=e("./timeselect/timeselect"),p=[0,0,"day","week","month","page"],c=[{SaleRangeType:2,IsPush:!1,WhiteList:[],BlackList:[],Interval:0,PushTime:0},{SaleRangeType:3,IsPush:!1,WhiteList:[],BlackList:[],Interval:0,PushTime:0},{SaleRangeType:4,IsPush:!1,WhiteList:[],BlackList:[],Interval:0,PushTime:0}],e=Backbone.View.extend({initialize:function(e){this.day=[],this.week=[],this.month=[],this.page=[],this.setElement(e.wrapper),this.el.innerHTML=n()},events:{"click .switch-sec":"clickHandle","click .mn-checkbox-item":"checkHandle"},render:function(){var t=this;l.FHHApi({url:"/EM1HCRM/SaleReportSetting/GetSaleReportSetting",success:function(e){0==e.Result.StatusCode&&(t.reportSets=t.formatData(e.Value),t.$(".content-wrap").hide().html(s(t.reportSets)),t.initPlug(),t.$(".crm-loading").hide(),t.$(".content-wrap").show())}})},clickHandle:function(e){var t=0,a=$(e.currentTarget),e=a.closest(".report-set"),n=e.attr("type"),s=e.find(".forbiden-mask").length?e.find(".forbiden-mask"):e.prepend('<div class="forbiden-mask" />');a.toggleClass("on"),a.hasClass("on")?(t=1,e.removeClass("forbiden"),e.find(".mn-selected").removeClass("disabled-selected"),s.remove()):(t=0,e.addClass("forbiden"),e.find(".mn-selected").addClass("disabled-selected")),_.each(this[n]||[],function(e){a.hasClass("on")?e.instance&&e.instance.lock&&e.instance.unlock():e.instance&&e.instance.lock&&e.instance.lock()}),this.setSaleReport(n,t)},checkHandle:function(e){var t=this,e=$(e.currentTarget),a=e.closest(".report-set");return e.toggleClass("mn-selected"),t.remindDayList=[],_.each(a.find(".mn-checkbox-item"),function(e){$(e).hasClass("mn-selected")&&t.remindDayList.push($(e).data("index"))}),t.setSaleReport(a.attr("type"),a.find(".switch-sec").hasClass("on")?1:0),!1},collect:function(e,t){var n,s,l=[],i=[];return _.each(this[e],function(e){var t,a;"interval"==e.type&&(n=e.instance.select.getValue()),"pushtime"==e.type&&(s=e.instance.select.getValue()),"rangWhite"==e.type&&(t=e.instance.getValue("member"),a=e.instance.getValue("group"),t&&0<t.length&&_.each(t,function(e){l.push({type:1,data:e})}),a)&&0<a.length&&_.each(a,function(e){l.push({type:2,data:e})}),"rangBlack"==e.type&&(t=e.instance.getValue("member"),a=e.instance.getValue("group"),t&&0<t.length&&_.each(t,function(e){i.push({type:1,data:e})}),a)&&0<a.length&&_.each(a,function(e){i.push({type:2,data:e})})}),{SaleRangeType:{day:2,week:3,month:4,page:5}[e],IsPush:t,WhiteList:l,BlackList:i,Interval:n||0,PushTime:s?FS.moment(FS.moment().format("YYYY/MM/DD"),"YYYY/MM/DD").valueOf()+60*s*60*1e3:0,RemindDays:this.remindDayList.join(",")}},initPlug:function(){function c(e){var t=1==e.Type?"p":"g",a=1==e.Type?l.getEmployeeById(e.Data):l.getCircleById(e.Data);return{name:a,id:e.Data,type:t}}var r=this;_.each(this.reportSets.SaleReportSettingList,function(e,t){var a=r.$("."+p[e.SaleRangeType]+"-push"),n=r.$("."+p[e.SaleRangeType]+"-unpush"),s=r.$("."+p[e.SaleRangeType]+"-hour-select"),l=r.$("."+p[e.SaleRangeType]+"-week-select"),i=r.$("."+p[e.SaleRangeType]+"-day-select");r[p[e.SaleRangeType]].push({type:"rangWhite",instance:r.instanceSelectBar({selectbar:a,isPush:e.IsPush,defaultsValue:_.map(e.WhiteList,c)})}),r[p[e.SaleRangeType]].push({type:"rangBlack",instance:r.instanceSelectBar({selectbar:n,isPush:e.IsPush,defaultsValue:_.map(e.BlackList,c)})}),0<s.length&&r[p[e.SaleRangeType]].push({type:"pushtime",instance:new o({parentNode:s,element:s,defaultValue:FS.moment(e.PushTime).format("H"),callback:function(e){var t=this.element.closest(".report-set").attr("type");r.setSaleReport(t,1)}})}),0<l.length&&r[p[e.SaleRangeType]].push({type:"interval",instance:new o({parentNode:l,type:2,defaultValue:e.Interval,element:l,callback:function(e){var t=this.element.closest(".report-set").attr("type");r.setSaleReport(t,1)}})}),0<i.length&&r[p[e.SaleRangeType]].push({type:"interval",instance:new o({parentNode:i,type:3,defaultValue:e.Interval,element:i,callback:function(e){var t=this.element.closest(".report-set").attr("type");r.setSaleReport(t,1)}})}),2===e.SaleRangeType&&(r.remindDayList=e.RemindDays?e.RemindDays.split(","):[])})},instanceSelectBar:function(e){var t,a=this,n=new i({$wrap:a.$(e.selectbar),parentNode:a.$(e.selectbar),zIndex:1e3,member:!0,group:!0,single:!1,label:e.label||$t("选择范围"),defaultSelectedItems:(t={member:[],group:[]},_.each(e.defaultsValue||[],function(e){("p"==e.type?t.member:t.group).push(e.id)}),t)});return n.on("change",function(){var e=n.$el.closest(".report-set").attr("type");a.setSaleReport(e,1)}),e.isPush||n.lock(),n},setSaleReport:function(e,t){e=this.collect(e,t);l.FHHApi({url:"/EM1HCRM/SaleReportSetting/SetSaleReportSetting",data:e,success:function(e){0==e.Result.StatusCode?l.remind(1,$t("设置成功")):l.remind(2,$t("设置失败"))}})},formatData:function(e){return _.each(c,function(t,a){_.each(e.SaleReportSettingList,function(e){e.SaleRangeType==t.SaleRangeType&&(c[a]=e)})}),{SaleReportSettingList:c}},destroy:function(){var t=this;_.each(["day","week","month","page"],function(e){_.each(t[e],function(e){e.instance.destroy&&e.instance.destroy()}),t.items=null}),this.undelegateEvents()}});a.exports=e});
define("crm-setting/salesbrief/template/content-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(SaleReportSettingList, function(item) {
                var configArr = [ {}, {}, {
                    type: "day",
                    text: $t("日报"),
                    interval: $t("推送时间")
                }, {
                    type: "week",
                    text: $t("周报"),
                    interval: $t("每周")
                }, {
                    type: "month",
                    text: $t("月报"),
                    interval: $t("每月")
                }, {
                    type: "page",
                    text: $t("目标设置页面"),
                    interval: ""
                } ];
                var dayList = [ $t("周一"), $t("周二"), $t("周三"), $t("周四"), $t("周五"), $t("周六"), $t("周日") ];
                __p += ' <div type="' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '" class="' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + "-set report-set " + ((__t = item.IsPush ? "" : "forbiden") == null ? "" : __t) + '"> ';
                if (!item.IsPush) {
                    __p += ' <div class="forbiden-mask"></div> ';
                }
                __p += ' <h3 class="title">' + ((__t = configArr[item.SaleRangeType].text) == null ? "" : __t) + "" + ((__t = $t("推送设置")) == null ? "" : __t) + '</h3> <div class="content"> <div class="switch-sec ' + ((__t = item.IsPush ? "on" : "") == null ? "" : __t) + '"> <span class="label">' + ((__t = $t("推送")) == null ? "" : __t) + '</span> <span class="core"> <i class="slider"></i> </span> </div> <div class="field-item clearfix"> <label class="field-label">' + ((__t = $t("推送范围")) == null ? "" : __t) + '</label> <div class="field-anchor ' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '-push"></div> </div> <div class="field-item clearfix"> <label class="field-label">' + ((__t = $t("不参加推送")) == null ? "" : __t) + '</label> <div class="field-anchor ' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '-unpush"></div> </div> <div class="field-item clearfix"> <label class="field-label">' + ((__t = configArr[item.SaleRangeType].interval) == null ? "" : __t) + "</label> ";
                if (item.SaleRangeType == 2) {
                    __p += ' <div> <ul class="mn-checkbox-box checkbox-group b-g-clear"> ';
                    _.each(dayList, function(ite, index) {
                        __p += ' <li> <span class="mn-checkbox-item ' + ((__t = item.RemindDays && item.RemindDays.indexOf(index + 1) > -1 ? "mn-selected" : "") == null ? "" : __t) + '" data-index="' + ((__t = index + 1) == null ? "" : __t) + '"></span> <span class="check-lb">' + ((__t = ite) == null ? "" : __t) + "</span> </li> ";
                    });
                    __p += ' </ul> <div class="field-anchor hour-select ' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '-hour-select"></div> </div> ';
                } else if (item.SaleRangeType == 3) {
                    __p += ' <div class="field-anchor week-select ' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '-week-select"></div> <div class="field-anchor hour-select ' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '-hour-select"></div> ';
                } else if (item.SaleRangeType == 4) {
                    __p += ' <span class="pre-text">' + ((__t = $t("第")) == null ? "" : __t) + '</span> <div class="field-anchor day-select ' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '-day-select"></div> <span class="center-text">' + ((__t = $t("天")) == null ? "" : __t) + '</span> <div class="field-anchor hour-select ' + ((__t = configArr[item.SaleRangeType].type) == null ? "" : __t) + '-hour-select"></div> ';
                }
                __p += " </div> </div> </div> ";
            });
        }
        return __p;
    };
});
define("crm-setting/salesbrief/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("销售简报设置")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="salesbrief-sec crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <ol> <li>" + ((__t = $t("1销售简报通过企信推送可以设置销售简报的推送时间及推送范围如果关闭则不推送。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2日报推送前一天的数据周报推送上一个自然周的数据月报推送上一个自然月的数据。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3每月1日当存在没有设置目标的下属时推送目标设置页。")) == null ? "" : __t) + '</li> </ol> <div class="share-group-box" data-title="databoard"></div> </div> <div class="crm-loading"></div> <div class="content-wrap"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/salesbrief/timeselect/timeselect",["crm-modules/common/util","crm-widget/select/select"],function(e,t,n){var o=e("crm-modules/common/util"),s=e("crm-widget/select/select"),e=Backbone.View.extend({initialize:function(e){this.options=e,this.options.type=this.options.type||1,this.setElement(e.element),this.render()},render:function(){var t=this,e=[];1==this.options.type?e=this.hourItem():2==this.options.type?e=this.weekItem():3==this.options.type&&(e=this.monthItem()),this.select=this.listSelect({parentNode:this.options.parentNode,element:this.$el,arr:e,errmsg:this.options.errmsg||$t("请选择"),defaultValue:this.options.defaultValue||1}),this.select.on("change",function(e){t.options.callback(e)})},hourItem:function(){for(var e=[],t=FS.moment(FS.moment().format("YYYY/MM/DD"),"YYYY/MM/DD"),n=t.add("minutes",120),o=0;o<22;o++)e.push({value:n.format("H"),name:n.format("HH:mm")}),n=t.add("minutes",60);return e},weekItem:function(){for(var e=[],t=FS.moment(FS.moment().format("YYYY/MM/DD"),"YYYY/MM/DD").subtract("day",FS.moment().format("d")-1),n=t,o=0;o<7;o++)e.push({value:n.format("d"),name:n.format("dddd")}),n=t.add("day",1);return e},monthItem:function(){for(var e=[],t=FS.moment(FS.moment().format("YYYY/MM/DD"),"YYYY/MM/DD").subtract("day",FS.moment().format("D")-1),n=t,o=0;o<28;o++)e.push({value:n.format("D"),name:n.format("DD")}),n=t.add("day",1);return e},listSelect:function(n){var e={$wrap:n.element,zIndex:1100,options:n.arr,appendBody:!1};return _.isUndefined(n.defaultValue)||(e.defaultValue=n.defaultValue),(e=new s(e)).on("change",function(e,t){0==t.value?o.showErrmsg(n.element,n.errmsg):o.hideErrmsg(n.element)}),e},destory:function(){this.select.destroy&&this.selectbarMember.destroy()}});n.exports=e});