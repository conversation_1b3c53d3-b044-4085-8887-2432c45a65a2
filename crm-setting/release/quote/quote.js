define("crm-setting/quote/quote",["./tpl-html"],function(n,t,e){var a=CRM.util;return Backbone.View.extend({template:n("./tpl-html"),initialize:function(t){this.setElement(t.wrapper)},config:{is_test_calculate:{key:"is_test_calculate",status:!1,msg_on:$t("确认开启报价单试算功能吗？"),msg_off:$t("确认关闭报价单试算功能吗？")},quote_history_price:{key:"quote_history_price",status:!1,msg_on:$t("确认开启报价单历史报价功能吗")+"？",msg_off:$t("确认关闭报价单历史报价功能吗")+"？"}},events:{"click .j-set-config":"setConfig","click .j-set-on":"setConfig","click .j-reload-config":"_reloadHandle"},render:function(){this.getConfig(this.renderTpl)},renderTpl:function(t){var e=this;this.$el.html(this.template({is_test_calculate:this.config.is_test_calculate.status,quote_history_price:this.config.quote_history_price.status,ajaxStatus:t})),n.async("crm-modules/components/biz_manage/biz_manage",function(t){this.guide=new t({$el:e.$el.find(".crm-module-con"),module:"tradeconfigure",type:"tradeconfigure_business_quote"})})},_reloadHandle:function(t){this.getConfig(this.renderTpl,$(t.currentTarget))},setSwitchStatus:function(t){var n=this;t.forEach(function(t){var e=t.key;n.config[e].status="1"==t.value})},setOneKeyStatus:function(t,e){this.config[t].status=e},getConfig:function(e,t){var n=this,i=(n._getAjax&&(n._getAjax.abort(),n._getAjax=null),Object.keys(this.config));a.getConfigValues(i).then(function(t){n.setSwitchStatus(t),e&&e.call(n,!0)},function(){e&&e.call(n,"error")})},setConfig:function(t){var e=this,t=$(t.target),n=t.hasClass("on"),i=t.data("key"),s=n?"0":"1",t=n?this.config[i].msg_off:this.config[i].msg_on,o=a.confirm(t,$t("提示"),function(){a.setConfigValue({key:i,value:s},{submitSelector:o.$(".b-g-btn")}).then(function(){a.remind(1,n?$t("关闭成功"):$t("启用成功")),e.setOneKeyStatus(i,"1"==s),e.renderTpl(),CRM.control.refreshAside()},function(t){a.alert(t||$t("启用失败请稍后重试或联系纷享客服"))}).always(function(){o.hide(),e.isSetting=!1})})}})});
define("crm-setting/quote/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("报价单管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="mn-radio-box crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>1." + ((__t = $t("报价单试算说明1")) == null ? "" : __t) + "</li> <li>&nbsp;&nbsp;1.1&nbsp;" + ((__t = $t("报价单试算说明2")) == null ? "" : __t) + "</li> <li>&nbsp;&nbsp;1.2&nbsp;" + ((__t = $t("报价单试算说明3")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("[试算]开关，可反复操作")) == null ? "" : __t) + "</li> </ul> </div> ";
            if (ajaxStatus === "error") {
                __p += ' <p><span class="pb-set-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span><a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + "</a></p> ";
            } else {
                __p += ' <div class="on-off"> <label class="title">' + ((__t = $t("试算")) == null ? "" : __t) + ':</label> <div class="switch-sec' + ((__t = is_test_calculate ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '" data-key="is_test_calculate"> </div> </div> ';
            }
            __p += ' </div> <div class="mn-radio-box crm-p20"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>1." + ((__t = $t("报价单历史报价说明1")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("报价单历史报价说明2")) == null ? "" : __t) + "</li> </ul> </div> ";
            if (ajaxStatus === "error") {
                __p += ' <p><span class="pb-set-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span><a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + "</a></p> ";
            } else {
                __p += ' <div class="on-off"> <label class="title">' + ((__t = $t("开启历史报价")) == null ? "" : __t) + ':</label> <div class="switch-sec' + ((__t = quote_history_price ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '" data-key="quote_history_price"> </div> </div> ';
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});