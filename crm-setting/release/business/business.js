define("crm-setting/business/business",["crm-modules/common/util","./template/dateManage-html"],function(e,t,n){var i=e("crm-modules/common/util"),s=e("./template/dateManage-html"),e=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.tenant_id=CRM.enterpriseId,this.comps={}},events:{"click .j-save":"saveHandle","click .j-checkbox":"change"},render:function(){var e=this;e.$el.html(s({})),e.renderCheckBox($t("新建编辑保存，自动回填工商信息"),"change_business_type"),e.renderCheckBox($t("工商查询不展示个体户信息"),"change_business_query"),e.renderBtns(),this.getConfig(["change_business_type","change_business_query","business_allowconfig_no_abnormal","business_no_abnormal"])},renderCheckBox:function(e,t,n){this.comps[t]=FxUI.create({wrapper:this.$el.find(".j-checkbox")[0],template:'<div class="chk-wrapper">\n\t\t\t\t\t<fx-checkbox v-model="checked"><span style="color:#606266">{{content}}</span></fx-checkbox>\n\t\t\t\t\t<p v-if="tip" class="chk-tip">{{tip}}</p>\n\t\t\t\t\t</div>',data:function(){return{checked:!1,content:e,tip:n}}})},renderBtns:function(){this.comps.button=this.comps.button||FxUI.create({wrapper:this.$el.find(".j-save")[0],template:'<fx-button type="primary" :disabled="disabled">{{$t(\'保存\')}}</fx-button>',data:function(){return{disabled:!0}}})},getConfig:function(e){var s=this;CRM.util.getConfigValues(e).then(function(e){for(var t in s.saveCompConfigs=_.filter(e,function(e){return"business_allowconfig_no_abnormal"!==e.key}),"1"==_.findWhere(e,{key:"business_allowconfig_no_abnormal"}).value&&s.renderCheckBox($t("工商查询不展示注销异常企业"),"business_no_abnormal"),e){var n=e[t].key;s.comps[n]&&(s.comps[n].checked="0"!=e[t].value)}},function(e){i.alert(e)})},saveHandle:function(){for(var e in this.saveCompConfigs){var t=this.saveCompConfigs[e].key;this.comps[t]&&(this.saveCompConfigs[e].value=0==this.comps[t].checked?"0":"1")}this.comps.button.disabled||this.setConfig(this.saveCompConfigs)},setConfig:function(e){var t=this;CRM.util.setConfigValues(e).then(function(e){i.remind(1,$t("保存成功")),t.comps.button.disabled=!0},function(e){i.alert(e)}).always(function(){t.isSetting=!1})},change:function(){this.comps.button.disabled=!1},destory:function(){for(var e in this.comps)this.comps[e].destory()}});n.exports=e});
define("crm-setting/business/template/dateManage-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-setting-business"> <div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("工商信息设置")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-p20" style="line-height: 35px;"> <div class="crm-intro"> <h3>' + ((__t = $t("工商信息自动回填设置:")) == null ? "" : __t) + "</h3> <ul> <li> " + ((__t = $t("说明:")) == null ? "" : __t) + "" + ((__t = $t("工商信息自动回填说明")) == null ? "" : __t) + ' </li> </ul> </div> <div class="partner-allowmodify mn-checkbox-box"> <div class="j-checkbox"></div> </div> <div class="partner-allowmodify mn-checkbox-box j-button"> <span class="j-save"></span> </div> </div> </div> </div>';
        }
        return __p;
    };
});