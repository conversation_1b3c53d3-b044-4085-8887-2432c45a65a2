define("crm-setting/businessflow/businessflow",[],function(e,t,n){var s=Backbone.View.extend({initialize:function(e){var t=this;t.setElement(e.wrapper),t.addFlowTag=!1,t.doing=!1,t.viewtype="list",t.$el.addClass("crm-business-table")},render:function(){var n=this;e.async(["paas-paasui/vui","paas-bpm/secondDev"],function(e,t){t.getComponent("manageList").then(function(e){n.inst=FxUI.create({wrapper:n.$el[0],template:"\n              <list></list>",components:{list:e}})})})},destroy:function(){this.inst&&this.inst.$destroy()}});n.exports=s});
define("crm-setting/businessflow/settingpage/settingpage-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="setting-page-config"> ';
            _.each(data.contentList, function(item) {
                __p += ' <div class="config-li ' + ((__t = data.settingValue == item.id ? "active" : "") == null ? "" : __t) + '" data-id="' + ((__t = item.id) == null ? "" : __t) + '"> <span class="config-radio">' + ((__t = item.title) == null ? "" : __t) + "</span> ";
                if (item.content) {
                    __p += " <p>" + ((__t = item.content) == null ? "" : __t) + "</p> ";
                }
                __p += " </div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/businessflow/settingpage/settingpage",["crm-widget/dialog/dialog","crm-widget/select/select","./settingpage-html"],function(t,e,i){var n=CRM.util,s=t("crm-widget/dialog/dialog"),l=(t("crm-widget/select/select"),t("./settingpage-html")),a=s.extend({attrs:{width:510,showBtns:!0,showScroll:!1,className:"crm-d-page-config setting-businessflow"},events:{"click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"cancelHandle","click .config-radio":"triggerRadio"},initialize:function(t){return this.attrs=$.extend(!0,{},a.prototype.attrs,this.attrs,t),this.settingType=t,a.superclass.initialize.apply(this,arguments)},show:function(){var t=a.superclass.show.call(this);return this.getPageConfig(),t},triggerRadio:function(t){t&&t.stopPropagation(),this.element.find(".config-li").removeClass("active");t=$(t.target).closest(".config-li");t.addClass("active"),this.settingValue=t.data("id")},getPageConfig:function(){var e=this,i={skipPageFromToDo:[{id:"TASK_PAGE",title:$t("默认进入任务处理页"),content:$t("在待处理的业务流任务列表中，点击列表条目，默认进入任务处理页。")},{id:"DATA_PAGE",title:$t("默认进入数据详情页"),content:$t("在待处理的业务流任务列表中，点击列表条目，默认进入数据详情页。")}],skipPageFromMobileCompleteTask:[{id:"TASK_PAGE",title:$t("默认停留在任务落地页")},{id:"BEFORE_PAGE",title:$t("默认返回之前页面"),content:"".concat($t("从待办列表进入任务详情页，处理完成后，返回至待办列表;"),"<br>").concat($t("从对象详情页进入任务详情页，处理完成后，返回对象详情页;"))}]};n.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"workflow_bpm",type:e.settingType.type,terminal:e.settingType.terminal},success:function(t){0==t.Result.StatusCode&&(e.settingValue=t.Value.value||"TASK_PAGE",e.element.find(".dialog-con").html(l({data:{contentList:i[e.settingType.type],settingValue:e.settingValue}})))}},{errorAlertModel:1})},hide:function(){return a.superclass.hide.call(this)},confirmHandle:function(t){var e=this;n.FHHApi({url:"/EM1AFLOW/Config/Save",data:{flowType:"workflow_bpm",flowConfigs:[{type:e.settingType.type,value:e.settingValue,terminal:e.settingType.terminal}]},success:function(t){0==t.Result.StatusCode&&e.hide()}},{errorAlertModel:1})},cancelHandle:function(){this.hide()},destroy:function(){s.prototype.destroy.call(this)}});i.exports=a});
define("crm-setting/businessflow/template/detailcon-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="detail-con"> <h3 class="detail-sec-tit"><span>' + ((__t = $t("流程信息")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("名称：")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("流程描述：")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("关联对象：")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("状态：")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("适用范围：")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("创建人：")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("创建时间")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("最后修改人")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("最后修改时间")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("流程图")) == null ? "" : __t) + '</span></h3> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("此处是流程图")) == null ? "" : __t) + '</span> <div class="item-con"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/businessflow/template/page-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-g-tit"> <h2><span class="tit-txt">' + ((__t = $t("业务流程管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="tab-con"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/businessflow/terminationReason/terminationReason-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="bpm-termination-reason"> <div class="config-li"> <div class="bpm-termination-reason-comment">' + ((__t = $t("终止原因是否必填")) == null ? "" : __t) + '</div> <span class="config-radio required-reason">' + ((__t = $t("是")) == null ? "" : __t) + '</span> <span class="config-radio required-reason">' + ((__t = $t("否")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/businessflow/terminationReason/terminationReason",["./terminationReason-html","crm-widget/dialog/dialog"],function(e,t,i){var n=CRM.util,r=e("./terminationReason-html"),e=e("crm-widget/dialog/dialog");i.exports=e.extend({attrs:{title:$t("终止原因配置"),width:500,showBtns:!0},events:{"click .required-reason":"radioHandleRequired","click .b-g-btn":"confirmHandle","click .b-g-btn-cancel":"_closeHandle"},radioHandleRequired:function(e){e&&e.stopPropagation();var t=$(e.target).closest(".required-reason"),i=this.element.find(".config-li .required-reason"),e=$(e.target).closest(".required-reason").index();t.hasClass("active")||(i.removeClass("active"),t.addClass("active"),this.required=e-1)},confirmHandle:function(){var t=this,e={flowType:"workflow_bpm",type:"BPM_CANCEL_OPINION_REQUIRED",terminal:"ALL",value:0===t.required};n.FHHApi({url:"/EM1AFLOW/Config/Save",data:e,success:function(e){t._closeHandle()}},{errorAlertModel:2})},show:function(e){var t=this;return n.FHHApi({url:"/EM1AFLOW/Config/Get",data:{flowType:"workflow_bpm",type:"BPM_CANCEL_OPINION_REQUIRED",terminal:"ALL"},success:function(e){e=e.Value.value;t.required=e?0:1,t.initBtnStatus()}},{errorAlertModel:2}),result=i.exports.superclass.show.call(this),t.setContent(r()),t.resizedialog(),result},initBtnStatus:function(){this.element.find(".config-li .required-reason")[this.required].classList.add("active")},_closeHandle:function(){this.hide()},destroy:function(){return i.exports.superclass.destroy.call(this)}})});