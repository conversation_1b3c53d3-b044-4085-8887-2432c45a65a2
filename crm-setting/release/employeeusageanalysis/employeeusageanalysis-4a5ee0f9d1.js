function asyncGeneratorStep(e,n,t,r,i,o,s){try{var a=e[o](s),c=a.value}catch(e){return void t(e)}a.done?n(c):Promise.resolve(c).then(r,i)}function _asyncToGenerator(a){return function(){var e=this,s=arguments;return new Promise(function(n,t){var r=a.apply(e,s);function i(e){asyncGeneratorStep(r,n,t,i,o,"next",e)}function o(e){asyncGeneratorStep(r,n,t,i,o,"throw",e)}i(void 0)})}}define("crm-setting/employeeusageanalysis/employeeusageanalysis",[],function(e,n,t){var r=Backbone.View.extend({initialize:function(r){var n,i=this;Fx.getBizComponent("fbi","EmployeeUsageAnalysisService").then((n=_asyncToGenerator(regeneratorRuntime.mark(function e(n){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,n();case 2:t=e.sent.default,i.ins=new t(r),i.ins.init();case 5:case"end":return e.stop()}},e)})),function(e){return n.apply(this,arguments)}))},destroy:function(){this.ins&&this.ins.destroy&&this.ins.destroy()}});t.exports=r});