define("crm-setting/rolemanage/editcustomizerole/editcustomizerole",["crm-modules/common/util","crm-widget/select/select","crm-widget/dialog/dialog","../template/edit-customizerole-html"],function(e,t,s){var o=e("crm-modules/common/util"),i=e("crm-widget/select/select"),l=e("crm-widget/dialog/dialog"),r=e("../template/edit-customizerole-html"),n=l.extend({isSelected:!1,attrs:{title:$t("新建角色"),content:'<div class="crm-loading "></div>',showBtns:!0,showScroll:!1,datas:null},events:{"click .b-g-btn-cancel":"destroy","click .b-g-btn":"onSave","focus .fm-ipt":"onRemoveErr","click .j-toggle":"onToggle"},render:function(){var e=this.get("type"),t=n.superclass.render.call(this);return this.setContent(r({datas:this.get("datas"),type:e})),"add"===e&&this.initSelect(),t},show:function(){return n.superclass.show.call(this)},initSelect:function(){this.selectOptions={$wrap:$(".j-select-box",this.$el),zIndex:1010,disabled:!0,options:this.get("roleList")},this.select=new i(this.selectOptions)},onToggle:function(e){var t=$(e.currentTarget),t=(t.toggleClass("mn-selected"),this.isSelected=t.hasClass("mn-selected"),_.extend({},this.selectOptions,{disabled:!this.isSelected}));return this.select.destroy(),this.select=new i(t),e.stopPropagation(),!1},onSave:function(){var e=this.$(".rolename-ipt"),t=this.$(".roledes-ipt"),s=e.val().trim(),i=t.val().trim();return""==s||0===s.length?(o.showErrmsg(e,$t("角色名称不能为空")),!1):20<s.length?(o.showErrmsg(e,$t("角色名称不能超过20个中文字符")),!1):""==i||0===i.length?(o.showErrmsg(t,$t("角色描述不能为空")),!1):2e3<i.length?(o.showErrmsg(t,$t("备注不能超过2000个中文字符")),!1):o.isContainEmojiCharacter(s)?(o.showErrmsg(e,$t("角色名称不能包含emoji表情符")),!1):o.isContainEmojiCharacter(i)?(o.showErrmsg(t,$t("角色描述不能包含emoji表情符")),!1):void this.submit(_.extend({roleCode:this.get("datas")?this.get("datas").id:null,roleName:s,description:i,isCopy:this.isSelected},{roleType:this.get("roleType")}))},onRemoveErr:function(e){$(e.currentTarget).next().remove()},submit:function(e){var t=this,s="add"===t.get("type");o.FHHApi({url:s?"/EM1HPAASUdobj/roleApi/addRole":"/EM1HPAASUdobj/roleApi/updateRole",data:e,success:function(e){0==e.Result.StatusCode&&(t.trigger("success",s?{sourceRoleCode:t.isSelected&&t.select.getValue(),destRoleCode:e.Value}:null),t.hide())}},{submitSelector:t.$(".b-g-btn")})},destroy:function(){return n.superclass.destroy.call(this)},hide:function(){return n.superclass.hide.call(this)}});s.exports=n});
define("crm-setting/rolemanage/examinepermissions/editexaminerole",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","./template/edit-examinerole-html"],function(e,t,i){var l=e("crm-modules/common/util"),s=e("crm-widget/dialog/dialog"),r=e("crm-widget/selector/selector"),c=e("./template/edit-examinerole-html"),o=s.extend({attrs:{title:$t("添加员工"),content:'<div class="crm-loading "></div>',showBtns:!0,showScroll:!1,type:"add",delEmp:[],data:null,FiledList:null,className:"crm-s-rolemanage"},events:{"click .b-g-btn":"onSave","click .b-g-btn-cancel":"destroy"},show:function(e){var t=this.get("FiledList"),i=o.superclass.show.call(this);return this.widget=[],this.set(e),this.setContent(c({subtitle:t.subtitle,AllowLeaderFinanceConfirm:t.AllowLeaderFinanceConfirm})),this.widgetCircleFn(),i},widgetCircleFn:function(){var e=this,t=new r({$wrap:e.$(".widget-circle"),zIndex:1e3,group:!0,single:!1,label:$t("crm.选择部门"),defaultSelectedItems:{group:_.pluck(e.get("FiledList").circleIds,"id")}});t.on("addItem",function(){e.$(".widget-circle").next().remove()}),e.circleWidget=t},onSave:function(){var e=this.circleWidget.getValue("group"),t=this.get("FiledList");if(e&&0==e.length)return l.showErrmsg($(".widget-circle",this.element),$t("请选择部门！")),!1;this.submit({CircleIDs:e,EmployeeID:t.EmployeeID,RoleID:t.RoleID,AllowLeaderFinanceConfirm:!!this.$(".mn-selected").length})},submit:function(e){var t=this,i="/EM1HCRM/ApprovalPermission/SetEmployeeRoleCircle";!FS.util.getUserAttribute("approveServerApi")&&-1==window.location.host.indexOf("hwcloud")||(i="/EM1HNCRM/API/v1/object/ApprovalPermission/service/update"),l.FHHApi({url:i,data:e,success:function(e){0==e.Result.StatusCode?(t.trigger("success"),l.remind(1,$t("操作成功！")),t.hide()):l.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},destroy:function(){return _.each(this.widget,function(e){e&&e.destroy&&e.destroy()},this),this.circleWidget&&this.circleWidget.destroy(),o.superclass.destroy.call(this)},hide:function(){return _.each(this.widget,function(e){e&&e.destroy&&e.destroy()},this),this.circleWidget&&this.circleWidget.destroy(),this.widget=null,o.superclass.hide.call(this)}});i.exports=o});
define("crm-setting/rolemanage/examinepermissions/index",["crm-modules/common/util","crm-widget/selector/selector","crm-widget/table/table","./editexaminerole","./template/index-html"],function(e,t,i){var a=e("crm-modules/common/util"),l=e("crm-widget/selector/selector"),o=e("crm-widget/table/table"),r=e("./editexaminerole"),n=e("./template/index-html");i.exports=Backbone.View.extend({events:{"click .role-item":"onClickItem"},initialize:function(){this.el.innerHTML=n(),this.examineSubtitle="".concat($t("crm.销售订单"),"/").concat($t("crm.退货单")),this.renderExamineRoleTable()},renderExamineRoleTable:function(){var e,n=this;n.examineRoleID="00000000000000000000000000000016",n.examineDt?n.examineDt.setParam({},!0):(e="/EM1HCRM/ApprovalPermission/GetEmployeeRoleCircleList",!FS.util.getUserAttribute("approveServerApi")&&-1==window.location.host.indexOf("hwcloud")||(e="/EM1HNCRM/API/v1/object/ApprovalPermission/service/list"),n.examineDt=new o({$el:n.$(".right-wrapper"),url:e,requestType:"FHHApi",showFilerBtn:!1,showMultiple:!1,trHandle:!1,autoWidth:!0,search:{pos:"T",placeHolder:$t("搜索员工"),type:"Keyword",fileName:"Keyword"},postData:{CircleIDs:"",RoleID:n.examineRoleID},columns:[{data:"Name",title:$t("姓名")},{data:"Department",title:$t("crm.部门")},{data:"Post",title:$t("职位")},{data:"",title:$t("审批负责部门"),render:function(e,t,i){i=_.map(i.CircleSolidInfoList,function(e){return e.Name}).join($t("、"));return i?'<span title="'+i+'">'+i+"</span>":"--"}},{data:"",title:$t("操作"),render:function(e,t,i){return'<div><a class="role-edit" style="margin-right: 5px;cursor:pointer;">'+$t("编辑审批部门")+"</a></div>"}}],formatData:function(e){var t=[];return _.each(e.EmployeeRoleScopeInfoList,function(e){t.push(_.extend({},e.EmployeeInfo,{CircleSolidInfoList:e.CircleSolidInfoList,AllowLeaderFinanceConfirm:e.AllowLeaderFinanceConfirm}))}),{totalCount:e.Page&&e.Page.TotalCount||0,data:t}},initComplete:function(e){$(".first-target-item",e).before(['<div class="item batch-c">','<span class="item-tit">'+$t("范围")+"：</span>",'<div class="item-con selector j-selector"></div>',"</div>"].join("")),n.initExamineCircle()}}),n.examineDt.on("trclick",function(e,t,i){i.hasClass("role-edit")&&n.editExamineRoleHandle(e,t)}))},initExamineCircle:function(){var n=this;n.examineCircleWidget&&n.examineCircleWidget.destroy(),n.examineCircleWidget=new l({$wrap:n.$(".j-selector"),zIndex:1e3,group:!0,single:!0,selectedAfterHideLabel:!0,label:$t("选择审批负责部门")}),n.examineCircleWidget.on("addItem",function(){a.hideErrmsg(this.$el.closest(".v-box"))}),n.examineCircleWidget.on("change",function(e){var t=this.getValue("group"),i="";0<t.length&&(i=[],_.each(t,function(e,t){i.push(+e)})),n.examineDt.setParamByKey("Keyword",n.$(".dt-ipt").val()),n.examineDt.setParam({CircleIDs:i},!0,!0)})},editExamineRoleHandle:function(e,t){var i=this,n=[];_.each(e.CircleSolidInfoList,function(e){n.push({id:e.CircleID,type:"g"})}),i.editExamineRoleWidget&&(i.editExamineRoleWidget.destroy(),i.editExamineRoleWidget=null),i.editExamineRoleWidget=new r({title:$t("编辑审批部门"),FiledList:_.extend({EmployeeID:e.EmployeeID,AllowLeaderFinanceConfirm:e.AllowLeaderFinanceConfirm,RoleID:i.examineRoleID,circleIds:n,subtitle:i.examineSubtitle})}),i.editExamineRoleWidget.on("success",function(){i.examineDt.setParam({},!0)}),i.editExamineRoleWidget.show()},onClickItem:function(e){e=$(e.currentTarget);e.hasClass("cur")||(this.$(".leftnav .cur").removeClass("cur"),e.addClass("cur"),this.$(".dt-ipt").val(""),this.$(".remove-h").click(),this.examineRoleID=e.data("roleid"),this.examineCircleWidget.clearAll(),this.$(".dt-ipt").val(""),this.examineDt._keyWord="",this.examineDt.refresh({CircleIDs:"",Keyword:"",RoleID:this.examineRoleID}),this.examineSubtitle=e.data("subtitle"))},destroy:function(){var t=this;_.each(["examineDt","examineCircleWidget","editExamineRoleWidget"],function(e){t[e]&&t[e].destroy(),t[e]&&(t[e]=null)})}})});
define("crm-setting/rolemanage/examinepermissions/template/edit-examinerole-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-g-form mn-checkbox-box edit-power-box"> <div class="fm-item"> <div style="font-size: 13px;line-height: 20px;">' + ((__t = $t("crm.配置后可以提交所有", {
                subtitle: subtitle
            })) == null ? "" : __t) + '</div> <div class="fm-wrap" style="margin: 20px 0 10px;"> <div class="widget-circle"></div> </div> <div class="fm-wrap"> <span class="mn-checkbox-item ' + ((__t = AllowLeaderFinanceConfirm ? "mn-selected" : "") == null ? "" : __t) + '"></span><span class="check-lb">' + ((__t = $t("当前部门所有下属部门")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/rolemanage/examinepermissions/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="leftnav crm-scroll"> <div class="item-group">' + ((__t = $t("财务角色")) == null ? "" : __t) + '</div> <ul data-groupid="00000000000000000000000000000003"> <li class="item role-item cur" data-roleid="00000000000000000000000000000016" data-name="' + ((__t = $t("订单管理员")) == null ? "" : __t) + '" data-subtitle="' + ((__t = $t("crm.销售订单")) == null ? "" : __t) + "/" + ((__t = $t("crm.退货单")) == null ? "" : __t) + '">' + ((__t = $t("订单管理员")) == null ? "" : __t) + '</li> <li class="item role-item" data-roleid="00000000000000000000000000000003" data-name="' + ((__t = $t("订单财务")) == null ? "" : __t) + '" data-subtitle="' + ((__t = $t("crm.销售订单")) == null ? "" : __t) + "/" + ((__t = $t("crm.退货单")) == null ? "" : __t) + '">' + ((__t = $t("订单财务")) == null ? "" : __t) + '</li> <li class="item role-item" data-roleid="00000000000000000000000000000002" data-name="' + ((__t = $t("crm.回款财务")) == null ? "" : __t) + '" data-subtitle="' + ((__t = $t("回款单")) == null ? "" : __t) + '">' + ((__t = $t("crm.回款财务")) == null ? "" : __t) + '</li> <li class="item role-item" data-roleid="00000000000000000000000000000004" data-name="' + ((__t = $t("crm.退款财务")) == null ? "" : __t) + '" data-subtitle="' + ((__t = $t("退款单")) == null ? "" : __t) + '">' + ((__t = $t("crm.退款财务")) == null ? "" : __t) + '</li> <li class="item role-item" data-roleid="00000000000000000000000000000005" data-name="' + ((__t = $t("开票财务")) == null ? "" : __t) + '" data-subtitle="' + ((__t = $t("crm.开票申请")) == null ? "" : __t) + '">' + ((__t = $t("开票财务")) == null ? "" : __t) + '</li> </ul> <div class="item-group">' + ((__t = $t("仓储角色")) == null ? "" : __t) + '</div> <ul data-groupid="00000000000000000000000000000003"> <li class="item role-item" data-roleid="00000000000000000000000000000020" data-name="' + ((__t = $t("crm.发货人员")) == null ? "" : __t) + '">' + ((__t = $t("crm.发货人员")) == null ? "" : __t) + '</li> </ul> </div> <div class="right-wrapper"> <div class="crm-loading"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/rolemanage/mainroleset/mainroleset",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/select/select","./template/mainroleset-html"],function(e,t,s){var o=e("crm-modules/common/util"),r=e("crm-widget/dialog/dialog"),l=e("crm-widget/select/select"),c=e("./template/mainroleset-html"),a=r.extend({attrs:{title:$t("提示"),content:'<div class="crm-loading"></div>',showScroll:!0,showBtns:!0,datas:null,className:"crm-s-rolemanage"},events:{"click .b-g-btn-cancel":"destroy","click .b-g-btn":"submit"},render:function(){var t,s=this,e=a.superclass.render.call(s);return s.select=[],o.FHHApi({url:"/EM1HPAASUdobj/userApi/getUserInfoByMajorRole",data:{roleCode:s.get("roleCode"),userIds:s.get("userIds")},success:function(e){0==e.Result.StatusCode?0===(t=_.map(e.Value,function(e){return e.userId})).length?(s.hide(),s.trigger("success")):(s.roleCodeList=_.map(e.Value,function(e){return e.roleCode2RoleNameMap}),s.userIdList=t,s.setContent(c({userIdList:t,rolename:s.get("rolename")})),_.each(s.select,function(e){e.destroy&&e.destroy()}),s.select=[],_.each(e.Value,function(e,t){s.select[t]=new l({$wrap:s.$(".j-select").eq(t),zIndex:2010,options:e.roleCode2RoleNameMap})}),s.resizedialog()):(o.alert(e.Result.FailureMessage),s.hide())}},{errorAlertModel:1}),e},show:function(){return a.superclass.show.call(this)},submit:function(){var r=this,l={};_.each(r.userIdList,function(e,t){var s={};_.each(r.roleCodeList[t],function(e){e.value===r.select[t].getValue()?s[e.value]=!0:s[e.value]=!1}),l[e]=s}),o.FHHApi({url:"/EM1HPAASUdobj/userApi/updateUserMajorRoleRespectively",data:{userId2RoleInfoMap:l},success:function(e){r.trigger("success"),r.hide()}},{errorAlertModel:1})},hide:function(){return a.superclass.hide.call(this)},destroy:function(){return _.each(this.select,function(e){e.destroy&&e.destroy()}),a.superclass.destroy.call(this)}});s.exports=a});
define("crm-setting/rolemanage/mainroleset/template/mainroleset-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="rolemanage-dialog-info"> ' + ((__t = $t("crm.确认新的主角色", {
                rolename: rolename
            })) == null ? "" : __t) + ' </div> <dl class="rolemanage-dialog-head crm-g-form"> <dt class="fm-lb">' + ((__t = $t("员工")) == null ? "" : __t) + "</dt> <dd>" + ((__t = $t("新的主角色")) == null ? "" : __t) + '</dd> </dl> <div class="crm-g-form select-box"> ';
            _.each(userIdList, function(id) {
                __p += ' <dl class="select-list"> <dt class="fm-lb">' + ((__t = FS.contacts.getEmployeeById(id) ? FS.contacts.getEmployeeById(id).name : "--") == null ? "" : __t) + '</dt> <dd class="select j-select""></dd> </dl> ';
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/rolemanage/permissionsset/confirmdialog",["crm-widget/dialog/dialog"],function(t,e,n){var s=t("crm-widget/dialog/dialog").extend({attrs:{title:$t("提示"),classPrefix:"crm-c-confirm-dialog crm-c-dialog",content:$t("本次权限更改尚未保存是否先保存本次更改。"),showBtns:!0},events:{"click .b-g-btn-cancel":"destroy","click .b-g-btn":"save","click .b-g-btn-not-save":"notSave"},render:function(){var t=s.superclass.render.call(this);return $(".dialog-btns",s.element).prepend('<span class="b-g-btn-not-save">'+$t("不保存")+"</span>"),$(".dialog-btns .b-g-btn",s.element).html($t("保存")),t},show:function(){return s.superclass.show.call(this)},save:function(){this.trigger("save"),this.hide()},notSave:function(){this.trigger("notSave"),this.hide()},destroy:function(){return s.superclass.destroy.call(this)},hide:function(){return s.superclass.hide.call(this)}});n.exports=s});
define("crm-setting/rolemanage/permissionsset/fieldprivilege/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="rolemanage-dialog-info">' + ((__t = $t("crm.设置字段权限提示")) == null ? "" : __t) + '</div> <div class="search-wrap"><span>' + ((__t = $t("搜索")) == null ? "" : __t) + ':</span><input class="b-g-ipt j-s-field" placeholder="' + ((__t = $t("搜索字段名")) == null ? "" : __t) + '"/></div> <dl class="rolemanage-dialog-head crm-g-form"> <dt class="fm-lb"><em></em>' + ((__t = $t("全选")) == null ? "" : __t) + '</dt> <dd class="mn-radio-box radio-box-head"> <span class="radio-item"> <span data-permissiontype="2" class="mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + '"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span data-permissiontype="1" class="mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + '"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span data-permissiontype="0" class="mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + '"></span><span class="radio-lb">' + ((__t = $t("不可见")) == null ? "" : __t) + '</span> </span> </dd> </dl> <div class="rolemanage-dialog-body crm-g-form"> ';
            _.each(fieldInfoList, function(item, index) {
                __p += " ";
                var fieldName = item.fieldName;
                __p += ' <dl> <dt class="fm-lb" title="' + ((__t = item.fieldCaption) == null ? "" : __t) + '"><em>';
                if (item.isRequire) {
                    __p += "*";
                }
                __p += '</em><div class="filed-caption">' + __e(item.fieldCaption) + '</div></dt> <dd class="mn-radio-box radio-box-body ';
                if ([ "product_amount", "order_amount", "discount" ].indexOf(fieldName) > -1) {
                    __p += "saleitem";
                }
                __p += " ";
                if ([ "ProductPrice", "Discount", "Price" ].indexOf(fieldName) > -1) {
                    __p += "orderitem";
                }
                __p += '" data-fieldname=' + ((__t = fieldName) == null ? "" : __t) + '> <span class="radio-item"> <span class="mn-radio-item ';
                if (item.status === 2) {
                    __p += "mn-selected";
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfWrite) {
                    __p += "disabled-selected";
                }
                __p += '" data-permissiontype="2"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="mn-radio-item ';
                if (item.status === 1) {
                    __p += "mn-selected";
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfReadOnly || item.isRequire && readonly) {
                    __p += "disabled-selected";
                }
                __p += '" data-permissiontype="1"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="mn-radio-item ';
                if (item.status === 0) {
                    __p += "mn-selected";
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfInvisible || item.isRequire && readonly) {
                    __p += "disabled-selected";
                }
                __p += '" data-permissiontype="0"></span><span class="radio-lb">' + ((__t = $t("不可见")) == null ? "" : __t) + "</span> </span> </dd> </dl> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/rolemanage/permissionsset/fieldprivilege/index",["crm-modules/common/util","crm-widget/dialog/dialog"],function(e,s,a){var t=e("crm-modules/common/util"),e=e("crm-widget/dialog/dialog"),r="mn-selected",n="disabled-selected",l="."+r,i=e.extend({attrs:{title:$t("字段权限"),content:'<div class="crm-loading "></div>',showScroll:!0,showBtns:!0,datas:null,className:"crm-s-rolemanage"},events:{"click .radio-box-body .mn-radio-item":"onCheck","click .radio-box-head .mn-radio-item":"onCheckAll","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy","input .j-s-field":"onInput","keypress .j-s-field":"onKeyPress"},replaceRegExp:function(e){return(e+="").replace(/\(/g,"\\(").replace(/\-/g,"\\-").replace(/\)/g,"\\)").replace(/\[/g,"\\[").replace(/\]/g,"\\]").replace(/\{/g,"\\{").replace(/\}/g,"\\}").replace(/\+/g,"\\+").replace(/\./g,"\\.")},highligh:function(e,s){var a;return s&&(s=_.escape(s),a=new RegExp(this.replaceRegExp(s),"gi"),s)?(e+="").replace(a,function(e){return'<span class="dt-mark">'+e+"</span>"}):e},onInput:function(){var o=this;o.searchTimer&&(clearTimeout(o.searchTimer),o.searchTimer=null),o.searchTimer=setTimeout(function(){var i=o.$(".j-s-field").val();o.$(".filed-caption").each(function(e,s){var a=$(s).text();$(s).html(o.highligh(a,i))})},100)},onKeyPress:function(e){var s=this;13==e.which&&0<s.$(".dt-mark").length&&(e=s.$(".dt-mark").offset().top-s.$(".dialog-scroll").offset().top,s.$(".dialog-scroll-el").scrollTop(e-30))},render:function(){return i.superclass.render.call(this)},show:function(e){return i.superclass.show.call(this)},onCheck:function(e){var a,i,s=$(e.currentTarget),o=this.$(".radio-box-head .mn-radio-item");return s.hasClass(n)?(e.stopPropagation(),!1):s.closest(".radio-box-body").find(l).hasClass(n)?(t.remind(3,$t("crm.已选中项不可编辑")),void e.stopPropagation()):(s.closest(".radio-box-body").find(".mn-radio-item").each(function(e,s){$(s).removeClass(r)}),s.toggleClass(r),a=s.data("permissiontype"),s.closest(".radio-box-body").hasClass("saleitem")&&this.$(".saleitem").each(function(e,s){(i=$(s).find(".mn-radio-item")).hasClass(n)||($(s).find(".mn-radio-item").removeClass(r),i.eq(2-a).addClass(r))}),s.closest(".radio-box-body").hasClass("orderitem")&&this.$(".orderitem").each(function(e,s){(i=$(s).find(".mn-radio-item")).hasClass(n)||($(s).find(".mn-radio-item").removeClass(r),i.eq(2-a).addClass(r))}),e=_.every(this.$(".radio-box-body"),function(e,s){return $(e).find(l).data("permissiontype")===a}),o.removeClass(r),e&&o.eq(2-a).addClass(r),!1)},onCheckAll:function(e){var a,s=$(e.currentTarget),i=s.data("permissiontype");if(s.hasClass(n))return e.stopPropagation(),!1;_.each(this.$(".radio-box-body"),function(e,s){(a=$(e).find(".mn-radio-item")).eq(2-i).hasClass(n)||(a.removeClass(r),a.eq(2-i).addClass(r))})},onSubmit:function(){var a={};_.each(this.$(".radio-box-body"),function(e,s){a[$(e).data("fieldname")]=$(e).find(l).data("permissiontype")}),this.trigger("submit",a)},destroy:function(){return i.superclass.destroy.call(this)}});a.exports=i});
define("crm-setting/rolemanage/permissionsset/index",["./leftnav","./rightcontent","./template/index-html"],function(t,e,n){var r=t("./leftnav"),l=t("./rightcontent"),a=t("./template/index-html"),o=CRM.util;n.exports=Backbone.View.extend({events:{"click .j-toggle-role":"onToggleRole"},initialize:function(){this.isOutRoles=!1,this.render()},render:function(n){var o=this,i=o.isOutRoles,s=(o.el.innerHTML=a({isOutRoles:i}),o.$(".right-wrapper"));o.getRoleList(function(t){o.destroy();var e=t.length&&t[0].roleInfoList[0]||{};n&&_.each(t,function(t){_.each(t.roleInfoList,function(t){t.roleCode===n&&(e=t)})}),o.leftnav=new r({el:o.$(".leftnav"),data:t,roleCode:e.roleCode,isOutRoles:i,hiddenOps:!!o.options.data}),o.rightcontent=new l({el:s,data:e,searchData:o.options.data,isOutRole:i}),o.leftnav.on("click_item",function(t){o.rightcontent.render({el:s,data:t,searchData:o.options.data,isOutRole:i})}),o.leftnav.on("refresh",function(t){o.render(t)}),o.rightcontent.on("reset",function(t){o.rightcontent.render(t)})})},getRoleList:function(e){o.FHHApi({url:"/EM1HPAASUdobj/roleApi/roleList",data:{isOutRoles:this.isOutRoles},success:function(t){0===t.Result.StatusCode&&(t=_.compact(t.Value),-1===o.findIndex(t,function(t){return 1===t.groupType})&&t.push({groupName:$t("自定义角色"),groupType:1,roleInfoList:[]}),e(t))}})},onToggleRole:function(){this.isOutRoles=!this.isOutRoles,this.render()},destroy:function(){_.each(["leftnav","rightcontent"],function(t){this[t]&&(this[t].destroy(),this[t]=null)},this)}})});
define("crm-setting/rolemanage/permissionsset/leftnav",["../template/leftnav-html","./template/outrole-list-html","./confirmdialog","../editcustomizerole/editcustomizerole","../mainroleset/mainroleset"],function(e,t,o){var i=e("../template/leftnav-html"),s=e("./template/outrole-list-html"),l=e("./confirmdialog"),d=e("../editcustomizerole/editcustomizerole"),r=e("../mainroleset/mainroleset"),a=CRM.util;o.exports=Backbone.View.extend({initialize:function(e){this.roleCode=e.roleCode,this.isOutRoles=e.isOutRoles,this.roleList=_.compact(e.data),this.searchRoleList=null,this.isOutRoles?this.el.innerHTML=s({roles:this.roleList,roleCode:this.roleCode}):this.el.innerHTML=i({roles:this.roleList,roleCode:this.roleCode,isPermissionsSet:!0,searchRoleList:this.searchRoleList}),e.hiddenOps&&(this.$(".j-add").hide(),this.$(".customize-rolelist .ops").hide()),this.$(".rolelist-box .item_box").eq(0).show()},events:{"click .default-rolelist li":"onGetRoleFcByRoleId","click .customize-rolelist li":"onGetDefaultRoleList","click .j-add":"onAdd","click .checkbox-head-toggle":"onClickArrow","keyup .j-s-field":"onKeyUp"},onClickArrow:function(e){var e=$(e.currentTarget),t=e.next();"none"==t.css("display")?e.addClass("current"):e.removeClass("current"),t.slideToggle()},onKeyUp:function(e){var t=this.$(".j-s-field").val();(t&&13==e.which||!t)&&this.search(t)},search:function(t){var o,e=this;t?(o=[],e.$(".item_box .item").each(function(){var e=$(this).data().rolename||"";t&&0<=e.toString().indexOf(t)&&o.push($(this).data())}),e.searchRoleList=o,e.el.innerHTML=i({roles:e.roleList,roleCode:e.roleCode,isPermissionsSet:!0,searchRoleList:e.searchRoleList}),e.$(".rolelist-box").hide()):(e.searchRoleList=null,e.$(".rolelist-box").show(),e.$(".search-rolelist-box").remove(),e.$(".item[data-id=".concat(e.roleCode,"]")).addClass("cur")),e.$(".j-s-field").val(t).focus()},onGetRoleFcByRoleId:function(t){var o=this,i=$(t.currentTarget);i.hasClass("cur")||(o.toSubmit?(o.confirmdialogWidget&&(o.confirmdialogWidget.destroy(),o.confirmdialogWidget=null),o.confirmdialogWidget=new l({title:$t("提示")}),o.confirmdialogWidget.on("save",function(e){o.onSave(),o.roleType=i.data("type"),o.jumpPage(t),o.toSubmit=!1}),o.confirmdialogWidget.on("notSave",function(e){o.roleType=i.data("type"),o.jumpPage(t),o.toSubmit=!1}),o.confirmdialogWidget.show()):(o.roleType=i.data("type"),o.jumpPage(t)))},onGetDefaultRoleList:function(e){var t=this,o=$(e.target),i=o.closest("li").data();o.hasClass("crm-ico-del")?t.onDel(i):o.hasClass("crm-ico-edite")?(t.addCustomizeRoleWidget&&t.addCustomizeRoleWidget.destroy(),t.addCustomizeRoleWidget=new d({title:$t("编辑角色"),type:"edit",datas:i,height:308}),t.addCustomizeRoleWidget.on("success",function(e){t.toSubmit=!1,t.trigger("refresh",t.roleCode)}),t.addCustomizeRoleWidget.show()):o.has("li")&&t.onGetRoleFcByRoleId(e)},jumpPage:function(e){e=$(e.currentTarget);this.roleCode=e.data().id,this.$(".item").removeClass("cur"),e.addClass("cur"),this.trigger("click_item",e.data())},onAdd:function(e){e.stopPropagation();var t=this,o=[];_.each(t.roleList,function(e){_.each(e.roleInfoList,function(e){e.roleCode!==CRM.config.MANAGER_ROLE_CODE&&o.push({value:e.roleCode,name:e.roleName})})}),t.addCustomizeRoleWidget&&t.addCustomizeRoleWidget.destroy(),t.addCustomizeRoleWidget=new d({title:$t("新建角色"),type:"add",roleList:o,roleType:t.isOutRoles?"4":"2"}),t.addCustomizeRoleWidget.on("success",function(e){e.sourceRoleCode&&a.FHHApi({url:"/EM1HPAASUdobj/roleApi/copyRole",data:{destRoleCode:e.destRoleCode,sourceRoleCode:e.sourceRoleCode},success:function(e){0==e.Result.StatusCode&&a.remind(1,$t("新建成功"))}}),t.toSubmit=!1,t.trigger("refresh",e.destRoleCode)}),t.addCustomizeRoleWidget.show()},onDel:function(e){var t=this,o=a.confirm("<p>"+$t("确定删除本角色")+'?</p><p class="crm-s-rolemanage-dialog-text">'+$t("crm.删除角色员工权限会受影响")+"</p>",$t("删除"),function(){t.mainRoleSetWidget&&t.mainRoleSetWidget.destroy(),t.mainRoleSetWidget=new r({roleCode:e.id}),t.mainRoleSetWidget.show(),o.hide(),t.mainRoleSetWidget.on("success",function(){a.FHHApi({url:"/EM1HPAASUdobj/roleApi/deleteRole",data:{roleCode:e.id},success:function(e){0==e.Result.StatusCode&&(a.remind(1,$t("操作成功")),o.destroy(),t.toSubmit=!1,t.trigger("refresh",t.roleCode))},error:function(){o.show()}})})})},destroy:function(){this.$el.off(),_.each(["confirmdialogWidget","addCustomizeRoleWidget","mainRoleSetWidget"],function(e){this[e]&&(this[e].destroy(),this[e]=null)},this)}})});
define("crm-setting/rolemanage/permissionsset/rightcontent",["crm-modules/common/util","./template/right-content-html","./template/error-html","./roleprivilege/index"],function(e,t,i){var r=e("crm-modules/common/util"),n=e("./template/right-content-html"),l=e("./template/error-html"),a=e("./roleprivilege/index"),s="b-g-btn-disabled";i.exports=Backbone.View.extend({ajax:null,initialize:function(e){this.render(e)},render:function(e){var o=this,t=e.data;o.options=e,o.roleCode=t.id||t.roleCode,o.roleType=t.type||t.roleType,o.isAllFold=!1,o.roleCode&&(o.$el.html(n({description:t.description,rolename:t.rolename||t.roleName,rolecode:o.roleCode,roletype:o.roleType})),o.options.searchData&&o.$(".j-s-field").parent().hide(),o.ajax&&o.ajax.abort(),o.ajax=r.FHHApi({url:"/EM1HPAASUdobj/roleApi/getRolePrivilegeWithLinkage",data:{roleCode:o.roleCode,roleType:o.roleType,isOutRole:e.isOutRole,appId:e.isOutRole?"PRM":"CRM"},success:function(e){0===e.Result.StatusCode?(o.rolePrivilegeData=e.Value,o.roleprivilege&&o.roleprivilege.destroy(),o.roleprivilege=new a({el:o.$(".role-privilege-wrapper"),roleCode:o.roleCode,data:o.formatData(e.Value.privilegeLists),linkData:e.Value}),o.initOriginNosArr(),o.roleprivilege.on("check",function(){o.$(".j-save").removeClass(s)}),o.options.searchData&&o.$(".lazyload-item").each(function(){var e=$(this).data().descapiname,t=$(this),i=o.options.searchData;i&&e!=i?t.hide():t.show()})):o.$el.html(l())},complete:function(){o.ajax=null}},{errorAlertModel:1}))},events:{"click .j-save":"onSave","click .j-reset":"onReset","click .j-refresh":"onRefresh","keyup .j-s-field":"onKeyUp","click .j-fold":"onFold"},onKeyUp:function(e){var t=this.$(".j-s-field").val();(t&&13==e.which||!t)&&this.search(t)},search:function(i){this.$(".lazyload-item").each(function(){var e=$(this).data().descapidisplayname||"",t=$(this);i&&e.toString().indexOf(i)<0?t.hide():t.show()})},onFold:function(){var e=this,t=(e.isAllFold=!e.isAllFold,e.$(".lazyload-item .mn-checkbox-box")),i=e.$(".collapse-icon"),o=e.$(".checkbox-head-toggle");e.isAllFold?o.removeClass("current"):o.addClass("current"),e.isAllFold?t.hide():t.show(),i.attr("xlink:href",e.isAllFold?"#zhankai2":"#shouqi")},formatData:function(e){return _.filter(e,function(e){return!("Visit"===e.descApiName&&CRM.control.isYunZhiJia)})},onSave:function(e){var t=this,i=[],o=[];$(e.currentTarget).hasClass(s)||(t.$(".mn-selected").each(function(e,t){$(t).data("functionnumber")&&!$(t).hasClass("j-set")&&i.push($(t).data("functionnumber")),$(t).hasClass("look-item")&&!$(t).hasClass("bi-item")&&o.push($(t).data("functionnumber"))}),t.$("script").each(function(e,t){$($(t).html()).find(".mn-selected").each(function(e,t){$(t).data("functionnumber")&&i.push($(t).data("functionnumber")),$(t).hasClass("look-item")&&!$(t).hasClass("bi-item")&&o.push($(t).data("functionnumber"))})}),r.FHHApi({url:"/EM1HPAASUdobj/roleApi/updateRolePrivilege",data:{roleType:t.roleType,roleCode:t.roleCode,funcCode:i,updatedViewListFuncCode:t.originNosArr.length>o.length?_.difference(t.originNosArr,o):_.difference(o,t.originNosArr)},success:function(e){0===e.Result.StatusCode&&(r.remind(1,$t("保存成功")),t.toSubmit=!1,t.$(".j-save").addClass(s),t.initOriginNosArr())}},{submitSelector:e?$(e.currentTarget):""}))},onReset:function(e){var t,i=this;$(e.currentTarget).hasClass(s)||(t=r.confirm($t("是否恢复默认设置？"),$t("提示"),function(){t.destroy(),r.FHHApi({url:"/EM1HPAASUdobj/roleApi/resetRole",data:{roleCode:i.roleCode},success:function(e){0===e.Result.StatusCode?(r.remind(1,$t("保存成功")),i.toSubmit=!1,i.trigger("reset",i.options)):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1})}))},onRefresh:function(){this.render(this.options)},initOriginNosArr:function(){var i=this;i.originNosArr=[],i.$(".mn-selected.look-item:not(.bi-item)").each(function(e,t){$(t).data("functionnumber")&&i.originNosArr.push($(t).data("functionnumber"))}),i.$("script").each(function(e,t){$($(t).html()).find(".mn-selected.look-item:not(.bi-item)").each(function(e,t){$(t).data("functionnumber")&&i.originNosArr.push($(t).data("functionnumber"))})})},destroy:function(){this.$el.off(),this.roleprivilege&&(this.roleprivilege.destroy(),this.roleprivilege=null)}})});
define("crm-setting/rolemanage/permissionsset/roleprivilege/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<!-- * @Descripttion: * @Author: LiAng * @Date: 2020-05-14 23:41:23 * @LastEditors: LiAng * @LastEditTime: 2020-05-14 23:49:21 --> ";
            _.each(list, function(item, index) {
                __p += ' <div class="lazyload-item" ';
                if (_.contains([ "AccountObj", "AccountAddrObj" ], item.descApiName)) {
                    __p += ' data-functionnumber="' + ((__t = item.descApiName) == null ? "" : __t) + '" ';
                }
                __p += ' data-descapiname="' + ((__t = item.descApiName) == null ? "" : __t) + '" data-descapidisplayname="' + ((__t = item.descApiDisplayName) == null ? "" : __t) + '"> <!-- <script type="text/lazyload"> --> <h3 class="checkbox-head checkbox-head-toggle current"> <span class="ico-arrow"></span> <span class=\'desc-api-display-name\'>' + ((__t = item.descApiDisplayName) == null ? "" : __t) + "</span> ";
                var titleDesc = "";
                if (item.descApiName === "BIPreConfigedReport") {
                    titleDesc = $t("系统预置报表全员可查看，导出功能走此处配置，数据权限走预置报表的主业务模块的数据权限.");
                } else if (item.descApiName === "BIBlankTemplate") {
                    titleDesc = $t("基于自选主题建立的图表的功能权限走此处配置，数据权限走主业务模块的数据权限.");
                } else if (item.descApiName === "BIProduct") {
                    titleDesc = $t("基于产品分析主题建立的图表的功能权限走此处配置，数据权限按主业务模块产品的数据权限进行控制。产品模块通常为公开只读，请谨慎设置.");
                } else if ([ "BICustomer", "BIOrder", "BIOpportunity", "BISalesClue", "BIContractAchievement", "BIEmployeeAchievement", "BICheckinsAnalyse", "BIGoalAnalysis" ].indexOf(item.descApiName) != -1) {
                    titleDesc = $t("基于{{descApiDisplayName}}主题建立的图表的功能权限走此处配置，数据权限按主业务模块{{descApiDisplayName}}的数据权限进行控制.", {
                        descApiDisplayName: item.descApiDisplayName
                    });
                }
                __p += " ";
                if (titleDesc) {
                    __p += ' <span class="crm-ui-title bi-object-title-desc" data-title="' + ((__t = titleDesc) == null ? "" : __t) + '" data-pos="top"></span> ';
                }
                __p += " ";
                var roleFunctionInfos = item.roleFunctionInfos;
                var allEnabled = _.every(roleFunctionInfos, function(ite) {
                    return ite.enabled;
                });
                var anyEnabled = _.some(roleFunctionInfos, function(ite) {
                    return ite.enabled;
                });
                var isNoEditable = _.every(roleFunctionInfos, function(ite) {
                    return ite.isEditable === false;
                });
                __p += " ";
                if (item.isHaveFieldPrivilege) {
                    __p += " ";
                    if (item.descApiName === "SalesOrderObj") {
                        __p += ' <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="SalesOrderObj" data-name="' + ((__t = $t("crm.销售订单")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置销售订单字段权限")) == null ? "" : __t) + '</span> <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="SalesOrderProductObj" data-name="' + ((__t = $t("crm.订单产品")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置订单产品字段权限")) == null ? "" : __t) + "</span> ";
                    } else if (item.descApiName === "ReturnedGoodsInvoiceObj") {
                        __p += ' <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="ReturnedGoodsInvoiceObj" data-name="' + ((__t = $t("crm.退货单")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置退货单字段权限")) == null ? "" : __t) + '</span> <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="ReturnedGoodsInvoiceProductObj" data-name="' + ((__t = $t("crm.退货单产品")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("crm.设置退货单产品字段权限")) == null ? "" : __t) + "</span> ";
                    } else if (item.descApiName === "Inventory") {
                        __p += " ";
                    } else {
                        __p += ' <span class="j-set set-btn ';
                        if (!anyEnabled) {
                            __p += " disabled-btn";
                        }
                        __p += '" data-objectid="' + ((__t = item.descApiName) == null ? "" : __t) + '" data-name="' + ((__t = item.descApiDisplayName) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("设置字段权限")) == null ? "" : __t) + "</span> ";
                    }
                    __p += " ";
                } else {
                    __p += ' <span class="j-set set-btn" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + "></span> ";
                }
                __p += ' </h3> <ul class="mn-checkbox-box b-g-clear ' + ((__t = item.descApiName) == null ? "" : __t) + '"> ';
                if (item.descApiName !== "DuplicateCheckObj") {
                    __p += ' <li class="checkbox-item"> <span class="mn-checkbox-item j-check-all ';
                    if (!item.isEditable || isNoEditable) {
                        __p += "disabled-selected";
                    }
                    __p += " ";
                    if (allEnabled) {
                        __p += "mn-selected";
                    }
                    __p += '"></span>' + ((__t = $t("全选")) == null ? "" : __t) + "</li> ";
                }
                __p += " ";
                _.each(roleFunctionInfos, function(ite, ind) {
                    __p += ' <li class="checkbox-item"> <span class="mn-checkbox-item ';
                    if (ite.isFiledReadOnlyRequired) {
                        __p += " readonly-item";
                    }
                    __p += " ";
                    if (!ite.isEditable) {
                        __p += " disabled-selected";
                    }
                    __p += " ";
                    if (ite.enabled) {
                        __p += " mn-selected";
                    }
                    __p += " ";
                    if (item.viewListFuncCode === ite.functionNumber) {
                        __p += " look-item";
                    }
                    __p += " ";
                    if (item.isBIObject) {
                        __p += " bi-item";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||BeforeSaleAction") {
                        __p += " edit-presale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||AfterSaleAction") {
                        __p += " edit-aftersale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||ViewAfterSaleAction") {
                        __p += " view-presale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "OpportunityObj||ViewBeforeSaleAction") {
                        __p += " view-aftersale";
                    }
                    __p += " ";
                    if (ite.functionNumber === "ContactObj||ImportFromAddressBook") {
                        __p += " import-contacts";
                    }
                    __p += " ";
                    if (ite.functionNumber === "PriceBookObj||Abolish") {
                        __p += " pricebook-abolish";
                    }
                    __p += " ";
                    if (ite.functionNumber === "PriceBookObj||Delete") {
                        __p += " pricebook-delete";
                    }
                    __p += " ";
                    if (ite.isIntelligentForm) {
                        __p += " smartform-item";
                    }
                    __p += " ";
                    if (ite.isClone) {
                        __p += " clone-item";
                    }
                    __p += " ";
                    if (ite.isAdd) {
                        __p += " add-item";
                    }
                    __p += '" data-functionnumber=' + ((__t = ite.functionNumber) == null ? "" : __t) + "></span> " + ((__t = ite.displayName) == null ? "" : __t) + " </li> ";
                });
                __p += " </ul> <!-- </script> --> </div> ";
            });
        }
        return __p;
    };
});
define("crm-setting/rolemanage/permissionsset/roleprivilege/index",["../fieldprivilege/index","../fieldprivilege/index-html","./index-html","./link","./lazyload-any"],function(e,i,l){var s=e("../fieldprivilege/index"),d=e("../fieldprivilege/index-html"),t=e("./index-html"),n=e("./link"),o=e("./lazyload-any"),a="mn-selected";l.exports=Backbone.View.extend({initialize:function(e){this.roleCode=e.roleCode,this.$el.html(t({list:e.data})),this.link=new n(e.linkData),this.link.init(e.data)},events:{"click .mn-checkbox-item":"onCheck","click .j-set":"onSet","click .checkbox-head-toggle":"onClickArrow"},onClickArrow:function(e){var e=$(e.currentTarget),i=e.next();"none"==i.css("display")?e.addClass("current"):e.removeClass("current"),i.slideToggle()},onCheck:function(e){var i=$(e.currentTarget),l=i.closest(".mn-checkbox-box"),t=null;return i.hasClass("disabled-selected")?(-1!=["AccountAddrObj||Add","AccountAddrObj||Edit"].indexOf(i.data("functionnumber"))&&CRM.util.remind(3,$t("该权限受客户的【编辑】权限配置影响")),e.stopPropagation()):(this.trigger("check"),i.toggleClass(a),i.hasClass(a)?i.hasClass("j-check-all")?((t=$(".mn-checkbox-item",l).not(".disabled-selected")).addClass(a),this.link.update(this.$el,t)):this.link.update(this.$el,i):i.hasClass("j-check-all")||i.hasClass("look-item")?this.link.unCheckedAll(this.$el,i):this.link.update(this.$el,i),this.link.updateAllStatus(l),this.link.updateSetDisabledStatus(l),this.link.updateLookStatus(l),e.stopPropagation()),!1},onSet:function(e){e.stopPropagation();var i,l,t=this,e=$(e.currentTarget),n=e.data();e.hasClass("disabled-btn")||(i=t.roleCode===CRM.config.MANAGER_ROLE_CODE,l=_.some(e.parent().next().find(".readonly-item"),function(e){return $(e).hasClass(a)}),t.fieldprivilege&&(t.fieldprivilege.destroy(),t.fieldprivilege=null),t.fieldprivilege=new s({title:$t("设置")+n.name+$t("字段权限"),showBtns:!i,className:"crm-s-rolemanage"}),t.fieldprivilege.on("submit",function(e){CRM.util.FHHApi({url:"/EM1HPAASUdobj/roleApi/updateRoleObjectFieldPrivilege",data:{descApiName:n.objectid,roleCode:t.roleCode,fieldPermission:e},success:function(e){0==e.Result.StatusCode&&(CRM.util.remind(1,$t("操作成功")),t.fieldprivilege.destroy())}},{submitSelector:t.fieldprivilege.element.find(".b-g-btn")})}),t.fieldprivilege.show(),CRM.util.FHHApi({url:"/EM1HPAASUdobj/roleApi/getRoleObjectFieldPrivilege",data:{roleCode:t.roleCode,descApiName:n.objectid},success:function(e){"0"==e.Result.StatusCode?(t.fieldprivilege.setContent(d({roleCode:t.roleCode,fieldInfoList:e.Value,disabled:i,readonly:l})),t.fieldprivilege.resizedialog()):CRM.util.remind(3,$t("操作失败"))}},{errorAlertModel:1}))},initLazyload:function(){new o(this.$(".lazyload-item"))},destroy:function(){this.$el.off(),this.fieldprivilege&&(this.fieldprivilege.destroy(),this.fieldprivilege=null),this.link=null}})});
define("crm-setting/rolemanage/permissionsset/roleprivilege/lazyload-any",[],function(t,n,i){function e(){var t,n,i,e,a,r=h(this);r.is(":visible")&&(n=(t=r)[0].getBoundingClientRect(),t=-t.data(x).threshold,e=v-(i=t),a=g-t,n.top>=i&&n.top<=e||n.bottom>=i&&n.bottom<=e)&&(n.left>=t&&n.left<=a||n.right>=t&&n.right<=a)&&r.trigger(I)}function a(){v=u.innerHeight||f.documentElement.clientHeight,g=u.innerWidth||f.documentElement.clientWidth,r()}function r(){H=H.filter(S),(1==this.nodeType?h(this).find(S):H).each(e)}function o(){var t=h(this),n=t.data(x),i=t.data("lazyload"),e=(i||(e=t.children().filter('script[type="text/lazyload"]').get(0),i=h(e).html()),i||(i=(e=t.contents().filter(function(){return 8===this.nodeType}).get(0))&&h.trim(e.data)),k.html(i).contents());t.replaceWith(e),h.isFunction(n.load)&&n.load.call(e,e)}function d(){var t=h(this);(t=>{var n;return!(t.data(E)||"scroll"!=(n=t.css("overflow"))&&"auto"!=n||(t.data(E,1),t.bind("scroll",r),0))})(t)|(t=>{if(!t.data(W)){var n=t.css("display");if("none"==n)return t.data(W,1),t._bindShow(r),!0}})(t)&&(t.data(j)||(t.data(j,1),t.bind(I,s)))}function s(){var t=h(this);0===t.find(S).length&&(t.removeData(E).removeData(W).removeData(j),t.unbind("scroll",r).unbind(I,s)._unbindShow(r))}function l(){var t=h(this),n="none"!=t.css("display");t.data(m)!=n&&(t.data(m,n),n)&&t.trigger(y)}function c(){(z=z.filter(b)).each(l),0===z.length&&(p=clearInterval(p))}var h,u,f,v,g,p,y,m,b,w,z,x,I,D,S,E,W,j,k,B,H;function T(t){this.$el=t,this.init()}h=jQuery,u=window,f=document,S=":"+(D=(x="jquery-lazyload-any")+"-"+(I="appear")),E=x+"-scroller",W=x+"-display",j=x+"-watch",k=h("<div/>"),B=!1,H=h(),h.expr[":"][D]=function(t){return void 0!==h(t).data(D)},h.fn.lazyload=function(t){var n={threshold:0,trigger:I},t=(h.extend(n,t),n.trigger.split(" "));return this.data(D,-1!=h.inArray(I,t)).data(x,n),this.bind(n.trigger,o),this.each(e),this.parents().each(d),this.each(function(){H=H.add(this)}),B||(B=!0,a(),h(f).ready(function(){h(u).bind("resize",a).bind("scroll",r)})),this},h.lazyload={check:r,refresh:function(t){(void 0===t?H:h(t)).each(function(){var t=h(this);t.is(S)&&t.parents().each(d)})},show:o},b=":"+(m=x+"-"+(y="show")),w=50,z=h(),h.expr[":"][m]=function(t){return void 0!==h(t).data(m)},h.fn._bindShow=function(t){this.bind(y,t),this.data(m,"none"!=this.css("display")),z=z.add(this),w&&!p&&(p=setInterval(c,w))},h.fn._unbindShow=function(t){this.unbind(y,t),this.removeData(m)},h.lazyload.setInterval=function(t){t==w||!h.isNumeric(t)||t<0||(w=t,p=clearInterval(p),0<w&&(p=setInterval(c,w)))},T.prototype=_.extend({constructor:T,init:function(){this.$el.lazyload({load:function(t){var n=t.not("text");n.fadeOut(0,function(){n.fadeIn(0)})}})}},Backbone.Events),i.exports=T});
define("crm-setting/rolemanage/permissionsset/roleprivilege/link",[],function(e,t,a){var s="mn-selected";function n(e){this.global=_.extend({},e.allPositiveLinkage,this.reverseLinkData(e.allReverseLinkage)),this.data=_.extend({},e.specialPositive,this.reverseLinkData(e.specialReverse))}n.prototype.reverseLinkData=function(e){var a={};return _.each(e,function(e,t){e=_.map(e,function(e){return"!"+e}),a[t="!"+t]=e}),a},n.prototype.getObjectName=function(e){return e.closest(".mn-checkbox-box").prev().find(".j-set").data("functionnumber")},n.prototype.unCheckedAll=function(e,t){t.closest(".mn-checkbox-box").find(".mn-checkbox-item").removeClass(s);var t=this.getObjectName(t),a=this.data["!"+t];if(a)for(var n=0;n<a.length;n++){var i=a[n],i=(/^\!/.test(i)&&(i=i.replace("!","")),e.find('span[data-functionnumber="'+i+'"].mn-checkbox-item'));this.unCheckedAll(e,i)}},n.prototype.update=function(e,t){for(var a=0;a<t.length;a++){var n=t.eq(a),i=n.data("functionnumber"),n=n.hasClass(s)?"":"!";this.data[n+i]&&this.active(e,this.data[n+i])}},n.prototype.active=function(e,t){for(var a=0;a<t.length;a++){var n=t[a],i=/^\!/.test(n),n=(i&&(n=n.replace("!","")),e.find('span[data-functionnumber="'+n+'"]')),i=(i?n.removeClass(s):n.addClass(s),n.closest(".mn-checkbox-box"));this.updateAllStatus(i),this.updateSetDisabledStatus(i),this.updateLookStatus(i),this.update(e,n)}},n.prototype.init=function(e){for(var t=0;t<e.length;t++){var a,n=e[t].roleFunctionInfos[0].functionNumber.replace(/\*||/,"");for(a in this.global){var i=this.replace(n,a);this.data[i]?this.data[i]=this.data[i].concat(this.replace(n,this.global[a])):this.data[i]=this.replace(n,this.global[a])}}},n.prototype.replace=function(t,e){return _.isArray(e)?e.map(function(e){return e.replace("{obj}",t)}):_.isString(e)?e.replace("{obj}",t):void 0},n.prototype.updateAllStatus=function(e){var t=e.find("span:not(.j-check-all)"),a=t.filter("span:not(.disabled-selected)").length;t.filter("span:not(.disabled-selected)").filter(".mn-selected").length==a?e.find(".j-check-all").addClass(s):e.find(".j-check-all").removeClass(s)},n.prototype.updateSetDisabledStatus=function(e){var t=e.prev().find(".j-set");_.some($(".mn-checkbox-item",e),function(e){return $(e).hasClass(s)})?t.removeClass("disabled-btn"):t.addClass("disabled-btn")},n.prototype.updateLookStatus=function(e){var t=e.find(".look-item");e.find("span:not(.look-item)").filter(".mn-selected").length&&t.addClass(s)},a.exports=n});
define("crm-setting/rolemanage/permissionsset/template/error-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="b-g-btn refresh-btn j-refresh">' + ((__t = $t("重 试")) == null ? "" : __t) + "</div>";
        }
        return __p;
    };
});
define("crm-setting/rolemanage/permissionsset/template/index-html", [ "crm-setting/common/loading/loading" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var loadTpl = require("crm-setting/common/loading/loading");
            __p += ' <!--<h2 class="out-role-wrapper"> ';
            if (isOutRoles) {
                __p += ' <span class="back-btn j-toggle-role">&lt; 返回</span> <span class="label">CRM外部角色设置</span> ';
            } else {
                __p += ' <span class="label">CRM内部角色设置</span> <div class="b-g-btn j-toggle-role">设置外部角色</div> ';
            }
            __p += ' </h2>--> <div class="leftnav crm-scroll"> ' + ((__t = loadTpl()) == null ? "" : __t) + ' </div> <div class="right-wrapper crm-scroll"> ' + ((__t = loadTpl()) == null ? "" : __t) + " </div>";
        }
        return __p;
    };
});
define("crm-setting/rolemanage/permissionsset/template/outrole-list-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<ul class="customize-rolelist"> <div class="btn-wrapper"> <div class="b-g-btn j-add">+' + ((__t = $t("新建角色")) == null ? "" : __t) + "</div> </div> ";
            if (roles.length) {
                __p += " ";
                _.each(roles, function(item, index) {
                    __p += " ";
                    _.each(item.roleInfoList, function(ite, idx) {
                        __p += ' <li class="item ';
                        if (ite.roleCode === roleCode) {
                            __p += " cur";
                        }
                        __p += '" data-id="' + ((__t = ite.roleCode) == null ? "" : __t) + '" data-type="' + ((__t = ite.roleType) == null ? "" : __t) + '" data-rolename="' + ((__t = ite.roleName) == null ? "" : __t) + '" data-description="' + ((__t = ite.description) == null ? "" : __t) + '"> <div class="name" title="' + ((__t = ite.roleName) == null ? "" : __t) + '">' + ((__t = ite.roleName) == null ? "" : __t) + '</div> <div class="ops"> <span class="crm-ico-edite" action-type="reject"></span><span class="crm-ico-del" action-type="reject"></span> </div> </li> ';
                    });
                    __p += " ";
                });
                __p += " ";
            } else {
                __p += " <div>" + ((__t = $t("暂无角色")) == null ? "" : __t) + "</div> ";
            }
            __p += " </ul>";
        }
        return __p;
    };
});
define("crm-setting/rolemanage/permissionsset/template/right-content-html", [ "crm-setting/common/loading/loading" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var loadTpl = require("crm-setting/common/loading/loading");
            __p += ' <div class="content-wrap"> ';
            if (rolename) {
                __p += ' <div class="info-words"> <div style="flex: 1"> <h3>' + ((__t = rolename) == null ? "" : __t) + "</h3> <p>" + ((__t = description || "--") == null ? "" : __t) + '</p> </div> <div class="search-wrap"><span>' + ((__t = $t("搜索")) == null ? "" : __t) + ':</span><input class="b-g-ipt j-s-field" placeholder="' + ((__t = $t("搜索对象名")) == null ? "" : __t) + '"/></div> <span class="j-fold collapse-box"> <svg class="crm-icon" aria-hidden="true"> <use class="collapse-icon" xlink:href="#shouqi"></use> </svg> </span> </div> <div class="crm-scroll role-privilege-wrapper-scroll-box"> <div class="role-privilege-wrapper"> ' + ((__t = loadTpl()) == null ? "" : __t) + " </div> </div> ";
            } else {
                __p += ' <div class="no-data">' + ((__t = $t("暂无数据")) == null ? "" : __t) + "</div> ";
            }
            __p += " </div> ";
            if (rolename) {
                __p += ' <div class="checkbox-btn"> <div class="b-g-btn j-save b-g-btn-disabled">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> ";
                if (roletype !== 2 && rolecode != "personnelrole") {
                    __p += ' <div class="j-reset reset-btn ' + ((__t = rolecode === CRM.config.MANAGER_ROLE_CODE ? "b-g-btn-disabled" : "") == null ? "" : __t) + '"> ' + ((__t = $t("恢复默认")) == null ? "" : __t) + " </div> ";
                }
                __p += " </div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/rolemanage/roleasign/allusertable",["crm-widget/table/table","crm-widget/dialog/dialog","./editpowerdialog","crm-modules/common/util","../util"],function(t,e,o){var i=t("crm-widget/table/table"),a=(t("crm-widget/dialog/dialog"),t("./editpowerdialog")),d=t("crm-modules/common/util"),r=t("../util");o.exports=Backbone.View.extend({initialize:function(t){this.roleList=t.data,this.render()},events:{"click .j-add":"onAdd","click .j-del":"onDel","click .j-export":"onExport"},render:function(){var a=this,t=[],e=CRM.control.functionCodes;_.contains(e,"permission_business_role_manage")&&t.push({text:$t("添加员工"),className:"j-add"}),_.contains(e,"permission_business_role_export")&&t.push({text:$t("导出"),className:"j-export"}),a.dt=new i({$el:a.$el,url:"/EM1HPAASUdobj/userApi/crmUserList",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!0,trHandle:!1,operate:{pos:"T",btns:t},search:{pos:"T",placeHolder:$t("搜索员工"),type:"username",fileName:"username"},batchBtns:[{text:$t("删除"),className:"j-del"}],postData:{deptId:""},columns:[{data:"name",title:$t("姓名")},{data:"mainDepartment",title:$t("crm.主属部门")},{data:"departmentInfoList",title:$t("附属部门")},{data:"post",title:$t("职位"),width:120},{data:"defualtRoleName",title:$t("主角色")},{data:"roleList",title:$t("角色"),render:function(t){return _.map(t,function(t){return t.roleName}).join($t("、"))||"--"}},{data:"",lastFixed:!0,width:180,title:$t("操作"),render:function(t,e,o){t=1===t.length&&t[0].roleCode===CRM.config.MANAGER_ROLE_CODE?"disable":"j-copy";return'<div class="table-ops"><a class="j-edit">'+$t("编辑")+'</a><a class="j-del '+t+'">'+$t("删除")+'</a><a class="'+t+'">'+$t("复制角色到员工")+"</a></div>"}}],formatData:function(t){return{totalCount:t.page&&t.page.totalCount||0,data:a.getformatData(t.map,t.userInfo)}},initComplete:function(t){$(".first-target-item",t).before(['<div class="item dt-sc-box batch-c">','<span class="item-tit">'+$t("范围：")+"</span>",'<div class="item-con selector j-selector"></div>',"</div>"].join("")),r.initSelector(a.$(".j-selector"),function(t,e){a.selector=e,a.dt.setParam({deptId:t.join(",")},!0,!0)})}}),a.dt.on("trclick",function(t,e,o){o.hasClass("j-edit")?a.editHandle(t):o.hasClass("j-del")?a.onDel(t):o.hasClass("j-copy")&&a.copyHandle(t)}),a.dt.on("selector.change",function(t){a.dt.setParam({deptId:t.join(",")},!0,!0)})},getformatData:function(t,a){var i=[],d=this;return _.each(t,function(t,e){var o=a[e];o&&i.push({employeeId:e,name:o.name,mainDepartment:r.getMainDepartment(o),departmentInfoList:d.getDeparts(o),post:r.getPostByEmployeeId(o),defualtRoleName:(_.findWhere(t,{defaultRole:!0})||{}).roleName||"",roleList:_.map(t,function(t){return{roleCode:t.roleCode,roleName:t.roleName,defaultRole:t.defaultRole}})})}),i},getDeparts:function(t){var e=t.departmentInfoList,o=t.mainDepartment,a=[];return _.each(e,function(t){o&&t.departmentId!=o.departmentId&&a.push(t.departmentName)}),0<a.length?a.join("、"):"--"},onAdd:function(t){var e=this;e.addPowerWidget&&(e.addPowerWidget.destroy(),e.addPowerWidget=null),e.addPowerWidget=new a({title:$t("添加员工"),type:"add",FiledList:e.roleList}),e.addPowerWidget.on("success",function(){e.trigger("update_quota"),e.dt.setParam({},!0)}),e.addPowerWidget.show({})},onExport:function(t){var e=$t("全部CRM用户");d.FHHApi({url:"/EM1HPAASUdobj/bulkimport/exportUser",data:{roleCode:""},success:function(t){0==t.Result.StatusCode?(t=t.Value,t=CRM.util.getFscLink(t.path+"."+t.ext,t.file_name+"."+t.ext,!0),r.showExportDialog(t,e)):r.showExportDialog("",e)}},{submitSelector:this.$(".j-export")})},onDel:function(t){var e=this,a=e.dt.getCheckedData(),i=d.confirm($t("确定从CRM用户中删除员工")+"<br/>"+$t("此操作会删除CRM管理员以外的所有角色。"),$t("提示"),function(){var o=[];_.each(a,function(t,e){o.push(t.employeeId)}),d.FHHApi({url:"/EM1HPAASUdobj/userApi/removeCrmUser",data:{userIds:t.employeeId||o.join(",")},success:function(t){0==t.Result.StatusCode&&(d.remind(1,$t("操作成功")),i.destroy(),e.trigger("update_quota"),e.dt.setParam({},!0))}},{submitSelector:i.$(".b-g-btn")})})},editHandle:function(t){var e=this;e.editPowerWidget&&(e.editPowerWidget.destroy(),e.editPowerWidget=null),e.editPowerWidget=new a({title:$t("编辑角色"),type:"edit",FiledList:e.roleList}),e.editPowerWidget.on("success",function(){e.trigger("update_quota"),e.dt.setParam({},!0)}),e.editPowerWidget.show({data:t})},copyHandle:function(t){var e=this;e.copyPowerWidget&&(e.copyPowerWidget.destroy(),e.copyPowerWidget=null),e.copyPowerWidget=new a({title:$t("复制角色到员工"),type:"copy",FiledList:e.roleList}),e.copyPowerWidget.on("success",function(){e.trigger("update_quota"),e.dt.setParam({},!0)}),e.copyPowerWidget.show({data:t})},destroy:function(){this.$el.off(),_.each(["addPowerWidget","editPowerWidget","copyPowerWidget","dt","selector"],function(t){this[t]&&(this[t].destroy(),this[t]=null)},this)}})});
define("crm-setting/rolemanage/roleasign/editpowerdialog",["crm-modules/common/util","../util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","./template/edit-power-html"],function(e,t,o){var s=e("crm-modules/common/util"),a=e("../util"),l=e("crm-widget/dialog/dialog"),i=e("crm-widget/selector/selector"),d=e("crm-widget/select/select"),r=e("./template/edit-power-html"),n=l.extend({attrs:{title:$t("添加员工"),content:'<div class="crm-loading "></div>',size:"md",showBtns:!0,showScroll:!1,type:"add",delEmp:[],data:null,FiledList:null,className:"crm-s-rolemanage",zIndex:800},events:{"click .form-item .mn-checkbox-item":"onSelect","click .j-checkbox":"onCheck","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},render:function(){return n.superclass.render.call(this)},show:function(e){var t=this,o=t.get("type"),e=(t.widget=[],t.set(e),t.isEditPower="edit"===o,n.superclass.show.call(t)),l=t.get("data")?t.get("data").roleList:[];return t.setContent(r({type:o,FiledList:t.get("FiledList"),roleList:t.get("data")?_.map(l,function(e){return e.roleCode}):t.get("roleCode")})),t.isEditPower||t.widgetEmployeeFn(),0===t.$(".form-item .mn-selected").length?t.selectData=[{value:"",name:$t("请选择")}]:(t.selectData=[],"edit"===o||"copy"===o?(_.each(_.sortBy(l,function(e){return!e.defaultRole}),function(e){t.selectData.push({value:e.roleCode,name:e.roleName})}),t.mainRoleId=t.selectData[0].value):_.each(t.$(".form-item .mn-selected"),function(e){t.selectData.push({value:$(e).data("id"),name:$(e).data("rolename")})})),t.initSelect("add"===o),t.resizedialog(),e},onSelect:function(e){var t,o=$(e.currentTarget),l=this;return o.hasClass("disabled-selected")?e.stopPropagation():(o.toggleClass("mn-selected"),o=l.$(".j-checkbox"),(t=l.$(".form-item .mn-selected")).length?(l.selectData=[],_.each(_.sortBy(t,function(e){return $(e).data("id")!==l.mainRoleId}),function(e){l.selectData.push({value:$(e).data("id"),name:$(e).data("rolename")})}),l.initSelect(!1),o.removeClass("disabled-selected")):(l.selectData=[{value:"",name:$t("请选择")}],l.initSelect(!0),o.addClass("disabled-selected")),e.stopPropagation()),!1},onCheck:function(e){var t=$(e.currentTarget);return t.hasClass("disabled-selected")||t.toggleClass("mn-selected"),e.stopPropagation(),!1},widgetEmployeeFn:function(){var l=this;a.getEmployeesAndCircles("permission_business_role_manage").then(function(e){var t,o={id:"member",type:"sort",title:$t("同事_s"),data:e.employees},e={id:"group",type:"tree",title:$t("部门_s"),data:e.circles},o=new i({$wrap:l.$(".widget-employee"),zIndex:+l.get("zIndex")+10,tabs:[o,e],showProfileCard:!0,label:$t("选择员工或部门"),defaultSelectedItems:(t={member:[],group:[]},_.each(l.get("employee")||[],function(e){("p"==e.type?t.member:t.group).push(e.id)}),t)});o.on("addItem",function(){l.$(".widget-employee").next().remove()}),l.employeeWidget=o})},initSelect:function(e){this.mainRoleWidget&&this.mainRoleWidget.destroy(),this.mainRoleWidget=new d({$wrap:this.$(".main-role-select"),zIndex:1010,disabled:e,options:this.selectData})},onSubmit:function(){var e,t=this,o=[],l=t.get("type"),a=t.$(".form-item .mn-selected"),i=t.employeeWidget?t.employeeWidget.getValue():{member:[],group:[]};return t.isEditPower||i.member.length+i.group.length!=0?a.length?(e=i.member,i=i.group.map(function(e){return parseInt(e)}),a.each(function(e,t){o.push($(t).data("id"))}),a="add"===l||"add2"===l?{url:"/EM1HPAASUdobj/userApi/batchAddUsersToRoles",data:{employeeIds:_.uniq(e),departmentIds:_.uniq(i),roleCodes:o.join(","),majorRoleCode:t.mainRoleWidget.getValue(),updateFlag:!t.$(".j-checkbox").hasClass("mn-selected")}}:"edit"===l?{url:"/EM1HPAASUdobj/userApi/updateUserRole",data:{userId:t.get("data").employeeId,roleCodes:o.join(","),majorRoleCode:t.mainRoleWidget.getValue()}}:{url:"/EM1HPAASUdobj/userApi/copyUserRoleToUsers",data:{employeeIds:_.uniq(e),departmentIds:_.uniq(i),userIdOld:t.get("data").employeeId,majorRoleCode:t.mainRoleWidget.getValue()}},void t.submit(a)):(s.remind(3,$t("请选择员工角色")),!1):(s.showErrmsg(t.$(".widget-employee"),$t("请选择员工或部门")),!1)},submit:function(e){var t=this;s.FHHApi({url:e.url,data:e.data,success:function(e){0==e.Result.StatusCode?(t.trigger("success"),s.remind(1,$t("操作成功！")),t.hide()):s.alert(e.Result.FailureMessage)}},{submitSelector:t.$(".b-g-btn"),errorAlertModel:1})},hide:function(){var e=this;return _.each(e.widget,function(e){e&&e.destroy&&e.destroy()}),e.employeeWidget&&e.employeeWidget.destroy(),e.widget=[],n.superclass.hide.call(e)},destroy:function(){var e=this;return e.employeeWidget&&e.employeeWidget.destroy(),e.mainRoleWidget&&e.mainRoleWidget.destroy(),_.each(e.widget,function(e){e&&e.destroy&&e.destroy()}),n.superclass.destroy.call(e)}});o.exports=n});
define("crm-setting/rolemanage/roleasign/forbiddenusertable",["crm-widget/table/table","crm-modules/common/util","../util"],function(e,t,a){var n=e("crm-widget/table/table"),r=e("crm-modules/common/util"),i=e("../util");a.exports=Backbone.View.extend({initialize:function(e){this.roleList=e.data,this.render()},events:{"click .j-recovery":"onRecovery"},render:function(){var o=this;o.dt=new n({$el:o.$el,url:"/EM1HPAASUdobj/forbiddenUsesApi/queryRoleInfoListByForbiddenUsers",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!0,trHandle:!1,checked:{idKey:"employeeId"},search:{pos:"T",placeHolder:$t("搜索员工"),type:"username",fileName:"username"},batchBtns:[{text:$t("恢复"),className:"j-recovery"}],postData:{forbiddenFlag:!0},columns:[{data:"name",title:$t("姓名")},{data:"mainDepartment",title:$t("crm.主属部门")},{data:"post",title:$t("职位"),width:120},{data:"defualtRoleName",title:$t("主角色")},{data:"roleNames",title:$t("角色")},{data:"",lastFixed:!0,width:180,title:$t("操作"),render:function(){return'<div class="table-ops"><a class="j-recovery">'+$t("恢复")+"</a></div>"}}],formatData:function(e){return{totalCount:e.page&&e.page.totalCount||0,data:o.getformatData(e.userRoles,e.userInfo)}},initComplete:function(e){$(".first-target-item",e).before(['<div class="item dt-sc-box batch-c">','<span class="item-tit">'+$t("范围：")+"</span>",'<div class="item-con selector j-selector"></div>',"</div>"].join("")),i.initSelector(o.$(".j-selector"),function(e,t){o.selector=t,o.dt.setParam({deptId:e.join(",")},!0,!0)})}}),o.dt.on("trclick",function(e,t,a){a.hasClass("j-recovery")&&o.onRecovery(e)}),o.dt.on("selector.change",function(e){o.dt.setParam({deptId:e.join(",")},!0,!0)})},getformatData:function(e,n){var r=[];return _.each(e,function(e,t){var a=n[t],o=e.defualtRole,e=e.roleInfoList;r.push({employeeId:t,name:a.name,mainDepartment:i.getMainDepartment(a),post:i.getPostByEmployeeId(a),defualtRoleName:(_.findWhere(e,{roleCode:o})||{}).roleName||"--",roleNames:_.map(e,function(e){return e.roleName}).join($t("、"))||"--"})}),r},onRecovery:function(e){var t=this,a=t.dt.getRemberData()||[],o=a.length||1,n=r.confirm('<div style="text-align: center;">'+$t("本操作将恢复{{length}}个用户的原角色<br/>（包含角色与主角色）",{length:o})+"</div>",$t("提示"),function(){r.FHHApi({url:"/EM1HPAASUdobj/forbiddenUsesApi/updateForbiddenUses",data:{orgIds:e.employeeId?[e.employeeId]:_.map(a,function(e){return e.employeeId}),forbiddenFlag:!1},success:function(e){e.Value.success?(r.remind(1,$t("操作成功")),t.dt.setParam({},!0),t.trigger("update_quota")):r.alert('<div style="text-align: center;">'+$t("配额不足!您还可以恢复【{{res.Value.quota}}】个用户，建议您联系纷享客服增购。",{"res.Value.quota":e.Value.quota})+"</div>"),n.destroy()}},{submitSelector:n.$(".b-g-btn")})})},destroy:function(){this.$el.off(),_.each(["dt","selector"],function(e){this[e]&&(this[e].destroy(),this[e]=null)},this)}})});
define("crm-setting/rolemanage/roleasign/index",["./template/index-html","./leftnav","./allusertable","./forbiddenusertable","./usertable"],function(t,e,n){var i=t("./template/index-html"),r=t("./leftnav"),l=t("./allusertable"),a=t("./forbiddenusertable"),c=t("./usertable");n.exports=Backbone.View.extend({initialize:function(){this.el.innerHTML=i(),this.render("all")},render:function(t){var n=this,i=n.$(".right-wrapper"),o=_.contains(["all","forbidden"],t)?"_"+t+"_":"_";new Promise(n.getRoleList).then(function(e){n.destroy(),n.leftnav=new r({el:n.$(".leftnav"),data:e,roleCode:t}),n.leftnav.on("click_item",function(t){n.rightcontent&&n.rightcontent.destroy(),n.rightcontent=new c({el:i,data:e,roleCode:t,roleName:n.$(".leftnav cur").data("rolename")})}),n.leftnav.on("click_all_item",function(){n.rightcontent&&n.rightcontent.destroy(),n.rightcontent=new l({el:i,data:e})}),n.leftnav.on("click_forbidden_item",function(){n.rightcontent&&n.rightcontent.destroy(),n.rightcontent=new a({el:i,data:e})}),n.leftnav.trigger("click"+o+"item",t),n.leftnav.on("refresh",function(t){n.render(t)}),n.rightcontent.on("update_quota",function(){n.leftnav.updateQuota()})})},getRoleList:function(e,t){CRM.util.FHHApi({url:"/EM1HPAASUdobj/roleApi/roleListWithOutPersonnelRole",success:function(t){0===t.Result.StatusCode&&(t=_.compact(t.Value),-1===CRM.util.findIndex(t,function(t){return 1===t.groupType})&&t.push({groupName:$t("自定义角色"),groupType:1,roleInfoList:[]}),e(t))}})},destroy:function(){_.each(["leftnav","rightcontent"],function(t){this[t]&&(this[t].destroy(),this[t]=null)},this)}})});
define("crm-setting/rolemanage/roleasign/leftnav",["crm-modules/common/util","crm-widget/dialog/dialog","../mainroleset/mainroleset","../template/leftnav-html","../editcustomizerole/editcustomizerole"],function(e,t,o){var i=e("crm-modules/common/util"),s=(e("crm-widget/dialog/dialog"),e("../mainroleset/mainroleset"),e("../template/leftnav-html")),l=e("../editcustomizerole/editcustomizerole");o.exports=Backbone.View.extend({initialize:function(e){var t=_.compact(e.data);this.roleList=t,this.roleCode=e.roleCode,this.searchRoleList=null,this.el.innerHTML=s({roles:this.roleList,isPermissionsSet:!1,roleCode:e.roleCode,searchRoleList:this.searchRoleList}),this.updateQuota()},events:{"click .j-add":"onAdd","click .item":"onClickItem","click .checkbox-head-toggle":"onClickArrow","keyup .j-s-field":"onKeyUp"},onClickArrow:function(e){var e=$(e.currentTarget),t=e.next();"none"==t.css("display")?e.addClass("current"):e.removeClass("current"),t.slideToggle()},onKeyUp:function(e){var t=this.$(".j-s-field").val();(t&&13==e.which||!t)&&this.search(t)},search:function(t){var o,e=this;t?(o=[],e.$(".item_box .item").each(function(){var e=$(this).data().rolename||"";t&&0<=e.toString().indexOf(t)&&o.push($(this).data())}),e.searchRoleList=o,e.el.innerHTML=s({roles:e.roleList,isPermissionsSet:!1,roleCode:e.roleCode,searchRoleList:e.searchRoleList}),e.updateQuota(),e.$(".rolelist-box").hide()):(e.searchRoleList=null,e.$(".rolelist-box").show(),e.$(".search-rolelist-box").remove(),e.$(".item[data-id=".concat(e.roleCode,"]")).addClass("cur")),e.$(".j-s-field").val(t).focus()},updateQuota:function(){var t=this;i.FHHApi({url:"/EM1HPAASUdobj/userApi/queryQuota",success:function(e){0==e.Result.StatusCode&&(e=e.Value,t.$(".quota").html(e.userNum+"/"+e.quota),t.$(".total").html(e.total))}})},onAdd:function(e){e.stopPropagation();var t=this,o=[];_.each(t.roleList,function(e){_.each(e.roleInfoList,function(e){e.roleCode!==CRM.config.MANAGER_ROLE_CODE&&o.push({value:e.roleCode,name:e.roleName})})}),t.addCustomizeRoleWidget&&t.addCustomizeRoleWidget.destroy(),t.addCustomizeRoleWidget=new l({title:$t("新建角色"),type:"add",roleList:o}),t.addCustomizeRoleWidget.on("success",function(e){e.sourceRoleCode?i.FHHApi({url:"/EM1HPAASUdobj/roleApi/copyRole",data:{destRoleCode:e.destRoleCode,sourceRoleCode:e.sourceRoleCode},success:function(e){0==e.Result.StatusCode&&i.remind(1,$t("新建成功"))}}):i.remind(1,$t("新建成功")),t.trigger("refresh",e.destRoleCode)}),t.addCustomizeRoleWidget.show()},onClickItem:function(e){e=$(e.currentTarget);e.hasClass("cur")||(this.$(".item").removeClass("cur"),e.addClass("cur"),e.hasClass("j-all")?this.trigger("click_all_item"):e.hasClass("j-forbidden")?this.trigger("click_forbidden_item"):(this.roleCode=e.data("id"),this.trigger("click_item",e.data("id"))))},destroy:function(){this.$el.off(),this.addCustomizeRoleWidget&&(this.addCustomizeRoleWidget.destroy(),this.addCustomizeRoleWidget=null)}})});
define("crm-setting/rolemanage/roleasign/template/edit-power-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (type === "edit") {
                __p += ' <div class="crm-g-form mn-checkbox-box edit-power-box"> ';
                _.each(FiledList, function(item, index) {
                    __p += ' <div class="form-item"> <label class="fm-lb">' + ((__t = item.groupName) == null ? "" : __t) + '</label> <div class="fm-wrap"> ';
                    _.each(item.roleInfoList, function(ite, ind) {
                        __p += ' <div class="checkbox-item"> <span class="mn-checkbox-item ' + ((__t = roleList.indexOf(ite.roleCode) > -1 ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = ite.roleCode === CRM.config.MANAGER_ROLE_CODE ? "disabled-selected" : "") == null ? "" : __t) + '" data-id="' + ((__t = ite.roleCode) == null ? "" : __t) + '" data-type="' + ((__t = ite.roleType) == null ? "" : __t) + '" data-rolename="' + ((__t = ite.roleName) == null ? "" : __t) + '" data-description="' + ((__t = ite.description) == null ? "" : __t) + '"></span> <span class="check-lb">' + ((__t = ite.roleName) == null ? "" : __t) + "</span> </div> ";
                    });
                    __p += " </div> </div> ";
                });
                __p += ' <div class="fm-item" style="border-bottom: 1px solid #eee;color:#999;">' + ((__t = $t("主角色")) == null ? "" : __t) + '</div> <div class="fm-item" style="display: flex;"> <label class="fm-lb">' + ((__t = $t("主角色")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="main-role-select"></div> </div> </div> </div> ';
            } else {
                __p += ' <div class="crm-g-form mn-checkbox-box edit-power-box"> <div class="fm-item"> <div style="color:#999;">' + ((__t = $t("说明：")) == null ? "" : __t) + "" + ((__t = type === "copy" ? $t("crm.复制的角色将覆盖原有") : $t("新添加的角色会与员工的原有角色进行合并")) == null ? "" : __t) + '</div> <div class="fm-wrap" style="width: 440px;"> <div class="widget-employee"></div> </div> </div> <div class="fm-item" style="border-bottom: 1px solid #eee;color:#999;">' + ((__t = $t("员工角色配置")) == null ? "" : __t) + "</div> ";
                _.each(FiledList, function(item, index) {
                    __p += ' <div class="form-item"> <label class="fm-lb">' + ((__t = item.groupName) == null ? "" : __t) + '</label> <div class="fm-wrap"> ';
                    _.each(item.roleInfoList, function(ite, ind) {
                        __p += ' <div class="checkbox-item"> <span class="mn-checkbox-item ' + ((__t = type === "add" || roleList.indexOf(ite.roleCode) === -1 || type === "copy" && ite.roleCode === CRM.config.MANAGER_ROLE_CODE ? "" : "mn-selected") == null ? "" : __t) + " " + ((__t = type === "copy" || ite.roleCode === CRM.config.MANAGER_ROLE_CODE ? "disabled-selected" : "") == null ? "" : __t) + '" data-id="' + ((__t = ite.roleCode) == null ? "" : __t) + '" data-type="' + ((__t = ite.roleType) == null ? "" : __t) + '" data-rolename="' + ((__t = ite.roleName) == null ? "" : __t) + '" data-description="' + ((__t = ite.description) == null ? "" : __t) + '" ></span> <span class="check-lb" title="' + ((__t = ite.roleName) == null ? "" : __t) + '">' + ((__t = ite.roleName) == null ? "" : __t) + "</span> </div> ";
                    });
                    __p += " </div> </div> ";
                });
                __p += ' <div class="fm-item" style="border-bottom: 1px solid #eee;color:#999;">' + ((__t = $t("主角色")) == null ? "" : __t) + '</div> <div class="fm-item" style="display: flex;"> <label class="fm-lb">' + ((__t = $t("主角色")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="main-role-select"></div> ';
                if (type !== "copy") {
                    __p += ' <div class="main-role-checkbox"> <span class="mn-checkbox-item j-checkbox ';
                    if (type === "add") {
                        __p += "disabled-selected";
                    }
                    __p += '"></span> <span class="check-lb">' + ((__t = $t("已有主角色的员工和部门不更新主角色")) == null ? "" : __t) + "</span> </div> ";
                }
                __p += " </div> </div> </div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/rolemanage/roleasign/template/index-html", [ "crm-setting/common/loading/loading" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var loadTpl = require("crm-setting/common/loading/loading");
            __p += ' <div class="leftnav crm-scroll"> ' + ((__t = loadTpl()) == null ? "" : __t) + ' </div> <div class="right-wrapper"> <div class="crm-loading"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/rolemanage/roleasign/usertable",["crm-modules/common/util","../util","crm-widget/table/table","crm-widget/dialog/dialog","./editpowerdialog","../mainroleset/mainroleset"],function(e,t,o){var r=e("crm-modules/common/util"),n=e("../util"),i=e("crm-widget/table/table"),a=(e("crm-widget/dialog/dialog"),e("./editpowerdialog")),d=e("../mainroleset/mainroleset");o.exports=Backbone.View.extend({initialize:function(e){this.roleList=e.data,this.roleCode=e.roleCode,this.roleName=e.roleName,this.render()},events:{"click .j-add":"onAdd","click .j-del":"onDel","click .j-set":"onSet","click .j-export":"onExport"},render:function(){var a=this,t=a.roleCode===CRM.config.MANAGER_ROLE_CODE,e=CRM.control.functionCodes,o=[];_.contains(e,"permission_business_role_manage")&&o.push({text:$t("添加员工"),className:"j-add"}),_.contains(e,"permission_business_role_export")&&o.push({text:$t("导出"),className:"j-export"}),a.dt=new i({$el:a.$el,url:"/EM1HPAASUdobj/userApi/roleUsers",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!0,trHandle:!1,operate:{pos:"T",btns:o},search:{pos:"T",placeHolder:$t("搜索员工"),type:"username",fileName:"username"},batchBtns:[{text:$t("删除"),className:"j-del"},{text:$t("当前角色设为主角色"),className:"j-set"}],postData:{roleCode:a.roleCode},columns:[{data:"name",title:$t("姓名")},{data:"mainDepartment",title:$t("crm.主属部门")},{data:"departmentInfoList",title:$t("附属部门")},{data:"post",title:$t("职位"),width:120},{data:"",title:$t("操作"),width:180,render:function(){return a.roleCode===CRM.config.MANAGER_ROLE_CODE?'<div class="table-ops"><span style="color:#ccc;margin-right:5px;">'+$t("删除")+'</span><a class="j-set">'+$t("当前角色设为主角色")+" </a></div>":'<div class="table-ops"><a class="j-del">'+$t("删除")+'</a><a class="j-set">'+$t("当前角色设为主角色")+"</a></div>"}}],formatData:function(e){return{totalCount:e.page&&e.page.totalCount||0,data:a.getformatData(e.map,e.userInfo)}},initComplete:function(e){$(".first-target-item",e).before(['<div class="item dt-sc-box batch-c">','<span class="item-tit batch-c">'+$t("范围：")+"</span>",'<div class="item-con selector j-selector"></div>',"</div>"].join("")),n.initSelector(a.$(".j-selector"),function(e,t){a.selector=t,a.dt.setParam({deptId:e.join(",")},!0,!0)}),t?a.$(".j-add").hide():a.$(".j-add").show()}}),a.dt.on("trclick",function(e,t,o){o.hasClass("j-del")?a.onDel(e):o.hasClass("j-set")&&a.onSet(e)})},getformatData:function(e,a){var i=[],s=this;return _.each(e,function(e,t){var o=a[t];i.push({employeeId:t,name:o.name,mainDepartment:n.getMainDepartment(o),post:n.getPostByEmployeeId(o),departmentInfoList:s.getDeparts(o)})}),i},getDeparts:function(e){var t=e.departmentInfoList,o=e.mainDepartment,a=[];return _.each(t,function(e){o&&e.departmentId!=o.departmentId&&a.push(e.departmentName)}),0<a.length?a.join("、"):"--"},onAdd:function(e){var t=this;t.addPowerWidget&&(t.addPowerWidget.destroy(),t.addPowerWidget=null),t.addPowerWidget=new a({title:$t("添加员工"),type:"add2",FiledList:t.roleList}),t.addPowerWidget.on("success",function(){t.trigger("update_quota"),t.dt.setParam({},!0)}),t.addPowerWidget.show({data:{roleList:[{roleCode:t.roleCode}]}})},onExport:function(e){var t=$t("全部CRM用户");r.FHHApi({url:"/EM1HPAASUdobj/bulkimport/exportUser",data:{roleCode:this.roleCode},success:function(e){0==e.Result.StatusCode?(e=e.Value,e=CRM.util.getFscLink(e.path+"."+e.ext,e.file_name+"."+e.ext,!0),n.showExportDialog(e,t)):n.showExportDialog("",t)}},{submitSelector:this.$(".j-export")})},onDel:function(t){var a=this,i=a.dt.getCheckedData();if(a.roleCode===CRM.config.MANAGER_ROLE_CODE)return r.remind(3,$t("不能删除管理员角色")),!1;var s=r.confirm($t("确定从当前角色中删除员工"),$t("提示"),function(){var o=[],e=(_.each(i,function(e,t){o.push(e.employeeId)}),t.employeeId?[t.employeeId]:o);a.mainRoleSetWidget&&a.mainRoleSetWidget.destroy(),a.mainRoleSetWidget=new d({roleCode:a.roleCode,userIds:e,rolename:a.roleName}),a.mainRoleSetWidget.show(),a.mainRoleSetWidget.on("success",function(){r.FHHApi({url:"/EM1HPAASUdobj/roleApi/roleRemoveUsers",data:{roleCode:a.roleCode,users:e},success:function(e){0==e.Result.StatusCode&&(r.remind(1,$t("操作成功")),s.destroy(),a.trigger("update_quota"),a.dt.setParam({},!0))}},{submitSelector:s.$(".b-g-btn")})})})},onSet:function(e){var t=this,a=t.dt.getCheckedData(),i=r.confirm($t("确定将当前角色设为主角色")+"?",$t("当前角色设为主角色"),function(){var o=[];_.each(a,function(e,t){o.push(e.employeeId)}),r.FHHApi({url:"/EM1HPAASUdobj/userApi/updateUsersMajorRole",data:{majorRoleCode:t.roleCode,userIds:e.employeeId?[e.employeeId]:o},success:function(e){0==e.Result.StatusCode&&(r.remind(1,$t("操作成功")),i.destroy(),t.dt.setParam({},!0))}},{submitSelector:i.$(".b-g-btn")})})},destroy:function(){this.$el.off(),_.each(["addPowerWidget","dt","selector","mainRoleSetWidget"],function(e){this[e]&&(this[e].destroy(),this[e]=null)},this)}})});
define("crm-setting/rolemanage/rolemanage",["./template/index-html","manage-modules/vues/businessauthority/index","manage-modules/leftnav/leftnav"],function(e,t,n){var a=e("./template/index-html"),i=e("manage-modules/vues/businessauthority/index").default,s=e("manage-modules/leftnav/leftnav"),e=Backbone.View.extend({initialize:function(e){this.widgets={},this.setElement(e.wrapper),this.el.innerHTML=a()},render:function(e){var t=this;s.getValidateStatusDeferred().done(function(){var e=new i({el:t.$(".crm-rolemanage-con"),title:s.getMenuTitles(location.hash.replace("#",""))});(t.widgets.app=e).render()})},destroy:function(){this.widgets.app.destroy(),this.widgets.app=null}});n.exports=e});
define("crm-setting/rolemanage/template/edit-customizerole-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form crm-s-customizerole"> <div class="fm-item"> <label class="fm-lb"> <em>*</em><span>' + ((__t = $t("角色名称")) == null ? "" : __t) + '</span> </label> <input class="b-g-ipt fm-ipt rolename-ipt" maxlength="20" ';
            if (datas) {
                __p += 'value="' + ((__t = datas.rolename) == null ? "" : __t) + '"';
            } else {
                __p += 'placeholder="' + ((__t = $t("最多20个中文字符")) == null ? "" : __t) + '"';
            }
            __p += '> </div> <div class="fm-item"> <label class="fm-lb"> <em>*</em><span>' + ((__t = $t("角色描述")) == null ? "" : __t) + '</span> </label> <textarea maxlength="1000" class="b-g-ipt fm-ipt roledes-ipt">';
            if (datas) {
                __p += (__t = datas.description) == null ? "" : __t;
            }
            __p += "</textarea> </div> ";
            if (type === "add") {
                __p += ' <div class="fm-item mn-checkbox-box"> <span class="mn-checkbox-item j-toggle"></span><span class="check-lb">' + ((__t = $t("复制")) == null ? "" : __t) + "</span><span class='j-select-box'></span>" + ((__t = $t("的角色权限")) == null ? "" : __t) + "</div> ";
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/rolemanage/template/index-html", [ "crm-setting/common/loading/loading" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<!-- ";
            var loadTpl = require("crm-setting/common/loading/loading");
            __p += ' <div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("业务功能权限")) == null ? "" : __t) + '<a class="crm-doclink" href="http://www.fxiaoke.com/mob/guide/crmdoc/src/7-1-2功能权限管理.html" target="_blank"></a><a href="http://www.fxiaoke.com/mob/guide/crmdoc/video/?id=3" class="crm-ico-play" target="_blank">' + ((__t = $t("了解更多")) == null ? "" : __t) + '</a></span></h2> </div> <div class="crm-module-con"></div> --> <div class="crm-rolemanage-con" style="width: 100%;height: 100%;"></div>';
        }
        return __p;
    };
});
define("crm-setting/rolemanage/template/leftnav-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="search-wrap"> <input class="b-g-ipt j-s-field" placeholder="' + ((__t = $t("搜索角色名")) == null ? "" : __t) + '"/> </div> ';
            if (roles && roles.length > 0) {
                __p += ' <div class="rolelist-box"> ';
                if (!isPermissionsSet) {
                    __p += ' <ul class="crm-rolelist"> <li class="item j-all ';
                    if (roleCode === "all") {
                        __p += " cur";
                    }
                    __p += '"> <div class="name">' + ((__t = $t("全部CRM用户")) == null ? "" : __t) + '</div> <span class="quota"></span> </li> <li class="item j-forbidden ';
                    if (roleCode === "forbidden") {
                        __p += " cur";
                    }
                    __p += '"> <div class="name">' + ((__t = $t("禁用CRM用户")) == null ? "" : __t) + '</div> <span class="total"></span> </li> </ul> <div class="line"></div> ';
                }
                __p += " ";
                _.each(roles, function(item) {
                    __p += ' <ul class="' + ((__t = item.groupType === 1 ? "customize-rolelist" : "default-rolelist") == null ? "" : __t) + '"> <div class="item-group checkbox-head-toggle"> <span class="ico-arrow"></span> ' + ((__t = item.groupName) == null ? "" : __t);
                    if (item.groupType === 1) {
                        __p += ' <span class="add-btn j-add">+' + ((__t = $t("新建")) == null ? "" : __t) + "</span> ";
                    }
                    __p += ' </div> <div class="item_box"> ';
                    _.each(item.roleInfoList, function(ite, idx) {
                        __p += ' <li class="item ';
                        if (ite.roleCode === roleCode) {
                            __p += " cur";
                        }
                        __p += '" data-grouptype="' + ((__t = item.groupType) == null ? "" : __t) + '" data-id="' + ((__t = ite.roleCode) == null ? "" : __t) + '" data-type="' + ((__t = ite.roleType) == null ? "" : __t) + '" data-rolename="' + ((__t = ite.roleName) == null ? "" : __t) + '" data-description="' + ((__t = ite.description) == null ? "" : __t) + '"> ';
                        if (item.groupType === 1 && isPermissionsSet) {
                            __p += ' <div class="name" title="' + ((__t = ite.roleName) == null ? "" : __t) + '">' + ((__t = ite.roleName) == null ? "" : __t) + '</div> <div class="ops"> <span class="crm-ico-edite" action-type="reject"></span><span class="crm-ico-del" action-type="reject"></span> </div> ';
                        } else {
                            __p += ' <div class="name" title="' + ((__t = ite.roleName) == null ? "" : __t) + '">' + ((__t = ite.roleName) == null ? "" : __t) + "</div> ";
                        }
                        __p += " </li> ";
                    });
                    __p += " </div> </ul> ";
                });
                __p += " </div> ";
            }
            __p += " ";
            if (searchRoleList) {
                __p += ' <div class="search-rolelist-box"> ';
                if (searchRoleList.length > 0) {
                    __p += " ";
                    _.each(searchRoleList, function(ite, idx) {
                        __p += ' <ul class="search-rolelist ' + ((__t = ite.grouptype === 1 ? "customize-rolelist" : "default-rolelist") == null ? "" : __t) + '"> <div> <li class="item ';
                        if (ite.id === roleCode) {
                            __p += " cur";
                        }
                        __p += '" data-id="' + ((__t = ite.id) == null ? "" : __t) + '" data-type="' + ((__t = ite.type) == null ? "" : __t) + '" data-rolename="' + ((__t = ite.rolename) == null ? "" : __t) + '" data-description="' + ((__t = ite.description) == null ? "" : __t) + '"> ';
                        if (ite.grouptype === 1 && isPermissionsSet) {
                            __p += ' <div class="name" title="' + ((__t = ite.rolename) == null ? "" : __t) + '">' + ((__t = ite.rolename) == null ? "" : __t) + '</div> <div class="ops"> <span class="crm-ico-edite" action-type="reject"></span><span class="crm-ico-del" action-type="reject"></span> </div> ';
                        } else {
                            __p += ' <div class="name" title="' + ((__t = ite.rolename) == null ? "" : __t) + '">' + ((__t = ite.rolename) == null ? "" : __t) + "</div> ";
                        }
                        __p += " </li> </div> </ul> ";
                    });
                    __p += " ";
                } else {
                    __p += ' <div class="no-data">' + ((__t = $t("暂无数据")) == null ? "" : __t) + "</div> ";
                }
                __p += " </div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/rolemanage/util",["crm-modules/common/util","crm-widget/selector/selector","crm-widget/dialog/dialog","manage-modules/manage-utils/manage-utils"],function(e,t,n){var r=e("crm-modules/common/util"),i=e("crm-widget/selector/selector"),o=e("crm-widget/dialog/dialog"),s=e("manage-modules/manage-utils/manage-utils");return{getFunctionCodes:function(){return new Promise(function(t,e){s.FHHApi({url:"/EM2HORG/Management/Permission/GetFunctionCodesByEmployee",data:{appId:"facishare-system"},success:function(e){0==e.Result.StatusCode?(CRM.control.functionCodes=_.map(e.Value.functionCodeVos,function(e){return e.functionCode}),t(CRM.control.functionCodes)):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},getEmployeesAndCircles:function(t){return new Promise(function(i,e){s.FHHApi({url:"/EM2HORG/Management/Permission/GetOptionByFunctionCodeAndAppId",data:{appId:"facishare-system",functionCode:t},success:function(e){var t,n,o;0==e.Result.StatusCode?(t=FS.contacts,n=e.Value.employeeOptions,o=e.Value.departmentOptions,i({employees:t.sortEmployeesByLetter(n),circles:t.buildTreeOfCircles(o)})):r.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},initSelector:function(e,t){var o=new i({$wrap:e,zIndex:1e3,group:!0,single:!0,size:1,label:$t("crm.选择部门")});o.on("addItem",function(){r.hideErrmsg(this.$el.closest(".v-box"))}),o.on("change",function(e){var e=e.group||[],n=[];_.each(e,function(e,t){n.push(+e)}),t(n,o)})},findIndex:function(e,t,n){for(var o=0;o<e.length;o++)if(t.call(n,e[o],o,e))return o;return-1},getPostByEmployeeId:function(e){return e.post||"--"},getMainDepartment:function(e){return e.mainDepartment&&e.mainDepartment.departmentName||"--"},showExportDialog:function(e,t){var e=e?['<div class="con-box" style="height: 150px;">','<div class="con">',"</div>",'<div class="single-down">','<div class="inner">','<p><em class="crm-ico-importsuc"></em><em>'+$t("生成成功(不打印外部联系人数据)")+"</em></p>","<a href="+e+' target="_blank">'+$t("点击下载 Excel 表")+"</a>","</div>","</div>","</div>"]:['<div class="con-box">','<div class="con">',"</div>",'<div class="fail">','<div class="inner">','<p><em class="crm-ico-importerror"></em><em class="j-error-text">'+$t("生成失败")+"</em></p>","</div>","</div>","</div>"],n=new o({classPrefix:"crm-c-dialog crm-c-dialog-confirm crm-c-importout",title:$t("导出")+t,showScroll:!1,showBtns:!1,content:e.join(""),zIndex:2e3});return n.on("hide",function(e){n.destroy()}),n.show(),n}}});