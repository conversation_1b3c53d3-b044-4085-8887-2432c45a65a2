define("crm-setting/stage/api",["crm-modules/common/util"],function(e,n,t){var o=e("crm-modules/common/util");function u(e,n){return o.FHHApi({url:e.url,data:e.data,success:function(e){0===e.Result.StatusCode&&n&&n(e.Value)}})}t.exports={list:function(e,n){return u({url:"/EM1HSTAGE/Definition/GetDefinitionList",data:{name:e.name,enable:e.enable,page:e.page,pageSize:e.pageSize}},n)},enable:function(e,n){return u({url:"/EM1HSTAGE/Definition/Enable",data:{enable:e.enable,sourceWorkflowId:e.flowid}},n)},delete:function(e,n){return u({url:"/EM1HSTAGE/Definition/Delete",data:{sourceWorkflowId:e}},n)},quota:function(e){return u({url:"/EM1HSTAGE/Tenant/GetQuota"},e)},get:function(e,n){return u({url:"/EM1HSTAGE/Definition/GetBySourceWorkflowId",data:{sourceWorkflowId:e}},n)}}});
define("crm-setting/stage/feature/feature-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<ul class="crm-rocket-feature"> <li class="feature_1"> <div class="feature_1img"></div> <span>' + __e($t("第一步：维护基础信息")) + '</span> <span class="feature-content">' + __e($t("填写流程名称、流程描述、适用范围、选择阶段")) + '</span> </li> <li class="feature-ground"></li> <li class="feature_2"> <div class="feature_2img"></div> <span class="feature-content1">' + __e($t("第二步：维护阶段/任务")) + '</span> <span class="feature-content">' + __e($t('设置：阶段的"完成条件"和"审批配置" 以及任务的"基础信息"和"完成条件"')) + "</span> </li> </ul>";
        }
        return __p;
    };
});
define("crm-setting/stage/feature/feature",["base-modules/dialog/dialog","./feature-html"],function(e,t,i){var a=e("base-modules/dialog/dialog"),o=e("./feature-html"),e=Backbone.View.extend({initialize:function(){var e=this;this.featureView=o({}),this.featureDialog=new a({title:$t("阶段推进器说明"),content:this.featureView,btns:[{label:$t("我知道了"),action:"iknow",type:"default"}],width:712,height:460,zIndex:999}),this.featureDialog.show(),this.featureDialog.on("hide",function(){e.trigger("featureClose"),e.featureDialog.destroy()}),this.featureDialog.on("iknow",function(){e.trigger("featureClose"),e.featureDialog.hide(),e.featureDialog.destroy()})}});i.exports=e});
define("crm-setting/stage/stage",["crm-modules/common/util","crm-widget/table/table","./feature/feature","./api"],function(o,e,t){var r=o("crm-modules/common/util"),n=o("crm-widget/table/table"),a=o("./feature/feature"),d=o("./api"),i=Backbone.View.extend({initialize:function(e){var t=this;t.setElement(e.wrapper),r.getUserGroups(function(e){t.groups=e,r.getUserRoles(function(e){t.roles=e,t.initTable()})}),"firstRocket"!=window.localStorage.getItem("has_showFisrt_rocket_feature")&&(t.featureView=new a({}),t.featureView.on("featureClose",function(){window.localStorage.setItem("has_showFisrt_rocket_feature","firstRocket")}))},events:{"click .j-add":"handleAdd"},initTable:function(){var i=this;i.dt||(i.dt=new n({$el:i.$el,url:"/EM1HSTAGE/Definition/GetDefinitionList",tableName:"saleaction",showMultiple:!1,showFilerBtn:!1,showMoreBtn:!1,showManage:!1,showCustom:!1,title:$t("阶段推进器"),search:{placeHolder:$t("搜索阶段推进器名称"),type:"name",highFieldName:"Name"},requestType:"FHHApi",searchTerm:{pos:"C",type:"enable",options:[{id:1,isdef:!0,name:$t("使用中")},{id:3,name:$t("已停用")}]},columns:[{data:"name",title:$t("阶段推进器名称"),width:220,fixed:!0,render:function(e){return e}},{data:null,title:$t("适用范围"),render:function(e,t,n){var a=[];return _.each(n.rangeCircleIds,function(e){e=r.getCircleById(e);a.push(e?e.name:"--")}),_.each(n.rangeEmployeeIds,function(e){e=r.getEmployeeById(e);a.push(e?e.name:"--")}),_.each(n.rangeGroupIds,function(e){e=_.findWhere(i.groups,{id:e});a.push(e?e.name:"--")}),_.each(n.rangeRoleIds||[],function(e){e=_.findWhere(i.roles,{id:e});a.push(e?e.name:"--")}),a.length?a.join($t("，")):"--"}},{data:"creator",title:$t("创建人"),render:function(e){return(FS.contacts.getEmployeeById(e)||{}).name}},{data:"createTime",title:$t("创建时间"),render:function(e){return FS.moment(e).format("YYYY-MM-DD HH:mm")}},{data:"modifier",title:$t("最后修改人"),render:function(e){return(FS.contacts.getEmployeeById(e)||{}).name}},{data:"modifyTime",title:$t("最后修改时间"),render:function(e){return FS.moment(e).format("YYYY-MM-DD HH:mm")}},{data:"enable",title:$t("状态"),width:100,render:function(e){return[$t("禁用"),$t("启用")][+(e||0)]}},{data:null,title:$t("操作"),width:180,lastFixed:!0,lastFixedIndex:1e3,render:function(e,t,n){var a="",i=[$t("停用"),$t("启用")][n.enable?0:1];return _.each([{label:$t("编辑"),value:"Edit"},{label:i,value:"Enable"},{label:$t("复制并新建"),value:"CopyAdd"},{label:n.enable?"":$t("删除"),value:"Delete"}],function(e){a+='<a href="javascript:;" style="padding-right: 10px;" data-action="'+e.value+'">'+e.label+"</a>"}),a}}],operate:{btns:[{text:$t("新建"),className:"j-add"}]},paramFormat:function(e){return{page:e.pageNumber,pageSize:e.pageSize,enable:e.enable,name:e.name||""}},formatData:function(e){return{totalCount:e.total||0,data:e.data}}}),i.dt.on("trclick",function(t,e,n){n=n.data("action");n?i["handle"+n]&&i["handle"+n](t):o.async("paas-rocket/detail",function(e){i.detail&&i.detail.destroy(),i.detail=new e({sourceWorkflowId:t.sourceWorkflowId}),i.detail.on("refresh",function(){var e=i.dt.getParam();i.dt.setParam(e,!0)})})}))},handleEnable:function(e){function t(){d.enable({flowid:e.sourceWorkflowId,enable:a},function(){FS.util.remind(i+$t("成功")),n.dt&&n.dt.setParam({},!0)})}var n=this,a=!e.enable,i=a?$t("启用"):$t("禁用");a?t():FS.util.confirm($t("确认停用吗"),null,function(){t(),this.destroy()})},handleAdd:function(e){this.renderEdit({_type:"add"})},handleEdit:function(e){this.renderEdit({_type:"edit",id:e.sourceWorkflowId})},handleCopyAdd:function(e){this.renderEdit({_type:"copy_add",id:e.sourceWorkflowId})},handleDelete:function(e){var t=this,n=FS.util.confirm($t("确认删除吗"),null,function(){d.delete(e.sourceWorkflowId,function(){FS.util.remind($t("删除成功")),t.dt&&t.dt.setParam({},!0),n.hide()})})},renderEdit:function(n,a){var i=this,r=n._type;delete n._type,o.async("paas-rocket/sdk",function(e){i.rocketInstance||(i.rocketInstance=new e(n),i.rocketInstance.on("refresh",function(){i.dt.setParam({},!0)}));function t(){$(document.body).append(i.rocketInstance.$el),a&&a()}"add"==r?d.quota(function(e){if(e.quota>=e.totalQuota)return FS.util.alert($t("当前流程总数已达到配额上限{{number}}条",{number:e.totalQuota}));i.rocketInstance.add(),t()}):"edit"==r?d.get(n.id,function(e){i.rocketInstance.edit({definition:e}),t()}):"copy_add"==r&&d.get(n.id,function(e){e=$.extend(!0,{},e);e.name=e.name+$t("副本"),delete e.sourceWorkflowId,i.rocketInstance.add({definition:e}),t()})})},renderDetail:function(t,n){var a=this;o.async("paas-rocket/sdk",function(e){e=e.renderDetail(t);a.sdkInstance=e,n&&n()})},destroy:function(){var t=this;_.each(["dt","detail","_add","_edit"],function(e){t[e]&&t[e].destroy()}),this.sdkInstance&&this.sdkInstance.destroy()}});t.exports=i});