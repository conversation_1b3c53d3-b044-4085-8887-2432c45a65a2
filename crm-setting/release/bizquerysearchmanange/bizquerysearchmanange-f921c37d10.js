define("crm-setting/bizquerysearchmanange/bizquerysearchmanange",["crm-modules/common/util","./template/index-html"],function(n,e,t){var a=n("crm-modules/common/util"),r=n("./template/index-html"),i=Backbone.View.extend({config:{page1:{id:"transformlimit",wrapper:".clue-pool-box",path:"./transformlimit/transformlimit"}},events:{},initialize:function(e){this.setElement(e.wrapper)},render:function(e){var t=a.getTplQueryParams(),t=_.values(t);e=e||[t[1]],this.pages={},this.$el.html(r()),this.renderPage(e)},renderPage:function(e){var t,a=this,r=a.$(".crm-tab .item"),r=(r.removeClass("cur"),(e&&e[0]?r.filter("."+e[0]):r.eq(0)).addClass("cur"),a.curId=e&&e[0]?e[0]:"page1");_.map(a.pages,function(e){e.hide()}),a.pages[r]?a.pages[r].show():(t=r,n.async(a.config[t].path,function(e){e=new e(_.extend({parentWrapper:a.$el,wrapper:a.config[t].wrapper,apiname:"LeadsObj"}));a.curId===t&&e.show(),a.pages[t]=e}))},destroy:function(){_.map(this.pages,function(e){e.destroy&&e.destroy()}),this.pages=this.curId=null}});t.exports=i});
define("crm-setting/bizquerysearchmanange/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit crm-manageclue-title"> <h2><span class="tit-txt">' + ((__t = $t("management.menu.facishare-system.crmmanage/=/module-bizquerysearchmanange")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-tab"> <a class="item page1" href="#crm/setting/bizquerysearchmanange">' + ((__t = $t("crm.bizquery.manage.transform.limit.tab")) == null ? "" : __t) + '</a> </div> <div class="tab-con"> <div class="crm-p20 crm-scroll"> <div class="bizquery-secrch-manage-tip"></div> <div class="item clue-pool-box" style="display:none;"> <div class="crm-loading"></div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/bizquerysearchmanange/transformlimit/transformlimit",["crm-modules/common/util","crm-modules/components/objecttable/objecttable"],function(t,e,a){var r=t("crm-modules/common/util"),i=t("crm-modules/components/objecttable/objecttable"),n=Backbone.View.extend({initialize:function(t){var e=this;e.setElement(t.wrapper),e.opts=t,e.apiname="BizQueryManageObj",e.initTable(),this.setTipWrap()},setTipWrap:function(){this.tipWrap=FxUI.create({wrapper:this.opts.parentWrapper.find(".bizquery-secrch-manage-tip")[0],template:'\n          <div class="bizquery-secrch-manage-tip-box" style="padding: 12px 18px 4px 24px;">\n              <fx-alert\n                  :closable="false"\n                  type="info">\n                  <template slot="title">\n                    <div class="">\n                      1、{{$t(\'crm.bizquery.manage.transform.default.limit.tip\')}}\n                    </div>\n                    <div class="">\n                      2、{{$t(\'crm.bizquery.manage.transform.default.limit.tip2\')}}\n                    </div>\n                  </template>\n              </fx-alert>  \n          </div>\n          '})},refresh:function(){this.table.table._clearChecked(),this.table.refresh()},events:{},initTable:function(){var s=this;if(s.table)return this;var t=i.extend({parseData:function(t){var e=i.prototype.parseData.apply(this,arguments);return _.each(e.data,function(e){_.each(e.operate,function(t){"ChangeStatus"==t.action&&(t.label=1==e.biz_status?$t("停用"):$t("启用")),t.render_type="not_fold"})}),e},parseColumns:function(){var t=i.prototype.parseColumns.apply(this,arguments);return t.some(function(t){if("operate"==t.dataType)return t.width=170,!0;"name"==t.api_name&&(t.render=function(t){return"<span>".concat(t,"</span>")})}),t},operateBtnClickHandle:function(t){var e,t=$(t.target),a=t.data("action"),i=t.closest(".tr").data("index"),n=this.getCurData().data,o=n&&n[i],n=t.closest(".tr").find("[data-action=ChangeStatus]").text();"add"==a?s.doAction("add"):"Edit"==a?(i={apiname:o.object_describe_api_name,dataId:o._id,displayName:o.name,data:o,success:function(){s.refresh()}},s.doAction("edit",i)):"Delete"==a?FxUI.MessageBox.confirm($t("crm.bizquery.manage.delete.rule.tip"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){r.FHHApi({url:"/EM1HNCRM/API/v1/object/"+o.object_describe_api_name+"/action/BulkDelete",data:{describe_api_name:o.object_describe_api_name,idList:_.pluck([o],"_id")},success:function(t){0===t.Result.StatusCode?(FxUI.Message.success($t("删除规则成功")),s.table.refresh()):r.remindFail(t.Result.FailureMessage)}})}):"ChangeStatus"==a?e=r.confirm($t("确认{{changestatus}}这个规则？",{changestatus:n}),null,function(){e.destroy(),s.changeEnable(o._id,1==o.biz_status?"0":"1")},{hideFn:function(){},stopPropagation:!0}):"Clone"==a&&s.cloneDataList(o)},getOptions:function(){var t=i.prototype.getOptions.apply(this,arguments);return _.extend(t,{custom_className:"crm-cluepool-table",searchTerm:!1,isOrderBy_allColumn:!1,showFilerBtn:!1,showMultiple:!1,hideIconSet:!0,checked:{idKey:"_id",data:[]}})}});s.table=new t({el:s.$el,apiname:s.apiname,showTitle:!1,showTerm:!1,showTermBatch:!1,showOperate:!0,search:{placeHolder:$t("搜索规则名称"),type:"Keyword",highFieldName:"name",pos:"T"},operate:{pos:"T",btns:[{action:"add",attrs:"data-action=add",className:"j-action",text:$t("新建")}]}}),s.table.render()},changeEnable:function(t,e){var a=this;CRM.util.waiting(),CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/BizQueryManageObj/action/ChangeStatus",data:{objectIds:[t],bizStatus:e},success:function(t){0==t.Result.StatusCode?(CRM.util.waiting(!1),CRM.util.remind(1,$t("操作成功")),a.refresh()):(CRM.util.waiting(!1),CRM.util.remindFail(t.Result.FailureMessage))},error:function(){CRM.util.waiting(!1)}},{errorAlertModel:1})},cloneDataList:function(a){var i=this;CRM.util.waiting(),CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/BizQueryManageObj/action/Clone",data:{objectDataId:a._id},success:function(t){var e;CRM.util.waiting(!1),0===t.Result.StatusCode?(e={show_type:"full",dataId:a._id,_sourceId:a._id,data:t.Value.objectData,_staticData:t.Value.objectData,mdData:t.Value.details,apiname:a.object_describe_api_name,displayName:a.name,title:$t("复制"),className:"",_from:"clone",isCopy:!0,showMask:!0,success:function(){i.refresh()}},i.doAction("add",e)):r.alert(t.Result.FailureMessage||$t("操作失败"))},error:function(){CRM.util.waiting(!1)}},{errorAlertModel:1})},doAction:function(e,a,i){var n=this;t.async("crm-modules/action/bizquerymanageobj/bizquerymanageobj",function(t){n.action||(n.action=new t,n.listenTo(n.action,{success:n.doSuccess,error:n.doError})),n.action[e]&&n.action[e](a,i)})},doSuccess:function(t,e,a){this.refresh()},doError:function(t,e){},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){var t,e=this;e.stopListening(),e.remove(),e.action&&e.action.destroy(),e.table&&e.table.destroy(),e.tipWrap&&null!=(t=e.tipWrap)&&t.destroy(),e.table=e.action=e.$el=e.el=e.events=e.options=null}});a.exports=n});