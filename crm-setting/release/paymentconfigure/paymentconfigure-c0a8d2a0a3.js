define("crm-setting/paymentconfigure/config",[],function(e,t,i){i.exports={CONFIG_DATA:[{domId:"level0",moduleId:"Payment",title:$t("crm.PaymentObj",null,"回款"),moduleList:[{domId:"level0_0",title:$t("crm.setting.order_payment_setting.title"),type:"radio",key:"order_payment_setting__c",isShow:function(){return CRM.util.isGrayScale("CRM_PAYMENT_V2")},children:[{title:$t("crm.setting.is_open_order_payment_multi_source.title"),key:"is_open_order_payment_multi_source",value:"0",enableClose:!1,describeList:[{title:$t("crm.setting.is_open_order_payment_multi_source.desc1")},{title:$t("crm.setting.is_open_order_payment_multi_source.desc2")}]},{title:$t("crm.setting.order_payment_mapping_rule.title"),key:"order_payment_mapping_rule",depKeys:["is_open_payment_multi_source_field"],type:"OrderPaymentMappingRule",isShow:function(e){return!!e.is_open_order_payment_multi_source}},{title:$t("crm.setting.order_payment_required.title"),key:"order_payment_required",type:"OrderPaymentRequired",value:!1,describeList:[{title:$t("crm.setting.order_payment_required.desc1")}]}]},{domId:"level0_1",title:$t("开启线上支付能力"),key:"is_payment_pay_enable",dependKeys:["is_customer_account_enable"],value:!1,enableClose:!1,describeList:[{title:$t("回款默认为客户的回款记录当企业开通企业钱包后可启用线上支付能力")},{title:$t("启用客户账户2.0回款可入账到客户账户")}]},{domId:"level0_2",title:$t("开启入账到客户账户"),key:"is_payment_enter_account_enable",value:!1,enableClose:!1,describeList:[{title:$t("开启入账到客户账户.desc")}],children:[{title:$t("带回款明细的回款，仍可以全额入账"),key:"is_payment_with_detail_enter_account_enable",value:!1,enableClose:!1,isShow:function(e){return!!e.is_payment_enter_account_enable}}]},{domId:"level0_3",title:$t("crm.setting.tradeconfigure.is_open_received_payment_title"),type:"switch",key:"is_open_received_payment",value:!1,enableClose:!1,describeList:[{title:$t("crm.setting.tradeconfigure.is_open_received_payment_desc")}]}]}],KEY_CONFIG:{is_customer_account_enable:{type:"string"},is_payment_enter_account_enable:{type:"string"},claim_upon_receipt_of_payment:{type:"string"},payment_order_setting:{type:"string"},order_payment_mapping_rule:{type:"string"},is_payment_with_detail_enter_account_enable:{type:"string"},order_payment_required:{type:"string"}}}});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function asyncGeneratorStep(e,t,n,r,o,a,i){try{var c=e[a](i),u=c.value}catch(e){return void n(e)}c.done?t(u):Promise.resolve(u).then(r,o)}function _asyncToGenerator(c){return function(){var e=this,i=arguments;return new Promise(function(t,n){var r=c.apply(e,i);function o(e){asyncGeneratorStep(r,t,n,o,a,"next",e)}function a(e){asyncGeneratorStep(r,t,n,o,a,"throw",e)}o(void 0)})}}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/paymentconfigure/paymentconfigure",["../promotionrebate/promotionrebate","./config"],function(e,t,n){var a=e("../promotionrebate/promotionrebate").Base,e=e("./config"),r=e.CONFIG_DATA,o=e.KEY_CONFIG,i="claim_upon_receipt_of_payment";n.exports=a.extend({getConfigData:function(){return r},getConfigKeyData:function(){return o},transValue:function(e,t,n){return["is_payment_enter_account_enable","is_payment_with_detail_enter_account_enable"].includes(e)?n?t?"2":"0":"2"===t:[i].includes(e)?n?t?"1":"0":"1"===t:"order_payment_mapping_rule"===e?n?JSON.stringify(t):JSON.parse(t):"order_payment_required"===e?n?JSON.stringify(_objectSpread(_objectSpread({},t),{},{status:t.status?"1":"0"})):_objectSpread(_objectSpread({},e=JSON.parse(t)),{},{status:"1"===e.status}):t},beforeSetConfig:function(n,e){var r=arguments,o=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,a.prototype.beforeSetConfig.apply(o,r);case 2:return t=e.sent,n===i&&(t.confirmInfo=$t("crm.setting.claim_upon_receipt_of_payment.confirm")),e.abrupt("return",t);case 5:case"end":return e.stop()}},e)}))()},setConfig:function(e){var t=this.currentChangeKey;return"is_payment_pay_enable"===t?this.setPaymentPayEnableConfig():"is_payment_enter_account_enable"===t?this.setPaymentEnterAccountConfig():"is_payment_with_detail_enter_account_enable"===t?this.setPaymentWithDetailEnterAccount():t===i?this.setClaimUponReceiptOfPayment():a.prototype.setConfig.apply(this,arguments)},afterSetConfig:function(e){e=e.update;"is_open_order_payment_multi_source"===this.currentChangeKey&&e(["order_payment_mapping_rule"])},setPaymentPayEnableConfig:function(){var r=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=r,e.next=3,t.fetch({url:"/EM1HNCRM/API/v1/object/payment_pay/service/query_valid_isv"}).then(function(e){return e.Value.valid_isv_list});case 3:if((n=e.sent)&&n.length)return e.next=7,t.confirm($t("确定要开启显示支付能力吗"));e.next=11;break;case 7:return e.next=9,t.commonSetConfigFetch({url:"/EM1HNCRM/API/v1/object/payment_pay/service/enable_payment_pay",logData:{key:"is_payment_pay_enable",value:"1"}});case 9:e.next=14;break;case 11:return e.next=13,t.confirm($t("企业钱包暂未绑定支付宝或微信账号请先绑定后在启用"),{btnLabel:{confirm:$t("现在绑定"),cancel:$t("知道了")}});case 13:window.location.hash="#app/entwallet/wallet";case 14:case"end":return e.stop()}},e)}))()},setPaymentEnterAccountConfig:function(){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("2"!==(t=n).getKeyValues("is_customer_account_enable"))throw new Error($t("该租户未启用客户账户模块请联系管理员开通"));e.next=3;break;case 3:return e.next=5,t.confirm($t("启用回款入账将在对象上新增等字段并预设按钮确定要启用吗"));case 5:return e.next=7,t.commonSetConfigFetch({url:"/EM1HNCRM/API/v1/object/fund_account/service/payment_enter_account_init",logData:{key:"is_payment_enter_account_enable",value:"1"}});case 7:case"end":return e.stop()}},e)}))()},setPaymentWithDetailEnterAccount:function(){var n=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t={key:"is_payment_with_detail_enter_account_enable",value:"2"},e.next=3,n.commonSetConfigFetch({url:"/EM1HNCRM/API/v1/object/crm_config/service/set_config_value",data:t});case 3:case"end":return e.stop()}},e)}))()},setClaimUponReceiptOfPayment:function(){var t=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,t.fetch({url:"/EM1HNCRM/API/v1/object/payment_claim/service/claim_upon_receipt_of_payment"});case 2:case"end":return e.stop()}},e)}))()}}),n.exports.config=e});