define("crm-setting/menumanage/menumanage",["crm-modules/common/util","crm-widget/table/table","./update/update"],function(e,t,a){var i=e("crm-modules/common/util"),n=e("crm-widget/table/table"),s=e("./update/update");a.exports=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.widgets={},this.createEl(),this.sysMenuFlag=!0},events:{"click .j-add":"onAdd","click .j-switch-sysmenu":"switchSysMenu"},createEl:function(){var t=this;t.$el.html(['<div class="list-view">','<div class="list-tb"></div>','<div class="list-info">'+$t("当前有")+"<em>--</em>"+$t("个crm菜单最多可新建30个crm菜单")+"</div>","</div>",'<div class="set-view"></div>'].join("")),t.getColumns(function(e){t.initTable(e)})},initTable:function(e){var s=this;s.widgets.dt?s.widgets.dt.setParam({},!0):(s.widgets.dt=new n({$el:s.$(".list-tb"),title:$t("uipaas.CRM.menu",null,"CRM菜单"),requestType:"FHHApi",url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/menu_list",paramFormat:function(e){return e.formManagerFlag=!0,e},showMultiple:!1,showPage:!1,operate:{pos:"C",btns:[{text:$t("新建"),className:"j-add"}]},columns:e,formatData:function(e){return s.$(".list-info em").html(e.menuList.length),{data:s.formatData(e.menuList)}}}),s.widgets.dt.on("trclick",function(e,t,a){var n=a.data("operate");a.hasClass("disabled")||n&&s.operateHandle(n,e,t)}),s.tableComplete())},getColumns:function(t){var a=[{data:"name",title:$t("应用菜单名称"),fixed:!0},{data:"description",title:$t("描述")},{data:"scopeNameList",title:$t("适用范围"),render:function(e,t,a){try{return e.length?e.join(","):"--"}catch(e){return"--"}}},{data:"created_by",title:$t("创建人"),referRule:"Employee",isId:!0,dataType:8},{data:"create_time",title:$t("创建时间"),dataType:4},{data:"status",title:$t("状态")},{data:"",title:$t("操作"),lastFixed:!0,render:function(e,t,a){a=a.is_system?['<div class="ops-btns">','<a data-operate="edit">'+$t("编辑")+"</a>",'<a data-operate="copyMenu">'+$t("复制")+"</a>","</div>"]:"1"===a.active_status?['<div class="ops-btns">','<a data-operate="edit">'+$t("编辑")+"</a>",'<a data-operate="toggle">'+$t("禁用")+"</a>",'<a data-operate="copyMenu">'+$t("复制")+"</a>","</div>"]:['<div class="ops-btns">','<a data-operate="edit">'+$t("编辑")+"</a>",'<a data-operate="toggle">'+$t("启用")+"</a>",'<a data-operate="del">'+$t("删除")+"</a>",'<a data-operate="copyMenu">'+$t("复制")+"</a>","</div>"];return a.join("")}}];this.isShowMenuId(function(e){e&&a.splice(6,0,{data:"_id",title:$t("paas.crm.setting.menumanage.menu_id",null,"菜单ID"),render:function(e,t,a){return e?['<div class="td-hover" title="'.concat(e,'">'),'<span class="j-menu-id" style="display: inline-block;width: 70%;overflow: hidden;vertical-align: top;text-overflow: ellipsis;white-space: nowrap;">'.concat(e,"</span>"),'<a href="javascript:;" data-operate="copy" class="operate-copy" data-templateid="'.concat(e,'">').concat($t("复制"),"</a> "),"</div>"].join(""):"--"}}),t&&t(a)})},tableComplete:function(){var e=this;$(".j-showSysMenu",e.$el).length||(e.$(".dt-tit").after(['<div class="fx-checkbox-inline sysmenu-item" style="margin-top:22px;">','<label class="item sysmenu-item" >','<input type="checkbox" class="j-switch-sysmenu" name="options1"  value="1">','<span class="fx-checkbox-icon"></span>','<span class="fx-checkbox-text">'+$t("已配置租户级菜单的账号，隐藏系统预设菜单")+"</span>","</label>","</div>"].join("")),e.queryHideSysMenu())},queryHideSysMenu:function(){var t=this;i.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/query_tenant_config",success:function(e){0==e.Result.StatusCode&&e.Value&&(1==e.Value.result||"true"==e.Value.result?t.sysMenuFlag=!0:t.sysMenuFlag=!1,t.sysMenuCheck(t.sysMenuFlag))}})},isShowMenuId:function(e){var t=!1;i.FHHApi({url:"/EM2HUSEREXT/MenuTemplate/openRestriction",data:{},success:function(e){e&&e.Value&&(t=e.Value.isOpen)},complete:function(){e&&e(t)}},{errorAlertModel:1})},sysMenuCheck:function(e){e?$(".sysmenu-item input").prop("checked",!0):$(".sysmenu-item input").prop("checked",!1)},switchSysMenu:function(){var t=this,e=this.sysMenuFlag?"close_tenant_config":"open_tenant_config";i.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/"+e,success:function(e){0==e.Result.StatusCode&&(t.sysMenuFlag=!t.sysMenuFlag,t.sysMenuCheck(t.sysMenuFlag),t.refresh())}})},formatData:function(e){return _.each(e,function(e){e.is_system&&(e.scopeNameList=[$t("全公司")]),e.status="1"===e.active_status?$t("已启用"):$t("已禁用")}),e},operateHandle:function(e,t,a){switch(e){case"toggle":this.onToggle(t);break;case"del":this.onDel(t);break;case"edit":this.onUpdate(_.extend(t,{type:"edit"}));break;case"copy":this.onCopyMenuId(a);break;case"copyMenu":this.onUpdate(_.extend(t,{type:"copy"}))}},refresh:function(){CRM.control.refreshAside(),this.widgets.dt.setParam({},!0)},onToggle:function(e){function t(){i.FHHApi({url:s?"/EM1HWebPage/API/v1/object/crm_menu_admin/service/disable":"/EM1HWebPage/API/v1/object/crm_menu_admin/service/enable",data:{menu_id:e._id},success:function(e){0==e.Result.StatusCode&&(i.remind(1,$t("操作成功！")),n.refresh())}})}var a,n=this,s="1"===e.active_status;s?a=i.confirm("<p>"+$t("确认禁用该应用菜单吗禁用后相关用户将无法查看并使用该菜单")+"</p>",$t("禁用"),function(){t(),a.hide()}):t()},onDel:function(e){var t=this,a=i.confirm("<p>"+$t("确认删除该应用菜单")+"</p>",$t("删除"),function(){i.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/delete",data:{menu_id:e._id},success:function(e){0==e.Result.StatusCode&&(i.remind(1,$t("操作成功！")),t.refresh()),a.hide()}})})},onAdd:function(){var t=this;i.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/validate_menu_limit",success:function(e){0==e.Result.StatusCode&&t.onUpdate({type:"add"})}})},onUpdate:function(e){var t=this,a=t.widgets;a.update&&(a.update.destroy(),a.update=null),a.update=new s(e),a.update.on("refresh",function(e){t.refresh(),a.update=null})},onCopyMenuId:function(e){var t=document.createRange(),a=document.getSelection();t.selectNode(e.find(".j-menu-id")[0]),a.removeAllRanges(),a.addRange(t),document.execCommand("Copy")},destroy:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets={}}})});
define("crm-setting/menumanage/sortmenu/item-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<li class="item menu-item" data-type="group"> <div class="name group-name"> <div class="text hide"></div> <input type="text" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" class="b-g-ipt" maxlength="8"> <div class="ops"> <span class="crm-ico-edit"></span> <span class="crm-ico-delete"></span> <span class="crm-ico-stick"></span> <span class="crm-ico-bottom"></span> <span class="crm-ico-visible"></span> </div> </div> <ul class="sub-menu" data-sort_index="' + ((__t = _.uniqueId()) == null ? "" : __t) + '"></ul> </li>';
        }
        return __p;
    };
});
define("crm-setting/menumanage/sortmenu/item-menu-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<li class="item menu-item" data-menu_item_id="' + ((__t = ite.menuItemId) == null ? "" : __t) + '" data-type="menu"> <div class="name"> <div class="text" data-name="' + ((__t = ite.value) == null ? "" : __t) + '">' + ((__t = ite.name) == null ? "" : __t) + '</div> <div class="ops"> <span class="crm-ico-delete crm-ico-canDelete"></span> <span class="crm-ico-stick"></span> <span class="crm-ico-bottom"></span> <span class="crm-ico-visible ';
            if (ite.isHidden) {
                __p += " " + ((__t = " crm-ico-invisible") == null ? "" : __t);
            }
            __p += '"></span> </div> </div> </li>';
        }
        return __p;
    };
});
define("crm-setting/menumanage/sortmenu/menu-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="content crm-menu"> <div class="search-box"> <span class="search-icon"></span> <input class=\'j-search-box search-input\' value=\'\' type=\'text\' placeholder="' + ((__t = $t("请输入对象名称")) == null ? "" : __t) + '" /> <span data-v-25a7196c="" class="search-clear j-search-clear" style=""><svg data-v-25a7196c="" version="1.1" role="presentation" width="10" height="13" viewBox="0 0 12 16" class="clear-svg octicon"><path d="M7.48 8l3.75 3.75-1.48 1.48L6 9.48l-3.75 3.75-1.48-1.48L4.52 8 .77 4.25l1.48-1.48L6 6.52l3.75-3.75 1.48 1.48z"></path></svg></span> <span class="menu-search-btn crm-btn crm-btn-primary j-search-menu">' + ((__t = $t("搜索")) == null ? "" : __t) + '</span> <div class="search-tip"> <p>' + ((__t = $t("无搜索结果")) == null ? "" : __t) + '</p> </div> </div> <div class="menu-wrapper crm-scroll"> <ul class="menu"> <li class="item menu-item fixed-item"> <div class="name"> <div class="text" data-searchWords="首页,shouye,sy">' + ((__t = $t("首页")) == null ? "" : __t) + "</div> </div> </li> ";
            _.each(items, function(item, index) {
                __p += " ";
                var is_group_item = item.type === "group";
                __p += " ";
                var is_saleRecord = (item.value || item.referenceApiname) === "SaleRecord";
                __p += ' <li class="item menu-item ';
                if (is_saleRecord) {
                    __p += "unshow";
                }
                __p += '" data-type="' + ((__t = item.type) == null ? "" : __t) + '" ';
                if (!is_group_item) {
                    __p += ' data-menu_item_id="' + ((__t = item.menuItemId) == null ? "" : __t) + '"';
                }
                __p += '> <div class="name ';
                if (is_group_item) {
                    __p += (__t = "group-name") == null ? "" : __t;
                }
                __p += '" > <div class="text" data-name="' + ((__t = item.value || item.referenceApiname) == null ? "" : __t) + '" data-searchWords="' + ((__t = item.searchWords || -item.displayName || item.name) == null ? "" : __t) + '">' + __e(item.displayName || item.name) + '</div> <input type="text" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" class="b-g-ipt hide"> <div class="ops"> ';
                if (is_group_item) {
                    __p += ' <span class="crm-ico-edit"></span> <span class="crm-ico-delete"></span> ';
                }
                __p += " ";
                if (!is_group_item && item.canDelete) {
                    __p += ' <span class="crm-ico-delete ';
                    if (item.canDelete) {
                        __p += " " + ((__t = " crm-ico-canDelete") == null ? "" : __t);
                    }
                    __p += '"></span> ';
                }
                __p += ' <span class="crm-ico-stick"></span> <span class="crm-ico-bottom"></span> <span class="crm-ico-visible ';
                if (item.isHidden) {
                    __p += " " + ((__t = " crm-ico-invisible") == null ? "" : __t);
                }
                __p += '"></span> </div> </div> ';
                if (is_group_item) {
                    __p += ' <ul class="sub-menu" data-sort_index="' + ((__t = index) == null ? "" : __t) + '"> ';
                    _.each(item.children, function(ite) {
                        __p += ' <li class="item" data-menu_item_id="' + ((__t = ite.menuItemId) == null ? "" : __t) + '" data-type="menu"> <div class="name"> <div class="text" data-name="' + ((__t = ite.value || ite.referenceApiname) == null ? "" : __t) + '" data-searchWords="' + ((__t = ite.searchWords || ite.displayName || ite.name) == null ? "" : __t) + '">' + ((__t = ite.displayName || ite.name) == null ? "" : __t) + '</div> <div class="ops"> <span class="crm-ico-stick"></span> <span class="crm-ico-bottom"></span> <span class="crm-ico-visible ';
                        if (ite.isHidden) {
                            __p += " " + ((__t = " crm-ico-invisible") == null ? "" : __t);
                        }
                        __p += '"></span> </div> </div> </li> ';
                    });
                    __p += " </ul> ";
                }
                __p += " </li> ";
            });
            __p += ' </ul> </div> </div> <p class="add-group"> <span class="add-btn j-add ">' + ((__t = $t("创建分组")) == null ? "" : __t) + '</span> <span class="add-custom-page j-add-custom ">' + ((__t = $t("添加自定义页面")) == null ? "" : __t) + "</span> </p>";
        }
        return __p;
    };
});
define("crm-setting/menumanage/sortmenu/sortmenu",["crm-modules/common/util","base-sortable","./menu-html","./item-html","./item-menu-html"],function(e,t,i){var c=e("crm-modules/common/util"),n=e("base-sortable"),s=e("./menu-html"),a=e("./item-html"),r=e("./item-menu-html");i.exports=Backbone.View.extend({isEdit:!1,initialize:function(e){this.widgets=[],this.groupNameCache="",this.data=this.parseData(e.data),this.el.innerHTML=s({items:this.data}),this.initMenuSortable(),this.$menu=this.$(".menu"),this.initSubMenuSortable(this.$menu),this.menu_id=e.data.id,this.fxSelectValue=""},events:{"click .group-name":"onToggle","click .crm-ico-edit":"onEdit","click .crm-ico-visible":"onVisualize","click .crm-ico-delete":"onDel","click .crm-ico-stick":"onStick","click .crm-ico-bottom":"onBottom","click .b-g-ipt":"stopPropagation","blur .b-g-ipt":"onBlur","keypress .b-g-ipt":"onKeypress","click .j-add":"onAdd","click .add-custom-page":"onAddCustomPage","focus .j-search-box":"searchBox","blur .search-box":"searchBlur","click .j-search-clear":"searchClear","click .j-search-menu":"searchMenu"},stopPropagation:function(e){e.stopPropagation()},onToggle:function(e){var e=$(e.currentTarget),t=e.next();e.toggleClass("fold"),t.slideToggle()},onEdit:function(e){var t=$(e.currentTarget).parent().prev(),i=t.prev();t.removeClass("hide"),i.addClass("hide"),this.groupNameCache=i.text(),t.val(i.text()),t.focus(),e.stopPropagation()},onVisualize:function(e){var t,i=$(e.currentTarget),n=i.parents(".group-name");i.toggleClass("crm-ico-invisible"),n.length?(t=n.next(),i.hasClass("crm-ico-invisible")?t.find(".crm-ico-visible").addClass("crm-ico-invisible"):t.find(".crm-ico-visible").removeClass("crm-ico-invisible")):(n=(t=i.parents(".sub-menu")).prev(),_.every(t.find(".crm-ico-visible"),function(e){return $(e).hasClass("crm-ico-invisible")})?n.find(".crm-ico-visible").addClass("crm-ico-invisible"):n.find(".crm-ico-visible").removeClass("crm-ico-invisible")),e.stopPropagation()},onStick:function(e){var t,i=$(e.target).closest(".item");i.hasClass("menu-item")?(t=i.parent(".menu").children(".fixed-item"),i.insertAfter(t)):(t=i.parent(".sub-menu")).prepend(i),e.stopPropagation()},onBottom:function(e){var t=$(e.target).closest(".item"),i=t.hasClass("menu-item")?t.parent(".menu"):t.parent(".sub-menu");i.append(t),e.stopPropagation()},onDel:function(e){var t,i,n=$(e.target).parents(".item"),s=n.find(".sub-menu"),a=s.data("sort_index"),r=this.widgets;"menu"===n.attr("data-type")?t=c.confirm("<p>"+$t("crm.删除自定义菜单提示")+"</p>",$t("删除"),function(){n.remove(),t.hide()}):i=c.confirm("<p>"+$t("crm.删除菜单分组提示")+"</p>",$t("删除"),function(){s.find(".item").addClass("menu-item"),r[a].destroy&&(r[a].destroy(),r[a]=null),n.before(s.find(".item")),n.remove(),i.hide()}),e.stopPropagation()},onBlur:function(e){var t=this.$(".b-g-ipt:visible"),i=t.val(),n=t.prev(),s=this.$(".group-name"),a=!1;i!==this.groupNameCache&&_.each(s,function(e){e=$(e).find(".text").text();i==e&&(a=!0)}),a?(t.addClass("error"),c.remind(3,$t("分组名称已重复"))):t.length&&!t.val()?c.remind(3,$t("请先完成当前操作")):(t.addClass("hide"),n.removeClass("hide"),n.text(t.val()))},onKeypress:function(e){var t=this.$(".b-g-ipt:visible"),i=t.prev();13===e.keyCode&&(t.length&&!t.val()?c.remind(3,$t("请先完成当前操作")):(t.addClass("hide"),i.removeClass("hide"),i.text(t.val())))},onAdd:function(){var e=this.$(".b-g-ipt:visible");this.groupNameCache="",30==this.$(".group-name").length?c.remind(3,$t("菜单分组不能超过30个")):e.length&&!e.val()?(c.remind(3,$t("请先完成当前操作")),e.focus()):(this.$menu.append(a()),this.initSubMenuSortable(this.$menu.children().last()),this.$(".content").scrollTop(this.$menu[0].scrollHeight),this.$(".b-g-ipt:visible").focus(),this.isEdit=!0)},onAddCustomPage:function(){var i,e=this;this.confirm=c.confirm('<div id="j-add-custom-page"></div>',$t("选择要添加的自定义页面"),function(){if(!i)return e.confirm.hide(),!1;e.$menu.append(r({ite:{value:i.value,name:i.label}})),e.initSubMenuSortable(e.$menu.children().last()),e.$(".crm-scroll").scrollTop(e.$menu[0].scrollHeight),e.fxSelectValue="",e.confirm.hide()}),e.getCusMenuItems(function(e){FxUI.create({wrapper:"#j-add-custom-page",template:'<fx-select\n                                :placeholder="$t(\'请选择\')"\n                                v-model="value"\n                                :options="options"\n                                filterable\n                                width="90%"\n                                :disabled="disabled">\n                            </fx-select>',data:function(){return{options:e,value:""}},watch:{value:function(t){i=this.options.filter(function(e){return e.value===t})[0]}}})})},initMenuSortable:function(){var s=this,t=this.$(".menu")[0];s.widgets[0]=n.create(t,{group:"menu",animation:300,filter:".fixed-item",handle:".item",draggable:".item",scroll:t,scrollSensitivity:50,scrollSpeed:20,onMove:function(e){var t=$(e.related),i=($(e.dragged),t.find(".group-name")),n=t.find(".sub-menu"),e=$(e.dragged).find(".group-name");if(s.isEdit=!0,s.$(".text").removeClass("text-target"),i.find(".text").addClass("text-target"),t.hasClass("fixed-item"))return!1;if(i.hasClass("fold"))i.removeClass("fold"),n.show();else{if((i.length||t.parents(".item").find(".group-name").length)&&e.length)return!1;n.find(".item").length||n.addClass("empty-menu")}},onEnd:function(e){s.$(".text").removeClass("text-target"),$(t).find(".sub-menu").removeClass("empty-menu");e=$(e.item);e.closest("ul").hasClass("sub-menu")&&e.removeClass("menu-item")},onUpdate:function(e){s.isEdit=!0}})},initSubMenuSortable:function(e){var i=this,e=e.find(".sub-menu");_.each(e,function(e,t){i.widgets[$(e).data("sort_index")]=n.create(e,{group:"menu",animation:300,handle:".item",draggable:".item",scroll:e,scrollSensitivity:50,scrollSpeed:20,onMove:function(e){var e=$(e.related).closest(".menu-item"),t=e.find(".group-name");t.hasClass("fold")?(t.removeClass("fold"),e.find(".sub-menu").show()):e.find(".item").length||e.find(".sub-menu").addClass("empty-menu"),i.isEdit=!0},onEnd:function(e){i.$(".sub-menu").removeClass("empty-menu");e=$(e.item);e.closest("ul").hasClass("sub-menu")||e.addClass("menu-item")},onUpdate:function(){i.isEdit=!0}})})},getCusMenuItems:function(r){var o=this;c.FHHApi({url:"/EM1HWebPage/tenantMenu/getCusMenuItems",success:function(e){if(e.Value){for(var i,n=[],s=o.$(".text"),a=e.Value.customerPageList,t=0;t<a.length;t++)(t=>{i=!1,_.each(s,function(e){e=$(e).attr("data-name");a[t].layoutApiName===e&&(i=!0)}),n[t]={value:a[t].layoutApiName,label:a[t].displayName,disabled:i}})(t);return r(n)}}})},getIsEditStatus:function(){return this.isEdit},getValue:function(){var n=[],s=this.menu_id;return _.each(this.$menu.children(".item:not(.fixed-item)"),function(e,t){var e=$(e),i=e.children(".name"),t=_.extend({is_hidden:!!i.find(".crm-ico-invisible").length,number:t,menu_id:s},e.data());"group"==t.type?t.display_name=i.find(".text").text():t.api_name=i.find(".text").attr("data-name"),t.children=_.map(e.find(".item"),function(e,t){return{api_name:$(e).find(".text").attr("data-name")||$(e).find(".text").text(),is_hidden:!!$(e).find(".crm-ico-invisible").length,number:t,menu_id:s,menu_item_id:$(e).data("menu_item_id"),type:"menu"}}),n.push(t)},this),n},parseData:function(e){var i=[],t=[];return _.each(e,function(e){("menu"!=e.type||e.pid?"group"==e.type?(e.children=[],i):t:i).push(e)}),_.each(t,function(t){_.each(i,function(e){t.pid==e.id&&e.children.push(t)})}),i},searchBox:function(){this.$(".search-box").addClass("focus")},searchBlur:function(){var e=this;setTimeout(function(){e.$(".search-box").removeClass("focus")},300)},searchMenu:function(){var e=this.$(".search-input").val();this.searchHandle(e)},searchClear:function(){this.$(".search-input").val(""),this.$(".name").removeClass("searchTar")},searchHandle:function(s){function n(e,t){var i=o(s),n=(n=t.attr("data-searchWords").split(","))&&n.some(function(e){return i.test(e)});s&&n&&(a++,t.parent(".name").addClass("searchTar"),s)&&1==a&&(n=e.offsetTop-200,r(n))}var t=this,a=0,r=(t.$(".name").removeClass("searchTar"),function(e){t.$(".crm-scroll").animate({scrollTop:e},300,"swing")}),o=function(e){return new RegExp("(.*)(".concat(e,")(.*)"),"i")};_.each(this.$menu.children(".menu-item"),function(e,t){var i=$(e).children(".name").children(".text");n(e,i),_.each($(e).find(".sub-menu").children(".item"),function(e,t){var i=$(e).find(".text");n(e,i)})}),0==a&&(t.$(".search-tip").css("display","block"),setTimeout(function(){t.$(".search-tip").css("display","none")},1e3))},destroy:function(){this.undelegateEvents(),_.each(this.widgets,function(e){e.destroy()}),this.widgets=[]}})});
define("crm-setting/menumanage/template/firstcontent-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item"> <div class="fm-lb"> <em>*</em>' + ((__t = $t("应用菜单名称")) == null ? "" : __t) + '</div> </div> <div class="fm-item"> <input class="b-g-ipt fm-ipt is_required" value="' + __e(name) + '" name="name" maxlength="50" placeholder="' + ((__t = $t("请输入最多50字")) == null ? "" : __t) + '"> <p class="fm-error crm-ico-error name">' + ((__t = $t("请输入规则名称!")) == null ? "" : __t) + '</p> </div> <div class="fm-item fm-api"> <div class="fm-lb"> <em>*</em>API Name </div> </div> <div class="fm-item fm-api"> <input class="b-g-ipt fm-ipt is_required ';
            if (_id) {
                __p += (__t = " b-g-ipt-disabled") == null ? "" : __t;
            }
            __p += '" value="' + __e(api_name) + '" ';
            if (_id) {
                __p += " disabled ";
            }
            __p += ' name="api_name" maxlength="20" placeholder="' + ((__t = $t("请输入最多10字")) == null ? "" : __t) + '"> <p class="fm-error crm-ico-error api_name">' + ((__t = $t("请输入API Name")) == null ? "" : __t) + '</p> </div> <div class="fm-item"> <div class="fm-lb">' + ((__t = $t("菜单描述")) == null ? "" : __t) + '</div> </div> <div class="fm-item"> <!-- TODO: 必须手动去掉空格 --> <textarea maxlength="2000" class="b-g-ipt fm-ipt" name="description" placeholder=' + ((__t = $t("请输入描述最多2000字")) == null ? "" : __t) + ">" + __e(description) + '</textarea> <!-- <span class="charcount"> <span class="">0</span>/<span>200</span> </span> --> </div> <div class="fm-item"> <div class="fm-lb"> <em>*</em>' + ((__t = $t("适用范围")) == null ? "" : __t) + '</div> </div> <div class="fm-item"> <div class="fm-role role"> </div> <p class="fm-error crm-ico-error role">' + ((__t = $t("请输入API Name")) == null ? "" : __t) + '</p> <p class="edit-tip ' + ((__t = name ? "edit" : "new") == null ? "" : __t) + '">' + ((__t = $t("注:当用户从适用范围中移除后，基于此菜单所生成的个人级菜单也将消失")) == null ? "" : __t) + "</p> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/menumanage/template/header-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="left"> <h4 class="title">' + ((__t = type === "edit" ? $t("编辑CRM菜单") : type === "copy" ? $t("复制CRM菜单") : $t("新建CRM菜单")) == null ? "" : __t) + '</h4> <div class="step-bar"> <div class="step-item step-one ';
            if (stepIndex === 0) {
                __p += (__t = " done") == null ? "" : __t;
            }
            __p += '"> ';
            if (0 < stepIndex) {
                __p += ' <i class="el-icon-circle-check ok-icon"></i> ';
            } else {
                __p += ' <span class="step-icon">1</span> ';
            }
            __p += ' <span class="';
            if (0 <= stepIndex) {
                __p += " " + ((__t = "step-title") == null ? "" : __t);
            }
            __p += '">' + ((__t = $t("填写基础信息")) == null ? "" : __t) + '</span> </div> <div class="step-item step-two ';
            if (stepIndex === 1) {
                __p += (__t = " done") == null ? "" : __t;
            }
            __p += '"> <span class="step-label">····</span> <span class="step-icon">2</span> <span class="';
            if (1 <= stepIndex) {
                __p += " " + ((__t = "step-title") == null ? "" : __t);
            }
            __p += '">' + ((__t = $t("设置菜单项")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="right"> ';
            if (stepIndex === 0) {
                __p += ' <span class="crm-btn crm-btn-primary" data-action="next">' + ((__t = $t("下一步")) == null ? "" : __t) + "</span> ";
            } else if (stepIndex === 1) {
                __p += ' <span class="crm-btn crm-btn-primary" data-action="pre">' + ((__t = $t("上一步")) == null ? "" : __t) + "</span> ";
                if (type === "add") {
                    __p += ' <span class="crm-btn crm-btn-primary j-save" data-action="save">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> ";
                }
                __p += " ";
            }
            __p += " ";
            if (type !== "add") {
                __p += ' <span class="crm-btn crm-btn-primary j-save" data-action="save">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> ";
            }
            __p += ' <span class="crm-btn" data-action="cancel">' + ((__t = $t("取 消")) == null ? "" : __t) + "</span> </div>";
        }
        return __p;
    };
});
define("crm-setting/menumanage/template/main-html", [ "crm-setting/common/loading/loading" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var loadTpl = require("crm-setting/common/loading/loading");
            __p += ' <div class="full-header"> <div class="h-inner"></div> </div> <div class="full-content"> <div class="content-inner h-100"> <div class="first-view-wrap"> ' + ((__t = loadTpl({
                count: 4,
                width: 500
            })) == null ? "" : __t) + ' </div> <div class="h-100 overflow-hidden second-view-wrap crm-hide"></div> <div class="three-view-wrap crm-hide"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/menumanage/template/view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="headline"> <h2 class="title">' + ((__t = headline) == null ? "" : __t) + '</h2> <h2 class="subtitle">' + ((__t = subtitle) == null ? "" : __t) + '</h2> </div> <div class="view-wrap"></div> ';
            if (obj.showReset) {
                __p += ' <p class="reset-btn">' + ((__t = $t("恢复默认设置")) == null ? "" : __t) + "</p> ";
            }
        }
        return __p;
    };
});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var n,i=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),i.push.apply(i,n)),i}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/menumanage/update/update",["crm-modules/common/util","../template/main-html","../template/header-html","../views/firstview","paas-paasapp/sdk","paas-appcustomization/sdk","../views/secondview"],function(e,t,n){var a=e("crm-modules/common/util"),i=e("../template/main-html"),o=e("../template/header-html"),u=e("../views/firstview"),r=(e("paas-paasapp/sdk"),e("paas-appcustomization/sdk")),c=(e("../views/secondview"),Backbone.View.extend({initialize:function(e){var t=this,n=e.data,i=document.createElement("div");this.$el.append(i),r.menuTransfer().then(function(e){t._componentModule=new e.CRMMenuTransfer({propsData:FS.util.deepClone(n)}).$mount(i)})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},getValue:function(){return this._componentModule.getValue()},getValue2:function(){return this._componentModule.getValue2()},validate:function(){return!this.$(".right-sec .empty").length||(this.$(".fm-error").show(),!1)},destroy:function(){this._componentModule&&this._componentModule.$destroy&&this._componentModule.$destroy()}})),e=Backbone.View.extend({stepIndex:0,type:"add",data:{},options:{className:"crm-action-field-full crm-a-menumanage",tagName:"div"},initialize:function(e){var t=this;t.widgets=[],t.type=e.type,t.menuId=e._id,t.el.innerHTML=i(),$("body").append(t.$el),t.askmenuDropList().then(function(){"add"===t.type?t.render():t.getMenuById(e._id,function(){t.render()})})},events:{"click .crm-btn":"onOperate"},askmenuDropList:function(){var t=this;return FS.util.FHHApi({url:"/EM1HWebPage/dropList/getMenuDropList",data:{appId:"CRM"},success:function(e){e=e.Value.menuDropList;t.menuDropList=e}})},getMenuById:function(e,n){var i=this;FS.util.FHHApi({url:"/EM1HWebPage/tenantMenu/getMenuById",data:{menu_id:e},success:function(e){var e=e.Value,t=e.detailMenuItems,t=t.map(function(e){return Object.assign(e,{key:e.id,items:[],name:e.displayName})});i.data=Object.assign(e,{detailMenuItems:t}),n()}})},getViewData:function(e,t){var n=this;a.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/view",data:{menu_id:e},success:function(e){0==e.Result.StatusCode?(n.data=e.Value,t()):(a.alert(e.Result.FailureMessage),n.destroy())}},{errorAlertModel:1})},render:function(){var t,e,n,i=this.widgets,r=this.stepIndex,s=this.data;_.each(i,function(e){e.hide()}),this.$(".h-inner").html(o({stepIndex:r,type:this.type})),i[r]||("add"===this.type?0===r?this.widgets[r]=new u({el:this.$(".first-view-wrap"),data:{api_name:"menu_"+a.getUUId(5)}}):(e=[],0<this.menuDropList.length&&Fx.util.getGray("paasIsGrayMenuCustomize")&&(t=["crmIndex","recent","remind","todo"],e=this.menuDropList.filter(function(e){return t.includes(e.id)})),this.widgets[r]=new c({el:this.$(".second-view-wrap"),data:{menuValue:e,transferData:this.menuDropList}})):(e=s.objectData,n=s.scopeList,"copy"===this.type&&Object.assign(e,{api_name:"menu_"+a.getUUId(5),_id:null,name:e.name+"-"+$t("副本",{},"副本"),is_system:!1}),this.widgets[0]=new u({el:this.$(".first-view-wrap"),data:e,role:n,is_system:e.is_system}),this.widgets[1]=new c({el:this.$(".second-view-wrap"),data:{menuValue:s.detailMenuItems,menu_id:e._id,is_system:e.is_system,transferData:this.menuDropList,isShowMenuIcon:e.is_show_menu_icon,hiddenQuickCreate:e.hiddenQuickCreate}}))),i[r].show()},onOperate:function(e){var e=$(e.currentTarget).data("action"),t=this.stepIndex,n=this.widgets;if(!_.contains(["next","save"],e)||!this.$(".fm-error:visible").length&&n[t].validate())switch(e){case"pre":this.stepIndex--,this.render();break;case"next":this.stepIndex++,this.render();break;case"save":this.save();break;case"cancel":this.destroy()}},save:function(){var t=this,e=t.widgets;try{"copy"===t.type&&(n={msg:$t("菜单模板复制",{},"菜单模板复制"),url:location.href,eventId:"paas_crm_menu_copy"},FS)&&FS.log("paas_crm_menu_copy","click",n)}catch(e){}var n=e[1].getValue?e[1].getValue():{};a.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/create",data:{object_data:_objectSpread(_objectSpread({},e[0].getValue()),{},{is_show_menu_icon:n.isShowMenuIcon,hiddenQuickCreate:n.hiddenQuickCreate}),detail_menu_items:n.detailMenuItems,scopeList:e[0].getRoleValue()},success:function(e){0==e.Result.StatusCode&&(t.trigger("refresh"),t.destroy())}},{submitSelector:t.$(".j-save")})},destroy:function(){_.each(this.widgets,function(e){e.destroy&&e.destroy()}),this.remove()}});n.exports=e});
define("crm-setting/menumanage/views/firstview",["crm-modules/common/util","../template/view-html","../template/firstcontent-html","vue-selector-input"],function(e,t,r){var n=e("crm-modules/common/util"),a=e("../template/view-html"),i=e("../template/firstcontent-html"),o=e("vue-selector-input"),s=Backbone.Model.extend({defaults:{_id:null,name:"",api_name:"",description:""}}),l={role:4,outer:5,group:2,member:1,usergroup:3};r.exports=Backbone.View.extend({title:{headline:"",subtitle:""},initialize:function(e){this.el.innerHTML=a(this.title),this.model=new s(e.data),this.$(".view-wrap").html(i(this.model.toJSON()));var t=e.role;e.data.is_system&&(t=[{DataID:999999,DataType:2}]),this.renderSelector(t,e.is_system),this.$errors=this.$(".fm-error")},events:{"input .fm-ipt":"setText","blur [name=name]":"checkName","blur [name=api_name]":"checkApiname"},checkName:function(e){var t=this,e=e.target,e=$.trim(e.value);""===e?t.errorHandle(".name",$t("请输入菜单名称!")):n.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/validate_menu_name",data:{menu_id:t.model.get("_id"),name:e},success:function(e){e.Value.result||t.errorHandle(".name",$t("菜单名称重复"))}})},checkApiname:function(e){var t=this,e=e.target,e=$.trim(e.value);""===e?t.errorHandle(".api_name",$t("请输入API Name")):n.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/validate_menu_apiname",data:{apiname:e},success:function(e){e.Value.result||t.errorHandle(".api_name",$t("API Name重复"))}})},checkRole:function(t){Object.keys(t).some(function(e){return t[e].length})?this.errorHandle(".role"):this.errorHandle(".role",$t("请选择适用范围！"))},setText:function(e){e=e.target;this.errorHandle("."+e.name),this.model.set(e.name,$.trim(e.value))},formatData:function(e){var t={member:[],group:[],role:[]};return _.each(e,function(e){1==e.DataType?t.member.push(e.DataID):2==e.DataType?t.group.push(e.DataID):4==e.DataType&&t.role.push(e.DataID)}),t},renderSelector:function(e,t){var r=this;r.select=new o({el:this.$(".fm-role"),parentNode:this.$(".fm-role"),member:!0,group:{company:!0,preventIsolate:!0},disabled:t,role:!0,defaultSelectedItems:this.formatData(e),showClear:!1,label:$t("适用范围"),zIndex:9,errorText:""}),r.select.on("change",function(e){r.checkRole(e)})},errorHandle:function(e,t){e=this.$errors.filter(e);t?(e.show(),e.text(t)):e.hide()},validate:function(){var t,e=_.pluck(this.$(".is_required"),"value");return _.isEmpty(e[0])?(this.errorHandle(".name",$t("请输入菜单名称!")),!1):_.isEmpty(e[1])?(this.errorHandle(".name",$t("请输入API Name")),!1):(t=this.select.getValue(),!!Object.keys(t).some(function(e){return t[e].length})||(this.errorHandle(".role",$t("请选择适用范围！")),!1))},scopesObj2Arr:function(n){return Object.keys(n).reduce(function(e,t){return e.concat((e=n[t],r=l[t],e.map(function(e){return{DataID:e,DataType:r}})));var r},[])},getValue:function(){return this.model.toJSON()},getRoleValue:function(){return this.scopesObj2Arr(this.select.getValue())},hide:function(){this.$el.hide()},show:function(){this.$el.show()},destroy:function(){this.select&&(this.select.destroy(),this.select=null)}})});
define("crm-setting/menumanage/views/secondview",["crm-modules/common/util","../template/view-html","../sortmenu/sortmenu"],function(e,t,n){var i=e("crm-modules/common/util"),s=e("../template/view-html"),u=e("../sortmenu/sortmenu");n.exports=Backbone.View.extend({title:{headline:$t("设置菜单项"),subtitle:$t("您可以通过拖动来改变已选择菜单项的上下顺序")},initialize:function(t){var n=this;n.menu_id=t.menu_id,n.el.innerHTML=s(_.extend(n.title,{showReset:t.is_system})),n.getMenuList(function(e){e=t.data||e;n.renderSortMenu(e)})},events:{"click .reset-btn":"onReset"},getMenuList:function(t){i.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_admin/service/menu_item_list",success:function(e){0==e.Result.StatusCode&&t(_.map(e.Value,function(e){return{name:e.display_name+(e.is_active?"":' ($t("禁用"))'),value:e.api_name,type:e.type||"menu",searchWords:e.searchWords}}))}})},getRecoverList:function(t){i.FHHApi({url:"/EM1HWebPage/API/v1/object/crm_menu_init/service/get_init_menu_items",data:{menuId:this.menu_id},success:function(e){0==e.Result.StatusCode&&t(_.map(e.Value.detailMenuItems,function(e){return{name:e.displayName,value:e.referenceApiname,type:e.type||"menu",id:e.id||"",pid:e.pid||""}}))}})},onReset:function(){var t=this,e=i.confirm("<p>"+$t("crm.确定恢复默认设置说明")+"</p>",$t("提示"),function(){t.sortmenu&&(t.sortmenu.destroy(),t.sortmenu=null),t.getRecoverList(function(e){t.renderSortMenu(e)}),e.hide()})},validate:function(){return!this.$(".right-sec .empty").length||(this.$(".fm-error").show(),!1)},getValue:function(){return this.sortmenu.getValue()},renderSortMenu:function(e){this.sortmenu&&this.sortmenu.destroy(),this.sortmenu=new u({el:this.$(".view-wrap"),data:e})},hide:function(){this.$el.hide()},show:function(){this.$el.show()},destroy:function(){this.transfer&&(this.transfer.destroy(),this.transfer=null)}})});