define("crm-setting/roleset/fieldpermissions/fieldpermissions",["../template/edit-fieldpermissions-html","paas-object-modules/api"],function(e,t,i){var a=e("../template/edit-fieldpermissions-html"),d=e("paas-object-modules/api"),e=Backbone.View.extend({events:{"change .radio-box-body input":"radioHandle","change .radio-box-head input":"allSelectHandle"},initialize:function(){this.render()},render:function(){var e=this.options.datas;this.$el.html(a({fieldInfoList:e.fieldInfoList,objectId:e.objectId,disabled:e.disabled,readonly:e.readonly}))},radioHandle:function(e){var e=$(e.currentTarget),t=$(".radio-box-body",this.$el),i=$(".radio-box-head input",this.$el),a=e.data("permissiontype"),e=e.parents(".radio-box-body").data("fieldname");return"SalesOrderObj"==this.options.datas.descApiName&&_.contains(["product_amount","order_amount","discount"],e)&&_.each(t,function(e){var t=$(e).data("fieldname"),e=$(e).find("input");_.contains(["product_amount","order_amount","discount"],t)&&!e.eq(2-a).attr("disabled")&&e.eq(2-a).attr({checked:!0})}),e=_.every(t,function(e){return $(e).find("input:checked").data("permissiontype")===a}),i.removeAttr("checked"),e&&i.eq(2-a).attr({checked:!1}),!1},allSelectHandle:function(e){var t,i=$(e.currentTarget).data("permissiontype"),e=$(".radio-box-body",this.$el);_.each(e,function(e){(t=$(e).find("input")).eq(2-i).attr("disabled")||t.eq(2-i).attr({checked:!0})})},submit:function(i){var e=this,t=$(".radio-box-body",e.$el),a={};_.each(t,function(e){a[$(e).data("fieldname")]=$(e).find("input:checked").data("permissiontype")}),d.updateRoleObjectFieldPrivilege({descApiName:e.options.datas.descApiName,roleCode:e.options.datas.roleCode,fieldPermission:a},function(e,t){"success"===e?FS.util.remind(1,$t("操作成功")):FS.util.alert(t.FailureMessage),i()})},destroy:function(){this.remove()}});i.exports=e});
define("crm-setting/roleset/roleset",["./template/tpl-html","manage-modules/vues/objectauthority/index"],function(e,t,s){var i=e("./template/tpl-html"),n=e("manage-modules/vues/objectauthority/index").default,e=Backbone.View.extend({options:{className:"paaso-role-setting",tagName:"div",$wrap:null,data:null},events:{},initialize:function(){},render:function(){this.$el.html(i());var e=this;e.permissionsset=new n({el:e.$(".permissions-set"),api_name:e.model.getDescribeProperty("api_name")}),e.permissionsset.render()},destroy:function(){var e=this;e.undelegateEvents(),e.$el.remove(),e.permissionsset&&(e.permissionsset.destroy(),e.permissionsset=null)}});s.exports=e});
define("crm-setting/roleset/template/edit-fieldpermissions-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="rolemanage-dialog-info"> ' + ((__t = $t("说明如果角色对当前对象设置了新建或导入权限")) == null ? "" : __t) + '</div> <dl class="rolemanage-dialog-head"> <dt class="fm-lb"><em></em>' + ((__t = $t("全选")) == null ? "" : __t) + '</dt> <dd class="radio-box-head paaso-radio"> <label class="paaso-radio"> <input type="radio" name="paas-role-head" data-permissiontype="2" class="" ';
            if (disabled) {
                __p += " disabled ";
            }
            __p += '> <i></i> <span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + '</span> </label> <label class="paaso-radio"> <input type="radio" name="paas-role-head" data-permissiontype="1" class="" ';
            if (disabled) {
                __p += " disabled ";
            }
            __p += '> <i></i> <span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span> </label> <label class="paaso-radio"> <input type="radio" name="paas-role-head" data-permissiontype="0" class="" ';
            if (disabled) {
                __p += " disabled ";
            }
            __p += '> <i></i> <span class="radio-lb">' + ((__t = $t("不可见")) == null ? "" : __t) + '</span> </label> </dd> </dl> <div class="rolemanage-dialog-body"> ';
            _.each(fieldInfoList, function(item, index) {
                __p += " ";
                var fieldName = item.fieldName;
                __p += ' <dl> <dt class="fm-lb" title="' + ((__t = item.fieldCaption) == null ? "" : __t) + '"><em>';
                if (item.isRequire) {
                    __p += "*";
                }
                __p += "</em>" + __e(item.fieldCaption) + '</dt> <dd class="radio-box-body ';
                if ([ "TotalMoney", "Discount", "TradeMoney" ].indexOf(fieldName) > -1) {
                    __p += "saleitem";
                }
                __p += " ";
                if ([ "ProductPrice", "Discount", "Price" ].indexOf(fieldName) > -1) {
                    __p += "orderitem";
                }
                __p += '" data-fieldname=' + ((__t = fieldName) == null ? "" : __t) + '> <label class="paaso-radio"> <input type="radio" name="paas-role-item-' + ((__t = index) == null ? "" : __t) + '" ';
                if (item.status === 2) {
                    __p += ' checked="checked" ';
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfWrite) {
                    __p += " disabled ";
                }
                __p += ' data-permissiontype="2"> <i></i> <span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + '</span> </label> <label class="paaso-radio"> <input type="radio" name="paas-role-item-' + ((__t = index) == null ? "" : __t) + '" ';
                if (item.status === 1) {
                    __p += ' checked="checked" ';
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfReadOnly || item.isRequire && readonly) {
                    __p += " disabled ";
                }
                __p += ' data-permissiontype="1"></span> <i></i> <span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span> </label> <label class="paaso-radio"> <input type="radio" name="paas-role-item-' + ((__t = index) == null ? "" : __t) + '" ';
                if (item.status === 0) {
                    __p += ' checked="checked" ';
                }
                __p += " ";
                if (!item.isEditable || !item.isEditableOfInvisible || item.isRequire && readonly) {
                    __p += " disabled ";
                }
                __p += ' data-permissiontype="0"> <i></i> </span><span class="radio-lb">' + ((__t = $t("不可见")) == null ? "" : __t) + "</span> </label> </dd> </dl> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/roleset/template/rolelist-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (roles && roles.length > 0) {
                __p += " ";
                _.each(roles, function(item, index) {
                    __p += ' <ul class="default-rolelist"> <div class="item-group">' + ((__t = item.groupName) == null ? "" : __t) + "</div> ";
                    _.each(item.roleInfoList, function(ite, ind) {
                        __p += ' <li data-id="' + ((__t = ite.roleCode) == null ? "" : __t) + '" data-type="' + ((__t = ite.roleType) == null ? "" : __t) + '" data-rolename="' + ((__t = ite.roleName) == null ? "" : __t) + '" data-description="' + ((__t = ite.description) == null ? "" : __t) + '" title="' + ((__t = item.description) == null ? "" : __t) + '"> <div class="name" title="' + ((__t = ite.roleName) == null ? "" : __t) + '">' + ((__t = ite.roleName) == null ? "" : __t) + "</div> </li> ";
                    });
                    __p += " </ul> ";
                });
                __p += " ";
            }
        }
        return __p;
    };
});
define("crm-setting/roleset/template/roleset-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="checkbox-scroll crm-scroll"> <div style="max-width: 1030px;"> <div class="info-words"> <h3>' + ((__t = rolename) == null ? "" : __t) + "</h3> <p>" + ((__t = description || "--") == null ? "" : __t) + "</p> </div> ";
            _.each(list, function(item, index) {
                __p += ' <h3 class="checkbox-head"> ' + ((__t = descApiDisplayName) == null ? "" : __t) + " ";
                if (item.isHaveFieldPrivilege) {
                    __p += " ";
                    if (item.descApiDisplayName === "SalesOrderObj") {
                        __p += ' <span class="btn-to-set-field ';
                        if (!item.isHaveViewListPermiss) {
                            __p += "disabled-btn";
                        }
                        __p += '" data-objectid="SalesOrderObj" data-name="' + ((__t = $t("crm.销售订单")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("设置销售订单字段权限")) == null ? "" : __t) + '</span> <span class="btn-to-set-field ';
                        if (!item.isHaveViewListPermiss) {
                            __p += "disabled-btn";
                        }
                        __p += '" data-objectid="SalesOrderProductObj" data-name="' + ((__t = $t("crm.订单产品")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("设置订单产品字段权限")) == null ? "" : __t) + "</span> ";
                    } else if (item.descApiDisplayName === "ReturnedGoodsInvoiceObj") {
                        __p += ' <span class="btn-to-set-field ';
                        if (!item.isHaveViewListPermiss) {
                            __p += "disabled-btn";
                        }
                        __p += '" data-objectid="ReturnedGoodsInvoiceObj" data-name="' + ((__t = $t("crm.退货单")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("设置退货单字段权限")) == null ? "" : __t) + '</span> <span class="btn-to-set-field ';
                        if (!item.isHaveViewListPermiss) {
                            __p += "disabled-btn";
                        }
                        __p += '" data-objectid="ReturnedGoodsInvoiceProductObj" data-name="' + ((__t = $t("crm.退货单产品")) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("设置退货单产品字段权限")) == null ? "" : __t) + "</span> ";
                    } else if (item.descApiDisplayName === "Inventory") {
                        __p += " ";
                    } else {
                        __p += ' <span class="btn-to-set-field ';
                        if (!item.isHaveViewListPermiss) {
                            __p += "disabled-btn";
                        }
                        __p += '" data-objectid="' + ((__t = item.descApiDisplayName) == null ? "" : __t) + '" data-name="' + ((__t = descApiDisplayName) == null ? "" : __t) + '" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + ">" + ((__t = $t("设置字段权限")) == null ? "" : __t) + "</span> ";
                    }
                    __p += " ";
                } else {
                    __p += ' <span class="btn-to-set-field" data-functionnumber=' + ((__t = item.viewListFuncCode) == null ? "" : __t) + "></span> ";
                }
                __p += ' </h3> <dl class="mn-checkbox-box checkbox-group b-g-clear"> <dd class="checkbox-list"> ';
                var enabled = _.every(item.roleFunctionInfos, function(item) {
                    return item.enabled;
                });
                var isNoEditable = _.every(item.roleFunctionInfos, function(item) {
                    return item.isEditable === false;
                });
                __p += ' <label class="paaso-checkbox"> <input type="checkbox" class="j-check-all" ';
                if (!item.isEditable || isNoEditable) {
                    __p += " disabled ";
                }
                __p += " ";
                if (enabled) {
                    __p += " checked ";
                }
                __p += '> <i></i> <span class="check-lb">' + ((__t = $t("全选")) == null ? "" : __t) + "</span> </label> ";
                _.each(item.roleFunctionInfos, function(ite, ind) {
                    __p += ' <label class="paaso-checkbox"> <input type="checkbox" class="mn-checkbox-item ';
                    if (ite.isFiledReadOnlyRequired) {
                        __p += " readonly-item";
                    }
                    __p += " ";
                    if (ite.functionNumber === "PriceBookObj||Abolish") {
                        __p += " pricebook-abolish";
                    }
                    __p += " ";
                    if (ite.functionNumber === "PriceBookObj||Delete") {
                        __p += " pricebook-delete";
                    }
                    __p += " ";
                    if (item.viewListFuncCode === ite.functionNumber) {
                        __p += " look-item";
                    }
                    __p += " ";
                    if (ite.isClone || ite.functionNumber.indexOf("||Clone") != -1) {
                        __p += " clone-item";
                    }
                    __p += " ";
                    if (ite.isAdd) {
                        __p += " add-item";
                    }
                    __p += " ";
                    if (item.isBIObject) {
                        __p += " bi-item";
                    }
                    __p += '" ';
                    if (ite.enabled) {
                        __p += " checked ";
                    }
                    __p += " ";
                    if (!ite.isEditable) {
                        __p += " disabled ";
                    }
                    __p += " data-defaultstatus=" + ((__t = ite.defaultStatus) == null ? "" : __t) + " data-functionnumber=" + ((__t = ite.functionNumber) == null ? "" : __t) + ' > <i></i> <span class="check-lb">' + ((__t = ite.displayName) == null ? "" : __t) + "</span> </label> ";
                });
                __p += " </dd> </dl> ";
            });
            __p += ' </div> </div> <div class="checkbox-btn"> <div class="b-g-btn btn-to-save b-g-btn-disabled">' + ((__t = $t("保 存")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/roleset/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<!-- <div class="box-leftnav crm-scroll"> <div class="paaso-loading "></div> </div> <div class="box-centersection"> <div class="paaso-loading "></div> </div> --> <!-- <div class="crm-s-rolemanage"> --> <div class="permissions-set" style="width: 100%;height: 100%;"></div> <!-- </div> -->';
        }
        return __p;
    };
});