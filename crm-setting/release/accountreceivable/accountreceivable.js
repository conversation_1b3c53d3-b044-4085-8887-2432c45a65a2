function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var n,r=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)),r}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(n),!0).forEach(function(e){_defineProperty(t,e,n[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ownKeys(Object(n)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})}return t}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray(e)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray(e)}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function asyncGeneratorStep(e,t,n,r,o,i,a){try{var c=e[i](a),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,o)}function _asyncToGenerator(c){return function(){var e=this,a=arguments;return new Promise(function(t,n){var r=c.apply(e,a);function o(e){asyncGeneratorStep(r,t,n,o,i,"next",e)}function i(e){asyncGeneratorStep(r,t,n,o,i,"throw",e)}o(void 0)})}}define("crm-setting/accountreceivable/accountreceivable",["./template/tpl-html","./template/container-html","./template/pay-container-html","./comps/payForm","./comps/receiveForm","./comps/OpeningBalanceDateSetting","./comps/MatchStatus","./comps/AutoMatchStatus","./comps/AutoMatchRuleOrder","./comps/CreateArByObjects","./comps/AutoCreateReveivable","./comps/SettlementDetailMappingRule","./comps/AutoCreateAr"],function(e,t,n){var i=FS.crmUtil,r=e("./template/tpl-html"),o=e("./template/container-html"),a=e("./template/pay-container-html"),c=e("./comps/payForm"),s=e("./comps/receiveForm"),p=[{key:"opening_balance_date_setting",component:e("./comps/OpeningBalanceDateSetting"),configKeys:["opening_balance_date","opening_balance_force_check"]},{key:"is_open_match",component:e("./comps/MatchStatus")},{key:"is_open_auto_match",component:e("./comps/AutoMatchStatus"),configKeys:["is_open_auto_match"]},{key:"auto_match_rule",component:e("./comps/AutoMatchRuleOrder"),configKeys:["auto_match_rule"]},{key:"create_ar_by_objects",component:e("./comps/CreateArByObjects"),configKeys:["create_ar_by_objects","is_open_ar_quick_add"]},{key:"is_open_periodic_accounts_receivable",component:e("./comps/AutoCreateReveivable"),configKeys:["is_open_periodic_accounts_receivable","periodic_accounts_receivable_type"]},{key:"settlement_detail_mapping_rule",component:e("./comps/SettlementDetailMappingRule"),configKeys:["settlement_detail_mapping_rule"]},{key:"is_open_ar_quick_rule",component:e("./comps/AutoCreateAr"),configKeys:["is_open_ar_quick_rule"]}],e=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.el.innerHTML=r(),this.$container=this.$(".container"),this.PayForm=null,this.ReceiveForm=null,this.timer=null,this.SFAConfigValues={}},events:{"click .j-set-on":"_onSetAccountReceivable","click .j-set-matchway":"_onSetMatchWay","click .tab-item":"_onTabCh"},render:function(){this.getbillControl()},renderPayForm:function(e){var t=this,n=t.$(".content-container")[0],n=this.PayForm=new c({el:n,propsData:e});1==e.count&&n.$on("getStatus",function(e){t.getPayConfig(e.count)})},renderReceiveForm:function(e){var t=this,n=this.$(".receive-bill")[0],r=this.ReceiveForm=new s({el:n,propsData:e});r.$on("getRules",function(e){t.getBillSwitch().then(function(e){r.updateRules(e)})})},renderReceviceCustomSettingItem:function(){function s(e){return m.$el.find(".content-container .column-item."+e)[0]}function u(t,e,n){"is_open_auto_match"===t&&n&&(r="auto_match_rule",i=p.find(function(e){return e.key===r}).component,i=a(r,i),"1"===e)&&null!=i&&i.setDefaultValue();var r,o,i=m.recevieCustomWidgets[t];i||(o=null==(o=p.find(function(e){return e.configKeys&&e.configKeys.includes(t)}))?void 0:o.key,i=m.recevieCustomWidgets[o]),null!=(o=i)&&null!=(i=o.changeComplete)&&i.call(o,t,e,n)}var l=this,m=(this.recevieCustomWidgets={},this),a=function(e,t){var n,r,o,i,a,c=s(e);if(null!=(n=l.recevieCustomWidgets[e])&&null!=(r=n.destroy)&&r.call(n),c&&t)return null!=(o=t(c,e,l.SFAConfigValues))&&o.$on("change",(a=_asyncToGenerator(regeneratorRuntime.mark(function e(t,n){var r;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=!0,e.prev=1,e.next=4,m.setSFAServiceConfig([{key:t,value:n}]);case 4:e.next=9;break;case 6:e.prev=6,e.t0=e.catch(1),r=!1;case 9:u(t,n,r);case 10:case"end":return e.stop()}},e,null,[[1,6]])})),function(e,t){return a.apply(this,arguments)})),null!=o&&o.$on("update",(i=_asyncToGenerator(regeneratorRuntime.mark(function e(t){var n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,m.getSFAServiceConfig([t]);case 2:null!=o&&null!=(n=o.updateComplete)&&n.call(o,m.SFAConfigValues);case 3:case"end":return e.stop()}},e)})),function(e){return i.apply(this,arguments)})),m.recevieCustomWidgets[e]=o};p.forEach(function(e){var t=e.key;a(t,e.component)})},setSFAServiceConfig:function(n){var r=this;return new Promise(function(e,t){CRM.util.showLoading_tip(),CRM.util.setConfigValues(n).then(function(){n.forEach(function(e){var t=e.key;r.SFAConfigValues[t]=e.value}),CRM.util.remind(1,$t("设置成功")),CRM.util.hideLoading_tip(),e()},function(e){CRM.util.remind(3,e||$t("设置失败")),CRM.util.hideLoading_tip(),t()})})},getSFAServiceConfig:function(e){var t,n=this;return null!=(t=e)&&t.length||(e=p.reduce(function(e,t){t=t.configKeys;return e.push.apply(e,_toConsumableArray(void 0===t?[]:t)),e},[])),new Promise(function(t){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_values",data:{isAllConfig:!1,keys:e},success:function(e){0==e.Result.StatusCode&&e.Value.values.forEach(function(e){var t=e.key;n.SFAConfigValues[t]=e.value}),t(n.SFAConfigValues)}})})},getConfig:function(){var t=["accounts_receivable_status","accounts_receivable_match_way"];return new Promise(function(o,e){i.FHHApi({url:"/EM1HNCRM/API/v1/object/accounts_receivable/service/get_config",data:{keys:t},success:function(e){var t,n,r=e.Result;0==r.StatusCode?(e=e.Value.values||[],t=null,n=1,_.each(e,function(e){switch(e.key){case"accounts_receivable_status":t=e.value;break;case"accounts_receivable_match_way":n=e.value}}),o({account_receivable_status:t,match_way:n})):(o({account_receivable_status:null,match_way:1}),i.alert(r.FailureMessage||r.message||$t("设置失败请联系纷享客服")))}},{errorAlertModel:1})})},getbillControl:function(){var r=this;this.showLoading(),Promise.all([this.getConfig(),this.getBillConfig(),this.getBillSwitch(),this.getSFAServiceConfig()]).then(function(e){var t=0<e.length?e[0]:{},n=1<e.length?e[1]:[],e=2<e.length?e[2]:{};$(".mn-radio-box",r.$el).html(o(_objectSpread(_objectSpread({},t),{},{recevieCustomKeys:p}))),r.renderReceiveForm({objects:n,billOpen:e.billOpen,dataPrevList:e.objects}),r.renderReceviceCustomSettingItem(),r.hideLoading()})},getBillSwitch:function(){var n=this;return new Promise(function(r,e){var t=n;i.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsPeriodConfig/GetAccountsPeriodConfig",data:{},success:function(e){var t,n=e.Result;0==n.StatusCode?(t=e.Value.open||!1,e=e.Value.objects||[],r({billOpen:t,objects:e})):(r({billOpen:!1,objects:[]}),i.alert(n.FailureMessage||n.message||$t("查询失败")))},complete:function(){t.hideLoading()}},{errorAlertModel:1})})},getBillConfig:function(){var r=this;return new Promise(function(n,e){var t=r;i.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsPeriodConfig/GetObjectInitializationInformation",data:{},success:function(e){var t=e.Result;0==t.StatusCode?(e=e.Value.objects||[],n(e)):i.alert(t.FailureMessage||t.message||$t("查询失败"))},complete:function(){t.hideLoading()}},{errorAlertModel:1})})},getPayConfig:function(n){var r=this,e=this.timer;3==n&&null==e||i.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsPayable/GetStatus",data:{},success:function(e){var t=e.Result;0==t.StatusCode?(1==(e=e.Value.status)?r.timer=setTimeout(function(){3!=n&&i.alert($t("正在开启中，请耐心等待")),r.getPayConfig(3)},5e3):2==e?(r.timer=null,2!=n&&3!=n||CRM.util.remind(1,$t("开启成功"))):3==e&&(r.timer=null,CRM.util.remind(1,$t("crm.manage.accountreceivable.pay.open_fail_tip"))),$(".mn-radio-box",r.$el).html(a({account_pay_status:e,isOpen:2==e})),r.renderPayForm({account_pay_status:e,isOpen:2==e,count:n}),r.PayForm.updateStatus(e)):i.alert(t.FailureMessage||t.message||$t("查询失败"))},complete:function(){r.hideLoading()}},{errorAlertModel:1})},_onSetAccountReceivable:function(e){var t=this,n=i.confirm($t("应收管理一旦启用将无法停用确定要启用吗"),$t("提示"),function(){n.destroy(),t.enableAccountReceivable()})},showLoading:function(e){FS.crmUtil.waiting(e||"")},hideLoading:function(){FS.crmUtil.waiting(!1)},enableAccountReceivable:function(){var n,r=this,o=$t("设置失败请联系纷享客服");i.FHHApi({url:"/EM1HNCRM/API/v1/object/accounts_receivable/service/enable_accounts_receivable",data:{},success:function(e){var t=e.Result;0===t.StatusCode?0===(n=e.Value.enableStatus)||3===n?i.alert(e.Value.message||o):(i.remind(1,$t("设置成功")),r.getbillControl(),1===n&&i.alert($t("crm.系统检测客户太多系统预设在次日凌晨开启")),CRM.control.refreshAside()):i.alert(t.FailureMessage||o)}},{errorAlertModel:1})},_onSetMatchWay:function(e){var t=this,n=$(e.currentTarget),e=n.data("value");n.hasClass("mn-selected")||i.FHHApi({url:"/EM1HNCRM/API/v1/object/accounts_receivable/service/update_config",data:{key:"accounts_receivable_match_way",value:parseInt(e)},success:function(e){e=e.Result;0===e.StatusCode?($(".matchway-radio-item",t.$el).removeClass("mn-selected"),n.addClass("mn-selected"),i.remind(1,$t("设置成功"))):i.alert(e.FailureMessage||$t("设置失败请联系纷享客服"))}},{errorAlertModel:1})},_onTabCh:function(e){var t=$(e.currentTarget),t=(t.siblings().removeClass("active"),t.addClass("active"),e&&e.currentTarget&&e.currentTarget.dataset&&e.currentTarget.dataset.prop||null);"receive"==t?(this.showLoading(),this.getbillControl(),this.destroy("pay")):"pay"==t&&(this.showLoading(),this.getPayConfig(1))},destroyPayForm:function(){this.PayForm&&this.PayForm.$off&&this.PayForm.$off(),this.PayForm&&this.PayForm.$destroy&&this.PayForm.$destroy(),this.PayForm&&this.PayForm.$el.remove&&this.PayForm.$el.remove(),this.PayForm=null},destroy:function(e){"pay"==e?this.destroyPayForm():(this.destroyPayForm(),this.ReceiveForm&&this.ReceiveForm.$off&&this.ReceiveForm.$off(),this.ReceiveForm&&this.ReceiveForm.$destroy&&this.ReceiveForm.$destroy(),this.ReceiveForm&&this.ReceiveForm.$el.remove&&this.ReceiveForm.$el.remove(),this.ReceiveForm=null,this.timer=null),Object.values(this.recevieCustomWidgets||{}).forEach(function(e){return null==e?void 0:e.destroy()})}});n.exports=e});
define("crm-setting/accountreceivable/comps/AutoCreateAr",["./BackstageComp"],function(a,e,t){var i=a("./BackstageComp").BackstageSettingItem;t.exports=function(e,t,n){return FxUI.create({wrapper:e,template:'\n                <backstage-setting-item\n                    key="'.concat(t,'"\n                    :title="title"\n                    :describeList="describeList"\n                >\n                    <template slot="header-right">\n                        <fx-switch\n                            :value="value"\n                            size="small"\n                            true-label="0"\n                            false-label="1"\n                            @input="handleChange"\n                        />\n                    </template>\n                    <template slot="main">\n                        <content-main v-if="value" />\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:i,ContentMain:function(){return new Promise(function(t){a.async("vcrm/sdk",function(e){e.getComponent("backstage").then(function(e){return t(e.AutoQuickCreateAr)})})})}},data:function(){var e;return{value:"1"===(null!=(e=n[t])?e:"0"),title:$t("sfa.crm_setting.accountreceivable.is_open_ar_quick_rule_title"),describeList:[$t("sfa.crm_setting.accountreceivable.is_open_ar_quick_rule_desc1")]}},methods:{handleChange:function(e){this.$emit("change",t,e?"1":"0")},changeComplete:function(e,t,n){n&&(this.value="1"===t)}}})}});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _defineProperty(e,t,i){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var i=e[Symbol.toPrimitive];if(void 0===i)return("string"===t?String:Number)(e);i=i.call(e,t||"default");if("object"!=_typeof(i))return i;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/accountreceivable/comps/AutoCreateReveivable",["./BackstageComp"],function(e,t,i){var r=e("./BackstageComp").BackstageSettingItem;i.exports=function(e,n,t){var i="periodic_accounts_receivable_type",a="one",c="cycle";return FxUI.create({wrapper:e,template:'\n                <backstage-setting-item\n                    key="'.concat(n,'"\n                    :title="title"\n                    :describeList="describeList"\n                    :class="{\'closed\': !openStatus}"\n                    v-if="isShow"\n                >\n                    <template slot="header-right">\n                        <fx-switch\n                            :value="openStatus"\n                            size="small"\n                            @input="handleOpenStatusChange"\n                        />\n                    </template>\n                    <template slot="main" v-if="openStatus">\n                        <h2>{{typeTitle}}</h2>\n                        <fx-radio-group :value="type" @input="handleTypeInput" is-vertical>\n                            <fx-radio :label="opt.value" v-for="opt in typeOptions" :key="opt.value">\n                                <span>\n                                    {{opt.label}}\n                                    <fx-input v-if="opt.value === \'cycle\'" v-model="typeNumber" @blur="handleTypeNumberInput" size="mini" type="number" maxlength="3" decimal-places="0" style="width: 50px;" />\n                                </span>\n                                <p class="desc">{{opt.desc}}</p>\n                            </fx-radio>\n                        </fx-radio-group>\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:r},data:function(){var e=JSON.parse(t[i]);return{isShow:!CRM.util.isGrayScale("CRM_ACCOUNTS_RECEIVABLE_BIND_ORDER")&&"1"===CRM.util.getConfigStatusByKey("periodic_product"),title:$t("sfa.crm.setting_accountreceivable.is_open_periodic_accounts_receivable_title"),openStatus:"1"==t[n],describeList:[$t("sfa.crm.setting_accountreceivable.is_open_periodic_accounts_receivable_desc")],typeTitle:$t("sfa.crm.setting_accountreceivable.periodic_accounts_receivable_type_title"),type:e.hasOwnProperty(a)?a:c,typeNumber:e[c]||"1",typeOptions:[{label:$t("sfa.crm.setting_accountreceivable.periodic_accounts_receivable_type_option_once"),value:a,desc:$t("sfa.crm.setting_accountreceivable.periodic_accounts_receivable_type_option_once_desc")},{label:$t("sfa.crm.setting_accountreceivable.periodic_accounts_receivable_type_option_expired"),value:c,desc:$t("sfa.crm.setting_accountreceivable.periodic_accounts_receivable_type_option_expired_desc")}]}},watch:{},computed:{},methods:{handleTypeInput:function(e){e===c&&!this.validateTypeNumber(this.typeNumber)||(this.type=e,this.handleTypeChange())},validateTypeNumber:function(e){var t,i=!0;return e||"0"===e?e<0&&(t=$t("sfa.crm.setting_accountreceivable.periodic_accounts_receivable_type_validate_greaterzero"),i=!1):(t=$t("sfa.crm.setting_accountreceivable.periodic_accounts_receivable_type_validate_required"),i=!1),t&&this.$message.warning(t),i},handleTypeNumberInput:function(){this.type===c&&this.validateTypeNumber(this.typeNumber)&&this.handleTypeChange()},handleTypeChange:function(){var e=_defineProperty({},this.type,this.type===c?this.typeNumber:"0");this.$emit("change",i,JSON.stringify(e))},handleOpenStatusChange:function(e){this.$emit("change",n,e?"1":"0")},changeComplete:function(e,t,i){i&&e===n&&(this.openStatus="1"==t)}}})}});
function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(t,e){var a;if(t)return"string"==typeof t?_arrayLikeToArray(t,e):"Map"===(a="Object"===(a={}.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:a)||"Set"===a?Array.from(t):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(t,e):void 0}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var a=0,n=Array(e);a<e;a++)n[a]=t[a];return n}define("crm-setting/accountreceivable/comps/AutoMatchRuleOrder",["./BackstageComp"],function(t,e,a){var r=t("./BackstageComp").BackstageSettingItem,i=[{key:"match_related_account_method",label:$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_account"),isUdef:!1}];a.exports=function(t,e,a,n){return FxUI.create({wrapper:t,template:'\n                <backstage-setting-item\n                    key="'.concat(e,'"\n                    :title="title"\n                    :describeList="describeList"\n                    v-if="dependValue == \'1\'"\n                >\n                    <template slot="main">\n                        <fx-transfer\n                            :value="value"\n                            @input="handleInput"\n                            :data="options"\n                            target-order0="push"\n                            draggable\n                            filterable\n                            :titles="titles"\n                        >\n                            <span slot-scope="{ option }" class="transfer-item-label">\n                                <span class="transfer-item-label__text">{{option.label}}</span>\n                                <fx-tooltip v-if="option.tip" :content="option.tip">\n                                    <span class="fx-icon-jingshi"></span>\n                                </fx-tooltip>\n                            </span>\n                        </fx-transfer>\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:r},data:function(){var t;return{value:((t=a[e])?"string"==typeof t?JSON.parse(t):t:[]).map(function(t){return t.methodApiName}),dependValue:a.is_open_auto_match||"0",title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_title"),describeList:[{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_desc1"),showIndex:!1},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_desc2")},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_desc3")},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_desc4")}],options:[].concat(i),aplList:[],titles:[$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_transfer_left_title"),$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_transfer_right_title")]}},created:function(){this.fetchAPLOptions()},methods:{setDefaultValue:function(){var t;null!=(t=this.value)&&t.length||this.handleInput(["match_related_account_method"])},getSavedValue:function(){var r=this,t=this.value.map(function(e,t){var a=!i.find(function(t){return t.key===e}),n=(r.options.find(function(t){return t.key===e})||{}).label;return{methodApiName:e,methodName:n,isUdef:a,order:t+1}});return JSON.stringify(t)},handleInput:function(t){t.length?(this.value=t,this.$emit("change",e,this.getSavedValue())):this.$message({type:"warning",message:$t("sfa.crm.setting_accountreceivable.setting_auto_match_rule_order_rule_required")})},fetchAPLOptions:function(){var e=this;CRM.util.fetchAplList({name_space:["automatch_function"]}).then(function(t){e.aplList=t.map(function(t){return{label:t.function_name,key:t.api_name,isUdef:!0}}),(t=e.options).push.apply(t,_toConsumableArray(e.aplList))})}}})}});
define("crm-setting/accountreceivable/comps/AutoMatchStatus",["./BackstageComp"],function(t,e,a){var s=t("./BackstageComp").BackstageSettingItem;a.exports=function(t,e,a){return FxUI.create({wrapper:t,template:'\n                <backstage-setting-item\n                    key="'.concat(e,'"\n                    :title="title"\n                    :describeList="describeList"\n                >\n                    <template slot="header-right">\n                        <fx-switch\n                            :value="value"\n                            size="small"\n                            true-label="0"\n                            false-label="1"\n                            @input="handleChange"\n                        />\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:s},data:function(){var t;return{value:"1"===(null!=(t=a[e])?t:"0"),title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_title"),describeList:[{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc0"),showIndex:!1},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc1"),list:[{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc1.1")},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc1.2")}]},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc2"),list:[{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc2.1")},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc2.2")},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc2.3")}]},{title:$t("sfa.crm.setting_accountreceivable.setting_auto_match_status_desc3")}]}},methods:{handleChange:function(t){this.$emit("change",e,t?"1":"0")},changeComplete:function(t,e,a){a&&(this.value="1"===e)}}})}});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}define("crm-setting/accountreceivable/comps/BackstageComp",[],function(t,e,n){var i={props:["index","dataList","showAllIndex"],render:function(l){function d(t,c){var r=0,t=t.map(function(t,e){function n(){return(!(0<arguments.length&&void 0!==arguments[0])||arguments[0])&&u.showAllIndex?[c,++r].filter(function(t){return 0<t}).join("")+"".concat(c?"":"."):""}var i,s,a=null,o="";return"object"===_typeof(t)?(s=n(t.showIndex),t.list&&t.list.length&&(a=d(t.list,s)),o=Array.isArray(t.title)?(i=t.title.map(function(t,e){var n=t.text,t=t.styles;return l("span",{class:void 0===t?"":t},n)}),["".concat(s),i,a]):["".concat(s).concat(t.title),a]):"function"==typeof t?o=t(l):"string"==typeof t&&(o="".concat(n(),"         ").concat(t)),l("li",{key:e,style:{padding:"0 10px"}},o)});return l("ul",{},t)}var u=this;return d(this.dataList,this.index+1)}},s={template:'\n            <div class="backstage_setting_item">\n                <div class="backstage_setting_item__header">\n                    <slot name="header">\n                        <div class="backstage_setting_item__title">\n                            <h2>{{title}}</h2>\n                        </div>\n                        <div class="backstage_setting_item__headerright">\n                            <slot name="header-right" />\n                        </div>\n                    </slot>\n                </div>\n                <div class="backstage_setting_item__main">\n                    <div class="backstage_setting_item__desc" v-if="describeList && describeList.length">\n                        <desc-list :index="-1" :dataList="describeList" :showAllIndex="describeList.length > 1" />\n                    </div>\n                    <slot name="main" />\n                </div>\n            </div>\n        ',components:{DescList:i},props:{key:{type:String,required:!0},title:{type:String,default:""},describeList:{type:Array,default:function(){return[]}}}};n.exports={BackstageSettingItem:s,BackstageSettingDescList:i}});
function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var r;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,a,i,c,o=[],s=!0,l=!1;try{if(i=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;s=!1}else for(;!(s=(n=i.call(r)).done)&&(o.push(n.value),o.length!==t);s=!0);}catch(e){l=!0,a=e}finally{try{if(!s&&null!=r.return&&(c=r.return(),Object(c)!==c))return}finally{if(l)throw a}}return o}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/accountreceivable/comps/CreateArByObjects",["./BackstageComp"],function(e,t,r){var a=e("./BackstageComp").BackstageSettingItem;r.exports=function(e,r,n){return FxUI.create({wrapper:e,template:'\n                <backstage-setting-item\n                    key="'.concat(r,'"\n                    :title="title"\n                    :describeList="describeList"\n                    :class="{\'closed\': !openStatus}"\n                    v-if="isShow"\n                >\n                    <template slot="header-right">\n                        <span class="setting-item__desc">{{openStatusText}}</span>\n                        <fx-switch\n                            :value="openStatus"\n                            size="small"\n                            true-label="0"\n                            false-label="1"\n                            @input="handleOpenStatusChange"\n                        />\n                    </template>\n                    <template slot="main" v-if="openStatus">\n                        <fx-checkbox-group v-model="value" is-vertical>\n                            <fx-checkbox v-for="opt in checkOptions" :key="opt.value" :label="opt.value">{{opt.label}}</fx-checkbox>\n                        </fx-checkbox-group>\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:a},data:function(){var e=[{label:$t("sfa.crm.setting_accountreceivable.create_ar_by_objects_opt_from_order"),value:"SalesOrderObj"},{label:$t("sfa.crm.setting_accountreceivable.create_ar_by_objects_opt_from_contract"),value:"SaleContractObj",hidden:!CRM._cache.sale_contract},{label:$t("sfa.crm.setting_accountreceivable.create_ar_by_objects_opt_from_settlement"),value:"SettlementObj"}],t=Object.entries(n[r]?"string"==typeof n[r]?JSON.parse(n[r]):n[r]:{}).filter(function(e){e=_slicedToArray(e,2);e[0];return"1"===e[1]}).map(function(e){e=_slicedToArray(e,2),e=e[0];return e});return{isShow:!CRM.util.isGrayScale("CRM_ACCOUNTS_RECEIVABLE_BIND_ORDER"),value:t,checkOptions:e.filter(function(e){return!e.hidden}),title:$t("sfa.crm.setting_accountreceivable.account_receivable_status_title"),noTrigger:!1,openStatus:"1"==n.is_open_ar_quick_add}},watch:{value:{handler:function(e,t){null!=e&&e.length?(e=e.reduce(function(e,t){return e[t]="1",e},{}),this.noTrigger?this.noTrigger=!1:this.$emit("change",r,JSON.stringify(e))):(this.$message({message:$t("至少保留一条选项"),type:"warning"}),this.value=t,this.noTrigger=!0)},deep:!0}},computed:{describeList:function(){return this.openStatus?[$t("sfa.crm.setting_accountreceivable.create_ar_by_objects_desc1")]:null},openStatusText:function(){return this.openStatus?$t("sfa.crm.setting_accountreceivable.account_receivable_status_closetip"):$t("sfa.crm.setting_accountreceivable.account_receivable_status_opentip")}},methods:{handleOpenStatusChange:function(e){this.$emit("change","is_open_ar_quick_add",e?"1":"0")},changeComplete:function(e,t,r){r&&"is_open_ar_quick_add"===e&&(this.openStatus="1"==t,!this.openStatus||null!=(r=this.value)&&r.length||(this.value=["SalesOrderObj"]))}}})}});
define("crm-setting/accountreceivable/comps/MatchStatus",["./BackstageComp"],function(t,e,a){var n=t("./BackstageComp").BackstageSettingItem;a.exports=function(t,e,a){return FxUI.create({wrapper:t,template:'\n                <backstage-setting-item\n                    key="'.concat(e,'"\n                    :title="title"\n                    :describeList="describeList"\n                >\n                    <template slot="header-right">\n                        <fx-switch\n                            v-model="value"\n                            size="small"\n                            true-label="0"\n                            :disabled="disabled"\n                            false-label="1"\n                            @change="handleChange"\n                        />\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:n},data:function(){return{value:!1,title:$t("fmcg.crm.setting_accountreceivable.setting_match_status_title"),disabled:!1,describeList:[{title:$t("fmcg.crm.setting_accountreceivable.setting_match_status_desc")}]}},methods:{handleChange:function(t){var e=this;CRM.util.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsReceivable/EnableAutoMatch",data:{},success:function(t){0==t.Result.StatusCode?(CRM.util.remind(1,$t("设置成功")),e.disabled=!0):(CRM.util.remind(0,$t("设置失败")),e.disabled=e.value=!1),e.QueryAutoMatchStatus()}})},QueryAutoMatchStatus:function(){var e=this;CRM.util.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsReceivable/QueryAutoMatchStatus",data:{},success:function(t){0==t.Result.StatusCode&&(t=t.Value.status,e.disabled=e.value=1==(t=void 0===t?0:t)||2==t)}},{errorAlertModel:1})}},mounted:function(){this.QueryAutoMatchStatus()}})}});
define("crm-setting/accountreceivable/comps/OpeningBalanceDateSetting",["./BackstageComp"],function(e,t,n){var a=e("./BackstageComp").BackstageSettingItem,i=$t("crm.setting.accountreceivable.setting_opening_balance_date_title"),e=$t("crm.setting.accountreceivable.setting_opening_balance_date_strong"),c=$t("crm.setting.accountreceivable.setting_opening_balance_date_strong_desc"),l={template:'\n            <div class="setting_child_item setting_date">\n                <div class="setting_date_form">\n                <p class="setting_child_item__title">'.concat(i,'</p>\n                <fx-date-picker\n                    v-model="dValue"\n                    type="date"\n                    :size="size"\n                    :disabled="!editable"\n                    placeholder="').concat($t("选择日期"),'"\n                    value-format="timestamp"\n                />\n                </div>\n                <div class="actions">\n                    <template v-if="editable">\n                        <fx-button :size="size" type="primary" @click="handleClick(\'submit\')">').concat($t("提交"),'</fx-button>\n                        <fx-button :size="size" @click="handleClick(\'cancel\')">').concat($t("取消"),'</fx-button>\n                    </template>\n                    <fx-button v-else :size="size" @click="handleClick(\'edit\')">').concat($t("编辑"),"</fx-button>\n                </div>\n            </div>\n        "),props:{value:{type:String}},data:function(){return{dValue:"",size:"small",editable:!1}},watch:{value:{handler:function(e){this.dValue="0"===e?null:e},immediate:!0}},methods:{handleClick:function(e){this.editable=!this.editable,"submit"===e&&this.$emit("change",this.dValue||"0")}}},s={template:'\n            <div class="setting_child_item setting_strong_validation">\n                <fx-checkbox\n                    :value="value"\n                    :disabled="disabled"\n                    @input="(val) => $emit(\'change\', val)"\n                    true-label="1"\n                    false-label="0"\n                >'.concat(e,'</fx-checkbox>\n                <p class="desc">').concat(c,"</div>\n            </div>\n        "),props:{value:{type:Boolean},disabled:{type:Boolean,default:!1}},methods:{}};n.exports=function(e,t,n){if(e)return FxUI.create({wrapper:e,template:'\n                <backstage-setting-item\n                    key="setting_opening_balance_date"\n                    title="'.concat(i,'"\n                    :describeList="describeList"\n                >\n                    <template slot="main">\n                        <setting-date\n                            :value="value.opening_balance_date"\n                            @change="handleDateChange"\n                        />\n                        <setting-strong-validation\n                            :disabled="value.opening_balance_date === \'0\'"\n                            :value="value.opening_balance_force_check"\n                            @change="(val) => setConfig(\'opening_balance_force_check\', val)"\n                        />\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:a,SettingDate:l,SettingStrongValidation:s},data:function(){var e;return{value:{opening_balance_date:null!=(e=n.opening_balance_date)?e:"",opening_balance_force_check:null!=(e=n.opening_balance_force_check)?e:"0"},describeList:[{title:$t("crm.setting.accountreceivable.setting_opening_balance_date_desc1")},{title:$t("crm.setting.accountreceivable.setting_opening_balance_date_desc2")},{title:$t("crm.setting.accountreceivable.setting_opening_balance_date_desc3")},{title:$t("crm.setting.accountreceivable.setting_opening_balance_date_desc4"),list:[{title:$t("crm.setting.accountreceivable.setting_opening_balance_date_desc4-1")},{title:$t("crm.setting.accountreceivable.setting_opening_balance_date_desc4-2")}]}]}},created:function(){},methods:{handleDateChange:function(e){"0"===e&&"1"===this.value.opening_balance_force_check&&this.setConfig("opening_balance_force_check",0),this.setConfig("opening_balance_date",e)},setConfig:function(n,e){var a=this;CRM.util.showLoading_tip(),CRM.util.setConfigValues([{key:n,value:e}]).then(function(){CRM.util.remind(1,$t("设置成功")),a.$set(a.value,n,e),CRM.util.hideLoading_tip()},function(e){var t=a.value[n];CRM.util.remind(3,e||$t("设置失败")),a.$set(a.value,n,""),a.$nextTick(function(){a.$set(a.value,n,t)}),CRM.util.hideLoading_tip()})}}})}});
define("crm-setting/accountreceivable/comps/payForm",[],function(t,e,a){var n=Vue.extend({template:'\n        <div class="column-item">\n            <label class="label">{{$t("crm.manage.accountreceivable.pay.tab_pay")}}</label>\n            <fx-switch class="pay-switch" size="mini" v-model="isOpenPay" :disabled="isDisable" :before-change="openPayChange"></fx-switch>\n            <span class="pay-tip" v-if="isOpenPay">{{$t(\'开启后不可关闭\')}}</span>\n        </div>\n\t',components:{},props:{isOpen:{type:Boolean,default:!1},account_pay_status:{type:Boolean,default:!1}},watch:{account_pay_status:function(t){this.status=t,this.isDisable=1==t||2==t},isOpen:function(t){this.setSwitch(t)}},data:function(){return{isOpenPay:!1,status:!1,isDisable:!1}},created:function(){},methods:{showLoading:function(t){FS.crmUtil.waiting(t||"")},hideLoading:function(){FS.crmUtil.waiting(!1)},setSwitch:function(t){this.isOpenPay=t},updateStatus:function(t){this.status=t,this.isDisable=1==t||2==t,2==t&&this.setSwitch(!0)},openPayChange:function(){var n=this;return new Promise(function(t,e){var a=CRM.util.confirm($t("crm.manage.accountreceivable.pay.pay_opentip"),$t("提示"),function(){a.destroy(),n.handleOpen()},{btnLabel:{confirm:$t("crm.form_save_btn")}})})},handleOpen:function(){this.showLoading();var a=$t("crm.manage.accountreceivable.pay.open_fail_tip"),n=this;CRM.util.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsPayable/Enable",data:{},success:function(t){var e=t.Result;0===e.StatusCode?t.Value.enable?n.$emit("getStatus",{count:2}):CRM.util.alert(t.Value.message||a):CRM.util.alert(e.FailureMessage||a),n.hideLoading()},complete:function(){n.hideLoading()}},{errorAlertModel:2})}},beforeDestroy:function(){},destroyed:function(){}});a.exports=n});
define("crm-setting/accountreceivable/comps/receiveForm",[],function(e,t,i){var a=Vue.extend({template:'\n        <div>\n            <div class="column-item">\n                <label class="label">{{$t("crm.manage.accountreceivable.receive.bill_open")}}：</label>\n                <fx-switch class="pay-switch" size="mini" v-model="isOpenReceive" :disabled="isDisable" @change="openPayChange"></fx-switch>\n            </div>\n            <div class="billcontrol-content" v-show="isOpenReceive">\n                <div class="billcontrol-tiptext">\n                    <p>{{$t(\'crm.manage.accountreceivable.receive.desc1\')}}</p>\n                    <p>{{$t(\'crm.manage.accountreceivable.receive.desc2\')}}</p>\n                </div>\n                <div class="bill-rules-wrap">\n                    <div class="bill-rules">\n                        <div class="rule-item rule-title">{{$t(\'object\')}}</div>\n                        <div class="rule-item rule-title">{{$t(\'对象类型\')}}</div>\n                        <div class="rule-item rule-title">{{$t(\'控制强度\')}}</div>\n                    </div>\n                    <div class="" v-if="dataList.length > 0">\n                        <div class="bill-rules receive-selitem" v-for="(item,index) in dataList" :key="index">\n                            <fx-select\n                                v-model="item.apiName"\n                                :options="objList"\n                                :no-data-text="$t(\'el.cascader.noData\')"\n                                @change="onObjChange($event,index)"\n                                size="small"\n                                class="rule-item"\n                                option-value-key="apiName"\n                            ></fx-select>\n                            <fx-select\n                                v-model="item.recordType.apiName"\n                                :options="item.recordTypes"\n                                :no-data-text="$t(\'el.cascader.noData\')"\n                                @visible-change="typeVisible($event,index)"\n                                size="small"\n                                class="rule-item"\n                                option-value-key="apiName"\n                            ></fx-select>\n                            <fx-select\n                                v-model="item.block"\n                                :options="controlStatusList"\n                                :no-data-text="$t(\'el.cascader.noData\')"\n                                size="small"\n                                class="rule-item"\n                            ></fx-select>\n                            <span class="fx-icon-process-delete bill-del-icon" :class="{\'disable\':dataList.length == 1}" @click="delRule(index)"></span>\n                        </div>\n                    </div>\n                    <div class="add-rule">\n                        <span class="add-btn" @click="addRule">+{{$t(\'添加一行\')}}</span>\n                    </div>\n                </div>\n                <div class="save-rule"><span @click="saveRule(true)">{{$t(\'crm.manage.accountreceivable.receive.save_gule_btn\')}}</span></div>\n            </div>\n        </div>\n\t',components:{},props:{isOpen:{type:Boolean,default:!1},account_pay_status:{type:Boolean,default:!1},objects:{type:Array,default:[]},billOpen:{type:Boolean,default:!1},dataPrevList:{type:Array,default:[]}},watch:{},data:function(){return{objList:[],typeList:[],controlStatusList:[],dataList:[],submitList:[],isOpenReceive:!1,status:!1,isDisable:!1}},created:function(){this.getInit()},methods:{getInit:function(){var e=this.objects,t=this.dataPrevList,i=this.billOpen,i=void 0!==i&&i;i&&this.setInit(void 0===e?[]:e,void 0===t?[]:t),this.isOpenReceive=i},setInit:function(t,e){var i=t.findIndex(function(e){return"SalesOrderObj"==e.apiName}),a=t.findIndex(function(e){return"DeliveryNoteObj"==e.apiName}),n=t.map(function(e){return{label:e.label,apiName:e.apiName}}),s=[{label:$t("阻断"),value:!0},{label:$t("不阻断"),value:!1}];0<e.length?this.dataList=e.map(function(e){return"SalesOrderObj"==e.apiName?e.recordTypes=-1<i?t[i].recordTypes:[]:"DeliveryNoteObj"==e.apiName&&(e.recordTypes=-1<a?t[a].recordTypes:[]),e}):-1<i&&(this.dataList=[{label:t[i].label,apiName:t[i].apiName,recordTypes:t[i].recordTypes,recordType:{apiName:"default__c"},block:!0}]),this.objList=n,this.controlStatusList=s},showLoading:function(e){FS.crmUtil.waiting(e||"")},hideLoading:function(){FS.crmUtil.waiting(!1)},setSwitch:function(e){this.isOpenReceive=e},addRule:function(){var e=this.dataList;(void 0===e?[]:e).push({label:"",apiName:"",recordTypes:[],recordType:{apiName:""},block:""})},delRule:function(e){var t=this.dataList,t=void 0===t?[]:t;1<t.length&&(t.splice(e,1),this.dataList=t)},openPayChange:function(e){this.setSwitch(e),e?this.$emit("getRules"):this.saveRule(!1)},onObjChange:function(t,e){var i=this.objects,i=void 0===i?[]:i,a=i.findIndex(function(e){return e.apiName==t}),n=this.dataList,n=void 0===n?[]:n;n[e].recordTypes=i[a].recordTypes,n[e].recordType={apiName:""},n[e].label=i[a].label,this.dataList=n},typeVisible:function(e,t){var i=this.dataList,i=(void 0===i?[]:i)[t].apiName||"";e&&""==i&&CRM.util.alert($t("请先选择对象"))},handleOpen:function(){this.showLoading();var i=$t("crm.manage.accountreceivable.pay.open_fail_tip"),a=this;CRM.util.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsPayable/Enable",data:{},success:function(e){var t=e.Result;0===t.StatusCode?e.Value.enable?a.$emit("getStatus",{count:2}):CRM.util.alert(e.Value.message||i):CRM.util.alert(t.FailureMessage||i),a.hideLoading()},complete:function(){a.hideLoading()}},{errorAlertModel:2})},updateRules:function(e){var e=e.objects||[],t=this.objects;this.setInit(void 0===t?[]:t,e)},billValidate:function(){var e=this.dataList,e=void 0===e?[]:e,i={},t=e.every(function(e){var t=e.apiName+"-"+e.recordType.apiName;return i[t]||(i[t]=e.block),e.apiName&&e.recordType&&e.recordType.apiName&&""!==e.block}),a=Object.keys(i).length;return t?a==e.length||(CRM.util.alert($t("CRM.accountreceivable.validate_text.repeat_tip")),!1):(CRM.util.alert($t("选项不能为空")),!1)},saveRule:function(e){(!e||this.billValidate())&&this.handleSave(e)},handleSave:function(e){var i="",i=e?$t("保存成功!"):$t("关闭成功"),e=this.dataList,e=void 0===e?[]:e,t=this.isOpenReceive,e=e.map(function(e){return{apiName:e.apiName,recordType:e.recordType,block:e.block}}),a=(this.showLoading(),this);CRM.util.FHHApi({url:"/EM1HNCRM/FMCG/API/DMS/AccountsPeriodConfig/SaveAccountsPeriodConfig",data:{objects:e,open:t},success:function(e){var t=e.Result;0===t.StatusCode?CRM.util.alert(e.Value.message||i):CRM.util.alert(t.FailureMessage||$t("操作失败！")),a.hideLoading()},complete:function(){a.hideLoading()}},{errorAlertModel:2})}}});i.exports=a});
define("crm-setting/accountreceivable/comps/SettlementDetailMappingRule",["./BackstageComp"],function(i,e,t){var a=i("./BackstageComp").BackstageSettingItem,l=function(e){return CRM.util.ajax_base("/EM1HNCRM/API/v1/object/accounts_receivable/service/create_or_update_settlementdetail_rule",e,null)},s=function(e){var t=e.ruleApiName;return CRM.util.ajax_base("/EM1HNCRM/API/v1/object/accounts_receivable/service/delete_settlementdetail_rule",{describe_api_name:e.objectApiName,rule_api_name:t},null)};t.exports=function(e,n,t){return FxUI.create({wrapper:e,template:'\n                <backstage-setting-item\n                    key="'.concat(n,'"\n                    :title="title"\n                    :describeList="describeList"\n                >\n                    <template slot="main">\n                        <rule-comp\n                            :rules="selects"\n                            :targetApiName="targetApiName"\n                            :dialogFormVisible.sync="dialogFormVisible"\n                            @change="handleItemChange"\n                            @del="handleDelItem"\n                            @confirm="handleConfirm"\n                            @add="handleAddLine"\n                        />\n                    </template>\n                </backstage-setting-item>\n            '),components:{BackstageSettingItem:a,RuleComp:function(){return new Promise(function(t){i.async("vcrm/sdk",function(e){e.getComponent("backstage").then(function(e){return t(e.MultiSourceMappingRuleComp)})})})}},data:function(){return{selects:this.parseValue(t[n]),title:$t("sfa.crm.setting_accountreceivable.setting_settlement_mapping_rule_title"),describeList:[],targetApiName:"SettlementDetailObj",dialogFormVisible:!1}},methods:{handleAddLine:function(){this.selects.push({objectApiName:"",fieldApiName:"",ruleApiName:""})},handleConfirm:function(e){var t=this;l(e).then(function(){t.dialogFormVisible=!1,t.$emit("update",n)})},handleItemChange:function(e,t){this.selects.splice(t,1,e)},handleDelItem:function(e,t){var n=this;e.objectApiName&&e.ruleApiName?s(e).then(function(e){n.selects.splice(t,1)}):this.selects.splice(t,1)},updateComplete:function(e){this.selects=this.parseValue(e[n])},parseValue:function(e){return"string"==typeof e?JSON.parse(e):e}}})}});
define("crm-setting/accountreceivable/template/container-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>1." + ((__t = $t("sfa.crm.setting_accountreceivable.account_receivable_status_desc1")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("sfa.crm.setting_accountreceivable.account_receivable_status_desc2")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("sfa.crm.setting_accountreceivable.account_receivable_status_desc3")) == null ? "" : __t) + '</li> </ul> </div> <div class="content-container"> <div class="render-wrap"> ';
            if (account_receivable_status === 0 || account_receivable_status === 3) {
                __p += ' <p class="column-item"> <label class="label">' + ((__t = $t("应收管理")) == null ? "" : __t) + '</label> <span class="switch-sec j-set-on"></span> </p> ';
            } else if (account_receivable_status === 2) {
                __p += ' <div class="column-item"> <label class="label">' + ((__t = $t("应收管理")) == null ? "" : __t) + "：</label>" + ((__t = $t("已开启")) == null ? "" : __t) + "&nbsp;&nbsp;" + ((__t = $t("已完成初始化")) == null ? "" : __t) + ' </div> <p class="column-item"> <label class="label">' + ((__t = $t("核销方式")) == null ? "" : __t) + '：</label> <span> <span class="matchway-radio-item j-set-matchway ' + ((__t = match_way == "1" ? "mn-selected" : "") == null ? "" : __t) + '" data-value="1"></span> <span class="control-matchway">' + ((__t = $t("按应收单核销")) == null ? "" : __t) + '</span> </span> <span> <span class="matchway-radio-item j-set-matchway ' + ((__t = match_way == "2" ? "mn-selected" : "") == null ? "" : __t) + '" data-value="2"></span> <span class="control-matchway">' + ((__t = $t("按应收单明细核销")) == null ? "" : __t) + '</span> </span> </p> <div class="receive-bill"></div> ';
                for (var i = 0; i < recevieCustomKeys.length; i++) {
                    __p += ' <div class="column-item ' + ((__t = recevieCustomKeys[i].key) == null ? "" : __t) + '"></div> ';
                }
                __p += " ";
            } else if (account_receivable_status === 1) {
                __p += ' <p class="column-item"><label>' + ((__t = $t("应收管理")) == null ? "" : __t) + "</label>" + ((__t = $t("开启中")) == null ? "" : __t) + "</p> ";
            }
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/accountreceivable/template/pay-container-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>" + ((__t = $t("crm.manage.accountreceivable.pay.desc1_new")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2.crm.manage.accountreceivable.pay.desc2")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3.crm.manage.accountreceivable.pay.desc3")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("4.crm.manage.accountreceivable.pay.desc4")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("5.crm.manage.accountreceivable.pay.desc5")) == null ? "" : __t) + '</li> </ul> </div> <div class="content-container"> <div class="render-wrap pay-render-wrap"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/accountreceivable/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit tpl-wrap"> <h2> <span class="tit-txt">' + ((__t = $t("crm.manage.accountreceivable.receive_pay.title")) == null ? "" : __t) + '</span> </h2> <div class="title-tab"> <div class="tab-item active" data-prop="receive">' + ((__t = $t("应收管理")) == null ? "" : __t) + '</div> <div class="tab-item" data-prop="pay">' + ((__t = $t("crm.manage.accountreceivable.pay.tab_pay")) == null ? "" : __t) + '</div> </div> </div> <div class="crm-module-con crm-scroll"> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> </div> </div>';
        }
        return __p;
    };
});