define("crm-setting/fastsale/area-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("1.crm.manage.fastsale.area.desc1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2.crm.manage.fastsale.area.desc2")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3.crm.manage.fastsale.area.desc3")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("crm.manage.fastsale.area")) == null ? "" : __t) + '：</label> <div class="spu-switch switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="important-info" >' + ((__t = $t("crm.manage.fastsale.area.importantInfo")) == null ? "" : __t) + "</div> ";
            if (start) {
                __p += ' <div class="open-area-wrap"> <div class="area-form-wrap"> </div> </div> ';
            }
        }
        return __p;
    };
});
define("crm-setting/fastsale/bill-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("1.crm.manage.fastsale.bill.desc1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2.crm.manage.fastsale.bill.desc2")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3.crm.manage.fastsale.bill.desc3")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("4.crm.manage.fastsale.bill.desc4")) == null ? "" : __t) + '</li> </ul> </div> <div class="open-bill-wrap"></div>';
        }
        return __p;
    };
});
function _createForOfIteratorHelper(e,t){var a,r,n,i,l="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(l)return n=!(r=!0),{s:function(){l=l.call(e)},n:function(){var e=l.next();return r=e.done,e},e:function(e){n=!0,a=e},f:function(){try{r||null==l.return||l.return()}finally{if(n)throw a}}};if(Array.isArray(e)||(l=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length)return l&&(e=l),i=0,{s:t=function(){},n:function(){return i>=e.length?{done:!0}:{done:!1,value:e[i++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var a;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(a="Object"===(a={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:a)||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,r=Array(t);a<t;a++)r[a]=e[a];return r}define("crm-setting/fastsale/comps/AreaDepartSetDialog",[],function(e,t,a){var r=Vue.extend({template:'\n        <fx-dialog\n            :title="$t(\'fmcg.checkin.areaManage.depart.set.label\')"\n            :visible="dialogVisible"\n            :append-to-body="true"\n            :close-on-click-modal="false"\n            :close-on-press-escape="false"\n            :before-close="handleClose"\n            width="700px"\n            custom-class="fx-dialog-area-depart-dialog"\n        >\n        <div>\n            <div class="depart-desc-wrap">\n                <h3>{{$t(\'说明\')}}</h3>\n                <ul>\n                    <li v-for="(desc,i) in desc_arr" :key="i">{{i+1}}.{{desc}}</li>\n                </ul>\n            </div>\n\n            <div class="form-wrap">\n                <div class="form-item" v-if="!showAllLevel">\n                    <span class="form-label">{{thirdDepart.label}}</span>\n                    <fx-select\n                        v-model="thirdDepart.departLevel"\n                        size="small"\n                        @change="thirdDepartChange"\n                        :placeholder="$t(\'请选择\')"\n                        :options="thirdDepart.options"\n                        :el-style="{ width: \'150px\' }"\n                    ></fx-select>\n                    <fx-input\n                        maxlength="10"\n                        size="small"\n                        :el-style="{ display: \'inline-block\', width: \'130px\' }"\n                        v-model="thirdDepart.departName"\n                        :placeholder="$t(\'请输入层级名称\')"\n                    />  \n                </div>\n                <div v-else>\n                    <div class="form-item tree-item"  v-for="(item,i) in allDepart" :key="i">\n                        <span class="form-label">{{item.label}}</span>\n                        <fx-select\n                            v-model="item.departLevel"\n                            size="small"\n                            :placeholder="$t(\'请选择\')"\n                            :options="item.options"\n                            :disabled="item.disabled"\n                            @change="i==2 && thirdDepartChange()"\n                            :el-style="{ width: \'150px\' }"\n                        ></fx-select>\n                        <fx-input\n                            maxlength="10"\n                            size="small"\n                            :el-style="{ display: \'inline-block\', width: \'130px\' }"\n                            v-model="item.departName"\n                            :placeholder="$t(\'请输入层级名称\')"\n                        />  \n                    </div>\n                </div>\n            </div>\n            <div v-if="showError" class="err-msg">{{errMsg}}</div>\n        </div>\n        <div slot="footer" class="dialog-footer">\n            <fx-button type="primary" size="small" @click="submit">{{\n                $t("保 存")\n            }}</fx-button>\n            <fx-button @click="handleClose" size="small">{{\n                $t("取 消")\n            }}</fx-button>\n        </div>\n    </fx-dialog>\n\t',props:{openType:{type:String,default:"edit"},departLevelList:{type:Array,default:[]},DepartConfigList:{type:Array,default:[]}},data:function(){return{dialogVisible:!1,showAllLevel:!1,showError:!1,errMsg:"",thirdDepart:{},allDepart:[],desc_arr:[$t("fmcg.checkin.areaManage.depart.set.desc1"),$t("fmcg.checkin.areaManage.depart.set.desc2"),$t("fmcg.checkin.areaManage.depart.set.desc3")]}},computed:{},watch:{dialogVisible:function(e){var t,a;e&&(e=this.DepartConfigList,this.showAllLevel=!!e.length,e=this.getDepart(0),t=this.getDepart(1),a=this.getDepart(2),this.thirdDepart=a,this.allDepart=[e,t,a])}},methods:{show:function(){this.dialogVisible=!0},hide:function(){this.dialogVisible=!1},thirdDepartChange:function(e){var t=this.thirdDepart,a=this.allDepart;if(e=e||t.departLevel){this.showAllLevel=!0;for(var r=0;r<a.length;r++)0==r&&(a[r].departLevel=e-2),1==r&&(a[r].departLevel=e-1)}},getDepart:function(a){var e=this.departLevelList,t=this.DepartConfigList,r=this.openType,n=[$t("crm.manage.fastsale.area.departLevel.firstLevel"),$t("crm.manage.fastsale.area.departLevel.secondLevel"),$t("crm.manage.fastsale.area.departLevel.thirdLevel")],i={},e=_.sortBy(e).filter(function(e,t){return a<=t});return i.options=e.map(function(e){return{label:$t("第num级部门",{num:e}),value:e}}),i.label=n[a],i.departLevel=t[a]?t[a].departLevel:"",i.departName=t[a]?t[a].departName:"",i.disabled=!![0,1].includes(a)||"add"!==r,i},valid:function(){var e,t=!1,a="",r=this.allDepart,n=_createForOfIteratorHelper(r);try{for(n.s();!(e=n.n()).done;){var i=e.value;if(!i.departName||!i.departName.trim()){t=!0,a=$t("fmcg.checkin.areaManage.depart.set.tip1");break}if(/[\\\\`~\!@#$%\^\&\*\(\)\+=\|\{\}':;',\[\].<>\/\?！￥%……&*（）——+]/i.test(i.departName)){t=!0,a=$t("fmcg.checkin.areaManage.depart.set.tip2");break}}}catch(e){n.e(e)}finally{n.f()}return t||_.uniq(r.map(function(e){return e.departName.trim()})).length!==r.length&&(t=!0,a=$t("fmcg.checkin.areaManage.depart.set.tip3")),this.showError=t,this.errMsg=a,t},submit:function(){var t,a,r=this;this.valid()||(t=this.allDepart.map(function(e){return{departLevel:e.departLevel,departName:e.departName}}),"add"===this.openType?a=CRM.util.confirm($t("fmcg.checkin.areaManage.depart.set.tip4"),$t("提示"),function(){a.destroy();var e=[{name:$t("片区负责人"),postList:[],adminMultiArea:!1,isStrideArea:!1}];r.$emit("save",{DepartConfigList:t,personnelApiNameAndConfig:e}),r.handleClose()}):(this.$emit("save",{DepartConfigList:t}),this.handleClose()))},handleClose:function(){this.hide()}}});a.exports=r});
define("crm-setting/fastsale/comps/AreaForm",["./AreaDepartSetDialog","./AreaJobSetDialog"],function(a,e,t){var s=a("./AreaDepartSetDialog"),a=a("./AreaJobSetDialog"),s=Vue.extend({template:'\n        <div class="area-form-box">\n            <div class="area-item">\n                <div class="area-item-title">\n                    {{$t(\'crm.manage.fastsale.area.departLevel.title\')}}\n                </div>\n                <div class="depart-set-wrap">\n                    <template v-for="(depart, i) in areaForm.DepartConfigList">\n                        <div class="depart-info"><span>{{level_arr[i]}}</span><span>{{$t(\'第num级部门\',{num:depart.departLevel})}}</span><span>{{depart.departName}}</span></div>\n                    </template>\n                </div>\n                <span class="set-text" @click="showDepartDialog">{{$t(\'设置\')}}</span>\n            </div>\n            <div class="area-item" v-if="areaRouteDataFix != 0">\n                <div class="title-wrap">\n                    <div class="area-item-title">{{$t(\'crm.manage.fastsale.area.historyRoute.title\')}}</div>\n                    <fx-button type="primary" size=\'small\'  @click="handelOldRoute" v-if="areaRouteDataFix==1 && !historyProcess">{{$t(\'crm.manage.fastsale.area.historyRoute.startBtn\')}}</fx-button>\n                    <span class="historyRoute-tip" v-if="areaRouteDataFix==-1 || historyProcess" >\n                        <i class="el-alert__icon el-icon-success"></i>\n                        {{$t(\'crm.manage.fastsale.area.historyRoute.processInfo\')}}\n                    </span>\n                    <span class="historyRoute-tip" v-if="areaRouteDataFix==2" ><i class="el-alert__icon el-icon-success"></i>{{$t(\'crm.manage.fastsale.area.historyRoute.finish\')}}</span>\n                </div>\n                <div class="applicable-scene">{{$t(\'crm.manage.fastsale.area.historyRoute.desc\')}}</div>\n            </div>\n            <div class="area-item">\n                <div class="title-wrap">\n                    <div class="area-item-title">{{$t(\'crm.manage.fastsale.area.post.title\')}}</div>\n                    <fx-switch size="small" v-model="areaForm.isOpenPostAttr" :disabled="areaForm.isOpenPostAttr" :before-change="openJobChange"></fx-switch>\n                </div>\n                <div class="important-info">{{$t(\'crm.manage.fastsale.area.post.tip\')}}</div>\n                <div class="applicable-scene">{{$t(\'crm.manage.fastsale.area.post.desc\')}}</div>\n                <fx-table\n                    border\n                    style="width: 361px"\n      \t\t\t\t:data="areaForm.personnelApiNameAndConfig"\n                    v-if="areaForm.isOpenPostAttr"\n      \t\t\t>\n      \t\t\t\t<fx-table-column\n      \t\t\t\t\tv-for="(col,col_index) in column"\n      \t\t\t\t\t:key="col.prop"\n        \t\t\t\t:prop="col.prop"\n        \t\t\t\t:label="col.label"\n                        width="180"\n        \t\t\t>\n                    <template slot-scope="{ row }">\n                        <div v-if="col_index === 1">{{row[col.prop][0]}}</div>\n                        <div v-else>{{row[col.prop]}}</div>\n                    </template>\n      \t\t\t\t</fx-table-column>\n    \t\t\t</fx-table>\n                <span class="set-text" @click="showEditJobDialog" v-if="areaForm.isOpenPostAttr">{{$t(\'设置\')}}</span>\n            </div>\n            <div class="area-item">\n                <div class="area-item-title">\n                    {{$t(\'crm.manage.fastsale.area.adminAreaNum.title\')}}\n                 </div>\n                <div class="check-wrap" v-for="(item,i) in areaForm.personnelApiNameAndConfig">\n                    <div class="job-label">{{item.name}}</div>\n                    <fx-radio-group\n                        :hasFormItem="false"\n                        v-model="item.adminMultiArea"\n                        @change="changeRadio"\n                    >\n                        <fx-radio  :label="true"  :key="1">{{$t(\'crm.manage.fastsale.area.adminAreaNum.multi\')}}\n                        </fx-radio>\n                        <fx-radio  :label="false"  :key="0" :disabled="item.isStrideArea">{{$t(\'crm.manage.fastsale.area.adminAreaNum.one\')}}\n                        </fx-radio>\n                    </fx-radio-group>\n                </div>\n            </div>\n            <div class="area-item">\n                <div class="area-item-title">\n                    {{$t(\'crm.manage.fastsale.area.crossArea.title\')}}\n                 </div>\n                <div class="check-wrap" v-for="(item,i) in areaForm.personnelApiNameAndConfig">\n                    <div class="job-label">{{item.name}}</div>\n                    <fx-radio-group\n                        :hasFormItem="false"\n                        v-model="item.isStrideArea"\n                        @change="changeRadio"\n                    >\n                        <fx-radio :label="true"  :key="1" :disabled="!item.adminMultiArea">{{$t(\'crm.manage.fastsale.area.crossArea.can\')}}\n                        </fx-radio>\n                        <fx-radio :label="false"  :key="0">{{$t(\'crm.manage.fastsale.area.crossArea.not\')}}\n                        </fx-radio>\n                    </fx-radio-group>\n                </div>\n            </div>\n            <AreaDepartSetDialog openType="edit" :departLevelList="departLevelList" :DepartConfigList="DepartConfigList" @save="save" ref="departDialog"  />\n            <AreaJobSetDialog :personnelApiNameAndConfig="personArr" @save="save" ref="jobDialog"  />\n        </div>\n\t',components:{AreaDepartSetDialog:s,AreaJobSetDialog:a},props:{departLevelList:{type:Array,default:[]},DepartConfigList:{type:Array,default:[]},isOpenPostAttr:{type:Number,default:0},areaRouteDataFix:{type:Number,default:0},personnelApiNameAndConfig:{type:Array,default:[]}},data:function(){return{areaForm:{},level_arr:[$t("crm.manage.fastsale.area.departLevel.firstLevel"),$t("crm.manage.fastsale.area.departLevel.secondLevel"),$t("crm.manage.fastsale.area.departLevel.thirdLevel")],personArr:[],column:[{prop:"name",label:$t("crm.manage.fastsale.area.post.attr")},{prop:"postList",label:$t("crm.manage.fastsale.area.post.name")}],historyProcess:!1}},created:function(){this.areaForm={DepartConfigList:this.DepartConfigList,isOpenPostAttr:!!this.isOpenPostAttr,personnelApiNameAndConfig:this.personnelApiNameAndConfig}},computed:{},methods:{openJobChange:function(){var s=this;return new Promise(function(a,e){var t=CRM.util.confirm($t("crm.manage.fastsale.area.post.opetTip"),$t("提示"),function(){t.destroy(),s.personArr=JSON.parse(JSON.stringify(s.personnelApiNameAndConfig)),s.$refs.jobDialog.show()})})},showDepartDialog:function(){this.$refs.departDialog.show()},showEditJobDialog:function(){this.personArr=JSON.parse(JSON.stringify(this.personnelApiNameAndConfig)),this.$refs.jobDialog.show()},changeRadio:function(){var a=JSON.parse(JSON.stringify(this.areaForm));a.isOpenPostAttr=Number(a.isOpenPostAttr),this.save(a)},save:function(a){a.hasOwnProperty("isOpenPostAttr")||(a.isOpenPostAttr=Number(this.areaForm.isOpenPostAttr));a=Object.assign({},this.areaForm,a);this.$emit("save",a)},handelOldRoute:function(){this.$emit("handleOldRouteDataToArea")},showProcessTip:function(a){this.historyProcess=!0,CRM.util.alert(a)}}});t.exports=s});
function _createForOfIteratorHelper(e,t){var n,i,a,o,r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(r)return a=!(i=!0),{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){a=!0,n=e},f:function(){try{i||null==r.return||r.return()}finally{if(a)throw n}}};if(Array.isArray(e)||(r=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length)return r&&(e=r),o=0,{s:t=function(){},n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(n="Object"===(n={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,i=Array(t);n<t;n++)i[n]=e[n];return i}define("crm-setting/fastsale/comps/AreaJobSetDialog",[],function(e,t,n){var i=Vue.extend({template:'\n        <fx-dialog\n            :title="$t(\'fmcg.checkin.areaManage.position.set.label\')"\n            :visible="dialogVisible"\n            :append-to-body="true"\n            :close-on-click-modal="false"\n            :close-on-press-escape="false"\n            :before-close="handleClose"\n            width="400px"\n            custom-class="fx-dialog-area-job-dialog"\n        >\n        <div>\n            <fx-table\n                border\n                :data="tableData"\n            >\n                <fx-table-column\n                    prop="name"\n                    :label="$t(\'fmcg.checkin.areaManage.position.set.name\')"\n                    width="150">\n                    <template slot-scope="scope">\n                        <div v-if="scope.$index==0">{{scope.row.name}}</div>\n                        <div v-else>\n                            <fx-input\n                                maxlength="10"\n                                size="small"\n                                :el-style="{ display: \'inline-block\', width: \'130px\' }"\n                                v-model="scope.row.name"\n                                :placeholder="$t(\'请输入\')"\n                            /> \n                        </div>\n                    </template>\n                    \n                </fx-table-column>\n                <fx-table-column prop="postList">\n                    <template slot="header" slot-scope="scope">{{$t(\'fmcg.checkin.areaManage.position.label\')}}\n                        <fx-tooltip effect="dark" :content="$t(\'fmcg.checkin.areaManage.position.person.label\')" placement="top">\n                            <span class="area-job-tooltip"></span>\n                        </fx-tooltip>\n                    </template>\n                    <template slot-scope="scope">\n                        <fx-input\n                            maxlength="10"\n                            size="small"\n                            :el-style="{ display: \'inline-block\', width: \'140px\' }"\n                            v-model="scope.row.postList[0]"\n                            :placeholder="$t(\'请输入\')"\n                        />\n                        <span v-if="scope.$index>0 && scope.row.isAdd" class="del-btn" @click="delRow(scope.$index)"></span>\n                    </template>\n                </fx-table-column>\n            </fx-table>\n            <div v-if="showError" :class="[\'err-msg\', is_rightside_err && \'right-err\']">{{errMsg}}</div>\n            <fx-button size="small" @click="addRow" :class="[\'add-btn\',tableData.length>=5?\'add-disabled\':\'\']">{{$t(\'添加\')}}</fx-button>\n        </div>\n        <div slot="footer" class="dialog-footer">\n            <fx-button type="primary" size="small" @click="submit">{{\n                $t("保 存")\n            }}</fx-button>\n            <fx-button @click="handleClose" size="small">{{\n                $t("取 消")\n            }}</fx-button>\n        </div>\n    </fx-dialog>\n\t',props:{personnelApiNameAndConfig:{type:Array,default:function(){return[]}}},data:function(){return{dialogVisible:!1,showError:!1,is_rightside_err:!1,errMsg:"",tableData:[]}},computed:{},watch:{dialogVisible:function(e){e&&(e=this.personnelApiNameAndConfig,this.showError=!1,e.forEach(function(e){e.postList||(e.postList=[""])}),this.tableData=e.length?e:[{name:$t("片区负责人"),postList:[""],adminMultiArea:!1,isStrideArea:!1}])}},methods:{show:function(){this.dialogVisible=!0},hide:function(){this.dialogVisible=!1},addRow:function(){var e=this.tableData;5<=e.length?FxUI.MessageBox.alert($t("fmcg.checkin.areaManage.position.maxtip"),"",{type:"info"}):e.push({name:"",postList:[""],adminMultiArea:!1,isStrideArea:!1,isAdd:!0})},delRow:function(e){this.tableData.splice(e,1)},valid:function(){var e,t=!1,n=!1,i="",a=this.tableData,o=_createForOfIteratorHelper(a);try{for(o.s();!(e=o.n()).done;){var r=e.value;if(!r.name||!r.name.trim()){t=!0,i=$t("fmcg.checkin.areaManage.position.tip1");break}if(!r.postList[0]||!r.postList[0].trim()){n=t=!0,i=$t("fmcg.checkin.areaManage.position.tip2");break}if(/[\\\\`~\!@#$%\^\&\*\(\)\+=\|\{\}':;',\[\].<>\/\?！￥%……&*（）——+]/i.test(r.name.trim())){t=!0,i=$t("fmcg.checkin.areaManage.position.tip3");break}if(/[\\\\`~\!@#$%\^\&\*\(\)\+=\|\{\}':;',\[\].<>\/\?！￥%……&*（）——+]/i.test(r.postList[0].trim())){n=t=!0,i=$t("fmcg.checkin.areaManage.position.tip4");break}}}catch(e){o.e(e)}finally{o.f()}return t||(_.uniq(a.map(function(e){return e.name.trim()})).length!==a.length&&(t=!0,i=$t("fmcg.checkin.areaManage.position.tip5")),_.uniq(a.map(function(e){return e.postList[0].trim()})).length!==a.length&&(n=t=!0,i=$t("fmcg.checkin.areaManage.position.tip6"))),this.showError=t,this.is_rightside_err=n,this.errMsg=i,t},submit:function(){var e,t,n=this;this.valid()||(e=this.tableData,_.some(e,function(e){return e.isAdd})?t=CRM.util.confirm($t("fmcg.checkin.areaManage.position.tip7"),$t("提示"),function(){t.destroy(),n.$emit("save",{personnelApiNameAndConfig:e,isOpenPostAttr:1}),n.handleClose()}):(this.$emit("save",{personnelApiNameAndConfig:e,isOpenPostAttr:1}),this.handleClose()))},handleClose:function(){this.hide()}}});n.exports=i});
define("crm-setting/fastsale/comps/BillForm",[],function(e,t,i){var l=Vue.extend({template:'\n        <div class="bill-wrap">\n            <div class="bill-title">{{$t(\'crm.manage.fastsale.bill.post.title\')}}</div>\n            <fx-switch v-model="isOpenBill" :disabled="isOpenBill" :before-change="openBillChange"></fx-switch>\n            <div class="bill-tip">{{$t(\'开启后不可关闭\')}}</div>\n        </div>\n\t',components:{},props:{isOpen:{type:Boolean,default:!1}},data:function(){return{isOpenBill:!1}},created:function(){this.setSwitch(this.isOpen)},methods:{setSwitch:function(e){this.isOpenBill=e},openBillChange:function(){var l=this;return new Promise(function(e,t){var i=CRM.util.confirm($t("crm.bill.fastsale.bill.post.opetTip"),$t("提示"),function(){i.destroy(),l.handleOpen()},{btnLabel:{confirm:$t("crm.form_save_btn")}})})},handleOpen:function(){this.setSwitch(!0),CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/SalesStatementsObj/controller/Open",data:{},success:function(e){e&&e.Value&&0==e.Value.errorCode?CRM.util.remind(1,$t("设置成功")):CRM.util.alert(e&&e.Result&&e.Result.FailureMessage||$t("网络错误，请重试！"))}},{errorAlertModel:2})}}});i.exports=l});
define("crm-setting/fastsale/comps/FxFormulaDesigner",["./FxRichtext","crm-modules/common/util"],function(e,n,t){var i=e("./FxRichtext"),r=e("crm-modules/common/util"),e='\n    <div class="fx-formula-designer">\n    <fx-row>\n      <fx-col :span="24">\n        <fx-richtext\n          ref="textarea"\n          class="fx-formula-designer-richtext-area" \n          :placeholder="placeholder" \n          :richValue="drichValue"\n          :value="dvalue">\n        </fx-richtext>\n      </fx-col>\n    </fx-row>\n\n    <fx-row class="fx-formula-designer-buttons">\n      <fx-col :span="16">\n        <fx-dropdown @command="insertField">\n          <fx-button size="small">\n            {{$t(\'插入字段\')}}<i class="el-icon-arrow-down el-icon--right"></i>\n          </fx-button>\n          <fx-dropdown-menu slot="dropdown">\n            <fx-dropdown-item\n              v-for="op in dfieldList"\n              :key="op.value"\n              :disabled="op.disabled"\n              :command="op.disabled?\'\':\''.concat("+op.label+",'\'">\n              {{op.label}}\n            </fx-dropdown-item>\n          </fx-dropdown-menu>\n        </fx-dropdown>\n\n        <fx-dropdown @command="insertOperator">\n          <fx-button size="small">\n            {{$t(\'插入运算符\')}}<i class="el-icon-arrow-down el-icon--right"></i>\n          </fx-button>\n          <fx-dropdown-menu slot="dropdown">\n            <fx-dropdown-item\n              v-for="op in operatorList"\n              :key="op.value"\n              :command="op.valueLabel">\n              {{op.valueLabel}} {{op.label}}\n              </fx-dropdown-item>\n          </fx-dropdown-menu>\n        </fx-dropdown>\n      </fx-col>\n      <fx-col :span="8" class="fx-formula-designer-button-check">\n        <fx-button size="small" type="primary" @click="emitCheck">{{$t(\'语法检查\')}}</fx-button>\n      </fx-col>\n    </fx-row>\n  </div>\n\t'),e=Vue.extend({template:e,name:"fx-formula-designer",components:{"fx-richtext":i},data:function(){return{dvalue:this.value,dbizKey:this.bizKey,drichValue:"",operatorList:[{label:$t("加"),valueLabel:"+",value:"+"},{label:$t("减"),valueLabel:"-",value:"-"},{label:$t("乘"),valueLabel:"×",value:"*"},{label:$t("除"),valueLabel:"÷",value:"/"},{label:$t("括号"),valueLabel:"()",value:"()"}],dfieldList:[{value:"",label:$t("没有可用于计算的字段"),disabled:!0}]}},watch:{value:function(e,n){this.dvalue=e,this.drichValue=this.transformExpression(this.value,1)},fieldList:function(e,n){e&&0!=e.length?this.dfieldList=e:this.dfieldList=[{value:"",label:$t("没有可用于计算的字段"),disabled:!0}]},bizKey:function(e,n){this.dbizKey=e}},props:{bizKey:{default:function(){return""}},value:{default:function(){return""}},fieldList:{default:function(){return null}},placeholder:{default:function(){return""}}},computed:{transformList:function(){var n={},t={};return this.fieldList&&this.operatorList.forEach(function(e){n[e.valueLabel]=e.value,t[e.value]=e.valueLabel}),this.fieldList&&this.fieldList.forEach(function(e){n[e.label]=e.value,t[e.value]=e.label}),{myMap:n,reverseMap:t}},cFunctionList:function(){var i=[],e=Config.function_classifys||[],a=Config.functions||{};return e.forEach(function(e){var n=e.types||[],t=[];n.forEach(function(e){t.push(a[e])}),i.push({label:e.label,value:e.value,children:t})}),[{children:i}]}},mounted:function(){this.drichValue=this.transformExpression(this.value,1)},methods:{handleAddFunction:function(){var e=((0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).value||[])[1];this.insertExpressionIn({value:e},-1)},insertExpressionIn:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};this.insertContent(e.value,!1)},insertField:function(e){this.insertContent(e,1)},insertOperator:function(e){this.insertContent(e,!1)},insertContent:function(e,n){this.$refs.textarea.insert(e,n)},emitCheck:function(){this.$emit("input",this.getValue()),this.checking=!0,this.$emit("check")},getValue:function(){return this.transformExpression(this.$refs.textarea.getValue().value)},getValueText:function(){return this.$refs.textarea.getValue().value},transformExpression:function(e,t){var n=this.transformList,i=n.myMap;t&&(i=n.reverseMap);n=(n=e.replace(/\${([\w\.\_\-\u4e00-\u9fa5\(\)（）-]+)}/gi,function(e,n){return t?i[n]?' <span class="bpm-vars">${'+i[n]+"}</span>":' <span class="bpm-vars">${'+n+"}</span> ":"${"+i[n]+"}"||"${"+n+"}"})).replace(/([×|÷])/gi,function(e,n){return i[n]||n});return n=t?n:n.replace(/\s+/gi,"")},check:function(i){var a=this,e=this.transformExpression(this.$refs.textarea.getValue().value),l=this.dbizKey;return new Promise(function(n,t){r.FHHApi({url:"/EM1HINTE/rule/validateExpression",data:{expression:e,bizKey:l},success:function(e){e.Value&&e.Value.legal?(i(),a.checking&&r.remind($t("计算公式验证通过")),n()):(i($t("计算公式错误")),t($t("计算公式错误"))),delete a.checking},error:function(){i($t("计算公式错误")),t($t("计算公式错误")),delete a.checking}})}).catch(function(e){})},reset:function(){this.dvalue="",this.drichValue="",this.$refs.textarea.reset()}}});t.exports=e});
define("crm-setting/fastsale/comps/FxRichtext",[],function(e,t,n){function l(e,t,n){var i,a;-1<["bpm-vars","bpm-error","var"].indexOf(n.className)?n.nextSibling&&-1==["bpm-vars","bpm-error","var"].indexOf(n.nextSibling.className)?t.setStartAfter(n.nextSibling):(i=document.createDocumentFragment(),(a=document.createElement("span")).innerHTML="&nbsp;",i.appendChild(a),o(i,n),a=n.parentNode.lastChild,t.setStartAfter(a)):t.setStartAfter(n),s(e,t)}function s(e,t){t.collapse(!0),e.removeAllRanges(),e.addRange(t)}function r(e){return(e=(e||{}).focusNode)?e.parentNode:null}function o(e,t){var n=t.parentNode;n.lastChild==t?n.appendChild(e):n.insertBefore(e,t.nextSibling)}var i=Vue.extend({name:"fx-richtext",template:'\n        <div class="fx-richtext-area" \n            spellcheck="false"\n            @placeholder="placeholder"\n            contenteditable="true"\n            @click = "getRange"\n            @keydown = "changeContent"\n            @keyup = "removeText"\n            @mousedown = "handleMousedown"\n            @focus = "handleFocus"\n            @paste = "handlePast"\n            v-html="drichValue"\n        ></div>\n\t',data:function(){return{show:!1,dvalue:this.value,drichValue:this.richValue}},watch:{value:function(e,t){this.dvalue=e},richValue:function(e,t){this.drichValue=e}},props:{value:{default:function(){return""}},richValue:{default:function(){return""}},placeholder:{default:function(){return $t("请输入公式")}}},components:{},mounted:function(){this.$input=$(this.$el),this.$input.focus(),_.delay(this.focus,100)},computed:{},methods:{reset:function(){this.$input.html("")},changeContent:function(e){if(e.stopPropagation(),13===(e.keyCode||e.which))return e.preventDefault(),this.insertText("<br />&nbsp;"),!1;this.getRange(e)},handlePast:function(e){e=(window.clipboardData||e.clipboardData).getData("Text");return e=e.replace(/</gm,"&lt;").replace(/[\r\n]/g,"<br />"),this.insertText(e),!1},setValue:function(e){var t=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];Vue.set(this,"dvalue",e),t?this.$input.html(' <span class="bpm-vars">'.concat(e,"</span> ")):this.$input.html(e),_.delay(this.focus,100)},focus:function(){if(window.getSelection)if(this.$input[0].lastChild)try{this.$input.focus();var e=document.createDocumentFragment(),t=document.createElement("span"),n=(t.innerHTML="&nbsp;",e.appendChild(t),-1<["bpm-vars","bpm-error","var"].indexOf(this.$input[0].lastChild.className)?this.$input[0].appendChild(e):-1<["bpm-vars","bpm-error","var"].indexOf(this.$input[0].lastChild.innerHTML)&&this.$input[0].lastChild.appendChild(e),this.$input[0].lastChild),i=window.getSelection().getRangeAt(0);i.setStartAfter(n),this.lastEditRange=i}catch(e){}else this.$input.focus()},insert:function(e){var t=!(1<arguments.length&&void 0!==arguments[1])||arguments[1];""==this.$input.text()?this.setValue(e,t):(t&&(e=' <span class="bpm-vars">'.concat(e,"</span> ")),this.insertText(e,1))},getRange:function(e){var t;window.getSelection&&(t=window.getSelection(),this.lastEditRange=t.getRangeAt(0)),this.moveToAfter(),"click"==e.type&&this.$emit("click")},handleFocus:function(){this.$emit("focus")},handleMousedown:function(){this.$emit("mousedown")},getValue:function(){var e=this.$input.text().replace(/\s+/gi,"");Vue.set(this,"dvalue",e);var t=(t=this.$input.html()).replace(/<span>&nbsp;<\/span>/gm,"").replace(/&lt;/gm,"<").replace(/&gt;/gm,">").replace(/<br * >/gm,"[\r\n]");return Vue.set(this,"drichValue",t),{value:e,richValue:t}},moveToAfter:function(){var e,t,n;window.getSelection&&(e=window.getSelection(),t=r(e),-1<["bpm-vars","bpm-error","var"].indexOf(t.className))&&(n=e.getRangeAt(0).cloneRange(),l(e,n,t),this.lastEditRange=n)},removeText:function(e){e&&e.stopPropagation();var t,n,i,a=e.keyCode||e.which;8!==a&&46!==a||window.getSelection&&(t=window.getSelection(),t=r(t),-1<(n=["bpm-vars","var","bpm-error"]).indexOf(t.className)?((i=document.createElement("span")).innerHTML="&nbsp;",t.parentNode.replaceChild(i,t)):46===a&&t.nextElementSibling&&-1<n.indexOf(t.nextElementSibling.className)&&t.parentNode.removeChild(t.nextElementSibling)),this.getRange(e)},insertText:function(e,t){if(e&&window.getSelection){var n=window.getSelection();if(t&&(void 0!==this.lastEditRange&&this.lastEditRange||(this.lastEditRange=n.getRangeAt(0)),n.removeAllRanges(),n.addRange(this.lastEditRange)),document.queryCommandSupported("insertHTML"))document.execCommand("insertHTML",!1,e),this.focus(),this.lastEditRange=n.getRangeAt(0);else if(n.getRangeAt&&n.rangeCount){var i,a,t=this.lastEditRange,s=(t.deleteContents(),document.createElement("div")),r=document.createDocumentFragment();for(s.innerHTML=e;i=s.firstChild;)a=r.appendChild(i);t.insertNode(r),a&&(t=t.cloneRange(),l(n,t,a)),this.lastEditRange=n.getRangeAt(0)}}}}});n.exports=i});
define("crm-setting/fastsale/comps/GenerateForm",["./AreaDepartSetDialog","./AreaJobSetDialog"],function(e,t,i){var n=e("./AreaDepartSetDialog"),e=e("./AreaJobSetDialog"),n=Vue.extend({template:'\n        <div class="generate-form-wrap">\n            <div class="generate-title">{{$t(\'crm.manage.fastsale.generate.sel_geneerate_data\')}}</div>\n            <ul class="generate-checkbox" v-if="checkBoxInfoList">\n                <li v-for="(item,i) in checkBoxInfoList" :key="item.checkBoxType">\n                    <p>{{i+1}}.{{item.checkBoxName}}</p>\n                    <p v-if="item.checkBoxType === \'TpmBasicsData\'" class="tip-txt">{{$t(\'crm.manage.fastsale.generate.tpmTip\')}}</p>\n                    <fx-checkbox-group \n                        v-model="copyTypeStringList[i]" \n                        class="generate-checkgroup"  \n                        @change="onChange"\n                    >\n                        <fx-checkbox\n                            class="generate-checkitem" \n                            :class="{\'empty-checked\':checkItem.isCheck==2}" \n                            :label="checkItem.checkBoxType" \n                            v-for="checkItem in item.childList" \n                            :disabled="checkItem.isCheck != 1" \n                            :key="checkItem.checkBoxType"\n                        >\n                            {{checkItem.checkBoxName}}\n                        </fx-checkbox>\n                    </fx-checkbox-group>\n                </li>\n            </ul>\n            <div class="generate-title">{{$t(\'crm.manage.fastsale.generate.del_data_period\')}}</div>\n            <fx-select\n                v-model="timeString"\n                :options="timeQuantumStrList"\n                :no-data-text="$t(\'el.cascader.noData\')"\n                size="small"\n                v-if="timeQuantumStrList"\n            ></fx-select>\n            <div class="generate-btn"><fx-button type="primary" size="mini" :disabled="!isAvailable" @click="saveSubmit">{{$t(\'crm.manage.fastsale.generate.submit\')}}</fx-button></div>\n        </div>\n\t',components:{AreaDepartSetDialog:n,AreaJobSetDialog:e},props:{checkBoxInfoList:{type:Array,default:[]},timeQuantumStrList:{type:Array,default:[]}},data:function(){return{copyTypeStringList:[],timeString:"",isHasTpmTarget:!1,tpmTargetIndex:[],isHasCheckinTarget:!1,checkinTargetIndex:[],isHasOrderTarget:!1,orderTargetIndex:[]}},created:function(){this.initCheckbox()},computed:{isAvailable:function(){var e=this.copyTypeStringList,t=e.flat(),i=this.isHasCheckinTarget&&e&&e.length>=this.checkinTargetIndex+1&&e[this.checkinTargetIndex]||[],n=this.isHasTpmTarget&&e&&e.length>=this.tpmTargetIndex+1&&e[this.tpmTargetIndex]||[];return!(0===t.length||(!this.isHasTpmTarget||!this.isHasCheckinTarget||-1===n.indexOf("TpmData")||-1==i.indexOf("CheckinsObj")||""==this.timeString)&&(this.isHasCheckinTarget||this.isHasOrderTarget)&&this.isSelCheckbox(e)&&""===this.timeString)}},methods:{initCheckbox:function(){var i,n,a,s,c,r,e=this.checkBoxInfoList.length,e=new Array(e).fill([]);this.copyTypeStringList=e,this.checkBoxInfoList.find(function(e,t){0==(e.childList&&0<e.childList.length&&e.childList||[]).length?(a=!1,s=!(i=-1),r=!(c=n=-1)):"CheckinsData"==e.checkBoxType?(n=t,s=!0):"TpmData"==e.checkBoxType?(a=!0,i=t):"SalesOrderData"==e.checkBoxType&&(r=!0,c=t)}),this.isHasTpmTarget=a,this.tpmTargetIndex=i,this.isHasCheckinTarget=s,this.checkinTargetIndex=n,this.isHasOrderTarget=r,this.orderTargetIndex=c},isSelCheckbox:function(e){var i=this;return e.some(function(e,t){return(t==i.checkinTargetIndex||t==i.orderTargetIndex)&&0<e.length})},saveSubmit:function(){var e=this,t=this.copyTypeStringList,i=this.timeString,t={copyTypeStringList:t.flat(),timeString:i,onSuccess:function(){e.resetForm()}};this.$emit("save",t)},resetForm:function(){this.initCheckbox(),this.timeString=""},onChange:function(e){var t=this,i=this.copyTypeStringList,n=this.isHasCheckinTarget&&i&&i.length>=this.checkinTargetIndex+1&&i[this.checkinTargetIndex]||[],i=this.isHasTpmTarget&&i&&i.length>=this.tpmTargetIndex+1&&i[this.tpmTargetIndex]||[];this.isHasTpmTarget&&this.isHasCheckinTarget&&(-1!==e.indexOf("TpmData")&&-1==n.indexOf("CheckinsObj")?(e=$t("crm.manage.fastsale.generate.tipMessage"),this.$confirm(e,$t("el.messagebox.title"),{confirmButtonText:$t("el.colorpicker.confirm"),cancelButtonText:$t("richtext.cancel"),callback:function(){t.$set(t.copyTypeStringList,t.tpmTargetIndex,[])}})):-1!=i.indexOf("TpmData")&&-1==n.indexOf("CheckinsObj")&&this.$set(this.copyTypeStringList,this.tpmTargetIndex,[]))}}});i.exports=n});
define("crm-setting/fastsale/comps/SmartEstimateForm",["./FxFormulaDesigner","paas-function/sdk.js"],function(e,n,s){var a=e("./FxFormulaDesigner"),t=e("paas-function/sdk.js"),e=Vue.extend({template:'\n        <div class="smartEstimate-form-box">\n            <div class="ticket selected not-allow">\n                {{$t(\'预估【采购订单】\')}}\n            </div>\n            <fx-form ref="form" :model="smartForm" label-width="130px" class="form">\n                <fx-form-item :label="$t(\'预估产品范围\')+\'：\'" prop="productRange">\n                    <fx-radio-group\n                        :hasFormItem="false"\n                        v-model="smartForm.range"\n                    >\n                        <fx-radio  :label="0"  :key="0" >{{$t(\'全部产品\')}}\n                            <slot v-if="smartForm.range === 0">\n                                <p class="range-desc">{{$t(\'创建采购订单时，点击“智能预估”，会预估全部符合条件的产品，并填入采购订单中\')}}</p>\n                            </slot>\n                        </fx-radio>\n                        <fx-radio  :label="1"  :key="1" >{{$t(\'选择的产品\')}}\n                            <slot v-if="smartForm.range === 1">\n                                <p class="range-desc">{{$t(\'创建采购订单时，需要先勾选产品，再预估所选产品的采购数量\')}}</p>\n                            </slot>\n                        </fx-radio>\n                    </fx-radio-group>\n                </fx-form-item>\n\n                <fx-form-item :label="$t(\'智能预估方式\')+\'：\'" prop="productRange">\n                    <fx-radio-group\n                        :hasFormItem="false"\n                        v-model="smartForm.method"\n                    >\n                        <fx-radio  :label="0"  :key="0" >{{$t(\'按照预置公式\')}}\n                            <slot v-if="smartForm.method === 0">\n                                <div class="method-desc-wrap">\n                                    <div class="formula-box">\n                                        <span class="label">{{$t(\'预置公式\')}}：</span>\n                                        <div class="content">\n                                            {{$t(\'建议采购量\')}}  =  {{$t(\'销售总数量\')}}  -  {{$t(\'可用库存量\')}}\n                                            <span :class="[onWayPurchaseChecked ? \'checked\' : \'no-checked\']"> -<fx-checkbox v-model="onWayPurchaseChecked" @change="v=>changeCheck(v,1)" >{{$t(\'在途采购量\')}}</fx-checkbox></span>\n                                            <span :class="[safeInStockChecked ? \'checked\' : \'no-checked\']"> +<fx-checkbox v-model="safeInStockChecked" @change="v=>changeCheck(v,2)" >{{$t(\'安全库存量\')}}</fx-checkbox></span>\n                                        </div>\n                                    </div>\n                                    <div class="formula-box">\n                                        <span class="label">{{$t(\'取值时间范围\')}}：</span>\n                                        <div class="content">\n                                            {{$t(\'取最近\')}}\n                                            <fx-input \n                                                size="small"\n                                                type="number"\n                                                :isPositiveNum="true"\n                                                :hasFormItem="false"\n                                                class="recent-day"\n                                                v-model="smartForm.recent"\n                                            />\n                                            {{$t(\'天\')}}<span class="no-checked">（{{\n                                                    $t(\'fmcg.checkin.fastsale.smartForm.tip1\')\n                                            }}）</span>{{$t(\'fmcg.checkin.fastsale.smartForm.tip2\')}}\n                                        </div>\n                                    </div>\n                                </div>\n                            </slot>\n                        </fx-radio>\n                        <fx-radio  :label="1"  :key="1" >{{$t(\'按照自定义公式\')}}\n                            <slot v-if="smartForm.method === 1">\n                                <div class=\'method-desc-wrap\'>\n                                    <div class="formula-box" :style="{alignItems:\'start\'}">\n                                        <span>{{$t(\'建议采购量\')}} = </span>\n                                        <div class="formula-richtext">\n                                            <fx-formula-designer v-model="smartForm.expression" ref="formulaDesigner" :hasFormItem="false" :fieldList="formulaFieldList" :placeholder="$t(\'请输入公式\')" @check="emitFormulaCheck()"></fx-formula-designer>\n                                        </div>\n                                    </div>\n                                </div>\n                            </slot>\n                        </fx-radio>\n                        <fx-radio  :label="2"  :key="2" >{{$t(\'按照函数\')}}\n                            <slot v-if="smartForm.method === 2">\n                                <span class="add-apl-code" @click="addFun"><a>{{$t(\'添加APL代码\')}}</a></span>\n                            </slot>\n                        </fx-radio>\n                    </fx-radio-group>\n                </fx-form-item>\n            </fx-form>\n        </div>\n\t',components:{"fx-formula-designer":a},props:{range:Number,method:Number,expression:String,recent:Number},data:function(){return{smartForm:{},onWayPurchaseChecked:this.expression.includes("onWayPurchase_num"),safeInStockChecked:this.expression.includes("safeInStock_num"),expressionSupportFieldTypes:["number","currency"]}},created:function(){this.smartForm={range:this.range,method:this.method,expression:this.expression,recent:this.recent}},computed:{formulaFieldList:function(){}},methods:{addFun:function(){t.create({object_api_name:"SalesOrderProductObj",zIndex:2e3}).on("complete",function(e){})},changeCheck:function(e,n){1===n?this.onWayPurchaseChecked=e:this.safeInStockChecked=e},getValue:function(){return{value:this.value,days:this.days}},handleConfirm:function(){this.$emit("confirm",this.getValue()),this.handleClosed()},handleClosed:function(){this.$emit("close",this.getValue()),this.$destroy()},emitFormulaCheck:function(){this.$refs.form.validateField("expression",function(){})}}});s.exports=e});
define("crm-setting/fastsale/fastsale",["crm-modules/common/util","./spu-html","./multiunit-html","crm-modules/components/tabs/tabs","./switchSpu-html","./orderDelivery-html","./smartEstimate-html","./area-html","./fixedCollocation-html","./generate-html","./bill-html","./comps/SmartEstimateForm","./comps/AreaForm","./comps/GenerateForm","./comps/BillForm","./comps/AreaDepartSetDialog","../cmmodityproduct/data","./tpl-html"],function(e,t,i){var o=e("crm-modules/common/util"),n=e("./spu-html"),r=e("./multiunit-html"),a=e("crm-modules/components/tabs/tabs"),s=e("./switchSpu-html"),l=e("./orderDelivery-html"),c=e("./smartEstimate-html"),u=e("./area-html"),d=e("./fixedCollocation-html"),m=e("./generate-html"),p=e("./bill-html"),f=e("./comps/SmartEstimateForm"),g=e("./comps/AreaForm"),b=(GenerateForm=e("./comps/GenerateForm"),BillForm=e("./comps/BillForm"),AreaDepartSetDialog=e("./comps/AreaDepartSetDialog"),e("../cmmodityproduct/data")),C={multiunitStatus:{code:"multiple_unit",cache:"multiunitStatus",targetCon:"multipleUnit",msg:$t("确认开启多单位吗")},multi_unit_price_book:{code:"multi_unit_price_book",cache:"multi_unit_price_book",targetCon:"multipleUnit",msg:$t("确认开启多单位定价吗")}};return Backbone.View.extend({template:e("./tpl-html"),initialize:function(e){this.setElement(e.wrapper),this.spuSetting=!1,this.productOpenSpu=!1,this.mobileSpec="capsule",this.tenant_id=CRM.enterpriseId,this.GenerateForm=null},events:{"click .j-switch-tab":"switchTab","click .j-set-config":"setConfig","click .j-set-on":"showTip","click .j-reload-config":"_reloadHandle","click .j-mobile-spec":"setMobileSpec"},render:function(e,t){this.tarTab=e||"switchSpu-box";var i=this;Promise.all([this.isCpqBuyed(),this.isOpenArea(),this.isShowFixedCollocation(),this.getMenuList()]).then(function(t){CRM.util.getproductWithSpuConfig().done(function(e){switch(i.$el.html(i.template({isCpq:t[0],isMulOpen:!0,isSpuOpen:e&&t[3].allowIds.includes("selectProductSettings"),isGrayProduct:!0,isJunLeBao:CRM.util.getUserAttribute("jlbpeisong")&&t[3].allowIds.includes("orderBasedDeliverySettings"),isFixedCollocation:t[2]&&t[3].allowIds.includes("fixedCollocation"),isAreaOpen:t[1].isOpen&&t[3].allowIds.includes("areaSetting"),isBatchGenerate:t[1].isBatchGenerate&&t[3].allowIds.includes("oneClickGenerationOfPOCPresetData"),isOpenSalesStatements:t[1].isOpenSalesStatements&&t[3].allowIds.includes("accountReconciliationManagement")})),i.renderManage("spu"),$("[data-render="+i.tarTab+"]").addClass("cur"),a.prototype.getParameters()){case"tabs-switchSpu":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="switchSpu-box"]').addClass("cur"),i.tarTab="switchSpu-box",i.renderManage("spu");break;case"tabs-multiunit":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="multiunit-box"]').addClass("cur"),i.tarTab="multiunit-box",i.renderManage("multiple_unit");break;case"tabs-spu":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="spu-box"]').addClass("cur"),i.tarTab="spu-box",i.renderManage("saveConfig");break;case"tabs-orderDelivery":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="orderDelivery-box"]').addClass("cur"),i.tarTab="orderDelivery-box",i.getConfig(i.renderTpl);break;case"tabs-fixedCollocation":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="fixedCollocation-box"]').addClass("cur"),i.tarTab="fixedCollocation-box",i.renderManage("simple_cpq")}})})},renderManage:function(t){var i=this;e.async("crm-modules/components/biz_manage/biz_manage",function(e){this.manage=new e({$el:$(".render-content",i.$el),module:"cmmodityproduct",type:t})})},renderTpl:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{multi_spec_display_style:!1},i=this,a={"spu-box":n,"multiunit-box":r,"switchSpu-box":s,"orderDelivery-box":l,"smartEstimate-box":c,"area-box":u,"fixedCollocation-box":d,"generate-box":m,"bill-box":p};$(".render-content",i.$el).html(a[i.tarTab](_.extend({start:e,priceBookPriority:CRM._cache.priceBookPriority},t))),"fixedCollocation-box"===i.tarTab&&e&&i.renderBOMCalculateCom(),"multiunit-box"===i.tarTab&&CRM._cache.multiunitStatus&&(i.getMobileConfig(),i.renderMobileMultConfig(),i.renderMobileMultUnitConfig())},getMobileConfig:function(){this._mobileConfig=b.find(function(e){return"mobileMultiUnitConfig"===e.moduleId})},renderMobileMultConfig:function(){var t=this;e.async("vcrm/sdk",function(e){e.getComponent("MobileMulticonfig").then(function(e){e=e.default,new Vue({el:t.$(".mobile-multi-config")[0],template:'<div class="mobile-multi-config-old">\n                                <MobileMulticonfig :dataItem="mc"></MobileMulticonfig>\n                          </div>',components:{MobileMulticonfig:e},data:function(){return{mc:t._mobileConfig}},mounted:function(){},methods:{}})})})},renderMobileMultUnitConfig:function(){var t=this;e.async("vcrm/sdk",function(e){e.getComponent("MobileMultUnitConfig").then(function(e){e=e.default,new Vue({el:t.$(".mobile-multi-showType")[0],template:'<div class="mobile-multi-config-old">\n                                <MobileMultUnitConfig :dataItem="mc"></MobileMultUnitConfig>\n                          </div>',components:{MobileMultUnitConfig:e},data:function(){return{mc:t._mobileConfig}},mounted:function(){},methods:{}})})})},renderBOMCalculateCom:function(){var e=this.$el.find(".cpq-calculateConfig-box");this.radio=FxUI.create({wrapper:e[0],template:'\n                    <fx-radio-group v-model="radio" @change="onChange">\n                         <fx-radio class="cpq-pricebook-config-def" label="0">'.concat($t("CRM.setting.fastsale.fixedCollocation.bom.description.option1",null,"产品包价格包含包内产品价格"),'\n                          <br/>\n                         <div class="crm-intro-warning">').concat($t("CRM.setting.fastsale.fixedCollocation.bom.description.option1.warn_info1",null,"选用此选项，则：产品包价格为整个固定搭配的价格，可用于为整个固定搭配定价。"),'</div>\n                         </fx-radio>\n                         <br/>\n                         <fx-radio label="1">').concat($t("CRM.setting.fastsale.fixedCollocation.bom.description.option2",null,"产品包价格不包含所添加产品价格"),'\n                          <br/>\n                         <div class="crm-intro-warning">').concat($t("CRM.setting.fastsale.fixedCollocation.bom.description.option2.warn_info1",null,"选用此选项，则：产品包价格+搭配内产品的金额（数量x价格）为固定搭配的价格；"),'</div>\n                         <div class="crm-intro-warning">').concat($t("CRM.setting.fastsale.fixedCollocation.bom.description.option2.warn_info2",null,"当固定搭配的“组合销售类型”为【产品包不独立销售】时，选此开关产品包价格设置为0，则：搭配内产品的总金额（数量x价格）为固定搭配的价格。"),"</div>\n                         </fx-radio>\n                    </fx-radio-group>\n                "),data:function(){return{radio:CRM._cache.bom_price_calculation_configuration||"0"}},created:function(){var t=this;CRM.util.getConfigValue("bom_price_calculation_configuration").then(function(e){t.radio=e})},methods:{onChange:function(e){CRM.util.setConfigValue({key:"bom_price_calculation_configuration",value:e}).then(function(){o.remind(1,$t("设置成功")),CRM._cache.bom_price_calculation_configuration=e})}}})},isCpqBuyed:function(){return new Promise(function(t,e){o.FHHApi({url:"/EM1HNCRM/API/v1/object/version_privilege/service/check_app",data:{proudct_code:"cpq_app"},success:function(e){0===e.Result.StatusCode?t(e.Value.result):o.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},isOpenArea:function(){return new Promise(function(a,e){o.FHHApi({url:"/EM1HWaiQinV2/ruleWebServiceV2/getEaInfo",data:{},success:function(e){var t,i;0===e.Result.StatusCode?(i=(t=e.Value).checkKXEa,isOpenSalesStatements=1==t.openSalesStatements,a({isOpen:i&&t.isOpenStandardArea,isBatchGenerate:1==t.openDemo2Poc,isOpenSalesStatements:isOpenSalesStatements})):o.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},getMenuList:function(){return new Promise(function(i,e){o.FHHApi({url:"/EM1HWaiQinV2/ruleWebServiceV2/getAllowData",data:{key:"fmcgServicePlugin"},success:function(e){var t;0===e.Result.StatusCode?(t=e.Value.allowIds,i({allowIds:void 0===t?[]:t})):o.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},isShowFixedCollocation:function(){return new Promise(function(t){CRM.util.getConfigValue("price_policy").then(function(e){return t("1"===e&&CRM.util.isGrayScale("CRM_FIXED_COLLOCATION"))})})},switchTab:function(e){e=$(e.target),e.parent().children().removeClass("cur"),e.addClass("cur"),this.tarTab=e.attr("data-render"),e={"switchSpu-box":"spu","spu-box":"saveConfig","multiunit-box":"multiple_unit","fixedCollocation-box":"simple_cpq"};e[this.tarTab]?this.renderManage(e[this.tarTab]):this.getConfig(this.renderTpl)},_reloadHandle:function(e){this.getConfig(this.renderTpl,$(e.currentTarget))},getConfig:function(r,e){var t,i,s=this,a={};if("spu-box"==s.tarTab)s.getSpuTabConfig({$btn:e,renderTpl:r});else if("multiunit-box"===s.tarTab)this.getMultiKeyConfig([{code:"multiple_unit",name:"multiunitStatus"},{code:"multi_unit_price_book",name:"multi_unit_price_book"}],r);else{if("cpq-box"===s.tarTab)var n={"cpq-box":{code:"cpq",name:"cpqSetting"}},a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/check_module_status",data:{moduleCode:n[s.tarTab].code,tenantId:s.tenant_id},success:function(e){var t;0!=e.Result.StatusCode?o.alert(e.Result.FailureMessage):(e="1"==e.Value.value.openStatus,t=n[s.tarTab].name,s[t]=e,CRM._cache[t]=e,r&&r.call(s,e))}};else if("switchSpu-box"===s.tarTab)a={url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"spu"},success:function(e){0!=e.Result.StatusCode?o.alert(e.Result.FailureMessage):(e=1==e.Value.value||"1"==e.Value.value,s.productOpenSpu=e,CRM._cache.productOpenSpu=e,r&&r.call(s,e))}};else if("orderDelivery-box"===s.tarTab)a={url:"/EM1HWaiQinV2/ruleWebServiceV2/getEaInfo",data:{},success:function(e){var t;0!=e.Result.StatusCode?o.alert(e.Result.FailureMessage):(t="1"==e.Value.openDelivery,e=e.Value.checkKXEa,s.orderDelivery=t,CRM._cache.orderDelivery=t,s.isKXea=e,r&&r.call(s,t))}};else if("smartEstimate-box"===s.tarTab)a={url:"/EM1HWaiQinV2/ruleWebServiceV2/getEaInfo",data:{},success:function(e){0!=e.Result.StatusCode?o.alert(e.Result.FailureMessage):(e="1"==e.Value.openDelivery,s.smartEstimate=e,CRM._cache.smartEstimate=e,r&&r.call(s,e),s.renderSmartEstimateForm({range:0,method:0,expression:"${sale_num}-${stock_num}-${onWayPurchase_num}",recent:30}))}};else{if("area-box"===s.tarTab)return t=this._promiseAjax("/EM1HWaiQinV2/ruleWebServiceV2/getAreaSetting",{}),i=this._promiseAjax("/EM1HWaiQinV2/ruleWebServiceV2/getAreaDepartSelectLevel",{}),void Promise.all([t,i]).then(function(e){var t=e[0],i=t.isOpenAreaConfig,a=t.isOpenPostAttr,n=t.areaRouteDataFix,o=t.DepartConfigList,t=t.personnelApiNameAndConfig,e=e[1].departLevelList,i=1==i,e=(s.area=i,CRM._cache.area=i,r&&r.call(s,i),{departLevelList:e,DepartConfigList:o,isOpenPostAttr:a,areaRouteDataFix:n,personnelApiNameAndConfig:_.values(t)});s.areaConfig=e,i&&s.renderAreaForm(e)});if("fixedCollocation-box"===s.tarTab)return void Promise.resolve(CRM.util.getConfigValue("simple_cpq")).then(function(e){e="1"===e;CRM._cache.fixedCollocationOpenStatus=e,r.call(s,e)}).catch(function(){r.call(s,!1)});"generate-box"===s.tarTab?a={url:"/EM1HWaiQinV2/dataWebService/getDemoCopyCheckBoxInfo",data:{},success:function(e){var t;0!=e.Result.StatusCode?o.alert(e.Result.FailureMessage||$t("网络错误，请重试！")):(t=(e=e.Value).checkBoxInfoList,e=e.timeQuantumStrList,r&&r.call(s,!0),s.renderGenerateForm({checkBoxInfoList:t,timeQuantumStrList:e}))}}:"bill-box"===s.tarTab&&(a={url:"/EM1HNCRM/API/v1/object/SalesStatementsObj/controller/OpenStatus",data:{},success:function(e){var t;e&&e.Result&&0==e.Result.StatusCode?(t=!1,e&&e.Value&&1==e.Value.value&&(t=!0),r&&r.call(s,!0),s.renderBillForm({isOpen:t})):o.alert(e&&e.Result&&e.Result.FailureMessage||$t("网络错误，请重试！"))}})}s.getConfigHandle(a,e)}},setConfig:function(e){var t=this,i={},e=$(e.target),a="";if("spu-box"==t.tarTab)var n=!t.spuSetting,i={url:"/EM1HNCRM/API/v1/object/spu_sku_choose/service/save_spu_selector_config",data:{saveConfig:n},status:n,targetCon:"spu",confirmInfo:n?$t("crm.确定启用选产品吗"):$t("crm.确定关闭选产品吗"),confirmTit:$t("提示"),successText:n?$t("启用成功"):$t("关闭成功")};else if("multiunit-box"===t.tarTab){if("multi_unit_price_book"===(a=e.data("key"))&&!CRM._cache.multiunitStatus)return void o.alert($t("crm.setting.fastsale.error.tip1",null,"请先开启多单位"));var n=C[a];i={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:n.code,tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:n.targetCon,confirmInfo:n.msg,confirmTit:$t("提示")}}else if("switchSpu-box"==t.tarTab){var e=!t.productOpenSpu;i={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"spu",value:e?1:0},status:e,targetCon:"switchSpu",confirmInfo:e?$t("确认开启商品设置吗"):$t("确认关闭商品设置吗"),confirmTit:$t("提示"),successText:e?$t("启用成功"):$t("关闭成功")}}else if("orderDelivery-box"==t.tarTab){n=!t.orderDelivery;if(!t.isKXea)return void o.alert($t("非快消企业不能开启引单配送"));i={url:"/EM1AWaiQinV2/tradeThroughService/openDelivery",data:{},status:n,targetCon:"orderDelivery",confirmInfo:$t("开通引单配送功能之后：<br>1.支持把发货单根据配送员和交货日期分配到配送单中，并显示在外勤列表<br>2.开启之后不可再次关闭"),confirmTit:$t("开启引单配送功能"),successText:$t("启用成功")}}else if("smartEstimate-box"==t.tarTab){e=!t.smartEstimate;i={url:"/EM1AWaiQinV2/tradeThroughService/openDelivery",data:{},status:e,targetCon:"smartEstimate",confirmInfo:e?$t("确认开启智能预估设置吗"):$t("确认关闭智能预估设置吗"),confirmTit:$t("提示"),successText:e?$t("启用成功"):$t("关闭成功")}}else{if("area-box"==t.tarTab)return n=t.areaConfig.departLevelList,e=$("<div></div>"),$("body").append(e),(e=new AreaDepartSetDialog({el:e[0],propsData:{openType:"add",departLevelList:n,DepartConfigList:[]}})).show(),void e.$on("save",function(e){e=Object.assign({},t.areaConfig,e);t.setAreaConfig(e)});"fixedCollocation-box"===t.tarTab&&(i={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"simple_cpq",tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:"fixedCollocation",confirmInfo:$t("确认开启固定搭配"),confirmTit:$t("提示"),successText:$t("启用成功")})}t.setConfigHandle(i,a)},getConfigHandle:function(t,e){var i=this;i._getAjax&&(i._getAjax.abort(),i._getAjax=null),i._getAjax=o.FHHApi({url:t.url,data:t.data||"",success:function(e){0===e.Result.StatusCode?t.success(e):o.alert(e.Result.FailureMessage)},complete:function(){i._getAjax=null}},{errorAlertModel:1,submitSelector:e})},setConfigHandle:function(i,a){var n=this,e=null,e=o.confirm(i.confirmInfo,i.confirmTit,function(){o.FHHApi({url:i.url,data:i.data,success:function(e){var t;0==e.Result.StatusCode?"fixedCollocation"!=i.targetCon||e.Value.success?"multipleUnit"!=i.targetCon||e.Value.success?(o.remind(1,i.successText||$t("启用成功")),t={},"spu"===i.targetCon&&(n.spuSetting=i.status,CRM._cache.spuStatus=i.status,t={multi_spec_display_style:n.mobileSpec}),"multipleUnit"===i.targetCon&&(CRM._cache[C[a].cache]=i.status),n.renderTpl(i.status,t),"switchSpu"===i.targetCon&&(CRM._cache.productOpenSpu=i.status,n.productOpenSpu=i.status,n.render("switchSpu-box",i.status)),"orderDelivery"===i.targetCon&&(CRM._cache.orderDelivery=i.status,n.orderDelivery=i.status,n.render("orderDelivery-box",i.status)),"fixedCollocation"===i.targetCon&&(CRM._cache.fixedCollocationOpenStatus=i.status,n.renderTpl(i.status)),CRM.control.refreshAside(),n.initProduct()):o.alert(e.Value.errMessage):CRM.util.alert(e.Value.errMessage):o.alert(e.Result.FailureMessage)},complete:function(){e.hide(),n.isSetting=!1}},{errorAlertModel:1,submitSelector:e.$(".b-g-btn")})})},initProduct:function(){o.FHHApi({url:"/EM1HNCRM/API/v1/object/pricebook_standard/service/check_or_init_standard_pricebook",data:{}},{errorAlertModel:1})},showTip:function(e){var t,i=this;"multiunit-box"==i.tarTab?"multiunitStatus"===(t=$(e.target).data("key"))?o.alert($t("多单位开启无法再关闭")):"multi_unit_price_book"===t&&o.alert($t("多单位定价开启无法再关闭")):"ladderPrice-box"==i.tarTab?o.alert($t("价格政策开启后无法再关闭")):"availablerange-box"==i.tarTab?o.alert($t("可售范围开启后无法再关闭")):["orderDelivery-box","smartEstimate-box","area-box"].includes(i.tarTab)?o.alert($t("开启后不可关闭")):i.setConfig(e)},setMobileSpec:function(e){var t,i,a=$(e.currentTarget);if(t=e,i=!1,$(".skeleton-icon, .label",t.currentTarget).each(function(e){$.contains($(this).get(0),t.target)&&(i=!0)}),!i)return!1;o.setConfigValue({key:"multi_spec_display_style",value:a.data("flag")}).then(function(){(e=>{if(!$(e.target).hasClass("mn-radio-item"))return a.find(".mn-radio-item").trigger("click");$(".skeleton-entirety .skeleton-icon-selected").removeClass("skeleton-icon-selected"),a.find(".skeleton-icon").addClass("skeleton-icon-selected")})(e),o.remind(1,$t("设置成功"))},function(e){o.alert(e)})},getSpuTabConfig:function(e){var t,i=e.$btn,a=e.renderTpl,n=this;$.when((e=i,t=$.Deferred(),o.FHHApi({url:"/EM1HNCRM/API/v1/object/spu_sku_choose/service/choose_spu",data:"",success:function(e){0===e.Result.StatusCode?t.resolve(e.Value.result):t.reject(e.Result.FailureMessage)}},{errorAlertModel:1,submitSelector:e}),t.promise()),o.getConfigValue("multi_spec_display_style")).then(function(e,t){e="true"==e;n.spuSetting=e,CRM._cache.spuStatus=e,a&&a.call(n,e,{multi_spec_display_style:n.mobileSpec=t})},function(e){o.alert(e)})},getMultiKeyConfig:function(e,i){var a=this,t=e.map(function(e){return e.code});CRM.util.getConfigValues(t).then(function(t){e.forEach(function(e){CRM._cache[e.name]="1"===(_.findWhere(t,{key:e.code})||{}).value}),i&&i.call(a)})},setAreaConfig:function(e){var t=this;CRM.util.showLoading_tip(),t._promiseAjax("/EM1HWaiQinV2/ruleWebServiceV2/saveAreaSetting",e).then(function(e){CRM.util.hideLoading_tip(),0===e.errorCode&&(o.remind(1,$t("设置成功")),t.getConfig(t.renderTpl))},function(e){CRM.util.hideLoading_tip()})},_promiseAjax:function(e,a){return new Promise(function(t,i){o.FHHApi({url:e,data:a,success:function(e){0===e.Result.StatusCode?t(e.Value):(o.alert(e.Result.FailureMessage),i(e))}},{errorAlertModel:1})})},renderSmartEstimateForm:function(e){new f({el:this.$(".smartEstimate-form-wrap")[0],propsData:e})},renderAreaForm:function(e){var t=this,i=new g({el:t.$(".area-form-wrap")[0],propsData:e});i.$on("save",function(e){e=Object.assign({},t.areaConfig,e);t.setAreaConfig(e)}),i.$on("handleOldRouteDataToArea",function(){t._promiseAjax("/EM1HWaiQinV2/ruleWebServiceV2/handleOldRouteDataToArea",{}).then(function(e){0===e.errorCode&&(e=1==e.bizCode?$t("当前企业路线数量较大，为避免影响正常使用，数据会在今晚24点后开始处理，处理结果会在“企信-外勤”中提醒，请注意查看"):$t("已进入后台进行初始化处理，可能需要较长时间，处理结果会在“企信-外勤”中提醒，请注意查看"),i.showProcessTip(e))})})},renderGenerateForm:function(e){var t=this;(this.GenerateForm=new GenerateForm({el:t.$(".open-generate-wrap")[0],propsData:e})).$on("save",function(e){t.generateSubmit(e)})},renderBillForm:function(e){new BillForm({el:this.$(".open-bill-wrap")[0],propsData:e})},generateSubmit:function(e){CRM.util.showLoading_tip();var t=e.copyTypeStringList,i=e.onSuccess;this._promiseAjax("/EM1HWaiQinV2/dataWebService/copyDemoData2Poc",{copyTypeStringList:t,timeString:e.timeString}).then(function(e){CRM.util.hideLoading_tip(),0===e.errorCode?(o.remind(1,$t("crm.manage.fastsale.generate.success_tip")),i&&i()):o.alert(e.message)},function(e){CRM.util.hideLoading_tip()})},destroy:function(){this.GenerateForm&&this.GenerateForm.$off(),this.GenerateForm&&this.GenerateForm.$destroy(),this.GenerateForm&&this.GenerateForm.$el.remove(),this.GenerateForm=null}})});
define("crm-setting/fastsale/fixedCollocation-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("CRM.setting.fastsale.fixedCollocation.intro.line1", null, "开关一旦开启，不能关闭；开启此开关后，不能再开启CPQ；开启此开关后，不能再开启商品；")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("CRM.setting.fastsale.fixedCollocation.intro.line2", null, "开启后，可进行固定搭配促销（如：2个A产品+3个B产品组合为一个优惠套装，该套装满200减20），同时支持“固定搭配不促”（如：2个A产品+3个B产品组合为一个套装）不参与促销活动，该搭配可直接下单；")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("CRM.setting.fastsale.fixedCollocation.intro.line3", null, "已开启CPQ，不允许开启此开关。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("CRM.setting.fastsale.fixedCollocation.intro.line4", null, "注意：创建固定搭配时的【组合销售类型】字段，主要用于区分“整个固定搭配”是否为真实的独立产品，且影响后续的业务。")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("CRM.setting.fastsale.fixedCollocation.open_switch_title", null, "开启固定搭配")) == null ? "" : __t) + '：</label> <div class="switch-sec switch-sec' + ((__t = start ? " on" : " j-set-config") == null ? "" : __t) + '"></div> </div> <div class="important-info" >' + ((__t = $t("CRM.setting.fastsale.fixedCollocation.open_switch_info", null, "开关一旦开启，不可关闭；且此开关与CPQ/商品都互斥，只能二选其一，请谨慎开启。")) == null ? "" : __t) + "</div> ";
            if (start) {
                __p += ' <div class="on-off pp-switch cpq-pricebook-config "> <div class="title">' + ((__t = $t("CRM.setting.fastsale.fixedCollocation.bom.description.title", null, "产品包价格是否包含包内产品价格")) == null ? "" : __t) + '</div> <div class="cpq-calculateConfig-box"></div> </div> ';
            }
        }
        return __p;
    };
});
define("crm-setting/fastsale/generate-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + '</h3> <ul> <li class="tipTxt">' + ((__t = $t("crm.manage.fastsale.generate.desc")) == null ? "" : __t) + '</li> </ul> </div> <div class="open-generate-wrap"></div>';
        }
        return __p;
    };
});
define("crm-setting/fastsale/multiunit-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("多单位设置说明1")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("多单位设置说明2")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("多单位设置说明3")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("单位性质")) == null ? "" : __t) + "" + ((__t = $t("：")) == null ? "" : __t) + '</li> <li class="crm-ml8">4.1 ' + ((__t = $t("不支持自定义选项值")) == null ? "" : __t) + '</li> <li class="crm-ml8">4.2 ' + ((__t = $t("根据单位性质指定赠品单位")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("多单位设置说明4")) == null ? "" : __t) + "</li> <li>6." + ((__t = $t("多单位设置说明5")) == null ? "" : __t) + "</li> <li>7." + ((__t = $t("多单位设置说明6")) == null ? "" : __t) + "</li> <li>8." + ((__t = $t("此开关打开后，不可再关闭")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("开启多单位")) == null ? "" : __t) + '：</label> <div data-key="multiunitStatus" class="spu-switch switch-sec' + ((__t = CRM._cache.multiunitStatus ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("多单位定价说明1")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("多单位定价说明2")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("多单位定价说明3")) == null ? "" : __t) + ':</li> <li class="crm-ml8">3.1 ' + ((__t = $t("多单位定价说明3-1")) == null ? "" : __t) + '</li> <li class="crm-ml8">3.2 ' + ((__t = $t("多单位定价说明3-2")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("多单位定价说明4")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("多单位定价")) == null ? "" : __t) + '：</label> <div data-key="multi_unit_price_book" class="spu-switch switch-sec' + ((__t = CRM._cache.multi_unit_price_book ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="mobile-multi-config-title ' + ((__t = CRM._cache.multiunitStatus ? "" : "displayNone") == null ? "" : __t) + '">' + ((__t = $t("多单位模式配置")) == null ? "" : __t) + '</div> <div class="' + ((__t = CRM._cache.multiunitStatus ? "" : "displayNone") == null ? "" : __t) + '"> <div class="mobile-multi-config"></div> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("多单位模式配置1")) == null ? "" : __t) + '</li> <li class="crm-ml8">1.1' + ((__t = $t("多单位模式配置1.1")) == null ? "" : __t) + '</li> <li class="crm-ml8">1.1' + ((__t = $t("多单位模式配置1.2")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("多单位模式配置2")) == null ? "" : __t) + '</li> <li class="crm-ml8">2.1' + ((__t = $t("多单位模式配置2.1")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("多单位模式配置3")) == null ? "" : __t) + '</li> </ul> </div> </div> <div class="' + ((__t = CRM._cache.multiunitStatus ? "" : "displayNone") == null ? "" : __t) + '"> <div class="mobile-multi-showType"></div> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("单位显示样式1")) == null ? "" : __t) + '</li> <li class="crm-ml8">1.1' + ((__t = $t("单位显示样式1.1")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("下拉展示")) == null ? "" : __t) + ': </li> <li class="crm-ml8">2.1' + ((__t = $t("下拉展示1.1")) == null ? "" : __t) + '</li> <li class="crm-ml8">2.2' + ((__t = $t("下拉展示1.2")) == null ? "" : __t) + '</li> <li class="crm-ml8">2.3' + ((__t = $t("下拉展示1.3")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("单位显示样式3")) == null ? "" : __t) + "</li> </ul> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/fastsale/orderDelivery-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("1.crm.manage.fastsale.orderDelivery.desc1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2.crm.manage.fastsale.orderDelivery.desc2")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3.crm.manage.fastsale.orderDelivery.desc3")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("crm.manage.fastsale.orderDelivery")) == null ? "" : __t) + '：</label> <div class="spu-switch switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="important-info" >' + ((__t = $t("crm.manage.fastsale.orderDelivery.importantInfo")) == null ? "" : __t) + '</div> <div class="orderDelivery-tip" >' + ((__t = $t("crm.manage.fastsale.orderDelivery.tip.head")) == null ? "" : __t) + '<a href="#app/checkin/set/=/param-advanced?tabName=scenes&childScenesTabName=scenes" target="_blank">' + ((__t = $t("crm.manage.fastsale.orderDelivery.tip.a")) == null ? "" : __t) + "</a>，" + ((__t = $t("crm.manage.fastsale.orderDelivery.tip.end")) == null ? "" : __t) + "</div>";
        }
        return __p;
    };
});
define("crm-setting/fastsale/smartEstimate-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("1.crm.manage.fastsale.smartEstimate.desc1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2.crm.manage.fastsale.smartEstimate.desc2")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3.crm.manage.fastsale.smartEstimate.desc3")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("4.crm.manage.fastsale.smartEstimate.desc4")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("5.crm.manage.fastsale.smartEstimate.desc5")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("crm.manage.fastsale.smartEstimate")) == null ? "" : __t) + '：</label> <div class="spu-switch switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> ';
            if (!start) {
                __p += ' <div class="open-smartEstimate-wrap"> <div class="smartEstimate-form-wrap"> </div> <div class="smartEstimate-save-box"> <span class="b-g-btn j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/fastsale/spu-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<!--[ignore-i18n-file]--> <div class="crm-intro"> ';
            var serial = 0, grayMobile = true;
            __p += " <h3>" + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = ++serial) == null ? "" : __t) + "." + ((__t = $t("crm.选商品产品开关描述")) == null ? "" : __t) + "</li> ";
            if (grayMobile) {
                __p += " <li>" + ((__t = ++serial) == null ? "" : __t) + "." + ((__t = $t("开启该开关后，移动端支持选择产品样式配置")) == null ? "" : __t) + "</li> ";
            }
            __p += " <li>" + ((__t = ++serial) == null ? "" : __t) + "." + ((__t = $t("crm.该开关仅作用")) == null ? "" : __t) + "</li> <li>" + ((__t = ++serial) == null ? "" : __t) + "." + ((__t = $t("此开关可根据业务需要反复开启和关闭。")) == null ? "" : __t) + "</li> ";
            var msg = $t("请注意:移动端使用该功能，需升级至{{num}}以上版本", {
                num: 6.5
            });
            __p += ' <li style="color:#ff8837;">' + ((__t = ++serial) == null ? "" : __t) + "." + ((__t = msg) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("基于商品选择产品")) == null ? "" : __t) + '</label> <div class="spu-switch switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> ';
            if (grayMobile) {
                __p += ' <div class="mobile-multi-specification ' + ((__t = start ? "mobile-multi__able" : "") == null ? "" : __t) + '"> <div>' + ((__t = $t("移动端多规格商品选择产品样式配置")) == null ? "" : __t) + '</div> <ul class="skeleton-entirety mn-radio-box"> <li class="one-by-one j-mobile-spec" data-flag="capsule"> <div class="skeleton-icon ' + ((__t = multi_spec_display_style == "capsule" ? "skeleton-icon-selected" : "") == null ? "" : __t) + '"> <div class="skeleton"> <strong></strong> <ul> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> </ul> </div> <div class="skeleton"> <strong></strong> <ul> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> </ul> </div> </div> <div class="label"> <div class="mn-radio-item ' + ((__t = multi_spec_display_style == "capsule" ? "mn-selected" : "") == null ? "" : __t) + '"></div> <div>' + ((__t = $t("逐一选择模式")) == null ? "" : __t) + '</div> </div> <div class="description"> <h6>' + ((__t = $t("推荐属性组合较多、每次下单一个产品只选单一规格值的产品的场景选择")) == null ? "" : __t) + "</h6> <b>" + ((__t = $t("示例")) == null ? "" : __t) + '：</b> <div class="example example-onebyone"> <div class="example-spec"> <div class="spec-title">颜色选择</div> <ul> <li><span>A1</span></li> <li><span>A2</span></li> <li><span>A3</span></li> <li><span>A3.5</span></li> <li><span>A4</span></li> <li><span>A5</span></li> <li><span>B1</span></li> <li><span>B2</span></li> <li><span>B3</span></li> <li><span>B4</span></li> <li><span>B5</span></li> <li><span>C1</span></li> </ul> <div class="spec-title">型号</div> <ul> <li><span>92.12</span></li> <li><span>92.15</span></li> <li><span>98.12</span></li> <li><span>98.15</span></li> </ul> </div> <div class="example-select"> <div class="select-add"> <span>+</span> <span>增加一条</span> </div> <div class="select-title">已选0</div> <div> <span>请选择属性</span> <div class="select-handle"> <span>-</span> <span>0</span> <span>+</span> </div> </div> </div> </div> </div> </li> <li class="flatten j-mobile-spec" data-flag="tiled"> <div class="skeleton-icon ' + ((__t = multi_spec_display_style == "tiled" ? "skeleton-icon-selected" : "") == null ? "" : __t) + '"> <div class="skeleton"> <ul> <li></li> <li></li> <li></li> <li></li> <li></li> </ul> </div> </div> <div class="label"> <div class="mn-radio-item ' + ((__t = multi_spec_display_style == "tiled" ? "mn-selected" : "") == null ? "" : __t) + '"></div> <div>' + ((__t = $t("sku平铺模式")) == null ? "" : __t) + '</div> </div> <div class="description"> <h6>' + ((__t = $t("推荐属性组合较少、每次下单一个产品选多个规格值的产品的场景选择")) == null ? "" : __t) + "</h6> <b>" + ((__t = $t("示例")) == null ? "" : __t) + '：</b> <ul class="example example-sku-flatten"> <li> <span>红色,12.5,1ml</span> <div class="spec-num"> <span>-</span><span>1</span><span>+</span> </div> </li> <li> <span>白色,12.5,1ml</span> <div class="spec-num"> <span>-</span><span>1</span><span>+</span> </div> </li> <li> <span>蓝色,14,1ml</span> <div class="spec-num"> <span>-</span><span>1</span><span>+</span> </div> </li> <li> <span>红色,14,2.5ml</span> <div class="spec-num"> <span>-</span><span>1</span><span>+</span> </div> </li> </ul> </div> </li> <li class="select-directly j-mobile-spec" data-flag="last_level_tiled"> <div class="skeleton-icon ' + ((__t = multi_spec_display_style == "last_level_tiled" ? "skeleton-icon-selected" : "") == null ? "" : __t) + '"> <div class="skeleton"> <strong></strong> <ul> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> <li></li> </ul> <ul> <li></li> <li></li> <li></li> </ul> </div> </div> <div class="label"> <div class="mn-radio-item ' + ((__t = multi_spec_display_style == "last_level_tiled" ? "mn-selected" : "") == null ? "" : __t) + '"></div> <div>' + ((__t = $t("直接多选sku模式")) == null ? "" : __t) + '</div> </div> <div class="description"> <h6>' + ((__t = $t("推荐属性组合较少、每次下单一个产品在最末级规格里选多个规格值的产品的场景选择")) == null ? "" : __t) + "</h6> <b>" + ((__t = $t("示例")) == null ? "" : __t) + '：</b> <div class="example example-directly"> <div class="example-spec"> <div class="spec-title">颜色选择</div> <ul> <li><span>A1</span></li> <li><span>A2</span></li> <li><span>A3</span></li> <li><span>A3.5</span></li> <li><span>A4</span></li> <li><span>A5</span></li> <li><span>B1</span></li> <li><span>B2</span></li> <li><span>B3</span></li> <li><span>B4</span></li> <li><span>B5</span></li> <li><span>C1</span></li> </ul> <div class="spec-title">型号</div> <ul> <li><span>92.12</span></li> <li><span>92.15</span></li> <li><span>98.12</span></li> <li><span>98.15</span></li> </ul> </div> <ul class="example-sku-flatten"> <li> <span>1ml</span> <div class="spec-num"> <span>-</span><span>1</span><span>+</span> </div> </li> <li> <span>1.5ml</span> <div class="spec-num"> <span>-</span><span>1</span><span>+</span> </div> </li> <li> <span>2.0ml</span> <div class="spec-num"> <span>-</span><span>1</span><span>+</span> </div> </li> </ul> </div> </div> </li> </ul> </div> ';
            }
        }
        return __p;
    };
});
define("crm-setting/fastsale/switchSpu-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("1.开关默认关闭，商品、规格&规格值对象隐藏，直接在产品对象下维护产品数，且选择产品页面，无选择商品页面，直接选择产品，即开关“选择产品设置”无法打开")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("2.此开关可以从默认的关闭状态修改为开启状态，但开启后不可再关闭")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3.线上企业，如不存在多规格产品，可以关闭此开关，系统会将商品上的字段“标签（原商品标签）、是否多单位、批次与序列号管理“的值自动赋值到产品对象，但关闭后，不可再开启")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("4.线上企业，如关闭此开关，系统会判断商品上是否存在自定义字段，商品对象被查找关联，字段被引用等，均需要调整现有关联到所需产品对象之后，才可关闭，系统不做自动处理")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("5.如企业有OpenAPI的对接，请做好接口的升级后，开启或关闭此开关")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("商品设置")) == null ? "" : __t) + '：</label> <div class="spu-switch switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="important-info" >' + ((__t = $t("开启或关闭的操作，只可操作一次，请慎重操作")) == null ? "" : __t) + "</div>";
        }
        return __p;
    };
});
define("crm-setting/fastsale/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("快消业务插件")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-tab"> ';
            if (isGrayProduct) {
                __p += '<span data-render="switchSpu-box" class="j-switch-tab">' + ((__t = $t("商品设置")) == null ? "" : __t) + "</span>";
            }
            __p += " ";
            if (isSpuOpen) {
                __p += '<span data-render="spu-box" class="j-switch-tab">' + ((__t = $t("选择产品设置")) == null ? "" : __t) + "</span>";
            }
            __p += " ";
            if (isMulOpen) {
                __p += '<span data-render="multiunit-box" class="j-switch-tab ">' + ((__t = $t("多单位设置")) == null ? "" : __t) + "</span>";
            }
            __p += " ";
            if (isJunLeBao) {
                __p += ' <span data-render="orderDelivery-box" class="j-switch-tab ">' + ((__t = $t("crm.manage.fastsale.leadDeliverySetting.tab")) == null ? "" : __t) + "</span> ";
            }
            __p += ' <!-- <span data-render="smartEstimate-box" class="j-switch-tab ">' + ((__t = $t("crm.manage.fastsale.smartEstimateSetting.tab")) == null ? "" : __t) + "</span> --> ";
            if (isAreaOpen) {
                __p += ' <span data-render="area-box" class="j-switch-tab ">' + ((__t = $t("crm.manage.fastsale.area.tab")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (isFixedCollocation) {
                __p += ' <span data-render="fixedCollocation-box" class="j-switch-tab ">' + ((__t = $t("CRM.setting.fastsale.fixedCollocation.tab", null, "固定搭配")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (isBatchGenerate) {
                __p += ' <span data-render="generate-box" class="j-switch-tab ">' + ((__t = $t("crm.manage.fastsale.batch_generate.tab")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (isOpenSalesStatements) {
                __p += ' <span data-render="bill-box" class="j-switch-tab ">' + ((__t = $t("crm.manage.fastsale.reconciliation_manage.tab")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </div> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="tab-con"> <div class="item render-content"> </div> </div> </div> </div>';
        }
        return __p;
    };
});