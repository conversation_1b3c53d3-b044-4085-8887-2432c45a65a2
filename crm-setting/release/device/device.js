define("crm-setting/device/device",["base-modules/utils","crm-modules/common/util","crm-widget/table/table","./editbusinesstype/editbusinesstype","./tpl-html"],function(e,t,r){e("base-modules/utils");var i=e("crm-modules/common/util"),n=e("crm-widget/table/table"),s=e("./editbusinesstype/editbusinesstype");return Backbone.View.extend({template:e("./tpl-html"),initialize:function(e){this.setElement(e.wrapper)},events:{"click .j-switch":"switch"},render:function(){var t=this;this.queryDeviceConfig(function(e){t.renderTpl({on:e.deviceStatus})})},renderTpl:function(e){this.$el.html(this.template(e))},showTip:function(e){/<\w+>/g.test(e)?i.alert(e,null,{className:"crm-c-dialog crm-c-dialog-alert crm-s-device-dialog"}):i.alert(e)},reloadHandle:function(e){this.render()},_doLog:function(e){CRM.util.uploadLog("crmsetting","s-rule",{eventId:{delivery:"ondeliverynoteobj",stock:"onstockobj"}[e],eventType:"cl"})},switch:function(e){var t,r=this,e=$(e.target);e.hasClass("state-requesting")||(e=e.attr("data-name"),r._doLog(e),t=i.confirm($t("启用设备初始化后将无法停用确认启用吗"),$t("提示"),function(){t.hide(),r.initDevice({})}))},initDevice:function(e){var r=this;i.FHHApi({url:"/EM1AESERVICE/DeviceService/initialize",data:{status:1},success:function(e){e.Value&&(e.Value,"C120060000"==e.Value.errorCode)||(e.Value&&"C320060012"==e.Value.errorCode?r.showTip($t("启用失败客户设备通已经初始化")):r.showTip($t("启用失败请稍后重试或联系纷享客服")))},complete:function(e,t){r.reloadHandle()}},{submitSelector:r.$el.find(".j-switch"),errorAlertModel:1})},queryDeviceConfig:function(t){var r=this;i.FHHApi({url:"/EM1AESERVICE/DeviceService/queryDeviceConfig",data:{},success:function(e){e.Value&&"C120060000"==e.Value.errorCode?(t&&t(e.Value.data),1==e.Value.data.deviceStatus&&(r.workOrderRecordTypes=e.Value.data.workOrderRecordTypes||[],r.renderWorkOrderTable(e.Value.data.workOrderRecordTypeAppMappings))):i.remind(3,$t("获取状态失败"))}},{errorAlertModel:1})},renderWorkOrderTable:function(e){var i=this,t=[{title:$t("设备通应用"),data:"appType",render:function(e){return 1==e?$t("设备查询"):$t("设备通")}},{title:$t("互联类型"),data:"linkType",render:function(e){return 1==e?$t("企业互联"):$t("客户互联")}},{title:$t("服务工单业务类型"),data:"recordType",render:function(e){return e?i.getNameByType(e):'<span style="color: #999;">'+$t("未设置")+"</span>"}},{title:$t("操作"),data:"operate",render:function(e,t,r){return['<div class="table-operate">','\t<a class="j-edit">'+$t("配置")+"</a>","<div/>"].join("")}}],t=(i.workOrderTable&&i.workOrderTable.destroy(),new n({$el:i.$(".work-order_table"),className:"crm-table",showMultiple:!1,openStart:!0,doStatic:!0,showPage:!1,columns:t}));t.on("trclick",function(e,t,r){r.hasClass("j-edit")&&i.onEdit(e)}),t.render(),t.doStaticData(e),i.workOrderTable=t},onEdit:function(e){var t=this;t.EditBusinessTypeWidget&&(t.EditBusinessTypeWidget.destroy(),t.EditBusinessTypeWidget=null),t.EditBusinessTypeWidget=new s({title:$t("服务工单业务类型"),type:"edit"}),t.EditBusinessTypeWidget.on("success",function(){t.render()}),t.EditBusinessTypeWidget.show({data:e,types:t.workOrderRecordTypes})},getNameByType:function(t){var e=_.filter(this.workOrderRecordTypes,function(e){return e.recordType==t});return e[0]?e[0].recordName:t}})});
define("crm-setting/device/editbusinesstype/editbusinesstype",["crm-modules/common/util","crm-widget/dialog/dialog","../template/edit-business-type-html"],function(e,t,i){var s=e("crm-modules/common/util"),r=e("crm-widget/dialog/dialog"),n=e("../template/edit-business-type-html"),a=r.extend({attrs:{title:$t("服务工单业务类型"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-device"},events:{"click .mn-radio-item":"onSet","click .label":"onLabel","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=a.superclass.show.call(this);return this.set(e),this.val=this.get("data").recordType,this.setContent(n({val:this.val,types:this.get("types")})),t},onSubmit:function(){this.val?this.submit({workOrderRecordTypeMappings:[{appType:this.get("data").appType,linkType:this.get("data").linkType,recordType:this.val}]}):s.remind(3,$t("请选择服务工单业务类型！"))},submit:function(e){var t=this;s.FHHApi({url:"/EM1AESERVICE/DeviceService/saveDeviceWorkOrderRecordTypeMapping",data:e,success:function(e){e.Value&&"C120060000"==e.Value.errorCode?(t.trigger("success"),s.remind(1,$t("操作成功！")),t.hide()):s.remind(3,$t("操作失败！"))}},{errorAlertModel:1})},onSet:function(e){e=$(e.currentTarget);if(e.hasClass("mn-selected"))return!1;this.val=e.attr("data-value")},onLabel:function(e){$(e.currentTarget).closest("p").find(".mn-radio-item").trigger("click")},hide:function(){this.destroy()},destroy:function(){a.superclass.destroy.call(this)}});i.exports=a});
define("crm-setting/device/template/edit-business-type-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form mn-radio-box"> ';
            _.each(types, function(item) {
                __p += ' <p style="margin: 5px 0;"> <span class="mn-radio-item ' + ((__t = val == item.recordType ? "mn-selected" : "") == null ? "" : __t) + '" data-value="' + ((__t = item.recordType) == null ? "" : __t) + '"></span> <span class="label" style="margin-left:10px; cursor:pointer;">' + ((__t = item.recordName) == null ? "" : __t) + "</span> </p> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/device/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("设备通")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con crm-scroll"> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ul> <li>1." + ((__t = $t("crm.设备通说明")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.开启设备通后说明")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.开启设备通后其他说明")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("【设备通】功能一旦开启将不可关闭。")) == null ? "" : __t) + '</li> </ul> </div> <div class="so-actions"> ';
            if (obj.errCode) {
                __p += ' <p> <span class="so-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span> <a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + "</a> </p> ";
            } else {
                __p += ' <div class="so-action"> <label class="so-action_label">' + ((__t = $t("设备通")) == null ? "" : __t) + "</label> ";
                if (obj.on != 1) {
                    __p += ' <div class="so-action_oprate"> <button type="button" class="crm-btn crm-btn-primary j-switch">' + ((__t = $t("启用")) == null ? "" : __t) + "</button> </div> ";
                } else {
                    __p += ' <div class="so-action_display"> <span class="so-action_tick_icon"></span>' + ((__t = $t("已开启")) == null ? "" : __t) + "</div> ";
                }
                __p += " </div> ";
                if (obj.on == 1) {
                    __p += ' <div class="work-order"> <label class="so-action_label">' + ((__t = $t("服务工单设置")) == null ? "" : __t) + '</label> <div class="work-order_bd"> <div class="work-order_table"></div> <p style="color: #999;">' + ((__t = $t("设置两个应用中，设备档案内提交工单的业务类型")) == null ? "" : __t) + "</p> </div> </div> ";
                }
                __p += " ";
                if (obj.deliveryNote) {
                    __p += ' <p class="so-action_intro">' + ((__t = $t("发货单已开启建议先去后台设置")) == null ? "" : __t) + '<a href="' + ((__t = obj.deliveryNoteUrl) == null ? "" : __t) + '" target="_blank">' + ((__t = $t("crm.发货单")) == null ? "" : __t) + "</a>" + ((__t = $t("对象的字段布局等信息。")) == null ? "" : __t) + "</p> ";
                }
                __p += " ";
            }
            __p += " </div> </div> </div>";
        }
        return __p;
    };
});