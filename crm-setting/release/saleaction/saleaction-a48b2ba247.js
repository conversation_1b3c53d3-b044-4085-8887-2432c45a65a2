define("crm-setting/saleaction/addedite/addedite",["crm-modules/common/util","base-modules/ui/scrollbar/scrollbar","crm-widget/selector/selector","crm-widget/select/select","./stage/stage","./template/tpl-html","./template/step-html","./template/nav-html","../utils/migrationdatatransfer","./template/saveconfirm-html"],function(e,t,a){var o=e("crm-modules/common/util"),s=e("base-modules/ui/scrollbar/scrollbar"),i=e("crm-widget/selector/selector"),l=e("crm-widget/select/select"),n=e("./stage/stage"),r=e("./template/tpl-html"),d=e("./template/step-html"),c=e("./template/nav-html"),g=e("../utils/migrationdatatransfer"),u=e("./template/saveconfirm-html"),e=Backbone.View.extend({options:{className:"b-g-crm crm-s-saleaction",tagName:"div"},status:"edit",initialize:function(){var e=this;$("body").append(e.$el),e._bindEvents(),e.delStages=[],e._postid=o.getTraceId(),e._getDescribeLayoutMigration(),e.requestId=o.getUUIdAsMiniProgram(),e.data={isNotYunZhiJia:CRM.control.isNotYunZhiJia,SaleActionID:"",Name:"",Description:"",CircleIDs:[],IsAllowOldData:!0,IsAllowTradeIfWin:!1,CheckFieldIfWin:!1,IsAllowTrade:!1,AllowTradeStageOrder:0,Stages:[],PreStages:[],EndStages:[],NextStages:[],changeNum:0},e.stageData={SaleActionStageID:"",Description:"",Name:"",StageFlag:1,CustomerFieldInfos:[],OppoFieldInfos:[],UDFieldInfos:[],AsIsThatDayRemind:!0,AsThatRemainDays:10,AsIsTimeoutRemind:!1,AsRemainDays:15,IsLeaderConfirm:!1,IsFinishByContacts:!1,ContactCount:1,IsTimeoutRemind:!0,RemainDays:10,Weight:"",changeNum:0,isNew:!0,isValid:!1},e.preStageData=[$.extend(!0,{},e.stageData,{Type:1,Name:$t("验证客户"),Weight:10}),$.extend(!0,{},e.stageData,{Type:1,Name:$t("需求确定"),Weight:30}),$.extend(!0,{},e.stageData,{Type:1,Name:$t("方案")+"/"+$t("报价"),Weight:60}),$.extend(!0,{},e.stageData,{Type:1,Name:$t("谈判审核"),Weight:80})],e.endStageData=[$.extend(!0,{},e.stageData,{Type:1,StageFlag:2,Name:$t("赢单"),Weight:100}),$.extend(!0,{},e.stageData,{Type:1,StageFlag:4,Name:$t("输单"),Weight:0}),$.extend(!0,{},e.stageData,{Type:1,StageFlag:3,Name:$t("无效"),Weight:0})]},show:function(e){var t=this;t.status=e?"edit":"add",t._setData(e),t._defaultName=t.data.Name,t.data.status=t.status,t.$el.html(r(t.data)),t._renderStep(),t._renderNav(),t._renderCircle(),t._renderCompleteSelect(),t._scrollbar=new s(t.$(".scroll-el"))},_setData:function(e){var t=this;"edit"==t.status?t.data=_.extend({PreStages:[],NextStages:[],EndStages:[],changeNum:0,IsAllowTrade:!1,AllowTradeStageOrder:0,isNotYunZhiJia:CRM.control.isNotYunZhiJia},e||{},{IsAllowOldData:!0}):(t.data.Stages=t.data.Stages.concat(t.preStageData),t.data.Stages=t.data.Stages.concat(t.endStageData)),_.each(t.data.Stages,function(e){e.Weight=e.Weight,e.changeNum=0,e.StageFlag=e.StageFlag||1,e.CustomerFieldInfos=t._parseRelateData(e.CustomerDescribe,e.CustomerIsRequiredDescribe),e.OppoFieldInfos=t._parseRelateData(e.OpportunityDescribe,e.OpportunityIsRequiredDescribe),(1==e.Type?e.StageFlag&&1!=e.StageFlag?t.data.EndStages:t.data.PreStages:t.data.NextStages).push(e)}),0==t.data.PreStages.length&&t.data.PreStages.push($.extend(!0,{Type:1,StageFlag:1},t.stageData)),0==t.data.EndStages.length&&(t.data.EndStages=t.data.EndStages.concat(t.endStageData))},_renderStep:function(){var e=this,t=e.$(".pre-step"),a=e.$(".next-step"),s=e.$(".end-step");e._renderStepByParam(e.data.PreStages,t,1),e._renderStepByParam(e.data.NextStages,a,2),e._renderStepByParam(e.data.EndStages,s,3)},_renderStepByParam:function(a,e,t){var s=this,i="";0<a.length?_.each(a,function(e){i+=s._createStep(e)}):(e.closest(".step-box").find("h4").hide(),i='<span class="add-step-btn">'+$t("添加售后阶段")+"</span>"),e.html(i),s._setStepNum(e,t),s._toggleBtn(e,t),3==t&&$(".step-status-select",e).each(function(e,t){s._initEndStatus($(t),a[e])})},_initEndStatus:function(e,t){var n=this,t=new l({$wrap:e,options:[{name:$t("赢单"),value:2},{name:$t("输单"),value:4},{name:$t("无效"),value:3}],defaultValue:t.StageFlag,disabled:""!=t.SaleActionStageID,zIndex:2e3});t.on("change",function(e,t,a){var a=a.get("$wrap"),s=a.attr("data-val"),i=a.closest(".b-item"),s=n.$(".step-status-select[data-val="+s+"]");o.hideErrmsg(s),s.removeClass("error"),a.attr("data-val",t.value),$(".step-may",i).val(2==t.value?100:0),n._changeData(i.index(),3,{StageFlag:t.value})}),e[0].select=t},_renderNav:function(){var e=this,t=e.$(".pre-nav"),a=e.$(".next-nav");e._renderNavByParam(e.data.PreStages,t),e._renderNavByParam(e.data.NextStages,a)},_renderNavByParam:function(e,t){var a=this,s="";_.each(e,function(e){s+=a._createNav(e)}),t.html(s),a._setStepNum(t),a._toggleNav(t)},_createStep:function(e){return d(e)},_createNav:function(e){return c(e)},_setStepNum:function(e,t){e=e||this.$el,$(".step-num",e).each(function(e,t){$(t).html(e+1)})},_toggleBtn:function(e,t){var a=$(".del-step",e),e=$(".add-step",e),s=3==t?3:20;a.toggle(a.length>(1==t||3==t?1:0)),e.hide(),e.length<s&&e.eq(e.length-1).show()},_toggleNav:function(e){var t=$(".no-name",e),a=$(".n-item",e);e.toggle(t.length<a.length)},_renderCircle:function(){var t=this,e=t.data.CircleIDs;t._circle=new i({$wrap:t.$(".circle-select"),group:!0,zIndex:1300,label:$t("crm.添加部门"),defaultSelectedItems:{group:e}}),t._circle.on("change",function(){var e=t._circle.getValue("group")||[];t.data.CircleIDs=e,t.data.changeNum+=1})},_renderCompleteSelect:function(){var a=this;a._select=new l({$wrap:a.$(".j-complete"),options:[],zIndex:2e3}),a._select.on("change",function(e,t){t&&(a.data.AllowTradeStageOrder=t.value,a._allowTradeId=t.id,a.data.changeNum+=1)}),"edit"==a.status&&a.data.IsAllowTrade&&(a._allowTradeId=a.data.PreStages[a.data.AllowTradeStageOrder-1||0].SaleActionStageID),a._resetComplateSelect()},_resetComplateSelect:function(){var e=this,a=[],t=null;_.each(e.data.PreStages,function(e,t){t+=1;a.push({name:$t("第{{order}}阶段",{order:t}),value:t,id:e.SaleActionStageID||""})}),e._select.resetOptions(a),t=e._allowTradeId?_.findWhere(a,{id:e._allowTradeId}):_.findWhere(a,{value:e.data.AllowTradeStageOrder}),e.data.AllowTradeStageOrder=t?t.value:1,e._select.setValue(t?t.value:1,!0)},_checkSaleActionMigration:function(e,t){e&&""!=e?o.FHHApi({url:"/EM1HNCRM/API/v1/object/sale_action/service/checkSaleActionStageIsApply",data:{saleActionStageIds:[e]},success:function(e){0==e.Result.StatusCode?e.Value.IsApply?o.alert($t("有商机正在使用该阶段无法删除")):t&&t():o.alert(e.Result.FailureMessage)}},{errorAlertModel:1}):t&&t()},_validBase:function(){var r=this,d=0,e=r.$(".j-base-name");return""==$.trim(e.val())&&(d+=1,o.showErrmsg(e,$t("请填写销售流程名称"))),r.$(".step-box-pre .step-name").each(function(e,t){var a=$(t),s="",i=a.closest(".b-item"),n=i.find(".step-status-select").attr("data-val"),l=[$t("赢单"),$t("无效"),$t("输单")][n-2],n=r.$('.step-status-select[data-val="'+n+'"]'),i=$.trim(i.find(".step-may").val());""==$.trim(a.val())&&(s+=$t("请填写名称")),""==i&&(s=s?s+$t("与赢率"):$t("请填写赢率")),(i<0||100<i)&&(s+=$t("赢率为0-100之间的整数。")),1<n.length&&(n.addClass("error"),s=s?s+l+$t("状态不能重复"):l+$t("状态不能重复")),""!=s&&(d+=1,o.showErrmsg($(t),s))}),r.$(".next-step .step-name").each(function(e,t){var a=$(t),s="";""==$.trim(a.val())&&(s+=$t("请填写名称")),""!=s&&(d+=1,o.showErrmsg($(t),s))}),0==d},_hideError:function(e){e=$(e.target);o.hideErrmsg(e)},events:{"click .j-nav .item":"_onNav","click .add-step":"_onAddStep","click .del-step":"_onDelStep","click .add-step-btn":"_onAddNextStep","click .nav-box .n-item":"_onStepNav","click .default-nav .n-item":"_onStopPropagation","click .b-g-btn-cancel":"_onOut","click .add-tit .b-g-btn":"_saveHandle","click .r-item .mn-checkbox-item":"_onCheckRule","focus .base-item .b-g-ipt":"_hideError","click .complete-step-box .mn-checkbox-item":"_onAllowTrade"},_onStopPropagation:function(e){return!1},_bindEvents:function(){var e=this;o.fixInputEvent(".step-name",$.proxy(e._onChangeStepName,e),e.$el),o.fixInputEvent(".j-base-name",$.proxy(e._onChangeName,e),e.$el),o.onlyAllowNum(".step-may",e.$el,3,2,!1),o.fixInputEvent(".step-may",$.proxy(e._onChangeStepWeight,e),e.$el)},_onNav:function(e){var e=$(e.currentTarget),t=e.index();if(this._validBase()){if(0==$(".n-item",e).length&&e.hasClass("next-step-nav"))return o.alert($t("暂未设置售后阶段")),!1;this._validStage()&&(this.$(".n-item").removeClass("active"),e.hasClass("step-nav")?$(".n-item",e).eq(0).trigger("click"):(e.addClass("active").siblings().removeClass("active"),this.$(".j-con .item").eq(t).show().siblings().hide()))}},_onAddStep:function(e){var t=this,e=$(e.target).closest(".b-item"),a=e.closest(".j-step"),s=a.attr("data-type"),i=e.index(),n=$.extend(!0,{},t.stageData,{Type:s}),l=null;3==s&&(n=_.extend(n,{Type:1,StageFlag:2})),l=$(t._createStep(n)),e.after(l),3==s&&t._initEndStatus(l.find(".step-status-select"),n),t._setStepNum(a,s),t._toggleBtn(a,s),t._addNav(i,s),t._addData(i,s)},_onAddNextStep:function(e){var t=this,e=$(e.target).closest(".j-step");e.html(t._createStep($.extend(!0,{Type:2},t.stageData))),t._setStepNum(e),t._toggleBtn(e),t._addNav(0,2),t._addData(0,2),e.closest(".step-box").find("h4").show()},_onDelStep:function(e){var t=this,a=$(e.target).closest(".b-item"),s=a.closest(".j-step"),i=s.attr("data-type"),n=a.index();t._checkSaleActionMigration(a.attr("data-id"),function(){var e;3==i&&(e=a.find(".step-status-select"))[0].select&&e[0].select.destroy(),a.remove(),t._setStepNum(s,i),t._toggleBtn(s,i),t._delNav(n,i),t._delData(n,i),0==$(".b-item",s).length&&(s.html('<span class="add-step-btn">'+$t("添加售后阶段")+"</span>"),s.closest(".step-box").find("h4").hide()),t.data.changeNum+=1})},_addData:function(e,t){var a=this,s=["PreStages","NextStages","EndStages"][t-1],i=$.extend(!0,{Type:3==t?1:t},a.stageData);3==t&&(i=_.extend(i,{Type:1,StageFlag:2})),a.data[s].splice(e+1,0,i),a.data.changeNum+=1,"PreStages"==s&&_.each(a.data.EndStages,function(e){e.changeNum+=1}),1==t&&a._resetComplateSelect()},_delData:function(e,t){var a=this,s=["PreStages","NextStages","EndStages"][t-1],i=a.data[s][e];i.SaleActionStageID&&a.delStages.push({EditFlag:3,SaleActionStageID:i.SaleActionStageID}),a.data[s].splice(e,1),"edit"==a.status&&(_.each(a.data[s],function(e){e.changeNum+=1}),"PreStages"==s)&&_.each(a.data.EndStages,function(e){e.changeNum+=1}),1==t&&a._resetComplateSelect()},_addNav:function(e,t){var a=[".pre-nav",".next-nav",".default-nav"][t-1],e=$(".n-item",this.$(a)).eq(e),t=3==t?{Type:1,StageFlag:2}:{};0<e.length?e.after(c($.extend(!0,{},this.stageData,t))):this.$(a).html(c($.extend(!0,{},this.stageData,t))),this._setStepNum(this.$(a))},_delNav:function(e,t){t=[".pre-nav",".next-nav",".default-nav"][t-1];$(".n-item",this.$(t)).eq(e).remove(),this._setStepNum($(t)),this._toggleNav($(t))},_onStepNav:function(e){var t=$(e.currentTarget),a=t.closest(".nav-box"),s=a.closest(".item"),i=this.$(".j-con .item").eq(s.index()),a=a.hasClass("pre-nav")?"PreStages":"NextStages";this._validBase()&&this._validStage()&&(this._stageKey=a,this._initStage(i,this.data[a][t.index()]),this.$(".n-item").removeClass("active"),t.addClass("active"),s.addClass("active").siblings().removeClass("active"),i.show().siblings().hide()),e.stopPropagation()},_initStage:function(e,t){this._stage&&this._stage.destroy(),this._stage=new n({$el:e,data:t})},_validStage:function(e){var t=this;return!t._stage||!!t._stage.valid()&&(t._setStageData(),e||(t._stage.destroy(),t._stage=null),!0)},_setStageData:function(){var e=this,t=e.$(".n-item.active").index(),a=e._stage.getValue();0<a.changeNum&&(e.data.changeNum+=1),e.data[e._stageKey][t]=a},_onChangeStepName:function(s){var i=this;i._timer&&(clearTimeout(i._timer),i._timer=null),i._timer=setTimeout(function(){var e=$(s.target),t=e.closest(".b-item"),a=t.closest(".j-step").attr("data-type"),t=t.index(),e=$.trim(e.val());i._changeNav(t,a,e),i._changeData(t,a,{Name:e})},80)},_onChangeStepWeight:function(s){var i=this;i._timer&&(clearTimeout(i._timer),i._timer=null),i._timer=setTimeout(function(){var e=$(s.target),t=e.closest(".b-item"),a=t.closest(".j-step").attr("data-type"),t=t.index(),e=e.val();i._changeData(t,a,{Weight:e})},80)},_onChangeName:function(s){var i=this;i._timer&&(clearTimeout(i._timer),i._timer=null),i._timer=setTimeout(function(){var e=$(s.target),t=e.attr("data-default"),a=$.trim(e.val());t!=(i.data.Name=a)?e[0].hasChange||(i.data.changeNum+=1,e[0].hasChange=!0):(--i.data.changeNum,e[0].hasChange=!1)},80)},_changeNav:function(e,t,a){t=this.$([".pre-nav",".next-nav",".default-nav"][t-1]),e=$(".n-item",t).eq(e);$(".nav-name",e).html(a),e.toggleClass("no-name",""==a),this._toggleNav(t)},_changeData:function(e,t,a){t=this.data[["PreStages","NextStages","EndStages"][t-1]][e];t.SaleActionStageID&&(t.changeNum+=1),_.extend(t,a),this.data.changeNum+=1},_onCheckRule:function(e){var e=$(e.target),t=e.attr("data-key"),a=e.attr("data-default");return e.toggleClass("mn-selected"),this.data[t]=e.hasClass("mn-selected"),a!=(e.hasClass("mn-selected")?1:2)?this.data.changeNum+=1:--this.data.changeNum,!1},_onAllowTrade:function(e){var t=this,e=$(e.target),a=e.attr("data-key"),s=e.attr("data-default");return e.toggleClass("mn-selected"),t.data[a]=e.hasClass("mn-selected"),s!=(e.hasClass("mn-selected")?1:2)?t.data.changeNum+=1:--t.data.changeNum,t.$(".complete-step-box").toggleClass("disabled",!t.data[a]),!1},_onOut:function(){var e=this,t=null;if(!(0<e.data.changeNum))return e.trigger("refresh","out"),e.hide(),!1;t=o.confirm($t("确定舍弃已填写内容吗"),$t("提示"),function(){e.trigger("refresh","out"),t.hide(),e.hide()})},_validAllData:function(){return!(!this._checkPreStages()||!this._checkNextStages())},_checkScope:function(){return!this.data.IsAllowTradeIfWin||!this.data.IsAllowTrade||this._select.getValue()!=this.data.PreStages.length||(o.alert($t("crm.请勿同时选择某两项内容")),!1)},_checkPreStages:function(){this.data;return!0},_checkNextStages:function(){var e=this.data.NextStages;return 0==e.length||!(e.length<2&&(o.alert($t("至少创建两个售后阶段")),1))},_getAddStagesData:function(t){var e=this.data,i=[],n=e.PreStages.length+e.EndStages.length,e=[].concat(e.PreStages,e.EndStages,e.NextStages),e=_.map(e,function(e){return $.extend(!0,{},e)});return _.each(e,function(a,e){var s=a.CustomerFieldInfos||[];a.EditFlag=1,"Add"==t&&(a.SaleActionStageID=""),a.SaleStageFieldRelationRequests=_.map(s,function(e,t){return{EditFlag:1,UserDefinedFieldID:e.UserDefinedFieldID,IsNotNull:e.IsNotNull,FieldOrder:e.FieldOrder||+t,OwnerType:2,FieldApiName:e.FieldApiName}}),_.each(a.OppoFieldInfos||[],function(e,t){a.SaleStageFieldRelationRequests.push({EditFlag:1,UserDefinedFieldID:e.UserDefinedFieldID,IsNotNull:e.IsNotNull,FieldOrder:e.FieldOrder||s.length+ +t,OwnerType:8,FieldApiName:e.FieldApiName})}),a.UserDefinedFieldRequests=_.map(a.UDFieldInfos||[],function(e){return e.EditFlag=1,_.each(e.EnumDetails,function(e,t){e.EditFlag=1,e.ItemCode=e.ItemCode||0,_.each(e.Children,function(e,t){e.EditFlag=1,e.ItemCode=e.ItemCode||0})}),e.ModifyEnums=e.EnumDetails,e}),a.UDFieldInfos=a.CustomerFieldInfos=a.OppoFieldInfos=a.submitData=null,_.each(["ContactCount","AsRemainDays","AsThatRemainDays","RemainDays","Weight"],function(e){a[e]=+a[e]}),a.StageOrder=1==a.Type?e+1:e+1-n,delete a.CustomerDescribe,delete a.CustomerIsRequiredDescribe,delete a.OpportunityDescribe,delete a.OpportunityIsRequiredDescribe,delete a.changeNum,delete a.isValid,delete a.submitData,i.push(a)}),i},_getEditStagesData:function(){var e=this.data,t=[],i=e.PreStages.length+e.EndStages.length,e=[].concat(e.PreStages,e.EndStages,e.NextStages),e=_.map(e,function(e){return $.extend(!0,{},e)});return _.each(e,function(a,e){var s=_.extend({customer:[],oppo:[],fields:[]},a.submitData||{});(a.isNew||0<a.changeNum)&&(a.EditFlag=a.isNew?1:2,a.SaleStageFieldRelationRequests=_.map(s.customer,function(e,t){return{EditFlag:e.EditFlag,UserDefinedFieldID:e.UserDefinedFieldID,IsNotNull:e.IsNotNull,FieldOrder:e.FieldOrder||t,OwnerType:2,FieldApiName:e.FieldApiName}}),_.each(s.oppo||[],function(e,t){a.SaleStageFieldRelationRequests.push({EditFlag:e.EditFlag,UserDefinedFieldID:e.UserDefinedFieldID,IsNotNull:e.IsNotNull,FieldOrder:e.FieldOrder||s.customer.length+t,OwnerType:8,FieldApiName:e.FieldApiName})}),a.UserDefinedFieldRequests=_.map(s.fields,function(e){return e.ModifyEnums=e.ModifyEnums2||e.ModifyEnums,e}),a.UDFieldInfos=a.CustomerFieldInfos=a.OppoFieldInfos=a.submitData=null,_.each(["ContactCount","AsRemainDays","AsThatRemainDays","RemainDays","Weight"],function(e){a[e]=+a[e]}),a.StageOrder=1==a.Type?e+1:e+1-i,delete a.CustomerDescribe,delete a.OpportunityDescribe,delete a.changeNum,delete a.isValid,delete a.submitData,t.push(a))}),t.concat(this.delStages)},_saveHandle:function(){var t,a,s=this;if(s._validBase()&&s._validStage(!0)&&s._checkScope()){if(0==s.data.changeNum&&"edit"==s.status)return s.trigger("refresh","out"),void s.hide();s._validAllData()&&s._checkScope()&&("add"==s.status?s._AddActionMigration():0<(t=s._getEditStagesData()).length?(a=o.confirm(u({Name:s.data.Name}),$t("请选择保存数据的类型"),function(){if(s.data.IsAllowOldData=$(".allow-olddata",a.element).hasClass("mn-selected"),!s.data.IsAllowOldData){var e=$.trim($(".new-sele-name",a.element).val());if(""==e)return void o.alert($t("请填写新销售流程名称")+"!");if(e==s._defaultName)return void o.alert($t("销售流程名称不能重复")+","+$t("请修改销售流程名称"));s.data.Name=e}s.data.IsAllowOldData?(a.hide(),s._AddActionMigration(t,"Edit")):($(".isstop",a.element).hasClass("mn-selected")?s._stopUseStatusMigration({sale_action_id:s.data.SaleActionID,status:"2"}):s.data.SaleActionID="",a.hide(),s._AddActionMigration(!0))},{stopPropagation:!1,className:"crm-s-saleaction"})).on("radio",function(e){e=$(e.currentTarget);$(".old-sale-change",a.element).toggle(2==e.attr("data-val")),a.resetPosition()}):s._AddActionMigration(t,"Edit"))}return!1},_getDescribeLayoutMigration:function(e){var t=this;t.getDescribeAjax=o.FHHApi({url:"/EM1HNCRM/API/v1/object/SaleActionNewObj/controller/DescribeLayout",data:{include_detail_describe:!1,include_layout:!1,apiname:"SaleActionNewObj",layout_type:"add",recordType_apiName:"default__c"},success:function(e){0==e.Result.StatusCode?t.objectValueMigration=e.Value:o.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},_getPostId:function(){var e=this._postid;return e?"?_postid="+e:""},_AddActionMigration:function(t){var e,a=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"Add",s=this,i=s.data;s.createAjax||s.objectValueMigration&&(e=s._getAddStagesData(a),i=g.addAndEditParamMigration(s.objectValueMigration,i,a,e,s.requestId),s.createAjax=o.FHHApi({url:"/EM1HNCRM/API/v1/object/SaleActionNewObj/action/"+a+this._getPostId(),data:i,success:function(e){0==e.Result.StatusCode?(t?s.trigger("refresh","del"):s.trigger("refresh","add"),o.remind($t("".concat("Add"==a?"保存":"编辑","成功"))+"！"),s.hide()):o.alert(e.Result.FailureMessage)},complete:function(){s.createAjax=null}},{submitSelector:$(".add-tit .b-g-btn",s.$el),errorAlertModel:1}))},_stopUseStatusMigration:function(e){o.FHHApi({url:"/EM1HNCRM/API/v1/object/SaleActionNewObj/action/ChangeStatus",data:e,success:function(e){0!=e.Result.StatusCode&&o.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},hide:function(){this.$el.hide(),this.destroy()},_parseRelateData:function(e,t){var a=!!_.isArray(e),s=JSON.parse(t||'""'),i=[];if(a)i=e;else{var n,l=JSON.parse(e||'""');for(n in l)i.push(l[n])}_.each(i,function(e){var t,a;e.EditFlag||(t=e.config&&e.config.attrs&&("false"==e.config.attrs.is_required||0==e.config.attrs.is_required)?0:1,a=[],e.options&&_.each(e.options,function(e){e.not_usable||(e=_.extend(e,{ItemName:e.label}),a.push(e))}),"country"==e.api_name&&(e.label=$t("国家/省/市/区")),_.extend(e,{FieldType:e.type,FieldCaption:e.label,FieldApiName:e.api_name,FieldName:e.api_name,DefaultValue:e.default_value||"",RelationField:e.relation_field||"",IsAllowEditNotNull:t,IsAllowHide:e.is_allowhide||!1,IsWatermark:e.is_watermark||!1,IsNotNull:s[e.api_name]||e.is_required,IsVisible:e.is_visible||!0,UserDefinedFieldID:e._id,EnumDetails:a}))});var r={province:1,city:1,district:1};return i=_.filter(i,function(e){return!r[e.FieldType]})},destroy:function(){this._circle.destroy(),this.$(".step-status-select").each(function(e,t){t.select&&t.select.destroy()}),this._scrollbar&&this._scrollbar.destroy(),this._select.destroy(),this._stage&&this._stage.destroy(),this.undelegateEvents(),this.remove(),this.data=this.delStages=this.stageData=this.options=this.events=null,Backbone.View.prototype.remove.call(this)}});a.exports=e});
define("crm-setting/saleaction/addedite/stage/stage",["crm-modules/common/util","crm-setting/common/fieldmanage/fieldmanage","./template/tpl-html"],function(e,t,i){var s=e("crm-modules/common/util"),a=e("crm-setting/common/fieldmanage/fieldmanage"),n=e("./template/tpl-html"),e=Backbone.View.extend({options:{className:"stage-wrap",tageName:"div",$el:null,data:null},initialize:function(){var e=this;e.options.$el.append(e.$el),e.relateObjPos=2,e._getAllField(function(){e._initFieldMange(),e._renderRule(),e._bindEvents()})},_getAllField:function(e){var t="__saleActionStageRelateObjPos";CRM.get(t)?setTimeout(function(){e&&e()},100):(CRM.set(t,!0),e&&e())},_initFieldMange:function(){var e=this,t=(e.options.data.UDFieldInfos,[16,20,23,24,25,26,27,28,29,30,31]),i=(CRM.control.isYunZhiJia&&t.push(14),e.options.data);e._field=new a({el:e.$el,isRelateObj:!0,relateObjPos:e.relateObjPos,isShowHideSet:!1,isSetRelate:!1,isSaveBtn:!1,addFilter:t,from:3,relateData:{customer:1<=i.CustomerFieldInfos.length?e._parseRelateData(i.CustomerFieldInfos,i.CustomerIsRequiredDescribe):e._parseRelateData(i.CustomerDescribe,i.CustomerIsRequiredDescribe),oppo:1<=i.OppoFieldInfos.length?e._parseRelateData(i.OppoFieldInfos,i.OpportunityIsRequiredDescribe):e._parseRelateData(i.OpportunityDescribe,i.OpportunityIsRequiredDescribe)},fieldData:e.options.data.UDFieldInfos||[]}),e._field.on("add",function(){s.hideErrmsg(e.$(".tit-backinfo .text")),e.$(".tit-backinfo").hide()})},_parseRelateData:function(e,t){var i=!!_.isArray(e),a=JSON.parse(t||'""'),n=[];if(i)n=e;else{var o,s=JSON.parse(e||'""');for(o in s)n.push(s[o])}_.each(n,function(e){var t,i;e.EditFlag||(t=e.config&&e.config.attrs&&("false"==e.config.attrs.is_required||0==e.config.attrs.is_required)?0:1,i=[],e.options&&_.each(e.options,function(e){e.not_usable||(e=_.extend(e,{ItemName:e.label}),i.push(e))}),"country"==e.api_name&&(e.label=$t("国家/省/市/区")),_.extend(e,{FieldType:e.type,FieldCaption:e.label,FieldApiName:e.api_name,FieldName:e.api_name,DefaultValue:e.default_value||"",RelationField:e.relation_field||"",IsAllowEditNotNull:t,IsAllowHide:e.is_allowhide||!1,IsWatermark:e.is_watermark||!1,IsNotNull:a[e.api_name]||e.is_required,IsVisible:e.is_visible||!0,UserDefinedFieldID:e._id,EnumDetails:i}))});var d={province:1,city:1,district:1};return n=_.filter(n,function(e){return!d[e.FieldType]})},_renderRule:function(){this.$(".defined-box").before(n(this.options.data))},_setChange:function(){this.options.data.changeNum+=1},events:{"click .s-item .mn-checkbox-item":"_onCheckRule","focus .stage-rule .b-g-ipt":"_hideError"},_bindEvents:function(){var t=this;s.onlyAllowNum(".j-days",t.$el,!1,99999,$.proxy(t._changeDays,t)),s.fixInputEvent(".j-days",function(e){t._changeDays($(e.target))},t.$el),s.fixInputEvent(".j-desc",function(e){t._changeDays($(e.target))},t.$el)},_onCheckRule:function(e){var t=this,i=$(e.target),a=i.attr("data-key");return i.toggleClass("mn-selected"),t.options.data[a]=i.hasClass("mn-selected"),t._setChange(),t._hideError(e),!1},_changeDays:function(e){var t=e.attr("data-key"),e=e.val();this.options.data[t]=e,this._setChange()},getValue:function(){var e,i=this,t=i._doFieldData();return null!=(e=t.fields)&&e.length&&t.fields.forEach(function(t){var e=i.options.data.UDFieldInfos.find(function(e){return e.UserDefinedFieldID==t.UserDefinedFieldID});e&&(t.is_deleted=e.is_deleted,t.create_time=e.create_time,t.created_by=e.created_by,t.last_modified_by=e.last_modified_by,t.last_modified_time=e.last_modified_time,t.object_describe_api_name=e.object_describe_api_name,t.sale_action_stage_id=e.sale_action_stage_id,t.sale_action_stage_id__relation_ids=e.sale_action_stage_id__relation_ids,t.tenant_id=e.tenant_id,t.version=e.version)}),i.options.data.UDFieldInfos=t.fields,i.options.data.CustomerFieldInfos=t.customer,i.options.data.OppoFieldInfos=t.oppo,i.options.data.submitData=i._field.getSubmitData(),0<i._field.changeNum&&(i.options.data.changeNum+=1),i.options.data},valid:function(){return!(!this._field.getSubmitData()||!this._validRule())&&(this.options.data.isValid=!0)},_validRule:function(){var o=0;this.$(".j-desc");return this.$(".j-days").each(function(e,t){var t=$(t),i=$.trim(t.val()),a=t.closest("p"),n="ContactCount"==t.attr("data-key")?$t("要存在的联系人的个数"):$t("停留提醒天数");$(".mn-checkbox-item",a).hasClass("mn-selected")&&""==i&&(o+=1,s.showErrmsg(t,$t("crm.请填写")+n))}),0==o},_hideError:function(e){e=$(e.target);s.hideErrmsg(e)},_doFieldData:function(){var e=this._field.getSubmitData(!0),t=_.map(e.fields,function(e){return 8!=e.FieldType&&9!=e.FieldType||(e.ModifyEnums=e.ModifyEnums2&&0<e.ModifyEnums2.length?e.ModifyEnums2:e.ModifyEnums,e.ModifyEnums=e.ModifyEnums&&0<e.ModifyEnums.length?e.ModifyEnums:[{ItemName:$t("选项一"),ItemCode:"dfc1"},{ItemName:$t("选项二"),ItemCode:"dfc2"}]),e.FieldCaption=e.FieldCaption||$t("字段名称"),e.EnumDetails=e.ModifyEnums,_.extend({IsAllowEditNotNull:!0,IsAllowEditOption:!0,FieldProperty:2},e)}),i=_.map(e.customer,function(e){return 8!=e.FieldType&&9!=e.FieldType||(e.ModifyEnums=[{ItemName:$t("选项一"),ItemCode:"dfc1"},{ItemName:$t("选项二"),ItemCode:"dfc2"}]),e.EnumDetails=e.ModifyEnums,_.extend({IsAllowEditOption:!1},e)}),e=_.map(e.oppo,function(e){return 8!=e.FieldType&&9!=e.FieldType||(e.ModifyEnums=[{ItemName:$t("选项一"),ItemCode:"dfc1"},{ItemName:$t("选项二"),ItemCode:"dfc2"}]),e.EnumDetails=e.ModifyEnums,_.extend({IsAllowEditOption:!1},e)});return{fields:_.filter(t,function(e){return 3!=e.EditFlag}),customer:_.filter(i,function(e){return 3!=e.EditFlag}),oppo:_.filter(e,function(e){return 3!=e.EditFlag})}},destroy:function(){this.undelegateEvents(),this.options=this.events=null,this.remove(),this._field.destroy()}});i.exports=e});
define("crm-setting/saleaction/addedite/stage/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="stage-rule"> <div class="s-item"> <label>' + ((__t = $t("阶段名称")) == null ? "" : __t) + '</label> <p style="padding: 0 0 25px;">' + __e(Name) + '</p> </div> <div class="s-item"> <label>' + ((__t = $t("阶段要求")) == null ? "" : __t) + "</label> ";
            var type_t = $t("请填写此阶段对{{type}}人员的要求以便{{type}}人员更好的执行", {
                type: Type == 1 ? $t("售前") : $t("售后")
            });
            __p += ' <textarea class="b-g-ipt j-desc" data-key="Description" maxlength="1000" placeholder=\'' + ((__t = type_t) == null ? "" : __t) + "'>" + __e(Description) + '</textarea> </div> <div class="s-item mn-checkbox-box"> ';
            if (Type == 1) {
                __p += ' <p> <span class="mn-checkbox-item j-salestate ' + ((__t = IsTimeoutRemind ? "mn-selected" : "") == null ? "" : __t) + '" data-key="IsTimeoutRemind"></span> <span class="check-lb"> <em>' + ((__t = $t("本阶段停留")) == null ? "" : __t) + '</em> <input class="b-g-ipt j-days" type="text" value="' + ((__t = RemainDays) == null ? "" : __t) + '" maxlength="5" data-key="RemainDays" /> <em>' + ((__t = $t("天时提醒销售人员执行（当商机关联了合作伙伴时提醒人为外部负责人）")) == null ? "" : __t) + '</em> </span> </p> <p> <span class="mn-checkbox-item j-leader ' + ((__t = IsLeaderConfirm ? "mn-selected" : "") == null ? "" : __t) + '" data-key="IsLeaderConfirm"></span> <span class="check-lb">' + ((__t = $t("本阶段完成需直属上级确认（当商机由合作伙伴来推进时由该数据的负责人来确认）")) == null ? "" : __t) + '</span> </p> <p> <span class="mn-checkbox-item j-contacts ' + ((__t = IsFinishByContacts ? "mn-selected" : "") == null ? "" : __t) + '" data-key="IsFinishByContacts"></span> <span class="check-lb"> <em>' + ((__t = $t("客户中至少存在")) == null ? "" : __t) + '</em> <input class="b-g-ipt j-days" type="text" value="' + ((__t = ContactCount) == null ? "" : __t) + '" maxlength="5" data-key="ContactCount" /> <em>' + ((__t = $t("个联系人时方可完成本阶段")) == null ? "" : __t) + "</em> </span> </p> ";
            } else {
                __p += ' <p> <span class="mn-checkbox-item j-salestate ' + ((__t = AsIsThatDayRemind ? "mn-selected" : "") == null ? "" : __t) + '" data-key="AsIsThatDayRemind"></span> <span class="check-lb"> <em>' + ((__t = $t("本阶段停留")) == null ? "" : __t) + '</em> <input class="b-g-ipt j-days" type="text" value="' + ((__t = AsThatRemainDays) == null ? "" : __t) + '" data-key="AsThatRemainDays" maxlength="5" /> <em>' + ((__t = $t("天时提醒售后人员执行")) == null ? "" : __t) + '</em> </span> </p> <p> <span class="mn-checkbox-item j-saletimeout ' + ((__t = AsIsTimeoutRemind ? "mn-selected" : "") == null ? "" : __t) + '" data-key="AsIsTimeoutRemind"></span> <span class="check-lb"> <em>' + ((__t = $t("本阶段停留")) == null ? "" : __t) + '</em> <input class="b-g-ipt j-days" type="text" value="' + ((__t = AsRemainDays) == null ? "" : __t) + '" data-key="AsRemainDays" maxlength="5" /> <em>' + ((__t = $t("天后超时提醒负责人及售后人员")) == null ? "" : __t) + "</em> </span> </p> ";
            }
            __p += ' </div> </div> <div class="tit-backinfo b-g-hide"> <span class="text"></span> </div>';
        }
        return __p;
    };
});
define("crm-setting/saleaction/addedite/template/nav-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<p class="n-item ' + ((__t = Name == "" ? "no-name" : "") == null ? "" : __t) + '" data-id="' + ((__t = SaleActionStageID) == null ? "" : __t) + '"> ';
            if (StageFlag == 1) {
                __p += " <span>" + ((__t = $t("阶段")) == null ? "" : __t) + '<em class="step-num">1</em>：</span> ';
            } else {
                __p += " <span>" + ((__t = $t("结束:")) == null ? "" : __t) + "</span> ";
            }
            __p += ' <span class="nav-name">' + __e(Name) + "</span> </p>";
        }
        return __p;
    };
});
define("crm-setting/saleaction/addedite/template/saveconfirm-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="mn-radio-box save-confirm"> <p> <span class="allow-olddata mn-radio-item mn-selected" action-type="radio" data-val="1"></span>' + ((__t = $t("对新数据生效同时更新已有数据")) == null ? "" : __t) + '</p> <p> <span class="mn-radio-item" action-type="radio" data-val="2"></span>' + ((__t = $t("对新数据生效不更新已有数据。")) == null ? "" : __t) + '<span class="save-tip">' + ((__t = $t("同时新建一个销售流程")) == null ? "" : __t) + "(" + ((__t = $t("名称不能重复")) == null ? "" : __t) + ")" + ((__t = $t("可选择原流程是否停用停用后不可见")) == null ? "" : __t) + '</span> </p> </div> <div class="mn-checkbox-box old-sale-change b-g-hide"> <p><label>' + ((__t = $t("停用原销售流程")) == null ? "" : __t) + '</label><span class="mn-checkbox-item isstop"></span></p> <p><label>' + ((__t = $t("新销售流程名称")) == null ? "" : __t) + '</label><input type="text" class="b-g-ipt new-sele-name" value="' + __e(Name) + '"/></p> </div>';
        }
        return __p;
    };
});
define("crm-setting/saleaction/addedite/template/step-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="b-item" data-id="' + ((__t = SaleActionStageID) == null ? "" : __t) + '"> ';
            if (StageFlag != 1) {
                __p += " <label>" + ((__t = $t("结束:")) == null ? "" : __t) + "</label> ";
            } else {
                __p += " <label>" + ((__t = $t("阶段")) == null ? "" : __t) + '<em class="step-num">1</em>：</label> ';
            }
            __p += ' <input type="text" value="' + ((__t = Name) == null ? "" : __t) + '" class="b-g-ipt step-name" maxlength="10" /> ';
            if (Type == 1) {
                __p += ' <input type="text" value="' + ((__t = Weight) == null ? "" : __t) + '" class="b-g-ipt step-may" /> <span class="txt">%</span> ';
                if (StageFlag != 1) {
                    __p += ' <div class="step-status step-status-select" data-val="' + ((__t = StageFlag) == null ? "" : __t) + '"></div> ';
                } else {
                    __p += ' <div class="step-status" data-val="1">' + ((__t = $t("进行中")) == null ? "" : __t) + "</div> ";
                }
                __p += " ";
            }
            __p += ' <span class="add-step">+</span> <span class="del-step">-</span> </div>';
        }
        return __p;
    };
});
define("crm-setting/saleaction/addedite/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="add-saleaction"> <h3 class="add-tit"> <span class="text">' + ((__t = status == "add" ? $t("新建销售流程") : $t("编辑销售流程")) == null ? "" : __t) + '</span> <div class="btns-box"> <span class="b-g-btn">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span class="b-g-btn-cancel">' + ((__t = $t("退 出")) == null ? "" : __t) + '</span> </div> </h3> <div class="add-con"> <div class="con-l j-nav"> <div class="scroll-el"> <div class="item active"> <div class="nav-tit"> <h3>' + ((__t = $t("第一步")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("设置基本信息")) == null ? "" : __t) + '</p> </div> </div> <div class="item pre-step-nav step-nav"> <div class="nav-tit"> <h3>' + ((__t = $t("第二步")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("售前阶段反馈表")) == null ? "" : __t) + '</p> <div class="pre-nav nav-box"></div> <div class="default-nav"> ';
            _.each(EndStages || [], function(item) {
                __p += " ";
                if (item.StageFlag != 1) {
                    __p += ' <p class="n-item" data-id="' + ((__t = item.SaleActionStageID) == null ? "" : __t) + '"><span>' + ((__t = $t("结束:")) == null ? "" : __t) + '</span><span class="nav-name">' + __e(item.Name) + "</span></p> ";
                }
                __p += " ";
            });
            __p += " </div> </div> </div> ";
            if (isNotYunZhiJia) {
                __p += ' <div class="item next-step-nav step-nav"> <div class="nav-tit"> <h3>' + ((__t = $t("第三步")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("售后阶段反馈表")) == null ? "" : __t) + '</p> <div class="next-nav nav-box"></div> </div> </div> ';
            }
            __p += ' <div class="item"> <div class="nav-tit"> ';
            if (isNotYunZhiJia) {
                __p += " <h3>" + ((__t = $t("第四步")) == null ? "" : __t) + "</h3> ";
            } else {
                __p += " <h3>" + ((__t = $t("第三步")) == null ? "" : __t) + "</h3> ";
            }
            __p += " <p>" + ((__t = $t("选择适用范围")) == null ? "" : __t) + '</p> </div> </div> </div> </div> <div class="con-c j-con"> <div class="scroll-el"> <div class="item base-item"> <div class="base-name"> <label><em>*</em>' + ((__t = $t("销售流程名称")) == null ? "" : __t) + '</label> <input type="text" data-default="' + ((__t = Name) == null ? "" : __t) + '" maxlength="20" value="' + ((__t = Name) == null ? "" : __t) + '" class="b-g-ipt j-base-name" placeholder=\'' + ((__t = $t("例如销售部销售流程")) == null ? "" : __t) + '\' /> </div> <div class="base-step b-g-clear"> <div class="step-box step-box-pre"> <h3>' + ((__t = $t("售前阶段设置")) == null ? "" : __t) + '</h3> <h4><span class="tit-name">' + ((__t = $t("阶段名称")) == null ? "" : __t) + '</span><span class="tit-may">' + ((__t = $t("赢率")) == null ? "" : __t) + '</span><span class="tit-status">' + ((__t = $t("状态")) == null ? "" : __t) + '</span></h4> <div class="pre-step j-step" data-type="1"></div> <div class="default-step end-step j-step" data-type="3"> </div> </div> ';
            if (isNotYunZhiJia) {
                __p += ' <div class="step-box step-box-next"> <h3>' + ((__t = $t("售后阶段设置")) == null ? "" : __t) + '</h3> <h4><span class="tit-name">' + ((__t = $t("阶段名称")) == null ? "" : __t) + '</span></h4> <div class="next-step j-step" data-type="2"></div> </div> ';
            }
            __p += ' </div> </div> <div class="item b-g-hide"></div> ';
            if (isNotYunZhiJia) {
                __p += ' <div class="item b-g-hide"></div> ';
            }
            __p += ' <div class="item b-g-hide"> <div class="r-item"> <h3><span>' + ((__t = $t("以下部门今后新增的商机将使用此销售流程")) == null ? "" : __t) + '</span></h3> <div class="circle-select"></div> </div> <div class="r-item mn-checkbox-box"> <h3><span class="mn-checkbox-item ' + ((__t = CheckFieldIfWin ? "mn-selected" : "") == null ? "" : __t) + '" data-default="' + ((__t = CheckFieldIfWin ? 1 : 2) == null ? "" : __t) + '" data-key="CheckFieldIfWin"></span>' + ((__t = $t("赢单时校验之前所有阶段必填项")) == null ? "" : __t) + '</h3> </div> <div class="r-item mn-checkbox-box"> <h3><span class="mn-checkbox-item ' + ((__t = IsAllowTradeIfWin ? "mn-selected" : "") == null ? "" : __t) + '" data-default="' + ((__t = IsAllowTradeIfWin ? 1 : 2) == null ? "" : __t) + '" data-key="IsAllowTradeIfWin"></span>' + ((__t = $t("必须新建订单方可赢单")) == null ? "" : __t) + '</h3> <p class="tip">' + ((__t = $t("勾选并保存当选择赢单时必须新建销售订单后商机的状态才为赢单反之不需要新建销售订单直接赢单")) == null ? "" : __t) + '</p> </div> <div class="complete-step-box mn-checkbox-box ' + ((__t = IsAllowTrade ? "" : "disabled") == null ? "" : __t) + '"> <span class="mn-checkbox-item ' + ((__t = IsAllowTrade ? "mn-selected" : "") == null ? "" : __t) + '" data-default="' + ((__t = IsAllowTrade ? 1 : 2) == null ? "" : __t) + '" data-key="IsAllowTrade"></span>' + ((__t = $t("完成售前")) == null ? "" : __t) + '<div class="j-complete complete-step"></div>' + ((__t = $t("后方可新建订单")) == null ? "" : __t) + '<div class="crm-tip"> <span class="tip-btn"></span> <div class="tip-text"> <p>1.' + ((__t = $t("如果需要此功能控制新建订单时关联商机建议将新建销售订单中的商机字段设置为必填")) == null ? "" : __t) + "," + ((__t = $t("才能达到更好的效果。")) == null ? "" : __t) + "</p> <p>2." + ((__t = $t("使用该销售流程的商机需要完成指定阶段后才能在新建订单时关联此商机。")) == null ? "" : __t) + '</p> </div> </div> <div class="mask"></div> </div> </div> </div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/saleaction/detail/detail",["crm-modules/common/util","crm-modules/common/slide/slide","crm-widget/select/select","./../addedite/addedite","./template/tpl-html"],function(e,t,a){var l=e("crm-modules/common/util"),n=e("crm-modules/common/slide/slide"),s=e("crm-widget/select/select"),i=e("./../addedite/addedite"),r=e("./template/tpl-html"),o=n.extend({events:{click:"hideSelect","click .detail-nav span":"navHandle","click .page-select span":"selectHandle","click .page-select li":"selectedHandle","click .j-del":"delHandle","click .j-edite":"editHandle","click .step-type-tab span":"stepTypeTabHandle"},curSaleData:null,stepTypeTabHandle:function(e){var t,e=$(e.currentTarget),a=e.attr("data-type"),n=e.parents(".step-type-tab").siblings(".detail-con");n.removeClass("sale-nodata"),e.hasClass("cur")||(t=0===$("."+a+"-sale-box",this.$el).find(".step-item").length?"sale-nodata":"",e.addClass("cur").siblings().removeClass("cur"),n.addClass(t),$(".j-step-type-item",this.$el).hide(),$("."+a+"-sale-box",this.$el).show())},hideSelect:function(){$(".page-select",this.$el).removeClass("page-selected")},delHandle:function(e){var t=this,a=$t("确定删除这个销售流程吗"),n=l.confirm(a,$t("删除"),function(){l.FHHApi({url:"/EM1HCRM/SaleActionSetting/DeleteSaleAction",data:{SaleActionID:t.sId},success:function(e){n.hide(),0==e.Result.StatusCode?(t.trigger("refresh","del"),t.hide(),l.remind("1",$t("删除成功"))):l.alert(e.Result.FailureMessage)}},{submitSelector:$(".b-g-btn",n.element),errorAlertModel:1})})},editHandle:function(){var t=this,e=new i;t.hide(),e.on("refresh",function(e){"del"!=e&&t.show(t.sId),t.trigger("refresh",e)}),e.show(t.curSaleData)},selectHandle:function(e){var a=$(e.currentTarget).parent();return a.toggleClass("page-selected"),$(".page-selected",this.$el).each(function(e,t){$(t)[0]!=a[0]&&$(t).removeClass("page-selected")}),e.stopPropagation(),!1},selectedHandle:function(e){var t=$(e.currentTarget),a=t.closest(".page-select");return a.removeClass("page-selected"),$(".text",a).html(t.html()),e.stopPropagation(),!1},navHandle:function(e){var e=$(e.currentTarget),t=e.index();e.addClass("cur").siblings().removeClass("cur"),$(".detail-con .info-item",this.$el).eq(t).show().siblings().hide(),$(".step-type-tab",this.$el).toggle(0==t),0==t?$(".step-type-tab span:eq(0)",this.$el).click():$(".detail-con",this.$el).removeClass("sale-nodata")},getDetailDataById:function(t){var a=this;a.detailAjax&&a.detailAjax.abort(),a.detailAjax=l.FHHApi({url:"/EM1HCRM/SaleActionSetting/GetSaleActionByID",data:{SaleActionID:a.sId},success:function(e){0==e.Result.StatusCode?t&&t(e.Value):a.setDetailCon(e.Result.FailureMessage)},complete:function(){a.detailAjax=null}},{errorAlertModel:1})},renderDetail:function(){var s=this,i=$(".slide-con",s.$el);s.getDetailDataById(function(e){var t=e.SaleAction.Stages.length||2,a=[],n=[],t=(i.width()-42-27*t)/(t-1);e.moment=s.moment,e.getNameByIds=l.getNameByIds,e.marginLeft=t+"px",e.suffixes=["","",$t("这是一个单行文本字段"),$t("这是一个多行文本字段"),"8888","8888.00","8,888.88","YYYY-MM-DD"],s.curSaleData=e.SaleAction,_.each(e.SaleAction.Stages||[],function(e){(1==e.Type?a:n).push(e)}),e.nextStages=s._parseRelateData(n),e.preStages=s._parseRelateData(a),i.html(r(e)),s.renderBanner(a,n),s.renderCascade(e)})},_parseRelateData:function(e){var n=this;return _.each(e,function(e){var t=JSON.parse(e.CustomerDescribe||'""'),a=JSON.parse(e.OpportunityDescribe||'""');e.CustomerFieldInfos=n._parseRelateHandle(t),e.OppoFieldInfos=n._parseRelateHandle(a)}),e},_parseRelateHandle:function(e){var t,a=[];for(t in e)a.push(e[t]);return _.each(a,function(e){_.extend(e,{FieldType:e.type,FieldCaption:e.label,FieldApiName:e.api_name,FieldName:e.api_name})}),a},renderBanner:function(t,a){var n=this;e.async("crm-setting/common/saleaction/saleaction",function(e){0<n.$(".pre-sale-box .steps-nav").length&&(n.prebanner&&n.prebanner.destroy(),n.prebanner=new e.Banner({width:520,el:n.$(".pre-sale-box .steps-nav"),maxNum:5}),n.prebanner.on("click",function(e){$(".pre-sale-box .steps-con .step-item",n.$el).eq(e.order-1).show().siblings().hide()}),n.prebanner.show(_.map(t,function(e,t){return{active:2,name:e.Name,order:t+1,stageID:e.SaleActionStageID}}))),0<n.$(".next-sale-box .steps-nav").length&&(n.nextbanner&&n.nextbanner.destroy(),n.nextbanner=new e.Banner({width:520,el:n.$(".next-sale-box .steps-nav"),maxNum:5}),n.nextbanner.on("click",function(e){$(".next-sale-box .steps-con .step-item",n.$el).eq(e.order-1).show().siblings().hide()}),n.nextbanner.show(_.map(a,function(e,t){return{active:2,name:e.Name,order:t+1,stageID:e.SaleActionStageID}})))})},renderCascade:function(e){var t={};function a(){_.each(e.preStages,function(e){_.each(e.CustomerFieldInfos,function(e){14==e.FieldType&&(t[e.UserDefinedFieldID]=e.EnumDetails)}),_.each(e.OppoFieldInfos,function(e){14==e.FieldType&&(t[e.UserDefinedFieldID]=e.EnumDetails)}),_.each(e.UDFieldInfos,function(e){14==e.FieldType&&(t[e.UserDefinedFieldID]=e.EnumDetails)})})}a(e.preStages),a();var n=this;n.$(".cascade-wrap").each(function(){var e=$(this);n._createCascade(e,t[e.data("id")])})},_createCascade:function(e,t){var n,a=[];e.html('<div class="sel-p"></div><div class="sel-c"></div>'),_.each(t,function(e){a.push({value:e.ItemCode,name:e.ItemName,children:e.Children})}),t=new s({$wrap:e.find(".sel-p"),appendBody:!1,zIndex:2e3,options:[{name:$t("请选择"),value:"---"}].concat(a||[]),disabled:!0,defaultValue:"---"}),n=new s({$wrap:e.find(".sel-c"),appendBody:!1,zIndex:2e3,disabled:!0,options:[{name:$t("请选择"),value:"---"}],defaultValue:"---"}),t.on("change",function(e,t){var a=[];t&&(_.each(t.children,function(e){a.push({value:e.ItemCode,name:_.escape(e.ItemName)})}),n.resetOptions([].concat(a)))}),(this.cascadeList||(this.cascadeList=[])).push(t,n)},moment:function(e){return FS.moment.unix(e/1e3,!0).format("YYYY-MM-DD HH:mm")},show:function(e){e&&(this.sId=e,this.renderDetail()),n.prototype.hasShowDetail=!1,n.prototype.show.call(this)},hide:function(){n.prototype.hide.call(this)},destroy:function(){this.prebanner&&(this.prebanner.destroy(),this.prebanner=null),this.nextbanner&&(this.nextbanner.destroy(),this.nextbanner=null),n.prototype.destroy.apply(this,arguments)}});a.exports=o});
define("crm-setting/saleaction/detail/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-d-detail"> <div class="detail-tit"> <h2>' + __e(SaleAction.Name) + "</h2> ";
            if (SaleAction.Status == 1) {
                __p += ' <div class="detail-btns"> <span class="crm-ico-del j-del">' + ((__t = $t("删除")) == null ? "" : __t) + '</span> <span class="crm-ico-edite j-edite">' + ((__t = $t("编辑")) == null ? "" : __t) + "</span> </div> ";
            }
            __p += ' <div class="detail-status"> <span>' + ((__t = $t("状态")) == null ? "" : __t) + "：" + ((__t = [ $t("使用中"), $t("已停用") ][SaleAction.Status - 1]) == null ? "" : __t) + '</span> </div> </div> <div class="detail-nav"> <span class="cur">' + ((__t = $t("销售流程详情")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("基础信息")) == null ? "" : __t) + '</span> </div> <div class="step-type-tab"> <span class="cur" data-type="pre">' + ((__t = $t("售前流程")) == null ? "" : __t) + '</span> <em class="line">|</em> <span data-type="next">' + ((__t = $t("售后流程")) == null ? "" : __t) + '</span> </div> <div class="detail-con ';
            if (preStages.length == 0) {
                __p += "sale-nodata";
            }
            __p += '"> <div class="info-item step-info"> <div class="pre-sale-box j-step-type-item"> ';
            if (preStages.length > 0) {
                __p += ' <div class="steps-nav b-g-clear"></div> <div class="steps-con"> ';
                _.each(preStages, function(item, index) {
                    __p += " ";
                    var number = index + 1;
                    __p += ' <div class="step-item ' + ((__t = index != 0 ? "b-g-hide" : "") == null ? "" : __t) + '"> <div class="step-desc"> ';
                    var number_t = $t("第{{number}}阶段", {
                        number: number
                    });
                    __p += " <h2>" + ((__t = number_t) == null ? "" : __t) + "：" + __e(item.Name) + ' <span class="may">' + ((__t = $t("赢率")) == null ? "" : __t) + "" + ((__t = item.Weight ? item.Weight + "%" : "--") == null ? "" : __t) + "</span></h2> <h3>" + ((__t = $t("阶段要求")) == null ? "" : __t) + "</h3> <p>" + ((__t = -item.Description || $t("无")) == null ? "" : __t) + "</p> ";
                    if (item.IsTimeoutRemind || item.IsLeaderConfirm || item.IsFinishByContacts) {
                        __p += " <h3>" + ((__t = $t("规则")) == null ? "" : __t) + '</h3> <p style="line-height: 24px;">';
                        if (item.IsTimeoutRemind) {
                            __p += " ";
                            var RemainDays_t = $t("本阶段停留时间超过{{RemainDays}}天时提醒销售人员", {
                                RemainDays: item.RemainDays
                            });
                            __p += " " + ((__t = RemainDays_t) == null ? "" : __t) + " <br/> ";
                        }
                        __p += " ";
                        if (item.IsLeaderConfirm) {
                            __p += ((__t = $t("本阶段完成需直属上级确认")) == null ? "" : __t) + "<br/> ";
                        }
                        __p += " ";
                        if (item.IsFinishByContacts) {
                            __p += " ";
                            var ContactCount_t = $t("客户中存在{{ContactCount}}个联系人时方可完成本阶段", {
                                ContactCount: item.ContactCount
                            });
                            __p += " " + ((__t = ContactCount_t) == null ? "" : __t) + " ";
                        }
                        __p += "</p> ";
                    }
                    __p += ' </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("阶段反馈")) == null ? "" : __t) + '</span></h3> <div class="step-field"> ';
                    if (item.UDFieldInfos.length > 0) {
                        __p += " ";
                        _.each(item.UDFieldInfos, function(fItem) {
                            __p += ' <div class="detail-item"> <span class="item-tit"><em>' + ((__t = fItem.IsNotNull ? "*" : "") == null ? "" : __t) + "</em> " + __e(fItem.FieldCaption) + '</span> <div class="item-con"> ';
                            if (fItem.FieldType == 3) {
                                __p += ' <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">' + ((__t = suffixes[fItem.FieldType]) == null ? "" : __t) + "</textarea> ";
                            } else if (fItem.FieldType == 8) {
                                __p += ' <div class="page-select"> <span><em class="text">' + ((__t = $t("请选择")) == null ? "" : __t) + "</em><i></i></span> <ul> <li>" + ((__t = $t("请选择")) == null ? "" : __t) + "</span></li> ";
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += " <li>" + __e(eItem.ItemName) + "</span></li> ";
                                });
                                __p += " </ul> </div> ";
                            } else if (fItem.FieldType == 9) {
                                __p += ' <div class="mn-checkbox-box"> ';
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += ' <p><span class="mn-checkbox-item"></span><span class="check-label">' + __e(eItem.ItemName) + "</span></p> ";
                                });
                                __p += " </div> ";
                            } else if (fItem.FieldType == 10) {
                                __p += ' <div class="upload-img-box"> <div class="img-place"></div> <span class="up-text">' + ((__t = $t("上传图片")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("支持jpggifpng格式的图片")) == null ? "" : __t) + "</span> </div> ";
                            } else if (fItem.FieldType == 14) {
                                __p += ' <div data-id="' + ((__t = fItem.UserDefinedFieldID) == null ? "" : __t) + '" class="cascade-wrap"></div> ';
                            } else if (fItem.FieldType == 17) {
                                __p += " <p>+" + ((__t = $t("添加附件")) == null ? "" : __t) + "</p> ";
                            } else {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="' + ((__t = suffixes[fItem.FieldType] || "") == null ? "" : __t) + '" disabled="disabled"/> ';
                            }
                            __p += " </div> </div> ";
                        });
                        __p += " ";
                    } else {
                        __p += ' <div class="no-data">' + ((__t = $t("暂未设置任何阶段反馈")) == null ? "" : __t) + "</div> ";
                    }
                    __p += ' </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("关联客户字段")) == null ? "" : __t) + '</span></h3> <div class="step-field"> ';
                    if (item.CustomerFieldInfos.length > 0) {
                        __p += " ";
                        _.each(item.CustomerFieldInfos, function(fItem) {
                            __p += ' <div class="detail-item"> <span class="item-tit"><em>' + ((__t = fItem.IsNotNull ? "*" : "") == null ? "" : __t) + "</em> " + __e(fItem.FieldCaption) + '</span> <div class="item-con"> ';
                            if (fItem.FieldType == "long_text") {
                                __p += ' <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">' + ((__t = suffixes[fItem.FieldType]) == null ? "" : __t) + "</textarea> ";
                            } else if (fItem.FieldType == "select_one") {
                                __p += ' <div class="page-select"> <span><em class="text">' + ((__t = $t("请选择")) == null ? "" : __t) + "</em><i></i></span> <ul> <li>" + ((__t = $t("请选择")) == null ? "" : __t) + "</span></li> ";
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += " <li>" + __e(eItem.ItemName) + "</span></li> ";
                                });
                                __p += " </ul> </div> ";
                            } else if (fItem.FieldType == "select_many") {
                                __p += ' <div class="mn-checkbox-box"> ';
                                _.each(fItem.options, function(eItem) {
                                    __p += ' <p><span class="mn-checkbox-item"></span><span class="check-label">' + __e(eItem.label) + "</span></p> ";
                                });
                                __p += " </div> ";
                            } else if (fItem.FieldType == "image") {
                                __p += ' <div class="upload-img-box"> <div class="img-place"></div> <span class="up-text">' + ((__t = $t("上传图片")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("支持jpggifpng格式的图片")) == null ? "" : __t) + "</span> </div> ";
                            } else if (fItem.FieldType == "multi_level_select_one") {
                                __p += ' <div data-id="' + ((__t = fItem.UserDefinedFieldID) == null ? "" : __t) + '" class="cascade-wrap"></div> ';
                            } else if (fItem.FieldType == "file_attachment") {
                                __p += " <p>+" + ((__t = $t("添加附件")) == null ? "" : __t) + "</p> ";
                            } else {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="' + ((__t = suffixes[fItem.FieldType] || "") == null ? "" : __t) + '" disabled="disabled"/> ';
                            }
                            __p += " </div> </div> ";
                        });
                        __p += " ";
                    } else {
                        __p += ' <div class="no-data">' + ((__t = $t("暂未设置任何关联客户字段")) == null ? "" : __t) + "</div> ";
                    }
                    __p += ' </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("关联商机字段")) == null ? "" : __t) + '</span></h3> <div class="step-field"> ';
                    if (item.OppoFieldInfos.length > 0) {
                        __p += " ";
                        _.each(item.OppoFieldInfos, function(fItem) {
                            __p += ' <div class="detail-item"> <span class="item-tit"><em>' + ((__t = fItem.IsNotNull ? "*" : "") == null ? "" : __t) + "</em> " + __e(fItem.FieldCaption) + '</span> <div class="item-con"> ';
                            if (fItem.FieldType == "long_text") {
                                __p += ' <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">' + ((__t = suffixes[fItem.FieldType]) == null ? "" : __t) + "</textarea> ";
                            } else if (fItem.FieldType == "select_one") {
                                __p += ' <div class="page-select"> <span><em class="text">' + ((__t = $t("请选择")) == null ? "" : __t) + "</em><i></i></span> <ul> <li>" + ((__t = $t("请选择")) == null ? "" : __t) + "</span></li> ";
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += " <li>" + __e(eItem.ItemName) + "</span></li> ";
                                });
                                __p += " </ul> </div> ";
                            } else if (fItem.FieldType == "select_many") {
                                __p += ' <div class="mn-checkbox-box"> ';
                                _.each(fItem.options, function(eItem) {
                                    __p += ' <p><span class="mn-checkbox-item"></span><span class="check-label">' + __e(eItem.label) + "</span></p> ";
                                });
                                __p += " </div> ";
                            } else if (fItem.FieldType == "image") {
                                __p += ' <div class="upload-img-box"> <div class="img-place"></div> <span class="up-text">' + ((__t = $t("上传图片")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("支持jpggifpng格式的图片")) == null ? "" : __t) + "</span> </div> ";
                            } else if (fItem.FieldType == "multi_level_select_one") {
                                __p += ' <div data-id="' + ((__t = fItem.UserDefinedFieldID) == null ? "" : __t) + '" class="cascade-wrap"></div> ';
                            } else if (fItem.FieldType == "file_attachment") {
                                __p += " <p>+" + ((__t = $t("添加附件")) == null ? "" : __t) + "</p> ";
                            } else {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="' + ((__t = suffixes[fItem.FieldType] || "") == null ? "" : __t) + '" disabled="disabled"/> ';
                            }
                            __p += " </div> </div> ";
                        });
                        __p += " ";
                    } else {
                        __p += ' <div class="no-data">' + ((__t = $t("暂未设置任何关联商机字段")) == null ? "" : __t) + "</div> ";
                    }
                    __p += " </div> </div> ";
                });
                __p += " </div> ";
            } else {
                __p += ' <div class="no-data">' + ((__t = $t("暂未设置售前流程")) == null ? "" : __t) + "</div> ";
            }
            __p += ' </div> <!--售后阶段--> <div class="next-sale-box j-step-type-item b-g-hide"> ';
            if (nextStages.length > 0) {
                __p += ' <div class="steps-nav b-g-clear"></div> <div class="steps-con"> ';
                _.each(nextStages, function(item, index) {
                    __p += " ";
                    var number = index + 1;
                    __p += ' <div class="step-item ' + ((__t = index != 0 ? "b-g-hide" : "") == null ? "" : __t) + '"> <div class="step-desc"> ';
                    var number_t = $t("第{{number}}阶段", {
                        number: number
                    });
                    __p += " <h2>" + ((__t = number_t) == null ? "" : __t) + "：" + __e(item.Name) + "</h2> <h3>" + ((__t = $t("阶段要求")) == null ? "" : __t) + "</h3> <p>" + ((__t = -item.Description || $t("无")) == null ? "" : __t) + "</p> ";
                    if (item.AsIsThatDayRemind || item.AsIsTimeoutRemind) {
                        __p += " <h3>" + ((__t = $t("规则")) == null ? "" : __t) + "</h3> ";
                        if (item.AsIsThatDayRemind) {
                            __p += ((__t = $t("本阶段停留")) == null ? "" : __t) + "" + ((__t = item.AsThatRemainDays) == null ? "" : __t) + "" + ((__t = $t("天时提醒售后人员执行")) == null ? "" : __t) + "<br/> ";
                        }
                        __p += " ";
                        if (item.AsIsTimeoutRemind) {
                            __p += " ";
                            var RemainDays_t = $t("本阶段停留时间超过{{RemainDays}}天时提醒销售人员", {
                                RemainDays: item.AsRemainDays
                            });
                            __p += " " + ((__t = RemainDays_t) == null ? "" : __t) + " ";
                        }
                        __p += "</p> ";
                    }
                    __p += ' </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("阶段反馈")) == null ? "" : __t) + '</span></h3> <div class="step-field"> ';
                    if (item.UDFieldInfos.length > 0) {
                        __p += " ";
                        _.each(item.UDFieldInfos, function(fItem) {
                            __p += ' <div class="detail-item"> <span class="item-tit"><em>' + ((__t = fItem.IsNotNull ? "*" : "") == null ? "" : __t) + "</em> " + __e(fItem.FieldCaption) + '</span> <div class="item-con"> ';
                            if (fItem.FieldType == 3) {
                                __p += ' <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">' + ((__t = suffixes[fItem.FieldType]) == null ? "" : __t) + "</textarea> ";
                            } else if (fItem.FieldType == 8) {
                                __p += ' <div class="page-select"> <span><em class="text">' + ((__t = $t("请选择")) == null ? "" : __t) + "</em><i></i></span> <ul> <li>" + ((__t = $t("请选择")) == null ? "" : __t) + "</span></li> ";
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += " <li>" + __e(eItem.ItemName) + "</span></li> ";
                                });
                                __p += " </ul> </div> ";
                            } else if (fItem.FieldType == 9) {
                                __p += ' <div class="mn-checkbox-box"> ';
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += ' <p><span class="mn-checkbox-item"></span><span class="check-label">' + __e(eItem.ItemName) + "</span></p> ";
                                });
                                __p += " </div> ";
                            } else if (fItem.FieldType == 10) {
                                __p += ' <div class="upload-img-box"> <div class="img-place"></div> <span class="up-text">' + ((__t = $t("上传图片")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("支持jpggifpng格式的图片")) == null ? "" : __t) + "</span> </div> ";
                            } else if (fItem.FieldType == 14) {
                                __p += ' <div data-id="' + ((__t = fItem.UserDefinedFieldID) == null ? "" : __t) + '" class="cascade-wrap"></div> ';
                            } else if (fItem.FieldType == 17) {
                                __p += " <p>+" + ((__t = $t("添加附件")) == null ? "" : __t) + "</p> ";
                            } else if (fItem.FieldType == 18) {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value=\'' + ((__t = $t("这是一个电话号码")) == null ? "" : __t) + '\' disabled="disabled"/> ';
                            } else if (fItem.FieldType == 19) {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value=\'' + ((__t = $t("这是一个邮箱地址")) == null ? "" : __t) + '\' disabled="disabled"/> ';
                            } else {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="' + ((__t = suffixes[fItem.FieldType] || "") == null ? "" : __t) + '" disabled="disabled"/> ';
                            }
                            __p += " </div> </div> ";
                        });
                        __p += " ";
                    } else {
                        __p += ' <div class="no-data">' + ((__t = $t("暂未设置任何阶段反馈")) == null ? "" : __t) + "</div> ";
                    }
                    __p += ' </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("关联客户字段")) == null ? "" : __t) + '</span></h3> <div class="step-field"> ';
                    if (item.CustomerFieldInfos.length > 0) {
                        __p += " ";
                        _.each(item.CustomerFieldInfos, function(fItem) {
                            __p += ' <div class="detail-item"> <span class="item-tit"><em>' + ((__t = fItem.IsNotNull ? "*" : "") == null ? "" : __t) + "</em> " + __e(fItem.FieldCaption) + '</span> <div class="item-con"> ';
                            if (fItem.FieldType == 2 || fItem.FieldType == 4 || fItem.FieldType == 14 || fItem.FieldType == 11 || fItem.FieldType == 5 || fItem.FieldType == 6 | fItem.FieldType == 7) {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="' + ((__t = suffixes[fItem.FieldType]) == null ? "" : __t) + '" disabled="disabled"/> ';
                            } else if (fItem.FieldType == 3) {
                                __p += ' <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">' + ((__t = suffixes[fItem.FieldType]) == null ? "" : __t) + "</textarea> ";
                            } else if (fItem.FieldType == 8) {
                                __p += ' <div class="page-select"> <span><em class="text">' + ((__t = $t("请选择")) == null ? "" : __t) + "</em><i></i></span> <ul> <li>" + ((__t = $t("请选择")) == null ? "" : __t) + "</span></li> ";
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += " <li>" + __e(eItem.ItemName) + "</span></li> ";
                                });
                                __p += " </ul> </div> ";
                            } else if (fItem.FieldType == 9) {
                                __p += ' <div class="mn-checkbox-box"> ';
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += ' <p><span class="mn-checkbox-item"></span><span class="check-label">' + __e(eItem.ItemName) + "</span></p> ";
                                });
                                __p += " </div> ";
                            } else if (fItem.FieldType == 10) {
                                __p += ' <div class="upload-img-box"> <div class="img-place"></div> <span class="up-text">' + ((__t = $t("上传图片")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("支持jpggifpng格式的图片")) == null ? "" : __t) + "</span> </div> ";
                            } else if (fItem.FieldType == 14) {
                                __p += ' <div data-id="' + ((__t = fItem.UserDefinedFieldID) == null ? "" : __t) + '" class="cascade-wrap"></div> ';
                            } else if (fItem.FieldType == 17) {
                                __p += " <p>+" + ((__t = $t("添加附件")) == null ? "" : __t) + "</p> ";
                            } else if (fItem.FieldType == 18) {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value=\'' + ((__t = $t("这是一个电话号码")) == null ? "" : __t) + '\' disabled="disabled"/> ';
                            } else if (fItem.FieldType == 19) {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value=\'' + ((__t = $t("这是一个邮箱地址")) == null ? "" : __t) + '\' disabled="disabled"/> ';
                            } else {
                                __p += " ";
                            }
                            __p += " </div> </div> ";
                        });
                        __p += " ";
                    } else {
                        __p += ' <div class="no-data">' + ((__t = $t("暂未设置任何关联客户字段")) == null ? "" : __t) + "</div> ";
                    }
                    __p += ' </div> <h3 class="detail-sec-tit"><span>' + ((__t = $t("关联商机字段")) == null ? "" : __t) + '</span></h3> <div class="step-field"> ';
                    if (item.OppoFieldInfos.length > 0) {
                        __p += " ";
                        _.each(item.OppoFieldInfos, function(fItem) {
                            __p += ' <div class="detail-item"> <span class="item-tit"><em>' + ((__t = fItem.IsNotNull ? "*" : "") == null ? "" : __t) + "</em> " + __e(fItem.FieldCaption) + '</span> <div class="item-con"> ';
                            if (fItem.FieldType == 3) {
                                __p += ' <textarea class="b-g-ipt b-g-ipt-disabled" disabled="disabled">' + ((__t = suffixes[fItem.FieldType]) == null ? "" : __t) + "</textarea> ";
                            } else if (fItem.FieldType == 8) {
                                __p += ' <div class="page-select"> <span><em class="text">' + ((__t = $t("请选择")) == null ? "" : __t) + "</em><i></i></span> <ul> <li>" + ((__t = $t("请选择")) == null ? "" : __t) + "</span></li> ";
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += " <li>" + __e(eItem.ItemName) + "</span></li> ";
                                });
                                __p += " </ul> </div> ";
                            } else if (fItem.FieldType == 9) {
                                __p += ' <div class="mn-checkbox-box"> ';
                                _.each(fItem.EnumDetails, function(eItem) {
                                    __p += ' <p><span class="mn-checkbox-item"></span><span class="check-label">' + __e(eItem.ItemName) + "</span></p> ";
                                });
                                __p += " </div> ";
                            } else if (fItem.FieldType == 10) {
                                __p += ' <div class="upload-img-box"> <div class="img-place"></div> <span class="up-text">' + ((__t = $t("上传图片")) == null ? "" : __t) + "</span> <span>" + ((__t = $t("支持jpggifpng格式的图片")) == null ? "" : __t) + "</span> </div> ";
                            } else if (fItem.FieldType == 14) {
                                __p += ' <div data-id="' + ((__t = fItem.UserDefinedFieldID) == null ? "" : __t) + '" class="cascade-wrap"></div> ';
                            } else if (fItem.FieldType == 17) {
                                __p += " <p>+" + ((__t = $t("添加附件")) == null ? "" : __t) + "</p> ";
                            } else {
                                __p += ' <input tpye="text" class="b-g-ipt b-g-ipt-disabled" value="' + ((__t = suffixes[fItem.FieldType] || "") == null ? "" : __t) + '" disabled="disabled"/> ';
                            }
                            __p += " </div> </div> ";
                        });
                        __p += " ";
                    } else {
                        __p += ' <div class="no-data">' + ((__t = $t("暂未设置任何关联商机字段")) == null ? "" : __t) + "</div> ";
                    }
                    __p += " </div> </div> ";
                });
                __p += " </div> ";
            } else {
                __p += ' <div class="no-data">' + ((__t = $t("暂未设置售后流程")) == null ? "" : __t) + "</div> ";
            }
            __p += ' </div> </div> <!--基础信息--> <div class="info-item base-info b-g-hide"> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("创建时间")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = moment(SaleAction.CreateTime)) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("应用部门")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = getNameByIds(SaleAction.CircleIDs, "g") || "--") == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("必须新建销售订单方可赢单")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = SaleAction.IsAllowTradeIfWin ? $t("是") : $t("否")) == null ? "" : __t) + '</div> </div> <div class="detail-item"> <span class="item-tit">' + ((__t = $t("赢单时校验之前所有阶段必填项")) == null ? "" : __t) + '</span> <div class="item-con">' + ((__t = SaleAction.CheckFieldIfWin ? $t("是") : $t("否")) == null ? "" : __t) + "</div> </div> ";
            if (SaleAction.IsAllowTrade) {
                __p += ' <div class="detail-item"> <span class="item-tit">' + ((__t = $t("完成售前第几阶段方可新建订单")) == null ? "" : __t) + "</span> ";
                var SaleAction_t = $t("第{{AllowTradeStageOrder}}阶段", {
                    AllowTradeStageOrder: SaleAction.AllowTradeStageOrder
                });
                __p += ' <div class="item-con">' + ((__t = SaleAction_t) == null ? "" : __t) + "</div> </div> ";
            }
            __p += " </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/saleaction/saleaction",["crm-modules/common/util","./addedite/addedite","./utils/migrationdatatransfer","crm-modules/page/list/list"],function(t,e,a){var i=t("crm-modules/common/util"),n=t("./addedite/addedite"),r=t("./utils/migrationdatatransfer"),l=t("crm-modules/page/list/list"),t=Backbone.View.extend({initialize:function(t){this.setElement(t.wrapper),this.initTable()},events:{},initTable:function(){var t,e=this;e.dt||(t=l.extend({__ChangeStatusHandle:function(){e.setNewStatusMigration.apply(e,arguments)},__EditHandle:function(){e.editHandleMigration.apply(e,arguments)},addHandle:function(){e.addHandleMigration.apply(e,arguments)},initComplete:function(){e.$(".last-target-item").after('<a style="float:right; margin-left: 16px" href="http://www.fxiaoke.com/mob/guide/crmdoc/src/7-4-1销售流程设置.html" target="_blank">使用说明</a>')},parseTerm:function(){var t=l.prototype.parseTerm.apply(this,arguments);return t.showManage=!1,t}}),e.dt=new t({wrapper:e.$el,apiname:"saleactionnew",tableOptions:{showMultiple:!1,forbidSernialnumber:!0,hideIconSet:!0}}),e.dt.render&&e.dt.render(["SaleActionNewObj"]))},editHandleMigration:function(t){var a=this,t=$(t.target),e=t.data("action"),t=t.closest(".tr").data("index");allData=a.dt.getCurData().data,e&&null!=(e=allData)&&e[t]&&(e=null==(e=allData)?void 0:e[t],a.fetchWebDetailDataMigration(e,function(t){var e=new n;e.on("refresh",function(t){"out"!=t&&a.dt.refresh(),a._edit=null}),e.show(t),a._edit=e}))},fetchWebDetailDataMigration:function(e,a){var t=this,i={Stages:[]};Promise.all([t.fetchWebDetailMigration(e),t.fetchObjDescribeDataMigration("AccountObj"),t.fetchObjDescribeDataMigration("OpportunityObj")]).then(function(t){r.handleObjectdataMapping(e,i,t[0].Value.data),r.handleStagesdataMapping(i,t[0].Value.data.stage,t),a&&a(i)})},setNewStatusMigration:function(t){var e={},t=$(t.target),a=t.data("action"),t=t.closest(".tr").data("index");allData=this.dt.getCurData().data,a&&null!=(a=allData)&&a[t]&&(e={sale_action_id:(a=null==(a=allData)?void 0:a[t])._id,status:"1"==a.status?"2":"1"},this.fetchChangeStatusMigration(e))},fetchChangeStatusMigration:function(e){var a=this;i.FHHApi({url:"/EM1HNCRM/API/v1/object/SaleActionNewObj/action/ChangeStatus",data:e,success:function(t){0===t.Result.StatusCode?(1==e.status?i.remind($t("启用成功！")):i.remind($t("停用成功")+"！"),a.dt.refresh()):i.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},addHandleMigration:function(t){var e=this,a=new n;a.on("refresh",function(t){"out"!=t&&e.dt.refresh(),e._add=null}),a.show(),e._add=a},fetchWebDetailMigration:function(t){return new Promise(function(e){i.FHHApi({url:"/EM1HNCRM/API/v1/object/SaleActionNewObj/controller/WebDetail",data:{layoutVersion:"V3",objectDataId:t._id,objectDescribeApiName:"SaleActionNewObj",fromRecycleBin:!1,management:!1,serializeEmpty:!1,describeVersionMap:{SaleActionNewObj:3}},success:function(t){0==t.Result.StatusCode?e(t):i.alert(t.Result.FailureMessage)}})})},fetchObjDescribeDataMigration:function(t){return new Promise(function(e){i.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName",type:"post",data:{describe_apiname:t,include_layout:!1,include_related_list:!1,get_label_direct:!0},success:function(t){0===t.Result.FailureCode?e(t.Value.objectDescribe.fields):i.alert(t.Result.FailureMessage)}})})},destroy:function(){var e=this;_.each(["dt","detail","_add","_edit"],function(t){e[t]&&e[t].destroy()})}});a.exports=t});
define("crm-setting/saleaction/utils/migrationdatatransfer",[],function(e,t,i){var a=["select_one","select_many","multi_level_select_one"],s={enumDetailsTransfer:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],i=1<arguments.length?arguments[1]:void 0,a=[];return e.forEach(function(e){var t;"select_one"==i||"select_many"==i?a.push(s.enumItemTransfer(e)):"multi_level_select_one"==i&&((t=s.enumItemTransfer(e)).Children=(e.children||[]).map(function(e){return s.enumItemTransfer(e)}),a.push(t))}),a},enumItemTransfer:function(e){return{EnumDetailID:e.itemOrder,isNew:!0,IsDeleted:!1,IsSysItem:!1,ItemCode:e.itemCode,ItemName:e.itemName}},checkIsAllowEditOption:function(t){return-1!=a.findIndex(function(e){return e==t})},handleObjectdataMapping:function(e,t,i){t.Description="",t.IsAllowOldData=!0,t.SaleActionID=e._id,t.Name=i.name,t.CircleIDs=(i.sale_action_circle_id||[]).map(function(e){return Number(e)}),t.Status=i.status,t.CreateTime=i.create_time,t.IsAllowTradeIfWin=i.is_allow_trade_if_win,t.IsAllowTrade=i.is_allow_trade,t.AllowTradeStageOrder=i.allow_trade_stage_order,t.CheckFieldIfWin=i.check_field_if_win},handleStagesdataMapping:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],n=2<arguments.length?arguments[2]:void 0;e.Stages=t.map(function(e){var t=[],i=[],a=[];return(e.stage_user_field_info||[]).forEach(function(e){("AccountObj"===e.field_belong?t:"OpportunityObj"===e.field_belong?i:a).push(e)}),{SaleActionStageID:e._id,Name:e.name,Description:e.description,IsTimeoutRemind:e.is_timeout_remind,RemainDays:e.remain_days,IsLeaderConfirm:e.is_leader_confirm,IsFinishByContacts:e.is_finish_by_contacts,ContactCount:e.contact_count,Type:e.type,AsIsThatDayRemind:e.as_is_that_day_remind,AsThatRemainDays:e.as_that_remain_days,AsIsTimeoutRemind:e.as_is_timeout_remind,AsRemainDays:e.as_remain_days,Weight:e.weight,StageFlag:e.stage_flag,CustomerFieldInfos:[],OppoFieldInfos:[],UDFieldInfos:a.length?s.handleStagesUdFieldInfoMigration(a):[],CustomerDescribe:t.length?s.handleStagesDescribeMigration(t,n[1]):"",CustomerIsRequiredDescribe:t.length?s.handleStagesDescribeIsRequiredMigration(t):"",OpportunityDescribe:i.length?s.handleStagesDescribeMigration(i,n[2]):"",OpportunityIsRequiredDescribe:i.length?s.handleStagesDescribeIsRequiredMigration(i):""}})},handleStagesUdFieldInfoMigration:function(e){return e.map(function(e){var t;return{FieldName:e.field_api_name,FieldCaption:e.field_caption,FieldOrder:Number(e.field_order),FieldType:s.fieldTypeTransfer(e.field_type,Number(e.decimal_digits)),IsNotNull:e.is_not_null,IsWatermark:e.is_watermark,UserDefinedFieldID:e._id,DecimalDigits:e.decimal_digits||0,EnumDetails:null!=(t=e.select_options)&&t.length?s.enumDetailsTransfer(e.select_options,e.field_type):[],IsAllowEditOption:s.checkIsAllowEditOption(e.field_type),EnumName:(null==e||null==(t=e.select_options)||null==(t=t[0])?void 0:t.enumName)||"",AggregateFieldName:"",AggregateObject:0,AggregateType:"",CalFormula:"",CascadeEnumRelations:[],CascadeFields:[],CodeRule:{},DefaultIsZero:!0,DefaultValue:"",ExtendProp:'{"New_IsVisible":true,"New_IsEditable":true,"Modify_IsVisible":true,"Modify_IsEditable":true,"View_IsVisible":true,"IsAllowHide":true}',FieldLength:100,FieldProperty:2,Fields:"",Formula:"",IsAllowEditNotNull:!0,IsCalculateFieldHidden:!1,IsCodeField:!1,IsSelfClone:!1,IsVisible:!0,ObjectAggregate:null,ObjectRelation:null,OwnerType:7,ReferRule:"",RelationField:"",ReturnValueType:"",UsedIn:"",create_time:e.create_time,created_by:e.created_by,is_deleted:e.is_deleted,last_modified_by:e.last_modified_by,last_modified_time:e.last_modified_time,object_describe_api_name:e.object_describe_api_name,sale_action_stage_id:e.sale_action_stage_id,sale_action_stage_id__relation_ids:e.sale_action_stage_id,tenant_id:e.tenant_id,version:e.version}})},handleStagesDescribeMigration:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],i={};return e.forEach(function(e){i[e.field_api_name]=t[e.field_api_name]}),JSON.stringify(i)},handleStagesDescribeIsRequiredMigration:function(e){var t={};return e.forEach(function(e){t[e.field_api_name]=!!e.is_not_null}),JSON.stringify(t)},fieldTypeTransfer:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:0;return"text"==e?2:"long_text"==e?3:"number"==e&&0==t?4:"number"==e&&0<t?5:"currency"==e?6:"date"==e?7:"select_one"==e?8:"select_many"==e?9:"image"==e?10:"multi_level_select_one"==e?14:"file_attachment"==e?17:"phone_number"==e?18:"email"==e?19:0},submitFieldTypeTransfer:function(e){switch(e){case 2:return"text";case 3:return"long_text";case 4:case 5:return"number";case 6:return"currency";case 7:return"date";case 8:return"select_one";case 9:return"select_many";case 10:return"image";case 14:return"multi_level_select_one";case 17:return"file_attachment";case 18:return"phone_number";case 19:return"email";default:return"0"}},formatAddStagesDataMigration:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:[],i=2<arguments.length?arguments[2]:void 0,a=e.objectData,n=e.objectDescribe;return t.map(function(e){var t={description:e.Description,name:e.Name,stage_flag:e.StageFlag+"",as_is_that_day_remind:e.AsIsThatDayRemind,as_that_remain_days:e.AsThatRemainDays,as_is_timeout_remind:e.AsIsTimeoutRemind,as_remain_days:e.AsRemainDays,is_leader_confirm:e.IsLeaderConfirm,is_finish_by_contacts:e.IsFinishByContacts,contact_count:e.ContactCount,is_timeout_remind:e.IsTimeoutRemind,remain_days:e.RemainDays,weight:e.Weight,stage_order:e.StageOrder,type:e.Type+"",object_describe_api_name:a.object_describe_api_name,record_type:a.record_type,object_describe_id:n._id,lock_rule:"default_lock_rule",lock_status:"0",opportunity_stage_field:s.formatAddFieldMigration((e.SaleStageFieldRelationRequests||[]).filter(function(e){return 8==e.OwnerType}),"OpportunityObj",i),account_stage_field:s.formatAddFieldMigration((e.SaleStageFieldRelationRequests||[]).filter(function(e){return 2==e.OwnerType}),"AccountObj",i),udef_stage_field:s.formatAddFieldMigration(e.UserDefinedFieldRequests,"UdobjObj")};return"Edit"==i&&(t._id=e.SaleActionStageID),t})},formatAddFieldMigration:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],n=1<arguments.length?arguments[1]:void 0;return e.map(function(e){var t,i={},a={is_not_null:e.IsNotNull,field_order:e.FieldOrder,field_belong:n,field_api_name:e.FieldApiName};return"UdobjObj"==n&&(i={is_watermark:e.IsWatermark,field_api_name:e.FieldName,decimal_digits:e.DecimalDigits,field_caption:e.FieldCaption,field_type:s.submitFieldTypeTransfer(e.FieldType),field_order:e.FieldOrder+"",is_deleted:e.is_deleted,create_time:e.create_time,created_by:e.created_by,last_modified_by:e.last_modified_by,last_modified_time:e.last_modified_time,object_describe_api_name:e.object_describe_api_name,sale_action_stage_id:e.sale_action_stage_id,sale_action_stage_id__relation_ids:e.sale_action_stage_id__relation_ids,tenant_id:e.tenant_id,version:Number(e.version)+1+"",_id:e.UserDefinedFieldID},null!=e)&&null!=(t=e.EnumDetails)&&t.length&&(i.select_options=JSON.stringify(s.formatSelectOptionsMigration(e.EnumDetails))),Object.assign({},a,i)})},formatSelectOptionsMigration:function(e){return e.map(function(e){var t,i=s.enumItemFormat(e);return null!=e&&null!=(t=e.Children)&&t.length&&(i.children=e.Children.map(function(e){return s.enumItemFormat(e)})),i})},enumItemFormat:function(e){return{itemName:e.ItemName,itemCode:e.ItemCode+"",itemOrder:e.ItemOrder+""}},addAndEditParamMigration:function(e,t,i,a,n){var r=e.objectData,r={status:r.status,is_default:!1,requestId:n,object_describe_api_name:r.object_describe_api_name,object_describe_id:e.objectDescribe._id,record_type:r.record_type,created_by:r.created_by,data_own_department:r.data_own_department,data_own_department__r:r.data_own_department__r,name:t.Name,description:t.Description,sale_action_circle:(t.CircleIDs||[]).map(function(e){return Number(e)}),is_allow_trade_if_win:t.IsAllowTradeIfWin,check_field_if_win:t.CheckFieldIfWin,is_allow_trade:t.IsAllowTrade,allow_trade_stage_order:t.IsAllowTrade?t.AllowTradeStageOrder:0};return"Edit"==i&&(r._id=t.SaleActionID),{object_data:r,details:{SaleActionStageNewObj:s.formatAddStagesDataMigration(e,a,i)},maskFieldApiNames:{SaleActionNewObj:[]},seriesId:n}}};i.exports=s});