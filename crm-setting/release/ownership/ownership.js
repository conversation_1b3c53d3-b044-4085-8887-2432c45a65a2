define("crm-setting/ownership/helper",["crm-modules/common/filtergroup/setting_helper"],function(e,a,t){e=e("crm-modules/common/filtergroup/setting_helper");t.exports=$.extend(!0,e,{compare:[{value:1,name:$t("等于"),value1:"EQ"},{value:2,name:$t("不等于"),value1:"N"},{value:3,name:$t("大于"),value1:"GT"},{value:4,name:$t("大于等于"),value1:"GTE"},{value:5,name:$t("小于"),value1:"LT"},{value:6,name:$t("小于等于"),value1:"LTE"},{value:7,name:$t("包含"),value1:"LIKE"},{value:8,name:$t("不包含"),value1:"NLIKE"},{value:9,name:$t("为空"),value1:"IS"},{value:10,name:$t("不为空"),value1:"ISN"},{value:11,name:$t("早于"),value1:"LT"},{value:12,name:$t("晚于"),value1:"GT"},{value:13,name:$t("属于"),value1:"IN"},{value:14,name:$t("不属于"),value1:"NIN"},{value:17,name:$t("时间段"),value1:"BETWEEN"},{value:19,name:$t("自定义"),value1:"CUSTOM"}],getType:function(e,a){switch(e){case"select_one":return"selectone";case"select_many":return"selectmany";case"country":case"province":case"city":case"district":return CRM.util.isCrmRulesNewArea()?"area":"selectone";case"true_or_false":case"record_type":return"selectone";case"date":return"date"+(a?"-relate":"");case"date_time":return"datetime"+(a?"-relate":"");case"time":return"time"+(a?"-relate":"");case"employee":case"department":case"exemployee":return e;case"number":case"currency":case"percentile":return"text"+(a?"-relate":"");default:return"text"}},getCompare:function(e){var a,t=this;switch(e){case"text":case"long_text":case"phone_number":case"email":case"url":case"auto_number":case"object_reference":a=[1,2,7,8,9,10];break;case"department":a=[1,2,7,8,13,14,9,10];break;case"date":case"date_time":case"time":a=[1,11,12,17,9,10];break;case"select_one":case"select_many":case"employee":case"record_type":case"exemployee":a=[1,2,13,14,9,10];break;case"country":case"province":case"city":case"district":a=[1,2,13,14,9,10];break;case"true_or_false":a=[1];break;case"count":case"number":case"currency":case"percentile":a=[1,2,3,4,5,6];break;case"master_detail":a=[1,2,7,8,9,10];break;case"location":a=[1,2,7,8];break;default:a=[1]}return _.map(a,function(a){return _.find(t.compare,function(e){return e.value===a})})},getTypeByCompare:function(e,a,t){if(18===e.value)return"between";if(-1!=["select_one","select_many"].indexOf(t)&&-1!=[1,2].indexOf(e.value))return"selectone";if(-1!=[17,19].indexOf(e.value)){var t=["date","datetime","time"],n=t.indexOf(a);if(-1<n)return t[n]+"2"}else if(_.contains(["selectone","selectmany"],a)&&_.contains([13,14],e.value))return"selectmany"},formatFields:function(e,a){var t={};return _.each(e,function(e){!e.is_index&&"formula"==e.type||(a.selectone_multiple&&-1!=["select_one","record_type"].indexOf(e.type)&&(e.type="select_many"),t[e.api_name]=e)}),t}})});
define("crm-setting/ownership/ownership_define/ownership_define",["crm-modules/common/util","./template/tpl-html","crm-modules/common/filtergroup/filtergroup","crm-setting/ownership/helper"],function(e,t,i){var o=e("crm-modules/common/util"),r=e("./template/tpl-html"),n=e("crm-modules/common/filtergroup/filtergroup"),a=e("crm-setting/ownership/helper"),e=Backbone.View.extend({initialize:function(e){var t=this;this.apiname=e.apiname,this.comps={},this.setElement(e.wrapper),this.$el.html(r({apiname:this.apiname})),this.fetchGlobalRule(function(e){t.initFilter(e),t.initSaveBtn()})},events:{"click .j-save":"saveHandle","click .j-default":"defaultHandle"},fetchGlobalRule:function(t){o.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/get_object_limit_global_filter",data:{objectApiName:this.apiname},success:function(e){0==e.Result.StatusCode&&(e=e.Value.objectLimitRuleGlobalFilter,t)&&t(JSON.parse(e.wheres||"[]"))}})},initSaveBtn:function(){this.comps.save||(this.comps.save=FxUI.create({wrapper:this.$(".j-save").get(0),template:'<fx-button size="mini" type="primary">'+$t("保存")+"</fx-button>",data:function(){return{}}}))},initFilter:function(e){var t=[];"LeadsObj"===this.apiname?t=["leads_status","transform_time","assigner_id","assigned_time","assigned_time","expire_time","remaining_time","last_modified_by","owner","remind_days","resale_count","returned_time","owner_department","last_modified_time","account_id","contact_id","new_opportunity_id","opportunity_id","out_tenant_id","extend_days"].concat(CRM.get("isOpenPRM")?[]:["out_owner","partner_id"]):"AccountObj"===this.apiname?t=["back_reason","lock_status","owner","owner_department","created_by","remaining_time","last_modified_by","lock_status","data_own_department","account_status","fax","url","recycled_reason","total_refund_amount","last_deal_closed_amount","filling_checker_id","is_remind_recycling","owner_modified_time","expire_time","returned_time","claimed_time","completed_field_quantity","remind_days","extend_days"].concat(CRM.get("isOpenPRM")?[]:["out_owner","partner_id"]):"NewOpportunityObj"===this.apiname&&(t=["owner","owner_department","created_by","remaining_time","last_modified_time","last_modified_by","lock_status","data_own_department","owner_modified_time","expire_time","returned_time","claimed_time","remind_days","extend_days"].concat(CRM.get("isOpenPRM")?[]:["out_owner","partner_id"])),this.comps.filtergroup=new n({$wrapper:$(".ownership-define-filter",this.$el),title:$t("且(AND)"),width:850,addBtnName:$t("新增或关系"),apiname:this.apiname,selectone_multiple:!0,defaultValue:e,OR_MIN:0,filterType:["url","time","image","file_attachment","percentile","count","quote","formula","signature","employee_many","department_many","object_reference_many","html_rich_text"],filterApiname:t,helper:a,parseCompare:function(e,t){return e=CRM.get("isOpenPRM")&&"out_owner"===t.api_name?e.filter(function(e){return-1!=["IS","ISN"].indexOf(e.value1)}):e}})},getDefaultValue:function(){return[{connector:"OR",filters:[{field_name:"biz_status",operator:"EQ",field_values:["un_assigned","transformed","closed"]},{field_name:"life_status",operator:"EQ",field_values:["ineffective","invalid"]}]}]},saveHandle:function(){var e;return this.hideError(),this.comps.filtergroup.valid()?this.validLifeStatus()?(e=this.comps.filtergroup.getValue(),void o.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/save_object_limit_global_filter",data:{objectApiName:this.apiname,objectLimitRuleGlobalFilter:{wheres:e}},success:function(e){0==e.Result.StatusCode&&o.remindSuccess(e.Value.message)}},{submitSelector:this.comps.save.$el})):null:(this.showError(),null)},defaultHandle:function(){var t=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/get_default_object_limit_global_filter",data:{objectApiName:this.apiname},success:function(e){0==e.Result.StatusCode&&(e=e.Value.objectLimitRuleGlobalFilter,t.comps.filtergroup.resetRender(JSON.parse(e.wheres||"[]")))}})},validLifeStatus:function(){for(var e=this,t=!1,i=JSON.parse(this.comps.filtergroup.getValue()),r=0;r<i.length;r++)for(var n=i[r].filters,a=0;a<n.length;a++){var s=n[a];if("life_status"===s.field_name&&-1==s.field_values.indexOf("normal"))return o.showErrmsg(e.$el.find(".crm-filter-group"),$t("sfa.ownership.validLife.yes")),!1;if("life_status"===s.field_name&&-1!=s.field_values.indexOf("invalid"))return o.showErrmsg(e.$el.find(".crm-filter-group"),$t("sfa.ownership.validLife.noSelct")),!1;"life_status"==s.field_name&&(t=!0)}return!!t||(o.showErrmsg(e.$el.find(".crm-filter-group"),$t("sfa.ownership.validLife.yes")),!1)},show:function(){this.$el.show()},hide:function(){this.$el.hide()},showError:function(){o.showErrmsg(this.$el.find(".crm-filter-group"),$t("请填写筛选值!"))},hideError:function(){o.hideErrmsg(this.$el.find(".crm-filter-group"))},destroy:function(){for(var e in this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null}});i.exports=e});
define("crm-setting/ownership/ownership_define/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="ownership-define-container"> <div class="ownership-define-scroll"> <div class="info"> ';
            if (obj.apiname === "LeadsObj") {
                __p += ' <span class="msg">' + ((__t = $t("定义占有保有量的线索的条件")) == null ? "" : __t) + "</span> ";
            } else if (obj.apiname === "NewOpportunityObj") {
                __p += ' <span class="msg">' + ((__t = $t("定义商机保有量的通用条件")) == null ? "" : __t) + "</span> ";
            } else {
                __p += ' <span class="msg">' + ((__t = $t("定义占有保有量的客户的条件")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </div> <div class="ownership-define-filter"> </div> </div> <div class="footer"> <span class="save-btn j-save"></span> <!-- <a href="javascript:;" class="default-btn j-default">恢复默认</a> --> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/ownership/ownership_rule/add/form/add_rule/add_rule_detail/form/filter_group/filter_group",["crm-modules/action/field/field","crm-modules/common/util","crm-modules/common/filtergroup/filtergroup","crm-setting/ownership/helper"],function(e,t,i){var r=e("crm-modules/action/field/field").C.Base,n=e("crm-modules/common/util"),o=e("crm-modules/common/filtergroup/filtergroup"),a=e("crm-setting/ownership/helper"),e=r.extend({render:function(){this.apiname=this.model.get("apiname"),this.init()},init:function(){var e=this.get("data").filter_group,t=[];"LeadsObj"===this.apiname?t=["completion_rate","life_status","leads_status","transform_time","assigner_id","assigned_time","assigned_time","expire_time","remaining_time","last_modified_by","owner","remind_days","resale_count","returned_time","owner_department","last_modified_time","account_id","contact_id","new_opportunity_id","opportunity_id","out_tenant_id","extend_days"].concat(CRM.get("isOpenPRM")?[]:["out_owner","partner_id"]):"AccountObj"===this.apiname?t=["completion_rate","life_status","back_reason","lock_status","owner","owner_department","created_by","remaining_time","last_modified_by","lock_status","data_own_department","account_status","fax","url","recycled_reason","total_refund_amount","last_deal_closed_amount","filling_checker_id","is_remind_recycling","owner_modified_time","expire_time","returned_time","claimed_time","completed_field_quantity","remind_days","extend_days","account_path","industry_ext"].concat(CRM.get("isOpenPRM")?[]:["out_owner","partner_id"]):"NewOpportunityObj"===this.apiname&&(t=["completion_rate","life_status","owner","owner_department","created_by","remaining_time","last_modified_time","last_modified_by","lock_status","data_own_department","owner_modified_time","expire_time","returned_time","claimed_time","remind_days","extend_days"].concat(CRM.get("isOpenPRM")?[]:["out_owner","partner_id"])),this.widget=new o({$wrapper:this.$el,title:$t("且(AND)"),width:750,AND_MAX:10,addBtnName:$t("新增或关系"),apiname:this.apiname,selectone_multiple:!0,defaultValue:e,filterType:["url","time","image","file_attachment","percentile","count","quote","formula","signature","employee_many","department_many","html_rich_text","object_reference_many"],filterApiname:t,helper:a,parseCompare:function(e,t){return e=CRM.get("isOpenPRM")&&"out_owner"===t.api_name?e.filter(function(e){return-1!=["IS","ISN"].indexOf(e.value1)}):e}})},getValue:function(){var e=this.widget.getValue(),t=e=JSON.parse(e),t=(e.type,t.map(function(e){return{filters:e.filters.map(function(e){return{operator:e.operator,operator_name:e.operator_name,field_name:e.field_name,field_values:e.field_values,isIndex:!1,fieldNum:0,connector:"AND",isObjectReferencea:!1}}),connector:"OR"}}));return this.hideError(),this.widget.valid()?t:(this.showError(),null)},show:function(){this.$el.show()},hide:function(){this.$el.hide()},showError:function(){n.showErrmsg(this.$el,$t("请填写筛选值!"))},hideError:function(){n.hideErrmsg(this.$el)},destroy:function(){this.widget&&this.widget.destroy()}});i.exports=e});
define("crm-setting/ownership/ownership_rule/add/form/add_rule/add_rule_detail/form/limit_number/limit_number",["crm-modules/action/field/field","crm-modules/common/util","crm-modules/common/filtergroup/filtergroup"],function(e,t,i){var n=e("crm-modules/action/field/field").C.number,a=e("crm-modules/common/util"),e=(e("crm-modules/common/filtergroup/filtergroup"),n.extend({_onBlurHandle:function(e){n.prototype._onBlurHandle.apply(this,arguments),this.getData()<1&&this.showError(null,$t("保有量范围必须是{{n}}的正整数",{n:"1-99999"}))},render:function(){n.prototype.render.apply(this,arguments),"NewOpportunityObj"!=this.get("apiname")&&"LeadsObj"!=this.get("apiname")&&"AccountObj"!=this.get("apiname")||(this.$el.css({position:"relative"}),this.createSelectIcon())},createSelectIcon:function(){var t=this,e=this.getData("scope"),i=this.getData("filter_type"),n=this.model.get("isEdit"),l=this.model.getData("limitObjectApiName"),a=this.model.getData("limitFieldName"),o=this.model.getData("limitNumberLabel");"employee"==e?(1!=i&&2!=i||this.getObjFields("PersonnelObj",function(e){t.addToolIcon(e||[]),n&&"PersonnelObj"==l&&a&&!_.findWhere(e,{api_name:a})&&t.showError(null,$t("字段被禁用，无法获取"))}),n&&l&&("PersonnelObj"!=l?this.showError(null,$t("当前参数仅在为部门且是合作伙伴配置保有量时有效")):1!=i&&2!=i&&this.showError(null,$t("当前参数仅在为内部员工配置保有量时有效")),this.addDispalyPanel(o))):"organization"==e&&(3!=i&&4!=i||this.getObjFields("PartnerObj",function(e){t.addToolIcon(e||[]),n&&"PartnerObj"==l&&a&&!_.findWhere(e,{api_name:a})&&t.showError(null,$t("字段被禁用，无法获取"))}),n)&&l&&("PartnerObj"!=l?this.showError(null,$t("当前参数仅在为内部员工配置保有量时有效")):3!=i&&4!=i&&this.showError(null,$t("当前参数仅在为部门且是合作伙伴配置保有量时有效")),this.addDispalyPanel(o))},addToolIcon:function(e){var i=this;this.widget=FxUI.create({wrapper:this.$el.get(0),template:'<div style="position:absolute; top:1px; right:1px;">\n                                    <fx-popover\n                                        popper-class="opportunityrention-rule-detail-pop"\n                                        placement="bottom-start"\n                                        width="200"\n                                        v-model="visible"\n                                        trigger="click">\n                                            <fx-input\n                                                size="small"\n                                                clearable\n                                                :placeholder="$t(\'搜索\')"\n                                                prefix-icon="el-icon-search"\n                                                @input="filterMethod"\n                                                v-model="value">\n                                            </fx-input>\n                                            <div class="pop-panel">\n                                                <ul>\n                                                    <li @click="clickItem(item, $event)"\n                                                        v-for="item in options"\n                                                        :key="item.api_name">{{item.label}}</li>\n                                                </ul>\n                                            </div>\n                                            <i slot="reference" class="el-icon-s-tools" style="font-size:18px;padding:0 10px;color:#bcc5ce;line-height:32px;cursor:pointer;"></i>\n                                    </fx-popover>\n                                </div>',data:function(){return{options:e,value:"",visible:!1,displayObj:{}}},computed:{displayName:function(){return this.displayObj.label&&"${".concat("employee"==i.getData("scope")?$t("人员"):$t("crm.合作伙伴"),".").concat(this.displayObj.label,"}")||""}},created:function(){this.backup=this.options},methods:{filterMethod:FxUI.Utils.throttle(150,!1,function(){var t=this;this.value?this.options=this.backup.filter(function(e){return-1!=(e.label+"").indexOf(t.value)}):this.options=this.backup}),clickItem:function(e){this.displayObj=e,this.visible=!1,i.addDispalyPanel(this.displayName),i.hideError(),i.setValue(0),_.each({limitFieldName:e.api_name,limitNumberLabel:this.displayName,limitObjectApiName:"employee"==i.getData("scope")?"PersonnelObj":"PartnerObj"},function(e,t){i.setData(e,t)})}}}),this.widget.$on("clear",function(){this.displayObj={}})},addDispalyPanel:function(e){var t=this;this.widgetPanel&&this.widgetPanel.destroy(),this.widgetPanel=FxUI.create({wrapper:this.$el.get(0),template:'<div v-show="display" @mouseover="close=true" @mouseout="close=false" class="opportunityrention-rule-detail-display">\n                                <div>\n                                    <div>{{displayName}}</div>\n                                    <i class="el-icon-error" \n                                        v-show="close"\n                                        @click="clearDisplay"\n                                        style="padding: 0 10px;font-size:16px;color:#bcc5ce;line-height:32px;cursor:pointer;"></i>\n                                </div>\n                            </div>',data:function(){return{display:!0,close:!1,displayName:e}},methods:{clearDisplay:function(){t.widget&&t.widget.$emit("clear"),this.displayName="",this.display=!1,t.setValue("")}}})},getObjFields:function(t,i){var n=this,e=CRM.get("fields."+t);function l(e){e=a.deepClone(e);return _.filter(e,function(e,t){return(_.contains(["count","formula"],e.type)&&e.is_index&&"number"==e.return_type||"number"==e.type)&&!_.contains(n.getFieldMustFilter(),e.api_name)})}e?i&&i(l(e)):CRM.util.getFieldsByApiName(t,!0).done(function(){var e=CRM.get("fields."+t);i&&i(l(e))})},getFieldMustFilter:function(){return["version","package","tenant_id","object_describe_api_name","object_describe_id","_id","extend_obj_data_id","sales_process_id","sales_process_name","oppo_stage_id","is_deleted","pin_yin","filling_checker_id","product_group_id","total_refund_amount","is_remind_recycling","Address","SalesOrderProductObj","lock_user","lock_rule","order_by","life_status_before_invalid","active_status","relevant_team","enterprise_wechat_user_id","refresh_duplicated_version","uniform_social_credit_code","poi_information"]},getValue:function(){var e=this.widgetPanel&&this.widgetPanel.displayName||"";if(!e)return n.prototype.getValue.apply(this,arguments)},proxyGetValue:function(){var e,t=this.widgetPanel&&this.widgetPanel.displayName||"";return t?(e="employee"==this.getData("scope")?"PersonnelObj":"PartnerObj",this.getData("limitObjectApiName"),{limitNumber:void 0,limitObjectApiName:e,limitFieldName:this.getData("limitFieldName")||this.widget&&this.widget.displayObj.api_name,limitNumberLabel:t}):{limitNumber:n.prototype.getValue.apply(this,arguments)}},destroy:function(){this.widget&&(this.widget.destroy(),this.widget=null),this.widgetPanel&&(this.widgetPanel.destroy(),this.widgetPanel=null),n.prototype.destroy.apply(this,arguments)}}));i.exports=e});
define("crm-setting/ownership/ownership_rule/add/form/add_rule/add_rule_detail/model",[],function(e,t,r){r.exports={fetch:function(){this.parse()},parse:function(){var e=this.getFields();this.set({layout:[{api_name:"basic1",label:"",columns:1,components:[e.true_or_false,e.filter_group]},{api_name:"basic2",label:"",columns:2,components:[e.limit_number]}],fields:e})},getFields:function(){var e={LeadsObj:$t("全部线索"),AccountObj:$t("全部客户"),NewOpportunityObj:$t("全部")+$t("crm.商机2")};return{true_or_false:{api_name:"true_or_false",type:"true_or_false",label:$t("数据范围"),options:[{label:$t("按条件设置规则"),value:1},{label:e[this.get("apiname")],value:2}]},filter_group:{api_name:"filter_group"},limit_number:{api_name:"limit_number",type:"number",renderType:"number",label:$t("保有量"),is_required:!0,placeholder:$t("请输入"),length:5}}}}});
define("crm-setting/ownership/ownership_rule/add/form/add_rule/add_rule_detail/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="add-rule-detail-container"> </div>';
        }
        return __p;
    };
});
function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(t,e){var r,o=Object.keys(t);return Object.getOwnPropertySymbols&&(r=Object.getOwnPropertySymbols(t),e&&(r=r.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),o.push.apply(o,r)),o}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach(function(e){_defineProperty(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _defineProperty(e,t,r){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0===r)return("string"===t?String:Number)(e);r=r.call(e,t||"default");if("object"!=_typeof(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/ownership/ownership_rule/add/form/add_rule/add_rule_detail/view",["crm-modules/action/field/field","./form/filter_group/filter_group","./form/limit_number/limit_number","crm-modules/common/util"],function(e,t,r){var o=e("crm-modules/action/field/field"),i=e("./form/filter_group/filter_group"),n=e("./form/limit_number/limit_number"),u=e("crm-modules/common/util");r.exports={mycomponents:{filter_group:i,limit_number:n},initView:function(){var t=this;o.View.prototype.initView.apply(this,arguments),this.forms.true_or_false.setValue(1),t.forms.filter_group.show(),this.get("data").filter_group&&0==this.get("data").filter_group.length&&(this.forms.true_or_false.setValue(2),t.forms.filter_group.hide()),this.forms.true_or_false.on("change",function(e){1==e?(t.forms.filter_group.show(),t.forms.filter_group.widget.removeAllFilters(),t.forms.filter_group.widget.addFilter()):(t.forms.filter_group.hide(),u.hideErrmsg(t.forms.filter_group.$el))})},collect:function(){var e=_objectSpread({limitNumber:this.forms.limit_number.getValue()},this.forms.limit_number.proxyGetValue());return 1==this.forms.true_or_false.getValue()?e.filter_group=this.forms.filter_group.getValue():e.filter_group=[],e},submit:function(){var e=this.collect();this.validate()&&(2==e.true_or_false&&(e.filter_group=[]),"edit"==this.get("layout_type")&&(e.__tbIndex=this.get("data").__tbIndex),this.trigger("success",e))}}});
define("crm-setting/ownership/ownership_rule/add/form/add_rule/add_rule",["crm-modules/action/field/field","crm-modules/common/filtergroup/util","crm-widget/table/table","crm-modules/action/field/field","./add_rule_detail/view","./add_rule_detail/model","crm-modules/common/util","./template/tpl-html","crm-setting/ownership/helper"],function(t,e,i){var a=t("crm-modules/action/field/field").C.Base,r=(t("crm-modules/common/filtergroup/util"),t("crm-widget/table/table")),l=t("crm-modules/action/field/field"),n=t("./add_rule_detail/view"),d=t("./add_rule_detail/model"),o=t("crm-modules/common/util"),s=t("./template/tpl-html");t("crm-setting/ownership/helper");i.exports=a.extend({options:{MAX_COUNT:20,curCount:0},events:{"click .j-add-rule-detail-btn":"addRuleDeatilHandle"},dataEvents:{"change:scope change:filter_type":"hideError"},render:function(){this.comps={},this.apiname=this.model.get("apiname"),this.data=this.formatServerData(this.model.getData("add_rule")),this.$el.html(s({MAX_COUNT:this.options.MAX_COUNT,curCount:this.options.curCount})),this.createRuleList(),this.setCount(this.data.length)},addRuleDeatilHandle:function(){var e=this;l.add({title:$t("新增规则明细"),className:"ownership-addrule-detail",Model:l.Model.extend(d),View:l.View.extend(n),record_type:"default__c",show_type:"dialog",size:"hg",apiname:this.model.get("apiname"),data:_.extend({},this.getData(),this.getExtraData()),success:function(t){e.data.push(t),e.table.doStaticData(e.doStaticData()),e.setCount(e.data.length),e.hideError()}})},getData:function(){return{rule_name:"rule_name",true_or_false:1,scope:this.model.getData("scope"),filter_type:this.model.getData("filter_type")}},getExtraData:function(){return{scope:this.model.getData("scope"),filter_type:this.model.getData("filter_type")}},formatServerData:function(t){return t?t.map(function(t){return{limitNumber:t.limitObjectApiName?void 0:t.limitNumber,limitObjectApiName:t.limitObjectApiName,limitFieldName:t.limitFieldName,filter_group:JSON.parse(t.wheres)}}):[]},doStaticData:function(){for(var t=[],e=0;e<this.data.length;e++){var i=this.data[e],a=i.limitNumber,l=i.limitObjectApiName,r=i.limitFieldName,n=i.filter_group;t.push(l?{limitObjectApiName:l,limitFieldName:r,limitNumberLabel:i.limitNumberLabel||"${".concat("PersonnelObj"==l?$t("人员"):$t("crm.合作伙伴"),".").concat((_(CRM.get("fields."+l)).findWhere({api_name:r})||{}).label,"}"),filter_group:n}:{limitNumber:a,filter_group:n})}return t},setCount:function(t){$(".max-count",this.$el).html("(".concat(t,"/").concat(this.options.MAX_COUNT,")"));var e=$(".j-add-rule-detail-btn",this.$el);t<this.options.MAX_COUNT?e.show():e.hide()},getValue:function(){return this.hideError(),this.valid()?this.data:(this.showError(),null)},createRuleList:function(){var a=this,l=this;l.hideError(),l.table&&l.table.destroy(),l.table=new r({$el:$(".add-rule-table",l.$el),className:"crm-table add-rule-list",tableName:"tableName",showFilerBtn:!1,showMultiple:!1,showMoreBtn:!1,showTermBatch:!0,noAllowedWrap:!1,autoHeight:!0,noAlwaysShowPage:!0,doStatic:!0,columns:[{data:"filter_group",title:$t("数据范围"),width:250,render:function(t,e,i){var a;return t.length?(a=CRM.get("fields.".concat(l.apiname)),CRM.util.parseFiltersRule(t,a)):{LeadsObj:$t("全部线索"),AccountObj:$t("全部客户"),NewOpportunityObj:$t("全部")+$t("crm.商机2")}[l.apiname]}},{data:"limitNumber",title:$t("保有量"),width:80,render:function(t,e,i){var a=i.limitNumberLabel;return _.isUndefined(i.limitNumber)?a:t}},{data:"",title:$t("操作"),width:80,render:function(t,e,i){return'<div class="ops-btns">\n\t\t\t\t\t\t\t\t\t<a data-operate="edit">'.concat($t("编辑"),'</a>\n\t\t\t\t\t\t\t\t\t<a data-operate="delete">').concat($t("删除"),"</a>\n\t\t\t\t\t\t\t\t</div>")}}]}),l.table.doStaticData(l.doStaticData()),l.table.on("trclick",function(t,e,i){i=i.data("operate");t.limit_number=t.limitNumber,"edit"==i?a.editHandle(t):"delete"==i&&a.deleteHandle(t)})},editHandle:function(t){var e=this;l.edit({noUIAction:!0,title:$t("编辑规则明细"),Model:l.Model.extend(d),View:l.View.extend(n),className:"ownership-addrule-detail",record_type:"default__c",show_type:"dialog",size:"hg",height:700,data:_.extend({},t,this.getExtraData()),apiname:this.model.get("apiname"),success:function(t){e.data[t.__tbIndex]=t,e.table.doStaticData(e.doStaticData()),e.setCount(e.data.length)}})},deleteHandle:function(t){this.data.splice(t.__tbIndex,1),this.table.doStaticData(this.doStaticData()),this.setCount(this.data.length)},valid:function(){if(this.data.length){var t=this.model.getData("scope"),e=this.model.getData("filter_type");if("employee"==t){if(1==e||2==e)return!_.find(this.data,function(t){return t.limitObjectApiName&&"PersonnelObj"!=t.limitObjectApiName})}else if("organization"==t&&(3==e||4==e))return!_.find(this.data,function(t){return t.limitObjectApiName&&"PartnerObj"!=t.limitObjectApiName});return!_.find(this.data,function(t){return t.limitObjectApiName})}return!1},showError:function(){o.showErrmsg(this.$el,$t("请设置正确的保有量规则明细!"))},hideError:function(){o.hideErrmsg(this.$el)}})});
define("crm-setting/ownership/ownership_rule/add/form/add_rule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="add-rule-container"> <div class="add-rule-title"> <i class="icon">*</i><span class="label">' + ((__t = $t("保有量规则明细")) == null ? "" : __t) + '</span><span class="max-count">(' + ((__t = curCount) == null ? "" : __t) + "/" + ((__t = MAX_COUNT) == null ? "" : __t) + ')</span> </div> <div class="add-rule-table"> </div> <div class="add-rule-btn j-add-rule-detail-btn"> <em>+</em><span>' + ((__t = $t("新增规则明细")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/ownership/ownership_rule/add/form/filter_group/filter_group",["crm-modules/action/field/field","crm-modules/common/util","crm-modules/common/filtergroup/filtergroup","crm-setting/ownership/helper"],function(e,t,i){var r=e("crm-modules/action/field/field").C.Base,n=e("crm-modules/common/util"),o=e("crm-modules/common/filtergroup/filtergroup"),l=e("crm-setting/ownership/helper"),e=r.extend({render:function(){this.init(),2!=this.model.get("data").filter_type&&this.hide()},init:function(){var e=this.get("data").filter_group;this.widget=new o({$wrapper:this.$el,title:$t("且(AND)"),width:850,addBtnName:$t("新增或关系"),apiname:"PersonnelObj",selectone_multiple:!0,defaultValue:e,filterApiname:["is_active","is_pause_login","out_tenant_id"].concat(CRM.get("isOpenPRM")?[]:["partner_id","out_owner"]),helper:l,parseCompare:function(e,t){return e=CRM.get("isOpenPRM")&&"out_owner"===t.api_name?e.filter(function(e){return-1!=["IS","ISN"].indexOf(e.value1)}):e}})},getValue:function(){var e=this.widget.getValue(),t=e=JSON.parse(e),t=(e.type,t.map(function(e){return{filters:e.filters.map(function(e){return{operator:e.operator,operator_name:e.operator_name,field_name:e.field_name,field_values:e.field_values,isIndex:!1,fieldNum:0,connector:"AND",isObjectReferencea:!1}}),connector:"OR"}}));return this.hideError(),this.widget.valid()?t:(this.showError(),null)},show:function(){this.$el.parent().show()},hide:function(){this.$el.parent().hide()},showError:function(){n.showErrmsg(this.$el,$t("请填写筛选值!"))},hideError:function(){n.hideErrmsg(this.$el)},destroy:function(){this.widget&&this.widget.destroy()}});i.exports=e});
define("crm-setting/ownership/ownership_rule/add/form/partners_scope/partners_scope",["crm-modules/action/field/field","crm-modules/common/util","crm-modules/common/filtergroup/filtergroup","crm-setting/ownership/helper"],function(e,t,r){var i=e("crm-modules/action/field/field").C.Base,n=e("crm-modules/common/util"),o=e("crm-modules/common/filtergroup/filtergroup"),s=e("crm-setting/ownership/helper"),e=i.extend({render:function(){this.init(),4!=this.model.get("data").filter_type&&this.hide()},init:function(){var e=this.get("data").partners_scope;this.widget&&this.widget.destroy(),this.widget=new o({$wrapper:this.$el,title:$t("且(AND)"),width:850,addBtnName:$t("新增或关系"),apiname:"PartnerObj",selectone_multiple:!0,defaultValue:e,filterApiname:["out_tenant_id"].concat(CRM.get("isOpenPRM")?[]:["partner_id","out_owner"]),helper:s,parseCompare:function(e,t){return e=CRM.get("isOpenPRM")&&"out_owner"===t.api_name?e.filter(function(e){return-1!=["IS","ISN"].indexOf(e.value1)}):e}})},getValue:function(){var e=this.widget.getValue(),t=e=JSON.parse(e),t=(e.type,t.map(function(e){return{filters:e.filters.map(function(e){return{operator:e.operator,operator_name:e.operator_name,field_name:e.field_name,field_values:e.field_values,isIndex:!1,fieldNum:0,connector:"AND",isObjectReferencea:!1}}),connector:"OR"}}));return this.hideError(),this.widget.valid()?t:(this.showError(),null)},show:function(){this.$el.parent().show()},hide:function(){this.$el.parent().hide()},showError:function(){n.showErrmsg(this.$el,$t("请填写筛选值!"))},hideError:function(){n.hideErrmsg(this.$el)},destroy:function(){this.widget&&this.widget.destroy()&&(this.widget=null)}});r.exports=e});
define("crm-setting/ownership/ownership_rule/add/form/partners/partners",["crm-modules/action/field/field","crm-modules/common/util","crm-widget/selector/selector"],function(e,t,i){var r=e("crm-modules/action/field/field").C.Base,s=e("crm-modules/common/util"),e=(e("crm-widget/selector/selector"),r.extend({render:function(){this.$el.html('<div class="index-partners-scope" style="width:400px"></div>'),this.data=this.model.toJSON(),this.initSelector(),3!=this.model.get("data").filter_type&&this.hide()},initSelector:function(){var t=this,e=this.model.getData("partners"),i=$(".index-partners-scope",t.$el);this.widget=CRM.util.initICSelctor(i,{icselProps:{propsLine:{label:$t("选择伙伴对接企业")},propsInput:{tabs:[{id:"outerTenantIds",tabIcon:"partner",title:$t("sfa.ownership.friend_Enterprise"),selectAll:!0},{id:"outerUids",title:$t("sfa.ownership.friend_People"),selectAll:!0}]},icsOptions:{sceneId:"leadsPoolOrHighSeasCheckAuthority"},selected:e,width:400},onChange:function(e){e.outerTenantIds.length+e.outerUids.length?t.hideError():t.showError()}}),this.model.get("isSystem")&&this.widget.lock()},getValue:function(){this.hideError();var e,t=this.widget.getValue(),s=this.widget.getSelectedItems(),o=[],n=[];for(e in t)(i=>{var r;"exEnterprise"==i?r=1:"exEmployee"==i&&(r=2),_.map(t[i],function(e){var t=(_.findWhere(s[i],{id:+e})||{}).name;t&&n.push(t),o.push({DataID:e,DataType:r})})})(e);if(o.length)return{scope:o,scopeName:n.join($t("、"))};this.showError()},show:function(){this.$el.parent().show()},hide:function(){this.$el.parent().hide()},showError:function(){s.showErrmsg(this.$el,$t("请选择适用范围"))},hideError:function(){s.hideErrmsg(this.$el)},destroy:function(){this.widget&&this.widget.destroy()}}));i.exports=e});
define("crm-setting/ownership/ownership_rule/add/form/selector/selector",["crm-modules/action/field/field","crm-modules/common/util","crm-widget/selectorV2/selectorV2"],function(e,t,i){var o=e("crm-modules/action/field/field").C.Base,r=e("crm-modules/common/util"),s=e("crm-widget/selectorV2/selectorV2"),e=o.extend({render:function(){this.$el.html('<div class="index-manage-scope"></div>'),this.initSelector(),1!=this.model.get("data").filter_type&&this.hide()},initSelector:function(){var i=this,e=this.model.getData("selector"),t=this.model.getData("scope");this.widget=new s({$wrap:$(".index-manage-scope",this.$el),zIndex:this.options.zIndex,width:400,group:{company:!0},member:"organization"!==t,usergroup:!0,role:"organization"!==t,single:!1,label:"organization"===t?$t("选择部门、用户组"):$t("选择员工部门用户组或角色"),defaultSelectedItems:e,groupIncludeChildrenStatus:2,groupIncludeChildrenStatusDisabled:!0}),this.widget.on("change",function(e,t){t.len?i.hideError():i.showError()}),this.model.get("isSystem")&&this.widget.lock()},getValue:function(){this.hideError();var e=this.widget.getValue(),r=this.widget.getSelectedItems(),s=[],n=[];if(_.map(e,function(e,i){var o;"member"==i?o=1:"group"==i?o=2:"usergroup"===i?o=3:"role"===i&&(o=4),_.map(e,function(e){var t=(_.findWhere(r[i],{id:+e})||{}).name;t&&n.push(t),s.push({DataID:e,DataType:o})})}),s.length)return{scope:s,scopeName:n.join($t("、"))};this.showError()},show:function(){this.$el.parent().show()},hide:function(){this.$el.parent().hide()},showError:function(){r.showErrmsg(this.$el,$t("请选择适用范围"))},hideError:function(){r.hideErrmsg(this.$el)},destroy:function(){this.widget&&this.widget.destroy()}});i.exports=e});
define("crm-setting/ownership/ownership_rule/add/form/trueorfalse/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-comp-trueorfalse"> <div class="mn-radio-box"> ';
            _.each(enums, function(a) {
                __p += " ";
                if (a.value == "employee" || a.value == "organization") {
                    __p += ' <span data-value="' + ((__t = a.value) == null ? "" : __t) + '" class="mn-radio-item' + ((__t = a.selected ? " mn-selected" : "") == null ? "" : __t) + '"></span><span class="f-radio-lb">' + __e(a.label) + '<em class="crm-ui-help crm-ui-title" data-title="' + __e(a.tip) + '" data-pos="top">?</em></span> ';
                } else {
                    __p += ' <span data-value="' + ((__t = a.value) == null ? "" : __t) + '" class="mn-radio-item' + ((__t = a.selected ? " mn-selected" : "") == null ? "" : __t) + '"></span><span class="f-radio-lb">' + __e(a.label) + "</span> ";
                }
                __p += " ";
            });
            __p += " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/ownership/ownership_rule/add/form/trueorfalse/trueorfalse",["crm-modules/action/field/field","crm-modules/action/field/field","./tpl-html"],function(e,l,t){var i=e("crm-modules/action/field/field").C.true_or_false;e("crm-modules/action/field/field");t.exports=i.extend({template:e("./tpl-html")})});
define("crm-setting/ownership/ownership_rule/add/model",["crm-modules/common/util"],function(e,t,a){e("crm-modules/common/util");a.exports={fetch:function(){this.parse()},parse:function(){var e=this.getFields(),t=this.get("data").is_prm,a=[e.scope,e.filter_type,e.selector,e.filter_group];t&&(a=[e.scope,e.filter_type,e.selector,e.filter_group,e.partners,e.partners_scope]),this.set({layout:[{api_name:"basic",label:$t("基本信息"),columns:2,components:[e.rule_name]},{api_name:"where",label:$t("适用范围"),columns:1,components:a},{api_name:"rules",label:$t("设置规则"),columns:1,components:[e.add_rule]}],fields:e})},getFields:function(){var e=this.get("data").is_prm,t=this.get("data").scope,a=[{label:"organization"===t?$t("选择部门、用户组"):$t("按同事、部门、用户组选择"),value:1},{label:$t("按指定条件选择"),value:2},{label:$t("按伙伴对接企业选择"),value:3},{label:$t("按合作伙伴对象条件选择"),value:4}];return e?(e={employee:a,organization:a.slice(0,1).concat(a.slice(2,a.length))})[t]&&(a=e[t]):(e={employee:a.slice(0,2),organization:a.slice(0,1)})[t]&&(a=e[t]),{rule_name:{api_name:"rule_name",type:"text",label:$t("规则名称"),is_required:!0,max_length:20},scope:{api_name:"scope",type:"true_or_false",label:$t("类型"),options:[{label:$t("为个人设置保有量"),value:"employee",tip:$t("保有量上限将会适用到符合条件的人员上")},{label:$t("为部门、用户组、对接企业设置整体保有量"),value:"organization",tip:$t("保有量上限将会适用到符合条件的部门、用户组、对接企业上")}]},filter_type:{api_name:"filter_type",type:"true_or_false",label:$t("选择范围"),options:a},selector:{api_name:"selector"},filter_group:{api_name:"filter_group"},partners:{api_name:"partners"},partners_scope:{api_name:"partners_scope"},add_rule:{api_name:"add_rule",label:""}}}}});
define("crm-setting/ownership/ownership_rule/add/view",["crm-modules/action/field/field","./form/selector/selector","./form/add_rule/add_rule","./form/filter_group/filter_group","./form/partners/partners","./form/partners_scope/partners_scope","./form/trueorfalse/trueorfalse","crm-widget/selectorV2/selectorV2"],function(e,t,r){var o=e("crm-modules/action/field/field"),s=e("./form/selector/selector"),i=e("./form/add_rule/add_rule"),l=e("./form/filter_group/filter_group"),a=e("./form/partners/partners"),n=e("./form/partners_scope/partners_scope"),p=e("./form/trueorfalse/trueorfalse"),m=e("crm-widget/selectorV2/selectorV2");r.exports={mycomponents:{selector:s,add_rule:i,filter_group:l,partners:a,partners_scope:n,true_or_false:p},initView:function(){var r,s=this;o.View.prototype.initView.apply(this,arguments),this.forms.scope&&(r=this.model.getData("selector"),this.forms.scope.on("change",function(e){var t=s.forms.filter_type;s.showFilterType(e),s.forms.selector.widget&&s.forms.selector.widget.destroy&&s.forms.selector.widget.destroy(),"employee"==e?(t.setValue(1),t.trigger("change",1),$(".crm-w-selector").html(""),s.forms.selector.widget=new m({$wrap:$(".index-manage-scope",s.$el),zIndex:s.options.zIndex,width:400,group:{company:!0},member:!0,usergroup:!0,role:!0,single:!1,label:$t("选择员工部门用户组或角色"),defaultSelectedItems:r,groupIncludeChildrenStatus:2,groupIncludeChildrenStatusDisabled:!0})):"organization"==e&&($(".crm-w-selector").html(""),s.forms.selector.widget=new m({$wrap:$(".index-manage-scope",s.$el),zIndex:s.options.zIndex,width:400,group:{company:!0},member:!1,usergroup:!0,role:!1,single:!1,label:$t("选择部门、用户组"),defaultSelectedItems:r,groupIncludeChildrenStatus:2,groupIncludeChildrenStatusDisabled:!0}),t.setValue(1),t.trigger("change",1))})),this.forms.filter_type.on("change",function(e){s.hideForms(["selector","filter_group","partners","partners_scope"]),1==e?s.showForms(["selector"]):2==e?s.showForms(["filter_group"]):3==e?(s.showForms(["partners"]),s.forms.partners.render()):4==e&&(s.showForms(["partners_scope"]),s.forms.partners_scope.render())})},collect:function(){var e=this.forms.filter_type.getValue(),t={rule_name:this.forms.rule_name.getValue(),filter_type:e,add_rule:this.forms.add_rule.getValue()};return 1==e?t.selector=this.forms.selector.getValue():2==e?t.filter_group=this.forms.filter_group.getValue():3==e?t.partners=this.forms.partners.getValue():4==e&&(t.partners_scope=this.forms.partners_scope.getValue()),t},submit:function(){var t=this,e=this.collect();this.validate()&&(e={objectApiName:this.get("apiname"),objectLimitRule:{name:e.rule_name,groupId:this.model.get("data").groupId,dataRule:this.getDataRule(e),filters:this.getFilters(e),ruleType:this.forms.scope.getValue()}},CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/save_object_limit_rule",data:e,success:function(e){0==e.Result.StatusCode&&t.trigger("success")}},{submitSelector:$('span[data-action="submit"]',t.$el.closest(".crm-c-dialog"))}))},getDataRule:function(e){var t={employeeList:[],departmentList:[],userGroupList:[],userRoleList:[],customDataList:[],partnerList:[],partnerEmployeeList:[],partnerCustomDataList:[]};if(1==e.filter_type)for(var r=["","employeeList","departmentList","userGroupList","userRoleList"],s=e.selector.scope,o=0;o<s.length;o++){var i=s[o];t[r[i.DataType]].push(i.DataID)}else if(2==e.filter_type)t.customDataList=[JSON.stringify(e.filter_group)];else if(3==e.filter_type)for(var l=["","partnerList","partnerEmployeeList"],a=e.partners.scope,n=0;n<a.length;n++){var p=a[n];t[l[p.DataType]].push(p.DataID)}else 4==e.filter_type&&(t.partnerCustomDataList=[JSON.stringify(e.partners_scope)]);return t},getFilters:function(e){for(var t=e.add_rule,r=[],s=0;s<t.length;s++){var o=t[s];o.limitObjectApiName?r.push({wheres:JSON.stringify(o.filter_group),limitObjectApiName:o.limitObjectApiName,limitFieldName:o.limitFieldName}):r.push({wheres:JSON.stringify(o.filter_group),limitNumber:o.limitNumber})}return r},showFilterType:function(e){var t,r=this.forms.filter_type.fieldAttr,s=this.get("data").scope,o=[{label:"organization"===s?$t("选择部门、用户组"):$t("按同事、部门、用户组选择"),value:1},{label:$t("按指定条件选择"),value:2},{label:$t("按伙伴对接企业选择"),value:3},{label:$t("按合作伙伴对象条件选择"),value:4}];this.model.get("data").is_prm?(t={employee:o,organization:o.slice(0,1).concat(o.slice(2,o.length))})[s]&&(r.options=t[s]):(t={employee:o.slice(0,2),organization:o.slice(0,1)})[s]&&(r.options=t[s]),this.forms.filter_type.render()},showForms:function(e){for(var t=0;t<e.length;t++){var r=e[t];this.forms[r]&&this.forms[r].show()}},hideForms:function(e){for(var t=0;t<e.length;t++){var r=e[t];this.forms[r]&&this.forms[r].hide(),this.forms[r]&&this.forms[r].hideError&&this.forms[r].hideError()}}}});
define("crm-setting/ownership/ownership_rule/ownership_rule",["crm-modules/common/util","./template/ownership_rule-html","crm-widget/table/table","crm-modules/action/field/field","./add/view","./add/model","./view_rule/view_rule","crm-modules/common/filtergroup/util","crm-setting/ownership/helper"],function(e,t,i){var r=e("crm-modules/common/util"),n=e("./template/ownership_rule-html"),s=e("crm-widget/table/table"),a=e("crm-modules/action/field/field"),o=e("./add/view"),l=e("./add/model"),c=e("./view_rule/view_rule"),e=(e("crm-modules/common/filtergroup/util"),e("crm-setting/ownership/helper"),Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.apiname=e.apiname,this.$el.html(n),this.comps={},this.user={user_groups:[],user_roles:[]}},events:{"click .j-create-rule":"addHandle","click .j-view-rule":"viewHandle"},initTable:function(){var a=this,n=this;if(n.table)return this;n.table=new s({$el:$(".ownership-rule-table",n.$el),className:"crm-table ownership-rule-list",tableName:"tableName",tableWidth:"100%",maxHeight:"500",requestType:"FHHApi",url:"/EM1HNCRM/API/v1/object/object_limit/service/get_object_limit_rule_list",showFilerBtn:!1,showMultiple:!1,showMoreBtn:!1,showTermBatch:!0,noAllowedWrap:!1,autoHeight:!0,showPage:!1,postData:{objectApiName:n.apiname,pageNumber:50,pageIndex:1},columns:[{data:"name",title:$t("规则名称")},{data:"scope",title:$t("人员范围"),render:function(e,t,i){return n.getScopeHtml(i)}},{data:"content",title:$t("规则内容"),render:function(e,t,i){return n.getRuleContent(i)}},{data:"",title:$t("操作"),render:function(e,t,i){return'<div class="ops-btns">\n\t\t\t\t\t\t\t\t\t<a data-operate="edit">'.concat($t("编辑"),'</a>\n\t\t\t\t\t\t\t\t\t<a data-operate="clone">').concat($t("复制"),'</a>\n\t\t\t\t\t\t\t\t\t<a data-operate="delete">').concat($t("删除"),"</a>\n\t\t\t\t\t\t\t\t</div>")}}],formatData:function(e){return e.outer_employee&&e.outer_employee.length&&CRM.util.setExContactsCache(e.outer_employee,"em"),e.outer_enterprise&&e.outer_enterprise.length&&CRM.util.setExContactsCache(e.outer_enterprise,"en"),e.areaList&&e.areaList.length&&CRM.util.setAreaCache(e.areaList),{data:e.limitRuleList}}}),n.table.on("trclick",function(e,t,i){var n=i.data("operate");"edit"==n?a.editHandle(e):"clone"==n?a.cloneHandle(e):"delete"==n?a.deleteHandle(e):"show"==n&&a.showHandle(i)}),n.table.on("renderListComplete",function(){var e=n.table.getRowData().length;n.createBtn(),n.viewRuleBtn(),n.setTitleCount(50,e),n.hideRow(),n.comps.view.show=0!==e,n.comps.create.disabled=50<=e,n.comps.create.close=!(50<=e),window.lxdtable=n.table})},refreshTable:function(){this.table.setParam({pageNumber:50},!0)},addHandle:function(){var e=this;this.comps.create.disabled||a.add({url:"/EM1HNCRM/API/v1/object/object_limit/service/save_object_limit_rule",Model:a.Model.extend(l),View:a.View.extend(o),record_type:"default__c",show_type:"full",ownerType:1e3,apiname:this.apiname,className:"ownership_rule_dialog",data:{scope:"employee",filter_type:1,action:"add",is_prm:CRM.get("isOpenPRM")},success:function(){e.refreshTable()}})},editHandle:function(e){var t=this;t.checkOperate(e)||a.edit({url:"/EM1HNCRM/API/v1/object/object_limit/service/save_object_limit_rule",Model:a.Model.extend(l),View:a.View.extend(o),record_type:"default__c",show_type:"full",ownerType:1e3,apiname:this.apiname,className:"ownership_rule_dialog",data:t.getData(e,"edit"),success:function(){t.refreshTable()}})},cloneHandle:function(e){var t=this;t.checkOperate(e)||a.add({url:"/EM1HNCRM/API/v1/object/object_limit/service/save_object_limit_rule",Model:a.Model.extend(l),View:a.View.extend(o),record_type:"default__c",show_type:"full",ownerType:1e3,apiname:this.apiname,data:t.getData(e,"clone"),success:function(){t.refreshTable()}})},deleteHandle:function(e){var t=this,i=r.confirm($t("确定删除本条规则吗"),$t("提示"),function(){r.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/delete_object_limit_rule",data:{objectApiName:t.apiname,groupId:e.groupId},success:function(e){0==e.Result.StatusCode&&(i.hide(),t.refreshTable())}})})},showHandle:function(e){e.prev().removeClass("more"),e.remove()},checkOperate:function(e){if(!CRM.get("isOpenPRM")&&(e.dataRule.partnerList.length||e.dataRule.partnerEmployeeList.length||e.dataRule.partnerCustomDataList.length))return r.alert($t("代理通被关闭, 不可以编辑.")),!0},viewHandle:function(){this.comps.view_rule=new c({apiname:this.apiname,rules:this.table.getCurData()}),this.comps.view_rule.show()},getData:function(e,t){var i={id:e.id,groupId:e.groupId,rule_name:e.name,add_rule:e.filters,scope:e.ruleType,is_prm:CRM.get("isOpenPRM")},e=e.dataRule;return e.employeeList.length||e.departmentList.length||e.userGroupList.length||e.userRoleList.length?(i.filter_type=1,i.selector={member:e.employeeList,group:e.departmentList,usergroup:e.userGroupList,role:e.userRoleList}):e.customDataList.length?(i.filter_type=2,i.filter_group=JSON.parse(e.customDataList[0])):e.partnerList.length||e.partnerEmployeeList.length?(i.filter_type=3,i.partners={outerTenantIds:e.partnerList,outerUids:e.partnerEmployeeList}):e.partnerCustomDataList.length&&(i.filter_type=4,i.partners_scope=JSON.parse(e.partnerCustomDataList[0])),"clone"==t&&(delete i.groupId,i.rule_name=(i.rule_name+$t("-副本")).slice(0,20),i=JSON.parse(JSON.stringify(i))),i.action=t,i},show:function(){var e=this;this.$el.show(),("NewOpportunityObj"!==this.apiname?$.when(this.fetchOverRule(),CRM.util.getCountryAreaOptions(!0)):$.when(CRM.util.getCountryAreaOptions(!0))).always(function(){$.when(e.fetchUser(),r.getFieldsByApiName(e.apiname),r.getFieldsByApiName("PersonnelObj"),CRM.get("isOpenPRM")&&r.getFieldsByApiName("PartnerObj")).done(function(){e.initTable()})})},hide:function(){this.$el.hide()},createBtn:function(){this.comps.create||(this.comps.create=FxUI.create({wrapper:".ownership-rule-container .j-create-rule",template:'<fx-tooltip effect="dark" :content="content" :disabled="close" placement="top">\n\t\t\t\t\t\t\t<span><fx-button plain size="mini" :disabled="disabled">'.concat($t("新建规则"),"</fx-button></span>\n\t\t\t\t\t\t</fx-tooltip>"),data:function(){return{disabled:!1,close:!0,content:$t("最多创建{{count}}条规则",{count:50})}}}))},viewRuleBtn:function(){this.comps.view||(this.comps.view=FxUI.create({wrapper:".ownership-rule-container .j-view-rule",template:'<fx-button plain size="mini" v-if="show">'.concat($t("查看员工规则"),"</fx-button>"),data:function(){return{show:!0}}}))},setTitleCount:function(e,t){$(".ownership-rule-count",this.$el).html("(".concat(t,"/").concat(e,")"))},hideRow:function(){for(var e=this.table.$el.find(".ruel-container"),t=0;t<e.length;t++){var i=e.eq(t);190<i.height()&&(i.addClass("more"),i.parent().append('<a data-operate="show" href="javascript:;">'.concat($t("显示全部"),"</a>")))}},getScopeHtml:function(e){var t,i,n=this,a=[],e=e.dataRule,e=(e.employeeList.length||e.departmentList.length||e.userGroupList.length||e.userRoleList.length?(_.each(e.employeeList,function(e){e=FS.contacts.getEmployeeById(e);e&&a.push(e.name)}),_.each(e.departmentList,function(e){e=FS.contacts.getCircleById(e);e&&a.push(e.name)}),_.each(e.userGroupList,function(e){e=_.findWhere(n.user.user_groups,{id:e});e&&a.push(e.name)}),_.each(e.userRoleList,function(e,t){e=_.findWhere(n.user.user_roles,{id:e});e&&a.push(e.name)})):e.customDataList.length?(t=JSON.parse(e.customDataList[0]),i=CRM.get("fields.PersonnelObj"),a.push(CRM.util.parseFiltersRule(t,i))):e.partnerList.length||e.partnerEmployeeList.length?(_.each(e.partnerList,function(e,t){e=r.getExContactByIds(e,"en");e&&a.push(e.name)}),_.each(e.partnerEmployeeList,function(e,t){e=r.getExContactByIds(e,"em");e&&a.push(e.name)})):e.partnerCustomDataList.length&&(t=JSON.parse(e.partnerCustomDataList[0]),i=CRM.get("fields.PartnerObj"),a.push(CRM.util.parseFiltersRule(t,i))),a.join(",")||$t("成员已被全部禁用"));return'<div class="ruel-container">'.concat(e,"</div>")},getRuleContent:function(e){var s=this,e=e.filters.map(function(e,t){var i,n=JSON.parse(e.wheres),a=[],r={LeadsObj:$t("全部线索"),AccountObj:$t("全部客户"),NewOpportunityObj:$t("全部")+$t("crm.商机2")},n=(n.length?(i=CRM.get("fields.".concat(s.apiname)),a.push(CRM.util.parseFiltersRule(n,i))):a.push("<p>".concat(r[s.apiname],"</p>")),e.limitObjectApiName?"${".concat("PersonnelObj"==e.limitObjectApiName?$t("人员"):$t("crm.合作伙伴"),".").concat((_(CRM.get("fields."+e.limitObjectApiName)).findWhere({api_name:e.limitFieldName})||{}).label,"}"):e.limitNumber);return a.push('<p class="limit-number">【'.concat($t("保有量"),"：").concat($t("length个",{length:n}),"】</p>")),a.join("")?'<div class="ruel-content-item">\n\t\t\t\t\t\t\t<div class="left-icon">◎</div>\n\t\t\t\t\t\t\t<div class="right-content">'.concat(a.join(""),"</div>\n\t\t\t\t\t   </div>"):a.join("")}).join("");return'<div class="ruel-container">'.concat(e,"</div>")},fetchUser:function(){var e=$.Deferred(),t=this;return $.when(r.getUserGroups(function(e){t.user.user_groups=e}),r.getUserRoles(function(e){t.user.user_roles=e})).always(function(){e.resolve(t.user)}),e.promise()},fetchOverRule:function(t){var i=this,n={LeadsObj:$t("请配置【超线索的处理规则】，否则会导致部分线索丢失。"),AccountObj:$t("请配置【超客户的处理规则】，否则会导致部分客户丢失。"),NewOpportunityObj:$t("请配置【超商机的处理规则】，否则会导致部分商机丢失。")};r.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/get_object_limit_over_rule",data:{objectApiName:this.apiname},success:function(e){0==e.Result.StatusCode&&(e.Value.objectLimitOverRule?t&&t():(e=n[i.apiname],FxUI.MessageBox.confirm(e,$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){$(".crm-ownership-tab .page3").click()}).catch(function(){t&&t()})))}})},extendFieldDesp:function(){},destroy:function(){for(var e in this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null,this.user=null}}));i.exports=e});
define("crm-setting/ownership/ownership_rule/template/ownership_rule-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="ownership-rule-container"> <div class="ownership-rule-title"> <div class="ownership-rule-layout-left"> <span class="ownership-rule-text">' + ((__t = $t("规则列表")) == null ? "" : __t) + '</span> <span class="ownership-rule-count">(--/--)</span> </div> <div class="ownership-rule-layout-right"> <span class="ownership-rule-btn j-create-rule"></span> <span class="ownership-rule-btn j-view-rule"></span> </div> </div> <div class="ownership-rule-table"> <!-- <a href="javascript:;">加载失败,请重新加载</a> --> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/ownership/ownership_rule/view_rule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="view_rule-container"> <div class="tabs"> </div> <div class="content"> <div class="info"> <span class="label">' + ((__t = $t("说明")) == null ? "" : __t) + '</span> <p class="msg">1. ' + ((__t = $t("同一员工如适用多规则，则以保有量最小的为准。如手动设置默认规则，则预先按设置的为准")) == null ? "" : __t) + '。</p> </div> <div class="view_rule-table"> <div class="crm-loading"></div> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/ownership/ownership_rule/view_rule/view_rule",["crm-modules/common/util","crm-widget/table/table","./template/tpl-html","crm-modules/common/filtergroup/util","crm-setting/ownership/helper"],function(t,e,n){var a=t("crm-modules/common/util"),s=t("crm-widget/table/table"),i=t("./template/tpl-html"),f=t("crm-modules/common/filtergroup/util"),b=t("crm-setting/ownership/helper");n.exports=Backbone.View.extend({initialize:function(t){},render:function(){var t=this;this.comps={},this.apiname=t.options.apiname,this.is_show=t.isShowPartners(),this.comps.dialog=FxUI.create({template:'<fx-dialog\n\t\t\t\t\t\t\t:visible.sync="dialogVisible"\n\t\t\t\t\t\t\t:append-to-body="true"\n\t\t\t\t\t\t\tfullscreen\n\t\t\t\t\t\t\tsize="medium"\n\t\t\t\t\t\t\tcustom-class="view_rule-dialog"\n\t\t\t\t\t\t\t:title="getTitle"\n\t\t\t\t\t\t\t@closed="handleClose">\n\t\t\t\t\t\t\t'.concat(i(),'\n\t\t\t\t\t\t\t<span slot="footer" class="dialog-footer">\n\t\t\t\t\t\t\t\t<fx-button type="primary" @click="dialogVisible = false">').concat($t("关闭"),"</fx-button>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t</fx-dialog>"),data:function(){return{dialogVisible:!0}},computed:{getTitle:function(){return{LeadsObj:$t("查看员工线索保有量规则"),AccountObj:$t("查看员工客户保有量规则"),NewOpportunityObj:$t("查看员工{{objectName}}保有量规则",{objectName:$t("crm.商机2")})}[t.apiname]}},methods:{handleClose:function(){t.hide()}}}),this.$el=$(this.comps.dialog.$el),setTimeout(function(){t.initTab()})},initTab:function(){var n=this;this.comps.tabs&&this.comps.tabs.destroy(),this.comps.tabs=FxUI.create({wrapper:".view_rule-container .tabs",template:'<fx-tabs v-model="active_name" :stretch="true" @tab-click="handleClick">\n\t\t\t\t\t\t\t\t<fx-tab-pane label="'.concat($t("员工"),'" name="employees">\n\t\t\t\t\t\t\t\t\t<fx-input placeholder="').concat($t("搜索员工"),'" v-model="employees_value" @change="clickSearch(\'employees\')">\n\t\t\t\t\t\t\t\t\t\t<i slot="suffix" class="el-input__icon el-icon-search" @click="clickSearch(\'employees\')"></i>\n\t\t\t\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t\t\t\t\t<ul class="employees-list">\n\t\t\t\t\t\t\t\t\t\t<li :class="[\'item\', {active: active_employees_index==index}]" v-for="(employee, index) in employees_list" @click="clickEmployee(employee, index)">{{employee.name}}</li>\n\t\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t</fx-tab-pane>\n\t\t\t\t\t\t\t\t<fx-tab-pane label="').concat($t("伙伴"),'" name="partners" lazy v-if="is_show">\n\t\t\t\t\t\t\t\t\t<fx-input placeholder="').concat($t("sfa.ownership.searchPartner"),'" v-model="partners_value" @change="clickSearch(\'partners\')">\n\t\t\t\t\t\t\t\t\t\t<i slot="suffix" class="el-input__icon el-icon-search" @click="clickSearch(\'partners\')"></i>\n\t\t\t\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t\t\t\t\t<fx-menu\n\t\t\t\t\t\t\t\t\t\tdefault-active="2"\n\t\t\t\t\t\t\t\t\t\tclass="partners-list">\n\t\t\t\t\t\t\t\t\t\t<fx-submenu :index="i+\'\'" v-for="(enterprise, i) in partners_list" v-if="!enterprise.hidden">\n\t\t\t\t\t\t\t\t\t\t\t<template slot="title">\n\t\t\t\t\t\t\t\t\t\t\t<span>{{enterprise.name}}</span>\n\t\t\t\t\t\t\t\t\t\t\t</template>\n\t\t\t\t\t\t\t\t\t\t\t<fx-menu-item-group>\n\t\t\t\t\t\t\t\t\t\t\t\t<fx-menu-item :index="i+\'-\'+j" v-for="(employee, j) in enterprise.children" @click="fetchRuleListByEmployee(employee)">{{employee.name}}</fx-menu-item>\n\t\t\t\t\t\t\t\t\t\t\t</fx-menu-item-group>\n\t\t\t\t\t\t\t\t\t\t</fx-submenu>\n\t\t\t\t\t\t\t\t\t</fx-menu>\n\t\t\t\t\t\t\t\t</fx-tab-pane>\n\t\t\t\t\t\t\t</fx-tabs>'),data:function(){return{active_name:"employees",employees_value:"",partners_value:"",employees_list:[],partners_list:[],active_employees_index:0,active_partners_index:0,is_show:n.is_show}},methods:{handleClick:function(t,e){"employees"===t.name?n.fetchEmployees(this):"partners"===t.name&&n.fetchPartners(this)},clickSearch:function(t){"employees"===t?n.fetchEmployees(this,{keyWord:this.employees_value}):"partners"===t&&n.filterPartners(this,{keyWord:this.partners_value})},clickEmployee:function(t,e){this.active_employees_index=e,n.initTable(t.id)},fetchRuleListByEmployee:function(t,e){n.initTable(t.id)}},created:function(){n.fetchEmployees(this)}})},fetchEmployees:function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=this;t.keyWord=t.keyWord||"",a.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/get_object_limit_rule_employee_list",data:{objectApiName:n.apiname,keyWord:t.keyWord,pageNumber:100,pageIndex:1,viewType:"employee"},success:function(t){0==t.Result.StatusCode&&(t=t.Value.dataIds,t=(t=FS.contacts.getEmployeesByIds(t)).filter(function(t){return t&&_.pick(t,"name","id")}),e.employees_list=t,n.initTable(0<e.employees_list.length&&e.employees_list[0].id))}})},filterPartners:function(t){for(var e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{},n=0;n<t.partners_list.length;n++){var a=t.partners_list[n];t.$set(t.partners_list[n],"hidden",!1),-1==a.name.indexOf(e.keyWord)&&t.$set(t.partners_list[n],"hidden",!0)}},fetchPartners:function(n){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:{};t.keyWord=t.keyWord||"",a.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/get_object_limit_rule_employee_list",data:{objectApiName:this.apiname,keyWord:t.keyWord,pageNumber:100,pageIndex:1,viewType:"partner"},success:function(t){var e;0==t.Result.StatusCode&&(e=t.Value.dataIds,t.Value.outer_enterprise&&t.Value.outer_enterprise.length&&CRM.util.setExContactsCache(t.Value.outer_enterprise,"en"),n.partners_list=a.getExContactByIds(e,"en"))}})},initTable:function(i){var o=this,l=this;l.table&&l.table.destroy(),l.table=new s({$el:$(".view_rule-table",l.$el),className:"crm-table",tableName:"tableName",requestType:"FHHApi",url:"/EM1HNCRM/API/v1/object/object_limit/service/get_employee_object_limit_rule",doStatic:!i,showFilerBtn:!1,showMultiple:!1,showMoreBtn:!1,showTermBatch:!0,noAllowedWrap:!1,autoHeight:!0,showPage:!1,postData:{objectApiName:l.apiname,employeeId:i},columns:[{data:"name",title:$t("规则名称"),width:80,render:function(t,e,n){return n.defaultRule?'<span class="status-icon">【'.concat($t("执行中"),"】</span>").concat(n.name):n.name}},{data:"content",dataType:2,title:$t("规则内容"),render:function(t,e,n){return l.getRuleContent(n)}},{data:"action",title:$t("设置执行规则"),width:80,render:function(t,e,n){return'<div class="ops-btns">\n\t\t\t\t\t\t\t\t\t'.concat(n.defaultRule?'<a data-operate="cancel">'.concat($t("取消选择"),"</a>"):'<a data-operate="exec">'.concat($t("仅执行本条规则"),"</a>"),"\n\t\t\t\t\t\t\t\t</div>")}}],formatData:function(t){return{data:t.limitRuleList.map(function(t){return{id:t.id,groupId:t.groupId,defaultRule:t.defaultRule,name:t.name,content:l.getRuleContent(t),filters:t.filters}})}}}),l.table.on("trclick",function(t,e,n){var a=n.data("operate"),t={objectApiName:l.apiname,groupId:t.groupId,employeeId:i,defaultRule:!t.defaultRule};"cancel"==a||"exec"==a?o.operateHandle(t):"show"==a&&o.showHandle(n)}),l.table.on("renderListComplete",function(){l.hideRow()}),i||l.table.doStaticData([])},hideRow:function(){for(var t=this.table.$el.find(".ruel-container"),e=0;e<t.length;e++){var n=t.eq(e);190<n.height()&&(n.addClass("more"),n.parent().append('<a data-operate="show" href="javascript:;">'.concat($t("显示全部"),"</a>")))}},showHandle:function(t){t.prev().removeClass("more"),t.remove()},actionIcon:function(){return FxUI.create({template:'<fx-tooltip effect="dark" content="'.concat($t("同一员工如适用多规则，则以保有量最小的为准。如手动设置默认规则，则预先按设置的为准"),'。" placement="bottom-start">\n\t\t\t\t\t\t\t\t<span class="th-action-icon"></span>\n\t\t\t\t\t\t\t</fx-tooltip>')}).$el},operateHandle:function(t){var e=this;a.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/set_employee_default_object_limit_rule",data:t,success:function(t){0==t.Result.StatusCode&&e.table.setParam({pageNumber:50},!0)}})},getRuleContent:function(t){var d=this,h=this,t=t.filters.map(function(t,e){var n=JSON.parse(t.wheres),a=[],i={LeadsObj:$t("全部线索"),AccountObj:$t("全部客户"),NewOpportunityObj:$t("全部")+$t("crm.商机2")};n.length||a.push("<p>".concat(i[d.apiname],"</p>"));for(var o=0;o<n.length;o++){for(var l=n[o],s=[],r=0;r<l.filters.length;r++){var c,p,u=l.filters[r],m=CRM.get("fields.".concat(h.apiname))[u.field_name];m&&(c=f.formatFieldValue(m,u.field_values,u.operator),p=_.findWhere(b.compare,{value1:u.operator}),s.push('"'.concat(m.label,'" ').concat(p&&p.name||u.operator_name," ").concat(c?'"'+c+'"':"")))}a.push((0==o?"<p>":"<p>".concat($t("或")," ")).concat(s.join(" ".concat($t("且")," ")),";</p>"))}return a.push('<p class="limit-number">【'.concat($t("保有量"),"：").concat(*********==t.limitNumber?$t("无限制"):t.limitNumber).concat(*********==t.limitNumber?"":$t("个"),"】</p>")),'<div class="ruel-content-item">\n\t\t\t\t\t\t\t<div class="left-icon">◎</div>\n\t\t\t\t\t\t\t<div class="right-content">'.concat(a.join(""),"</div>\n\t\t\t\t\t   </div>")}).join("");return'<div class="ruel-container">'.concat(t,"</div>")},isShowPartners:function(){for(var t=this.options.rules,e=0;e<t.length;e++){var n=t[e].dataRule;if(n.partnerList.length||n.partnerEmployeeList.length||n.partnerCustomDataList.length)return!0}return!1},show:function(){this.render()},hide:function(){this.destroy()},destroy:function(){for(var t in this.comps)this.comps[t].destroy&&this.comps[t].destroy();this.comps=null}})});
define("crm-setting/ownership/ownership_transfinite/ownership_transfinite",["crm-modules/common/util","./template/tpl-html"],function(t,e,i){var o=t("crm-modules/common/util"),s=t("./template/tpl-html"),t=Backbone.View.extend({initialize:function(t){this.apiname=t.apiname,this.comps={},this.setElement(t.wrapper),this.$el.html(s({apiname:this.apiname})),this.initSaveBtn(),this.fetchCluePoolList()},events:{"click .j-save":"saveHandle"},fetchCluePoolList:function(){var i=this,t="LeadsObj"===this.apiname?"LeadsPoolObj":"HighSeasObj",e="/EM1HNCRM/API/v1/object/".concat(t,"/controller/List");o.FHHApi({url:e,data:{object_describe_api_name:t,ignore_scene_record_type:!1,search_query_info:JSON.stringify({limit:2e3,offset:0,filters:[]})},success:function(t){var e;0==t.Result.StatusCode?(e=t.Value.dataList.map(function(t){return{label:t.name__r||t.name,value:t._id}}),i.initCluePool(e)):alert(t.messages)}})},formatList:function(t){var e=this;return t.map(function(t){return{label:t.Name,value:"LeadsObj"===e.apiname?t.SalesCluePoolID:t.HighSeasID}})},initCluePool:function(t){var e=this;this.comps.cluepool&&this.comps.cluepool.destroy(),this.comps.cluepool=FxUI.create({wrapper:".ownership-transfinite-select",template:'<fx-select\n\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t:placeholder="getPlaceholder"\n\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t:el-style="{width:\'200px\'}"\n\t\t\t\t\t\t></fx-select>',data:function(){return{value:"",options:t}},computed:{getPlaceholder:function(){return"LeadsObj"===e.apiname?$t("请选择线索池"):$t("请选择公海")}},created:function(){e.getRule()}})},getRule:function(){var e=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/get_object_limit_over_rule",data:{objectApiName:this.apiname},success:function(t){0==t.Result.StatusCode&&t.Value.objectLimitOverRule&&(e.comps.cluepool.value=t.Value.objectLimitOverRule.objectPoolId)}})},initSaveBtn:function(){this.comps.save=FxUI.create({wrapper:".ownership-transfinite-container .j-save",template:'<fx-button size="mini" type="primary">'.concat($t("保存"),"</fx-button>"),data:function(){return{}}})},saveHandle:function(){o.FHHApi({url:"/EM1HNCRM/API/v1/object/object_limit/service/save_object_limit_over_rule",data:{objectApiName:this.apiname,objectLimitOverRule:{objectPoolId:this.comps.cluepool.value}},success:function(t){0==t.Result.StatusCode&&o.remindSuccess(t.Value.message)}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){for(var t in this.comps)this.comps[t].destroy&&this.comps[t].destroy();this.comps=null}});i.exports=t});
define("crm-setting/ownership/ownership_transfinite/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="ownership-transfinite-container"> ';
            if (obj.apiname === "LeadsObj") {
                __p += ' <div class="ownership-transfinite-title">' + ((__t = $t("超限线索自动进入哪个线索池")) == null ? "" : __t) + '?</div> <div class="ownership-transfinite-info">1、' + ((__t = $t("通过智能表单收集的线索直接对接到员工，且员工的保有量已达上限时，您希望超限线索进入到哪个线索池？")) == null ? "" : __t) + "</div> ";
            } else {
                __p += ' <div class="ownership-transfinite-title">' + ((__t = $t("超限客户自动进入哪个公海")) == null ? "" : __t) + '？</div> <div class="ownership-transfinite-info">1、' + ((__t = $t("通过智能表单收集的客户直接对接到员工，且员工的保有量已达上限时，您希望超限客户进入到哪个公海？")) == null ? "" : __t) + "</div> ";
            }
            __p += ' <div class="ownership-transfinite-select"></div> <div class="footer"> <span class="save-btn j-save"></span> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/ownership/ownership",["crm-modules/common/util","./template/ownership-html","crm-setting/ownership/helper"],function(s,e,i){var t=s("crm-modules/common/util"),a=s("./template/ownership-html"),n=s("crm-setting/ownership/helper"),r=Backbone.View.extend({config:{page1:{id:"ownership_rule",wrapper:".ownership-rule-box"},page2:{id:"ownership_define",wrapper:".ownership-define-box"},page3:{id:"ownership_transfinite",wrapper:".ownership-transfinite-box"}},initialize:function(e){var i=this,n=this;this.pages={},this.apiname=e.apiname,this.firstLoaded=!1,this.setElement(e.wrapper),t.getPRMRight().done(function(){n.PRMRightLoaded=!0,n.$el.html(a({apiname:i.apiname,is_prm:CRM.get("isOpenPRM")}))})},events:{"click .crm-ownership-tab a":"onHandle"},onHandle:function(e){if(this.firstLoaded)return(e=$(e.target)).hasClass("page1")?this.switchPage("page1"):e.hasClass("page2")?this.switchPage("page2"):e.hasClass("page3")&&this.switchPage("page3"),!1},switchPage:function(e){this.renderPage(e)},render:function(e){this.pages={},this.$el.html(a()),this.renderPage(e)},renderPage:function(e,i){var n=this,t=n.$(".crm-ownership-tab .item"),a=(t.removeClass("cur"),t.filter("."+e).addClass("cur"),n.curId=e);_.map(n.pages,function(e){e.hide()}),n.pages[a]?n.pages[a].show():(t=[".",n.config[a].id,n.config[a].id].join("/"),s.async(t,function(e){n.firstLoaded=!0;e=new e(_.extend({wrapper:n.config[a].wrapper,apiname:n.apiname,firstLoad:i}));e.show(),n.pages[a]=e}))},show:function(){var e,i,n=this,t=this;this.$el.show(),t.PRMRightLoaded?this.renderPage("page1"):(e=0,i=setInterval(function(){e++,(t.PRMRightLoaded||6<e)&&(n.renderPage("page1"),clearInterval(i))},500))},hide:function(){this.$el.hide()},destroy:function(){}});r.helper=n,i.exports=r});
define("crm-setting/ownership/template/ownership-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="ownership-container"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ol> ";
            if (obj.apiname === "LeadsObj") {
                __p += " <li>1、" + ((__t = $t("线索保有量规则可以控制不同员工保有不同线索的数量上限;不在任何规则中的部门或员工无保有量限制。")) == null ? "" : __t) + "</li> <li>2、" + ((__t = $t("一个员工有多个规则时，默认使用最小保有量规则。")) == null ? "" : __t) + "</li> ";
            } else if (obj.apiname === "NewOpportunityObj") {
                __p += " <li>1、" + ((__t = $t("保有量可以控制不同员工保有不同商机的数量上限。")) == null ? "" : __t) + "</li> <li>2、" + ((__t = $t("不在任何规则中的部门或员工无保有量限制。")) == null ? "" : __t) + "</li> <li>3、" + ((__t = $t("一个员工有多个规则时，默认使用最小保有量规则。")) == null ? "" : __t) + "</li> <li>4、" + ((__t = $t("通过参数调取人员或合作伙伴的保有量时，如返回值是负数，则按0处理。")) == null ? "" : __t) + "</li> ";
            } else {
                __p += " <li>1、" + ((__t = $t("客户保有量规则可以控制不同员工保有不同客户的数量上限;不在任何规则中的部门或员工无保有量限制。")) == null ? "" : __t) + "</li> <li>2、" + ((__t = $t("一个员工有多个规则时，默认使用最小保有量规则。")) == null ? "" : __t) + "</li> <li>3、" + ((__t = $t("已成交客户不占保有量，可在保有量规则设置条件【成交状态】等于未成交。")) == null ? "" : __t) + "</li> <li>4、" + ((__t = $t("公海客户不占保有量，可在保有量规则设置条件【所属公海】为空。")) == null ? "" : __t) + "</li> ";
            }
            __p += " ";
            if (obj.is_prm) {
                __p += " ";
                if (obj.apiname === "LeadsObj") {
                    __p += " <li>3、" + ((__t = $t("当开启互联场景（代理通应用）后，需要下游伙伴不占有上游保有量，可在保有量规则设条件【合作伙伴】和【外部负责人】为空。")) == null ? "" : __t) + "</li> ";
                } else {
                    __p += " <li>5、" + ((__t = $t("当开启互联场景（代理通应用）后，需要下游伙伴不占有上游保有量，可在保有量规则设条件【合作伙伴】和【外部负责人】为空。")) == null ? "" : __t) + "</li> ";
                }
                __p += " ";
            }
            __p += ' </ol> </div> <div class="crm-module-con-ownership"> <div class="crm-ownership-tab"> <a class="item page1 cur" href="#crm/setting/clue/ownership/=/page1">' + ((__t = $t("保有量规则")) == null ? "" : __t) + '</a> <i></i> <a class="item page2" href="#crm/setting/clue/ownership/=/page2">' + ((__t = $t("保有量定义")) == null ? "" : __t) + "</a> ";
            if (obj.apiname !== "NewOpportunityObj") {
                __p += ' <i></i> <a class="item page3" href="#crm/setting/clue/ownership/=/page3">' + ((__t = obj.apiname === "LeadsObj" ? $t("超限线索的处理规则") : $t("超限客户的处理规则")) == null ? "" : __t) + "</a> ";
            }
            __p += ' </div> <div> <div class="item ownership-rule-box"> <div class="crm-loading"></div> </div> <div class="item ownership-define-box" style="display:none;"> <div class="crm-loading"></div> </div> ';
            if (obj.apiname !== "NewOpportunityObj") {
                __p += ' <div class="item ownership-transfinite-box" style="display:none;"> <div class="crm-loading"></div> </div> ';
            }
            __p += " </div> </div> </div>";
        }
        return __p;
    };
});