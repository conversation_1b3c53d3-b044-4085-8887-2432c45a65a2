define("crm-setting/cpq/cpq-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("该开关一旦开启，不可关闭。")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("开启该开关后，报价单和订单可以配置复杂产品和价格，其它业务模块暂时不支持。")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("cpq开关说明3")) == null ? "" : __t) + "</li> <li>&nbsp;&nbsp;3.1" + ((__t = $t("cpq开关说明3_1")) == null ? "" : __t) + "</li> <li>&nbsp;&nbsp;3.2" + ((__t = $t("cpq开关说明3_2")) == null ? "" : __t) + "：</li> <li>&nbsp;&nbsp;&nbsp;&nbsp;(1)" + ((__t = $t("cpq开关说明3_2_1")) == null ? "" : __t) + ";</li> <li>&nbsp;&nbsp;&nbsp;&nbsp;(2)" + ((__t = $t("cpq开关说明3_2_2")) == null ? "" : __t) + ";</li> <li>4." + ((__t = $t("开启该开关后，价目表暂时只能设置产品组合的折扣/价格，无法设置其子产品的折扣/价格。")) == null ? "" : __t) + "<br/> &nbsp;&nbsp;&nbsp;" + ((__t = $t("如果在报价单和订单中选择了产品组合并配置了其子产品，最终产品组合显示价格按照价目表中的折扣折算。")) == null ? "" : __t) + "<br/> &nbsp;&nbsp;&nbsp;" + ((__t = $t("比如，熊猫电脑 价格5,000元，价目表中折扣设置80%。")) == null ? "" : __t) + "" + ((__t = $t("cpq举例说明")) == null ? "" : __t) + ' </li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("CPQ开启开关")) == null ? "" : __t) + '</label> <div class="cpqClass switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <br/> <div class="on-off pp-switch bomPrintOnlyRootBtn "> <label class="title">1、' + ((__t = $t("报价单订单销售合同打印时只包含母件产品")) == null ? "" : __t) + '</label> <div class="bomPrintOnlyRootClass switch-print j-set-config ' + ((__t = bom_print_template_has_sub_node ? " on " : "") == null ? "" : __t) + '"> </div> </div> <div class="on-off pp-switch bomPrintIndentBtn "> <label class="title">2、' + ((__t = $t("打印订单、报价单、销售合同开启多层级区分展示")) == null ? "" : __t) + '</label> <div class="bomPrintIndentClass switch-print j-set-config ' + ((__t = bomPrintIndent ? " on " : "") == null ? "" : __t) + '"> </div> </div> <div class="crm-intro"> <ul> <li>' + ((__t = $t("开启后，产品名称字段，主产品和子产品的展示样式如下")) == null ? "" : __t) + "：</li> <li>" + ((__t = $t("cpq层级文案1")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("层级")) == null ? "" : __t) + "&nbsp;&nbsp;" + ((__t = $t("产品名称")) == null ? "" : __t) + "</li> <li>1 &nbsp;&nbsp;" + ((__t = $t("母件A")) == null ? "" : __t) + "</li> <li>1.1 &nbsp;&nbsp;" + ((__t = $t("子件")) == null ? "" : __t) + "a</li> <li>1.1.1 &nbsp;&nbsp;" + ((__t = $t("子件a的子件1")) == null ? "" : __t) + "</li> <li>1.1.2 &nbsp;&nbsp;" + ((__t = $t("子件a的子件2")) == null ? "" : __t) + "</li> <li>1.2 &nbsp;&nbsp;" + ((__t = $t("子件")) == null ? "" : __t) + 'b</li> </ul> </div> <!--cpq子产品价目表规则--> <div class="on-off pp-switch cpq-pricebook-config "> <div class="title">3、' + ((__t = $t("cpq-子产品价目表规则")) == null ? "" : __t) + '</div> <div class="cpq-pricebook-config-box"></div> </div> <div class="on-off pp-switch cpq-pricebook-config "> <div class="title">4、' + ((__t = $t("BOM计算不包含默认选中项价格")) == null ? "" : __t) + '</div> <div class="cpq-calculateConfig-box"></div> </div> <!--cpq开启生成选配实例--> <div class="on-off pp-switch bomPrintOnlyRootBtn "> <label class="title">5、' + ((__t = $t("cpq.optionalSwitch")) == null ? "" : __t) + '</label> <div class="bomInstanceBtn switch-sec ' + ((__t = bom_instance ? " on j-set-on" : "j-set-config") == null ? "" : __t) + '"> </div> </div> <!--临时子件--> <div class="on-off pp-switch bomPrintOnlyRootBtn "> <label class="title">6、' + ((__t = $t("开启临时子件")) == null ? "" : __t) + '</label> <div class="bom_temp_node switch-sec ' + ((__t = bom_temp_node ? " on j-set-on" : "j-set-config") == null ? "" : __t) + '"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/cpq/cpq",["crm-modules/common/util","./cpq-html","./ladderPrice-html","./tpl-html"],function(t,e,a){var n=t("crm-modules/common/util"),o=t("./cpq-html"),c=t("./ladderPrice-html");return Backbone.View.extend({template:t("./tpl-html"),initialize:function(t){this.setElement(t.wrapper),this.spuSetting=!1,this.productOpenSpu=!1,this.tenant_id=CRM.enterpriseId},events:{"click .j-switch-tab":"switchTab","click .j-set-config":"setConfig","click .j-set-on":"showTip","click .j-reload-config":"_reloadHandle"},render:function(t,e){this.renderGuide()},renderGuide:function(){var e=this;this.$el.html(this.template()),t.async("crm-modules/components/biz_manage/biz_manage",function(t){this.guide=new t({$el:$(".render-content",e.$el),module:"cpqconfigure",type:"cpq"})})},renderTpl:function(){var e=this,a={"cpq-box":o,"ladderPrice-box":c};"ladderPrice-box"===e.tarTab?n.getCpqConfig().then(function(t){$(".render-content",e.$el).html(a[e.tarTab]({start:CRM._cache.cpqStatus,isOpenCpq:t,priceBookPriority:CRM._cache.priceBookPriority}))}):$(".render-content",e.$el).html(a[e.tarTab]({start:CRM._cache.cpqStatus,priceBookPriority:CRM._cache.priceBookPriority,bomPrintIndent:CRM._cache.bomPrintIndent,bom_print_template_has_sub_node:CRM._cache.bom_print_template_has_sub_node,bom_price_calculation_configuration:CRM._cache.bom_price_calculation_configuration,bom_instance:CRM._cache.bom_instance,bom_temp_node:CRM._cache.bom_temp_node})),this.renderRadio(),this.renderBOMCalculateCom()},isCpqBuyed:function(){return new Promise(function(e,t){n.FHHApi({url:"/EM1HNCRM/API/v1/object/version_privilege/service/check_app",data:{proudct_code:"cpq_app"},success:function(t){0===t.Result.StatusCode?e(t.Value.result):n.alert(t.Result.FailureMessage)}},{errorAlertModel:1})})},switchTab:function(t){t=$(t.target);t.parent().children().removeClass("cur"),t.addClass("cur"),this.tarTab=t.attr("data-render"),this.getConfig(this.renderTpl)},_reloadHandle:function(t){this.getConfig(this.renderTpl,$(t.currentTarget))},getConfig:function(e,t){var a=this;"cpq-box"===a.tarTab&&CRM.util.getConfigValues(["28","enforce_priority","bom_print_template_indent","bom_print_template_has_sub_node","cpq","bom_adaptation_price_list_rules","bom_price_calculation_configuration","bom_instance","bom_temp_node"]).then(function(t){CRM._cache.openPriceList="1"===(_.findWhere(t,{key:"28"})||{}).value,CRM._cache.priceBookPriority="1"===(_.findWhere(t,{key:"enforce_priority"})||{}).value,CRM._cache.bomPrintIndent="1"===(_.findWhere(t,{key:"bom_print_template_indent"})||{}).value,CRM._cache.bom_print_template_has_sub_node="1"===(_.findWhere(t,{key:"bom_print_template_has_sub_node"})||{}).value,CRM._cache.bom_instance="1"===(_.findWhere(t,{key:"bom_instance"})||{}).value,CRM._cache.bom_price_calculation_configuration=(_.findWhere(t,{key:"bom_price_calculation_configuration"})||{}).value,CRM._cache.cpqStatus="1"===(_.findWhere(t,{key:"cpq"})||{}).value,CRM._cache.bom_adaptation_price_list_rules=(_.findWhere(t,{key:"bom_adaptation_price_list_rules"})||{}).value,CRM._cache.bom_temp_node="1"===(_.findWhere(t,{key:"bom_temp_node"})||{}).value,e&&e.call(a,CRM._cache.openPriceList)})},setConfig:function(t){var e=this,a={};"cpq-box"===e.tarTab&&((t=$(t.target)).hasClass("cpqClass")?n.getCrmAllConfig(function(){CRM._cache.spuStatus?n.alert($t("请先关闭SPU再开启CPQ，且开启CPQ之后，不能再开启SPU")):CRM._cache.promotionStatus?n.alert($t("先关闭促销再开启CPQ")):(a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"cpq",tenantId:e.tenant_id,openStatus:1},status:!0,targetCon:"cpq",confirmInfo:$t("crm.确认开启CPQ吗"),confirmTit:$t("提示")},e.setConfigHandle(a))}):t.hasClass("bomPrintIndentClass")?CRM._cache.cpqStatus?CRM._cache.bom_print_template_has_sub_node?n.alert($t("打印层级开关和只打印母件开关互斥，不能同时打开")):(a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"bom_print_template_indent",tenantId:e.tenant_id,openStatus:CRM._cache.bomPrintIndent?0:1},status:!CRM._cache.bomPrintIndent,targetCon:"bomPrint",confirmInfo:CRM._cache.bomPrintIndent?$t("确认关闭打印支持层级展示吗")+"?":$t("确认开启打印支持层级展示吗")+"?",confirmTit:$t("提示")},e.setConfigHandle(a)):n.alert($t("请先开启CPQ")):t.hasClass("bomPrintOnlyRootClass")?CRM._cache.cpqStatus?CRM._cache.bomPrintIndent?n.alert($t("打印层级开关和只打印母件开关互斥")):(a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"bom_print_template_has_sub_node",tenantId:e.tenant_id,openStatus:CRM._cache.bom_print_template_has_sub_node?0:1},status:!CRM._cache.bom_print_template_has_sub_node,targetCon:"bomPrintOnlyRoot",confirmInfo:CRM._cache.bom_print_template_has_sub_node?$t("确认关闭只打印母件开关吗？"):$t("打印订单、报价单、销售合同开启多层级区分展示"),confirmTit:$t("提示")},e.setConfigHandle(a)):n.alert($t("请先开启CPQ")):t.hasClass("bomCalculateSwitch")?CRM._cache.cpqStatus?(a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"bom_price_calculation_configuration",tenantId:e.tenant_id,openStatus:CRM._cache.bom_price_calculation_configuration?0:1},status:!CRM._cache.bom_price_calculation_configuration,targetCon:"bomCalculateSwitch",confirmInfo:CRM._cache.bom_price_calculation_configuration?$t("确认关闭产品包不含默认选中项价格开关吗？"):$t("打开后默认选中项价格不包含在产品包价格中呢"),confirmTit:$t("提示")},e.setConfigHandle(a)):n.alert($t("请先开启CPQ")):t.hasClass("bomInstanceBtn")?CRM._cache.cpqStatus?(a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"bom_instance",tenantId:e.tenant_id,openStatus:CRM._cache.bom_instance?0:1},status:!CRM._cache.bom_instance,targetCon:"bom_instance",confirmInfo:$t("cpq.optionalSwitch.msg"),confirmTit:$t("提示")},e.setConfigHandle(a)):n.alert($t("请先开启CPQ")):t.hasClass("bom_temp_node")&&(CRM._cache.cpqStatus?(a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"bom_temp_node",tenantId:e.tenant_id,openStatus:CRM._cache.bom_temp_node?0:1},status:!CRM._cache.bom_temp_node,targetCon:"bom_temp_node",confirmInfo:$t("确认开启临时子件吗？开关开启后不可关闭"),confirmTit:$t("提示")},e.setConfigHandle(a)):n.alert($t("请先开启CPQ"))))},getConfigHandle:function(e,t){var a=this;a._getAjax&&(a._getAjax.abort(),a._getAjax=null),a._getAjax=n.FHHApi({url:e.url,data:e.data||"",success:function(t){0===t.Result.StatusCode?e.success(t):n.alert(t.Result.FailureMessage)},complete:function(){a._getAjax=null}},{errorAlertModel:1,submitSelector:t})},setConfigHandle:function(e){var a=this,t=null,t=n.confirm(e.confirmInfo,e.confirmTit,function(){n.FHHApi({url:e.url,data:e.data,success:function(t){if(0==t.Result.StatusCode)return"cpq"!=e.data.moduleCode||t.Value.success?(n.remind(1,e.successText||$t("启用成功")),"bomPrint"===e.targetCon&&(CRM._cache.bomPrintIndent="1"===t.Value.value.openStatus),"bomPrintOnlyRoot"===e.targetCon&&(CRM._cache.bom_print_template_has_sub_node="1"===t.Value.value.openStatus),"bom_instance"===e.targetCon&&(CRM._cache.bom_instance="1"===t.Value.value.openStatus),"bom_temp_node"===e.targetCon&&(CRM._cache.bom_temp_node="1"===t.Value.value.openStatus),"cpq"===e.targetCon&&(CRM._cache.cpqStatus=e.status,a._addLog()),a.renderTpl(e.status),CRM.control.refreshAside(),void a.initProduct()):void n.alert(t.Value.errMessage);n.alert(t.Result.FailureMessage)},complete:function(){t.hide(),a.isSetting=!1}},{errorAlertModel:1,submitSelector:t.$(".b-g-btn")})})},showTip:function(t){"cpq-box"==this.tarTab?$(t.target).hasClass("cpqClass")?n.alert($t("CPQ开启无法再关闭")):$(t.target).hasClass("bomInstanceBtn")&&n.alert($t("开关开启无法再关闭")):this.setConfig(t)},initProduct:function(){n.FHHApi({url:"/EM1HNCRM/API/v1/object/pricebook_standard/service/check_or_init_standard_pricebook",data:{}},{errorAlertModel:1})},_addLog:function(){CRM.util.sendLog("BOMObj","mannage",{eventId:"opencpq"})},renderRadio:function(){var e=this,t=this.$el.find(".cpq-pricebook-config-box");this.radio=FxUI.create({wrapper:t[0],template:'<fx-radio-group v-model="radio" @change="onChange">\n\t\t\t\t\t\t\t <fx-radio class="cpq-pricebook-config-def" label="0">'.concat($t("子件产品跟随母件选择的价目表"),'\n\t\t\t\t\t\t\t <br/>\n\t\t\t\t\t\t\t <div class="crm-intro-warning">').concat($t("主子同步注意1"),'</div>\n\t\t\t\t\t\t\t </fx-radio>\n\t\t\t\t\t\t\t <br/>\n\t\t\t\t\t\t\t <fx-radio label="1">').concat($t("子件产品随价目表优先级"),'\n\t\t\t\t\t\t\t <br/>\n\t\t\t\t\t\t\t <div class="crm-intro-warning">').concat($t("主子同步注意2"),"</div>\n\t\t\t\t\t\t\t </fx-radio>\n\t\t\t\t\t\t  </fx-radio-group>"),data:function(){return{radio:CRM._cache.bom_adaptation_price_list_rules||"0"}},methods:{onChange:function(t){e.afterChangeRadio(t)}}})},afterChangeRadio:function(e){CRM.util.setConfigValue({key:"bom_adaptation_price_list_rules",value:e}).then(function(t){n.remind(1,$t("设置成功")),CRM._cache.bom_adaptation_price_list_rules=e})},renderBOMCalculateCom:function(){var e=this,t=this.$el.find(".cpq-calculateConfig-box");this.radio=FxUI.create({wrapper:t[0],template:'<fx-radio-group v-model="radio" @change="onChange">\n\t\t\t\t\t\t\t <fx-radio class="cpq-pricebook-config-def" label="0">'.concat($t("产品包价格包含默认选择子件价格"),'\n\t\t\t\t\t\t\t  <br/>\n\t\t\t\t\t\t\t <div class="crm-intro-warning">').concat($t("BOM计算说明1"),'</div>\n\t\t\t\t\t\t\t </fx-radio>\n\t\t\t\t\t\t\t <br/>\n\t\t\t\t\t\t\t <fx-radio label="1">').concat($t("产品包价格不包含默认选择子件价格"),'\n\t\t\t\t\t\t\t  <br/>\n\t\t\t\t\t\t\t <div class="crm-intro-warning">').concat($t("BOM计算说明2"),"</div>\n\t\t\t\t\t\t\t </fx-radio>\n\t\t\t\t\t\t  </fx-radio-group>"),data:function(){return{radio:CRM._cache.bom_price_calculation_configuration||"0"}},methods:{onChange:function(t){e.afterChangeCalculateCom(t)}}})},afterChangeCalculateCom:function(e){CRM.util.setConfigValue({key:"bom_price_calculation_configuration",value:e}).then(function(t){n.remind(1,$t("设置成功")),CRM._cache.bom_price_calculation_configuration=e})},destroy:function(){this.radio&&this.radio.destroy&&this.radio.destroy(),this.radio=this._getAjax=null,this.remove()}})});
define("crm-setting/cpq/ladderPrice-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro "> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("该开关一旦开启，不可关闭。")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("开启该开关的前提，必须开启CPQ和价目表开关。")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("价格政策开关说明")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("价格政策开关说明2")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off"> <label class="title">' + ((__t = $t("开启价格政策")) == null ? "" : __t) + "</label> ";
            var statusClass = "disabled";
            __p += " ";
            if (start) {
                __p += " ";
                statusClass = " on j-set-on";
                __p += " ";
            } else if (isOpenCpq) {
                __p += " ";
                statusClass = " j-set-config";
                __p += " ";
            }
            __p += ' <div class="switch-sec ' + ((__t = statusClass) == null ? "" : __t) + '"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/cpq/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("CPQ管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="tab-con"> <div class="item render-content"> </div> </div> </div> </div>';
        }
        return __p;
    };
});