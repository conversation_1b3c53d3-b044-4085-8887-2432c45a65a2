define("crm-setting/ddsreportdata/ddsreportdata",["./model/model","./pages/reportdata/reportdata","./tpl-html"],function(e,t,i){var a=e("./model/model"),r=e("./pages/reportdata/reportdata");i.exports=Backbone.View.extend({template:e("./tpl-html"),initialize:function(e){this.setElement(e.wrapper),this.model=new a},render:function(){this.$el.html(this.template({title:this.model.get("title")})),this.renderPage()},renderPage:function(){this.page=new r({element:this.$(".j-setting-wrap"),model:this.model}),this.page.render()},destroy:function(){this.page&&this.page.destroy(),this.page=null,this.undelegateEvents(),this.remove()}})});
define("crm-setting/ddsreportdata/model/api",[],function(t,e,r){var o=FS.crmUtil;r.exports={_commonApi:function(t){var e=_.extend({url:"",type:"post"},t||{}),t={submitSelector:e.submitSelector,errorAlertModel:e.errorAlertModel||2};return e.successCb&&!e.success&&(e.success=function(t){0==t.Result.StatusCode&&e.successCb(t.Value)}),o.FHHApi(_.omit(e,"errorAlertModel","submitSelector","successCb"),t)},queryDataReport:function(e){var r=this;return this._commonApi({url:"/EM1HDDS/dataReportConfig/isOpenDataReport",successCb:function(t){r.set("dataReportSwitchCode",t?1:2),e&&e(t)}})},switchDataReport:function(t){return this._commonApi(_.extend({url:"/EM1HDDS/dataReportConfig/saveDataReportSwitch",data:{switchCode:2}},t))}}});
define("crm-setting/ddsreportdata/model/model",["./api"],function(e,t,d){return Backbone.Model.extend(_.extend({defaults:{title:$t("渠道数据上报"),dataReportSwitchCode:2}},e("./api")))});
define("crm-setting/ddsreportdata/pages/reportdata/reportdata",["../../switch/switch","./tpl-html"],function(t,e,i){var s=t("../../switch/switch"),n=FS.crmUtil;i.exports=Backbone.View.extend({className:"data-report",template:t("./tpl-html"),options:{element:null,topInfo:{messages:[$t("启用渠道数据上报后，会增加经销-b537b843"),$t("启用后，【企业互联】对接企业详-143f9960")]},switchLabel:$t("渠道数据上报")},initialize:function(t){this.options=_.extend(this.options,t),this.options.element&&this.options.element.html(this.$el)},render:function(){var t=this;this.$el.html('<div class="crm-loading content-loading"></div>'),this.model.queryDataReport(function(){t.$el.html(t.template({topInfo:t.options.topInfo})),t.renderSwitch()})},renderSwitch:function(){this.switch&&this.switch.destroy(),this.switch=new s({el:this.$(".j-setting-switch")[0],label:this.options.switchLabel,type:this.getSwitchType()});var i=this;this.switch.on("open",function(){var t,e;1!==i.model.get("dataReportSwitchCode")&&(t=$t("启用渠道数据上报功能后，不可停-d439faa7"),e=n.confirm(t,$t("温馨提示"),function(){i.model.switchDataReport({submitSelector:e.$(".b-g-btn")}).done(function(t){e.hide(),0==t.Result.StatusCode&&t.Value.isSuccess&&(n.remind(1,$t("渠道数据上报开启成功")),i.render(),CRM.control.refreshAside())})}))}),this.switch.render()},getSwitchType:function(){return 1===this.model.get("dataReportSwitchCode")?"info":"unopen"},destroy:function(){this.switch&&this.switch.destroy(),this.switch=null,this.undelegateEvents(),this.remove()}})});
define("crm-setting/ddsreportdata/pages/reportdata/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> ';
            if (_.isString(obj.topInfo.messages)) {
                __p += " " + __e(obj.topInfo.messages) + " ";
            } else {
                __p += " <h3>" + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> ";
                _.each(obj.topInfo.messages, function(msg, index) {
                    __p += " <li>" + ((__t = index + 1) == null ? "" : __t) + ". " + __e(msg) + "</li> ";
                });
                __p += " </ul> ";
            }
            __p += ' </div> <div class="setting-content"> <div class="j-setting-switch"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/ddsreportdata/switch/switch",["./tpl-html","./type-html"],function(t,e,i){i.exports=Backbone.View.extend({template:t("./tpl-html"),typeTemplate:t("./type-html"),options:{label:"",type:"unopen",desc:$t("已启用"),unopenBtn:$t("启用")},events:{"click [data-action]":"doAction"},initialize:function(t){this.options=_.extend(this.options,t)},render:function(){this.$el.html(this.template({label:this.options.label})),this.renderType()},renderType:function(){this.$(".j-switch-operate").html(this.typeTemplate(this.options))},doAction:function(t){t=$(t.currentTarget).attr("data-action");this.trigger(t)},destroy:function(){this.undelegateEvents(),this.remove()}})});
define("crm-setting/ddsreportdata/switch/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="s-m-switch"> <div class="switch-lable">' + __e(obj.label) + '</div> <div class="switch-operate j-switch-operate"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/ddsreportdata/switch/type-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (obj.type === "info") {
                __p += " <span>" + __e(obj.desc) + "</span> ";
            }
            __p += " ";
            if (obj.type === "off") {
                __p += ' <i class="operate-btn_switch j-switch" data-action="open"></i> ';
            }
            __p += " ";
            if (obj.type === "on") {
                __p += ' <i class="operate-btn_switch operate-btn_switch--on j-switch" data-action="off"></i> ';
            }
            __p += " ";
            if (obj.type === "unopen") {
                __p += ' <a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary j-switch" data-action="open">' + __e(obj.unopenBtn) + "</a> ";
            }
        }
        return __p;
    };
});
define("crm-setting/ddsreportdata/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2> <span class="tit-txt j-setting-title">' + ((__t = obj.title) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con j-stock-con"> <div class="setting-wrap j-setting-wrap"> </div> </div>';
        }
        return __p;
    };
});