define("crm-setting/myobject/guide-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-w-guide-alert"> <div class="inner"> <h3>V6.' + ((__t = $t("2重大回款更新")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("回款有功能升级和配置变更请查收")) == null ? "" : __t) + '</p> <a href="https://www.fxiaoke.com/mob/guide/6.2.0/crm/6.2/6.2.html#173-%E5%9B%9E%E6%AC%BE%E9%87%8D%E8%A6%81%E6%9B%B4%E6%96%B0%EF%BC%8C%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9" target="_blank">' + ((__t = $t("查看详情")) == null ? "" : __t) + '</a> </div> <span class="close">×</span> </div>';
        }
        return __p;
    };
});
define("crm-setting/myobject/myobject",["crm-modules/common/util","crm-widget/table/table","paas-object/sdk.js","./guide-html"],function(e,t,i){var a=e("crm-modules/common/util"),s=e("crm-widget/table/table"),n=e("paas-object/sdk.js"),c=(e("./guide-html"),Backbone.View.extend({initialize:function(e){var t=a.getTplQueryParams()||{};this.setElement(e.wrapper),this.oObjectSDK=new n,this.useableDescribeCount=1,this.keyword="",t.api_name?this.jumpObjectDetail(t):this.initDesc()},loadingTpl:_.template('<div class="myobject-loading"></div>'),events:{"click .j-designadd":"onDesignAdd","click .j-exceladd":"onExcelAdd"},initDesc:function(){this.$el.html('<div class="myobject-box"><div class="my-tb-wrap"></div><div class="my-tb-info"></div></div>'),this.initTb()},initTb:function(){var n=this,e=[{text:$t("excel.add.describe"),className:"j-exceladd",isFold:!1},{text:$t("新建"),className:"j-designadd",isFold:!1}];n.tb=new s({$el:n.$(".my-tb-wrap"),tableName:"inventory",url:"/EM1HCRMUdobj/objectDescribe/findByTenantId",postData:{tenant_id:""},requestType:"FHHApi",showMultiple:!1,trHandle:!1,searchTip:$t("对象"),title:$t("自定义对象管理")+'<a class="crm-doclink" href="https://help.fxiaoke.com/dbde/2049/629c/554f" target="_blank"></a>',search:{placeHolder:$t("搜索自定义对象"),type:"Keyword",highFieldName:"Name"},doStatic:!0,openStart:!0,showPage:!1,operate:{moreBtnClass:"crm-btn",btns:e},columns:[{data:"Name",title:$t("自定义对象名称"),width:250,render:function(e,t,i){e='<a href="javascript:;" class="j-detail" title="'+(_.escape(i.Name)||"--")+'">'+e+"</a>";return i&&"public"==i.visibleScope&&(e+='<span class="el-tag fx-tag el-tag--link el-tag--small el-tag--light" style="line-height:19px;height:19px;margin-left:4px;border:0;">Public</span>'),e}},{data:"ApiName",title:"API Name"},{data:"Desc",title:$t("描述")},{data:"CreatedBy",title:$t("创建人"),render:function(e){e=FS.contacts.getEmployeeById(e)||{};return e.fullName||e.name||"--"}},{data:"CreateTime",title:$t("创建时间"),dataType:4},{data:"LastModifiedBy",title:$t("最后修改人"),render:function(e){e=FS.contacts.getEmployeeById(e)||{};return e.fullName||e.name||"--"}},{data:"LastModifiedTime",title:$t("最后修改时间"),dataType:4},{data:"Status",title:$t("状态")},{data:"Define_type",title:$t("操作"),lastFixed:!0,width:170,render:function(){var e=arguments[2]||{},t=["disable","enable"][+!e.IsActive],i=[$t("禁用"),$t("启用")][+!e.IsActive],a="",s=(n.objectConfigs||{})[e.ApiName]||{};return 0==(null===s||null==(s=s.object)?void 0:s.controlLevel)?a="":e.OriginalDescribeApiName?a+='<a href="javascript:;" class="j-object-btn j-extendchangeorder">'+$t("同步")+"</a>":a+='<a href="javascript:;" class="j-object-btn j-'+t+'">'+i+'</a><a href="javascript:;" class="j-object-btn j-delete">'+$t("删除")+'</a></a><a href="javascript:;" class="j-object-btn j-copy">'+$t("复制")+"</a>",a}}],formatData:function(e){return null},initComplete:function(){n.keyword?(n.tb.$el.find(".dt-ipt").val(n.keyword),n.tb.$el.find(".dt-sc-btn").trigger("click")):n.searchObjectList()}}),n.tb.on("dt.search",function(e){n.keyword=e,n.searchObjectList()}),n.tb.on("trclick",function(e,t,i){0<i.closest(".j-detail").length?n.showDetail(e):i.hasClass("j-delete")?n.onDelete(e):i.hasClass("j-enable")?n.onEnableOrDisable(e,!0):i.hasClass("j-disable")?n.onEnableOrDisable(e,!1):i.hasClass("j-copy")?n.onCopy(e):i.hasClass("j-extendchangeorder")&&n.onExtendChangeOrder(e)})},refresh:function(e){CRM.control.refreshAside(),this.searchObjectList()},searchObjectList:function(){var c=this;c.tb.showLoading(),a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeManageList",data:{isIncludeFieldDescribe:!1,describeDefineType:"custom",isIncludeUnActived:!0,sourceInfo:"object_management",packageName:"CRM",includeControlLevel:!0},success:function(e){var e=e.Value||{},t=e.objectDescribeList,t=void 0===t?[]:t,i=e.useableDescribeCount,i=void 0===i?0:i,a=e.manageGroup,s=void 0===a?{}:a,a=e.objectConfigs,e=void 0===a?{}:a,n=[],t=t.filter(function(e){return!1===s.all&&s.apiNames&&s.apiNames.includes(e.api_name)||!1!==s.all}),a=(c.objectConfigs=e,c.objectDescribeList=t,_.each(t,function(e){var t={Name:e.display_name,ApiName:e.api_name,OriginalDescribeApiName:e.original_describe_api_name,visibleScope:e.visible_scope,CreatedBy:e.created_by,CreateTime:new Date(e.create_time).getTime(),Desc:e.description,Status:[$t("禁用"),$t("启用")][+(e.is_active||0)],IsActive:e.is_active,Define_type:e.define_type,LastModifiedBy:e.last_modified_by,LastModifiedTime:e.last_modified_time};(""==c.keyword||-1<(e.display_name||"").toLowerCase().indexOf((c.keyword||"").toLowerCase()))&&n.push(t)}),c.tb.hideLoading(),c.tb.doStaticData(n),$t("当前自定义对象个数{{length}}",{length:n.length}));_.isNumber(i)&&(a+="&nbsp&nbsp|&nbsp&nbsp"+$t("剩余可用自定义对象个数{{useableDescribeCount}}",{useableDescribeCount:i}),c.useableDescribeCount=i),c.$(".my-tb-info").html(a)}})},jumpObjectDetail:function(e){this.showDetail({Name:e.display_name,ApiName:e.api_name,ChildType:e.child_type,SubChildType:e.sub_child_type})},showDetail:function(t){var i=this;i.$el.append(this.loadingTpl()),this.oObjectSDK.getDetail({display_name:t.Name,api_name:t.ApiName},function(e){i.$el.find(".myobject-loading").remove();e=e.defaultView({child_type:t.ChildType,sub_child_type:t.SubChildType});e.on("go_back",function(){i.initDesc()}),e.render(),i.$el.html(e.$el),e.resetTab()})},onDelete:function(e){var t=this;t.oObjectSDK.deleteObject({api_name:e.ApiName,is_active:e.IsActive},function(e){"success"===e&&t.refresh()})},onEnableOrDisable:function(e,t){var i=this;i.oObjectSDK.enableOrDisableObject({api_name:e.ApiName,is_active:t},function(e){"success"==e&&i.refresh()})},onExtendChangeOrder:function(e,t){var i=e.OriginalDescribeApiName,a=(this.objectDescribeList||{}).find(function(e){return e.api_name==i})||{};this.oObjectSDK.extendChangeOrderObject({api_name:e.ApiName,display_name:e.Name,original_describe_api_name:i,original_describe_display_name:a.display_name||""},function(e){})},onCopy:function(e){var t=this;t.oObjectSDK.CopyObject({targetObjectDescribeApiName:e.ApiName,describeLabel:$t("paas.crm.setting.myobject.duplicate",null,"副本_")+e.Name},function(e){"success"===e&&t.refresh()}),FS.log&&FS.log("s-paasobj_copy_object","cl",{module:"s-paasobj",subModule:"copy"})},onExcelAdd:function(){var t,i=this;i.useableDescribeCount<=0?a.alert($t("自定义对象配额不足")):(t=function(e){e&&(CRM.control.refreshAside(),i.showDetail({Name:e.objectDescribe.display_name,ApiName:e.objectDescribe.api_name}))},i.$el.append(this.loadingTpl()),e.async("paas-vui/sdk",function(e){e.getObjectManage().then(function(e){e.excelDescribe().then(function(e){i.$el.find(".myobject-loading").remove(),e.create().$on("close",t)})})}),FS.log&&FS.log("s-paasobj_create_object_excel","cl",{module:"s-paasobj",subModule:"excel"}))},onDesignAdd:function(){var t,i=this;i.useableDescribeCount<=0?a.alert($t("自定义对象配额不足")):(t=function(e){e&&(CRM.control.refreshAside(),i.showDetail({Name:e.objectDescribe.display_name,ApiName:e.objectDescribe.api_name}))},i.$el.append(this.loadingTpl()),this.oObjectSDK.getNewLayoutDesigner().then(function(e){i.$el.find(".myobject-loading").remove(),e.init().$on("close",t)}),FS.log&&FS.log("s-paasobj_create_object_designer","cl",{module:"s-paasobj",subModule:"designer"}))},destroy:function(){this.remove()}}));i.exports=c});