define("crm-setting/exchangegoods/components/base",[],function(t,e,n){var i=Backbone.View.extend({options:{wrapper:""},events:{"click [action-type]":"actionHandle"},initialize:function(t){this.super=i.prototype,this.options=_.extend({},this.super.options,this.options,t),this.events=_.extend({},this.super.events,this.events),this.options.wrapper&&this.setElement(this.options.wrapper),this.initCompleteHooks()},initCompleteHooks:function(){},render:function(){},actionHandle:function(t,e,n){},doActions:function(t){var e=$(t.currentTarget),n=(e.attr("action-type")||"").toLowerCase(),i=n?n+"Handle":"actionHandle";_.isFunction(this[i])&&this[i](e,t,n)},destroy:function(){this.undelegateEvents(),this.remove(),this.$el=this.options=null}});n.exports=i});
define("crm-setting/exchangegoods/components/components",["./base","./base","./field","./field","./tabs/tabs","./switch/switch","./selectone/selectone","./selectone/selectone"],function(e,s,t){t.exports={base:e("./base"),Base:e("./base"),field:e("./field"),Field:e("./field"),Tabs:e("./tabs/tabs"),Switch:e("./switch/switch"),selectone:e("./selectone/selectone"),SelectOne:e("./selectone/selectone")}});
define("crm-setting/exchangegoods/components/field",["./base"],function(t,e,i){var n=t("./base");i.exports=n.extend({options:{name:"",autoListen:!0},dataEvents:{},initCompleteHooks:function(){var t,i=this;this.fieldAttr=this.getAttr(),this.options.autoListen&&(t="change:"+this.options.name,this.dataEvents[t]=this.dataEvents[t]||"dataChange"),_.each(this.dataEvents,function(t,e){i.listenTo(i.model,e,i[t])})},get:function(t){return this.model.get(t||this.options.name)},set:function(t,e){e=e||this.options.name,this.model.set(e,t)},getAttr:function(){return this.get("fields")[this.options.name]||{}},isReadonly:function(){return this.fieldAttr.isReadonly},isHidden:function(){return this.fieldAttr.isHidden},disabled:function(){this.fieldAttr.isReadonly=!0},setStatus:function(){this.isHidden()?this.$el.hide():this.$el[this.isReadonly()?"addClass":"removeClass"]("view-field--disabled").show()},renderField:function(t){this.render()},dataChange:function(){this.renderField(this.get())},destroy:function(){this.model=null,n.prototype.destroy.call(this)}})});
define("crm-setting/exchangegoods/components/selectone/selectone",["../field","./tpl-html"],function(e,t,i){var l=e("../field");i.exports=l.extend({template:e("./tpl-html"),render:function(){var e=this.fieldAttr,t=this.get();this.$el.addClass("c-view-selectone"),this.$el.html(this.template({label:e.label,tip:e.tip,options:e.options,value:t}))},renderField:function(e){this.$(".field-radio_item").removeClass("on").filter("[data-value="+e+"]").addClass("on")},selectHandle:function(e,t){this.isReadonly()||this.isHidden()||(t=+t.attr("data-value"))!==this.get()&&this.set(t)}})});
define("crm-setting/exchangegoods/components/selectone/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<label class="field-label">' + __e(obj.label) + "</label> ";
            if (obj.tip) {
                __p += ' <span class="field-label--tip">' + ((__t = obj.tip) == null ? "" : __t) + "</span> ";
            }
            __p += ' <div class="field-content"> ';
            _.each(obj.options, function(item) {
                __p += ' <div class="field-radio"> <div class="field-radio_item ' + ((__t = obj.value == item.value ? "on" : "") == null ? "" : __t) + '" action-type="select" data-value="' + ((__t = item.value) == null ? "" : __t) + '"> <i class="field-radio_icon"></i> <span>' + __e(item.name) + "</span> </div> </div> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/exchangegoods/components/switch/switch",["../base","./tpl-html","./type-html"],function(t,e,n){var i=t("../base");n.exports=i.extend({template:t("./tpl-html"),typeTemplate:t("./type-html"),options:{label:"",type:"off",desc:$t("已启用"),unopenBtn:$t("开启")},events:{"click .j-switch":"actionHandle"},render:function(){this.$el.html(this.template(this.options)),this.renderType()},renderType:function(){this.$(".switch-operate").html(this.typeTemplate(this.options))},actionHandle:function(t){var t=$(t.currentTarget),e=t.attr("data-type");e&&this.trigger("switch",e,t)},setValue:function(t,e){this.options.type=t,e&&("info"===t&&(this.options.desc=e),"unopnen"===t)&&(this.options.unopenBtn=e),this.renderType()}})});
define("crm-setting/exchangegoods/components/switch/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="c-switch"> <label class="switch-label">' + __e(obj.label) + '</label> <div class="switch-operate"> </div> </div> <div class="j-switch-tip"></div>';
        }
        return __p;
    };
});
define("crm-setting/exchangegoods/components/switch/type-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (obj.type === "info") {
                __p += " <span>" + __e(obj.desc) + "</span> ";
            }
            __p += " ";
            if (obj.type === "off") {
                __p += ' <i class="operate-btn_switch j-switch" data-type="off"></i> ';
            }
            __p += " ";
            if (obj.type === "on") {
                __p += ' <i class="operate-btn_switch operate-btn_switch--on j-switch" data-type="on"></i> ';
            }
            __p += " ";
            if (obj.type === "unopen") {
                __p += ' <a href="javascript:;" class="crm-btn crm-btn-sm crm-btn-primary j-switch" data-type="unopen">' + __e(obj.unopenBtn) + "</a> ";
            }
        }
        return __p;
    };
});
define("crm-setting/exchangegoods/components/tabs/tabs",["../base","./tpl-html"],function(t,e,a){var s=t("../base");a.exports=s.extend({template:t("./tpl-html"),options:{tabs:[],curTab:0},events:{"click .j-tab":"selectHandle"},render:function(){var t=this.options;this.$el.html(this.template({tabs:t.tabs,curTab:t.curTab}))},selectHandle:function(t){var t=$(t.currentTarget),e=+t.attr("data-tab");e!==this.options.curTab&&(this.setValue(e,t),this.trigger("change",e))},setValue:function(t,e){(e||this.$("[data-tab="+t+"]")).addClass("cur").siblings(".cur").removeClass("cur"),this.options.curTab=t}})});
define("crm-setting/exchangegoods/components/tabs/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tab"> ';
            _.each(obj.tabs, function(tab, index) {
                __p += ' <span class="j-tab ' + ((__t = index === obj.curTab ? "cur" : "") == null ? "" : __t) + '" data-tab="' + ((__t = index) == null ? "" : __t) + '">' + __e(tab) + "</span> ";
            });
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/exchangegoods/exchangegoods",["./model/model","./tpl-html"],function(n,e,t){var a=n("./model/model");t.exports=Backbone.View.extend({template:n("./tpl-html"),initialize:function(e){this.setElement(e.wrapper),this.model=new a,this.listenTo(this.model,"change:curtPage",this.renderPage)},render:function(){var t=this;this.$el.html(this.template({title:t.model.get("title")})),this.model.fetchExchangeManageConfig(function(e){e=2!==e.exchangeGoodsNoteStatus?3:0;t.model.set("curtPage",e)})},renderPage:function(){var t=this,e=this.getPagePath(this.model.get("curtPage"));e&&n.async(e,function(e){t.page=new e({wrapper:t.$(".j-page-content"),model:t.model}),t.page.render()})},getPagePath:function(e){var t={0:"exchange",3:"update"};if(t[e])return"./pages/"+t[e]+"/"+t[e]},destroy:function(){this.page&&this.page.destroy(),this.page=null,this.remove()}})});
define("crm-setting/exchangegoods/model/api",[],function(e,t,r){var c=FS.crmUtil;r.exports={_queryApi:function(r){var s=this,e=_.extend({url:"",type:"post",success:function(e){var t;0==e.Result.StatusCode?t=e.Value:s.set({errCode:500}),r.successCb&&r.successCb(t)}},r||{});return c.FHHApi(_.omit(e,"successCb"),{errorAlertModel:2})},_operateApi:function(e){var t=_.extend({url:"",type:"post"},e||{}),e={submitSelector:t.submitSelector};return e.errorAlertModel=t.errorAlertModel||2,t.successCb&&!t.success&&(t.success=function(e){0==e.Result.StatusCode&&t.successCb(e.Value)}),c.FHHApi(_.omit(t,"errorAlertModel","submitSelector","successCb"),e)},fetchExchangeManageConfig:function(t){var r=this;return this._operateApi({url:"/EM1HNCRM/API/v1/object/exchange_return_note/service/query_exchange_return_note_config",successCb:function(e){e&&(r.set({exchangeGoodsNoteStatus:+e.exchangeGoodsNoteSwitch||1,stockStatus:e.isStockEnable?2:1,exchangeReturnNoteStatus:+e.exchangeReturnNoteSwitch||1}),t)&&t(r.toJSON())}})},enableExchangeReturnNote:function(e){return this._operateApi(_.extend({url:"/EM1HNCRM/API/v1/object/exchange_return_note/service/enable_exchange_return_note"},e))}}});
define("crm-setting/exchangegoods/model/model",["./api"],function(e,t,o){FS.crmUtil;o.exports=Backbone.Model.extend(_.extend({defaults:{title:$t("退换货管理"),curtPage:"",exchangeGoodsNoteStatus:1,stockStatus:1,exchangeReturnNoteStatus:1,exchangeGoodsWay:1}},e("./api")))});
define("crm-setting/exchangegoods/pages/exchange/exchange",["../view/view"],function(e,n,o){e=e("../view/view");FS.crmUtil;o.exports=e.extend({className:"view-content crm-scroll j-container",options:{topInfo:{messages:[$t("换货单一旦开启不可关闭。")]},layout:{switchInfo:{name:"exchangegoods",label:$t("换货单状态"),type:"info"}}}})});
define("crm-setting/exchangegoods/pages/multiview/multiview",["crm-modules/components/components","./tpl-html"],function(t,e,i){var s=t("crm-modules/components/components"),n=s.Base,o=s.Tabs,s=(FS.crmUtil,n.extend({template:t("./tpl-html"),options:{tabs:[],curTab:0},initCompleteHooks:function(){this.widgets={}},render:function(){var t=this.options;this.$el.html(this.template(t)),this.$container=this.$(".j-container"),this.initTabs(),this.renderTpl()},setWidget:function(t,e){var i=this.widgets;i[t]&&i[t].destroy&&i[t].destroy(),i[t]=e},initTabs:function(){var e=this,t=this.options,t=new o({el:this.$(".j-crm-tabs").get(0),curTab:t.curTab,tabs:t.tabs});t.render(),t.on("change",function(t){e.options.curTab=t,e.renderTpl()}),this.setWidget("tabs",t)},renderTpl:function(){var t=this.getCurPage();t&&((t=new t({model:this.model})).render(),this.$container.html(t.$el),this.setWidget("page",t))},getCurPage:function(){},destroy:function(){_.each(this.widgets,function(t){t&&t.destroy&&t.destroy()}),this.widgets=null,n.prototype.destroy.call(this)}}));i.exports=s});
define("crm-setting/exchangegoods/pages/multiview/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="j-crm-tabs"> </div> <div class="view-content--hastab crm-scroll j-container"> </div>';
        }
        return __p;
    };
});
define("crm-setting/exchangegoods/pages/return/pages/exchange",["../../view/view"],function(e,n,t){e=e("../../view/view");FS.crmUtil;t.exports=e.extend({className:"view-content crm-scroll",options:{topInfo:{messages:["退换货单默认包括三种业务类型，均不强关联销售订单，分别为退货、换货、补发三种业务场景；"]},layout:{switchInfo:{name:"exchangereturn",label:"退换货单状态",type:"info",tipHtml:"退换货单已开启，建议先去设置对象的字段、布局信息"},fields:[{name:"exchangeGoodsWay",label:"换货差价",type:"selectone",isReadonly:!0,options:[{name:"等价换货，换出产品的价格与换入产品价格相同，支持手动调整",value:1}]}]}}})});
define("crm-setting/exchangegoods/pages/return/pages/return",["../../view/view"],function(e,n,t){e=e("../../view/view");FS.crmUtil;t.exports=e.extend({className:"view-content crm-scroll j-container",options:{topInfo:{messages:["退换货单默认包括三种业务类型，均不强关联销售订单，分别为退货、换货、补发三种业务场景；"]},layout:{switchInfo:{name:"return",label:"退款单状态",type:"info"}}}})});
define("crm-setting/exchangegoods/pages/return/return",["../multiview/multiview","./pages/exchange","./pages/return"],function(e,t,r){var n=e("../multiview/multiview"),a={exchange:e("./pages/exchange"),return:e("./pages/return")};FS.crmUtil;r.exports=n.extend({options:{tabs:["退换货单","退款单"],curTab:0},getCurPage:function(){var e=1===this.options.curTab?"return":"exchange";return a[e]}})});
define("crm-setting/exchangegoods/pages/unenable/unenable",["../view/view"],function(e,t,n){var e=e("../view/view"),o=FS.crmUtil;n.exports=e.extend({className:"view-content crm-scroll j-container",options:{topInfo:{messages:["开启退换货管理后，会新增退换货单、退款单两个对象；","退换货单默认包括三种业务类型，均不强关联销售订单，分别对应退货、换货、补发三种业务场景；","退换货管理功能一旦开启，不可停用；","需将客户端版本升级到 6.8.5 及以上方可在移动端使用；","退款单不强关联销售订单，主要用来处理退货的退款及换货、补发场景下的差价退还；"]},layout:{switchInfo:{name:"exchangegoods",label:$t("退换货管理"),type:"unopen",btnLabel:$t("开启"),desc:$t("已启用"),tipHtml:""}}},switchHandle:function(e){var t,n;"unopen"===e&&(2!==(t=this).model.toJSON().stockStatus?this.showTip($t("stock.stock_manage.warn.text6")):(e=$t("stock.stock_manage.warn.text7"),n=o.confirm(e,$t("stock.stock_manage.warn.text8"),function(){t.widgets.switchInfo.setTip(""),t.widgets.switchInfo.setType("info",$t("系统正在处理请耐心等待")),t.model.enableExchangeReturnNote({submitSelector:n.$(".b-g-btn")}).done(function(e){n.hide(),0==e.Result.StatusCode&&e.Value.isSuccess?(t.showTip($t("stock.stock_manage.warn.text9"),1),t.render(),CRM.control.refreshAside()):(t.widgets.switchInfo.setType("unopen"),t.showTip($t("启用失败请稍后重试或联系纷享客服")))})})))}})});
define("crm-setting/exchangegoods/pages/update/update",["crm-modules/components/components"],function(e,n,t){e=e("crm-modules/components/components").Base;t.exports=e.extend({className:"view-content",render:function(){this.$el.html('<div class="view-content--tip">功能升级中，敬请期待！</div>')}})});
define("crm-setting/exchangegoods/pages/view/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="view-content--inner"> ';
            if (obj.topInfo) {
                __p += ' <div class="crm-intro"> ';
                if (_.isString(obj.topInfo.messages)) {
                    __p += " " + __e(obj.topInfo.messages) + " ";
                } else {
                    __p += " <h3>" + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> ";
                    _.each(obj.topInfo.messages, function(msg, index) {
                        __p += " <li>" + ((__t = index + 1) == null ? "" : __t) + ". " + __e(msg) + "</li> ";
                    });
                    __p += " </ul> ";
                }
                __p += " </div> ";
            }
            __p += ' <div class="view-actions"> <div class="crm-loading tab-loading j-action-loading" style="' + ((__t = obj.loading ? "" : "display: none;") == null ? "" : __t) + '"></div> <p class="j-action-error" style="' + ((__t = obj.errCode !== 0 ? "" : "display: none;") == null ? "" : __t) + '"> <span class="so-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span> <a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + '</a> </p> <div class="j-fields" style="' + ((__t = !obj.loading && obj.errCode === 0 ? "" : "display: none;") == null ? "" : __t) + '"> ';
            if (obj.layout.switchInfo) {
                __p += ' <div class="j-action-switch"></div> ';
            }
            __p += ' </div> <div class="view-fields j-settings"> ';
            _.each(obj.layout.fields, function(b) {
                __p += ' <div class="c-view-field j-field-comp-wrap" data-type="' + ((__t = b.type) == null ? "" : __t) + '" data-name="' + ((__t = b.name) == null ? "" : __t) + '"></div> ';
            });
            __p += ' <div class="view-btns"> ';
            _.each(obj.layout.buttons, function(btn) {
                __p += ' <a href="javascript:;" class="crm-btn crm-btn-primary j-layout-btn" data-action="' + ((__t = btn.action) == null ? "" : __t) + '" style="' + ((__t = btn.show ? "" : "display: none;") == null ? "" : __t) + '">' + ((__t = btn.label) == null ? "" : __t) + "</a> ";
            });
            __p += " </div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/exchangegoods/pages/view/view",["crm-modules/components/components","./tpl-html"],function(t,i,e){var n=t("crm-modules/components/components"),o=n.Base,s=n.Switch,h=FS.crmUtil,t=o.extend({template:t("./tpl-html"),options:{topInfo:null,layout:null},mycomponents:{},events:{"click .j-reload-config":"reloadHandle"},initCompleteHooks:function(){this.model=this.options.model,this.widgets={},this.forms={}},render:function(){this.$el.html(this.template(this.parsePageDescribe())),this.initWidgets(),this.initForm()},parsePageDescribe:function(){var t=this.options;return this.setFields(t.layout&&t.layout.fields),{loading:!1,errCode:0,topInfo:t.topInfo,layout:t.layout}},setFields:function(t){var i={};_.each(t,function(t){i[t.name]=t}),this.model.set("fields",i)},initWidgets:function(){this.initSwitch()},initSwitch:function(){var e=this,t=this.options.layout||{};t.switchInfo&&((t=new s(_.extend({el:e.$(".j-action-switch").get(0)},t.switchInfo))).render(),t.on("switch",function(t,i){e.switchHandle(t,i)}),this.widgets.switch&&this.widgets.switch.destroy(),this.widgets.switch=t)},initForm:function(){var e=this.model,o=this.forms,s=this.mycomponents;this.$(".j-field-comp-wrap").each(function(){var t=$(this).data(),i=s[t.name]||s[t.type]||n[t.type];i&&((i=new i({el:this,model:e,type:t.type,name:t.name})).render(),i.setStatus&&i.setStatus(),o[t.name]=i)})},switchHandle:function(t,i){},showTip:function(t,i){i?h.remind(i,t||$t("操作成功")):h.alert(t||$t("操作失败请稍后尝试或联系纷享客服"))},showLoading:function(){this.$(".j-action-loading").show(),this.$(".j-action-error").hide(),this.$(".j-fields").hide()},hideLoading:function(){this.$(".j-action-loading").hide()},showErrMsg:function(){this.$(".j-action-loading").hide(),this.$(".j-action-error").show(),this.$(".j-fields").hide()},hideErrMsg:function(){this.$(".j-action-error").hide()},showContent:function(){this.hideLoading(),this.hideErrMsg(),this.$(".j-fields").show()},reloadHandle:function(){this.render()},destroy:function(){_.each(this.widgets,function(t){t&&t.destroy&&t.destroy()}),_.each(this.forms,function(t){t&&t.destroy&&t.destroy()}),this.widgets=this.forms=this.model=null,o.prototype.destroy.call(this)}});e.exports=t});
define("crm-setting/exchangegoods/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2 class="page-title"> <span class="tit-txt j-page-title">' + ((__t = obj.title) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con j-page-content"> <div class="crm-loading tab-loading"></div> </div>';
        }
        return __p;
    };
});