define("crm-setting/membermanage/components/crmfilters/config",[],{COMPARE:[{},{value:1,math:"=",EN:"EQ"},{value:2,math:"!=",EN:"N"},{value:3,math:">",EN:"GT"},{value:4,math:">=",EN:"GTE"},{value:5,math:"<",EN:"LT"},{value:6,math:"<=",EN:"LTE"},{value:7,math:$t("icm.crm.membermanage_LIKE-240919"),EN:"LIKE"},{value:8,math:$t("icm.crm.membermanage_NLIKE-240919"),EN:"NLIKE"},{value:9,math:$t("icm.crm.membermanage_IS-240919"),EN:"IS"},{value:10,math:$t("icm.crm.membermanage_ISN-240919"),EN:"ISN"},{value:11,math:$t("icm.crm.membermanage_STARTWITH-240919"),EN:"STARTWITH"},{value:12,math:$t("icm.crm.membermanage_ENDWITH-240919"),EN:"ENDWITH"},{value:13,math:$t("icm.crm.membermanage_IN-240919"),EN:"IN"},{value:14,math:$t("icm.crm.membermanage_NIN-240919"),EN:"NIN"}],filterType:["group","image","file_attachment","email","url","auto_number","formula","department","master_detail","count","signature","quote","component",""],filterApiname:["account_id","bill_money_to_confirm","commision_info","confirmed_delivery_date","confirmed_receive_date","delivered_amount_sum","delivery_comment","delivery_date","discount","invoice_amount","is_user_define_work_flow","lock_status","logistics_status","name","new_opportunity_id","opportunity_id","order_status","order_time","out_resources","owner","owner_department","partner_id","payment_amount","payment_money_to_confirm","plan_payment_amount","price_book_id","product_amount","promotion_id","quote_id","receipt_type","receivable_amount","record_type","refund_amount","relevant_team","remark","resource","returned_goods_amount","SalesOrderProductObj","settle_type","shipping_warehouse_id","ship_to_add","ship_to_id","ship_to_tel","signature_attachment","submit_time","team_member","_id","created_by","last_modified_by","package","tenant_id","object_describe_id","object_describe_api_name","version","create_time","last_modified_time","is_deleted","out_tenant_id","out_owner","data_own_department","extend_obj_data_id"]});
define("crm-setting/membermanage/components/crmfilters/crmfilters-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="member-rule-crmfilters"> <div class="filter-box"> <!-- v-for --> <div v-for="(item, index) in conditions" :key="item.uuid" class="filter-group" > <div class="filter-left"> <div class="filter-des"> <i class="ivu-icon ivu-icon-ios-minus-outline group-icon-del " @click.stop="delFilterGroup(index)" ></i> <div class="filter-symbol">' + ((__t = $t("且(AND)")) == null ? "" : __t) + '</div> </div> </div> <div class="filter-con-wrap"><div class="filter-cons" :ref="`fieldfilter${item.uuid}`"></div></div> <div v-if="item.err" class="ivu-form-item-error-tip"> ' + ((__t = $t("请完善以上触发条件")) == null ? "" : __t) + ' </div> <div v-if="(index+1) < orMax" class="sep-line"> <span class="sep-line-text"> ' + ((__t = $t("或(OR)")) == null ? "" : __t) + ' </span> </div> </div> </div> <div v-if="conditions.length < orMax" class="filter-add-btn" @click.stop="addFilterGroup" > + ' + ((__t = $t("添加触发条件")) == null ? "" : __t) + " </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/membermanage/components/crmfilters/crmfilters",["./config","crm-modules/common/fieldfilter/fieldfilter"],function(i,t,e){var o=i("./config"),n=i("crm-modules/common/fieldfilter/fieldfilter");e.exports=window.Vue.extend({template:'<div class="member-rule-crmfilters"><div class="filter-box"><div v-for="(item, index) in conditions" :key="item.uuid" class="filter-group" ><div class="filter-left"><div class="filter-des"><i class="ivu-icon ivu-icon-ios-minus-outline group-icon-del " @click.stop="delFilterGroup(index)" ></i><div class="filter-symbol">{{$t("且(AND)")}}</div></div></div><div class="filter-con-wrap"><div class="filter-cons" :ref="`fieldfilter${item.uuid}`"></div></div><div v-if="item.err" class="ivu-form-item-error-tip"> {{$t("请完善以上触发条件")}} </div><div v-if="(index+1) < orMax" class="sep-line"><span class="sep-line-text"> {{$t("或(OR)")}} </span></div></div></div><div v-if="conditions.length < orMax" class="filter-add-btn" @click.stop="addFilterGroup" > + {{$t("添加触发条件")}} </div></div>',components:{},props:{apiname:{type:String,default:function(){return"SalesOrderObj"}},iconditions:{type:Array,default:function(){return[]}}},data:function(){return{orMax:5,conditions:[{uuid:0,dataList:[]}]}},methods:{initFilterGroupData:function(i){return{uuid:FS.util.uuid(),dataList:i.dataList||[],err:0}},initData:function(){var t=this,e=[],i=_.clone(t.iconditions);i&&0!=i.length||(i=[{dataList:[]}]),_.each(i,function(i){return e.push(t.initFilterGroupData(i))}),t.conditions=e},initFiledFilter:function(){var t=this;_.each(t.conditions,function(i){t.$$[i.uuid]||(t.$$[i.uuid]=new n({apiname:t.apiname||"SalesOrderObj",$wrapper:$(t.$refs["fieldfilter"+i.uuid]),width:640,title:"",max:10,filterType:o.filterType,filterApiname:o.filterApiname,defaultValue:_.map(i.dataList,function(i){return[i.field_name,i.operator,i.field_values]})}),t.$$[i.uuid].on("add.item",function(){t.$emit("fieldfilter-change","additem")}),t.$$[i.uuid].on("del.item",function(){t.$emit("fieldfilter-change","delitem")}))})},delFilterGroup:function(i){var t=this,e=this.conditions[i],i=(t.$$[e.uuid]&&t.$$[e.uuid].destroy(),_.filter(this.conditions,function(i){return i.uuid!=e.uuid}));t.$set(t,"conditions",i),t.$nextTick(function(){t.$emit("fieldfilter-change","delgroup")})},addFilterGroup:function(){var i=this,t=this.conditions;t.push(i.initFilterGroupData({})),i.$set(i,"conditions",t),i.$nextTick(function(){i.initFiledFilter(),i.$emit("fieldfilter-change","addgroup")})},valid:function(){var t=this,e=!0;return _.each(t.conditions,function(i){t.$$[i.uuid]&&t.$$[i.uuid].isExitNull()&&(i.err=1,e=!1)}),e},getConditions:function(){var e,n,r,t=this,d={};return _.map(t.conditions,function(i){return e=t.$$[i.uuid].getPreviewData(),i.dataList=_.map(t.$$[i.uuid].getData(),function(i,t){return n=e[t],r=n[2]?"“"+n[2]+"”":"",d={field_name:i[0],field_values:_.isArray(i[2])?i[2]:[i[2]],field_type:i[3],operator:i[1],desc:""+n[0]+n[1]+r},n=_.findWhere(o.COMPARE,{EN:d.operator}),d.comparison=n?n.value:"",d}),{dataList:i.dataList}})}},created:function(){this.initData()},mounted:function(){this.$$={},this.initFiledFilter()},beforeDestroy:function(){var i,t=this;for(i in t.$$)t.$$.hasOwnProperty(i)&&(t.$$[i]&&t.$$[i].destroy&&t.$$[i].destroy(),delete t.$$[i]);t.$$={}}})});
define("crm-setting/membermanage/components/radio/radio-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="mn-radio-box"> <div class="mn-radio-list" v-for="(item,index) in lists" :key="index" @click="_onToggle(index)"> <span :class="\'mn-radio-item\' + (selectValue == item.value ? \' mn-selected\':\'\')"></span> <span class="label" style="margin-left:5px; cursor:pointer;">' + ((__t = item.label) == null ? "" : __t) + ' <a class="rule-question" target="_blank" :title="item.desc"></a> </span> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/membermanage/components/radio/radio",[],function(e,t,i){i.exports=window.Vue.extend({template:'<div class="mn-radio-box"><div class="mn-radio-list" v-for="(item,index) in lists" :key="index" @click="_onToggle(index)"><span :class="\'mn-radio-item\' + (selectValue == item.value ? \' mn-selected\':\'\')"></span><span class="label" style="margin-left:5px; cursor:pointer;">{{item.label}} <a class="rule-question" target="_blank" :title="item.desc"></a></span></div></div>',data:function(){return{selectValue:1,lists:[]}},methods:{_onToggle:function(e){this.selectValue=this.lists[e].value,this.$emit("change",this.lists[e])}}})});
define("crm-setting/membermanage/components/ruledialog/ruledialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="member-rule-dialog"> <div class="member-rule-dialog-item tag-box"> <label><em>*</em>' + ((__t = $t("规则类型")) == null ? "" : __t) + '</label> <div class="member-rule-radio"></div> </div> <div class="member-rule-dialog-item tag-box"> <label><em>*</em>' + ((__t = $t("规则名称")) == null ? "" : __t) + '</label> <input type="text" class="b-g-ipt tag-name member-rule-name" maxlength="20" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" /> </div> <div class="member-rule-dialog-item"> <label><em>*</em>' + ((__t = $t("触发操作")) == null ? "" : __t) + '</label> <div class="member-rule-item-list"></div> </div> <div class="member-rule-dialog-item member-rule-integral-wrap" style="display:none"> <label><em>*</em>' + ((__t = $t("积分字段")) == null ? "" : __t) + '</label> <div class="member-rule-integral"></div> </div> <div class="member-rule-dialog-item" id="ruleAction"> <label><em>*</em>' + ((__t = $t("触发条件")) == null ? "" : __t) + '</label> <div class="member-rule-action-wrap"> <div class="member-rule-action"></div> <!-- <div class="member-rule-action-mask"></div> --> </div> </div> <div class="member-rule-dialog-item tag-box"> <label><em>*</em>' + ((__t = $t("积分")) == null ? "" : __t) + '</label> <input type="number" class="b-g-ipt member-integral-name" placeholder="' + ((__t = $t("请输入")) == null ? "" : __t) + '" /> <span class="member-rule-unit">' + ((__t = $t("积分/次")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/membermanage/components/ruledialog/ruledialog",["crm-widget/select/select","base-modules/calendar/datepicker2","crm-widget/dialog/dialog","../crmfilters/crmfilters","../radio/radio","crm-modules/common/util","./ruledialog-html"],function(e,t,i){var n=e("crm-widget/select/select"),r=e("base-modules/calendar/datepicker2"),a=e("crm-widget/dialog/dialog"),l=e("../crmfilters/crmfilters"),s=e("../radio/radio"),o=e("crm-modules/common/util"),e=e("./ruledialog-html"),u=Backbone.Model.extend({defaults:{status:"add",apiName:"",type:2,action:"",rules:[],aggregateFieldLists:[{name:$t("请选择"),value:"",type:"apiName"},{name:$t("销售订单"),value:"SalesOrderObj",type:"apiName",children:[{name:$t("新建"),value:"INSERT",type:"action"}]}],actionFieldLists:[{name:$t("请选择"),value:"",type:"action"}]}}),d=a.extend({attrs:{width:640,className:"crm-member-rule-dialog",title:$t("新建积分规则"),showScroll:!1,showBtns:!0,content:e()},defaultData:{},events:{"click .b-g-btn-cancel":"destroy","click .b-g-btn":"onSubmit","input .member-rule-name":"onInputChange","input .member-integral-name":"onInputChange"},initialize:function(){d.superclass.initialize.call(this),this.widget={},this.model=new u;var e=this;this.model.on("change",function(){e.receiveProps(this._previousAttributes,this.attributes)})},render:function(){d.superclass.render.call(this),this.$ruleActionWrap=$("#ruleAction"),this.$ruleRadioWrap=$(".member-rule-radio"),this.$ruleSelectWrap=$(".member-rule-item-list"),this.$ruleDatePickerWrap=$(".member-rule-datepicker"),this.$ruleName=$(".member-rule-name"),this.$integralName=$(".member-integral-name"),this.$unit=$(".member-rule-unit"),this.$rukeIntegral=$(".member-rule-integral"),this.$rukeIntegralWrap=$(".member-rule-integral-wrap"),this._renderFormItems(),this._createDefaultIntegralSelect()},receiveProps:function(e,t){t.apiName!==e.apiName&&this._createSelect(),""!=t.action&&""!=t.apiName&&o.hideErrmsg(this.$ruleSelectWrap),t.type!=e.type&&(this._createRadio(),this._createSelect("reset"))},formatInput:function(e){var e=e.split("."),t=e[0].slice(0,14),e=void 0!==e[1]?e[1].slice(0,2):"";return e?t+"."+e:t},onInputChange:function(e){e=$(e.target);"number"===e.attr("type")&&e.val(this.formatInput(e.val())),o.hideErrmsg(e)},formatRuleForParse:function(e,t){return e=e.replace(/\(|\)/g,""),_.map(e.split("or"),function(e){return{dataList:_.map(e.split("and"),function(e){e-=1;return{field_name:t[e].fieldName,field_type:t[e].fieldType,operator:t[e].operate,field_values:t[e].fieldValue}})}})},edit:function(e){var t=e.integralDataVo.integralTriggerType,i=e.integralDataVo.operateType,r=e.integralRuleRuleGroupVo.entityId,a=e.integralRuleRuleGroupVo.rules,l=e.integralRuleRuleGroupVo.ruleParse;return this.show(),this.$ruleName.val(e.integralRuleRuleGroupVo.ruleName),this.$integralName.val(e.integralDataVo.integral),this.model.set({type:t,action:i,apiName:r,status:"edit",rules:this.formatRuleForParse(l,a)||[]}),this._createRadio(),this._createDefaultIntegralSelect(),this},_createRadio:function(){var i=this,t=this.model.get("type"),e=[{label:$t("非聚合类积分"),value:2,desc:$t("只对一个对象一条数据-16be3b47")},{label:$t("聚合类积分"),value:1,desc:$t("对一个对象下字段积分-e45b7850")}];"edit"==this.model.get("status")&&(e=_.filter(e,function(e){return e.value==t})),this.widget.radio||(this.widget.radio=(new s).$mount(this.$ruleRadioWrap[0]),this.widget.radio.$on("change",function(e){i.model.set("type",e.value);var t=2===e.value&&i.model.get("apiName")&&"edit"!=i.model.get("status");t?(i._createCrmfilters(i.model.get("apiName")),i._toggleCrmfilters(t)):"edit"!=i.model.get("status")&&i._toggleCrmfilters(t),1==e.value?i.$rukeIntegralWrap.show():i.$rukeIntegralWrap.hide()})),this.widget.radio.selectValue=t,this.widget.radio.lists=e,2==t?this.$unit.html($t("积分/次")):this.$unit.html($t("积分/元"))},_createDefaultIntegralSelect:function(){this.widget.integralSelect||(this.widget.integralSelect=new n({$wrap:this.$rukeIntegral,width:145,zIndex:2e3,defaultVal:"order_amount",options:[{name:$t("订单金额"),value:"order_amount",type:"price"}]})),1==this.model.get("type")&&this.$rukeIntegralWrap.show()},_createCrmfilters:function(e){var t;this.widget.crmfilters||(t=this.model.get("rules"),this.widget.crmfilters=new l({propsData:{apiname:e,iconditions:t}}).$mount(".member-rule-action"))},_resetSelectData:function(e){this.widget.actionSelect.resetOptions(e);e=e[0]?e[0].value:"";this.widget.actionSelect.setValue(e),this.model.set("action",e)},_onSelectChange:function(e,t){this.model.set("apiName",e),e&&2==this.model.get("type")?(this._createCrmfilters(e),this._toggleCrmfilters(!0)):this._toggleCrmfilters(!1),t.children&&t.children.length?this._resetSelectData(t.children):this._resetSelectData(this.model.get("actionFieldLists"))},filterActionLists:function(e,t){return(_.find(e,function(e){return e.value==t})||{}).children||[]},_createSelect:function(e){var t=this.model.get("status"),i=this.model.get("apiName"),r=this.model.get("action"),a=this.model.get("type"),l=this.model.get("aggregateFieldLists"),s=this.model.get("actionFieldLists"),e=("edit"!==t&&"reset"===e&&this.model.set({action:"",apiName:""}),{$wrap:this.$ruleSelectWrap,width:145,zIndex:2e3});this.widget.apiNameSelect&&(this.widget.apiNameSelect.destroy(),this.widget.apiNameSelect=null),this.widget.apiNameSelect=new n(_.extend(e,{defaultVal:i,disabled:"edit"==t,options:l})),i&&2==a&&(s=this.filterActionLists(l,i),this._createCrmfilters(i),this._toggleCrmfilters(!0)),r&&1==a&&(s=this.filterActionLists(l,i)),this.widget.actionSelect&&(this.widget.actionSelect.destroy(),this.widget.actionSelect=null),this.widget.actionSelect=new n(_.extend(e,{defaultVal:r,disabled:"edit"==t,options:s})),this.widget.apiNameSelect.on("change",this._onSelectChange.bind(this)),this.widget.actionSelect.on("change",function(e){this.model.set("action",e)}.bind(this))},_createDateSelect:function(){this.dateSelect=new r({element:this.$ruleDatePickerWrap,placeholder:$t("请选择时间"),formatStr:"YYYY-MM-DD",zIndex:2e3}),this.dateSelect.on("change",function(e,t){})},_renderFormItems:function(){this._createRadio(),this._createSelect(),this._createDateSelect()},_toggleCrmfilters:function(e){e?this.$ruleActionWrap.show():this.$ruleActionWrap.hide(),this.resetPosition()},createRuleWithParse:function(e){var a="",l=1,s=e.length;return{rules:_.reduce(e,function(e,t,i){var r;return _.isArray(t.dataList)&&(r=[],e=e.concat(_.map(t.dataList,function(e){return r.push(l),{fieldName:e.field_name,fieldType:e.field_type,operate:e.operator,fieldValue:e.field_values,ruleOrder:l++}})),a=a+("("+r.join(" and "))+")"+(i!==s-1?" or ":"")),e},[]),ruleParse:a}},onSubmit:function(){var e,t=this.$integralName.val(),i=this.$ruleName.val(),r=this.model.get("apiName"),a=this.model.get("action"),l=this.model.get("type");i?r&&a?2!=l||this.widget.crmfilters.valid()?t?0==t?o.showErrmsg(this.$integralName,$t("积分数量不能为0")):(e={},r={ruleGroupArg:{entityId:r,ruleName:i,ruleParse:(e=this.widget.crmfilters?this.createRuleWithParse(this.widget.crmfilters.getConditions()):e).ruleParse||"",status:1,rules:e.rules||[]},integralDataArg:{integral:t,operateType:a,integralTriggerType:l}},1==l&&(r.ruleGroupArg.ruleParse="(1)",r.ruleGroupArg.rules=[{fieldName:"order_amount",fieldType:"currency",operate:"GT",fieldValue:["0"],ruleOrder:1}]),this.trigger("onSubmit",r)):o.showErrmsg(this.$integralName,$t("请输入积分")):o.showErrmsg($(".member-rule-crmfilters"),$t("请完善触发条件")):o.showErrmsg(this.$ruleSelectWrap,$t("请选择触发操作")):o.showErrmsg(this.$ruleName,$t("请输入规则名称"))},hide:function(){this.destroy()},destroy:function(){return _.each(this.widget,function(e){e&&e.destroy&&e.destroy(),e&&e.$destroy&&e.$destroy()}),this.widget=null,d.superclass.destroy.call(this)}});i.exports=d});
define("crm-setting/membermanage/credit/credit-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="m-member-manage-credit"> <div class="sc-wrap"> <div class="fd-title">' + ((__t = $t("积分有效期")) == null ? "" : __t) + ':</div> <div class="fd-wrap" style="max-width: 400px;" v-show="expireTimeState === \'view\'"> <span v-show="orgExpireTime.type === 1">' + ((__t = $t("永久有效")) == null ? "" : __t) + '</span> <span v-show="orgExpireTime.type === 2">' + ((__t = $t("逐笔过期从发放之日起")) == null ? "" : __t) + "" + ((__t = orgExpireTime.expireTime) == null ? "" : __t) + "" + ((__t = $t("年后过期")) == null ? "" : __t) + '</span> </div> <a class="a-btn a-btn-setup" href="javascript:;" v-show="expireTimeState === \'view.hide\'" @click="handleEdit(\'edit\')">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> <div class="fd-wrap" v-show="expireTimeState === \'edit\'"> <div class="mn-radio-box"> <div class="radio-wrap"> <span :class="{\'mn-radio-item\': 1, \'mn-selected\': integralExpireTime.type === 1}" @click="handleRadio(1)"></span> <span class="radio-label" @click="handleRadio(1)">' + ((__t = $t("永久有效")) == null ? "" : __t) + '</span> </div> <div class="radio-wrap"> <span :class="{\'mn-radio-item\': 1, \'mn-selected\': integralExpireTime.type === 2}" @click="handleRadio(2)"></span> <span> <span class="radio-label" @click="handleRadio(2)">' + ((__t = $t("逐笔过期从发放之日起")) == null ? "" : __t) + '</span> <input class="b-g-ipt" style="width: 60px; margin: 4px;" maxlength="6" ref="inputExpireTime" v-model="integralExpireTime.expireTime" @focus="handleinputfocus" @blur="handleinputblur" > ' + ((__t = $t("年后过期")) == null ? "" : __t) + ' </span> </div> </div> </div> </div> <div class="sc-wrap" v-show="expireTimeState === \'edit\'"> <span class="b-g-btn" @click="handleSubmit">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span class="crm-btn" style="margin-left: 10px;" @click="handleEdit(\'view\')">' + ((__t = $t("取消")) == null ? "" : __t) + '</span> </div> <div class="sc-wrap"> <div class="h3-bar"> <div class="title">' + ((__t = $t("积分规则：")) == null ? "" : __t) + '</div> <span class="b-g-btn" @click="handleOps(\'create\')">' + ((__t = $t("新建积分规则")) == null ? "" : __t) + '</span> </div> </div> <div class="sc-table"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/membermanage/credit/credit",["base-modules/utils","../../../common/util","crm-widget/table/table","../service/api","../components/radio/radio","../components/ruledialog/ruledialog"],function(e,t,i){var l=e("base-modules/utils"),r=e("../../../common/util"),n=e("crm-widget/table/table"),d=e("../service/api"),a=e("../components/radio/radio"),s=e("../components/ruledialog/ruledialog");var o=[{data:"ruleName",title:$t("规则名称")},{data:"triggerName",title:$t("规则类型")},{data:"operateName",title:$t("触发操作")},{data:"integralText",title:$t("积分")},{lastFixed:!0,data:"",title:$t("操作"),className:"td-opts",width:120,render:function(){return'<a class="a-btn" data-tdop="edit">'+$t("编辑")+'</a><a class="a-btn" data-tdop="delete">'+$t("删除")+"</a>"}}],p={0:"",1:$t("聚合类积分"),2:$t("非聚合类积分")},u={0:"",1:$t("积分/元"),2:$t("积分/次")},c={"":"",INSERT:$t("销售订单，新建"),DELETE:"",UPDATE:"",INVALID:$t("销售订单，废除")};function m(e){var e=e.pageIntegralRulesVoPageResult,a=[];return _.forEach(e.result,function(e){var t={x_raw:e},i=e.integralDataVo,e=e.integralRuleRuleGroupVo;t.ruleCode=e.ruleCode,t.ruleName=e.ruleName,t.integral=i.integral,t.triggerType=i.integralTriggerType,t.operateType=i.operateType,i.integral&&u[t.triggerType]&&(t.integralText=i.integral+u[t.triggerType]),t.triggerName=p[t.triggerType]||p[0],t.operateName=c[t.operateType]||c[""],a.push(t)}),m.cacheData=a,{totalCount:e.totalCount||0,data:a}}function g(i){var a=-1;return _.forEach(m.cacheData,function(e,t){i===e.ruleName&&(a=t)}),-1+a-a}function h(e){return{entityIds:["SalesOrderObj"],pageInfo:{pageSize:e.pageSize,currentPage:e.pageNumber}}}i.exports=window.Vue.extend({template:'<div class="m-member-manage-credit"><div class="sc-wrap"><div class="fd-title">{{$t("积分有效期")}}:</div><div class="fd-wrap" style="max-width: 400px;" v-show="expireTimeState === \'view\'"><span v-show="orgExpireTime.type === 1">{{$t("永久有效")}}</span><span v-show="orgExpireTime.type === 2">{{$t("逐笔过期从发放之日起")}}{{orgExpireTime.expireTime}}{{$t("年后过期")}}</span></div><a class="a-btn a-btn-setup" href="javascript:;" v-show="expireTimeState === \'view.hide\'" @click="handleEdit(\'edit\')">{{$t("设置")}}</a><div class="fd-wrap" v-show="expireTimeState === \'edit\'"><div class="mn-radio-box"><div class="radio-wrap"><span :class="{\'mn-radio-item\': 1, \'mn-selected\': integralExpireTime.type === 1}" @click="handleRadio(1)"></span><span class="radio-label" @click="handleRadio(1)">{{$t("永久有效")}}</span></div><div class="radio-wrap"><span :class="{\'mn-radio-item\': 1, \'mn-selected\': integralExpireTime.type === 2}" @click="handleRadio(2)"></span><span><span class="radio-label" @click="handleRadio(2)">{{$t("逐笔过期从发放之日起")}}</span><input class="b-g-ipt" style="width: 60px; margin: 4px;" maxlength="6" ref="inputExpireTime" v-model="integralExpireTime.expireTime" @focus="handleinputfocus" @blur="handleinputblur" > {{$t("年后过期")}} </span></div></div></div></div><div class="sc-wrap" v-show="expireTimeState === \'edit\'"><span class="b-g-btn" @click="handleSubmit">{{$t("保存")}}</span><span class="crm-btn" style="margin-left: 10px;" @click="handleEdit(\'view\')">{{$t("取消")}}</span></div><div class="sc-wrap"><div class="h3-bar"><div class="title">{{$t("积分规则：")}}</div><span class="b-g-btn" @click="handleOps(\'create\')">{{$t("新建积分规则")}}</span></div></div><div class="sc-table"></div></div>',components:{"v-radio":a},data:function(){return{inputvalue:"1",expireTimeState:"view",orgExpireTime:{type:1,expireTime:1},integralExpireTime:{type:1,expireTime:1},table:null}},methods:{handleinputfocus:function(){r.hideErrmsg($(this.$refs.inputExpireTime))},handleinputblur:function(){var e="",t=$.trim(this.integralExpireTime.expireTime),i=+(+t).toFixed(3);return t?isFinite(t)&&.001<=i&&i<=1e4||(e=$t("只能填*之间的数值",{str:"0.001 ~ 10000"})):e=$t("请填写过期时间"),e?r.showErrmsg($(this.$refs.inputExpireTime),e):this.integralExpireTime.expireTime=i,e},handleRadio:function(e){this.integralExpireTime.type=+e},handleEdit:function(e){"view"===(this.expireTimeState=e)&&(_.extend(this.integralExpireTime,this.orgExpireTime),this.handleinputfocus())},handleSubmit:function(){var e;this.handleinputblur()||d.setIntegralExpireTime({data:(e=this).integralExpireTime}).then(function(){l.remind(1,$t("保存成功")),_.extend(e.orgExpireTime,e.integralExpireTime),e.handleEdit("view")})},getIntegralExpireTime:function(){var t=this;d.getIntegralExpireTime().then(function(e){_.extend(t.integralExpireTime,e),t.integralExpireTime.expireTime=+(+t.integralExpireTime.expireTime).toFixed(3),_.extend(t.orgExpireTime,t.integralExpireTime)})},getIniDatas:function(){},handleOps:function(e,t){"create"===e?this.handleOpCreate(t):"edit"===e?this.handleOpEdit(t):"delete"===e&&this.handleOpDelete(t)},handleOpCreate:function(){var t,i,e=this;t=function(){e.renderTable(),l.remind(1,$t("保存成功"))},(i=new s({title:$t("新建积分规则")}).show()).on("onSubmit",function(e){0<=g(e.ruleGroupArg.ruleName)?l.remind(3,$t("规则名称有重复")):(i.showLoading(),d.createIntegralRule({data:e}).then(function(){i.hide(),t&&t()}).always(function(){i.hideLoading()}))})},handleOpEdit:function(e){var t,i,a,n=this;function r(e){(e.ruleGroupArg||{}).ruleCode=i.integralRuleRuleGroupVo.ruleCode,0<=g(e.ruleGroupArg.ruleName)?l.remind(3,$t("规则名称有重复")):(a.showLoading(),d.updateIntegralRule({data:e}).then(function(e){a.hide(),t&&t()}).always(function(){a.hideLoading()}))}e=e,t=function(){n.renderTable(),l.remind(1,$t("保存成功"))},(a=(new s).show()).setTitle($t("修改积分规则")),a.showLoading(),d.getIntegralRule({data:{ruleCodes:[e.ruleCode]}}).then(function(e){i=e,a.edit(i),a.on("onSubmit",r)}).always(function(){a.hideLoading()})},handleOpDelete:function(e){var t,i,a,n=this;t=e,i=function(){n.renderTable(),l.remind(1,$t("已删除"))},a=r.confirm([$t("确定要删除该规则吗？"),$t("删除可能导致等级改变-03b9ad58")].join("<br>"),$t("提示"),function(e){d.deleteIntegralRule({data:{entityId:"SalesOrderObj",ruleCodes:[t.ruleCode]}},{submitSelector:e.target}).always(function(){a.destroy(),a=null,i&&i()})})},renderTable:function(){this.table.setParam({},!0)},initTable:function(){var a=this;a.table=new n({$el:$(".sc-table",a.$el),url:d.pageIntegralRules._config.url,requestType:"FHHApi",trHandle:!1,paramFormat:h,columns:o,formatData:m}),a.table.on("trclick",function(e,t,i){i=i.data("tdop");i&&a.handleOps(i,e)})}},mounted:function(){this.initTable(),this.getIniDatas()}})});
define("crm-setting/membermanage/growth/growth-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="m-member-manage-growth"> <div class="sc-wrap"> <div class="fd-title">' + ((__t = $t("成长值增长规则")) == null ? "" : __t) + ':</div> <div class="fd-wrap" style="max-width: 400px;"> <div>' + ((__t = $t("累计积分")) == null ? "" : __t) + '</div> <div style="line-height: 42px;"> ' + ((__t = $t("获得1积分累计")) == null ? "" : __t) + " <span v-show=\"editState === 'view'\">" + ((__t = orgGrowthValue || "--") == null ? "" : __t) + '</span> <input class="b-g-ipt" style="width: 60px; margin: 4px;" maxlength="6" ref="input" v-show="editState === \'edit\'" v-model="growthValuePerPoint" @focus="handleinputfocus" @blur="handleinputblur" > ' + ((__t = $t("成长值")) == null ? "" : __t) + ' </div> </div> <a class="a-btn a-btn-setup" href="javascript:;" v-show="editState === \'view\'" @click="handleEdit(\'edit\')">' + ((__t = $t("设置")) == null ? "" : __t) + '</a> </div> <div class="sc-wrap"> <div class="fd-title">' + ((__t = $t("成长值有效期")) == null ? "" : __t) + ':</div> <div class="fd-wrap">' + ((__t = $t("永久有效")) == null ? "" : __t) + '</div> </div> <div class="sc-wrap" v-show="editState === \'edit\'"> <span class="b-g-btn" ref="btnSubmit" @click="handleSubmit">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span class="crm-btn" style="margin-left: 10px;" @click="handleEdit(\'view\')">' + ((__t = $t("取消")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/membermanage/growth/growth",["base-modules/utils","../../../common/util","../service/api"],function(t,e,i){var a=t("base-modules/utils"),r=t("../../../common/util"),n=t("../service/api");i.exports=window.Vue.extend({template:'<div class="m-member-manage-growth"><div class="sc-wrap"><div class="fd-title">{{$t("成长值增长规则")}}:</div><div class="fd-wrap" style="max-width: 400px;"><div>{{$t("累计积分")}}</div><div style="line-height: 42px;"> {{$t("获得1积分累计")}} <span v-show="editState === \'view\'">{{orgGrowthValue || \'--\'}}</span><input class="b-g-ipt" style="width: 60px; margin: 4px;" maxlength="6" ref="input" v-show="editState === \'edit\'" v-model="growthValuePerPoint" @focus="handleinputfocus" @blur="handleinputblur" > {{$t("成长值")}} </div></div><a class="a-btn a-btn-setup" href="javascript:;" v-show="editState === \'view\'" @click="handleEdit(\'edit\')">{{$t("设置")}}</a></div><div class="sc-wrap"><div class="fd-title">{{$t("成长值有效期")}}:</div><div class="fd-wrap">{{$t("永久有效")}}</div></div><div class="sc-wrap" v-show="editState === \'edit\'"><span class="b-g-btn" ref="btnSubmit" @click="handleSubmit">{{$t("保存")}}</span><span class="crm-btn" style="margin-left: 10px;" @click="handleEdit(\'view\')">{{$t("取消")}}</span></div></div>',data:function(){return{editState:"view",orgGrowthValue:0,growthValueruleType:0,growthValuePerPoint:1}},methods:{handleinputfocus:function(){r.hideErrmsg($(this.$refs.input))},handleinputblur:function(){var t="",e=$.trim(this.growthValuePerPoint),i=+(+e).toFixed(2);return e?isFinite(e)&&.01<=i&&i<=1e4||(t=$t("只能填*之间的数值",{str:"0.01 ~ 10000"})):t=$t("请填写成长值"),t?r.showErrmsg($(this.$refs.input),t):this.growthValuePerPoint=i,t},handleEdit:function(t){"view"===(this.editState=t)&&(this.growthValuePerPoint=this.orgGrowthValue||1,this.handleinputfocus())},handleSubmit:function(){var t;this.handleinputblur()||n.updateGrowthValue({data:{growthValueruleType:0,growthValuePerPoint:+(t=this).growthValuePerPoint}},{submitSelector:t.$refs.btnSubmit}).then(function(){a.remind(1,$t("保存成功")),t.orgGrowthValue=t.growthValuePerPoint,t.handleEdit("view")})}},mounted:function(){var e=this;n.getGrowthValue().then(function(t){e.growthValueruleType=t.growthValueruleType||0,e.growthValuePerPoint=t.growthValuePerPoint||1,e.growthValuePerPoint=+(+e.growthValuePerPoint).toFixed(2),e.orgGrowthValue=e.growthValuePerPoint})}})});
define("crm-setting/membermanage/home/<USER>", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="m-member-manage" v-if="!isLoading"> <div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("会员管理")) == null ? "" : __t) + '</span></h2> </div> <div class="unauthed-company" v-if="!isGreyCompany"> <div class="unauthed-company-tips">' + ((__t = $t("该模块未开通如有需要-61a79ded")) == null ? "" : __t) + '</div> </div> <div class="crm-module-con member-manage-colflex" v-else-if="isActivate"> <div class="crm-tab" style="min-width: 500px;"> <div v-for="menu in menus" :key="menu.id" :class="[\'item\', activeMenu === menu.id && \'cur\']" @click="handleSwitchMenu(menu)">' + ((__t = menu.name) == null ? "" : __t) + '</div> </div> <div class="content-wrapper crm-scroll"> <!-- 会员等级设置 --> <level-setting v-if="activeMenu === menus[0].id"></level-setting> <!-- 会员积分设置 --> <credit-setting v-if="activeMenu === menus[1].id"></credit-setting> <!-- 会员成长值设置 --> <growth-setting v-if="activeMenu === menus[2].id"></growth-setting> </div> </div> <div class="unstart-member-manage" v-else> <div class="active-tips">' + ((__t = $t("启用会员管理功能帮助-64fb1500")) == null ? "" : __t) + '</div> <div> <div class="crm-btn crm-btn-primary j-start-manage" @click="handleInitialMemberManage">' + ((__t = $t("启用会员管理")) == null ? "" : __t) + '</div> </div> </div> </div> </div> <div class="m-member-manage loading" v-else>' + ((__t = $t("加载中")) == null ? "" : __t) + "...</div>";
        }
        return __p;
    };
});
define("crm-setting/membermanage/home/<USER>",["../service/api","base-modules/utils","../level/level","../credit/credit","../growth/growth"],function(e,i,t){var n=e("../service/api"),a=e("base-modules/utils"),s=e("../level/level"),c=e("../credit/credit"),e=e("../growth/growth");t.exports=window.Vue.extend({template:'<div class="m-member-manage" v-if="!isLoading"><div class="crm-tit"><h2><span class="tit-txt">{{$t("会员管理")}}</span></h2></div><div class="unauthed-company" v-if="!isGreyCompany"><div class="unauthed-company-tips">{{$t("该模块未开通如有需要-61a79ded")}}</div></div><div class="crm-module-con member-manage-colflex" v-else-if="isActivate"><div class="crm-tab" style="min-width: 500px;"><div v-for="menu in menus" :key="menu.id" :class="[\'item\', activeMenu === menu.id && \'cur\']" @click="handleSwitchMenu(menu)">{{menu.name}}</div></div><div class="content-wrapper crm-scroll"><level-setting v-if="activeMenu === menus[0].id"></level-setting><credit-setting v-if="activeMenu === menus[1].id"></credit-setting><growth-setting v-if="activeMenu === menus[2].id"></growth-setting></div></div><div class="unstart-member-manage" v-else><div class="active-tips">{{$t("启用会员管理功能帮助-64fb1500")}}</div><div><div class="crm-btn crm-btn-primary j-start-manage" @click="handleInitialMemberManage">{{$t("启用会员管理")}}</div></div></div></div></div><div class="m-member-manage loading" v-else>{{$t("加载中")}}...</div>',data:function(){return{menus:[{id:1,name:$t("会员等级设置")},{id:2,name:$t("会员积分设置")},{id:3,name:$t("会员成长值设置")}],activeMenu:1,isLoading:!0,isActivate:!0,isGreyCompany:!1}},created:function(){var e={level:1,integral:2,growth:3},i=FS.util.getQueryStringArgs()||{};i.tab&&e[i.tab]&&(this.activeMenu=e[i.tab])},mounted:function(){var i=this;n.enableMember().then(function(e){7!==e.enableStatus&&(i.isGreyCompany=!0,i.isActivate=1===e.enableStatus)}).always(function(){i.isLoading=!1})},methods:{handleSwitchMenu:function(e){this.activeMenu=e.id},handleInitialMemberManage:function(){var i=this;n.initializeMember({},{submitSelector:$(".j-start-manage",this.$el)}).then(function(e){i.isActivate=2===e.enableStatus},function(){a.remind(3,$t("初始化失败")+"!")})}},components:{"level-setting":s,"credit-setting":c,"growth-setting":e}})});
define("crm-setting/membermanage/level/level-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="m-member-manage-level"> <div class="member-level-header"> <div class="member-level-tips">' + ((__t = $t("会员等级")) == null ? "" : __t) + "<span>" + ((__t = $t("（数字越大等级越高-1ec5dbcf")) == null ? "" : __t) + '</span></div> <div class="member-level-add b-g-btn" @click="handleAddLevel">+' + ((__t = $t("添加等级")) == null ? "" : __t) + '</div> </div> <table class="member-level-table crm-table crm-table-lg crm-table-nowrap crm-table-noborder"> <thead> <tr> <th><div class="tb-cell">' + ((__t = $t("等级")) == null ? "" : __t) + '</div></th> <th><div class="tb-cell">' + ((__t = $t("等级名称")) == null ? "" : __t) + '</div></th> <th><div class="tb-cell">' + ((__t = $t("升级条件")) == null ? "" : __t) + '</div></th> <th><div class="tb-cell">' + ((__t = $t("会员权益")) == null ? "" : __t) + '</div></th> <th><div class="tb-cell">' + ((__t = $t("操作")) == null ? "" : __t) + '</div></th> </tr> </thead> <tbody v-if="isStopLoading"> <t-row v-for="item in list" :data="item" :list="list" :key="item.id" :stopLoading="isUpdateDataDone" @delete="handleDeleteRow" @update="handleUpdateRow"></t-row> </tbody> </table> <div class="member-level-loading crm-loading" v-if="!isStopLoading"></div> <div class="member-level-empty" v-if="isStopLoading && !list.length">' + ((__t = $t("没有数据")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/membermanage/level/level",["./table-row/row","../service/api","base-modules/utils"],function(e,t,i){var a=e("./table-row/row"),s=e("../service/api"),d=e("base-modules/utils");i.exports=window.Vue.extend({template:'<div class="m-member-manage-level"><div class="member-level-header"><div class="member-level-tips">{{$t("会员等级")}}<span>{{$t("（数字越大等级越高-1ec5dbcf")}}</span></div><div class="member-level-add b-g-btn" @click="handleAddLevel">+{{$t("添加等级")}}</div></div><table class="member-level-table crm-table crm-table-lg crm-table-nowrap crm-table-noborder"><thead><tr><th><div class="tb-cell">{{$t("等级")}}</div></th><th><div class="tb-cell">{{$t("等级名称")}}</div></th><th><div class="tb-cell">{{$t("升级条件")}}</div></th><th><div class="tb-cell">{{$t("会员权益")}}</div></th><th><div class="tb-cell">{{$t("操作")}}</div></th></tr></thead><tbody v-if="isStopLoading"><t-row v-for="item in list" :data="item" :list="list" :key="item.id" :stopLoading="isUpdateDataDone" @delete="handleDeleteRow" @update="handleUpdateRow"></t-row></tbody></table><div class="member-level-loading crm-loading" v-if="!isStopLoading"></div><div class="member-level-empty" v-if="isStopLoading && !list.length">{{$t("没有数据")}}</div></div>',data:function(){return{list:[],isStopLoading:!0,isUpdateDataDone:!0}},mounted:function(){var t=this;this.isStopLoading=!1,s.listGrade().then(function(e){t.list=t.parse(e.grades)}).always(function(){t.isStopLoading=!0})},methods:{handleUpdateRow:function(i){var a=-1,e=_.clone(this.list);i.gradeId?_.each(e,function(e,t){e.gradeId===i.gradeId&&(a=t)}):a=e.length-1,-1!==a&&e.splice(a,1,i),this.update(e)},handleDeleteRow:function(t){if(!t.gradeId)return this.list.pop();this.update(_.filter(this.list,function(e){return!!e.gradeId&&e.gradeId!==t.gradeId}),_.omit(t,["gradeEquitiesList","equitiesNames"]))},handleAddLevel:function(){var e;this.isStopLoading&&!_.some(this.list,function(e){return!e.gradeId})&&(e=_.sortBy(this.list,["gradeNo"]).pop()||{},this.list.push({gradeNo:(e.gradeNo||0)+1}))},parse:function(e){e=_.sortBy(e,["gradeNo"]);return _.map(e,function(e){var t=[],i=[];return _.each(e.gradeEquitiesList,function(e){_.isEmpty(e.equities)||("string"==typeof e.equities.id&&t.push(e.equities.id),i.push(e.equities.equities))}),_.extend(e,{equitiesIds:t,equitiesNames:i})})},update:function(e,t){var i=this,e=(i.isUpdateDataDone=!1,_.filter(e,function(e){return e.gradeName&&e.growthValueThreshold})),e={grades:_.map(e,function(e){return _.omit(e,["gradeEquitiesList","equitiesNames"])})};s.saveAllGrade({data:t?_.extend(e,{toDeleteGrades:[t]}):e}).then(function(e){i.list=i.parse(e.grades),d.remind(1,t?$t("删除成功"):$t("保存成功"))},function(){d.remind(3,t?$t("删除失败"):$t("保存失败"))}).always(function(){i.isUpdateDataDone=!0})}},components:{"t-row":a}})});
define("crm-setting/membermanage/level/table-row/row-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<!-- 显示 --> <tr v-if="!isEditMode"> <td><div class="tb-cell ex-tb-cell">' + ((__t = data.gradeNo) == null ? "" : __t) + '</div></td> <td><div class="tb-cell ex-tb-cell">' + ((__t = data.gradeName) == null ? "" : __t) + '</div></td> <td><div class="tb-cell ex-tb-cell">' + ((__t = $t("成长值")) == null ? "" : __t) + "" + ((__t = data.growthValueThreshold) == null ? "" : __t) + '</div></td> <td><div class="tb-cell ex-tb-cell">' + ((__t = data.equitiesNames && data.equitiesNames.join("，")) == null ? "" : __t) + '</div></td> <td> <div class="tb-cell ex-tb-cell"> <span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleModifyOperation">' + ((__t = $t("修改")) == null ? "" : __t) + '</span> <span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleDeleteOperation(true)" style="margin-left:10px;"> <span v-if="stopLoading || !isLoading">' + ((__t = $t("删除")) == null ? "" : __t) + '</span> <span v-else class="crm-action-icon-requesting"></span> </span> </div> </td> </tr> <!-- 新增、编辑 --> <tr class="table-row-modify" v-else> <td> <div class="tb-cell ex-tb-cell">' + ((__t = temp.gradeNo) == null ? "" : __t) + '</div> </td> <td> <div class="tb-cell ex-tb-cell"> <div> <input v-model="temp.gradeName" ref="name-input" class="b-g-ipt tb-input ex-tb-input" :placeholder="$t(\'请输入等级名称\')"> </div> </div> </td> <td> <div class="tb-cell ex-tb-cell"> <div> <label>' + ((__t = $t("成长值达")) == null ? "" : __t) + '</label> <input v-model="temp.growthValueThreshold" ref="growth-input" class="b-g-ipt tb-input ex-tb-input" style="margin-left:20px;" type="number" max="99999999999999" min="-99999999999999" :placeholder="$t(\'请输入\')"> </div> </div> </td> <td> <div class="tb-cell ex-tb-cell"> <div class="b-g-ipt tb-input ex-tb-input ex-tb-selector" ref="equities-input"> <div class="tag-wrapper"> <span class="tag-item" v-for="item in temp.equitiesNames">' + ((__t = item) == null ? "" : __t) + '</span> </div> <span class="btn-text tag-button" @click="handleAddEquities">+' + ((__t = $t("添加权益")) == null ? "" : __t) + '</span> </div> </div> </td> <td> <div class="tb-cell ex-tb-cell operation-edit-state"> <span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleSaveOperation"> <span v-if="stopLoading || !isLoading">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <span v-else class="crm-action-icon-requesting"></span> </span> <span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleCancelOperation" style="margin-left:10px;">' + ((__t = $t("取消")) == null ? "" : __t) + "</span> </div> </td> </tr>";
        }
        return __p;
    };
});
define("crm-setting/membermanage/level/table-row/row",["crm-modules/common/util","base-modules/utils"],function(t,e,i){var s=t("crm-modules/common/util"),a=t("base-modules/utils");i.exports=window.Vue.extend({template:'<tr v-if="!isEditMode"><td><div class="tb-cell ex-tb-cell">{{data.gradeNo}}</div></td><td><div class="tb-cell ex-tb-cell">{{data.gradeName}}</div></td><td><div class="tb-cell ex-tb-cell">{{$t("成长值")}}{{data.growthValueThreshold}}</div></td><td><div class="tb-cell ex-tb-cell">{{data.equitiesNames && data.equitiesNames.join(\'，\')}}</div></td><td><div class="tb-cell ex-tb-cell"><span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleModifyOperation">{{$t("修改")}}</span><span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleDeleteOperation(true)" style="margin-left:10px;"><span v-if="stopLoading || !isLoading">{{$t("删除")}}</span><span v-else class="crm-action-icon-requesting"></span></span></div></td></tr><tr class="table-row-modify" v-else><td><div class="tb-cell ex-tb-cell">{{temp.gradeNo}}</div></td><td><div class="tb-cell ex-tb-cell"><div><input v-model="temp.gradeName" ref="name-input" class="b-g-ipt tb-input ex-tb-input" :placeholder="$t(\'请输入等级名称\')"></div></div></td><td><div class="tb-cell ex-tb-cell"><div><label>{{$t("成长值达")}}</label><input v-model="temp.growthValueThreshold" ref="growth-input" class="b-g-ipt tb-input ex-tb-input" style="margin-left:20px;" type="number" max="99999999999999" min="-99999999999999" :placeholder="$t(\'请输入\')"></div></div></td><td><div class="tb-cell ex-tb-cell"><div class="b-g-ipt tb-input ex-tb-input ex-tb-selector" ref="equities-input"><div class="tag-wrapper"><span class="tag-item" v-for="item in temp.equitiesNames">{{item}}</span></div><span class="btn-text tag-button" @click="handleAddEquities">+{{$t("添加权益")}}</span></div></div></td><td><div class="tb-cell ex-tb-cell operation-edit-state"><span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleSaveOperation"><span v-if="stopLoading || !isLoading">{{$t("保存")}}</span><span v-else class="crm-action-icon-requesting"></span></span><span :class="[\'btn-text\', !stopLoading && \'disabled\']" @click="handleCancelOperation" style="margin-left:10px;">{{$t("取消")}}</span></div></td></tr>',props:{data:{type:Object,default:function(){return{}}},list:{type:Array,default:function(){return[]}},stopLoading:{type:Boolean,default:!0}},data:function(){return{temp:_.clone(this.data),isLoading:!1,isEditMode:!this.data.gradeId}},methods:{handleModifyOperation:function(){this.isEditMode=!0},handleDeleteOperation:function(t){var e=this;t?a.confirm($t("会员等级删除后该等级-52402195"),$t("提示"),function(){e.isLoading=!0,e.$emit("delete",e.data),this.destroy()}):e.$emit("delete",e.data)},handleSaveOperation:function(){this.validate(this.temp)&&(this.isLoading=!0,this.$emit("update",this.temp))},handleCancelOperation:function(){if(!this.data.gradeId)return this.handleDeleteOperation(!1);this.isEditMode=!1,this.temp=_.clone(this.data)},handleAddEquities:function(){var e=this;t.async("crm-modules/components/pickselfobject/pickselfobject",function(t){t=new t;t.on("select",function(t){e.temp=_.extend({},e.temp,{equitiesIds:_.pluck(t,["_id"]),equitiesNames:_.pluck(t,["equities"])})}),t.render({isMultiple:!0,apiname:"MemberEquitiesObj",dataId:_.map(e.temp.equitiesIds,function(t){return{_id:t}})})})},validate:function(e){var t,i,a;return e.gradeName?!(e.gradeName!==this.data.gradeName&&_.find(this.list,function(t){return t.gradeName===e.gradeName})||(e.growthValueThreshold&&(e.growthValueThreshold=(t=e.growthValueThreshold,i=(t=String(t).replace(/[^0-9\-+.]/g,"")).match(/^[-+]/)?t.charAt(0):"",(a=(t=i?t.slice(1):t).split(".")).length=2,a[0]=a[0].replace(/\D/g,"").slice(0,14)||"0",a[1]=(a[1]||"").replace(/\D/g,"").slice(0,2),+((t=(i+a.join(".")).replace(/\.$/,"")).match(/^[-+]?\d+(\.\d+)?$/)?t:""))),!e.growthValueThreshold||99999999999999<e.growthValueThreshold||e.growthValueThreshold<-99999999999999?(s.showInputError(this.$refs["growth-input"]),1):(!e.equitiesIds||!e.equitiesIds.length)&&(s.showInputError(this.$refs["equities-input"]),1))):(s.showInputError(this.$refs["name-input"]),!1)}},watch:{data:function(t){this.isLoading=!1,this.isEditMode=!t.gradeId,this.temp=_.clone(this.data)},temp:{deep:!0,handler:function(e){e.gradeName!==this.data.gradeName&&_.find(this.list,function(t){return t.gradeName===e.gradeName})?s.showErrmsg($(this.$refs["name-input"]),$t("等级名称不能重复")):s.hideErrmsg($(this.$refs["name-input"]))}}}})});
define("crm-setting/membermanage/membermanage",["./home/<USER>"],function(e,t,i){var n=e("./home/<USER>");i.exports=Backbone.View.extend({name:"CRM-MEMBER-MANAGE",initialize:function(e){this.widgets={},this.setElement(e.wrapper)},render:function(){this.$el.html('<div class="j-member-manage-wrapper"></div>'),this.widgets.home=(new n).$mount(".j-member-manage-wrapper")},destroy:function(){_.each(this.widgets,function(e){e&&(e.destroy&&e.destroy(),e.$destroy)&&e.$destroy()}),this.widgets={}}})});
define("crm-setting/membermanage/service/api",["base-modules/utils"],function(e,r,t){var a,l,i=e("base-modules/utils"),e="5c08c3d64df47d0001708f2a",u={baseUrl:"/EM1HNCRM/API/v1/object/member/",enableMember:{url:"service/enable_member"},initializeMember:{url:"service/initialize_member"},listGrade:{url:"/EM1HNCRM/API/v1/object/grade/service/list_grade",data:{}},saveAllGrade:{url:"/EM1HNCRM/API/v1/object/grade/service/save_all_grade",data:{grades:[{ruleCode:1,gradeId:1,gradeNo:1,gradeName:$t("金牌"),growthValueThreshold:1,equitiesIds:[]}]}},getGradeEquities:{url:"/EM1HNCRM/API/v1/object/grade/service/get_grade_equities",data:{gradeId:""}},createGradeEquities:{url:"/EM1HNCRM/API/v1/object/grade/service/create_grade_equities",data:{gradeName:$t("金牌"),equitiesNames:["007"]}},updateGradeEquities:{url:"/EM1HNCRM/API/v1/object/grade/service/update_grade_equities",data:{gradeId:"",equitiesIds:[]}},initGrowthValue:{url:"/EM1HNCRM/API/v1/object/growth_value/service/init_growth_value_increase",data:{}},getGrowthValue:{url:"/EM1HNCRM/API/v1/object/growth_value/service/get_growth_value_setting",data:{}},updateGrowthValue:{url:"/EM1HNCRM/API/v1/object/growth_value/service/update_growth_value_increase",data:{growthValueruleType:0,growthValuePerPoint:15}},getIntegralExpireTime:{url:"integralRule/getIntegralExpireTime"},setIntegralExpireTime:{url:"integralRule/settingIntegralExpireTime",data:{type:1,expireTime:1}},pageIntegralRules:{url:"/EM1HNCRM/API/v1/object/integral_rule/service/page_integral_rules",data:{entityIds:["SalesOrderProductObj"],status:1,pageInfo:{pageSize:10,currentPage:0}}},getIntegralRule:{url:"/EM1HNCRM/API/v1/object/integral_rule/service/get_integral_rule",data:{ruleCodes:[e]}},deleteIntegralRule:{url:"/EM1HNCRM/API/v1/object/integral_rule/service/delete_integral_rule",data:{entityId:"SalesOrderProductObj",ruleCodes:[e]}},createIntegralRule:{url:"/EM1HNCRM/API/v1/object/integral_rule/service/create_rule_group",data:{ruleGroupArg:{entityId:"SalesOrderProductObj",ruleName:$t("联调积分"),ruleParse:"(1)",status:1,rules:[{fieldName:"sales_price",fieldType:"number",operate:"EQ",fieldValue:["1000"],ruleOrder:1}]},integralDataArg:{integral:10,integralTriggerType:3}}},updateIntegralRule:{url:"/EM1HNCRM/API/v1/object/integral_rule/service/update_integral_rule",data:{ruleGroupArg:{entityId:"SalesOrderProductObj",ruleCode:e,ruleName:$t("联调积分"),ruleParse:"(1)",status:1,rules:[{fieldName:"sales_price",fieldType:"number",operate:"EQ",fieldValue:["1000"],ruleOrder:1}]},integralDataArg:{integral:10,integralTriggerType:3}}}};function s(e,r){var t={};if(e.submitSelector){var e=$(e.submitSelector),a="state-requesting";if(e)return t.$el=e,t.loading=e.hasClass(a),"add"!==r||t.loading||e.addClass(a).width(e.width()+35).prepend('<span class="icon-requesting"><img src="'+FS.BLANK_IMG+'">&nbsp;&nbsp;</span>'),"rm"===r&&t.loading&&e.removeClass(a).width("").find(".icon-requesting").remove(),t}}function d(){var e=$.Deferred();return setTimeout(function(){e.reject("stillLoading")},1),e}function n(e,r,t){var a,l;return t=t||{},"/"!==(r=_.extend({type:"post"},u[e],r)).url.charAt(0)&&(r.url=u.baseUrl+r.url),!t.submitSelector||"hideZhong"in t||(t.hideZhong=!0),t.mock?(e=s(a=t,"add"))&&e.loading?d():(l=$.Deferred(),setTimeout(function(){l.resolve({Result:{FailureCode:0,StatusCode:0,UserInfo:{}},Value:a.mock}),s(a,"rm")},a.mockDelay||200),l):(e=s(t))&&e.loading?d():i.FHHApi(r,t).then(function(e,r,t){return e=e||{},t.responseJSON=t.responseJSON||e,null==e.Value?$.Deferred().reject(t,r,e):$.Deferred().resolve(e.Value,r,t)})}t.exports=(a=u,l={},_.keys(a).forEach(function(t){var e=a[t];e.url&&(l[t]=function(e,r){return n(t,e,r)},l[t]._key=t,"/"!==(l[t]._config=e).url.charAt(0))&&(e.url=a.baseUrl+e.url)}),l)});
var fs=require("fs"),child_process=require("child_process"),argv=process.argv,cmdFind='find . \\( -path "*/node_modules" -o -path "*/.git" -o -path "*/.svn" \\) -prune -o -name "*.js" -print';function getlist(e){return e.split("\n").filter(function(e){return!!e&&"."!==e.split("/").pop().charAt(0)&&!e.match(/.*-html.js$/)})}function contentreplace(e,a){var n=0;return e.forEach(function(e){var c={replaced:0,file:e,matched:[],freplace:r=fs.readFileSync(e,"utf-8").split(/\r\n|\n/)},r=r.map(function(e,r){return("reback"!==a?fcreplace:fcreback)(c,e,r)});if(c.replaced){if(c.matched.length)for(var t=c.matched.length;t--;)r.splice(c.matched[t],1);fs.writeFileSync(e,r.join("\n"),"utf-8"),n++}}),n}function fcreplace(e,r,c){if(!r.match(/^\s*\/\//)&&r.match(/\s+\/\/\s+vue-template\s*$/)){var t,a="",n="",s="";if(r.replace(/\w+\s+(\w+)\s+=\s+require\(([^)]+)\)(\S?)/,function(e,r,c,t){a=r,n=(c||"").replace(/['"]/g,""),s=t||""}),a&&n)return t=e.file.split("/").slice(0,-1).concat([n]).join("/"),t=(t=(t=(t=fs.readFileSync(t,"utf-8").replace(/\r/g,"")).replace(/\\/g,"\\\\").replace(/'/g,"\\'")).replace(/>\s*\n\s*</g,"><").replace(/\s*\n\s*/g," ").replace(/\s+$/,"")).replace(/<!--.*?-->/g,""),e.replaced++,[r.replace(/(^\s*)/,"$1// "),r.replace(/(.*?=).*/,"$1 '"+t+"'"+s)].join("\n")}return r}function fcreback(e,r,c){if(r.match(/^\s*\/\//)&&r.match(/\s+\/\/\s+vue-template\s*$/)){var t,a="",n="";if(r.replace(/\w+\s+(\w+)\s+=\s+require\(([^)]+)\)/,function(e,r,c){a=r,n=(c||"").replace(/['"]/g,"")}),a&&n)if((e.freplace[c+1]||"").replace(/\w+\s+(\w+)\s+=\s+'/,function(e,r){t=r===a}),t)return e.replaced++,e.matched.push(c+1),r.replace(/(^\s*)\/\/\s*/,"$1")}return r}child_process.exec(cmdFind,function(e,r){contentreplace(getlist(r),argv[2])});