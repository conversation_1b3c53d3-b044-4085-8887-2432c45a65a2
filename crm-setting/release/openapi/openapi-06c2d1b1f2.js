define("crm-setting/openapi/openapi",["crm-modules/common/util","./template/anchortpl-html","./template/page-html","base-modules/ui/scrollbar/scrollbar"],function(t,e,r){t("crm-modules/common/util");var o=t("./template/anchortpl-html"),n=t("./template/page-html"),l=t("base-modules/ui/scrollbar/scrollbar"),t=Backbone.View.extend({events:{"click .crm-left-tab span":"tabHandle","click .sub-menu-tit":"toggleSubMenu","click .crm-right-tab a":"scrollToTarget"},initialize:function(t){this.setElement(t.wrapper)},render:function(){this.$el.append(n({}))},tabHandle:function(t){var e=this,t=$(t.currentTarget);t.attr("data-index");t.hasClass("cur")||($(".crm-left-tab span",e.$el).removeClass("cur"),e.index=t.addClass("cur").attr("data-index"),e.renderPage(function(){$(".tab-con [style]",e.$el).removeAttr("style"),e.initScroll(),e.renderAnchor()}))},toggleSubMenu:function(t){$(t.currentTarget).toggleClass("up-arrow down-arrow").siblings(".sub-tab-menu").toggle()},renderPage:function(e){var r=this;$.ajax({url:"https://open.fxiaoke.com/support/articles/"+r.index,crossDomain:!0,success:function(t){0==t.errCode?($(".tab-con .inner",r.$el).html(t.data.content),e&&e()):$(".tab-con .inner",r.$el).html($t("获取不到文档"))}})},renderAnchor:function(){var n,l={17:"h3",19:"h2",151:"h4"}[this.index];$(".crm-module-con",this.$el).toggleClass("with-anchor",!!l),l&&(n=[],$(".tab-con "+l,this.$el).each(function(t,e){var r=$(e);r.attr("id")||r.attr("id",l+"_"+t),n.push({text:$(e).text(),href:$(e).attr("id")})}),$(".crm-right-tab .inner").html(o({list:n})))},scrollToTarget:function(t){t=$(t.currentTarget).attr("data-href"),t=$(".tab-con",this.$el).find(t);$(".tab-con",this.$el).scrollTop(0).scrollTop(t.offset().top-150)},initScroll:function(){this._scroll&&this._scroll.destroy(),this._scroll=new l($(".crm-g-con .inner",this.$el))},destroy:function(){this._scroll&&this._scroll.destroy()}});r.exports=t});
define("crm-setting/openapi/template/anchortpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            _.each(list, function(item, index) {
                __p += ' <a data-href="' + ((__t = "#" + item.href) == null ? "" : __t) + '" href="javascript:;">' + ((__t = item.text) == null ? "" : __t) + "</a> ";
            });
        }
        return __p;
    };
});
define("crm-setting/openapi/template/page-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("OpenAPI")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-p20"> <div class="inner"> <div class="con-item"> <p>' + ((__t = $t("crm.接口介绍")) == null ? "" : __t) + '</p> </div> <div class="con-item"> <h3>' + ((__t = $t("业务使用场景举例")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("crm.避免信息孤岛产生")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.统一IT管理")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.订单信息同步到ERP系统")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.订单信息同步到财务系统")) == null ? "" : __t) + '</p> </div> <div class="con-item"> <h3>' + ((__t = $t("OPEN API支持的能力")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("crm.数据流动说明")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.接口相关逻辑说明")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("crm.接口数据同步和业务操作说明")) == null ? "" : __t) + '</p> </div> <div class="con-item"> <h3>' + ((__t = $t("OPEN API详细介绍")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("具体内容详见纷享开放平台")) == null ? "" : __t) + '<br><a href="http://open.fxiaoke.com" target="_blank">http://open.fxiaoke.com</a></p> </div> </div> </div> </div>';
        }
        return __p;
    };
});