function asyncGeneratorStep(e,t,n,a,r,s,i){try{var c=e[s](i),o=c.value}catch(e){return void n(e)}c.done?t(o):Promise.resolve(o).then(a,r)}function _asyncToGenerator(c){return function(){var e=this,i=arguments;return new Promise(function(t,n){var a=c.apply(e,i);function r(e){asyncGeneratorStep(a,t,n,r,s,"next",e)}function s(e){asyncGeneratorStep(a,t,n,r,s,"throw",e)}r(void 0)})}}define("crm-setting/syncrules/syncrules",["./template/tpl-html","./syncRulesHandle"],function(e,t,n){var a,r,s,i=CRM.util,c=e("./template/tpl-html"),o=e("./syncRulesHandle");return Backbone.View.extend({template:c,events:{"click .add-btn":function(e){this.showSyncRule()}},initialize:function(e){this.$el.html(this.template()),this.render()},render:(s=_asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getRules();case 2:t=e.sent,t=t.result,this.renderTable(t||[]);case 5:case"end":return e.stop()}},e,this)})),function(){return s.apply(this,arguments)}),getRules:function(){return i.ajax_base("/EM1HNCRM/API/v1/object/master_data_app/service/list",{})},renderTable:function(t){var n=this;this.table&&this.table.destroy(),e.async("crm-widget/table/table",function(e){n.table=new e({$el:n.$(".sync-rules-table"),showPage:!1,doStatic:!0,colResize:!0,columns:[{data:"display_name",title:$t("对象名称")},{data:null,title:$t("操作"),width:"100px",render:function(e,t,n){return'<span class="btn js-detail">'+$t("查看详情")+"</span>"}}]}),n.table.on("trclick",function(e,t,n){n.hasClass("js-detail")&&(location.hash="app/shuttles/index")}),n.table.doStaticData(t)})},showSyncRule:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"add",t=1<arguments.length?arguments[1]:void 0,n=this,a=$t("添加操作行为");n.sr&&n.sr.destroy(),n.sr=new o({title:a}),n.sr.on("suc",function(e){e.object_api_name&&n.handleRule("add",{api_name:e.object_api_name})}),n.sr.show(t,e,n.getTableData())},handleRule:(r=_asyncToGenerator(regeneratorRuntime.mark(function e(t,n,a){var r,s;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r={add:"/EM1HNCRM/API/v1/object/master_data_app/service/add",stop:"/EM1HNCRM/API/v1/object/master_data_app/service/stop",start:"/EM1HNCRM/API/v1/object/master_data_app/service/start",delete:"/EM1HNCRM/API/v1/object/master_data_app/service/delete"},e.next=3,i.ajax_base(r[t],n);case 3:if(s=e.sent,a)return a(s),e.abrupt("return");e.next=7;break;case 7:s&&this.refreshTable();case 8:case"end":return e.stop()}},e,this)})),function(e,t,n){return r.apply(this,arguments)}),getTableData:function(){var t=[];return _.each(this.table.getCurData()||[],function(e){t.push(e.api_name)}),t},refreshTable:(a=_asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getRules();case 2:t=e.sent,this.table.doStaticData(t.result||[]);case 4:case"end":return e.stop()}},e,this)})),function(){return a.apply(this,arguments)})})});
function asyncGeneratorStep(t,e,s,i,a,n,r){try{var c=t[n](r),o=c.value}catch(t){return void s(t)}c.done?e(o):Promise.resolve(o).then(i,a)}function _asyncToGenerator(c){return function(){var t=this,r=arguments;return new Promise(function(e,s){var i=c.apply(t,r);function a(t){asyncGeneratorStep(i,e,s,a,n,"next",t)}function n(t){asyncGeneratorStep(i,e,s,a,n,"throw",t)}a(void 0)})}}define("crm-setting/syncrules/syncRulesHandle",["crm-widget/dialog/dialog"],function(i,t,e){var s,a=i("crm-widget/dialog/dialog").extend({attrs:{width:640,className:"crm-s-customer-follow",title:$t("添加跟进行为"),showBtns:!0,content:'<div class="follow-box"><div class="follow-objTitle">'+$t("所属对象")+'</div><div class="follow-objSelect" style="width:280px;"></div><div class="follow-wrap"></div></div>'},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"saveData"},saveData:function(){this.trigger("suc",_.omit(this.state,["filterData"])),this.hide()},getObjectList:function(){var s=this;return new Promise(function(e){s.objectList?e(s.objectList):CRM.util.ajax_base("/EM1HNCRM/API/v1/object/master_data_app/service/object_list",{}).then(function(t){s.objectList=s.mapData(t.result||[]),s.objectList=s.filterApiName(s.objectList),e(s.objectList)})})},mapData:function(t){return t.map(function(t){return{name:t.display_name,value:t.api_name}})},filterApiName:function(t){var e=this;return _.filter(t,function(t){return!e.state.filterData.includes(t.value)})},createObjSelect:(s=_asyncToGenerator(regeneratorRuntime.mark(function t(){var e,s;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=this,t.next=3,this.getObjectList();case 3:s=t.sent,this.select&&this.select.destroy(),i.async("crm-widget/select/select",function(t){e.select=new t({$wrap:e.$(".follow-objSelect"),zIndex:999,width:280,options:s,defaultValue:e.state.object_api_name||""}),e.updateSelectValue(e.select.getValue()),e.select.on("change",function(t){e.updateSelectValue(t)})});case 6:case"end":return t.stop()}},t,this)})),function(){return s.apply(this,arguments)}),updateSelectValue:function(e){var t;e&&(this.setState("object_api_name",e),t=_.find(this.objectList,function(t){return t.value===e}).name,this.setState("displayName",t))},setState:function(t,e){this.state[t]=e},show:function(t,e,s){var i=a.superclass.show.call(this);return this.type=e,this.state=_.extend({},t||{}),this.state.filterData=s||[],"add"===e&&(this.$(".follow-objTitle").show(),this.createObjSelect()),this.resizedialog(),i},hide:function(){var t=a.superclass.hide.call(this);return this.destroy(),t},destroy:function(){var t=a.superclass.destroy.call(this);return this.select&&this.select.destroy(),t}});e.exports=a});
define("crm-setting/syncrules/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-s-syncrules"> <p class="page-title">' + ((__t = $t("同步规则")) == null ? "" : __t) + '</p> <div class="behavior-btn-box"> <span class="behavior-btn-box add-btn">' + ((__t = $t("添加")) == null ? "" : __t) + '</span> </div> <div class="sync-rules-table"> </div> </div>';
        }
        return __p;
    };
});