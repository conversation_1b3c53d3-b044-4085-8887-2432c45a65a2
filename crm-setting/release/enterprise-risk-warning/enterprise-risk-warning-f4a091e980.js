define("crm-setting/enterprise-risk-warning/enterprise-risk-warning",["crm-modules/components/objecttable/objecttable"],function(e,t,a){var i=e("crm-modules/components/objecttable/objecttable"),e=Backbone.View.extend({initialize:function(){this.apiname="EnterpriseRiskObj"},render:function(){this.renderTable()},renderTable:function(){var n=this,e=i.extend({initialize:function(e){this.setElement(e.wrapper),i.prototype.initialize.apply(this,arguments)},getColumns:function(){var e=i.prototype.getColumns.apply(this,arguments);return _.findWhere(e,{dataType:"operate"}).width=150,e},getCustomOperate:function(e,t){return _.each(e,function(e){e.render_type="not_fold",e.data=t}),e},operateBtnClickHandle:function(e){var t=$(e.target),a=t.data("action"),i=t.closest(".tb-cell").data("id"),t=this.table&&this.table.options&&this.table.options.url;a&&(["add","Clone","Edit"].includes(a)?(a="__"+a.split("_").join("")+"Handle",n[a]&&n[a](e,i)):(e=this.getCurData().data.find(function(e){return e._id===i}),a=_.findWhere(e.operate,{action:a}),CRM.api.list_btn_operate({fields:this._filterParamForm(e,a.param_form),data:e,title:a.label,button_apiname:a.api_name,button_action:a.action,button_type:a.button_type,apiname:e.object_describe_api_name,dataId:e._id,redirect_type:a.redirect_type,_from:t&&/RelatedList$/.test(t)?"relatedList":"list",success:function(){n.refresh()}})))},trclickHandle:function(e){n.showDetail(e,this.table.getCurData())},getDataBack:function(e){this.$(".dt-page-left").html($t("crm.enterpriseriskobj.licensePara",{max:e.extendInfo.maxLicensePara,used:e.extendInfo.usingLicensePara,rest:e.extendInfo.maxLicensePara-e.extendInfo.usingLicensePara}))}});n.list=new e({wrapper:n.options.wrapper,apiname:n.apiname,showOperate:!0,tableOptions:{showFilerBtn:!1,showMultiple:!1,searchTerm:null,search:{placeHolder:$t("搜索规则名称"),type:"Keyword",highFieldName:"name",pos:"T"},operate:{pos:"T",btns:[{action:"add",attrs:"data-action=add",className:"j-action",text:$t("新建")}]},refreshCallBack:function(){n.refresh()}}}),n.list.render()},refresh:function(){this.list.refresh()},showDetail:function(e,t){var a=this;CRM.api.show_crm_detail({apiName:a.apiname,id:e._id,idList:_.pluck(t,"_id"),showMask:!1,top:56,callback:function(){a.refresh()}})},__addHandle:function(){var e=this;e.add=CRM.api.add({apiname:e.apiname,success:function(){e.refresh()}})},__EditHandle:function(e,t){var a=this;a.edit=CRM.api.edit({apiname:a.apiname,id:t,success:function(){a.refresh()}})},__CloneHandle:function(e,t){var a=this;CRM.api.clone({apiname:a.apiname,dataId:t,success:function(){a.refresh()}})}});a.exports=e});