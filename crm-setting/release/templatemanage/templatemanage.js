define("crm-setting/templatemanage/email/dialog/dialog",["crm-widget/dialog/dialog","crm-widget/select/select","crm-modules/common/util","./template/tpl-html"],function(e,t,l){var s=e("crm-widget/dialog/dialog"),i=e("crm-widget/select/select"),a=e("crm-modules/common/util"),o=e("./template/tpl-html"),n=s.extend({templateId:"",options:{selectList:[],apiName:"",noTemplate:"",hasTemplate:"",objSelectList:[],defaultObj:""},attrs:{title:$t("新建模板"),showBtns:!0,showScroll:!1,content:'<div class="content"></div>',className:"crm-s-templatemanage print-dialog"},events:{"click .b-g-btn-cancel":"hide","click .dialog-btns .b-g-btn":"onEnter"},show:function(e){var t=this,l=(n.superclass.show.call(this),$(this.element).find(".content").html(o({noTemplate:e.noTemplate,hasTemplate:e.hasTemplate})),t.options.objSelectList=e.objSelectList,t.options.defaultObj=e.defaultObj,t.options.selectList=e.selectList,t.options.apiName=e.selectList&&e.selectList[0]&&e.selectList[0].apiName,t.filterSelect(e.selectList,t.options.defaultObj));t.templateId=l&&l[0]&&l[0].value,t.initObjSelect(e.objSelectList,this.options.defaultObj),t.initTplSelect(_.filter(e.selectList,function(e){return e.apiName==t.options.defaultObj}))},initObjSelect:function(e,t){var l=this;l.objselect&&l.objselect.destroy(),l.objselect=new i({$wrap:$(this.element).find(".j-obj-select"),width:290,options:e||[],zIndex:1e3,defaultValue:t}),l.objselect.on("change",function(e,t){l.options.defaultObj=t.value,l.objselect.setValue(t.value),l.select&&(0==(t=l.filterSelect(l.options.selectList,t.value)).length&&(l.$(".mn-radio-item").eq(0).removeClass("mn-selected"),l.$(".mn-radio-item").eq(1).addClass("mn-selected")),t=t.length?t:[{name:"",value:""}],l.select&&l.select.resetOptions(t,!0),l.select.setValue(t.length?t[0].value:"",!0))})},filterSelect:function(e,t){return _.filter(e,function(e){return e.apiName==t})},initTplSelect:function(e){var l=this;l.select&&l.select.destroy(),l.select=new i({$wrap:$(this.element).find(".j-tpl-select"),width:290,options:e||[],zIndex:1e3}),l.select.on("change",function(e,t){l.templateId=t.value,l.options.apiName=t.apiName,""!=t.value&&l.objselect.getValue()!=l.options.apiName&&l.objselect.trigger("change",t.apiName,{value:t.apiName},l.objselect)})},checkAddRight:function(e,t){a.FHHApi({url:"/EM1HCRMTemplate/emailTemplateAdminApi/isNotOverflow",data:{objDescApiName:e},success:function(e){0==e.Result.StatusCode&&(0==e.Value.code?t&&t(e.Value.result,e.Value.msg):a.alert(e.Value.msg))},error:function(){a.alert($t("网络错误请重试！"))}})},_onClose:function(e){var t=this;2==e&&(t.template&&t.template.destroy(),t.trigger("suc")),t.trigger("suc")},onEnter:function(){var s=this;e.async("paas-template/sdk",function(l){2==$(".crm-s-templatemanage .mn-selected").attr("data-permissiontype")?s.checkAddRight(s.options.defaultObj,function(e,t){e?s.template=new l({objectName:s.options.defaultObj,templateType:"email",onClose:function(e){s._onClose(e),s.hide()}}):(a.alert(t),s.hide())}):s.checkAddRight(s.options.defaultObj,function(e,t){e?s.template=new l({objectName:s.options.defaultObj,copyFrom:s.templateId,templateType:"email",onClose:function(e){s._onClose(e),s.hide()}}):(a.alert(t),s.hide())})})},hide:function(){n.superclass.hide.call(this),this.destroy()},destroy:function(){_.each(["select","objselect"],function(e){this[e]&&this[e].destroy&&this[e].destroy(),this[e]=null}),$(this.element).off().remove(),n.superclass.destroy.call(this)}});l.exports=n});
define("crm-setting/templatemanage/email/dialog/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="content"> <div class="check-obj-box"> <span class="select-lb">' + ((__t = $t("所属对象")) == null ? "" : __t) + '</span> <div class="j-obj-select"></div> </div> <div class="check-tpl-box"> <span class="select-lb">' + ((__t = $t("新建方式")) == null ? "" : __t) + '</span> <div class="mn-radio-box add-content"> <div class="radio-item"> <div class="mn-radio-item ' + ((__t = hasTemplate) == null ? "" : __t) + ' " data-permissiontype="1"></div> <div class="radio-lb">' + ((__t = $t("从现有的模板复制")) == null ? "" : __t) + '</div> <div class="j-tpl-select"></div> </div> <div class="radio-item"> <div class="mn-radio-item ' + ((__t = noTemplate) == null ? "" : __t) + ' " data-permissiontype="2"></div> <div class="radio-lb">' + ((__t = $t("不复制直接新建")) == null ? "" : __t) + "</div> </div> </div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/templatemanage/email/email",["crm-widget/table/table","./template/tpl-html"],function(l,e,t){var o=FS.crmUtil,a=l("crm-widget/table/table"),i=l("./template/tpl-html"),s=Backbone.Model.extend({defaults:{objDescApiName:""}}),n=Backbone.View.extend({tagName:"div",className:"",selectList:[],objSelectOptions:[],templateId:"",templateList:[],keyword:"",initialize:function(e){var t=this;t.model=new s,t.widgets={},t.render(),t.listenTo(t.model,"change:objDescApiName",t._refreshTB)},show:function(){this.widgets.tb&&this.widgets.tb.setParam({},!0)},render:function(){var t=this;t.$el.html(i({})),t.getObjectList(function(e){t.initTable(e),t.setTableData(t.keyword,t.model.get("objDescApiName"))})},events:{"click .j-add-tpl":"_onAdd"},getObjectList:function(t){var i=this;o.FHHApi({url:"/EM1HCRMTemplate/emailTemplateAdminApi/findObjectListInfo",success:function(e){var a;0==e.Result.StatusCode&&(a=[{name:$t("全部"),value:""}],_.each(e.Value,function(e,t){a.push({name:e,value:t})}),i.objSelectOptions=a,t)&&t(a)}})},createOperate:function(t){var a=this;l.async("crm-modules/components/templatemanage/templatemanage",function(e){a.operate&&a.operate.destroy&&a.operate.destroy(),a.operate=new e({type:"email"}),a.operate.on("refresh",function(){a.widgets.tb&&a.widgets.tb.setParam({},!0),a.setTableData(a.keyword,a.model.get("objDescApiName"),!0)}),t&&t(a.operate)})},initTable:function(e){var t,i=this;i.widgets.tb?(i.widgets.tb.setParam({},!0),i.setTableData(i.keyword,i.model.get("objDescApiName"))):(i.widgets.tb=new a({$el:i.$(".right-content"),showMoreBtn:!1,doStatic:!0,operate:{pos:"T",btns:[{text:$t("新建"),className:"j-add-tpl"}]},showPage:!1,search:{pos:"T",placeHolder:$t("搜索模板名称"),type:"Keyword",highFieldName:"name"},columns:[{data:"name",title:$t("模板名称"),width:240,orderValues:[1,0],filterCompare:[22,23],isOrderBy:!0,isFilter:!0,render:function(e,t,a){return"<div>"+(a.isDefault?'<span class="setStatus" >'+$t("默认")+"</span>":"")+(0==a.type?'<span class="setStatus" >'+$t("预设")+"</span>":"")+"<span >"+e+"</span></div>"}},{data:"objDescApiText",title:$t("所属对象"),orderValues:[1,0],isOrderBy:!0,render:function(e,t,a){return e}},{data:"templateId",title:$t("模板ID"),orderValues:[0,1],width:220,isOrderBy:!0},{data:"creatorId",title:$t("创建人"),orderValues:[1,0],isOrderBy:!0,isId:!0,dataType:8,referRule:"Employee"},{data:"createTime",title:$t("创建时间"),dataType:4,orderValues:[1,0],filterCompare:[1,2,17,18,19,20,21,4,6,9,10,25,26,27,28,29,30,31,32,33,34,35,36],isOrderBy:!0,isFilter:!0,render:function(e,t,a){return e}},{data:"modifierId",title:$t("最后修改人"),orderValues:[1,0],isOrderBy:!0,isId:!0,dataType:8,referRule:"Employee"},{data:"modifyTime",title:$t("最后修改时间"),dataType:4,orderValues:[1,0],filterCompare:[1,2,17,18,19,20,21,4,6,9,10,25,26,27,28,29,30,31,32,33,34,35,36],isOrderBy:!0,isFilter:!0,render:function(e,t,a){return e}},{data:null,title:$t("操作"),lastFixed:!0,width:170,render:function(e,t,a){return'<div class="operate"  templateId="'+a.templateId+'" apiname="'+a.objDescApiName+'" type="'+a.type+'" default="'+a.isDefault+'"><a href="javascript:;" class="j-edit edit-icon" tplId="'+a.templateId+'">'+$t("编辑")+"</a>"+i._moreTpl(a.type,a.isDefault,a.templateId,a.name)+"</div>"}}],formatData:function(e){return{data:e.result&&e.result.list,totalCount:e.result&&(e.result.totalCount||0)}},initComplete:function(){this.$el.find(".batch-term-operate .crm-btn-groups").prepend('<a class=\'el-icon-question vui-tooltip-question help-link\' href="https://help.fxiaoke.com/dbde/ef25/c45b/5b4a"  target="_blank"></a>')}}),(t=i.getAssignObjDescApiName())&&i.once("templateListDataLoaded",function(e){i.model.set("objDescApiName",t)}),i.widgets.objSelect=i.widgets.tb.addSelect({$target:i.$(".dt-op-box"),pos:"before",label:$t("对象:"),options:e,defaultValue:t,zIndex:1e3}),i.widgets.tb.on("dt.search",function(e){i.keyword=e,i.setTableData(e,i.model.get("objDescApiName"))}),i.widgets.objSelect.on("change",function(e,t){""!=t.value?i.model.set("isAll",!1):i.model.set("isAll",!0),i.model.set("objDescApiName",t.value)}),i.widgets.tb.on("trclick",function(e,t,a){a.hasClass("j-edit")?i._onEdit(a):a.hasClass("j-init")?i._doInit(a):a.hasClass("j-default")?i._doDefault(a):a.hasClass("j-delete")?i._onDelDialog(a):i._renderDetail(e)}))},setTableData:function(){var a,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",i=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",s=this;!e&&!i||2<arguments.length&&void 0!==arguments[2]&&arguments[2]?(s.widgets.tb.showLoading(),o.FHHApi({url:"/EM1HCRMTemplate/emailTemplateAdminApi/page",data:{objDescApiName:s.model.get("objDescApiName"),pageNumber:1,pageSize:1e6,_isfilter:!1},success:function(e){var t;0==e.Result.StatusCode?(t=e.Value.result.list,s.templateList=t,s.widgets.tb.doStaticData(t),s.selectList=[],s.templateId=0<t.length?t[0].templateId:[],s.trigger("templateListDataLoaded",t),_.each(t,function(e){[2,3].includes(e.isToWord)||s.selectList.push({value:e.templateId,name:e.name,apiName:e.objDescApiName})})):e.Result.FailureMessage&&CRM.util.alert(e.Result.FailureMessage),s.widgets.tb.hideLoading()}})):(a=[],_.each(s.templateList,function(e){var t=e.objDescApiName||"";!(-1<(e.name||"").toLowerCase().indexOf((s.keyword||"").toLowerCase())||""==s.keyword)||i!=t&&""!=i||a.push(e)}),s.widgets.tb.doStaticData(a))},getAssignObjDescApiName:function(){return CRM.util.getUrlParam(location.search,"assignObjDescApiName")||""},_refreshTB:function(){var e=this;e.widgets.tb&&e.widgets.tb.setParam({objDescApiName:e.model.get("objDescApiName")},!0),e.setTableData(e.keyword,e.model.get("objDescApiName"))},_renderDetail:function(t){var a=this;return l.async("crm-modules/detail/templatemanage/emaildetail/edetail",function(e){a.widgets.detail||(a.widgets.detail=new e,a.widgets.detail.on("refresh",function(){a.widgets.tb&&a.widgets.tb.setParam({},!0),a.setTableData(a.keyword,a.model.get("objDescApiName"))})),a.widgets.detail.show({type:t.type,templateId:t.templateId,Default:t.isDefault,id:t.id,name:t.name,apiName:t.objDescApiName})}),!1},_onEdit:function(t){var a=t.attr("tplId");return this.createOperate(function(e){e.edit(a,t.closest(".operate").attr("apiName"))}),!1},_moreTpl:function(e,t,a,i){return'<a class="j-delete del-icon"  templateId="'+a+'" tplName="'+FS.util.encodeHtml(i)+'">'+$t("删除")+"</a>"},_onAdd:function(e){var t=this,a=1<=t.selectList.length?"mn-selected":"",i=a?"":"mn-selected",s=_.clone(t.objSelectOptions);return s.shift(),l.async("./dialog/dialog",function(e){t.widgets.createDialog=new e,t.widgets.createDialog.show({selectList:t.selectList,noTemplate:i,hasTemplate:a,objSelectList:s,defaultObj:""==t.model.get("objDescApiName")?s[0]&&s[0].value:t.model.get("objDescApiName")}),t.widgets.createDialog.on("suc",function(){t.widgets.tb&&t.widgets.tb.setParam({},!0),t.setTableData(t.keyword,t.model.get("objDescApiName"))})}),FS.log&&FS.log("s-paasobj_create_email_template","cl",{module:"s-paasobj",subModule:"template"}),e.preventDefault(),e.stopPropagation(),!1},_onClose:function(e){var t=this;2==e&&(t.template&&t.template.destroy(),t.widgets.tb&&t.widgets.tb.setParam({},!0),t.setTableData(t.keyword,t.model.get("objDescApiName"))),t.widgets.tb&&t.widgets.tb.setParam({},!0),t.setTableData(t.keyword,t.model.get("objDescApiName"))},_doInit:function(t){var e=this,a=o.confirm($t("crm.确认初始化当前模板"),$t("提示"),function(){e.createOperate(function(e){e.init(t.closest(".operate").attr("apiName"))}),a.hide()});return!1},_doDefault:function(t){var a=t.attr("templateId");return this.createOperate(function(e){e.setDefault(a,t.closest(".operate").attr("apiName"))}),!1},_onDelDialog:function(e){var t=e.attr("templateId"),a=e.attr("tplName");return this.createOperate(function(e){e.delDialog(t,a,$t("该模板"))}),!1},destroy:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,this.$el.off().remove()}});t.exports=n});
define("crm-setting/templatemanage/email/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="right"> <div class="right-content"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/templatemanage/print/dialog/dialog",["crm-widget/dialog/dialog","crm-widget/select/select","crm-modules/common/util","./template/tpl-html"],function(e,t,l){var s=e("crm-widget/dialog/dialog"),i=e("crm-widget/select/select"),o=e("crm-modules/common/util"),n=e("./template/tpl-html"),a=s.extend({templateId:"",options:{selectList:[],apiName:"",noTemplate:"",hasTemplate:"",objSelectList:[],defaultObj:""},attrs:{title:$t("新建模板"),showBtns:!0,showScroll:!1,content:'<div class="content"></div>',className:"crm-s-templatemanage print-dialog"},events:{"click .b-g-btn-cancel":"hide","click .dialog-btns .b-g-btn":"onEnter"},show:function(e){var t=this,l=(a.superclass.show.call(this),$(this.element).find(".content").html(n({noTemplate:e.noTemplate,hasTemplate:e.hasTemplate})),t.options.objSelectList=e.objSelectList,t.options.defaultObj=e.defaultObj,t.options.selectList=e.selectList,t.options.apiName=e.selectList&&e.selectList[0]&&e.selectList[0].apiName,t.filterSelect(e.selectList,t.options.defaultObj));t.templateId=l&&l[0]&&l[0].value,t.initObjSelect(e.objSelectList,this.options.defaultObj),t.initTplSelect(_.filter(e.selectList,function(e){return e.apiName==t.options.defaultObj}))},initObjSelect:function(e,t){var l=this;l.objselect&&l.objselect.destroy(),l.objselect=new i({$wrap:$(this.element).find(".j-obj-select"),width:290,options:e||[],zIndex:1e3,defaultValue:t}),l.objselect.on("change",function(e,t){l.options.defaultObj=t.value,l.objselect.setValue(t.value),l.select&&(0==(t=l.filterSelect(l.options.selectList,t.value)).length&&(l.$(".mn-radio-item").eq(0).removeClass("mn-selected"),l.$(".mn-radio-item").eq(1).addClass("mn-selected")),t=t.length?t:[{name:"",value:""}],l.select&&l.select.resetOptions(t,!0),l.select.setValue(t.length?t[0].value:"",!0))})},filterSelect:function(e,t){return _.filter(e,function(e){return e.apiName==t})},initTplSelect:function(e){var l=this;l.select&&l.select.destroy(),l.select=new i({$wrap:$(this.element).find(".j-tpl-select"),width:290,options:e||[],zIndex:1e3}),l.select.on("change",function(e,t){l.templateId=t.value,l.options.apiName=t.apiName,""!=t.value&&l.objselect.getValue()!=l.options.apiName&&l.objselect.trigger("change",t.apiName,{value:t.apiName},l.objselect)})},checkAddRight:function(e,t){o.FHHApi({url:"/EM1HCRMTemplate/printTemplateAdminApi/isNotOverflow",data:{objDescApiName:e},success:function(e){0==e.Result.StatusCode&&(0==e.Value.code?t&&t(e.Value.result,e.Value.msg):o.alert(e.Value.msg))},error:function(){o.alert($t("网络错误请重试！"))}})},_onClose:function(e){var t=this;2==e&&(t.template&&t.template.destroy(),t.trigger("suc")),t.trigger("suc")},onEnter:function(){var s=this;e.async("paas-template/sdk",function(l){2==$(".crm-s-templatemanage .mn-selected").attr("data-permissiontype")?s.checkAddRight(s.options.defaultObj,function(e,t){e?s.template=new l({objectName:s.options.defaultObj,onClose:function(e){s._onClose(e),s.hide()}}):(o.alert(t),s.hide())}):s.checkAddRight(s.options.defaultObj,function(e,t){e?s.template=new l({objectName:s.options.defaultObj,copyFrom:s.templateId,onClose:function(e){s._onClose(e),s.hide()}}):(o.alert(t),s.hide())})})},hide:function(){a.superclass.hide.call(this),this.destroy()},destroy:function(){_.each(["select","objselect"],function(e){this[e]&&this[e].destroy&&this[e].destroy(),this[e]=null}),$(this.element).off().remove(),a.superclass.destroy.call(this)}});l.exports=a});
define("crm-setting/templatemanage/print/dialog/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="content"> <div class="check-obj-box"> <span class="select-lb">' + ((__t = $t("所属对象")) == null ? "" : __t) + '</span> <div class="j-obj-select"></div> </div> <div class="check-tpl-box"> <span class="select-lb">' + ((__t = $t("新建方式")) == null ? "" : __t) + '</span> <div class="mn-radio-box add-content"> <div class="radio-item"> <div class="mn-radio-item ' + ((__t = hasTemplate) == null ? "" : __t) + ' " data-permissiontype="1"></div> <div class="radio-lb">' + ((__t = $t("从现有的模板复制")) == null ? "" : __t) + '</div> <div class="j-tpl-select"></div> </div> <div class="radio-item"> <div class="mn-radio-item ' + ((__t = noTemplate) == null ? "" : __t) + ' " data-permissiontype="2"></div> <div class="radio-lb">' + ((__t = $t("不复制直接新建")) == null ? "" : __t) + "</div> </div> </div> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/templatemanage/print/print",["crm-widget/table/table","paas-vui/sdk","./template/tpl-html"],function(l,e,t){var o=FS.crmUtil,a=l("crm-widget/table/table"),i=l("paas-vui/sdk"),s=l("./template/tpl-html"),r=Backbone.Model.extend({defaults:{objDescApiName:""}}),n=Backbone.View.extend({tagName:"div",className:"",selectList:[],templateId:"",objSelectOptions:[],templateList:[],keyword:"",initialize:function(e){var t=this;t.model=new r,t.widgets={},t.render(),t.listenTo(t.model,"change:objDescApiName",t._refreshTB)},show:function(){this.widgets.tb&&this.widgets.tb.setParam({},!0)},events:{"click .j-add-tpl":"_onAdd","click .j-add-excel-tpl ":"_onAddExcel","click .j-add-word-tpl":"_onAddWord"},render:function(){var a=this;a.$el.html(s({})),a.getObjectList(function(t){i.getTemplate().then(function(e){a.initTable(t),a.setTableData(a.keyword,a.model.get("objDescApiName"))})})},getObjectList:function(t){o.FHHApi({url:"/EM1HCRMTemplate/printTemplateAdminApi/findObjectListInfo",success:function(e){var a;0==e.Result.StatusCode?(a=[{name:$t("全部"),value:""}],_.each(e.Value,function(e,t){a.push({name:e,value:t})}),t&&t(a)):e.Result.FailureMessage&&CRM.util.alert(e.Result.FailureMessage)}})},createOperate:function(t){var a=this;l.async("crm-modules/components/templatemanage/templatemanage",function(e){a.operate&&a.operate.destroy&&a.operate.destroy(),a.operate=new e({type:"print"}),a.operate.on("refresh",function(){a.setTableData(a.keyword,a.model.get("objDescApiName"),!0),a.widgets.tb&&a.widgets.tb.setParam({},!0)}),t&&t(a.operate)})},initTable:function(e){var t,s=this,e=(s.objSelectOptions=e,[{isFold:!1,text:$t("新建Word模板"),className:"j-add-word-tpl"},{isFold:!1,text:$t("新建Excel模板"),className:"j-add-excel-tpl"},{isFold:!1,text:$t("新建模板"),className:"j-add-tpl"}]);s.widgets.tb?(s.setTableData(s.keyword,s.model.get("objDescApiName")),s.widgets.tb.setParam({},!0)):(s.widgets.tb=new a({$el:s.$(".right-content"),showMoreBtn:!1,doStatic:!0,showPage:!1,search:{pos:"T",placeHolder:$t("搜索模板名称"),type:"Keyword",highFieldName:"name"},operate:{pos:"T",btns:e},columns:[{data:"name",title:$t("模板名称"),width:240,orderValues:[1,0],isOrderBy:!0,isFilter:!0,filterCompare:[22,23],render:function(e,t,a){return"<div>"+(a.isDefault?'<span class="setStatus" >'+$t("默认")+"</span>":"")+(0==a.type?'<span class="setStatus" >'+$t("预设")+"</span>":"")+"<span >"+e+"</span></div>"}},{data:"objDescApiText",title:$t("所属对象"),orderValues:[1,0],isOrderBy:!0,render:function(e,t,a){return e}},{data:"outPdf",title:$t("打印输出格式"),orderValues:[0,1],isOrderBy:!0,render:function(e,t,a){switch(a.outPdf?0:a.isToWord){case 0:return"<span>pdf</span>";case 1:return"<span>word（".concat($t("仅支持web端"),"）</span>");case 2:return"<span>excel</span>";case 3:return"<span>word(".concat($t("上传离线word模板"),")</span>");default:return"<span>--</span>"}}},{data:"isToWord",title:$t("source.file.format"),orderValues:[0,1],isOrderBy:!0,render:function(e,t,a){switch(e){case 0:return"<span>pdf</span>";case 1:return"<span>word（".concat($t("仅支持web端"),"）</span>");case 2:return"<span>excel</span>";case 3:return"<span>word(".concat($t("上传离线word模板"),")</span>");default:return"<span>--</span>"}}},{data:"templateId",title:$t("模板ID"),orderValues:[0,1],width:220,isOrderBy:!0},{data:"creatorId",title:$t("创建人"),orderValues:[1,0],isOrderBy:!0,isId:!0,dataType:8,referRule:"Employee"},{data:"createTime",title:$t("创建时间"),dataType:4,orderValues:[1,0],filterCompare:[1,2,17,18,19,20,21,4,6,9,10,25,26,27,28,29,30,31,32,33,34,35,36],isOrderBy:!0,isFilter:!0,render:function(e,t,a){return e}},{data:"modifierId",title:$t("最后修改人"),orderValues:[1,0],isOrderBy:!0,isId:!0,dataType:8,referRule:"Employee"},{data:"modifyTime",title:$t("最后修改时间"),dataType:4,filterCompare:[1,2,17,18,19,20,21,4,6,9,10,25,26,27,28,29,30,31,32,33,34,35,36],orderValues:[1,0],isOrderBy:!0,isFilter:!0,render:function(e,t,a){return e}},{data:null,title:$t("操作"),lastFixed:!0,width:170,render:function(e,t,a){return'<div class="operate"  templateId="'+a.templateId+'" apiname="'+a.objDescApiName+'" type="'+a.type+'" default="'+a.isDefault+'"><a href="javascript:;" class="j-edit" tplId="'+a.templateId+'" tplIsToWord="'+a.isToWord+'">'+$t("编辑")+"</a>"+s._moreTpl(a.type,a.isDefault,a.templateId,a.name)+"</div>"}}],formatData:function(e){if(e.result)return s.selectList=[],s.templateId=e.result.list&&e.result.list[0]&&e.result.list[0].templateId,_.each(e.result.list,function(e){[2,3].includes(e.isToWord)||s.selectList.push({value:e.templateId,name:e.name,apiName:e.objDescApiName})}),{data:e.result&&e.result.list,totalCount:e.result&&(e.result.totalCount||0)}},initComplete:function(){this.$el.find(".batch-term-operate .crm-btn-groups").prepend('<a class=\'el-icon-question vui-tooltip-question help-link\' href="https://help.fxiaoke.com/dbde/ef25/e552/cebd"  target="_blank"></a>')}}),(t=s.getAssignObjDescApiName())&&s.once("templateListDataLoaded",function(e){s.model.set("objDescApiName",t)}),s.widgets.tb.on("dt.search",function(e){s.keyword=e,s.setTableData(e,s.model.get("objDescApiName"))}),s.widgets.objSelect=s.widgets.tb.addSelect({$target:s.$(".dt-op-box"),pos:"before",label:$t("对象:"),className:"",options:s.objSelectOptions,defaultValue:t||""}),s.widgets.objSelect.on("change",function(e,t){s.model.set("objDescApiName",t.value)}),s.widgets.tb.on("trclick",function(e,t,a){a.hasClass("j-edit")?s._onEdit(a):a.hasClass("j-init")?s._doInit(a):a.hasClass("j-delete")?s._onDelDialog(a):a.hasClass("j-default")?s._doDefault(a):s._renderDetail(e)}))},setTableData:function(){var a,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",s=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",i=this;!e&&!s||2<arguments.length&&void 0!==arguments[2]&&arguments[2]?(i.widgets.tb.showLoading(),o.FHHApi({url:"/EM1HCRMTemplate/printTemplateAdminApi/page",data:{objDescApiName:i.model.get("objDescApiName"),pageNumber:1,pageSize:1e6,_isfilter:!1},success:function(e){var t;0==e.Result.StatusCode?(t=e.Value.result.list,i.templateList=t,i.widgets.tb.doStaticData(t),i.selectList=[],i.templateId=0<t.length?t[0].templateId:[],i.trigger("templateListDataLoaded",t),_.each(t,function(e){[2,3].includes(e.isToWord)||i.selectList.push({value:e.templateId,name:e.name,apiName:e.objDescApiName})})):e.Result.FailureMessage&&CRM.util.alert(e.Result.FailureMessage),i.widgets.tb.hideLoading()}})):(a=[],_.each(i.templateList,function(e){var t=e.objDescApiName||"";!(-1<(e.name||"").toLowerCase().indexOf((i.keyword||"").toLowerCase())||""==i.keyword)||s!=t&&""!=s||a.push(e)}),i.widgets.tb.doStaticData(a))},getAssignObjDescApiName:function(){return CRM.util.getUrlParam(location.search,"assignObjDescApiName")||""},_refreshTB:function(){var e=this;e.widgets.tb&&e.widgets.tb.setParam({objDescApiName:e.model.get("objDescApiName")},!0),e.setTableData(e.keyword,e.model.get("objDescApiName"))},_renderDetail:function(t){var a=this;return l.async("crm-modules/detail/templatemanage/printdetail/pdetail",function(e){a.widgets.detail||(a.widgets.detail=new e,a.widgets.detail.on("refresh",function(){a.setTableData(a.keyword,a.model.get("objDescApiName")),a.widgets.tb&&a.widgets.tb.setParam({},!0)})),a.widgets.detail.show({type:t.type,templateId:t.templateId,Default:t.isDefault,id:t.id,name:t.name,apiName:t.objDescApiName,isToWord:t.isToWord})}),!1},_onEdit:function(t){var a=t.attr("tplId"),s=+t.attr("tplIsToWord");return this.createOperate(function(e){e.edit(a,t.closest(".operate").attr("apiName"),s)}),!1},_moreTpl:function(e,t,a,s){s=FS.util.encodeHtml(s);return 0==e?t?'<a class="j-init" apiname="'+s+'">'+$t("初始化")+"</a>":'<a class="j-default"  templateId="'+a+'"  apiname="'+s+'">'+$t("设为默认")+'</a><a class="j-init" apiname="'+s+'">'+$t("初始化")+"</a>":t?'<a class="j-delete"  templateId="'+a+'" tplName="'+s+'">'+$t("删除")+"</a>":'<a class="j-default" templateId="'+a+'"  apiname="'+s+'">'+$t("设为默认")+'</a><a class="j-delete" templateId="'+a+'" tplName="'+s+'">'+$t("删除")+"</a>"},_onAdd:function(e){var t=this,a=1<=t.selectList.length?"mn-selected":"",s=a?"":"mn-selected",i=_.clone(t.objSelectOptions);i.shift(),l.async("./dialog/dialog",function(e){t.createDialog=new e,t.createDialog.on("suc",function(){t.setTableData(t.keyword,t.model.get("objDescApiName")),t.widgets.tb&&t.widgets.tb.setParam({},!0)}),t.createDialog.show({selectList:t.selectList,noTemplate:s,hasTemplate:a,objSelectList:i,defaultObj:""==t.model.get("objDescApiName")?i[0]&&i[0].value:t.model.get("objDescApiName")})}),FS.log&&FS.log("s-paasobj_create_print_template","cl",{module:"s-paasobj",subModule:"template"})},printExcelAndWordTpl:function(t){var a=this,s=this.model.get("objDescApiName");i.getTemplate().then(function(e){a.excelTemplate=e.createPrintTpl({objDescApiName:s,isToWord:t}),a.excelTemplate.$on("save",function(){a.widgets.tb&&a.widgets.tb.setParam({},!0)})})},_onAddExcel:function(){this.printExcelAndWordTpl(2),FS.log&&FS.log("s-paasobj_create_excel_template","cl",{module:"s-paasobj",subModule:"template"})},_onAddWord:function(){this.printExcelAndWordTpl(3),FS.log&&FS.log("s-paasobj_create_word_template","cl",{module:"s-paasobj",subModule:"template"})},_onClose:function(e){var t=this;2==e&&(t.template&&t.template.destroy(),t.setTableData(t.keyword,t.model.get("objDescApiName")),t.widgets.tb)&&t.widgets.tb.setParam({},!0),t.setTableData(t.keyword,t.model.get("objDescApiName")),t.widgets.tb&&t.widgets.tb.setParam({},!0)},_doInit:function(t){var e=this,a=o.confirm($t("crm.确认初始化当前模板"),$t("提示"),function(){e.createOperate(function(e){e.init(t.closest(".operate").attr("apiName"),t.closest(".operate").attr("templateid"))}),a.hide()});return!1},_doDefault:function(t){var a=t.attr("templateId");return this.createOperate(function(e){e.setDefault(a,t.closest(".operate").attr("apiName"))}),!1},_onDelDialog:function(e){var t=e.attr("templateId"),a=e.attr("tplName");return this.createOperate(function(e){e.delDialog(t,a)}),!1},destroy:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.excelTemplate&&this.excelTemplate.$destroy(),this.wordTemplate&&this.wordTemplate.$destroy(),this.printHelp&&this.printHelp.$destroy(),this.widgets=null,this.$el.off().remove()}});t.exports=n});
define("crm-setting/templatemanage/print/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="right"> <div class="right-content"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/templatemanage/service/service",["crm-modules/common/util","./template/tpl-html"],function(e,t,n){var i=e("crm-modules/common/util"),m=e("./template/tpl-html"),e=Backbone.View.extend({initialize:function(e){this.$el.html(m({PhoneNum:"18211048931"}))},events:{"click .j-save":"_onSave"},_onSave:function(){i.remind($t("保存成功"))},destroy:function(){}});n.exports=e});
define("crm-setting/templatemanage/service/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("crm.请设置客服电话")) == null ? "" : __t) + '</li> </ul> </div> <div class="crm-g-form" style="padding-top: 35px;"> <div class="fm-item"> <label class="fm-lb" style="width: 70px;">' + ((__t = $t("服务热线")) == null ? "" : __t) + '：</label> <input type="text" class="b-g-ipt fm-ipt" value="' + ((__t = PhoneNum) == null ? "" : __t) + '" style="width: 200px;"/> </div> <div style="padding-left: 80px;"> <span class="b-g-btn j-save" style="padding: 0 25px;">' + ((__t = $t("保存")) == null ? "" : __t) + "</span> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/templatemanage/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("对象模板管理")) == null ? "" : __t) + '<a class="crm-doclink" href="https://help.fxiaoke.com/dbde/ef25" target="_blank"></a></span></h2> </div> <div class="crm-module-con"> <div class="crm-tab b-g-clear"> <span class="cur" data-type="print">' + ((__t = $t("打印模板")) == null ? "" : __t) + ' <a class="el-icon-question vui-tooltip-question help-link" href="https://help.fxiaoke.com/dbde/ef25/e552/cebd" target="_blank"></a> </span> <span data-type="email">' + ((__t = $t("邮件模板")) == null ? "" : __t) + ' <a class="el-icon-question vui-tooltip-question help-link" href="https://help.fxiaoke.com/dbde/ef25/c45b/5b4a" target="_blank"></a> </span> </div> <div class="tab-con"> <div class="view-item print-box"></div> <div class="view-item email-box b-g-hide"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/templatemanage/templatemanage",["crm-modules/common/util","./print/print","./email/email","./template/tpl-html"],function(e,t,i){e("crm-modules/common/util");var a=e("./print/print"),n=e("./email/email"),r=e("./template/tpl-html"),l=Backbone.Model.extend({defaults:{printObjSelectList:[],emailObjSelectList:[]}}),e=Backbone.View.extend({apiName:"SalesOrderObj",initialize:function(e){this.setElement(e.wrapper),this.model=new l},render:function(){var e=this;e.pageType="print",e.$el.html(r({})),e.triggerDefaultActiveTab()},triggerDefaultActiveTab:function(){var e=Array.from(this.$(".crm-tab span")),t=e[0],i=CRM.util.getUrlParam(location.search,"assignTabType")||"",e=i&&e.find(function(e){return e.dataset&&e.dataset.type===i});$(e||t).trigger("click")},switchPage:function(){var e=this.pageType.replace(/(\w)/,function(e){return"_render"+e.toUpperCase()});this[e]()},_renderPrint:function(e){var t=this;t._print||(t._print=new a({el:t.$el.find(".print-box")})),t._print.show()},_renderEmail:function(e){var t=this;t._email||(t._email=new n({el:t.$el.find(".email-box")})),t._email.show()},events:{"click .crm-tab span":"_onTab","click .left":"_checkTplType"},_checkTplType:function(e){var t=$(e.target),e=$(e.currentTarget).attr("type");t.hasClass("tpl-checked")||(t.toggleClass("tpl-checked",!t.hasClass("tpl-checked")).siblings().removeClass("tpl-checked"),this["_render"+e]())},_onTab:function(e){var t=this,e=$(e.currentTarget),i=e.index();e.addClass("cur").siblings().removeClass("cur"),t.$(".tab-con .view-item").eq(i).show().siblings().hide(),t.pageType=e.attr("data-type"),t.switchPage()},destroy:function(){var t=this;_.each(["_print","_email"],function(e){t[e]&&t[e].destroy()})}});i.exports=e});