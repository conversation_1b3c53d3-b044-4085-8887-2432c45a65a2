define("crm-setting/interactive-assistant-agent/interactive-assistant-agent",["crm-modules/common/util","./template/index-html"],function(i,e,t){var n=i("crm-modules/common/util"),r=i("./template/index-html"),a=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},config:{page1:{id:"customerInteractionAgent",wrapper:".recording-retention-rules-box",path:"./recordingRetentionRules/recordingRetentionRules"},page2:{id:"selfprompt",wrapper:".self-prompt",path:"./selfprompt/selfprompt"},page3:{id:"recordSetting",wrapper:".recording-setting",path:"./recordingSetting/recordingSetting"}},events:{"click .crm-tab a":"onHandle"},render:function(e){var t=n.getTplQueryParams(),t=_.values(t);e=e||[t[1]],this.pages={},this.$el.html(r()),this.renderPage()},onHandle:function(e){var n=$(e.target),r=this;e.preventDefault(),_.some(this.config,function(e,t){if(n.hasClass(t))return r.switchPage([t]),!0})},switchPage:function(e){this.renderPage(e)},renderPage:function(e){var t,n=this,r=n.$(".crm-tab .item"),r=(r.removeClass("cur"),(e&&e[0]?r.filter("."+e[0]):r.eq(0)).addClass("cur"),n.curId=e&&e[0]?e[0]:"page1");_.map(n.pages,function(e){e.hide()}),n.pages[r]?n.pages[r].show():(t=r,i.async(n.config[t].path,function(e){e=new e(_.extend({wrapper:n.config[t].wrapper}));n.curId===t&&e.show(),n.pages[t]=e}))},destroy:function(){_.map(this.pages,function(e){e.destroy&&e.destroy()}),this.pages=this.curId=null}});t.exports=a});
function asyncGeneratorStep(e,t,n,r,i,a,o){try{var c=e[a](o),s=c.value}catch(e){return void n(e)}c.done?t(s):Promise.resolve(s).then(r,i)}function _asyncToGenerator(c){return function(){var e=this,o=arguments;return new Promise(function(t,n){var r=c.apply(e,o);function i(e){asyncGeneratorStep(r,t,n,i,a,"next",e)}function a(e){asyncGeneratorStep(r,t,n,i,a,"throw",e)}i(void 0)})}}define("crm-setting/interactive-assistant-agent/recordingRetentionRules/recordingRetentionRules",[],function(e,t,n){var r,i=Backbone.View.extend({config:{dont_save_real_time_record_audio:!1},initialize:(r=_asyncToGenerator(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return this.setElement(t.wrapper),e.next=3,this.getConfig();case 3:this.render(t);case 4:case"end":return e.stop()}},e,this)})),function(e){return r.apply(this,arguments)}),render:function(e){this.renderCom()},renderCom:function(){var e=this;FxUI.create({wrapper:this.$el[0],template:'\n                   <div class="interactive-assistant-agent-wrapper">\n                         <fx-alert\n                            class="alert"\n                            type="info"\n                            show-icon\n                            title=\''.concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_title"),'\'\n                            :closable="false"\n                        >\n                            <p>').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_tip1"),"</p>\n                            <p>").concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_tip2"),"</p>\n                            <p>").concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_tip3"),'</p>\n                        </fx-alert>\n                        <div class="form-wrapper">\n                            <fx-checkbox\n                                size="mini"\n                                v-model="checked"\n                                label=\'').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_checkbox"),'\'\n                            ></fx-checkbox>\n                            <br />\n                            <fx-button size="mini" type="primary" @click="save">').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_button"),"</fx-button>\n                        </div>\n                    </div>\n                "),data:function(){return{checked:e.config.dont_save_real_time_record_audio}},methods:{save:function(){CRM.util.showLoading();CRM.util.setConfigValue({key:"dont_save_real_time_record_audio",value:this.checked?"1":"0"}).then(function(){CRM.util.remind(1,$t("设置成功"))}),CRM.util.hideLoading()}}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},getConfig:function(){var n=this;return new Promise(function(t,e){CRM.util.getConfigValue("dont_save_real_time_record_audio").then(function(e){n.config.dont_save_real_time_record_audio="1"===e,t()})})}});n.exports=i});
function asyncGeneratorStep(t,e,n,i,r,a,o){try{var c=t[a](o),s=c.value}catch(t){return void n(t)}c.done?e(s):Promise.resolve(s).then(i,r)}function _asyncToGenerator(c){return function(){var t=this,o=arguments;return new Promise(function(e,n){var i=c.apply(t,o);function r(t){asyncGeneratorStep(i,e,n,r,a,"next",t)}function a(t){asyncGeneratorStep(i,e,n,r,a,"throw",t)}r(void 0)})}}define("crm-setting/interactive-assistant-agent/recordingSetting/recordingSetting",["crm-modules/common/util"],function(t,e,n){var i,r=t("crm-modules/common/util"),t=Backbone.View.extend({config:{sfa_activity_record_audio_layout:!1},initialize:(i=_asyncToGenerator(regeneratorRuntime.mark(function t(e){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return this.setElement(e.wrapper),t.next=3,this.getConfig();case 3:this.render(e);case 4:case"end":return t.stop()}},t,this)})),function(t){return i.apply(this,arguments)}),render:function(t){this.renderComps()},renderComps:function(){var t=this;FxUI.create({wrapper:this.$el[0],template:'\n                 <div class="interactive-assistant-agent-wrapper">\n                       <fx-alert\n                          class="alert"\n                          type="info"\n                          show-icon\n                          title=\''.concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_title"),'\'\n                          :closable="false"\n                      >\n                          <p>').concat($t("sfa.activity.crmmanage.recording_setting_tip1"),"</p>\n                          <p>").concat($t("sfa.activity.crmmanage.recording_setting_tip2"),'</p>\n                      </fx-alert>\n                      <div class="form-wrapper">\n                          <fx-checkbox\n                              size="mini"\n                              v-model="checked"\n                              label=\'').concat($t("sfa.activity.crmmanage.recording_setting_checkbox"),'\'\n                          ></fx-checkbox>\n                          <br />\n                          <fx-button size="mini" type="primary" @click="save">').concat($t("sfa.activity.crmmanage.recording_retention_rules_alert_button"),"</fx-button>\n                      </div>\n                  </div>\n              "),data:function(){return{checked:t.config.sfa_activity_record_audio_layout}},methods:{save:function(){r.showLoading();r.setConfigValue({key:"sfa_activity_record_audio_layout",value:this.checked?"1":"0"}).then(function(){r.remind(1,$t("设置成功"))}),r.hideLoading()}}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},getConfig:function(){var n=this;return new Promise(function(e,t){r.getConfigValue("sfa_activity_record_audio_layout").then(function(t){n.config.sfa_activity_record_audio_layout="1"===t,e()})})}});n.exports=t});
function asyncGeneratorStep(t,e,n,a,i,r,o){try{var s=t[r](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(a,i)}function _asyncToGenerator(s){return function(){var t=this,o=arguments;return new Promise(function(e,n){var a=s.apply(t,o);function i(t){asyncGeneratorStep(a,e,n,i,r,"next",t)}function r(t){asyncGeneratorStep(a,e,n,i,r,"throw",t)}i(void 0)})}}define("crm-setting/interactive-assistant-agent/selfprompt/selfprompt",["crm-modules/page/list/list"],function(t,e,n){var a,s=t("crm-modules/page/list/list"),t=Backbone.View.extend({initialize:(a=_asyncToGenerator(regeneratorRuntime.mark(function t(e){return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:this.setElement(e.wrapper),this.render(e);case 2:case"end":return t.stop()}},t,this)})),function(t){return a.apply(this,arguments)}),render:function(t){this.renderList()},renderList:function(){var t=this,e=s.extend({getOptions:function(){var t=s.prototype.getOptions.apply(this,arguments);return _.extend(t,{autoHeight:!0,maxHeight:$(".crm-s-interactive-assistant-agent").height()-450})},parseData:function(t){var e=s.prototype.parseData.apply(this,arguments);return _.each(e.data,function(e){_.each(e.operate,function(t){"ChangeStatus"==t.action&&(t.label="disable"==e.prompt_status?$t("启用"):$t("停用")),t.render_type="not_fold"})}),e},parseColumns:function(t,r){var e=s.prototype.parseColumns.apply(this,arguments);return e.map(function(t){return"operate"==t.dataType&&(t.render=function(t,e,n){return t.replace('class="tr-operate-btn-item tr-operate- crm-ui-title"','class="tr-operate-btn-item tr-operate- crm-ui-title" style="color: #1890ff;"')}),"rule"==t.api_name&&(t.render=function(t,e,n){var a,n=(n=n.rule?null==(n=JSON.parse(n.rule))||null==(n=n.filter[0])?void 0:n.field_values:[])||[],i=(null==r||null==(a=r.interactive_scenario)?void 0:a.options)||[];return n.map(function(e){var t=i.find(function(t){return t.value===e});return t?t.label:e}).join(",")||"--"}),"subtemplate_api_name"==t.api_name&&(t.render=function(t,e,n){n=n.subtemplate_api_name?null==(n=JSON.parse(n.subtemplate_api_name))?void 0:n.promptName:"--";return'<span style="color: #1890ff;" data-action="showTemplateDetailEdit">'.concat(n,"</span>")}),t}),e},trclickHandle:function(t,e,n,a,i,r){"showTemplateDetailEdit"===n.data("action")?this[n.data("action")](t):s.prototype.trclickHandle.apply(this,arguments)},showTemplateDetailEdit:function(t){t=t.subtemplate_api_name?null==(t=JSON.parse(t.subtemplate_api_name))?void 0:t.promptApiName:"";t&&Fx.getBizAction("paasdev","openPromptFormDialog",{status:"update",apiName:t})},completeRender:function(){this.table&&this.table.hideLoading()},operateBtnClickHandle:function(t){var e,n=this,t=$(t.target),a=t.data("action"),i=t.closest(".tr").data("index"),r=n.getCurData().data,o=r&&r[i],r=t.closest(".tr").find("[data-action=ChangeStatus]").text();"ChangeStatus"==a?e=CRM.util.confirm($t("确认{{changestatus}}这个订阅规则项目？",{changestatus:r}),null,function(){e.destroy(),n.changeEnable(o._id,"disable"==o.prompt_status)},{hideFn:function(){},stopPropagation:!0}):s.prototype.operateBtnClickHandle.apply(this,arguments)},changeEnable:function(t,e){var n=this;CRM.util.waiting(),CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/AgentPromptVersionObj/action/ChangeStatus",data:{objectIds:[t],isEnabled:e},success:function(t){0==t.Result.StatusCode?(CRM.util.waiting(!1),CRM.util.remind(1,$t("操作成功"))):(CRM.util.waiting(!1),CRM.util.remindFail(t.Result.FailureMessage)),n.table.refresh()},error:function(){CRM.util.waiting(!1),n.table.refresh()}},{errorAlertModel:1})}});t.list=new e({wrapper:t.$el.find(".list-view"),apiname:"AgentPromptVersionObj"}),t.list.render()},show:function(){this.$el.show()},hide:function(){this.$el.hide()}});n.exports=t});
define("crm-setting/interactive-assistant-agent/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit crm-manageclue-title"> <h2><span class="tit-txt">' + ((__t = $t("sfa.activity.crmmanage.customer_interaction_agent_title")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-tab"> <a class="item page1" href="javascript:void(0)">' + ((__t = $t("sfa.activity.crmmanage.recording_retention_rules_tab_item")) == null ? "" : __t) + '</a> <a class="item page2" href="javascript:void(0)">' + ((__t = $t("sfa.activity.prompt.self_prompt_tab_item")) == null ? "" : __t) + '</a> <a class="item page3" href="javascript:void(0)">' + ((__t = $t("sfa.activity.crmmanage.recording_setting_tab_item")) == null ? "" : __t) + '</a> </div> <div class="tab-con"> <div class="crm-p20 crm-scroll"> <div class="item recording-retention-rules-box" style="display:none;"> <!-- <div class="crm-loading"></div> --> </div> <div class="item self-prompt" style="display:none;"> <!-- <div class="crm-loading"></div> --> <div class="crm-set-system crm-set-bid"> <div class="crm-intro"> <h3>' + ((__t = $t("功能说明")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("sfa.activity.prompt.self_prompt_desc")) == null ? "" : __t) + "</p> <h3>" + ((__t = $t("注意事项")) == null ? "" : __t) + "：</h3> <p>" + ((__t = $t("sfa.activity.prompt.self_prompt_desc_notice_first")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("sfa.activity.prompt.self_prompt_desc_notice_second")) == null ? "" : __t) + '</p> </div> <div class="list-view"> <div class="crm-loading"></div> </div> </div> </div> <div class="item recording-setting" style="display:none;"> </div> </div> </div> </div>';
        }
        return __p;
    };
});