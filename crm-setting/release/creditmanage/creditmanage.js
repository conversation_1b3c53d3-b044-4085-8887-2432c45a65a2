define("crm-setting/creditmanage/components/main",[],function(t,e,n){var a=FS.crmUtil;n.exports={template:'\n\t\t<div class="creditmanage-wrap">\n\t\t\t<div class="crm-intro">\n\t\t\t\t<h3>{{$t("说明")}}：</h3>\n\t\t\t\t<ul>\n\t\t\t\t\t<li>1. {{$t("crm.creditmanage.信用说明1")}}</li>\n\t\t\t\t\t<li>2. {{$t("crm.creditmanage.信用说明2")}}</li>\n\t\t\t\t\t<li>3. {{$t("crm.creditmanage.信用说明3")}}</li>\n\t\t\t\t\t<li>4. {{$t("crm.creditmanage.信用说明4")}}</li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t\t<div class="creditmanage-content">\n\t\t\t\t<div class="column-item">\n\t\t\t\t\t<label class="label">{{$t("启用信用")}}</label>\n\t\t\t\t\t<fx-switch\n\t\t\t\t\t\tv-model="config.credit_enable"\n\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t:disabled="config.credit_enable"\n\t\t\t\t\t\t:before-change="beforeChangeHandle"\n\t\t\t\t\t>\n\t\t\t\t\t</fx-switch>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>',name:"Main",data:function(){return{config:{credit_enable:null}}},created:function(){this.fetchConfig()},methods:{fetchConfig:function(){var n=this;a.waiting(),a.FHHApi({url:"/EM1HNCRM/API/v1/object/crm_config/service/get_config_values",data:{keys:["is_credit_management_enable"]},success:function(t){var e=t.Result;0==e.StatusCode?t.Value.values.forEach(function(t){"is_credit_management_enable"===t.key&&(n.config.credit_enable="2"===t.value)}):a.alert(e.FailureMessage||e.message||$t("设置失败请联系纷享客服")),a.waiting(!1)}},{errorAlertModel:1})},fetchUpdate:function(){var e=this;a.waiting(),a.FHHApi({url:"/EM1HNCRM/API/v1/object/crm_config/service/set_config_value",data:{key:"is_credit_management_enable",value:"2"},success:function(t){t=t.Result;0==t.StatusCode?(e.config.credit_enable=!0,e.$message({type:"success",message:$t("操作成功")})):a.alert(t.FailureMessage||t.message||$t("设置失败请联系纷享客服")),a.waiting(!1)}},{errorAlertModel:1})},beforeChangeHandle:function(){var t=this;this.$confirm($t("crm.creditmanage.确定启用"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){return t.fetchUpdate(),!0}).catch(function(){return!1})}}}});
function _typeof(t){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,_toPropertyKey(r.key),r)}}function _createClass(t,e,n){return e&&_defineProperties(t.prototype,e),n&&_defineProperties(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}function _toPropertyKey(t){t=_toPrimitive(t,"string");return"symbol"==_typeof(t)?t:t+""}function _toPrimitive(t,e){if("object"!=_typeof(t)||!t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/creditmanage/creditmanage",["./components/main"],function(t,e,n){var r=t("./components/main"),o=Vue.extend({template:'\n\t\t\t<div>\n \t\t\t\t<div class="crm-tit">\n\t\t\t\t\t<h2>\n\t\t\t\t\t\t<span class="tit-txt">\n\t\t\t\t\t\t\t{{$t("management.credit_manage")}}\n\t\t\t\t\t\t</span>\n\t\t\t\t\t</h2>\n\t\t\t\t</div>\n\t        \t<div class="crm-module-con">\n\t\t\t\t\t<Main></Main>\n\t\t\t\t</div>\n\t\t\t</div>'});n.exports=_createClass(function t(e){_classCallCheck(this,t),this.app=new o({name:"App",el:e.wrapper.append("<div></div>").children()[0],components:{Main:r}})},[{key:"destroy",value:function(){this.app.$destroy()}}])});