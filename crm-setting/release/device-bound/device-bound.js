define("crm-setting/device-bound/device-bound",["./template/index-html"],function(n,e,i){var a=n("./template/index-html");i.exports=Backbone.View.extend({initialize:function(e){var i=this;i.setElement(e.wrapper),i.el.innerHTML=a(),i.$(".crm-tab .item").eq(0).click()},events:{"click .crm-tab a":"onTab"},onTab:function(e){var e=$(e.target),i=(i=e.attr("href")).split("/=/");return this._render([i[1]],e.index()),!1},_render:function(e,i){var a=this,t=a.$(".crm-tab .item");switch(t.removeClass("cur"),t.eq(i).addClass("cur"),a.page1&&a.page1.hide(),a.page2&&a.page2.hide(),a.page3&&a.page3.hide(),a.page4&&a.page4.hide(),e&&e[0]){case"page2":n.async("./pc-device/index.js",function(e){a.page2=new e({el:a.$(".pc-device")}),a.page2.show()});break;case"page3":n.async("./white-list/index",function(e){a.page3=new e({el:a.$(".white-list")}),a.page3.show()});break;case"page4":n.async("./record-list/index",function(e){a.page4=new e({el:a.$(".record-list")}),a.page4.show()});break;default:n.async("./mobile-device/index",function(e){a.page1=new e({el:a.$(".mobile-device")}),a.page1.show()})}},destroy:function(){_.each(["page1","page2","page3","page4"],function(e){this[e]&&this[e].destroy&&this[e].destroy(),this[e]&&(this[e]=null)},this)}})});
define("crm-setting/device-bound/mobile-device/index",["crm-modules/common/util","./template/list-view-html","../pc-device/table-list"],function(e,t,i){var n=e("crm-modules/common/util"),s=e("./template/list-view-html"),a=e("../pc-device/table-list");i.exports=Backbone.View.extend({events:{},initialize:function(){this.el.innerHTML=s(),this.$(".switch-wrapper-mask").hide(),this.$(".table-wrap").hide(),this.getEnterpriseConfig(),this.initSwitch()},initSwitch:function(){var n=this;this.switch=FxUI.create({replaceWrapper:!0,wrapper:this.$(".fx-switch")[0],template:'<fx-switch v-model="value" size="small" :beforeChange="beforeChange" @change="onChange"></fx-switch>',data:function(){return{value:!1}},methods:{beforeChange:function(){var i=this.value;return new Promise(function(e,t){i?(n.$el.find(".switch-status-txt > span").text($t("已停用")),n.setDeviceBindConfig(0),n.$(".table-wrap").hide(),e()):setTimeout(function(){FxUI.MessageBox.confirm($t("开启此功能，非绑定状态的员工将会被强制登出，确认操作吗？")).then(function(){n.$el.find(".switch-status-txt > span").text($t("已启用")),n.setDeviceBindConfig(1),n.$(".table-wrap").show(),e()})},200)})},onChange:function(e){}}})},getEnterpriseConfig:function(){var t=this;n.FHHApi({url:"/EM2HORG/Management/Enterprise/GetEnterpriseConfig",data:{key:"appDeviceBindStatus"},success:function(e){0===e.Result.StatusCode&&(e=e.Value.value,t.switch.value=0!=e,t.$el.find(".switch-status-txt >span").text(0==e?$t("已停用"):$t("已启用")),1==e)&&(t.$(".table-wrap").show(),t.tableList=new a({$el:t.$(".table-wrap"),type:"mobile"}))}})},setDeviceBindConfig:function(t){var i=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/DeviceBindObj/service/SetDeviceBindConfig",data:{key:"appDeviceBindStatus",value:t},success:function(e){0===e.Result.StatusCode&&1==t&&(i.tableList?i.tableList.refresh():i.tableList=new a({$el:i.$(".table-wrap"),type:"mobile"}))}})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.undelegateEvents(),this.tableList&&(this.tableList.destroy(),this.tableList=null)}})});
define("crm-setting/device-bound/mobile-device/template/list-view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("移动设备绑定功能")) == null ? "" : __t) + '</h3> <span class="icon icon-mobile"></span> <ol> <li>1. ' + ((__t = $t("开启此功能，员工首次登录的移动设备将会自动与登录账户进行绑定")) == null ? "" : __t) + "</li> <li>2. " + ((__t = $t("解除绑定后，员工可与任意一台移动设备进行重新绑定")) == null ? "" : __t) + "</li> <li>3. " + ((__t = $t("添加至设备绑定白名单的员工，可在任意移动设备登录系统")) == null ? "" : __t) + '</li> </ol> <div class="switch-box"> <span class="switch-status-txt">' + ((__t = $t("状态")) == null ? "" : __t) + ": <span>" + ((__t = $t("已停用")) == null ? "" : __t) + '</span> </span> <div class="switch-wrapper"> <div class="switch-wrapper-mask"></div> <div class="fx-switch"></div> </div> </div> </div> <div class="table-wrap"></div>';
        }
        return __p;
    };
});
define("crm-setting/device-bound/pc-device/index",["crm-modules/common/util","./template/list-view-html","./table-list"],function(e,t,i){var n=e("crm-modules/common/util"),s=e("./template/list-view-html"),a=e("./table-list");i.exports=Backbone.View.extend({events:{},initialize:function(){this.el.innerHTML=s(),this.$(".switch-wrapper-mask").hide(),this.$(".table-wrap").hide(),this.getEnterpriseConfig(),this.initSwitch()},initSwitch:function(){var n=this;this.switch=FxUI.create({replaceWrapper:!0,wrapper:this.$(".fx-switch")[0],template:'<fx-switch v-model="value" size="small" :beforeChange="beforeChange" @change="onChange"></fx-switch>',data:function(){return{value:!1}},methods:{beforeChange:function(){var i=this.value;return new Promise(function(e,t){i?(n.$el.find(".switch-status-txt > span").text($t("已停用")),n.setDeviceBindConfig(0),n.$(".table-wrap").hide(),e()):setTimeout(function(){FxUI.MessageBox.confirm($t("开启此功能，非绑定状态的员工将会被强制登出，确认操作吗？")).then(function(){n.$el.find(".switch-status-txt > span").text($t("已启用")),n.setDeviceBindConfig(1),n.$(".table-wrap").show(),e()})},200)})},onChange:function(e){}}})},getEnterpriseConfig:function(){var t=this;n.FHHApi({url:"/EM2HORG/Management/Enterprise/GetEnterpriseConfig",data:{key:"webDeviceBindStatus"},success:function(e){0===e.Result.StatusCode&&(e=e.Value.value,t.switch.value=0!=e,t.$el.find(".switch-status-txt>span").text(0==e?$t("已停用"):$t("已启用")),1==e)&&(t.$(".table-wrap").show(),t.tableList=new a({$el:t.$(".table-wrap"),type:"pc"}))}})},setDeviceBindConfig:function(t){var i=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/DeviceBindObj/service/SetDeviceBindConfig",data:{key:"webDeviceBindStatus",value:t},success:function(e){0===e.Result.StatusCode&&1==t&&(i.tableList?i.tableList.refresh():i.tableList=new a({$el:i.$(".table-wrap"),type:"pc"}))}})},show:function(){this.$el.show(),this.switch.value},hide:function(){this.$el.hide()},destroy:function(){this.undelegateEvents(),this.tableList&&(this.tableList.destroy(),this.tableList=null)}})});
define("crm-setting/device-bound/pc-device/table-list",["crm-modules/common/util","crm-modules/page/list/list","vue-selector-parse-v2"],function(e,t,n){var o=e("crm-modules/common/util"),i=e("crm-modules/page/list/list"),s=e("vue-selector-parse-v2"),a=i.extend({showAddDialog:function(){var a=this,e=s.parseContacts({member:!0});this.addDialog=FxUI.create({template:'<fx-dialog\n                    title="'.concat($t("新建"),'"\n                    :visible.sync="show"\n                    size = "small"\n                    append-to-body\n                  >\n                    <fx-selector-input-v2\n                      ref="selectorInputLine"\n                      :tabs="tabs"\n                      addBtnLabel="').concat($t("选择人员"),'"\n                      @change="changeHandler"\n                      @selector-mounted="onSelectorMounted"\n                    ></fx-selector-input-v2>\n\n                    <div style="color: #F56C6C;font-size: 12px;padding-top: 4px;" v-show="showErr">').concat($t("请选择人员"),'</div>\n\n                    <template slot="footer">\n                      <fx-button type="primary" size="small" @click="handleConfirm">').concat($t("确定"),'</fx-button>\n                      <fx-button size="small" @click="handleCancel">').concat($t("取消"),"</fx-button> \n                    </template>\n                  </fx-dialog>"),data:function(){return{show:!1,tabs:e.tabs,showErr:!1}},watch:{show:function(e){var t;e||(t=this).$nextTick(function(){t.destroy()})}},methods:{changeHandler:function(e,t){this.showErr=!1},handleConfirm:function(){var t=this.$refs.selectorInputLine.getValue(),n=!1;Object.keys(t).forEach(function(e){0<t[e].length&&(n=!0)}),n?o.FHHApi({url:"/EM1HNCRM/API/v1/object/DeviceBindObj/service/BatchAddWhiteList",data:{employeeIds:t.member},success:function(e){0==e.Result.StatusCode&&(a.refresh(),a.addDialog.show=!1)}},{errorAlertModel:1}):this.showErr=!0},handleCancel:function(){this.show=!1},onSelectorMounted:function(){}}}),this.addDialog.show=!0},excuteAction:function(e,t){var n=this,a=arguments;"add"==e?this.showAddDialog():"list_batchbtn_operate"==e&&("AsyncBulkRemoveBind"==t.button_action?FxUI.MessageBox.confirm($t("解除绑定之后，选中账号将可以在新的设备上登录，并在登录时与新设备绑定")).then(function(){i.prototype.excuteAction.apply(n,a)}):"AsyncBulkAddDeviceWhiteList"==t.button_action?FxUI.MessageBox.confirm($t("添加至设备绑定白名单，选中账号将可以在任意设备进行登录，确认添加吗？")).then(function(){i.prototype.excuteAction.apply(n,a)}):"AsyncBulkRemoveDeviceBind"===t.button_action&&FxUI.MessageBox.confirm($t("移除后，所有人员登录的设备会被强制登出，在新设备登录时进行绑定操作，确认删除吗？")).then(function(){i.prototype.excuteAction.apply(n,a)}))},_operateHandle:function(e,t){var n=this,a=arguments;"RemoveBind"===e.action?FxUI.MessageBox.confirm($t("解除绑定之后，【{{name}}】将可以在新的设备上登录，并在登录时与新设备绑定",{data:{name:t.ref_personnel_obj__r}})).then(function(){i.prototype._operateHandle.apply(n,a)}):"AddDeviceWhiteList"===e.action?FxUI.MessageBox.confirm($t("添加至设备绑定白名单，【{{name}}】将可以在任意设备进行登录，确认添加吗？",{data:{name:t.ref_personnel_obj__r}})).then(function(){i.prototype._operateHandle.apply(n,a)}):"RemoveDeviceBind"===e.action&&FxUI.MessageBox.confirm($t("移除后，【{{name}}】所有登录的设备会被强制登出，在新设备登录时进行绑定操作，确认删除吗？",{data:{name:t.ref_personnel_obj__r}})).then(function(){i.prototype._operateHandle.apply(n,a)})},parseParam:function(e){var t=i.prototype.parseParam.apply(this,arguments),n=this.get("type");return"pc"===n?t.search_template_id="5fe3109e10a3c8000102cda8":"mobile"===n?t.search_template_id="5fe3104810a3c8000102cd71":"whiteList"===n&&(t.search_template_id="5fe311e310a3c8000102d359"),t},trclickHandle:function(){},initComplete:function(){this.table.resize()},getColumns:function(){var e=i.prototype.getColumns.apply(this,arguments),t=_.findWhere(e,{dataType:"operate"});return t.width=170,t.render=function(){var t,n;if(!(arguments.length<3))return n=[],(t=arguments[2]).operate.forEach(function(e){n.push('<span data-id="'+t._id+'" class="operate-btn crm-ui-title j-row-operate-btn" data-maxwidth="64" data-pos="top" data-title="'+e.label+'" data-action="'+e.action+'">'+e.label+"</span>")}),'<div class="tr-operate-wrap20">'+n.join("")+"</div>"},e}});return Backbone.View.extend({apiname:"DeviceBindObj",initialize:function(e){"pc"===(this.opts=e).type||"mobile"===e.type?this.apiname="DeviceBindObj":"whiteList"===e.type&&(this.apiname="EmployeeListObj"),this.setElement(e.$el),this.initTable()},initTable:function(){var e=this;if(this.table)return this;e.table=new a({type:e.opts.type,wrapper:e.$el,apiname:e.apiname,tableOptions:{colMinWidth:160,searchTerm:null,showTagBtn:!1},showTitle:!1,showBatchBtns:!1}),e.table.render([e.apiname])},refresh:function(){this.table.table.refresh()},destroy:function(){this.table.destroy(),this.table=null}})});
define("crm-setting/device-bound/pc-device/template/list-view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("PC设备绑定功能")) == null ? "" : __t) + '</h3> <span class="icon icon-pc"></span> <ol> <li>1. ' + ((__t = $t("开启此功能，员工首次登录的PC设备将会自动与登录账户进行绑定")) == null ? "" : __t) + "</li> <li>2. " + ((__t = $t("解除绑定后，员工可与任意一台PC设备进行重新绑定")) == null ? "" : __t) + "</li> <li>3. " + ((__t = $t("添加至设备绑定白名单的员工，可在任意PC设备登录系统")) == null ? "" : __t) + '</li> </ol> <div class="switch-box"> <span class="switch-status-txt">' + ((__t = $t("状态")) == null ? "" : __t) + ": <span>" + ((__t = $t("已停用")) == null ? "" : __t) + '</span> </span> <div class="switch-wrapper"> <div class="switch-wrapper-mask"></div> <div class="fx-switch"></div> </div> </div> </div> <div class="table-wrap"></div>';
        }
        return __p;
    };
});
define("crm-setting/device-bound/record-list/index",["crm-modules/common/util","./template/list-view-html","../pc-device/table-list"],function(e,t,i){e("crm-modules/common/util");var l=e("./template/list-view-html"),s=e("../pc-device/table-list");i.exports=Backbone.View.extend({events:{},initialize:function(){this.el.innerHTML=l(),this.tableList=new s({$el:this.$(".table-wrap"),type:"recordList"})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.undelegateEvents(),this.tableList.destroy(),this.tableList=null}})});
define("crm-setting/device-bound/record-list/template/list-view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="table-wrap"></div>';
        }
        return __p;
    };
});
define("crm-setting/device-bound/template/index-html", [ "crm-setting/common/loading/loading" ], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            var loadTpl = require("crm-setting/common/loading/loading");
            __p += ' <div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("员工设备管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con"> <div class="crm-tab"> <a class="item" href="#crm/setting/device-bound/=/page1">' + ((__t = $t("移动设备绑定")) == null ? "" : __t) + '</a> <a class="item" href="#crm/setting/device-bound/=/page2">' + ((__t = $t("PC设备绑定")) == null ? "" : __t) + '</a> <a class="item" href="#crm/setting/device-bound/=/page3">' + ((__t = $t("设备绑定白名单")) == null ? "" : __t) + '</a> <!-- <a class="item" href="#crm/setting/device-bound/=/page4">' + ((__t = $t("授权记录查询")) == null ? "" : __t) + '</a> --> </div> <div class="content mobile-device" style="display:none;"> <div class="crm-loading"></div> </div> <div class="content pc-device" style="display:none;"> <div class="crm-loading"></div> </div> <div class="content white-list" style="display:none;"> <div class="crm-loading"></div> </div> <div class="content record-list" style="display:none;"> <div class="crm-loading"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/device-bound/white-list/index",["crm-modules/common/util","./template/list-view-html","../pc-device/table-list"],function(t,e,i){t("crm-modules/common/util");var s=t("./template/list-view-html"),l=t("../pc-device/table-list");i.exports=Backbone.View.extend({events:{},initialize:function(){this.el.innerHTML=s(),this.tableList=new l({$el:this.$(".table-wrap"),type:"whiteList"})},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.undelegateEvents(),this.tableList&&(this.tableList.destroy(),this.tableList=null)}})});
define("crm-setting/device-bound/white-list/template/list-view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="table-wrap"></div>';
        }
        return __p;
    };
});