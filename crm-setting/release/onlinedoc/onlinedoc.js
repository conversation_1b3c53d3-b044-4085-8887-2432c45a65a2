function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function ownKeys(n,e){var t,i=Object.keys(n);return Object.getOwnPropertySymbols&&(t=Object.getOwnPropertySymbols(n),e&&(t=t.filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})),i.push.apply(i,t)),i}function _objectSpread(n){for(var e=1;e<arguments.length;e++){var t=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(t),!0).forEach(function(e){_defineProperty(n,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(n,Object.getOwnPropertyDescriptors(t)):ownKeys(Object(t)).forEach(function(e){Object.defineProperty(n,e,Object.getOwnPropertyDescriptor(t,e))})}return n}function _defineProperty(e,n,t){return(n=_toPropertyKey(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,n){if("object"!=_typeof(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0===t)return("string"===n?String:Number)(e);t=t.call(e,n||"default");if("object"!=_typeof(t))return t;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/onlinedoc/api",[],function(e,n,t){function i(e,i){return new Promise(function(n,t){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/packagePlugin/service/".concat(i),data:e,success:function(e){(0===e.Result.StatusCode?n:t)(e)}},{errorAlertModel:1})})}t.exports={fetchAllDocList:function(){return new Promise(function(n){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/packagePlugin/service/getAllPlugin",data:{pageNumber:0,pageSize:200,appType:"OnlineDoc"},success:function(e){0===e.Result.StatusCode?n(_.map(e.Value.pluginList,function(e){return _objectSpread(_objectSpread({},e),{},{functionApiName:e.extraInfo&&e.extraInfo.function_api_name,pwcApiName:e.extraInfo&&e.extraInfo.pwc_api_name,name:e.pluginName,isActive:e.active?1:0,isBinding:e.binding?1:0})})):FxUI.Message({message:e.Result.FailureMessage,type:"error"})}},{errorAlertModel:1})})},updateAccount:function(e){return i(e,"updatePlugin")},createAccount:function(e){return e.appType="OnlineDoc",e.extraInfo={pwc_api_name:e.pwcApiName,function_api_name:e.functionApiName},i(e,"createPlugin")},bindAccount:function(e){return i(e,"bindingCompanyDevInfo")},unbindAccount:function(e){return i(e,"unbindingCompanyDevInfo")},disableDoc:function(e){return new Promise(function(n,t){i({pluginApiName:e.pluginApiName},"disablePlugin").then(function(e){e.Value&&e.Value.success?n():t(e)}).catch(t)})},enableDoc:function(e){try{window.logger.action({eventId:"s-paasobj_"+e.pluginApiName+"_enable"})}catch(e){}return new Promise(function(n,t){i({pluginApiName:e.pluginApiName},"enablePlugin").then(function(e){e.Value&&e.Value.success?n():t(e)}).catch(t)})},deleteDoc:function(e){return new Promise(function(n,t){i({pluginApiName:e.pluginApiName},"deletePlugin").then(function(e){e.Value&&e.Value.success?n():t(e)}).catch(t)})}}});
define("crm-setting/onlinedoc/constant",[],function(s,e,n){n.exports={DocStatus:{disable:0,enable:1},BindingStatus:{unbound:0,bound:1},DefineType:{custom:"custom",system:"system",systemPersonal:"system_personal"}}});
define("crm-setting/onlinedoc/generator",["./api"],function(n,e,o){function a(e){return FxUI.create({wrapper:(n=document.createElement("div"),document.body.appendChild(n),n),template:'\n        <fx-dialog v-loading="dLoading" :visible.sync="visible" :close-on-click-modal="false" @closed="$emit(\'closed\')" size="small" :title="title">\n          <fx-form class="online-doc__generator" label-position="top" ref="form" :model="docInfo" :append-to-body="true" :close-on-press-escape="false">\n            <div>\n              <fx-input required size="small" v-model="docInfo.name">\n                <span slot="label">'.concat($t("crm.onlinedoc.name"),'</span>\n              </fx-input>\n              <p v-show="!docInfo.name && isXX" class="online-error">').concat($t("请填写{{label}}",{label:$t("crm.onlinedoc.name")}),'</p>\n            </div>\n            <fx-form-item required size="small" label="').concat($t("crm.onlinedoc.icon"),'">\n              <fx-upload\n                ref="upload"\n                class="logo-uploader"\n                url="/FSC/EM/File/UploadByForm?needCdn=true&needThumbnail=true&business=customloginpage"\n                :rules="name"\n                :limit="1"\n                accept=".jpeg,.jpg,.png" \n                :max-size="1024 * 1024 * 1024"\n                :show-file-list="false"\n                :before-upload="handleBefore"\n                :on-success="handleUploadSuccess"\n                :on-error="handleUploadError"\n                :on-change="handleUploadChange"\n              >\n                <div v-if="docInfo.icon" v-loading="uploading" fx-loading-spinner="el-icon-loading" class="logo">\n                  <img :src="lpis"/>\n                  <span class="i-mask"></span>\n                  <span @click.stop class="i-delete"><span @click.stop="handleRemove" class="fx-icon-delete"></span></span>\n                </div>\n                <i v-else v-loading="uploading" fx-loading-spinner="el-icon-loading" class="el-icon-plus logo-uploader-icon"></i>\n                <p slot="tip" class="el-upload__tip">').concat($t("crm.onlinedoc.uploadtip"),'</p>\n              </fx-upload>\n              <p v-show="!docInfo.icon && isXX" style="transform:translateY(-8px)" class="online-error">').concat($t("请上传图片"),'</p>\n            </fx-form-item>\n            <div>\n              <fx-input required :disabled="isEdit" ref="apiname" size="small" v-model="docInfo.pluginApiName">\n                <span slot="label">Api Name</span>\n              </fx-input>\n              <p v-show="!docInfo.pluginApiName && isXX" class="online-error">').concat($t("请填写{{label}}",{label:"Api Name"}),'</p>\n            </div>\n            <div>\n              <fx-select\n                  required\n                  label="').concat($t("crm.onlinedoc.pwc"),'"\n                  style="width:432px"\n                  v-model="docInfo.pwcApiName"\n                  :options="pwcOptions"\n                  :disabled="!pwcOptions.length"\n                  size="small"\n              />\n              <p v-show="!docInfo.pwcApiName && isXX" class="online-error">').concat($t("请填写{{label}}",{label:$t("crm.onlinedoc.pwc")}),'</p>\n            </div>\n            <fx-form-item required size="small" label="').concat($t("函数"),'">\n              <fx-button v-if="!docInfo.functionApiName" @click="handleFun" type="text">{{cFunName}}</fx-button>\n              <fx-input v-else readonly size="small" :value="docInfo.functionApiName">\n                <i slot="suffix">\n                  <i @click="handleUpdateFun" class="el-icon-edit el-input__icon"></i>\n                  <i @click="docInfo.functionApiName = \'\'" class="el-icon-delete el-input__icon"></i>\n                </i>\n              </fx-input>\n              <p v-show="!docInfo.functionApiName && isXX" class="online-error">').concat($t("请填写{{label}}",{label:$t("函数")}),'</p>\n            </fx-form-item>\n          </fx-form>\n          <span slot="footer" class="dialog-footer">\n            <fx-button type="primary" size="small" @click="submit">{{$t(\'确 定\')}}</fx-button>\n            <fx-button size="small" @click="cancel">{{$t(\'取 消\')}}</fx-button>\n          </span>\n        </fx-dialog>\n      '),data:function(){var n;return{visible:!0,uploading:!1,dLoading:!1,pwcOptions:[],isXX:!1,isEdit:!!e.data.name,title:e.data.name?$t("编辑"):$t("新建"),docInfo:{name:null==(n=e.data)?void 0:n.name,icon:null==(n=e.data)?void 0:n.icon,pluginApiName:null==(n=e.data)?void 0:n.pluginApiName,pwcApiName:null==(n=e.data)?void 0:n.pwcApiName,functionApiName:null==(n=e.data)?void 0:n.functionApiName}}},mounted:function(){var e=this;this.fetchPWCPlugins().then(function(n){return e.pwcOptions=_.map(n,function(n){return{value:n.apiName,label:n.name}})})},computed:{cFunName:function(){return this.docInfo.functionApiName||$t("执行自定义函数")},lpis:function(){return this.docInfo.icon&&this.ctpi(this.docInfo.icon)}},methods:{ctpi:function(n){return/^TN|TC/.test(n)?FS.util.getFscTempFileViewUrl(n):e.data.iconUrl},handleBefore:function(){if(this.docInfo.icon)return!1},handleRemove:function(){this.docInfo.icon="",this.$refs.upload.clearFiles()},handleUploadChange:function(n){null!=n&&n.response&&"success"===n.status?this.uploading=!1:this.uploading=!0},handleUploadError:function(){this.uploading=!1},handleUploadSuccess:function(n){this.uploading=!1,this.docInfo.icon="".concat(n.TempFileName,".").concat(n.FileExtension)},handleUpdateFun:function(){var o=this;this.dLoading=!0,seajs.use("paas-function/sdk.js",function(n){o.dLoading=!1,n.update({api_name:o.docInfo.functionApiName,object_api_name:"NONE",return_type:"",type:"class"},function(n){var e=n.data;n.status&&(o.docInfo.functionApiName=e.function.api_name,o.docInfo.functionName=e.function.function_name)})})},handleFun:function(){var o=this;this.dLoading=!0,seajs.use("paas-function/sdk.js",function(n){o.dLoading=!1,n.getFunction({name_space:"online_doc",object_api_name:"NONE",return_type:"",type:"class",data:o.docInfo},function(n){var e=n.data;n.status&&(o.docInfo.functionApiName=e.function.api_name,o.docInfo.functionName=e.function.function_name)})})},fetchPWCPlugins:function(){return new Promise(function(e){CRM.util.FHHApi({url:"/EM1HCompBuild/Component/queryAllComponents",success:function(n){n.Value&&n.Value.componentVOList&&e(n.Value.componentVOList)}})})},submit:function(){var e,o=this;this.isXX=!0,_.find(this.docInfo,function(n){if(!n)return e=!0}),e||(this.dLoading=!0,i[this.isEdit?"updateAccount":"createAccount"](this.docInfo).then(function(){o.visible=!1,o.$emit("success")}).catch(function(n){o.dLoading=!1,o.$message({duration:2e3,type:"error",isMiddle:!0,message:n.Result.FailureMessage})}))},cancel:function(){this.visible=!1,this.$emit("cancel")}}});var n}var i=n("./api");o.exports=function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(e,n){var o=a({data:t}),i=!1;o.$on("success",function(n){i=!0,e(n)}),o.$on("closed",function(){i||n("cancel"),o.$destroy()})})}});
define("crm-setting/onlinedoc/onlinedoc",["./api","./constant","./generator"],function(n,t,i){var o=n("./api"),c=n("./constant"),s=n("./generator"),a=c.DocStatus,d=c.BindingStatus,r=c.DefineType,n=Backbone.View.extend({initialize:function(n){this.setElement(n.wrapper)},render:function(){this.instance=FxUI.create({wrapper:this.$el.get(0),template:'\n          <div class="online-doc-setting-container" v-loading="loading">\n            <h2 class="online-doc__title">'.concat($t("paas.object.online.doc"),'</h2>\n            <div class="online-doc__content">\n              <div v-for="group in docList" :key="group.id" class="doc-group">\n                <div class="doc-group__title">{{group.title}}</div>\n                <div v-if="group.desc" class="doc-group__desc">{{group.desc}}</div>\n                <div class="doc-group__list">\n                  <div v-for="doc in group.items" :key="doc.id" class="doc-item">\n                    <div class="doc-content">\n                      <div class="doc-logo">\n                        <img class="doc-logo__image" :src="doc.iconUrl"/>\n                      </div>\n                      <div class="doc-inner">\n                        <h3 :title="doc.pluginName" class="doc-title">{{doc.pluginName}}</h3>\n                        <div class="doc-status">\n                          <fx-tag v-if="isActived(doc)" type="link" effect="light-noborder" size="mini">').concat($t("已启用"),'</fx-tag>\n                          <fx-tag v-if="isShowBound(doc)" type="success" effect="light-noborder" size="mini">').concat($t("已绑定"),'</fx-tag>\n                        </div>\n                      </div>\n                      <span v-if="isShowEdit(doc)" @click="handleEditAccount(doc)" class="fx-btn fx-btn-link fx-btn-mc doc-edit__btn">').concat($t("编辑"),'</span>\n                    </div>\n                    <div class="doc-button" @click="(event) => handleClick(event, doc)" v-html="createButtons(doc)"></div>\n                  </div>\n\n                  \x3c!-- 新建 --\x3e\n                  <div v-if="group.isCustom" class="doc-item create-btn">\n                    <span class="fx-btn fx-btn-link" @click="handleCreateAccount"> \n                      <i class="el-icon-plus"></i>{{$t(\'新建\')}}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        '),data:function(){return{loading:!1,docList:[],supportCustom:CRM.util.getUserAttribute("crmOnlinedocCustom")}},computed:{},methods:{isShowBound:function(n){if(n.defineType!==r.systemPersonal)return n.isBinding===d.bound},isShowEdit:function(n){return n.defineType===r.custom&&!n.isActive},isActived:function(n){return n.isActive===a.enable},createButtons:function(t){return[{name:$t("解绑"),action:"unbindAccount",filter:function(n){if(n.defineType!==r.systemPersonal)return n.isBinding===d.bound&&n.isActive===a.disable}},{name:$t("绑定"),action:"bindAccount",filter:function(n){return n.isBinding===d.unbound&&n.isActive===a.disable}},{name:$t("停用"),action:"disableDoc",filter:function(n){return n.isActive===a.enable}},{name:$t("启用"),action:"enableDoc",filter:function(n){return n.isBinding===d.bound&&n.isActive===a.disable}},{name:$t("删除"),action:"deleteDoc",filter:function(n){return n.isActive===a.disable}}].filter(function(n){return n.filter(t)}).map(function(n){return'\n                <span\n                  class="fx-btn fx-btn-link fx-btn-mc doc-btn__item"\n                  data-action="'.concat(n.action,'"\n                  role="button"\n                  >').concat(n.name,"</span>\n              ")}).join("")},updateDocList:function(){var t=this;this.loading=!0,o.fetchAllDocList().then(function(n){t.loading=!1,t.docList=[{title:$t("crm.onlinedoc.titledes"),desc:$t("crm.onlinedoc.hdes"),items:_.filter(n,function(n){return r.custom!==n.defineType})}],t.supportCustom&&t.docList.push({isCustom:!0,title:$t("crm.onlinedoc.titleconnector"),items:_.filter(n,function(n){return r.custom===n.defineType})})})},handleCreateAccount:function(n){var t=this;s().then(function(){t.refresh()}).catch(function(n){})},handleEditAccount:function(n){var t=this;s(n).then(function(){t.refresh()})},handleBindAccount:function(n){this.handleUsePWC(n)},handleUsePWC:function(i){var c=this;FxUI.userDefine.open_custom_comp(i.pwcApiName,{bindAccount:function(n){return new Promise(function(t,e){o.bindAccount({devInfoMap:n,pluginApiName:i.pluginApiName}).then(function(n){c.refresh(),FxUI.Message.success($t("操作成功")),t()}).catch(function(n){FxUI.Message.error(n.Result.FailureMessage),e()})})}},{title:i.name})},handleUnbindAccount:function(n){var t=this;FxUI.MessageBox.confirm($t("crm.onlinedoc.unbind"),$t("提示")).then(function(){t.loading=!0,o.unbindAccount(n).then(function(){t.loading=!1,t.refresh(),FxUI.Message.success($t("操作成功"))}).catch(function(n){t.loading=!1,FxUI.Message.error(n.Result.FailureMessage)})}).catch(e={})},handleDisableDoc:function(n){var t=this;this.loading=!0,o.disableDoc(n).then(function(){t.loading=!1,t.refresh(),FxUI.Message.success($t("操作成功"))}).catch(function(n){t.loading=!1,FxUI.Message.error(n.Result.FailureMessage)})},handleEnableDoc:function(n){var t=this;this.loading=!0,o.enableDoc(n).then(function(){t.loading=!1,t.refresh(),FxUI.Message.success($t("操作成功"))}).catch(function(n){t.loading=!1,FxUI.Message.error(n.Result.FailureMessage)})},handleDeleteDoc:function(n){var t=this;FxUI.MessageBox.confirm($t("crm.onlinedoc.deltip"),$t("提示")).then(function(){t.loading=!0,o.deleteDoc(n).then(function(){t.loading=!1,t.refresh(),FxUI.Message.success($t("操作成功"))}).catch(function(n){t.loading=!1,FxUI.Message.error(n.Result.FailureMessage)})}).catch(function(n){})},handleClick:function(n,t){n=CRM.util.fupper(n.target.dataset.action||"");n&&this["handle".concat(n)]&&this["handle".concat(n)](t)},refresh:function(){this.updateDocList()}},mounted:function(){this.refresh()}})},destroy:function(){this.instance&&this.instance.destroy(),this.instance=null}});i.exports=n});