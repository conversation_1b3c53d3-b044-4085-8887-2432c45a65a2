define("crm-setting/channel-home/channel-home",["crm-modules/common/util"],function(t,n,e){t("crm-modules/common/util");var i=Backbone.View.extend({initialize:function(n){this.setElement(n.wrapper),this.init()},init:function(n){var e=this;t.async("vcrm/sdk",function(n){n.getComponent("channelguide").then(function(n){e.initView(n.default)})})},initView:function(n){new Vue({el:this.$el[0],template:"<div style='height:100%'>\n                                <ChannelGuide></ChannelGuide>\n                          </div>",components:{ChannelGuide:n},data:function(){return{}},watch:{},computed:{},created:function(){},mounted:function(){},methods:{}})},destroy:function(){}});e.exports=i});