define("crm-setting/servicemarketlog/servicemarketlog",["./template/tpl-html","crm-modules/page/list/list"],function(e,t,i){var s=e("./template/tpl-html"),r=e("crm-modules/page/list/list"),e=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.apiname="ServiceMarketLogObj",this.render()},render:function(){this.$el.html(s()),this.renderTable()},renderTable:function(){this.list=new r({wrapper:$(".crm-qir-table",this.$el),apiname:this.apiname,showOperate:!1,tableOptions:{searchTerm:null,showMultiple:!1,showFilerBtn:!1,showTerm:!1}}),this.list.render&&this.list.render()},refresh:function(){var e=this;e.list&&e.list.refresh&&e.list.refresh()},destroy:function(){this.list&&this.list.destroy&&this.list.destroy()}});i.exports=e});
define("crm-setting/servicemarketlog/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-qir-wrapper"> <div class="crm-qir-table"></div> </div>';
        }
        return __p;
    };
});