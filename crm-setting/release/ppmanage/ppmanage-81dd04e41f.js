function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function asyncGeneratorStep(e,t,n,i,r,l,o){try{var a=e[l](o),c=a.value}catch(e){return void n(e)}a.done?t(c):Promise.resolve(c).then(i,r)}function _asyncToGenerator(a){return function(){var e=this,o=arguments;return new Promise(function(t,n){var i=a.apply(e,o);function r(e){asyncGeneratorStep(i,t,n,r,l,"next",e)}function l(e){asyncGeneratorStep(i,t,n,r,l,"throw",e)}r(void 0)})}}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,_toPropertyKey(i.key),i)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"==_typeof(e)?e:e+""}function _toPrimitive(e,t){if("object"!=_typeof(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!=_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}define("crm-setting/ppmanage/comps/availablerangefilter",[],function(e,t,n){var c='<fx-dialog\n        :visible.sync="dialogVisible"\n        :title="title"\n        size="small"\n        appendToBody\n        class="available-range-filter-dialog"\n        :zIndex='.concat(+FxUI.Utils.getPopupZIndex()+50,'>\n            <div class="filter-content" :style="contentStyle">\n                <div :style="styleObj" >\n                    <label :style="labelStyle"><i style="color:#f56c6c;margin-right:4px;">*</i>').concat($t("过滤字段"),'</label>\n                    <fx-select\n                        ref="field"\n                        v-model="field"\n                        :options="fieldOptions"\n                        filterable\n                        size="small"\n                        :style="formStyle"\n                    ></fx-select>\n                </div>\n                <div :style="styleObj" class="func-form">\n                    <label :style="labelStyle"><i style="color:#f56c6c;margin-right:4px;">*</i>').concat($t("关联函数"),'</label>\n                    <fx-select\n                        @click.native="clickFuncHandle"\n                        ref="func"\n                        v-model="func"\n                        :options="funcOptions"\n                        disabled\n                        size="small"\n                        :style="[formStyle]"\n                    ></fx-select>\n                </div>\n            </div>\n            <span slot="footer" class="dialog-footer">\n                <fx-button type="primary" @click="submitFilter" size="small">').concat($t("确 定"),'</fx-button>\n                <fx-button @click="cancelFilter" size="small">').concat($t("取 消"),"</fx-button>\n            </span>\n    </fx-dialog>"),i=_createClass(function e(t){_classCallCheck(this,e),this.dialog,this.clickPromise,this.result={},this.initView(t)},[{key:"initView",value:function(){var e,n=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},i=this,r=t.isEdit,l=null!=(e=t.apiName)?e:"SalesOrderObj",o="",a="";r&&(o=t.field,a=t.func),this.dialog&&(this.dialog=null),this.clickPromise=new Promise(function(e,t){n.dialog=FxUI.create({template:c,data:function(){return{dialogVisible:!0,isEdit:r,field:o,fieldOptions:[],func:a,funcOptions:[],contentStyle:{display:"flex",flexDirection:"column",justifyContent:"space-around",height:"120px"},styleObj:{display:"flex",justifyContent:"space-between"},labelStyle:{lineHeight:"32px"},formStyle:{width:0,flex:1,marginLeft:"2em"}}},computed:{title:function(){return this.isEdit?$t("编辑可售范围过滤条件"):$t("启用过滤可售范围")}},created:function(){this.getObjField(),this.getFilterFunction(this.func)},methods:{clickFuncHandle:function(){var t=this;CRM.util.waiting(),new Promise(function(t,n){seajs.use("paas-function/sdk",function(e){CRM.util.waiting(!1),e&&e.getScopeRuleFunction({object_api_name:"AvailableRangeObj",zIndex:+FxUI.Utils.getPopupZIndex()+60},function(e){e.status?t(e.data.function):n()})})}).then(function(e){t.updateFuncOptions(e),t.func=e.api_name})},submitFilter:function(){i.result={filter_field:this.field,filter_function:this.func},this.validateFilter()?(e(i.result),this.dialogVisible=!1):t()},validateFilter:function(){return this.field?!!this.func||(CRM.util.remind(3,$t("请填写")+$t("关联函数"),void 0,2500),!1):(CRM.util.remind(3,$t("请填写")+$t("过滤字段"),void 0,2500),!1)},cancelFilter:function(){t("cancel"),this.dialogVisible=!1},getObjField:function(){var i=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,n;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(i.fieldOptions&&0!==i.fieldOptions.length)return e.abrupt("return");e.next=2;break;case 2:return t="/EM1HNCRM/API/v1/object/"+l+"/controller/DescribeLayout",n={apiname:l,include_detail_describe:!1,include_layout:!1,layout_type:"add",recordType_apiName:"default__c"},e.next=6,CRM.util.ajax_base(t,n);case 6:t=e.sent,n=t.objectDescribe,i.fieldOptions=i._parseData(n);case 9:case"end":return e.stop()}},e)}))()},_parseData:function(){var e=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).fields,t=[];return _.each(e,function(e){if(!e.is_active)return!1;"object_reference"!=e.type&&"select_one"!=e.type&&"record_type"!=e.type||t.push(_.extend(e,{label:e.label,value:e.api_name}))}),t},getFilterFunction:function(n){var i=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n){e.next=2;break}return e.abrupt("return");case 2:return t={api_name:n,binding_object_api_name:"AvailableRangeObj"},e.next=6,CRM.util.ajax_base("/EM1HNCRM/API/v1/object/function/service/find",t,$.noop,!0);case 6:t=e.sent,i.updateFuncOptions(t.function);case 8:case"end":return e.stop()}},e)}))()},updateFuncOptions:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{};e.id;this.funcOptions=[_.extend({value:e.api_name,label:e.function_name},e)]}}})})}},{key:"getClickPromise",value:function(){return this.clickPromise}},{key:"getValue",value:function(){return this.result}},{key:"setValue",value:function(){}},{key:"destroy",value:function(){var e,t;null!=(e=this.dialog)&&null!=(t=e.destroy)&&t.call(e)}}]);n.exports=i});
function asyncGeneratorStep(e,t,a,i,r,o,c){try{var n=e[o](c),l=n.value}catch(e){return void a(e)}n.done?t(l):Promise.resolve(l).then(i,r)}function _asyncToGenerator(n){return function(){var e=this,c=arguments;return new Promise(function(t,a){var i=n.apply(e,c);function r(e){asyncGeneratorStep(i,t,a,r,o,"next",e)}function o(e){asyncGeneratorStep(i,t,a,r,o,"throw",e)}r(void 0)})}}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var a;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(a="Object"===(a={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:a)||"Set"===a?Array.from(e):"Arguments"===a||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var a=0,i=Array(t);a<t;a++)i[a]=e[a];return i}function _iterableToArrayLimit(e,t){var a=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=a){var i,r,o,c,n=[],l=!0,s=!1;try{if(o=(a=a.call(e)).next,0===t){if(Object(a)!==a)return;l=!1}else for(;!(l=(i=o.call(a)).done)&&(n.push(i.value),n.length!==t);l=!0);}catch(e){s=!0,r=e}finally{try{if(!l&&null!=a.return&&(c=a.return(),Object(c)!==c))return}finally{if(s)throw r}}return n}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/ppmanage/ppmanage",["crm-modules/components/tabs/tabs","crm-modules/common/util","./template/pricebook-html","./template/availablerange-html","./template/attribute-html","./template/pricepolicy-html","./template/coupon-html","./template/rebate-html","./comps/availablerangefilter","./template/tpl-html"],function(r,e,t){var o=r("crm-modules/components/tabs/tabs"),c=r("crm-modules/common/util"),i=(r("./template/pricebook-html"),r("./template/availablerange-html"),r("./template/attribute-html"),r("./template/pricepolicy-html"),r("./template/coupon-html"),r("./template/rebate-html"),r("./comps/availablerangefilter")),n={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"available_range_filter",value:{status:"",filter_field:"",filter_function:""}},status:!0,targetCon:"availableRangeBasedOnOrder",noConfirm:!1,confirmInfo:$t("确定要停用可售范围的过滤吗？如果启用了订货通，可能会影响经销商的正常使用，请谨慎操作。"),confirmTit:$t("提示"),successText:$t("操作成功")};return Backbone.View.extend({template:r("./template/tpl-html"),initialize:function(e){this.init(),this.setElement(e.wrapper),this.tenant_id=CRM.enterpriseId},init:function(){this._allComps={},this.priceBookForDate={SalesOrderObj:{name:$t("订单")+$t("对象"),defSelect:["order_time"],value:""},QuoteObj:{name:$t("报价单")+$t("对象"),defSelect:["quote_time"],value:""},NewOpportunityObj:{name:$t("商机2.0")+$t("对象"),defSelect:["close_date"],value:""},SaleContractObj:{name:$t("销售合同")+$t("对象"),defSelect:["contract_time","create_time"],value:""}}},events:{"click .j-switch-tab":"switchTab","click .j-set-config":"setConfig","click .j-range-order":"editRangeFilter","click .j-set-on":"showTip","click .j-reload-config":"_reloadHandle","mouseenter .j-availablerangep-case":"availablerangepCaseEnter","mouseleave .j-availablerangep-case":"availablerangepCaseLeave","click .j-radio-amortize":"amortizeCheck","click .j-radio-giftAmortize":"giftAmortizeCheck"},render:function(t,a){var i=this;CRM.api.get_licenses({key:["product_attribute_app","advanced_pricing_app","coupon_app","rebate_app"],cb:function(e){switch(i.tarTab=t||"pricebook-box",i.$el.html(i.template({isGreyProductAttribute:e.product_attribute_app,advancedPricing:e.advanced_pricing_app,isCoupon:e.coupon_app,isRebate:e.rebate_app})),t?i.renderTpl(a):i.getConfig(i.renderTpl),$("[data-render="+i.tarTab+"]").addClass("cur"),o.prototype.getParameters()){case"tabs-availablerange":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="availablerange-box"]').addClass("cur"),i.tarTab="availablerange-box",i.getConfig(i.renderTpl);break;case"tabs-pricebook":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="pricebook-box"]').addClass("cur"),i.tarTab="pricebook-box",i.getConfig(i.renderTpl);break;case"tabs-attribute":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="attribute-box"]').addClass("cur"),i.tarTab="attribute-box",i.getConfig(i.renderTpl);break;case"tabs-pricepolicy":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="pricepolicy-box"]').addClass("cur"),i.tarTab="pricepolicy-box",i.getConfig(i.renderTpl);break;case"tabs-coupon":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="coupon-box"]').addClass("cur"),i.tarTab="coupon-box",i.getConfig(i.renderTpl);break;case"tabs-rebate":$(".j-switch-tab").parent().children().removeClass("cur"),$('[data-render="rebate-box"]').addClass("cur"),i.tarTab="rebate-box",i.getConfig(i.renderTpl)}}})},renderGuide:function(e){var t=e.type,a=e.module,i=this;r.async("crm-modules/components/biz_manage/biz_manage",function(e){i.guide=new e({$el:$(".render-content",i.$el),module:a||"pricemanage",type:t||"6"})})},renderTpl:function(e){var t,a=this,i={"pricebook-box":{type:"pricebook",module:"pricemanage"},"availablerange-box":{type:"availablerange",module:"pricemanage"},"attribute-box":{type:"attribute",module:"cmmodityproduct"},"pricepolicy-box":{type:"pricepolicy",module:"promotionrebate"},"coupon-box":{type:"coupon",module:"promotionrebate"},"rebate-box":{type:"rebate",module:"promotionrebate"}};a.guide?(t=i[a.tarTab],this.guide.render(t.type,t.module)):this.renderGuide(i[a.tarTab])},renderGiftConfigDom:function(){this._allComps.giftConfigComp=FxUI.create({wrapper:".price-policy-gift-config",template:' <fx-radio-group v-model="giftAttendAmortize" @change="onChange">\n\t\t\t\t\t\t\t\t<fx-radio :label=\'"0"\' style="line-height:32px;margin-bottom:10px">{{not_label}}</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio :label=\'"1"\' style="line-height:32px;">{{yes_label}}</fx-radio>\n\t\t\t  \t\t\t\t</fx-radio-group>',data:function(){return{giftAttendAmortize:CRM._cache.giftAttendAmortize,not_label:$t("否:赠品的价值全部分摊到本品行，赠品最终【销售单价】为0、【费用分摊后小计】即为赠品自己的价值"),yes_label:$t("是:赠品的价值分摊到本品行和赠品行，赠品最终【销售单价】为0、【费用分摊后小计】为赠品自己的价值扣除分摊值的部分"),size:"small"}},methods:{onChange:function(t){var a=this;CRM.util.setConfigValue({key:"gift_attend_amortize",value:t}).then(function(e){CRM._cache.giftAttendAmortize=t,c.remind(1,$t("配置成功!"))},function(e){c.remind(3,e||$t("配置失败!")),a.giftAttend="0"==t?"1":"0"})}}})},renderTitleConfigDom:function(){var e=[{label:$t("移动端显示政策名称"),value:CRM._cache.showPricePolicyName,key:"show_price_policy_name"}];this._allComps.titleConfigComp=FxUI.create({wrapper:".price-policy-title-config .title-config-content",template:'<div class=\'checkbox-wrapper\'>\n\t\t\t\t\t\t\t\t<fx-checkbox \n\t\t\t\t\t\t\t\t\t@change="onChange($event,item)"\n\t\t\t\t\t\t\t\t\tv-for="item in data"\n\t\t\t\t\t\t\t\t\tv-model="item.value"\n\t\t\t\t\t\t\t\t\tclass="title-config-item config-item"\n\t\t\t\t\t\t\t\t \t:label="item.label"\n\t\t\t\t\t\t\t\t\t:key="item.key"\n\t\t\t\t\t\t\t\t></fx-checkbox>\n\t\t\t\t\t\t\t</div>',data:function(){return{data:e,size:"small"}},methods:{onChange:function(t,a){CRM.util.setConfigValue({key:a.key,value:t?"1":"0"}).then(function(e){CRM._cache.showPricePolicyName=t,a.value=t,c.remind(1,$t("配置成功!"))},function(e){a.value=CRM._cache.showPricePolicyName,c.remind(3,e||$t("配置失败!"))})}}})},renderSwitchPolicyDom:function(){var e=[{label:$t("整单促:主对象上匹配的价格政策允许手工切换或取消"),value:CRM._cache.allowSwitchMasterPricePolicy,crmKey:"allowSwitchMasterPricePolicy",key:"allow_switch_master_price_policy"},{label:$t("产品促:从对象上匹配的价格政策允许手工切换或取消"),value:CRM._cache.allowSwitchDetailPricePolicy,crmKey:"allowSwitchDetailPricePolicy",key:"allow_switch_detail_price_policy"}];this._allComps.switchPolicyComp=FxUI.create({wrapper:".price-policy-switchpolicy-config",template:'<div class=\'checkbox-wrapper\'>\n\t\t\t\t\t\t\t<fx-checkbox \n\t\t\t\t\t\t\t\t@change="onChange($event,item)"\n\t\t\t\t\t\t\t\tv-for="item in data"\n\t\t\t\t\t\t\t\tv-model="item.value"\n\t\t\t\t\t\t\t\tclass="switchpolicy-config-item config-item"\n\t\t\t\t\t\t\t\t :label="item.label"\n\t\t\t\t\t\t\t\t:key="item.key"\n\t\t\t\t\t\t\t></fx-checkbox>\n\t\t\t\t\t\t</div>',data:function(){return{data:e,size:"small"}},methods:{onChange:function(t,a){CRM.util.setConfigValue({key:a.key,value:t?"1":"0"}).then(function(e){CRM._cache[a.crmKey]=t,a.value=t,c.remind(1,$t("配置成功!"))},function(e){a.value=CRM._cache[a.crmKey],c.remind(3,e||$t("配置失败!"))})}}})},isCpqBuyed:function(){return new Promise(function(t,e){c.FHHApi({url:"/EM1HNCRM/API/v1/object/version_privilege/service/check_app",data:{proudct_code:"cpq_app"},success:function(e){0===e.Result.StatusCode?t(e.Value.result):c.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},switchTab:function(e){e=$(e.target);e.parent().children().removeClass("cur"),e.addClass("cur"),this.tarTab=e.attr("data-render"),this.getConfig(this.renderTpl)},_reloadHandle:function(e){this.getConfig(this.renderTpl,$(e.currentTarget))},getConfig:function(t,e){var r=this,a={};switch(r.tarTab){case"pricebook-box":CRM.util.getConfigValues(["price_book_product_valid_period","price_book_product_tiered_price","whether_filter_price_book_select_product","28","enforce_priority","ignore_price_book_valid_period","match_price_book_valid_field","sale_contract"]).then(function(e){CRM._cache.openPriceList="1"===(_.findWhere(e,{key:"28"})||{}).value,CRM._cache.priceBookPriority="1"===(_.findWhere(e,{key:"enforce_priority"})||{}).value,CRM._cache.priceBookProductValidPeriod="1"===(_.findWhere(e,{key:"price_book_product_valid_period"})||{}).value,CRM._cache.priceBookProductTieredPrice="1"===(_.findWhere(e,{key:"price_book_product_tiered_price"})||{}).value,CRM._cache.priceBookSelectProduct="1"===(_.findWhere(e,{key:"whether_filter_price_book_select_product"})||{}).value,CRM._cache.ignore_price_book_valid_period="1"===(_.findWhere(e,{key:"ignore_price_book_valid_period"})||{}).value,CRM._cache.match_price_book_valid_field=(_.findWhere(e,{key:"match_price_book_valid_field"})||{}).value,CRM._cache.sale_contract="1"===(_.findWhere(e,{key:"sale_contract"})||{}).value,CRM._cache.sale_contract||delete r.priceBookForDate.SaleContractObj,t&&t.call(r,CRM._cache.openPriceList),CRM.util.isGrayScale("CRM_PRICEBOOK_PERIOD")&&r.renderPriceBookForDate()});break;case"availablerange-box":CRM.util.getConfigValues(["available_price_book","available_range","is_open_available_range_priority","available_range_duplicated_check","available_range_filter"]).then(function(e){CRM._cache.openAvailablerange="1"===(_.findWhere(e,{key:"available_range"})||{}).value,CRM._cache.openAvailablePriceBook="1"===(_.findWhere(e,{key:"available_price_book"})||{}).value,CRM._cache.openAvailableRangePriority="1"===(_.findWhere(e,{key:"is_open_available_range_priority"})||{}).value,CRM._cache.openAvailableRangeDuplicatedCheck="1"===(_.findWhere(e,{key:"available_range_duplicated_check"})||{}).value;e=r.availableRangeFilterValue=JSON.parse((_.findWhere(e,{key:"available_range_filter"})||{}).value||{});CRM._cache.available_range_filter="1"===e.status,CRM._cache.available_range_filter&&r.displayFilterLabel(e),t&&t.call(r,CRM._cache.openAvailablerange)},function(e){c.alert(e)});break;case"attribute-box":CRM.util.getConfigValues(["is_open_attribute","is_open_nonstandard_attribute"]).then(function(a){var i={openAttribute:"is_open_attribute",openNsAttribute:"is_open_nonstandard_attribute"};Object.keys(i).forEach(function(t){var e=a.find(function(e){return e.key==i[t]})||{};CRM._cache[t]="1"===e.value||1===e.value}),t&&t.call(r)});break;case"pricepolicy-box":CRM.util.getConfigValues(["price_policy","gift_amortize_basis","gift_attend_amortize","show_price_policy_name","allow_switch_master_price_policy","allow_switch_detail_price_policy"]).then(function(a){var i={openPricePolicy:"price_policy",giftAmortizeBasis:"gift_amortize_basis",giftAttendAmortize:"gift_attend_amortize",showPricePolicyName:"show_price_policy_name",allowSwitchMasterPricePolicy:"allow_switch_master_price_policy",allowSwitchDetailPricePolicy:"allow_switch_detail_price_policy"};Object.keys(i).forEach(function(t){var e=a.find(function(e){return e.key==i[t]})||{};switch(t){case"openPricePolicy":case"showPricePolicyName":case"allowSwitchMasterPricePolicy":case"allowSwitchDetailPricePolicy":CRM._cache[t]="1"===e.value;break;default:CRM._cache[t]=e.value}}),t&&t.call(r)});break;case"coupon-box":CRM.util.getConfigValues(["coupon","paper_coupon"]).then(function(a){var i={openCoupon:"coupon",openPaperCoupon:"paper_coupon"};Object.keys(i).forEach(function(t){var e=a.find(function(e){return e.key==i[t]})||{};CRM._cache[t]="1"===e.value||1===e.value}),t&&t.call(r)});break;case"rebate-box":a={url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_values",data:{isAllConfig:!1,keys:["rebate"]},success:function(e){0!=e.Result.StatusCode?c.alert(e.Result.FailureMessage):(CRM._cache.openRebate="1"===(_.findWhere(e.Value.values,{key:"rebate"})||{}).value,t&&t.call(r))}}}r.getConfigHandle(a,e)},setConfig:function(e){var t=this,a={},i=$(e.target);switch(t.tarTab){case"pricebook-box":if(i.hasClass("switch-pricebook"))a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"28",value:"1"},status:!0,targetCon:"pricebook",confirmInfo:$t("确认开启价目表吗"),confirmTit:$t("提示")};else if(i.hasClass("switch-pricebook-priority")){if(!CRM._cache.openPriceList)return void CRM.util.alert($t("请先开启价目表"));a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"enforce_priority",value:CRM._cache.priceBookPriority?"0":"1"},status:!0,targetCon:"priceBookPriority",confirmInfo:CRM._cache.priceBookPriority?$t("确认关闭强制执行价目表优先级最优价格吗"):$t("确认开启强制执行价目表优先级最优价格吗"),confirmTit:$t("提示"),successText:$t("操作成功")}}else i.hasClass("switch-pricebookByDate")?a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"ignore_price_book_valid_period",value:CRM._cache.ignore_price_book_valid_period?"0":"1"},status:!0,targetCon:"priceBookValidPeriod",confirmInfo:CRM._cache.ignore_price_book_valid_period?$t("确认关闭时间配置吗"):$t("确认开启时间配置吗"),confirmTit:$t("提示")}:i.hasClass("switch-pricebookPeriod")?a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"price_book_product_valid_period",value:CRM._cache.priceBookProductValidPeriod?"0":"1"},status:!0,targetCon:"priceBookProductValidPeriod",confirmInfo:CRM._cache.priceBookProductValidPeriod?$t("确认关闭{{name}}吗",{name:$t("crm.ppmanage.price_book_product_valid_period")}):$t("确认开启{{name}}吗",{name:$t("crm.ppmanage.price_book_product_valid_period")}),confirmTit:$t("提示")}:i.hasClass("switch-pricebookLadder")?a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"price_book_product_tiered_price",value:CRM._cache.priceBookProductTieredPrice?"0":"1"},status:!0,targetCon:"priceBookProductTieredPrice",confirmInfo:CRM._cache.priceBookProductTieredPrice?$t("确认关闭{{name}}吗",{name:$t("crm.ppmanage.price_book_product_tiered_price")}):$t("确认开启{{name}}吗",{name:$t("crm.ppmanage.price_book_product_tiered_price")}),confirmTit:$t("提示")}:i.hasClass("switch-pricebookproductSelected")&&(a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"whether_filter_price_book_select_product",value:CRM._cache.priceBookSelectProduct?"0":"1"},status:!0,targetCon:"priceBookSelectProduct",confirmInfo:CRM._cache.priceBookSelectProduct?$t("确认关闭{{name}}吗",{name:$t("crm.ppmanage.price_book_product_selected")}):$t("确认开启{{name}}吗",{name:$t("crm.ppmanage.price_book_product_selected")}),confirmTit:$t("提示"),successText:$t("操作成功")});break;case"availablerange-box":if(!CRM._cache.openPriceList)return void CRM.util.alert($t("请先开启价目表"));i.hasClass("availableRange")?a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"available_range",tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:"availablerange",confirmInfo:$t("确认开启可售范围吗"),confirmTit:$t("提示")}:i.hasClass("availableRangePriority")?a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"is_open_available_range_priority",tenantId:t.tenant_id,openStatus:CRM._cache.openAvailableRangePriority?0:1},status:!0,targetCon:"availableRangePriority",confirmInfo:CRM._cache.openAvailableRangePriority?$t("确认关闭执行可售范围优先级吗"):$t("确认")+$t("开启执行可售范围优先级")+$t("吗？"),confirmTit:$t("提示"),successText:$t("操作成功")}:i.hasClass("availableRangeDuplicatedCheck")?a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"available_range_duplicated_check",value:CRM._cache.openAvailableRangeDuplicatedCheck?"0":"1"},status:!0,targetCon:"availableRangeDuplicatedCheck",confirmInfo:CRM._cache.openAvailableRangeDuplicatedCheck?$t("确认关闭可售范围查重吗"):$t("确认开启可售范围查重吗"),confirmTit:$t("提示"),successText:$t("操作成功")}:i.hasClass("availableRangeBasedOnOrder")?a=_.extend({},n,{data:{key:"available_range_filter",value:{status:CRM._cache.available_range_filter?"0":"1",filter_field:"",filter_function:""}},noConfirm:!CRM._cache.available_range_filter}):i.hasClass("availablePriceBook")&&(a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"available_price_book",tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:"availablerPriceBook",confirmInfo:$t("确认开启可售价目表吗"),confirmTit:$t("提示")});break;case"attribute-box":a=i.hasClass("attribute-switch")?{url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"is_open_attribute",value:1},status:!0,targetCon:"AttributeTpl",confirmInfo:$t("开启当前开关时，请先开启[价目表开关].注意：开启属性后，将不能开启[商品设置][多单位设置][促销设置]"),confirmTit:$t("提示"),successText:$t("启用成功")}:{url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"is_open_nonstandard_attribute",value:1},status:!0,targetCon:"AttributeTpl",confirmInfo:$t("开启当前开关时，请先开启[价目表开关].注意：开启非标属性后，将不能开启[商品设置][多单位设置][促销设置]"),confirmTit:$t("提示"),successText:$t("启用成功")};break;case"pricepolicy-box":if(i.hasClass("switch-pricepolicy")){if(!CRM._cache.openPriceList)return void CRM.util.alert($t("请先开启价目表"));a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"price_policy",tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:"pricepolicy",confirmInfo:$t("开启高级定价不可关闭"),confirmTit:$t("提示"),successText:$t("启用成功")}}else if(i.hasClass("switch-enforcePricepolicy")){if(!CRM._cache.openPricePolicy)return CRM.util.alert($t("请先开启高级定价")),!1;a={url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"enforce_price_policy_priority",value:CRM._cache.openEnforcePricePolicy?"0":"1"},status:!0,targetCon:"enforcePricepolicy",confirmInfo:CRM._cache.openEnforcePricePolicy?$t("确认关闭高级定价优先级吗"):$t("确认开启高级定价优先级吗"),confirmTit:$t("提示"),successText:$t("操作成功")}}break;case"coupon-box":if(i.hasClass("switch-coupon"))a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"coupon",tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:"coupon",confirmInfo:$t("优惠券政策开启不可关闭"),confirmTit:$t("提示"),successText:$t("操作成功")};else{if(!CRM._cache.openCoupon)return CRM.util.alert($t("请先开启优惠券")),!1;a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"paper_coupon",tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:"paperCoupon",confirmInfo:$t("纸质券业务开启不可关闭"),confirmTit:$t("提示"),successText:$t("操作成功")}}break;case"rebate-box":a={url:"/EM1HNCRM/API/v1/object/module_ctrl/service/save_module_status",data:{moduleCode:"rebate",tenantId:t.tenant_id,openStatus:1},status:!0,targetCon:"rebate",confirmInfo:$t("返利单政策开启不可关闭"),confirmTit:$t("提示"),successText:$t("操作成功")}}t.beforeSetConfigHandle(a)},getConfigHandle:function(t,e){var a;!t||Object.keys(t).length<=0||((a=this)._getAjax&&(a._getAjax.abort(),a._getAjax=null),a._getAjax=c.FHHApi({url:t.url,data:t.data||"",success:function(e){0===e.Result.StatusCode?t.success(e):c.alert(e.Result.FailureMessage)},complete:function(){a._getAjax=null}},{errorAlertModel:1,submitSelector:e}))},beforeSetConfigHandle:function(t){var a=this;switch(t.targetCon){case"AttributeTpl":this.isAttributeAllowed().done(function(e){e.value?a.setConfigHandle(t):CRM.util.alert(e.info)});break;case"pricepolicy":this.isPricepolicyAllowed().done(function(e){e.value?a.setConfigHandle(t):CRM.util.alert(e.info)});break;case"availableRangeBasedOnOrder":var e,i,r=t.data.value;"0"===r.status?(i=(e=a.availableRangeFilterValue).filter_field,t.data.value=JSON.stringify(_.extend({},r,{filter_field:i,filter_function:e.filter_function})),a.setConfigHandle(t)):this.isAvailableRangeBasedOnOrderAllowed().then(function(e){e.value?a.addRangeFilter(t).then(function(e){_.extend(a.availableRangeFilterValue,e),t.data.value=JSON.stringify(_.extend({},r,e)),a.setConfigHandle(t)}):CRM.util.alert(e.info)});break;default:this.setConfigHandle(t)}},setConfigHandle:function(t){var a=this,i=null;function e(){var e;c.FHHApi({url:t.url,data:t.data,success:function(e){if(0==e.Result.StatusCode)if(["available_range","price_policy"].includes(t.data.moduleCode)&&!e.Value.success)c.alert(e.Value.errMessage);else{if("availablerange"===t.targetCon&&(CRM._cache.openAvailablerange="1"===e.Value.value.openStatus),"availablerPriceBook"===t.targetCon&&(CRM._cache.openAvailablePriceBook="1"===e.Value.value.openStatus),"availableRangePriority"===t.targetCon&&(CRM._cache.openAvailableRangePriority="1"===e.Value.value.openStatus),"AttributeTpl"===t.targetCon&&("is_open_attribute"==t.data.key?CRM._cache.openAttribute=!0:CRM._cache.openNsAttribute=!0),"availableRangeDuplicatedCheck"===t.targetCon&&(CRM._cache.openAvailableRangeDuplicatedCheck=!CRM._cache.openAvailableRangeDuplicatedCheck),"priceBookPriority"===t.targetCon&&(CRM._cache.priceBookPriority=!CRM._cache.priceBookPriority),"pricepolicy"===t.targetCon&&(CRM._cache.openPricePolicy=!0),"enforcePricepolicy"===t.targetCon&&(CRM._cache.openEnforcePricePolicy=!CRM._cache.openEnforcePricePolicy),"priceBookValidPeriod"===t.targetCon&&(CRM._cache.ignore_price_book_valid_period=!CRM._cache.ignore_price_book_valid_period,a.setDateIsShow()),"priceBookProductValidPeriod"===t.targetCon&&(CRM._cache.priceBookProductValidPeriod=!CRM._cache.priceBookProductValidPeriod),"priceBookProductTieredPrice"===t.targetCon&&(CRM._cache.priceBookProductTieredPrice=!CRM._cache.priceBookProductTieredPrice),"priceBookSelectProduct"===t.targetCon&&(CRM._cache.priceBookSelectProduct=!CRM._cache.priceBookSelectProduct),"coupon"===t.targetCon){if(0!==e.Value.errCode)return void c.alert(e.Value.errMessage);CRM._cache.openCoupon=!0}if("paperCoupon"===t.targetCon&&(CRM._cache.openPaperCoupon=!0),"rebate"===t.targetCon){if(0!==e.Value.errCode)return void c.alert(e.Value.errMessage);CRM._cache.openRebate=!0}"availableRangeBasedOnOrder"===t.targetCon&&(t.isEdit||(CRM._cache.available_range_filter=!CRM._cache.available_range_filter,CRM._cache.available_range_filter))&&a.displayFilterLabel(a.availableRangeFilterValue),a.renderTpl(t.status),a.initProduct(),CRM.control.refreshAside(),c.remind(1,t.successText||$t("启用成功"))}else c.alert(e.Result.FailureMessage)},complete:function(){var e;null!=(e=i)&&e.hide(),a.isSetting=!1}},{errorAlertModel:1,submitSelector:null==(e=i)?void 0:e.$(".b-g-btn")})}t.noConfirm?e():i=c.confirm(t.confirmInfo,t.confirmTit,e)},showTip:function(e){var t=this;"pricebook-box"==t.tarTab&&$(e.target).hasClass("switch-pricebook")?c.alert($t("价目表已开启无法再关闭")):"pricebook-box"===t.tarTab&&$(e.target).hasClass("switch-pricebookPeriod")?c.alert($t("价目表明细支持有效期配置开启后无法再关闭")):"pricebook-box"===t.tarTab&&$(e.target).hasClass("switch-pricebookLadder")?c.alert($t("价目表明细支持产品阶梯价开启后无法再关闭")):"availablerange-box"==t.tarTab&&$(e.target).hasClass("availableRange")?c.alert($t("可售范围开启后无法再关闭")):"availablerange-box"==t.tarTab&&$(e.target).hasClass("availablePriceBook")?c.alert($t("开启可售价目表开启后无法再关闭")):"attribute-box"==t.tarTab?$(e.target).hasClass("attribute-switch")?c.alert($t("属性已开启无法再关闭")):c.alert($t("非标属性已开启无法再关闭")):"pricepolicy-box"==t.tarTab&&$(e.target).hasClass("switch-pricepolicy")?c.alert($t("高级定价已开启无法关闭")):"coupon-box"==t.tarTab?$(e.target).hasClass("switch-coupon")?c.alert($t("优惠券政策已开启无法关闭")):c.alert($t("纸质券业务已开启无法关闭")):"rebate-box"==t.tarTab?c.alert($t("返利单政策已开启无法关闭")):t.setConfig(e)},initProduct:function(){c.FHHApi({url:"/EM1HNCRM/API/v1/object/pricebook_standard/service/check_or_init_standard_pricebook",data:{}},{errorAlertModel:1})},availablerangepCaseEnter:function(e){var t='\n\t\t\t\t<div style="width: 300px;">\n\t\t\t\t'.concat($t("可售范围取价举例"),"\n\t\t\t\t</div>\n\t\t\t");CRM.util.remindTip($(e.target),t)},availablerangepCaseLeave:function(e){CRM.util.remindTip($(e.target))},_addLog:function(){CRM.util.sendLog("BOMObj","mannage",{eventId:"opencpq"})},isAttributeAllowed:function(){return new Promise(function(n,e){CRM.util.getConfigValues(["28","spu","multiple_unit","promotion_status"]).then(function(e){var t=$t("crm.propertiesOpenFailedTitle"),a=$t("crm.propertiesOpenFailedInfo"),i=!1,r=!1,o=!1,c=!1,e=(e.forEach(function(e){switch(e.key){case"promotion_status":c="2"==e.value;break;case"28":i="1"==e.value;break;case"spu":r="1"==e.value;break;case"multiple_unit":o="1"==e.value}}),i&&!r&&!o&&!c);i?(r&&(a+="[".concat($t("商品设置"),"]")),o&&(a+=(r?$t("和"):"")+"[".concat($t("多单位设置"),"]")),c&&(a+=(!r||o?$t("和"):"")+"[".concat($t("促销设置"),"]"))):(t=$t("crm.setting.pricebookStatus",null,"开启当前开关时，请先开启[价目表开关] "),a=$t("crm.setting.pricebookStatus_tip",null,"注意：开启属性后，将不能开启[商品设置][多单位设置][促销设置]")),n({value:e,info:t+"</br>"+a})})})},isPricepolicyAllowed:function(){return new Promise(function(i,e){CRM.util.getConfigValue("promotion_status").then(function(e){var e="2"==e,t=$t("crm.advancedPricingOpenFailureTitle"),a=$t("crm.propertiesOpenFailedInfo");e&&(a+="[".concat($t("促销设置"),"]")),i({value:!e,info:t+"</br>"+a})})})},displayFilterLabel:function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},a=this,i=e.filter_field,r=e.filter_function;Promise.all([a.getFilterFields(),a.getFilterFunction(r)]).then(function(e){var e=_slicedToArray(e,2),t=e[0],e=e[1],t=_.findWhere(t,{api_name:i}),e=null==e?void 0:e.function_name;a.$(".filter-field input").val(null!=(t=t.label)?t:i),a.$(".correlation-function input").val(null!=e?e:r)})},getFilterFields:function(){return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t,a;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t="SalesOrderObj",t={apiname:t,include_detail_describe:!1,include_layout:!1,layout_type:"add",recordType_apiName:"default__c"},e.next=4,CRM.util.ajax_base("/EM1HNCRM/API/v1/object/SalesOrderObj/controller/DescribeLayout",t,$.noop,!0);case 4:return t=e.sent,a=t.objectDescribe,e.abrupt("return",(void 0===a?{}:a).fields||{});case 8:case"end":return e.stop()}},e)}))()},getFilterFunction:function(a){return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t={api_name:a,binding_object_api_name:"AvailableRangeObj"},e.next=4,CRM.util.ajax_base("/EM1HNCRM/API/v1/object/function/service/find",t,$.noop,!0);case 4:return t=e.sent,e.abrupt("return",t.function||{});case 6:case"end":return e.stop()}},e)}))()},isAvailableRangeBasedOnOrderAllowed:function(){return new Promise(function(e,t){return e({value:CRM._cache.openAvailablerange,info:$t("crm.availableRangeOpenSwitchInfo")})})},addRangeFilter:function(){var e;return null!=(e=this.availableRangeFilterValue)&&e.filter_field||null!=(e=this.availableRangeFilterValue)&&e.filter_function?Promise.resolve({filter_field:null!=(e=this.availableRangeFilterValue.filter_field)?e:"",filter_function:null!=(e=this.availableRangeFilterValue.filter_function)?e:""}):(new i).getClickPromise()},editRangeFilter:function(){var t=this,a=this.availableRangeFilterValue;new i({isEdit:!0,field:a.filter_field,func:a.filter_function}).getClickPromise().then(function(e){_.extend(t.availableRangeFilterValue,e),t.setConfigHandle(_.extend({},n,{data:{key:"available_range_filter",value:JSON.stringify({status:"1",filter_field:a.filter_field,filter_function:a.filter_function})},noConfirm:!0,isEdit:!0}))})},renderPriceBookForDate:function(){this.setDateIsShow(),this.renderSelectorDom(),this.createDateList(),this.createBtn()},createDateList:function(){var r=this,a=[],o=["date_time","date"];_.each(r.priceBookForDate,function(e,t){a.push(CRM.util.fetchColumns({apiname:t}))}),Promise.all(a).then(function(e){r.priceBookForDate&&(_.each(e,function(e){var a,i,t=e.Value.objectDescribe.api_name;r.priceBookForDate[t].selector=(e=e.Value.objectDescribe.fields,a=r.priceBookForDate[t].defSelect,i=[{value:"",label:$t("当前操作时间")}],_.each(e,function(e,t){(o.includes(e.type)&&"custom"===e.define_type||a.includes(e.api_name))&&i.push({value:t,label:e.label})}),i)}),r.renderSelector())})},renderSelectorDom:function(){var e=$(".render-content",this.$el).find(".switch-pricebook-date"),a="";_.each(this.priceBookForDate,function(e,t){a+='<div class="switch-pricebook-date-li">\n\t\t\t\t\t\t<span class="switch-pricebook-date-name">'.concat(e.name,'</span>\n\t\t\t\t\t\t<span class="switch-pricebook-date-select"  data-key=').concat(t,'></span>\n\t\t\t\t\t\t<span class="switch-pricebook-date-msg">').concat($t("适配价目表有效期"),"</span>\n\t\t\t\t\t</div>")}),e.html(a)},renderSelector:function(){var i=this;CRM._cache.match_price_book_valid_field&&_.each(JSON.parse(CRM._cache.match_price_book_valid_field),function(e,t){i.priceBookForDate[t].value=e}),_.each(i.priceBookForDate,function(e,t){var a=i.$el.find('.switch-pricebook-date-select[data-key="'+t+'"]');i.initFXSelect(a,e,t)})},initFXSelect:function(e,t,a){var i=this;e[0]&&(this._allComps[a]&&this._allComps[a].destroy&&this._allComps[a].destroy(),this._allComps[a]=FxUI.create({wrapper:e[0],template:' <fx-select\n\t\t\t\t\t\t\t\tref="el1"\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:el-style="selectStyle"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t@change="change"\n\t\t\t\t\t\t\t  ></fx-select>',data:function(){return{value:t.value,selectStyle:{width:"200px",height:"30px"},options:t.selector}},methods:{change:function(e){i.priceBookForDate[a].value=e||""}}}))},createBtn:function(){var t=this;this._allComps.saveBtn&&this._allComps.saveBtn.destroy&&this._allComps.saveBtn.destroy(),this._allComps.resetBtn&&this._allComps.resetBtn.destroy&&this._allComps.resetBtn.destroy(),t.$el.find(".switch-pricebook-date-save")[0]&&(this._allComps.saveBtn=FxUI.create({wrapper:t.$el.find(".switch-pricebook-date-save")[0],template:'<fx-button type="primary" :size="size" @click="onClick">'.concat($t("保存设置"),"</fx-button>"),data:function(){return{size:"small"}},methods:{onClick:function(e){t.setPriceByDateConfig()}}}),this._allComps.resetBtn=FxUI.create({wrapper:t.$el.find(".switch-pricebook-date-reset")[0],template:'<fx-button  :size="size" @click="onClick">'.concat($t("恢复初始设置"),"</fx-button>"),data:function(){return{size:"small"}},methods:{onClick:function(e){t.resetPriceByDateConfig()}}}))},setDateIsShow:function(){var e=CRM._cache.ignore_price_book_valid_period?"removeClass":"addClass";this.$el.find(".switch-pricebook-date-box")[e]("hide")},resetPriceByDateConfig:function(){var a=this;_.each(this.priceBookForDate,function(e,t){e.value="",a._allComps[t].value=""}),this.setPriceByDateConfig()},setPriceByDateConfig:function(){var a={};_.each(this.priceBookForDate,function(e,t){a[t]=e.value||""}),a=JSON.stringify(a),CRM.util.setConfigValue({key:"match_price_book_valid_field",value:a}).then(function(e){c.remind(1,$t("设置成功"))})},amortizeCheck:function(e){var t=$(e.target);CRM.util.setConfigValue({key:"gift_amortize_basis",value:t.attr("data-attr")}).then(function(e){0==e.Result.StatusCode?(CRM._cache.giftAmortizeBasis=amortize,c.remind(1,$t("配置成功!"))):(c.remind(3,$t("配置失败!")),t.removeClass("mn-selected"),t.parent("p").siblings("p").children(".mn-radio-item").addClass("mn-selected"))})},destroy:function(){_.each(this._allComps,function(e){e&&e.destroy&&e.destroy()}),this._allComps=this.priceBookForDate=null,this.remove()}})});
define("crm-setting/ppmanage/template/attribute-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1. " + ((__t = $t("该开关一旦开启，不可关闭。")) == null ? "" : __t) + "</li> <li>2. " + ((__t = $t("开启当前开关时，请先开启 [ 价目表开关 ]。")) == null ? "" : __t) + '</li> <li class="crm-intro-warning intro-retract-8">' + ((__t = $t("注：开启属性后，将不能开启 [ 商品设置 ] [ 多单位设置 ] [ 促销设置 ]")) == null ? "" : __t) + "</li> <li>3. " + ((__t = $t("开关开启：通过属性属性值关联产品，供报价或下单时使用。属性有启用/禁用，关联产品等功能，实现产品业务差异化")) == null ? "" : __t) + '</li> <li class="intro-retract-8">3.1 ' + ((__t = $t("[ 属性 ]、[ 产品属性价目表 ] 对象显示")) == null ? "" : __t) + '</li> <li class="crm-intro-warning intro-retract-24">' + ((__t = $t("注：同时订单产品、报价单明细：显示 [ 属性 ]、[ 产品属性价目表名称 ]")) == null ? "" : __t) + ' </li> <li class="intro-retract-8">3.2 ' + ((__t = $t("[订单 ]、[报价单 ] “从历史订单”入口屏蔽")) == null ? "" : __t) + '</li> <li class="intro-retract-8">3.3 ' + ((__t = $t("[ 产品属性价目表 ] 使用")) == null ? "" : __t) + '</li> <li class="intro-retract-24"> 3.3.1 ' + ((__t = $t("销售订单、报价单中，产品适配的价目表带出规则不变（默认带出客户的最优可售范围中产品的最优的价目表）")) == null ? "" : __t) + '</li> <li class="intro-retract-24">3.3.2 ' + ((__t = $t("影响点：产品、订单产品、报价单明细中，产品价格以及折扣的取值")) == null ? "" : __t) + '</li> <li class="intro-retract-48">a、' + ((__t = $t("开启的[ 产品属性价目表 ] 适配某价目表后， [ 产品属性选择后 ] 价格取值： [ 产品属性价目表 ] 中 [ 当前属性价格 ] ，折扣带出依据 [ 产品属性价目表 ] 中[ 定价方式 ] 判断")) == null ? "" : __t) + '</li> <li class="intro-retract-56">① ' + ((__t = $t("[ 定价方式 ] 为 [ 价目表折扣 ] ，[ 折扣 ] 取值 [ 适配的价目表 ] 中：产品折扣")) == null ? "" : __t) + '</li> <li class="intro-retract-56">② ' + ((__t = $t("[ 定价方式 ] 为 [ 指定折扣 ] ，[ 折扣 ] 取值 [ 产品属性价目表 ] 中：产品属性的[ 折扣 ]")) == null ? "" : __t) + '</li> <li class="intro-retract-48"> b、' + ((__t = $t("[ 产品属性价目表 ] 未适配某价目表， [ 产品属性选择后 ] 取值：依据 [ 价目表 ] 中 [ 价目表售价 ] 和 [ 价目表折扣 ]")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off "> <label class="title">' + ((__t = $t("属性开启开关")) == null ? "" : __t) + '</label> <div class="attribute-switch switch-sec' + ((__t = openAttribute ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="on-off nonstandard-attribute"> <label class="title">' + ((__t = $t("非标属性开启开关")) == null ? "" : __t) + '</label> <div class="nonstandard-attribute-switch switch-sec' + ((__t = openNsAttribute ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="important-info" >' + ((__t = $t("开启或关闭的操作，只可操作一次，请慎重操作")) == null ? "" : __t) + "</div>";
        }
        return __p;
    };
});
define("crm-setting/ppmanage/template/availablerange-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<!-- * @Description: * @Author: sunsh * @Date: 2022-03-09 14:29:52 * @LastEditors: sunsh * @LastEditTime: 2022-04-11 16:29:27 --> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("开启价目表后不开启可售范围")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("开启可售范围")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("可售范围规定")) == null ? "" : __t) + '</li> <li class="crm-intro-warning">4.' + ((__t = $t("开启可售范围需要设置")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("如果开启可售范围且没有设置范围规则")) == null ? "" : __t) + '<span class="crm-intro-warning">' + ((__t = $t("没有可售产品")) == null ? "" : __t) + "</span></li> <li>6." + ((__t = $t("当可售产品范围取不到价目表价格时")) == null ? "" : __t) + "</li> <br> <li>" + ((__t = $t("客户名称只按照单独添加算")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("可售范围开启开关")) == null ? "" : __t) + '</label> <div class="availableRange switch-sec' + ((__t = openAvailablerange ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <!-- 开启可售价目表 暂时不展示--> <div class="j-checkbox" style="display:none;"> <div class="availablePriceBook crm-g-checkbox ' + ((__t = openAvailablePriceBook ? " state-active j-set-on" : " j-set-config") == null ? "" : __t) + " " + ((__t = !openAvailablerange || openAvailablePriceBook ? "disabled" : "") == null ? "" : __t) + '"></div> <span class="text">' + ((__t = $t("crm.ppmanage.available_price_book")) == null ? "" : __t) + '</span> <i class="crm-ui-title btn-addfromdpm-tip" data-pos="bottom" data-title="' + ((__t = $t("crm.ppmanage.available_price_book_tip_close")) == null ? "" : __t) + " " + ((__t = $t("crm.ppmanage.available_price_book_tip_check")) == null ? "" : __t) + '"> <span class="crm-doclink"></span> </i> <!-- 注：开关开启后，不可关闭 --> <div class="crm-intro-warning important-info">' + ((__t = $t("crm.ppmanage.available_price_book_warning")) == null ? "" : __t) + '</div> </div> <div class="pricebook-line"></div> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("开启执行可售范围优先级")) == null ? "" : __t) + "：</li> <li>&nbsp;&nbsp;" + ((__t = $t("客户的适用价目表获取逻辑为")) == null ? "" : __t) + "：</li> <li>&nbsp;&nbsp;&nbsp;&nbsp;a." + ((__t = $t("不同类型可售范围文案")) == null ? "" : __t) + '<span class="crm-doclink j-availablerangep-case"></span></li> <li>&nbsp;&nbsp;&nbsp;&nbsp;b.' + ((__t = $t("同类型可售范围文案")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("不开执行可售范围优先级文案")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("是否执行可售范围优先级")) == null ? "" : __t) + '</label> <div class="availableRangePriority switch-sec' + ((__t = openAvailableRangePriority ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="pricebook-line"></div> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("客户相同条件的可售范围开启查重校验")) == null ? "" : __t) + "。</li> <li>&nbsp;&nbsp;(1)" + ((__t = $t("客户相同条件的可售范围为")) == null ? "" : __t) + "。</li> <li>&nbsp;&nbsp;(2)" + ((__t = $t("客户相同条件的可售范围中")) == null ? "" : __t) + "。</li> <li>2." + ((__t = $t("可售范围创建者")) == null ? "" : __t) + "。</li> <li>3." + ((__t = $t("此开关可根据业务需要反复开启和关闭")) == null ? "" : __t) + '。</li> </ul> </div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("可售范围查重开关")) == null ? "" : __t) + '</label> <div class="availableRangeDuplicatedCheck switch-sec' + ((__t = openAvailableRangeDuplicatedCheck ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="pricebook-line"></div> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("功能开启，CRM销售订单可根据过滤字段过滤客户的可售产品")) == null ? "" : __t) + "。</li> <li>2." + ((__t = $t("过滤字段仅支持“销售订单业务类型”、“单选”和“查找关联”字段，通过自定义函数编写过滤逻辑")) == null ? "" : __t) + "。</li> <li>3." + ((__t = $t("此功能只作用于“销售订单”对象")) == null ? "" : __t) + "。</li> <li>4." + ((__t = $t("功能开启，当启用订货通后，商城也会基于过滤条件展示对应的商品数据，同时")) == null ? "" : __t) + '。</li> <li style="text-indent: 1em;">4.1.' + ((__t = $t("订货通所有WEB页面→组件列表：增加“过滤可售范围”组件，管理员可以在页面中，删除此组件，也可以编辑“组件名称”、“组件ICON”")) == null ? "" : __t) + '。</li> <li style="text-indent: 1em;">4.2.' + ((__t = $t("订货通移动端，个人中心，增加“商品范围”菜单，同时支持配置“菜单显示位置”、“菜单名称”、“显示ICON”等")) == null ? "" : __t) + '。</li> <li style="text-indent: 1em;">4.3.' + ((__t = $t("用户首次进入WEB渠道门户或APP小程序时，根据后台的过滤条件，要求客户选择过滤条件，选择好数据后，根据函数过滤可售范围，加载商品列表、购物车等数据")) == null ? "" : __t) + '。</li> <li style="text-indent: 1em;">4.4.' + ((__t = $t("用户非首次进入，WEB端可点击右上角的“过滤可售范围”进行切换，移动端可在个人中心/商品列表/购物车中切换")) == null ? "" : __t) + "。</li> <li>5." + ((__t = $t("此功能开关默认为“关闭”状态，启用后可再次关闭")) == null ? "" : __t) + '。</li> <br> <li class="crm-intro-warning">' + ((__t = $t("注：该插件支持按各种条件过滤可售范围，具体能力可在自定义函数中实现。当变更过滤条件时，请保持过滤字段与函数同时变更")) == null ? "" : __t) + '。</li> </ul> </div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("基于订单类型、项目过滤可售范围")) == null ? "" : __t) + '</label> <div class="availableRangeBasedOnOrder switch-sec' + ((__t = openAvailableRangeFilter ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="availableRangeFillter" style="display: ' + ((__t = openAvailableRangeFilter ? "flex" : "none") == null ? "" : __t) + ';"> <div class="input-wrapper filter-field"> <label for="" class="fm-lb" style="width:80px;">' + ((__t = $t("过滤字段")) == null ? "" : __t) + '</label> <input type="text" style="width:240px;"class="b-g-ipt fm-ipt ipt-disabled" disabled> </div> <div class="input-wrapper correlation-function"> <label for="" class="fm-lb" style="width:80px;">' + ((__t = $t("关联函数")) == null ? "" : __t) + '</label> <input type="text" style="width:240px;"class="b-g-ipt fm-ipt ipt-disabled" disabled> </div> <div class="input-edit-button j-range-order">' + ((__t = $t("编辑")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/ppmanage/template/coupon-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1. " + ((__t = $t("优惠券政策开关，开启后不可关闭。")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off mb20"> <label class="title">' + ((__t = $t("开启优惠券")) == null ? "" : __t) + '</label> <div class="switch-coupon switch-sec' + ((__t = openCoupon ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"></div> </div> ';
            if (isGrayPaperCoupon) {
                __p += ' <div class="on-off mb20"> <label class="title">' + ((__t = $t("启用纸质券业务")) == null ? "" : __t) + '</label> <div class="switch-papercoupon switch-sec' + ((__t = openPaperCoupon ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"></div> </div> ';
            }
        }
        return __p;
    };
});
define("crm-setting/ppmanage/template/pricebook-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("价目表一旦开启不可关闭。")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.价目表会影响订单里的内容")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.价目表开启后会默认给租客一个标准价目表")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("crm.开启价目表后需提前调整OpenAPI")) == null ? "" : __t) + "</li> </ul> </div> ";
            if (start === "error") {
                __p += ' <p><span class="pp-set-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span><a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + "</a></p> ";
            } else {
                __p += " ";
                if (start) {
                    __p += " <span>" + ((__t = $t("价目表已开启并已完成初始化")) == null ? "" : __t) + "</span> ";
                }
                __p += ' <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("价目表开启开关")) == null ? "" : __t) + '</label> <div class="switch-pricebook switch-sec' + ((__t = start ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> ';
            }
            __p += ' <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("是否强制执行价目表优先级最优价格")) == null ? "" : __t) + '</label> <div class="switch-pricebook-priority switch-sec' + ((__t = priceBookPriority ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> ';
            if (CRM.util.isGrayScale("CRM_PRICEBOOK_PERIOD")) {
                __p += ' <div class="pricebook-line"></div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("价目表适配单据时间配置")) == null ? "" : __t) + '</label> <div class="switch-pricebookByDate switch-sec' + ((__t = ignore_price_book_valid_period ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"></div> </div> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("价目表适配时间说明1")) == null ? "" : __t) + "；</li> <li>2." + ((__t = $t("价目表适配时间说明2")) == null ? "" : __t) + "；</li> <li>3." + ((__t = $t("配置的时间")) == null ? "" : __t) + "：</li> <li>&nbsp;&nbsp;3.1" + ((__t = $t("价目表适配时间说明3_1")) == null ? "" : __t) + ";</li> <li>&nbsp;&nbsp;3.2" + ((__t = $t("价目表适配时间说明3_2")) == null ? "" : __t) + ';</li> </ul> </div> <div class="switch-pricebook-date-box hide"> <div class="switch-pricebook-date"></div> <div class="switch-pricebook-date-btns"> <div class="switch-pricebook-date-btn switch-pricebook-date-save"></div> <div class="switch-pricebook-date-btn switch-pricebook-date-reset"></div> </div> </div> ';
            }
            __p += ' <div class="pricebook-line"></div> <!-- 价目表明细支持有效期配置 --> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("crm.ppmanage.price_book_product_valid_period.intro_1")) == null ? "" : __t) + "；</li> <li>2." + ((__t = $t("crm.ppmanage.price_book_product_valid_period.intro_2")) == null ? "" : __t) + "；</li> <li>3." + ((__t = $t("crm.ppmanage.price_book_product_valid_period.intro_3")) == null ? "" : __t) + '；</li> </ul> </div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("crm.ppmanage.price_book_product_valid_period")) == null ? "" : __t) + '</label> <div class="switch-pricebookPeriod switch-sec' + ((__t = priceBookProductValidPeriod ? " on j-set-on disabled" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <div class="pricebook-line"></div> <!-- 价目表明细支持产品阶梯价 --> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("crm.ppmanage.price_book_product_tiered_price.intro_1")) == null ? "" : __t) + "；</li> ";
            var intro_2 = $t("crm.ppmanage.price_book_product_tiered_price.intro_2", {
                start_count: $t("crm.ppmanage.price_book_product_tiered_price.start_count"),
                end_count: $t("crm.ppmanage.price_book_product_tiered_price.end_count")
            });
            __p += " <li>2." + ((__t = intro_2) == null ? "" : __t) + "；</li> <li>3." + ((__t = $t("crm.ppmanage.price_book_product_tiered_price.intro_3")) == null ? "" : __t) + "；</li> <li>4." + ((__t = $t("crm.ppmanage.price_book_product_tiered_price.intro_4")) == null ? "" : __t) + '；</li> </ul> </div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("crm.ppmanage.price_book_product_tiered_price")) == null ? "" : __t) + '</label> <div class="switch-pricebookLadder switch-sec' + ((__t = priceBookProductTieredPrice ? " on j-set-on disabled" : " j-set-config") == null ? "" : __t) + '"> </div> </div> <!-- 价目表明细，选产品页面，支持选择本价目表已选产品 --> ';
            if (priceBookProductValidPeriod || priceBookProductTieredPrice) {
                __p += ' <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("crm.ppmanage.price_book_product_selected.intro_1")) == null ? "" : __t) + "；</li> <li>2." + ((__t = $t("crm.ppmanage.price_book_product_selected.intro_2")) == null ? "" : __t) + "；</li> <li>3." + ((__t = $t("crm.ppmanage.price_book_product_selected.intro_3")) == null ? "" : __t) + "；</li> <li>4." + ((__t = $t("crm.ppmanage.price_book_product_selected.intro_4")) == null ? "" : __t) + '；</li> </ul> </div> <div class="on-off pp-switch"> <label class="title">' + ((__t = $t("crm.ppmanage.price_book_product_selected")) == null ? "" : __t) + '</label> <div class="switch-pricebookproductSelected switch-sec highlight' + ((__t = priceBookSelectProduct ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"> </div> </div> ';
            }
        }
        return __p;
    };
});
define("crm-setting/ppmanage/template/pricepolicy-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1. " + ((__t = $t("高级定价开关")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off mb20"> <label class="title">' + ((__t = $t("开启高级定价")) == null ? "" : __t) + '</label> <div class="switch-pricepolicy switch-sec' + ((__t = openPricePolicy ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"></div> </div> ';
            if (openPricePolicy) {
                __p += ' <div class="crm-p20 " style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = $t("允许切换政策：")) == null ? "" : __t) + '</div> <div class="price-policy-switchpolicy-config crm-pricepolicy-config"></div> <div class="config-desc crm-intro"> <p>' + ((__t = $t("注意:如果有限额限量控制，禁止切换可能导致无法下单")) == null ? "" : __t) + '</p> </div> </div> <div class="mn-radio-box crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = $t("赠品费用分摊依据：")) == null ? "" : __t) + '</div> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item j-radio-amortize ' + ((__t = giftAmortizeBasis == "price_book_price" ? "mn-selected" : "") == null ? "" : __t) + '" data-attr="price_book_price"></span> <span class="label" style="margin-left:8px;cursor: pointer;">' + ((__t = $t("价目表价格")) == null ? "" : __t) + '</span> </p> <p style="line-height:32px; margin-bottom:4px;"> <span class="mn-radio-item j-radio-amortize ' + ((__t = giftAmortizeBasis == "product_price" ? "mn-selected" : "") == null ? "" : __t) + '" data-attr="product_price"></span> <span class="label" style="margin-left:8px;cursor:pointer;">' + ((__t = $t("产品档案价格")) == null ? "" : __t) + '</span> </p> </div> <div class="crm-p20" style="line-height: 32px;"> <div class="mn-radio-title">' + ((__t = $t("赠品参与分摊：")) == null ? "" : __t) + '</div> <div class="price-policy-gift-config"></div> </div> <div class="crm-p20 price-policy-title-config crm-pricepolicy-config" > <div class="title-config-title">' + ((__t = $t("选产品页，各产品显示的内容")) == null ? "" : __t) + ':</div> <div class="title-config-wrapper"> <div class="title-config-content"></div> <div class="config-desc crm-intro"> <p>' + ((__t = $t("移动端选产品页，各产品将展示“全部政策名称”，建议创建价格政策时，只创建一条价格规则（或者多条规则的产品范围完全相同），将政策名称置为具体的促销信息，（如：满200减20）起到在选产品时可以查看产品的具体促销信息的作用。")) == null ? "" : __t) + '</p> </div> </div> <div class="price-policy-gift-config"></div> </div> ';
            }
        }
        return __p;
    };
});
define("crm-setting/ppmanage/template/rebate-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1. " + ((__t = $t("返利单政策开关，开启后不可关闭。")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off mb20"> <label class="title">' + ((__t = $t("开启返利单")) == null ? "" : __t) + '</label> <div class="switch-pricepolicy switch-sec' + ((__t = openRebate ? " on j-set-on" : " j-set-config") == null ? "" : __t) + '"></div> </div>';
        }
        return __p;
    };
});
define("crm-setting/ppmanage/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("价格管理")) == null ? "" : __t) + '</span></h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-tab"> <span data-render="pricebook-box" class="j-switch-tab ">' + ((__t = $t("crm.价目表")) == null ? "" : __t) + '</span> <span data-render="availablerange-box" class="j-switch-tab ">' + ((__t = $t("可售范围设置")) == null ? "" : __t) + "</span> ";
            if (isGreyProductAttribute) {
                __p += ' <span data-render="attribute-box" class="j-switch-tab ">' + ((__t = $t("属性")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (advancedPricing) {
                __p += ' <span data-render="pricepolicy-box" class="j-switch-tab ">' + ((__t = $t("高级定价")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (isCoupon) {
                __p += ' <span data-render="coupon-box" class="j-switch-tab ">' + ((__t = $t("优惠券")) == null ? "" : __t) + "</span> ";
            }
            __p += " ";
            if (isRebate) {
                __p += ' <span data-render="rebate-box" class="j-switch-tab ">' + ((__t = $t("返利单")) == null ? "" : __t) + "</span> ";
            }
            __p += ' </div> <div class="mn-radio-box crm-p20" style="line-height: 35px;"> <div class="tab-con"> <div class="item render-content"> </div> </div> </div> </div>';
        }
        return __p;
    };
});