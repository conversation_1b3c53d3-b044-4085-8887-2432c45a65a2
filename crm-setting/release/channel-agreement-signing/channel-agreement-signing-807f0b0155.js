define("crm-setting/channel-agreement-signing/channel-agreement-signing",["crm-modules/common/util"],function(t,n,e){t("crm-modules/common/util");var i=Backbone.View.extend({initialize:function(n){this.setElement(n.wrapper),this.init()},init:function(n){var e=this;t.async("vcrm/sdk",function(n){n.getComponent("channelaccess").then(function(n){e.initView(n.default)})})},initView:function(n){new Vue({el:this.$el[0],template:"<div style='height:100%;width:100%;background:#fff;overflow-x:auto'>\n                                <channelAccess></channelAccess>\n                          </div>",components:{channelAccess:n},data:function(){return{}}})},destroy:function(){}});e.exports=i});