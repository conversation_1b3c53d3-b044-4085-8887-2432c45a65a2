define("crm-setting/datapermissions/advancesetting/advancesetting",["../template/advance-setting-html"],function(t,e,a){var i=t("../template/advance-setting-html");a.exports=Backbone.View.extend({initialize:function(){var t,e=this;FS.util.getUserAttribute("datapermissionsRelevantTeam")?(this.$el.html(i({useNew:!0})),t=(t=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions).filter(function(t){return 1!=t.objectType}),this.crmDataPermissionsObj={},t.forEach(function(t){e.crmDataPermissionsObj[t.ObjectDescribeApiName]=t}),this.createSearch(),this.createTable(),this.roleTypeToObj={},this.queryTeamRoleDescribeList().then(function(){e.createAddDialog()}),this.queryTeamRoleMaxNumber().then(function(){e.search.limitNum=e.limitRolesCount,void 0!==e.rolesCount&&(e.search.showAdd=!0,e.limitRolesCount<=e.rolesCount?e.search.disabledAdd=!0:e.search.disabledAdd=!1)})):this.$el.html(i({useNew:!1,txt1:$t("datapermissions.advanceSetting.txt").split("{{str}}")[0],txt2:$t("datapermissions.advanceSetting.txt").split("{{str}}")[1]}))},createSearch:function(){var t=this;this.search=FxUI.create({wrapper:this.el.querySelector(".top"),replaceWrapper:!0,template:'\n\t\t\t\t\t\t<div class="top">\n\t\t\t\t\t\t\t<fx-input\n\t\t\t\t\t\t\t\tplaceholder="'.concat($t("datashare_xiangguantuandui_placeholder_input"),'"\n\t\t\t\t\t\t\t\tv-model="keyword"\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t\tclearable\n\t\t\t\t\t\t\t\tprefix-icon="fx-icon-search"\n\t\t\t\t\t\t\t\t@change="onSearch"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t\t\t<div class="add">\n\t\t\t\t\t\t\t\t<span v-show="limitNum"><i class="fx-icon-info"></i> \n\t\t\t\t\t\t\t\t{{ $t("datashare_xiangguantuandui_note",{num:limitNum}) }}\n\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t<fx-button size="mini" type="primary" :disabled="disabledAdd" v-show="showAdd" @click="handlerClickAdd">\n\t\t\t\t\t\t\t\t').concat($t("新建"),"\n\t\t\t\t\t\t\t\t</fx-button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t"),data:function(){return{keyword:"",limitNum:"",showAdd:!1,disabledAdd:!1}},methods:{onSearch:function(){var e=this;this.keyword?t.vmTable.tableData=t.vmTable.tableDataOrigin.filter(function(t){if(-1<t.roleName.toLowerCase().indexOf(e.keyword.toLowerCase()))return t}):t.vmTable.tableData=t.vmTable.tableDataOrigin.map(function(t){return t})},handlerClickAdd:function(){t.addDialog&&(t.addDialog.showDialog=!0),t.addDialog.title=$t("add_xiangguantuanduijuesequanxian")}},mounted:function(){}})},createAddDialog:function(){var i=this;this.addDialog=FxUI.create({template:'\n\t\t\t\t\t<fx-dialog\n\t\t\t\t\t\t:visible.sync="showDialog"\n\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t:title="title"\n\t\t\t\t\t\t:close-on-click-modal="false"\n\t\t\t\t\t>\n\t\t\t\t\t\t<fx-form\n\t\t\t\t\t\t\tref="form"\n\t\t\t\t\t\t\t:model="form"\n\t\t\t\t\t\t\tlabel-position="top"\n\t\t\t\t\t\t\t:show-message="false"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<fx-input\n\t\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\t\tv-model="form.roleName"\n\t\t\t\t\t\t\t\tprop="roleName"\n\t\t\t\t\t\t\t\tplaceholder="'.concat($t("请输入"),'"\n\t\t\t\t\t\t\t\t:maxlength="30"\n\t\t\t\t\t\t\t\tshow-word-limit\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t:disabled="form._editFields.length && form._editFields.indexOf(\'roleName\')==-1"\n\t\t\t\t\t\t\t\tlabel="').concat($t("角色名称"),'"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t</fx-input>\n\n\t\t\t\t\t\t\t<fx-select\n\t\t\t\t\t\t\t\tprop="entityIds"\n\t\t\t\t\t\t\t\tv-model="form.entityIds"\n\t\t\t\t\t\t\t\t:options="objOptions"\n\t\t\t\t\t\t\t\tlabel="').concat($t("适用对象"),'"\n\t\t\t\t\t\t\t\trequired\n\t\t\t\t\t\t\t\tfilterable\n\t\t\t\t\t\t\t\tmultiple\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\t:disabled="form._editFields.length && form._editFields.indexOf(\'entityIds\')==-1"\n\t\t\t\t\t\t\t\t:elStyle="{ width: \'100%\' }"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t</fx-select>\n\n\t\t\t\t\t\t\t<fx-input\n\t\t\t\t\t\t\t\tlabel="').concat($t("备注"),'"\n\t\t\t\t\t\t\t\tv-model="form.description"\n\t\t\t\t\t\t\t\tprop="description"\n\t\t\t\t\t\t\t\ttype="textarea"\n\t\t\t\t\t\t\t\tplaceholder="').concat($t("请输入"),'"\n\t\t\t\t\t\t\t\tmaxlength="200"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\tshow-word-limit\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t</fx-input>\n\t\t\t\t\t\t</fx-form>\n\t\t\t\t\t\t<div slot="footer" class="dialog-footer">\n\t\t\t\t\t\t\t<fx-button type="primary" @click="submitAdd" size="small" :loading="loading"\n\t\t\t\t\t\t\t\t>').concat($t("确定"),'\n\t\t\t\t\t\t\t</fx-button>\n\t\t\t\t\t\t\t<fx-button @click="cancel" size="small"\n\t\t\t\t\t\t\t\t>').concat($t("取消"),"\n\t\t\t\t\t\t\t</fx-button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</fx-dialog>\n\t\t\t\t"),data:function(){return{showDialog:!1,objOptions:[],row:null,title:$t("add_xiangguantuanduijuesequanxian"),form:{roleName:"",entityIds:[],description:"",roleType:"",_editFields:[]},loading:!1}},watch:{showDialog:function(t){var e=this;t||this.$nextTick(function(){e.title=$t("add_xiangguantuanduijuesequanxian"),e.form={roleName:"",entityIds:[],description:"",roleType:"",_editFields:[]},e.row=null,e.getObjOptions(),e.loading=!1,e.$nextTick(function(){e.$refs.form.clearValidate()})})}},methods:{submitAdd:function(){var a=this;this.$refs.form.validate(function(t,e){t&&(a.form.roleType?a.updateTeamRole():a.createTeamRole())})},cancel:function(){this.showDialog=!1},getObjOptions:function(t){var e=this,a=[],a=-1<["1","2","3","4"].indexOf(t)?i.roleTypeToObj[t]||[]:i.roleTypeToObj[0]||[];this.objOptions.splice(0),this.form.entityIds.forEach(function(t){-1==a.indexOf(t)&&a.push(t)}),a.forEach(function(t){"0"==t?e.objOptions.unshift({label:$t("全部对象"),value:"0"}):i.crmDataPermissionsObj[t]?e.objOptions.push({label:i.crmDataPermissionsObj[t].ObjectDescribeDisplayName,value:t}):e.row&&e.row.entityInfo[t]&&e.objOptions.push({label:e.row.entityInfo[t],value:t,disabled:!0})})},createTeamRole:function(){var a=this;this.loading=!0,CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/createTeamRole",data:{roleName:this.form.roleName,entityIds:this.form.entityIds,description:this.form.description,roleType:this.form.roleType},success:function(t){if(a.loading=!1,0==t.Result.StatusCode)if(t.Value.success)a.cancel(),i.vmTable.queryTeamRoles(),FxUI.Message({message:$t("操作成功"),type:"success"});else{var e=[];try{JSON.parse(t.Value.errMessage).forEach(function(t){i.crmDataPermissionsObj[t]&&e.push(i.crmDataPermissionsObj[t].ObjectDescribeDisplayName)})}catch(t){}201136024==t.Value.errCode?e.length&&FxUI.MessageBox.alert('"'+e.join(", ")+'"'+$t("datapermissions_advance_txt_201136024"),{type:"error"}):FS.util.alert(t.Value.errMessage,{type:"error"})}else t.Result.FailureMessage&&FS.util.alert(t.Result.FailureMessage,{type:"error"})},fail:function(){a.loading=!1}})},updateTeamRole:function(){var a=this;this.loading=!0,CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/updateTeamRole",data:{roleName:this.form.roleName,entityIds:this.form.entityIds,description:this.form.description,roleType:this.form.roleType},success:function(t){if(a.loading=!1,0==t.Result.StatusCode)if(t.Value.success)a.cancel(),setTimeout(function(){FxUI.Message({message:$t("操作成功"),type:"success"}),i.vmTable.queryTeamRoles()},1500);else{var e=[];try{JSON.parse(t.Value.errMessage).forEach(function(t){i.crmDataPermissionsObj[t]&&e.push(i.crmDataPermissionsObj[t].ObjectDescribeDisplayName)}),a.row&&JSON.parse(t.Value.errMessage).forEach(function(t){t=a.row.entityInfo[t];t&&-1==e.indexOf(t)&&e.push(t)})}catch(t){}201136029==t.Value.errCode?e.length&&FxUI.MessageBox.confirm("<strong>"+$t("datapermissions_advance_txt")+'</strong><br><span>"'+a.form.roleName+'" '+$t("datapermissions_advance_txt2")+' "'+e.join("、")+'" '+$t("datapermissions_advance_txt3")+"</span>",$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning",dangerouslyUseHTMLString:!0}):201136030==t.Value.errCode?e.length&&FxUI.MessageBox.alert($t("datapermissions_advance_txt_201136030",{str:e.join("、")}),{type:"error"}):201136024==t.Value.errCode?e.length&&FxUI.MessageBox.alert('"'+e.join(", ")+'"'+$t("datapermissions_advance_txt_201136024"),{type:"error"}):FS.util.alert(t.Value.errMessage,{type:"error"})}else t.Result.FailureMessage&&FS.util.alert(t.Result.FailureMessage,{type:"error"})},fail:function(){a.loading=!1}})}},created:function(){this.getObjOptions()}})},createTable:function(){var n=this;this.vmTable=FxUI.create({wrapper:this.el.querySelector(".table-wrap"),template:'\n\t\t\t\t\t<fx-table\n\t\t\t\t\t\t:data="tableData"\n\t\t\t\t\t\tborder\n\t\t\t\t\t\tstyle="width: 100%">\n\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\tprop="roleName"\n\t\t\t\t\t\t\tlabel="'.concat($t("角色名称"),'"\n\t\t\t\t\t\t\twidth="150">\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\tprop="entityStr"\n\t\t\t\t\t\t\tlabel="').concat($t("适用对象"),'"\n\t\t\t\t\t\t\twidth="150">\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\tprop="roleType"\n\t\t\t\t\t\t\twidth="150"\n\t\t\t\t\t\t\tlabel="').concat($t("系统编码"),'">\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\tprop="description"\n\t\t\t\t\t\t\t:resizable="false"\n\t\t\t\t\t\t\tlabel="').concat($t("备注"),'">\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\tprop="status"\n\t\t\t\t\t\t\tlabel="').concat($t("状态"),'"\n\t\t\t\t\t\t\t:resizable="false"\n\t\t\t\t\t\t\twidth="100">\n\t\t\t\t\t\t\t<template slot-scope="scope">{{ scope.row.status == 0 ? $t(\'已停用\') : $t(\'启用中\') }}</template>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t\t<fx-table-column\n\t\t\t\t\t\t\tlabel="').concat($t("操作"),'"\n\t\t\t\t\t\t\t:resizable="false"\n\t\t\t\t\t\t\tfixed="right"\n\t\t\t\t\t\t\twidth="150">\n\t\t\t\t\t\t\t<template slot-scope="scope"><a v-if="scope.row._editFields.indexOf(\'status\')>-1" style="margin-right: 5px;cursor:pointer;" @click="handleClickEnable(scope.row)">{{ scope.row.status == 1 ? $t("停用") : $t("启用") }}</a><a v-if="scope.row._editFields.length" @click="handleClickEdit(scope.row)" style="margin-right: 5px;cursor:pointer;">').concat($t("编辑"),'</a><a v-if="scope.row._editFields.indexOf(\'_canEdit\')>-1" @click="handleClickDel(scope.row)"  style="margin-right: 5px;cursor:pointer;">').concat($t("删除"),"</a></template>\n\t\t\t\t\t\t</fx-table-column>\n\t\t\t\t\t</fx-table>\n\t\t\t\t"),data:function(){return{tableData:[]}},methods:{handleClickEnable:function(t){this.updateTeamRoleStatus(t)},handleClickEdit:function(t){n.addDialog.form={roleName:t.roleName,entityIds:t.entityIds,description:t.description,roleType:t.roleType,_editFields:t._editFields},n.addDialog.row=t,n.addDialog.getObjOptions(t.roleType),n.addDialog.showDialog=!0,n.addDialog.title=$t("xiangguantuanduijuesequanxian_edit")},handleClickDel:function(t){var e=this;FxUI.MessageBox.confirm($t("confirm_to_delete"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){e.deleteTeamRole(t)}).catch(function(){})},queryTeamRoles:function(){var i=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/queryTeamRole",data:{},success:function(t){0==t.Result.StatusCode&&(i.tableDataOrigin=t.Value.teamRoleList||[],n.rolesCount=0,i.tableDataOrigin.forEach(function(e){var a=[];switch(e.entityIds.forEach(function(t){a.push(e.entityInfo[t])}),i.$set(e,"entityStr",a.join(", ")),i.$set(e,"_editFields",[]),e.roleType){case"1":break;case"2":case"3":e._editFields.push("roleName","status");break;case"4":e._editFields.push("roleName");break;default:e._editFields.push("roleName","entityIds","status","_canEdit"),n.rolesCount++}}),void 0!==n.limitRolesCount&&(n.search.showAdd=!0,n.limitRolesCount<=n.rolesCount?n.search.disabledAdd=!0:n.search.disabledAdd=!1),i.tableData=i.tableDataOrigin.map(function(t){return t}))}})},updateTeamRoleStatus:function(a){var i="0"==a.status?"1":"0";CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/updateTeamRoleStatus",data:{roleType:a.roleType,status:i},success:function(t){if(0==t.Result.StatusCode)if(t.Value.success)a.status=i,FxUI.Message({message:$t("操作成功"),type:"success"});else{var e=[];try{JSON.parse(t.Value.errMessage).forEach(function(t){n.crmDataPermissionsObj[t]&&e.push(n.crmDataPermissionsObj[t].ObjectDescribeDisplayName)}),a&&JSON.parse(t.Value.errMessage).forEach(function(t){t=a.entityInfo[t];t&&-1==e.indexOf(t)&&e.push(t)})}catch(t){}201136029==t.Value.errCode?e.length&&FxUI.MessageBox.confirm("<strong>"+$t("datapermissions_advance_txt")+'</strong><br><span>"'+a.roleName+'" '+$t("datapermissions_advance_txt2")+' "'+e.join("、")+'" '+$t("datapermissions_advance_txt3")+"</span>",$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning",dangerouslyUseHTMLString:!0}):201136030==t.Value.errCode?e.length&&FxUI.MessageBox.alert($t("datapermissions_advance_txt_201136030",{str:e.join("、")}),{type:"error"}):201136024==t.Value.errCode?e.length&&FxUI.MessageBox.alert('"'+e.join(", ")+'"'+$t("datapermissions_advance_txt_201136024"),{type:"error"}):FS.util.alert(t.Value.errMessage,{type:"error"})}else t.Result.FailureMessage&&FS.util.alert(t.Result.FailureMessage,{type:"error"})}})},deleteTeamRole:function(a){var i=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/deleteTeamRole",data:{roleType:a.roleType},success:function(t){if(0==t.Result.StatusCode)if(t.Value.success)i.queryTeamRoles(),FxUI.Message({message:$t("操作成功"),type:"success"});else{var e=[];try{JSON.parse(t.Value.errMessage).forEach(function(t){n.crmDataPermissionsObj[t]&&e.push(n.crmDataPermissionsObj[t].ObjectDescribeDisplayName)}),a&&JSON.parse(t.Value.errMessage).forEach(function(t){t=a.entityInfo[t];t&&-1==e.indexOf(t)&&e.push(t)})}catch(t){}201136029==t.Value.errCode?e.length&&FxUI.MessageBox.confirm("<strong>"+$t("datapermissions_advance_txt")+'</strong><br><span>"'+a.roleName+'" '+$t("datapermissions_advance_txt2")+' "'+e.join("、")+'" '+$t("datapermissions_advance_txt3")+"</span>",$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning",dangerouslyUseHTMLString:!0}):201136030==t.Value.errCode?e.length&&FxUI.MessageBox.alert($t("datapermissions_advance_txt_201136030",{str:e.join("、")}),{type:"error"}):201136024==t.Value.errCode?e.length&&FxUI.MessageBox.alert('"'+e.join(", ")+'"'+$t("datapermissions_advance_txt_201136024"),{type:"error"}):FS.util.alert(t.Value.errMessage,{type:"error"})}else t.Result.FailureMessage&&FS.util.alert(t.Result.FailureMessage,{type:"error"})}})}},created:function(){this.tableDataOrigin=[],this.queryTeamRoles()}})},queryTeamRoleDescribeList:function(){var e=this;return CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/queryTeamRoleDescribeList",data:{},success:function(t){0==t.Result.StatusCode&&(e.roleTypeToObj=t.Value||{})}})},queryTeamRoleMaxNumber:function(){var e=this;return CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/queryTeamRoleMaxNumber",data:{},success:function(t){0==t.Result.StatusCode&&(e.limitRolesCount=t.Value||0)}})},handlerClickAdd:function(){},show:function(){this.$el.show()},hide:function(){this.$el.hide()}})});
define("crm-setting/datapermissions/advancesetting/advancesetting0",["../template/advance-setting-html"],function(t,e,i){var n=t("../template/advance-setting-html");i.exports=Backbone.View.extend({initialize:function(){this.$el.html(n({txt1:$t("datapermissions.advanceSetting.txt").split("{{str}}")[0],txt2:$t("datapermissions.advanceSetting.txt").split("{{str}}")[1]}))},show:function(){this.$el.show()},hide:function(){this.$el.hide()}})});
define("crm-setting/datapermissions/basicsetting/basicsetting",["crm-modules/common/util","../template/basic-setting-html","../template/basic-content-html","../common/config"],function(e,t,i){var a=e("crm-modules/common/util"),s=e("../template/basic-setting-html"),n=e("../template/basic-content-html"),c=e("../common/config"),o="mn-selected",r="."+o;i.exports=Backbone.View.extend({events:{"click .mn-radio-item":"onCheck","click .j-save":"onSave","click .j-reset":"onReset"},initialize:function(){var t=this;t.el.innerHTML=s(),t.$wrap=t.$(".content-wrap"),a.getObjectDataPermissions(function(e){e=n(t.setDisabled(e));t.$wrap.html(e)})},setDisabled:function(e){for(var t=0;t<e.myObjectDataPermissions.length;t++){var i=e.myObjectDataPermissions[t];-1!=["SpecificationObj","ProductCategoryObj"].indexOf(i.ObjectDescribeApiName)?i.disabled1="disabled-selected":-1!=["SpecificationValueObj","PersonnelObj","UnitInfoObj"].indexOf(i.ObjectDescribeApiName)&&(i.disabled1="disabled-selected",i.disabled2="disabled-selected",i.disabled3="disabled-selected");try{i.ObjectDescribeDisplayName=i.ObjectDescribeDisplayName.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/'/g,"\\'").replace(/\//g,"\\/").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t")}catch(e){}}return e},show:function(){this.$el.show()},hide:function(){this.$el.hide()},onCheck:function(e){var t=$(e.currentTarget),i=t.closest(".mn-radio-box");return t.hasClass("disabled-selected")?e.stopPropagation():(this.$(".b-g-btn-disabled").removeClass("b-g-btn-disabled"),$(".mn-radio-item",i).each(function(e,t){$(t).removeClass(o)}),t.toggleClass(o)),!1},setObjectDataPermissions:function(e,t,i,s){a.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/updateCommonPrivilegeList",data:t,success:function(e){e.Result&&0===e.Result.StatusCode&&i()}},_.extend({errorAlertModel:1},s||{}))},initDefaultObjectDataPermissions:function(e,t,i,s){a.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/initializeCommonPrivilege",data:t,success:function(e){e.Result&&0===e.Result.StatusCode&&i()}},_.extend({errorAlertModel:1},s||{}))},onSave:function(e){var t=this.$(".mn-radio-box"),i=[],s=[];$(e.currentTarget).hasClass("b-g-btn-disabled")||(_.each(t,function(e,t){$(e).data("userdefined")?s.push({ObjectDescribeApiName:$(e).data("objectid"),ObjectDescribeDisplayName:$(e).data("objectname"),PermissionType:$(e).find(r).data("permissiontype")}):i.push({ObjectID:$(e).data("objectid"),ObjectName:$(e).data("objectname"),PermissionType:$(e).find(r).data("permissiontype")})}),this.setObjectDataPermissions({ObjectDataPermissionInfos:i},{ObjectDataPermissionInfos:s},function(){a.remind(1,$t("保存成功"))}))},onReset:function(){var t=this;FxUI.MessageBox.confirm($t("crm_datapermissions_basicsetting_basic-data-permission",{},"您即将重置“基础数据权限”至初始设置，这将清除所有客户自定义设置。请确认是否继续？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){var e=t.$(".mn-radio-box"),i=[];_.each(e,function(e,t){$(e).data("userdefined")&&i.push({ObjectDescribeApiName:$(e).data("objectid"),ObjectDescribeDisplayName:$(e).data("objectname"),PermissionType:1})}),t.$wrap.html('<div class="crm-loading"></div>'),t.initDefaultObjectDataPermissions({ObjectDataPermissionInfos:c.ObjectDataPermissionInfos},{ObjectDataPermissionInfos:i},function(){a.remind(1,$t("保存成功")),a.getObjectDataPermissions(function(e){t.$wrap.html(n(t.setDisabled(e)))})})}).catch(function(){})}})});
define("crm-setting/datapermissions/common/basetable",["crm-modules/common/util","crm-widget/table/table","./config"],function(e,t,n){var s=e("crm-modules/common/util"),a=e("crm-widget/table/table"),i=e("./config"),o=Backbone.View.extend({config:"",delegateEvents:function(){return this.events=_.extend({},this.events,this.__publicEvents),o.__super__.delegateEvents.call(this)},__publicEvents:{"click .j-add":"onAdd","click .j-enable":"onEnable","click .j-del":"onDel","click .j-copy":"onCopy"},initialize:function(e){this.widgets={},this.opts=e,this.setElement(e.$el),this.setCurrentSpecialValues(),this.userGroups=JSON.parse(sessionStorage.getItem("userGroups")),this.userRoles=JSON.parse(sessionStorage.getItem("userRoles")),this.render()},render:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets={},this.initTable()},show:function(e){this.widgets.dt.setParam(e,!0,!0)},setCurrentSpecialValues:function(){_.extend(this,i[this.config](this.options.scopeType))},_parseColumns:function(e){return e},initTable:function(){var s=this,e=s.widgets.dt=new a({$el:s.opts.$el,url:s.url,requestType:"FHHApi",showFilerBtn:!0,showMultiple:!0,trHandle:!1,batchBtns:[{text:$t("停用"),className:"j-enable stop"},{text:$t("启用"),className:"j-enable"},{text:$t("删除"),className:"j-del"}],openStart:s.opts.openStart||!1,operate:s.getOperateConfig(),postData:{},columns:s._parseColumns(s.columns),paramFormat:function(e){e=s.parseTableParam(e);return s.tableParams=e},formatData:function(e){return{totalCount:e.Page&&e.Page.TotalCount||0,data:s.formatTableData(e.SharedObjectInfos)}},formatDataAsync:function(n){return s.formatTableDataAsync?new Promise(function(t,e){s.formatTableDataAsync(n.data).then(function(e){n.data=e,t(n)})}):Promise.resolve(n)},initComplete:function(e){s.afterInitTable(e)}});e.on("trclick",function(e,t,n){n.hasClass("j-enable")?s.onEnable(n[0],e):n.hasClass("j-edit")&&!n.hasClass("disable")?s.onEdit(e,n):n.hasClass("j-del")?s.onDel(e):n.hasClass("j-copy")&&s.onCopy(e,n)}),e.on("checkbox.click",function(e,t,n){s.onCheckboxClick(n)}),e.on("term.bactchHide",function(e,t,n){s.onCheckboxClick(0)}),setTimeout(function(){e.resize()},0)},getOperateConfig:function(){return null},getNameById:function(e,t){var n;switch(t){case"member":n=-1e4==e?$t("系统"):(s.getEmployeeById(e)||{}).name||"--";break;case"group":n=(s.getCircleById(e)||{}).name||"--";break;case"usergroup":n=(_.findWhere(this.userGroups,{id:e})||{}).name||"--";break;case"role":n=(_.findWhere(this.userRoles,{id:e})||{}).name||"--"}return n},parseTableParam:function(){},formatTableData:function(){},afterInitTable:function(){},onAdd:function(){},onEdit:function(){},onEnable:function(){},onDel:function(){},onCopy:function(){},onCheckboxClick:function(e){var t=this,n=t.$el.attr("data-index")||t.$el.css("z-index");e?(t.$el.attr("data-index",n),t.$el.css("z-index",1e3)):t.$el.css("z-index",t.$el.attr("data-index"))},resize:function(){this.widgets.dt.resize()},setParam:function(e){this.widgets.dt.setParam(e||{},!0)},refresh:function(e){this.widgets.dt.setParam(e||{},!0,!0)},destroy:function(){this.undelegateEvents(),_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets={}}});return o});
define("crm-setting/datapermissions/common/config",[],{objectTypeMap:{"-1":$t("全部"),1:$t("crm.销售线索"),2:$t("crm.客户"),3:$t("crm.联系人"),4:$t("crm.产品"),6:$t("crm.退款"),7:$t("crm.销售流程"),8:$t("crm.商机"),9:$t("开票"),11:$t("订单"),12:$t("crm.退货单"),13:$t("crm.拜访"),14:$t("crm.拜访动作"),15:$t("盘点动作"),16:$t("crm.合同"),17:$t("crm.线索池"),18:$t("crm.公海"),19:$t("竞争对手"),20:$t("crm.市场活动"),21:$t("盘点"),22:$t("角色"),23:$t("销售记录"),24:$t("附件"),25:$t("crm.相关团队"),26:$t("费用"),29:$t("字段管理"),39:$t("地址"),40:$t("开票信息")},ObjectDataPermissionInfos:[{ObjectID:1,ObjectName:$t("crm.销售线索"),PermissionType:1},{ObjectID:2,ObjectName:$t("crm.客户"),PermissionType:1},{ObjectID:3,ObjectName:$t("crm.联系人"),PermissionType:1},{ObjectID:8,ObjectName:$t("crm.商机"),PermissionType:1},{ObjectID:4,ObjectName:$t("crm.产品"),PermissionType:2},{ObjectID:11,ObjectName:$t("订单"),PermissionType:1},{ObjectID:16,ObjectName:$t("crm.合同"),PermissionType:1},{ObjectID:9,ObjectName:$t("开票"),PermissionType:1},{ObjectID:12,ObjectName:$t("crm.退货单"),PermissionType:1},{ObjectID:6,ObjectName:$t("crm.退款"),PermissionType:1},{ObjectID:13,ObjectName:$t("crm.拜访"),PermissionType:1},{ObjectID:20,ObjectName:$t("crm.市场活动"),PermissionType:1}],filterType:["out_department","out_employee","group","multi_level_select_one","time","phone_number","image","file_attachment","email","auto_number","formula","location","master_detail","count","signature","component","component","component","","town","village","html_rich_text","big_text","object_reference","object_reference_many","data_visibility_range"],filterApiname:["date_of_birth","day_of_birth","month_of_birth","year_of_birth","mobile","tel","name_order","contact_status","refresh_duplicated_version","owner_department","high_seas_name","last_modified_by","last_follower","assigner_id","lock_status","order_amount","returned_goods_amount","refund_amount","invoice_amount","is_user_define_work_flow","receivable_amount","payment_money_to_confirm","bill_money_to_confirm","remaining_time","delivered_amount_sum","out_tenant_id","last_deal_closed_amount","config","order_by","resale_count","refresh_duplicated_version","remind_days","is_collected","enterprise_wechat_user_id","completed_field_quantity","is_duplicated","is_fixed_flow","account_path","extend_days"],getTableColumns:function(e,t){var i=this,o={1:"member",2:"group",3:"usergroup",4:"role",8:"group"};return[{data:"DataSourceName2",title:$t("数据来源"),isFilter:!0,dataType:8,referRule:"All",stop:!0,filterCompare:[14,9,10,23],renderTips:function(e,t,r){var a;return"group"==o[r.DataSourceType]?(a=[],(r=FS.contacts.getCircleById(r.DataSourceID))&&r.ancestors&&r.ancestors.slice(0).forEach(function(e){e=FS.contacts.getCircleById(e);e&&a.push(e.name)}),a.push(r.name),a.join("/")):""},render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"ObjectType",title:$t("共享数据"),isFilter:!0,dataType:7,multiple:!0,filterCompare:[1,2,8,9,10,13,14],options:e||[],render:function(e,t,r){var a=i.objectTypeMap[r.ObjectType]||r.ObjectDescribeDisplayName||"--";return 0===r.Status?'<span style="color:#ccc;">'+a+"</span>":a}},{data:"TargetName2",title:$t("共享范围"),dataType:8,referRule:"All",filterCompare:[14,9,10],isFilter:!1,renderTips:function(e,t,r){var a;return"group"==o[r.TargetType]?(a=[],(r=FS.contacts.getCircleById(r.TargetID))&&r.ancestors&&r.ancestors.slice(0).forEach(function(e){e=FS.contacts.getCircleById(e);e&&a.push(e.name)}),a.push(r.name),a.join("/")):""},render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"PermissionType",title:$t("共享权限"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:"-1"},{ItemName:$t("只读"),ItemCode:"1"},{ItemName:$t("读写"),ItemCode:"2"}],render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e}},{data:"Status",title:$t("状态"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:-1},{ItemName:$t("停用"),ItemCode:"0"},{ItemName:$t("启用"),ItemCode:1}],render:function(e,t,r){return 1===r.Status?$t("启用中"):'<span style="color:#ccc;">'+$t("已停用")+"</span>"}},{data:"creator",title:$t("创建人"),isFilter:!0,dataType:8,isId:!0,noGroup:!0,referRule:"Employee",filterCompare:[14,9,10,23],render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"createTime",title:$t("创建时间"),isFilter:!0,dataType:4,filterCompare:[1,2,5,3,4,6,9,10,18,19,20,21,25,26,27,28,29,30,31,32,33,34,35,36],noShortCuts:!0,render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"modifier",title:$t("修改人"),isFilter:!0,dataType:8,isId:!0,noGroup:!0,referRule:"Employee",filterCompare:[14,9,10,23],render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"modifyTime",title:$t("修改时间"),isFilter:!0,dataType:4,noShortCuts:!0,filterCompare:[1,2,5,3,4,6,9,10,18,19,20,21,25,26,27,28,29,30,31,32,33,34,35,36],render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:null,lastFixed:!0,title:$t("操作"),width:140,render:function(e,t,r){var a="--"===r.DataSourceName||"--"===r.TargetName?"disable":"";return 0===r.Status?'<div><a class="j-edit '+a+'" data-type="1" style="margin-right: 5px;cursor:pointer;">'+$t("edit")+'</a><a class="j-copy" style="margin-right:5px;cursor:pointer;">'+$t("copy")+'</a><a class="j-enable" style="margin-right: 5px;cursor:pointer;"> '+$t("启用")+'</a><a class="j-del" style="cursor:pointer;">'+$t("删除")+"</a></div>":'<div><a class="j-edit '+a+'" data-type="2" style="margin-right: 5px;cursor:pointer;">'+$t("查看")+'</a><a class="j-copy" style="margin-right: 5px;cursor:pointer;">'+$t("copy")+'</a><a class="j-enable stop" style="margin-right: 5px;cursor:pointer;">'+$t("停用")+"</a></div>"}}]},getMyObjectByFieldTableColumns:function(){return[{data:"ruleName",title:$t("规则名称"),isFilter:!0,filterCompare:[1,2,8,9,10,11,12,22,23],render:function(e,t,r){return 1!==r.status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"displayName",title:$t("共享数据"),isFilter:!0,multiple:!0,dataType:7,filterCompare:[1,2,8,9,10,13,14],render:function(e,t,r){var a=r.displayName;return 1!==r.status?'<span style="color:#ccc;">'+a+"</span>":a}},{data:"receiveIds",title:$t("共享范围"),isFilter:!1,dataType:8,filterCompare:[14,9,10],referRule:"All",render:function(t,e,r){return t&&r.receiveIdsGroupTitle&&Object.keys(r.receiveIdsGroupTitle).forEach(function(e){t=t.replace(e,'<span title="'+r.receiveIdsGroupTitle[e]+'">'+e+"</span>")}),1!==r.status?'<span style="color:#ccc;">'+t+"</span>":t||"--"}},{data:"permission",title:$t("共享权限"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:-1},{ItemName:$t("只读"),ItemCode:1},{ItemName:$t("读写"),ItemCode:2}],render:function(e,t,r){return 1!==r.status?'<span style="color:#ccc;">'+e+"</span>":e}},{data:"status",title:$t("状态"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:-1},{ItemName:$t("已停用"),ItemCode:0},{ItemName:$t("启用中"),ItemCode:1},{ItemName:$t("计算中"),ItemCode:2},{ItemName:$t("计算失败"),ItemCode:3}],render:function(e,t,r){return 1==r.status?e:'<span style="color:#ccc;">'+e+"</span>"}},{data:"creator",title:$t("创建人"),isFilter:!0,dataType:8,isId:!0,noGroup:!0,referRule:"Employee",filterCompare:[14,9,10,23],render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"createTime",title:$t("创建时间"),isFilter:!0,dataType:4,filterCompare:[1,2,5,3,4,6,9,10,18,19,20,21,25,26,27,28,29,30,31,32,33,34,35,36],noShortCuts:!0,render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"modifier",title:$t("修改人"),isFilter:!0,dataType:8,isId:!0,noGroup:!0,referRule:"Employee",filterCompare:[14,9,10,23],render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"modifyTime",title:$t("修改时间"),isFilter:!0,dataType:4,noShortCuts:!0,filterCompare:[1,2,5,3,4,6,9,10,18,19,20,21,25,26,27,28,29,30,31,32,33,34,35,36],render:function(e,t,r){return 0===r.Status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:null,lastFixed:!0,title:$t("操作"),width:140,render:function(e,t,r){return 0==r.status||3==r.status?'<div><a class="j-edit" data-type="1" style="margin-right: 5px;cursor:pointer;">'+$t("edit")+'</a><a class="j-copy" style="margin-right:5px;cursor:pointer;">'+$t("copy")+'</a><a class="j-del" style="margin-right: 5px;cursor:pointer;">'+$t("删除")+'</a><a class="j-enable" style="cursor:pointer;">'+$t("启用")+"</a></div>":2==r.status?'<div><a class="j-edit" data-type="2" style="cursor:pointer;">'+$t("查看")+'</a><a class="j-copy" style="margin-right:5px;cursor:pointer;">'+$t("copy")+"</a></div>":'<div><a class="j-edit" data-type="2" style="margin-right:5px;cursor:pointer;">'+$t("查看")+'</a><a class="j-copy" style="margin-right:5px;cursor:pointer;">'+$t("copy")+'</a><a class="j-enable stop" style="cursor:pointer;">'+$t("停用")+"</a></div>"}}]},myObjectByDatasource:function(e){return{url:"/EM1HNCRM/API/v1/object/data_privilege/service/getAllShareRuleList",enableUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/enableOrDisableShareRule",delUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/delShareRules",columns:this.getTableColumns([],e),typeMap:{1:"member",2:"group",3:"usergroup",4:"role",8:"group"},typePrefixMap:{1:"【人员】",2:"【部门】",3:"【用户组】",4:"【角色】",8:"【组织】"}}},myObjectByField:function(){return{url:"/EM1HNCRM/API/v1/object/data_privilege/service/getAllFieldShares",enableUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/changeFieldShareStatus",delUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/delFieldShare",columns:this.getMyObjectByFieldTableColumns(),typeMap:{0:"member",2:"group",1:"usergroup",4:"role",8:"group"},typePrefixMap:{0:"【人员】",2:"【部门】",1:"【用户组】",4:"【角色】",8:"【组织】"}}},exportList:function(){return{url:"/EM1HNCRM/API/v1/object/data_privilege/service/getFieldShares",columns:this.getMyObjectByFieldTableColumns()}}});
define("crm-setting/datapermissions/common2/basetable",["crm-modules/common/util","crm-widget/table/table","./config"],function(e,t,n){var s=e("crm-modules/common/util"),i=e("crm-widget/table/table"),a=e("./config"),o=Backbone.View.extend({config:"",delegateEvents:function(){return this.events=_.extend({},this.events,this.__publicEvents),o.__super__.delegateEvents.call(this)},__publicEvents:{"click .j-add":"onAdd","click .j-enable":"onEnable","click .j-del":"onDel"},initialize:function(e){this.widgets={},this.opts=e,this.setElement(e.$el),this.setCurrentSpecialValues(),this.userGroups=JSON.parse(sessionStorage.getItem("userGroups")),this.userRoles=JSON.parse(sessionStorage.getItem("userRoles")),this.render()},render:function(){_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets={},this.initTable()},show:function(e){this.widgets.dt.setParam(e,!0,!0)},setCurrentSpecialValues:function(){_.extend(this,a[this.config]())},_parseColumns:function(e){return e},initTable:function(){var s=this,e=s.widgets.dt=new i({$el:s.opts.$el,url:s.url,requestType:"FHHApi",showFilerBtn:!0,showMultiple:!0,trHandle:!1,batchBtns:[{text:$t("停用"),className:"j-enable stop"},{text:$t("启用"),className:"j-enable"},{text:$t("删除"),className:"j-del"}],openStart:s.opts.openStart||!1,operate:s.getOperateConfig(),postData:{},columns:s._parseColumns(s.columns),paramFormat:function(e){e=s.parseTableParam(e);return s.tableParams=e},formatData:function(e){var t=e.SharedObjectInfos;return-1<s.url.indexOf("getShareRuleGroups")&&(t=e.SharedGroupInfos),{totalCount:e.Page&&e.Page.TotalCount||0,data:s.formatTableData(t)}},initComplete:function(e){s.afterInitTable(e)}});e.on("trclick",function(e,t,n){n.hasClass("j-enable")?s.onEnable(n[0],e):n.hasClass("j-edit")&&!n.hasClass("disable")?s.onEdit(e,n):n.hasClass("j-del")&&s.onDel(e)}),e.on("checkbox.click",function(e,t,n){s.onCheckboxClick(n)}),e.on("term.bactchHide",function(e,t,n){s.onCheckboxClick(0)}),setTimeout(function(){e.resize()},0)},getOperateConfig:function(){return null},getNameById:function(e,t){var n;switch(t){case"member":n=(s.getEmployeeById(e)||{}).name||"--";break;case"group":n=(s.getCircleById(e)||{}).name||"--";break;case"usergroup":n=(_.findWhere(this.userGroups,{id:e})||{}).name||"--";break;case"role":n=(_.findWhere(this.userRoles,{id:e})||{}).name||"--"}return n},parseTableParam:function(){},formatTableData:function(){},afterInitTable:function(){},onAdd:function(){},onEdit:function(){},onEnable:function(){},onDel:function(){},onCheckboxClick:function(e){var t=this,n=t.$el.attr("data-index")||t.$el.css("z-index");e?(t.$el.attr("data-index",n),t.$el.css("z-index",1e3)):t.$el.css("z-index",t.$el.attr("data-index"))},resize:function(){this.widgets.dt.resize()},setParam:function(e){this.widgets.dt.setParam(e||{},!0)},refresh:function(e){this.widgets.dt.setParam(e||{},!0,!0)},destroy:function(){this.undelegateEvents(),_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets={}}});return o});
define("crm-setting/datapermissions/common2/config",[],{objectTypeMap:{"-1":$t("全部"),1:$t("crm.销售线索"),2:$t("crm.客户"),3:$t("crm.联系人"),4:$t("crm.产品"),6:$t("crm.退款"),7:$t("crm.销售流程"),8:$t("crm.商机"),9:$t("开票"),11:$t("订单"),12:$t("crm.退货单"),13:$t("crm.拜访"),14:$t("crm.拜访动作"),15:$t("盘点动作"),16:$t("crm.合同"),17:$t("crm.线索池"),18:$t("crm.公海"),19:$t("竞争对手"),20:$t("crm.市场活动"),21:$t("盘点"),22:$t("角色"),23:$t("销售记录"),24:$t("附件"),25:$t("crm.相关团队"),26:$t("费用"),29:$t("字段管理"),39:$t("地址"),40:$t("开票信息")},ObjectDataPermissionInfos:[{ObjectID:1,ObjectName:$t("crm.销售线索"),PermissionType:1},{ObjectID:2,ObjectName:$t("crm.客户"),PermissionType:1},{ObjectID:3,ObjectName:$t("crm.联系人"),PermissionType:1},{ObjectID:8,ObjectName:$t("crm.商机"),PermissionType:1},{ObjectID:4,ObjectName:$t("crm.产品"),PermissionType:2},{ObjectID:11,ObjectName:$t("订单"),PermissionType:1},{ObjectID:16,ObjectName:$t("crm.合同"),PermissionType:1},{ObjectID:9,ObjectName:$t("开票"),PermissionType:1},{ObjectID:12,ObjectName:$t("crm.退货单"),PermissionType:1},{ObjectID:6,ObjectName:$t("crm.退款"),PermissionType:1},{ObjectID:13,ObjectName:$t("crm.拜访"),PermissionType:1},{ObjectID:20,ObjectName:$t("crm.市场活动"),PermissionType:1}],filterType:["out_department","out_employee","group","multi_level_select_one","date","time","phone_number","image","file_attachment","email","url","auto_number","formula","location","master_detail","count","signature","component","component","component","","town","village","html_rich_text","object_reference","object_reference_many"],filterApiname:["date_of_birth","day_of_birth","month_of_birth","year_of_birth","mobile","tel","name_order","contact_status","refresh_duplicated_version","owner_department","high_seas_name","last_modified_by","last_follower","assigner_id","lock_status","order_amount","returned_goods_amount","refund_amount","invoice_amount","is_user_define_work_flow","receivable_amount","payment_money_to_confirm","bill_money_to_confirm","remaining_time","delivered_amount_sum","out_tenant_id","last_deal_closed_amount","config","order_by","resale_count","refresh_duplicated_version","remind_days","is_collected","enterprise_wechat_user_id","completed_field_quantity","is_duplicated","is_fixed_flow","account_path","extend_days"],getTableColumns:function(e){function a(e){return e<10?"0"+e:e}return[{data:"dataSourceName",title:$t("数据来源"),isFilter:!0,type:"text",dataType:8,hoverTitle:!0,referRule:"All",filterCompare:[14,9,10,23],render:function(e,t,r){return 0===r.status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"objectDescribeDisplayName",title:$t("共享数据"),hoverTitle:!0,type:"text",isFilter:!0,dataType:7,multiple:!0,filterCompare:[1,2,8,9,10,13,14],options:e||[],render:function(e,t,r){return e=r.objectDescribeDisplayName,0===r.status?'<span style="color:#ccc;">'+e+"</span>":e}},{data:"targetName",title:$t("共享范围"),hoverTitle:!0,type:"text",isFilter:!0,dataType:8,referRule:"All",filterCompare:[14,9,10,23],render:function(e,t,r){return 0===r.status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"permissionType",title:$t("共享权限"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:"-1"},{ItemName:$t("只读"),ItemCode:"1"},{ItemName:$t("读写"),ItemCode:"2"}],render:function(e,t,r){return 0===r.status?'<span style="color:#ccc;">'+e+"</span>":e}},{data:"status",title:$t("状态"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:-1},{ItemName:$t("停用"),ItemCode:"0"},{ItemName:$t("启用"),ItemCode:1}],render:function(e,t,r){return 1===r.status?$t("启用中"):'<span style="color:#ccc;">'+$t("已停用")+"</span>"}},{data:"createTime",title:$t("创建时间"),dataType:4,render:function(e,t,r){e=new Date(e);return e.getFullYear()+"-"+a(e.getMonth()+1)+"-"+a(e.getDate())+" "+a(e.getHours())+":"+a(e.getMinutes())+":"+a(e.getSeconds())}},{data:"modifyTime",title:$t("最后修改时间"),dataType:4,render:function(e,t,r){e=new Date(e);return e.getFullYear()+"-"+a(e.getMonth()+1)+"-"+a(e.getDate())+" "+a(e.getHours())+":"+a(e.getMinutes())+":"+a(e.getSeconds())}},{data:null,lastFixed:!0,title:$t("操作"),width:140,render:function(e,t,r){var a="--"===r.dataSourceName||"--"===r.targetName?"disable":"";return 0===r.status?'<div><a class="j-edit '+a+'" data-type="1" style="margin-right: 5px;cursor:pointer;">'+$t("编辑")+'</a><a class="j-enable" style="margin-right: 5px;cursor:pointer;"> '+$t("启用")+'</a><a class="j-del" style="cursor:pointer;">'+$t("删除")+"</a></div>":'<div><a class="j-edit '+a+'" data-type="2" style="margin-right: 5px;cursor:pointer;">'+$t("查看")+'</a><a class="j-enable stop" style="cursor:pointer;">'+$t("停用")+"</a></div>"}}]},getMyObjectByFieldTableColumns:function(){return[{data:"ruleName",title:$t("规则名称"),isFilter:!0,filterCompare:[1,2,8,9,10,11,12,22,23],render:function(e,t,r){return 1!==r.status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"displayName",title:$t("共享数据"),isFilter:!0,multiple:!0,dataType:7,filterCompare:[1,2,8,9,10,13,14],render:function(e,t,r){var a=r.displayName;return 1!==r.status?'<span style="color:#ccc;">'+a+"</span>":a}},{data:"receiveIds",title:$t("共享范围"),isFilter:!1,dataType:8,filterCompare:[14,9,10],referRule:"All",render:function(e,t,r){return 1!==r.status?'<span style="color:#ccc;">'+e+"</span>":e||"--"}},{data:"permission",title:$t("共享权限"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:-1},{ItemName:$t("只读"),ItemCode:1},{ItemName:$t("读写"),ItemCode:2}],render:function(e,t,r){return 1!==r.status?'<span style="color:#ccc;">'+e+"</span>":e}},{data:"status",title:$t("状态"),isFilter:!0,dataType:6,filterCompare:[2,9,10,13,14],notExtend:!0,options:[{ItemName:$t("全部"),ItemCode:-1},{ItemName:$t("已停用"),ItemCode:0},{ItemName:$t("启用中"),ItemCode:1},{ItemName:$t("计算中"),ItemCode:2},{ItemName:$t("计算失败"),ItemCode:3}],render:function(e,t,r){return 1==r.status?e:'<span style="color:#ccc;">'+e+"</span>"}},{data:null,lastFixed:!0,title:$t("操作"),width:140,render:function(e,t,r){return 0==r.status||3==r.status?'<div><a class="j-edit" data-type="1" style="margin-right: 5px;cursor:pointer;">'+$t("编辑")+'</a><a class="j-enable" style="margin-right: 5px;cursor:pointer;">'+$t("启用")+'</a><a class="j-del" style="cursor:pointer;">'+$t("删除")+"</a></div>":2==r.status?'<div><a class="j-edit" data-type="2" style="cursor:pointer;">'+$t("查看")+"</a></div>":'<div><a class="j-edit" data-type="2" style="margin-right: 5px;cursor:pointer;">'+$t("查看")+'</a><a class="j-enable stop" style="cursor:pointer;">'+$t("停用")+"</a></div>"}}]},myObjectByDatasource:function(){return{url:"/EM1HNCRM/API/v1/object/data_privilege/service/getShareRuleGroups",enableUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/enableOrDisableShareRuleGroup",delUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/delShareRuleGroups",columns:this.getTableColumns(),typeMap:["member","usergroup","group","","role"],typePrefixMap:["","【用户组】","【部门】|【部门含子部门】","","【角色】"]}},myObjectByField:function(){return{url:"/EM1HNCRM/API/v1/object/data_privilege/service/getAllFieldShares",enableUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/changeFieldShareStatus",delUrl:"/EM1HNCRM/API/v1/object/data_privilege/service/delFieldShare",columns:this.getMyObjectByFieldTableColumns(),typeMap:{0:"member",1:"usergroup",2:"group",4:"role"}}}});
define("crm-setting/datapermissions/datapermissions",["crm-modules/common/util","manage-modules/manage-utils/manage-utils","./template/index-html"],function(i,e,a){var n=i("crm-modules/common/util"),t=i("manage-modules/manage-utils/manage-utils"),s=i("./template/index-html");a.exports=Backbone.View.extend({initialize:function(e){var a=this;a.setElement(e.wrapper),a.getFunctionCodes().then(function(){a.isOpenOrg(function(){a.el.innerHTML=s({fns:CRM.control.functionCodes,openOrgStatus:a.openOrgStatus}),a.$(".crm-tab .item").eq(0).click()})})},isOpenOrg:function(a){var t=this;n.FHHApi({url:"/EM2HORG/Management/Enterprise/GetEnterpriseConfig",data:{key:"openOrganization"},success:function(e){0==e.Result.StatusCode?t.openOrgStatus=1==e.Value.value:t.openOrgStatus=!1,a&&a()},error:function(){t.openOrgStatus=!1,a&&a()}},{errorAlertModel:1})},getFunctionCodes:function(){return new Promise(function(a,e){t.FHHApi({url:"/EM2HORG/Management/Permission/GetFunctionCodesByEmployee",data:{appId:"facishare-system"},success:function(e){0==e.Result.StatusCode?(CRM.control.functionCodes=_.map(e.Value.functionCodeVos,function(e){return e.functionCode}),a(CRM.control.functionCodes)):n.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},events:{"click .crm-tab a":"onTab"},onTab:function(e){var e=$(e.target),a=(a=e.attr("href")).split("/=/");return this._render([a[1]],e.index()),!1},_render:function(e,a){var t,n=this,s=n.$(".crm-tab .item");switch(s.removeClass("cur"),s.eq(a).addClass("cur"),n.page1&&n.page1.hide(),n.pageOrganize&&n.pageOrganize.hide(),n.page1b&&n.page1b.hide(),n.page2&&n.page2.hide(),n.page3&&n.page3.hide(),n.page4&&n.page4.hide(),n.page5&&n.page5.hide(),e&&e[0]){case"page1b":n.page1b?n.page1b.show():i.async("./departmentdataright/index",function(e){n.page1b=new e({el:n.$(".department-data-right")}),n.page1b.show()});break;case"organize":n.pageOrganize?n.pageOrganize.show():i.async("./departmentdataright/index",function(e){n.pageOrganize=new e({el:n.$(".organize-permiss"),type:"organize"}),n.pageOrganize.show()});break;case"page2":n.page2?n.page2.show():(t="./datashare/datashare",FS.util.getUserAttribute("datasharenew")&&(t="./datashare2/datashare"),i.async(t,function(e){n.page2=new e({el:n.$(".datashare")}),n.page2.show()}));break;case"page3":n.page3?n.page3.show():i.async("./advancesetting/advancesetting",function(e){n.page3=new e({el:n.$(".datapermissions-advance")}),n.page3.show()});break;case"page4":n.page4?n.page4.show():i.async("./tempright/tempright",function(e){n.page4=new e({el:n.$(".temp-right")}),n.page4.show()});break;case"page5":n.page5?n.page5.show():i.async("./other/other",function(e){n.page5=new e({el:n.$(".other-setting")}),n.page5.show()});break;default:n.page1?n.page1.show():i.async("./basicsetting/basicsetting",function(e){n.page1=new e({el:n.$(".datapermissions-basic")}),n.page1.show()})}},destroy:function(){_.each(["page1","pageOrganize","page1b","page2","page3","page4"],function(e){this[e]&&this[e].destroy&&this[e].destroy(),this[e]&&(this[e]=null)},this),CRM.set("function_codes",null),sessionStorage.removeItem("userGroups"),sessionStorage.removeItem("userRoles"),sessionStorage.removeItem("crmDataPermissions")}})});
define("crm-setting/datapermissions/datashare/datashare",["./template/nav-html","./myobject/myobject"],function(t,e,i){var s=CRM.util,a=t("./template/nav-html"),n=t("./myobject/myobject");i.exports=Backbone.View.extend({events:{"click .leftnav li":"onChangeRole","input .leftnav .quickly-search-input":"onInputSearch","click .leftnav .set-search-reset":"onResetInputSearch"},initialize:function(){var e=this;e.crmDataPermissions=sessionStorage.getItem("crmDataPermissions"),e.crmDataPermissions?e.render():s.getObjectDataPermissions(function(t){e.crmDataPermissions=t,sessionStorage.setItem("crmDataPermissions",JSON.stringify(t)),e.render()})},render:function(){var t=_.isObject(this.crmDataPermissions)?this.crmDataPermissions:JSON.parse(this.crmDataPermissions);t.myObjectDataPermissions=t.myObjectDataPermissions.filter(function(t){return 1!=t.objectType}),this.el.querySelector(".leftnav").innerHTML=a(t),this.$(".all").eq(0).addClass("active"),this.myobject=new n({el:this.$(".rightsection"),ObjectType:this.$(".all").eq(0).attr("data-roleid")}),CRM.api.show_guide({show_type:"tip",key:"datashate_setting_guide",data:[{$target:this.$(".j-add"),pos:"right",appendBody:!0,text:$t("可以根据数据内容设置不同数据共享")}]})},show:function(){this.$el.show(),this.myobject&&this.myobject.resize()},hide:function(){this._guide&&this._guide.destroy(),this.$el.hide()},onChangeRole:function(t){var t=$(t.currentTarget);t.hasClass("active")||t.hasClass("btn-to-add-role")||(t.addClass("active").siblings().removeClass("active"),t=t.data("roleid"),this.myobject&&this.myobject.destroy(),this.myobject=new n({el:this.$(".rightsection"),ObjectType:t}))},onInputSearch:function(t){t.target.value,$(t.target).parent().find(".set-search-reset").show(),this.filterNav($(t.target),t.target.value)},onResetInputSearch:function(t){$(t.target).parent().find(".quickly-search-input").val(""),$(t.target).hide(),this.filterNav($(t.target))},filterNav:function(t,e){var i=t.closest("ul").find("li");if(e){e=e.toLowerCase();for(var s=0;s<i.length;s++){var a,n=$(i[s]);-1!=n.data("roleid")&&((a=n.data("apiname"))||n.hide(),-1<(a+="").toLowerCase().indexOf(e)?n.show():n.hide())}}else i.show()},destroy:function(){_.each(["myobject","_guide"],function(t){this[t]&&(this[t].destroy(),this[t]=null)},this)}})});
define("crm-setting/datapermissions/datashare/myobject/exportlist",["crm-modules/common/util","crm-widget/table/table"],function(t,e,a){var r=t("crm-modules/common/util"),s=t("crm-widget/table/table");a.exports=function(a){var t={taskTable:null,initTasklistTable:function(t){var a=this,e=(this.type="DataShareExport",this.getOptions());this.taskTable&&this.taskTable.destroy(),this.taskTable=new s(e),this.taskTable.stopQueryStatus=!1,this.taskTable.$el.on("click",".j-oprate",function(t){var t=$(t.target),e=t.closest("tr"),e=$.extend({},a.taskTable.getRowData(e));t.hasClass("j-d-download")&&!t.hasClass("disable")&&a.startDownload(e)}),this.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:a.type,operator:"LT",value_type:0}]}]},!0),this.taskTable.start()},getOptions:function(){var e=this,t=this.getColumns();return{$el:a.$wrap,url:"/EM1HPAASBATCH/task/list",title:"",trHandle:!0,openStart:!0,alwaysShowTermBatch:!0,showMultiple:!1,requestType:"FHHApi",postData:{},autoHeight:!1,caption:{},columns:t,rowCallBack:function(t,e){},initComplete:function(t){},getDataBack:function(t,e){return t},formatData:function(t){return e.listData=t&&t.data.map(function(t){return t.taskStatus})||[],0<t.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?e.queryStatus(t.pageNumber,t.pageSize):e.timer&&clearTimeout(e.timer),{totalCount:t.totalCount,data:t.data}},getFullDataBack:function(t){t.Error&&"s211030015"==t.Error.Code&&setTimeout(function(){r.alert(t.Error.Message,function(){location.reload()})},300)}}},queryStatus:function(e,r){var s=this;clearTimeout(s.timer),s.taskTable.stopQueryStatus||CRM.util.FHHApi({url:"/EM1HPAASBATCH/task/list",data:{pageNumber:e||1,pageSize:r||20,wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:s.type,operator:"LT",value_type:0}]}]},success:function(t){var a;0==t.Result.StatusCode&&(0<t.Value.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?(a=!1,t.Value.data.forEach(function(t,e){t&&t.taskStatus!==s.listData[e]&&(a=!0)}),s.timer=a?setTimeout(function(){clearTimeout(s.timer),s.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:s.type,operator:"LT",value_type:0}]}]},!0)},5e3):setTimeout(function(){clearTimeout(s.timer),s.queryStatus(e,r)},3e3)):(clearTimeout(s.timer),s.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:s.type,operator:"LT",value_type:0}]}]},!0)))}})},getColumns:function(){return[{data:"operationType",title:$t("操作类型"),render:function(t){if("DataShareExport"==t)return $t("导出共享规则")}},{data:"taskStatus",title:$t("任务状态"),width:200,render:function(t){switch(t){case"Ready":return $t("已准备");case"Running":return $t("执行中");case"Stopped":return $t("已停止");case"Finished":return $t("已完成")}}},{data:"lastTaskResult",title:$t("导出文件"),width:200,render:function(t){return t.fileNames&&t.fileNames.length?t.fileNames.join(", "):"--"}},{data:"createdBy",width:200,title:$t("创建人")||"--",render:function(t){if(t){t=FS.contacts.getEmployeeById(t);if(t)return t.name}}},{data:"createTime",title:$t("创建时间"),render:function(t){return t?FS.moment(t).format("YYYY-MM-DD HH:mm:ss"):"--"}},{data:"id",width:200,title:$t("任务ID")},{data:"taskStatus",title:$t("操作"),lastFixed:!0,render:function(t,e,a){var r="";return'<span class="options-wrapper"><a href="javascript:;" class="j-oprate j-d-download '+(r="Finished"==t&&a.lastTaskResult&&0!=a.lastTaskResult.warehouseFiles.length?r:"disable")+'" download="">'+$t("下载")+"</a></span>"}}]},startDownload:function(t){if(t.lastTaskResult)for(var e=t.lastTaskResult.warehouseFiles,a=0;a<e.length;a++){var r=document.createElement("a"),s=document.createEvent("HTMLEvents");s.initEvent("click",!1,!1),r.download=e[a].fileName,r.href=FS.BASE_PATH+"/FSC/EM/File/DownloadByPath?path="+e[a].nPath+"&&name="+e[a].fileName,r.dispatchEvent(s),r.click()}}};return t.initTasklistTable(),t.taskTable}});
define("crm-setting/datapermissions/datashare/myobject/importlist",["crm-modules/common/util","crm-widget/table/table"],function(t,a,e){var s=t("crm-modules/common/util"),i=t("crm-widget/table/table");e.exports=function(e){var t={taskTable:null,initTasklistTable:function(t){var e=this,a=(this.type="DataShareImport",this.getOptions());this.taskTable&&this.taskTable.destroy(),this.taskTable=new i(a),this.taskTable.stopQueryStatus=!1,this.taskTable.$el.on("click",".j-oprate",function(t){var t=$(t.target),a=t.closest("tr"),a=$.extend({},e.taskTable.getRowData(a));t.hasClass("j-d-download")&&!t.hasClass("disable")&&e.startDownload(a)}),this.taskTable.setParam({wheres:[]},!0),this.taskTable.start()},getOptions:function(){var a=this,t=this.getColumns();return{$el:e.$wrap,url:"/EM1HPAASBATCH/task/findImportDataShare",title:"",trHandle:!0,openStart:!0,alwaysShowTermBatch:!0,showMultiple:!1,requestType:"FHHApi",postData:{},autoHeight:!1,caption:{},columns:t,rowCallBack:function(t,a){},initComplete:function(t){},getDataBack:function(t,a){return t},formatData:function(t){return a.listData=t&&t.data.map(function(t){return t.taskStatus})||[],0<t.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?a.queryStatus(t.pageNumber,t.pageSize):a.timer&&clearTimeout(a.timer),{totalCount:t.totalCount,data:t.data}},getFullDataBack:function(t){t.Error&&"s211030015"==t.Error.Code&&setTimeout(function(){s.alert(t.Error.Message,function(){location.reload()})},300)}}},queryStatus:function(a,s){var i=this;clearTimeout(i.timer),i.taskTable.stopQueryStatus||CRM.util.FHHApi({url:"/EM1HPAASBATCH/task/findImportDataShare",data:{pageNumber:a||1,pageSize:s||20,wheres:[]},success:function(t){var e;0==t.Result.StatusCode&&(0<t.Value.data.filter(function(t){return"Finished"!=t.taskStatus}).length?(e=!1,t.Value.data.forEach(function(t,a){t&&t.taskStatus!==i.listData[a].taskStatus&&(e=!0)}),i.timer=e?setTimeout(function(){clearTimeout(i.timer),i.taskTable.setParam({wheres:[]},!0)},5e3):setTimeout(function(){clearTimeout(i.timer),i.queryStatus(a,s)},3e3)):(clearTimeout(i.timer),i.taskTable.setParam({wheres:[]},!0)))}})},getColumns:function(){return[{data:"operationType",title:$t("操作类型"),render:function(t){return $t("导入共享规则")}},{data:"taskStatus",title:$t("任务状态"),width:200,render:function(t){switch(t){case"Ready":return $t("已准备");case"Running":return $t("执行中");case"Stopped":return $t("已停止");case"Finished":return $t("已完成")}}},{data:"fileName",title:$t("导入文件"),width:200},{data:"userName",width:200,title:$t("创建人")||"--"},{data:"createTime",title:$t("创建时间"),render:function(t){return t?FS.moment(t).format("YYYY-MM-DD HH:mm:ss"):"--"}},{data:"taskId",width:200,title:$t("任务ID")},{data:"taskStatus",title:$t("操作"),lastFixed:!0,render:function(t,a,e){var s="";return s="Finished"===t&&(s='<span class="options-wrapper"><a href="'+FS.util.getFscLink(e.filePath.split(".")[0],e.fileName,!0)+'" target="_blank" class="j-oprate" >'+$t("下载")+"</a></span>",e.rowCount==e.successRowCount)&&0!=e.successRowCount?'<span class="options-wrapper">'+$t("导入成功")+"</span>":s}}]},startDownload:function(t){var a,e;t.filePath&&(a=document.createElement("a"),(e=document.createEvent("HTMLEvents")).initEvent("click",!1,!1),a.download=t.fileName,a.href=FS.util.getFscLink(t.filePath.split(".")[0],t.fileName,!0),a.dispatchEvent(e),a.click())}};return t.initTasklistTable(),t.taskTable}});
define("crm-setting/datapermissions/datashare/myobject/myobject",["crm-modules/common/util","crm-widget/select/select","crm-widget/selector/selector","../template/list-view-html","../template/import-rules-html","./myobjectbyfield","./myobjectbydatasource","./exportlist","./importlist","./myobjectdialog","base-h5uploader"],function(e,t,i){var a=e("crm-modules/common/util"),o=e("crm-widget/select/select"),s=e("crm-widget/selector/selector"),r=e("../template/list-view-html"),n=e("../template/import-rules-html"),l=e("./myobjectbyfield"),p=e("./myobjectbydatasource"),c=e("./exportlist"),d=e("./importlist"),h=e("./myobjectdialog"),u=e("base-h5uploader");i.exports=Backbone.View.extend({initialize:function(e){this.widgets={},this.el.innerHTML=r(),this.$wrap=this.$(".table-wrap1");var t=localStorage.getItem("datapermission_datashare");t&&(this.scopeType=t,localStorage.removeItem("datapermission_datashare")),this.initDatasourceScope(),this.initShareRange(),this.options={$el:this.$wrap,ObjectType:e.ObjectType},"field"===this.scopeType?this.widgets.myobjectbyfield=new l(this.options):this.widgets.myobjectdatasource=new p(_.extend(this.options,{scopeType:this.scopeType})),this.getDownloadDataShareTemplate()},resize:function(){this.widgets&&(this.widgets.myobjectbyfield&&this.widgets.myobjectbyfield.resize(),this.widgets.myobjectdatasource)&&this.widgets.myobjectdatasource.resize()},events:{"click .j-add":"onAdd","click .j-export":"onExport","click .j-import":"onImport","click .btn-export-list":"showExportList","click .btn-back-data-list":"hideExportList","click .fx-icon-set":"_onTableConfig"},scopeType:"datasource",initDatasourceScope:function(){var t=this,i=t.widgets,e=i.scope,a=[{value:"datasource",name:$t("基于数据负责人")},{value:"datasourcedep_org",name:$t("jiyushujuguishuzuzhi")},{value:"datasourcedep",name:$t("基于数据归属部门")}];"BpmInstance"!=t.options.ObjectType&&"ApprovalInstanceObj"!=t.options.ObjectType&&a.push({value:"field",name:$t("基于条件")}),(e=new o({$wrap:t.$(".scope"),zIndex:1e3,size:1,options:a,defaultVal:t.scopeType})).on("change",function(e){_.each(["myobjectbyfield","myobjectdatasource"],function(e){i[e]&&(i[e].destroy(),i[e]=null)}),"field"===e?i.myobjectbyfield=new l(t.options):i.myobjectdatasource=new p(_.extend(t.options,{scopeType:e})),t.scopeType=e,i.range.clearAll()}),i.scope=e},initShareRange:function(){var t=this.widgets,e=t.range;(e=new s({$wrap:this.$(".range"),label:$t("请选择"),width:190,size:1,v2Size:"mini",single:!1,member:!0,group:{},usergroup:!0,role:!0,stop:!0,excludeItems:{role:["personnelrole"]},foldInput:!0,selectedAfterHideLabel:!0,isFromManage:!0,enableScope:!0})).on("change",function(){var i=e.getSelectedItems();_.each(i,function(e,t){i[t]=_.pluck(e,"id")}),_.each(["myobjectbyfield","myobjectdatasource"],function(e){t[e]&&(t[e].setParamByKey("_shareRange",i),t[e].setParam({}))})}),t.range=e},_onTableConfig:function(){$(".j-dt-icon-tableconfig").click()},onAdd:function(){var t=this,e=t.widgets,e=(e.myobjectdialog&&e.myobjectdialog.destroy(),e.myobjectdialog=new h({title:$t("新建共享规则"),type:"add"}));e.on("success",function(e){t.resetScope(e),FS.setAppStore("crm_observer_emp",null)}),e.show({data:{ObjectType:t.options.ObjectType},showField:!0})},showExportList:function(e){-1==["export","import"].indexOf(e)&&(e="import");var t=this;this.$el.find(".btn-export-list").hide(),this.$el.find(".myobject-header").hide(),this.$el.find(".table-wrap1").hide(),this.$el.find(".btn-back-data-list").show(),this.$el.find(".operationType").show(),this.$el.find(".table-wrap2").show(),this.widgets.exportList&&(this.widgets.exportList.stopQueryStatus=!0,this.widgets.exportList.destroy()),this.widgets.exportList=("export"==e?c:d)({$wrap:this.$el.find(".table-wrap2")}),this.widgets.operationType?this.widgets.operationType.val=e:this.widgets.operationType=FxUI.create({wrapper:".operationType > .select",template:'<fx-select v-model="val" :options="options" size="small" @change="onChange"></fx-select>',data:function(){return{val:e||"import",options:[{value:"import",label:$t("导入共享规则")},{value:"export",label:$t("导出共享规则")}]}},methods:{onChange:function(e){t.widgets.exportList&&(t.widgets.exportList.stopQueryStatus=!0,t.widgets.exportList.destroy()),t.widgets.exportList=("export"==e?c:d)({$wrap:t.$el.find(".table-wrap2")})}}})},hideExportList:function(e){this.$el.find(".btn-export-list").show(),this.$el.find(".myobject-header").show(),this.$el.find(".table-wrap1").show(),this.$el.find(".btn-back-data-list").hide(),this.$el.find(".operationType").hide(),this.$el.find(".table-wrap2").hide(),this.widgets.exportList.stopQueryStatus=!0,this.widgets.exportList.destroy(),this.widgets.exportList=null},onExport:function(){var t=this,e={},i=1;"field"===this.scopeType?(e=this.widgets.myobjectbyfield.tableParams,i=3):(e=this.widgets.myobjectdatasource.tableParams,"datasource"===this.scopeType?i=1:"datasourcedep"===this.scopeType?i=2:"datasourcedep_org"===this.scopeType&&(i=4)),a.FHHApi({url:"/EM1HPAASBATCH/task/exportDataShareTask/create",data:{exportDataShareTaskConfig:{argType:i,arg:e}},success:function(e){0==e.Result.StatusCode&&e.Value&&FxUI.create({template:'<fx-dialog  :visible.sync="show" size="small" max-height="400px" :append-to-body="true" title="'+$t("导出数据共享规则")+'" ><div v-if="!progressStop"><p style="text-align:center;padding: 20px 0 6px;">{{$t("正在收集数据")}}...</p><br><fx-progress :percentage="percentage" color="#4D8CE6" :stroke-width="10" :show-text="false" style="margin-bottom: 44px;"></fx-progress></div><div v-else><p style="padding: 20px 0 30px;">'+$t("您可以在{{str}}查看导出进度。",{data:{str:'<a href="javascript:;" @click="showExportList">'+$t("已导出规则列表")+"</a>"}})+"</p></div></fx-dialog>",data:function(){return{show:!1,percentage:0,progressStop:!0}},watch:{show:function(e){e||clearInterval(this.timer)}},methods:{showExportList:function(){this.show=!1,this.$nextTick(function(){t.showExportList("export")})}},mounted:function(){this.show=!0}})},fail:function(e){e.Result.FailureMessage&&a.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},onImport:function(){var e,t=this,i="javascript:;",a=(t.downloadDataShareTemplate&&(e=t.downloadDataShareTemplate[1],i=FS.util.getFscLink(e.filePath.split(".")[0],e.fileName,!0)),FxUI.create({template:n().replace("%filename%","{{filename}}").replace("%note%",$t("导入数据上限为{{n}}条",{data:{n:100}})).replace("%progressResult%",$t("您可以在{{str}}中查看导出进度",{data:{str:'<a href="javascript:;" @click="viewResult">'+$t("导入导出结果")+"</a>"}})),data:function(){return{show:!0,dataSource:"1",dataSourceOptions:[{value:"1",label:$t("基于数据负责人")},{value:"4",label:$t("jiyushujuguishuzuzhi")},{value:"2",label:$t("基于数据归属部门")}],downloadUrl:i,hasFile:!1,filename:"",filepath:"",beforeImport:!0,percentage:0,disabled:!0}},watch:{show:function(e){e||(t.h5Uploader=null)}},methods:{initUpload:function(){t.h5Uploader||(t.h5Uploader=new u({multiple:!1,accept:".xlsx,.xls",autoPrependPath:!1,fileInput:this.$refs.fileInput,dragDrop:this.$refs.uploadBox,url:FS.BASE_PATH+"/FSC/EM/File/UploadByStream",timeout:180,onSelect:function(e){t.h5Uploader.startUpload(),a.uploadLoading=FxUI.Loading.service({target:a.$refs.uploadBox})},onSuccess:function(e,t){a.filename=e.name,a.hasFile=!0,a.filepath=JSON.parse(t).TempFileName,a.disabled=!1},onFailure:function(e){FxUI.MessageBox.alert($t("上传文件失败"),{type:"error"})},onComplete:function(){a.uploadLoading&&a.uploadLoading.close(),t.h5Uploader.removeAllFile()}}))},onDataSourceChange:function(e){t.downloadDataShareTemplate?(e=t.downloadDataShareTemplate[e],this.downloadUrl=FS.util.getFscLink(e.filePath.split(".")[0],e.fileName,!0)):this.downloadUrl="javascript:;"},onClickUpload:function(){this.$refs.fileInput.click()},onStartImport:function(){var e;this.dataSource&&this.filename&&this.filepath&&(this.beforeImport=!1,e=setInterval(function(){100<=a.percentage?clearInterval(e):(a.percentage+=.3,100<a.percentage&&(a.percentage=100))},150),FS.util.FHHApi({url:"/EM1HPAASBATCH/task/importDataShareTask/create",data:{importDataShareTaskConfig:{argType:this.dataSource,fileName:this.filename,file_path:this.filepath}},success:function(e){e.Result.StatusCode},fail:function(){},complete:function(){clearInterval(e),e=setInterval(function(){100<=a.percentage?clearInterval(e):(a.percentage+=.3,100<a.percentage&&(a.percentage=100))},10)}}))},onCancel:function(){this.show=!1,this.reset()},reset:function(){this.hasFile=!1,this.filename="",this.filepath="",this.beforeImport=!0,this.percentage=0,this.disabled=!0},viewResult:function(){this.show=!1,this.reset(),this.$nextTick(function(){t.showExportList("import")})}},mounted:function(){this.initUpload()}}))},getDownloadDataShareTemplate:function(e){var t=this;a.FHHApi({url:"/EM1HPAASBATCH/task/importDataShareTask/downloadDataShareTemplate/0",data:{},success:function(e){0==e.Result.StatusCode&&(t.downloadDataShareTemplate={},(e.Value.result||[]).forEach(function(e){t.downloadDataShareTemplate[e.argType]=e}))},fail:function(e){}},{errorAlertModel:0})},resetScope:function(e){this.widgets.scope.setValue(e),this.widgets.scope.trigger("change",e),this.scopeType=e},destroy:function(){this.undelegateEvents(),_.each(this.widgets,function(e){e&&(e.stopQueryStatus=!0,e.destroy)&&e.destroy()}),this.widgets={}}})});
define("crm-setting/datapermissions/datashare/myobject/myobjectbydatasource",["../../common/basetable","./myobjectdialog","../../common/config"],function(e,t,a){var o,n=CRM.util,r=e("../../common/basetable"),c=e("./myobjectdialog"),i=e("../../common/config");return r.extend({config:"myObjectByDatasource",_parseColumns:function(e){var t=(t=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions).filter(function(e){return 1!=e.objectType}),a=_.findWhere(e,{data:"ObjectType"});return-1==this.opts.ObjectType||-2==this.opts.ObjectType?(a.options=_.map(t,function(e){return{ItemName:e.ObjectDescribeDisplayName,ItemCode:e.ObjectDescribeApiName}}),a.isFilter=!0):a.isFilter=!1,e},parseTableParam:function(e){var c,a,r,t=this,s=_.extend(e,{outReceive:!1,ObjectDescribeApiName:t.opts.ObjectType||-1,basedType:{datasource:0,datasourcedep:1,datasourcedep_org:2}[t.options.scopeType||"datasource"]}),i=s._shareRange;return i&&(a=[],r=[],(i.group||[]).forEach(function(e){var t=n.getCircleById(e);t&&("default__c"==t.recordType?a:r).push(e)}),s.Receives={0:i.member.concat(i.stop||[])||[],1:i.usergroup||[],2:a,4:i.role||[],8:r}),t.ObjectDescribeApiName=t.opts.ObjectType||-1,s=_.extend(s,{PermissionType:-1,Status:-1,Sources:void 0,Entices:void 0,createIds:void 0,modifyIds:void 0,createTimeRange:void 0,modifyTimeRange:void 0}),e.QueryInfo&&e.QueryInfo.Conditions&&0<e.QueryInfo.Conditions.length&&_.each(e.QueryInfo.Conditions,function(e){var a,r,i,o,t=e.FilterValue;s.Receives=s.Receives||{},"DataSourceName2"===(c=e.FieldName)?(c="Sources",e=[],(t=n.parseJson(t)).stop&&(e=_.pluck(t.stop,"id")),a=[],r=[],_.pluck(t.group,"id").forEach(function(e){var t=n.getCircleById(e);t&&("default__c"==t.recordType?a:r).push(e)}),t={0:_.pluck(t.member,"id").concat(e),1:_.pluck(t.usergroup,"id"),2:a,4:_.pluck(t.role,"id"),8:r}):"TargetName2"===c?(c="Receives",t=n.parseJson(t),i=[],o=[],_.union(_.pluck(t.group,"id"),s.Receives[2]||[]).forEach(function(e){var t=n.getCircleById(e);t&&("default__c"==t.recordType?i:o).push(e)}),t={0:_.union(_.pluck(t.member,"id"),s.Receives[0]||[]),1:_.union(_.pluck(t.usergroup,"id"),s.Receives[1]||[]),2:i,4:_.union(_.pluck(t.role,"id"),s.Receives[4]||[]),8:o}):"ObjectType"===c?c="Entices":"creator"===c?c="createIds":"modifier"===c?c="modifyIds":"createTime"===c?(c="createTimeRange",t={startTime:t[0],endTime:t[1]}):"modifyTime"===c&&(c="modifyTimeRange",t={startTime:t[0],endTime:t[1]}),s[c]=t}),_.omit(s,"QueryInfo","ObjectType","_shareRange","Keyword","status","_isfilter")},setParamByKey:function(e,t){this.widgets.dt.setParamByKey(e,t)},formatTableData:function(e){return o=i[this.config](),_.map(e,function(e){var t=e.DataSourceName||this.getNameById(e.DataSourceID,o.typeMap[e.DataSourceType]);1==e.DataSourceStatus&&(t+="("+$t("已停用")+")");var a=(a=o.typePrefixMap[e.DataSourceType]||"")&&$t(a),r=e.TargetName||this.getNameById(e.TargetID,o.typeMap[e.TargetType]);1==e.TargetStatus&&(r+="("+$t("已停用")+")");var i=(i=o.typePrefixMap[e.TargetType]||"")&&$t(i);return _.extend({DataSourceName2:a+t,TargetName2:i+r},e)},this)},onEnable:function(e,t){var a=this,r=a.widgets.dt.getCheckedData();if(r&&0===r.length)return n.remind(2,$t("请勾选需要移除的员工")),!1;t=t?[t.SharedEntity]:_.map(r,function(e){return e.SharedEntity}),r=$(e.target||e);n.FHHApi({url:o.enableUrl,data:{SharedRuleIds:t,Status:r.text().replace(" ","")===$t("启用")?1:0},success:function(e){n.remind(1,$t("操作成功")),a.widgets.dt.setParam({},!0)},fail:function(e){n.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},onDel:function(e){var t,a=this,r=a.widgets.dt.getCheckedData();if(r){if(_.some(r,function(e){return 1===e.Status}))return n.remind(3,$t("不能删除启用中的共享规则")),!1;t=_.map(r,function(e){return e.SharedEntity})}else t=[e.SharedEntity];var i=n.confirm($t("确认删除数据"),$t("提示"),function(){n.FHHApi({url:o.delUrl,data:{SharedRuleIds:t},success:function(e){n.remind(1,$t("操作成功")),a.widgets.dt.setParam({},!0)},fail:function(e){n.alert(e.Result.FailureMessage)},complete:function(){i.destroy()}},{errorAlertModel:1})})},onEdit:function(e,t){var a=this,r=a.widgets,t=2==t.attr("data-type");r.myobjectdialog&&r.myobjectdialog.destroy(),r.myobjectdialog=new c({title:t?$t("查看权限"):$t("编辑权限"),type:"edit",disabled:t,scopeType:a.options.scopeType||"datasource"}),r.myobjectdialog.on("success",function(e){a.refresh()}),r.myobjectdialog.show({data:e,disabled:t,showField:"BpmInstance"!=e.ObjectDescribeApiName&&"ApprovalInstanceObj"!=e.ObjectDescribeApiName,datasource:[{id:e.DataSourceID,type:o.typeMap[e.DataSourceType]}],target:[{id:e.TargetID,type:o.typeMap[e.TargetType]}]})},onCopy:function(e,t){var a=this,r=a.widgets;r.myobjectdialog&&r.myobjectdialog.destroy();(r.myobjectdialog=new c({title:$t("新建共享规则"),type:"add",isCopy:!0,scopeType:a.options.scopeType||"datasource"})).on("success",function(e){a.refresh()});var i=Object.assign({},e);r.myobjectdialog.show({data:i,showField:"BpmInstance"!=e.ObjectDescribeApiName&&"ApprovalInstanceObj"!=e.ObjectDescribeApiName,datasource:[{id:e.DataSourceID,type:o.typeMap[e.DataSourceType]}],target:[{id:e.TargetID,type:o.typeMap[e.TargetType]}]})}})});
define("crm-setting/datapermissions/datashare/myobject/myobjectbyfield",["../../common/basetable","./myobjectdialog","../../common/config"],function(e,t,i){var d,o=CRM.util,r=e("../../common/basetable"),n=e("./myobjectdialog"),s=e("../../common/config");return r.extend({config:"myObjectByField",_parseColumns:function(e){var t=(t=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions).filter(function(e){return 1!=e.objectType}),i=_.findWhere(e,{data:"displayName"});return-1==this.opts.ObjectType||-2==this.opts.ObjectType?(i.options=_.map(t,function(e){return{ItemName:_.escape(e.ObjectDescribeDisplayName),ItemCode:e.ObjectDescribeApiName}}),i.isFilter=!0):i.isFilter=!1,e},parseTableParam:function(e){var n,i,r,a=_.extend({outReceive:!1,describeApiName:e.ObjectType||this.opts.ObjectType||-1},e),t=a._shareRange;return t&&(i=[],r=[],(t.group||[]).forEach(function(e){var t=o.getCircleById(e);t&&("default__c"==t.recordType?i:r).push(e)}),a.receivesWithType={0:t.member.concat(t.stop||[])||[],1:t.usergroup||[],2:i,4:t.role||[],8:r}),a=_.extend(a,{createIds:void 0,modifyIds:void 0,createTimeRange:void 0,modifyTimeRange:void 0}),e.QueryInfo&&e.QueryInfo.Conditions&&0<e.QueryInfo.Conditions.length?_.each(e.QueryInfo.Conditions,function(e){var i,r,t=e.FilterValue,s=a.receivesWithType||{};"receiveIds"===(n=e.FieldName)?(n="receivesWithType",t=o.parseJson(t),i=[],r=[],_.union(_.pluck(t.group,"id"),s[2]||[]).forEach(function(e){var t=o.getCircleById(e);t&&("default__c"==t.recordType?i:r).push(e)}),t={0:_.union(_.pluck(t.member,"id"),s[0]||[]),1:_.union(_.pluck(t.usergroup,"id"),s[1]||[]),2:i,4:_.union(_.pluck(t.role,"id"),s[4]||[]),8:r}):"displayName"===n?n="entices":"creator"===n?n="createIds":"modifier"===n?n="modifyIds":"createTime"===n?(n="createTimeRange",t={startTime:t[0],endTime:t[1]}):"modifyTime"===n&&(n="modifyTimeRange",t={startTime:t[0],endTime:t[1]}),a[n]=t}):a=_.extend(a,{permission:-1,status:-1,ruleName:"",entices:void 0}),_.omit(a,"QueryInfo","ObjectType","_shareRange","_shareRangeName","Keyword","Status","PermissionType")},setParam:function(e){this.widgets.dt.setParam(e||{},!0)},setParamByKey:function(e,t){this.widgets.dt.setParamByKey(e,t)},formatTableData:function(e){return this.myObjectDataPermissions=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,this.myObjectDataPermissions=this.myObjectDataPermissions.filter(function(e){return 1!=e.objectType}),d=s[this.config](),_.map(e,function(e){return this.formatTableColumnData(e)},this)},formatTableColumnData:function(e){e.permission=e.receives[0].permission,e.displayName=(_.findWhere(this.myObjectDataPermissions,{ObjectDescribeApiName:e.entityId})||{}).ObjectDescribeDisplayName||"--";var s=!1,n=[],a={};return _.each(e.receives,function(e){var t=this.getNameById(e.receiveId,d.typeMap[e.receiveType]);"--"==t&&(s=!0);var i,r=(r=d.typePrefixMap[e.receiveType]||"")&&$t(r);n.push(r+t),"2"!=e.receiveType&&"8"!=e.receiveType||(i=[],(e=FS.contacts.getCircleById(e.receiveId))&&(Array.isArray(e.ancestors)&&e.ancestors.slice(0).forEach(function(e){e=FS.contacts.getCircleById(e);e&&i.push(e.name)}),i.push(e.name)),a[r+t]=i.join("/"))},this),e.receiveIds=n.join($t("、")),e.receiveIdsGroupTitle=a,e.hasStop=s,e},formatTableDataAsync:function(t){var c=this,u={},r=(t.forEach(function(e){e.hasStop&&_.each(e.receives,function(e){var t=d.typeMap[e.receiveType];"--"==c.getNameById(e.receiveId,t)&&(u[t]||(u[t]={ids:[]}),u[t].ids.push(e.receiveId),u[t][e.receiveId]="")},this)}),[]);return Object.keys(u).forEach(function(e){var t,i=u[e];0!=i.ids.length&&("member"==e?(t=new Promise(function(e,t){o.getDetailEmployeeByIdsAsync(i.ids).then(function(e){e.forEach(function(e){var t;e&&(t=e.name,o.isStop(e)&&(t+="("+$t("已停用")+")"),i[e.id]=t)})}).done(function(){e()})}),r.push(t)):"group"==e?(t=new Promise(function(e,t){o.getCirclesByIdsAsync(i.ids).then(function(e){e.forEach(function(e){var t;e&&(t=e.name,o.isStop(e)&&(t+="("+$t("已停用")+")"),i[e.id]=t)})}).done(function(){e()})}),r.push(t)):"usergroup"==e&&(t=new Promise(function(e,t){o.getGroupsByArgs({status:1,groupIds:i.ids}).then(function(e){e.forEach(function(e){var t;e&&(t=e.groupName,o.isStop(e)&&(t+="("+$t("已停用")+")"),i[e.groupId]=t)})}).finally(function(){e()})}),r.push(t)))}),Promise.all(r).then(function(e){return _.each(t,function(e){var n=[],a=[],o={};_.each(e.receives,function(e){var t,i=(i=d.typePrefixMap[e.receiveType]||"")&&$t(i),r=d.typeMap[e.receiveType],s=c.getNameById(e.receiveId,r);"--"==s?(s=u[r][e.receiveId]||"--",a.push(i+s)):(n.push(i+s),"2"!=e.receiveType&&"8"!=e.receiveType||(t=[],(r=FS.contacts.getCircleById(e.receiveId))&&(Array.isArray(r.ancestors)&&r.ancestors.slice(0).forEach(function(e){e=FS.contacts.getCircleById(e);e&&t.push(e.name)}),t.push(r.name)),o[i+s]=t.join("/")))},this),n.length<20&&(n=n.concat(a.slice(0,20-n.length))),e.receiveIds=n.slice(0,20).join($t("、")),e.receiveIdsGroupTitle=o,delete e.hasStop},this),t})},onEnable:function(e,i){var t,r=this.widgets.dt,s=r.getCheckedData(),e=e.target||e;if(s)t=_.map(s,function(e){return e.ruleCode});else{if(2==i.status)return void o.alert($t("启用操作成功")+","+$t("正在处理数据")+","+$t("生效后会发送CRM通知给操作人"));t=[i.ruleCode]}o.FHHApi({url:d.enableUrl,data:{ruleCodes:t,status:$(e).hasClass("stop")?0:1},success:function(e){var t=s||0!=i.status?$t("操作成功"):$t("启用操作成功")+","+$t("正在处理数据")+","+$t("生效后会发送CRM通知给操作人");o.remind(1,t),r.setParam({},!0)}})},onDel:function(e){var t,i=this.widgets.dt,r=i.getCheckedData();if(r){if(_.some(r,function(e){return 1===e.status}))return o.remind(3,$t("不能删除启用中的共享规则")),!1;t=_.map(r,function(e){return e.ruleCode})}else t=[e.ruleCode];var s=o.confirm($t("确认删除数据"),$t("提示"),function(){o.FHHApi({url:d.delUrl,data:{ruleCodes:t},success:function(e){0===e.Result.StatusCode&&e.Value.success&&(o.remind(1,$t("操作成功")),i.setParam({},!0))},complete:function(){s.destroy()}})})},onEdit:function(e,t){var i=this,r=i.widgets,s=r.myobject_dialog,t=2==t.attr("data-type");e=i.formatTableColumnData(e),s&&s.destroy(),(s=new n({title:t?$t("查看规则"):$t("编辑共享规则"),type:"edit",scopeType:"field",disabled:t})).on("success",function(){i.refresh()}),s.show({data:e,datasource:[{}],showField:!0,target:_.map(e.receives,function(e){return{id:e.receiveId,type:d.typeMap[e.receiveType]}})}),r.myobject_dialog=s},onCopy:function(e){e=Object.assign({},e);var t=this,i=t.widgets,r=i.myobject_dialog,s=(e=t.formatTableColumnData(e),r&&r.destroy(),(r=new n({title:$t("新建共享规则"),type:"add",isCopy:!0,scopeType:"field"})).on("success",function(){t.refresh()}),Object.assign({},e));s.ruleCode=null,s.ruleName+=" "+$t("copyData"),r.show({data:s,datasource:[{}],showField:!0,target:_.map(e.receives,function(e){return{id:e.receiveId,type:d.typeMap[e.receiveType]}})}),i.myobject_dialog=r}})});
function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var r;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var i,a,s,o,n=[],d=!0,c=!1;try{if(s=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;d=!1}else for(;!(d=(i=s.call(r)).done)&&(n.push(i.value),n.length!==t);d=!0);}catch(e){c=!0,a=e}finally{try{if(!d&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(c)throw a}}return n}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/datapermissions/datashare/myobject/myobjectdialog",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","crm-modules/common/fieldfilter/fieldfilter","../template/add-dialog-html","../../common/config"],function(e,t,r){var v=e("crm-modules/common/util"),i=e("crm-widget/dialog/dialog"),y=e("crm-widget/selector/selector"),s=e("crm-widget/select/select"),n=e("crm-modules/common/fieldfilter/fieldfilter"),d=e("../template/add-dialog-html"),w=e("../../common/config"),j=999999,c=i.extend({config:"myObjectByField",attrs:{title:$t("共享规则"),content:'<div class="crm-loading"></div>',className:"crm-s-datapermissions",showBtns:!0,showScroll:!0,isCopy:!1,type:"add",data:{},width:"810px"},events:{"click .j-toggle":"onToggleCondition","focus .rulename-ipt":"onRemoveErrmsg","click .b-g-btn":"onSave","click .add-group-btn":"addFilterGroup","click .j-del-group":"delFilterGroup","click .b-g-btn-cancel":"destroy","click .j-permission":"onTogglePerMission"},render:function(){return this.widgets={},this.myObjectDataPermissions=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,this.myObjectDataPermissions=this.myObjectDataPermissions.filter(function(e){return 1!=e.objectType}),this.userGroups=JSON.parse(sessionStorage.getItem("userGroups")),c.superclass.render.call(this)},show:function(e){this.widgets={},this.set(e);var t=c.superclass.show.call(this),r="edit"===this.get("type"),i=this.get("isCopy"),a=-1<["datasource","datasourcedep","datasourcedep_org"].indexOf(this.get("scopeType")),s=(r||this.get("scopeType")||(a=!0),"datasourcedep"==this.get("scopeType")),o="datasourcedep_org"==this.get("scopeType");return this.curType="datasource",s&&(this.curType="datasourcedep"),o&&(this.curType="datasourcedep_org"),a||(this.curType="field"),this.setContent(d({isEdit:r,isDatasource:a,isDep:s,isDepOrg:o,data:this.get("data"),showField:this.get("showField")&&"BpmInstance"!=e.data.ObjectType&&"ApprovalInstanceObj"!=e.data.ObjectType,isSysObj:!1,disabled:this.get("disabled"),isCopy:i})),this.$sharedata=this.$(".j-sharedata"),this.$rulename=this.$(".rulename-ipt"),this.$shareobj=this.$(".j-shareobj"),this.$fieldfilter=this.$(".j-fieldfilter"),this.$conditionGroups=this.$(".condition-groups"),this.initSelectBar("datasource",r||this.get("disabled")),this.initSelectBar("target",r&&a||this.get("disabled")),this.initShareData(r&&a),"field"!==this.get("scopeType")&&(r||i)||this.initShareObj(r),this.resizedialog(),t},curType:"datasource",initSelectBar:function(e,t,r){var i,a,s,o,n,d=this,c=d.get("data"),l=("datasource"===d.get("scopeType")||"datasource"==d.curType)&&"target"!=e,p="datasource"===d.get("scopeType")||"datasourcedep"===d.get("scopeType")||"datasourcedep_org"===d.get("scopeType")||"datasource"==d.curType||"datasourcedep"==d.curType||"datasourcedep_org"==d.curType,u=("datasourcedep"===d.get("scopeType")||"datasourcedep"==d.curType)&&"target"!=e,g=("datasourcedep_org"===d.get("scopeType")||"datasourcedep_org"==d.curType)&&"target"!=e,h=d.get(e),m=this.get("isCopy"),h=("target"===e&&h&&"usergroup"===h[0].type?i=_.findWhere(d.userGroups,{id:h[0].id,type:0})?h:[]:(i=h||[],1==c.DataSourceStatus&&i.forEach(function(e){"member"==e.type&&(e.type="stop")})),0),m=("target"===e&&(h=1,"edit"!=this.get("type")&&!m||(p?a=c.receiveDeptCascade:c.receives[0]&&(a=c.receives[0].receiveCascade),h=1==a?2:1)),u||g),f=[],c=(p||i.forEach(function(e){var t=e.id;"group"!=e.type||Fx.contacts.getCircleById(t)||Fx.contacts.getCircleByIdSync(t,function(e){Fx.contacts.isStop(e)&&f.push(e)})}),new y({$wrap:d.$(".j-"+e),zIndex:d.get("zIndex"),group:{company:"datasource"===e||m,chooseType:u?"department":g?"organization":""},member:!m,usergroup:!m,role:!m,stop:l||!p&&{hidden:!0},stopDepartment:!p&&{hidden:!0,data:f},excludeItems:{role:["personnelrole"]},groupIncludeChildrenStatus:h,single:!1,label:m?u?$t("xuanzeshujuguishubumen"):$t("xuanzeshujuguishuzuzhi"):$t("选择员工部门用户组或角色"),defaultSelectedItems:(n={member:[],group:[],usergroup:[],role:[],stop:[],stopDepartment:[]},_.each(i,function(e){if(s=e.id,"member"==(o=e.type)){if(!p&&!Fx.contacts.getEmployeeById(s)){e=Fx.contacts.getEmployeeById(s,{includeStop:!0,includeStopByRequest:!0});if(Fx.contacts.isStop(e))return void n.stop.push(s)}n.member.push(s)}else"group"==o?(!p&&f.length&&-1!=f.findIndex(function(e){return e.id==s})?n.stopDepartment:n.group).push(s):"usergroup"===o?n.usergroup.push(s):"role"===o?n.role.push(s):"stop"===o?n.stop.push(s):"stopDepartment"===o&&n.stopDepartment.push(s)}),n),isFromManage:!0,enableScope:!0}));t&&c.lock(),c.on("addItem",function(){this.options.$wrap.next().hide(),d.resizedialog()}),d.widgets[e]=c},initShareData:function(e){var t=this,r=t.get("data"),r=-1===(r=r.ObjectType||r.ObjectDescribeApiName)?"":r,i=t.myObjectDataPermissions.reduce(function(e,t){return e.push({name:t.ObjectDescribeDisplayName,value:t.ObjectDescribeApiName}),e},[]),i=new s({$wrap:t.$sharedata,zIndex:t.get("zIndex"),width:500,multiple:"multiple",allCheck:!0,options:i,disabled:e,stopPropagation:!0,defaultValue:r});i.on("change",function(){this.options.$wrap.next().hide(),t.resizedialog()}),t.widgets.sharedata=i},initShareObj:function(t){var r=this,i=(r.get("data"),r.get("data").entityId),a=r.myObjectDataPermissions.map(function(e){return{name:e.ObjectDescribeDisplayName,value:e.ObjectDescribeApiName}}).filter(function(e){return-1==["SalesOrderProductObj","ReturnedGoodsInvoiceProductObj","GoalValueObj"].indexOf(e.value)}),a=_.filter(a,function(e){return"BpmInstance"!=e.value&&"ApprovalInstanceObj"!=e.value});r.publicObjectBlocking(function(e){e&&0<e.publicObject.length&&e.publicObject.forEach(function(t){a=_.filter(a,function(e){return e.value!=t})});e=r.get("isCopy"),e&&(t=!0),e=new s({$wrap:r.$shareobj,zIndex:r.get("zIndex"),multiple:"single",options:[{name:$t("请选择"),value:""}].concat(a),defaultValue:i,disabled:t&&!e});e.on("change",function(e){this.options.$wrap.next().hide(),r.renderFieldfilter({apiname:e}),r.resizedialog(),"AccountObj"==e?this.options.$wrap.next().next().show():this.options.$wrap.next().next().hide()}),t&&r.renderFieldfilter({apiname:i,isEdit:t}),r.widgets.shareobj=e})},publicObjectBlocking:function(t){v.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/getDescribeListByObjectType",data:{},success:function(e){0===e.Result.StatusCode?t&&t(e.Value):t&&t()}})},renderFieldfilter:function(t){var i,a,r=this,e=t.isEdit;delete t.isEdit,(r.widgets.filterGroup||[]).forEach(function(e){e.destroy(),r.$conditionGroups.html(""),r.$fieldfilter._hasAddBtn=0,r.$fieldfilter.find(".add-group-btn").remove()}),r.widgets.filterGroup=[],e?(e=r.get("data").ruleParse||"",i=r.get("data").rules||[],e=e.replace(/[() ]/g,"").split("or"),a=[],e.forEach(function(e){var r=[];e.split("and").forEach(function(t){i.forEach(function(e){"partner_id.name"==e.fieldName&&(e.fieldName="partner_id",e.fieldValue=[e.fieldValue.join(";")]),e.ruleOrder==t&&r.push(e)})}),a.push(r)}),a.forEach(function(e){r.widgets.filterGroup.push(r.createFieldfilter(t,e))})):r.widgets.filterGroup.push(r.createFieldfilter(t))},createFieldfilter:function(a,e){var t,r=this,i=Date.now()+"-"+Math.random(),s=$('<div class="filter-item filter-item-'+i+'"></div>'),c=["employee","employee_many","department","department_many"],o=[],e=(e&&(t=_.map(e,function(e){return-1<["IS","ISN"].indexOf(e.operate)?o.push([void 0,void 0,!0]):o.push([]),[e.fieldName,e.operate,e.fieldValue]})),new n(_.extend(a,{uuid:i,$wrapper:s,width:636,title:'<span class="el-icon-remove j-del-group" data-uuid='+i+"></span>"+$t("且（AND）"),max:10,filterType:w.filterType,filterApiname:w.filterApiname,openQuoteField:!0,openPartnerIdField:!0,extra:{from:"datashare"},disabledValue:o,defaultValue:t,parseCompare:function(e,t,r){return _.contains(c,t)||"dimension"==t?_.map([13,14,9,10],function(e){return r[e-1]}):(-1<["count","number","currency"].indexOf(t)?(e.push(r[8]),e.push(r[9])):"rich_text"==t?e.push(r[6],r[7]):-1<["country","province","city","district"].indexOf(t)&&e.push(r[12],r[13]),e)},_getCompareOptions:function(r){var t,i=this,e=this.opts.helper.getCompare(r.type),e=i.opts.parseCompare(e,r&&r.type,i.opts.helper.getCompareConfig()),a=this.opts.helper.getType(r.type,i.opts.isRelate),s=(_.contains(["object_reference","object_reference_many"],r.type)&&"partner_id"==r.api_name&&(a="text"),i.isEmpField(r.api_name)&&(a="employee"),CRM.config.objDes[i.opts.apiname.toLowerCase()]&&("select_many"==a?e=_.filter(e,function(e){return!_.contains([7,8,13,14],e.value)}):_.contains(["date","time","date_time"],r.type)&&(e=_.filter(e,function(e){return!_.contains([9,10],e.value)}))),[]),o=0,n=!1,d="";return"data_own_department"==r.api_name?(o=1,n=!0,d="_y",this.opts.defaultValue&&this.opts.defaultValue.forEach(function(e){"data_own_department"==e[0]&&(o=Array.isArray(e[2])&&/_y$/g.test(e[2][0])?(e[2]=e[2].map(function(e){return e.replace("_y","")}),2):1)})):"data_own_organization"==r.api_name?(o=1,d="_y",this.opts.defaultValue&&this.opts.defaultValue.forEach(function(e){"data_own_organization"==e[0]&&(o=Array.isArray(e[2])&&/_y$/g.test(e[2][0])?(e[2]=e[2].map(function(e){return e.replace("_y","")}),2):1)})):"department"==r.type&&(o=1,d=r.api_name.endsWith("__c")?"_z":"_y",this.opts.defaultValue)&&this.opts.defaultValue.forEach(function(e){e[0]==r.api_name&&(o=Array.isArray(e[2])&&(/_z$/g.test(e[2][0])||/_y$/g.test(e[2][0]))?(e[2]=e[2].map(function(e){return e.replace("_z","")}),e[2]=e[2].map(function(e){return e.replace("_y","")}),2):1)}),"department"!=r.type&&"department_many"!=r.type||(t=[],this.opts.defaultValue&&this.opts.defaultValue.forEach(function(e){e[0]==r.api_name&&Array.isArray(e[2])&&(t=e[2].slice(0))}),t.forEach(function(e){Fx.contacts.getCircleById(e)||Fx.contacts.getCircleByIdSync(e,function(e){Fx.contacts.isStop(e)&&s.push(e)})})),_.map(e,function(e){var t={type:i._getChildType(e,a,r.type),ftype:r.type,fname:r.api_name,options:i._getValueOptions(r),isReInit:_.contains(c,a),relatedName:r.target_related_list_name||"",targetApiName:r.target_api_name,dimension_type:r.dimension_type||"",stopDepartment:{hidden:!0,data:s},groupIncludeChildrenStatus:o,groupIncludeChildrenStatusCascade:n,valSubfix:d};return(_.contains(c,a)||"dimension"==a)&&_.contains([7,8,13,14],e.value)&&(t.isMultiple=!0),_.contains(["object_reference","object_reference_many"],r.type)&&"partner_id"==r.api_name&&_.contains(["IN","NIN"],e.value1)&&(t.useMInput=!0),{label:"date_time"==r.type&&e.dname||e.name,value:e.value,child:t}})},parseFields:function(e){var t,r=CRM.util.deepClone(e);for(t in r){var i=r[t];("formula"==i.type&&_.contains(["date_time","date","time"],i.return_type)||"formula"==i.type&&!i.is_index||"quote"==i.type&&!1===i.is_index)&&delete r[t],"quote"==i.type&&-1<["employee","employee_many","department","department_many"].indexOf(i.quote_field_type)&&delete r[t],"formula"===i.type&&(i.type=i.return_type||i.type),"select_one"===i.type&&i.cascade_parent_api_name&&delete i.cascade_parent_api_name,-1!=w.filterType.indexOf(i.quote_field_type)&&delete r[t],-1!=w.filterApiname.indexOf(i.api_name)&&delete r[t],"NewOpportunityObj"!==a.apiname&&"sales_process_id"===i.api_name&&delete r[t]}return r},formatFields:function(e){for(var t in e){t=e[t];t.type=t.quote_field_type||t.type}return e},disabled:r.get("disabled")})));return e.on("render",function(){var e,t;r.resizedialog(),r.$conditionGroups.find(".crm-loading").remove(),0==r.$conditionGroups.find(".filter-item-"+i).length&&(e=$('<div class="group-item"><span class="fm-error crm-ico-error" style="display:none">'+$t("请完善筛选条件")+'</span><div class="or-line"><span>'+$t("或",{},"或")+"</span></div></div>"),t=s.parent(),e.prepend(s),r.$conditionGroups.append(e),t)&&t.remove(),r.$fieldfilter._hasAddBtn||(r.$fieldfilter._hasAddBtn=1,r.$fieldfilter.append('<span class="add-group-btn el-icon-circle-plus">'+$t("添加条件")+"</span>")),r.get("disabled")&&r.$fieldfilter.append('<div class="fieldfilter-mask"></div>')}),e.on("change",function(){this.opts.$wrapper.next().hide()}),e.on("add.item",function(){r.resizedialog()}),e.on("del.item",function(){r.resizedialog()}),e},addFilterGroup:function(){var e=this.widgets.shareobj.getValue();this.widgets.filterGroup.push(this.createFieldfilter({apiname:e}))},delFilterGroup:function(e){if(1==this.widgets.filterGroup.length)this.widgets.filterGroup[0].reset();else for(var t=$(e.target).data("uuid"),r=0;r<this.widgets.filterGroup.length;r++){var i=this.widgets.filterGroup[r];if(i.opts.uuid==t){i.destroy(),i.opts.$wrapper.closest(".group-item").remove(),this.widgets.filterGroup.splice(r,1);break}}},onToggleWarn:function(e){$(e.currentTarget).closest(".update-tip").toggleClass("update-tip-hover"),this.resizedialog()},onToggleCondition:function(e){if($(e.currentTarget).hasClass("disabled-selected"))return e.stopPropagation(),!1;e=$(e.target).data("base"),e="datasourcedep"==(this.curType=e)||"datasourcedep_org"==e?"datasource":e;this.$(".j-basefield").hide(),this.$(".j-basedatasource").hide(),this.$(".j-base"+e).show(),"datasource"==e&&"edit"!==this.get("type")&&(this.widgets.datasource&&this.widgets.datasource.destroy(),this.widgets.datasource=null,this.initSelectBar("datasource")),this.widgets.target&&this.widgets.target.destroy(),this.widgets.target=null,this.initSelectBar("target",this.get("disabled"),e),this.resizedialog()},onTogglePerMission:function(e){if($(e.currentTarget).hasClass("disabled-selected"))return e.stopPropagation(),!1},onRemoveErrmsg:function(e){$(e.currentTarget).next().hide()},onSave:function(e){var t,s,r,o,i,a,n,d,c,l,p,u,g,h,m,f,y,b=this;if(!this.get("disabled"))return e=$(e.currentTarget),t=b.widgets.target.getValue(),s=b.widgets.target.getIncludeGroupChildrenStatus()?1:0,l=t.member.length+t.group.length+t.usergroup.length+t.role.length,Array.isArray(t.stop)&&(l+=t.stop.length),Array.isArray(t.stopDepartment)&&(l+=t.stopDepartment.length),0==l?(b.$(".j-target").next().show(),!1):(l="datasource"===(r=this.$(".j-toggle.mn-selected").data("base"))||"datasourcedep"===r||"datasourcedep_org"===r,o=b.$(".j-permission.mn-selected").data("permissiontype"),l?(l=b.widgets.datasource.getValue(),c=b.widgets.sharedata.getValue(),l=_.extend({member:[],group:[],usergroup:[],role:[]},l),"datasource"===r&&l.stop&&l.stop.length&&(l.member=l.member.concat(l.stop),delete l.stop),l.member.length+l.group.length+l.usergroup.length+l.role.length==0?(b.$(".j-datasource").next().show(),!1):0===c.length?(b.$sharedata.next().show(),!1):_.contains(t.group,j)&&_.contains(l.group,j)?(v.remind(3,$t("数据来源和数据共享不能同时为")+v.getCircleById(j).name),!1):(i=[],a=[],l.group.forEach(function(e){var t=FS.contacts.getCircleById(e);t&&("default__c"==t.recordType?i.push(e):"organization__c"==t.recordType&&a.push(e))}),n=[],d=[],t.group.forEach(function(e){var t=FS.contacts.getCircleById(e);t&&("default__c"==t.recordType?n.push(e):"organization__c"==t.recordType&&d.push(e))}),void v.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/addOrUpdateShareRules",data:{ObjectDescribeApiNames:c,SourceEmployeeIDs:l.member,SourceCircleIDs:i,SourceUserGroupIDs:l.usergroup,SourceRoleIDs:l.role,TargetEmployeeIDs:t.member,TargetCircleIDs:n,TargetUserGroupIDs:t.usergroup,TargetRoleIDs:t.role,PermissionType:o,receiveDeptCascade:s,SourceOrganizationIDs:a,TargetOrganizationIDs:d,basedType:{datasource:0,datasourcedep:1,datasourcedep_org:2}[r]},success:function(e){0===e.Result.StatusCode&&(b.trigger("success",r),v.remind(1,$t("操作成功！")),b.hide())}},{submitSelector:e}))):(c=b.widgets.shareobj.getValue(),l=$.trim(b.$rulename.val()),p=[],u=[],l?v.isContainEmojiCharacter(l)?(b.$rulename.next().text($t("规则名称不能包含emoji表情符")).show(),!1):c?(g=!1,(h=b.widgets.filterGroup).forEach(function(e){var t;if(e.isExitNull())return t=CRM.util.getUserAttribute("crmAreaV3"),e.opts.$wrapper.next().text(t?$t("请完善筛选条件,选中指定层级"):$t("请完善筛选条件")).show(),!(g=!0)}),void(g||(m=[],f=0,h.forEach(function(e){var a=[];_.each(e.getData(),function(e){var t=_slicedToArray(e,3),r=t[0],i=t[1],t=t[2];f++,u.push({fieldName:"partner_id"==r?r+".name":r,fieldType:e[3],fieldValue:"partner_id"==r&&_.contains(["IN","NIN"],i)?t.split(";"):_.contains(["IS","ISN"],i)?[""]:_.isArray(t)?t:[t],operate:e[1],ruleOrder:f}),a.push(f)}),m.push("("+a.join(" and ")+")")}),y=w[this.config]().typeMap,_.each(t,function(e,a){p=p.concat(_.map(e,function(e){var t,r=_.invert(y)[a],i={permission:o,receiveId:e,receiveType:r,receiveCascade:0};return"group"!=a&&"stopDepartment"!=a||((t=v.getCircleById(e))||Fx.contacts.getCircleByIdSync(e,function(e){t=e}),t&&(r="default__c"==t.recordType?2:8),i.receiveType=r,i.receiveCascade=s),i}))}),v.FHHApi({url:"add"===b.get("type")?"/EM1HNCRM/API/v1/object/data_privilege/service/addFieldShare":"/EM1HNCRM/API/v1/object/data_privilege/service/updateFieldShare",data:{describeApiName:c,receives:p,ruleCode:b.get("data").ruleCode||null,ruleName:l,ruleParse:m.join(" or "),rules:u},success:function(e){0===e.Result.StatusCode&&(b.trigger("success",r),v.remind(1,$t("操作成功！")),b.hide())}},{submitSelector:e})))):(b.$shareobj.next().show(),!1):(b.$rulename.next().text($t("请输入规则名称")).show(),!1)));this.hide()},hide:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,c.superclass.hide.call(this)},destroy:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,c.superclass.destroy.call(this)}});r.exports=c});
define("crm-setting/datapermissions/datashare/myobject/myobjectdialog0",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","../template/add-dialog-html","../../common/config"],function(e,t,i){var g=e("crm-modules/common/util"),r=e("crm-widget/dialog/dialog"),p=e("crm-widget/selector/selector"),s=e("crm-widget/select/select"),a=e("../template/add-dialog-html"),m=e("../../common/config"),h=999999,n=r.extend({config:"myObjectByField",attrs:{title:$t("共享规则"),content:'<div class="crm-loading"></div>',className:"crm-s-datapermissions",showBtns:!0,showScroll:!0,size:"md",type:"add",data:{}},events:{"click .update-tip .title":"onToggleWarn","click .j-toggle":"onToggleCondition","focus .rulename-ipt":"onRemoveErrmsg","click .b-g-btn":"onSave","click .b-g-btn-cancel":"destroy","click .j-permission":"onTogglePerMission"},render:function(){return this.widgets={},this.myObjectDataPermissions=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,this.myObjectDataPermissions=this.myObjectDataPermissions.filter(function(e){return 1!=e.objectType}),this.userGroups=JSON.parse(sessionStorage.getItem("userGroups")),n.superclass.render.call(this)},show:function(e){this.widgets={},this.set(e);var t=n.superclass.show.call(this),i="edit"===this.get("type"),r="datasource"===this.get("scopeType")||"datasourcedep"==this.get("scopeType");return this.setContent(a({isEdit:i,isDatasource:r,isDep:"datasourcedep"==this.get("scopeType"),data:this.get("data"),showField:this.get("showField")&&"BpmInstance"!=e.data.ObjectType&&"ApprovalInstanceObj"!=e.data.ObjectType,isSysObj:!1,disabled:this.get("disabled")})),this.$sharedata=this.$(".j-sharedata"),this.$rulename=this.$(".rulename-ipt"),this.$shareobj=this.$(".j-shareobj"),this.$fieldfilter=this.$(".j-fieldfilter"),this.initSelectBar("datasource",i||this.get("disabled")),this.initSelectBar("target",i&&r||this.get("disabled")),this.initShareData(i&&r),this.initShareObj(i),this.resizedialog(),t},curType:"datasource",initSelectBar:function(e,t){var i,r,a,s,n=this,o=n.get("data"),d=("datasourcedep"===n.get("scopeType")||"datasourcedep"==n.curType)&&"target"!=e,l="datasource"===n.get("scopeType")||"datasourcedep"===n.get("scopeType")||"datasourcedep"==n.curType,c=n.get(e),c="target"===e&&c&&"usergroup"===c[0].type?_.findWhere(n.userGroups,{id:c[0].id,type:0})?c:[]:c||[],u=0,l=("target"===e&&(u=1,"edit"==this.get("type"))&&(l?i=o.receiveDeptCascade:o.receives[0]&&(i=o.receives[0].receiveCascade),u=1==i?2:1),new p({$wrap:n.$(".j-"+e),zIndex:n.get("zIndex"),group:{company:"datasource"===e||d,chooseType:"department"},member:!d,usergroup:!d,role:!d,excludeItems:{role:["personnelrole"]},groupIncludeChildrenStatus:u,single:!1,label:d?$t("请选择数据归属部门"):$t("选择员工部门用户组或角色"),defaultSelectedItems:(s={member:[],group:[],usergroup:[],role:[]},_.each(c,function(e){r=e.id,"member"==(a=e.type)?s.member.push(r):"group"==a?s.group.push(r):"usergroup"===a?s.usergroup.push(r):"role"===a&&s.role.push(r)}),s)}));t&&l.lock(),l.on("addItem",function(){this.options.$wrap.next().hide(),n.resizedialog()}),n.widgets[e]=l},initShareData:function(e){var t=this,i=t.get("data"),r=-1===(r=i.ObjectType||i.ObjectDescribeApiName)?"":r,a=t.myObjectDataPermissions.reduce(function(e,t){return e.push({name:t.ObjectDescribeDisplayName,value:t.ObjectDescribeApiName}),e},[]),i=(i.ObjectType&&"-2"==i.ObjectType&&(a=_.filter(a,function(e){return e.value&&-1==e.value.indexOf("__c")})),i.ObjectType&&"-1"==i.ObjectType&&(a=_.filter(a,function(e){return e.value&&-1!=e.value.indexOf("__c")})),new s({$wrap:t.$sharedata,zIndex:t.get("zIndex"),width:500,multiple:"multiple",allCheck:!0,options:a,disabled:e,stopPropagation:!0,defaultValue:r}));i.on("change",function(){this.options.$wrap.next().hide(),t.resizedialog()}),t.widgets.sharedata=i},initShareObj:function(e){var t=this,i=t.get("data"),r=t.get("data").entityId,a=t.myObjectDataPermissions.map(function(e){return{name:e.ObjectDescribeDisplayName,value:e.ObjectDescribeApiName}}).filter(function(e){return-1==["SalesOrderProductObj","ReturnedGoodsInvoiceProductObj","GoalValueObj"].indexOf(e.value)}),a=_.filter(a,function(e){return"BpmInstance"!=e.value&&"ApprovalInstanceObj"!=e.value}),i=(i.ObjectType&&"-2"==i.ObjectType&&(a=_.filter(a,function(e){return e.value&&-1==e.value.indexOf("__c")})),i.ObjectType&&"-1"==i.ObjectType&&(a=_.filter(a,function(e){return e.value&&-1!=e.value.indexOf("__c")})),new s({$wrap:t.$shareobj,zIndex:t.get("zIndex"),multiple:"single",options:[{name:$t("请选择"),value:""}].concat(a),defaultValue:r,disabled:e}));i.on("change",function(e){this.options.$wrap.next().hide(),t.renderFieldfilter({apiname:e}),t.resizedialog()}),e&&t.renderFieldfilter({apiname:r}),t.widgets.shareobj=i},renderFieldfilter:function(a){var t=this,i=t.widgets.filter,r=this.get("data").rules;i?i.reset(a):e.async("crm-modules/common/fieldfilter/fieldfilter",function(e){(i=new e(_.extend(a,{$wrapper:t.$fieldfilter,width:636,title:'<span class="del-span j-delete-filter"></span><span style="color:#919eab">'+$t("且（AND）")+"</span>",max:10,filterType:m.filterType,filterApiname:m.filterApiname,openQuoteField:!0,defaultValue:r&&_.map(r,function(e){return[e.fieldName,e.operate,e.fieldValue]}),parseCompare:function(e,t,i){return"employee"==t||"department"==t||"dimension"==t?_.map([13,14,9,10],function(e){return i[e-1]}):e},parseFields:function(e){var t,i=CRM.util.deepClone(e);for(t in i){var r=i[t];("formula"==r.type&&_.contains(["date_time","date","time"],r.return_type)||"formula"==r.type&&!r.is_index||"quote"==r.type&&!1===r.is_index)&&delete i[t],"formula"===r.type&&(r.type=r.return_type||r.type),"select_one"===r.type&&r.cascade_parent_api_name&&delete r.cascade_parent_api_name,-1!=m.filterType.indexOf(r.quote_field_type)&&delete i[t],-1!=m.filterApiname.indexOf(r.api_name)&&delete i[t],"NewOpportunityObj"!==a.apiname&&"sales_process_id"===r.api_name&&delete i[t]}return i},formatFields:function(e){for(var t in e){t=e[t];t.type=t.quote_field_type||t.type}return e},disabled:t.get("disabled")}))).on("render",function(){t.resizedialog(),t.get("disabled")&&t.$fieldfilter.append('<div class="fieldfilter-mask"></div>')}),i.on("change",function(){this.opts.$wrapper.next().hide()}),i.on("add.item",function(){t.resizedialog()}),i.on("del.item",function(){t.resizedialog()}),t.widgets.filter=i})},onToggleWarn:function(e){$(e.currentTarget).closest(".update-tip").toggleClass("update-tip-hover"),this.resizedialog()},onToggleCondition:function(e){if($(e.currentTarget).hasClass("disabled-selected"))return e.stopPropagation(),!1;e=$(e.target).data("base"),e="datasourcedep"==(this.curType=e)?"datasource":e;this.$(".j-basefield").hide(),this.$(".j-basedatasource").hide(),this.$(".j-base"+e).show(),"datasource"==e&&"edit"!==this.get("type")&&(this.widgets.datasource&&this.widgets.datasource.destroy(),this.widgets.datasource=null,this.initSelectBar("datasource")),this.resizedialog()},onTogglePerMission:function(e){if($(e.currentTarget).hasClass("disabled-selected"))return e.stopPropagation(),!1},onRemoveErrmsg:function(e){$(e.currentTarget).next().hide()},onSave:function(e){var t,i,r,a,s,n,o,d,l,c=this,e=$(e.currentTarget),u=c.widgets.target.getValue(),p=c.widgets.target.getIncludeGroupChildrenStatus()?1:0;return u.member.length+u.group.length+u.usergroup.length+u.role.length==0?(c.$(".j-target").next().show(),!1):(a="datasource"===(t=this.$(".j-toggle.mn-selected").data("base"))||"datasourcedep"===t,i=c.$(".j-permission.mn-selected").data("permissiontype"),a?(a=c.widgets.datasource.getValue(),r=c.widgets.sharedata.getValue(),(a=_.extend({member:[],group:[],usergroup:[],role:[]},a)).member.length+a.group.length+a.usergroup.length+a.role.length==0?(c.$(".j-datasource").next().show(),!1):0===r.length?(c.$sharedata.next().show(),!1):_.contains(u.group,h)&&_.contains(a.group,h)?(g.remind(3,$t("数据来源和数据共享不能同时为")+g.getCircleById(h).name),!1):void g.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/addOrUpdateShareRules",data:{ObjectDescribeApiNames:r,SourceEmployeeIDs:a.member,SourceCircleIDs:a.group,SourceUserGroupIDs:a.usergroup,SourceRoleIDs:a.role,TargetEmployeeIDs:u.member,TargetCircleIDs:u.group,TargetUserGroupIDs:u.usergroup,TargetRoleIDs:u.role,PermissionType:i,receiveDeptCascade:p,basedType:{datasource:0,datasourcedep:1}[t]},success:function(e){0===e.Result.StatusCode&&(c.trigger("success",t),g.remind(1,$t("操作成功！")),c.hide())}},{submitSelector:e})):(r=c.widgets.shareobj.getValue(),a=$.trim(c.$rulename.val()),s=[],n=[],a?g.isContainEmojiCharacter(a)?(c.$rulename.next().text($t("规则名称不能包含emoji表情符")).show(),!1):r?(o=c.widgets.filter).isExitNull()?(c.$fieldfilter.next().show(),!1):(d=[],l=m[this.config]().typeMap,_.each(o.getData(),function(e,t){var i=e[2];n.push({fieldName:e[0],fieldType:e[3],fieldValue:_.isArray(i)?i:[i],operate:e[1],ruleOrder:t+1}),d.push(t+1)}),_.each(u,function(e,t){s=s.concat(_.map(e,function(e){return{permission:i,receiveId:e,receiveType:_.invert(l)[t],receiveCascade:p}}))}),void g.FHHApi({url:"add"===c.get("type")?"/EM1HNCRM/API/v1/object/data_privilege/service/addFieldShare":"/EM1HNCRM/API/v1/object/data_privilege/service/updateFieldShare",data:{describeApiName:r,receives:s,ruleCode:c.get("data").ruleCode||null,ruleName:a,ruleParse:d.join(" and "),rules:n},success:function(e){0===e.Result.StatusCode&&(c.trigger("success",t),g.remind(1,$t("操作成功！")),c.hide())}},{submitSelector:e})):(c.$shareobj.next().show(),!1):(c.$rulename.next().text($t("请输入规则名称")).show(),!1)))},hide:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,n.superclass.hide.call(this)},destroy:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,n.superclass.destroy.call(this)}});i.exports=n});
define("crm-setting/datapermissions/datashare/template/add-dialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (isEdit) {
                __p += ' <div class="crm-g-form datashare-dialog"> <div class="update-tip update-tip2"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="text"> ' + ((__t = $t("crm.基于条件的共享规则2")) == null ? "" : __t) + ' </div> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("ruleType")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item disabled-selected ';
                if (isDatasource && !isDep && !isDepOrg) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasource"></span> <span class="radio-lb">' + ((__t = $t("基于数据负责人")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item disabled-selected ';
                if (isDepOrg) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasourcedep_org"></span> <span class="radio-lb">' + ((__t = $t("jiyushujuguishuzuzhi")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item disabled-selected ';
                if (isDep) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasourcedep"></span> <span class="radio-lb">' + ((__t = $t("基于数据归属部门")) == null ? "" : __t) + "</span> </span> ";
                if (showField) {
                    __p += ' <span class="radio-item"> <span class="j-toggle mn-radio-item disabled-selected ';
                    if (!isDatasource) {
                        __p += (__t = " mn-selected") == null ? "" : __t;
                    }
                    __p += '" data-base="field"></span> <span class="radio-lb guide-mark">' + ((__t = $t("基于条件")) == null ? "" : __t) + '</span> <div class="qus-box crm-ico-qus"> <div class="qus-box-item"> <em class="sanjiao"></em> <div class="mes"> 1、' + ((__t = $t("客户数据负责人为空的数据，基于条件配置的共享规则不生效")) == null ? "" : __t) + "。<br/> 2、" + ((__t = $t("客户对象，不区分上下游数据")) == null ? "" : __t) + " </div> </div> </div> </span> ";
                }
                __p += " </div> </div> ";
                if (isDatasource) {
                    __p += ' <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据来源于")) == null ? "" : __t) + '</label> <div class="fm-wrap disabled-wraper"> <div class="j-datasource"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> <div class="disabled-layout"></div> </div> </div> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的数据")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-sharedata"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的数据！")) == null ? "" : __t) + "</span> </div> </div> ";
                } else {
                    __p += ' <div class="j-basefield"> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("规则名称")) == null ? "" : __t) + '</label> <input class="b-g-ipt fm-ipt rulename-ipt ' + ((__t = disabled ? "b-g-ipt-disabled" : "") == null ? "" : __t) + '" ' + ((__t = disabled ? disabled = "disabled" : "") == null ? "" : __t) + ' maxlength="20" value="' + ((__t = data.ruleName) == null ? "" : __t) + '" placeholder="' + ((__t = $t("最多20个中文字符")) == null ? "" : __t) + '"> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请输入规则名称!")) == null ? "" : __t) + '</span> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的对象")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-shareobj"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的对象")) == null ? "" : __t) + '</span> <span class="fm-note" style="display:none">' + ((__t = $t("基于条件的共享规则，若客户负责人为空，则共享规则不生效")) == null ? "" : __t) + '</span> </div> </div> <div class="j-fieldfilter" style="position: relative;z-index:100"> <div class="condition-groups"><div class="crm-loading"></div></div> </div> <!-- <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请完善筛选条件")) == null ? "" : __t) + "</span> --> </div> ";
                }
                __p += ' <div class="fm-item dash-line"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据共享到")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-target"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享后的权限")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + " ";
                if ((data.permission || data.PermissionType) === 1) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-permissiontype="1"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span></span> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + " ";
                if ((data.permission || data.PermissionType) === 2) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-permissiontype="2"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + "</span></span> </div> </div> </div> ";
            } else {
                __p += ' <div class="crm-g-form datashare-dialog advancedatashare-dialog"> <div class="update-tip update-tip2"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="text"> ' + ((__t = $t("crm.基于条件的共享规则2")) == null ? "" : __t) + '<br/ > </div> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("ruleType")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (isCopy) {
                    __p += (__t = "disabled-selected") == null ? "" : __t;
                }
                __p += " ";
                if (isDatasource && !isDep && !isDepOrg) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasource"></span> <span class="radio-lb">' + ((__t = $t("基于数据负责人")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (isCopy) {
                    __p += (__t = "disabled-selected") == null ? "" : __t;
                }
                __p += " ";
                if (isDepOrg) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasourcedep_org"></span> <span class="radio-lb">' + ((__t = $t("jiyushujuguishuzuzhi")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                if (isCopy) {
                    __p += (__t = "disabled-selected") == null ? "" : __t;
                }
                __p += " ";
                if (isDep) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasourcedep"></span> <span class="radio-lb">' + ((__t = $t("基于数据归属部门")) == null ? "" : __t) + "</span> </span> ";
                if (showField) {
                    __p += ' <span class="radio-item"> <span class="j-toggle mn-radio-item ';
                    if (isCopy) {
                        __p += (__t = "disabled-selected") == null ? "" : __t;
                    }
                    __p += " ";
                    if (!isDatasource) {
                        __p += (__t = " mn-selected") == null ? "" : __t;
                    }
                    __p += '" data-base="field"></span> <span class="radio-lb guide-mark">' + ((__t = $t("基于条件")) == null ? "" : __t) + '</span> <div class="qus-box crm-ico-qus"> <div class="qus-box-item"> <em class="sanjiao"></em> <div class="mes"> 1、' + ((__t = $t("客户数据负责人为空的数据，基于条件配置的共享规则不生效")) == null ? "" : __t) + "。<br/> 2、" + ((__t = $t("客户对象，不区分上下游数据")) == null ? "" : __t) + " </div> </div> </div> </span> ";
                }
                __p += ' </div> </div> <div class="j-basedatasource" ';
                if (!isDatasource) {
                    __p += ' style="display:none" ';
                }
                __p += '> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据来源于")) == null ? "" : __t) + '<div class="qus-box crm-ico-qus"><div class="qus-box-item"><em class="sanjiao"></em><div class="mes">' + ((__t = $t("按部门添加时支持附属部门同时所选部门包含子部门")) == null ? "" : __t) + '</div></div></div></label> <div class="fm-wrap"> <div class="j-datasource"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的数据")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-sharedata"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的数据")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="j-basefield" ';
                if (isDatasource) {
                    __p += ' style="display:none" ';
                }
                __p += '> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("规则名称")) == null ? "" : __t) + '</label> <input class="b-g-ipt fm-ipt rulename-ipt" maxlength="20" value="' + ((__t = data.ruleName) == null ? "" : __t) + '" placeholder="' + ((__t = $t("最多20个中文字符")) == null ? "" : __t) + '"> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请输入规则名称!")) == null ? "" : __t) + '</span> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的对象")) == null ? "" : __t) + ' <div class="qus-box crm-ico-qus"><div class="qus-box-item"><em class="sanjiao"></em><div class="mes">' + ((__t = $t("关于状态字段变更：客户、线索、销售订单、商机优化了原状态业务意义，变更为新的状态+生命状态，原状态字段变为【状态（原）】将在未来版本下线，为了不影响正常使用请勿配置相关条件设置，如已设置请更新为新的状态！具体每个对象的状态变化内容请参考产品手册。(涉及对象包含：线索，客户，市场活动，联系人，商机，订单，退货单，退款等)")) == null ? "" : __t) + '</div></div></div> </label> <div class="fm-wrap"> <div class="j-shareobj"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的对象")) == null ? "" : __t) + '</span> <span class="fm-note" style="display:none">' + ((__t = $t("基于条件的共享规则，若客户负责人为空，则共享规则不生效")) == null ? "" : __t) + '</span> </div> </div> <div class="j-fieldfilter" style="position: relative;z-index:100"> <div class="condition-groups"></div> </div> <!-- <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请完善筛选条件")) == null ? "" : __t) + '</span> --> </div> <div class="fm-item dash-line"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据共享到")) == null ? "" : __t) + '<div class="qus-box crm-ico-qus"><div class="qus-box-item"><em class="sanjiao"></em><div class="mes">' + ((__t = $t("按部门添加时支持附属部门但是所选部门不包含子部门")) == null ? "" : __t) + '</div></div></div></label> <div class="fm-wrap"> <div class="j-target"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享后的权限")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + " ";
                if (!isCopy || (data.permission || data.PermissionType) === 1) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-permissiontype="1"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span></span> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + " ";
                if (isCopy && (data.permission || data.PermissionType) === 2) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-permissiontype="2"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + "</span></span> </div> </div> </div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/datapermissions/datashare/template/import-rules-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<fx-dialog class="dialog-datapermissions-datashare-import_rules" :class="[!beforeImport?\'after-import\':\'\']" :visible.sync="show" size="small" max-height="400px" title="' + ((__t = $t("导入数据共享规则")) == null ? "" : __t) + '" > <div v-show="beforeImport"> <div style="color: #545861;">' + ((__t = $t("请选择数据来源")) == null ? "" : __t) + '</div> <fx-select v-model="dataSource" :options="dataSourceOptions" filterable size="small" @change="onDataSourceChange" ></fx-select> <div style="color: #545861;">' + ((__t = $t("请添加您要导入的数据")) == null ? "" : __t) + '</div> <div><a :href="downloadUrl">' + ((__t = $t("下载数据模板")) == null ? "" : __t) + '</a></div> <div ref="uploadBox" class="uploadBox"> <div v-show="!hasFile"> <i class="icon fx-icon-upload"></i> <span >' + ((__t = $t("将文件拖到此处或")) == null ? "" : __t) + '<a href="javascript:;" @click="onClickUpload" >' + ((__t = $t("点击上传")) == null ? "" : __t) + '</a ></span ><br /> <span class="note">%note%</span> </div> <div v-show="hasFile"> <i class="icon el-icon-circle-check"></i> <span>' + ((__t = $t("文件已添加")) == null ? "" : __t) + '</span><br /> <span class="note note2">%filename%</span> &nbsp;&nbsp;<a href="javascript:;" @click="onClickUpload" >' + ((__t = $t("重新上传")) == null ? "" : __t) + '</a > </div> </div> <input type="file" ref="fileInput" accept=".xlsx,.xls" style="display: none" /> </div> <div v-show="!beforeImport"> <div v-show="percentage<100" style="text-align: center; padding: 16px 0">' + ((__t = $t("正在收集数据")) == null ? "" : __t) + '</div> <fx-progress v-show="percentage<100" :percentage="percentage" :stroke-width="10" color="#4D8CE6" :show-text="false" style="margin-bottom: 30px" ></fx-progress> <div v-show="percentage>=100" style="padding: 26px 0;">%progressResult%</div> </div> <template slot="footer"> <fx-button type="primary" :disabled="disabled" size="small" @click="onStartImport" >' + ((__t = $t("开始导入")) == null ? "" : __t) + '</fx-button > <fx-button size="small" @click="onCancel">' + ((__t = $t("取消")) == null ? "" : __t) + "</fx-button> </template> </fx-dialog>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/datashare/template/list-view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="update-tip"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="text"> ' + ((__t = $t("crm.基于条件的共享规则")) == null ? "" : __t) + "<br/ >" + ((__t = $t("注意：数据共享启用或关闭后，根据数据量的大小，约2个小时后会生效")) == null ? "" : __t) + ' </div> </div> <a href="javascript:;" class="btn-export-list">' + ((__t = $t("查看")) == null ? "" : __t) + "<em>" + ((__t = $t("导入导出结果")) == null ? "" : __t) + '</em></a> </div> <div class="myobject-header"> <span class="label">' + ((__t = $t("数据来源范围")) == null ? "" : __t) + '</span> <div class="scope"></div> <span class="label">' + ((__t = $t("共享范围:")) == null ? "" : __t) + '</span> <div class="range"></div> <div class="crm-btn-box"> <i class="fx-icon-set"></i> <span class="crm-btn crm-btn-primary j-add">+' + ((__t = $t("新建共享规则")) == null ? "" : __t) + '</span> <span class="crm-btn j-import">' + ((__t = $t("导入")) == null ? "" : __t) + '</span> <span class="crm-btn j-export">' + ((__t = $t("导出")) == null ? "" : __t) + '</span> </div> </div> <a href="javascript:;" class="btn-back-data-list" style="display: none">&lt; ' + ((__t = $t("返回数据共享列表")) == null ? "" : __t) + '</a> <div class="operationType" style="display: none"> <span>' + ((__t = $t("操作类型")) == null ? "" : __t) + ': </span> <div class="select"></div> </div> <div class="table-wrap table-wrap1"></div> <div class="table-wrap table-wrap2" style="display: none"></div>';
        }
        return __p;
    };
});
define("crm-setting/datapermissions/datashare/template/nav-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<ul> ";
            if (!_.isEmpty(myObjectDataPermissions)) {
                __p += ' <!-- <li data-roleid="-2" class="all" data-is_myobject="true">' + ((__t = $t("全部预置对象")) == null ? "" : __t) + '</li> --> <!-- <li data-roleid="-1" class="all" data-is_myobject="true">' + ((__t = $t("全部自定义对象")) == null ? "" : __t) + '</li> --> <li data-roleid="-1" class="all" data-is_myobject="true">' + ((__t = $t("全部对象")) == null ? "" : __t) + '</li> <div class="quickly-search-box"> <!--阻止chrome浏览器自动补全--> <input type="text" style="display:none;" autocomplete="off"> <input type="password" style="display:none;" autocomplete="new-password"> <!--阻止chrome浏览器自动补全--> <input class="b-g-ipt quickly-search-input" type="text" placeholder="' + ((__t = $t("请输入", {}, "请输入")) == null ? "" : __t) + '"> <span class="set-search-btn el-icon-search"></span> <span class="set-search-reset fx-icon-close-2" style="display: none;"></span> </div> ';
            }
            __p += ' <div class="line"></div> ';
            _.each(myObjectDataPermissions, function(item, index) {
                __p += ' <li class="crm-ui-title" data-title="true" data-pos="right" data-roleid="' + ((__t = item.ObjectDescribeApiName) == null ? "" : __t) + '" data-apiname="' + ((__t = item.ObjectDescribeDisplayName) == null ? "" : __t) + '" data-is_myobject="true"> ' + ((__t = item.ObjectDescribeDisplayName) == null ? "" : __t) + " </li> ";
            });
            __p += " </ul>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/datashare2/datashare",["./template/nav-html","./myobject/myobject"],function(t,e,s){var i=CRM.util,a=t("./template/nav-html"),n=t("./myobject/myobject");s.exports=Backbone.View.extend({events:{"click .leftnav li":"onChangeRole"},initialize:function(){var e=this;e.crmDataPermissions=sessionStorage.getItem("crmDataPermissions"),e.crmDataPermissions?e.render():i.getObjectDataPermissions(function(t){e.crmDataPermissions=t,sessionStorage.setItem("crmDataPermissions",JSON.stringify(t)),e.render()})},render:function(){var t=_.isObject(this.crmDataPermissions)?this.crmDataPermissions:JSON.parse(this.crmDataPermissions);this.el.querySelector(".leftnav").innerHTML=a(t),this.$(".all").eq(0).addClass("active"),this.myobject=new n({el:this.$(".rightsection"),objectType:this.$(".all").eq(0).attr("data-roleid")}),CRM.api.show_guide({show_type:"tip",key:"datashate_setting_guide",data:[{$target:this.$(".j-add"),pos:"right",appendBody:!0,text:$t("可以根据数据内容设置不同数据共享")}]})},show:function(){this.$el.show(),this.myobject&&this.myobject.resize()},hide:function(){this._guide&&this._guide.destroy(),this.$el.hide()},onChangeRole:function(t){t=$(t.currentTarget);t.hasClass("active")||t.hasClass("btn-to-add-role")||(t.addClass("active").siblings().removeClass("active"),this.myobject&&this.myobject.destroy(),this.myobject=new n({el:this.$(".rightsection"),objectType:t.data("roleid")}))},destroy:function(){_.each(["myobject","_guide"],function(t){this[t]&&(this[t].destroy(),this[t]=null)},this)}})});
define("crm-setting/datapermissions/datashare2/myobject/exportlist",["crm-modules/common/util","crm-widget/table/table"],function(t,e,a){var r=t("crm-modules/common/util"),s=t("crm-widget/table/table");a.exports=function(a){var t={taskTable:null,initTasklistTable:function(t){var a=this,e=(this.type="DataShareExport",this.getOptions());this.taskTable&&this.taskTable.destroy(),this.taskTable=new s(e),this.taskTable.stopQueryStatus=!1,this.taskTable.$el.on("click",".j-oprate",function(t){var t=$(t.target),e=t.closest("tr"),e=$.extend({},a.taskTable.getRowData(e));t.hasClass("j-d-download")&&!t.hasClass("disable")&&a.startDownload(e)}),this.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:a.type,operator:"LT",value_type:0}]}]},!0),this.taskTable.start()},getOptions:function(){var e=this,t=this.getColumns();return{$el:a.$wrap,url:"/EM1HPAASBATCH/task/list",title:"",trHandle:!0,openStart:!0,alwaysShowTermBatch:!0,showMultiple:!1,requestType:"FHHApi",postData:{},autoHeight:!1,caption:{},columns:t,rowCallBack:function(t,e){},initComplete:function(t){},getDataBack:function(t,e){return t},formatData:function(t){return e.listData=t&&t.data.map(function(t){return t.taskStatus})||[],0<t.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?e.queryStatus(t.pageNumber,t.pageSize):e.timer&&clearTimeout(e.timer),{totalCount:t.totalCount,data:t.data}},getFullDataBack:function(t){t.Error&&"s211030015"==t.Error.Code&&setTimeout(function(){r.alert(t.Error.Message,function(){location.reload()})},300)}}},queryStatus:function(e,r){var s=this;clearTimeout(s.timer),s.taskTable.stopQueryStatus||CRM.util.FHHApi({url:"/EM1HPAASBATCH/task/list",data:{pageNumber:e||1,pageSize:r||20,wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:s.type,operator:"LT",value_type:0}]}]},success:function(t){var a;0==t.Result.StatusCode&&(0<t.Value.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?(a=!1,t.Value.data.forEach(function(t,e){t&&t.taskStatus!==s.listData[e]&&(a=!0)}),s.timer=a?setTimeout(function(){clearTimeout(s.timer),s.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:s.type,operator:"LT",value_type:0}]}]},!0)},5e3):setTimeout(function(){clearTimeout(s.timer),s.queryStatus(e,r)},3e3)):(clearTimeout(s.timer),s.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:s.type,operator:"LT",value_type:0}]}]},!0)))}})},getColumns:function(){return[{data:"operationType",title:$t("操作类型"),render:function(t){if("DataShareExport"==t)return $t("导出共享规则")}},{data:"taskStatus",title:$t("任务状态"),width:200,render:function(t){switch(t){case"Ready":return $t("已准备");case"Running":return $t("执行中");case"Stopped":return $t("已停止");case"Finished":return $t("已完成")}}},{data:"lastTaskResult",title:$t("导出文件"),width:200,render:function(t){return t.fileNames&&t.fileNames.length?t.fileNames.join(", "):"--"}},{data:"createdBy",width:200,title:$t("创建人")||"--",render:function(t){if(t){t=FS.contacts.getEmployeeById(t);if(t)return t.name}}},{data:"createTime",title:$t("创建时间"),render:function(t){return t?FS.moment(t).format("YYYY-MM-DD HH:mm:ss"):"--"}},{data:"id",width:200,title:$t("任务ID")},{data:"taskStatus",title:$t("操作"),lastFixed:!0,render:function(t,e,a){var r="";return'<span class="options-wrapper"><a href="javascript:;" class="j-oprate j-d-download '+(r="Finished"==t&&a.lastTaskResult&&0!=a.lastTaskResult.warehouseFiles.length?r:"disable")+'" download="">'+$t("下载")+"</a></span>"}}]},startDownload:function(t){if(t.lastTaskResult)for(var e=t.lastTaskResult.warehouseFiles,a=0;a<e.length;a++){var r=document.createElement("a"),s=document.createEvent("HTMLEvents");s.initEvent("click",!1,!1),r.download=e[a].fileName,r.href=FS.BASE_PATH+"/FSC/EM/File/DownloadByPath?path="+e[a].nPath+"&&name="+e[a].fileName,r.dispatchEvent(s),r.click()}}};return t.initTasklistTable(),t.taskTable}});
define("crm-setting/datapermissions/datashare2/myobject/importlist",["crm-modules/common/util","crm-widget/table/table"],function(t,a,e){var s=t("crm-modules/common/util"),i=t("crm-widget/table/table");e.exports=function(e){var t={taskTable:null,initTasklistTable:function(t){var e=this,a=(this.type="DataShareImport",this.getOptions());this.taskTable&&this.taskTable.destroy(),this.taskTable=new i(a),this.taskTable.stopQueryStatus=!1,this.taskTable.$el.on("click",".j-oprate",function(t){var t=$(t.target),a=t.closest("tr"),a=$.extend({},e.taskTable.getRowData(a));t.hasClass("j-d-download")&&!t.hasClass("disable")&&e.startDownload(a)}),this.taskTable.setParam({wheres:[]},!0),this.taskTable.start()},getOptions:function(){var a=this,t=this.getColumns();return{$el:e.$wrap,url:"/EM1HPAASBATCH/task/findImportDataShare",title:"",trHandle:!0,openStart:!0,alwaysShowTermBatch:!0,showMultiple:!1,requestType:"FHHApi",postData:{},autoHeight:!1,caption:{},columns:t,rowCallBack:function(t,a){},initComplete:function(t){},getDataBack:function(t,a){return t},formatData:function(t){return a.listData=t&&t.data.map(function(t){return t.taskStatus})||[],0<t.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?a.queryStatus(t.pageNumber,t.pageSize):a.timer&&clearTimeout(a.timer),{totalCount:t.totalCount,data:t.data}},getFullDataBack:function(t){t.Error&&"s211030015"==t.Error.Code&&setTimeout(function(){s.alert(t.Error.Message,function(){location.reload()})},300)}}},queryStatus:function(a,s){var i=this;clearTimeout(i.timer),i.taskTable.stopQueryStatus||CRM.util.FHHApi({url:"/EM1HPAASBATCH/task/findImportDataShare",data:{pageNumber:a||1,pageSize:s||20,wheres:[]},success:function(t){var e;0==t.Result.StatusCode&&(0<t.Value.data.filter(function(t){return"Finished"!=t.taskStatus}).length?(e=!1,t.Value.data.forEach(function(t,a){t&&t.taskStatus!==i.listData[a].taskStatus&&(e=!0)}),i.timer=e?setTimeout(function(){clearTimeout(i.timer),i.taskTable.setParam({wheres:[]},!0)},5e3):setTimeout(function(){clearTimeout(i.timer),i.queryStatus(a,s)},3e3)):(clearTimeout(i.timer),i.taskTable.setParam({wheres:[]},!0)))}})},getColumns:function(){return[{data:"operationType",title:$t("操作类型")},{data:"taskStatusDescription",width:200,title:$t("任务状态")},{data:"fileName",title:$t("导入文件"),width:200},{data:"userName",width:200,title:$t("创建人")||"--"},{data:"createTime",title:$t("创建时间"),render:function(t){return t?FS.moment(t).format("YYYY-MM-DD HH:mm:ss"):"--"}},{data:"taskId",width:200,title:$t("任务ID")},{data:"taskStatus",title:$t("操作"),lastFixed:!0,render:function(t,a,e){var s="";return s="Finished"===t&&(s='<span class="options-wrapper"><a href="'+FS.util.getFscLink(e.filePath.split(".")[0],e.fileName,!0)+'" target="_blank" class="j-oprate" >'+$t("下载")+"</a></span>",e.rowCount==e.successRowCount)&&0!=e.successRowCount?'<span class="options-wrapper">'+$t("导入成功")+"</span>":s}}]},startDownload:function(t){var a,e;t.filePath&&(a=document.createElement("a"),(e=document.createEvent("HTMLEvents")).initEvent("click",!1,!1),a.download=t.fileName,a.href=FS.util.getFscLink(t.filePath.split(".")[0],t.fileName,!0),a.dispatchEvent(e),a.click())}};return t.initTasklistTable(),t.taskTable}});
define("crm-setting/datapermissions/datashare2/myobject/myobject",["crm-modules/common/util","crm-widget/select/select","crm-widget/selector/selector","../template/list-view-html","../template/import-rules-html","./myobjectbyfield","./myobjectbydatasource","./exportlist","./importlist","./myobjectdialog","base-h5uploader"],function(e,t,a){var s=e("crm-modules/common/util"),o=e("crm-widget/select/select"),i=e("crm-widget/selector/selector"),r=e("../template/list-view-html"),n=e("../template/import-rules-html"),l=e("./myobjectbyfield"),p=e("./myobjectbydatasource"),c=e("./exportlist"),d=e("./importlist"),h=e("./myobjectdialog"),u=e("base-h5uploader");a.exports=Backbone.View.extend({initialize:function(e){this.widgets={},this.el.innerHTML=r(),this.$wrap=this.$(".table-wrap1");var t=localStorage.getItem("datapermission_datashare");t&&(this.scopeType=t,localStorage.removeItem("datapermission_datashare")),this.initDatasourceScope(),this.initShareRange(),this.options={$el:this.$wrap,objectType:e.objectType},"field"===this.scopeType?this.widgets.myobjectbyfield=new l(this.options):this.widgets.myobjectdatasource=new p(_.extend(this.options,{scopeType:this.scopeType})),this.getDownloadDataShareTemplate()},resize:function(){this.widgets&&(this.widgets.myobjectbyfield&&this.widgets.myobjectbyfield.resize(),this.widgets.myobjectdatasource)&&this.widgets.myobjectdatasource.resize()},events:{"click .j-add":"onAdd","click .j-export":"onExport","click .j-import":"onImport","click .btn-export-list":"showExportList","click .btn-back-data-list":"hideExportList"},scopeType:"datasource",initDatasourceScope:function(){var t=this,a=t.widgets,e=a.scope,i=[{value:"datasource",name:$t("基于数据负责人")},{value:"datasourcedep",name:$t("基于数据归属部门")}];"BpmInstance"!=t.options.objectType&&"ApprovalInstanceObj"!=t.options.objectType&&i.push({value:"field",name:$t("基于条件")}),(e=new o({$wrap:t.$(".scope"),zIndex:1e3,size:1,options:i,defaultVal:t.scopeType})).on("change",function(e){_.each(["myobjectbyfield","myobjectdatasource"],function(e){a[e]&&(a[e].destroy(),a[e]=null)}),"field"===e?a.myobjectbyfield=new l(t.options):a.myobjectdatasource=new p(_.extend(t.options,{scopeType:e})),t.scopeType=e,a.range.clearAll()}),a.scope=e},initShareRange:function(){var t=this.widgets,e=t.range;(e=new i({$wrap:this.$(".range"),label:$t("请选择"),width:190,size:1,v2Size:"mini",single:!1,member:!0,group:{chooseType:"department"},usergroup:!0,role:!0,excludeItems:{role:["personnelrole"]},foldInput:!0,selectedAfterHideLabel:!0})).on("change",function(){var a=e.getSelectedItems();_.each(a,function(e,t){a[t]=_.pluck(e,"id")}),_.each(["myobjectbyfield","myobjectdatasource"],function(e){t[e]&&(t[e].setParamByKey("_shareRange",a),t[e].setParam({}))})}),t.range=e},onAdd:function(){var t=this,e=t.widgets,e=(e.myobjectdialog&&e.myobjectdialog.destroy(),e.myobjectdialog=new h({title:$t("新建共享规则"),type:"add"}));e.on("success",function(e){t.resetScope(e),FS.setAppStore("crm_observer_emp",null)}),e.show({data:{objectType:t.options.objectType},showField:!0})},showExportList:function(e){-1==["export","import"].indexOf(e)&&(e="import");var t=this;this.$el.find(".btn-export-list").hide(),this.$el.find(".myobject-header").hide(),this.$el.find(".table-wrap1").hide(),this.$el.find(".btn-back-data-list").show(),this.$el.find(".operationType").show(),this.$el.find(".table-wrap2").show(),this.widgets.exportList&&(this.widgets.exportList.stopQueryStatus=!0,this.widgets.exportList.destroy()),this.widgets.exportList=("export"==e?c:d)({$wrap:this.$el.find(".table-wrap2")}),this.widgets.operationType?this.widgets.operationType.val=e:this.widgets.operationType=FxUI.create({wrapper:".operationType > .select",template:'<fx-select v-model="val" :options="options" size="small" @change="onChange"></fx-select>',data:function(){return{val:e||"import",options:[{value:"import",label:$t("导入共享规则")},{value:"export",label:$t("导出共享规则")}]}},methods:{onChange:function(e){t.widgets.exportList&&(t.widgets.exportList.stopQueryStatus=!0,t.widgets.exportList.destroy()),t.widgets.exportList=("export"==e?c:d)({$wrap:t.$el.find(".table-wrap2")})}}})},hideExportList:function(e){this.$el.find(".btn-export-list").show(),this.$el.find(".myobject-header").show(),this.$el.find(".table-wrap1").show(),this.$el.find(".btn-back-data-list").hide(),this.$el.find(".operationType").hide(),this.$el.find(".table-wrap2").hide(),this.widgets.exportList.stopQueryStatus=!0,this.widgets.exportList.destroy(),this.widgets.exportList=null},onExport:function(){var t=this,e={},a=1;"field"===this.scopeType?(e=this.widgets.myobjectbyfield.tableParams,a=3):(e=this.widgets.myobjectdatasource.tableParams,"datasource"===this.scopeType?a=1:"datasourcedep"===this.scopeType&&(a=2)),s.FHHApi({url:"/EM1HPAASBATCH/task/exportDataShareTask/create",data:{exportDataShareTaskConfig:{argType:a,arg:e,fileType:1}},success:function(e){0==e.Result.StatusCode&&e.Value&&FxUI.create({template:'<fx-dialog  :visible.sync="show" size="small" max-height="400px" :append-to-body="true" title="'+$t("导出数据共享规则")+'" ><div v-if="!progressStop"><p style="text-align:center;padding: 20px 0 6px;">{{$t("正在收集数据")}}...</p><br><fx-progress :percentage="percentage" color="#4D8CE6" :stroke-width="10" :show-text="false" style="margin-bottom: 44px;"></fx-progress></div><div v-else><p style="padding: 20px 0 30px;">'+$t("您可以在{{str}}查看导出进度。",{data:{str:'<a href="javascript:;" @click="showExportList">'+$t("已导出规则列表")+"</a>"}})+"</p></div></fx-dialog>",data:function(){return{show:!1,percentage:0,progressStop:!0}},watch:{show:function(e){e||clearInterval(this.timer)}},methods:{showExportList:function(){this.show=!1,this.$nextTick(function(){t.showExportList("export")})}},mounted:function(){this.show=!0}})},fail:function(e){e.Result.FailureMessage&&s.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},onImport:function(){var e,t=this,a="javascript:;",i=(t.downloadDataShareTemplate&&(e=t.downloadDataShareTemplate[1],a=FS.util.getFscLink(e.filePath.split(".")[0],e.fileName,!0)),FxUI.create({template:n().replace("%filename%","{{filename}}").replace("%note%",$t("导入数据上限为{{n}}条",{data:{n:100}})).replace("%progressResult%",$t("您可以在{{str}}中查看导出进度",{data:{str:'<a href="javascript:;" @click="viewResult">'+$t("导入导出结果")+"</a>"}})),data:function(){return{show:!0,dataSource:"1",dataSourceOptions:[{value:"1",label:$t("基于数据负责人")},{value:"2",label:$t("基于数据归属部门")}],downloadUrl:a,hasFile:!1,filename:"",filepath:"",beforeImport:!0,percentage:0,disabled:!0}},watch:{show:function(e){e||(t.h5Uploader=null)}},methods:{initUpload:function(){t.h5Uploader||(t.h5Uploader=new u({multiple:!1,accept:".xlsx,.xls",autoPrependPath:!1,fileInput:this.$refs.fileInput,dragDrop:this.$refs.uploadBox,url:FS.BASE_PATH+"/FSC/EM/File/UploadByStream",timeout:180,onSelect:function(e){t.h5Uploader.startUpload(),i.uploadLoading=FxUI.Loading.service({target:i.$refs.uploadBox})},onSuccess:function(e,t){i.filename=e.name,i.hasFile=!0,i.filepath=JSON.parse(t).TempFileName,i.disabled=!1},onFailure:function(e){FxUI.MessageBox.alert($t("上传文件失败"),{type:"error"})},onComplete:function(){i.uploadLoading&&i.uploadLoading.close(),t.h5Uploader.removeAllFile()}}))},onDataSourceChange:function(e){t.downloadDataShareTemplate?(e=t.downloadDataShareTemplate[e],this.downloadUrl=FS.util.getFscLink(e.filePath.split(".")[0],e.fileName,!0)):this.downloadUrl="javascript:;"},onClickUpload:function(){this.$refs.fileInput.click()},onStartImport:function(){var e;this.dataSource&&this.filename&&this.filepath&&(this.beforeImport=!1,e=setInterval(function(){100<=i.percentage?clearInterval(e):(i.percentage+=.3,100<i.percentage&&(i.percentage=100))},150),FS.util.FHHApi({url:"/EM1HPAASBATCH/task/importDataShareTask/create",data:{importDataShareTaskConfig:{argType:this.dataSource,fileName:this.filename,file_path:this.filepath,fileType:1}},success:function(e){e.Result.StatusCode},fail:function(){},complete:function(){clearInterval(e),e=setInterval(function(){100<=i.percentage?clearInterval(e):(i.percentage+=.3,100<i.percentage&&(i.percentage=100))},10)}}))},onCancel:function(){this.show=!1,this.reset()},reset:function(){this.hasFile=!1,this.filename="",this.filepath="",this.beforeImport=!0,this.percentage=0,this.disabled=!0},viewResult:function(){this.show=!1,this.reset(),this.$nextTick(function(){t.showExportList("import")})}},mounted:function(){this.initUpload()}}))},findTaskById:function(t,a){var i=this;i.findTaskByIdCount=i.findTaskByIdCount||0,i.findTaskByIdCount++,10<=i.findTaskByIdCount?a.progressStop=!0:s.FHHApi({url:"/EM1HPAASBATCH/task/findTaskById",data:{taskId:t},success:function(e){a.show&&0==e.Result.StatusCode&&("Finished"===e.Value.data.taskStatus?(a.percentage=100,a.progressStop=!0):setTimeout(function(){i.findTaskById(t,a)},1e3))},fail:function(e){e.Result.FailureMessage&&s.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},getDownloadDataShareTemplate:function(e){var t=this;s.FHHApi({url:"/EM1HPAASBATCH/task/importDataShareTask/downloadDataShareTemplate/0",data:{},success:function(e){0==e.Result.StatusCode&&(t.downloadDataShareTemplate={},(e.Value.result||[]).forEach(function(e){t.downloadDataShareTemplate[e.argType]=e}))},fail:function(e){}},{errorAlertModel:0})},resetScope:function(e){this.widgets.scope.setValue(e),this.widgets.scope.trigger("change",e),this.scopeType=e},destroy:function(){this.undelegateEvents(),_.each(this.widgets,function(e){e&&(e.stopQueryStatus=!0,e.destroy)&&e.destroy()}),this.widgets={}}})});
define("crm-setting/datapermissions/datashare2/myobject/myobjectbydatasource",["../../common2/basetable","./myobjectdialog","../../common2/config"],function(e,t,r){var u,i=CRM.util,a=e("../../common2/basetable"),s=e("./myobjectdialog"),o=e("../../common2/config");return a.extend({config:"myObjectByDatasource",_parseColumns:function(e){var t=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,r=_.findWhere(e,{data:"objectDescribeDisplayName"});return-1==this.opts.objectType||-2==this.opts.objectType?(r.options=_.map(t,function(e){return{ItemName:e.ObjectDescribeDisplayName,ItemCode:e.ObjectDescribeApiName}}),r.isFilter=!0):r.isFilter=!1,e},parseTableParam:function(e){var r,t=this,a=_.extend(e,{outReceive:!1,basedType:{datasource:0,datasourcedep:1}[t.options.scopeType||"datasource"],permissionType:null,status:null,sources:null,entices:null,receives:null}),t=(t.opts.objectType&&-2!=t.opts.objectType&&(a.entices=[t.opts.objectType]),a._shareRange);return t&&(a.receives={0:t.member||[],1:t.usergroup||[],2:t.group||[],4:t.role||[]}),e.QueryInfo&&e.QueryInfo.Conditions&&0<e.QueryInfo.Conditions.length&&_.each(e.QueryInfo.Conditions,function(e){var t=e.FilterValue;a.receives=a.receives||{},"dataSourceName"===(r=e.FieldName)?(r="sources",t=i.parseJson(t),t={0:_.pluck(t.member,"id"),1:_.pluck(t.usergroup,"id"),2:_.pluck(t.group,"id"),4:_.pluck(t.role,"id")}):"targetName"===r?(r="receives",t=i.parseJson(t),t={0:_.union(_.pluck(t.member,"id"),a.receives[0]||[]),1:_.union(_.pluck(t.usergroup,"id"),a.receives[1]||[]),2:_.union(_.pluck(t.group,"id"),a.receives[2]||[]),4:_.union(_.pluck(t.role,"id"),a.receives[4]||[])}):"objectDescribeDisplayName"===r&&(r="entices"),a[r]=t}),_.omit(a,"QueryInfo","objectDescribeDisplayName","_shareRange","Keyword","status","_isfilter")},setParamByKey:function(e,t){this.widgets.dt.setParamByKey(e,t)},formatTableData:function(e){var c=this;return u=o[this.config](),e.forEach(function(t){var a="",s=[],i=(t.shareIds.forEach(function(e){var e=e.split("_"),t=(s.push({id:e[1],type:u.typeMap[e[0]]}),c.getNameById(e[1],u.typeMap[e[0]])),r=u.typePrefixMap[e[0]],r=(r=2==e[0]?"c"==e[2]?r.split("|")[1]:r.split("|")[0]:r)&&$t(r);a+=","+r+t}),t.dataSourceName=a.substr(1),t.dataSourceObj=s,""),o=[],r=(t.receiveIds.forEach(function(e){var e=e.split("_"),t=(o.push({id:e[1],type:u.typeMap[e[0]]}),c.getNameById(e[1],u.typeMap[e[0]])),r=u.typePrefixMap[e[0]],r=(r=2==e[0]?"c"==e[2]?r.split("|")[1]:r.split("|")[0]:r)&&$t(r);i+=","+r+t}),t.targetName=i.substr(1),t.targetObj=o,""),n="";Object.keys(t.entityMap).forEach(function(e){r+=","+e,n+=","+t.entityMap[e]}),t.objectDescribeApiName=r.substr(1),t.objectDescribeDisplayName=n.substr(1)}),e},onEnable:function(e,t){var r=this,a=r.widgets.dt.getCheckedData();if(a&&0===a.length)return i.remind(2,$t("请勾选需要移除的员工")),!1;t=t?[t.groupId]:_.map(a,function(e){return e.groupId}),a=$(e.target||e);i.FHHApi({url:u.enableUrl,data:{sharedRuleGroupIds:t,status:a.text().replace(" ","")===$t("启用")?1:0},success:function(e){i.remind(1,$t("操作成功")),r.widgets.dt.setParam({},!0)},fail:function(e){i.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},onDel:function(e){var t,r=this,a=r.widgets.dt.getCheckedData();if(a){if(_.some(a,function(e){return 1===e.Status}))return i.remind(3,$t("不能删除启用中的共享规则")),!1;t=_.map(a,function(e){return e.groupId})}else t=[e.groupId];var s=i.confirm($t("确认删除数据"),$t("提示"),function(){i.FHHApi({url:u.delUrl,data:{sharedRuleGroupIds:t},success:function(e){i.remind(1,$t("操作成功")),r.widgets.dt.setParam({},!0)},fail:function(e){i.alert(e.Result.FailureMessage)},complete:function(){s.destroy()}},{errorAlertModel:1})})},onEdit:function(e,t){var r=this,a=r.widgets,t=2==t.attr("data-type");a.myobjectdialog&&a.myobjectdialog.destroy(),a.myobjectdialog=new s({title:t?$t("查看共享规则"):$t("编辑共享规则"),type:"edit",disabled:t,scopeType:r.options.scopeType||"datasource"}),a.myobjectdialog.on("success",function(e){r.refresh()}),a.myobjectdialog.show({data:e,showField:"BpmInstance"!=e.objectDescribeApiName&&"ApprovalInstanceObj"!=e.objectDescribeApiName,datasource:e.dataSourceObj,target:e.targetObj})}})});
define("crm-setting/datapermissions/datashare2/myobject/myobjectbyfield",["crm-modules/common/util","../../common2/basetable","./myobjectdialog","../../common2/config"],function(e,t,i){var r,o=e("crm-modules/common/util"),s=e("../../common2/basetable"),n=e("./myobjectdialog"),a=e("../../common2/config");return s.extend({config:"myObjectByField",_parseColumns:function(e){var t=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,i=_.findWhere(e,{data:"displayName"});return-1==this.opts.objectType||-2==this.opts.objectType?(i.options=_.map(t,function(e){return{ItemName:_.escape(e.ObjectDescribeDisplayName),ItemCode:e.ObjectDescribeApiName}}),i.isFilter=!0):i.isFilter=!1,e},parseTableParam:function(e){var s,a=_.extend({outReceive:!1,describeApiName:e.objectType||this.opts.objectType||-1},e),t=a._shareRange;return t&&(a.receivesWithType={0:t.member||[],1:t.usergroup||[],2:t.group||[],4:t.role||[]}),e.QueryInfo&&e.QueryInfo.Conditions&&0<e.QueryInfo.Conditions.length?_.each(e.QueryInfo.Conditions,function(e){var t=e.FilterValue,i=a.receivesWithType||{};"receiveIds"===(s=e.FieldName)?(s="receivesWithType",t=CRM.util.parseJson(t),t={0:_.union(_.pluck(t.member,"id"),i[0]||[]),1:_.union(_.pluck(t.usergroup,"id"),i[1]||[]),2:_.union(_.pluck(t.group,"id"),i[2]||[]),4:_.union(_.pluck(t.role,"id"),i[4]||[])}):"displayName"===s&&(s="entices"),a[s]=t}):a=_.extend(a,{permission:-1,status:-1,ruleName:"",entices:void 0}),_.omit(a,"QueryInfo","objectType","_shareRange","_shareRangeName","Keyword","Status","PermissionType")},setParam:function(e){this.widgets.dt.setParam(e||{},!0)},setParamByKey:function(e,t){this.widgets.dt.setParamByKey(e,t)},formatTableData:function(e){return this.myObjectDataPermissions=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,r=a[this.config](),_.map(e,function(e){return this.formatTableColumnData(e)},this)},formatTableColumnData:function(e){e.permission=e.receives[0].permission,e.displayName=(_.findWhere(this.myObjectDataPermissions,{ObjectDescribeApiName:e.entityId})||{}).ObjectDescribeDisplayName||"--";var t=[];return _.each(e.receives,function(e){t.push(this.getNameById(e.receiveId,r.typeMap[e.receiveType]))},this),e.receiveIds=t.join($t("、")),e},onEnable:function(e,i){var t,s=this.widgets.dt,a=s.getCheckedData(),e=e.target||e;if(a)t=_.map(a,function(e){return e.ruleCode});else{if(2==i.status)return void o.alert($t("启用操作成功")+","+$t("正在处理数据")+","+$t("生效后会发送CRM通知给操作人"));t=[i.ruleCode]}o.FHHApi({url:r.enableUrl,data:{ruleCodes:t,status:$(e).hasClass("stop")?0:1},success:function(e){var t=a||0!=i.status?$t("操作成功"):$t("启用操作成功")+","+$t("正在处理数据")+","+$t("生效后会发送CRM通知给操作人");o.remind(1,t),s.setParam({},!0)}})},onDel:function(e){var t,i=this.widgets.dt,s=i.getCheckedData();if(s){if(_.some(s,function(e){return 1===e.status}))return o.remind(3,$t("不能删除启用中的共享规则")),!1;t=_.map(s,function(e){return e.ruleCode})}else t=[e.ruleCode];var a=o.confirm($t("确认删除数据"),$t("提示"),function(){o.FHHApi({url:r.delUrl,data:{ruleCodes:t},success:function(e){0===e.Result.StatusCode&&e.Value.success&&(o.remind(1,$t("操作成功")),i.setParam({},!0))},complete:function(){a.destroy()}})})},onEdit:function(e,t){var i=this,s=i.widgets,a=s.myobject_dialog,t=2==t.attr("data-type");e=i.formatTableColumnData(e),a&&a.destroy(),(a=new n({title:t?$t("查看共享规则"):$t("编辑共享规则"),type:"edit",scopeType:"field",disabled:t})).on("success",function(){i.refresh()}),a.show({data:e,datasource:[{}],showField:!0,target:_.map(e.receives,function(e){return{id:e.receiveId,type:r.typeMap[e.receiveType]}})}),s.myobject_dialog=a}})});
function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray(e,t)||_nonIterableRest()}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var r;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(r="Object"===(r={}.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:r)||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,i=Array(t);r<t;r++)i[r]=e[r];return i}function _iterableToArrayLimit(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var i,a,s,n,o=[],d=!0,l=!1;try{if(s=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;d=!1}else for(;!(d=(i=s.call(r)).done)&&(o.push(i.value),o.length!==t);d=!0);}catch(e){l=!0,a=e}finally{try{if(!d&&null!=r.return&&(n=r.return(),Object(n)!==n))return}finally{if(l)throw a}}return o}}function _arrayWithHoles(e){if(Array.isArray(e))return e}define("crm-setting/datapermissions/datashare2/myobject/myobjectdialog",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","crm-modules/common/fieldfilter/fieldfilter","../template/add-dialog-html","../../common2/config"],function(e,t,r){var h=e("crm-modules/common/util"),i=e("crm-widget/dialog/dialog"),g=e("crm-widget/selector/selector"),s=e("crm-widget/select/select"),n=e("crm-modules/common/fieldfilter/fieldfilter"),a=e("../template/add-dialog-html"),f=e("../../common2/config"),y=999999,o=i.extend({config:"myObjectByField",attrs:{title:$t("共享规则"),content:'<div class="crm-loading"></div>',className:"crm-s-datapermissions crm-s-datapermissions2",showBtns:!0,showScroll:!0,size:"md",type:"add",data:{}},events:{"click .update-tip .title":"onToggleWarn","click .j-toggle":"onToggleCondition","focus .rulename-ipt":"onRemoveErrmsg","click .b-g-btn":"onSave","click .add-group-btn":"addFieldFilter","click .j-del-group":"delFilterGroup","click .b-g-btn-cancel":"destroy","click .j-permission":"onTogglePerMission"},render:function(){return this.widgets={},this.myObjectDataPermissions=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,this.userGroups=JSON.parse(sessionStorage.getItem("userGroups")),o.superclass.render.call(this)},show:function(e){this.widgets={},this.set(e);var t=o.superclass.show.call(this),r="edit"===this.get("type"),i="datasource"===this.get("scopeType")||"datasourcedep"==this.get("scopeType");return this.setContent(a({isEdit:r,isDatasource:i,isDep:"datasourcedep"==this.get("scopeType"),data:this.get("data"),showField:this.get("showField")&&"BpmInstance"!=e.data.objectType&&"ApprovalInstanceObj"!=e.data.objectType,isSysObj:!1,disabled:this.get("disabled")})),this.$sharedata=this.$(".j-sharedata"),this.$rulename=this.$(".rulename-ipt"),this.$shareobj=this.$(".j-shareobj"),this.$fieldfilter=this.$(".j-fieldfilter"),this.$conditionGroups=this.$(".condition-groups"),this.initSelectBar("datasource",this.get("disabled")),this.initSelectBar("target",this.get("disabled")),"add"==this.get("type")?(this.initShareData(r&&i),this.initShareObj(r)):"field"==this.get("scopeType")?this.initShareObj(r):this.initShareData(r&&i),this.resizedialog(),t},curType:"datasource",initSelectBar:function(e,t,r){var i,a,s,n,o=this,d=o.get("data"),l=("datasourcedep"===o.get("scopeType")||"datasourcedep"==o.curType)&&"target"!=e,u="datasource"===o.get("scopeType")||"datasourcedep"===o.get("scopeType")||"datasourcedep"==o.curType,p=o.get(e),p="target"===e&&p&&"usergroup"===p[0].type?_.findWhere(o.userGroups,{id:p[0].id,type:0})?p:[]:p||[],c=0,u=("target"===e&&(c=1,"edit"==this.get("type"))&&(u?i=d.receiveDeptCascade:d.receives[0]&&(i=d.receives[0].receiveCascade),c=1==i?2:1),new g({$wrap:o.$(".j-"+e),zIndex:o.get("zIndex"),group:{company:"datasource"===e||l,chooseType:"department"},member:!l,usergroup:!l,role:!l,excludeItems:{role:["personnelrole"]},groupIncludeChildrenStatus:c,single:!1,label:l?$t("请选择数据归属部门"):$t("选择员工部门用户组或角色"),defaultSelectedItems:(n={member:[],group:[],usergroup:[],role:[]},_.each(p,function(e){a=e.id,"member"==(s=e.type)?n.member.push(a):"group"==s?n.group.push(a):"usergroup"===s?n.usergroup.push(a):"role"===s&&n.role.push(a)}),n)}));t&&u.lock(),u.on("addItem",function(){this.options.$wrap.next().hide(),o.resizedialog()}),o.widgets[e]=u},initShareData:function(e){var t=this,r=t.get("data"),i=-1===(i=r.objectType||r.objectDescribeApiName)?"":i,a=t.myObjectDataPermissions.reduce(function(e,t){return e.push({name:t.ObjectDescribeDisplayName,value:t.ObjectDescribeApiName}),e},[]),r=(r.objectType&&"-2"==r.objectType&&(a=_.filter(a,function(e){return e.value})),new s({$wrap:t.$sharedata,zIndex:t.get("zIndex"),width:500,multiple:"multiple",allCheck:!0,options:a,disabled:this.get("disabled"),stopPropagation:!0,defaultValue:i.split(",")}));r.on("change",function(){this.options.$wrap.next().hide(),t.resizedialog()}),t.widgets.sharedata=r},initShareObj:function(e){var t=this,r=t.get("data"),i=t.get("data").entityId,a=t.myObjectDataPermissions.map(function(e){return{name:e.ObjectDescribeDisplayName,value:e.ObjectDescribeApiName}}).filter(function(e){return-1==["SalesOrderProductObj","ReturnedGoodsInvoiceProductObj","GoalValueObj"].indexOf(e.value)}),a=_.filter(a,function(e){return"BpmInstance"!=e.value&&"ApprovalInstanceObj"!=e.value}),r=(r.objectType&&"-2"==r.objectType&&(a=_.filter(a,function(e){return e.value})),new s({$wrap:t.$shareobj,zIndex:t.get("zIndex"),multiple:"single",options:[{name:$t("请选择"),value:""}].concat(a),defaultValue:i,disabled:this.get("disabled")}));r.on("change",function(e){this.options.$wrap.next().hide(),t.renderFieldfilter({apiname:e}),t.resizedialog()}),e&&t.renderFieldfilter({apiname:i,isEdit:e}),t.widgets.shareobj=r},renderFieldfilter:function(t){var i,a,r=this,e=t.isEdit;delete t.isEdit,(r.widgets.filterGroup||[]).forEach(function(e){e.destroy(),r.$conditionGroups.html(""),r.$fieldfilter._hasAddBtn=0,r.$fieldfilter.find(".add-group-btn").remove()}),r.widgets.filterGroup=[],e?(e=r.get("data").ruleParse||"",i=r.get("data").rules||[],e=e.replace(/[() ]/g,"").split("or"),a=[],e.forEach(function(e){var r=[];e.split("and").forEach(function(t){i.forEach(function(e){"partner_id.name"==e.fieldName&&(e.fieldName="partner_id",e.fieldValue=[e.fieldValue.join(";")]),e.ruleOrder==t&&r.push(e)})}),a.push(r)}),a.forEach(function(e){r.widgets.filterGroup.push(r.createFieldfilter(t,e))})):r.widgets.filterGroup.push(r.createFieldfilter(t))},createFieldfilter:function(a,e){var t=this,r=Date.now()+"-"+Math.random(),i=$('<div class="filter-item filter-item-'+r+'"></div>'),o=["employee","employee_many","department","department_many"],e=new n(_.extend(a,{uuid:r,$wrapper:i,width:636,title:'<span class="el-icon-remove j-del-group" data-uuid='+r+"></span>"+$t("且（AND）"),max:10,filterType:f.filterType,filterApiname:f.filterApiname,openQuoteField:!0,openPartnerIdField:!0,defaultValue:e&&_.map(e,function(e){return[e.fieldName,e.operate,e.fieldValue]}),parseCompare:function(e,t,r){return _.contains(o,t)||"dimension"==t?_.map([13,14,9,10],function(e){return r[e-1]}):e},_getCompareOptions:function(r){var i=this,e=this.opts.helper.getCompare(r.type),e=i.opts.parseCompare(e,r&&r.type,i.opts.helper.getCompareConfig()),a=this.opts.helper.getType(r.type,i.opts.isRelate),s=(_.contains(["object_reference","object_reference_many"],r.type)&&"partner_id"==r.api_name&&(a="text"),i.isEmpField(r.api_name)&&(a="employee"),CRM.config.objDes[i.opts.apiname.toLowerCase()]&&"select_many"==a&&(e=_.filter(e,function(e){return!_.contains([7,8,13,14],e.value)})),0),n=!1;return"data_own_department"==r.api_name&&(s=1,n=!0,this.opts.defaultValue)&&this.opts.defaultValue.forEach(function(e){"data_own_department"==e[0]&&(s=Array.isArray(e[2])&&/_y$/g.test(e[2][0])?(e[2]=e[2].map(function(e){return e.replace("_y","")}),2):1)}),_.map(e,function(e){var t={type:i._getChildType(e,a,r.type),ftype:r.type,fname:r.api_name,options:i._getValueOptions(r),isReInit:_.contains(o,a),relatedName:r.target_related_list_name||"",targetApiName:r.target_api_name,dimension_type:r.dimension_type||"",groupIncludeChildrenStatus:s,groupIncludeChildrenStatusCascade:n};return(_.contains(o,a)||"dimension"==a)&&_.contains([7,8,13,14],e.value)&&(t.isMultiple=!0),_.contains(["object_reference","object_reference_many"],r.type)&&"partner_id"==r.api_name&&_.contains(["IN","NIN"],e.value1)&&(t.useMInput=!0),{label:"date_time"==r.type&&e.dname||e.name,value:e.value,child:t}})},parseFields:function(e){var t,r=CRM.util.deepClone(e);for(t in r){var i=r[t];("formula"==i.type&&_.contains(["date_time","date","time"],i.return_type)||"formula"==i.type&&!i.is_index||"quote"==i.type&&!1===i.is_index)&&delete r[t],"quote"==i.type&&-1<["employee","employee_many","department","department_many"].indexOf(i.quote_field_type)&&delete r[t],"formula"===i.type&&(i.type=i.return_type||i.type),"select_one"===i.type&&i.cascade_parent_api_name&&delete i.cascade_parent_api_name,-1!=f.filterType.indexOf(i.quote_field_type)&&delete r[t],-1!=f.filterApiname.indexOf(i.api_name)&&delete r[t],"NewOpportunityObj"!==a.apiname&&"sales_process_id"===i.api_name&&delete r[t]}return r},formatFields:function(e){for(var t in e){t=e[t];t.type=t.quote_field_type||t.type}return e},disabled:t.get("disabled")}));return e.on("render",function(){var e;t.resizedialog(),t.$conditionGroups.find(".crm-loading").remove(),0==t.$conditionGroups.find(".filter-item-"+r).length&&((e=$('<div class="group-item"><span class="fm-error crm-ico-error" style="display:none">'+$t("请完善筛选条件")+'</span><div class="or-line"><span>'+$t("或",{},"或")+"</span></div></div>")).prepend(i),t.$conditionGroups.append(e)),t.$fieldfilter._hasAddBtn||(t.$fieldfilter._hasAddBtn=1,t.$fieldfilter.append('<span class="add-group-btn el-icon-circle-plus">'+$t("添加条件")+"</span>")),t.get("disabled")&&t.$fieldfilter.append('<div class="fieldfilter-mask"></div>')}),e.on("change",function(){this.opts.$wrapper.next().hide()}),e.on("add.item",function(){t.resizedialog()}),e.on("del.item",function(){t.resizedialog()}),e},addFieldFilter:function(){var e=this.widgets.shareobj.getValue();this.widgets.filterGroup.push(this.createFieldfilter({apiname:e}))},delFilterGroup:function(e){if(1==this.widgets.filterGroup.length)this.widgets.filterGroup[0].reset();else for(var t=$(e.target).data("uuid"),r=0;r<this.widgets.filterGroup.length;r++){var i=this.widgets.filterGroup[r];if(i.opts.uuid==t){i.destroy(),i.opts.$wrapper.closest(".group-item").remove(),this.widgets.filterGroup.splice(r,1);break}}},onToggleWarn:function(e){$(e.currentTarget).closest(".update-tip").toggleClass("update-tip-hover"),this.resizedialog()},onToggleCondition:function(e){if($(e.currentTarget).hasClass("disabled-selected"))return e.stopPropagation(),!1;e=$(e.target).data("base"),e="datasourcedep"==(this.curType=e)?"datasource":e;this.$(".j-basefield").hide(),this.$(".j-basedatasource").hide(),this.$(".j-base"+e).show(),"datasource"==e&&"edit"!==this.get("type")&&(this.widgets.datasource&&this.widgets.datasource.destroy(),this.widgets.datasource=null,this.initSelectBar("datasource")),this.widgets.target&&this.widgets.target.destroy(),this.widgets.target=null,this.initSelectBar("target",this.get("disabled"),e),this.resizedialog()},onTogglePerMission:function(e){if($(e.currentTarget).hasClass("disabled-selected"))return e.stopPropagation(),!1},onRemoveErrmsg:function(e){$(e.currentTarget).next().hide()},onSave:function(e){var t,r,i,a,s,n,o,d,l,u,p=this,e=$(e.currentTarget),c=p.get("data")||{},g=p.widgets.target.getValue(),m=p.widgets.target.getIncludeGroupChildrenStatus()?1:0;return g.member.length+g.group.length+g.usergroup.length+g.role.length==0?(p.$(".j-target").next().show(),!1):(o="datasource"===(t=this.$(".j-toggle.mn-selected").data("base"))||"datasourcedep"===t,r=p.$(".j-permission.mn-selected").data("permissiontype"),o?(o=p.widgets.datasource.getValue(),i=p.widgets.sharedata.getValue(),(o=_.extend({member:[],group:[],usergroup:[],role:[]},o)).member.length+o.group.length+o.usergroup.length+o.role.length==0?(p.$(".j-datasource").next().show(),!1):0===i.length?(p.$sharedata.next().show(),!1):_.contains(g.group,y)&&_.contains(o.group,y)?(h.remind(3,$t("数据来源和数据共享不能同时为")+h.getCircleById(y).name),!1):void h.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/addOrUpdateShareRuleGroups",data:{groupId:c.groupId||null,describeApiNames:i,sourceEmployeeIds:o.member,sourceDeptIds:o.group,sourceUserGroupIds:o.usergroup,sourceRoleIds:o.role,targetEmployeeIds:g.member,targetDeptIds:g.group,targetUserGroupIds:g.usergroup,targetRoleIds:g.role,permissionType:r,receiveDeptCascade:m,basedType:{datasource:0,datasourcedep:1}[t]},success:function(e){0===e.Result.StatusCode&&(p.trigger("success",t),h.remind(1,$t("操作成功！")),p.hide())}},{submitSelector:e})):(c=p.widgets.shareobj.getValue(),i=$.trim(p.$rulename.val()),a=[],s=[],i?h.isContainEmojiCharacter(i)?(p.$rulename.next().text($t("规则名称不能包含emoji表情符")).show(),!1):c?(n=!1,(o=p.widgets.filterGroup).forEach(function(e){if(e.isExitNull())return e.opts.$wrapper.next().show(),!(n=!0)}),void(n||(d=[],l=0,o.forEach(function(e){var a=[];_.each(e.getData(),function(e){var t=_slicedToArray(e,3),r=t[0],i=t[1],t=t[2];l++,s.push({fieldName:"partner_id"==r?r+".name":r,fieldType:e[3],fieldValue:"partner_id"==r&&_.contains(["IN","NIN"],i)?t.split(";"):_.contains(["IS","ISN"],i)?[""]:_.isArray(t)?t:[t],operate:e[1],ruleOrder:l}),a.push(l)}),d.push("("+a.join(" and ")+")")}),u=f[this.config]().typeMap,_.each(g,function(e,t){a=a.concat(_.map(e,function(e){return{permission:r,receiveId:e,receiveType:_.invert(u)[t],receiveCascade:m}}))}),h.FHHApi({url:"add"===p.get("type")?"/EM1HNCRM/API/v1/object/data_privilege/service/addFieldShare":"/EM1HNCRM/API/v1/object/data_privilege/service/updateFieldShare",data:{describeApiName:c,receives:a,ruleCode:p.get("data").ruleCode||null,ruleName:i,ruleParse:d.join(" or "),rules:s},success:function(e){0===e.Result.StatusCode&&(p.trigger("success",t),h.remind(1,$t("操作成功！")),p.hide())}},{submitSelector:e})))):(p.$shareobj.next().show(),!1):(p.$rulename.next().text($t("请输入规则名称")).show(),!1)))},hide:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,o.superclass.hide.call(this)},destroy:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,o.superclass.destroy.call(this)}});r.exports=o});
define("crm-setting/datapermissions/datashare2/template/add-dialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            if (isEdit) {
                __p += ' <div class="crm-g-form datashare-dialog"> <div class="update-tip update-tip-hover"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="title">' + ((__t = $t("基于条件警告")) == null ? "" : __t) + '</div> <div class="text">' + ((__t = $t("crm.基于条件的共享规则")) == null ? "" : __t) + "<br/ > ";
                if (isSysObj) {
                    __p += (__t = $t("crm.不生效的场景")) == null ? "" : __t;
                }
                __p += ' </div> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据范围")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box" style="width:430px;"> <span class="radio-item"> <span class="j-toggle mn-radio-item disabled-selected ';
                if (isDatasource && !isDep) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasource"></span> <span class="radio-lb">' + ((__t = $t("基于数据负责人")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item disabled-selected ';
                if (isDep) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-base="datasourcedep"></span> <span class="radio-lb">' + ((__t = $t("基于数据归属部门")) == null ? "" : __t) + "</span> </span> ";
                if (showField) {
                    __p += ' <span class="radio-item"> <span class="j-toggle mn-radio-item disabled-selected ';
                    if (!isDatasource) {
                        __p += (__t = " mn-selected") == null ? "" : __t;
                    }
                    __p += '" data-base="field"></span> <span class="radio-lb guide-mark">' + ((__t = $t("基于条件")) == null ? "" : __t) + "</span> </span> ";
                }
                __p += " </div> </div> ";
                if (isDatasource) {
                    __p += ' <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据来源于")) == null ? "" : __t) + '</label> <div class="fm-wrap disabled-wraper"> <div class="j-datasource"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> <div class="disabled-layout"></div> </div> </div> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的数据")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-sharedata"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的数据！")) == null ? "" : __t) + "</span> </div> </div> ";
                } else {
                    __p += ' <div class="j-basefield"> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("规则名称")) == null ? "" : __t) + '</label> <input class="b-g-ipt fm-ipt rulename-ipt ' + ((__t = disabled ? "b-g-ipt-disabled" : "") == null ? "" : __t) + '" ' + ((__t = disabled ? disabled = "disabled" : "") == null ? "" : __t) + ' maxlength="20" value="' + ((__t = data.ruleName) == null ? "" : __t) + '" placeholder="' + ((__t = $t("最多20个中文字符")) == null ? "" : __t) + '"> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请输入规则名称!")) == null ? "" : __t) + '</span> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的对象")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-shareobj"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的对象")) == null ? "" : __t) + '</span> </div> </div> <div class="j-fieldfilter" style="position: relative;z-index:100"> <div class="condition-groups"><div class="crm-loading"></div></div> </div> <!-- <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请完善筛选条件")) == null ? "" : __t) + "</span> --> </div> ";
                }
                __p += ' <div class="fm-item dash-line"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据共享到")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-target"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享后的权限")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + " ";
                if ((data.permission || data.permissionType) === 1) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-permissiontype="1"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span></span> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + " ";
                if ((data.permission || data.permissionType) === 2) {
                    __p += (__t = " mn-selected") == null ? "" : __t;
                }
                __p += '" data-permissiontype="2"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + "</span></span> </div> </div> </div> ";
            } else {
                __p += ' <div class="crm-g-form datashare-dialog advancedatashare-dialog"> <div class="update-tip update-tip-hover"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="title">' + ((__t = $t("基于条件警告")) == null ? "" : __t) + '</div> <div class="text">' + ((__t = $t("crm.基于条件的共享规则")) == null ? "" : __t) + "<br/ > ";
                if (isSysObj) {
                    __p += (__t = $t("crm.不生效的场景")) == null ? "" : __t;
                }
                __p += ' </div> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据范围")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"> <span class="j-toggle mn-radio-item mn-selected" data-base="datasource"></span> <span class="radio-lb">' + ((__t = $t("基于数据负责人")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="j-toggle mn-radio-item" data-base="datasourcedep"></span> <span class="radio-lb">' + ((__t = $t("基于数据归属部门")) == null ? "" : __t) + "</span> </span> ";
                if (showField) {
                    __p += ' <span class="radio-item"> <span class="j-toggle mn-radio-item" data-base="field"></span> <span class="radio-lb guide-mark">' + ((__t = $t("基于条件")) == null ? "" : __t) + "</span> </span> ";
                }
                __p += ' </div> </div> <div class="j-basedatasource"> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据来源于")) == null ? "" : __t) + '<div class="qus-box crm-ico-qus"><div class="qus-box-item"><em class="sanjiao"></em><div class="mes">' + ((__t = $t("按部门添加时支持附属部门同时所选部门包含子部门")) == null ? "" : __t) + '</div></div></div></label> <div class="fm-wrap"> <div class="j-datasource"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的数据")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-sharedata"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的数据")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="j-basefield" style="display:none"> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("规则名称")) == null ? "" : __t) + '</label> <input class="b-g-ipt fm-ipt rulename-ipt" maxlength="20" placeholder="' + ((__t = $t("最多20个中文字符")) == null ? "" : __t) + '"> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请输入规则名称!")) == null ? "" : __t) + '</span> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享的对象")) == null ? "" : __t) + ' <div class="qus-box crm-ico-qus"><div class="qus-box-item"><em class="sanjiao"></em><div class="mes">' + ((__t = $t("关于状态字段变更：客户、线索、销售订单、商机优化了原状态业务意义，变更为新的状态+生命状态，原状态字段变为【状态（原）】将在未来版本下线，为了不影响正常使用请勿配置相关条件设置，如已设置请更新为新的状态！具体每个对象的状态变化内容请参考产品手册。(涉及对象包含：线索，客户，市场活动，联系人，商机，订单，退货单，退款等)")) == null ? "" : __t) + '</div></div></div> </label> <div class="fm-wrap"> <div class="j-shareobj"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择共享的对象")) == null ? "" : __t) + '</span> </div> </div> <div class="j-fieldfilter" style="position: relative;z-index:100"> <div class="condition-groups"></div> </div> <!-- <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请完善筛选条件")) == null ? "" : __t) + '</span> --> </div> <div class="fm-item dash-line"> <label class="fm-lb"><em>*</em>' + ((__t = $t("数据共享到")) == null ? "" : __t) + '<div class="qus-box crm-ico-qus"><div class="qus-box-item"><em class="sanjiao"></em><div class="mes">' + ((__t = $t("按部门添加时支持附属部门但是所选部门不包含子部门")) == null ? "" : __t) + '</div></div></div></label> <div class="fm-wrap"> <div class="j-target"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择员工部门角色或用户组")) == null ? "" : __t) + '</span> </div> </div> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("共享后的权限")) == null ? "" : __t) + '</label> <div class="fm-wrap mn-radio-box"> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + ' mn-selected" data-permissiontype="1"></span><span class="radio-lb">' + ((__t = $t("只读")) == null ? "" : __t) + '</span></span> <span class="radio-item"><span class="j-permission mn-radio-item ' + ((__t = disabled ? "disabled-selected" : "") == null ? "" : __t) + '" data-permissiontype="2"></span><span class="radio-lb">' + ((__t = $t("读写")) == null ? "" : __t) + "</span></span> </div> </div> </div> ";
            }
        }
        return __p;
    };
});
define("crm-setting/datapermissions/datashare2/template/import-rules-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<fx-dialog class="dialog-datapermissions-datashare-import_rules" :class="[!beforeImport?\'after-import\':\'\']" :visible.sync="show" size="small" max-height="400px" title="' + ((__t = $t("导入数据共享规则")) == null ? "" : __t) + '" > <div v-show="beforeImport"> <div style="color: #545861;">' + ((__t = $t("请选择数据来源")) == null ? "" : __t) + '</div> <fx-select v-model="dataSource" :options="dataSourceOptions" filterable size="small" @change="onDataSourceChange" ></fx-select> <div style="color: #545861;">' + ((__t = $t("请添加您要导入的数据")) == null ? "" : __t) + '</div> <div><a :href="downloadUrl">' + ((__t = $t("下载数据模板")) == null ? "" : __t) + '</a></div> <div ref="uploadBox" class="uploadBox"> <div v-show="!hasFile"> <i class="icon fx-icon-upload"></i> <span >' + ((__t = $t("将文件拖到此处或")) == null ? "" : __t) + '<a href="javascript:;" @click="onClickUpload" >' + ((__t = $t("点击上传")) == null ? "" : __t) + '</a ></span ><br /> <span class="note">%note%</span> </div> <div v-show="hasFile"> <i class="icon el-icon-circle-check"></i> <span>' + ((__t = $t("文件已添加")) == null ? "" : __t) + '</span><br /> <span class="note note2">%filename%</span> &nbsp;&nbsp;<a href="javascript:;" @click="onClickUpload" >' + ((__t = $t("重新上传")) == null ? "" : __t) + '</a > </div> </div> <input type="file" ref="fileInput" accept=".xlsx,.xls" style="display: none" /> </div> <div v-show="!beforeImport"> <div v-show="percentage<100" style="text-align: center; padding: 16px 0">' + ((__t = $t("正在收集数据")) == null ? "" : __t) + '</div> <fx-progress v-show="percentage<100" :percentage="percentage" :stroke-width="10" color="#4D8CE6" :show-text="false" style="margin-bottom: 30px" ></fx-progress> <div v-show="percentage>=100" style="padding: 26px 0;">%progressResult%</div> </div> <template slot="footer"> <fx-button type="primary" :disabled="disabled" size="small" @click="onStartImport" >' + ((__t = $t("开始导入")) == null ? "" : __t) + '</fx-button > <fx-button size="small" @click="onCancel">' + ((__t = $t("取消")) == null ? "" : __t) + "</fx-button> </template> </fx-dialog>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/datashare2/template/list-view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="update-tip"> <div class="icon"> <span class="triangle"></span> <em class="">!</em> </div> <div class="content"> <div class="text"> ' + ((__t = $t("crm.基于条件的共享规则")) == null ? "" : __t) + "<br/ >" + ((__t = $t("注意：数据共享启用或关闭后，根据数据量的大小，约2个小时后会生效")) == null ? "" : __t) + ' </div> </div> <a href="javascript:;" class="btn-export-list">' + ((__t = $t("查看")) == null ? "" : __t) + "<em>" + ((__t = $t("导入导出结果")) == null ? "" : __t) + '</em></a> </div> <div class="myobject-header"> <span class="label">' + ((__t = $t("数据来源范围")) == null ? "" : __t) + '</span> <div class="scope"></div> <span class="label">' + ((__t = $t("共享范围:")) == null ? "" : __t) + '</span> <div class="range"></div> <div class="crm-btn-box"> <span class="crm-btn crm-btn-primary j-add">+' + ((__t = $t("新建共享规则")) == null ? "" : __t) + '</span> <span class="crm-btn j-import">' + ((__t = $t("导入")) == null ? "" : __t) + '</span> <span class="crm-btn j-export">' + ((__t = $t("导出")) == null ? "" : __t) + '</span> </div> </div> <a href="javascript:;" class="btn-back-data-list" style="display: none">&lt; ' + ((__t = $t("返回数据共享列表")) == null ? "" : __t) + '</a> <div class="operationType" style="display: none"> <span>' + ((__t = $t("操作类型")) == null ? "" : __t) + ': </span> <div class="select"></div> </div> <div class="table-wrap table-wrap1"></div> <div class="table-wrap table-wrap2" style="display: none"></div>';
        }
        return __p;
    };
});
define("crm-setting/datapermissions/datashare2/template/nav-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += "<ul> ";
            if (!_.isEmpty(myObjectDataPermissions)) {
                __p += ' <li data-roleid="-2" class="all" data-is_myobject="true">' + ((__t = $t("全部对象")) == null ? "" : __t) + '</li> <!-- <li data-roleid="-2" class="all" data-is_myobject="true">' + ((__t = $t("全部预置对象")) == null ? "" : __t) + '</li> --> <!-- <li data-roleid="-1" class="all" data-is_myobject="true">' + ((__t = $t("全部自定义对象")) == null ? "" : __t) + "</li> --> ";
            }
            __p += ' <div class="line"></div> ';
            _.each(myObjectDataPermissions, function(item, index) {
                __p += ' <li class="crm-ui-title" data-title="true" data-pos="right" data-roleid="' + ((__t = item.ObjectDescribeApiName) == null ? "" : __t) + '" data-is_myobject="true"> ' + ((__t = item.ObjectDescribeDisplayName) == null ? "" : __t) + " </li> ";
            });
            __p += " </ul>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/departmentdataright/index",["./myobject/myobject"],function(t,e,i){var s=CRM.util,n=t("./myobject/myobject");i.exports=Backbone.View.extend({options:{type:""},initialize:function(){var e=this;sessionStorage.getItem("crmDataPermissions")?e.render():s.getObjectDataPermissions(function(t){sessionStorage.setItem("crmDataPermissions",JSON.stringify(t)),e.render()})},render:function(){this.myobject=new n({el:this.$(".rightsection"),type:this.options.type})},show:function(){this.$el.show(),this.myobject&&this.myobject.resize()},hide:function(){this.$el.hide()},destroy:function(){_.each(["myobject"],function(t){this[t]&&(this[t].destroy(),this[t]=null)},this)}})});
define("crm-setting/datapermissions/departmentdataright/myobject/myobject",["crm-widget/selector/selector","../template/list-view-html","./myobjectbydata","./myobjectdialog"],function(e,t,a){var s=e("crm-widget/selector/selector"),i=e("../template/list-view-html"),l=e("./myobjectbydata"),n=e("./myobjectdialog");a.exports=Backbone.View.extend({initialize:function(e){this.widgets={},this.isOrganize="organize"==e.type,this.el.innerHTML=i({isOrganize:this.isOrganize}),this.initDataRange(),this.initDepartment(),this.widgets.myobjectbydata=new l({$el:this.$(".table-wrap"),isOrganize:this.isOrganize})},events:{"click .j-add":"onAdd"},resize:function(){this.widgets&&this.widgets.myobjectbydata&&this.widgets.myobjectbydata.resize()},initDepartment:function(){var e=this,t=e.widgets,i=new s({$wrap:e.$(".range"),label:$t("请选择"),width:240,size:1,v2Size:"mini",single:!1,group:{chooseType:e.isOrganize?"organization":"department"},foldInput:!0,selectedAfterHideLabel:!0,isFromManage:!0,enableScope:!0});i.on("change",function(){var a=i.getSelectedItems();Object.keys(a).forEach(function(e){var t=a[e];a[e]=t.map(function(e){return e.id})}),t.myobjectbydata.setParamByKey("_department",a),t.myobjectbydata.setParam({})}),t.department=i},initDataRange:function(){var a=this.widgets,e=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,t=[{label:$t("全部"),value:""}],e=(e.forEach(function(e){t.push({label:e.ObjectDescribeDisplayName,value:e.ObjectDescribeApiName})}),FxUI.create({wrapper:this.$(".scope")[0],template:'<fx-select v-model="value" size="mini" style="width:100%;" :options="options" multiple filterable collapse-tags clearable>\n            <template slot="options" slot-scope="slotProps">\n              <label role="checkbox" class="el-checkbox" :class="{\'is-checked\':isSelectAll}" style="margin-right: 0px;" v-if="slotProps.data.value===\'\'">\n                <span aria-checked="mixed" class="el-checkbox__input" :class="{\'is-checked\':isSelectAll}">\n                  <span class="el-checkbox__inner"></span> \n                </span>\n              </label>\n              <span>{{ slotProps.data.label }}</span>\n            </template>\n          </fx-select>',data:function(){return{value:[],options:t,allValues:t.map(function(e){return e.value}).filter(function(e){return""!==e}),isSelectAll:!1}},watch:{value:function(e,t){""===e[e.length-1]?(this.isSelectAll=!this.isSelectAll,this.isSelectAll?this.value=this.allValues:this.value=[]):(e.length==this.allValues.length?this.isSelectAll=!0:this.isSelectAll=!1,a.myobjectbydata.setParamByKey("_dataRange",this.value),a.myobjectbydata.setParam({}))}}}));a.datarange=e},onAdd:function(){var e=this,t=e.widgets,t=(t.myobjectdialog&&t.myobjectdialog.destroy(),t.myobjectdialog=new n({isOrganize:e.isOrganize,title:e.isOrganize?$t("添加组织内数据权限"):$t("添加部门内数据权限")}));t.on("success",function(){e.resetScope(),FS.setAppStore("crm_observer_emp",null)}),t.show()},resetScope:function(){this.widgets.department.clearAll(),this.widgets.myobjectbydata.setParamByKey("_department",null),this.widgets.datarange.value=[]},destroy:function(){this.undelegateEvents(),_.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets={}}})});
define("crm-setting/datapermissions/departmentdataright/myobject/myobjectbydata",["crm-modules/common/util","crm-widget/table/table","./myobjectdialog"],function(t,e,a){var r=t("crm-modules/common/util"),s=t("crm-widget/table/table"),n=t("./myobjectdialog"),i=Backbone.View.extend({config:{url:"/EM1HNCRM/API/v1/object/departmentDataPrivilege/service/query",enableUrl:"/EM1HNCRM/API/v1/object/departmentDataPrivilege/service/enable",disableUrl:"/EM1HNCRM/API/v1/object/departmentDataPrivilege/service/disable",delUrl:"/EM1HNCRM/API/v1/object/departmentDataPrivilege/service/delete",typeMap:{0:"member",2:"group",1:"usergroup",4:"role"}},delegateEvents:function(){return this.events=_.extend({},this.events,this.__publicEvents),i.__super__.delegateEvents.call(this)},__publicEvents:{"click .j-enable":"onEnable","click .j-del":"onDel"},initialize:function(t){this.widgets={},this.opts=t,this.isOrganize=t.isOrganize,this.setElement(t.$el),this.myObjectDataPermissions=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,this.render()},render:function(){_.each(this.widgets,function(t){t&&t.destroy&&t.destroy()}),this.widgets={},this.initTable()},show:function(t){this.widgets.dt.setParam(t,!0,!0)},getColumns:function(){var s=this;return[{data:"departmentNames",title:s.isOrganize?$t("组织名称"):$t("部门名称_web"),isFilter:!1,width:340,renderTips:function(t,e,a){var i=[],a=FS.contacts.getCircleById(a.deptId);return a&&a.ancestors&&a.ancestors.slice(0).forEach(function(t){t=FS.contacts.getCircleById(t);t&&i.push(t.name)}),i.push(a.name),i.join("/")},render:function(t,e,a){return 1==a.status?'<span style="color: #ccc">'+t+"</span>":t||"--"}},{data:"entityNames",title:$t("suoshuduixiang"),isFilter:!1,width:280,render:function(t,e,a){return 1==a.status?'<span style="color: #ccc">'+t+"</span>":t}},{data:"type",title:$t("可见范围"),isFilter:!1,render:function(t,e,a){var i=["本级部门及子部门数据互相可见","本级部门数据可见","本级及下级部门数据可见","本级部门数据可见（包含子部门）"][t];return s.isOrganize&&(i=["本级组织及子组织数据互相可见","本级组织数据可见","本级及下级组织数据可见","本级组织数据可见（包含子组织）"][t]),1==a.status?'<span style="color: #ccc">'+$t(i)+"</span>":$t(i)}},{data:null,lastFixed:!0,title:$t("操作"),width:170,render:function(t,e,a){return 1==a.status?'<div><a class="j-edit" data-type="1" style="margin-right: 5px;cursor:pointer;">'+$t("编辑")+'</a><a class="j-enable" style="margin-right: 5px; cursor:pointer;">'+$t("启用")+'</a><a class="j-del" style="cursor:pointer;">'+$t("删除")+"</a></div>":'<div><a class="j-edit disable00" data-type="1" style="margin-right: 5px;cursor:pointer;">'+$t("编辑")+'</a><a class="j-enable stop" style="cursor:pointer;">'+$t("停用")+"</a></div>"}}]},initTable:function(){var i=this,t=i.widgets.dt=new s({$el:i.opts.$el,url:i.config.url,requestType:"FHHApi",showFilerBtn:!0,showMultiple:!0,trHandle:!1,batchBtns:[{text:$t("停用"),className:"j-enable stop"},{text:$t("启用"),className:"j-enable"},{text:$t("删除"),className:"j-del"}],openStart:i.opts.openStart||!1,postData:{},columns:i.getColumns(),paramFormat:function(t){return i.parseTableParam(t)},formatData:function(t){return{totalCount:t.pageInfo&&t.pageInfo.total||0,data:i.formatTableData(t.result)}}});t.on("trclick",function(t,e,a){a.hasClass("j-enable")?i.onEnable(a,t):a.hasClass("j-edit")&&!a.hasClass("disable")?i.onEdit(t,a):a.hasClass("j-del")&&i.onDel(t)}),t.on("checkbox.click",function(t,e,a){i.onCheckboxClick(a)}),t.on("term.bactchHide",function(t,e,a){i.onCheckboxClick(0)})},getNameById:function(t,e){var a;return a="group"===e?(r.getCircleById(t)||{}).name||"--":a},parseTableParam:function(t){return{entityIds:t._dataRange||[],departmentIds:t._department&&t._department.group||[],page:t.pageNumber,size:t.pageSize,scene:this.isOrganize?1:0}},onCheckboxClick:function(t){var e=this,a=e.$el.attr("data-index")||e.$el.css("z-index");t?(e.$el.attr("data-index",a),e.$el.css("z-index",1e3)):e.$el.css("z-index",e.$el.attr("data-index"))},resize:function(){this.widgets.dt.resize()},setParam:function(t){this.widgets.dt.setParam(t||{},!0)},setParamByKey:function(t,e){this.widgets.dt.setParamByKey(t,e)},refresh:function(t){this.widgets.dt.setParam(t||{},!0,!0)},formatTableData:function(t){var e=this;return t.map(function(t){return e.formatTableColumnData(t)})},formatTableColumnData:function(e){this.myObjectDataPermissions.forEach(function(t){t.ObjectDescribeApiName==e.entityId&&(e.entityNames=t.ObjectDescribeDisplayName)});var t=FS.contacts.getCircleById(e.deptId);return t&&(e.departmentNames=t.name),e},onEnable:function(t,e){var a=[],i=this.widgets.dt,e=(e&&e.id?a=[e.id]:i.getCheckedData().forEach(function(t){a.push(t.id)}),(e?t:$(t.target)).hasClass("stop"));r.FHHApi({url:e?this.config.disableUrl:this.config.enableUrl,data:{ids:a,scene:this.isOrganize?1:0},success:function(t){r.remind(1,$t("操作成功")),i.setParam({},!0)}})},onDel:function(t){var e=this,a=[],i=e.widgets.dt;if(t&&t.id)a=[t.id];else{var t=i.getCheckedData(),s=!1;if(t.forEach(function(t){0===t.status&&(s=!0),a.push(t.id)}),s)return r.remind(3,$t("不能删除启用中的规则")),!1}var n=r.confirm($t("确认删除数据"),$t("提示"),function(){r.FHHApi({url:e.config.delUrl,data:{ids:a,scene:e.isOrganize?1:0},success:function(t){0===t.Result.StatusCode&&(r.remind(1,$t("操作成功")),i.setParam({},!0))},complete:function(){n.destroy()}},{submitSelector:n.get("element")})})},onEdit:function(t,e){var a=this,i=a.widgets,s=i.myobject_dialog;t=a.formatTableColumnData(t),s&&s.destroy(),(s=new n({isOrganize:a.isOrganize,title:a.isOrganize?$t("编辑组织内数据权限"):$t("编辑部门内数据权限"),type:"edit"})).on("success",function(){a.refresh()}),s.show({data:t}),i.myobject_dialog=s}});return i});
define("crm-setting/datapermissions/departmentdataright/myobject/myobjectdialog",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","crm-widget/select/select","../template/add-dialog-html"],function(e,t,a){var l=e("crm-modules/common/util"),s=e("crm-widget/dialog/dialog"),n=e("crm-widget/selector/selector"),i=(e("crm-widget/select/select"),e("../template/add-dialog-html")),r=s.extend({attrs:{title:$t("添加部门内数据权限"),content:'<div class="crm-loading"></div>',className:"crm-s-datapermissions",showBtns:!0,showScroll:!0,size:"md",type:"add",data:{},isOrganize:!1},events:{"click .b-g-btn":"onSave","click .b-g-btn-cancel":"destroy"},render:function(){return this.widgets={},this.myObjectDataPermissions=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,r.superclass.render.call(this)},show:function(e){var t=this,a=(t.set(e=e||{}),t.widgets={},r.superclass.show.call(t)),s="edit"===t.get("type");return t.setContent(i({isEdit:s,data:e.data,isOrganize:t.get("isOrganize")})),t.$department=t.$(".j-department"),t.$datarange=t.$(".j-datarange"),t.$viewRange=t.$(".j-viewrange"),this.initDepartment(s),this.initDataRange(s),this.initViewRange(s),this.resizedialog(),a},initDepartment:function(e){var t=this,a=t.widgets,s=this.get("data"),i=[],e=(e&&i.push(s.deptId),new n({$wrap:this.$department,label:$t("请选择"),size:1,v2Size:"mini",single:!1,group:{chooseType:t.get("isOrganize")?"organization":"department"},foldInput:!0,selectedAfterHideLabel:!0,defaultSelectedItems:{group:i},isFromManage:!0,enableScope:!0}));e.on("change",function(){t.$department.next().hide()}),a.department=e},initDataRange:function(e){var t=this,a=t.widgets,s=JSON.parse(sessionStorage.getItem("crmDataPermissions")).myObjectDataPermissions,i=[{label:$t("全部"),value:""}],l=(s.forEach(function(e){i.push({label:e.ObjectDescribeDisplayName,value:e.ObjectDescribeApiName})}),[]),s=(e&&l.push(this.get("data").entityId),FxUI.create({wrapper:this.$datarange[0],template:'<fx-select v-model="value" size="mini" style="width:100%;" :disabled="disabled" :options="options" multiple filterable collapse-tags clearable>\n\t\t\t\t\t<template slot="options" slot-scope="slotProps">\n              <label role="checkbox" class="el-checkbox" :class="{\'is-checked\':isSelectAll}" style="margin-right: 0px;" v-if="slotProps.data.value===\'\'">\n                <span aria-checked="mixed" class="el-checkbox__input" :class="{\'is-checked\':isSelectAll}">\n                  <span class="el-checkbox__inner"></span> \n                </span>\n              </label>\n              <span>{{ slotProps.data.label }}</span>\n            </template>\n\t\t\t\t  </fx-select>',data:function(){return{value:l,options:i,disabled:"edit"===t.get("type"),allValues:i.map(function(e){return e.value}).filter(function(e){return""!==e}),isSelectAll:!1}},watch:{value:function(e){""===e[e.length-1]?(this.isSelectAll=!this.isSelectAll,this.isSelectAll?this.value=this.allValues:this.value=[]):e.length==this.allValues.length?this.isSelectAll=!0:this.isSelectAll=!1,t.$datarange.next().hide()}}}));a.datarange=s},initViewRange:function(e){var t=this,a=t.widgets,s=t.get("isOrganize"),i=[{label:s?$t("本级组织及子组织数据互相可见"):$t("本级部门及子部门数据互相可见"),value:0},{label:s?$t("本级组织数据可见",{},"本级组织数据可见（不含子组织）"):$t("本级部门数据可见",{},"本级部门数据可见（不含子部门）"),value:1},{label:s?$t("本级及下级组织数据可见"):$t("本级及下级部门数据可见"),value:2},{label:s?$t("本级组织数据可见（包含子组织）",{},"本级组织数据可见（包含子组织）"):$t("本级部门数据可见（包含子部门）",{},"本级部门数据可见（包含子部门）"),value:3}],s=FxUI.create({wrapper:this.$viewRange[0],template:'<fx-select v-model="value" size="mini" style="width:100%;" :options="options" filterable></fx-select>',data:function(){return{value:e?t.get("data").type:0,options:i}}});a.viewRange=s},onSave:function(e){var t,a=this,e=$(e.currentTarget),s=a.widgets.department.getValue(),i=a.widgets.viewRange.value;return 0==s.group.length?(a.$department.next().show(),!1):0===(t=a.widgets.datarange.value).length?(a.$datarange.next().show(),!1):void l.FHHApi({url:"/EM1HNCRM/API/v1/object/departmentDataPrivilege/service/upsert",data:{entityIds:t,deptIds:s.group,type:i,scene:a.get("isOrganize")?1:0},success:function(e){0===e.Result.StatusCode&&(a.trigger("success"),l.remind(1,$t("操作成功！")),a.hide())}},{submitSelector:e})},hide:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,r.superclass.hide.call(this)},destroy:function(){return _.each(this.widgets,function(e){e&&e.destroy&&e.destroy()}),this.widgets=null,r.superclass.destroy.call(this)}});a.exports=r});
define("crm-setting/datapermissions/departmentdataright/template/add-dialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-g-form department-data-dialog"> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = isOrganize ? $t("组织名称") : $t("部门名称_web")) == null ? "" : __t) + '</label> <div class="fm-wrap disabled-wraper"> <div class="j-department"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = isOrganize ? $t("请选择组织") : $t("请选择部门")) == null ? "" : __t) + '</span> <div class="disabled-layout"></div> </div> </div> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb"><em>*</em>' + ((__t = $t("suoshuduixiang")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-datarange"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("select_suoshuduixiang")) == null ? "" : __t) + '</span> </div> </div> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb"><div class="fm-lb-text"><em>*</em>' + ((__t = $t("可见范围")) == null ? "" : __t) + '</div><div class="crm-tip"> <span class="tip-btn"></span> <div class="tip-text"> <h3>1.' + ((__t = $t("crm_datapermissions_departmentdataright_tip1", {
                organize: isOrganize ? $t("crm_datapermissions_departmentdataright_organization", {}, "组织") : $t("crm_datapermissions_departmentdataright_department", {}, "部门")
            }, "本级【部门/组织】及子【部门/组织】数据互相可见：本级和下级【部门/组织】成员都互相可见归属本级和下级的数据。")) == null ? "" : __t) + "</h3> <h3>2." + ((__t = $t("crm_datapermissions_departmentdataright_tip2", {
                organize: isOrganize ? $t("crm_datapermissions_departmentdataright_organization", {}, "组织") : $t("crm_datapermissions_departmentdataright_department", {}, "部门")
            }, "本级【部门/组织】数据可见（包含子【部门/组织】）：本级和下级【部门/组织】成员仅可见归属各自【部门/组织】的数据。")) == null ? "" : __t) + "</h3> <h3>3." + ((__t = $t("crm_datapermissions_departmentdataright_tip3", {
                organize: isOrganize ? $t("crm_datapermissions_departmentdataright_organization", {}, "组织") : $t("crm_datapermissions_departmentdataright_department", {}, "部门")
            }, "本级【部门/组织】数据可见（不含子【部门/组织】）：本【部门/组织】成员仅可见归属本【部门/组织】的数据。")) == null ? "" : __t) + "</h3> <h3>4." + ((__t = $t("crm_datapermissions_departmentdataright_tip4", {
                organize: isOrganize ? $t("crm_datapermissions_departmentdataright_organization", {}, "组织") : $t("crm_datapermissions_departmentdataright_department", {}, "部门")
            }, "本级及下级【部门/组织】数据可见：本【部门/组织】成员可见归属本【部门/组织】和下级【部门/组织】的数据。")) == null ? "" : __t) + '</h3> </div> </div></label> <div class="fm-wrap"> <div class="j-viewrange"></div> <span class="fm-error crm-ico-error" style="display:none">' + ((__t = $t("请选择可见范围")) == null ? "" : __t) + "</span> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/departmentdataright/template/list-view-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="myobject-header"> <span class="label">' + ((__t = isOrganize ? $t("组织名称") : $t("部门名称_web")) == null ? "" : __t) + ':</span> <div class="range"></div> <span class="label">' + ((__t = $t("suoshuduixiang")) == null ? "" : __t) + ':</span> <div class="scope"></div> <span class="crm-btn crm-btn-primary j-add">+' + ((__t = isOrganize ? $t("添加组织内数据权限") : $t("添加部门内数据权限")) == null ? "" : __t) + '</span> </div> <div class="table-wrap"></div>';
        }
        return __p;
    };
});
define("crm-setting/datapermissions/other/other",["crm-modules/common/util","crm-widget/select/select","../template/other-html"],function(e,t,n){var i=e("crm-modules/common/util"),o=e("crm-widget/select/select"),l=e("../template/other-html"),a="mn-selected";n.exports=Backbone.View.extend({initialize:function(){this.render()},events:{"click .mn-checkbox-item":"onSet"},render:function(){var n=this;n.getAllConfig(function(t){CRM.util.getPermissionByApiName(["NewOpportunityObj"]).done(function(){var e=1==CRM.get("permission").NewOpportunityObj;n.$el.html(l(_.extend(t,{newoppo:e}))),n.initSelect(t.defaultData)})})},getAllConfig:function(t){var n=this;i.getConfigValues(!0).then(function(e){t&&t(n.formatData(e))})},setConfig:function(e,t){i.setConfigValue(e).then(function(){i.remind(1,$t("设置成功"))},function(){i.remind(3,$t("设置失败")),t&&t.toggleClass(a)})},onSet:function(e){e=$(e.currentTarget);return e.toggleClass(a),this.setConfig({key:"7",value:e.hasClass(a)?"1":"0"}),!1},initSelect:function(e){var n=this;n.select=new o({$wrap:n.$(".j-select-box"),zIndex:1e3,options:[{name:$t("所有下级数据"),value:"1"},{name:$t("直属下级数据"),value:"2"}],defaultValue:e}),n.select.on("change",function(e,t){n.setConfig({key:"10",value:t.value})})},formatData:function(e){var t={},e=e||[];return t.notlook="1"==this.getKeyValue(e,"7"),t.defaultData=this.getKeyValue(e,"10"),t},getKeyValue:function(e,t){return(_.findWhere(e,{key:t})||_.findWhere([{key:"7",value:"0"},{key:"10",value:"1"}],{key:t})).value},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){this.select.destroy()}})});
define("crm-setting/datapermissions/template/add-dialog-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="fm"> <div class="fm-lb">' + ((__t = $t("临时权限适用对象")) == null ? "" : __t) + '</div> <div class="fm-con fm-con-object"></div> <span class="fm-con-err"></span> </div> <div class="fm"> <div class="fm-lb">' + ((__t = $t("权限可开始时间")) == null ? "" : __t) + '</div> <div class="fm-con fm-con-start mn-checkbox-box"> <p> <span class="mn-checkbox-item check-icon mn-selected disabled" data-type=""></span> <span class="con-item-lb"> ' + ((__t = $t("任务或审批开始时、被抄送时")) == null ? "" : __t) + '</span> </p> </div> <span class="fm-con-err"></span> </div> <div class="fm"> <div class="fm-lb">' + ((__t = $t("权限收回时间")) == null ? "" : __t) + '</div> <div class="fm-con fm-con-receive mn-radio-box"> <p> <span class="mn-radio-item check-icon ' + ((__t = recyclingWay == "handlingTask" ? "mn-selected" : "") == null ? "" : __t) + '" data-type="recyclingWay.handlingTask"></span> <span class="con-item-lb"> ' + ((__t = $t("任务或审批结束时、已阅时")) == null ? "" : __t) + '</span> </p> <p> <span class="mn-radio-item check-icon ' + ((__t = recyclingWay == "timeLimit" ? "mn-selected" : "") == null ? "" : __t) + '" data-type="recyclingWay.timeLimit"></span> <span class="con-item-lb">' + ((__t = $t("自定义回收时间")) == null ? "" : __t) + '</span> </p> <div class="inp-day ' + ((__t = recyclingWay == "timeLimit" ? "show" : "hide") == null ? "" : __t) + '" > <span class="con-item-lb">' + ((__t = shouquanTxt1) == null ? "" : __t) + '</span><input class="b-g-ipt fm-ipt" type="text" value="' + ((__t = validityTerm) == null ? "" : __t) + '"><span class="con-item-lb">' + ((__t = shouquanTxt2) == null ? "" : __t) + '</span> <!-- <span class="con-item-lb">' + ((__t = $t("授权开始")) == null ? "" : __t) + '</span><input class="b-g-ipt fm-ipt" type="text" value="' + ((__t = validityTerm) == null ? "" : __t) + '"><span class="con-item-lb">' + ((__t = $t("天后。")) == null ? "" : __t) + '</span> --> </div> </div> </div> <div class="fm"> <div class="fm-lb">' + ((__t = $t("权限类型")) == null ? "" : __t) + '</div> <div class="fm-con fm-con-type mn-radio-box"> <p> <span class="mn-radio-item check-icon ' + ((__t = level == 1 ? "mn-selected" : "") == null ? "" : __t) + '" data-type="level.1"></span> <span class="con-item-lb"> ' + ((__t = $t("只读")) == null ? "" : __t) + '</span> </p> <p> <span class="mn-radio-item check-icon ' + ((__t = level == 2 ? "mn-selected" : "") == null ? "" : __t) + '" data-type="level.2"></span> <span class="con-item-lb"> ' + ((__t = $t("读写")) == null ? "" : __t) + "</span> </p> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/template/advance-setting-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="checkbox-scroll crm-scroll"> ';
            if (!useNew) {
                __p += ' <div class="dp-advance-guide"> <!-- ' + ((__t = $t("该功能已迁移，请您到")) == null ? "" : __t) + '<a href="#crmmanage/=/module-sysobject">' + ((__t = $t("【对象管理】")) == null ? "" : __t) + "</a>" + ((__t = $t("的相应对象下进行权限控制")) == null ? "" : __t) + " --> " + ((__t = txt1) == null ? "" : __t) + '&nbsp;<a href="#crmmanage/=/module-sysobject">' + ((__t = $t("datapermissions.advanceSetting.txt2")) == null ? "" : __t) + "</a>&nbsp;" + ((__t = txt2) == null ? "" : __t) + " </div> ";
            } else {
                __p += ' <div class="top"></div> <div class="table-wrap"></div> ';
            }
            __p += " </div>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/template/basic-content-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<table class="crm-table crm-table-noborder crm-table-open"> <thead> <tr> <th class="title"> <div class="tb-cell">' + ((__t = $t("数据类型")) == null ? "" : __t) + '</div></th><th><div class="tb-cell">' + ((__t = $t("权限")) == null ? "" : __t) + '</div> </th> </tr> </thead> <tbody class="datapermissions-basic-table"> ';
            _.each(myObjectDataPermissions, function(item, index) {
                __p += ' <tr> <td class="title"> <div class="tb-cell">' + ((__t = item.ObjectDescribeDisplayName) == null ? "" : __t) + ' </td> <td class="mn-radio-box" data-objectname="' + ((__t = item.ObjectDescribeDisplayName) == null ? "" : __t) + '" data-objectid="' + ((__t = item.ObjectDescribeApiName) == null ? "" : __t) + '" data-userdefined="true"> <div class="tb-cell"> <span class="radio-item"> <span class="mn-radio-item ' + ((__t = item.PermissionType === "1" ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = item.disabled1) == null ? "" : __t) + '" data-permissiontype="1"></span> <span class="radio-lb">' + ((__t = $t("私有")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="mn-radio-item ' + ((__t = item.PermissionType === "2" ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = item.disabled2) == null ? "" : __t) + '" data-permissiontype="2"></span> <span class="radio-lb">' + ((__t = $t("公开只读")) == null ? "" : __t) + '</span> </span> <span class="radio-item"> <span class="mn-radio-item ' + ((__t = item.PermissionType === "3" ? "mn-selected" : "") == null ? "" : __t) + " " + ((__t = item.disabled3) == null ? "" : __t) + '" data-permissiontype="3"></span> <span class="radio-lb">' + ((__t = $t("公开读写")) == null ? "" : __t) + "</span> </span> </div> </td> </tr> ";
            });
            __p += " </tbody> </table>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/template/basic-setting-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="checkbox-scroll crm-scroll"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ol> <li>" + ((__t = $t("crm.私有权限说明")) == null ? "" : __t) + "[" + ((__t = $t("只读可编辑")) == null ? "" : __t) + "]" + ((__t = $t("上级部门的部门负责人可以看到下级部门的所有数据。")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("crm.公开只读")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("3公开读写对象中所有数据对全公司公开全员可编辑。")) == null ? "" : __t) + '</li> </ol> </div> <div class="content-wrap"> <div class="crm-loading"></div> </div> </div> <div class="checkbox-btn"> <div class="b-g-btn j-save b-g-btn-disabled">' + ((__t = $t("保存")) == null ? "" : __t) + '</div> <div class="reset-btn j-reset">' + ((__t = $t("恢复默认")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/template/index-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-tit"> <h2><span class="tit-txt">' + ((__t = $t("数据权限管理")) == null ? "" : __t) + '<a class="crm-doclink" href="https://help.fxiaoke.com/dbde/20d4/686e/4925/a042" target="_blank"></a><a href="http://www.fxiaoke.com/mob/guide/crmdoc/video/?id=2" class="crm-ico-play" target="_blank" style="position:absolute;right:30px;top:15px;">' + ((__t = $t("了解更多")) == null ? "" : __t) + '</a></span></h2> </div> <div class="crm-module-con"> <div class="crm-tab crm-scroll"> ';
            if (_.contains(fns, "data_permission_base_data")) {
                __p += '<a class="item" href="#crm/setting/datapermissions/=/page1">' + ((__t = $t("基础数据权限")) == null ? "" : __t) + "</a>";
            }
            __p += " ";
            if (_.contains(fns, "data_permission_organization_data") && openOrgStatus) {
                __p += '<a class="item" href="#crm/setting/datapermissions/=/organize">' + ((__t = $t("组织内数据权限")) == null ? "" : __t) + "</a>";
            }
            __p += " ";
            if (_.contains(fns, "data_permission_department_data")) {
                __p += '<a class="item" href="#crm/setting/datapermissions/=/page1b">' + ((__t = $t("部门内数据权限")) == null ? "" : __t) + "</a>";
            }
            __p += " ";
            if (_.contains(fns, "data_permission_data_share")) {
                __p += '<a class="item" href="#crm/setting/datapermissions/=/page2">' + ((__t = $t("数据共享")) == null ? "" : __t) + "</a>";
            }
            __p += " ";
            if (_.contains(fns, "data_permission_related_team")) {
                __p += '<a class="item" href="#crm/setting/datapermissions/=/page3">' + ((__t = $t("相关团队数据权限")) == null ? "" : __t) + "</a>";
            }
            __p += " ";
            if (_.contains(fns, "data_permission_temporary")) {
                __p += '<a class="item" href="#crm/setting/datapermissions/=/page4">' + ((__t = $t("临时权限")) == null ? "" : __t) + "</a>";
            }
            __p += " ";
            if (_.contains(fns, "data_permission_others")) {
                __p += '<a class="item" href="#crm/setting/datapermissions/=/page5">' + ((__t = $t("其他")) == null ? "" : __t) + "</a>";
            }
            __p += ' </div> <div class="datapermissions-basic" style="display:none;"> <div class="crm-loading"></div> </div> <div class="organize-depart organize-permiss" style="display:none;"> <div class="rightsection"> <div class="crm-loading"></div> </div> </div> <div class="organize-depart department-data-right" style="display:none;"> <div class="rightsection"> <div class="crm-loading"></div> </div> </div> <div class="datashare" style="display:none;"> <div class="leftnav crm-scroll"> ... </div> <div class="rightsection"> <div class="crm-loading"></div> </div> </div> <div class="datapermissions-advance" style="display:none;"> <div class="crm-loading"></div> </div> <div class="temp-right crm-scroll" style="display:none;"> <div class="crm-loading"></div> </div> <div class="other-setting crm-scroll" style="display:none;"> <div class="crm-loading"></div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/datapermissions/template/other-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-p20" style="padding-left: 25px;"> <p class="mn-checkbox-box"> <span class="mn-checkbox-item ' + ((__t = notlook ? "mn-selected" : "") == null ? "" : __t) + '"></span> <span class="check-lb">' + ((__t = newoppo ? $t("crm.禁止无权限员工查看其他新商机") : $t("crm.禁止无权限员工查看其他")) == null ? "" : __t) + '</span> </p> <p class="crm-gray-9">' + ((__t = $t("crm.关联客户禁止后说明")) == null ? "" : __t) + '</p> <p class="crm-gray-9">' + ((__t = $t("关联拜访禁止后纷享销客")) == null ? "" : __t) + "(2016-08-01)" + ((__t = $t("在工作流中将显示为北京")) == null ? "" : __t) + "********" + ((__t = $t("公司")) == null ? "" : __t) + '(2016-08-01)”</p> <p class="crm-gray-9">' + ((__t = $t("关联线索")) == null ? "" : __t) + "/" + ((__t = newoppo ? $t("商机2.0") : $t("crm.商机")) == null ? "" : __t) + "/" + ((__t = $t("crm.产品禁止后说明")) == null ? "" : __t) + '</p> <br> <p class="crm-gray-9">' + ((__t = $t("其它业务模块暂时不受影响。")) == null ? "" : __t) + '</p> <p class="crm-gray-9">' + ((__t = $t("crm.关联退货单禁止后说明")) == null ? "" : __t) + '</p> <p class="leader-range" style="display: none;"> <span class="check-lb">' + ((__t = $t("上级可见数据范围")) == null ? "" : __t) + "</span><span class='j-select-box select-box'></span> </p> </div>";
        }
        return __p;
    };
});
define("crm-setting/datapermissions/template/temp-right-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<!-- <div class="crm-intro"> <h3>' + ((__t = $t("说明")) == null ? "" : __t) + "：</h3> <ol> <li>" + ((__t = $t("crm.临时权限说明")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("crm.开启临时权限说明")) == null ? "" : __t) + "</li> <li>" + ((__t = $t("crm.开启临时权限提前注意项说明")) == null ? "" : __t) + '</li> </ol> </div> --> <div class="temp-right-wrapper"> <div class="left-nav j-check-nav"> <div class="nav-item" data-type="righted">' + ((__t = $t("已授权列表")) == null ? "" : __t) + '</div> <div class="nav-item" data-type="toright">' + ((__t = $t("临时权限规则")) == null ? "" : __t) + '</div> </div> <div class="temp-right-table-wrapper toright-wrapper"> </div> <div class="temp-right-table-wrapper righted-wrapper"> <!-- <div class="temp-right-table"></div> --> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/datapermissions/tempright/addialog",["crm-widget/dialog/dialog","crm-widget/select/select","../template/add-dialog-html"],function(e,t,i){var a=e("crm-widget/dialog/dialog"),n=e("crm-widget/select/select"),l=e("../template/add-dialog-html"),s=Backbone.Model.extend({defaults:{apiName:"",recyclingWay:"handlingTask",level:1,validityTerm:1},getFilterObj:function(){return CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/getAvailableObjectList",data:{sourceInfo:"object_management"}})}}),e=a.extend({attrs:{title:$t("新建临时权限"),classPrefix:"crm-c-dialog s-add-tempright",showBtns:!0,showScroll:!1,data:{apiName:"",recyclingWay:"handlingTask",level:1,validityTerm:1}},events:{"click .fm-con-receive .check-icon":"reveiveHandle","click .check-icon":"checkHandle"},show:function(e,t){e=_.clone(e),e=_.extend({},this.get("data")||{},e||{});this.model=new s(e),a.prototype.show.call(this),this.setTitle(t?$t("编辑临时权限"):$t("新建临时权限")),this.setContent(l(_.extend(e,{isEdit:t,shouquanTxt1:$t("授权开始{{x}}天后。").split("{{x}}")[0],shouquanTxt2:$t("授权开始{{x}}天后。").split("{{x}}")[1]}))),this.initSelect(e,t),this.bindEvents()},bindEvents:function(){CRM.util.onlyAllowNum(this,".fm-ipt",4)},reveiveHandle:function(e){e=$(e.target);$(".inp-day",this.element).toggleClass("hide","recyclingWay.timeLimit"!==e.data("type"))},checkHandle:function(e){var t=$(e.currentTarget),i=t.data("type"),a=i.indexOf("."),l=i.substring(0,a),i=i.substring(a+1);t.hasClass("disabled")?e.stopPropagation():this.model.set(l,i)},initSelect:function(t,a){var l=this;l.model.getFilterObj().then(function(e){var i;0===e.Result.StatusCode&&e.Value.success?(i=l.formatData(e.Value.objects),l.select&&l.select.destroy&&l.select.destroy(),l.publicObjectBlocking(function(e){e&&0<e.publicObject.length&&e.publicObject.forEach(function(t){i=_.filter(i,function(e){return e.value!=t})}),l.select=new n({$wrap:$(".fm-con-object",l.element),options:a?i.concat({value:t.apiName,name:t.displayName}):i,defaultVal:t.apiName||"",disabled:a}),l.model.set("apiName",l.select.getValue()),l.select.on("change",function(e){l.model.set("apiName",e)})})):CRM.util.alert(e.Result.FailMessage)})},publicObjectBlocking:function(t){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/getDescribeListByObjectType",data:{},success:function(e){0===e.Result.StatusCode?t&&t(e.Value):t&&t()}})},getData:function(){var e=$.extend({},this.model.toJSON(),{validityTerm:this.$(".fm-ipt").val()});if(this.validate(e))return e},validate:function(e){return+e.validityTerm?!!e.apiName||(CRM.util.alert($t("请选择对象")+"！"),!1):(CRM.util.alert($t("回收时间不能为0！")),!1)},formatData:function(e){return _.map(e,function(e){return{value:e.api_name,name:e.display_name}})},destroy:function(){a.prototype.destroy.call(this),this.select&&this.select.destroy&&this.select.destroy()}});i.exports=e});
define("crm-setting/datapermissions/tempright/common",["crm-modules/action/common/common","crm-modules/common/util","paas-template/sdk","crm-widget/dialog/dialog"],function(e,a,t){var i=e("crm-modules/action/common/common"),s=e("crm-modules/common/util"),e=(e("paas-template/sdk"),e("crm-widget/dialog/dialog"),i.extend({batchDeleteTemporaryRights:function(e,a){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/batchDeleteTemporaryRights",data:{temporaryRightsIds:e},success:function(e){0===e.Result.StatusCode&&e.Value.success?(t.trigger("refresh"),a&&a(!0)):s.alert(e.Result.FailureMessage)}})},fetchRemoveRight:function(e,a){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/deleteTemporaryRights",data:{sourceId:e.sourceId,dataId:e.objectDataId,owner:e.owner,apiName:e.objectDescribeApiName},success:function(e){0===e.Result.StatusCode&&e.Value.success?(t.trigger("refresh"),a&&a(!0)):s.alert(e.Result.FailureMessage)}})},showTipDialog:function(){var e=["<div class='tip-title'>"+$t("说明：")+"</div><div class='item-con'>"+$t("crm.临时权限说明")+"</div><div class='item-con'>"+$t("crm.开启临时权限说明")+"</div><div class='item-con'>"+$t("crm.开启临时权限提前注意项说明")+"</div>"].join("");s.alert(e,function(){},{okLabel:$t("知道了"),classPrefix:"crm-c-dialog temp-right-rule-tip-dialog",title:$t("权限提示信息")})},getCheckedOwners:function(){var e=$(".temp-right-remove-dialog .check-items .j-check").not(".mn-selected"),a=[];return _.each(e,function(e){a.push($(e).data("con")+"")}),a},add:function(e,a){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/createTemporaryPrivilege",data:{apiName:e.apiName,recyclingWay:e.recyclingWay,level:e.level,validityTerm:e.validityTerm},success:function(e){0==e.Result.StatusCode?e.Value.success?(t.trigger("refresh"),a&&a(!0)):(s.alert(e.Value.message),a&&a(!1)):(s.alert(e.Result.FailureMessage),t.trigger("refresh"),a&&a(!0))}})},edit:function(e,a){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/updateTemporaryPrivilege",data:{apiName:e.apiName,recyclingWay:e.recyclingWay,validityTerm:e.validityTerm,level:e.level},success:function(e){0==e.Result.StatusCode?e.Value.success?(t.trigger("refresh"),a&&a(!0)):(s.alert(e.Value.message),a&&a(!1)):(s.alert(e.Result.FailureMessage),t.trigger("refresh"),a&&a(!0))}})},enable:function(e,a){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/enableTemporaryPrivilege",data:{apiName:e.apiName},success:function(e){0==e.Result.StatusCode?e.Value.success?(t.trigger("refresh"),a&&a(!0)):(s.alert(e.Value.message),a&&a(!1)):s.alert(e.Result.FailureMessage)}})},disable:function(e,a){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/disableTemporaryPrivilege",data:{apiName:e.apiName},success:function(e){0==e.Result.StatusCode?e.Value.success?(t.trigger("refresh"),a&&a(!0)):(s.alert(e.Value.message),a&&a(!1)):s.alert(e.Result.FailureMessage)}})},remove:function(e,a){var t=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/deleteTemporaryPrivilege",data:{apiName:e.apiName},success:function(e){0==e.Result.StatusCode?e.Value.success?(t.trigger("refresh"),a&&a(!0)):(s.alert(e.Value.message),a&&a(!1)):s.alert(e.Result.FailureMessage)}})}}));t.exports=e});
define("crm-setting/datapermissions/tempright/righted",["crm-modules/common/util","crm-widget/table/table","crm-widget/selector/selector"],function(e,t,a){var r=e("crm-modules/common/util"),i=e("crm-widget/table/table"),s=e("crm-widget/selector/selector"),n=[{value:"EQ",label:$t("等于"),index:1},{value:"N",label:$t("不等于"),index:2},{value:"LT",label:$t("早于"),index:5},{value:"GT",label:$t("晚于"),index:3},{value:"GTE",label:$t("晚于等于"),index:4},{value:"LTE",label:$t("早于等于"),index:6},{value:"IS",label:$t("为空（未填写）"),index:9},{value:"ISN",label:$t("不为空"),index:10},{value:"BETWEEN",label:$t("时间段"),index:17}],d=Backbone.Model.extend({getAllFieldsByApiName:function(){return r.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findBizDescribeList",data:{sourceInfo:"object_management",isDraft:!0,isIncludeFieldDescribe:!0,isIncludeSystemObj:!0,isIncludeUnActived:!0,packageName:"CRM"}})},batchDeleteTemporaryRights:function(e,t){r.FHHApi({url:"/EM1HNCRM/API/v1/object/data_privilege/service/batchDeleteTemporaryRights",data:{temporaryRightsIds:e},success:function(e){0===e.Result.StatusCode&&e.Value.success?t&&t(!0):r.alert(e.Result.FailureMessage)}})}}),l=Backbone.View.extend({initialize:function(){this.model=new d,this.widgets={}},events:{"click .j-remove-right-batch":"_removeRightBatch"},show:function(){this._initTable()},_initTable:function(){var s=this;s.widgets.tb&&s.widgets.tb.destory&&s.widgets.tb.destory(),s.widgets.tb=new i({$el:this.$el,url:"/EM1HNCRM/API/v1/object/data_privilege/service/queryTemporaryRightsList",requestType:"FHHApi",alwaysShowTermBatch:!0,showMultiple:!0,postData:{describe_api_name:"",scene:"",user_id:""},page:{pageSize:20,pageNumber:1},batchBtns:[{text:$t("解除权限"),className:"j-remove-right-batch"}],columns:[{data:"objectDescribeLabel",title:$t("数据所属对象"),referRule:"Employee",isId:!0,dataType:1,orderValues:[1,0],isFilter:!0},{data:"objectDataLabel",title:$t("主属性"),orderValues:[1,0],isFilter:!0},{data:"ownerName",title:$t("临时授权人员"),width:120},{data:"sceneName",title:$t("来源"),width:120},{data:"scene",title:$t("授权方式"),render:function(e){return"QIXIN"==e?$t("转发时手动授权"):$t("任务或审批开始时、被抄送时")}},{data:"startTime",title:$t("授权开始时间"),dataType:4,width:120},{data:"expiryTime",title:$t("授权结束时间"),dataType:4,width:120},{data:"withdrawalWay",title:$t("回收方式"),width:120,render:function(e){return"timeLimit"===e?$t("到期收回"):$t("任务处理后")}},{data:"status",title:$t("状态"),width:120,render:function(e){return"enable"===e?$t("已授权"):$t("权限已被禁用")}},{lastFixed:!0,width:180,title:$t("操作"),render:function(e,t,a){return'<div class="table-ops"><a class="j-remove-right">'+$t("解除权限")+"</a></div>"}}],formatData:function(e){return{data:e.rightsInfoList,totalCount:e.totalNumber||0}},initComplete:function(){s._renderFilter()}},{errorAlertModel:1}),s.widgets.tb.on("trclick",function(t,e,a){var i=[];_.each(t.owners,function(e,t){i.push({name:e,value:t})}),a.hasClass("j-remove-right")&&FxUI.MessageBox.confirm($t("确定要解除权限吗？")).then(function(){s._createOperate(function(e){e.batchDeleteTemporaryRights([t.temporaryRightsId])})}).catch(function(){})})},_renderFilter:function(){var t=this;t._initSelect(function(e){t.widgets.select&&t.widgets.select.destroy&&t.widgets.select.destroy(),t.widgets.select=t.widgets.tb.addSelect({label:$t("所属对象:"),options:e,defaultValue:0,width:"126"}),t.widgets.select.on("change",function(e){t.widgets.tb&&t.widgets.tb.setParam({describe_api_name:0!=e?e:""},!0)}),t._initFromSelect(),t._initSelectBar(),t._initDate("start"),t._initDate("end")})},_createOperate:function(t){var a=this;e.async("./common",function(e){a.widgets.operate&&a.widgets.operate.destroy&&a.widgets.operate.destroy(),a.widgets.operate=new e,a.widgets.operate.on("refresh",function(){a.widgets.tb.setParam({},!0)}),t&&t(a.widgets.operate)})},_removeRightBatch:function(){var e=this,t=this.widgets.tb.getCheckedData().map(function(e){return e.temporaryRightsId});t.length&&FxUI.MessageBox.confirm($t("确定要解除{{n}}条临时权限吗？",{data:{n:t.length}})).then(function(){e._createOperate(function(e){e.batchDeleteTemporaryRights(t)})}).catch(function(){})},_initFromSelect:function(){var t=this,e=[{name:$t("全部"),value:"0"},{name:$t("审批流"),value:"approval"},{name:$t("业务流"),value:"bpm"},{name:$t("阶段推进器"),value:"stage"}];FS.util.getUserAttribute("temprightfreeapprovalflow")&&e.push({name:$t("自由审批流"),value:"free_approvalflow"}),t.widgets.fromSelect&&t.widgets.fromSelect.destroy&&t.widgets.fromSelect.destroy(),t.widgets.fromSelect=t.widgets.tb.addSelect({label:$t("所属流程"),width:"110",options:e,defaultValue:0}),t.widgets.fromSelect.on("change",function(e){t.widgets.tb&&t.widgets.tb.setParam({scene:0!=e?e:""},!0)})},_initSelect:function(s){$.when(this.model.getAllFieldsByApiName()).then(function(e){var t,a,i;0===e.Result.StatusCode?(t=[{name:$t("全部"),value:"0"}],i=e.Value.objectDescribeList,e.Value.manageGroup&&!e.Value.manageGroup.all&&Array.isArray(e.Value.manageGroup.apiNames)&&(a=e.Value.manageGroup.apiNames,i=i.filter(function(e){return-1<a.indexOf(e.api_name)})),_.each(i,function(e){t.push({name:e.display_name,value:e.api_name})}),s&&s(t)):r.alert(e.Result.FailureMessage)})},_initSelectBar0:function(){var t=this,e=['<div class="item">','<span class="item-tit">'+$t("授权人员")+"</span>",'<div class="item-con emploee-wrapper" style="width:136pxpx;"></div>',"</div>","</div>"].join("");t.$(".batch-term").append($(e)),t.widgets.selectBar&&t.widgets.selectBar.destroy&&t.widgets.selectBar.destroy(),t.widgets.selectBar=new s({$wrap:t.$(".emploee-wrapper"),width:130,single:!0,member:!0,v2Size:"mini",selectedAfterHideLabel:!0,isFromManage:!0,enableScope:!0}),t.widgets.selectBar.on("change",function(e){t.widgets.tb&&t.widgets.tb.setParam({user_id:e.member[0]?""+e.member[0]:""},!0)})},_initSelectBar:function(){var a=this,e=['<div class="item">','<span class="item-tit">'+$t("授权人员")+"</span>",'<div class="item-con emploee-wrapper" style="width:136pxpx;"></div>',"</div>","</div>"].join("");a.$(".batch-term").append($(e)),a.widgets.selectBar&&a.widgets.selectBar.destroy&&a.widgets.selectBar.destroy(),seajs.use("icmanage-modules/fx-ui/icselector",function(e){var t=[{id:"outerErUids",title:$t("互联用户"),noFilterVal:!0}],e={single:!0,member:!0,size:"mini",selectedAfterHideLabel:!0,isFromManage:!0,enableScope:!0,tabs:e.getOuterTabs(t)},t=FS.selectorParseContactV2.parseContacts(e);a.widgets.selectBar=new Vue({el:a.$(".emploee-wrapper")[0],template:'<div class="item-con" style="width:130px;"><fx-selector-input-v2 ref="icsel" v-bind="opts" @change="onChange"></fx-selector-input-v2></div>',data:{opts:t},methods:{onChange:function(e){var t=this.$refs.icsel.getValue(),t=t.member[0]||t.outerErUids[0]||"";a.widgets.tb&&a.widgets.tb.setParam({user_id:t+""},!0)}}})})},_initDate:function(a){var e="",i="date"+a,e="start"==a?$t("授权开始时间"):$t("授权结束时间"),s=this,e=['<div class="item">','<div class="item-tit datetime"><span>'+e+': </span><div class="'+a+'-date-select"></div></div>','<div class="item-con datetime"><div class="'+a+'-date"></div></div>',"</div>"].join("");s.$(".batch-term").append($(e)),s.widgets[i]&&s.widgets[i].destroy&&s.widgets[i].destroy(),s.widgets[i]=FxUI.create({wrapper:s.$(".batch-term ."+a+"-date-select")[0],template:'<fx-select ref="select" v-model="value" :options="options" size="mini" @change="onChange"></fx-select>',data:function(){return{options:n,value:"EQ"}},methods:{onChange:function(e){var t="",t="BETWEEN"===e?"datetimerange":-1<"LTED|GTED|LTEM|GTEM|LTEW|GTEW|LTEDO|GTEDO|LTEMO|GTEMO|LTEWO|GTEWO|LTDAY|GTDAY|LTWEEK|GTWEEK".split("|").indexOf(e)?"input":"datetime";s[i+"comtype"]!=t?(s[i+"comtype"]=t,s.createDateTime(a)):"start"==a?s.widgets.tb&&s.widgets.tb.setParam({createTimeRange:[{operator:e,queryTime:s.widgets[i+"com"].value}]},!0):s.widgets.tb&&s.widgets.tb.setParam({expiryTimeRange:[{operator:e,queryTime:s.widgets[i+"com"].value}]},!0)}}}),s[i+"comtype"]="datetime",s.createDateTime(a)},createDateTime:function(a){var i=this,s="date"+a,e=i[s+"comtype"],t="";"datetimerange"==e?t=' <fx-date-picker v-model="value" size="mini" type="datetimerange" format="yyyy-MM-dd HH:mm" value-format="timestamp" start-placeholder="'+$t("开始时间")+'" end-placeholder="'+$t("结束时间")+'" @change="onChange"> </fx-date-picker>':"datetime"==e?t='<fx-date-picker v-model="value" size="mini" type="datetime" placeholder="'+$t("选择日期时间")+'" value-format="timestamp" format="yyyy-MM-dd HH:mm" @change="onChange"></fx-date-picker>':"input"==e&&(t='<fx-input v-model="value" size="mini" type="number" clearable maxlength="3" placeholder="'+$t("请输入")+'" @change="onChange"> </fx-input>'),i.widgets[s+"com"]&&i.widgets[s+"com"].destroy&&i.widgets[s+"com"].destroy(),i.widgets[s+"com"]=FxUI.create({wrapper:i.$(".batch-term ."+a+"-date")[0],template:t,data:function(){return{value:""}},methods:{onChange:function(e){var t="",t=Array.isArray(e)?[{operator:i.widgets[s].value,startTime:e[0],endTime:e[1],queryTime:""}]:[{operator:i.widgets[s].value,queryTime:e||"",startTime:"",endTime:""}];"start"==a?i.widgets.tb&&i.widgets.tb.setParam({createTimeRange:t},!0):i.widgets.tb&&i.widgets.tb.setParam({expiryTimeRange:t},!0)}}})},hide:function(){this.$el.hide()},destroy:function(){_.each(this.widgets,function(e){e.destroy&&e.destroy()}),this.widgets=null}});a.exports=l});
define("crm-setting/datapermissions/tempright/tempright",["../template/temp-right-html","./righted","./to_right"],function(t,e,i){var h=t("../template/temp-right-html"),s=t("./righted"),r=t("./to_right");i.exports=Backbone.View.extend({initialize:function(){this.type="righted",this.widgets={},this.$el.html(h())},events:{"click .j-add":"addHandle","click .j-check-nav":"checkNavHandle"},show:function(){this.$el.show(),this.$(".nav-item").eq(0).trigger("click")},hide:function(){this.$el.hide()},checkNavHandle:function(t){t=$(t.target);t.addClass("checked").siblings().removeClass("checked"),this.type=t.data("type"),this.$("."+this.type+"-wrapper").removeClass("hide").siblings(".temp-right-table-wrapper").addClass("hide"),this.switch()},switch:function(){var t=this.type.replace(/(\w)/,function(t){return"render"+t.toUpperCase()});this[t]()},renderToright:function(){this.widgets.to_right_tb||(this.widgets.to_right_tb=new r({el:this.$(".toright-wrapper")})),this.widgets.to_right_tb.show()},renderRighted:function(){this.widgets.righted_tb||(this.widgets.righted_tb=new s({el:this.$(".righted-wrapper")})),this.widgets.righted_tb.show()},destroy:function(){_.each(this.widgets,function(t){t.destroy&&t.destroy()}),this.$el.remove(),this.$el=null}})});
define("crm-setting/datapermissions/tempright/to_right",["crm-modules/common/util","crm-widget/table/table","./addialog"],function(t,e,i){var a=t("crm-modules/common/util"),o=t("crm-widget/table/table"),d=t("./addialog"),n=Backbone.View.extend({initialize:function(){this.widgets={}},show:function(){this.widgets={},this._initTable()},events:{"click .j-add":"doAdd","click .j-show-tip":"showTip"},_initTable:function(){var a=this;a.widgets.tb&&a.widgets.tb.destory&&a.widgets.tb.destory(),a.widgets.tb=new o({$el:this.$el,url:"/EM1HNCRM/API//v1/object/data_privilege/service/queryTemporaryPrivilege",requestType:"FHHApi",showFilerBtn:!1,showMultiple:!1,showPage:!1,postData:{api_name:""},operate:{pos:"T",btns:[{text:"+ "+$t("新建临时权限规则"),className:"j-add"}]},columns:[{data:"displayName",title:$t("数据所属对象"),referRule:"Employee",isId:!0,dataType:1,orderValues:[1,0],isFilter:!0},{data:"recyclingWay",title:$t("权限收回方式"),orderValues:[1,0],isFilter:!0,render:function(t,e,i){return"timeLimit"==t?$t("到期收回"):$t("任务处理后")}},{data:"level",title:$t("权限类型"),width:120,render:function(t,e,i){return 1==t?$t("只读"):$t("读写")}},{data:"confSwitch",title:$t("状态"),width:120,render:function(t,e,i){return 1==t?$t("已开启"):$t("已禁用")}},{lastFixed:!0,width:180,title:$t("操作"),render:function(t,e,i){return i.confSwitch?'<div class="table-ops"><a class="j-disable ops-item" data-action="disable">'+$t("禁用")+'</a><a class="j-edit ops-item" data-action="edit">'+$t("编辑")+"</a></div>":'<div class="table-ops"><a class="j-enable ops-item" data-action="enable">'+$t("启用")+'</a><a class="j-edit ops-item" data-action="edit">'+$t("编辑")+'</a><a class="j-remove ops-item" data-action="remove">'+$t("删除")+"</a></div>"}}],initComplete:function(){$(".target-item",this.$el).after('<a class="tip j-show-tip" href="javascript:;">'+$t("查看临时权限说明")),this.$el.prepend('<div style="position: absolute;left:16px;top:14px;z-index:1100;">'+$t("datapermissions_tempright_txt1")+"</div>")},formatData:function(t){return{data:t.configs,totalCount:t.totalNumber||0}}},{errorAlertModel:1}),a.widgets.tb.on("trclick",function(t,e,i){switch($(i).data("action")){case"enable":a.doEnable(t);break;case"disable":a.doDisable(t);break;case"edit":a.doEdit(t);break;case"remove":a.doRemove(t)}})},showTip:function(){this.createOperate(function(t){t.showTipDialog()})},createOperate:function(e){var i=this;t.async("./common",function(t){i.widgets.operate&&i.widgets.operate.destroy&&i.widgets.operate.destroy(),i.widgets.operate=new t,i.widgets.operate.on("refresh",function(){i.widgets.tb&&i.widgets.tb.setParam({},!0)}),e&&e(i.widgets.operate)})},doEnable:function(e){this.createOperate(function(t){t.enable(e,function(t){t&&a.remind(1,$t("启用成功"))})})},doDisable:function(e){var t=this,i=a.confirm($t("确定要禁用临时权限吗？")+"</br> "+$t("禁用后已授权人员的临时权限将会被收回"),$t("提示"),function(){t.createOperate(function(t){t.disable(e,function(t){t&&a.remind(1,$t("禁用成功")),i.hide()})})})},doRemove:function(e){var t=this,i=a.confirm($t("确定要删除临时权限吗？")+"</br> "+$t("删除临时权限后已授权的数据会被清空"),$t("提示"),function(){t.createOperate(function(t){t.remove(e,function(t){t&&a.remind(1,$t("删除成功")),i.hide()})})})},doEdit:function(t){var e,i=this;i.widgets.editdialog=this._createDialog(),i.widgets.editdialog.on("dialogEnter",function(){(e=this.getData())&&i._doEdit(e,function(t){t?CRM.util.remind(1,$t("编辑成功!")):CRM.util.remind(3,$t("编辑失败!")),i.widgets.editdialog.hide()})}),i.widgets.editdialog.show(t,!0)},_doEdit:function(e,i){this.createOperate(function(t){t.edit(e,i)})},_doAdd:function(e,i){this.createOperate(function(t){t.add(e,i)})},doAdd:function(){var t,e=this;e.widgets.addialog=this._createDialog(),e.widgets.addialog.on("dialogEnter",function(){(t=this.getData())&&e._doAdd(t,function(t){t?CRM.util.remind(1,$t("新建成功")+"!"):CRM.util.remind(3,$t("新建失败!")),e.widgets.addialog.hide()})}),e.widgets.addialog.show()},_createDialog:function(){var t=this;return t.widgets.dialog&&t.widgets.dialog.destroy&&t.widgets.dialog.destroy(),t.widgets.dialog=new d,t.widgets.dialog.on("dialogCancel",function(){t.widgets.dialog.hide()}),t.widgets.dialog},destroy:function(){_.each(this.widgets,function(t){t&&t.destroy&&t.destroy()}),this.$el=null}});i.exports=n});