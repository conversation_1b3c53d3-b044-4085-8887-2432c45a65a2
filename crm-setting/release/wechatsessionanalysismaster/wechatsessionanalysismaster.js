define("crm-setting/wechatsessionanalysismaster/wechatsessionanalysismaster",["crm-modules/components/objecttable/objecttable"],function(e,t,a){var n=e("crm-modules/components/objecttable/objecttable"),e=Backbone.View.extend({initialize:function(e){this.apiname="WechatSessionAnalysisMasterObj"},render:function(){this.renderTable()},renderTable:function(){var e=this,i=this,t=n.extend({initialize:function(e){this.setElement(e.wrapper),n.prototype.initialize.apply(this,arguments)},getColumns:function(){var e=n.prototype.getColumns.apply(this,arguments);return _.findWhere(e,{dataType:"operate"}).width=100,e},getCustomOperate:function(e,t){return _.each(e,function(e){e.render_type="not_fold",e.data=t}),e},operateBtnClickHandle:function(e){var t=$(e.target),a=t.data("action"),t=t.closest(".tb-cell").data("id");a&&(a="__"+a.split("_").join("")+"Handle",i[a])&&i[a](e,t)},trclickHandle:function(e){i.showDetail(e,this.table.getCurData())}});this.list=new t({wrapper:this.options.wrapper,apiname:this.apiname,showOperate:!0,tableOptions:{searchTerm:null,search:{placeHolder:$t("crm.wechatsessionanalysismasterobj.searchPlaceHolder"),type:"Keyword",highFieldName:"name",pos:"T"},operate:{pos:"T",btns:[{action:"add",attrs:"data-action=add",className:"j-action",text:$t("新建")}]},refreshCallBack:function(){e.refresh()}}}),this.list.render()},refresh:function(){this.list.refresh()},showDetail:function(e,t){var a=this;CRM.api.show_crm_detail({apiName:this.apiname,id:e._id,idList:_.pluck(t,"_id"),showMask:!1,top:56,callback:function(){a.refresh()}})},__addHandle:function(){var e=this;this.add=CRM.api.add({apiname:e.apiname,success:function(){e.refresh()}})},__EditHandle:function(e,t){var a=this;a.edit=CRM.api.edit({apiname:a.apiname,id:t,success:function(){a.refresh()}})},__DeleteHandle:function(e,t){var a=this,i=FS.crmUtil.confirm($t("确定要删除？"),$t("删除"),function(){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/WechatSessionAnalysisMasterObj/action/Delete",data:{objectDataId:t},success:function(e){i.hide(),0===e.Result.StatusCode?(CRM.util.remind($t("操作成功")),a.refresh()):CRM.util.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},{hideFn:function(){i=null}})}});a.exports=e});