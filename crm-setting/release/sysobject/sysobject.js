define("crm-setting/sysobject/component/accountTreeConfig/accountTreeConfig",[],function(e,t,n){var i=Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper),this.apiName=e.apiName,this.$wrapper=e.el[0],this.render()},render:function(){var t=this;e.async("vcrm/sdk",function(e){e.getComponent("accountTreeConfig").then(function(e){e=Vue.extend(e.default);t.widget=new e({el:t.$wrapper,propsData:{apiName:t.apiName}})})})},destroy:function(){this.widget&&this.widget.destroy&&this.widget.destroy()}});n.exports=i});
define("crm-setting/sysobject/component/enterpriselibrary/enterpriselibrary",["crm-modules/common/util","./template/tpl-html","paas-function/sdk"],function(t,e,n){var a=t("crm-modules/common/util"),i=t("./template/tpl-html"),o=t("paas-function/sdk"),c={},u={},t=Backbone.View.extend({initialize:function(t){this.comps={},this.setElement(t.el),this.$el.html(i()),this.getConfig()},render:function(){},events:{"click .j-save":"saveHandle"},renderRadioTpl:function(t){var e=this,n={wrapper:".leads-wrapper",template:'<fx-radio-group v-model="value" @change="change">\n\t\t\t\t\t\t\t\t<fx-radio label="1">'.concat($t("默认规则（线索/客户的公司名称是工商注册）"),'</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="0">').concat($t("自定义规则"),'\n\t\t\t\t\t\t\t\t\t<div v-if="custom_function" class="custom-function-name" @click="clickCustomFunction">-{{custom_function.func_name}}<i class="el-icon-edit"></i></div>\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t</fx-radio-group>'),data:function(){return{value:t,custom_function:null}},methods:{change:function(t){"0"===(e.oppoLeads=t)?(e.renderCustomFunction(),this.custom_function={func_name:$t("添加")}):this.custom_function=null},clickCustomFunction:function(){var t=this.custom_function?this.custom_function.func_api_name:"";e.renderCustomFunction(t)}},created:function(){"0"==this.value&&(this.custom_function=c,this.custom_function.func_name=u&&u.function?c.func_name:$t("该函数已删除"))}};e.comps.radiobox=window.FxUI.create(n)},renderCustomFunction:function(t){var e=this;o&&o.getEnterpriseDirectoryFunction({object_api_name:"EnterpriseInfoObj",checked_api_name:t},function(t){t.status&&(t=t.data&&t.data.function,e.comps.radiobox.custom_function={func_api_name:t.api_name,binding_object_api_name:t.binding_object_api_name,func_name:t.function_name})})},getFunc:function(){var e=this,t=c&&c.binding_object_api_name?c.binding_object_api_name:"NONE";e._getAjax&&(e._getAjax.abort(),e._getAjax=null),e._getAjax=a.FHHApi({url:"/EM1HNCRM/API/v1/object/function/service/find",data:{api_name:c&&c.func_api_name,binding_object_api_name:t},success:function(t){0===t.Result.StatusCode?t.Value&&(u=t.Value,e.renderRadioTpl(e.oppoLeads)):a.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},getConfig:function(){var i=this;i._getAjax&&(i._getAjax.abort(),i._getAjax=null),i._getAjax=a.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key",data:{key:"account_leads_to_enterprise"},success:function(t){var e,n;0===t.Result.StatusCode?t.Value&&t.Value.value&&(n=null,"string"==typeof(e=t.Value.value)&&(c=e=JSON.parse(e)),n=e&&e.use_default_rule,i.oppoLeads=n,i.getFunc()):a.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},saveHandle:function(){var t=this.comps.radiobox,e=t.custom_function,e={use_default_rule:this.oppoLeads,func_api_name:"0"==this.oppoLeads?e&&e.func_api_name:"",func_name:"0"==this.oppoLeads?e&&e.func_name:"",binding_object_api_name:"0"==this.oppoLeads?e&&e.binding_object_api_name:""};t.custom_function&&!t.custom_function.func_api_name?CRM.util.alert($t("请选择一个自定义函数")):this.setConfig(JSON.stringify(e))},setConfig:function(t){var e=this;e._getAjax&&(e._getAjax.abort(),e._getAjax=null),e._getAjax=CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"account_leads_to_enterprise",value:t},success:function(t){0===t.Result.StatusCode?CRM.util.remind(1,$t("设置成功")):CRM.util.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},destroy:function(){this.undelegateEvents()}});n.exports=t});
define("crm-setting/sysobject/component/enterpriselibrary/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="enterpriselibrary-box"> <div class="crm-module-con crm-scroll crm-top"> <h3 class="title">' + ((__t = $t("选择同步规则:")) == null ? "" : __t) + '</h3> <div class="leads-wrapper"></div> <div class="crm-btn crm-btn-primary j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/onlyone/onlyone",["base-modules/utils","./template/tpl-html","../../data/data","crm-widget/select/select"],function(e,t,l){var a=e("base-modules/utils"),s=e("./template/tpl-html"),o=(e("../../data/data").data,e("crm-widget/select/select")),n=Backbone.Model.extend({defaults:{IsImportEnabled:"",IsOpenApiEnabled:"",checkList:[],defaultValue:"",RuleSettings:[],ownerType:""}}),e=Backbone.View.extend({choiceNum:1,ruleSelects:[],options:{ownerType:2,checklist:[{value:"",name:$t("请选择")},{value:"key1",name:$t("条件1")},{value:"key2",name:$t("条件2")}]},events:{"click .check-btn":"offHandle","click .j-add-item":"addItemHandle","click .j-del-item":"delItemHandle","click .j-ok":"saveHandle"},initialize:function(e){var i=this;this.$el.appendTo(e.$el),this.model=new n,i.collection=[],i.$el.append('<div class="crm-loading"></div>'),this.getCheckList(function(){i.getUniquenessRule(function(e){e=e||{},i.$(".crm-loading").remove();var t=(e.pending_rules||{}).rules||[],l=t.length&&t[0].conditions&&t[0].conditions[0]&&t[0].conditions[0].field_name||i.model.get("defaultValue");i.model.set("defaultValue",l),i.$el.html(s({IsImportEnabled:!!e.use_when_import_excel,IsOpenApiEnabled:!!e.use_when_call_open_api,defaultValue:l})),t.length&&_.each(t[0].conditions||[],function(e,t){1<=t?(i.$el.find(".j-add-item").trigger("click",e.field_name),i.ruleSelects[t+1].trigger("change",e.field_name,{value:e.field_name,name:""},i.ruleSelects[t+1])):i.model.set("defaultValue",e.field_name)}),i.render(),i.model.set("IsImportEnabled",!!e.use_when_import_excel),i.model.set("IsOpenApiEnabled",!!e.use_when_call_open_api)})}),this.listenTo(this.model,"change:IsImportEnabled",this._changBtn),this.listenTo(this.model,"change:IsOpenApiEnabled",this._changBtn)},render:function(){var i=this;i.ruleSelects&&i.ruleSelects[1]&&i.ruleSelects[1].destroy(),i.ruleSelects[1]=new o({$wrap:$(".rule1",i.$el),width:290,zIndex:1e3,options:i.getOptions(),defaultValue:i.model.get("defaultValue"),appendBody:!1}),i.ruleSelects[1].on("change",function(e,t){var l=this;_.filter(i.getRules(),function(e){return e.FieldName!=l.$el.closest(".list-item").attr("data-value")}).find(function(e){return e.FieldName==t.value})?(a.remind(3,$t("请勿设置重复字段")),l.setValue("")):l.$el.closest(".list-item").attr("data-value",t.value)})},getOptions:function(){var t=_.clone(this.model.get("checkList")),e=((this.resValue.unique_rule||{}).pending_rules||{}).rules||[],i=!1;return e.length&&_.map(e[0].conditions,function(l,e){_.map(t,function(e,t){e.value===l.field_name&&e.virtualField&&(i=!0)})}),t=i?t:_.filter(t,function(e,t){return!e.virtualField})},getUniquenessRule:function(t){var l=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/unique_rule/service/get_setting",data:{api_name:this.options.apiName},success:function(e){e.Result&&0==e.Result.StatusCode?(l.resValue=e.Value,t&&t(e.Value.unique_rule)):t&&t({IsImportEnabled:!0,IsOpenApiEnabled:!1,Rules:[]})}},{errorAlertModel:1})},getCheckList:function(l){var i=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/unique_rule/service/field_describe_list",data:{api_name:i.options.apiName},success:function(e){var t;e.Result&&0==e.Result.StatusCode?(t=[{value:0,name:$t("请选择")}],_.map(e.Value.field_describes,function(e){t.push({value:e.api_name,name:e.label,virtualField:e.virtualField,isTitle:e.virtualField})}),i.model.set("defaultValue",t[1].value),i.model.set("checkList",t),l&&l()):a.alert(e.Result.FailureMessage)}},{errorAlertModel:1})},_changBtn:function(){this.model.get("IsImportEnabled")||this.model.get("IsOpenApiEnabled")||$(".rule-box",this.$el).hide(),(this.model.get("IsImportEnabled")||this.model.get("IsOpenApiEnabled"))&&$(".rule-box",this.$el).show()},addItemHandle:function(e,t){var l=$(e.target),i=l.closest(".list-item"),l=l.closest(".choices"),s=this,n=(this.choiceNum++,4==l.find(".rule-item").length?($('<li class="list-item" ><span class="line" style="display:none;" ></span><span class="rule'+this.choiceNum+' rule-item" ></span><span class="del-icon j-del-item"></span></span></li>').insertAfter(l.find(".list-item").eq(l.find(".list-item").length-2)),s.$(".tip").removeClass("hide")):($('<li class="list-item" ><span class="line" ></span><span class="rule'+this.choiceNum+' rule-item" ></span><span class="del-icon j-del-item"></span></span></li>').insertAfter(l.find(".list-item").eq(l.find(".list-item").length-2)),s.$(".tip").addClass("hide")),l.find(".rule"+this.choiceNum));this.ruleSelects[this.choiceNum]=new o({$wrap:n,width:290,zIndex:1e3,options:s.getOptions(),defaultValue:t||"",appendBody:!1}),this.ruleSelects[this.choiceNum].on("change",function(e,t){var l=this;_.filter(s.getRules(),function(e){return e.FieldName!=l.$el.closest(".list-item").attr("data-value")}).find(function(e){return e.FieldName==t.value})?(a.remind(3,$t("请勿设置重复字段")),this.setValue("")):(this.$el.closest(".list-item").attr("data-value",t.value),s.collection.splice(s.collection.indexOf(t.value),1,t.value),$(this.el).parent().closest(".list-item").find(".error-tip")&&""!=t.value&&($(this.el).parent().closest(".list-item").find(".error-tip").remove(),$(this.el).parent().closest(".list-item").find(".g-select-title-wrapper").removeClass("error-item"),$(this.el).parent().closest(".list-item").find(".line").css("padding-bottom","0")))}),5==i.parent().find(".rule-item").length&&l.find("li").eq(l.find("li").length-1).remove(),e.stopPropagation()},delItemHandle:function(e){var e=$(e.target),t=e.closest(".choices");5==t.find(".rule-item").length&&(e.closest(".rule-box").find(".tip").addClass("hide"),t.find(".line").css("display","block"),t.append('<li class="list-item"><span class="line" style="display:none;"></span><span class="add j-add-item"></span></li>')),e.closest(".list-item").remove()},offHandle:function(e){var t=$(e.currentTarget),l=t.attr("data-con");t.hasClass("on")?(t.addClass("off").removeClass("on"),t.find(".core").addClass("off-core"),this.model.set(l,!1)):(t.removeClass("off").addClass("on"),t.find(".core").removeClass("off-core"),this.model.set(l,!0)),e.stopPropagation()},saveHandle:function(e){var t=$(".list-item",this.$el),l=[],i=!0,s=this;this.$el.find(".error-tip")&&this.$el.find(".error-tip").remove(),_.each(t,function(e){var t=$(e).attr("data-value");t&&"0"!==t?($(e).find(".g-select-title-wrapper").removeClass("error-item"),l.push(t)):$(e).find(".add").length||($(e).find(".g-select-title-wrapper").addClass("error-item"),$(e).append('<div class="error-tip">'+$t("请选择字段")+"</div>"),$(e).find(".line").css("padding-bottom","34px"),i=!1)}),i&&s.judgeRuleBox(function(){s.setRuleSettings()})},setRuleSettings:function(){var t=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/unique_rule/service/save",data:{unique_rule:{describe_api_name:t.options.apiName,use_when_import_excel:t.model.get("IsImportEnabled"),use_when_call_open_api:t.model.get("IsOpenApiEnabled"),pending_rules:{version:((t.resValue.unique_rule||{}).pending_rules||{}).version||0,rules:t.model.get("RuleSettings")},version:(t.resValue.unique_rule||{}).version||0}},success:function(e){e.Result&&0==e.Result.StatusCode?(t.resValue=e.Value,a.remind(1,$t("设置成功！"))):a.remind(3,e.Result.FailureMessage)}},{errorAlertModel:1})},judgeRuleBox:function(e){var t=this.$el.find(".check-con .check-btn"),t=(this.model.set("IsImportEnabled",!!$(t[0]).hasClass("on")),this.model.set("IsOpenApiEnabled",!!$(t[1]).hasClass("on")),this.formatRules(this.getRules()));this.model.set("RuleSettings",t),t&&e&&e(t)},getRules:function(){var e=this.$el.find(".choices .list-item"),t=[];return _.each(e,function(e){$(e).attr("data-value")&&t.push({FieldName:$(e).attr("data-value")})}),t},formatRules:function(e){var t=[];return _.map(e,function(e){t.push({connector:"AND",field_name:e.FieldName,field_value:"PRECISE",mapping_field:""})}),[{connector:"OR",conditions:t}]},destroy:function(){this.undelegateEvents(),_.each(this.ruleSelects,function(e){e&&e.destory&&e.destory()})}});l.exports=e});
define("crm-setting/sysobject/component/onlyone/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="onlyone-box"> <div class="content crm-scroll"> <div class="box-intro"> <p>' + ((__t = $t("什么是唯一性规则")) == null ? "" : __t) + "</p> <p>" + ((__t = $t("用于判断业务记录是否唯一的准则例如当2条以上客户记录的姓名和电话的值完全相同则为重复记录。")) == null ? "" : __t) + '</p></div> <div class="check-box"> <div class="check-title">' + ((__t = $t("唯一性规则可应用于以下业务场景如需请开启")) == null ? "" : __t) + '</div> <div class="check-con"> <div class="excal-check"> <span data-con="IsImportEnabled" class="check-btn ' + ((__t = IsImportEnabled ? "on" : "off") == null ? "" : __t) + ' "><i class="core ' + ((__t = IsImportEnabled ? "" : "off-core") == null ? "" : __t) + '"></i></span> <span class="check-tip">' + ((__t = $t("从外部导入excel时")) == null ? "" : __t) + '</span> </div> <div class="open-check"> <span data-con="IsOpenApiEnabled" class="check-btn ' + ((__t = IsOpenApiEnabled ? "on" : "off") == null ? "" : __t) + ' "><i class="core ' + ((__t = IsOpenApiEnabled ? "" : "off-core") == null ? "" : __t) + '"></i></span> <span class="check-tip">' + ((__t = $t("调用OpenAPI时")) == null ? "" : __t) + '</span> </div> </div> </div> <div class="rule-box"> <div class="check-title">' + ((__t = $t("请设置唯一性规则")) == null ? "" : __t) + '</div> <div class="rule-con"> <span style="display:inline-block;margin-top:-20px;height:100%">' + ((__t = $t("当")) == null ? "" : __t) + '</span> <span class="rule-items"> <div class="and"><div class="text">' + ((__t = $t("且")) == null ? "" : __t) + '(AND)</div></div> <ul class="choices"> <li class="list-item" data-value="' + ((__t = defaultValue) == null ? "" : __t) + '"><span class="line" ></span><span class="rule1 rule-item" data-con="1"></span></li> <li class="list-item"><span class="line" style="display:none;" ></span><span class="add j-add-item" data-con="3"></span></li> </ul> </span> <span style="display:inline-block;margin-left:40px;margin-top:-9px;height:100%">' + ((__t = $t("的值完全相同时业务记录出现重复")) == null ? "" : __t) + '</span> </div> <div class="tip hide">' + ((__t = $t("最多支持设置5个字段")) == null ? "" : __t) + '</div> </div> </div> <div class="btns"> <span class="b-g-btn btn-ok j-ok">' + ((__t = $t("保存")) == null ? "" : __t) + '</span> <!--<span class="reset-btn j-reset">恢复默认</span>--> </div> </div>';
        }
        return __p;
    };
});
function asyncGeneratorStep(e,t,a,r,n,s,i){try{var o=e[s](i),c=o.value}catch(e){return void a(e)}o.done?t(c):Promise.resolve(c).then(r,n)}function _asyncToGenerator(o){return function(){var e=this,i=arguments;return new Promise(function(t,a){var r=o.apply(e,i);function n(e){asyncGeneratorStep(r,t,a,n,s,"next",e)}function s(e){asyncGeneratorStep(r,t,a,n,s,"throw",e)}n(void 0)})}}define("crm-setting/sysobject/component/repeat_clue/repeat_clue",["base-modules/utils","./template/tpl-html"],function(n,e,t){n("base-modules/utils");var s=n("./template/tpl-html"),a=Backbone.View.extend({choiceNum:1,ruleSelects:[],options:{pages:[{id:"repeat_data_process_rule",className:"page-repeat-data-process-rule"},{id:"repeat_rule",className:"page-repeat-rule"}]},events:{"click .menu-tabs li":"tabsHandle"},initialize:function(a){var r=this;return _asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=r,e.next=3,t.getGrayByServerForLeads("graySfaImportBehaviorActiveRecords");case 3:t=e.sent,r.cachePages=[],r.firstLoaded=!1,r.$el=a.el,r.$el.html(s({isGrayBehaviorActiveRecords:t||!1})),r.renderPage(0);case 9:case"end":return e.stop()}},e)}))()},getGrayByServerForLeads:function(){var r=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"LeadsPoolObj";return new Promise(function(t,a){r||a();try{CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit",data:{api_name:e},success:function(e){0===e.Result.StatusCode?t(null==(e=e.Value)?void 0:e[r]):t(!1)}},{errorAlertModel:1})}catch(e){a()}})},tabsHandle:function(e){this.firstLoaded&&(e=Math.floor($(e.target).index()/2),this.$el.find(".menu-tabs li").removeClass("active").eq(e).addClass("active"),this.$el.find(".page-list li").removeClass("active").eq(e).addClass("active"),this.renderPage(e))},renderPage:function(t){var a=this,r=this.options.pages[t],e=(a.cachePages[t]&&a.cachePages[t].show(),[".",r.id,r.id].join("/"));n.async(e,function(e){a.firstLoaded=!0;e=new e({wrapper:"."+r.className});e.show(),a.cachePages[t]=e})},destroy:function(){this.undelegateEvents()}});t.exports=a});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/filters/filters",["crm-modules/action/field/field","crm-modules/common/filtergroup/filtergroup","./helper","crm-modules/common/util"],function(e,t,r){var i=e("crm-modules/action/field/field").C.Base,o=e("crm-modules/common/filtergroup/filtergroup"),s=e("./helper"),n=e("crm-modules/common/util");r.exports=i.extend({render:function(){this.comps={},this.renderFilters()},renderFilters:function(){var e=this.get("data").filters;this.comps.filters=new o({$wrapper:this.$el,title:$t("且(AND)"),width:750,AND_MAX:10,OR_MIN:0,addBtnName:$t("新增或关系"),apiname:"LeadsObj",OR_KEY:"conditions",selectone_multiple:!0,datetimeOprateMore:!0,defaultValue:e||[],filterType:["group","url","time","image","file_attachment","percentile","signature","true_or_false","employee_many","department_many","html_rich_text","object_reference_many"],filterApiname:["leads_status","resale_count","transform_time","assigner_id","assigned_time","expire_time","remaining_time","out_tenant_id","out_owner","last_modified_by","owner","is_remind_recycling","new_opportunity_id","opportunity_id","contact_id","account_id","returned_time","remind_days"],helper:s})},getValue:function(){return this.hideError(),this.comps.filters.valid()?JSON.parse(this.comps.filters.getValue()):(this.showError(),null)},showError:function(){n.showErrmsg(this.$el,$t("请填写筛选值!"))},hideError:function(){n.hideErrmsg(this.$el)},destroy:function(){for(var e in i.prototype.destroy.apply(this),this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/filters/helper",["crm-modules/common/filtergroup/filtergroup"],function(e,a,t){var u=e("crm-modules/common/filtergroup/filtergroup").helper;return _.extend({},u,{compare:[{value:1,name:$t("等于"),value1:"EQ"},{value:2,name:$t("不等于"),value1:"N"},{value:3,name:$t("大于"),value1:"GT"},{value:4,name:$t("大于等于"),value1:"GTE"},{value:5,name:$t("小于"),value1:"LT"},{value:6,name:$t("小于等于"),value1:"LTE"},{value:7,name:$t("包含"),value1:"LIKE"},{value:8,name:$t("不包含"),value1:"NLIKE"},{value:9,name:$t("为空"),value1:"IS"},{value:10,name:$t("不为空"),value1:"ISN"},{value:11,name:$t("早于"),value1:"LT"},{value:12,name:$t("晚于"),value1:"GT"},{value:13,name:$t("属于"),value1:"IN"},{value:14,name:$t("不属于"),value1:"NIN"},{value:17,name:$t("时间段"),value1:"BETWEEN"},{},{value:19,name:$t("自定义"),value1:"CUSTOM"},{value:21,name:$t("相同"),value1:"SAME"},{value:22,name:$t("不相同"),value1:"NSAME"},{value:23,name:$t("开始于"),value1:"STARTWITH"},{value:24,name:$t("结束于"),value1:"ENDWITH"},{value:25,name:$t("属于"),value1:"HASANYOF"},{value:26,name:$t("不属于"),value1:"NHASANYOF"},{value:27,name:$t("过去N天内(不含当天)"),value1:"LTE",optionTip:$t("前N天(不含当天)，比如当前是5月8号，前3天是指5.7、5.6、5.5")},{value:28,name:$t("未来N天内(不含当天)"),value1:"GTE",optionTip:$t("后N天(不含当天)，比如当前是5月8号，后3天是指5.9、5.10、5.11")},{value:29,name:$t("过去N月内(不含当月)"),value1:"LTE",optionTip:$t("前N月：指从当天所在月的前一个月开始，向前N个月；前1月=上月；前2个月=上月+上上月")},{value:30,name:$t("未来N月内(不含当月)'"),value1:"GTE",optionTip:$t("后N月：指从当天所在月的下个月开始，向后N个月；后1月=下月；后2月=下月+下下月")},{value:31,name:$t("N天前"),value1:"LT",optionTip:$t("N天前(当天算1天)，比如当前日期是0508，3天前，是指0505的23:59:59之前")},{value:32,name:$t("N天后"),value1:"GT",optionTip:$t("N天后(当天算1天)，比如当前日期是0508，3天后，是指0511的00:00:00之后")},{value:33,name:$t("N周前"),value1:"LT",optionTip:$t("N周前(当周算1周)，比如当前是2020年第7周，3周前，是指2020年第4周周日23:59:59之前")},{value:34,name:$t("N周后"),value1:"GT",optionTip:$t("N周后(当周算1周)，比如当前是2020年第7周，3周后，是指2020年第10周周一00:00:00之后")},{value:35,name:$t("过去N周内(不含当周)"),value1:"LTE",optionTip:$t("crm_filter_tips13")},{value:36,name:$t("未来N周内(不含当周)"),value1:"GTE",optionTip:$t("crm_filter_tips14")},,{value:37,value1:"LTEO",name:$t("过去N天内(含当天)"),optionTip:$t("crm_filter_tips15")},{value:38,name:$t("未来N天内(含当天)"),value1:"GTEO",optionTip:$t("crm_filter_tips16")},{value:39,name:$t("过去N周内(含当周)"),value1:"LTEO",optionTip:$t("crm_filter_tips17")},{value:40,name:$t("未来N周内(含当周)"),value1:"GTEO",optionTip:$t("crm_filter_tips18")},{value:41,name:$t("过去N月内(含当月)"),value1:"LTEO",optionTip:$t("crm_filter_tips19")},{value:42,name:$t("未来N月内(含当月)"),value1:"GTEO",optionTip:$t("crm_filter_tips20")}],getCompareNums:function(e){var a=u.getCompareNums(e);switch(e){case"date_time":a=[1,2,11,12,9,10,17,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42];case"date":a=[1,2,11,12,9,10,17,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42]}return a},getTypeByCompare:function(e,a,t){a=u.getTypeByCompare(e,a,t);return-1!=[27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42].indexOf(e.value)?"datetimeleadstext":a},moreTimeOpratorGet:function(){return[35,36,37,38,39,40,41,42]},moreTimeOpratorGetValue:function(e,a){switch(e){case 37:case 38:return["day",a];case 41:case 42:return["month",a];case 35:case 36:case 39:case 40:return["week",a]}}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/helper",["crm-modules/common/filtergroup/setting_helper"],function(e,a,t){e=e("crm-modules/common/filtergroup/setting_helper");t.exports=$.extend(!0,{},e,{compare:[{value:1,name:$t("等于"),value1:"EQ"},{value:2,name:$t("不等于"),value1:"N"},{value:3,name:$t("大于"),value1:"GT"},{value:4,name:$t("大于等于"),value1:"GTE"},{value:5,name:$t("小于"),value1:"LT"},{value:6,name:$t("小于等于"),value1:"LTE"},{value:7,name:$t("包含"),value1:"LIKE"},{value:8,name:$t("不包含"),value1:"NLIKE"},{value:9,name:$t("为空"),value1:"IS"},{value:10,name:$t("不为空"),value1:"ISN"},{value:11,name:$t("早于"),value1:"LT"},{value:12,name:$t("晚于"),value1:"GT"},{value:13,name:$t("属于"),value1:"IN"},{value:14,name:$t("不属于"),value1:"NIN"},{value:17,name:$t("时间段"),value1:"BETWEEN"},{value:19,name:$t("自定义"),value1:"CUSTOM"}],getCompare:function(e){var a,t=this;switch(e){case"text":case"long_text":case"phone_number":case"department":case"email":case"url":case"auto_number":case"object_reference":a=[1,2,7,8,9,10];break;case"date":case"date_time":case"time":a=[1,11,12,17];break;case"select_one":case"select_many":case"employee":case"record_type":case"country":case"province":case"city":case"district":a=[1,2,13,14,9,10];break;case"count":case"number":case"currency":case"percentile":a=[1,2,3,4,5,6];break;case"master_detail":a=[1,2,7,8,9,10];break;case"location":a=[1,2,7,8];break;default:a=[1]}return _.map(a,function(a){return _.find(t.compare,function(e){return e.value===a})})}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/add/add",["crm-modules/common/util","crm-assets/widget/dialog/dialog","./template/tpl-html","./template/collect-html","crm-modules/common/filtergroup/filtergroup","./helper"],function(t,e,n){var l=t("crm-modules/common/util"),s=t("crm-assets/widget/dialog/dialog"),o=t("./template/tpl-html"),i=t("./template/collect-html"),a=t("crm-modules/common/filtergroup/filtergroup"),c=t("./helper"),o=s.extend({attrs:{width:840,className:"repeat-process-type-dialog",title:$t("新增处理规则"),showBtns:!0,showScroll:!0,content:o()},events:{"click .b-g-btn":"onSave","click .b-g-btn-cancel":"onCancel"},render:function(){s.prototype.render.apply(this,arguments),this.$el=this.element,this.leadsObj_fields=this.defaults.leadsObj_fields,this.comps={},this.data=this.defaults.data||{},this.mode_rule=this.data.mode_rule||{},this.trigger_actions=this.defaults.trigger_actions||[],this.set_repeat_rule=this.defaults.set_repeat_rule,this.isRender=_.some(this.trigger_actions||[],function(t){return"ADD"==t||"SMART_FORM"==t||"OPEN_API"==t||"MARKETING_ADD"==t}),this.isRender=this.set_repeat_rule&&this.isRender,this.renderScope(),this.renderRadioBox(),"ADD_INTERACTIVE_RECORDS"==this.data.mode_action&&this.set_repeat_rule&&this.isRender&&this.renderCheckBox(),this.mode_rule.extend_rules&&this.mode_rule.extend_rules.auto_update&&this.isRender&&(this.renderMulRadioBox(),this.renderMulChoiceRadioBox(),this.renderNonMulRadioBox())},renderScope:function(){var t=this.data.filters;this.comps.scope=new a({$wrapper:this.$el.find(".scope"),title:$t("且(AND)"),width:750,AND_MAX:20,OR_MIN:0,addBtnName:$t("新增或关系"),apiname:"LeadsObj",OR_KEY:"conditions",selectone_multiple:!0,defaultValue:t||[],filterType:["date","time","date_time","true_or_false","employee","location","area","image","auto_number","master_detail","payment","sign_in","quote","percentile","formula","employee_many","department_many","html_rich_text","object_reference_many"],filterApiname:["biz_status","life_status","leads_status","new_opportunity_id","opportunity_id","account_id","contact_id","lock_status","remind_days","resale_count","out_tenant_id"],helper:c})},renderRadioBox:function(){var t=this,e=this.data.mode_action||"COLLECT";this.comps.radiobox=FxUI.create({wrapper:this.$el.find(".actions")[0],template:'<div>\n\t\t\t\t\t\t\t<fx-radio-group v-model="value" @change="change">\n\t\t\t\t\t\t\t\t<fx-radio label="COLLECT">'.concat($t("归集到已有线索"),'\n\t\t\t\t\t\t\t\t\t<fx-tooltip effect="dark" content="').concat($t("如果只存在重复的客户或联系人，则只打重复标记。"),'" placement="top">\n\t\t\t\t\t\t\t\t\t\t<i class="help-btn"></i>\n\t\t\t\t\t\t\t\t\t</fx-tooltip>\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio v-if="isRender" label="ADD_INTERACTIVE_RECORDS">').concat($t("已有线索下生成行为记录"),'\n\t\t\t\t\t\t\t\t\t<fx-tooltip v-if="graySfaImportBehaviorActiveRecords" effect="dark" placement="top">\n\t\t\t\t\t\t\t\t\t\t<div slot="content">').concat($t("sfa.repeat_process_type.add_interactive_recordsOne"),"</br>").concat($t("sfa.repeat_process_type.add_interactive_recordsTwo"),'</div>\n\t\t\t\t\t\t\t\t\t\t<i class="help-btn"></i>\n\t\t\t\t\t\t\t\t\t</fx-tooltip>\n\t\t\t\t\t\t\t\t\t<fx-tooltip effect="dark" v-else content="').concat($t("该功能仅对智能表单或Openapi触发动作生效"),'" placement="top">\n\t\t\t\t\t\t\t\t\t\t<i class="help-btn"></i>\n\t\t\t\t\t\t\t\t\t</fx-tooltip>\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="CUSTOM_FUNCTION">').concat($t("执行自定义函数"),'\n\t\t\t\t\t\t\t\t\t<div v-if="custom_function" class="custom-function-name" @click="clickCustomFunction">-{{custom_function.function_name}}<i class="el-icon-edit"></i></div>\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t\t<div class="tips" v-if="showTip">\n\t\t\t\t\t\t\t<span v-if="graySfaImportBehaviorActiveRecords">').concat($t("sfa.repeat_process_type.radiobox.bottom"),"</span>\n\t\t\t\t\t\t\t<span v-else>").concat($t("仅支持新建、智能表单、Openapi和营销通新建"),"</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>"),data:function(){return{value:e,custom_function:null,isRender:t.isRender,showTip:!1,graySfaImportBehaviorActiveRecords:!1}},mounted:function(){var e=this;e.getGrayByServerForLeads("graySfaImportBehaviorActiveRecords").then(function(t){e.graySfaImportBehaviorActiveRecords=t})},methods:{getGrayByServerForLeads:function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"LeadsPoolObj";return new Promise(function(e,n){o||n();try{CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit",data:{api_name:t},success:function(t){0===t.Result.StatusCode?e(null==(t=t.Value)?void 0:t[o]):e(!1)}},{errorAlertModel:1})}catch(t){n()}})},change:function(){"COLLECT"===this.value?(t.renderCollect(this.value),this.showTip=!1,this.custom_function=null):"ADD_INTERACTIVE_RECORDS"===this.value?(t.renderCollect(this.value),t.renderCheckBox(),t.mode_rule.extend_rules&&t.mode_rule.extend_rules.auto_update&&(t.renderMulRadioBox(),t.renderMulChoiceRadioBox(),t.renderNonMulRadioBox()),this.custom_function=null,this.showTip=!0):"CUSTOM_FUNCTION"===this.value&&(this.showTip=!1,t.renderCustomFunction(),this.custom_function={function_name:$t("添加")})},clickCustomFunction:function(){t.renderCustomFunction(this.custom_function.function_api_name)}},created:function(){this.showTip=!1,"COLLECT"===this.value||"ADD_INTERACTIVE_RECORDS"===this.value?(t.renderCollect(),this.showTip="ADD_INTERACTIVE_RECORDS"===this.value&&t.isRender&&t.set_repeat_rule):"CUSTOM_FUNCTION"===this.value&&(this.showTip=!1,this.custom_function=t.mode_rule.custom_function)}})},renderMulRadioBox:function(){var t=this;this.comps["collect.multext"]=FxUI.create({wrapper:this.$el.find(".actions-container")[0],template:'<div class="multext">\n\t\t\t\t\t\t\t<h3 class="field-title">'.concat($t("多行文本字段类型更新方式"),'</h3>\n\t\t\t\t\t\t\t<fx-radio-group v-model="value" @change="change">\n\t\t\t\t\t\t\t\t<fx-radio label="SKIP">').concat($t("跳过"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="COVER">').concat($t("sysobject.repeat_case_setting_add_new_rules_cover_all"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="NULL_VALUE_COVER">').concat($t("sysobject.repeat_case_setting_add_new_rules_cover_empty"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="SPLICING">').concat($t("拼接"),"\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t</div>"),data:function(){return{value:this.getDefaultValue()}},methods:{change:function(){},getDefaultValue:function(){return(t.mode_rule.extend_rules||{}).multi_line_update_mode}},created:function(){}})},renderMulChoiceRadioBox:function(){var t=this;this.comps["collect.mulchoice"]=FxUI.create({wrapper:this.$el.find(".actions-container")[0],template:'<div class="multext">\n\t\t\t\t\t\t\t<h3 class="field-title">'.concat($t("多选类型字段更新方式"),'</h3>\n\t\t\t\t\t\t\t<fx-radio-group v-model="value" @change="change">\n\t\t\t\t\t\t\t\t<fx-radio label="SKIP">').concat($t("跳过"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="COVER">').concat($t("sysobject.repeat_case_setting_add_new_rules_cover_all"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="NULL_VALUE_COVER">').concat($t("sysobject.repeat_case_setting_add_new_rules_cover_empty"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="SPLICING">').concat($t("拼接"),"\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t</div>"),data:function(){return{value:this.getDefaultValue()}},methods:{change:function(){},getDefaultValue:function(){return(t.mode_rule.extend_rules||{}).many_select_update_mode}}})},renderNonMulRadioBox:function(){var t=this;this.comps["collect.non-multext"]=FxUI.create({wrapper:this.$el.find(".actions-container")[0],template:'<div class="multext">\n\t\t\t\t\t\t\t<h3 class="field-title">'.concat($t("其他类型字段更新方式"),'</h3>\n\t\t\t\t\t\t\t<fx-radio-group v-model="value" @change="change">\n\t\t\t\t\t\t\t\t<fx-radio label="SKIP">').concat($t("跳过"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="COVER">').concat($t("sysobject.repeat_case_setting_add_new_rules_cover_all"),'\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t\t<fx-radio label="NULL_VALUE_COVER">').concat($t("sysobject.repeat_case_setting_add_new_rules_cover_empty"),"\n\t\t\t\t\t\t\t\t</fx-radio>\n\t\t\t\t\t\t\t</fx-radio-group>\n\t\t\t\t\t\t</div>"),data:function(){return{value:this.getDefaultValue()}},methods:{change:function(){},getDefaultValue:function(){return(t.mode_rule.extend_rules||{}).other_update_mode}},created:function(){}})},renderCheckBox:function(){var t=this;this.comps["collect.record"]=FxUI.create({wrapper:t.$el.find(".actions-container")[0],template:'<div class="chk-wrapper">\n\t\t\t\t\t\t\t\t<fx-checkbox v-model="checked" @change="change"><span style="color:#606266">'.concat($t("自动更新已有线索"),'\n\t\t\t\t\t\t\t\t\t\t<fx-tooltip effect="dark" placement="top">\n\t\t\t\t\t\t\t\t\t\t\t<div slot="content">').concat($t("1.更新方式说明"),"<br/>").concat($t("sysobject.repeat_case_setting_add_new_rules_cover_all_tips"),"<br/>").concat($t("sysobject.repeat_case_setting_add_new_rules_cover_empty_tips"),"<br/>").concat($t("拼接:新数据拼接到原数据后"),"<br/>\n\t\t\t\t\t\t\t\t\t\t\t").concat($t("跳过:不更新"),"<br/>").concat($t("2.字段类型说明"),"<br/>").concat($t("多选类型字段类型：包括多选、查找关联（多选）、人员（多选）、部门（多选）字段类型"),"<br/>").concat($t("拼接:新数据拼接到原数据后"),"<br/>\n\t\t\t\t\t\t\t\t\t\t\t").concat($t("图片、附件字段类型默认走拼接"),'</div>\t\n\t\t\t\t\t\t\t\t\t\t\t<i class="help-btn"></i>\n\t\t\t\t\t\t\t\t\t\t</fx-tooltip>\n\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t</fx-checkbox>\n\t\t\t\t\t\t   </div>'),data:function(){return{checked:this.getDefaultValue()}},methods:{change:function(){this.checked?(t.renderMulRadioBox(),t.renderMulChoiceRadioBox(),t.renderNonMulRadioBox()):(t.$el.find(".multext").html(""),t.comps["collect.multext"]=null,t.comps["collect.non-multext"]=null)},getDefaultValue:function(){return(t.mode_rule.extend_rules||{}).auto_update||!1}},created:function(){}})},renderCollect:function(t){var e=this;this.$el.find(".actions-container").html(i({mode_action:t||this.data.mode_action})),this.comps["collect.field"]=FxUI.create({wrapper:this.$el.find(".actions-container .field")[0],template:'<fx-select\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t></fx-select>',data:function(){return{value:this.getDefaultValue(),options:this.getOptions()}},methods:{getDefaultValue:function(){return(e.mode_rule.selection_strategy||{}).field_name||"create_time"},getOptions:function(){return _.values(e.leadsObj_fields).filter(function(t){return-1!=["date","time","date_time"].indexOf(t.type)&&-1==["assigned_time","expire_time","owner_change_time","returned_time"].indexOf(t.api_name)}).map(function(t){return{label:t.label,value:t.api_name}})}}}),this.comps["collect.date"]=FxUI.create({wrapper:this.$el.find(".actions-container .date")[0],template:'<fx-select\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t></fx-select>',data:function(){return{value:this.getDefaultValue(),options:[{label:$t("最早"),value:"ASC"},{label:$t("最晚"),value:"DESC"}]}},methods:{getDefaultValue:function(){return(e.mode_rule.selection_strategy||{}).sort_direction||"ASC"}}})},renderCustomFunction:function(e){var n=this;this.$el.find(".actions-container").html(""),t.async("paas-function/sdk.js",function(t){t.getFunction({name_space:"button",return_type:"void",object_api_name:"LeadsObj",disable_binding:!1,disable_namespace:!0,disable_returntype:!1,checked_api_name:e}).then(function(t){n.comps.radiobox.custom_function={function_api_name:t.function.api_name,binding_object_api_name:t.function.binding_object_api_name,function_name:t.function.function_name}})})},isAlertCreateTimeInterval:function(t){if(-1!=t.indexOf("create_time_interval")){t=JSON.parse(t);for(var e=0;e<t.length;e++)for(var n=t[e].conditions,o=0;o<n.length;o++){var i=n[o];if("create_time_interval"===i.field_name&&1e4<i.field_values[0])return!0}}return!1},onSave:function(){var t=this.comps.scope.getValue(),e=this.comps.radiobox,n=this.comps["collect.record"],o=this.comps["collect.field"],i=this.comps["collect.date"],a=this.comps["collect.non-multext"],c=this.comps["collect.multext"],r=this.comps["collect.mulchoice"];this.comps["collect.overwrite.field"];if(this.hideError(),!this.comps.scope.valid())return this.showError(),null;if(this.isAlertCreateTimeInterval(t))return l.showErrmsg(this.$el.find(".scope"),$t("时间间隔最大值不能超过 {{n}}",{n:1e4})),null;t={filters:JSON.parse(this.comps.scope.getValue()),mode_action:e.value,mode_rule:{}};if("COLLECT"===e.value){if(""===o.value)return void l.alert($t("请选择字段名称!"));if(""===i.value)return void l.alert($t("请选择最早或最晚!"));t.mode_rule.selection_strategy={field_name:o.value,sort_direction:i.value}}else if("ADD_INTERACTIVE_RECORDS"===e.value){if(""===o.value)return void l.alert($t("请选择字段名称!"));if(""===i.value)return void l.alert($t("请选择最早或最晚!"));if(c&&null==c.value||r&&null==r.value||a&&null==a.value)return void l.alert($t("请设置自动更新已有线索规则!"));t.mode_rule.selection_strategy={field_name:o.value,sort_direction:i.value},t.mode_rule.extend_rules={auto_update:n.checked,multi_line_update_mode:n.checked?c.value:null,many_select_update_mode:n.checked?r.value:null,other_update_mode:n.checked?a.value:null}}else if("CUSTOM_FUNCTION"===e.value){if(!e.custom_function||!e.custom_function.function_api_name)return void l.alert($t("请选择一个自定义函数!"));t.mode_rule.custom_function={function_api_name:e.custom_function.function_api_name,binding_object_api_name:e.custom_function.binding_object_api_name,function_name:e.custom_function.function_name}}this.trigger("success",t),s.prototype.hide.apply(this,arguments)},onCancel:function(){s.prototype.hide.apply(this,arguments)},showError:function(){l.showErrmsg(this.$el.find(".scope"),"$t('请填写筛选值')!")},hideError:function(){l.hideErrmsg(this.$el.find(".scope"))},destroy:function(){for(var t in s.prototype.destroy.apply(this),this.comps)this.comps[t].destroy&&this.comps[t].destroy();this.comps=null}});n.exports=o});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/add/helper",["crm-modules/common/filtergroup/setting_helper"],function(e,a,t){e=e("crm-modules/common/filtergroup/setting_helper");t.exports=$.extend(!0,{},e,{compare:[{value:1,name:$t("等于"),value1:"EQ"},{value:2,name:$t("不等于"),value1:"N"},{value:3,name:$t("大于"),value1:"GT"},{value:4,name:$t("大于等于"),value1:"GTE"},{value:5,name:$t("小于"),value1:"LT"},{value:6,name:$t("小于等于"),value1:"LTE"},{value:7,name:$t("包含"),value1:"LIKE"},{value:8,name:$t("不包含"),value1:"NLIKE"},{value:9,name:$t("为空"),value1:"IS"},{value:10,name:$t("不为空"),value1:"ISN"},{value:11,name:$t("早于"),value1:"LT"},{value:12,name:$t("晚于"),value1:"GT"},{value:13,name:$t("属于"),value1:"IN"},{value:14,name:$t("不属于"),value1:"NIN"},{value:17,name:$t("时间段"),value1:"BETWEEN"},{value:19,name:$t("自定义"),value1:"CUSTOM"},{value:21,name:$t("相同"),value1:"SAME"},{value:22,name:$t("不相同"),value1:"NSAME"}],getCompare:function(e){var a,t=this;switch(e){case"number":case"currency":a=[3,4,5,6];break;default:a=[21,22]}return _.map(a,function(a){return _.find(t.compare,function(e){return e.value===a})})},getNameByValue:function(e,a){e=_.findWhere(this.compare,{value1:e});return e&&e.name},getTypeByCompare:function(e,a,t){if(-1!=[21,22].indexOf(e.value))return"diasbled"},formatFields:function(e,a){return e.create_time_interval={api_name:"create_time_interval",label:$t("创建时间间隔（小时）"),type:"number"},e}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/add/template/collect-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="actions-collect"> ' + ((__t = mode_action == "ADD_INTERACTIVE_RECORDS" ? $t("当有多条时, 行为数据落到") : $t("当有多条时, 归集到")) == null ? "" : __t) + '<div class="field"></div> <div class="date"></div>' + ((__t = $t("的线索")) == null ? "" : __t) + ' </div> <div class="update-field-checkbox"></div> <div class="overwrite-field-checkbox"></div>';
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/add/template/custom-function-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += "";
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/add/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="repeat-process-type-add-container"> <div class="filter-container"> <p class="title">1.' + ((__t = $t("筛选条件")) == null ? "" : __t) + '</p> <div class="scope"></div> </div> <ul class="type-container"> <p class="title">2.' + ((__t = $t("处理方式")) == null ? "" : __t) + '</p> <div class="actions"></div> <div class="actions-container"> </div> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/repeat_process_type",["crm-modules/action/field/field","crm-modules/common/util","crm-components/rules_sortable/rules_sortable","./template/tpl-html","./template/process_type_tpl-html","./add/add","./add/helper"],function(e,t,s){var o=e("crm-modules/action/field/field").C.Base,r=(e("crm-modules/common/util"),e("crm-components/rules_sortable/rules_sortable")),a=e("./template/tpl-html"),i=e("./template/process_type_tpl-html"),c=e("./add/add"),n=e("./add/helper");s.exports=o.extend({events:{"click .j-add-btn":"clickAddHandle"},render:function(){var t=this,s=this;this.comps={},this.duplicated_processing_modes_list=this.model.get("data").duplicated_processing_modes||[],this.trigger_actions=this.get("data").trigger_actions||[],this.$el.html(a()),this.getGrayByServerForLeads("graySfaImportBehaviorActiveRecords").then(function(e){s.graySfaImportBehaviorActiveRecords=e,t.renderProcessType()}).catch(function(){s.graySfaImportBehaviorActiveRecords=!1,t.renderProcessType()})},getGrayByServerForLeads:function(){var o=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",e=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"LeadsPoolObj";return new Promise(function(t,s){o||s();try{CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit",data:{api_name:e},success:function(e){0===e.Result.StatusCode?t(null==(e=e.Value)?void 0:e[o]):t(!1)}},{errorAlertModel:1})}catch(e){s()}})},renderProcessType:function(){var a=this;this.comps.rulesSortable=new r({$el:this.$el.find(".repeat-process-type-list"),labelName:$t("优先级"),data:a.duplicated_processing_modes_list,renderRule:function(e,t){return i({scope_content:a.getFilterText(e.filters),type_content:"ADD_INTERACTIVE_RECORDS"==e.mode_action?a.graySfaImportBehaviorActiveRecords?a.getProcessType(e)+"(".concat($t("sfa.repeat_process_type.radiobox.bottom"),")"):a.getProcessType(e)+"(".concat($t("仅支持新建、智能表单、Openapi和营销通新建"),")"):a.getProcessType(e)})},actions:[{action:"edit",name:$t("编辑")},{action:"delete",name:$t("删除")}]}),this.comps.rulesSortable.on("action",function(e,t,s,o){var r=this;"delete"===e?(t.splice(o,1),this.renderLabel(),this.renderRules(),a.updateAddBtnStatus()):"edit"===e&&(t=a.getRepeatRules(),this.comps.dialog=new c({leadsObj_fields:a.model.get("leadsObj_fields"),data:s,trigger_actions:a.get("forms").trigger_actions.getValue(),set_repeat_rule:t}),this.comps.dialog.show(),this.comps.dialog.on("success",function(e){a.duplicated_processing_modes_list.splice(o,1,e),r.renderLabel(),r.renderRules()}))})},getRepeatRules:function(){return this.get("forms").set_repeat_rule.comps.LeadsObj.value||""},clickAddHandle:function(){var t=this,e=t.getRepeatRules();this.comps.dialog=new c({leadsObj_fields:this.model.get("leadsObj_fields"),trigger_actions:t.get("forms").trigger_actions.getValue(),set_repeat_rule:e}),this.comps.dialog.show(),this.comps.dialog.on("success",function(e){t.duplicated_processing_modes_list.push(e),t.comps.rulesSortable.renderLabel(),t.comps.rulesSortable.renderRules(),t.updateAddBtnStatus()})},updateAddBtnStatus:function(){5<=this.duplicated_processing_modes_list.length?this.$el.find(".j-add-btn").hide():this.$el.find(".j-add-btn").show()},getFilterText:function(e){var r=this,s=[];return e.length?(_.each(e,function(e,t){var o=[],e=e.conditions;_.each(e,function(e,t){var s=CRM.get("fields.LeadsObj")[e.field_name];s?(r.formatOperator(s,e.operator),-1!=["SAME","NSAME"].indexOf(e.operator)?o.push("“".concat(s.label,"” ").concat(e.operator_name)):o.push("“".concat(s.label,"” ").concat(e.operator_name," ").concat(e.field_values||"--"))):"create_time_interval"===e.field_name&&o.push("“".concat($t("创建时间间隔（小时）"),"” ").concat(e.operator_name," ").concat(e.field_values||"--"))}),s.push((0==t?"<p>":"<p>".concat($t("或")," ")).concat(o.join(" ".concat($t("且")," ")),";</p>"))}),s.join("")):$t("全部线索")},formatOperator:function(e,t){t=_.findWhere(n.compare,{value1:t});return t&&t.name},getProcessType:function(e){switch(e.mode_action){case"COLLECT":return $t("归集到已有线索");case"ADD_INTERACTIVE_RECORDS":return $t("已有线索下生成行为记录");case"CUSTOM_FUNCTION":return"".concat($t("执行自定义函数"),"-").concat(e.mode_rule.custom_function.function_name)}},getValue:function(){return this.hideError(),this.duplicated_processing_modes_list},destroy:function(){for(var e in o.prototype.destroy.apply(this),this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/template/process_type_tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="scope"> <label>' + ((__t = $t("筛选条件")) == null ? "" : __t) + ':</label> <div class="scope-content">' + ((__t = obj.scope_content) == null ? "" : __t) + '</div> </div> <div class="type"> <label>' + ((__t = $t("处理方式")) == null ? "" : __t) + ':</label> <div class="type-content">' + ((__t = obj.type_content) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/repeat_process_type/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="repeat-process-type-container"> <div class="repeat-process-type-list"> </div> <a href="javascript:;" class="repeat-process-type-btn j-add-btn"> <i class="el-icon-plus icon-add"></i><span>' + ((__t = $t("添加处理规则")) == null ? "" : __t) + "</span> </a> </div>";
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/set_repeat_rule/set_repeat_rule",["crm-modules/action/field/field","crm-modules/common/util","./template/tpl-html","crm-modules/action/field/field","crm-setting/sysobject/component/repeat_clue/repeat_rule/add/view","crm-setting/sysobject/component/repeat_clue/repeat_rule/add/model","./template/dialog_tpl-html"],function(e,t,a){var i=e("crm-modules/action/field/field").C.Base,n=e("crm-modules/common/util"),l=e("./template/tpl-html"),s=e("crm-modules/action/field/field"),o=e("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/view"),c=e("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/model"),r=e("./template/dialog_tpl-html");a.exports=i.extend({options:{list:[{api_name:"LeadsObj",obj_name:$t("线索")},{api_name:"AccountObj",obj_name:$t("客户")},{api_name:"ContactObj",obj_name:$t("联系人")}]},events:{"click .view_rule":"viewRuleHandle","click .clear_rule":"clearRuleHandle","click .add_clue":"addRuleHandle"},render:function(){var e=this;this.comps={},this.data={},this.$el.html(l({list:this.options.list})),this.fetchRulesList(function(){e.renderSelect()})},fetchRulesList:function(e){var i=this;n.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_search_rule_list",data:{search_template:{limit:50,offset:0,table_name:"biz_leads_duplicated_search",where_params:[{operator:"LIKE",field_name:"name",values:[]},{operator:"EQ",field_name:"status",values:[1]}]}},success:function(t){0==t.Result.StatusCode&&(_.each(i.options.list,function(a,e){i.data[a.api_name]=[],_.each(t.Value.duplicated_search_rule_list,function(e,t){a.api_name===e.object_api_name&&i.data[a.api_name].push(e)})}),e)&&e()}})},renderSelect:function(){var e=this,a=this;_.each(this.options.list,function(t){a.comps[t.api_name]&&a.comps[t.api_name].destroy(),a.comps[t.api_name]=FxUI.create({wrapper:e.$el.find(".leadsobj_".concat(t.api_name,"_rule"))[0],template:'<fx-select\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\t@change="change"\n\t\t\t\t\t\t\t\tsize="mini"\n\t\t\t\t\t\t\t></fx-select>',data:function(){return{value:this.getDefaultValue(),options:this.getOptions()}},methods:{getDefaultValue:function(){var e=a.get("data").duplicated_search_rules,e=_.findWhere(e,{object_api_name:t.api_name});return e&&_.findWhere(a.data[t.api_name],{id:e.id})?e.id:""},getOptions:function(){return a.data[t.api_name].map(function(e,t){return{label:e.name,value:e.id,map_full_fields:e.map_full_fields}})},change:function(){a.updateMapRule(),a.updateBtnColor()}}})}),setTimeout(function(){a.updateMapRule(),a.updateBtnColor()})},updateBtnColor:function(){var i=this;_.each(this.options.list,function(e){var t=i.$el.find('.view_rule[data-api_name="'.concat(e.api_name,'"]')),a=i.$el.find('.clear_rule[data-api_name="'.concat(e.api_name,'"]'));i.comps[e.api_name].value?(t.addClass("active"),a.addClass("active")):(t.removeClass("active"),a.removeClass("active"))})},updateMapRule:function(){var i=this;_.each(this.options.list,function(e){var t=i.$el.find('.field_map_rule[data-api_name="'.concat(e.api_name,'"]')),a=i.comps[e.api_name].value;!a||_.findWhere(i.data[e.api_name],{id:a}).map_full_fields?t.hide():t.show()})},viewRuleHandle:function(e){var a=this,e=$(e.target).data("api_name"),e=this.comps[e].value;if(!e)return n.alert($t("sfa.settings.repeat_clue.pleaseWriteRule")),!1;n.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_search_rule",data:{duplicated_search_rule_id:e},success:function(e){var t;0==e.Result.StatusCode&&(e=(t=e.Value.duplicated_search_rule).object_api_name,n.getFieldsByApiName(e).done(function(){a.viewRuleDialog(t)}))}})},viewRuleDialog:function(e){FxUI.MessageBox.alert(r({description:e.description,object_name:_.findWhere(this.options.list,{api_name:e.object_api_name}).obj_name,rule_detail:this.format_rule_detail(e)}),$t("客户重复规则"),{customClass:"view-repeat-rule-continer",dangerouslyUseHTMLString:!0,confirmButtonText:$t("知道了")})},format_rule_detail:function(e){for(var t=[],a=e.usable_rules,i=e.object_api_name,n=0;n<a.length;n++){for(var l=a[n].conditions,s=[],o=0;o<l.length;o++){var c=CRM.get("fields.".concat(i))[l[o].field_name];c&&s.push("“".concat(c.label,"”"))}0==n?t.push("".concat($t("当")," ").concat(s.join(" ".concat($t("sfa.settings.repeat_clue.sfa.settings.repeat_clue.and"))))):(a.length,t.push(";".concat($t("或")," ").concat(s.join(" ".concat($t("sfa.settings.repeat_clue.sfa.settings.repeat_clue.and"))))))}return t.join("")+"".concat($t("的值完全相同时, 业务记录出现重复。"))},clearRuleHandle:function(e){e=$(e.target).data("api_name");this.comps[e].value="",this.updateMapRule(),this.updateBtnColor()},addRuleHandle:function(){var e=this;s.add({Model:s.Model.extend(c),View:s.View.extend(o),record_type:"default__c",show_type:"full",action_type:"add",className:"repeat_rule_dialog",data:{object_api_name:"LeadsObj"},success:function(){e.fetchRulesList(function(){e.renderSelect()})}})},getValue:function(){this.hideError();for(var e=[],t=0;t<this.options.list.length;t++){var a=this.options.list[t],a=this.comps[a.api_name].value;a&&e.push({id:a})}return e.length?e:(this.showError(),!1)},showError:function(){n.showErrmsg(this.$el,$t("请设置查重规则!"))},hideError:function(){n.hideErrmsg(this.$el)},destroy:function(){for(var e in i.prototype.destroy.apply(this),this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null,this.list=null,this.data=null}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/set_repeat_rule/template/dialog_tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="repeat-rule-row"> <label >' + ((__t = $t("规则描述")) == null ? "" : __t) + "</label> <span>" + ((__t = obj.rule_description) == null ? "" : __t) + '</span> </div> <div class="repeat-rule-row"> <label >' + ((__t = $t("所属对象")) == null ? "" : __t) + "</label> <span>" + ((__t = obj.object_name) == null ? "" : __t) + '</span> </div> <div class="repeat-rule-row"> <label >' + ((__t = $t("规则明细")) == null ? "" : __t) + "</label> <span>" + ((__t = obj.rule_detail) == null ? "" : __t) + "</span> </div>";
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/set_repeat_rule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="set_repeat_rule-container"> <ul class="rule_list"> ';
            for (var i = 0; i < obj.list.length; i++) {
                __p += ' <li class="rule_item"> ';
                var name = $t("将线索与{{name}}查重", {
                    name: obj.list[i].obj_name
                });
                __p += ' <label class="rule_label">' + ((__t = name) == null ? "" : __t) + '</label> <div class="rule_select leadsobj_' + ((__t = obj.list[i].api_name) == null ? "" : __t) + '_rule"></div> <a href="javascript:;" class="view_rule" data-api_name="' + ((__t = obj.list[i].api_name) == null ? "" : __t) + '">' + ((__t = $t("查看规则")) == null ? "" : __t) + '</a> <a href="javascript:;" class="clear_rule" data-api_name="' + ((__t = obj.list[i].api_name) == null ? "" : __t) + '">' + ((__t = $t("清空")) == null ? "" : __t) + '</a> <a href="https://' + ((__t = window.location.host) == null ? "" : __t) + '/XV/Home/Index#crmmanage/=/module-objmap" target="_blank" class="field_map_rule" data-api_name="' + ((__t = obj.list[i].api_name) == null ? "" : __t) + '">' + ((__t = $t("字段映射规则未匹配")) == null ? "" : __t) + "</a> </li> ";
            }
            __p += ' </ul> <a href="javascript:;" class="add_clue">' + ((__t = $t("新建查重规则")) == null ? "" : __t) + " >></a> </div>";
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/form/trigger_actions/trigger_actions",["crm-modules/action/field/field","crm-modules/common/util"],function(t,e,c){var o=t("crm-modules/action/field/field").C.Base,n=t("crm-modules/common/util");c.exports=o.extend({render:function(){this.comps={},this.renderRuleAcion()},renderRuleAcion:function(){var t=this;this.comps.checkbox=FxUI.create({wrapper:this.$el[0],template:'<fx-checkbox-group v-model="value">\n\t\t\t\t\t\t\t<fx-checkbox label="ADD">'.concat($t("新建"),'</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="IMPORT">').concat($t("导入"),'</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="SMART_FORM">').concat($t("智能表单"),'</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="OPEN_API">Openapi</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="EDIT">').concat($t("编辑"),'</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="RECOVER">').concat($t("恢复"),'</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="MARKETING_ADD">').concat($t("营销通新建"),'</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="MERGE">').concat($t("合并"),'</fx-checkbox>\n\t\t\t\t\t\t\t<fx-checkbox label="CHANGE_OWNER">').concat($t("变更负责人"),"</fx-checkbox>\n\t\t\t\t\t\t</fx-checkbox-group>"),data:function(){return{value:this.getDefaultValue()}},methods:{getDefaultValue:function(){return t.get("data").trigger_actions||[]}}})},getValue:function(){this.hideError();var t=this.comps.checkbox.value;if(t.length)return t;this.showError()},showError:function(){n.showErrmsg(this.$el,"".concat($t("请填写触发规则操作"),"!"))},hideError:function(){n.hideErrmsg(this.$el)},destroy:function(){for(var t in o.prototype.destroy.apply(this),this.comps)this.comps[t].destroy&&this.comps[t].destroy();this.comps=null}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/model",["crm-modules/common/util"],function(e,t,a){e("crm-modules/common/util");a.exports={fetch:function(){this.parse(),this.set({leadsObj_fields:CRM.get("fields.LeadsObj")})},parse:function(){var e=this.getFields();this.set({layout:[{api_name:"basic",label:$t("基本信息"),columns:2,components:[e.name,e.description]},{api_name:"trigger_actions",label:$t("触发规则的操作"),columns:1,components:[e.trigger_actions]},{api_name:"filters",label:$t("触发规则的线索条件"),columns:1,components:[e.filters]},{api_name:"set_repeat_rule",label:$t("设置查重规则"),columns:1,components:[e.set_repeat_rule]},{api_name:"repeat_process_type",label:$t("线索重复处理方式"),columns:1,components:[e.repeat_process_type]}],fields:e})},getFields:function(){return{name:{api_name:"name",type:"text",label:$t("规则名称"),placeholder:$t("sfa.setting.sysobject.repeat_clue.maxWrite"),is_required:!0,max_length:50},description:{api_name:"description",type:"long_text",label:$t("描述")},trigger_actions:{api_name:"trigger_actions",is_required:!0,label:$t("触发操作")},filters:{api_name:"filters",label:$t("条件范围")},set_repeat_rule:{api_name:"set_repeat_rule",is_required:!0,label:$t("查重规则 (至少设置一个对象)")},repeat_process_type:{api_name:"repeat_process_type",label:$t("处理规则：所有线索均默认打重复标记(可拖拽调整顺序)")}}}}});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/add/view",["crm-modules/common/util","crm-modules/action/field/field","./form/trigger_actions/trigger_actions","./form/filters/filters","./form/set_repeat_rule/set_repeat_rule","./form/repeat_process_type/repeat_process_type"],function(e,t,r){var c=e("crm-modules/common/util"),s=(e("crm-modules/action/field/field"),e("./form/trigger_actions/trigger_actions")),i=e("./form/filters/filters"),o=e("./form/set_repeat_rule/set_repeat_rule"),e=e("./form/repeat_process_type/repeat_process_type");r.exports={mycomponents:{trigger_actions:s,filters:i,set_repeat_rule:o,repeat_process_type:e},collect:function(){var e={duplicated_processing:{name:this.forms.name.getValue(),description:this.forms.description.getValue(),trigger_actions:this.forms.trigger_actions.getValue(),filters:this.forms.filters.getValue(),duplicated_processing_modes:this.forms.repeat_process_type.getValue(),duplicated_search_rules:this.forms.set_repeat_rule.getValue(),priority:this.model.get("data").priority}};return"edit"===this.model.get("action_type")&&(e.duplicated_processing.id=this.model.get("data").id),e},featchSave:function(e){var t=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/save",data:e,success:function(e){0==e.Result.StatusCode?t.trigger("success"):CRM.util.alert(e.Result.FailureMessage)}},{submitSelector:$('span[data-action="submit"]',t.$el.closest(".crm-c-dialog"))})},submit:function(){var e=this,t=this.collect();if(this.validate()){var r,s=t.duplicated_processing.duplicated_processing_modes,i=!0;if($.each(s,function(e,t){if("ADD_INTERACTIVE_RECORDS"==t.mode_action)return i=!1}),!i){var s=this.forms.set_repeat_rule.comps.LeadsObj.value,o=t.duplicated_processing.trigger_actions||[],a=_.filter(["ADD","SMART_FORM","OPEN_API","MARKETING_ADD"],function(e){return _.contains(o,e)});if(!s||0==a.length)return void c.alert($t("提交失败，当前规则不满足“已有线索下生成行为记录”的前置条件"))}"edit"===this.model.get("action_type")?r=c.confirm($t("此操作将造成系统在今晚 ".concat(this.model.get("data").task_execute_time||"--"," 更新已有线索的重复标签,是否继续？")),$t("确认"),function(){r.destroy(),e.featchSave(t)}):e.featchSave(t)}}}});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/repeat_data_process_rule",["crm-modules/common/util","crm-widget/table/table","./template/tpl-html","crm-modules/action/field/field","./add/view","./add/model","./ruleprioritydialog/ruleprioritydialog","crm-modules/common/filtergroup/util"],function(t,e,a){var o=t("crm-modules/common/util"),s=t("crm-widget/table/table"),i=t("./template/tpl-html"),n=t("crm-modules/action/field/field"),c=t("./add/view"),r=t("./add/model"),l=t("./ruleprioritydialog/ruleprioritydialog"),t=(t("crm-modules/common/filtergroup/util"),Backbone.View.extend({options:{MAX_RULE_COUNT:5},initialize:function(t){var e=this;this.comps={},this.setElement(t.wrapper),this.$el.html(i()),o.getFieldsByApiName("LeadsObj").done(function(){e.initTable()})},initTable:function(){var i=this,t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",n=this;this.comps.table&&this.comps.table.destroy(),this.comps.table=new s({$el:this.$el.find(".repeat_data_process_rule-table"),tableName:"tableName",requestType:"FHHApi",url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_processing_list",showFilerBtn:!1,showMultiple:!1,showMoreBtn:!1,showTermBatch:!0,noAllowedWrap:!1,autoHeight:!0,showPage:!1,postData:{search_template:{table_name:"biz_leads_duplicated_processing",where_params:[{operator:"LIKE",field_name:"name",values:""===t?[]:[t]}],orders:[{field_name:"create_time",is_asc:!0}]}},columns:[{data:"name",title:$t("规则名称")},{data:"description",title:$t("规则描述"),dataType:2},{data:"trigger_actions",title:$t("触发动作"),dataType:2,render:function(t){return t.map(function(t){switch(t){case"ADD":return $t("新建");case"IMPORT":return $t("导入");case"SMART_FORM":return $t("智能表单");case"OPEN_API":return"Openapi";case"EDIT":return $t("编辑");case"RECOVER":return $t("恢复");case"MARKETING_ADD":return $t("营销通新建");case"MERGE":return $t("合并");case"CHANGE_OWNER":return $t("变更负责人")}return"--"}).join(",")}},{data:"last_modified_by",title:$t("最后修改人"),dataType:2,render:function(t){var e=FS.contacts.getEmployeeById(t);return e?e.name:t}},{data:"last_modified_time",title:$t("最后修改时间"),dataType:4},{data:"status",title:$t("状态"),dataType:2,render:function(t){switch(t){case 1:return $t("已启用");case 0:return $t("已禁用")}}},{data:"",title:$t("操作"),render:function(t,e,a){var i=[],n=['<a data-operate="edit">'.concat($t("编辑"),"</a>"),'<a data-operate="start">'.concat($t("启用"),"</a>"),'<a data-operate="disable">'.concat($t("禁用"),"</a>"),'<a data-operate="copy">'.concat($t("复制"),"</a>"),'<a data-operate="delete">'.concat($t("删除"),"</a>")];switch(a.status){case 1:i=[n[0],n[2],n[3]];break;case 0:i=[n[0],n[1],n[3],n[4]];break;default:i=[]}return'<div class="ops-btns">'.concat(i.join("\n"),"</div>")}}],formatData:function(t){return{data:t.duplicated_processing_list}}}),this.comps.table.on("trclick",function(t,e,a){a=a.data("operate");"edit"==a?i.editHandle(t):"start"==a?i.statusHandle(t,1):"disable"==a?FxUI.MessageBox.confirm($t("确定禁用此条规则？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){i.statusHandle(t,0)}):"copy"==a?i.comps.table.getRowData().length>=n.options.MAX_RULE_COUNT?o.alert($t("最多创建{{MaxRule}}条规则",{MaxRule:i.options.MAX_RULE_COUNT})):i.copyHandle(t):"delete"==a&&FxUI.MessageBox.confirm($t("确定删除此条规则？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){i.statusHandle(t,-1)})}),this.comps.table.on("renderListComplete",function(){var t=n.options.MAX_RULE_COUNT,e=this.getRowData().length;n.searchBtn(),n.createBtn(),n.setTitleCount(t,e),n.comps.create.disabled=t<=e,n.comps.create.hidden=!(t<=e)})},refreshTable:function(){this.comps.table.setParam({pageNumber:50},!0)},editHandle:function(t){var e=this;n.edit({Model:n.Model.extend(r),View:n.View.extend(c),record_type:"default__c",show_type:"full",action_type:"edit",className:"repeat_rule_dialog",data:t,success:function(){e.refreshTable()}})},disableHandle:function(t){FxUI.MessageBox.confirm($t("确定禁用此条规则？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){})},copyHandle:function(t){var e=this;(t=o.deepClone(t)).name=t.name+"-"+$t("副本"),t.priority=null,n.add({Model:n.Model.extend(r),View:n.View.extend(c),record_type:"default__c",show_type:"full",action_type:"copy",className:"repeat_rule_dialog",data:t,success:function(){e.refreshTable()}})},statusHandle:function(t,e){var a=this;o.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/update_status",data:{table_name:"biz_leads_duplicated_processing",data_id:t.id,status:e},success:function(t){0==t.Result.StatusCode&&(o.remindSuccess(t.Value.message),a.refreshTable())}})},searchBtn:function(){var t=this;this.comps.search||(this.comps.search=FxUI.create({wrapper:".repeat_data_process_rule-title .j-srarch-rule",template:'<fx-input placeholder="'.concat($t("搜索规则"),'" v-model="keyword" size="mini" @change="clickSearch" >\n\t\t\t\t\t\t\t\t<fx-button slot="append" icon="el-icon-search" @click="clickSearch"></fx-button>\n\t\t\t\t\t\t\t</fx-input>'),data:function(){return{keyword:""}},methods:{clickSearch:function(){t.initTable(this.keyword)}}}))},sortBtn:function(){var t;this.comps.sort||(t=$t("多条规则时可排序"),this.comps.sort=FxUI.create({wrapper:".repeat_data_process_rule-title .j-sort-rule",template:'<fx-tooltip effect="dark" content="'.concat(t,'" :disabled="hidden" placement="top">\n\t\t\t\t\t\t\t<span><fx-button plain size="mini" :disabled="disabled">').concat($t("重新排序"),"</fx-button></span>\n\t\t\t\t\t\t</fx-tooltip>"),data:function(){return{disabled:!1,hidden:!0}}}))},createBtn:function(){var t,e=this;this.comps.create||(t=$t("最多创建{{MaxRule}}条规则",{MaxRule:this.options.MAX_RULE_COUNT}),this.comps.create=FxUI.create({wrapper:".repeat_data_process_rule-title .j-create-rule",template:'<div><fx-button plain size="mini" :disabled="false" @click="clickCreatDialogHandle">'.concat($t("规则优先级设置"),'</fx-button>\n\t\t\t\t\t\t\t<fx-tooltip effect="dark" content="').concat(t,'" :disabled="hidden" placement="top"><span>\n\t\t\t\t\t\t\t<fx-button plain size="mini" :disabled="disabled" @click="clickCreateHandle">').concat($t("新建处理规则"),"</fx-button></span>\n\t\t\t\t\t\t</fx-tooltip></div>"),data:function(){return{disabled:!1,hidden:!0}},methods:{clickCreateHandle:function(){n.add({Model:n.Model.extend(r),View:n.View.extend(c),record_type:"default__c",action_type:"add",show_type:"full",className:"repeat_rule_dialog",success:function(){e.refreshTable()}})},clickCreatDialogHandle:function(){e.rulePriorityDialog=new l({title:$t("规则优先级"),success:e.refreshTable.bind(e)}),e.rulePriorityDialog.show(e.comps.table.getCurData())}},created:function(){this.tableData=[]}}))},setTitleCount:function(t,e){$(".repeat_data_process_rule-count",this.$el).html("(".concat(e,"/").concat(t,")"))},show:function(){this.$el.show()},hide:function(){var t=FollowBehaviorDialog.superclass.hide.call(this);return this.destroy(),t},destroy:function(){for(var t in this.comps)this.comps[t].destroy&&this.comps[t].destroy();this.comps=null}}));a.exports=t});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/ruleprioritydialog/ruleprioritydialog",["crm-widget/dialog/dialog","crm-widget/table/table"],function(t,i,e){t("crm-widget/dialog/dialog"),t("crm-widget/table/table");t=Backbone.View.extend({options:function(){return{cols:[{data:"name",title:$t("规则名称")},{data:"description",title:$t("规则描述"),dataType:2},{data:"trigger_actions",title:$t("触发动作")},{data:"status",title:$t("状态")}],tableData:[],title:$t("规则优先级"),configObj:{url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/batch_update_lead_duplicate_priority",data:{}}}},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"submitBtn"},sortRulePriority:function(){var i=this;CRM.util.FHHApi({url:i.options.configObj.url,data:i.options.configObj.data,success:function(t){0===t.Result.StatusCode&&(i.dialog.dialogVisible=!1,i.options.success)&&i.options.success()}})},viewRulePriority:function(){var t=this;t.dialog=FxUI.create({template:'<fx-dialog\n\t\t\t\t\t\t\t:visible.sync="dialogVisible"\n\t\t\t\t\t\t\t:append-to-body="true"\n\t\t\t\t\t\t\tsize="medium"\n\t\t\t\t\t\t\twidth="70%"\n\t\t\t\t\t\t\tclass="pvui-sort-scene-dialog"\n\t\t\t\t\t\t\t:title="getTitle"\n\t\t\t\t\t\t\t@closed="handleClose">\n\t\t\t\t\t\t\t<fx-scrollbar class="pvui-sort-scene-dialog-scroll">\n\t\t\t\t\t\t\t<div class="pvui-sort-scene-dialog-content">\n\t\t\t\t\t\t\t\t<fx-alert\n\t\t\t\t\t\t\t\tclass="sort-tip"\n\t\t\t\t\t\t\t\t:title="$t(\'您可以通过拖动来改变顺序\')"\n\t\t\t\t\t\t\t\ttype="info"\n\t\t\t\t\t\t\t\tshow-icon\n\t\t\t\t\t\t\t\t></fx-alert>\n\t\t\t\t\t\t\t\t<div class="pvui-sort-scene-list">\n\t\t\t\t\t\t\t\t<ul class="list-head">\n\t\t\t\t\t\t\t\t\t<li v-for="item in cols">\n\t\t\t\t\t\t\t\t\t\t<span>{{ item.title }}</span>\n\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t</ul>\n\t\t\t\t\t\t\t\t<fx-draggable\n\t\t\t\t\t\t\t\t\tclass="table-list"\n\t\t\t\t\t\t\t\t\tv-model="rulePriorityList"\n\t\t\t\t\t\t\t\t\ttag="ul"\n\t\t\t\t\t\t\t\t\t:animation="150"\n\t\t\t\t\t\t\t\t\tv-loading="loading"\n\t\t\t\t\t\t\t\t\tghost-class="draggable-item"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t<li v-for="item in rulePriorityList">\n\t\t\t\t\t\t\t\t\t\t<span><i class="drag-target om-icon-drag"\n\t\t\t\t\t\t\t\t\t\t\t:title="$t(\'按住拖拽排序\')"\n\t\t\t\t\t\t\t\t\t\t\t></i>{{ item.name }}\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t<span>{{ item.description || \'--\' }}</span>\n\t\t\t\t\t\t\t\t\t\t<span>{{ handleTranslate(item.trigger_actions) }}</span>\n\t\t\t\t\t\t\t\t\t\t<span>{{ item.status == 1 ? $t(\'已启用\') : $t(\'已禁用\')}}</span>\n\t\t\t\t\t\t\t\t\t</li>\n\t\t\t\t\t\t\t\t</fx-draggable>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</fx-scrollbar>\n\t\t\t\t\t\t\t<span slot="footer" class="dialog-footer">\n\t\t\t\t\t\t\t<fx-button type="primary" :loading="saveLoading" @click="handleSave">{{\n\t\t\t\t\t\t\t\t$t("保 存")\n\t\t\t\t\t\t\t}}</fx-button>\n\t\t\t\t\t\t\t<fx-button @click="handleClose">{{ $t("取 消") }}</fx-button>\n\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t</fx-dialog>',data:function(){return{dialogVisible:!0,loading:!1,saveLoading:!1,rulePriorityList:[],cols:t.options.cols}},computed:{getTitle:function(){return $t("{{objectName}}",{objectName:t.options.title})}},methods:{handleSave:function(){var e={};_.each(this.rulePriorityList,function(t,i){e[t.id]=i+1}),t.options.configObj.data.data_id_and_priority_map=e,t.sortRulePriority()},handleClose:function(){this.destroy&&this.destroy()},handleTranslate:function(t){return t.map(function(t){switch(t){case"ADD":return $t("新建");case"IMPORT":return $t("导入");case"SMART_FORM":return $t("智能表单");case"OPEN_API":return"Openapi";case"EDIT":return $t("编辑");case"RECOVER":return $t("恢复")}return"--"}).join(",")}},mounted:function(){},created:function(){this.rulePriorityList=t.data}})},show:function(t){this.data=t,this.viewRulePriority()},destroy:function(){this.$el.remove(),this.options=null,this.ruleTable&&this.ruleTable.destroy()}});e.exports=t});
define("crm-setting/sysobject/component/repeat_clue/repeat_data_process_rule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="repeat_data_process_rule-title"> <div class="repeat_data_process_rule-layout-left"> <span class="repeat_data_process_rule-text">' + ((__t = $t("规则列表")) == null ? "" : __t) + '</span> <span class="repeat_data_process_rule-count">(--/--)</span> </div> <div class="repeat_data_process_rule-layout-right"> <span class="repeat_data_process_rule-item j-srarch-rule"></span> <span class="repeat_data_process_rule-item line"></span> <span class="repeat_data_process_rule-item j-sort-rule"></span> <span class="repeat_data_process_rule-item j-create-rule"></span> </div> </div> <div class="repeat_data_process_rule-table"> </div>';
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/form/helper",["crm-modules/common/filtergroup/setting_helper","./util"],function(e,t,r){var o=e("crm-modules/common/filtergroup/setting_helper"),n=e("./util");r.exports=$.extend(!0,{},o,{formatFields:function(e){return e},getFieldMustFilter:function(){return n.global}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/form/show_fields/show_fields",["crm-modules/action/field/field","crm-modules/common/util","crm-modules/common/filtergroup/util","./../util"],function(e,t,a){var i=e("crm-modules/action/field/field").C.Base,s=e("crm-modules/common/util"),o=(e("crm-modules/common/filtergroup/util"),e("./../util"));a.exports=i.extend({dataEvents:{"change.object_api_name":"change_object_handle"},render:function(){this.comps={},this.api_name=this.model.get("data").object_api_name,this.renderShowFields()},change_object_handle:function(e){this.comps.show_fields.api_name=e.value,this.comps.show_fields.value=["name"]},renderShowFields:function(e){var t=this;this.comps.show_fields&&this.comps.show_fields.$destroy(),this.comps.show_fields=FxUI.create({wrapper:this.$el[0],template:'<fx-transfer v-model="value" :data="data" target-order="push" filterable filter-placeholder="'.concat($t("搜索字段"),'" :titles="[\'').concat($t("全部字段"),"', '").concat($t("显示字段"),"']\"></fx-transfer>"),data:function(){return{data:[],value:t.get("data").show_fields||["name"],api_name:""}},methods:{generateData:function(){var t=this;this.api_name||(this.data=[]),s.getFieldsByApiName(this.api_name).done(function(){var e=CRM.get("fields.".concat(t.api_name));t.data=Object.values(e).filter(function(e,t){return!1!==e.is_active&&-1==o.global.indexOf(e.api_name)&&-1==["signature","image","file_attachment","group"].indexOf(e.type)}).map(function(e,t){return{key:e.api_name,label:e.label}})})}},watch:{api_name:function(){this.generateData()}},created:function(){this.api_name=t.api_name,this.generateData()}})},getValue:function(){return this.hideError(),this.comps.show_fields.value.length<2?(this.showError(),!1):this.comps.show_fields.value},showError:function(){s.showErrmsg(this.$el,$t("显示字段不得少于 2 个!"))},hideError:function(){s.hideErrmsg(this.$el)},destroy:function(){for(var e in i.prototype.destroy.apply(this),this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/form/usable_rules/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="usable-rules"> <div class="usable-rules-left-label">' + ((__t = $t("当")) == null ? "" : __t) + '</div> <div class="usable-rules-continer"></div> <div class="usable-rules-right-label">' + ((__t = $t("的值完全相同时, 业务记录出现重复")) == null ? "" : __t) + "</div> </div>";
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/form/usable_rules/usable_rules",["crm-modules/action/field/field","crm-modules/common/filtergroup/filtergroup","./../helper","crm-modules/common/util","./../util","./template/tpl-html"],function(e,t,s){var r=e("crm-modules/action/field/field").C.Base,i=e("crm-modules/common/filtergroup/filtergroup"),l=e("./../helper"),o=e("crm-modules/common/util"),a=e("./../util"),n=e("./template/tpl-html");s.exports=r.extend({dataEvents:{"change.object_api_name":"change_object_handle"},render:function(){this.comps={},this.$el.html(n()),this.api_name=this.model.get("data").object_api_name||"LeadsObj";var e=this.get("data").usable_rules;this.renderScope(e)},change_object_handle:function(e){this.api_name=e.value,this.renderScope()},renderScope:function(e){this.comps.usable_rules&&this.comps.usable_rules.destroy(),this.comps.usable_rules=new i({$wrapper:this.$el.find(".usable-rules-continer"),title:$t("且(AND)"),width:750,AND_MAX:20,OR_MAX:5,OR_KEY:"conditions",level:1,addBtnName:$t("新增或关系"),apiname:this.api_name,selectone_multiple:!0,defaultValue:e,filterType:a.filter[this.api_name].type,filterApiname:a.filter[this.api_name].api_name,helper:l})},getValue:function(){this.hideError();var e=this.comps.usable_rules.getValue(),e=JSON.parse(e);return this.comps.usable_rules.valid()?e:(this.showError(),null)},showError:function(){o.showErrmsg(this.$el,$t("请填写筛选值"))},hideError:function(){o.hideErrmsg(this.$el)},destroy:function(){for(var e in r.prototype.destroy.apply(this),this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null}})});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/form/util",[],function(e,t,_){_.exports={global:["relevant_team","lock_rule","life_status_before_invalid","lock_user","extend_obj_data_id","out_owner","out_tenant_id","owner_department","_id","version","is_deleted","object_describe_api_name","object_describe_id","tenant_id","order_by","package","resale_count","enterprise_wechat_user_id","refresh_duplicated_version","confirm_time","completed_field_quantity","filling_checker_id","is_remind_recycling","last_deal_closed_amount","pin_yin","remind_days","total_refund_amount","day_of_birth","date_of_birth","year_of_birth","month_of_birth","owner_changed_time","name_order","order_by","industry_ext","extend_days"],filter:{LeadsObj:{type:["formula","file_attachment","image","count","auto_number","quote","employee_many","department_many","html_rich_text","object_reference_many"],api_name:["life_status","biz_status","conversion_probability","resale_count","account_id","account_id__r","opportunity_id","opportunity_id__r","contact_id","contact_id__r","partner_id","close_reason","assigner_id","completed_result","is_overtime","last_follow_time","leads_status","remaining_time","last_follower","remark","lock_status","created_by","last_modified_by","data_own_department","back_reason"]},AccountObj:{type:["formula","file_attachment","image","count","auto_number","quote","employee_many","department_many","html_rich_text","object_reference_many"],api_name:["account_status","last_follower","created_by","life_status","deal_status","biz_status","resale_count","last_deal_closed_time","total_refund_amount","partner_id","out_resources","close_reason","account_no","area_location","back_reason","industry_level1","industry_level2","remark","last_modified_by","data_own_department","completion_rate","transfer_count","recycled_reason","leads_id","owner","lock_status","remaining_time"]},ContactObj:{type:["formula","file_attachment","image","count","auto_number","quote","employee_many","department_many","html_rich_text","object_reference_many"],api_name:["life_status","resale_count","leads_id","partner_id","out_resources","close_reason","tel1","tel2","tel3","tel4","tel5","mobile1","mobile2","mobile3","mobile4","mobile5","created_by","data_own_department","lock_status","owner","contact_status","last_modified_by","name_order"]}}}});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/model",["crm-modules/common/util"],function(e,t,l){e("crm-modules/common/util");l.exports={fetch:function(){this.parse(),this.set({leadsObj_fields:CRM.get("fields.LeadsObj")})},parse:function(){var e=this.getFields();this.set({layout:[{api_name:"basic",label:$t("基本信息"),columns:2,components:[e.object_api_name,e.name,e.description]},{api_name:"rule_detail",label:$t("规则明细"),columns:1,components:[e.usable_rules]},{api_name:"show_fields",label:$t("设置显示字段"),columns:1,components:[e.show_fields]}],fields:e})},getFields:function(){return{object_api_name:{api_name:"object_api_name",type:"select_one",label:$t("所属对象"),is_required:!0,options:[{label:$t("线索"),value:"LeadsObj"},{label:$t("客户"),value:"AccountObj"},{label:$t("联系人"),value:"ContactObj"}]},name:{api_name:"name",type:"text",label:$t("规则名称"),placeholder:$t("请输入，最多50字"),is_required:!0,max_length:50},description:{api_name:"description",type:"long_text",label:$t("描述")},usable_rules:{api_name:"usable_rules"},show_fields:{api_name:"show_fields",is_required:!0,label:$t("显示字段")}}}}});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/add/view",["crm-modules/common/util","crm-modules/action/field/field","./form/usable_rules/usable_rules","./form/show_fields/show_fields"],function(e,t,s){var i=e("crm-modules/common/util"),o=e("crm-modules/action/field/field"),l=e("./form/usable_rules/usable_rules"),e=e("./form/show_fields/show_fields");s.exports={mycomponents:{usable_rules:l,show_fields:e},initView:function(){o.View.prototype.initView.apply(this,arguments),"edit"===this.model.get("action_type")&&this.forms.object_api_name.disable()},collect:function(){var e={object_api_name:this.forms.object_api_name.getValue(),name:this.forms.name.getValue(),description:this.forms.description.getValue(),usable_rules:this.forms.usable_rules.getValue(),show_fields:this.forms.show_fields.getValue(),map_full_fields:!1};return"edit"===this.model.get("action_type")&&(e.id=this.model.get("data").id),e},featchSave:function(e){var t=this;CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/save_search_rule",data:e,success:function(e){0==e.Result.StatusCode&&t.trigger("success")}},{submitSelector:$('span[data-action="submit"]',this.$el.closest(".crm-c-dialog"))})},submit:function(){var e,t=this,s=this.collect();this.validate()&&("edit"===this.model.get("action_type")?e=i.confirm($t("此操作将造成系统在今晚 {{time}} 更新已有线索的重复标签,是否继续？",{time:this.model.get("data").task_execute_time||"--"}),$t("确认"),function(){e.destroy(),t.featchSave(s)}):t.featchSave(s))}}});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/repeat_rule",["crm-modules/common/util","crm-widget/table/table","./template/tpl-html","crm-modules/action/field/field","./add/view","./add/model"],function(e,t,a){var s=e("crm-modules/common/util"),o=e("crm-widget/table/table"),n=e("./template/tpl-html"),i=e("crm-modules/action/field/field"),l=e("./add/view"),c=e("./add/model"),e=Backbone.View.extend({options:{MAX_RULE_COUNT:50},events:{},initialize:function(e){var t=this;this.comps={},this.setElement(e.wrapper),this.$el.html(n()),$.when(s.getFieldsByApiName("LeadsObj"),s.getFieldsByApiName("AccountObj"),s.getFieldsByApiName("ContactObj")).done(function(){t.initTable()})},initTable:function(){var n=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"",i=this;this.comps.table&&this.comps.table.destroy(),this.comps.table=new o({$el:this.$el.find(".repeat_rule-table"),tableName:"tableName",requestType:"FHHApi",url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/get_duplicated_search_rule_list",showFilerBtn:!1,showMultiple:!1,showMoreBtn:!1,showTermBatch:!0,noAllowedWrap:!1,autoHeight:!0,showPage:!1,postData:{search_template:{table_name:"biz_leads_duplicated_search",where_params:[{operator:"LIKE",field_name:"name",values:""===e?[]:[e]}]}},columns:[{data:"name",title:$t("规则名称")},{data:"description",title:$t("规则描述"),dataType:2},{data:"object_name",title:$t("所属对象"),dataType:2},{data:"usable_rules",title:$t("匹配规则"),dataType:2,render:function(e,t,a){return i.getUsableRulesHtml(a)}},{data:"status",title:$t("状态"),dataType:2,render:function(e){switch(e){case 1:return $t("已启用");case 0:return $t("已禁用")}}},{data:"",title:$t("操作"),render:function(e,t,a){var n=[],i=['<a data-operate="edit">'+$t("编辑")+"</a>",'<a data-operate="start">'+$t("启用")+"</a>",'<a data-operate="disable">'+$t("禁用")+"</a>",'<a data-operate="copy">'+$t("复制")+"</a>",'<a data-operate="delete">'+$t("删除")+"</a>"];switch(a.status){case 1:n=[i[0],i[2],i[3]];break;case 0:n=[i[0],i[1],i[3],i[4]];break;default:n=[i[1]]}return'<div class="ops-btns">'.concat(n.join("\n"),"</div>")}}],formatData:function(e){return{data:e.duplicated_search_rule_list}}}),this.comps.table.on("trclick",function(e,t,a){a=a.data("operate");"edit"==a?n.editHandle(e):"start"==a?n.statusHandle(e,1):"disable"==a?FxUI.MessageBox.confirm($t("确定禁用此条规则？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){n.statusHandle(e,0)}):"copy"==a?n.comps.table.getRowData().length>=i.options.MAX_RULE_COUNT?s.alert($t("最多创建{{MaxRule}}条规则",{MaxRule:n.options.MAX_RULE_COUNT})):n.copyHandle(e):"delete"==a&&FxUI.MessageBox.confirm($t("确定删除此条规则？"),$t("提示"),{confirmButtonText:$t("确定"),cancelButtonText:$t("取消"),type:"warning"}).then(function(){n.statusHandle(e,-1)})}),this.comps.table.on("renderListComplete",function(){var e=i.options.MAX_RULE_COUNT,t=this.getRowData().length;i.searchBtn(),i.createBtn(),i.setTitleCount(e,t),i.comps.create.disabled=e<=t,i.comps.create.hidden=!(e<=t)})},refreshTable:function(){this.comps.table.setParam({pageNumber:50},!0)},editHandle:function(e){var t=this;i.edit({Model:i.Model.extend(c),View:i.View.extend(l),record_type:"default__c",show_type:"full",action_type:"edit",className:"repeat_rule_dialog",data:e,success:function(){t.refreshTable()}})},copyHandle:function(e){var t=this;(e=s.deepClone(e)).name=e.name+$t("-副本"),i.add({Model:i.Model.extend(c),View:i.View.extend(l),record_type:"default__c",show_type:"full",action_type:"copy",className:"repeat_rule_dialog",data:e,success:function(){t.refreshTable()}})},statusHandle:function(e,t){var a=this;s.FHHApi({url:"/EM1HNCRM/API/v1/object/leads_duplicated_processing/service/update_status",data:{table_name:"biz_leads_duplicated_search",data_id:e.id,status:t},success:function(e){0==e.Result.StatusCode&&(s.remindSuccess(e.Value.message),a.refreshTable())}})},searchBtn:function(){var e=this;this.comps.search||(this.comps.search=FxUI.create({wrapper:".repeat_rule-title .j-srarch-rule",template:'<fx-input placeholder="'.concat($t("搜索规则"),'" v-model="keyword" size="mini" @change="clickSearch">\n\t\t\t\t\t\t\t\t<fx-button slot="append" icon="el-icon-search" @click="clickSearch"></fx-button>\n\t\t\t\t\t\t\t</fx-input>'),data:function(){return{keyword:""}},methods:{clickSearch:function(){e.initTable(this.keyword)}}}))},createBtn:function(){var e,t=this;this.comps.create||(e=$t("最多创建{{MaxRule}}条规则",{MaxRule:this.options.MAX_RULE_COUNT}),this.comps.create=FxUI.create({wrapper:".repeat_rule-title .j-create-rule",template:'<fx-tooltip effect="dark" content="'.concat(e,'" :disabled="hidden" placement="top">\n\t\t\t\t\t\t\t<span><fx-button plain size="mini" :disabled="disabled" @click="clickCreateHandle">').concat($t("新建查重规则"),"</fx-button></span>\n\t\t\t\t\t\t</fx-tooltip>"),data:function(){return{disabled:!1,hidden:!0}},methods:{clickCreateHandle:function(){i.add({Model:i.Model.extend(c),View:i.View.extend(l),record_type:"default__c",show_type:"full",action_type:"add",className:"repeat_rule_dialog",data:{object_api_name:"LeadsObj"},success:function(){t.refreshTable()}})}}}))},getUsableRulesHtml:function(e){for(var t=[],a=e.usable_rules,n=0;n<a.length;n++){for(var i=a[n],s=[],o=0;o<i.conditions.length;o++){var l=i.conditions[o],l=CRM.get("fields.".concat(e.object_api_name))[l.field_name];l&&s.push("".concat(l.label))}t.push((0==n?"(":" OR (").concat(s.join(" AND "),")"))}var c=t.join("")||$t("sfa.setting.sysobj.feild.disable");return'<div class="ruel-container">'.concat(c,"</div>")},setTitleCount:function(e,t){$(".repeat_rule-count",this.$el).html("(".concat(t,"/").concat(e,")"))},show:function(){this.$el.show()},hide:function(){this.$el.hide()},destroy:function(){for(var e in this.comps)this.comps[e].destroy&&this.comps[e].destroy();this.comps=null}});a.exports=e});
define("crm-setting/sysobject/component/repeat_clue/repeat_rule/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="repeat_rule-title"> <div class="repeat_rule-layout-left"> <span class="repeat_rule-text">' + ((__t = $t("规则列表")) == null ? "" : __t) + '</span> <span class="repeat_rule-count">(--/--)</span> </div> <div class="repeat_rule-layout-right"> <span class="repeat_rule-item j-srarch-rule"></span> <span class="repeat_rule-item line"></span> <span class="repeat_rule-item j-create-rule"></span> </div> </div> <div class="repeat_rule-table"> </div>';
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/repeat_clue/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="repeat-clue-manage"> <div class="crm-intro"> <h3>' + ((__t = $t("说明:")) == null ? "" : __t) + "</h3> <ol> <li>1." + ((__t = $t("可协助识别重复数据")) == null ? "" : __t) + "</li> <li> " + ((__t = isGrayBehaviorActiveRecords ? $t("sfa.setting.sysobject.repeat_clue.rule.title") : "2." + $t("生成行为记录和自动更新功能仅对“智能表单”和“Openapi”触发动作有效")) == null ? "" : __t) + ' </li> </ol> </div> <div class="container"> <ul class="menu-tabs"> <li class="active">' + ((__t = $t("重复数据处理规则")) == null ? "" : __t) + '</li> <span class="line"></span> <li>' + ((__t = $t("查重规则")) == null ? "" : __t) + '</li> </ul> <ul class="page-list"> <li class="page-repeat-data-process-rule active"> <div class="crm-loading"></div> </li> <li class="page-repeat-rule"> <div class="crm-loading"></div> </li> </ul> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/sysobject/component/tags",["crm-setting/common/tags/tags"],function(e,t,n){e=e("crm-setting/common/tags/tags");n.exports=e.extend({initialize:function(e){var t=this;t.typeObj={enumName:"EnumCRMAddressType",name:"address",type:4,desc:$t("地址类型"),showRequire:!1},t._bindEvents(),t.getConfig(),t.render()}})});
define("crm-setting/sysobject/data/data",[],function(e,t,a){var i=[{data:"Name",title:$t("预设对象名称"),render:function(e,t,a){return'<a href="javascript:;" class="j-name j-'+a.ApiName.toLowerCase()+'">'+e+"</a>"}},{data:"ApiName",title:"API Name"},{data:"CreatedBy",title:$t("创建人")},{data:"Desc",title:$t("描述")},{data:"Status",title:$t("状态")}],s=[{Name:$t("crm.销售线索"),ApiName:"LeadsObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:1,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:********},{Name:$t("crm.客户"),ApiName:"AccountObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:2,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:********},{Name:$t("crm.联系人"),ApiName:"ContactObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:3,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:********},{Name:$t("crm.商机"),ApiName:"OpportunityObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:8,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:********},{Name:$t("crm.销售订单"),ApiName:"SalesOrderObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:11,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:********},{Name:$t("crm.销售订单产品"),ApiName:"SalesOrderProductObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:28,layoutCode:********},{Name:$t("crm.退货单"),ApiName:"ReturnedGoodsInvoiceObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:12,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:10200306},{Name:$t("crm.退货单产品"),ApiName:"ReturnedGoodsInvoiceProductObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:27,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:10200306},{Name:$t("crm.拜访"),ApiName:"VisitingObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:13,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:10200307,checkedProp:"isNotYunZhiJia"},{Name:$t("crm.回款"),ApiName:"PaymentObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:5,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:102003081},{Name:$t("crm.退款"),ApiName:"RefundObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:6,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:10200309},{Name:$t("crm.开票申请"),ApiName:"InvoiceApplicationObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:9,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:10200310},{Name:$t("crm.合同"),ApiName:"ContractObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:16,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:10200311},{Name:$t("crm.市场活动"),ApiName:"MarketingEventObj",CreatedBy:$t("系统"),Desc:"--",Status:$t("启用"),objectType:20,CreateTime:0,IsActive:!0,Define_type:"sys",layoutCode:10200313}],o=[];CRM.control.crmVersion&&_.each({basic_edition:[0,1,2,3,4,5,6,7,10,11,13],wechat_standard_edition:[0,1,2,3,4,5,9,11],kdweibo_edition:[0,1,2,3,4,5,9,11],kis_edition:[0,1,2,4,5],dealer_edition:[1,2,4,5,6,7,10,11,12,13],agent_edition:[0,1,2,3,4,5,6,7,10,11,12,13],promotion_sales_edition:[1,2,4,5,6,7,10,11,12,13],wechat_standardpro_edition:[0,1,2,3,4,5,6,7,10,11,13],standardpro_edition:[0,1,2,3,4,5,6,7,10,11,12,13],strengthen_edition:[0,1,2,3,4,5,6,7,10,11,12,13],enterprise_edition:[0,1,2,3,4,5,6,7,10,11,12,13],office_edition:[0,1,2,3,4,5,6,7,10,11,12,13]}[CRM.control.crmVersion],function(e){o.push(s[e])}),a.exports={data:o,columns:i}});
define("crm-setting/sysobject/sysobject",["crm-modules/common/util","crm-widget/table/table","paas-object/sdk.js","./template/guide-html"],function(e,t,i){var a=e("crm-modules/common/util"),n=e("crm-widget/table/table"),s=e("paas-object/sdk.js"),e=(e("./template/guide-html"),Backbone.View.extend({guide_key:"guide_setting_myobject_payment",initialize:function(e){var t=a.getTplQueryParams()||{};CRM.api.get_licenses({objectApiName:t&&t.api_name}),this.setElement(e.wrapper),this.oObjectSDK=new s,this.keyword="",t.api_name?this.jumpObjectDetail(t):this.initDesc()},loadingTpl:_.template('<div class="myobject-loading"></div>'),events:{"click .j-designadd":"onDesignAdd","click .j-handadd":"onHandAdd"},initDesc:function(){this.$el.html('<div class="myobject-box"><div class="my-tb-wrap"></div><div class="my-tb-info"></div></div>'),this.initTb()},initTb:function(){var s=this;s.tb=new n({$el:s.$(".my-tb-wrap"),tableName:"inventory",url:"/EM1HCRMUdobj/objectDescribe/findByTenantId",postData:{tenant_id:""},requestType:"FHHApi",showMultiple:!1,trHandle:!1,searchTip:$t("对象"),title:$t("预设对象管理")+'<a class="crm-doclink" href="https://help.fxiaoke.com/dbde/2049/5130/5a98" target="_blank"></a>',search:{placeHolder:$t("搜索预设对象"),type:"Keyword",highFieldName:"Name"},doStatic:!0,openStart:!0,showPage:!1,columns:[{data:"Name",title:$t("对象名称"),width:250,render:function(e,t,i){e='<a href="javascript:;" class="j-detail" title="'+(_.escape(i.Name)||"--")+'">'+e+"</a>";return i&&"public"==i.visibleScope&&(e+='<span class="el-tag fx-tag el-tag--link el-tag--small el-tag--light" style="line-height:19px;height:19px;margin-left:4px;border:0;">Public</span>'),e}},{data:"ApiName",title:"API Name"},{data:"Desc",title:$t("描述")},{data:"CreatedBy",title:$t("创建人"),render:function(e){e=FS.contacts.getEmployeeById(e)||{};return e.fullName||e.name||"--"}},{data:"CreateTime",title:$t("创建时间"),dataType:4},{data:"LastModifiedBy",title:$t("最后修改人"),render:function(e){e=FS.contacts.getEmployeeById(e)||{};return e.fullName||e.name||"--"}},{data:"LastModifiedTime",title:$t("最后修改时间"),dataType:4},{data:"Status",title:$t("状态")},{data:"Define_type",title:$t("操作"),lastFixed:!0,width:170,render:function(){var e=arguments[2]||{},t=["disable","enable"][+!e.IsActive],i=[$t("禁用"),$t("启用")][+!e.IsActive],a="",n=(s.objectConfigs||{})[e.ApiName]||{};return 0==(null===n||null==(n=n.object)?void 0:n.controlLevel)?a="":e.OriginalDescribeApiName&&(a+='<a href="javascript:;" class="j-object-btn j-extendchangeorder">'+$t("同步")+"</a>"),/__c$/.test(e.ApiName)&&(a+='<a href="javascript:;" class="j-object-btn j-'+t+'">'+i+'</a><a href="javascript:;" class="j-object-btn j-delete">'+$t("删除")+"</a>"),a}}],formatData:function(e){return null},initComplete:function(){s.keyword?(s.tb.$el.find(".dt-ipt").val(s.keyword),s.tb.$el.find(".dt-sc-btn").trigger("click")):s.searchObjectList()}}),s.tb.on("dt.search",function(e){s.keyword=e,s.searchObjectList()}),s.tb.on("trclick",function(e,t,i){0<i.closest(".j-detail").length?s.showDetail(e):i.hasClass("j-delete")?s.onDelete(e):i.hasClass("j-enable")?s.onEnableOrDisable(e,!0):i.hasClass("j-disable")?s.onEnableOrDisable(e,!1):i.hasClass("j-extendchangeorder")&&s.onExtendChangeOrder(e)})},refresh:function(e){CRM.control.refreshAside(),this.searchObjectList()},searchObjectList:function(){var s=this;s.tb.showLoading(),a.FHHApi({url:"/EM1HNCRM/API/v1/object/describe/service/findDescribeManageList",data:{isIncludeFieldDescribe:!1,isIncludeSystemObj:!1,isIncludeUnActived:!0,packageName:"CRM",sourceInfo:"object_management",describeDefineType:"package",includeControlLevel:!0},success:function(e){var i=[],e=e.Value||{},t=e.objectDescribeList,t=void 0===t?[]:t,a=e.manageGroup,n=void 0===a?{}:a,a=e.objectConfigs,e=void 0===a?{}:a,t=t.filter(function(e){return!1===n.all&&n.apiNames&&n.apiNames.includes(e.api_name)||!1!==n.all}),a=(s.objectConfigs=e,s.objectDescribeList=t,_.each(t,function(e){var t={Name:e.display_name,ApiName:e.api_name,OriginalDescribeApiName:e.original_describe_api_name,visibleScope:e.visible_scope,CreatedBy:e.created_by,CreateTime:new Date(e.create_time).getTime(),Desc:e.description,Status:[$t("禁用"),$t("启用")][+(e.is_active||0)],IsActive:e.is_active,Define_type:e.define_type,LastModifiedBy:e.last_modified_by,LastModifiedTime:e.last_modified_time};(""==s.keyword||-1<(e.display_name||"").toLowerCase().indexOf((s.keyword||"").toLowerCase()))&&i.push(t)}),s.tb.hideLoading(),s.tb.doStaticData(i),$t("当前预置对象个数{{length}}",{length:i.length}));s.$(".my-tb-info").html(a)}})},jumpObjectDetail:function(e){this.showDetail({Name:e.display_name,ApiName:e.api_name,ChildType:e.child_type,SubChildType:e.sub_child_type})},showDetail:function(t){var i=this;i.$el.append(this.loadingTpl()),this.oObjectSDK.getDetail({display_name:t.Name,api_name:t.ApiName},function(e){i.$el.find(".myobject-loading").remove();e=e.defaultView({child_type:t.ChildType,sub_child_type:t.SubChildType});e.on("go_back",function(){i.initDesc()}),e.render(),i.$el.html(e.$el),e.resetTab()})},onDelete:function(e){var t=this;t.oObjectSDK.deleteObject({api_name:e.ApiName,is_active:e.IsActive},function(e){"success"===e&&t.refresh()})},onEnableOrDisable:function(e,t){var i=this;i.oObjectSDK.enableOrDisableObject({api_name:e.ApiName,is_active:t},function(e){"success"==e&&i.refresh()})},onExtendChangeOrder:function(e,t){var i=e.OriginalDescribeApiName,a=(this.objectDescribeList||{}).find(function(e){return e.api_name==i})||{};this.oObjectSDK.extendChangeOrderObject({api_name:e.ApiName,display_name:e.Name,original_describe_api_name:i,original_describe_display_name:a.display_name||""},function(e){})},onHandAdd:function(){var t=this;t.$el.append(this.loadingTpl()),this.oObjectSDK.getLayout(function(e){t.$el.find(".myobject-loading").remove(),t.objectView=e.createObjectForBasic(),t.objectView.on(t.oObjectSDK.EVENT_NAME.OBJECT_BASIC_SAVE,function(e){CRM.control.refreshAside(),t.showDetail({Name:e.objectDescribe.display_name,ApiName:e.objectDescribe.api_name})})})},onDesignAdd:function(){var t=this;t.$el.append(this.loadingTpl()),this.oObjectSDK.getLayout(function(e){t.$el.find(".myobject-loading").remove(),t.objectView=e.createObject(),t.objectView.on(t.oObjectSDK.EVENT_NAME.LAYOUT_SAVE_QUIT,function(e){CRM.control.refreshAside(),t.showDetail({Name:e.objectDescribe.display_name,ApiName:e.objectDescribe.api_name})}),t.objectView.on(t.oObjectSDK.EVENT_NAME.LAYOUT_QUIT,function(e){e&&(CRM.control.refreshAside(),t.showDetail({Name:e.objectDescribe.display_name,ApiName:e.objectDescribe.api_name}))})})},destroy:function(){this.remove()}}));i.exports=e});
define("crm-setting/sysobject/template/guide-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-w-guide-alert"> <div class="inner"> <h3>V6.' + ((__t = $t("2重大回款更新")) == null ? "" : __t) + "</h3> <p>" + ((__t = $t("回款有功能升级和配置变更请查收")) == null ? "" : __t) + '</p> <a href="https://www.fxiaoke.com/mob/guide/6.2.0/crm/6.2/6.2.html#173-%E5%9B%9E%E6%AC%BE%E9%87%8D%E8%A6%81%E6%9B%B4%E6%96%B0%EF%BC%8C%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9" target="_blank">' + ((__t = $t("查看详情")) == null ? "" : __t) + '</a> </div> <span class="close">×</span> </div>';
        }
        return __p;
    };
});