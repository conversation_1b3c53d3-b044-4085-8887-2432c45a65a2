define("crm-setting/usergroup/editusergroup/editusergroup",["crm-modules/common/util","crm-widget/dialog/dialog","crm-widget/selector/selector","../template/edit-usergroup-html"],function(e,t,a){var o=e("crm-modules/common/util"),r=e("crm-widget/dialog/dialog"),n=e("crm-widget/selector/selector"),s=e("../template/edit-usergroup-html"),i=r.extend({attrs:{title:$t("新建用户组"),content:'<div class="crm-loading"></div>',showBtns:!0,showScroll:!1,data:null,className:"crm-s-usergroup"},events:{"click .name":"onRemoveErr","click .b-g-btn":"onSubmit","click .b-g-btn-cancel":"destroy"},show:function(e){var t=i.superclass.show.call(this);return this.set(e),this.setContent(s({data:this.get("data")})),this.initSelector(),this.initTranslateInput(),t},initTranslateInput:function(){var t=this;"add"===t.get("type")?t.initNameInput():o.FHHApi({url:"/EM1HCRMUdobj/groupApi/queryGroupMulti",data:{groupId:t.get("groupId")},success:function(e){0==e.Result.StatusCode&&(t.set({oldLanguages:e.Value}),t.initNameInput(e.Value))}},{errorAlertModel:1})},initNameInput:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},a=this;a.translateInput=window.FxUI.create({wrapper:".j-translate-input",template:'<fx-input-translate ref="translateInput" size="small" v-model="value" :translateDataDef="translateDataDef"\n clearable @change="onChange"></fx-input-translate>',data:function(){var e;return{value:(null==(e=a.get("data"))?void 0:e.defName)||(null==(e=a.get("data"))?void 0:e.name)||"",translateDataDef:t}},methods:{onChange:function(){}},mounted:function(){}})},initSelector:function(){this.employeeWidget=new n({$wrap:this.$(".j-selector"),zIndex:+this.get("zIndex")+10,member:!0,stop:!0,single:!1,label:$t("选择员工"),defaultSelectedItems:this.get("groupUsers"),isFromManage:!0,enableScope:!0})},onSubmit:function(){var e,t=this,a=t.$(".remark"),r=t.$(".j-translate-input"),n=a.val()||"",s=t.translateInput.$refs.translateInput.getTranslateData(),i=t.translateInput.value;return i?20<i.length?(o.showErrmsg(r,$t("角色名称不能超过20个中文字符")),!1):2e3<n.length?(o.showErrmsg(a,$t("备注不能超过2000个中文字符")),!1):(a=this.employeeWidget.getValue("member")||[],e=this.employeeWidget.getValue("stop")||[],a=a.concat(e).join(","),e="add"===t.get("type")?{name:i,languages:s,description:n,groupId:this.get("groupId"),userIds:a}:{name:i,defName:i,languages:s,oldLanguages:this.get("oldLanguages")||{},description:n,groupId:this.get("groupId"),userIds:a},void this.submit(e)):(o.showErrmsg(r,$t("请输入组名称")),!1)},onRemoveErr:function(e){$(e.currentTarget).next().remove()},submit:function(e){var t=this;o.FHHApi({url:"add"===t.get("type")?"/EM1HCRMUdobj/groupApi/createGroup":"/EM1HCRMUdobj/groupApi/updateGroupInfo",data:e,success:function(e){0==e.Result.StatusCode?(t.trigger("success"),o.remind(1,$t("操作成功！")),t.hide()):o.alert(e.Result.FailureMessage||$t("操作失败！"))}},{submitSelector:t.$(".b-g-btn"),errorAlertModel:1})},hide:function(){this.destroy()},destroy:function(){return this.employeeWidget&&this.employeeWidget.destroy(),i.superclass.destroy.call(this)}});a.exports=i});
define("crm-setting/usergroup/exportlist",["crm-modules/common/util","crm-widget/table/table"],function(t,e,a){var r=t("crm-modules/common/util"),n=t("crm-widget/table/table");a.exports=function(a){var t={taskTable:null,initTasklistTable:function(t){var a=this,e=(this.type="DataShareExport",this.getOptions());this.taskTable&&this.taskTable.destroy(),this.taskTable=new n(e),this.taskTable.stopQueryStatus=!1,this.taskTable.$el.on("click",".j-oprate",function(t){var t=$(t.target),e=t.closest("tr"),e=$.extend({},a.taskTable.getRowData(e));t.hasClass("j-d-download")&&!t.hasClass("disable")&&a.startDownload(e)}),this.taskTable.setParam({taskType:2,wheres:[]},!0),this.taskTable.start()},getOptions:function(){var e=this,t=this.getColumns();return{$el:a.$wrap,url:"/EM1HPAASBATCH/task/findGroupTask",title:"",trHandle:!0,openStart:!0,alwaysShowTermBatch:!0,showMultiple:!1,requestType:"FHHApi",postData:{},autoHeight:!1,caption:{},columns:t,rowCallBack:function(t,e){},initComplete:function(t){},getDataBack:function(t,e){return t},formatData:function(t){return e.listData=t&&t.data.map(function(t){return t.taskStatus})||[],0<t.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?e.queryStatus(t.pageNumber,t.pageSize):e.timer&&clearTimeout(e.timer),{totalCount:t.totalCount,data:t.data}},getFullDataBack:function(t){t.Error&&"s211030015"==t.Error.Code&&setTimeout(function(){r.alert(t.Error.Message,function(){location.reload()})},300)}}},queryStatus:function(e,r){var n=this;clearTimeout(n.timer),n.taskTable.stopQueryStatus||CRM.util.FHHApi({url:"/EM1HPAASBATCH/task/findGroupTask",data:{pageNumber:e||1,pageSize:r||20,taskType:2,wheres:[]},success:function(t){var a;0==t.Result.StatusCode&&(0<t.Value.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?(a=!1,t.Value.data.forEach(function(t,e){t&&t.taskStatus!==n.listData[e]&&(a=!0)}),n.timer=a?setTimeout(function(){clearTimeout(n.timer),n.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:n.type,operator:"LT",value_type:0}]}]},!0)},5e3):setTimeout(function(){clearTimeout(n.timer),n.queryStatus(e,r)},3e3)):(clearTimeout(n.timer),n.taskTable.setParam({wheres:[{connector:"OR",filters:[{field_name:"operation_type",field_values:n.type,operator:"LT",value_type:0}]}]},!0)))}})},getColumns:function(){return[{data:"operation",title:$t("操作类型"),render:function(t){if("GroupExport"==t)return $t("daochuyonghuzu")}},{data:"taskStatus",title:$t("任务状态"),width:200,render:function(t){switch(t){case"Ready":return $t("已准备");case"Running":return $t("执行中");case"Stopped":return $t("已停止");case"Finished":return $t("已完成")}}},{data:"fileName",title:$t("导出文件"),width:200},{data:"userName",width:200,title:$t("创建人")||"--"},{data:"createTime",title:$t("创建时间"),render:function(t){return t?FS.moment(t).format("YYYY-MM-DD HH:mm:ss"):"--"}},{data:"taskId",width:200,title:$t("任务ID")},{data:"taskStatus",title:$t("操作"),lastFixed:!0,render:function(t,e,a){var r="";return'<span class="options-wrapper"><a href="javascript:;" class="j-oprate j-d-download '+(r="Finished"==t&&a.filePath?r:"disable")+'" download="">'+$t("下载")+"</a></span>"}}]},startDownload:function(t){var e,a;t.filePath&&(e=document.createElement("a"),(a=document.createEvent("HTMLEvents")).initEvent("click",!1,!1),e.download=t.fileName,e.href=FS.util.getFscLink(t.filePath,t.fileName,!0),e.dispatchEvent(a),e.click())}};return t.initTasklistTable(),t.taskTable}});
define("crm-setting/usergroup/importlist",["crm-modules/common/util","crm-widget/table/table"],function(t,a,e){var s=t("crm-modules/common/util"),r=t("crm-widget/table/table");e.exports=function(e){var t={taskTable:null,initTasklistTable:function(t){var e=this,a=(this.type="DataShareImport",this.getOptions());this.taskTable&&this.taskTable.destroy(),this.taskTable=new r(a),this.taskTable.stopQueryStatus=!1,this.taskTable.$el.on("click",".j-oprate",function(t){var t=$(t.target),a=t.closest("tr"),a=$.extend({},e.taskTable.getRowData(a));t.hasClass("j-d-download")&&!t.hasClass("disable")&&e.startDownload(a)}),this.taskTable.setParam({taskType:1,wheres:[]},!0),this.taskTable.start()},getOptions:function(){var a=this,t=this.getColumns();return{$el:e.$wrap,url:"/EM1HPAASBATCH/task/findGroupTask",title:"",trHandle:!0,openStart:!0,alwaysShowTermBatch:!0,showMultiple:!1,requestType:"FHHApi",postData:{},autoHeight:!1,caption:{},columns:t,rowCallBack:function(t,a){},initComplete:function(t){},getDataBack:function(t,a){return t},formatData:function(t){return a.listData=t&&t.data.map(function(t){return t.taskStatus})||[],0<t.data.filter(function(t){return"Running"==t.taskStatus||"Ready"==t.taskStatus}).length?a.queryStatus(t.pageNumber,t.pageSize):a.timer&&clearTimeout(a.timer),{totalCount:t.totalCount,data:t.data}},getFullDataBack:function(t){t.Error&&"s211030015"==t.Error.Code&&setTimeout(function(){s.alert(t.Error.Message,function(){location.reload()})},300)}}},queryStatus:function(a,s){var r=this;clearTimeout(r.timer),r.taskTable.stopQueryStatus||CRM.util.FHHApi({url:"/EM1HPAASBATCH/task/findGroupTask",data:{pageNumber:a||1,pageSize:s||20,taskType:1,wheres:[]},success:function(t){var e;0==t.Result.StatusCode&&(0<t.Value.data.filter(function(t){return"Finished"!=t.taskStatus}).length?(e=!1,t.Value.data.forEach(function(t,a){t&&t.taskStatus!==r.listData[a].taskStatus&&(e=!0)}),r.timer=e?setTimeout(function(){clearTimeout(r.timer),r.taskTable.setParam({wheres:[]},!0)},5e3):setTimeout(function(){clearTimeout(r.timer),r.queryStatus(a,s)},3e3)):(clearTimeout(r.timer),r.taskTable.setParam({wheres:[]},!0)))}})},getColumns:function(){return[{data:"operation",title:$t("操作类型"),render:function(t){if("GroupImport"==t)return $t("daoruyonghuzu")}},{data:"taskStatus",title:$t("任务状态"),width:200,render:function(t){switch(t){case"Ready":return $t("已准备");case"Running":return $t("执行中");case"Stopped":return $t("已停止");case"Finished":return $t("已完成")}}},{data:"fileName",title:$t("导入文件"),width:200},{data:"userName",width:200,title:$t("创建人")||"--"},{data:"createTime",title:$t("创建时间"),render:function(t){return t?FS.moment(t).format("YYYY-MM-DD HH:mm:ss"):"--"}},{data:"taskId",width:200,title:$t("任务ID")},{data:"taskStatus",title:$t("操作"),lastFixed:!0,render:function(t,a,e){var s="";return s="Finished"===t&&(s='<span class="options-wrapper"><a href="'+FS.util.getFscLink(e.filePath.split(".")[0],e.fileName,!0)+'" target="_blank" class="j-oprate" >'+$t("下载")+"</a></span>",e.rowCount==e.successRowCount)&&0!=e.successRowCount?'<span class="options-wrapper">'+$t("导入成功")+"</span>":s}}]},startDownload:function(t){var a,e;t.filePath&&(a=document.createElement("a"),(e=document.createEvent("HTMLEvents")).initEvent("click",!1,!1),a.download=t.uploadFileName,a.href=FS.util.getFscLink(t.filePath,t.uploadFileName,!0),a.dispatchEvent(e),a.click())}};return t.initTasklistTable(),t.taskTable}});
define("crm-setting/usergroup/selectsearch/selectsearch",["base-modules/utils"],function(e,t,i){var o=e("base-modules/utils");i.exports=Backbone.View.extend({options:{wrapper:null,trigger:"click",position:"absolute",width:null,height:240,zIndex:100,spacing:5,defaultValue:null,parentNode:$("body"),scrollNode:$(window),disabled:!1,options:[],titleTemplate:"{{name}}",optionTemplate:'<li data-value="{{value}}" title="{{name}}">{{name}}</li>'},initialize:function(){var i=this,e=i.options;i.setElement(['<div class="f-g-select" style="width:110px;">','<div class="g-select-title-wrapper" style="border-right:0;border-top-right-radius: 0;border-bottom-right-radius: 0;">','<div class="select-title"></div><i></i>',"</div>","</div>",'<div class="dt-ipt-wrap" style="flex:1;display: flex;border-left: 0;border-top-left-radius: 0;border-bottom-left-radius: 0;padding:2px 5px;">','<span class="line" style="display:block;margin: 3px 10px 3px 0;height: 16px;"></span>',"<input placeholder="+$t("请输入")+' class="dt-ipt"></div><span class="dt-sc-ico j-sc-btn"></span>'].join("")),i.$titleWrapper=i.$(".g-select-title-wrapper"),i.$title=i.$titleWrapper.find(".select-title"),e.wrapper.append(i.$el),i.uuid=_.uniqueId("select_"),i.disabled=e.disabled||!1,i.disabled&&i.$el.addClass("g-select-disabled"),"mouseenter"==e.trigger||"mouseover"==e.trigger?(i.hideTimer=null,i.showTimer=null,i.$el.on("mouseenter",function(){i.disabled||i._hoverShow()}),i.$el.on("mouseleave",function(){i.disabled||i._hoverHide()})):(i.$el.filter(".f-g-select").on(e.trigger,function(e){i.disabled||("show"==i.status?i._hide():i._show(),o.stopPropagation(e,i.uuid))}),$("body").on("click.select"+i.uuid,function(e,t){e=$(e.target);t&&t.target==i.uuid||0==e.closest(i.$el.filter(".f-g-select")).length&&i._hide()})),i.optionTemplate=_.template(e.optionTemplate),i.titleTemplate=_.template(e.titleTemplate),null!==e.defaultValue?i.setValue(e.defaultValue):e.options.length&&i.setValue(e.options[0].value),i.rendered=!1},_render:function(){var t,i=this,e=i.options;i.rendered||(t=[],i.$optionsWrapper||(i.$optionsWrapper=$('<div class="'+e.prefix+'options-wrapper" style="display:none;"></div>'),i.$options=$('<div style="display: inline-block;width: 130px;height: 32px;line-height:32px;color: #999;margin: 0 15px;border-bottom: 1px solid #eee;box-shadow: inset 0 1px 0 0 #eee;">'+$t("选择搜索范围")+'</div><ul class="g-select-options"></ul>').appendTo(i.$optionsWrapper),i._bindEvent()),_.each(i.options.options,function(e){t.push(i.optionTemplate(e))}),i.$optionsWrapper.find(".g-select-options").html(t.join("")),i.$optionsWrapper.appendTo($(i.options.parentNode)),i.$options.find('[data-value="'+i.value+'"]').addClass("state-selected"),i.rendered=!0)},_bindEvent:function(){var e,i=this,t=i.options;i.$optionsWrapper.on("click","li",function(e){var t=$(this).attr("data-value");i.setValue(t),i._hide(),e.stopPropagation()}),$(window).on("resize.select"+i.uuid,function(){clearTimeout(e),e=setTimeout(function(){i.$optionsWrapper.is(":hidden")||i._setPosition()},100)}),"mouseenter"!=t.trigger&&"mouseover"!=t.trigger||(i.$optionsWrapper.on("mouseenter",function(){i._hoverShow()}),i.$optionsWrapper.on("mouseleave",function(){i._hoverHide()}))},_hoverShow:function(){var e=this;clearTimeout(e.hideTimer),e.showTimer=setTimeout(function(){e._show()},250)},_hoverHide:function(){var e=this;clearTimeout(e.showTimer),e.hideTimer=setTimeout(function(){e._hide()},250)},_show:function(){var e=this;e._render(),e._setPosition(),e.$titleWrapper.addClass("g-select-expand"),e.$optionsWrapper.slideDown(200),e.status="show"},_hide:function(){var e=this;"show"==e.status&&(e.$titleWrapper.removeClass("g-select-expand"),e.$optionsWrapper.hide(),e.status="hide")},setValue:function(e,t){var i=this,o=i.options,s=-1;if(e!=i.value){i.$options&&i.$options.find('[data-value="'+e+'"]').addClass("state-selected").siblings().removeClass("state-selected");for(var n,l=0;l<o.options.length;l++)if(o.options[l].value==e){s=l;break}-1<s&&(n=o.options[s],i.$title.html(i.titleTemplate(n)),i.value=e,t||i.trigger("change",n))}},getReturnValue:function(){return{selectValue:this.getValue(),searchValue:this.$el.find(".dt-ipt").val()}},reset:function(){this.$el.find(".dt-ipt").val(""),this.setValue(this.options.options[0]&&this.options.options[0].value,!0)},getValue:function(){return this.value},resetOptions:function(e){var t=this;t.rendered=!1,t.options.options=e,"show"==t.status&&t._render();for(var i=0;i<e.length;i++)if(e[i].value==t.value){t.$title.html(t.titleTemplate(e[i]));break}},addOption:function(e){var t=this;t.options.options.push(e),"show"==t.status?t.$optionsWrapper.find(".g-select-options").append(t.optionTemplate(e)):t.rendered=!1},removeOption:function(e){for(var t=this,i=t.options.options,o=-1,s=0;s<i.length;s++)if(i[s].value==e){o=s;break}-1<o&&(e==t.value&&(1<i.length?t.setValue(i[(o+1)%i.length].value):t.setValue(null)),i.splice(o,1),"show"==t.status?t.$options.find('[data-value="'+e+'"]').remove():t.rendered=!1)},setDisable:function(e){(this.disabled=e=e||!1)?this.$el.addClass("g-select-disabled"):this.$el.removeClass("g-select-disabled")},_setPosition:function(){var e,t,i=this,o=i.options,s=o.position,n=o.spacing,l=o.parentNode.closest(".ui-scrollbar"),a=l&&l[0]?l:o.scrollNode,r=a.offset()?a.offset().top:0,p=$(o.parentNode).offset(),d=i.$el,u=d.offset(),h=i.$optionsWrapper.height();"fixed"==s?(t=u.left-a.scrollLeft()-p.left,(e=u.top-a.scrollTop()+d.height()+n-p.left)<0&&(e=u.top-a.scrollTop()-h-n)):(s="absolute",e=u.top+d.height()+n-p.top,t=u.left-p.left,d=h>o.height?o.height:h,-10<e+p.top-r-a.height()&&(p.top-r<d?l&&0<l.length&&setTimeout(function(){$(".scroll-content",l.parent()).scrollTop(40)},200):e=u.top-d-n-p.top)),i.$optionsWrapper.css({position:s,top:e,left:t,zIndex:o.zIndex}),i.$options.css({"max-height":o.height})},destroy:function(){var e=this.uuid;this.trigger("destroy"),this.undelegateEvents(),this.$optionsWrapper&&this.$optionsWrapper.remove(),$("body").off(".select"+e),$(window).off("resize.select"+e),this.remove()}})});
define("crm-setting/usergroup/template/edit-usergroup-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-g-form"> <div class="fm-item"> <label class="fm-lb"><em>*</em>' + ((__t = $t("组名称")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-translate-input"></div> <!-- <input maxlength="20" class="b-g-ipt fm-ipt name" ';
            if (data) {
                __p += 'value="' + ((__t = data.name) == null ? "" : __t) + '"';
            } else {
                __p += 'placeholder="' + ((__t = $t("最多20个中文字符")) == null ? "" : __t) + '"';
            }
            __p += '> --> </div> </div> <div class="fm-item fn-clear mn-checkbox-box"> <label class="fm-lb"><em></em>' + ((__t = $t("组成员")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <div class="j-selector"></div> </div> </div> <div class="fm-item"> <label class="fm-lb"><em></em>' + ((__t = $t("备注信息")) == null ? "" : __t) + '</label> <div class="fm-wrap"> <textarea class="b-g-ipt fm-ipt remark" maxlength="1000">';
            if (data) {
                __p += (__t = data.description) == null ? "" : __t;
            }
            __p += "</textarea> </div> </div> </div>";
        }
        return __p;
    };
});
define("crm-setting/usergroup/template/import-rules-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<fx-dialog class="dialog-datapermissions-datashare-import_rules" :class="[!beforeImport?\'after-import\':\'\']" :visible.sync="show" size="small" max-height="400px" title="' + ((__t = $t("daoruyonghuzu")) == null ? "" : __t) + '"> <div v-show="beforeImport"> <div style="color: #545861;">' + ((__t = $t("请添加您要导入的数据")) == null ? "" : __t) + '</div> <div><a :href="downloadUrl">' + ((__t = $t("下载数据模板")) == null ? "" : __t) + '</a></div> <div ref="uploadBox" class="uploadBox"> <div v-show="!hasFile"> <i class="icon fx-icon-upload"></i> <span>' + ((__t = $t("将文件拖到此处或")) == null ? "" : __t) + '<a href="javascript:;" @click="onClickUpload">' + ((__t = $t("点击上传")) == null ? "" : __t) + '</a></span><br /> <span class="note">%note%</span> </div> <div v-show="hasFile"> <i class="icon el-icon-circle-check"></i> <span>' + ((__t = $t("文件已添加")) == null ? "" : __t) + '</span><br /> <span class="note note2">%filename%</span> &nbsp;&nbsp;<a href="javascript:;" @click="onClickUpload">' + ((__t = $t("重新上传")) == null ? "" : __t) + '</a> </div> </div> <input type="file" ref="fileInput" accept=".xlsx,.xls" style="display: none" /> </div> <div v-show="!beforeImport"> <div v-show="percentage<100" style="text-align: center; padding: 16px 0">' + ((__t = $t("正在收集数据")) == null ? "" : __t) + '</div> <fx-progress v-show="percentage<100" :percentage="percentage" :stroke-width="10" color="#4D8CE6" :show-text="false" style="margin-bottom: 30px"></fx-progress> <div v-show="percentage>=100" style="padding: 26px 0;">%progressResult%</div> </div> <template slot="footer"> <fx-button type="primary" :disabled="disabled" size="small" @click="onStartImport">' + ((__t = $t("开始导入")) == null ? "" : __t) + '</fx-button> <fx-button size="small" @click="onCancel">' + ((__t = $t("取消")) == null ? "" : __t) + "</fx-button> </template> </fx-dialog>";
        }
        return __p;
    };
});
define("crm-setting/usergroup/usergroup",["crm-modules/common/util","crm-widget/table/table","./selectsearch/selectsearch","./editusergroup/editusergroup","./exportlist","./importlist","./template/import-rules-html","base-h5uploader"],function(t,e,s){var r=t("crm-modules/common/util"),i=t("crm-widget/table/table"),a=(t("./selectsearch/selectsearch"),t("./editusergroup/editusergroup")),o=t("./exportlist"),n=t("./importlist"),l=t("./template/import-rules-html"),p=t("base-h5uploader");s.exports=Backbone.View.extend({initialize:function(t){this.widgets={},t.wrapper.append('<div class="table-wrap table-wrap1"></div><div class="table-wrap table-wrap2" style="display: none"><div class="table-box-header"><a href="javascript:;" class="btn-back-data-list">&lt; '+$t("usergroup_back_to_list")+'</a><div class="operationType"><span>'+$t("操作类型")+': </span><div class="select"></div></div></div><div class="table-box"></div></div>'),this.tableWrapper1=t.wrapper.find(".table-wrap1"),this.tableWrapper2=t.wrapper.find(".table-wrap2"),this.tableWrapper2Table=t.wrapper.find(".table-box"),this.setElement(this.tableWrapper1),this.initTable(),this.tableWrapper1.find(".btn-export-list").on("click",this.showExportList.bind(this)),this.tableWrapper2.find(".btn-back-data-list").on("click",this.hideExportList.bind(this)),this.getDownloadDataShareTemplate()},events:{"click .j-add":"onAdd","click .j-stop":"onStop","click .j-start":"onStar","click .j-del":"onDel","click .j-export":"onExport","click .j-import":"onImport","click .view-export-list":"showExportList","click .btn-back-data-list":"hideExportList"},initTable:function(){var a=this;a.dt=new i({$el:a.$el,title:$t("crm.用户组"),url:"/EM1HCRMUdobj/groupApi/groupList",requestType:"FHHApi",trHandle:!1,showMultiple:!0,postData:{status:0},searchTerm:{pos:"C",type:"_status",showManage:!1,showCustom:!1,options:[{id:"9999",name:$t("全部"),type:"1",isNotRequest:!0},{id:0,isdef:!0,name:$t("启用中"),isNotRequest:!0},{id:1,name:$t("已停用"),isNotRequest:!0}]},operate:{btns:[{text:$t("新建用户组"),className:"j-add"},{text:$t("导入"),className:"j-import"},{text:$t("导出"),className:"j-export"},{text:$t("查看")+$t("导入导出结果"),className:"view-export-list"}]},batchBtns:[{text:$t("停用"),className:"j-stop"},{text:$t("启用"),className:"j-start"},{text:$t("删除"),className:"j-del"}],columns:[{data:"name",title:$t("组名"),width:240,isOrderBy:!0,render:function(t,e,s){return 1===s.status?'<span style="color:#ccc;">'+t+"</span>":t||"--"}},{data:"groupUsers",title:$t("组成员"),width:334,render:function(t,e,s){var a=[],i=(_.each(s.groupUsers||[],function(t){var t=r.getEmployeeById(t,!0,!0),e="--";t&&(e=t.name,t.isStop)&&(e+="("+$t("已停用")+")"),a.push(e)}),a.join("、"));return 1===s.status?'<span style="color:#ccc;" title="'+i+'">'+(i||"--")+"</span>":'<span title="'+i+'">'+(i||"--")+"</span>"}},{data:"description",title:$t("备注"),isOrderBy:!0,render:function(t,e,s){return 1===s.status?'<span style="color:#ccc;">'+t+"</span>":t||"--"}},{data:"status",title:$t("状态"),isOrderBy:!0,render:function(t,e,s){return 1===t?'<span style="color:#ccc;">'+$t("已停用")+"</span>":$t("启用中")}},{data:null,title:$t("操作"),lastFixed:!0,render:function(t,e,s){return 1===s.status?'<div class="btns"><a class="j-del-btn">'+$t("删除")+'</a><a class="j-start-btn"> '+$t("启用")+"</a></div>":'<div class="btns"><a class="j-edit">'+$t("编辑")+'</a><a class="j-stop-btn">'+$t("停用")+"</a></div>"}}],search:{pos:"C",placeHolder:$t("搜索"),type:"keyword",filterColumns:[{title:$t("组名"),data:"name",isFilter:!0,dataType:1},{title:$t("组成员"),data:"groupUsers",isFilter:!0,dataType:1}]},sortField:"orderKey",sortType:"isAsc",paramFormat:function(t){var e=null==(e=a.dt)||null==(e=e._search)||null==(e=e.searchComp)||null==(e=e.selectIntance)?void 0:e.getValue();return e&&(t.searchType="name"==e?"0":"1"),t.isAsc&&("1"==t.isAsc&&(t.isAsc=!0),"2"==t.isAsc)&&(t.isAsc=!1),a.tableParam=t},formatData:function(e){return _.each(e.list,function(t){t.groupUsers=e.groupUsers[t.id]}),{data:e.list,totalCount:e.page?e.page.totalCount:0}}}),a.dt.on("term.change",function(t){a.dt.setParam({status:"9999"!=t?t:null},!0,!0)}),a.dt.on("trclick",function(t,e,s){s.hasClass("j-start-btn")?a.onStar(t):s.hasClass("j-stop-btn")?a.onStop(t):s.hasClass("j-edit")?a.onEdit(t):s.hasClass("j-del-btn")&&a.onDel(t)})},onAdd:function(){var t=this;t.EditUserGroupWidget&&(t.EditUserGroupWidget.destroy(),t.EditUserGroupWidget=null),t.EditUserGroupWidget=new a({title:$t("新建用户组"),type:"add"}),t.EditUserGroupWidget.on("success",function(){t.refresh()}),t.EditUserGroupWidget.show()},onEdit:function(t){var e=this,s=(e.EditUserGroupWidget&&(e.EditUserGroupWidget.destroy(),e.EditUserGroupWidget=null),e.EditUserGroupWidget=new a({title:$t("编辑用户组"),type:"edit",groupId:t.id}),e.EditUserGroupWidget.on("success",function(){e.refresh()}),{member:[],stop:[]});(t.groupUsers||[]).forEach(function(t){t=r.getEmployeeById(t,!0,!0);t&&(t.isStop?s.stop:s.member).push(t.id)}),e.EditUserGroupWidget.show({data:t,groupUsers:s})},onStop:function(t){var e=this,s=e.dt.getCheckedData(),a=[];_.each(s,function(t,e){a.push(t.id)}),r.FHHApi({url:"/EM1HCRMUdobj/groupApi/updateGroupStatus",data:{groupIds:t.id||a.join(","),stopStatus:!0},success:function(t){0==t.Result.StatusCode&&(r.remind(1,$t("操作成功")),e.refresh())}})},onStar:function(t){var e=this,s=e.dt.getCheckedData(),a=[];_.each(s,function(t,e){a.push(t.id)}),r.FHHApi({url:"/EM1HCRMUdobj/groupApi/updateGroupStatus",data:{groupIds:t.id||a.join(","),stopStatus:!1},success:function(t){0==t.Result.StatusCode&&(r.remind(1,$t("操作成功")),e.refresh())}})},onDel:function(t){var e=this,s=e.dt.getCheckedData(),a=[],i=(_.each(s,function(t,e){a.push(t.id)}),r.confirm($t("确定删除用户组")+"?",$t("删除"),function(){r.FHHApi({url:"/EM1HCRMUdobj/groupApi/deleteGroups",data:{groupIds:t.id||a.join(",")},success:function(t){0==t.Result.StatusCode&&(r.remind(1,$t("操作成功")),i.destroy(),e.refresh())}})}))},onImport:function(){var t,e=this,s="javascript:;",a=(e.downloadDataShareTemplate&&(t=e.downloadDataShareTemplate[1],s=FS.util.getFscLink(t.filePath.split(".")[0],t.fileName,!0)),FxUI.create({template:l().replace("%filename%","{{filename}}").replace("%note%",$t("导入数据上限为{{n}}条",{data:{n:100}})).replace("%progressResult%",$t("您可以在{{str}}中查看导出进度",{data:{str:'<a href="javascript:;" @click="viewResult">'+$t("导入导出结果")+"</a>"}})),data:function(){return{show:!0,downloadUrl:s,hasFile:!1,filename:"",filepath:"",beforeImport:!0,percentage:0,disabled:!0}},watch:{show:function(t){t||(e.h5Uploader=null)}},methods:{initUpload:function(){e.h5Uploader||(e.h5Uploader=new p({multiple:!1,accept:".xlsx,.xls",autoPrependPath:!1,fileInput:this.$refs.fileInput,dragDrop:this.$refs.uploadBox,url:FS.BASE_PATH+"/FSC/EM/File/UploadByStream",timeout:180,onSelect:function(t){e.h5Uploader.startUpload(),a.uploadLoading=FxUI.Loading.service({target:a.$refs.uploadBox})},onSuccess:function(t,e){a.filename=t.name,a.hasFile=!0,a.filepath=JSON.parse(e).TempFileName,a.disabled=!1},onFailure:function(t){FxUI.MessageBox.alert($t("上传文件失败"),{type:"error"})},onComplete:function(){a.uploadLoading&&a.uploadLoading.close(),e.h5Uploader.removeAllFile()}}))},onDataSourceChange:function(t){e.downloadDataShareTemplate?(t=e.downloadDataShareTemplate[t],this.downloadUrl=FS.util.getFscLink(t.filePath.split(".")[0],t.fileName,!0)):this.downloadUrl="javascript:;"},onClickUpload:function(){this.$refs.fileInput.click()},onStartImport:function(){var t;this.filename&&this.filepath&&(this.beforeImport=!1,t=setInterval(function(){100<=a.percentage?clearInterval(t):(a.percentage+=.3,100<a.percentage&&(a.percentage=100))},150),FS.util.FHHApi({url:"/EM1HPAASBATCH/task/importGroupTask/create",data:{importGroupTaskConfig:{argType:1,fileName:this.filename,file_path:this.filepath}},success:function(t){t.Result.StatusCode},fail:function(){},complete:function(){clearInterval(t),t=setInterval(function(){100<=a.percentage?clearInterval(t):(a.percentage+=.3,100<a.percentage&&(a.percentage=100))},10)}}))},onCancel:function(){this.show=!1,this.reset()},reset:function(){this.hasFile=!1,this.filename="",this.filepath="",this.beforeImport=!0,this.percentage=0,this.disabled=!0},viewResult:function(){this.show=!1,this.reset(),this.$nextTick(function(){e.showExportList("import")})}},mounted:function(){this.initUpload()}}))},onExport:function(){var e=this;r.FHHApi({url:"/EM1HPAASBATCH/task/exportGroupTask/create",data:{exportGroupTaskConfig:{argType:1,arg:e.tableParam||{}}},success:function(t){0==t.Result.StatusCode&&t.Value&&FxUI.create({template:'<fx-dialog  :visible.sync="show" size="small" max-height="400px" :append-to-body="true" title="'+$t("daochuyonghuzu")+'" ><div v-if="!progressStop"><p style="text-align:center;padding: 20px 0 6px;">{{$t("正在收集数据")}}...</p><br><fx-progress :percentage="percentage" color="#4D8CE6" :stroke-width="10" :show-text="false" style="margin-bottom: 44px;"></fx-progress></div><div v-else><p style="padding: 20px 0 30px;">'+$t("您可以在{{str}}查看导出进度。",{data:{str:'<a href="javascript:;" @click="showExportList">'+$t("usergroup_exportlist")+"</a>"}})+"</p></div></fx-dialog>",data:function(){return{show:!1,percentage:0,progressStop:!0}},watch:{show:function(t){t||clearInterval(this.timer)}},methods:{showExportList:function(){this.show=!1,this.$nextTick(function(){e.showExportList("export")})}},mounted:function(){this.show=!0}})},fail:function(t){t.Result.FailureMessage&&r.alert(t.Result.FailureMessage)}},{errorAlertModel:1})},showExportList:function(t){-1==["export","import"].indexOf(t)&&(t="import");var e=this;this.tableWrapper2.show(),this.tableWrapper1.find(".dt-op-box").hide(),this.widgets.exportList&&(this.widgets.exportList.stopQueryStatus=!0,this.widgets.exportList.destroy()),this.widgets.exportList=("export"==t?o:n)({$wrap:this.tableWrapper2Table}),this.widgets.operationType?this.widgets.operationType.val=t:this.widgets.operationType=FxUI.create({wrapper:".operationType > .select",replaceWrapper:!0,template:'<fx-select v-model="val" :options="options" size="small" @change="onChange"></fx-select>',data:function(){return{val:"export"==t?"export":"import",options:[{value:"import",label:$t("daoruyonghuzu")},{value:"export",label:$t("daochuyonghuzu")}]}},methods:{onChange:function(t){e.widgets.exportList&&(e.widgets.exportList.stopQueryStatus=!0,e.widgets.exportList.destroy()),e.widgets.exportList=("export"==t?o:n)({$wrap:e.tableWrapper2Table})}}})},hideExportList:function(){this.tableWrapper2.hide(),this.tableWrapper1.find(".dt-op-box").show(),this.widgets.exportList.stopQueryStatus=!0,this.widgets.exportList.destroy(),this.widgets.exportList=null},getDownloadDataShareTemplate:function(){var e=this;r.FHHApi({url:"/EM1HPAASBATCH/task/importGroupTask/downloadGroupTemplate",data:{argType:1},success:function(t){0==t.Result.StatusCode&&(e.downloadDataShareTemplate={},(t.Value.result||[]).forEach(function(t){e.downloadDataShareTemplate[t.argType]=t}))},fail:function(t){}},{errorAlertModel:0})},refresh:function(){FS.MEDIATOR.trigger("selector.usergroup.update"),this.dt.setParam({},!0)},destroy:function(){_.each(["dt","selectSearch","EditUserGroupWidget"],function(t){this[t]&&this[t].destroy(),this[t]&&(this[t]=null)},this)}})});