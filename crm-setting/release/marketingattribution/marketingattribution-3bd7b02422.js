define("crm-setting/marketingattribution/marketingattribution",["crm-modules/common/util","./tpl-html"],function(e,t,a){var i=e("crm-modules/common/util"),n=e("./tpl-html");return Backbone.View.extend({initialize:function(e){this.setElement(e.wrapper)},render:function(){var t=this;t.$el.html(n()),this.getConfig().done(function(e){t.oppoLeads=e,t.renderRadioTpl(e)})},events:{"click .j-save":"saveHandle"},renderRadioTpl:function(e){var t=this;window.FxUI.create({wrapper:".leads-wrapper",template:'<fx-radio-group  v-model="value"><fx-radio v-for="data in oppoLeads" :key="data.value" :label="data.value" v-on:change="change(value)">{{data.label}}</fx-radio></fx-radio-group>',data:function(){return{value:e,oppoLeads:[{label:$t("自动关联第一个转客户且未转为商机的线索"),value:"1"},{label:$t("自动关联最近一个转客户且未转为商机的线索"),value:"2"},{label:$t("不自动关联线索"),value:"0"}]}},methods:{change:function(e){t.oppoLeads=e}}})},getConfig:function(){return new Promise(function(a,e){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/get_config_values",data:{isAllConfig:!1,keys:["new_opportuntiy_leads_setting"]},success:function(e){var t;0===e.Result.StatusCode?e.Value&&e.Value.values&&(t=(t=e.Value.values[0])&&t.value,a(t)):i.alert(e.Result.FailureMessage)}},{errorAlertModel:1})})},saveHandle:function(){this.oppoLeads?this.setConfig(this.oppoLeads):CRM.util.alert($t("请选择一项"))},setConfig:function(e){var t=this,a=null,i="0"==e?$t("确认不自动关联？"):$t("确认自动关联？"),a=CRM.util.confirm(i,"",function(){CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"new_opportuntiy_leads_setting",value:e},success:function(e){0===e.Result.StatusCode?CRM.util.remind(1,$t("设置成功")):(t.render(),CRM.util.alert(e.Result.FailureMessage))},complete:function(){a.hide()}},{errorAlertModel:1})})}})});
define("crm-setting/marketingattribution/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-setting-oppoleads"> <div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("营销归因设置")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con crm-scroll"> <h3 class="title">' + ((__t = $t("商机归因")) == null ? "" : __t) + '</h3> <div class="leads-wrapper"></div> <div class="crm-btn crm-btn-primary j-save">' + ((__t = $t("保存")) == null ? "" : __t) + "</div> </div> </div>";
        }
        return __p;
    };
});