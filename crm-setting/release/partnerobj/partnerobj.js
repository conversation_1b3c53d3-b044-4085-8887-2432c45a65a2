function asyncGeneratorStep(t,e,n,a,i,o,r){try{var s=t[o](r),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(a,i)}function _asyncToGenerator(s){return function(){var t=this,r=arguments;return new Promise(function(e,n){var a=s.apply(t,r);function i(t){asyncGeneratorStep(a,e,n,i,o,"next",t)}function o(t){asyncGeneratorStep(a,e,n,i,o,"throw",t)}i(void 0)})}}define("crm-setting/partnerobj/partner_repeat_rule/partner_repeat_rule",["crm-widget/dialog/dialog"],function(t,e,n){var a,i=t("crm-widget/dialog/dialog").extend({attrs:{width:640,className:"crm-s-customer-follow",title:$t("添加跟进行为"),showBtns:!0,content:'<div class="follow-box"><div class="follow-objTitle">'+$t("所属对象")+'</div><div class="follow-objSelect" style="width:280px;"></div><div class="follow-wrap"></div></div>'},events:{"click .b-g-btn-cancel":"hide","click .b-g-btn":"saveData"},saveData:function(){this.trigger("suc",_.omit(this.state,["filterData"])),this.hide()},getObjectList:function(){var n=this;return new Promise(function(e){n.objectList?e(n.objectList):CRM.util.ajax_base("/EM1HNCRM/API/v1/object/partner_rule/service/object_list",{}).then(function(t){n.objectList=n.mapData(t.result||[]),n.objectList=n.filterApiName(n.objectList),e(n.objectList)})})},mapData:function(t){return t.map(function(t){return{name:t.display_name,value:t.api_name}})},filterApiName:function(t){var e=this;return _.filter(t,function(t){return!e.state.filterData.includes(t.value)})},createObjSelect:(a=_asyncToGenerator(regeneratorRuntime.mark(function t(){var e,n;return regeneratorRuntime.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return e=this,t.next=3,this.getObjectList();case 3:return n=t.sent,t.abrupt("return",FxUI.create({wrapper:$(".follow-objSelect",e.$el)[0],template:' <fx-select\n\t\t\t\t\t\t\t\tref="select"\n\t\t\t\t\t\t\t\tv-model="value"\n\t\t\t\t\t\t\t\t:options="options"\n\t\t\t\t\t\t\t\tsize="small"\n\t\t\t\t\t\t\t\tstyle="width:240px"\n\t\t\t\t\t\t\t\t@change="onChange"\n\t\t\t\t\t\t\t></fx-select>',data:function(){return{options:[],value:""}},created:function(){this.options=_.map(n,function(t){return{label:t.name,value:t.value}}),this.value||(this.value=this.options[0]&&this.options[0].value),e.updateSelectValue(this.value)},methods:{onChange:function(t){e.updateSelectValue(t)}}}));case 5:case"end":return t.stop()}},t,this)})),function(){return a.apply(this,arguments)}),updateSelectValue:function(e){var t;e&&(this.setState("object_api_name",e),t=_.find(this.objectList,function(t){return t.value===e}).name,this.setState("displayName",t))},setState:function(t,e){this.state[t]=e},show:function(t,e,n){var a=i.superclass.show.call(this);return this.type=e,this.state=_.extend({},t||{}),this.state.filterData=n||[],"add"===e&&(this.$(".follow-objTitle").show(),this.createObjSelect()),this.createCheckbox(e),this.resizedialog(),a},createCheckbox:function(){var e=this;this.checkboxCom&&this.checkboxCom.destroy(),this.checkboxCom=FxUI.create({wrapper:this.element.find(".follow-wrap")[0],template:'<fx-checkbox-group v-model="checkList" @change="onChange">\n\t\t\t\t\t\t\t <fx-checkbox class="cpq-pricebook-config-def" label="join_team">'.concat($t("添加团队成员"),' </fx-checkbox>\n\t\t\t\t\t\t\t <br/>\n\t\t\t\t\t\t\t <fx-checkbox label="change_partner_owner">').concat($t("更换外部负责人"),"</fx-checkbox>\n\t\t\t\t\t\t  </fx-checkbox-group>"),data:function(){var t=[];return e.state.join_team&&t.push("join_team"),e.state.change_partner_owner&&t.push("change_partner_owner"),{checkList:t}},methods:{onChange:function(t){e.afterChangeRadio(t)}}})},afterChangeRadio:function(t){this.setState("join_team",t.includes("join_team")),this.setState("change_partner_owner",t.includes("change_partner_owner"))},hide:function(){var t=i.superclass.hide.call(this);return this.destroy(),t},destroy:function(){var t=i.superclass.destroy.call(this);return this.select&&this.select.destroy(),this.checkboxCom&&this.checkboxCom.destroy(),t}});n.exports=i});
function asyncGeneratorStep(e,t,n,r,a,i,o){try{var s=e[i](o),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,a)}function _asyncToGenerator(s){return function(){var e=this,o=arguments;return new Promise(function(t,n){var r=s.apply(e,o);function a(e){asyncGeneratorStep(r,t,n,a,i,"next",e)}function i(e){asyncGeneratorStep(r,t,n,a,i,"throw",e)}a(void 0)})}}define("crm-setting/partnerobj/partnerobj",["crm-modules/common/util","./template/partner_switch-html","./template/partner_repeatrule-html","./partner_repeat_rule/partner_repeat_rule","./template/partner_address-html","crm-setting/hierarchicalconfig/hierarchicalconfig","./template/tpl-html"],function(e,t,n){var r,a,i,o,s=e("crm-modules/common/util"),c=e("./template/partner_switch-html"),l=e("./template/partner_repeatrule-html"),u=e("./partner_repeat_rule/partner_repeat_rule"),p=e("./template/partner_address-html"),d=e("crm-setting/hierarchicalconfig/hierarchicalconfig");return Backbone.View.extend({template:e("./template/tpl-html"),initialize:function(e){this.setElement(e.wrapper),this.init()},events:{"click .isopen":"showTip","click .j-reload-config":"_reloadHandle","click .j-switch-tab":"switchTab"},init:function(){this.$el.html(this.template()),this.render()},switchTab:function(e){e=$(e.target);e.parent().children().removeClass("cur"),e.addClass("cur"),this.render(e.attr("data-render"))},render:(o=_asyncToGenerator(regeneratorRuntime.mark(function e(){var t,n,r,a,i=arguments;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:t=0<i.length&&void 0!==i[0]?i[0]:"partnerSwitch",(n=this).tarTab=t||"partnerSwitch","partnerSwitch"!==t?e.next=7:(Promise.all([this.getConfig(),this.getConfigVal("48"),this.getConfigVal("partner_subsidiary")]).then(function(e){e={start:e[0],isOpen:"open"===e[0]||"opening"===e[0],allowModify:"1"==e[1][0],isSub:"1"==e[2][0]};e.isOpen&&n.tipShow(),n.renderTpl_switch(e)}),e.next=27);break;case 7:if("partnerRepeatRule"===t)return n.$el.find(".render-content").html(l()),e.next=11,this.getRepeatRule();e.next=16;break;case 11:r=e.sent,n.renderTable_repeat(r.result||[]),n.addPartnerRule(),e.next=27;break;case 16:"partnerLevels"!==t?e.next=20:(new d({apiname:"PartnerObj",el:n.$(".render-content")}),e.next=27);break;case 20:if("partnerAddress"===t)return e.next=23,n.getConfigVal("partner_address");e.next=27;break;case 23:r=e.sent,a={isOpen:"1"==r,flag:"1"},n.$el.find(".render-content").html(p()),n.switchTrans(a);case 27:case"end":return e.stop()}},e,this)})),function(){return o.apply(this,arguments)}),renderTpl_switch:function(t){var n=this;CRM.util.getPermissionByApiName(["NewOpportunityObj"]).done(function(){var e=1==CRM.get("permission").NewOpportunityObj;n.$el.find(".render-content").html(c(_.extend(t,{newoppo:e}))),n.switchTrans(t),t.isOpen&&n.checkBoxTrans(t)})},getConfig:function(e,t){var r=this;if("partnerSwitch"==r.tarTab)return new Promise(function(n,e){s.FHHApi({url:"/EM1HNCRM/API/v1/object/partner_init/service/status_open",success:function(e){var t="error";0===e.Result.StatusCode&&(t=e.Value.result),n(t)},complete:function(){r._getAjax=null}},{errorAlertModel:1,submitSelector:t})})},showTip:function(){"partnerSwitch"==this.tarTab?s.alert($t("合作伙伴已开启无法关闭")):"partnerAddress"==this.tarTab&&s.alert($t("合作伙伴多地址已开启无法关闭"))},getConfigVal:function(e){return new Promise(function(t,n){s.getConfigValue(e).then(function(e){t(e)},function(e){n(e)})})},setBasicConfig:function(e){s.setConfigValue(e).then(function(){s.remind(1,$t("设置成功"))},function(e){s.remind(3,$t("设置失败"))})},addPartnerRule:function(){var e=this;return FxUI.create({wrapper:$(".behavior-btn-box",e.$el)[0],template:' <fx-button size="mini" @click="add" type="primary">'.concat($t("添加"),"</fx-button>"),data:function(){return{}},methods:{add:function(){e.showPartnerRule()}}})},renderTable_repeat:function(t){var r=this;this.table&&this.table.destroy(),e.async("crm-widget/table/table",function(e){r.table=new e({$el:r.$el.find(".partner-repeatrule-table"),showPage:!1,doStatic:!0,colResize:!0,columns:[{data:"display_name",title:$t("对象名称")},{data:"behavior",title:$t("操作行为"),render:function(e,t,n){var r=[];return n.change_partner_owner&&r.push($t("更换外部负责人")),n.join_team&&r.push($t("添加团队成员")),r.length?r.map(function(e){return"<span>".concat(e,"</span>")}).join(",").replace(",","、 "):"--"}},{data:null,title:$t("操作"),width:"100px",render:function(e,t,n){return'<span class="btn js-edit">'+$t("编辑")+'</span><span class="btn js-del">'+$t("删除")+"</span>"}}]}),r.table.on("trclick",function(e,t,n){n.hasClass("js-edit")?r.showPartnerRule("edit",e):n.hasClass("js-del")&&r.deleteRow(e)}),r.table.doStaticData(t)})},getRepeatRule:function(){return s.ajax_base("/EM1HNCRM/API/v1/object/partner_rule/service/list",{})},showPartnerRule:function(){var t=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"add",e=1<arguments.length?arguments[1]:void 0,n=this,r="add"===t?$t("添加操作行为"):$t("编辑操作行为");n.fb&&n.fb.destroy(),n.fb=new u({title:r}),n.fb.on("suc",function(e){e.object_api_name&&n.updateData(e,t)}),n.fb.show(e,t,n.getTableData())},updateData:(i=_asyncToGenerator(regeneratorRuntime.mark(function e(t,n){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("add"===n)return e.next=3,this.addRepeatRule({object_api_name:t.object_api_name,join_team:null!=t.join_team&&t.join_team,change_partner_owner:null!=t.change_partner_owner&&t.change_partner_owner});e.next=7;break;case 3:e.sent&&this.refreshTable(),e.next=11;break;case 7:return e.next=9,this.editRepeatRule({id:t.id,object_api_name:t.object_api_name,join_team:t.join_team,change_partner_owner:t.change_partner_owner});case 9:e.sent&&this.refreshTable();case 11:case"end":return e.stop()}},e,this)})),function(e,t){return i.apply(this,arguments)}),addRepeatRule:function(e){return s.ajax_base("/EM1HNCRM/API/v1/object/partner_rule/service/add",e)},editRepeatRule:function(e){return s.ajax_base("/EM1HNCRM/API/v1/object/partner_rule/service/update",e)},delRepeatRule:function(e){return s.ajax_base("/EM1HNCRM/API/v1/object/partner_rule/service/delete",e)},deleteRow:(a=_asyncToGenerator(regeneratorRuntime.mark(function e(t){return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.delRepeatRule({id:t.id});case 2:e.sent&&this.refreshTable();case 4:case"end":return e.stop()}},e,this)})),function(e){return a.apply(this,arguments)}),getTableData:function(){var t=[];return _.each(this.table.getCurData()||[],function(e){t.push(e.object_api_name)}),t},refreshTable:(r=_asyncToGenerator(regeneratorRuntime.mark(function e(){var t;return regeneratorRuntime.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.getRepeatRule();case 2:t=e.sent,this.table.doStaticData(t.result||[]);case 4:case"end":return e.stop()}},e,this)})),function(){return r.apply(this,arguments)}),tipShow:function(){$(".tab-show-none").css("display","block")},checkBoxTrans:function(e){var t=this;t.modifyName&&t.modifyName.destroy(),t.companySet&&t.companySet.destroy(),t.modifyName=FxUI.create({wrapper:$(".mn-checkbox-box",t.$el)[0],template:" <fx-checkbox\n\t\t\t\tv-model=\"value\"\n\t\t\t\t@change='onChange'\n                 >".concat($t("允许修改合作伙伴名称"),"</fx-checkbox>"),data:function(){return{value:e.allowModify}},methods:{onChange:function(e){t.setBasicConfig({key:"48",value:e?"1":"0"})}}}),t.companySet=FxUI.create({wrapper:$(".mn-company-set",t.$el)[0],template:" <div class=\"append\">\n\t\t\t\t\t<fx-checkbox\n\t\t\t\t\tv-model=\"value\"\n\t\t\t\t\t@change='onChange'\n\t\t\t\t\t:disabled='isChecked'\n\t\t\t\t\t>".concat($t("设置该业务类型为分子公司"),'</fx-checkbox>\n\t\t\t\t\t<fx-tooltip popper-class="crm-s-partnerobj-maxWidth" :effect="effectType" :content="text"" placement="right" transition="">\n\t\t\t\t\t\t<i class=\'fx-icon-question\'></i>\n\t\t\t\t\t</fx-tooltip>\n\t\t\t\t </div>\n\t\t\t\t'),data:function(){return{value:e.isSub,text:$t("crm.partnerobj.text"),isChecked:e.isSub}},methods:{onChange:function(e){e&&(this.isChecked=!0),t.setBasicConfig({key:"partner_subsidiary",value:e?"1":"0",oldValue:e?"0":"1"})}}})},switchTrans:function(a){var n=this;return FxUI.create({wrapper:$(".on-off",n.$el)[0],template:' <fx-switch\n\t\t\t\t\tv-model="value"\n\t\t\t\t\t:class="hasOpen"\n\t\t\t\t\t@change=\'onChange\'\n\t\t\t\t\tsize="small"\n\t\t\t\t\t:disabled="disabled"\n\t\t\t\t\t:before-change="beforeChanage"\n\t\t\t\t\t></fx-switch>',data:function(){return{value:a.isOpen}},computed:{disabled:function(){return this.value},hasOpen:function(){return this.value?"isopen":""}},methods:{onChange:function(){var t=this;"partnerSwitch"==n.tarTab?s.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"37",value:"1",oldValue:""},success:function(e){0==e.Result.StatusCode?(s.remind(1,$t("已开通，正在初始化配置，完成后会通过CRM提醒通知您，请知悉！")),t.value=!0,n.render(),CRM.control.refreshAside()):s.alert($t("启用失败请稍后重试或联系纷享客服"))},complete:function(){n.isSetting=!1}},{errorAlertModel:1}):"partnerAddress"==n.tarTab&&CRM.util.FHHApi({url:"/EM1HNCRM/API/v1/object/biz_config/service/set_config_value",data:{key:"partner_address",value:"1"},success:function(e){0===e.Result.StatusCode?(CRM.util.remind(1,$t("设置成功")),t.value=!0):CRM.util.alert(e.Result.FailureMessage)},complete:function(){}},{errorAlertModel:1})},beforeChanage:function(){var e,t=this,n=(e="1"==a.flag?"<p style= 'font-size:14px;font-weight:600'>".concat($t("crm.partneraddres.confirm"),"</p><p \t\tstyle='color:red'>").concat($t("crm.partneraddres.info"),"</p>"):$t("启用合作伙伴后将无法关闭确认启用吗"),$t("提示")),r=s.confirm(e,n,function(){r.destroy(),t.onChange()})}}})}})});
define("crm-setting/partnerobj/template/partner_address-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("crm.partneraddres.li1")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.partneraddres.li2")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.partneraddres.li3")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("crm.partneraddres.li4")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("crm.partneraddres.li5")) == null ? "" : __t) + "</li> <li>6." + ((__t = $t("crm.partneraddres.li6")) == null ? "" : __t) + "</li> <li>7." + ((__t = $t("crm.partneraddres.li7")) == null ? "" : __t) + '</li> </ul> </div> <div class="on-off"> <label class="title">' + ((__t = $t("crm.partneraddres")) == null ? "" : __t) + "</label> </div>";
        }
        return __p;
    };
});
define("crm-setting/partnerobj/template/partner_repeatrule-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="partner-repeatrule"> <div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>" + ((__t = $t("伙伴重复数据处理说明")) == null ? "" : __t) + '</li> </ul> </div> <div class="behavior-btn-box"></div> <div class="partner-repeatrule-table"> </div> </div>';
        }
        return __p;
    };
});
define("crm-setting/partnerobj/template/partner_switch-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape, __j = Array.prototype.join;
        function print() {
            __p += __j.call(arguments, "");
        }
        with (obj) {
            __p += '<div class="crm-intro"> <h3>' + ((__t = $t("说明：")) == null ? "" : __t) + "</h3> <ul> <li>1." + ((__t = $t("crm.partner.one")) == null ? "" : __t) + "</li> <li>2." + ((__t = $t("crm.partner.two")) == null ? "" : __t) + "</li> <li>3." + ((__t = $t("crm.partner.three")) == null ? "" : __t) + "</li> <li>4." + ((__t = $t("crm.partner.four")) == null ? "" : __t) + "</li> <li>5." + ((__t = $t("crm.partner.five")) == null ? "" : __t) + "</li> <li>6." + ((__t = $t("crm.partner.six")) == null ? "" : __t) + "</li> <li>7." + ((__t = $t("crm.partner.seven")) == null ? "" : __t) + "</li> <li>8." + ((__t = $t("crm.partner.eight")) == null ? "" : __t) + "</li> </ul> </div> ";
            if (start === "error") {
                __p += ' <p><span class="pp-set-error">' + ((__t = $t("获取相关配置失败")) == null ? "" : __t) + '</span><a href="javascript:;" class="j-reload-config">' + ((__t = $t("重试")) == null ? "" : __t) + "</a> </p> ";
            } else {
                __p += ' <div class="on-off"> <label class="title">' + ((__t = $t("合作伙伴")) == null ? "" : __t) + "</label> </div> ";
                if (start === "opening") {
                    __p += " <span class='on-status opening'>" + ((__t = $t("已开启")) == null ? "" : __t) + " " + ((__t = $t("初始化中")) == null ? "" : __t) + " </span> ";
                } else if (start === "open") {
                    __p += " <span class='on-status open'>" + ((__t = $t("已开启 已完成初始化")) == null ? "" : __t) + " </span> ";
                } else if (start === "close") {
                    __p += " <span class='on-status warn'>" + ((__t = $t("crm.partnerobj.warn")) == null ? "" : __t) + "</span> ";
                }
                __p += " ";
            }
            __p += ' <div class="partner-allowmodify mn-checkbox-box"></div> <div class="partner-allowmodify mn-company-set"></div>';
        }
        return __p;
    };
});
define("crm-setting/partnerobj/template/tpl-html", [], function(require, exports, module) {
    return function(obj) {
        obj || (obj = {});
        var __t, __p = "", __e = _.escape;
        with (obj) {
            __p += '<div class="crm-tit"> <h2> <span class="tit-txt">' + ((__t = $t("合作伙伴管理")) == null ? "" : __t) + '</span> </h2> </div> <div class="crm-module-con crm-scroll"> <div class="crm-tab"> <span data-render="partnerSwitch" class="cur j-switch-tab ">' + ((__t = $t("启用合作伙伴")) == null ? "" : __t) + '</span> <span data-render="partnerRepeatRule" class="j-switch-tab tab-show-none">' + ((__t = $t("伙伴重复数据处理规则")) == null ? "" : __t) + '</span> <span data-render="partnerLevels" class="j-switch-tab tab-show-none">' + ((__t = $t("伙伴层级配置")) == null ? "" : __t) + '</span> <span data-render="partnerAddress" class="j-switch-tab tab-show-none">' + ((__t = $t("crm.partneraddres.manage")) == null ? "" : __t) + '</span> </div> <div class="crm-p20 partner-tab-wrapper"> <div class="tab-con"> <div class="item render-content"> </div> </div> </div> </div> <!--<div class="crm-module-con crm-scroll">--> <!--<div class="crm-p20" style="line-height: 35px;">--> <!--</div>--> <!--</div>-->';
        }
        return __p;
    };
});