.temp-right-rule-tip-dialog {
	.crm-c-dialog .alert-message {
		text-align: left !important;
	}
	.tip-title {
		font-size: 14px;
	}
	.item-title {
		margin-top: 14px;
		font-size: 12px;
		color: #212121;
	}
	.item-con {
		margin-top: 6px;
		font-size: 12px;
		color: var(--color-neutrals15);
	}
	.alert-message {
		padding-left: 32px;
	}
}
.temp-right-open-dialog {
	.dialog-scroll {
		padding: 20px 0 39px 24px;
	}
	.dialog-temp-right-input {
		width: 388px;
		border-radius: 2px;
		border: 1px solid #c3ced9;
		margin: 6px 8px 10px 0;
	}
	.check-item {
		display: inline-block;
	}
	.check-item + .check-item {
		margin-left: 20px;
	}
	.con {
		margin-left: 8px;
	}
	.dialog-intro {
		padding: 10px 20px;
		margin-bottom: 20px;
		background-color: #f4f6f9;
		-webkit-border-radius: 4px;
		-moz-border-radius: 4px;
		border-radius: 4px;
		line-height: 20px;
		color: #666;
		position: relative;
		li {
			line-height: 24px;
		}
	}
}
.temp-right-remove-dialog {
	.mn-checkbox-box {
		display: inline-block;
	}
	.dialog-scroll {
		padding: 22px 0 39px 32px;
	}
	.tip-title {
		margin-bottom: 25px;
	}
	.item-con {
		display: inline-block;
		height: 15px;
		line-height: 15px;
		margin-left: 8px;
		margin-right: 24px;
	}
	.check-all-btn {
		position: absolute;
		bottom: 24px;
		left: 32px;
	}
}
.s-add-tempright {
	.fm {
		margin-top: 24px;
	}
	.fm-lb {
		font-size: 14px;
		margin-bottom: 8px;
		&:before {
			content: "*";
			width: 10px;
			height: 10px;
			color: #ff8989;
			display: inline-block;
		}
	}
	.fm-con {
		.con-item-lb {
			font-size: 12px;
		}
	}
	p {
		height: 32px;
		line-height: 32px;
	}
	.fm-ipt {
		margin: 0 7px;
	}
	.inp-day {
		margin-left: 20px;
	}
}
.crm-s-datapermissions {
	.crm-tab {
		overflow-x: auto;
		overflow-y: hidden;
		white-space: nowrap;
		height: 52px;
		.item {
			padding: 0 20px 6px;
			margin-right: 0;
			float: none;

			&:hover {
				text-decoration: none;
			}
		}
	}

	// 数据权限管理下 radio样式统一
	.radio-item {
		margin-right: 28px;
	}
	.mn-radio-item {
		margin-right: 8px;
	}

	.datapermissions-basic,
	.datapermissions-advance {
		position: absolute;
		left: 0;
		right: 0;
		top: 49px;
		bottom: 0;
		.crm-intro {
			margin: 20px;
		}
		.checkbox-scroll {
			position: absolute;
			left: 0;
			right: 0;
			top: 1px;
			bottom: 60px;
			padding-bottom: 10px;
			overflow: auto;
		}
		.checkbox-btn {
			position: absolute;
			left: 0;
			right: 0;
			bottom: 0;
			padding-left: 40px;
			height: 60px;
			line-height: 60px;
			box-shadow: inset 0 1px 0 0 #eee;
			.reset-btn {
				display: inline-block;
				height: 32px;
				line-height: 32px;
				padding: 0 15px;
				border: 0 !important;
				background-color: var(--color-neutrals01) !important;
				color: #3487e2;
				text-align: center;
				text-decoration: none;
				cursor: pointer;
				font-family: inherit;
				vertical-align: middle;
			}
		}
	}

	// 基础数据权限
	.datapermissions-basic {
		th,
		td {
			padding: 0 20px;
		}
		.title {
			width: 200px;
		}
	}

	// 相关团队数据权限
	.datapermissions-advance {
		.top {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 10px 20px;

			.el-input{
				width: 300px
			}

			.el-button{
				margin-left:10px;
			}
			
			.add .fx-icon-info{
				margin-right: 2px;
				
				&:before{
					color:#333;
				}
			}
		}

		.el-table {
			font-size: 12px;
			th {
				color: var(--color-neutrals15);
				background-color: #f0f2f5;
				line-height: 25px;
				padding: 8px 0;
			}
			td{
				padding: 9px 0;
			}
			.el-table__body tr.hover-row>td{
				background-color: #f4f8fe!important;
			}
			
		}

		.el-button--text{
			color: #0c6cff;
    	color: var(--color-info06);
		}
	}

	// 其他设置
	.other-setting {
		width: 100%;
		height: 100%;

		.crm-gray-9 {
			padding-left: 24px;
			font-size: 12px;
			line-height: 18px;
		}

		.mn-checkbox-box {
			padding: 10px 0;
			span {
				margin-right: 10px;
				display: inline-block;
				vertical-align: middle;
			}
		}

		.leader-range {
			padding-top: 30px;
			span {
				display: block;
				margin-bottom: 10px;
			}
			.select-box {
				width: 180px;
			}
		}
	}

	.temp-right {
		// 临时权限
		position: absolute;
		bottom: 0;
		top: 50px;
		left: 0;
		right: 0;
		.ops-item {
			margin-right: 2px;
		}
		.crm-intro {
			margin: 20px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}
		.temp-right-wrapper {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
		}
		.left-nav {
			width: 180px;
			height: 100%;
			float: left;
			#css3 .shadow(1px -1px 0.1px 0.1px #e4e9f2);
			.nav-item {
				height: 36px;
				line-height: 36px;
				padding-left: 20px;
				&.checked {
					background-color: #f1f4fc;
				}
			}
		}

		.temp-right-table-wrapper {
			#css3 .shadow(1px -1px 0.1px 0.1px #e4e9f2);
			position: absolute;
			top: 0;
			left: 181px;
			right: 0;
			bottom: 0;

			.crm-w-table .batch-item {
				top: 8px;
			}

			.crm-w-table .dt-term-batch .item{
				margin-bottom: 4px;
			}
			.crm-w-table .dt-term-batch .item-tit.datetime{
				display: flex			;
				align-items: center;
				border: 1px solid var(--color-neutrals05, #dee1e8);
				border-radius: 6px;
				padding-left: 8px;
				padding-right: 0px;
				height: 28px;
				line-height: 28px;
				box-sizing: border-box;
				overflow: hidden;

				.el-select{
					width:64px;
				}
				.el-select .el-input .el-input__inner{
					border:none;
					height: 26px;
					line-height: 26px;
				}
			}
			.crm-w-table .dt-term-batch .item-con.datetime{
				margin-left: 8px;
    		width: 208px;

				.el-date-editor--datetimerange.el-input, .el-date-editor--datetimerange.el-input__inner{
					width: auto;
				}
			}
		}

		.emploee-wrapper {
			.btn-trigger {
				height: 20px !important;
				line-height: 20px !important;
			}
			.plus {
				width: 14px !important;
				height: 14px !important;
				margin-top: 6px !important;
			}
			.selected-list {
				.item-wrap {
					height: 14px !important;
					line-height: 14px !important;
					padding: 1px 1px 1px 2px !important;
				}
			}
		}

		.rule-tip {
			// position: absolute;
			// right:  0;
			// top: 0;
			z-index: 502;
			font-size: 12px;
			color: #407fff;
			height: 47px;
			line-height: 47px;
			margin-right: 22px;
			float: right;
		}
		.search-batch-wrapper {
			height: 32px;
			color: #999;
			padding: 9px 20px 10px;
			.search-item {
				display: inline-block;
			}
			label {
				float: left;
				height: 28px;
				line-height: 28px;
				margin-right: 8px;
			}
			.obj-box,
			.employee-box,
			.from-box {
				float: left;
				display: inline-block;

				.crm-w-select {
					width: 156px;
				}
				margin-right: 20px;
			}
		}

		.switch-right {
			border-bottom: 1px solid #d6e2ed;
			height: 40px;
			&:after {
				content: "";
				display: inline-block;
				clear: both;
			}
		}
		.switch-box {
			margin-right: 32px;
			height: 28px;
			padding: 9px 20px 10px;
			color: #999;
			float: left;
			.item-con {
				display: inline-block;
			}
			.check-btn {
				display: inline-block;
				width: 44px;
				height: 24px;
				#css3 > .radius(50px);
				background: #72ce56;
				position: relative;
				vertical-align: middle;

				.core {
					display: inline-block;
					position: absolute;
					right: 2px;
					top: 2px;
					width: 20px;
					height: 20px;
					background-color: var(--color-neutrals01);
					#css3 > .radius(50px);
				}
			}
			.off {
				background: #ccc;
			}
			.off-core {
				left: 2px;
			}
			.check-tip {
				margin-left: 16px;
				vertical-align: middle;
				//line-height:;
			}
		}
	}

	.dp-advance-guide {
		// padding-top: 320px;
		// min-width: 600px;
		// background: url("@{imgUrl}/permission-guide.gif") no-repeat center top;
		// text-align: center;
		// font-size: 16px;
		// margin-top: 120px;

		font-size: 16px;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
	}
}
