define(function (require, exports, module) {
	var util = require('crm-modules/common/util'),
		Tb = require('crm-widget/table/table'),
		Selector = require('crm-widget/selector/selector');

	var dateCompares = [{"value":"EQ","label":$t("等于"),"index":1},{"value":"N","label":$t("不等于"),"index":2},{"value":"LT","label":$t("早于"),"index":5},{"value":"GT","label":$t("晚于"),"index":3},{"value":"GTE","label":$t("晚于等于"),"index":4},{"value":"LTE","label":$t("早于等于"),"index":6},{"value":"IS","label":$t("为空（未填写）"),"index":9},{"value":"ISN","label":$t("不为空"),"index":10},{"value":"BETWEEN","label":$t("时间段"),"index":17}]

	// ,{"value":"LTED","label":"过去N天内(不含当天)","title":"原名：前N天，比如当天是2023-05-08，过去3天内（不含当天）是指2023-05-07、2023-05-06、2023-05-05。","index":18},{"value":"GTED","label":"未来N天内(不含当天)","title":"原名：后N天，比如当天是2023-05-08，未来3天内（不含当天）是指2023-05-09、2023-05-10、2023-05-11。","index":19},{"value":"LTEM","label":"过去N月内(不含当月)","title":"原名：前N月，比如当前是2023年5月，过去3月内（不含当月）是指2023年4月、2023年3月、2023年2月。","index":20},{"value":"GTEM","label":"未来N月内(不含当月)","title":"原名：后N月，比如当前是2023年5月，未来3月内（不含当月）是指2023年6月、2023年7月、2023年8月。","index":21},{"value":"LTEW","label":"过去N周内(不含当周)","title":"比如当前是2023年第7周，过去3周内（不含当周）是指2023年第6周、2023年第5周、2020年第4周。","index":25},{"value":"GTEW","label":"未来N周内(不含当周)","title":"比如当前是2023年第7周，未来3周内（不含当周）是指2023年第8周、2023年第9周、2023年第10周。","index":26},{"value":"LTEDO","label":"过去N天内(含当天)","title":"比如当天是2023-05-08，过去3天内（含当天）是指2023-05-08、2023-05-07、2023-05-06、2023-05-05。","index":27},{"value":"GTEDO","label":"未来N天内(含当天)","title":"比如当天是2023-05-08，未来3天内（含当天）是指2023-05-08、2023-05-09、2023-05-10、2023-05-11。","index":28},{"value":"LTEWO","label":"过去N周内(含当周)","title":"比如当前是2023年第7周，过去3周内（含当周）是指2023年第7周、2023年第6周、2023年第5周、2020年第4周。","index":29},{"value":"GTEWO","label":"未来N周内(含当周)","title":"比如当前是2023年第7周，未来3周内（含当周）是指2023年第7周、2023年第8周、2023年第9周、2023年第10周。","index":30},{"value":"LTEMO","label":"过去N月内(含当月)","title":"比如当前是2023年5月，过去3月内（含当月）是指2023年5月、2023年4月、2023年3月、2023年2月。","index":31},{"value":"GTEMO","label":"未来N月内(含当月)","title":"比如当前是2023年5月，未来3月内（含当月）是指2023年5月、2023年6月、2023年7月、2023年8月。","index":32},{"value":"LTDAY","label":"N天前","title":"当天算1天，比如当前日期是2023-05-08，3天前，是指2023-05-05的23:59:59之前。","index":33},{"value":"GTDAY","label":"N天后","title":"当天算1天，比如当前日期是2023-05-08，3天后，是指2023-05-11的00:00:00之后。","index":34},{"value":"LTWEEK","label":"N周前","title":"当周算1周，比如当前是2023年第7周，3周前，是指2023年第4周周日23:59:59之前。","index":35},{"value":"GTWEEK","label":"N周后","title":"当周算1周，比如当前是2023年第7周，3周后，是指2023年第10周周一00:00:00之后。","index":36}

	var Model = Backbone.Model.extend({
		getAllFieldsByApiName: function () {
			return util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/describe/service/findBizDescribeList',
				data: {
					sourceInfo:"object_management",
					isDraft: true,
					isIncludeFieldDescribe: true,
					isIncludeSystemObj: true,
					isIncludeUnActived: true,
					packageName: "CRM"
				}
			})
		},
    batchDeleteTemporaryRights: function (data, cb) {
    	var me = this;
    	util.FHHApi({
    		url: '/EM1HNCRM/API/v1/object/data_privilege/service/batchDeleteTemporaryRights',
    		data: {
    			temporaryRightsIds: data
    		},
    		success: function (data) {
    			if (data.Result.StatusCode === 0 && data.Value.success) {
    				cb && cb(true);
    			} else {
    				util.alert(data.Result.FailureMessage);
    			}
    		}
    	})
    },
	});

	var Righted = Backbone.View.extend({

		initialize: function () {
			var me = this;
			me.model = new Model();
			me.widgets = {};
		},
		events: {
			'click .j-remove-right-batch': '_removeRightBatch'
		},

		show: function () {
			this._initTable();
		},

		_initTable: function () {
			var me = this;
			me.widgets.tb && me.widgets.tb.destory && me.widgets.tb.destory();
			me.widgets.tb = new Tb({
				$el: this.$el,
				url: '/EM1HNCRM/API/v1/object/data_privilege/service/queryTemporaryRightsList',
				requestType: 'FHHApi',
				alwaysShowTermBatch: true,
				showMultiple: true,
				postData: {
					describe_api_name: '',
					scene: '',
					user_id: ''
				},
				page: {
					pageSize: 20,
					pageNumber: 1
				},
				batchBtns: [{
					text: $t("解除权限"),
					className: 'j-remove-right-batch',
				}],
				columns: [{
					data: 'objectDescribeLabel',
					title: $t("数据所属对象"),
					referRule: 'Employee',
					isId: true,
					dataType: 1,
					orderValues: [1, 0],
					isFilter: true
				}, {
					data: 'objectDataLabel',
					title: $t("主属性"),
					orderValues: [1, 0],
					isFilter: true
				}, {
					data: 'ownerName',
					title: $t("临时授权人员"),
					width: 120,
				}, {
					data: 'sceneName',
					title: $t("来源"),
					width: 120
				}, {
					data: 'scene',
					title: $t('授权方式'),
					render: function (data) {
						return data == 'QIXIN' ? $t('转发时手动授权') : $t('任务或审批开始时、被抄送时');
					}
				}, {
					data: 'startTime',
					title: $t('授权开始时间'),
					dataType: 4,
					width: 120
				}, {
					data: 'expiryTime',
					title: $t('授权结束时间'),
					dataType: 4,
					width: 120
				}, {
					data: 'withdrawalWay',
					title: $t('回收方式'),
					width: 120,
					render: function (data) {
						return data === 'timeLimit' ? $t('到期收回') : $t('任务处理后')
					}
				}, {
					data: 'status',
					title: $t('状态'),
					width: 120,
					render: function (data) {
						return data === 'enable' ? $t('已授权') : $t('权限已被禁用')
					}
				}, {
					lastFixed: true,
					width: 180,
					title: $t("操作"),
					render: function (data, type, full) {
						return '<div class="table-ops"><a class="j-remove-right">' + $t("解除权限") + '</a></div>'
					}
				}],
				formatData: function (data) {
					return {
						data: data.rightsInfoList,
						totalCount: data.totalNumber || 0
					}
				},
				initComplete: function () {
					me._renderFilter();
				}
			}, {
				errorAlertModel: 1
			});
			me.widgets.tb.on('trclick', function (data, tr, target) {
				var owners = [];
				_.each(data.owners, function (value, key) {
					owners.push({
						name: value,
						value: key
					})
				});
				if (target.hasClass('j-remove-right')) {
					console.log(333,data);
					// var confirm = util.confirm($t('确定要解除权限吗？'), $t('提示'), function () {
					// 	me._createOperate(function (operate) {
					// 		// operate.showRemoveRightDialog(owners, data);
					// 		confirm.hide();
					// 		operate.fetchRemoveRight(data);
					// 	});
					// });

					FxUI.MessageBox.confirm($t('确定要解除权限吗？')).then(() => {
						me._createOperate(function (operate) {
							operate.batchDeleteTemporaryRights([data.temporaryRightsId]);
						});
					}).catch(() => {})
				}
			})
		},

		_renderFilter: function () {
			var me = this;
			me._initSelect(function (data) {
				me.widgets.select && me.widgets.select.destroy && me.widgets.select.destroy();
				me.widgets.select = me.widgets.tb.addSelect({
					// $target: this.$('.dt-op-box'),
					// pos: 'before',
					label: $t("所属对象:"),
					options: data,
					defaultValue: 0,
					width: '126'
				})
				me.widgets.select.on('change', function (value) {
					me.widgets.tb && me.widgets.tb.setParam({
						describe_api_name: (value != 0) ? value : ''
					}, true);
				})
				me._initFromSelect();
				me._initSelectBar();
				me._initDate('start')
				me._initDate('end')
			})
		},

		_createOperate: function (cb) {
			var me = this;
			require.async('./common', function (Operate) {
				me.widgets.operate && me.widgets.operate.destroy && me.widgets.operate.destroy();
				me.widgets.operate = new Operate();
				me.widgets.operate.on('refresh', function () {
					me.widgets.tb.setParam({}, true);
				});
				cb && cb(me.widgets.operate);
			})
		},
    //crm\crm2\modules\page\test\test.js
		_removeRightBatch: function () {
			var me = this;
      var ids = this.widgets.tb.getCheckedData().map(function(item){
        return item.temporaryRightsId
      })
      if (ids.length){
        FxUI.MessageBox.confirm($t('确定要解除{{n}}条临时权限吗？', {
        		data: {
        			n: ids.length
        		}
        	})).then(() => {
          me._createOperate(function (operate) {
          	operate.batchDeleteTemporaryRights(ids);
            
            // me.model.batchDeleteTemporaryRights(ids, function () {
            // 	me.widgets.tb.setParam({}, true);
            // });
          });
        }).catch(() => {})
      }
		},

		_initFromSelect: function () {
			var me = this;
			var opts = [{
				name: $t("全部"),
				value: '0'
			}, {
				name: $t("审批流"),
				value: 'approval'
			}, {
				name: $t("业务流"),
				value: 'bpm'
			}, {
				name: $t('阶段推进器'),
				value: 'stage'
			}];

			if (FS.util.getUserAttribute('temprightfreeapprovalflow')) {
				opts.push({
					name: $t('自由审批流'),
					value: 'free_approvalflow'
				})
			}

			me.widgets.fromSelect && me.widgets.fromSelect.destroy && me.widgets.fromSelect.destroy();
		
			me.widgets.fromSelect = me.widgets.tb.addSelect({
				// $target: this.$('.dt-op-box'),
				// pos: 'before',
				label: $t("所属流程"),
				width: '110',
				options: opts,
				defaultValue: 0
			});
			me.widgets.fromSelect.on('change', function (value) {
				me.widgets.tb && me.widgets.tb.setParam({
					scene: (value != 0) ? value : ''
				}, true);
			});

		},

		_initSelect: function (cb) {
			var me = this;
			$.when(me.model.getAllFieldsByApiName()).then(function (res) {
				if (res.Result.StatusCode === 0) {
					var data = [{
						name: $t("全部"),
						value: '0'
					}];
					// _.each(res.Value.objectDescribeList, function (item) {
					// 	data.push({
					// 		name: item.display_name,
					// 		value: item.api_name
					// 	})
					// });

					var list = res.Value.objectDescribeList;
					if(res.Value.manageGroup && !res.Value.manageGroup.all && Array.isArray(res.Value.manageGroup.apiNames)){
						var apiNames=res.Value.manageGroup.apiNames;
						list = list.filter(function(item){
							return apiNames.indexOf(item.api_name)>-1;
						})
					}
					_.each(list, function (item) {
						data.push({
							name: item.display_name,
							value: item.api_name
						})
					});

					cb && cb(data);
				} else {
					util.alert(res.Result.FailureMessage);
				}

			});
		},

		_initSelectBar0: function () {
			var me = this,
				dom = ['<div class="item">',
					'<span class="item-tit">' + $t("授权人员") + '</span>',
					'<div class="item-con emploee-wrapper" style="width:136pxpx;"></div>',
					'</div>',
					'</div>'
				].join('');
			me.$('.batch-term').append($(dom));
			me.widgets.selectBar && me.widgets.selectBar.destroy && me.widgets.selectBar.destroy();
			me.widgets.selectBar = new Selector({
				$wrap: me.$('.emploee-wrapper'),
				width: 130,
				single: true,
				member: true,
				v2Size: 'mini',
				selectedAfterHideLabel: true,
				isFromManage:true,
				enableScope:true
			});
			me.widgets.selectBar.on('change', function (data) {
				me.widgets.tb && me.widgets.tb.setParam({
					user_id: data.member[0] ? "" + data.member[0] : ''
				}, true);
			});
		},
		_initSelectBar: function () {
			var me = this,
				dom = ['<div class="item">',
					'<span class="item-tit">' + $t("授权人员") + '</span>',
					'<div class="item-con emploee-wrapper" style="width:136pxpx;"></div>',
					'</div>',
					'</div>'
				].join('');
			me.$('.batch-term').append($(dom));
			// me.widgets.selectBar && me.widgets.selectBar.destroy && me.widgets.selectBar.destroy();
			// me.widgets.selectBar = new Selector({
			// 	$wrap: me.$('.emploee-wrapper'),
			// 	width: 130,
			// 	single: true,
			// 	member: true,
			// 	v2Size: 'mini',
			// 	selectedAfterHideLabel: true,
			// 	isFromManage:true,
			// 	enableScope:true
			// });

			me.widgets.selectBar && me.widgets.selectBar.destroy && me.widgets.selectBar.destroy();
			seajs.use('icmanage-modules/fx-ui/icselector', (mod) => {
				var tabs = [
					{
						id: "outerErUids",
						title: $t("互联用户"),
						noFilterVal: true,
						// showSelectAll: true, // 非必填，是否出现【全选】
					// }, {
					// 	id: "stopOuterUids",
					// 	title: $t("已停用"),
					// 	noFilterVal: true,
					}];
				let opts = {
					single: true,
					member: true,
					size:'mini',
					selectedAfterHideLabel: true,
					isFromManage:true,
					enableScope:true,
					tabs:  mod.getOuterTabs(tabs),
				}
				let newOpts = FS.selectorParseContactV2.parseContacts(opts);
				me.widgets.selectBar = new Vue({
					el: me.$('.emploee-wrapper')[0],
					template: '<div class="item-con" style="width:130px;"><fx-selector-input-v2 ref="icsel" v-bind="opts" @change="onChange"></fx-selector-input-v2></div>',
					data: {
						opts: newOpts,
					},
					methods: {
						onChange(val) {
							let data = this.$refs.icsel.getValue();
							let id = data.member[0] || data.outerErUids[0] ||'';
							me.widgets.tb && me.widgets.tb.setParam({
								user_id: id + ''
							}, true);
						}
					}
				});
			});

		},

		//\fe\crm\crm2\assets\widget\table\termbatch\outfilter\filteritem.js
		_initDate: function (fieldType) {
			var me = this;
			let title = '';
			let widgetName = 'date' + fieldType;
			if (fieldType == 'start') {
					title = $t('授权开始时间');
			} else {
					title = $t('授权结束时间');
			}
			var me = this,
					dom = [
							'<div class="item">',
							'<div class="item-tit datetime"><span>' + title + ': </span><div class="'+fieldType+'-date-select"></div></div>',
							'<div class="item-con datetime"><div class="' + fieldType + '-date"></div></div>',
							'</div>',
					].join('');
			me.$('.batch-term').append($(dom));
			me.widgets[widgetName] && me.widgets[widgetName].destroy && me.widgets[widgetName].destroy();
			me.widgets[widgetName] = FxUI.create({
					wrapper: me.$('.batch-term .'+fieldType+'-date-select')[0],
					template: '<fx-select ref="select" v-model="value" :options="options" size="mini" @change="onChange"></fx-select>',
					data: function () {
							return {
									options: dateCompares,
									value: 'EQ',
							};
					},
					methods: {
							onChange: function (v) {
								let t='';
								if (v === 'BETWEEN') {
									t = 'datetimerange'
								} else if('LTED|GTED|LTEM|GTEM|LTEW|GTEW|LTEDO|GTEDO|LTEMO|GTEMO|LTEWO|GTEWO|LTDAY|GTDAY|LTWEEK|GTWEEK'.split('|').indexOf(v) > -1){
									t = 'input'
								} else {
									t = 'datetime'
								}
								if(me[widgetName+'comtype'] != t){
									me[widgetName+'comtype'] = t;
									me.createDateTime(fieldType)
								}else{
									if (fieldType == 'start') {
											me.widgets.tb &&
													me.widgets.tb.setParam(
															{
																createTimeRange : [{
																	"operator": v,
																	"queryTime": me.widgets[widgetName+'com'].value
																}],
															},
															true
													);
									} else {
											me.widgets.tb &&
													me.widgets.tb.setParam(
															{
																expiryTimeRange : [{
																	"operator": v,
																	"queryTime": me.widgets[widgetName+'com'].value
																}],
															},
															true
													);
									}
								}
							},
					},
			});
			me[widgetName+'comtype'] = 'datetime'
			me.createDateTime(fieldType)
		},
		createDateTime(fieldType) {
			let me = this;
			let widgetName = 'date' + fieldType;
			let comType = me[widgetName+'comtype'];
			let str='';
			let value='';
			if (comType == 'datetimerange') {
					str = ' <fx-date-picker v-model="value" size="mini" type="datetimerange" format="yyyy-MM-dd HH:mm" value-format="timestamp" start-placeholder="'+$t('开始时间')+'" end-placeholder="'+$t('结束时间')+'" @change="onChange"> </fx-date-picker>';
					value=[];
			}else if(comType == 'datetime'){
				str =
					'<fx-date-picker v-model="value" size="mini" type="datetime" placeholder="' + $t('选择日期时间') +'" value-format="timestamp" format="yyyy-MM-dd HH:mm" @change="onChange"></fx-date-picker>';
			}else if(comType == 'input'){
				str='<fx-input v-model="value" size="mini" type="number" clearable maxlength="3" placeholder="'+$t("请输入")+'" @change="onChange"> </fx-input>'
			}
			me.widgets[widgetName+'com'] && me.widgets[widgetName+'com'].destroy && me.widgets[widgetName+'com'].destroy();
			me.widgets[widgetName+'com'] = FxUI.create({
					wrapper: me.$('.batch-term .' + fieldType + '-date')[0],
					template: str,
					// replaceWrapper: true,
					data: function () {
							return {
									value: '',
							};
					},
					methods: {
							onChange: function (v) {
								let params='';
								if(Array.isArray(v)){
									params = [{
										"operator": me.widgets[widgetName].value,
										"startTime":v[0],
										"endTime":v[1],
										"queryTime":'',
									}]
								}else{
									params = [{
										"operator": me.widgets[widgetName].value,
										"queryTime": v||'',
										"startTime":'',
										"endTime":'',
									}]
								}
								if (fieldType == 'start') {
										me.widgets.tb &&
												me.widgets.tb.setParam(
														{
															createTimeRange : params,
														},
														true
												);
								} else {
										me.widgets.tb &&
												me.widgets.tb.setParam(
														{
															expiryTimeRange : params,
														},
														true
												);
								}
							},
					},
			});
		},

		hide: function () {
			this.$el.hide();
		},

		destroy: function () {
			_.each(this.widgets, function (widget) {
				widget.destroy && widget.destroy();
			})
			this.widgets = null;
		}
	});

	module.exports = Righted;
})
