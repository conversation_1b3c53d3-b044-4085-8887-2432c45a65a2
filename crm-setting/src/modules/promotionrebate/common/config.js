/*
 * @Descripttion:
 * @Author: chaoxin
 * @Date: 2022-12-14 10:18:01
 * @LastEditors: chaoxin
 * @LastEditTime: 2024-10-23 15:22:11
 */

define(function (require, exports, module) {
    module.exports = {
        CONFIG_DATA: [
            {
                domId: 'level0',
                moduleId: 'advancedPricing',
                title: $t('高级定价', null, '价格政策'),
                licenseKey: 'advanced_pricing_app',
                moduleList: [
                    {
                        type: 'switch',
                        title: $t("开启高级定价", null, '开启价格政策'),
                        key: 'price_policy',
                        dependKeys: ['promotion_status', '28'],
                        value: false,
                        enableClose: false,
                        setConfigParam: 2,
                        describeList: [
                            { title: $t("高级定价开关", null, '价格政策开关') }
                        ],
                        children:[{
                            _id:"price_policy_objects",
                            key:"price_policy_objects",
                            title:"",// $t("开启指定对象的价格政策："),
                            type: 'PricePolicyObject',
                            dependKeys: ['price_policy'],
                            isShow({ price_policy }) {
                                return price_policy;
                            },
                        }]
                    },  {
                        // 跨对象促销
                        title: $t("sfa.crm.price_policy_config.multiple_object"),
                        type: 'switch',
                        key: 'multiple_object_price_policy',
                        isShow({ price_policy }) {
                            return price_policy && CRM.util.isGrayScale("CRM_POLICY_MULTIPLE_OBJECT");
                        },
                        value: false,
                        enableClose: false,
                        setConfigParam: 2,
                        describeList: [
                            { title: $t("sfa.crm.price_policy_config.multiple_object_desc") }
                        ],
                    },  {
                        title: $t("允许切换政策："),
                        type: 'checkbox',
                        key: ['allow_switch_master_price_policy', 'allow_switch_detail_price_policy'],
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        value: [false, false],
                        options: [
                            {
                                key: 'allow_switch_master_price_policy',
                                label: $t("整单促:主对象上匹配的价格政策允许手工切换或取消"),
                            },
                            {
                                key: 'allow_switch_detail_price_policy',
                                label: $t("产品促:从对象上匹配的价格政策允许手工切换或取消"),
                            }
                        ],
                        describeList: [
                            { title: $t("注意:如果有限额限量控制，禁止切换可能导致无法下单") }
                        ]
                    },
                    {
                        //支持送一定金额的赠品、按比例送赠品
                        title: $t("crm.price_policy_config.percentile_gift"),
                        key: 'price_policy_support_percentile_gift',
                        isShow({ price_policy }) {
                            return price_policy ;
                        },
                        type: 'switch',
                        value: false,
                        enableClose: false,
                        describeList: [
                            { title: $t("crm.price_policy_config.percentile_gift_no") },
                            { title: $t("crm.price_policy_config.percentile_gift_yes")}
                        ]
                    },
                    {
                        title: $t('sfa.crm.promotionrebate.price_policy_unit_title'),
                        key: 'price_policy_unit',
                        type: 'PricePolicyUnit',
                        isShow({ price_policy, multiple_unit }) {
                            return price_policy && multiple_unit;
                        },
                    },
                    {
                        title: $t("赠品费用分摊依据："),
                        key: 'gift_amortize_basis',
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        type: 'radio',
                        value: 'price_book_price',
                        radioOptions: [
                            {
                                label: $t("价目表价格"),
                                value: 'price_book_price'
                            },
                            {
                                label: $t("产品档案价格"),
                                value: 'product_price'
                            }
                        ]
                    },
                    {
                        title: $t("赠品参与分摊："),
                        type: 'radio',
                        key: 'gift_attend_amortize',
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        value: '0',
                        radioOptions: [
                            {
                                value: '0',
                                label: $t("否:赠品的价值全部分摊到本品行，赠品最终【销售单价】为0、【费用分摊后小计】即为赠品自己的价值"),
                            },
                            {
                                value: '1',
                                label: $t("是:赠品的价值分摊到本品行和赠品行，赠品最终【销售单价】为0、【费用分摊后小计】为赠品自己的价值扣除分摊值的部分"),
                            }
                        ]
                    },
                    {
                        //赠品校验可售范围
                        title: $t("crm.price_policy_config.gift_range_title"),
                        type: 'radio',
                        key: 'enable_gift_range_shelves',
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        value: '0',
                        radioOptions: [
                            {
                                value: '0',
                                label: $t("crm.price_policy_config.gift_range_no"),
                            },
                            {
                                value: '1',
                                label: $t("crm.price_policy_config.gift_range_yes"),
                            }
                        ]
                    },
                    {
                        //折上折
                        title: $t("crm.price_policy_config.stacked_discount_title"),
                        type: 'switch',
                        enableClose: false,
                        key: 'discount_on_discount',
                        isShow({ price_policy }) {
                            return price_policy && CRM.util.isGrayScale('CRM_POLICY_STACKED_DISCOUNT');
                        },
                        value: false,
                        setConfigParam: 2,
                        describeList: [
                            { title: $t("crm.price_policy_config.stacked_discount_no") },
                            { title: $t("crm.price_policy_config.stacked_discount_yes")}
                        ]
                    },
                    {
                        title: $t("crm.price_policy_config.match_mode"),
                        key: 'match_mode',
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        type: 'radio',
                        value: 'immediately',
                        radioOptions: [
                            {
                                label: $t("crm.price_policy_config.match_immediately"),
                                value: 'immediately'
                            },
                            {
                                label: $t("crm.price_policy_config.match_once"),
                                value: 'once'
                            }
                        ]
                    },
                    {
                        type: 'checkbox',
                        title: $t("选产品页，各产品显示的内容"),
                        key: ['show_price_policy_name'],
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        value: [false],
                        options: [
                            {
                                label: $t("移动端显示政策名称"),
                                key: 'show_price_policy_name',
                            }
                        ],
                        describeList: [
                            { title: $t("移动端选产品页，各产品将展示“全部政策名称”，建议创建价格政策时，只创建一条价格规则（或者多条规则的产品范围完全相同），将政策名称置为具体的促销信息，（如：满200减20）起到在选产品时可以查看产品的具体促销信息的作用。") }
                        ],
                    },
                    {
                        //开价格政策后支持调整从对象顺序
                        title:$t("crm.price_policy_config.change_order"),
                        key: 'price_policy_change_order',
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        type: 'radio',
                        value: '0',
                        radioOptions: [
                            {
                                label: $t("crm.price_policy_config.change_order_no"),
                                value: '0'
                            },
                            {
                                label: $t("crm.price_policy_config.change_order_yes"),
                                value: '1'
                            }
                        ]
                    },
                    {
                        title: $t("crm.price_policy_config.dep"),
                        key: 'price_policy_dept_not_this',
                        isShow({ price_policy }) {
                            return price_policy;
                        },
                        type: 'radio',
                        value: '0',
                        radioOptions: [
                            {
                                label: $t("crm.price_policy_config.dep_self"),
                                value: '0'
                            },
                            {
                                label: $t("crm.price_policy_config.dep_all"),
                                value: '1'
                            }
                        ]
                    }
                ]
            }, {
                domId: 'level1',
                moduleId: 'rebate',
                title: $t('返利单'),
                licenseKey: 'rebate_app',
                moduleList: [
                    {
                        title: $t("开启返利单"),
                        type: 'switch',
                        key: 'rebate',
                        value: false,
                        enableClose: false,
                        setConfigParam: 2,
                        describeList: [
                            {
                                title: $t("返利单政策开关，开启后不可关闭。")
                            }
                        ]
                    },
                    {
                        title: $t('crm.setting.promotionrebate.rebate_policy_source_title', null, '返利产生来源'),
                        key: 'rebate_policy_source',
                        isShow({ rebate }) {
                            return rebate;
                        },
                        type: 'RebatePolicySource'
                    },
                    {
                        title: $t('sfa.crm.setting.promotionrebate.rebate_product_range_shelves', null, '返利品校验可售范围'),
                        key: 'rebate_product_range_shelves',
                        isShow({ rebate }) {
                            return rebate;
                        },
                        type: 'radio',
                        value: '0',
                        radioOptions: [
                            {
                                label: $t("sfa.crm.rebate_config.product_range_no"),
                                value: '0'
                                // 否：下单时可选择返利单配置的返利品，不区分返利品是否在可售范围内，是否下架
                            },
                            {
                                label: $t("sfa.crm.rebate_config.product_range_yes"),
                                value: '1'
                                // 是：下单时在发你单配置的返利品范围内选择，系统自动过滤掉不在可售范围内的产品、过滤掉下架产品
                            }
                        ]
                    }
                ]
            }, {
                domId: 'level2',
                moduleId: 'coupon',
                title: $t('优惠券'),
                licenseKey: 'coupon_app',
                moduleList: [
                    {
                        title: $t("开启优惠券"),
                        type: 'switch',
                        key: 'coupon',
                        value: false,
                        enableClose: false,
                        setConfigParam: 2,
                        describeList: [
                            {
                                title: $t("优惠券政策开关，开启后不可关闭。")
                            }
                        ]
                    },
                    {
                        title: $t('启用纸质券业务'),
                        key: 'paper_coupon',
                        value: false,
                        enableClose: false,
                        setConfigParam: 2,
                        isShow: CRM.util.isGrayScale('CRM_PAPER_COUPON')
                    }
                ]
            },
        ],
        KEY_CONFIG: {
            // 价格政策
            price_policy: {
                cache_key: 'advancedPricing',
            },
            multiple_object_price_policy:{
                type:"boolean"
            },
            '28': {
                type: 'string',
            },
            promotion_status: {
                type: 'string',
            },
            clone_history_order_product: {
                type: 'string',
            },
            gift_amortize_basis: {
                cache_key: 'giftAmortizeBasis',
                type: 'string',
            },
            gift_attend_amortize: {
                cache_key: 'giftAttendAmortize', // 价格政策里赠品配置
                type: 'string',
            },
            enable_gift_range_shelves:{
                type: 'string',
            },
            show_price_policy_name: {
                cache_key: 'showPricePolicyName', // 移动端显示政策名称配置
            },
            allow_switch_master_price_policy: {
                cache_key: 'allowSwitchMasterPricePolicy', // 配置政策是否允许切换
            },
            allow_switch_detail_price_policy: {
                cache_key: 'allowSwitchDetailPricePolicy',
            },
            price_policy_support_percentile_gift:{
                type:"boolean"
            },
            match_mode :{
                type:"string"
            },
            price_policy_change_order:{
                type:"string"
            },
            price_policy_dept_not_this:{
                type:"string"
            },
            discount_on_discount:{
                type:"boolean"
            },
            gift_fixed_attribute:{ //价格政策赠品配置
                type:'string'
            },
            // 优惠券
            coupon: {
                cache_key: 'openCoupon',
            },
            paper_coupon: {
                cache_key: 'openPaperCoupon'
            },
            // 返利
            rebate: {
                cache_key: 'openRebate',
            },
            rebate_policy_source: {
                type: 'string'
            },
            price_policy_unit: {
                type: 'string'
            },
            rebate_product_range_shelves: {
                type: 'string'
            }
        }
    }
});
