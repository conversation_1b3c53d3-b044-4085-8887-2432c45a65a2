/** 
 * @desc 相关团队数据权限
 */

define(function (require, exports, module){
	var util   = require('crm-modules/common/util');
	module.exports = Backbone.View.extend({
        initialize: function(data){
            var changeCustomer = data.changeCustomer ? 'mn-selected' : '';
            var repetitionIsHint = data.repetitionIsHint ? 'mn-selected' : '';
            this.el.innerHTML = `<div class="crm-account-rule-setting crm-account-name-setting">
            <div class="title"><h1>${$t("sfa.customer.name.setting.title")}</h1></div>
            <div class="setting-content">
                <div class="crm-intro">
                    <ol>
                        <li>${$t("开启多组织后此配置失效")}</li>
                    </ol>
                </div>
                <div class="setting-setrule">
                    <span class="set-basic mn-checkbox-item ${changeCustomer}" data-key="11"></span><span class="check-lb">${$t("允许修改客户名称")}</span>
                </div>
                <div class="setting-setrule">
                    <span class="set-basic-two mn-checkbox-item ${repetitionIsHint}" data-key='check_account_name_duplicated'></span><span class="check-lb">${$t("新建编辑客户页面，当客户名称与系统中已有客户名称重复时进行提示。")}</span>
                </div>
            </div>
            </div>
            <div class="crm-account-rule-setting crm-account-address-setting"></div>
            `;
			if(CRM.util.isGrayScale('SFA_ACCOUNT_RULE_OPEN_ADDR_CONFIG')){
				this.renderAccountAddressSetting(data);
			}

		},
        
        events: {
            'click .set-basic': 'setBasicHandle',
            'click .set-basic-two' : 'setRepetitionIsHint'
        },
        
        setBasicHandle: function (e) {
            var $target = $(e.target),
                key = $target.attr('data-key'),
                value = $target.hasClass('mn-selected') ? '1' : '0';
            this.setConfig({
                key: key,
                value: key == '11' ? (value == '0' ? '1' : '0') : value
            }, $target);
        },

        setRepetitionIsHint:function(e){
            let $target = $(e.target),
            key = $target.attr('data-key')
            value = $target.hasClass('mn-selected') ? '1' : '0';
            this.setConfig({
                key: key,
                value: key == 'check_account_name_duplicated' ? (value == '0' ? '1' : '0') : value
            }, $target);
        },

        setConfig: function (config, $target) {
            var me = this;
            util.setConfigValue(config).then(function(){
                util.remind(1, $t("设置成功"));
            }, function(){
                util.remind(3, $t("设置失败"));
                $target.toggleClass('mn-selected');
            });
        },

        renderAccountAddressSetting: function(data) {
            this.widget = FxUI.create({
                wrapper: this.$('.crm-account-address-setting')[0],
                template: `
                    <div class="address-setting-box">
                        <h2 class="title"><h1>{{$t("sfa.customer.rule.address.setting")}}</h1></h2>
                        <div class="setting-content">
                            <div class="backstage-switch">
                                <div class="crm-module-title">{{$t("sfa.customer.rule.address.setting.open.title")}}</div>
                                <div class="on-off">
                                    <span class="fx-icon-jingshi" style="margin-right: 3px;font-size: 15px;"></span>
                                    <label>
                                        {{$t('crm.setting.backstage.warn_cannot_closed', null, '一旦启用，将无法停用')}}
                                    </label>
                                    <fx-switch
                                        :disabled="isDisabled"
                                        v-model="isOpenAddress" 
                                        size="small" 
										:before-change="openAutoAddress"
                                    />
                                </div>
                            </div>
                            <div class="open-address-tip tip-box">
                                    <div>{{$t("sfa.customer.rule.address.setting.tip1")}}</div>
                                    <div>{{$t("sfa.customer.rule.address.setting.tip2")}}</div>
                                    <div>{{$t("sfa.customer.rule.address.setting.tip2.1")}}</div>
                                    <div>{{$t("sfa.customer.rule.address.setting.tip2.2")}}</div>
                            </div>
                            <div class="add-switch-box" v-show="showStatus">
                                <div class="setting-add-sync">
                                    <div class="switch-content-box">
                                        <fx-checkbox @change="onSyncAddress" v-model="isSyncAddress">${$t("sfa.customer.rule.address.setting.sync.address")}</fx-checkbox>
                                    </div>
                                    <div class="add-sync-tip tip-box">
                                        <div>{{$t("sfa.customer.rule.address.setting.sync.address.tip1")}}</div>
                                        <div>{{$t("sfa.customer.rule.address.setting.sync.address.tip1.1")}}</div>
                                        <div>{{$t("sfa.customer.rule.address.setting.sync.address.tip1.2")}}</div>
                                    </div>
                                </div>
                                <div class="setting-position-sync">
                                    <div class="switch-content-box">
                                        <fx-checkbox @change="onSyncLocation" v-model="isSyncLocation"  :disabled="isLocationDisabled">${$t("sfa.customer.rule.address.setting.sync.location")}</fx-checkbox>
                                    </div>
                                    <div class="position-sync-tip tip-box">
                                        <div>{{$t("sfa.customer.rule.address.setting.sync.location.tip")}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>`,
                data() {
                    return {
                        isOpenAddress: data.isOpenAddress,
                        isSyncAddress: data.isSyncAddress,
                        isSyncLocation: data.isSyncLocation,
                        isLocationDisabled: data.isSyncAddress,
                    }
                },
                computed: {
                    isDisabled() {
                        return this.isOpenAddress;
                    },
                    showStatus() {
                        return this.isOpenAddress;
                    },
                },
                methods: {
                    openAutoAddress() {
                        let me = this;
                        this.$confirm($t('sfa.customer.rule.address.settiing.open.confirm'), $t('提示'), {
                            confirmButtonText: $t('确定'),
                            cancelButtonText: $t('取消'),
                            type: 'warning'
                        }).then(async () => {
                            CRM.util.setConfigValue({
                                key: 'is_open_account_addr_config',
                                value: '1',
                            }).then(res => {
								me.isOpenAddress = true;
							})
                        }).catch(() => {
							me.isOpenAddress = false;
						});
                    },
                    onSyncAddress(val) {
                        console.log(val, 'onSyncAddress');
                        let me = this;
                        if(!val) {
                            this.$confirm($t('sfa.customer.rule.address.setting.sync.address.close.tip'), $t('提示'), {
                                confirmButtonText: $t('继续'),
                                cancelButtonText: $t('取消'),
                                type: 'warning'
                            }).then(async () => {
                                CRM.util.setConfigValue({
                                    key: 'add_account_meanwhile_add_addr',
                                    value: '0',
                                }).then(() => {
                                    me.isSyncAddress = val;
                                    me.isLocationDisabled = false;
                                });
                            }).catch(() => {
                                me.isSyncAddress = true;
                                me.isLocationDisabled = true;
                                me.isLocationDisabled = true;
                            });
                        }else{
                            CRM.util.setConfigValues([{
                                key: 'add_account_meanwhile_add_addr',
                                value: '1',
                            },{
                                key: 'account_and_addr_sync_upd_location',
                                value: '1',
                            }]).then(() => {
                                me.isSyncAddress = val;
                                me.isSyncLocation = true;
                                me.isLocationDisabled = true;
                            })
                        }
                    },
                    onSyncLocation(val) {
                        CRM.util.setConfigValue({
                            key: 'account_and_addr_sync_upd_location',
                            value: val ? '1' : '0',
                        }).then(() => {
                            this.isSyncLocation = val;
                        })
                    },
                    destroy() {
                        this.$destroy();
                    }
                }
            })
        },

        show: function() {
            this.$el.show();
        },
        
        hide: function() {
            this.$el.hide();
        },
        
        destroy: function() {
        }
	});
});
