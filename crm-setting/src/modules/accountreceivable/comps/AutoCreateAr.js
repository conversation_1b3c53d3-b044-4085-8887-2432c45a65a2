/*
 * @Descripttion: 快捷自动化立应收
 * @Author: chao<PERSON>
 * @Date: 2024-09-26 16:19:04
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-08-11 10:22:35
 */
define(function (require, exports, module) {
    const {BackstageSettingItem} = require('./BackstageComp');
    module.exports = (wrapper, key, values) => {
        return FxUI.create({
            wrapper,
            template: `
                <backstage-setting-item
                    key="${key}"
                    :title="title"
                    :describeList="describeList"
                >
                    <template slot="header-right">
                        <fx-switch
                            :value="value"
                            size="small"
                            true-label="0"
                            false-label="1"
                            @input="handleChange"
                        />
                    </template>
                    <template slot="main">
                        <content-main v-if="value" />
                    </template>
                </backstage-setting-item>
            `,
            components: {
                BackstageSettingItem,
                ContentMain: () => new Promise((resolve) => {
                    require.async("vcrm/sdk", (sdk) => {
                        sdk.getComponent("backstage").then((backstage) => resolve(backstage.AutoQuickCreateAr))
                    })
                })
            },
            data() {
                return {
                    value: (values[key] ?? '0') === '1',
                    title: $t('sfa.crm_setting.accountreceivable.is_open_ar_quick_rule_title'),
                    describeList: [
                        $t('sfa.crm_setting.accountreceivable.is_open_ar_quick_rule_desc1'),
                    ],
                }
            },
            methods: {
                handleChange(val) {
                    this.$emit('change', key, val ? '1' : '0');
                },
                changeComplete(key, value, flag) {
                    if (flag) {
                        this.value = value === '1';
                    }
                }
            }
        })
    }
})
