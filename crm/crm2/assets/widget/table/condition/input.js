/**
 * @desc   但行文本 input批量输入
 * <AUTHOR>
 */
define(function(require, exports, module) {

    var Dialog     = require('crm-assets/widget/dialog/dialog');
    var MAX = _.contains(['wly002434','fktest087'], CRM.ea) ? 500 : 200;
    var Input = Dialog.extend({

        attrs: {
            width:         480,
            className:     'crm-w-table',
            title:         $t('单行文本'),
            showScroll:    false,
            showBtns:      true,
            zIndex:        8000,
            val:             '',
            stopPropagation: true,
            content:     '<div class="conditon-input-batch">' +
                            '<textarea placeholder="' + $t('可在该区域添加多条数据') + '"></textarea>' + 
                            '<p>' + $t('注意：按换行符分隔，每行一个值，最多支持{{num}}行', {num: MAX}) + '</p>' +
                         '</div>'
        },
        
        events: {
            'click .j-cancel':                'hide',
            'click .j-enter':                 'onEnter'
        },
        
        /**
         * @desc 渲染
         */
        render: function() {
            var me = this,
                result = Input.superclass.render.call(this);
            
            var val = this.get('val').replace(/；/g, ';');
            
            me.$el = this.element;
            
            me.$('.b-g-btn-cancel').addClass('j-cancel');
            
            me.$('.b-g-btn').addClass('j-enter');
            
            me.$('textarea').val(val.split(';').join('\n'));
            
            return result;
        },

        
        onEnter: function(e) {
            var val = $.trim($('textarea', this.$el).val());
            val = val.split('\n');
            val = _.filter(val, function(i) {
                return !!i;
            });
            if (val.length > MAX) {
                CRM.util.alert($t('最多支持{{num}}行', {num: MAX}));
                return false;
            }
            this.trigger('enter', val.join(';'));
            this.hide();
            e.stopPropagation();
            return false;
        },
        
        
        /**
         * @desc 隐藏
         */
        hide: function(e) {
            var result = Input.superclass.hide.call(this);
            this.destroy();
            e && e.stopPropagation();
            return false;
        }
    });

    Input.MAX = MAX;
    
    module.exports = Input;
});
