/**
 * @desc 所有与请求相关的函数
 */
define(function (require, exports, module) {
    var util = require('base-modules/utils');
    var gray = require('./gray');
    var contacts = require('./contacts');
    var parse = require('./parse');
    var getEmployeeById = contacts.getEmployeeById;
    var getUserGroups = contacts.getUserGroups;
    var getUserRoles = contacts.getUserRoles;
    var ddd = {};
    // var helper = require('crm-modules/components/helper/helper');

    var ajax = {
        /**
         * @desc 通用ajax
         * @param url
         * @param param: JSON对象
         * @param cb: 可以做些缓存逻辑
         * @param noLoading: 默认出loading，不需要loading传true
         * @returns {Promise<any>}
         */
        ajax_base: function (url, param, cb, noLoading, newLoading) {
            if (!noLoading) newLoading ? CRM.util.showLoading_tip() : CRM.util.showLoading_new();
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: url,
                    data: param,
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            cb && cb(res.Value);
                            return;
                        }
                        reject();
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                    complete: function () {
                        if (!noLoading) newLoading ? CRM.util.hideLoading_tip() : CRM.util.hideLoading_new();
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc  获取国家省市区
         * @param disableCache 不使用缓存
         * @return Promise
         */
        getCountryAreaOptions: function (disableCache) {
            return new Promise(function (resolve, reject) {
                var key = 'country_area_options', value;
                if (!disableCache) {
                    value = CRM.get(key);
                }

                if (value && value.country) return resolve(value);

                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/global_data/service/country_area_field_options',
                    data: {},
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            value = res.Value;
                            CRM.set(key, value);
                            resolve(value);
                        } else {
                            util.alert(res.Result.FailureMessage);
                            reject(res);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
            })
        },

        /**
         * @desc  获取工商 1,2,3,4级行业数据
         * @param disableCache 不使用缓存
         * @return Promise
         */
        getCategoryByCode: function (disableCache) {
          return new Promise(function (resolve, reject) {
              var key = 'biz_query_search_category', value;
              if (!disableCache) {
                  value = CRM.get(key);
              }

              if (value && value["1"]) return resolve(value);

              CRM.util.FHHApi({
                  url: '/EM1HDataptIndustry/industryFcpService/getCategoryGroup',
                  data: {level: 'all'},
                  success: function (res) {
                      if (res.Result.StatusCode == 0) {
                          value = res.Value;
                          CRM.set(key, value);
                          resolve(value);
                      } else {
                          util.alert(res.Result.FailureMessage);
                          reject(res);
                      }
                  }
              }, {
                  errorAlertModel: 1
              });
          })
      },

        fetchCountryAreaOptions: function (disableCache) {
            var _resolve, _reject, _abort;
            ajax.getCountryAreaOptions(disableCache).then((res) => {
                _resolve && _resolve(res);
            }, (res) => {
                _reject && _reject(res)
            })
            return {
                then(resolve, reject) {
                    _resolve = resolve;
                    _reject = reject;
                },
                abort() {
                    _resolve = _reject = null;
                }
            }
        },

        /**
         * @desc  根据id获取公海
         * @return Promise
         */
        getHighSeasByID: function (id, disableCache, type) {
            return new Promise(function (resolve, reject) {
                var key = 'highsea_' + id;
                var value = CRM.get(key);

                if (value && !disableCache) {
                    resolve(value);
                    return;
                }

                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/pool_service/service/detail',
                    data: {
                        "id": id,
                        "api_name": "HighSeasObj"
                    },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            // 设置人员信息数据缓存 - 用于数据解析
                            res.Value.outer_employee && res.Value.outer_employee.length && CRM.util.setExContactsCache(res.Value.outer_employee, 'em');
                            res.Value.outer_enterprise && res.Value.outer_enterprise.length && CRM.util.setExContactsCache(res.Value.outer_enterprise, 'en');
                            // 设置国家省市区数据缓存 - 用于数据解析
					        res.Value.areaList && res.Value.areaList.length && CRM.util.setAreaCache(res.Value.areaList);
                            value = res.Value;
                            if (type == 'new') {
                                let _data = JSON.parse(JSON.stringify(res.Value.data));
                                value = _.extend({}, res.Value, _data);
                                delete value.data;
                            }
                            CRM.set(key, value);
                            resolve(value);
                        } else {
                            util.alert(res.Result.FailureMessage);
                        }
                    }
                })
            })
        },

        /**
         * @desc  获取所有公海
         * @return Promise
         */
        getHighSeasList: function (disableCache) {
            return new Promise(function (resolve, reject) {
                var key = 'highseas_list';
                var value = CRM.get(key);

                if (value && !disableCache) {
                    resolve(value);
                    return;
                }

                CRM.util.FHHApi({
                    // url: '/EM1HCRM/HighSeas/GetAllHighSeasList',
                    url: '/EM1HNCRM/API/v1/object/HighSeasObj/controller/List',
                    data: {
                        object_describe_api_name: "HighSeasObj",
                        ignore_scene_record_type: false,
                        search_query_info: '{"limit":200,"offset":0,"filters":[],"orders":[{"fieldName":"last_modified_time","isAsc":false}]}'
                    },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            // value = res.Value.HighSeasList;
                            value = res.Value.dataList.map(item => {
                                var data = CRM.util.parsePropFromKebabToCamel(item);
                                data.HighSeasID = item._id;
                                data.HighSeasName = item.name;
                                return Object.assign(item, data);
                            })
                            console.log(value);
                            CRM.set(key, value);
                            resolve(value);
                        } else {
                            util.alert(res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
            })
        },

        /**
         * @desc  获取所有线索
         * @return Promise
         */
        getCluePoolList: function (disableCache, ignoreManageGroupRule) {
            return new Promise(function (resolve, reject) {
                var key = 'cluepool_list';
                var value = CRM.get(key);

                if (value && !disableCache) {
                    resolve(value);
                    return;
                }

                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/LeadsPoolObj/controller/List',
                    data: {
                        object_describe_api_name: "LeadsPoolObj",
                        ignore_scene_record_type: false,
                        search_query_info: '{"limit":500,"offset":0,"filters":[],"orders":[{"fieldName":"last_modified_time","isAsc":false}]}',
                        get_data_only: true,
                        ...(ignoreManageGroupRule && { extra_params: {ignoreManageGroupRule: true} }), //分管
                    },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            let value = res.Value.dataList.map(item => {
                                let data = CRM.util.parsePropFromKebabToCamel(item)
                                data.name = item.name
                                data.SalesCluePoolID = item._id
                                return Object.assign({}, data, item)
                            })
                            CRM.set(key, value);
                            resolve(value);

                            return;
                        }
                        util.alert(res.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc  根据id获取线索池
         * @return Promise
         */
        getCluePoolByID: function (id, isNocache, type) {
            let me = this;
            return new Promise(function (resolve, reject) {
                var key = 'cluepool_' + id + (type ? type : '');
                var value = CRM.get(key);
                if (value && !isNocache) {
                    resolve(value);
                    return;
                }
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/pool_service/service/detail',
                    data: {
                        id,
                        api_name: "LeadsPoolObj"
                    },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            let value;
                            // 设置人员信息数据缓存 - 用于数据解析
                            res.Value.outer_employee && res.Value.outer_employee.length && CRM.util.setExContactsCache(res.Value.outer_employee, 'em');
                            res.Value.outer_enterprise && res.Value.outer_enterprise.length && CRM.util.setExContactsCache(res.Value.outer_enterprise, 'en');
                            // 设置国家省市区数据缓存 - 用于数据解析
					        res.Value.areaList && res.Value.areaList.length && CRM.util.setAreaCache(res.Value.areaList);
                            if (type == 'new') {
                                let _data = JSON.parse(JSON.stringify(res.Value.data));
                                value = _.extend({}, res.Value, _data);
                                delete value.data;
                            } else {
                                value = {
                                    SalesCluePool: me._formatDataNewAPIToOldAPI(res.Value)
                                }
                            }
                            CRM.set(key, value);
                            resolve(value);
                        } else {
                            util.alert(res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
            })
        },

        // _parseFilter(filterStr) {
        //     let filterObj = JSON.parse(filterStr),
        //         filterArr = filterObj.filters
        //     let temp = filterArr.map(item => {
        //         item.FieldName = item.field_name
        //         item.FieldValue = item.field_values
        //         item.Compare = item.operator == 'BETWEEN' ? 'CUSTOM' : item.operator
        //         let comporeObj = helper.compare.find(it => it.value1 == item.Compare)
        //         comporeObj && (item.Compare = comporeObj.value)
        //         return item
        //     })
        //     return temp
        // },

        getAllPoolList: function (action, apiName, cb) {
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/pool/service/getTargePoolList',
                data: {
                    apiName: apiName || "LeadsObj",
                    action: action || 'move',
                },
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        let list = res.Value.poolList.map(item => {
                            return {
                                SalesCulePoolID: item._id,
                                Name: item.name,
                                AssignerID: item.assigner_id,
                                AssignerName: (CRM.util.getEmployeeById(item.assigner_id) || {}).name,
                                id: item._id,
                                value: item.name__r || item.name
                            }
                        })
                        cb && cb(list)
                    } else {
                        cb && cb([])
                    }
                }
            }, {
                errorAlertModel: 1
            });
        },

        _formatDataNewAPIToOldAPI: function (data) {
            let newParam = {}
            newParam.MemberInfos = []
            if (data.data) {
                newParam = CRM.util.parsePropFromKebabToCamel(data.data)
                newParam.OverTimeHours = data.data.overtime_hours
                newParam.OverTimeMinutes = data.data.overtime_minutes
                newParam.SalesClueCount = data.data.leads_count
                newParam.PoolType = typeof data.data.pool_type == 'string' ? {
                    'private': 1,
                    'normal': 0
                }[data.data.pool_type] : data.data.pool_type || 0
                newParam.LimitType = typeof data.data.limit_type == 'string' ? {
                    'enterprise': 1,
                    'personal': 0
                }[data.data.limit_type] : data.data.limit_type || 0
            }
            if (data.pool_owner_rule) {
                newParam.PoolOwnerRule = JSON.parse(data.pool_owner_rule);
            } else {
                newParam.PoolOwnerRule = null;
            }
            if(data.is_collaborator){
                newParam.isCollaborator = data.isCollaborator
            }
            if (data.recycling_rule_list && data.recycling_rule_list.length) {
                newParam.RecyclingRuleList = data.recycling_rule_list.map(item => {
                    let temp = CRM.util.parsePropFromKebabToCamel(item)
                    if (item.target_pool_id) {
                        temp.HighSeasID = item.target_pool_id
                    }
                    if (item.target_pool_name) {
                        temp.HighSeasName = item.target_pool_name
                    }
                    if (item.wheres) {
                        let _wheres = JSON.parse(item.wheres);
                        _.each(_wheres.filters, (f) => {
                            f = parse.hackORule(f);
                        })
                        temp.RecyclingFilterList = _wheres;
                    }
                    return temp
                })
            }
            if (data.allocate_rule_list && data.allocate_rule_list) {
                newParam.SalesCluePoolAllocateRuleList = data.allocate_rule_list.map(item => {
                    let temp = CRM.util.parsePropFromKebabToCamel(item)
                    temp.IsAllSalesClue = item.is_all_pool
                    if (item.pool_id) {
                        temp.SalesCluePoolID = item.pool_id
                        newParam.SalesCluePoolID = item.pool_id
                    }
                    if (item.pool_type) {
                        temp.poolType = typeof item.pool_type == 'string' ? {
                            'private': 1,
                            'normal': 0
                        }[item.pool_type] : item.pool_type || 0;
                    }
                    if (item.allocate_rule_id) {
                        temp.SalesCluePoolAllocateRuleID = item.allocate_rule_id
                    }
                    if (item.wheres) {
                        let _wheres = JSON.parse(item.wheres);
                        if (!_.isArray(_wheres)) _wheres = [_wheres];
                        _.each(_wheres, (item) => {
                            _.each(item.filters, (f) => {
                                f = parse.hackORule(f);
                            })
                        })
                        temp.RuleFilterList = _wheres; //this._parseFilter(item.wheres)
                    }
                    if (item.member_wheres) {
                        temp.RuleFilterGroupList = JSON.parse(item.member_wheres)
                    }
                    if (temp.MemberList) {
                        temp.MemberList = temp.MemberList.map(item => {
                            let _item = _.extend({}, item);
                            _item.MemberId && (_item.MemberID = _item.MemberId, delete _item.MemberId);
                            return _item;
                        })
                    }
                    return temp
                })
            }
            //type 1员工，2部门，3外部企业，4外部员工，5用户组，6角色
            if (data.member_pool_permissions) {
                data.member_pool_permissions.forEach(item => {
                    //线索池 成员
                    if (item.type == 1) {
                        (newParam.MemberIDs || (newParam.MemberIDs = [])).push(item.data_id);
                        if (!newParam.MemberInfos) {
                            newParam.MemberInfos = []
                        }
                        newParam.MemberInfos.push({
                            DataID: item.data_id,
                            IsAdmin: false,
                            Type: 1
                        })
                    }
                    //线索池 部门
                    if (item.type == 2) {
                        (newParam.CircleIDs || (newParam.CircleIDs = [])).push(item.data_id)
                        if (!newParam.MemberInfos) {
                            newParam.MemberInfos = []
                        }
                        newParam.MemberInfos.push({
                            DataID: item.data_id,
                            IsAdmin: false,
                            Type: 2
                        })
                    }
                    //外部企业 todo
                    if (item.type == 3) {
                        (newParam.OuterEnterpriseIDs || (newParam.OuterEnterpriseIDs = [])).push(item.data_id)
                        if (!newParam.MemberInfos) {
                            newParam.MemberInfos = []
                        }
                        newParam.MemberInfos.push({
                            DataID: item.data_id,
                            IsAdmin: false,
                            Type: 3
                        })
                    }
                    //外部成员
                    if (item.type == 4) {
                        (newParam.OuterMemberIDs || (newParam.OuterMemberIDs = [])).push(item.data_id)
                        if (!newParam.MemberInfos) {
                            newParam.MemberInfos = []
                        }
                        newParam.MemberInfos.push({
                            DataID: item.data_id,
                            IsAdmin: false,
                            Type: 4
                        })
                    }
                    // 用户组
                    if (item.type == 5) {
                        (newParam.UserGroupIDs || (newParam.UserGroupIDs = [])).push(item.data_id);
                        if (!newParam.MemberInfos) {
                            newParam.MemberInfos = []
                        }
                        newParam.MemberInfos.push({
                            DataID: item.data_id,
                            IsAdmin: false,
                            Type: 5
                        })
                    }
                    // 角色
                    if (item.type == 6) {
                        (newParam.RoleIDs || (newParam.RoleIDs = [])).push(item.data_id);
                        if (!newParam.MemberInfos) {
                            newParam.MemberInfos = []
                        }
                        newParam.MemberInfos.push({
                            DataID: item.data_id,
                            IsAdmin: false,
                            Type: 6
                        })
                    }
                })
            }
            if (data.admin_pool_permissions) {
                data.admin_pool_permissions.forEach(item => {
                    //管理员 成员
                    if (item.type == 1) {
                        (newParam.EmployeeIDs || (newParam.EmployeeIDs = [])).push(item.data_id)
                        if (!newParam.Employees) {
                            newParam.Employees = []
                        }
                        newParam.Employees.push({
                            DataID: item.data_id,
                            IsAdmin: true,
                            Type: 1
                        })
                    }
                    //管理员 部门
                    if (item.type == 2) {
                        (newParam.AdminCircleIDs || (newParam.AdminCircleIDs = [])).push(item.data_id)
                        if (!newParam.Employees) {
                            newParam.Employees = []
                        }
                        newParam.Employees.push({
                            DataID: item.data_id,
                            IsAdmin: true,
                            Type: 2
                        })
                    }
                    // 外部成员
                    if (item.type == 4) {
                        (newParam.OuterEmployeeIDs || (newParam.OuterEmployeeIDs = [])).push(item.data_id)
                        if (!newParam.Employees) {
                            newParam.Employees = []
                        }
                        newParam.Employees.push({
                            DataID: item.data_id,
                            IsAdmin: false,
                            Type: 4
                        })
                    }
                    // 用户组
                    if (item.type == 5) {
                        (newParam.AdminUserGroupIDs || (newParam.AdminUserGroupIDs = [])).push(item.data_id)
                        if (!newParam.Employees) {
                            newParam.Employees = []
                        }
                        newParam.Employees.push({
                            DataID: item.data_id,
                            IsAdmin: true,
                            Type: 5
                        })
                    }
                    // 角色
                    if (item.type == 6) {
                        (newParam.AdminRoleIDs || (newParam.AdminRoleIDs = [])).push(item.data_id)
                        if (!newParam.Employees) {
                            newParam.Employees = []
                        }
                        newParam.Employees.push({
                            DataID: item.data_id,
                            IsAdmin: true,
                            Type: 6
                        })
                    }
                })
            }
            if(data.collaborator_pool_permissions){
                data.collaborator_pool_permissions.forEach(item => {
                        // (newParam.CollaboratorPoolPermissionsIDs || (newParam.CollaboratorPoolPermissionsIDs = [])).push(item.data_id)
                        if (!newParam.CollaboratorPoolPermissions) {
                            newParam.CollaboratorPoolPermissions = []
                        }
                        newParam.CollaboratorPoolPermissions.push({
                            DataID: item.data_id,
                            // IsAdmin: true,
                            Type: 1
                        })
                    
                })
            }
            if (newParam.Employees) {
                let temp = []
                newParam.Employees.forEach(item => {
                    let obj
                    if (item.Type == 1) {
                        obj = CRM.util.getEmployeeById(item.DataID, true)
                    }
                    if (item.Type == 2) {
                        obj = FS.contacts.getCircleById(item.DataID)
                    }
                    if (item.Type == 5) {
                        obj = CRM.util.getUserGroupByIds(item.DataID)[0];
                    }
                    if (item.Type == 6) {
                        obj = CRM.util.getRoleByIds(item.DataID)[0];
                    }
                    return obj && temp.push(obj.name);
                })
                if (newParam.OuterEmployeeIDs && newParam.OuterEmployeeIDs.length) {
                    let outerEm = CRM.util.getExContactByIds(newParam.OuterEmployeeIDs, 'em');
                    let name = outerEm.map((oi) => {
                        return oi.name;
                    })
                    name && temp.push(name);
                }
                newParam.EmployeeDisplayText = temp.join(',')
            } else {
                newParam.EmployeeDisplayText = ''
            }
            if(newParam.CollaboratorPoolPermissions){
                let temp = []
                newParam.CollaboratorPoolPermissions.forEach(item => {
                    let obj
                    if (item.Type == 1) {
                        obj = CRM.util.getEmployeeById(item.DataID, true)
                    }
                    return obj && temp.push(obj.name);
                })
                newParam.CollaboratorPoolPermissionsText = temp.join(',')
            }else{
                newParam.CollaboratorPoolPermissionsText = ''
            }
            if (newParam.MemberInfos) {
                let temp = []
                newParam.MemberInfos.forEach((item) => {
                    let obj
                    if (_.contains([1, 2, 5, 6], item.Type)) {
                        if (item.Type == 1) {
                            obj = CRM.util.getEmployeeById(item.DataID)
                        }
                        if (item.Type == 2) {
                            obj = FS.contacts.getCircleById(item.DataID)
                        }
                        if (item.Type == 5) {
                            obj = CRM.util.getUserGroupByIds(item.DataID)[0];
                        }
                        if (item.Type == 6) {
                            obj = CRM.util.getRoleByIds(item.DataID)[0];
                        }
                        temp.push(obj && obj.name)
                    }
                })
                if (newParam.OuterEnterpriseIDs && newParam.OuterEnterpriseIDs.length) {
                    let outerEn = CRM.util.getExContactByIds(newParam.OuterEnterpriseIDs, 'en');
                    let name = outerEn.map((oi) => {
                        return oi.name;
                    })
                    name && temp.push(name)
                }
                if (newParam.OuterMemberIDs && newParam.OuterMemberIDs.length) {
                    let outerEm = CRM.util.getExContactByIds(newParam.OuterMemberIDs, 'em');
                    let name = outerEm.map((oi) => {
                        return oi.name;
                    })
                    name && temp.push(name)
                }
                newParam.MemberName = temp.join(',')
            } else {
                newParam.MemberName = ''
            }

            if (data.pool_permission_templates) {
                newParam.PoolPermissionList = data.pool_permission_templates.map(item => {
                    item.field_name = item.field_api_name
                    return CRM.util.parsePropFromKebabToCamel(item)
                })
            }
            newParam.SalesCluePoolID = data.data._id
            return newParam

        },

        getCluePoolByEmployeeId: function (isNocache) {
            let me = this;
            return new Promise(function (resolve, reject) {
                var key = 'cluepoollist_' + CRM.curEmpId;
                var value = CRM.get(key);
                if (value && !isNocache) {
                    resolve(value);
                    return;
                }
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/pool_service/service/get_my_leads_pool_list',
                    data: {},
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            let value = me._parseNewLeadsPoolListByEmployeeIdDatatoOld(res.Value);
                            CRM.set(key, value);
                            resolve(value);
                        } else {
                            reject(res.Result.FailureMessage);
                        }
                    },
                    error: function (error) {
                        reject(error);
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        _parseNewLeadsPoolListByEmployeeIdDatatoOld(data) {
            let newParam = {};
            newParam = data.data.map(item => {
                let temp = {
                    leadsPoolID: item._id,
                    name: item.name,
                    name__r:item.name__r,
                    overTimeHours: item.overtime_hours,
                    leadsCount: item.leads_count,
                    assignerId: item.assigner_id,
                    limitCount: item.limit_count,
                    isVisibleToMember: item.is_visible_to_member,
                    isChooseToNotify: item.is_choose_to_notify,
                    creatorId: item.created_by[0],
                    creatorTime: item.create_time,
                    updaterId: item.last_modified_by[0],
                    updateTime: item.last_modified_time
                }
                return temp
            })
            return newParam
        },

        /**
         *@desc 获取线索池成员(包括外部) type 1：内部 3：外部企业 4：外部人员
         *@param {{String}} 线索池id
         */
        getPoolMembersByID: function (data) {
            return new Promise(function (resolve, reject) {
                if (!data) {
                    resolve({});
                    return;
                }
                var param = {
                    apiName: 'LeadsObj',
                };
                if (toString.call(data) == '[object String]') {
                    param = _.extend(param, {
                        poolIds: [data]
                    })
                } else {
                    param = _.extend(param, data);
                }

                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/pool/service/get_pool_members',
                    data: param,
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            var _res = res.Value.poolMembers[param.poolIds];
                            var _data = {};
                            _.each(_res, function (item) {
                                if (!_data[item.type]) {
                                    _data[item.type] = [];
                                }
                                if (item.type == 1) {
                                    var obj = getEmployeeById(item.id);
                                    obj && _data[item.type].push(obj);
                                } else {
                                    _data[item.type].push(item);
                                }
                            });
                            var _pData = {
                                employee: _data[1], //1内部
                                exEmployee: _data[4], //4对接人
                                exEnterprise: _data[3], //3对接企业
                            }
                            _pData.employee = _.uniq(_pData.employee, false, function (item) {
                                return item.id;
                            });
                            _pData.employee = _.sortBy(_pData.employee, 'spell');

                            resolve(_pData);
                            return;
                        }
                        util.alert(res.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc  获取仓库列表
         * @return Promise
         */
        getWareHouseList: function () {
            return new Promise(function (resolve, reject) {
                var key = 'warehouse_list';
                var value = CRM.get(key);

                if (value) {
                    resolve(value);
                    return;
                }

                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/warehouse/service/query_list',
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            value = res.Value.warehouseVOs;
                            var result = [{
                                name: $t("全部仓库"),
                                value: ''
                            },];
                            _.each(value || [], function (item) {
                                if (item.name && item.id) {
                                    result.push({
                                        name: _.escape(item.name),
                                        value: item.id,
                                    });
                                }
                            })
                            CRM.set(key, result);
                            resolve(result);
                        } else {
                            util.alert(res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
            })
        },

        /** TODO
         *@desc 拉取字段级权限
         *@param {Array} || {Number} 权限类型
         *@param {Function} 回调
         *@param {Boolean} true不使用缓存数据 默认使用
         */
        fetchFieldPrivilege: function (types, cb, noUseCache, context) {
            _.isArray(types) || (types = [types]);

            var me = this;
            var privileges = {};
            var postType = noUseCache ? types : [];
            var isCrmAdmin = me.isCrmAdmin();

            noUseCache || _.each(types, function (type) {
                var privilege = FS.getAppStore('crm_fieldprivilege_' + type);
                privilege ? (privileges[type] = privilege) : postType.push(type);
            });

            //crm管理员默认有所有的权限
            if (isCrmAdmin || !postType.length) {
                cb && cb.call(context, privileges);
                return;
            }

            me.crmprivilegeAjax && me.crmprivilegeAjax.abort();
            me.crmprivilegeAjax = me.FHHApi({
                url: '/EM1HCRMUdobj/roleShareApi/getObjectsFieldPrivilege',
                data: {
                    objectTypes: postType,
                    userId: me.getCurrentEmployee().id,
                    timestamp: new Date().getTime()
                },
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        _.each(res.Value.fieldInfo, function (obj, type) {
                            privileges[type] = obj;
                            FS.setAppStore('crm_fieldprivilege_' + type, obj);
                        })
                        cb && cb.call(context, privileges);
                        return;
                    }
                    cb && cb.call(context, null);
                },
                complete: function () {
                    me.crmprivilegeAjax = null;
                },
                error: function () {
                    cb && cb.call(context, null);
                }
            }, {
                errorAlertModel: 1
            })
        },

        /** TODO
         * @desc 通过产品id获取产品，并作数据格式化
         */
        getProductByIds: function (ids, cb) {
            var me = this;
            require.async('crm-widget/table/table', function (table) {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/product/service/query_product_by_ids',
                    type: 'post',
                    data: {
                        productIds: ids,
                    },
                    success: function (res) {
                        if (res.Result.FailureCode === 0 && res.Value) {
                            var pData = {};
                            var objectDescribe = res.Value.objectDescribe || {};
                            var value = me._formatProductData(table.helper, res.Value.value, objectDescribe.fields);
                            _.each(value, function (v) {
                                pData[v._id] = v;
                            });
                            cb && cb(pData);
                            return;
                        }
                        util.alert(res.Result.FailureMessage);
                    },
                }, {
                    errorAlertModel: 1
                });
            });
        },

        //
        getWorkFlowInfo: function (param) {
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: '/EM1HCRM/WorkFlow/GetWorkFlowInfo',
                    data: {
                        Type: param.Type, //2订单 3退货单
                        DataID: param.DataID,
                        WorkFlowID: param.WorkFlowID
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value.WorkFlowDataInfoList);
                            return;
                        }
                        util.alert(res.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1
                })
            });
        },

        //
        getWorkflowProgress: function (param) {
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: '/EM1AAPPROVAL/Instance/Progress',
                    data: {
                        entityId: param.entityId,
                        dataId: param.dataId
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1
                })
            });
        },

        /**
         * @desc 获取相关团队列表
         * @param {Object} param
         *              param.apiname
         *              param.id
         * @return {Array}
         */
        getTeamMemberList: function (param) {
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/data_privilege/service/getTeamMember',
                    data: {
                        objectDescribeApiName: param.apiname,
                        dataID: param.id,
                        includeOutMember: param.includeOutMember || false
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value.teamMemberInfos || []);
                            return;
                        }
                        util.alert(res.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1
                });
            });
        },

        /**
         * 员工角色分配      permission_business_role_manage
         * 角色权限设置      permission_bussiness_function_role_setting
         * 审批权限设置      permission_bussiness_function_approve_setting
         * 添加员工          permission_business_role_manage
         * 导出             permission_business_role_export
         * 基础数据权限      data_permission_base_data
         * 数据共享          data_permission_data_share
         * 相关团队数据权限  data_permission_related_team
         * 临时权限          data_permission_temporary
         * 其他             data_permission_others
         */
        getFunctionCodes: function () {
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: '/EM2HORG/Management/Permission/GetFunctionCodesByEmployee',
                    data: {
                        appId: 'facishare-system'
                    },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            CRM.control.functionCodes = _.map(res.Value.functionCodeVos, function (item) {
                                return item.functionCode;
                            });
                            resolve(CRM.control.functionCodes);
                        } else {
                            util.alert(res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                })
            });
        },

        // 查询基础数据权限
        getObjectDataPermissions: function (successCb) {
            var userGroups = [];
            var userRoles = [];
            var myObjectDataPermissions = [];

            $.when(
                getUserGroups(function (data) {
                    userGroups = data;
                }),
                getUserRoles(function (data) {
                    userRoles = data;
                }),
                CRM.util.FHHApi({
                    data: {},
                    url: '/EM1HNCRM/API/v1/object/data_privilege/service/getCommonPrivilegeList',
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            myObjectDataPermissions = res.Value.ObjectDataPermissionInfos || res.Value.objectDataPermissionInfos;
                        };
                    }
                })
            ).always(function () {
                if (_.isEmpty(myObjectDataPermissions)) {
                    util.alert($t("接口异常请联系纷享客服"));
                    return;
                };
                var res = {
                    myObjectDataPermissions: myObjectDataPermissions
                };
                sessionStorage.setItem('userGroups', JSON.stringify(userGroups));
                sessionStorage.setItem('userRoles', JSON.stringify(userRoles));
                sessionStorage.setItem('crmDataPermissions', JSON.stringify(res));

                successCb(res);
            });

        },
        /**
         * @desc 获取是否开启CPQ
         * @return true-开启，false-未开启
         */
        getCpqConfig: function (param) {
            return new Promise(function (resolve, reject) {
                if (CRM._cache.cpqStatus !== undefined) {
                    resolve(CRM._cache.cpqStatus);
                } else {
                    CRM.util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/module_ctrl/service/check_module_status',
                        data: {
                            "moduleCode": "cpq",
                            "tenantId": CRM.enterpriseId
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                var start = (res.Value.value.openStatus == '1'); //1-开启，0-关闭
                                CRM._cache.cpqStatus = start;
                                resolve(start);
                                return;
                            }
                            util.alert(res.Result.FailureMessage);
                        }
                    }, {
                        errorAlertModel: 1
                    });
                }
            });
        },
        /**
         * @desc 获取是否开启多单位
         * @return true-开启，false-未开启
         */
        getmultipleUnitConfig: function () {
            return new Promise(function (resolve, reject) {
                if (CRM._cache.multiunitStatus !== undefined) {
                    resolve(CRM._cache.multiunitStatus);
                } else {
                    CRM.util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/module_ctrl/service/check_module_status',
                        data: {
                            "moduleCode": "multiple_unit",
                            "tenantId": CRM.enterpriseId
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                var start = (res.Value.value.openStatus == '1'); //1-开启，0-关闭
                                CRM._cache.multiunitStatus = start;
                                resolve(start);
                                return;
                            }
                            util.alert(res.Result.FailureMessage);
                        }
                    }, {
                        errorAlertModel: 1
                    });
                }
            });
        },
        /**
         * @desc 获取是否开启商品
         * @return 1-开启，0-未开启
         */
        getproductWithSpuConfig: function () {
            return new Promise(function (resolve, reject) {
                if (CRM._cache.productOpenSpu !== undefined) {
                    resolve(CRM._cache.productOpenSpu);
                } else {
                    CRM.util.FHHApi({
                        url: '//EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key',
                        data: {
                            key: "spu",
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                var start = (res.Value.value == '1'); //1-开启，0-关闭
                                CRM._cache.productOpenSpu = start;
                                resolve(start);
                                return;
                            }
                            util.alert(res.Result.FailureMessage);
                        }
                    }, {
                        errorAlertModel: 1
                    });
                }
            });
        },

        /**
         * @desc 获取当前企业所有的配置信息；
         * @param cb
         * @param errorCb
         * @param isRefresh 强制刷新
         */
        getCrmAllConfig: function (cb, errorCb, isRefresh) {
            return new Promise((resolve, reject) => {
                // 特殊key，需要转换后缓存
                let configs = {
                    '28': 'openPriceList',   // 价目表
                    cpq: 'cpqStatus',       // cpq
                    spu: 'productOpenSpu',       // spu
                    config_spu_or_sku_selector: 'spuStatus',       // 基于商品选择产品
                    multiple_unit: 'multiunitStatus', // 多单位
                    is_open_attribute: 'openAttribute', // 属性价目表
                    is_open_nonstandard_attribute: "openNsAttribute",//非标属性
                    promotion_status: 'promotionStatus',  // 促销
                    price_policy: 'advancedPricing', // 高级定价
                    ignore_price_book_valid_period: 'ignore_price_book_valid_period',
                    match_price_book_valid_field: 'match_price_book_valid_field',
                    bom_adaptation_price_list_rules: 'bom_adaptation_price_list_rules',
                    bom_price_calculation_configuration: 'bom_price_calculation_configuration', // BOM计算新逻辑
                    sale_contract: 'sale_contract', // 开启销售合同
                    change_to_new_opportunity: 'newOppoStatus', // 开启新商机
                    category_model_type: 'category_model_type', // 产品分类样式，1树形：true,2平铺:false
                    gift_amortize_basis: 'giftAmortizeBasis', //价格政策赠品取价
                    allow_switch_master_price_policy: 'allowSwitchMasterPricePolicy', //价格政策整单促是否允许切换政策
                    allow_switch_detail_price_policy: 'allowSwitchDetailPricePolicy',//价格政策单品促是否允许切换政策
                    enforce_priority: 'priceBookPriority', // 强制优先级
                    close_old_category: 'close_old_category', // 产品分类对象化：1灰度，0未灰度
                    multi_unit_price_book: 'multi_unit_price_book', // 多单位定价
                    sfa_loyalty_plugin_switch_apply_SalesOrderObj: 'sfa_loyalty_plugin_switch_apply_SalesOrderObj', // 忠诚度积分
                    sfa_loyalty_plugin_partner_switch_apply_SalesOrderObj: 'sfa_loyalty_plugin_partner_switch_apply_SalesOrderObj', //忠诚度积分是否支持合伙人
                    rebate: 'rebate',//返利
                    coupon: 'coupon', //优惠券
                    paper_coupon: 'openPaperCoupon',
                    available_range: 'openAvailablerange',
                    available_price_book: 'openAvailablePriceBook', // 开启可售价目表
                    price_book_product_valid_period: 'priceBookProductValidPeriod', // 价目表明细支持有效期配置
                    price_book_product_tiered_price: 'priceBookProductTieredPrice', // 价目表明细支持产品阶梯价
                    whether_filter_price_book_select_product: 'priceBookSelectProduct', // 本价目表已选产品
                    tenant_whether_filter_order_select_product: 'tenant_whether_filter_order_select_product', // 允许选择已选产品
                    quote_history_price: 'quote_history_price', // 报价单历史报价开关
                    bom_temp_node: 'bom_temp_node', // bom临时子件
                    simple_cpq: 'fixedCollocationOpenStatus', // 固定搭配开关
                    // 不要再加了，需要的话找负责人，通过CRM._cache.crmAllConfig.xxx获取
                };

                // value是特殊值的配置
                let values = {
                    promotion_status: '2',
                    config_spu_or_sku_selector: 'true',
                    sfa_loyalty_plugin_switch_apply_SalesOrderObj: 'true',
                    sfa_loyalty_plugin_partner_switch_apply_SalesOrderObj: 'true',
                };
                // 自定义比较函数
                let customHandler = {
                    // available_range_filter(value = '{}') {
                    //     let val = JSON.parse(value);
                    //     return val.status === '1'
                    // }
                };

                // 结果是json串
                let res_json = [
                    'match_price_book_valid_field',
                    'bom_adaptation_price_list_rules',
                    'bom_price_calculation_configuration',
                    'gift_amortize_basis',
                ];

                CRM._cache._keyConfigs = configs;

                if (CRM._cache._crmAllConfig && !isRefresh) {
                    cb && cb();
                    resolve(CRM._cache);
                    return;
                }

                this.getConfigValues().then((data) => {
                    // 特殊转换，兼容缓存历史存在的key
                    _.each(configs, (value, key) => {
                        if (customHandler[key]) {
                            CRM._cache[value] = customHandler[key]((_.findWhere(data, { key: key }) || {}).value);
                        } else {
                            CRM._cache[value] = (_.findWhere(data, { key: key }) || {}).value === (values[key] || '1');
                            if (res_json.includes(key)) CRM._cache[value] = (_.findWhere(data, { key: key }) || {}).value;
                        }
                    });

                    CRM._cache._crmAllConfig = true;
                    CRM._cache.crmAllConfig = data;

                    cb && cb();
                    resolve(CRM._cache);
                }).fail((err) => {
                    errorCb && errorCb(err);
                    reject(err);
                })
            });
        },

        /**
         * @desc 获取是否开启新商机
         * @return true-开启，false-未开启
         */
        getNewoppoConfig: function () {
            return new Promise(function (resolve, reject) {
                if (CRM._cache.newOppoStatus !== undefined) {
                    resolve(CRM._cache.newOppoStatus);
                } else {
                    CRM.util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/module_ctrl/service/check_module_status',
                        data: {
                            "moduleCode": "change_to_new_opportunity",
                            "tenantId": CRM.enterpriseId
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                var start = (res.Value.value.openStatus == '1'); //1-开启，0-关闭
                                CRM._cache.newOppoStatus = start;
                                resolve(start);
                                return;
                            }
                            util.alert(res.Result.FailureMessage);
                        }
                    }, {
                        errorAlertModel: 1
                    });
                }

            });
        },

        /**
         * @desc 获取是否开启新合同
         * @return true-开启，false-未开启
         */
        getSaleContractObj: function () {
          return new Promise(function (resolve, reject) {
                if (CRM._cache.isNewContract !== undefined) {
                  resolve(CRM._cache.isNewContract);
                  return
                }
				if(CRM?._cache?.crmAllConfig && CRM._cache.crmAllConfig.find(item => item.key == 'sale_contract')) {
					const saleContractConfig = CRM._cache.crmAllConfig.find(item => item.key == 'sale_contract');
					CRM._cache.isNewContract = saleContractConfig?.value == '1';
					resolve(CRM._cache.isNewContract);
					return;
				}
                CRM.util.getCrmAllConfig().then((result) => {
					if(result?.crmAllConfig && result.crmAllConfig.find(item => item.key == 'sale_contract')) {
						const saleContractObjConfig = result.crmAllConfig.find(item => item.key == 'sale_contract');
						CRM._cache.isNewContract = saleContractObjConfig?.value == '1';
						resolve(CRM._cache.isNewContract);
						return;
					}
					CRM._cache.isNewContract = false;
					resolve(false);
					return;
				});
          });
        },
        /**
         * @desc 判断当前账号是否存在 对象
         * @return true-存在，false-不存在
         */
        checkObjIsExist: function (objApiName) {
          return new Promise(function (resolve, reject) {
              CRM.util.FHHApi({
                  url: `/EM1HNCRM/API/v1/object/${objApiName}/controller/ListHeader`,
                  data: {
                      apiname: objApiName,
                      include_detail_describe: false,
                      include_layout: false,
                      layout_type: "add",
                      recordType_apiName: "default__c"
                  },
                  success: function (res) {
                      if (res.Result.StatusCode === 0) {
                          resolve(true);
                          return;
                      }
                      resolve(false);
                  }
              }, {
                  errorAlertModel: 1
              });
          });
        },
        /**
         * 获取操作权限
         * @param    {Object}   data    数据 {AccountObj: ['Add', 'List'], ContactObj:['Edit']}
         * @param    {boolean}  focus   是否必须请求接口
         * @return   {Object}   promise
         */
        getActionRights: function (data, focus) {
            var me = this;
            var rights = CRM._cache.actionRights = CRM._cache.actionRights || {};

            if (!focus) {
                var rightsObj = me._getActionRights(data, rights);

                if (rightsObj) {
                    return new Promise(function (resolve, reject) {
                        resolve(rightsObj);
                    });
                }
            }

            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/function/service/batchObjectActionCodesPrivilegeCheck',
                    data: {
                        apiName2ActionCodes: data
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            me._setActionRights($.extend(true, {}, res.Value.result), rights);
                            resolve(res.Value.result);

                            return;
                        }

                        util.alert(res.Result.FailureMessage);
                        reject && reject(res);
                    },
                    error() {
                        reject()
                    }
                }, {
                    errorAlertModel: 1
                });
            });
        },

        _getActionRights: function (data, rights) {
            var noActionCache = false;
            var rightsObj = {};

            _.map(data, function (val, key) {
                if (rights[key]) {
                    rightsObj[key] = rightsObj[key] || {};

                    _.map(val, function (item) {
                        if (!_.isUndefined(rights[key][item])) {
                            rightsObj[key][item] = rights[key][item];
                        } else {
                            noActionCache = true;
                        }
                    });
                } else {
                    noActionCache = true;
                }
            });

            if (noActionCache) {
                return;
            }

            return rightsObj;
        },

        _setActionRights: function (data, rights) {
            _.map(data, function (val, key) {
                rights[key] = rights[key] || {};

                _.map(val, function (item, index) {
                    rights[key][index] = item;
                });
            });
        },

        /**
         * @desc 获取价目表配置是否开启
         * @param callBack
         */
        getPriceListConfig: function (callBack) {
            if (CRM._cache.openPriceList !== undefined) {
                callBack && callBack();
                return;
            }
            ajax.getConfigValue('28').then(function (value) {
                CRM._cache.openPriceList = value == '1';
                callBack && callBack();
            })
        },

        /**
         * 获取指定的一个配置
         * @param {String | Object} param
         */
        getConfigValue: function (param, ajaxConfig) {
            var data = {
                key: param,
            }
            if (_.isObject(param)) {
                data = param;
            }
            var def = $.Deferred();

            // 接口参数必须有值
            if (Object.values(data).filter(item => typeof item !== 'undefined').length) {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/biz_config/service/get_config_value_by_key',
                    data: data,
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            def.resolve(res.Value.value);
                            return;
                        }
                        def.reject(res.Result.FailureMessage);
                    },
                    error: function (e) {
                        def.reject(e);
                    }
                }, _.extend({
                    errorAlertModel: 1
                }, ajaxConfig));
            }
            else {
                def.reject();
            }
            
            return def.promise();
        },

        /**
         * 获取全部配置，或者 获取指定的多个配置
         * @param {Boolean | Array} param
         */
        getConfigValues: function (param, ajaxConfig) {
            var data = {
                isAllConfig: true,
            }
            if (_.isArray(param)) {
                data.isAllConfig = false;
                data.keys = param;
            }
            var def = $.Deferred();
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/biz_config/service/get_config_values',
                data: data,
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        def.resolve(res.Value.values, res.Value);
                        return;
                    }
                    def.reject(res.Result.FailureMessage);
                },
            }, _.extend({
                errorAlertModel: 1
            }, ajaxConfig));
            return def.promise();
        },

        /**
         * 设置单个配置
         * @param {Object} data
         */
        setConfigValue: function (data, ajaxConfig) {
            var def = $.Deferred();
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/biz_config/service/set_config_value',
                data: data,
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        def.resolve(res);
                        return;
                    }
                    def.reject(res.Result.FailureMessage);
                },
            }, _.extend({
                errorAlertModel: 1
            }, ajaxConfig));
            return def.promise();
        },

        /**
         * 设置多个配置
         * @param {Object | Array} param
         */
        setConfigValues: function (param, ajaxConfig) {
            var data = {
                ConfigInfoList: [],
            }
            if (_.isArray(param)) {
                data.ConfigInfoList = param;
            } else if (_.isObject(param)) {
                data = param;
            }
            var def = $.Deferred();
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/biz_config/service/set_config_values',
                data: data,
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        def.resolve();
                        return;
                    }
                    def.reject(res.Result.FailureMessage);
                },
            }, _.extend({
                errorAlertModel: 1
            }, ajaxConfig));
            return def.promise();
        },

        // 获取是否开启PRM
        getPRMRight: function (noCache) {
            var def = $.Deferred();
            var isOpenPRM = CRM.get('isOpenPRM');
            if (!noCache && typeof isOpenPRM != 'undefined') {
                def.resolve(isOpenPRM);
                return def.promise();
            }
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/out_api/service/is_prm_open',
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        var value = res.Value;
                        CRM.set('isOpenPRM', value);
                        def.resolve(value);
                    }
                }
            }, {
                errorAlertModel: 1
            });
            return def.promise();
        },

        // 获取时段权限
		getAuthority(apiName){
			return new Promise((resolve,rej) => {
				 CRM.util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/pool_service/service/isGrayTimeClaimLimit',
					data: {
						api_name: apiName,
					},
					success: function (res) {
						if (res.Result.StatusCode === 0) {
							resolve(res.Value);
							return;
						}
						resolve(false);
					},
				 
				}, {
					errorAlertModel: 1
				})
			})
        },
        // 根据ApiNames获取对象具体内容
        getUserMenuItemByApiNames: function (apiNames) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi(
                    {
                        url: "/EM1HWebPage/API/v1/object/crm_menu/service/getUserMenuItemByApiNames",
                        data: { menuApiNames: apiNames },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                resolve(res.Value);
                            }
                            resolve(false);
                        },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
            });
        },
        // 获取当前菜单list
        getMenuNavList: function () {
            if (FS.util.getUserAttribute("paasbiz_home_use_cache")) {
                return new Promise((resolve, reject) => {
                    Fx.getBizAction(
                        "paasbiz",
                        "callFetch",
                        {
                            name: "getOldMenuViewList",
                            data: {__errorAlertModel: 1},
                        },
                        {
                            enableCache: true,
                        }
                    ).then((res) => {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        resolve(false);
                    });
                });
            } else {
                return new Promise((resolve, reject) => {
                    CRM.util.FHHApi(
                        {
                            url: "/EM1HWebPage/API/v1/object/crm_menu/service/getMenuDirectory",
                            data: {},
                            success: function (res) {
                                if (res.Result.StatusCode === 0) {
                                    resolve(res.Value);
                                }
                                resolve(false);
                            },
                        },
                        {
                            errorAlertModel: 1,
                        }
                    );
                })
            }
        },
        // 根据菜单Id获取菜单内容
        getCurrentMenuList: function (menuId) {
            if (FS.util.getUserAttribute("paasbiz_home_use_cache")) {
                return new Promise((resolve, reject) => {
                    Fx.getBizAction(
                        "paasbiz",
                        "callFetch",
                        {
                            name: "getOldMenuViewListByMenuId",
                            data: {menuId: menuId, __errorAlertModel: 1},
                        },
                        {
                            enableCache: true,
                        }
                    ).then((res) => {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        resolve(false);
                    });
                });
            } else {
                return new Promise((resolve, reject) => {
                    CRM.util.FHHApi(
                        {
                            url: "/EM1HWebPage/API/v1/object/crm_menu/service/getUserMenuItemByMenuId",
                            data: {
                                menuId: menuId,
                            },
                            success: function (res) {
                                if (res.Result.StatusCode === 0) {
                                    resolve(res.Value);
                                    return;
                                }
                                resolve(false);
                            },
                        },
                        {
                            errorAlertModel: 1,
                        }
                    );
                });
            }
        },
        getCrmCurrentMenu: function (cb, noCache, menuId = '') {
            // allMenu接口优化,allMenu接口拆分为2个接口，单独请求菜单和菜单item
            if (FS.util.getUserAttribute("newCRMAllMenuGray")) {
                // menuId为空时，请求菜单列表
                if (menuId) {
                    this.getCurrentMenuList(menuId).then((res) => {
                        // 冗余代码公用方法抽取
                        const updateMenuCurrent = (menuConfig, menuId, res) => {
                            if (!menuConfig || !menuId) return;

                            const { menus } = menuConfig;
                            if (!Array.isArray(menus)) return;

                            // 更新isCurrent属性
                            menus.forEach((menu) => {
                                menu.isCurrent = menu.id === menuId;
                            });

                            // 如果是新版菜单，还需要更新items
                            if (
                                menuConfig === window.CRM_All_menu_new &&
                                res?.items
                            ) {
                                const targetMenu = menus.find(
                                    (menu) => menu.id === menuId
                                );
                                if (targetMenu) {
                                    targetMenu.items = res.items;
                                }
                            }
                        };

                        updateMenuCurrent(window.CRM_All_menu_new, menuId, res);
                        updateMenuCurrent(window.CRM_All_menu, menuId);

                        cb && cb(window.CRM_All_menu_new);
                    });
                } else {
                    this.getMenuNavList().then((res) => {
                        console.log("getCrmCurrentMenu", res);
                        var { configinfo, incomplete, menus } = res;
                        if (!menus) {
                            cb && cb();
                            return;
                        };
                        var targetMenu = menus.find((menu) => menu.isCurrent);
                        this.getCurrentMenuList(targetMenu.id).then((res) => {
                            console.log("getCurrentMenuList", res);
                            // 将res拼接到指定id对象的items中
                            if (menus) {
                                if (targetMenu) {
                                    targetMenu.items = res.items;
                                }
                            }
                            window.CRM_All_menu_new = $.extend(
                                true,
                                {},
                                { 
                                    configinfo, 
                                    incomplete, 
                                    menus
                                }
                            );

                            // cb返回的内容window.CRM_All_menu_new中的menus只返回isCurrent为true的菜单
                            const filteredMenuData = {
                                ...window.CRM_All_menu_new,
                                menus: window.CRM_All_menu_new.menus.filter(menu => menu.isCurrent)
                            };
                            cb && cb(filteredMenuData);
                        });
                    });
                }
            } else {
                this.getCrmAllMenu(cb, noCache);
            }
        },       
        getCrmAllMenu: function (cb, noCache, menuId = '') {
            if (window.CRM_All_menu && !noCache) {
                cb && cb($.extend(true, {}, window.CRM_All_menu));
            }else if (
                // 原则上不修改window.CRM_All_menu
                FS.util.getUserAttribute("paasbiz_home_use_cache") &&
                !noCache
            ) {
                Fx.getBizAction(
                    "paasbiz",
                    "callFetch",
                    {
                        name: "getOldMenu",
                        data: {},
                    },
                    {
                        enableCache: true,
                    }
                ).then((res) => {
                    if (res.Result.StatusCode === 0) {
                        window.CRM_All_menu = $.extend(
                            true,
                            {},
                            window.CRM_All_menu,
                            res.Value
                        );
                        cb && cb(res.Value);
                    }
                });
            } else {
                CRM.util.FHHApi(
                    {
                        url: "/EM1HWebPage/API/v1/object/crm_menu/service/all_menu",
                        success: function (data) {
                            if (data.Result.StatusCode == 0) {
                                window.CRM_All_menu = $.extend(
                                    true,
                                    {},
                                    window.CRM_All_menu,
                                    data.Value
                                );
                                saveToIndexDB("crm_all_menu", data.Value);
                                console.log("success", data.Value);
                                cb && cb(data.Value);
                            }
                        },
                        error: function () {
                            console.log("error");
                            getFromIndexDB("crm_all_menu").then(cb);
                        },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
            }

            function saveToIndexDB(key, data) {
                if (window.localforage) {
                    var store = window.localforage.createInstance({
                        name: "UI-PAAS_cache",
                    });

                    store.setItem(key, data);
                }
            }

            function getFromIndexDB(key) {
                if (window.localforage) {
                    var store = window.localforage.createInstance({
                        name: "UI-PAAS_cache",
                    });

                    return store.getItem(key).then(function (value) {
                        return value;
                    });
                }
                return Promise.resolve(null);
            }
        },

        getFieldsByApiName: function (apiname, noCache) {
            var def = $.Deferred();
            var fields = CRM.get('fields.' + apiname);
            if (!noCache && typeof fields != 'undefined') {
                def.resolve(fields);
                return def.promise();
            }
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/describe/service/findDraftByApiName',
                data: {
                    draft_apiname: apiname,
                    include_layout: false,
                    layout_type: "detail"
                },
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        let data = res.Value.objectDescribeDraft.fields;
                        CRM.set('fields.' + apiname, data);
                        def.resolve(data);
                        return data;
                    }
                    def.reject(res.Result);
                }
            }, {
                errorAlertModel: 1
            });
            return def.promise();
        },

        getObjectConfig(param) {
            let _param = {};
            if (_.isString(param)) {
                _param.apiNameList = [param];
            } else if (_.isArray(param)) {
                _param.apiNameList = param;
            } else if (_.isObject(param)) {
                _param = param;
            }
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/describe/service/findObjectConfig',
                    data: _.extend({
                        includeBusiness: false,
                        includeFilters: false,
                    }, _param),
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            let data = res.Value.config;
                            resolve(data);
                            return data;
                        }
                        reject(res.Result);
                    }
                }, {
                    errorAlertModel: 1
                });
            })
        },

        // 通过apiname判断是否有该对象权限
        getPermissionByApiName(api_names) {
            var def = $.Deferred();
            let permission = CRM.get('permission') || {};
            let all = _.every(api_names, (api_name) => {
                return permission[api_name] === true
            })
            if (all) {
                let data = {};
                _.each(api_names, (api_name) => {
                    data[api_name] = permission[api_name]
                })
                def.resolve(data);
                return def.promise();
            }
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/version_privilege/service/object_is_avaliable_by_license',
                data: {
                    api_names: api_names
                },
                success(res) {
                    if (res.Result.StatusCode == 0) {
                        $.extend(permission, res.Value);
                        CRM.set('permission', permission);
                        def.resolve(permission);
                    }
                }
            })
            return def.promise();
        },

        /**
         * @desc 获取子产品明细和分组信息
         */
        getSubProductObj: function (id, cb) {
            return CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/SubProductObj/controller/TreeRelatedList',
                data: {
                    "source_data_id": id,
                    "source_obj_desc_api_name": 'ProductObj',
                    "target_parent_obj_desc_api_name": "SubProductCatalogObj",
                    "target_child_obj_desc_api_name": "SubProductObj",
                    "associated_child_object_field_related_list_name": "target_related_list_subproductobj",
                    "associated_parent_object_field_related_list_name": "target_related_list_subproductcatalogobj",
                    "target_parent_search_query_info": "{\"limit\":200,\"offset\":0}",
                    "target_child_search_query_info": "{\"limit\":200,\"offset\":0}"
                },
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        cb && cb(res.Value);
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                }
            }, {
                errorAlertModel: 1
            })
        },
        /**
         * @desc 请求bom节点；
         * @param ids Array bom产品组合的id
         * @param include_desc 是否返回描述信息
         * @param cb
         * @returns {{XHR}}
         */
        fetchBomChildren: function (ids, include_desc = false, success, others = {}, hideLoading = false, error) {
            if(!hideLoading) CRM.util.showLoading_tip();
            return CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/BOMObj/controller/TreeRelatedListV1',
                data: _.extend({
                    bom_core_id: '',
                    new_bom_path: null,
                    new_bom_id: null,
                    "root_product_ids": ids, //根产品id
                    // "parent_data_id":'5e65f0a7f4f92100010865c2',//根产品id
                    "extend_obj_desc_api_name": "ProductGroupObj", //需额外返回的对象描写,改成固定为分组即可
                    "child_search_query_info": "{\"limit\":2000,\"offset\":0}",
                    "price_book_id": '',
                    'include_desc': include_desc,
                    'bom_list': [],
                    'include_constraint': false,
                    'account_id': ''
                }, others),
                success: function (res) {
					if(!hideLoading) CRM.util.hideLoading_tip();
                    if (res.Result.StatusCode === 0) {
                        success && success(res.Value);
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                    error && error(res.Result);
                },
                error: function () {
					if(!hideLoading) CRM.util.hideLoading_tip();
                }
            }, {
                errorAlertModel: 1
            })
        },
        /**
         * @desc 查询的产品的bomcore版本下所有节点&复用bom的所有节点；
         * @param {{Array}} ids 产品id
         * @param {{Object}} others 其他参数
         * @returns {{Promise}}
         */
        fetchBomAndRelatedBomData: function (ids, params = {bom_core_id: ''}, isChildBom ) {
            let _this = this;
            let loadingKey = ids[0] || 'fetchBomAndRelatedBomData';
			CRM.util.showLoading_tip('', null, loadingKey);

            const _fetchBomData = function (args) {
                return new Promise((resolve, reject) => {
                    _this.fetchBomChildren(ids, false, resolve, {...params, ...args}, true, reject)
                })
            }
            const _fetchSubBomData = async function (allSubBomList) {
                let _promiseArr = allSubBomList.map(async item => {
                    let data =  {
                        res: await _fetchBomData({ 
                            bom_core_id: item.bom_core_id,
                            new_bom_path: item.new_bom_path,
                            new_bom_id: item.new_bom_id,
                            include_all_sub_core_id: false, // 是否返回所有复用bom的id等数据
                            bom_list: null // 查子bom不用传
                        }),
                        config: item
                    };
                    return data;
                });
                return await Promise.all(_promiseArr);
            }

            // 改第一层子件的parent_bom_id， 合并数据
            function _changeParentBomId(res, config){
				let _mapObj = _this.keyBy(res.dataMapList, 'describeApiName');
				let p_bom_id = _mapObj['ProductObj'].dataList[0]?.bom_id;
				if(!p_bom_id) return console.error('未查到产品包');
                let bomData = _mapObj['BOMObj'].dataList[0];
                let productId__r = _mapObj['ProductObj']?.dataList[0].name;
				let current_root_new_path = '';
				_mapObj['ProductObj']?.dataList.forEach(item => {
					item.current_root_new_path = item.bom_id;
				});
				_mapObj['BOMObj']?.dataList.forEach(item => {
					current_root_new_path = item.current_root_new_path;
					if (item.parent_bom_id === p_bom_id) {
						// 替换parent_bom_id
						item.parent_bom_id = config.new_bom_id;
						item.root_id = config.new_bom_id;
					}
				});
				_mapObj['ProductGroupObj']?.dataList.forEach(item => {
                    item.current_root_new_path = current_root_new_path;
					if (item.parent_bom_id === p_bom_id) {
						// 替换parent_bom_id
						item.parent_bom_id = config.new_bom_id;
					}
				});
				_mapObj['BomAttributeConstraintLinesObj']?.dataList.forEach(item => {
					item.current_root_new_path = current_root_new_path ;     // 给约束规则加标记属于哪个包
					item._newRuleId = current_root_new_path + '.'+ item._id; // 给约束规则添加 唯一标记
                    item.productId__r = productId__r;
                    item.coreId = bomData?.core_id;
                    item.coreId__r = bomData?.core_id__r;
				});
                // 记录bom版本
                _mapObj['AplFunctionList']?.dataList.forEach(item => {
                    item.current_root_new_path = current_root_new_path ;     // 给约束规则加标记属于哪个包
                    item.productId__r = productId__r;
                    item.coreId = bomData?.core_id;
                    item.coreId__r = bomData?.core_id__r;
                });
				_mapObj['AssignAplFunctionList']?.dataList.forEach(item => {
					item.current_root_new_path = current_root_new_path ;     // apl计算 标记属于哪个包
                    item.productId__r = productId__r;
                    item.coreId = bomData?.core_id;
                    item.coreId__r = bomData?.core_id__r;
				});
				_mapObj['TriggerFormulaMap']?.dataList.forEach(item => {
					item.current_root_new_path = current_root_new_path ;     // 高级公式触发 标记属于哪个包
				});

				return _mapObj;
			}

            let _p = _fetchBomData({
                include_all_sub_core_id: true, // 首次请求，查询所有子bom的core_id
                ...params
            }).then(async result => {
                let _BOMObj = result.dataMapList.find(item => item.describeApiName === 'BOMObj');
                let ProductObj = result.dataMapList.find(item => item.describeApiName === 'ProductObj');
                let ProductGroupObj = result.dataMapList.find(item => item.describeApiName === 'ProductGroupObj');
                let bomData = _BOMObj.dataList[0];
                let productId__r = ProductObj?.dataList[0].name;
                if (ProductGroupObj && ProductGroupObj.dataList.length) {
                    let current_root_new_path = _BOMObj.dataList[0]?.current_root_new_path || '';
                    ProductGroupObj.dataList.forEach(item => {
                        item.current_root_new_path = current_root_new_path;
                    });
                }
                let _BomAttributeConstraintLinesObj = result.dataMapList.find(item => item.describeApiName === 'BomAttributeConstraintLinesObj');
                if (_BomAttributeConstraintLinesObj && _BomAttributeConstraintLinesObj.dataList.length) {
                    let current_root_new_path = _BOMObj.dataList[0]?.current_root_new_path || '';
                    _BomAttributeConstraintLinesObj.dataList.forEach(item => {
                        item.current_root_new_path = current_root_new_path;
                        item._newRuleId = current_root_new_path + '.'+ item._id; // 给约束规则添加 唯一标记
                        item.productId__r = productId__r;
                        item.coreId = bomData?.core_id;
                        item.coreId__r = bomData?.core_id__r;
                    })
                }

                let _AplFunctionList = result.dataMapList.find(item => item.describeApiName === 'AplFunctionList');
                if (_AplFunctionList?.dataList?.length) {
                    let current_root_new_path = _BOMObj.dataList[0]?.current_root_new_path || '';
                    _AplFunctionList.dataList.forEach(item => {
                        item.current_root_new_path = current_root_new_path;
                        item.productId__r = productId__r;
                        item.coreId = bomData?.core_id;
                        item.coreId__r = bomData?.core_id__r;
                    })
                }
                // apl 计算
				let AssignAplFunctionList = result.dataMapList.find(item => item.describeApiName === 'AssignAplFunctionList');
				if (AssignAplFunctionList && AssignAplFunctionList.dataList.length) {
					let current_root_new_path = _BOMObj.dataList[0]?.current_root_new_path || '';
					AssignAplFunctionList.dataList.forEach(item => {
						item.current_root_new_path = current_root_new_path;
                        item.productId__r = productId__r;
                        item.coreId = bomData?.core_id;
                        item.coreId__r = bomData?.core_id__r;
					})
				}

				// 高级公式触发字段
				let TriggerFormulaMap = result.dataMapList.find(item => item.describeApiName === 'TriggerFormulaMap');
				if (TriggerFormulaMap && TriggerFormulaMap.dataList.length) {
					let current_root_new_path = _BOMObj.dataList[0]?.current_root_new_path || '';
					TriggerFormulaMap.dataList.forEach(item => {
						item.current_root_new_path = current_root_new_path;
					})
				}

				ProductObj.dataList.forEach(d => d.current_root_new_path = d.bom_id);

                // 如果是直接查的子 bom，需要处理下子件数据
                if(isChildBom){
					_changeParentBomId(result, params)
				}
                // 合并复用 bom coreid
				// if(params.extendBomCoreId?.length){
				// 	result.allSubBomList = result.allSubBomList || [];
				// 	params.extendBomCoreId.forEach(d => {
				// 		let f = result.allSubBomList.find(c => c.bom_core_id === d.bom_core_id);
				// 		if(!f) result.allSubBomList.push(d)
				// 	})
				// }
                if (result?.allSubBomList?.length) {
                    // 获取子bom数据
                    let releatedBomList = await _fetchSubBomData(result.allSubBomList);
                    releatedBomList.forEach(data => {
                        let { res, config } = data;
						let _mapObj = _changeParentBomId(res, config);
						_mapObj && Object.entries(_mapObj).forEach(([describeApiName, {dataList}]) => {
							if (!['BOMObj', 'ProductGroupObj', 'BomAttributeConstraintLinesObj', 'AplFunctionList', 'AssignAplFunctionList', 'TriggerFormulaMap'].includes(describeApiName)
							) return;
                            // 合并数据
							let data = result.dataMapList.find(item => item.describeApiName === describeApiName)
							if (data) {
								data.dataList = data.dataList.concat(dataList);
							} else {
								result.dataMapList.push({describeApiName, dataList});
							}
						})
                    });
                }
				CRM.util.hideLoading_tip(null, loadingKey);
				return result;
            }).catch(err => {
            	CRM.util.hideLoading_tip(null, loadingKey);
            	console.error(err);
            });
            return _p;
        },

        /**
         * @desc 获取固定搭配数据，先查询bomcore版本，在查询选配明细；
         */
        async fetchFixedCollocationData(productId) {
            const isArrPrdId = Array.isArray(productId);
            const productIds = isArrPrdId ? productId : [productId];
            if (!productIds.length || !productId) return [];
            const getBomCoreId = () => {
                return CRM.util.fetchObjRelatedList('BomCoreObj', {
                    associated_object_describe_api_name: 'BomCoreObj',
                    search_query_info: JSON.stringify({
                        limit: 2000,
                        offset: 0,
                        filters: [
                            {
                                field_name: 'product_id',
                                field_values: productIds,
                                operator: 'IN'
                            }
                        ],
                        orders: [
                            {
                                fieldName: 'last_modified_time',
                                isAsc: false
                            }
                        ]
                    }),
                    extractExtendInfo: true,
                    ignore_scene_record_type: false,
                    include_describe: false,
                    serializeEmpty: false
                }, true).then(res => ((res?.dataList || []).map((item) => item._id)));
            }
            const getBomData = (bomCoreId, id) => {
                return new Promise((resolve) => {
                    CRM.util.fetchBomChildren([id], false, (res) => {
                        resolve(res.dataMapList);
                    }, {bom_core_id: bomCoreId}, true)
                })
            }
            const getBomDatas = (bomCoreIds) => (Promise.all(bomCoreIds.map((id, i) => getBomData(id, productIds[i]))));
            try {
                const bomCoreIds = await getBomCoreId();
                if (!bomCoreIds.length) return [];
                const allDataList = await getBomDatas(bomCoreIds);
                return isArrPrdId ? allDataList : allDataList?.[0];
            } catch {
                return [];
            }
        },

        getNodeList: function (data) {
            CRM.util.showLoading_new();
            return new Promise((resolve) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/bom/service/nodeList',
                    data: data,
                    success: function (res) {
                        CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                    },
                    error: function () {
                        CRM.util.hideLoading_new();
                    }
                }, {
                    errorAlertModel: 1
                })
            });
        },

        getParentsList: function (id) {
            return new Promise((resolve) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/bom/service/getAllParentProdList',
                    data: {
                        productId: id,
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value.parentProdIdList);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                    },
                }, {
                    errorAlertModel: 1
                })
            });
        },

        fetchLeafChildren: function (ids, cb) {
            return CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/bom/service/nodeListBytIds',
                data: {
                    "nodeId": ids
                },
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        cb && cb(res.Value);
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                }
            }, {
                errorAlertModel: 1
            })
        },

        /**
         * @desc 获取对象的所有业务类型的表头信息；
         * @param param {apiname, recordType, dataId}
         * @returns {Promise<any>}
         */
        getColumnsByApiname: function (param) {
            return new Promise(resolve => {
                var columnsAjax = CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/controller/DetailListHeader',
                    data: {
                        "master_record_type": param.recordType,
                        "master_data_id": param.dataId,
                        "layout_type": "list",
                        "related_list_component": param.relatedListComponent,
                        ...(param.extParams || {})
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            var rv = res.Value;
                            var fields = rv.objectDescribe.fields;
                            if (rv.objectDescribeExt) {
                                _.each(rv.objectDescribeExt.fields, (fieldInfo, fApiName) => {
                                    fields[fApiName] = Object.assign(fields[fApiName], fieldInfo);
                                })
                            }
                            CRM.util.FHHApi({
                                url: '/EM1HNCRM/API/v1/object/custom_scene/service/findFieldWidthConfig',
                                data: {
                                    describeApiNames: [param.apiname],
                                    extendAttribute: null
                                },
                                success: function (res) {
                                    if (res.Result.StatusCode === 0) {
                                        _.each(res.Value.visibleFieldsWidth[param.apiname], function (item) {
                                            item.width && fields[item.field_name] && (fields[item.field_name].width = item.width);
                                        })
                                    }
                                    resolve(rv);
                                }
                            }, {
                                errorAlertModel: 1
                            })
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                    },
                    complete: function () {
                        columnsAjax = null;
                    }
                }, {
                    errorAlertModel: 1
                })
            })

        },

        getObjectDescribe(data) {
            let _data = {
                "describe_apiname": "",
                "get_label_direct": true,
                "include_buttons": false,
                "include_layout": false,
                "include_related_list": false,
                "include_describe_extra": true,
                "layout_type": "detail"
            }
            if (_.isString(data)) {
                _data.describe_apiname = data;
            } else {
                _data = _.extend(_data, data);
            }
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName`,
                    data: _data,
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                        reject(res.Result);
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        getDescribeLayout: function (data) {
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/${data.apiname}/controller/DescribeLayout`,
                    data,
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                        reject(res.Result);
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },


        getFieldsByDescribe: async function (apiname) {
            const res = await this.getDescribeLayout({
                "apiname": apiname,
                "include_layout": false,
                "include_detail_describe": true
            }),
                fields = res.objectDescribe.fields,
                fieldsMap = {
                    [apiname]: fields
                };
            (res.detailObjectList || []).forEach(detail => {
                fieldsMap[detail.objectApiName] = detail.objectDescribe.fields;
            });
            return fieldsMap;
        },

        /**
         * 
         * @param {Object} data {describe_apiname_list: Array, includeSocialObject:Boolean|false, includeBigObject:Boolean|false}
         * @returns Promise
         */
        getDescribeListByApiNames(data) {
            let _data = _.extend({
                describe_apiname_list: [],
                includeSocialObject: false,
                includeBigObject: false
            }, data);
            return this.ajax_base( "/EM1HNCRM/API/v1/object/describe/service/findDescribeListByApiName", _data,  function(){}, true);
        },

        /**
         * 获取指定对象的指定插件配置
         * @param apiName
         * @param pluginName
         */
        getObjectPlugin(apiName, pluginName) {
            const request = (apiName) => {
                const CACHE_PLUGIN_DATA = CRM.get('CACHE_PLUGIN_DATA') || {};
                const cacheData = CACHE_PLUGIN_DATA[apiName];
                if (cacheData) return Promise.resolve(cacheData);
                return new Promise((resolve, reject) => {
                    CRM.util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/' + apiName + '/controller/PluginList',
                        data: {
                            actionCode: 'Add',
                            agentType: 'web'
                        },
                        success(res) {
                            if (res.Result.StatusCode === 0) {
                                const plugin = res.Value.domains;
                                CRM.set('CACHE_PLUGIN_DATA', {
                                    ...CACHE_PLUGIN_DATA,
                                    [apiName]: plugin
                                });
                                resolve(plugin);
                            } else {
                                reject();
                            }
                        },
                        error() {
                            reject();
                        }
                    })
                })
            }

            if (!apiName) {
                return Promise.resolve();
            }
            if (Array.isArray(apiName)) {
                return Promise.all(apiName.map((a) => request(a)));
            } else if (pluginName) {
                return request(apiName).then((plugins) => plugins.find((plugin) => plugin.pluginApiName === pluginName));
            }
            return request(apiName);
        },

        /**
         * @description: 对象是否开启新建|编辑布局
         * @param {*} describeApiName： 对象apiName
         * @return {*}
         */
        getEditLayoutStatus: function (describeApiName) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/layout/service/get_edit_layout_status',
                    data: {
                        describeApiName,
                    },
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            let { status } = res.Value; // 0: 关 1：开
                            resolve(!!status);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                        reject();
                    },
                    error: function() {
                        reject();
                    },
                    complete: function () {

                    }
                }, {
                    errorAlertModel: 1
                });
            });
        },

        /**
         * @desc 根据客户id获取价目表
         * @param param:{
         *      "accountId": "xxxxxxx",		// 客户id
         *      "partnerId": "xxxxxx",		// 合作伙伴id
         *      "enableIsValidorder":false  //原价目表逻辑true:编辑时下发,禁用和过期的也可以出现，false:新建时用,不出现禁用和过期的
         * }
         * @returns {Promise<any>}
         */
        getPriceBookList: function (param) {
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/available_range/service/get_available_price_book_list`,
                    data: _.extend({
                        accountId: '',
                        partnerId: '',
                        enableIsValidorder: false,
                        object_api_name: '',         // 主对象apiname
                        detail_object_api_name: '',  // 从对象apiname

                    }, param),
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc 取价服务接口，替换已选产品价格；
         * @param param:{
		 *     fullProductList:[
		 *         {
		 *             productId:'xxx',
		 *         },
		 *         {
		 *             productId:'xxx',
		 *         }
		 *     ]
		 * }
         * @returns {Promise<any>}
         */
        replacePriceForPriceBook: function (param) {
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/available_range/service/get_real_price`,
                    data: _.extend({
                        accountId: '',
                        partnerId: '',
                        productIdList: [],
                        fullProductList: []
                    }, param),
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        reject(res);
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                    error: function () {
                        util.alert($t("暂时无法获取相关数据请稍后重试"));
                        reject();
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        poolDelete: function (param, config) {
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/pool_service/service/delete',
                    data: Object.assign({
                        api_name: "LeadsPoolObj"
                    }, param),
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            util.remind(1, config.type == '1' ? $t("转移成功") : $t("删除成功"));
                            resolve(param);
                            return;
                        }
                        reject();
                        util.alert(res.Result.FailureMessage);
                    }
                }, {
                    submitSelector: config.$btn,
                    errorAlertModel: 1
                });
            })
        },

        /**
         * @desc 获取表格表头描述信息；
         * @param obj
         * @param ajaxConfig
         * @param cb
         * @returns {{XHR}}
         */
        fetchColumns: function (obj, ajaxConfig, cb) {
            return CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + obj.apiname + '/controller/ListHeader',
                data: _.extend({
                    apiname: obj.apiname,
                    check_edit_permission: false,
                    include_layout: true,
                    layout_by_template: true,
                    layout_type: "list"
                }, obj),
                success: function (res) {
                    if (res.Result.StatusCode == 0) {
                        cb && cb(res);
                        return
                    }
                    util.alert(res.Result.FailureMessage)
                },
            }, _.extend({
                errorAlertModel: 1
            }, ajaxConfig));
        },

        fetchDescribe: function (apiname, obj, callback, isCache) {
            if (isCache && CRM.get(`describe.${apiname}`)) {
                callback && callback(CRM.get(`describe.${apiname}`));
                return;
            }
            return util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + apiname + '/controller/DescribeLayout',
                data: _.extend({
                    include_layout: false,
                    apiname: apiname,
                    layout_type: "list"
                }, obj),
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        CRM.set(`describe.${apiname}`, res.Value);
                        callback && callback(res.Value);
                        return
                    }
                    util.alert(res.Result.FailureMessage)
                }
            }, {
                errorAlertModel: 1
            })
        },

        // 校验选配bom
        checkBom: function (param, callback) {
            return CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/bom/service/check_bom',
                data: param,
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        callback && callback(res.Value);
                        return res.Value
                    }
                    util.alert(res.Result.FailureMessage)
                }
            }, {
                errorAlertModel: 1
            })
        },

        // 编辑bom权限
        checkEditBomPower: function (callback) {
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/bom/service/haveConfigBOMPrivilege',
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        callback && callback(res.Value);
                        return
                    }
                    util.alert(res.Result.FailureMessage)
                }
            }, {
                errorAlertModel: 1
            })
        },

        // 获取BOM分组信息；
        getGroupInfo: function (id, cb) {
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/bom/service/get_product_group_by_ids',
                data: [id],
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        cb(res.Value[0])
                    } else {
                        cb(null);
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取数据!"))
                    }
                },
            }, {
                errorAlertModel: 1
            })
        },

        // 校验产品是否在阶梯价目表中；
        checkProductIsInLadderPrice: function (param, callback) {
            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/bom/service/check_tiered_price_product_bom',
                data: param,
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        callback && callback(res.Value);
                        return
                    }
                    util.alert(res.Result.FailureMessage)
                }
            }, {
                errorAlertModel: 1
            })
        },

        /**
         * @desc 获取某产品的所有适用可售范围的价目表明细；
         * @param param
         * @returns {Promise<any>}
         */
        getPriceBookProductInfo: function (param) {
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/available_range/service/get_price_book_list_by_product_ids`,
                    data: _.extend({
                        accountId: '',
                        partnerId: '',
                        productIdList: [],
                        enableIsValidorder: false
                    }, param),
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc 查价工具；根据客户和产品查价
         * @param param
         * @returns {Promise<any>}
         */
        getPriceByCustomerAndProduct: function (param) {
            CRM.util.showLoading_new();
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/available_range/service/get_price_tool_data_list1`,
                    data: _.extend({
                        account_id: '',
                        product_id: '',
                    }, param),
                    success: function (res) {
                        CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc 查价工具；根据客户和产品查价
         * @param param
         * @returns {Promise<any>}
         */
        getPriceByProduct: function (param) {
            CRM.util.showLoading_new();
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/available_range/service/get_price_tool_data_list2`,
                    data: _.extend({
                        price_book_id: '',
                        product_id: '',
                    }, param),
                    success: function (res) {
                        CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        // 查价工具表头
        // getPriceBookListHeader:function (param) {
        // 	return new Promise(function(resolve){
        // 		CRM.util.FHHApi({
        // 			url: `/EM1HNCRM/API/v1/object/available_range/service/PriceBookListHeader`,
        // 			data: _.extend({
        //
        // 			}, param),
        // 			success: function(res) {
        // 				if (res.Result.StatusCode === 0) {
        // 					resolve(res.Value);
        // 					return;
        // 				}
        // 				util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
        // 			},
        // 		}, {
        // 			errorAlertModel: 1
        // 		})
        // 	})
        // },

        // 查对象列表
        fetchObjList: function (apiname, param) {
            !param.hideLoading && CRM.util.showLoading_new();
            return new Promise(function (resolve, reject) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/${apiname}/controller/List`,
                    data: _.extend({
                        "object_describe_api_name": apiname,
                        "search_template_id": "",
                        "include_describe": false,
                        "search_template_type": "default",
                        "ignore_scene_record_type": false,
                        "search_query_info": "{\"limit\":100,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"name\",\"isAsc\":false}]}"
                    }, param),
                    success: function (res) {
                        !param.hideLoading && CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        reject(res.Result);
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        // 同步BOM结构
        SynchronizeBom: function (param) {
            CRM.util.showLoading_new();
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/bom/service/syncToOtherNode`,
                    data: _.extend({
                        "node": { "bomId": "", "rootId": "" },     //同步的节点
                        "nodeList": [{ "bomId": "", "rootId": "" }],     //被同步的节点
                        "same": false   //是否同步给相同的产品结构
                    }, param),
                    success: function (res) {
                        CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc 查价工具；根据客户查价
         * @param param
         * @returns {Promise<any>}
         */
        getPriceByCustomer: function (param) {
            CRM.util.showLoading_new();
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/available_range/service/get_price_tool_data_list3`,
                    data: _.extend({
                        account_ids: [],
                    }, param),
                    success: function (res) {
                        CRM.util.hideLoading_new();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        // 查从对象列表
        fetchObjRelatedList: function (apiname, param, noLoading = false) {
            if (!noLoading) CRM.util.showLoading_tip();
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/${apiname}/controller/RelatedList`,
                    data: _.extend({
                        associate_object_data_id: "",
                        associate_object_describe_api_name: "",
                        associated_object_describe_api_name: "",
                        associated_object_field_related_list_name: "",
                        include_associated: true,
                        is_ordered: true,
                        search_query_info: '{"limit":2000,"offset":0}'
                    }, param),
                    success: function (res) {
                        if (!noLoading) CRM.util.hideLoading_tip();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc 校验产品约束关系
         * @param param
         * @returns {Promise<any>}
         */
        checkProductConstraint: function (param) {
            CRM.util.showLoading_tip();
            return new Promise(function (resolve) {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/constraint/service/check_product_constraint`,
                    data: _.extend({
                        productIds: [],
                    }, param),
                    success: function (res) {
                        CRM.util.hideLoading_tip();
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                }, {
                    errorAlertModel: 1
                })
            })
        },

        /**
         * @desc 获取是否开启属性产品
         * @return 1-开启，0-未开启
         */
        getProductWithAttr: function () {
            return new Promise(function (resolve, reject) {
                if (CRM._cache.openAttribute !== undefined) {
                    resolve(CRM._cache.openAttribute);
                } else {
                    CRM.util.getConfigValue('is_open_attribute').then(status => {
                        CRM._cache.openAttribute = status == '1';
                        resolve(CRM._cache.openAttribute);
                    })
                }
            });
        },

        // 获取产品分类列表数据
        getCategoryDataList(data, opts) {
            return new Promise((resolve, reject) => {
                const params = {
                    filterCategory: 'Product', // ALL 全部 Shop 商城分类 Product 产品分类 , 上游只显示产品分类（890新增）
                    ...data,
                }
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/product_category/service/list',
                    data: opts?.parseParam ? opts.parseParam(params) : params,
                    success(res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value.result);
                            return;
                        }
                        reject();
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"))
                    },
                    complete() {
                        reject();
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        // 产品分类对象化灰度接口
        getCloseOldCategory: function () {
            return new Promise((resolve, reject) => {
                if (CRM._cache.close_old_category != void (0)) {
                    resolve(CRM._cache.close_old_category);
                } else {
                    CRM.util.getConfigValue('close_old_category').then(status => {
                        CRM._cache.close_old_category = status == '1'; // 1灰度，0未灰度
                        resolve(CRM._cache.close_old_category);
                    }).fail(err => {
                        CRM.util.alert(err || $t("暂时无法获取相关数据请稍后重试"))
                    });
                }
            });
        },

        // 获取用户级配置，选产品或明细时，是否包含已选产品；
        getIncludeSelected: function (param) {
            let url = `/EM1HNCRM/API/v1/object/biz_config/service/get_set_user_config`;
            let data = {
                key: param.key,
            };
            return this.ajax_base(url, data, function (res) {
                CRM._cache.isIncludeSelectedForMd = res.value ? res.value == '1' : '';
            })
        },

        // 设置用户级配置，选产品或明细时，是否包含已选产品；
        setIncludeSelected: function (param) {
            let url = `/EM1HNCRM/API/v1/object/biz_config/service/get_set_user_config`;
            let data = {
                key: param.key,
                value: param.value,
            };
            return this.ajax_base(url, data, function (res) {
                CRM._cache.isIncludeSelectedForMd = res.value ? res.value == '1' : '';
            })
        },

        duplicateCheck(param) {
            return new Promise((resolve, reject) => {
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/sfa_duplicate_search/service/duplicate_check',
                    data: param,
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value.duplicated);
                            return;
                        }
                        reject(res.Result);
                    },
                }, {
                    errorAlertModel: 1
                });
            })
        },
        // 获取本位币信息；
        getFunctionalCurrency: function (param) {
            let url = `/EM1HNCRM/API/v1/object/currency/service/find_functional_currency`;
            return this.ajax_base(url, {}, function (res) {
                CRM._cache.functionalCurrency = res.currencyCode || 'CNY';
            }, true)
        },

        // 获取是否开启多币种；
        getCurrencyStatus: function (param) {
            let url = `/EM1HNCRM/API/v1/object/currency/service/multi_currency_status`;
            return this.ajax_base(url, {}, function (res) {
                CRM._cache.currencyStatus = res.status == '1';
            }, true)
        },

        // 是否支持促销；
        isSupportPromotion: function (param) {
            let url = `/EM1HNCRM/API/v1/object/promotion_sfa/service/is_promotion_open`;
            return this.ajax_base(url, _.extend({
                "recordType": '' // 通过业务类型查询是否支持促销
            }, param), null, true)
        },

        // 获取业务类型
        getRecordType(param) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/' + param.describeApiName + '/controller/ValidRecordType',
                    data: param,
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value.record_list);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                        reject(res.Result);
                    },
                }, {
                    errorAlertModel: 1
                });
            })
        },

        //
        // 判断是否开启多组织
        //
        isOpenOrg: function (cb) {
            if (CRM._cache.openOrgStatus !== undefined) {
                cb && cb();
                return;
            }
            let url = `/EM1HORG/Management/Enterprise/GetEnterpriseConfig`;
            return this.ajax_base(url, {
                key: 'openOrganization'
            }, function (res) {
                CRM._cache.openOrgStatus = res.value == 1;
                cb && cb();
            }, true);
        },

        // server计算产品包价格；
        calculateBomPrice: function (param) {
            let url = `/EM1HNCRM/API/v1/object/bom/service/query_bom_price`;
            return this.ajax_base(url, _.extend({
                "rootBomId": "", // 根结点bom_id
                // "priority": false, // 是否开启强制优先级
                "accountId": "",
                "bomList": [],
                "detailApiName": ""
            }, param))
        },

        //获取下载权限
        fetchObjectDownloadPermission: function (apiname) {
            return new Promise(function (resolve) {
                if (!ajax.__downloadPermissionCache) {
                    ajax.__downloadPermissionCache = {};
                }

                if (!_.isUndefined(ajax.__downloadPermissionCache[apiname])) {
                    return resolve(ajax.__downloadPermissionCache[apiname]);
                }
                if (!apiname || !/__c|obj$/i.test(apiname)) {//不认识的都为true
                    return resolve(true);
                }

                if (!ajax._fetchDownloadPermission) {
                    ajax._fetchDownloadPermission = _.debounce(() => {
                        let config = ajax._fetchDownloadPermissionConfig;
                        CRM.util.FHHApi({
                            url: '/EM1HNCRM/API/v1/object/userprivilege/service/batchGetObjectFunctionsByUser',
                            data: {
                                actionCodes: ['PictureAnnexDownload'],
                                apiNames: _.union(_.pluck(config, 'apiname'))
                            },
                            success: function (res) {
                                res.Value && _.each(res.Value.objectFunction, (a, objectApiName) => {
                                    ajax.__downloadPermissionCache[objectApiName] = !!a.PictureAnnexDownload;
                                })
                                _.each(config, a => a.resolve(ajax.__downloadPermissionCache[a.apiname]));
                                config = null;
                            },
                            error: function () {
                                _.each(config, a => a.resolve(ajax.__downloadPermissionCache[a.apiname]));
                                config = null;
                            }
                        }, {
                            errorAlertModel: 1
                        });

                        ajax._fetchDownloadPermissionConfig = null;
                    }, 50)
                }
                (ajax._fetchDownloadPermissionConfig || (ajax._fetchDownloadPermissionConfig = [])).push({ apiname, resolve })
                ajax._fetchDownloadPermission();
            })
        },

        // 校验客户、日期 和 价目表是否匹配；
        validPriceBookByCustomer: function (param) {
            let url = `/EM1HNCRM/API/v1/object/pricebook/service/validate_account_pricebook`;
            return this.ajax_base(url, _.extend({
                price_book_id: '',
                account_id: '',
                partner_id: '',
                object_data: {} // 主对象数据
            }, param))
        },

        createJob(data) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HJobCenter/inputJobCenter/createJob',
                    data: data,
                    // data: {
                    //     "apiFullName": '',
                    //     "apiName": '',
                    //     "queryParam": {
                    //     },
                    //     "jobType": 3,
                    //     "templateId": "crm_004"
                    // },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            if (res.Value.code == -1) {
                                reject(res.Value.message);
                            } else {
                                resolve(res.Value);
                            }
                            return;
                        }
                        reject(res.Result.FailureMessage || $t("服务器返回错误!"));
                    },
                    error: function (res) {
                        reject((res.Result && res.Result.FailureMessage) || $t("服务器返回错误!"));
                    },
                }, {
                    errorAlertModel: 1
                });
            })
        },

        queryJob(data) {
            var me = this;
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: "/EM1HJobCenter/inputJobCenter/queryJobState",
                    data: data,
                    success: function (reg) {
                        if (reg.Result.StatusCode == 0) {
                            if (reg.Value.errorMessage) {
                                reject(reg.Value.errorMessage);
                            } else {
                                resolve(reg.Value);
                            }
                        } else {
                            reject(reg.Result.FailureMessage || $t("服务器返回错误!"));
                        }
                    },
                    error: function (reg) {
                        reject((reg.Result && reg.Result.FailureMessage) || $t("服务器返回错误!"));
                    },
                }, {
                    errorAlertModel: 1
                });
            })
        },

        queryJobStatus: function (data, ajaxObj, tips) {
            return new Promise((resolve, reject) => {
                var _tips = tips || $t("编辑数据需要较长时间，完成后发通知");
                var _count = 0;
                query();
                function query() {
                    _count++;
                    if (_count > 4) {
                        resolve(_tips);
                        return;
                    }
                    ajaxObj.query = CRM.util.queryJob({
                        jobId: data.id,
                    })
                        .then(function (res) {
                            if (res.status === 2) {
                                resolve();
                            } else {
                                setTimeout(query, 2000);
                            }
                        }, (msg) => {
                            reject(msg);
                        })
                        .finally(function () {
                            ajaxObj.query = null;
                        });
                }
            })
        },

        // 对象A的从对象数据映射到对象B的从对象
        mapMdData: function (param) {
            let url = `/EM1HNCRM/API/v1/object/data_mapping/service/mapping`;
            return this.ajax_base(url, _.extend({
                sourceApiName: '', // 来源对象A
                targetApiName: '', // 被映射的对象B
                detailDataMap: []  // 从对象数据
            }, param))
        },

        getFieldRights(data, noCache) {
            const me = this;
            return new Promise((resolve, reject) => {
                let cacheRights = CRM.get('fieldRights') || {};
                if (!noCache) {
                    let rights = me._getFieldRights(data, cacheRights);
                    if (!_.isBoolean(rights)) {
                        resolve(rights);
                        return;
                    }
                }
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/userprivilege/service/batchGetObjectFieldByUser',
                    data: {
                        apiNames: data,
                    },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            let _rights = me._setFieldRights(res.Value.objectFieldPermissions, cacheRights);
                            resolve(_rights);
                            return;
                        }
                        util.alert(res.Result.FailureMessage || $t("服务器返回错误!"));
                        reject(res.Result.FailureMessage || $t("服务器返回错误!"));
                    },
                    error: function (res) {
                        util.alert((res.Result && res.Result.FailureMessage) || $t("服务器返回错误!"));
                        reject((res.Result && res.Result.FailureMessage) || $t("服务器返回错误!"));
                    },
                }, {
                    errorAlertModel: 1,
                })
            })
        },

        _getFieldRights(data, rights) {
            let _rights = {};
            let flag = true;
            _.each(data, (name) => {
                let _r = _.findWhere(rights, { apiName: name });
                if (_r) {
                    _rights[name] = _r
                } else {
                    flag = false;
                }
            })
            return flag ? _rights : flag;
        },

        _setFieldRights(data, rights) {
            _.each(data, (item) => {
                !rights[item.apiName] && (rights[item.apiName] = item);
            });
            CRM.set('fieldRights', rights);
            return rights;
        },

        // 对象A的从对象数据映射到对象B的从对象
        getToolsPower: function (param) {
            let url = `/EM1HNCRM/API/v1/object/userprivilege/service/batchGetObjectFunctionsByUser`;
            return this.ajax_base(url, _.extend({
                "apiNames": [
                    ""
                ],
                "actionCodes": [
                    "List"
                ]
            }, param))
        },

        getRecycleRule(data) {
            return new Promise((resolve, reject) => {
                util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/pool_service/service/get_recycling_rule_list',
                    data: data,
                    // data: {
                    //     api_name: "HighSeasObj",
                    //     data_id: id
                    // },
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            let ruleList = res.Value.recyclingRuleList;
                            if (ruleList.length > 0) {
                                ruleList = ruleList.sort(function (a, b) {
                                    return a.priority - b.priority;
                                });
                            }
                            resolve(ruleList);
                            return;
                        }
                        util.alert(res.Result.FailureMessage);
                        reject(res.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        // 查询产品在哪些价目表中；
        getPriceBookByProductId: function (param) {
            let url = `/EM1HNCRM/API/v1/object/product_find_data/service/include_product_pricebook`;
            return this.ajax_base(url, _.extend({
                "productIds": [""]
            }, param))
        },

        // 查询产品在哪些价目表中；
        getMultiPriceList: function (param) {
            let url = `/EM1HNCRM/API/v1/object/mutipleUnit/service/getProductMultiPriceList`;
            return this.ajax_base(url, _.extend({
                "productIdList": [""]
            }, param))
        },

        getCRMTableConfigs: function () {
            if (CRM._cache && CRM._cache.CRM_TABLE_CONFIG) return;

            CRM.util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/personal_config/service/find',
                data: {
                    key: 'crm.table.config'
                },
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        try {
                            CRM._cache.CRM_TABLE_CONFIG = res.Value.value ? JSON.parse(res.Value.value) : {};
                        } catch (e) { console.log(e) }
                    }
                }
            }, {
                errorAlertModel: 1
            })
        },

        getObjectDetail(data) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: `/EM1HNCRM/API/v1/object/${data.objectDescribeApiName}/controller/WebDetail`,
                    data: _.extend({
                        fromRecycleBin: false,
                        layoutVersion: "V3",
                        management: false,
                        // objectDataId: "6140548d2cfe000001a6dc77",
                        // objectDescribeApiName: "SalesOrderObj",
                        serializeEmpty: false,
                    }, data),
                    success: function (res) {
                        if (res.Result.StatusCode === 0) {
                            resolve(res.Value);
                            return;
                        }
                        reject(res.Result.FailureMessage);
                    },
                    error() {
                        reject($t("暂时无法获取相关数据请稍后重试"));
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        // 查多单位产品的单位
        getProductUnitOptions: function (param) {
            let url = `/EM1HNCRM/API/v1/object/lazyLoadOptions/service/getLazyLoadOptions`;
            return this.ajax_base(url, _.extend({
                "dataId": '', //数据ID
                "dataObjApiName": "ProductObj", //数据对象(产品对象apiname）
                "targetObjApiName": "UnitInfoObj", //目标对象(单位对象apiname）
                "sourceObjApiName": '', //源对象(订单产品,XXX明细等)
                "extend_info": "" //预留字段用于做特殊逻辑判断，暂时无用
            }, param))
        },

        // 查多单位产品的单位
        getProductUnitOptionsBatch: function (param) {
            let url = `/EM1HNCRM/API/v1/object/lazyLoadOptions/service/batchGetLazyLoadOptions`;
            return this.ajax_base(url, _.extend({
                "dataIds": '', //数据ID
                "dataObjApiName": "ProductObj", //数据对象(产品对象apiname）
                "targetObjApiName": "UnitInfoObj", //目标对象(单位对象apiname）
                "sourceObjApiName": '', //源对象(订单产品,XXX明细等)
                "extend_info": "" //预留字段用于做特殊逻辑判断，暂时无用
            }, param))
        },

        // 查多单位产品的单位对应价格
        getProductPriceByUnit: function (param) {
            let url = `/EM1HNCRM/API/v1/object/mutipleUnit/service/getConversionPrice`;
            return this.ajax_base(url, _.extend({
                "productId": '', // 产品id
                "unitId": "",	 // 单位
            }, param))
        },

        // 查多单位产品的单位信息
        getProductMultiInfo: function (param) {
            let url = `/EM1HNCRM/API/v1/object/product_find_data/service/get_product_multi_info`;
            return this.ajax_base(url, _.extend({
                "productIds": [], // 产品id
            }, param))
        },

        // 获取对象按钮信息
        // 默认存缓存数据里
        getButtonInfo(data, unCache) {
            let url = `/EM1HNCRM/API/v1/object/${data.describeApiName}/controller/ButtonLayout`;
            let key = `${data.describeApiName}_${data.buttonApiName}`;
            if (!unCache && CRM.get(key)) {
                return Promise.resolve({
                    button: CRM.get(key),
                })
            }
            delete data.describeApiName;
            return this.ajax_base(url, data, function (res) {
                !unCache && CRM.set(key, res.button);
            }, true);
        },

        //拉取crm所有对象
        fetchAllObject(callback) {
            if (CRM.get('crm_all_objectdescribeList')) {
                return callback(CRM.get('crm_all_objectdescribeList'));
            }

            (this._fetchDescribeListCallBack || (this._fetchDescribeListCallBack = [])).push(callback);
            if (this._fetchDescribeListAjax) return;
            this._fetchDescribeListAjax = this.ajax_base('/EM1HNCRM/API/v1/object/describe/service/findDescribeList', {
                isIncludeFieldDescribe: false,
                isIncluceSystemObj: true,
                isIncludeUnActived: true,
                packagename: 'CRM'
            }, null, true).done(rst => {
                let list;
                if (rst) {
                    list = _.map(rst.objectDescribeList, a => ({ api_name: a.api_name, display_name: a.display_name }))
                    CRM.set('crm_all_objectdescribeList', list);
                }
                _.each(this._fetchDescribeListCallBack, callback => callback(list));
                this._fetchDescribeListAjax = this._fetchDescribeListCallBack = null;
            })
        },

        getRelatedObject(param, noLoading = true) {
            let url = `/EM1HNCRM/API/v1/object/describe/service/findRelatedObjectList`;
            return this.ajax_base(url, _.extend({
                describeApiName: '',
                includeDetailList: true,
                includeRefList: true,
                excludeInvalid: true,
            }, param), null, noLoading);
        },
        // 获取对象下rfm规则的数量限制
        checkRFMRulesLimit(apiname, noLoading = true) {
            let url = '/EM1HNCRM/API/v1/object/rfm_service/service/checkRuleIsCreateLimit';
            let param = apiname ? { objApiName: apiname } : {};
            return this.ajax_base(url, param, null, noLoading);
        },

        getRiskToken(param = {}) {
            let url = '/EM1HNCRM/API/v1/object/risk_brain_service/service/getToken';
            return this.ajax_base(url, param, null, true);
        },

        /**
         * 获取主属性显示值属性
         * @param {*} apiName ；主对象apiname
         * @param {*} fieldName :字段，不传表示获取主对象配置
         * @param {*} fieldsAttr ：如果已经获取过描述，可以传入避免再次调用接口获取
         */
        async getPrimeAttrDisplay(apiName, fieldName, fieldsAttr = {}, defAttr) {
            const { objectConfig, fieldConfig } = CRM.get('displayNameConfig') || { objectConfig: {}, fieldConfig: {} };
            let displayConfig = fieldName ? fieldConfig[fieldName] : objectConfig[apiName];

            if (fieldName) {
                if (!fieldConfig.hasOwnProperty(fieldName)) {
                    if (!fieldsAttr[fieldName]) {
                        const objectsMap = await this.getFieldsByDescribe(apiName),
                            objectKeys = Object.keys(objectsMap);

                        for (const key of objectKeys) {
                            const fields = objectsMap[key];
                            if (fields[fieldName]) {
                                fieldsAttr = fields;
                                break;
                            }
                        }
                    }
                    displayConfig = fieldsAttr[fieldName].is_open_display_name;
                    fieldConfig[fieldName] = displayConfig;
                }
            } else {
                if (!objectConfig.hasOwnProperty(apiName)) {
                    const describe = await this.getDescribeLayout({
                        "apiname": apiName,
                        "include_layout": false,
                        "include_detail_describe": false
                    });
                    displayConfig = describe.objectDescribe.is_open_display_name;
                    objectConfig[apiName] = displayConfig;
                }
            }
            CRM.set("displayNameConfig", {
                objectConfig, fieldConfig
            });
            return displayConfig ? "display_name" : (defAttr || "name");
        },
        getBqueryRights(apiname) {
            const me = this;
            const basicAuth = CRM.util.isGrayScale('CRM_BQUERY_BASICAUTH');
            let bqueryRights = {
                basicRights: true,
            }
            return new Promise((resolve) => {
                let param = {};
                // mock调试
                // resolve({
                //     dengbaishiRights: false,
                //     tianyanchaRights: false,
                //     qichachaRights: false,
                //     basicRights: false,
                // });
                // return;
                param[apiname] = ['DnbCommercialInforQuery', 'EyeCommercialInforQuery', 'QichachaCommercialInforQuery'];
                // 灰度内支持普通工商权限设置
                basicAuth && param[apiname].push('BasicCommercialInforQuery');
                CRM.util.getActionRights(param, true).then((rights) => {
                    bqueryRights.dengbaishiRights = rights[apiname].DnbCommercialInforQuery;
                    bqueryRights.tianyanchaRights = rights[apiname].EyeCommercialInforQuery;
                    bqueryRights.qichachaRights = rights[apiname].QichachaCommercialInforQuery;
                    if (_.isUndefined(rights[apiname].BasicCommercialInforQuery)) {
                        bqueryRights.basicRights = true;
                    } else {
                        bqueryRights.basicRights = rights[apiname].BasicCommercialInforQuery;
                    }
                    resolve(bqueryRights);
                }, () => {
                    bqueryRights.basicRights = true;
                    bqueryRights.dengbaishiRights = false;
                    bqueryRights.tianyanchaRights = false;
                    bqueryRights.qichachaRights = false;
                    resolve(bqueryRights);
                })
            })
        },
        /**
         * 获取国家省市区对应的label数据
         * @param {Array} values
         * @returns {Array}
         */
        areaCode2Label(values) {
            return new Promise(resolve => {
                if(!values.length) return resolve();
                values = values.slice(0);
                let index = values.length;
                let result = [];
                let _inner = () => {
                    let code = values.shift();
                    if (code && CRM.get('areaCache') && CRM.get('areaCache')[code]) {
                        --index ? _inner() : afterFetch(result);
                        return;
                    }
                    code && CRM.util.fetchAreaParent(code).then(parentList => {
                        parentList && parentList.length && result.push({
                            label: parentList.pop().label,
                            value: code,
                            path: parentList
                        })
                        --index ? _inner() : afterFetch(result);
                    })
                }
                _inner();_inner();_inner();

                function afterFetch(result) {
                    CRM.util.setAreaCache(result);
                    resolve(result);
                }
            })
        },
        /**
         * 获取国家省市区对应的数据及父级数据
         * @param {String} value
         * @returns {Array}
         */
        fetchAreaParent(value) {
            return new Promise(resolve => {
                if(CRM.get('areaParentCache') && CRM.get('areaParentCache')[value]) return resolve(CRM.get('areaParentCache')[value].slice(0));

                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/global_data/service/get_parent_zone',
                    data: {value},
                    success(res) {
                        let pl = res.Value && res.Value.parentList;
                        if(pl) {
                            let types = ['国家', '省', '市', '区', '乡镇'];  //[ignore-i18n]
                            pl = _.sortBy(pl, a => _.indexOf(types, a.type));
                            let _cache = CRM.get('areaParentCache') || {};
                            _cache[value] = pl;
                            CRM.set('areaParentCache', _cache);
                        }
                        resolve(pl && pl.slice(0));
                    },
                    error() {
                        resolve()
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        fetchRoleList(apiName) {
            return new Promise((resolve) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/describe/service/roleList',
                    data: {
                        describeApiName: apiName
                    },
                    success(response) {
                        if (response.Result.StatusCode == 0) {
                            const dataList = response.Value?.roleList;
                            const roleList = dataList?.map((role) => ({
                                status: role.status,
                                label: role.role_name,
                                value: role.role_type,
                            }));
                            resolve(roleList);
                        } else {
                            resolve(null);
                        }
                    },
                    error() {
                        resolve(null);
                    }
                }, {errorAlertModel: 1});
            });
        },

        supportCustomRole(apiName) {
            return new Promise((resolve) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/describe/service/supportCustomTeamRole',
                    data: {
                        describeApiName: apiName
                    },
                    success(response) {
                        if (response.Result.StatusCode == 0) {
                            resolve(response.Value.success === true);
                        } else {
                            resolve(false);
                        }
                    },
                    error() {
                        resolve(false);
                    }
                }, {errorAlertModel: 1});
            });
        },

        // 获取团队角色
        fetchTeamRoleList(apiName) {
            const enableStatus = 1;
            const principalType = '1'; // 负责人
            return ajax.fetchRoleList(apiName).then((roleList) => {
                roleList = roleList?.filter((role) => {
                    return (role.status === enableStatus) && (
                        role.value !== principalType
                    );
                });

                return roleList;
            });
        },
        formulaCheck(param = {expression: ''}) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/bom/service/expression_check',
                    data: param,
                    success(res) {
                        if (res.Result.StatusCode === 0) {
                            // if (res.Value.pass)
                            resolve(res.Value);
                            return;
                        }
                        reject(res.Result.FailureMessage);
                    },
                    error(res) {
                        reject(res.Result && res.Result.FailureMessage);
                    }
                }, {errorAlertModel: 1});
            });
        },

        // 
        /**
         * 获取apl列表
         * {
         *      pageNumber: 1,
         *      pageSize: 500,
         *      is_include_used: true,
         *      binding_object_api_name: 'PartnerObj', // 绑定对象
         *      name_space: ['channel_sign'], // 范围规则
         *      return_type: null, // 返回值类型
         * },
         */
        fetchAplList(param) {
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HFUNC/biz/query',
                    data: {
                        pageNumber: 1,
                        pageSize: 500,
                        is_include_used: true,
                        ...param,
                    },
                    success: (res) => {
                        if (res.Result.StatusCode == 0 && res.Value) {
                            resolve(res.Value?.function || [])
                        } else {
                            CRM.util.alert(res?.Value?.msg || res.Result.FailureMessage);
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
            });
        },

        /**
         * 获取对象布局的自定义插件列表
         * @param {string} apiname - 对象API名称
         * @returns {Promise<Array>} 返回插件列表数组，如果获取失败则返回空数组
         */
        fetchLayoutPlugins(apiname, options = {}) {
            const pluginType = options?.type;

            if (!apiname || !pluginType) {
                return Promise.resolve([]);
            }

            return new Promise((resolve) => {
				CRM.util.FHHApi({
					url: `/EM1HNCRM/API/v1/object/${apiname}/controller/ListLayout`,
					data: {
						describeApiName: apiname,
						includeDescribe: false
					},
					success: function(res) {
                        try {
                            if (res.Result?.StatusCode === 0 && res.Value) {
                                const plugins = res.Value.layout?.layout_structure?.plugins?.layout || [];
                                const pluginList = plugins.filter((plugin) => {
                                    if (plugin.client_type !== 'web') {
                                        return false;
                                    }
                                    return (plugin.type || 'list_plugin') === pluginType;
                                });
                                resolve(pluginList);
                            } else {
                                resolve([]);
                            }
                        } catch (error) {
                            resolve([]);
                        }
					},
					error: function() {
						resolve([]);
					}
				}, {
                    errorAlertModel: 1
                });
			});
        }
    }

    module.exports = ajax;
});
