// 项目资源
define(function (require, exports, module) {
    var floatCalculate = require('crm-modules/common/stock/floatCalculate').floatCalculate;
    let List = require('crm-modules/page/list_tree/list_tree');
    let SelectBox = require('vue-selector-box');
    let Resourceview = require('./resourceview/resourceview');
    let ScrollBar = require("base-modules/ui/scrollbar/scrollbar");
    let TaskView = require('./taskmodeview/taskmodeview');
    var util = CRM.util;
    let api = require('./util');
    let ResourceList = List.extend({
        my_events: {
            'click .crm-pr-addmember': 'addMember',
            'click .crm-pr-fullscreen': 'toggleFullScreen',
            'click .crm-pr-legend': 'toggleLegend',
            'click .dialog-resource-delete': 'closeDialog',
            'click .tag-box': 'initSelector',
            'click .merber-more-btn': 'addMore',
            'click .crm-pr-search': 'handleSearch',
            'click .crm-set-top': '_scrollTopFun'
        },
        initialize() {
            //tab筛选参数
            this.selectType = "personal";
            this.dtmain_minWidth = 324;  //左侧表格的最小宽度
            this.isShow = false; //是否显示加载更多
            this.partOffset = 0; //分页数
            this.storageDeptData = []; //累计分页
            //工时
            this.totalHours = 0; //统计工时
            this.participants_sum_hours = 0; //计划工时
            this.participants_sum_actual_hours = 0; //实际工时
            this.participants_threshold_hours = 0; //满勤
            this.isAccumulate = false; //工时是否累加
            //数字语义化
            this.PROJECT_TYPE = 64; //项目类型
            this.STAGE_TYPE = 256;  //阶段类型
            this.TASK_TYPE = 128;   //任务类型
            this.OWNER_TYPE = 32;   //负责人类型
            //项目对象传入的参数信息
            this.projectTask = this.options.projecttaskobj?.options;
            this.resource = this.options.projecttaskobj?.resource;
            this.project_id = this.projectTask ? this.projectTask.data["_id"] : '';
            this.project_name = this.projectTask ? this.projectTask.data["name"] : '';
            this.selectDimension = this.resource ? this.resource.selectDimension : 'personal_mode'; //维度
            this.selectWorking = this.resource ? this.resource.selectWorking : 'planed_resource'; //工时
            List.prototype.initialize.apply(this, arguments);
        },
        render(param) {
            if (param[2]) {
                this.from_task_edit = true;
                let _param = param[2].split('|');
                this.searchOwner = _param;
            }
            //埋点
            CRM.util.sendLog('ProjectResourceObj', 'list', {
              operationId: 'renderResource',
              eventType: 'cl',
            });
            List.prototype.render.apply(this, arguments);
        },
        renderTable() {
            const _this = this;
            this.fetchColunms(null, function() {
                require.async('crm-widget/treetable/treetable', function (Table) {
                    _this.resourceTable = new Table(_.extend({
                        isMyObject: true,
                        noAllowedWrap: true,
                        newStyle: true,
                    },_this.getOptions()));
                    _this.resourceTable.on('renderListComplete', function (init) {
                        _this.renderListCompleteHandle(init);
                    });
                    _this.resourceTable.getMainWrapper = function() {
                        return _this.$('.dt-resource-main-box').find('.dt-main');
                    };
                    _this.resourceTable.getTermBatchWrapper = function() {
                        return _this.$('.dt-resource-main-box').find('.dt-term-batch');
                    };
                    _this.resourceTable.fixedHeaderFn = function () {
                        Table.prototype.fixedHeader.apply(this, arguments);
                    };
                    _this.resourceTable._countWidth = function(width) {
                        let opts = this.options;
                        let w = Table.prototype._countWidth.apply(this, arguments);
                        opts.lastThWidth = width;
                        opts.tableWidth = width;
                        return opts.tableWidth;
                    };
                    _this.resourceTable.on('dropDown.click', _.bind(_this.dropDownclickHandle, _this, _this.resourceTable), _this);
                    _this.resourceTable.on('completeRender', _.bind(_this.completeRender, _this, _this.resourceTable), _this);
                    _this.resourceTable.on('trclick', function(trData, $tr, $target){
                        _this.trclickHandle(trData, $tr, $target);
                    });
                    _this._proxyTableFn(_this.resourceTable);
                });
            });
        },
        parseProxyFn(fns) {
            fns = _.filter(fns, item => !_.contains(['fixedTermBatch', 'fixedPageFooter', 'fixedHeader'], item));
            return fns;
        },
        //获取列配置
        fetchColunms: function(param, cb, context) {
            const _this = this;
            return util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/ProjectTaskObj/controller/ListHeader',
                data: _.extend({
                    include_layout: true,
                    apiname: 'ProjectTaskObj',
                    layout_type: "list"
                },param),
                success:function(res) {
                    if (res.Result.StatusCode === 0) {
                        _this.fetchColunmsSuccess(res, cb, context);
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("暂时无法获取相关数据请稍后重试"));
                }
            }, {
                errorAlertModel: 1
            })
        },
        getOptions() {
            const me = this;
            let options = List.prototype.getOptions.apply(this, arguments);
            this.no_dept_text = $t('proresource_no_memeber');
            this.no_member_text = $t('您的视图没有人员，请添加人员');
            options = _.extend(options, {
                custom_className: `crm-pr-table ${this.options?.isDialog ? 'crm-pr-dialog-table' : 'resource-table'}`,
                height: 'auto',
                searchTerm: null,
                termBatchPos: 'C',
                showMultiple: false,
                showFilerBtn: false,
                showSize: false,
                showPage: false,
                isOrderBy: false,
                isOrderBy_allColumn: false,
                operate: null,
				batchBtns: [],
                otherBtns: [],
                supportMultiFieldSort: false,
                hideIconSet: true,  //设置按钮
                noSupportLock: true,
                search: null,
                scrollLoad: false,
                newStyle: true,
                stepDistance: 25,
                isListLayout: false,
                title: this.projectTask ? '' : $t('sfa.projectResource.display_name'),
                noDataTip: `<p>${me.no_member_text}</p><div class="crm-pr-addmember crm-btn crm-btn-primary ${me.selectType !== 'personal' ? 'hide' : ''}">+${$t('添加员工')}</div>`,
                formatData: function (res) {
                    //拼接表格数据
                    let {lists, total} = me._formatData(res);
                    me.isShow = (me.selectType == "department" && total > lists.length) ? true : false;
                    return {
						totalCount: total,
						data: lists
					}
                },
                onTrHoverHandle:(index,e) => {
                    const $tr = $(e.currentTarget);
                    const trId = $tr.attr('data-id');
                    if(this.ganttview){
                        this.ganttview.add_grid_row_class(
							"gantt-row-hover",
							index,
							trId
						);
                    }
                },
                refreshCallBack: function() {
                    //刷新按钮
                    me.clearData();
                    me.changeView();
                }
            })
            return options;
        },
        changeView() {
            if(this.selectDimension === 'personal_mode'){
                this.clearData();
                this.$('.projecttask-box').hide();
                this.$('.dt-resource-main-box').show();
                let params = this.resourceTable.getParam();
                params = this.resourceTable.options.paramFormat && this.resourceTable.options.paramFormat(params);
                return this.refresh(params);
            }
            this.initTaskResource();
        },
        _formatData(res) {
            let {extendInfo, dataList, total} = res;
            const data = this.storageDeptData.concat(dataList);
            const lists = this.handleData(extendInfo, data);
            this.selectType === 'personal' && (this.memberDatas = lists || []);
            const work_hours = extendInfo.participants_sum_working_hours || 0;
            this.totalHours = this.participants_sum_hours = this.isAccumulate ? this.totalHours + work_hours : work_hours;
            this.participants_sum_actual_hours = this.isAccumulate ? (this.participants_sum_actual_hours + extendInfo.participants_sum_actual_working_hours) : extendInfo.participants_sum_actual_working_hours;
            this.participants_threshold_hours = this.isAccumulate ? (this.participants_threshold_hours + extendInfo.sum_participants_working_hours_threshold) : extendInfo.sum_participants_working_hours_threshold;
            const {result, hours} = this.computedWorks();
            this.$('.total-hours').find('i').html(this.totalHours);
            $('.plan-title').html(result);
            $('.plan-commute').html(hours);
            this.resourceTable.options.noDataTip = `<p>${this.no_member_text}</p><div class="crm-pr-addmember crm-btn crm-btn-primary ${this.selectType !== 'personal' ? 'hide' : ''}">+${$t('添加员工')}</div>`
            return {lists, total}
        },
        parseData(obj) {
            let dataObj = List.prototype.parseData.apply(this, arguments);
            if(this.selectType === 'project' && this.selectDimension === 'task_mode') return dataObj;
            const lists = this.handleData(obj.extendInfo, dataObj.data);
            dataObj.data = lists;
            return dataObj;
        },
        //格式化数据
        handleData(extendInfo, lists) {
            delete this.from_task_edit;
            this.working_hours_threshold = extendInfo && extendInfo.working_hours_threshold;
            _.each(lists, (item) => {
                item.groupbycount && (item.children = []);
                if (item.owner && item.owner.length) {
                    let _r = _.findWhere(extendInfo.employee, {id: item.owner[0]}) || FS.contacts.getEmployeeById(item.owner[0]);
                    let owner__r = {
                        id: _r.id,
                        name: _r.name,
                        picAddr: _r.picAddr || _r.profileImagePath,
                    }
                    item.owner__r = owner__r ? [owner__r] : [];
                }
            });
            return lists;
        },
        handleProTaskData(extendInfo, data, total) {
            delete this.from_task_edit;
            this.working_hours_threshold = extendInfo && extendInfo.working_hours_threshold;
            const dataLists = data.map(item => ({
                ...item,
                ...({sum_actual_planed_working_hours_cost : `${item.sum_actual_working_hours_cost}/${item.sum_planed_working_hours_cost}`}),
                ...({actual_planed_working_hours : `${item.actual_working_hours}/${item.allocated_working_hours}`}),
            }));
            const no_stage_tasks = dataLists.filter(item => !item.stage_id);
            let pro_tasks = dataLists.filter(item => item.stage_id);
            pro_tasks = CRM.util.parseDataToTree(pro_tasks.map(item => ({
                ...item,
                ...(item.stage_id && {rowId: item._id}),
                ...(item.task_no && {pid: item.stage_id}),
            })));
            const lists = no_stage_tasks.concat(pro_tasks);
            return {lists, total}
        },
        parseParam(obj) {
            let param = List.prototype.parseParam.apply(this, arguments);
            let timeRange = this.timeRange || this.getCurDateRange();
            const {PROJECT_TYPE, STAGE_TYPE, TASK_TYPE, OWNER_TYPE} = this;
            param.handler_parameter = {
                "working_time_start": timeRange[0], // 甘特图数据的起始时间
                "working_time_end": timeRange[1], // 甘特图数据的截止时间
                "row_type": OWNER_TYPE, // 甘特图行标识
                "column_type": 14, // 甘特图列标识
                "resource_type": this.selectWorking //工时类型
            }
            param.from_task_edit = this.from_task_edit;
            if (this.selectType === 'personal' && this.searchOwner) {
                param.handler_parameter.owner = this.searchOwner;
                param.handler_parameter.row_type = OWNER_TYPE;
            }

            //弹框-资源视图参数
            if(this.options?.isDialog) {
                param.from_task_add = true;
                param.handler_parameter.project_id = this.options.projectId; //当前任务所属项目ID
            }
            
            if (this.curRowData) {
                if (this.curRowData.row_type == OWNER_TYPE) {
                    param.handler_parameter.owner = this.curRowData.owner;
                    param.handler_parameter.row_type = PROJECT_TYPE;
                } else if(this.curRowData.row_type == PROJECT_TYPE) {
                    param.handler_parameter.project_id = this.curRowData.project_id;
                    param.handler_parameter.owner = this.curRowData.task_owner;
                    param.handler_parameter.row_type = this.options?.isDialog ? TASK_TYPE : STAGE_TYPE;
                } else if(this.curRowData.row_type == STAGE_TYPE) {
                    param.handler_parameter.stage_id = this.curRowData.stage_id;
                    param.handler_parameter.owner = this.curRowData.stage_owner;
                    param.handler_parameter.row_type = TASK_TYPE;
                }
            }

            if(!this.options?.isDialog && this.selectType !== 'personal') {
                param.handler_parameter.participants_type = this.selectType;
                param.handler_parameter.participants_offset = this.partOffset;
                this.selectType === 'department' && (param.handler_parameter.department_ids = this.selectIds);
                this.selectType === 'project' && (param.handler_parameter.project_id = this.project_id) && (param.handler_parameter.view_mode = this.selectDimension);
                this.curRowData?.row_type === STAGE_TYPE && delete param.handler_parameter.project_id;
            }

            if(this.projectTask) {
                param.handler_parameter.participants_offset = this.partOffset;
                this.curRowData?.row_type !== STAGE_TYPE && (param.handler_parameter.project_id = this.projectTask.data["_id"]); //当前任务所属项目ID
                param.handler_parameter.view_mode = this.selectDimension;
                param.handler_parameter.participants_type = "project";
            }
            (this.selectType === 'project' || this.projectTask) && this.selectDimension === 'task_mode' && (param.handler_parameter.row_type = TASK_TYPE);

            return param;
        },
        getColumns() {
            const _this = this;
            const {PROJECT_TYPE, STAGE_TYPE, TASK_TYPE, OWNER_TYPE, dtmain_minWidth} = this;
            const isDialog = this.options?.isDialog || false;
            let columns = this.columns = util.deepClone(List.prototype.getColumns.apply(this, arguments));
            const projectTitleHtml = `<div class="crm-pr-thwrapper project"><span>${$t('员工')}</span><span>${$t('work_hour.total')}</span></div>`;
            _.each(columns, column => {
                if(column.api_name === 'owner') {
                    column.title = '';
                    column.width = dtmain_minWidth;
                    column.titleHtml = this.projectTask ? projectTitleHtml : `<div class="crm-pr-thwrapper ${isDialog ? 'gray' : ''}"><div class="crm-pr-searchwrapper"></div><div class="crm-pr-addmember ${isDialog ? 'hide' : ''}"><img src="${FS.CRM_MODULE.ASSETS_PATH}/images/resource/icon-member-add.svg"/></span></div></div>`;
                    column.render = function(data, type, fulldata) {
                        let content = '';
                        data = fulldata.row_type == OWNER_TYPE ? data : fulldata.working_time_aggregate_label;
                        const {percent} = _this.computedWorks(fulldata);
                        const percent_working_hours = percent.split('/') || [];
                        let data_id = fulldata.row_type == PROJECT_TYPE ? fulldata.project_id : fulldata.row_type == STAGE_TYPE ? fulldata.stage_id : fulldata._id;
                        const {owner_img, owner_arrow} = _this.updateOwnerTag(fulldata);
                        if(isDialog) {
                            content = `<div class="hours-total" data-allocated="${fulldata.allocated_working_hours}" data-unallocated="${fulldata.unallocated_working_hours}"></div>`
                        } else {
                            content = `<div class="hours-total"><span class="${Number(percent_working_hours[0]) > Number(percent_working_hours[1]) ? 'over' : ''}">${percent_working_hours[0]}</span>/<span>${percent_working_hours[1]}</span></div>`;
                        }
                        let html = fulldata.row_type == OWNER_TYPE ?  content: '';
                        return  `${isDialog ? owner_arrow : ''}${owner_img}<div class="crm-pr-tdtext-wrapper" data-type="${fulldata.row_type}" data-owner="${fulldata.p_owner}"><span class="${fulldata.row_type == OWNER_TYPE ? '' : fulldata.row_type !== TASK_TYPE ? 'pro-ellipsis' : 'task-ellipsis'} crm-pr-tdtext ${fulldata.row_type === STAGE_TYPE ? 'stage' : ''}" data-id="${data_id}" title="${_.escape(data)}">${ _.escape(data)}</span>${html}</div>`;
                    }
                }else {
                    column.isHidden = true;
                }
            });
            return columns;
        },
        updateOwnerTag(fulldata, owner, name) {
            const isDialog = this.options?.isDialog || false;
            const {PROJECT_TYPE, STAGE_TYPE, OWNER_TYPE} = this;
            let img_path = '', owner_img = '', owner_tag = '', owner_arrow = '';
             if (fulldata.row_type == OWNER_TYPE || owner) {
                img_path = util.getAvatarLink(fulldata.owner__r[0] ? fulldata.owner__r[0].picAddr : '', 2);
                owner_img = `<img class="crm-pr-avatar" src="${img_path}"/>`;
                owner_arrow = `<span class="radio-wrapper ${!fulldata.groupbycount ? 'count' : ''} ${this.options.selectQwner?.toString() === fulldata.owner.toString() ? 'active': ''}"></span>`;
            }
            if (fulldata.row_type == PROJECT_TYPE) {
                owner_tag = $t('项目');
                img_path = `${FS.CRM_MODULE.ASSETS_PATH}/images/resource/file.svg`;
                owner_img = isDialog ? `<img class="crm-pr-project" src="${img_path}"/>` : `<span class="crm-pr-project">${/^[\u4e00-\u9fa5]$/.test(owner_tag) ? owner_tag.charAt(0) : owner_tag.charAt(0).toUpperCase()}</span>`;
            }
            if(fulldata.row_type === STAGE_TYPE || (name && fulldata.stage_no)) {
                owner_tag = $t('阶段');
                owner_img =  `<span class="crm-pr-project stage">${/^[\u4e00-\u9fa5]$/.test(owner_tag) ? owner_tag.charAt(0) : owner_tag.charAt(0).toUpperCase()}</span>`;
            };
            return {owner_img, owner_arrow}
        },
        renderListCompleteHandle(init) {
            const _this = this;
            CRM.util.hideLoading_tip(null, 'resource-loading');
            if (init) {
                this.addScreenLegendCloseBtn();
                this.initMemberSearch('personal', 'init');
                this.initSelectMember();
                this.initSelectWorking();
                this.initSelectDimension();
                this.initTimeSelector();
                this.setAssignHour();
                this.initRightLayout();
                this.recordWorking();
                this.resourceTable.on("resize", () => {
                    if(_this.selectDimension === 'task_mode') return;
                    //列表替换icon
                    _this.addReplaceRadio();
                    _this.addCellRadio();
                });
            }
            this.updateResource();
            api.setMemberTextWidth(this);
            //列表替换icon
            this.addReplaceRadio();
            //列添加单选icon
            this.addCellRadio();
        },
        completeRender() {
            this.setAssignHour();
            this.initRightLayout();
        },
        recordWorking() {
            if(this.options?.isDialog) return;
            const {result, hours} = this.computedWorks();
            this.$('.dt-main .main-con').before(`<div class="crm-pr-tdplace"><span class="plan-title">${result}</span><span class="plan-commute">${hours}</span></div>`);
        },
        computedWorks(data={}) {
            let result,hours,percent,sum_working_hours_label,working_hours_label,sum_working_hours_key, working_hours_key;
            const {participants_sum_hours, participants_sum_actual_hours, participants_threshold_hours} = this;
            switch (this.selectWorking) {
            case 'planed_resource':
                result = `${$t('计划')}/${$t('working_perfect.attendance')}`;
                sum_working_hours_key = 'sum_planed_working_hours_cost';
                working_hours_key = 'allocated_working_hours';
                sum_working_hours_label = $t('planed_working_hours_cost');
                working_hours_label = $t(working_hours_key);
                hours = `<i class="${participants_sum_hours > participants_threshold_hours ? 'over' : ''}">${participants_sum_hours}</i>/${participants_threshold_hours}`;
                percent = `${data.allocated_working_hours}/${floatCalculate.addCalc(data.allocated_working_hours,data.unallocated_working_hours)}`;
                break;
            case 'actual_resource':
                result = `${$t('实际')}/${$t('working_perfect.attendance')}`;
                sum_working_hours_key = 'sum_actual_working_hours_cost';
                working_hours_key = 'actual_working_hours';
                sum_working_hours_label = $t('actual_working_hours_cost');
                working_hours_label = $t(working_hours_key);
                hours = `<i class="${participants_sum_actual_hours > participants_threshold_hours ? 'over' : ''}">${participants_sum_actual_hours}</i>/${participants_threshold_hours}`;
                percent = `${data.sum_actual_working_hours || 0}/${floatCalculate.addCalc(data.allocated_working_hours,data.unallocated_working_hours)}`;
                break;
            default:
                result = `${$t('实际')}/${$t('计划')}`;
                sum_working_hours_key = 'sum_actual_planed_working_hours_cost';
                working_hours_key = 'actual_planed_working_hours';
                sum_working_hours_label = `${$t('实际')}/${$t('planed_working_hours_cost')}`;
                working_hours_label = `${$t('实际')}/${$t('allocated_working_hours')}`;
                hours = `<i class="${participants_sum_actual_hours > participants_sum_hours ? 'over' : '' }">${participants_sum_actual_hours}</i>/${participants_sum_hours}`;
                percent = `${data.sum_actual_working_hours || 0}/${data.allocated_working_hours}`;
            };
            return {result, hours, percent,sum_working_hours_label,working_hours_label,sum_working_hours_key, working_hours_key};
        },
        initMemberSearch(dept, init) {
            const isdept = dept === 'personal' ? true : false;
            let searchedMember = this.searchOwner || [];
            let _html = `<div class="crm-pr-search">
                <div class="crm-pr-search-clear"><i class="el-input__icon el-icon-circle-close el-input__clear"></i></div>
                <div class="crm-pr-search-content">
                    <a class="tag-item el-link crm-pr-search-add ${this.options?.isDialog ? 'gray' : ''}">${isdept ? $t('proresource_select_member') :'+ ' + $t('选择部门')}</a>
                </div>
            </div>`;
            this.searchHmtl = `<a class="tag-item el-link crm-pr-search-add">${this.selectType == 'personal' ? $t('proresource_select_member') : $t('选择部门')}</a>`;
            this.$('.crm-pr-searchwrapper').html(_html);
            isdept && this.$('.crm-pr-addmember').show();
            if(!init && this.selectType === 'personal' && this.memberDatas?.length && !this.searchOwner) {
                let hourLen = 0;
                this.resourceTable.doStaticData(this.memberDatas);
                this.memberDatas.map(m => m.allocated_working_hours).forEach(item => {
                    hourLen += Number(item);
                });
                $('.total-hours').find('i').html(hourLen);
                !this.options?.isDialog && this.recordWorks(this.memberDatas);
                return;
            }
            //缓存部门数据
            if(this.selectType === 'department' && this.searchDeptMember?.length) {
                this.updateHtml([{
                    id: this.selectIds[0],
                    name: this.selectName
                }], this.searchHmtl, true);
                this.doSearch(isdept, this.selectIds);
                return;
            }

            if (this.selectType === 'personal' && searchedMember.length) {
                this.searchOwner && this.updateHtml([{
                    id: searchedMember[0],
                    name: this.selectMemberName
                }], this.searchHmtl, true);
                // this.updateHtml(FS.contacts.getEmployeesByIds(searchedMember));
                this.doSearch(isdept, searchedMember);
                return;
            };
            return this.refresh();
        },
        //汇总工时/满勤
        recordWorks(data = []) {
            let plan_hours = 0,actual_hours = 0,participants_threshold_hours = 0;
            data.forEach(item => {
                plan_hours += this.selectWorking !== 'actual_resource' && Number(item.allocated_working_hours || 0);
                actual_hours += this.selectWorking !== 'planed_resource' && Number(item.sum_actual_working_hours || 0);
                participants_threshold_hours += (floatCalculate.addCalc(item.allocated_working_hours, item.unallocated_working_hours));
            });
            this.participants_sum_hours = plan_hours;
            this.participants_sum_actual_hours = actual_hours;
            this.participants_threshold_hours = participants_threshold_hours;
            const {result, hours} = this.computedWorks();
            $('.plan-title').html(result);
            $('.plan-commute').html(hours);
        },
        handleSearch(e) {
            const _this = this;
            const isdept = this.selectType === 'personal' ? true : false;
            let $tg = $(e.target);
            if ($tg.hasClass("crm-pr-search-clear") || $tg.closest('.crm-pr-search-clear').length) {
                doClear();
            } else {
                isdept ? update() : updateDept();
            }
            function update() {
                _this.initMemberSelectBox(_this.searchOwner, (selectBox) => {
                    let val = selectBox.getSelectedItems();
                    let ids = selectBox.getValue();
                    let members = val.member;
                    _this.updateHtml(members, _this.searchHmtl);
                    _this.selectMemberName = members.length ? `${members[0].name}-${members.length}` : '';
                    _this.doSearch(isdept, ids.member);
                    selectBox.hide();
                }, {
                    title: $t('proresource_select_member'),
                });
            }

            function updateDept() {
                _this.initGroupSelectBox(_this.searchDeptMember, (selectBox) => {
                    let val = selectBox.getSelectedItems();
                    let ids = selectBox.getValue();
                    let groups = val.group;
                    _this.updateHtml(groups, _this.searchHmtl);
                    _this.selectName = groups.length ? `${groups[0].name}-${groups.length}` : '';
                    _this.doSearch(isdept, ids.group);
                    selectBox.hide();
                }, {
                    title: $t('选择部门'),
                });
            }
            function doClear() {
                _this.$('.crm-pr-search-content').html(_this.searchHmtl);
                _this.clearData();
                _this.doSearch(isdept, []);
            }
        },
        doSearch(isdept, data) {
            !isdept && (this.selectIds = data);
            data = _.map(data, item => item + '');
            isdept && (this.searchOwner = data.length ? data : '');
            !isdept && (this.searchDeptMember = data.length ? data : '');
            this.isAccumulate = false;
            this.refresh();
        },
        //输入框模板
        updateHtml(members, searchHmtl, isEcho) {
            let htmls = [];
            members.length = isEcho ? members[0]['name'].split('-')[1] : members.length;
            let name = members.length ? isEcho ? members[0]['name'].split('-')[0] : members[0].name : '';
            if (members[0]) {
                htmls.push(`<span cid="member-${members[0].id}" class="tag-item">
                    <span class="el-tag el-tag--info el-tag--small el-tag--light" title="${members[0].name}">
                        <span class="el-select__tags-text">
                            <i class="selected-icon selected-icon-circle"></i>
                            ${name}
                        </span>
                    </span>
                </span>`);
            }
            if (members.length - 1 > 0) {
                htmls.push(`<span class="tag-item tag-more el-tag el-tag--info el-tag--small el-tag--light">
                    <span class="el-select__tags-text">+ ${(members.length - 1)}</span>
                </span>`)
            }
            htmls.push(searchHmtl);
            // this.$('.crm-pr-dept-add').html(htmls.join(''));
            this.$('.crm-pr-search-content').html(htmls.join(''));
            
        },
        //部门/项目/人员筛选
        initSelectMember() {
            if(this.options?.isDialog || this.projectTask) return;
            const _this = this;
            this.$('.dt-term-batch').before('<div class="crm-pr-selectMember"></div>');
            _this.selectWork = FxUI.create({
                wrapper: this.$('.crm-pr-selectMember')[0],
                template: `<fx-select
                    placeholder="${$t('请选择')}"
                    v-model="selectvalue"
                    :options="options"
                    size="mini"
                    default-first-option
                    @change="changeSlect"
                ></fx-select>`,
                data() {
                    return {
                        selectvalue: 'personal',
                        opts: [],
                        options: [
                            {
                                value: 'department',
                                label: $t('view.department.staff')
                            },
                            {
                                value: 'project',
                                label: $t('view.project.members'),
                            },
                            {
                                value: 'personal',
                                label: $t('view.assigned.personnel')
                            },
                        ]
                    }
                },
                methods: {
                    changeSlect(value) {
                        _this.selectType = value;
                        _this.clearData();
                        _this.$('.projecttask-box').hide();
                        _this.$('.dt-resource-main-box').show();
                        if(value == 'project') {
                            _this.initSelector();
                            _this.initSelectDimension();
                            return;
                        }else {
                            _this.selectDimension = 'personal_mode';
                        }
                        _this.initMemberSearch(value);
                    },
                }
            });
        },
        clearData() {
            this.partOffset = 0;
            this.totalHours = 0;
            this.isShow = false; //隐藏加载更多
            this.participants_sum_hours = 0; //计划工时
            this.participants_sum_actual_hours = 0; //实际工时
            this.participants_threshold_hours = 0; //满勤
            this.storageDeptData = [];
            this.resourceTable.doStaticData([]); //清空表格
            this.$('.total-hours').find('i').html(this.totalHours);
            $('.plan-commute').html(this.totalHours);
            if(this.selectType !== 'project' && !this.projectTask) {
                this.$('.dimension-wrapper')?.length && this.$('.dimension-wrapper').remove();
            }
            if(this.selectType !== 'personal') return this.$('.crm-pr-addmember').hide();
        },
        initSelector(value) {
            const _this = this;
            if(!value) {
                this.$('.crm-pr-searchwrapper').html(`<div class="tag-box"><span class="tag-item add-project">${this.project_id ? this.project_name : $t('请选择')}</span><span class="fx-icon-arrow-down circle-icon"></span></div>`);
                this.project_id && this.refresh();
                return;
            }
            CRM.api.pick_data({
                apiName: 'ProjectObj',
                title: $t('选择数据'),
                single: true,
                data: this.project_id,
                methods: {
                    select: (result => {
                        projectData = result.selected || {};
                        _this.project_id = projectData._id;
                        _this.project_name = projectData.name;
                        _this.totalHours = 0;
                        _this.$('.crm-pr-searchwrapper').find('.add-project').html(projectData.name);
                        _this.$('.crm-pr-searchwrapper').find('.add-project').attr('title', projectData.name || '');
                        _this.changeView();
                    })
                }
            });
        },

        addMember() {
            //埋点
            CRM.util.sendLog(this.options.apiname, 'list', {
              operationId: 'addOwner',
              eventType: 'cl',
            });
            this.getResourceMember().then((member) => {
                this.initMemberSelectBox(member, _.bind(this.doAddMember, this));
            })
        },
        doAddMember(selectBox) {
            let len = selectBox.getSelectedLength();
            if (len > 50) {
                CRM.util.remind(3, $t('最多可选择50名人员，请重新选择'));
                return;
            }
            let val = selectBox.getValue();
            CRM.util.waiting()
            this.saveResourceMember(val.member).then(() => {
                CRM.util.waiting(false);
                selectBox.hide();
                this.refresh();
            });
        },
        initMemberSelectBox(addedMember, selectedCallBack, options) {
            const me = this;
            let selectBox = this.widgets.memberSelectbox = new SelectBox(_.extend({
                member: true,
                title:  $t('添加员工'),
                defaultSelectedItems: {
                    member: addedMember
                },
            }, options))
            selectBox.on('selected', () => {
                selectedCallBack && selectedCallBack(selectBox);
            })
        },
        initGroupSelectBox(groups, selectedCallBack, options) {
            const me = this;
            let selectDeptBox = this.widgets.groupSelectbox = new SelectBox(_.extend({
                group: { company: true },
                single: false,
                title: $t('选择部门'),
                defaultSelectedItems: {
                    group: groups
                },
            }, options))
            selectDeptBox.on('selected', () => {
                me.storageDeptData = [];
                me.partOffset = 0;
                selectedCallBack && selectedCallBack(selectDeptBox);
            })
        },
        getCurDateRange() {
            let myDate = new Date();
            let year = myDate.getFullYear();
            let month = myDate.getMonth();
            let startDay = new Date(year, month, 1);
            let endDay = new Date(year, month + 1, 0, 23, 59, 59);
            return [startDay.getTime(), endDay.getTime()];
        },
        //维度、计划/实际工时
        initSelectDimension() {
            const _this = this;
            if(this.options?.isDialog || (this.selectType !== 'project' && !this.projectTask)) return;
            this.$('.working-wrapper').before('<div class="dimension-wrapper"></div></div>');
            FxUI.create({
                wrapper: this.$('.dimension-wrapper')[0],
                template: `<fx-select
					v-model="selected"
					size="mini"
					:options="options"
                    @change="changeSlect"
				></fx-select>`,
				data() {
					return {
						selected: this.selectDimension || 'personal_mode',
						options: [
                            {
								value: 'personal_mode',
								label: $t('personal_mode'),
							},
							{
								value: 'task_mode',
								label: $t('task_mode'),
							}
						]
					}
				},
                methods: {
                    changeSlect(value) {
                        _this.clearData();
                        _this.selectDimension = value;
                        _this.changeView();
                    }
                }
            });
        },

        //任务维度
        initTaskResource() {
            const _this = this;
            if(!this.project_name) return;
            this.$('.projecttask-box') && this.$('.projecttask-box').remove();
            this.$('.dt-resource-main-box').after('<div class="projecttask-box"></div>');
            this.$('.dt-resource-main-box').hide();
            this.taskobj = new TaskView({
                apiname: "ProjectResourceObj",
                wrapper: _this.$el.find('.projecttask-box'),
				autoHeight: true,
                resourceObj: _this
            });
            this.taskobj.render(["ProjectResourceObj"]);
        },
        initSelectWorking() {
            const _this = this;
            if(this.options?.isDialog) return;
            const html = `<div class="crm-pr-work-wrapper ${this.projectTask ? 'projecttask' : ''}"><div class="working-wrapper"></div></div>`;
            if(this.options.projecttaskobj) {
                $(".resource-box .dt-term-batch").html(html);
                $(".resource-box .dt-term-batch").css({'margin': 0, 'display': 'flex'});
                $(".resource-box .dt-caption.no-tit").height(40);
            }else {
                this.$('.crm-pr-selectMember').after(html);
            }
            
            FxUI.create({
                wrapper: this.$(".working-wrapper")[0],
                template: `<fx-select
					v-model="selected"
					size="mini"
					:options="options"
                    @change="changeSelect"
				></fx-select>`,
				data() {
					return {
						selected: this.selectWorking || 'planed_resource',
						options: [
							{
								value: 'planed_resource',
								label: $t('allocated_working_hours'),
							},
							{
								value: 'actual_resource',
								label: $t('actual_working_hours'),
							},
							{
								value: 'planed_and_actual',
								label: `${$t('实际')}/${$t('allocated_working_hours')}`
							}
						]
					}
				},
                methods: {
                    changeSelect(value) {
                        _this.selectWorking = value;
                        _this.clearData();
                        _this.changeView();
                    }
                }
            });
        },
        initTimeSelector() {
            const me = this;
            const wrapper = this.options?.isDialog ? $(".crm-resource-dialog .last-target-item") : this.$('.crm-pr-work-wrapper');
            wrapper.after(`<div class="crm-pr-timeselector  ${this.projectTask ? 'projecttask' : ''}"></div>`);
            let range = this.timeRange = this.getCurDateRange();
            FxUI.create({
                wrapper: this.$('.crm-pr-timeselector')[0],
                template: `<fx-date-picker
                    v-model="value"
                    type="daterange"
                    size="mini"
                    value-format="timestamp"
                    format="yyyy-MM-dd"
                    :clearable="false"
                    :default-time="defaultTime"
                    :range-separator="$t('至')"
                    :start-placeholder="$t('开始日期')"
                    :end-placeholder="$t('结束日期')"
                    :picker-options="pickerOptions"
                    :useLocaleFormat="true"
                    @change="changeHandler">
                </fx-date-picker>`,
                data() {
                    return {
                        defaultTime: ['00:00:00', '23:59:59'],
                        value: range,
                        choiceDate: '',
                        pickerOptions: {
                            onPick({maxDate, minDate}) {
                                this.choiceDate = minDate.getTime();
                                // 如何你选择了两个日期了，就把那个变量置空
                                // if (maxDate) this.choiceDate = '';
                            },
                            disabledDate(time) {
                                // if (!this.choiceDate) return false;
                                // 31天的时间戳
                                const one = 31 * 24 * 3600 * 1000;
                                // 当前日期 - one = 31天之前
                                const minTime = this.choiceDate - one;
                                // 当前日期 + one = 31天之后
                                const maxTime = this.choiceDate + one;
                                let flag = time.getTime() < minTime || time.getTime() > maxTime;
                                if (flag) {
                                    this.$message({
                                        message: $t('最多选择31天'),
                                        type: 'warning',
                                    });
                                }
                                return flag;
                            }
                        }
                    }
                },
                methods: {
                    changeHandler(val) {
                        me.clearData();
                        me.timeRange = val;
                        me.changeView();
                    },
                    getValue() {
                        return this.value;
                    }
                }
            })
        },
        addScreenLegendCloseBtn() {
            if(this.options?.isDialog) {
                this.$('.dt-term-batch').append(`<div class="dialog-resource-delete el-icon-close"></div>`);
                return;
            }
            //全屏/图例按钮
            this.$('.last-target-item').after(`<div class="crm-pr-fullscreen fx-icon-fullscreen crm-ui-title" data-pos="top" data-title="${$t('全屏显示')}"></div>`);
            this.$('.last-target-item').after(`<div class="crm-pr-legend crm-ui-title" data-pos="top" data-title="${$t('图例')}"></div>`);
        },
        toggleFullScreen(){
            let $screen = this.$('.crm-pr-fullscreen');
            $screen.toggleClass('fx-icon-fullscreen');
            $screen.toggleClass('fx-icon-fullscreen-exit');
            if ($screen.hasClass('fx-icon-fullscreen') && this.fulldialog) {
                $screen.attr('data-title', $t('全屏显示'));
                this.$parent.append(this.$fullEl);
                this.fulldialog.destroy && this.fulldialog.destroy();
                this.fulldialog = null;
                this.isFull = false;
            } else {
                $screen.attr('data-title', $t('退出全屏'));
                this.$fullEl = this.$el.closest('.uipaas-running-layout-wrapper');
                this.$parent = $(this.$el.closest('.uipaas-running-layout-wrapper')).parent();
                this.fulldialog = CRM.util.fullDialog({
                    el: this.$fullEl,
                    className: "crm-pr-fullscreenlayer crm-d-layout"
                });
                this.isFull = true;
            }
            this.resourceTable.resize();
            this.addResourceScroll(true);
            this.options?.isDialog && this.addDialogScroll();
        },
        //切换图例
        toggleLegend () {
            const _this = this;
            const $resourceBox = this.isFull ? $(".crm-pr-fullscreenlayer.crm-d-layout") : this.$('.crm-pr-table');
            $resourceBox.toggleClass('dt-resource-legend');
            if ($resourceBox.hasClass('dt-resource-legend')) {
                $resourceBox.append('<div class="dt-legend"></div>');
                FxUI.create({
                    wrapper: $('.dt-legend')[0],
                    template: `<div class="le-wrapper">
                    <h3 class="le-title">${$t('图例')}</h3>
                    <div class="content">
                        <div class="imgs">
                            <span class="le-th">${$t('图形')}</span>
                            <ol v-for="i in 4" :key="i">
                                <li :class="'le-img le-img-'+i"></li>
                            </ol>
                        </div>
                        <div class="desc"><span class="le-th">${$t('描述')}</span><span>${$t('人员、项目汇总工时')}</span><span>${$t('任务工时')}</span></div>
                        <div class="intro">
                            <span class="le-th">${$t('表示含义')}</span>
                            <ul>
                                <li>${$t('人员和项目资源视图说明')}</li>
                                <li>${$t('资源视图绿色含义')}</li>
                                <li>${$t('资源视图红色含义')}</li>
                            </ul>
                        </div>
                    </div>
                    </div>`,
                })
            } else {
                $(".dt-legend").length && $resourceBox.children('.dt-legend').remove();
            };
            $(document).on("click", function (e) {
                if($(e.target).hasClass('crm-pr-legend') || _this.projectTask) return;
                const leEl = $resourceBox.find(".dt-legend");
                $resourceBox.removeClass('dt-resource-legend');
                leEl.length && leEl.remove();
            });
        },
        dropDownclickHandle(e, rowData, curData, isUnfold) {
            const me = this;
            // 保存当前滚动位置
            me._lastScrollLeft = this.$('.resource-gantt-container').scrollLeft();
            me._isExpandCollapse = true; // 添加展开/收起标记
            
            this.getChildrenData(rowData, curData, isUnfold).then(() => {
                me.updateResource();
                api.setMemberTextWidth(me);
                me._toggleFixedHeader(true);
                // 恢复滚动位置
                setTimeout(() => {
                    me._isExpandCollapse = false;
                }, 100);
            });
        },
        getChildrenData(rowData, curData, isUnfold) {
            if (rowData.children.length || !isUnfold) return Promise.resolve();
            const me = this;
            this.curRowData = rowData;
            return new Promise((resolve) => {
                this.fetchData().then((res) => {
                    this.curRowData = '';
                    CRM.util.addRowId(res.data);
                    _.each(res.data, (item) => {
                        item.pid = rowData.rowId;
                        item.isShow = true;
                        item.p_owner = item.row_type === me.PROJECT_TYPE ? item.task_owner : item.row_type === me.STAGE_TYPE ? item.stage_owner  : item.owner;
                    })
                    rowData.children = res.data;
                    me.addRow();
                    resolve();
                })
            })
        },
        fetchData() {
            const me = this;
            let apiName = me.get('apiname');
            let params = this.resourceTable.getParam();
            params = this.resourceTable.options.paramFormat && this.resourceTable.options.paramFormat(params);
            // me.resourceTable.showLoading();
            CRM.util.waiting();
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/' + apiName + '/controller/List',
                    data: params,
                    success(res) {
                        if (res.Result.StatusCode == 0) {
                            let data = me.parseData(res.Value);
                            resolve(data);
                            return data;
                        }
                        let msg = res.Result.FailureMessage || $t('暂时无法获取相关数据请稍后重试');
                        CRM.util.alert(msg);
                    },
                    error() {},
                    complete() {
                        // me.resourceTable.hideLoading();
                        CRM.util.waiting(false);
                    }
                })
            })
        },
        getResourceMember() {
            return new Promise((resolve) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/project_resource/service/queryProjectResourceConfig',
                    success(res) {
                        if (res.Result.StatusCode == 0) {
                            let data = res.Value.data.participants;
                            resolve(data);
                            return data;
                        }
                        let msg = res.Result.FailureMessage || $t('暂时无法获取相关数据请稍后重试');
                        CRM.util.alert(msg);
                    },
                })
            })
        },
        saveResourceMember(data) {
            data = _.map(data, item => item + '');
            return new Promise((resolve) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/project_resource/service/saveProjectResourceConfig',
                    data: {
                        participants: data,
                    },
                    success(res) {
                        if (res.Result.StatusCode == 0) {
                            resolve();
                            return;
                        }
                        let msg = res.Result.FailureMessage || $t('暂时无法获取相关数据请稍后重试');
                        CRM.util.alert(msg);
                    },
                })
            })
        },
        trclickHandle (data, $tr, $target) {
            console.log(data, $tr, $target);
            if(this.options?.isDialog) return this.dialogClickHandle(data, $tr, $target);
            const stage_tr = $target.hasClass("crm-pr-tdtext stage");
            const task_tr = $target.hasClass("task-ellipsis crm-pr-tdtext");
            const project_tr = $target.hasClass("pro-ellipsis crm-pr-tdtext");
            const object_apiname = data.object_describe_api_name;
            const _id = $target.data("id");
            if(stage_tr || task_tr || project_tr) {
                CRM.api.show_crm_detail({
                    type: object_apiname,
                    data: {
                        crmId: _id,
                    }
                });
            };
        },
        dialogClickHandle(data, $tr, $target) {
           let allRadios = this.$el.find('.radio-wrapper');
           if($target.hasClass("radio-wrapper")) {
               allRadios.removeClass('active');
               $target.toggleClass('active');
           };
           this.options.selectQwner = data.owner;  //存储选中相关团队成员
        },
        layoutScroll: _.debounce(function(e) {
            this._toggleFixedHeader();
            this._showTop(e);
            List.prototype.layoutScroll.apply(this, arguments);
        }, 60),
        _toggleFixedHeader(isResize) {
            const resourceGantt = this.selectDimension === 'task_mode' ? this.$('.taskGantt') : this.$('.resourceGantt');
            const table = this.selectDimension === 'task_mode' ? this.taskobj.table : this.resourceTable;
            if(this.options?.isDialog) return;
            var mainWrapper = table.getMainWrapper();
            var offset = mainWrapper.offset();
            let rec = mainWrapper[0].getBoundingClientRect();
            let dRec = document.body.getBoundingClientRect();
            var $h = this.isFull ? 0 : (window.Fx && window.Fx.theme == 'new' ? 41 : 56); //为主站header的高度
            if(offset.top < $h - 1) {
                if(!this.__fixedHeader) {
                    mainWrapper.prepend(this.__fixedHeader = $('<div style="height:62px"></div>'));
                    table.fixedHeaderFn({
                        top: $h,
                        left: offset.left,
                        right: dRec.right - rec.right,
                        zIndex: 600
                    })
                }
                if (isResize) {
                    table.fixedHeaderFn({
                        top: $h,
                        left: offset.left,
                        right: dRec.right - rec.right,
                        zIndex: 600
                    })
                }
                //资源视图header吸顶
                this.createSVGFix(isResize);
            } else if(this.__fixedHeader && this.__fixedHeader.offset().top > $h - 2) {
                let wh = $(window).height();
                let $resourceGantt = this.$('.resource-gantt-container');
                let $trs = this.$(".main.main-tr-handle").find('.tr');
                let $page = $($trs[$trs.length-1]);
                let $fackScroll = resourceGantt.find('.fack-scroll');
                table.fixedHeaderFn();
                this.__fixedHeader.remove();
                this.__fixedHeader = null;
                this.$('.resource-fixed').length && $resourceGantt.find('.resource-fixed').remove();
                $resourceGantt.find('.date').css('display', 'block');
                $resourceGantt.find('.grid-resource-header').css('display', 'block');
                if($page.length && ($page.offset().top + 38 > wh)){
                    $fackScroll.length && $fackScroll.css({'position':'fixed','left':resourceGantt.offset().left});
                }else{
                    $fackScroll.length && $fackScroll.css({'position':'absolute','left':0});
                }
            }
            this.addResourceScroll(isResize);
        },
        //显示置顶
        _showTop(e) {
            const distance = e.target.clientHeight + e.target.scrollTop >= e.target.scrollHeight - 5;
            if(distance) {
                //滚动到底部
                !this.$('.top-icon').length && this.$el.append('<span class="top-icon crm-set-top"></span>');
            }else {
                this.$('.top-icon')?.length && this.$('.top-icon').remove();
            }
        },
        //资源视图头部固定
        createSVGFix (isResize) {
            const dtmain_w = this.$('.dt-main').width();
            const main_box = this.selectDimension === 'task_mode' ? this.$('.dt-task-main-box') : this.$('.dt-resource-main-box');
            let $resourceGantt = main_box.find('.resource-gantt-container');
            isResize && $resourceGantt.find('.project-resource-gantt').css({ left: 0});//重置
            let gan_left = main_box.find('.resource-gantt-container').offset().left;
            let reource_fix_left = main_box.find('.fack-scroll').length && main_box.find('.fack-scroll').scrollLeft() || 0;
            let gan_top = this.isFull ? 0 : (window.Fx && window.Fx.theme == 'new' ? 41 : 56); //新老主站header高度
            let date = $resourceGantt.find('.date').clone().html();
            let header_w = $resourceGantt.find('.project-resource-gantt').width();
            this.$('.resource-fixed').length && this.$('.resource-fixed').remove();
            const html = `<div class="resource-fixed" style="position:fixed;left:${gan_left}px;top:${gan_top}px;height:62px;width:${$('.resourceGantt').width()}px;overflow-x:hidden;"><svg class="project-resource-gantt" height="62" width="${header_w}" style="position: absolute;display:block;left:${-reource_fix_left}px"><g class="grid"><rect x="0" y="0" width="${header_w}" height="62" class="grid-resource-header ${this.options?.isDialog ? 'gray' : ''}"></rect><g class="date">${date}</g></g></svg></div>`;
            $resourceGantt.find('.date').css('display', 'none');
            $resourceGantt.find('.grid-resource-header').css('display', 'none');
            $resourceGantt.append(html);

            //弹框-资源视图样式
            if(this.options?.isDialog) {
                this.$('.resource-fixed').css({'overflow-x':'auto','overflow-y':'hidden',left: `${dtmain_w+24+1}px`, top: '66px'});
                this.$('.resource-fixed::-webkit-scrollbar').css('display', 'none');
            }
        },
        //资源视图布局初始化
        initRightLayout () {
            this.set_dtmain_layout();
            this.setTableWidthDrag();

            this.setResourceH();
        },
        //添加模拟滚动条
        addResourceScroll (isResize) {
            this._destroyScrollDiv();
            let $gantt = $(".dt-resource-main-box .resourceGantt");
            let $page = $($(".main.main-tr-handle").find('tr')[$(".main.main-tr-handle").find('tr').length-1]);
            let mw = $gantt.width();
            let wh = $(window).height();
            let fw = $(".project-resource-gantt").width();
            let $mainScroll = $gantt.find(".resource-gantt-container");
			if (this.options?.isDialog || (!$page.length || ($page.length && ($page.offset().top + 38 < wh)))) {
                this.$('.resource-gantt-container').css('overflow-x', 'auto');
                return;
			}
            if(isResize && $('.scroll-x.scroll-scrollx_visible').length){
                $('.scroll-x.scroll-scrollx_visible').hide();
            }
            if (mw == fw) {
				this._destroyScrollDiv();
				return;
			}
            this.setScrollEl('gantt', $gantt, mw, fw, $mainScroll);
        },
        //弹框-资源视图纵向滚动
        addDialogScroll() {
            const _this = this;
            const $main = $('.crm-resource-dialog .dt-resource-main-box');
            const $dtmain_w = $main.find('.dt-main').width();
            const $scrollEl = $main.find('.fack-scroll');
            $scrollEl.css('left', $dtmain_w);
            $main.append($scrollEl);
            $main.on("scroll", function(e){
                const _scrollTop = e.target.scrollTop || $main.scrollTop();
                if(_scrollTop > 50) {
                    //添加吸顶
                    _this._toggleDialogFixedHeader();
                }else {
                    //清除吸顶
                    _this.resourceTable.fixedHeaderFn();
                    $main.find('.main-fixed').remove();
                    $main.find('.resource-fixed').remove();
                    $main.find('.date').css('display', 'block');
                    $main.find('.grid-resource-header').css('display', 'block');
                }
            })
        },
        //弹框-资源视图头部吸顶
        _toggleDialogFixedHeader() {
            const mainWrapper = this.resourceTable.getMainWrapper();
            const resourceWrapper = mainWrapper.parent().find('.resource-fixed');
            //吸顶处理
            !mainWrapper.find('.main-fixed').length && mainWrapper.prepend(this.__fixedHeader = $('<div style="height:62px" class="main-fixed"></div>'));
            this.resourceTable.fixedHeaderFn({
                top: 66,
                left: 24,
                zIndex: 700,
                width: mainWrapper.width()
            });
            !resourceWrapper.length && this.createSVGFix();
        },
        setScrollEl(el,$main,mw,fw,$mainScroll){
            var noTrigger;
            var _this = this;
            var offset = $main.offset();
            var $scollDiv = $(
                `<div class="crm-scroll fack-scroll ${el}" style="position:fixed;left:${offset.left}px;bottom:0px;width:${mw}px;overflow:auto;z-index:100"><div style="height:16px;width:${fw}px"></div></div>`
            );
            $main.append($scollDiv);
            
            // 如果是展开/收起操作，使用保存的滚动位置
            if (this._isExpandCollapse && typeof this._lastScrollLeft !== 'undefined') {
                $scollDiv.scrollLeft(this._lastScrollLeft);
                $mainScroll.scrollLeft(this._lastScrollLeft);
            } else {
                $scollDiv.scrollLeft($mainScroll.scrollLeft());
            }

            // 先解绑之前的事件
            this.$('.resource-gantt-container').off('scroll');
            // 重新绑定scroll事件
            this.$('.resource-gantt-container').on('scroll', function(e) {
                if (_this._isExpandCollapse) return; // 如果是展开/收起操作，不处理滚动
                // 判断是否存在吸顶元素
                var $resourceFixed = _this.$('.resource-fixed');
                if ($resourceFixed.length) {
                    // 通过设置 SVG 元素的 left 值来实现水平滚动效果
                    $resourceFixed.find('.project-resource-gantt').css({ left: -e.target.scrollLeft });
                }
            });

            $scollDiv.on("scroll", function () {
                if (_this._isExpandCollapse) return; // 如果是展开/收起操作，不处理滚动
                if (noTrigger) {
                    noTrigger = null;
                    return;
                }
                $mainScroll.scrollLeft($scollDiv.scrollLeft());
                _this.$(".resource-fixed").length && _this.$(".project-resource-gantt").css({ left: -$scollDiv.scrollLeft() });
            });
            
            var fn = _.debounce(function () {
                if (_this._isExpandCollapse) return; // 如果是展开/收起操作，不处理滚动
                if (
                    Math.abs(
                        $scollDiv.scrollLeft() - $mainScroll.scrollLeft()
                    ) > 100
                ) {
                    noTrigger = true;
                    $scollDiv.scrollLeft($mainScroll.scrollLeft());
                }
            }, 100);
            $mainScroll.on(`scroll.${el}`, fn);

            $scollDiv.$mainScroll = $mainScroll;
            el === 'gantt' && (this._scrollGanttDiv = $scollDiv);
        },
        //销毁滚动条
        _destroyScrollDiv(){
            const main_box = this.selectDimension === 'task_mode' ? this.$('.dt-task-main-box') : this.$('.dt-resource-main-box');
            main_box.find('.fack-scroll').length && main_box.find('.fack-scroll').remove();
            this._scrollGanttDiv && this._scrollGanttDiv.remove();
            this._scrollGanttDiv && this._scrollGanttDiv.$mainScroll.off("scroll.gantt");
            this._scrollGanttDiv = null;

        },
        updateResource(isResize) {
            const _this = this;
            this.setResourceH();
            Resourceview(this.$('.resource-gantt-container'), _this.initGanttTasks(), {
                dates: _this.timeRange,
                projectresourceobj: _this
            }).then(ganttview => {
                _this.ganttview = ganttview;
                _this.addResourceScroll(isResize);
                _this.options?.isDialog && _this.addDialogScroll();
            });
        },
        //设置资源视图高度
        setResourceH(){
            let dtmain = this.isFull ? $(".crm-pr-fullscreenlayer .dt-main") : $(".crm-pr-table .dt-main");
            if ($(".dt-resource-main-box").length) {
                $('.dt-resource-main-box .dt-right').height(dtmain.height());
            }
        },
        set_dtmain_layout () {
            const _this = this;
            let dtmain = this.isFull ? $(".crm-pr-fullscreenlayer .dt-main") : $(".crm-pr-table .dt-main");
            let tbodys = dtmain.find('tbody');
            let theads = dtmain.find('.header thead');
            let lasTr = tbodys.find('tr')[tbodys.find('tr').length-1];
            if (!$(".dt-resource-main-box").length) {
                dtmain.after("<div class='dt-resource-main-box'></div>");
                $(".dt-resource-main-box").append(dtmain).append('<div class="dt-right"><div id="resource-gantt" class="resourceGantt" style="min-width:100%;width: auto;"><div class="resource-gantt-container"></div></div></div>');
            }

            //非弹框资源视图
            if(!this.options?.isDialog) {
                !this.$('.merber-more-btn').length && this.isShow && $(`<p class="merber-more-btn" style="height:38px;line-height:38px;">${$t('加载更多')}<i class="arrow el-icon-arrow-down"></i></p>`).insertAfter(lasTr);
            }
            theads.each((index, value) => {
                const trs = $(value).find('tr');
                trs.each((idx,tr) => {
                    $(tr).css({height:'61px','line-height':'61px'});
                    $(tr).find('.tb-cell').css({height: '61px','line-height':'61px', 'padding-top':0, width: this.dtmain_minWidth + 'px'});
                })
            });
            tbodys.each((index, item) => {
                const trs = $(item).find('.tr');
                trs.each((idx,tr) => {
                    const id = $(tr).data('id');
                    const row_type =$(tr).find('.crm-pr-tdtext-wrapper').data('type');
                    const p_owner =$(tr).find('.crm-pr-tdtext-wrapper').data('owner');
                    const tr_id = row_type !== _this.OWNER_TYPE && row_type !== _this.PROJECT_TYPE && p_owner ? `${id}.${Number(p_owner)}` : id;
                    $(tr).css({height: '38px', 'line-height': '38px'}).attr('data-id', tr_id);
                })
            });
            $('.dt-resource-main-box .dt-right').height(dtmain.height());
            new ScrollBar($(".dt-resource-main-box .dt-right"));
        },
         //设置分配工时
         setAssignHour () {
            if(!this.options?.isDialog) return;
            let wrapper = Array.from($('.hours-total'));
            _.each(wrapper, function (item, index) {
                $(item).html('');
                let allocated = $(item).data('allocated');
                let unallocated = $(item).data('unallocated');
                let percent = unallocated < 0 ? 1 : (allocated/(allocated + unallocated));
                FxUI.create({
                    wrapper: $(item)[0],
                    template:`<fx-popover
                    placement="bottom-end"
                    :visible-arrow="false"
                    :offset="165"
                    ref="assignPover"
                    popper-class="assign-pover"
                    trigger="hover"
                    >
                    <div class="assign-hover-box">
                        <span>${$t('已分配工时')}<i>${allocated}${$t('小时')}</i></span><span>${$t('待分配工时')}<i>${unallocated}${$t('小时')}</i></span>
                    </div>
                    <div slot="reference" class="assign-wrapper">
                        <p class="assign-total"><span class="assign" style="width:${percent*100}%;background:${percent>=1 ? '#FDA795' : '#98D9A4'}"></span></p><p class="assign-text">${allocated}h</p>
                    </div>
                  </fx-popover>`
                })
            });
        },
        //初始化资源视图
        initGanttTasks () {
            const _this = this;
            let resources = [];
            let curData = this.resourceTable.getCurData();
            let data = CRM.util.parseTreeToNormal(curData);
            data = this.addResourceData(data);
            data.forEach((item) => {
                if (item["isShow"]) {
                    if (item._id) {
                        resources.push(_this.setResourceData(item));
                    }
                }
            });
            this._ganttResources = [...resources];
            // console.log(resources);
            return resources;
        },
        //组装资源数据
        addResourceData (data) {
            let list = [];
            const times = this.timeRange;
            const start_date = times[0];
            const end_date = times[1];
            data.forEach(item => {
                list.push(
                    _.extend({}, item, {
                        plan_start_date: start_date,
                        plan_end_date: end_date
                    })
                )
            });
            !this.options?.isDialog && this.selectDimension !== 'task_mode' && list.unshift({
                working_no_time_aggregate: [],
                plan_start_date: start_date,
                plan_end_date: end_date,
                no_time: true,
                isShow: true,
                _id: 'no-data',
                length: util.calculateTimeBetween(start_date, end_date, 'Days'),
            })
            return list;
        },
        //组装多日期资源数据,格式为YYYY-MM-DD格式给组件计算
        setResourceData (item) {
            //工时上限值
            const times = this.timeRange;
            const start_date = FS.moment.unix(times[0] / 1000).format("YYYY-MM-DD");
            const end_date = FS.moment.unix(times[1] / 1000).format("YYYY-MM-DD");
            const working_time_aggregate = (item.working_time_aggregate || []).map((el) => {
                el.start = FS.moment
                    .unix(el.working_time_start / 1000)
                    .format("YYYY-MM-DD");
                el.end = FS.moment
                    .unix(el.working_time_end / 1000)
                    .format("YYYY-MM-DD");
                return el;
            });
            return {
                id: item.row_type !== this.PROJECT_TYPE && item.p_owner ? `${item._id}.${Number(item.p_owner[0])}` : item._id,
                name: item.working_time_aggregate_label,
                type: item.row_type,
                start: start_date,
                end: end_date,
                startDate: times[0],
                endDate: times[1],
                working_hours_threshold: this.working_hours_threshold,
                working_time_aggregate,
                // work_days: []
            }
        },
        //分割线
        setTableWidthDrag () {
            let minWidth = this.dtmain_minWidth + 'px';
            let dtmain = this.isFull ? $(".crm-pr-fullscreenlayer .dt-main") : $(".crm-pr-table .dt-main");
            let tb = dtmain.find('table.tb');
            if (!dtmain.find(".dt-main-drag-line").length) {
                let line = $(
                    '<div class="dt-main-drag-line"></div>'
                );
                dtmain.append(line);
            }
            dtmain.css('width', minWidth);
            tb.width(dtmain.width());
        },
        //列表箭头icon替换
        addReplaceRadio() {
            const trs = this.resourceTable.$el.find('.main tbody tr');
            _.each(trs, function(item) {
                let arrow = $(item).find('.table-dropdown-btn');
                !arrow.hasClass('fx-icon-fold-2') && arrow.addClass('fx-icon-fold-2');
            })
        },
        //列添加单选icon
        addCellRadio () {
            if(!this.options?.isDialog) return;  //非弹框视图表格不添加icon
            const trs = this.resourceTable.$el.find('.main tbody tr[data-level=0]');
            _.each(trs, function(item) {
                let isHasRadio = $(item).find('.radio-wrapper');
                $(item).addClass('tr-radio');
                isHasRadio.hasClass('count') && $(item).addClass('radio-count');
            });
        },
        //关闭弹框
        closeDialog() {
            this.options.wrapperEl.dialog.hide();
        },
        //加载更多
        addMore() {
            this.partOffset += 50;
            this.storageDeptData = this.resourceTable.getCurData();
            this.isAccumulate = true; //是否累加
            let params = this.resourceTable.getParam();
            params = this.resourceTable.options.paramFormat && this.resourceTable.options.paramFormat(params);
            this.resourceTable.setParam({
                handler_parameter: params.handler_parameter
            },true);
        },
        //返回顶部
        _scrollTopFun() {
            const wrapper = this.projectTask ? $('.crm-p-projectobj .uipaas-running-layout-scroll') : $('.crm-p-projectresourceobj .uipaas-running-layout-scroll');
            const $resourceLayout = this.isFull ? $(".crm-pr-fullscreenlayer .uipaas-running-layout-scroll") : wrapper;
            $resourceLayout.animate({scrollTop:0}, 300);
        }
    })
    module.exports = ResourceList;
})