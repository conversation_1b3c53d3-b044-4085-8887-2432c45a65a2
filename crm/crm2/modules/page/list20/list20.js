define(function(require, exports, module) {
	return Backbone.View.extend({
        events: {
            'mouseenter .dt-main': 'mouseenterHandle',
            'mouseleave .dt-main' : 'mouseleaveHandle'
        },
        mouseenterHandle: function(e) {
            this._mainenter = true;
            this._createScrollDiv($(e.currentTarget));
        },

        //创建模拟滚动条
        _createScrollDiv: function($main) {
            if(!this._mainenter) {
                this._destroyScrollDiv();
                return;
            };

            if (this.list.assertViewType('card')) {
                return;
            }

            var wh =  $(window).height();
            var $page = $main.next();
			if (!$page.length) return;

            if($page.offset().top < wh) {
                this._destroyScrollDiv();
                return;
            };

            var mw = $main.width();
            var fw = $main.find('.tb:first').width();
            if(mw == fw) {
                this._destroyScrollDiv();
                return;
            };

            const vacatedSize = this.list.vacatePlacement();
            if (vacatedSize) {
                try {
                    const size = window.parseFloat(vacatedSize);
                    if (!_.isNaN(size)) {
                        mw = mw - size;
                    }
                } catch (error) {
                    console.log(error);
                }
            }

            if(this._scrollDiv) return;

            var offset = $main.offset();
            var left = offset.left + this._getOffsetLeft();
            var $scollDiv = $('<div class="crm-scroll fack-scroll" style="position:fixed;left:' + left +'px;bottom:32px;width:' + mw +'px;overflow:auto;z-index:100"><div style="height:16px;width:' + fw + 'px"></div></div>');
            $main.append($scollDiv);
            var $mainScroll = $main.find('.main-scroll:first');
            var noTrigger;
            $scollDiv.scrollLeft($mainScroll.scrollLeft());
            $scollDiv.on('scroll', function() {
                if(noTrigger) {
                    noTrigger = null;
                    return;
                }
                $mainScroll.scrollLeft($scollDiv.scrollLeft());
            })
            var fn = _.debounce(function() {
                if(Math.abs($scollDiv.scrollLeft() - $mainScroll.scrollLeft()) > 100) {
                    noTrigger = true;
                    $scollDiv.scrollLeft($mainScroll.scrollLeft());
                }
            }, 100);
            $mainScroll.on('scroll.list20', fn);

            $scollDiv.$mainScroll = $mainScroll;
            this._scrollDiv = $scollDiv;
        },
        _destroyScrollDiv: function() {
            if(this._scrollDiv) {
                this._scrollDiv.remove();
                this._scrollDiv.$mainScroll.off('scroll.list20');
                this._scrollDiv = null;
            }
        },
        mouseleaveHandle: function() {
            this._mainenter = null;
            this._destroyScrollDiv();
        },

        initialize: function(opts){
            var me = this;
            me.options = _.extend({}, opts);
            me.setElement(me.getWrapper());
            me.render(me.options.param);
            FS.MEDIATOR.on('crm.aside.resize', me._updateFixLeft, me);
            FS.MEDIATOR.on('crmPageCacheShow', me._forcePaint = _.debounce(function() {
                if (!me.list) return;
                me.requestPaint();
            }, 20));
        },
        getWrapper: function() {
            return this.options.wrapper.html(`</div><div class="crm-module-content1"></div>`);
        },
        switchPage: function(param) {
            this.render(param);
        },
        render: function(param){
            this.options.param = param;
            if (param) {
                _.extend(this.options, {
                    source: 'list',
                    apiname: param[0]
                });
            }

            this.initList();

            this.$el && (this.isMultipleTabs = !!this.$el.closest('.crm-con.crm-multiple-tabs-con').length);
        },
        initPluginService() {
            let apiname = this.options.apiname;
            let pluginParams = this.options.pluginParams || {};

            if(!apiname) {
                return Promise.resolve();
            }

            return new Promise(resolve => {
                try {
                    CRM.api.pluginService.initByInterface({
                        pluginOpts: {
                            appId: `CRM-${CRM.util.getUUIdAsMiniProgram()}`,
                        },
                        formatPluginList(pluginList) {
                            return [].concat(pluginList).concat([
                                {
                                    "pluginApiName": 'PWCPlugin',
                                    "resource": function(){
                                        return window.PAAS.plugin?.libs.get('PWCPlugin');
                                    },
                                    "params": {
                                        _host: 'list',
                                        ...pluginParams
                                    }
                                }
                            ]);
                        },
                        describe: {},
                        apiName: apiname,
                        actionCode: 'List',
                        agentType: 'web',
                        shouldCachePluginList: true,
                        noErrorMessage: true
                    }).then((pluginService) => {
                        this.__pluginService = pluginService;
                        if(pluginService) {
                            const plugins = this.options.plugins?.layout;
                            const listPlugins = plugins?.filter?.((plugin) => {
                                return !plugin.type || plugin.type === 'list_plugin'
                            });
                            pluginService.run('pwc.init.before', {
                                plugins: listPlugins
                            }).then(resolve, resolve);
                        }else {
                            resolve();
                        }
                    }).catch(resolve);
                } catch(e) {
                    resolve();
                }
            });
        },
        initList: function(){
            var me = this,
                _st =  new Date().getTime();

            me.list && me.list.destroy();

			function exec(List) {
                const _pllst = new Date().getTime();
                me.list = new List(_.extend({}, me.options.listOptions || {}, {
                    wrapper: me.$('.crm-module-content1'),
                    isListLayout: true,
                    wrapperContext: me,
                    $context: me.options.$context,
                    filterMode: me.options.mode,
                    list_component: me.options.compInfo && _.omit( me.options.compInfo, ['jsPath', 'layoutApiname', 'layoutId', 'listModule', 'pageSource', 'param', 'plugins', 'apiName', 'disableLazyLoad', 'filterData','recordType', 'source', 'objectApiName', 'edit', 'componentType']),
                    pluginService: me.__pluginService,
                    loggerFn(param) {
                        if(!param) return;
                        try {
                            const paasLayoutLoadTime = _pllst - _st;
                            const netCost = param.listHeaderCostTime + param.getDataCostTime;
                            const domCost = param.mainRenderCostTime + param.dataRenderCostTime;
                            window.logger.performance({
                                eventId: 'crmPageLoadTime',
                                domCost: domCost,
                                netCost: netCost,
                                totalCost: paasLayoutLoadTime + domCost + netCost,
                                apiName: param.apiname,
                                str1: 'beforeCreate',
                                num1: paasLayoutLoadTime,//模块依赖总耗时，包含接口请求 ui渲染 插件初始化
                                num2: param.listHeaderCostTime,//listHeader耗时 表头请求
                                num3: param.getDataCostTime,//list耗时 数据请求耗时
                                num4: param.mainRenderCostTime, //表格主体渲染耗时，此时表格可见，但是还未请求数据，数据未填充
                                num5: param.dataRenderCostTime //表格数据渲染耗时
                            });
                            
                        } catch(e) {}
                    }
                }));
                me.options.listMounted && me.options.listMounted();
                me.list.render(me.options.param);
                var flag;
                if(me.list.on) {
                    me.list.on('term.advance', function () {
                        flag = true;
                    });
                    me.list.on('renderListComplete', function () {
                        flag && me._scrollToListTop();
                        flag = null;
                        me._toggleFixedPage();
                    })
                    me.list.on('checkbox.click term.bactchHide', function() {
                        if(me.__fixedTerm || me.__fixedBatchOperate) {
                            me._toggleFixedBatchOperate();
                            me._toggleFixedTermBatch();
                        }
                    })
                    me.list.on('otherbtn.change', function() {
                        me._cleanFixed();
                    })
                    me.list.on('window.resize height.change', function() {
                        me._correctFixedHeader();
                        me._toggleFixedPage();
                    })
                    me.list.on('tree.width.change', function(width, offsetleft) {
                        me._updateFixLeft(width + offsetleft - 8);
                    })
                }
			}

            this.initPluginService().then(() => {
                if (me.options.listModule) {
                    exec(me.options.listModule);
                } else {
                    require.async(me.options.jsPath, function (List) {
                        exec(me.extendComponent(List));
                    })
                }
            });
        },
        extendComponent(List) {
            return List;
        },

        _scrollToListTop: function() {
            var me = this;
			if (!me.__fixedHeader || !me.list.getCaptionWrapper) return;
            var $scroll = me.$el.closest('.uipaas-running-layout-scroll');
            if($scroll.length) {
                $scroll.scrollTop(0);
                $scroll.scrollTop(me.list.getCaptionWrapper().offset().top - 76);
            }
        },

        scrollHandle: function(e) {
            var me = this;

            if (!me._executor) {
                me._executor = function() {
                    if(!me.list || !me.list.fixedHeader) return;
                    if(me.list.el && me.list.el.clientHeight === 0) return;
                    me._toggleFixedTermBatch();
                    var obj = me.list.getCurData();
                    // 兼容object_treetable的getCueData返回的是数组数据
                    if(!obj || (_.isArray(obj) && !obj.length) || (obj.data && !obj.data.length)) {
                        // me.__fixedHeader && (me.__fixedHeader.remove(), me.__fixedHeader = null, me.list.fixedHeader());
                        me._cleanFixedHeader();
                        return;
                    };

                    me._toggleFixedBatchOperate();
                    me._toggleFixedHeader();
                    me._toggleFixedPage();
                };
            }
            if(!me._scrollHandle) {
                me._scrollHandle = _.debounce(me._executor, 60);
            }
            if(!me._scrollHandleWithRAF) {
                me._scrollHandleWithRAF = me.list?.throttleWithRAF(me._executor) || me._scrollHandle;
            }
            if(!me._scrollDivHandle) {
                me._scrollDivHandle = _.debounce(function() {
					if (!me.list || !me.list.getMainWrapper) return;
                    me._createScrollDiv(me.list.getMainWrapper(), true);
                }, 30)
            }

            me.list.enablehpaf() ?
                me._scrollHandleWithRAF() :
                me._scrollHandle();

            me._scrollDivHandle();

            //暴露布局纵向滚动事件
            me.list && me.list.layoutScroll && me.list.layoutScroll(e);
        },

        // 其他模块会调用
        requestPaint: function() {
            this._toggleFixedTermBatch();
            this._toggleFixedHeader();
            this._toggleFixedPage();
        },

        //表头区域
        _toggleFixedHeader: function() {
            var me = this;
            if(!me.list || !me.list.fixedHeader) return;
            var termBatchWrapper = me.list.getTermBatchWrapper();
            var termBatchHeight = termBatchWrapper.innerHeight();
            var mainWrapper = me.list.getMainWrapper();
            var offset = mainWrapper.offset();
            var $h = window.Fx  && window.Fx.theme == 'new' ? 41 : 56; //为主站header的高度
            if(this.isMultipleTabs) {
                $h += 40;
            }
            var fixedHeaderTop = $h + termBatchHeight;

            if(offset.top < fixedHeaderTop) {//56 + 38
                if(!me.__fixedHeader) {
                    mainWrapper.before(me.__fixedHeader = $(
                        `<div style="height:${Math.max(termBatchHeight, 42)}px"></div>`
                    ));
                    me.list.fixedHeader({
                        top: fixedHeaderTop,
                        right: 8,
                        left: offset.left + me._getOffsetLeft(),
                        zIndex: 600
                    })
                }
            } else if(me.__fixedHeader && me.__fixedHeader.offset().top > fixedHeaderTop) {//94 - 2
                me._cleanFixedHeader();
                // me.list.fixedHeader();
                // me.__fixedHeader.remove();
                // me.__fixedHeader = null;
            }
        },

        _toggleFixedTermBatch: function() {
            var me = this;
            if(!me.list || !me.list.fixedTermBatch) return;
            if((me.list.getRemberData() || []).length && (me.list.options.batchButtons || []).length) {//有选中的数据
                me.__fixedTerm && (me.__fixedTerm.remove(), me.__fixedTerm = null, me.list.fixedTermBatch());
                return;
            }

            var termBatchWrapper = me.list.getTermBatchWrapper();
            var height = termBatchWrapper.innerHeight();
            var offset = termBatchWrapper.offset();
            var $h = window.Fx  && window.Fx.theme == 'new' ? 41 : 56; //为主站header的高度
            if(this.isMultipleTabs) {
                $h += 40;
            }
            if(offset.top < $h - 1) {
                if(!me.__fixedTerm) {
                    termBatchWrapper.before(me.__fixedTerm = $(
                        `<div style="height:${Math.max(height, 38)}px"></div>`
                    ));
                    me.list.fixedTermBatch({
                        top: $h,
                        right: 16,
                        left: offset.left,
                        paddingTop: 4,
                        zIndex: 605
                    })
                }
            } else if(me.__fixedTerm && me.__fixedTerm.offset().top > $h - 1) {
                me.list.fixedTermBatch();
                me.__fixedTerm.remove();
                me.__fixedTerm = null;
            }
        },

        //批量操作区域
        _toggleFixedBatchOperate: function() {
            var me = this;
            if(!me.list || !me.list.fixedBatchOperate) return;
            if(!(me.list.options.batchButtons || []).length) return;
            if(!(me.list.getRemberData() || []).length) {//没有选中的数据 不用浮动
                me.__fixedBatchOperate && (me.__fixedBatchOperate.remove(), me.__fixedBatchOperate = null, me.list.fixedBatchOperate());
                return;
            }

            var $batchOperate = me.list.getBatchOperateWrapper();
            if(!$batchOperate.width()) return;
            var offset = $batchOperate.offset();
            var $h = window.Fx  && window.Fx.theme == 'new' ? 41 : 56; //为主站header的高度
            if(this.isMultipleTabs) {
                $h += 40;
            }
            if(offset.top < $h - 1 || me.__fixedTerm) {
                if(!me.__fixedBatchOperate) {
                    $batchOperate.before(me.__fixedBatchOperate = $('<div style="height:36px"></div>'));
                    me.list.fixedBatchOperate({
                        top: $h,
                        right: 16,
                        left: offset.left,
                        paddingTop: 8,
                        background: '#fff',
                        zIndex: 615
                    })
                }
            } else if(me.__fixedBatchOperate && me.__fixedBatchOperate.offset().top > $h - 1) {
                me.list.fixedBatchOperate();
                me.__fixedBatchOperate.remove();
                me.__fixedBatchOperate = null;
            }
        },

        //底部分页区
        _toggleFixedPage: function() {
            var me = this;
            if(!me.list || !me.list.fixedHeader) return;
            var dtPage = me.list.getPaginationWrapper();
            var offset = dtPage.offset();
            var mainWrapper = me.list.getMainWrapper();
            var $bodyH = $('body').height();
            if($bodyH - mainWrapper.offset().top < 200) {
                me.__fixedPage && (me.list.fixedPageFooter(), me.__fixedPage.remove(), me.__fixedPage = null);
                return
            }
            if(offset.top > $bodyH - 32) {//超出屏蔽外
                if(!me.__fixedPage) {
                    me.list.getMainWrapper().after(me.__fixedPage = $('<div style="height:32px"></div>'));
                    me.list.fixedPageFooter({
                        bottom: 0,
                        right: 8,
                        left: offset.left,
                        zIndex: 600
                    })
                }
            } else if(me.__fixedPage && me.__fixedPage.offset().top <  $bodyH - 32) {
                me.list.fixedPageFooter();
                me.__fixedPage.remove();
                me.__fixedPage = null;
            }
        },

        _getOffsetLeft(num = 0) {
            return num;
        },

        _updateFixLeft: function(width) {
            if(!this.list || !this.list.fixedHeader) return;

            if(this.__fixedTerm) {
                this.list.fixedTermBatch({
                    left: width + 8
                })
            }
            if(this.__fixedHeader) {
                this.list.fixedHeader({
                    left: width + 8
                })
            }
            if(this.__fixedPage) {
                this.list.fixedPageFooter({
                    left: width + 8
                })
            }
            if(this.__fixedBatchOperate) {
                this.list.fixedBatchOperate({
                    left: width + 8
                })
            }
        },

        _correctFixedHeader:  _.debounce(function() {
            this._cleanFixedHeader();
            this._toggleFixedHeader();
        }, 60),

        _cleanFixedHeader: function() {
            if (this.__fixedHeader) {
                this.__fixedHeader.remove();
                this.__fixedHeader = null;
                this.list.fixedHeader();
            }
        },

        _cleanFixed: function() {
            try {
                this.__fixedTerm && (this.__fixedTerm.remove(), this.__fixedTerm = null, this.list.fixedTermBatch());
                this.__fixedHeader && (this.__fixedHeader.remove(), this.__fixedHeader = null, this.list.fixedHeader());
                this.__fixedPage && (this.__fixedPage.remove(), this.__fixedPage = null, this.list.fixedPageFooter());
                this.__fixedBatchOperate && (this.__fixedBatchOperate.remove(), this.__fixedBatchOperate = null, this.list.fixedBatchOperate());
            } catch(e) {}
        },

        destroy: function() {
        	try {
        		FS.MEDIATOR.off('crm.aside.resize', this._updateFixLeft);
                FS.MEDIATOR.off('crmPageCacheShow', this._forcePaint);
	            this.list && (this.list.destroy(), this.list = null);
	            this.undelegateEvents();
	            this.off();
                this._cleanFixed();
                // 销毁插件引擎
                if (this.__pluginService) {
                    this.__pluginService.destroy();
                    this.__pluginService = null;
                }
        	} catch(e) {
        		console.error(e)
        	}
        }
   })
})
