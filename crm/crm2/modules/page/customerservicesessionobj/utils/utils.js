define(function (require, exports, module) {

	const util = CRM.util

	const ayncGetOnlineserviceComponent = (onlineservicepath) => {
		const me = this
		return new Promise((resolve, reject) => {
			require.async('app-onlineservice/app.js', () => {
				require.async('app-onlineservice-assets/style/all.css');
				require.async(onlineservicepath, (comp) => {
					resolve(comp);
				});
			});
		});
	}

	const handleClickCustomer = () => {
		const path = 'app-onlineservice/tpls/sessions/customerservicesessionobj/index'
		return ayncGetOnlineserviceComponent(path).then(res => {
			return res.handleClickCustomer && res.handleClickCustomer().then(result => {
				debugger
				return result
			})
		})
	}

	const handleTransferSession = (data) => {
		const detailData = data
		const path = 'app-onlineservice/tpls/sessions/customerservicesessionobj/index'

		const formatData = Array.isArray(data) ? data : [data]
		return ayncGetOnlineserviceComponent(path).then(res => {
			return res.handleTransferSession && res.handleTransferSession(formatData).then(result => {
				return result
			})
		})
	}

	const handleEndSession = (data) => {
		const detailData = data
		const path = 'app-onlineservice/tpls/sessions/customerservicesessionobj/index'

		const formatData = Array.isArray(data) ? data : [data]
		return ayncGetOnlineserviceComponent(path).then(res => {
			return res.handleEndSession && res.handleEndSession(formatData).then(result => {
				debugger
				return result
			})
		})
	}

	const handleViewChatRecord = (data) => {
		const detailData = data
		const path = 'app-onlineservice/tpls/sessions/customerservicesessionobj/index'

		const formatData = Array.isArray(data) ? data : [data]
		return ayncGetOnlineserviceComponent(path).then(res => {
			return res.handleViewChatRecord && res.handleViewChatRecord(formatData).then(result => {
				debugger
				return result
			})
		})
	}

	const handleViewLeaveMessage = (data) => {
		const detailData = data
		const path = 'app-onlineservice/tpls/sessions/customerservicesessionobj/index'

		const formatData = Array.isArray(data) ? data : [data]
		return ayncGetOnlineserviceComponent(path).then(res => {
			return res.handleViewLeaveMessage && res.handleViewLeaveMessage(formatData).then(result => {
				debugger
				return result
			})
		})
	}


	module.exports = {
		handleClickCustomer,
		handleTransferSession,
		handleEndSession,
		handleViewChatRecord,
		handleViewLeaveMessage
	}

});


// define(function (require, exports, module) {

// 	const util = CRM.util


// 	const handleClickCustomer = () => {
// 		return new Promise((resolve, reject) => {
// 			seajs.use('app-onlineservice/tpls/sessions/customerservicesessionobj/index', (res) => {
// 				res.handleClickCustomer && res.handleClickCustomer().then(result => {
// 					debugger
// 					resolve(result)
// 				})
// 			})
// 		})
// 	}

// 	const handleTransferSession = (data) => {
// 		const detailData = data
// 		// return util.FHHApi({
// 		// 	url: '/FHH/EM1HONLINECONSULT/online/consult/sessions/receivableConsultants',
// 		// 	type: 'get',
// 		// 	data: { appId: detailData.appId },
// 		// 	success: function (res) {
// 		// 		if (res.Result.StatusCode == 0 && (res.Value.errCode == "C120040000")) {
// 		// 			const userIds = res.Value.data.filter(item => {
// 		// 				return item !== detailData.userId
// 		// 			})

// 		// 			const params = {
// 		// 				rowData: [detailData],
// 		// 				userIds: userIds
// 		// 			}
// 		// 		} else if (res.Value.errMsg) {
// 		// 			util.alert(res.Value.errMsg);
// 		// 		}
// 		// 	}
// 		// }, {
// 		// 	autoPrependPath: false,
// 		// 	// errorAlertModel: 1
// 		// })

// 		const path = 'app-onlineservice/tpls/sessions/transferSession/transferSession-vue'

// 		return ayncGetOnlineserviceComponent(path).then(comp => {
// 			comp.$show = (propsData = {}) => {
// 				return new Promise((resolve, reject) => {
// 					const $vm = new comp({
// 						el: document.createElement("div"),
// 						propsData,
// 					});

// 					$vm.$on("hide", (...args) => {
// 						setTimeout(() => {
// 							$vm.$destroy();
// 							$vm.$el.remove();
// 						}, 1000);
// 					});

// 					$vm.$on("onupdate", (...args) => {
// 						$vm.$emit("hide");
// 						resolve(...args);
// 					});
// 					$vm.$on("onclose", (...args) => {
// 						$vm.$emit("hide");
// 						reject(...args);
// 					});

// 					$("body").append($vm.$el);
// 				});
// 			}
// 			return comp
// 		}).then(comp => {
// 			return comp.$show({ visible: true })
// 		}).then(res => {
// 			debugger
// 		})
// 	}


// 	module.exports = {
// 		handleClickCustomer,
// 		handleTransferSession
// 	}

// });
