/**
 * @desc   返利列表 list
 * @VIEW   继承自自定义对象列表
 * <AUTHOR>
 */
 define(function (require, exports, module) {

    var MyObject = require('crm-modules/page/list/list');

    var util = CRM.util;
    module.exports = MyObject.extend({

        options: _.extend({}, MyObject.prototype.options, {
            apiname: 'ProductObj',
        }),

		initialize: function(){
			MyObject.prototype.initialize.apply(this, arguments);
			this.initCPQGuide();
		},

		// bomcore升级提示
		initCPQGuide(){
			if (!CRM._cache.cpqStatus) {
				return;
			}
			let key = 'crm_bomcore_guide';
			if (window.CRM.getConfig(key) == 1 || CRM.getLocal(key)) {
				return;
			}
			require.async('base-modules/guide/guide', function (Guide) {
				new Guide({
					width: 800,
					notSetKey: false,
					title: 'CPQ',
					type: 'dialog',
					key,
					content: `<div class="pricebook-guide">
                    <span>${$t('CPQ配置产品组合入口已迁移到【产品组合】对象列表页')}</span>
                </div>`,
				});
			});
			CRM.setLocal(key, true);
		},

        addPackageIcon: function (columns) {
            if (CRM._cache.cpqStatus || CRM._cache.fixedCollocationOpenStatus) {
                columns.unshift({
                    data: null,
                    width: 56,
                    fixed: true,
                    fixedIndex: 0,
                    title: '<em class="ico-tit-rel ico-tit-rel-subproductobj p-s"></em>',
                    render: function (data, type, full) {
                        if (full.is_package) {
                            return '<span class="j-show-rel ico-tit-rel ico-tit-rel-subproductobj" data-listname="bom_core_product_list" data-apiname="BomCoreObj"></span>';
                        } else {
                            return '';
                        }
                    }
                })
            }
        },

        //产品的导入默认业务对象是“商品－产品”
        _onImportIn: function (e) {
            // MyObject.prototype._onImportIn.apply(this, arguments);
            var me = this;
            require.async('crm-modules/components/importtool/importtool', function (ImportTool) {
                var importTool = new ImportTool({
                    apiName: 'SPUObj'
                });
                importTool.on('success', function () {
                    CRM.control.navigate('#crm/iorecords');
                })
            })
        },
        //
        // 分类不允许筛选
        //
        getColumns: function () {
            var me = this;
            var colums = MyObject.prototype.getColumns.apply(me, arguments);
            var category = _.findWhere(colums, {
                data: 'category'
            });
            if (category) {
                category.isFilter = false; // 前端逻辑写死
                me.categoryColumn = category;
            }
            return colums;
        },

        getOptions: function () {
            var options = MyObject.prototype.getOptions.apply(this, arguments);
            this.filterColumns(options.filterColumns);
            options.beforeRequestFHH = this.beforeRequestFHH.bind(this);
            return options;
        },
        isGrayListHeaderCache() {
            if (this.isRequestFirst()) return false;
            let res = MyObject.prototype.isGrayListHeaderCache.apply(this, arguments);
            return res
        },
        // 开属性 优先查询分类和属性
        isRequestFirst() {
            return CRM._cache.openAttribute;
        },
        async beforeRequestFHH(opts){
            if (this.isRequestFirst()) {
                try {
                    // 初始化分类选择器
                    await this._initProductSelect();
                    // 初始化属性筛选
                    await this._initAttributeFilter();
                } catch (e) {
                    console.error(e);
                }
            }
            opts = await MyObject.prototype.beforeRequestFHH.apply(this, arguments);
            return opts;
        },
        filterColumns(filterColumns) {
            var item = _.findWhere(filterColumns, {
                data: 'category'
            });
            if (item) {
                item.isFilter = item.is_active = false; // 隐藏分类字段筛选
            }
        },

        initComplete: function () {
            var me = this;
            if (!this.isRequestFirst()) {
                this._initProductSelect();
            }
            MyObject.prototype.initComplete.apply(this, arguments);
        },

        parseActionParam: function (action, param) {
            param.productColumns = this.get('filterColumns');
            return param;
        },

        /**
         * @desc 初始化分类选择器
         */
        _initProductSelect: function(){
            let me = this;
            if ($('.j-category-select', me.$el).length) return;
            return new Promise((resolve) => {
                require.async('crm-modules/components/category/category', function (ProductCategory) {
                    me.$('.last-target-item').after([
                        '<div class="item">',
                        '<span class="line"></span>',
                        '<span class="item-tit">' + $t('分类') + '：</span>',
                        '<div class="item-con j-category-select"></div>',
                        '</div>'
                    ].join(''));
                    me.category && me.category.destroy();
                    me.category = new ProductCategory({
                        hasBtn: true,
                        target: $('.j-category-select', me.$el), //必须提供
                        parentNode: $('.j-category-select', me.$el).parent() //如果提供会将el插入到这个元素后面
                    });
                    if (me._conditions) {
                        me.category.setValue(me._conditions.FilterValue)
                    }
        
                    me.category.on('sel.suc', async function (data) {
                        if (data.CategoryCode) {
                            me._conditions = {
                                FieldName: 'category',
                                Comparison: 1,
                                FilterValue: data.CategoryCode
                            };
                            
                        } else {
                            me._conditions = null;
                        }
                        //筛选分类相关属性
                        if (CRM._cache.openAttribute) {
                            let conditions = await me.categoryAttribute.setCategory(data.CategoryID, data);
                            me._attrConditions = conditions;
                        }
                        me.table.setParam({}, true, true);
                        
                        me.queryParamChangeHandle && me.queryParamChangeHandle();
                        me.afterSelectCategory(data)
                    });
                    me.category.on('fetch.after', function(data){
                        resolve();
                    })
                })
            })
        },
        afterSelectCategory: $.noop,

        _batchUp: function () {
            this.submitUpDown(true);
        },

        _batchDown: function () {
            this.submitUpDown(false);
        },
        //批量关联属性
        _batchAssociate: function () {
            const me = this;
            require.async("crm-modules/components/pickattribute/pickattribute", function (PickAttribute) {
                let pickAttribute = new PickAttribute({
                    apiName: "AttributeObj",
                    type: "default",
                    isEdit: true,
                    success: function (res) {
                        let associateArgs = {
                            url: "AssociateAttribute",
                            data: {
                                "product_ids": me._getBatchIds(),
                                "attribute_infos": res.data
                            }
                        };
                        me._batchAttributes(associateArgs)
                    },
                });
                pickAttribute.render();
            })
        },

        //批量解除关联属性
        _batchDisAssociate: function () {
            const me = this;
            require.async("crm-modules/components/pickattribute/pickattribute", function (PickAttribute) {
                let pickAttribute = new PickAttribute({
                    apiName: "AttributeObj",
                    type: "pick",
                    isEdit: false,
                    title: $t("解除关联属性"),
                    success: function (res) {
                        let disAssociateArgs = {
                            url: "DisAssociateAttribute",
                            data: {
                                "product_ids": me._getBatchIds(),
                                "attribute_ids": res.map(d => d._id)
                            }
                        };
                        me._batchAttributes(disAssociateArgs)
                    },
                });
                pickAttribute.render();
            })
        },
        //批量关联非标属性
        _batchAssociateNs: function () {
            const me = this;
            require.async("crm-modules/components/pickattribute/pickattribute", function (PickAttribute) {
                let pickAttribute = new PickAttribute({
                    apiName: "NonstandardAttributeObj",
                    type: "pick",
                    isEdit: false,
                    needParseData: false,
                    filterIds: [],
                    title: $t("关联非标属性"),
                    success: function (res) {
                        const args = {
                            url: "AssociateNonstandardAttribute",
                            data: {
                                "product_ids": me._getBatchIds(),
                                "nonstandard_attribute_ids": res.map(r=>r._id)
                            }
                        };
                        me._batchAttributes(args);
                    },
                });
                pickAttribute.render();
            })
        },
        //批量解除关联非标属性
        _batchDisAssociateNs: function () {
            const me = this;
            require.async("crm-modules/components/pickattribute/pickattribute", function (PickAttribute) {
                let pickAttribute = new PickAttribute({
                    apiName: "NonstandardAttributeObj",
                    type: "pick",
                    isEdit: false,
                    needParseData: false,
                    title: $t("解除关联非标属性"),
                    success: function (res) {
                        const args = {
                            url: "DisAssociateNonstandardAttribute",
                            data: {
                                "product_ids": me._getBatchIds(),
                                "nonstandard_attribute_ids": res.map(r=>r._id)
                            }
                        };
                        me._batchAttributes(args);
                    },
                });
                pickAttribute.render();
            })
        },

        _getBatchIds: function () {
            let data = this.table.getCheckedData(),
                ids = data.map(d => {
                    return d._id
                });
            return ids;
        },

        _batchAttributes: function (parameter) {
            const me = this;
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/ProductObj/action/' + parameter.url,
                data: parameter.data,
                success: function (res) {
                    if (res.Result.StatusCode === 0) {
                        util.remind(res.msg || $t("操作成功"))
                        me.refresh();
                        return
                    }
                    util.alert(res.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1,
            });
        },

        //批量编辑价目表折扣&价目表售价
        submitUpDown: function (flag) {
            var me = this,
                data = me.table.getCheckedData(),
                status = flag ? $t("上架") : $t("下架"),
                ids = [];
            _.each(data, function (d) {
                ids.push(d._id)
            });
            util.getCpqConfig().done(function (iscpq) {
                var text = $t("确定批量调整产品{{status}}吗?", {
                    status: status
                })
                if (iscpq && !flag) {
                    text = $t("请确认该产品在子产品明细或分组上没有特殊校验，否则可能导致报价等模块配置产品时校验无法通过。")
                }
                var confirm = util.confirm(text, $t("提示"), function () {
                    confirm.hide();
                    util.FHHApi({
                        url: '/EM1HNCRM/API/v1/object/product_status/service/update',
                        data: {
                            "product_status": flag ? "1" : "2",
                            "product_ids": ids
                        },
                        success: function (res) {
                            if (res.Result.StatusCode === 0) {
                                if (res.Value.success) {
                                    util.remind(1, res.Value.msg || $t("操作成功"));
                                    me.refresh();
                                } else {
                                    util.alert(res.Value.msg);
                                }
                                return;
                            }
                            util.alert(res.Result.FailureMessage || $t("操作失败"));
                        }
                    }, {
                        errorAlertModel: 1
                    })
                }, {
                    hideFn: function () {
                        me.refresh();
                    }
                })
            })

        },
        //
        // 解析参数
        //
        parseParam(obj) {
            var param = MyObject.prototype.parseParam.apply(this, arguments);
            let search_query_info = JSON.parse(param.search_query_info);
            if (this._conditions) {
                let data = this._conditions;
                search_query_info.filters.push({
                    field_name: data.FieldName,
                    field_values: data.FilterValue,
                    operator: 'EQ'
                })
            }
            if (CRM._cache.openAttribute) {
                if (this._attrConditions?.length) {
                    search_query_info.filters = search_query_info.filters.concat(this._attrConditions);
                }
            }
            param.search_query_info = JSON.stringify(search_query_info)
            return param;
        },
        // parseParam: function (obj) {
        //     obj = obj || {};
        //     obj.QueryInfo = obj && obj.QueryInfo || {
        //         Conditions: []
        //     };
        //     let conditions = [].concat(obj.QueryInfo.Conditions || []);

        //     conditions = this.parseCusCondition(conditions, "category");
        //     //属性值相关
        //     if (CRM._cache.openAttribute) {
        //         conditions = this.parseCusCondition(conditions, "attribute");
        //     }
        //     obj.QueryInfo.Conditions = conditions;
        //     return MyObject.prototype.parseParam.apply(this, arguments);
        // },

        //格式化分类 or 属性筛选条件
        parseCusCondition(condition, type) {
            condition = _.filter(condition, function (item) {
                return item.FieldName.slice(0, 9) !== type
            })
            if (type == "category") {
                if (this._conditions) {
                    condition.push(this._conditions)
                }
            } else {
                if (this._attrConditions && this._attrConditions.length >= 1) {
                    condition = condition.concat(this._attrConditions)
                }
            }
            return condition;
        },
        /*
        * 属性值筛选相关
        */
        async _initAttributeFilter() {
            const me = this;
            if (me.categoryAttribute) return;
            // 获取是否开启属性产品
            let status = await CRM.util.getProductWithAttr();
            if (!status) return;
            me._attrConditions = [];
            await me._renderAttributeFilter();
        },
        _renderAttributeFilter(){
            let me = this;
            return new Promise((resolve) => {
                require.async('vcrm/sdk',function(sdk){
                    sdk.getComponent('categoryAttrSetting').then(async (Comp) => {
                        me.$el.css('overflow','auto');
                        me.$('.dt-term-batch').after('<div class="attribute-filter" style="padding:10px 16px; background:#fff"></div>')
                        $('.attribute-filter', me.$el).append('<div></div>')
                        // 初始化分类属性
                        let vm = new Vue({
                            el: $('.attribute-filter').children()[0],
                            render: (h) => h(Comp.default, {
                                props: {}
                            }),
                        })
                        me.categoryAttribute = vm.$children[0];
                        me.categoryAttribute.$on('change', (conditions) => {
                            me._attrConditions = conditions;
                            me.table.setParam({}, true, true);
                        })
                        let val = me.category?.getValue();
                        let conditions = await me.categoryAttribute.setCategory(val?.CategoryID || '', val);
                        me._attrConditions = conditions;
                        resolve();
                    })
                });
            })
            
        },
        getFilterAttrValue(arr, attrObj) {
            const id = attrObj.attributeValue,
                type = attrObj.type;
            switch (type) {
                case "1":
                    arr = [id];
                    break;
                case "2":
                    arr.push(id);
                    break;
                case "3":
                    arr = arr.filter(a => a !== id);
            }
            return arr;
        },
        destroy: function () {
            this.categoryAttribute?.$destroy();
            this.categoryAttribute = null;
            this.category && this.category.destroy();
            this.category = null;
            MyObject.prototype.destroy.apply(this, arguments);
        }

    });
});
