/**
 * @Desc     首页
 * <AUTHOR>
 */
define(function(require, exports, module) {
	var SaleReport = require('./salereport/salereport');

    var Index = Backbone.View.extend({

        layoutType: 1,

        initialize: function (opts) {
            this.opts = opts || {};
            this.setElement(opts.wrapper);
			this.$el.html('<div class="skeleton"><div class="title"></div><ul><li></li><li class="even"></li><li></li><li class="even"></li><li></li><li class="even"></li></ul></div>');
        },

        render: function (param) {
            param = param || [];
            this.initLayout();
            if (param.length > 1) {
                this.handleParam(param);
            }
        },

        //处理参数
        handleParam: function(param) {
            var me = this;

            // 客户端企信跳转详情
            if(param && param[0] === 'bpm'){
                if(param[2] === 'BpmTask'){
                    setTimeout(()=>{
                        require.async('paas-bpm/taskdetail', function(TaskDetail) {
                            me.taskDetail = new TaskDetail();
                            me.taskDetail.show({
                                taskId: param[1],
                            })
                            me.taskDetail.on('close', () => {
                                me.taskDetail = undefined;
                            })
                            me.taskDetail.on('refresh', (flag) => {
                                if (flag && flag ['nextTaskId']) {
                                    me.taskDetail.show({
                                        taskId: flag['nextTaskId'],
                                        from:'remind'
                                    })
                                }
                            })
                        });
                    },50);
                } else {
                    setTimeout(()=>{
                        require.async('paas-bpm/flowdetail', function(FlowDetail) {
                            me.flowDetail && me.flowDetail.destroy();
                            me.flowDetail = new FlowDetail({
                                $el: $('body')
                            });
                            me.flowDetail.show({
                                instanceId: param[1]
                            });
                        });
                    },50)
                }
                return;
            }
            if (param.length == 2 || param.length == 3) {
                if (param[0] == 'highsea') {
                    FS.MEDIATOR.trigger('crm.page.CrmHighSeas', param[1]);
                } else {
                    me.showDetailFromQX(param[0], param[1], param[2]);
                }
            }

            if (param.length > 3) { // 销售简报
                me.showSaleReport(param);
            }
        },

        //企信跳转详情
        showDetailFromQX: function(type, id, tabname) {
            if (!type) {
                return;
            }
            if (type == 'myobject' || type.toLocaleLowerCase() == 'udobj') { // 自定义对象
                id = id.replace('|', ',');
                type = 'myobject';
            }
            FS.MEDIATOR.trigger(
                'crm.detail', {
                    type: type.toLocaleLowerCase(),
                    subtab: tabname,
                    data: {
                        crmId: id
                    }
                }
            );
        },

        //显示销售简报
        showSaleReport: function(param) {
            var me = this;
            switch (param[0]) {
                case 'salereport':
                    me.salereport = new SaleReport({
                        el: $('body'),
                        salerangetype: param[3], //日报类型 2日报 3周报 4月报
                        start: param[1],
                        end: param[2]
                    });
                    me.salereport.render();
                    CRM.util.uploadLog('qx', 'bi', {
                        operationId: "openSaleReport",
                        eventType: 'cl',
                        eventData: {
                            salerangetype: param[3],//日报类型 2日报 3周报 4月报
                        }
                    });
                    break;
            }
        },

        uploadFirstLoaded: function(startTime) {
            var cost = new Date().getTime() - startTime;
            if (!window.crm_index_performance_index && cost > 0) {
                window.crm_index_performance_index = true;
                CRM.util.uploadLog('crmhome', 'appcsutom', {
                    operationId: 'firstLoaded',
                    cost: cost,
                    eventType: 'ct'
                });
            }
        },

        initLayout: function() {
            var me = this;
            var startTime = new Date().getTime();
            var wrapper = document.createElement('div');
			require.async('paas-appcustomization/runsdk', function(PaasSDK) {
				PaasSDK && PaasSDK.runningcrmindex().then(function (crmIndex) {
                    me.$el.html(wrapper);
                    me.uploadFirstLoaded(startTime);
					if (crmIndex) { // 兼容报错
                   		me.instance = crmIndex.init(wrapper);
					}
				});
			});
        },

        refresh: function () {
            this.destroy();
            this.render();
            this.needRefresh = false;
        },

        destroy: function () {
            this.instance && this.instance.$destroy();
            this.instance = null;
        }
    });

    module.exports = Index;

});
