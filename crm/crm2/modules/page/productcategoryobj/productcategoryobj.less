
.category-structure-style {
    margin: -16px -24px;
    display: flex;
    .structure-style-left, .structure-style-right {
        width: 50%;
        box-sizing: border-box;
    }
    .structure-style-left {
        padding: 16px 24px;
        .style-left-skeleton {
            display: flex;
            & > div:first-child {
                    margin-right: 1.5em;
            }
            .mini-skeleton {
                width: 90px;
                height: 90px;
                border: 2px solid var(--color-neutrals07);
                border-radius: 6px;
                padding: 8px;
                margin-bottom: 6px;
                &.checked, &:hover {
                    border-color: var(--color-primary06);
                    cursor: pointer;
                }
                display: flex;
            }
            .mini-tree-skeleton {
                .mini-tree-aside {
                    width: 30%;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    justify-content: space-around;
                    align-items: flex-end;
                    & > li {
                        height: 7px;
                        border-radius: 4px;
                        background-color: var(--color-neutrals05);
                    }
                    & > li:nth-of-type(4n+1) {
                        border-radius: 0;
                        background-color: var(--color-neutrals07);
                        width: 100%;
                    }
                    & > li:nth-of-type(4n+2),  & > li:nth-of-type(4n+3) {
                        width: 80%;
                    }
                    & > li:nth-of-type(4n+4) {
                        width: 70%;
                    }

                    & + section {
                        margin-left: 10%;
                        width: 60%;
                        height: 100%;
                        border-radius: 4px;
                        background-color: #eeeeee;
                    }
                }
            }

            .mini-flat-skeleton {
                .mini-flat-aside {
                    width: 30%;
                    height: 100%;
                    background-color: var(--color-neutrals05);
                    border-radius: 4px;
                    padding: 4px;
                    box-sizing: border-box;
                    & > li {
                        height: 7px;
                        margin-bottom: 6px;
                        border-radius: 4px;
                        background-color: var(--color-neutrals07);
                        &:first-child {
                            background-color: var(--color-neutrals01);
                        }
                    }
                }
                .mini-flat-aside + section {
                    margin-left: 10%;
                    width: 60%;
                    height: 100%;
                    border-radius: 4px;
                    .mini-flat-main {
                        padding: 4px 0 0;
                        & > h6, & > ul li {
                            width: 30%;
                            height: 7px;
                            border-radius: 4px;
                            background-color: var(--color-neutrals05);
                            margin-bottom: 6px;
                        }
                        & > h6 {
                            width: 45%;
                            background-color: var(--color-neutrals07);
                        }
                        & > ul {
                            display: flex;
                            justify-content: space-between;
                        }
                        &:last-child ul:nth-of-type(1) {
                            & > li:last-child {
                                background-color: transparent;
                            }
                        }

                    }
                }
            }
        }
        .style-left-recent {
            margin: 15px 0;
        }
    }
    .structure-style-right {
        & > p:first-child {
            padding: 17px 10px 27px;
        }
        background-color: var(--color-neutrals03);
        .style-right-skeleton {
            padding: 0 70px;
            height: 245px;
            .right-skeleton-inner {
                height: 100%;
                background-color: var(--color-neutrals01);
                box-shadow: 0 -1px 4px 1px #ebecee, 0 0 6px 0 #ebecee;
                .right-tree-skeleton, .right-flat-skeleton {
                    height: 100%;
                    display: flex;
                    aside {
                        line-height: 35px;
                        overflow: hidden;
                        flex: none;
                    }
                    main {
                        padding: 3px 10px;
                        flex: auto;
                        overflow: hidden;
                    }
                }
                .right-tree-skeleton {
                    aside > ul:last-child {
                        & > li {
                            padding: 0 10px;
                            background-color: #f5f5f5;
                            &:first-child {
                                position: relative;
                                &::before {
                                    content: "";
                                    width: 0;
                                    border: 2px solid var(--color-primary06);
                                    border-top: none;
                                    border-bottom: none;
                                    border-radius: 0 4px 4px 0;
                                    margin-left: -10px;
                                    margin-right: 6px;
                                }
                                & > i {
                                    padding: 0 4px;
                                    margin-right: -10px;
                                }
                            }
                            &.sub {
                                background-color: var(--color-neutrals01);
                            }
                        }
                    }
                    main {
                        ul {
                            height: 100%;
                            display: flex;
                            flex-flow: column nowrap;
                            justify-content: space-around;
                            li {
                                &:not(:first-child) {
                                    background-color: #eeeeee;
                                    border-radius: 2px;
                                    height: 1em;
                                }
                            }
                        }
                    }
                }
                .right-flat-skeleton {
                    aside > ul:last-child {
                        & > li {
                            padding: 0 10px;
                            background-color: #f5f5f5;
                            &:first-child {
                                position: relative;
                                &::before {
                                    content: "";
                                    width: 0;
                                    border: 2px solid var(--color-primary06);
                                    border-top: none;
                                    border-bottom: none;
                                    border-radius: 0 4px 4px 0;
                                    margin-left: -10px;
                                    margin-right: 6px;
                                }
                                .customization-icon {
                                    margin-right: -6px;
                                    margin-left: 4px;
                                    width: 14px;
                                    height: 14px;
                                    line-height: 14px;
                                    background-color: var(--color-primary06);
                                    border-radius: 50%;
                                    display: inline-block;
                                    position: relative;
                                    vertical-align: middle;
                                    transform: scale(0.7);
                                    i {
                                        content: "";
                                        width: 0;
                                        height: 0;
                                        border-width: 5px 0 5px 5px;
                                        border-style: solid;
                                        border-color: transparent transparent transparent var(--color-neutrals01);
                                        position: absolute;
                                        left: 50%;
                                        top: 50%;
                                        transform: translate(calc(~"-50% + 1px"), -50%);
                                    }
                                }
                            }
                            &.selected {
                                background-color: var(--color-neutrals01);
                            }
                        }
                    }
                    main {
                        h6 {
                            height: 29px;
                            line-height: 29px;
                        }
                        ul.sub-bgc {
                            display: flex;
                            flex-wrap: wrap;
                            justify-content: space-between;
                            li {
                                height: 35px;
                                line-height: 35px;
                                background-color: #eeeeee;
                                flex-basis: 30%;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                margin-top: 6px;
                                padding: 0 6px;
                                box-sizing: border-box;
                            }
                        }
                    }
                }
                .right-skeleton-recent > li {
                    padding: 0 10px;
                    background-color: #f5f5f5;
                }
            }
        }
    }
}

.crm-w-table .fix-start-b th.product-category-fixed:hover .ico-lock {
    display: none;
}
.crm-w-table .category-drag {
    &.category-drag-disable {
        pointer-events: none;
        opacity: 0.3;
        &:hover {
            cursor: not-allowed;
        }
    }
    &:hover {
        cursor: move!important;
    }
    .tb-cell {
        position: relative;
    }
}

.crm-w-table .category-drag .drag-row {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px !important;
    height: 20px;
    background-image: url(../images/table/operate-icon.png);
    background-position: -45px -50px;
    background-repeat: no-repeat;
    display: none;
}