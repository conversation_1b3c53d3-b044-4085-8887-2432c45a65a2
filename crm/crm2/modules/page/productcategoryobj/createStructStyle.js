/*
 * @Description: 
 * @Author: sunsh
 * @Date: 2021-12-07 19:15:51
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-08-12 15:30:53
 */
define(function(require, exports, module) {
    const util = CRM.util;

    function createMiniTreeSkeleton(initValue = '2') {
        return {
            template: `
                <div>
                    <div :class="{checked: radio==='1'}" class="mini-tree-skeleton mini-skeleton" @click="updateRadio('1')">
                        <ul class="mini-tree-aside">
                            <li v-for="n in 8"></li>
                        </ul>
                        <section></section>
                    </div>
                    <fx-radio v-model="radio" @change="updateRadio" label="1">{{$t('树形结构')}}</fx-radio>
                </div>
            `,
            data() {
                return {
                    radio: initValue
                }
            },
            props: {
                radioVal: String,
            },
            created() {
                this.radioVal && (this.radio = this.radioVal);
            },
            watch: {
                radioVal(val) {
                    this.radio = val;
                }
            },
            methods: {
                updateRadio(val) {
                    this.$emit('radio-change', val);
                }
            }
        }
    }

    function createMiniFlatSkeleton(initValue = '2') {
        return {
            template: `
                <div>
                    <div :class="{checked: radio==='2'}" class="mini-flat-skeleton mini-skeleton" @click="updateRadio('2')">
                        <ul class="mini-flat-aside">
                            <li v-for="n in 5"></li>
                        </ul>
                        <section>
                            <div class="mini-flat-main">
                                <h6></h6>
                                <ul v-for="m in 2">
                                    <li v-for="n in 3"></li>
                                </ul>
                            </div>
                            <div class="mini-flat-main">
                                <h6></h6>
                                <ul v-for="x in 2">
                                    <li v-for="y in 3"></li>
                                </ul>
                            </div>
                        </section>
                    </div>
                    <fx-radio v-model="radio" @change="updateRadio" label="2">{{$t('平铺结构')}}</fx-radio>
                </div>
            `,
            data() {
                return {
                    radio: initValue
                }
            },
            props: {
                radioVal: String,
            },
            created() {
                this.radioVal && (this.radio = this.radioVal);
            },
            watch: {
                radioVal(val) {
                    this.radio = val;
                }
            },
            methods: {
                updateRadio(val) {
                    this.$emit('radio-change', val);
                }
            }
        }
    }

    function createTreeSkeleton() {
        return {
            template: `
                <div class="right-tree-skeleton">
                    <aside>
                        <slot></slot>
                        <ul>
                            <li>${$t('一级分类')}<i class="el-icon-arrow-down"></i></li>
                            <li class="sub" v-for="n in 4">${$t("二级分类")}</li>
                            <li>${$t('一级分类')}</li>
                            <li>${$t('一级分类')}</li>
                        </ul>
                    </aside>
                    <main>
                        <ul>
                            <li>${$t("产品列表")}</li>
                            <li v-for="n in 6"></li>
                        </ul>
                    </main>
                </div>
            `,
        };
    }

    function createFlatSkeleton() {
        return {
            template: `
                <div class="right-flat-skeleton">
                    <aside>
                        <slot></slot>
                        <ul>
                            <li class="selected">${$t('一级分类')}<span class="customization-icon"><i></i></span></li>
                            <li v-for="n in 6">${$t('一级分类')}</li>
                        </ul>
                    </aside>
                    <main>
                        <ul>
                            <li v-for="m in 2">
                                <h6>${$t('二级分类')}</h6>
                                <ul class="sub-bgc">
                                    <li v-for="n in 3" title=${$t('三级分类')}>${$t('三级分类')}</li>
                                    <li title=${$t('四级分类')}>${$t('四级分类')}</li>
                                    <li title=${$t('五级分类')}>${$t('五级分类')}</li>
                                    <li title=${$t('六级分类')}>${$t('六级分类')}</li>
                                </ul>
                            </li>
                        </ul>
                    </main>
                </div>
            `,
        };
    }

    function createRecentCollect() {
        return {
            template: `
                <ul class="right-skeleton-recent" v-show="recentStatus || collectStatus">
                    <li v-show="recentStatus">
                        <i class="fx-icon-clock2"></i>
                        <span>${$t('最近')}</span>
                    </li>
                    <li v-show="collectStatus">
                        <i class="fx-icon-collect"></i>
                        <span>${$t('收藏')}</span>
                    </li>
                </ul>
            `,
            props: ['recentStatus', 'collectStatus']
        }
    }
    
    function createStructureStyle(ctx) {
        let miniTreeSkeleton = createMiniTreeSkeleton(),
            miniFlatSkeleton = createMiniFlatSkeleton(),
            treeSkeleton = createTreeSkeleton(),
            flatSkeleton = createFlatSkeleton(),
            recentCollect = createRecentCollect();

        return FxUI.create({
            template: `
                <fx-dialog
                    width="60%"
                    :visible.sync="dialogVisible"
                    :append-to-body="true"
                    :title="$t('分类结构样式')"
                    :z-index="3000"
                    >
                    <div class="category-structure-style">
                        <section class="structure-style-left">
                            <div class="style-left-skeleton">
                                <mini-tree-skeleton :radioVal="radio" @radio-change="radio=$event"></mini-tree-skeleton>
                                <mini-flat-skeleton :radioVal="radio" @radio-change="radio=$event"></mini-flat-skeleton>
                            </div>
                            <div class="style-left-recent" v-if="display">
                                <span>{{ $t('分类导航显示“最近”下单产品') }}</span>
                                <fx-switch
                                    v-model="recent"
                                    size="small">
                                </fx-switch>
                            </div>
                            <div class="style-left-collect" v-if="display">
                                <span>{{ $t('分类导航显示“收藏”产品') }}　　</span>
                                <fx-switch
                                    v-model="collect"
                                    size="small">
                                </fx-switch>
                            </div>
                        </section>
                        <section class="structure-style-right">
                            <p>{{ radio==='1'? $t("树形结构-适合分类级别、条目较少的业务场景, 示例") : $t("平铺结构-适合分类级别、条目较多的业务场景, 示例") }}:</p>
                            <div class="style-right-skeleton">
                                <div class="right-skeleton-inner">
                                    <component :is="(radio==='1'? 'treeSkeleton' : 'flatSkeleton')">
                                        <recent-collect 
                                            :recentStatus="recent"
                                            :collectStatus="collect"
                                        ></recent-collect>
                                    </component>
                                </div>
                            </div>
                        </section>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <fx-button type="primary" @click="onSave" size="small">{{$t('保存')}}</fx-button>
                        <fx-button @click="dialogVisible = false" size="small">{{$t('取消')}}</fx-button>
                    </span>
                </fx-dialog>
            `,
            data() {
                return {
                    dialogVisible: false,
                    radio: '2',
                    display: false,
                    recent: false,
                    collect: false,
                }
            },
            components: {
                treeSkeleton,
                flatSkeleton,
                miniTreeSkeleton,
                miniFlatSkeleton,
                recentCollect

            },
            watch: {
                dialogVisible(val) {
                    if (val) {
                        this.radio = CRM._cache.category_model_type ? '1' : '2';
                        this.getConfig();
                    }
                }
            },
            mounted() {
                this.$el.querySelector('.el-dialog').style.minWidth = "870px";
            },
            methods: {
                getConfig() {
                    let _this = this;
                    CRM.util.getConfigValues(['category_model_type'])
                        .then(function(data) {
                            let ret = _.reduce(data, (accumulator, item, index, arr) => {
                                accumulator[item.key] = item.value.split(',');
                                return accumulator;
                            }, {});

                            _this.radio = ret['category_model_type'][0]; // '1'树形，'2'平铺：默认
                            _this.cacheSwitch(ret);
                        }, function(err) {

                        });
                },
                cacheSwitch(ret) {
                    // 树形为true, 平铺false
                    CRM._cache.category_model_type = ret['category_model_type'] && ret['category_model_type'][0] == '1';
                },

                radioSaveSuccess() {
                    CRM._cache.category_model_type = this.radio == '1';
                },
                radioSaveFail() {
                    // 由于dialog的DOM没有销毁，需要恢复radio状态，
                    if (this.radio === '1') {
                        this.radio = '2';
                    } else {
                        this.radio = '1';
                    }
                },

                onSave() {
                    const styleMap = {
                        '1': 'TREE',
                        '2': 'LIST',
                    }
                    CRM.util.ajax_base('/EM1HNCRM/API/v1/object/product_category/service/set_category_display_mode', {
                        style: styleMap[this.radio],
                    }).then(() => {
                        this.radioSaveSuccess();

                        util.remind(1, $t("设置成功"));
                        this.dialogVisible = false;
                    }).catch(() => {
                        this.radioSaveFail();

                        util.remind(3, $t("设置失败"));
                    })
                    }
            }
        });
    }

    return createStructureStyle;
});