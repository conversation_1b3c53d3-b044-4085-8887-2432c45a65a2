/**
 * @desc: 商城
 * @author: chil
 * @date: 2021-11-09
 */
define(function (require, exports, module) {

	var CommonList = require('../list/list');
	var utils = require('./utils');
	var widgetService = require('crm-modules/components/pickselfobject_spusku/service');
	const isHideAttr = ['east2020', '88130'].includes($.cookie('ERUpstreamEa'));

	var List = Backbone.View.extend({
		isSpu: window.$dht.config.sail.isSpuMode,
		pricePolicyEnable: CRM._cache.advancedPricing, // 价格政策
		bomEnable: $dht.config.bom.isEnable, // bom
		rencentOrderGray: $dht.config.sail.isRecentOrder, // 最近订购灰度控制
		isHidePrice: $dht.config.sail.isHidePrice, // 是否隐藏价格

		initialize(opts) {
			this.comps = {};
			this.setElement(opts.wrapper);
		},

		events: {
			'click .j-attr-value': 'clickAttrValue',
		},

		render() {
			this.getUserCustomerLayout();
			const cacheViewType = CRM.util.getCache('dht-product-view-type');
			this._uploadLog('mode', 'cl', {
				isCard: cacheViewType === 'card',
			});
		},

		clickAttrValue(e) {
			// TODO: 访问路径太深，怎么让组件自调用
			this.comps.tabs.productList.table.clickAttrValue(e);
		},

		/**
		 * 日志监控
		 * @param operationId
		 * @param eventType
		 * @param data
		 */
		_uploadLog(operationId, eventType, data) {
			widgetService.getService('logService').then(({logService}) => {
				logService.log('shopmall', operationId, eventType, data);
			});
		},

		getUserCustomerLayout() {
			const data = {
				appType: 6,
				layoutApiName: "FSAID_11490c84-shopping"
			};
			CRM.util.FHHApi({
				url: '/EM6HWebPage/UserHomePage/getUserCustomerLayout',
				data,
				success: (res) => {
					if (res.Result.StatusCode === 0 && res.Value) {
						const components = res.Value.homePageLayout.customerLayout.components;
						const componentV = Object.values(components);
						const tabComponent = componentV.find((comp) => comp.type === 'tabs');
						const allProductComponent = componentV.find((comp) => comp.type === 'dht_shopmall_allproduct');
						const compontentIndexArr = tabComponent.props.components || []

						const tabs = tabComponent.props.tabs || [];
						// 默认视图（卡片和列表视图）
						const defaultView = allProductComponent.props.default_view || 'card';
						CRM.util.setCache('dht-product-view-type', defaultView);

						this.initTab(tabs,components,compontentIndexArr);
					}
				}
			});
		},

		initTab(tabs = [],components,compontentIndexArr) {
			let me = this;
			const  configuredTabApiNames = [];
			tabs.forEach(tab => {

				configuredTabApiNames.push(tab.api_name);
				if (tab.api_name.indexOf('dht_shopmall_allproduct') !== -1) {
					tab.header = this.isSpu ? $t('全部商品') : $t('全部产品');
				}
			});

			this.comps['tabs'] && this.comps['tabs'].destroy();
			this.comps['tabs'] = FxUI.create({
				wrapper:  this.options.wrapper[0],

				template: `<div class="dht-shopmall" :class="hideAttrClass">
					<fx-tabs v-model="active_name" @tab-click="handleClick">
						<fx-tab-pane
							v-for="tab in tabs"
							:key="tab.api_name"
							:label="tab.header"
							:name="tab.api_name"
							:index="tab.index"
						>
							<div
								:class="[
									'tabpane-' + tab.api_name,
									tab.api_name === 'dht_shopmall_collectionproduct' ? 'tabpane-no-page' : ''
								]"
							></div>
						</fx-tab-pane>
					</fx-tabs>
				</div>`,

				data() {
					return {
						active_name: tabs[0].api_name,
						curName: tabs[0].api_name,
						hasQuery: {},
						tabs: Object.freeze(tabs),
						configuredTabApiNames: Object.freeze(configuredTabApiNames),
						components: components,
						compontentIndexArr: compontentIndexArr,
						hideAttrClass: isHideAttr ? 'dht-hide-attr' : '',
					}
				},

				methods: {
					handleClick(tab, event) {
						if (tab.name === this.curName) return;
						this.curName = tab.name;
						this.renderTabPaneByName1(tab.name,tab.index);
						const key = tab.name.replace('dht_shopmall_', '');
						if (key.indexOf('allproduct') !== -1) {
							me._uploadLog(key, 'cl');
						}
					},

					renderTabPaneByName1(tabName,index = 0) {
						const $wrapper = $(`.tabpane-${tabName}`, me.$el);

						if (tabName.indexOf('dht_shopmall_allproduct') !== -1) {
							const options = { wrapper: $wrapper };
							if (this.hasQuery.from === 'homesearch') {
								options.filters = [{
									field_name: 'name',
									field_values: [this.hasQuery.value],
									operator: 'LIKE'
								}];
								options.hasQuery = this.hasQuery;
							}
							this.renderProductList(options);
							this.hasQuery = {};
							return;
						}

						if (tabName.indexOf('dht_shopmall_newproduct') !== -1){
							this.renderProductList({
								wrapper: $wrapper,
								selfFilters: [{
									field_name: 'commodity_label',
									field_values: ['option1'],
									operator: 'LIKE',
								}]
							});
						}

						if (tabName.indexOf('dht_shopmall_recentorder') !== -1){
							this.renderProductList({
								wrapper: $wrapper,
								objectData: {
									"tab_recently_ordered": true
								},
								forceHideCategory: true,
								tableOptions: {
									searchTerm:null, //选择场景
									showTagBtn:false, //按标签筛选
									showMoreBtn:false,//列设置
									showFilerBtn:false, //筛选
									search: false, //右边的搜索框
								}
							});
							return 
						}

						if (tabName.indexOf('dht_shopmall_promotionproduct') !== -1){
							this.renderProductList({
								wrapper: $wrapper,
								objectData: {
									"tab_promotion": true
								},
								forceHideCategory: true,
								tableOptions: {
									searchTerm:null, //选择场景
									showTagBtn:false, //按标签筛选
									showMoreBtn:false,//列设置
									showFilerBtn:false, //筛选
									search: false, //右边的搜索框
								}
							});
							return 
						}

						if (tabName.indexOf('dht_shopmall_collectionproduct') !== -1){
							CRM.util.showLoading_new();
							this.getCollectList().then(result => {
								CRM.util.hideLoading_new();
								let ids = _.keys(result);
								this.renderProductList({
									tabName: tabName,
									wrapper: $wrapper,
									selfFilters: [{
										field_name: '_id',
										field_values: ids.length ? ids : ['dht'],
										operator: 'IN',
									}],
									forceHideCategory: true,
									tableOptions: {
										searchTerm:null, //选择场景
										showTagBtn:false, //按标签筛选
										showMoreBtn:false,//列设置
										showFilerBtn:false, //筛选
										search: false, //右边的搜索框
										showPage: false, //分页
									},
									collectionIds: ids.length ? ids : ['dht'],
								});
							}, () => {})
							return 
						}

						if(tabName.indexOf('dht_shopmall_specify_product_range') !== -1) {
							let filters = this.getRangeFilters(index)
							this.renderProductList({
								wrapper: $wrapper,
								selfFilters: filters.filters,
							});
						}
					},

					renderTabPaneByName(tabName) {
						const $wrapper = $(`.tabpane-${tabName}`, me.$el);
						switch (tabName) {
							case 'dht_shopmall_allproduct':
								const options = { wrapper: $wrapper };
								if (this.hasQuery.from === 'homesearch') {
									options.filters = [{
										field_name: 'name',
										field_values: [this.hasQuery.value],
										operator: 'LIKE'
									}];
									options.hasQuery = this.hasQuery;
								}
								this.renderProductList(options);
								this.hasQuery = {};
								break;
							case 'dht_shopmall_newproduct':
								this.renderProductList({
									wrapper: $wrapper,
									selfFilters: [{
										field_name: 'commodity_label',
										field_values: ['option1'],
										operator: 'LIKE',
									}]
								});
								break;
							case 'dht_shopmall_recentorder':
								this.renderProductList({
									wrapper: $wrapper,
									objectData: {
										"tab_recently_ordered": true
									},
									forceHideCategory: true,
									tableOptions: {
										searchTerm:null, //选择场景
										showTagBtn:false, //按标签筛选
										showMoreBtn:false,//列设置
										showFilerBtn:false, //筛选
										search: false, //右边的搜索框
									}
								});
								break;
							case 'dht_shopmall_promotionproduct':
								this.renderProductList({
									wrapper: $wrapper,
									objectData: {
										"tab_promotion": true
									},
									forceHideCategory: true,
									tableOptions: {
										searchTerm:null, //选择场景
										showTagBtn:false, //按标签筛选
										showMoreBtn:false,//列设置
										showFilerBtn:false, //筛选
										search: false, //右边的搜索框
									}
								});
								break;
							case 'dht_shopmall_collectionproduct':
								CRM.util.showLoading_new();
								this.getCollectList().then(result => {
									CRM.util.hideLoading_new();
									let ids = _.keys(result || ['dht']);
									this.renderProductList({
										tabName: tabName,
										wrapper: $wrapper,
										selfFilters: [{
											field_name: '_id',
											field_values: ids,
											operator: 'IN',
										}],
										forceHideCategory: true,
										tableOptions: {
											searchTerm:null, //选择场景
											showTagBtn:false, //按标签筛选
											showMoreBtn:false,//列设置
											showFilerBtn:false, //筛选
											search: false, //右边的搜索框
											showPage: false, //分页
										},
										collectionIds: ids,
									});
								}, () => {})
								break;
							case 'dht_shopmall_specify_product_range':
								let filters = this.getRangeFilters('dht_shopmall_specify_product_range')
								this.renderProductList({
									wrapper: $wrapper,
									selfFilters: filters.filters,
								});
								break;
							default:
								break;
						}
					},

					getRangeFilters(index){
						const apiName = this.compontentIndexArr[Number(index)];
						return this.components[apiName].props.card_main_info.specify_product_range.filterValues[0];
					},
					/**
					 * 选产品/商品页
					 */
					renderProductList(options = {}) {
						this.productList && this.productList.destroy();

						require.async('crm-modules/components/pickselfobject_spusku/pickselfobject_spusku', async (PickObject) => {
							this.productList = new PickObject();

							if(me.isHidePrice) {
								options.tableOptions = _.extend({}, options.tableOptions, {
									showMoreBtn: false, // 屏蔽掉列设置，因为这里可以放出价格相关字段
								})
							}

							const commonOptions = await utils.getCommonOptions(me.isSpu, me.bomEnable);
							const { masterData, objectData } = options;
							if (masterData) {
								_.extend(commonOptions.master_data, masterData)
								delete options.masterData;
							}
							if (objectData) {
								_.extend(commonOptions.object_data, objectData)
								delete options.objectData;
							}


							this.productList.render(Object.assign({}, commonOptions, options));
						});
					},
					/**
					 * 标准列表页
					 */
					renderCommonList(options = {}) {
						this.commonList && this.commonList.destroy();

						let commonOptions = _.extend({
							wrapper: '',
							jsPath: [FS.ROOT_PATH, 'crm' + FS.PATH_SUFFIX, 'modules', 'page', 'list', 'list'].join('/') + '.js',
							modClassName: "portal- crm-page crm-p-undefined crm-p-pricepolicyobj"
						}, options)
						this.commonList = new CommonList(commonOptions);
						this.commonList.render([options.apiname]);
					},

					/**
					 * 获取收藏列表
					 */
					getCollectList() {
						let params = {pageNumber: 1, pageSize: 500}
						return widgetService.getService('collectionService').then(({collectionService}) => {
							return collectionService.getCollectionIds(params, true);
						});
					},
					/**
					 * 获取过滤条件
					 */
					getFilters() {
						// 默认的过滤条件
						let defaultWheres = [{
							connector: 'or',
							filters: [{
								value_type: 0,
								operator: 'EQ',
								field_name: 'product_status',
								field_values: ['1']
							}]
						}];
						if (me.bomEnable) {
							defaultWheres[0].filters.push({
								operator: 'EQ',
								field_name: 'is_saleable',
								field_values: [true]
							});
						}

						// 取缓存描述product_id字段里的wheres，如果有，直接使用
						let desCache = $dht.services.meta.getDescribeAndLayoutInCache({
							object_api_name: 'SalesOrderProductObj',
							record_type: 'default__c',
						});
						let fields = desCache && desCache.objectDescribe && desCache.objectDescribe.fields;
						let wheres = fields && fields.product_id && fields.product_id.wheres;
						if (wheres && wheres.length) {
							defaultWheres = wheres;
						}

						return defaultWheres;
					},

					handleJumpUrl() {
						const hasQuery = utils.getHashQuery(window.location.hash);
						this.hasQuery = hasQuery;
						const tab = hasQuery.tab;

						if (this.configuredTabApiNames.includes(tab)) {
							this.active_name = tab;
							this.curName = tab;
						}
					}
				},

				created() {
					this.productList = null;
					this.commonList = null;
					this.handleJumpUrl();
				},

				mounted() {
					this.$nextTick(() => {
						this.renderTabPaneByName1(this.active_name);
					});
				},

				beforeDestroy() {
					this.productList && this.productList.destroy();
					this.commonList && this.commonList.destroy();
				}
			});
		},

		destroy: function() {
			this.comps['tabs'] && this.comps['tabs'].$destroy();
		}
	});

	module.exports = List;
});