/**
 * @desc   发货单
 * <AUTHOR>
 */
define(function (require, exports, module) {
	const List = require("../list/list");
	const objectApiName = "DeliveryNoteObj";
    var util = CRM.util;

	module.exports = List.extend({
		options: _.extend({}, List.prototype.options, {
			apiname: objectApiName,
		}),

		initialize: function (opts) {
			List.prototype.initialize.call(this, opts)
			if(CRM.util.isConnectApp()){
				return
			}
			const queryParams = CRM.util.getTplQueryParams(window.location.href)
			// 如果链接带有filteremptysalesorderid参数 表明要过滤出发货单销售订单编号为空的发货单
			if (_.isObject(queryParams) && queryParams.filteremptysalesorderid) {
				this.setFilterParam('sales_order_id', {
					field_name: 'sales_order_id',
					field_values: [""],
					operator: 'IS'
				})
				setTimeout(() => {
					FS.crmUtil.remind(1, $t("已过滤出关联多个销售订单的发货单"),null,2500);
				}, 500);
			}
		},
		// 自定义表格按钮响应事件
		_operateHandle: function (opts, data) {
			const actionMap = {
				ViewLogistics_button_default:
					/**
					 * @desc 查看物流信息
					 */
					function handleViewLogistics(data) {
						CRM.api.show_logistics(data);
					},
				ConfirmReceipt_button_default:
					/**
					 * @desc 发货单确认收货
					 */
					function handleConfirmReceipt(data, param_form) {
						CRM.api.delivery_confirm_receipt({
							dataId: data._id,
							param_form,
							objectData: data,
							success: () => {
								// TODO 验证
								this.table.refresh();
							},
						});
					},
                designated_distributor__c:
					/**
					 * @desc 指定配送员
					 */
					function handledesignated_distributor__c(data) {
                        let { _id, date_of_delivery:dateOfDelivery, account_id } = data;
                        let orderList = [{_id, account_id}];
                        this.designatedDeliveryPerson(orderList, dateOfDelivery)
					},
			};

			const action = actionMap[opts.api_name];
			if (
				opts.describe_api_name === objectApiName &&
				action &&
				typeof action === "function"
			) {
				// return action.call(this, data);
				if (opts.api_name == 'ConfirmReceipt_button_default') {
					return action.call(this, data, opts.param_form); // param_form 点击按钮需要输入内容的字段列表，
				} else {
					return action.call(this, data)
				}

			} else {
				return List.prototype._operateHandle.apply(this, arguments);
			}
		},

		// 过滤表格按钮
		parseData(obj) {
			const object = List.prototype.parseData.call(this, obj);
			object.data.forEach((item) => {
				// 不等于has_delivered,则不显示确认收货按钮
				if (item.status !== "has_delivered") {
					item.operate = item.operate.filter(
						(item) =>
							item.api_name !== "ConfirmReceipt_button_default"
					);
				}
			});
			return object;
		},

        designatedDeliveryPerson(orderList, dateOfDelivery){
            var me = this;
						util.waiting();
            FS.MEDIATOR.trigger("appCheckin.getModule", {
                moduleInfo: [{
                    url: 'app-checkin-modules/es6/set-deliveryMan-dialog/index.js',
                    isEs6: true
                }],
                complete: function complete(Comp) {
                    var dialog = new Comp({
                        zIndex: 1000,
                        data: {
                            orderList,
                            dateOfDelivery
                        },
                        saveSuccess: function saveSuccess() {
                            me.table.refresh()
                        }
                    });
                    dialog.show();
										util.waiting(false);
                },
                isNeedCheckAdmin: false
            });
        },

        batchBtnChangeHandle: function (item) {
            //指定配送员
            if(item.button_api_name === "designated_distributor__c"){
                let data = this.getRemberData();
                if(!_.every(data, item=> item.distribution_status == '1')){
                    util.alert($t('crm.deliverynoteobj.designated.distributor.error'));
                    return;
                }else{
                    let orderList = data.map(curItem => {
											let { _id, account_id } = curItem;
											return {
													_id,
													account_id
											}
									}),
                        dateOfDelivery = _.pluck(data, "date_of_delivery").length ? _.pluck(data, "date_of_delivery")[0] : '';
                    this.designatedDeliveryPerson(orderList, dateOfDelivery)
                }
            }else{
                this.excuteAction(item.action, _.pick(item, ['async_button_apiname', 'button_action', 'button_api_name', 'button_label', 'param_form', 'button_type', 'redirect_type']));
            }
        },


	});
});
