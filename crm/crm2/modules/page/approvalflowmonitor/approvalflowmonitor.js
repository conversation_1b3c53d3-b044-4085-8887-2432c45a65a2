/**
 * @description 审批流运维监控
 * <AUTHOR>
 */

define(function(require, exports, module) {
    const tab = require('./tab/index');

    const ApprovalflowMonitor = Backbone.View.extend({

        initialize: function(opts) {
            this.setElement(opts.wrapper);
            this.tab = new tab({
                currentTab:'approval'
            })
        },
        render(){
            this.$el.html(this.tab.$el)
        },
        destroy: function() {

        }
    });

    module.exports = ApprovalflowMonitor;
});
