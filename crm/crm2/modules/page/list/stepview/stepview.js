define(function (require, exports, module) {
	var utils = CRM.util,
		Tpl = require('./tpl/tpl-html'),
		StepTpl = require('./tpl/steptpl-html'),
		ListBody = require('./tpl/listbody-html'),
		ScrollBar = require('base-modules/ui/scrollbar/scrollbar'),
		Dialog = require('crm-widget/dialog/dialog'),
		Sortable = require('base-sortable'),
		StepModel = require('./model/model'),
		stepFilterTpl = require('./tpl/stepfilters-html'),
		SetColumn = require('crm-widget/setcolumn/setcolumn'),
		Select = require('crm-widget/select/select');

	var StepView = Backbone.View.extend({
		initialize: function (options) {
			let me = this;
			_.extend(me, options);
			me.model = new StepModel();
			me.$el.addClass('crm-list-stepview');
			me.$el.html('<div class="step-wrapper"></div>');
			me.initStepTitle();
		},

		events: {
			'click .j-show-opportunity': 'showOppoDetail',
			'click .j-show-lookup': 'showCustomerDetail',
			'click .list-exact':'showExactNum'
		},

		//表头展示
		initStepTitle: function () {
			let me = this;
			me.allRocketStage = me.stageField && me.stageField.options;
			me.curProcessCache = me.options.apiname + 'Process';
			if (me.options.apiname == 'NewOpportunityObj') {
				me.curProcessCache = 'newoppoProcess';
			}
			me.curProcessCache += CRM.util.isConnectApp() ? 'isDownStream' : '';
      me.initPluginService().then(()=>{
        me.renderStepView();
        me.showStepView();
      })
		},

		$batchTermFind: function(selector) {
			return this.table.getTermBatchWrapper().find(selector);
		},

		renderStepView: function () {
			this.createEl();
			this.renderStepComponents();
		},
    initPluginService(){
      return new Promise((resolve)=>{
        window.CRM.api.pluginService.init({
          appId: 'stageStepView'
        }).then((ps)=>{
          this.pluginService = ps;
          return;
        }).then(()=>{
          const availablePlugins = [{
            "pluginApiName": 'PWCPlugin',
            "resource": function(){
              return window.PAAS.plugin &&window.PAAS.plugin.libs.get('PWCPlugin');
            },
            "params": {
              "fieldMapping": {},
              "details": []
            }
          }];
          return this.pluginService.use.apply(this.pluginService,[availablePlugins])
        }).then(()=>{
          return this.getPlugin()
        }).then((plugins)=>{
          this.pluginService.run('pwc.init.before', {
            plugins
          });
          resolve()
        }).catch(()=>{
          resolve()
        })
      })
    },
    PluginServiceRun(hook, param) {
      return new Promise((resolve, reject) => {
        this.pluginService && this.pluginService.run(hook, param).then((rst = {}) => {
          if(rst && rst.StatusCode === 0 && rst.BizCode === 2) {
            resolve(rst.Value || {})
          } else {
            resolve({})
          }
        })
      })

    },
    getPlugin(){
      return new Promise((resolve, reject)=>{
        utils.FHHApi({
          url: '/EM1AFLOW/Config/Get',
          data: {
            flowType: 'stage',
            type: 'pluginForStepView',
            terminal: 'WEB'
          },
          success:  (res)=> {
            if (res.Value && res.Value.value) {
              resolve(res.Value.value)
            }
          },
          complete:()=>{
            resolve()
          }
        })
      }).then((pluginApiName)=>{
        if(pluginApiName){
          return [{
            api_name:pluginApiName,
            client_type:'web'
          }]
        } else {
          return []
        }
      })
    },
		//渲染卡片个人布局按钮
		createEl: function () {
			this.$layoutSet = $(`<span data-title="${$t('设置')}" data-pos="top" class="crm-ui-title fx-icon-set"></span>`);
			this.$batchTermFind('.dt-control-btns').prepend(this.$layoutSet);
			this.$layoutSet.on('click', () => {
				this.showLayoutSetting();
			})
		},

		//渲染阶段视图的组件：销售流程组件
		renderStepComponents: function () {
			let me = this;
			let apiName = me.options.apiname;
			let labels;
			if (apiName == 'NewOpportunityObj') {
				labels = {};
				let sales_process_id = _.findWhere(me.options.columns, {
					api_name: 'sales_process_id'
				});
				labels.sales_process_id = (sales_process_id && sales_process_id.label) || $t('销售流程');

				let close_date = _.findWhere(me.options.columns, {
					api_name: 'close_date'
				});
                // 商机阶段视图顶部筛选字段显示优化, 字段没有禁用才显示，不再给默认值
                labels.close_date = (close_date && close_date.label);

				labels.range = !window.PRM;
			}
			this.$batchTermFind('.batch-term').append(stepFilterTpl({
				labels
			}));
			me._getSalesProcess(function (data) {
				me.renderProcessSelect(data);
				me.model.getLayoutConfig(apiName).then(function (res) {
					me.stepLayout = []
					if (res.Result.StatusCode == 0 && res.Value && res.Value.stage_view_infos) {
						me.stepLayout = res.Value.stage_view_infos;
					}
					me.initSetLayout(me.stepLayout);
				});
			});
		},

		//初始化阶段推进器select选择器
		renderProcessSelect: function (data) {
			let me = this;
			me.processId = CRM.util.getCache(me.curProcessCache) || data.length && data[0].value;
			me.processSelect = new Select({
				$wrap: me.$batchTermFind('.salesprocess-select'),
				size: 1,
				zIndex: 2001,
				options: data,
				defaultValue: me.processId
			});
			me.processSelect.on('change', function (value) {
				me.processId = value;
				CRM.util.setCache(me.curProcessCache, value);
				me.show();
			});
			if (me.processId) {
				me.initStepView();
			}
		},

		//获取全部流程&全部阶段
		_getSalesProcess: function (callBack) {
			let me = this;
			if (!me.allRocketStage) {
				return false;
			}

			me.salesStages = [];
			me.salesStagesNotUsable = [];
			//过滤禁用阶段&&缓存禁用阶段
			_.each(me.allRocketStage, function (ss) {
				if (!ss.not_usable) {
					me.salesStages.push(ss);
				} else {
					me.salesStagesNotUsable.push(ss);
				}
			});
			var data = [];
			me.model.getAllDefinitionList(me.options.apiname).then(function (res) {
				if (res.Result.StatusCode == 0) {
					if (res.Value.data && res.Value.data.length) {
						data = res.Value.data;
						data = _.map(data, function (item) {
							return {
								name: item.name,
								value: item.sourceWorkflowId
							}
						});
					}
					if (res.Value.showAllStage) {
						data.push({
							name: $t("展示全部阶段"),
							value: 'all'
						});
					}
				}
				callBack && callBack(data);
			});
		},

		//设置布局
		initSetLayout: function (layout) {
			let me = this,
				opts = me.options,
				columnsInLayout = [],
				columnsNotInLayout = [],
				blackList = ["relevant_team", "image", "file_attachment","big_file_attachment", "signature", 'province', 'city', 'district', 'rich_text'],
				columns = _.clone(opts.columns || []);

			// 列设置展示所有的列
			_.each(opts.addColumns || [], function (column) {
				let o = _.findWhere(columns, {data: column.data});
				if (!o) {
					columns.push(_.extend(column, {isHidden: true}))
				}
			});
			// 列设置屏蔽部分字段，按layout顺序展示已启用字段和未启用字段
			_.each(columns, function (col) {
				if (col.api_name == 'name') {
					col.fixed = false;
				}
				if (layout.length == 0) {
					if (!(_.contains(blackList, col.api_name) || _.contains(blackList, col.type))) {
						columnsNotInLayout.push(_.extend(col, {isHidden: true}));
					}
				} else {
					if (!(_.contains(blackList, col.api_name) || _.contains(blackList, col.type))) {
						let i = _.findWhere(layout, {api_name: col.api_name});
						if (i) {
							let index = _.indexOf(layout, i);
							columnsInLayout[index] = _.extend(col, {isHidden: false});
						} else {
							columnsNotInLayout.push(_.extend(col, {isHidden: true}));
						}
					}
				}
			})

			this.__layoutColumns = columnsInLayout.concat(columnsNotInLayout);

			// me._setLayout = new SetColumn({
			// 	$target: this.$layoutSet,
			// 	trigger: 'click',
			// 	pos: 'bottom left',
			// 	stopPropagation: false,
			// 	columns: utils.deepClone(setColumns),
			// 	orderColumn: opts.orderColumn,
			// 	addOrderColumn: opts.addOrderColumn,
			// });

			// me._setLayout.on('show', function () {
			// 	me.insertLayoutTip();
			// });
			// me._setLayout.on('rest', function () {
			// 	me.insertLayoutTip();
			// });
			// me._setLayout.on('save', function (data) {
			// 	//处理数据
			// 	let layoutData = [];
			// 	_.each(data, function (d, k) {
			// 		if (!d.isHidden && d.api_name) {
			// 			let item = {
			// 				api_name: d.api_name,
			// 				label: d.label,
			// 				render_type: d.render_type
			// 			}
			// 			layoutData.push(item);
			// 		}
			// 	})

			// 	if (layoutData.length >= 9) {
			// 		this.oldColumns = setColumns;
			// 		CRM.util.alert($t("阶段视图布局最多只能设置8个字段"));
			// 	} else {
			// 		setColumns = data;
			// 		me.model.saveLayoutConfig({
			// 			describe_api_name: opts.apiname,
			// 			stage_view_infos: layoutData
			// 		}).then(function (res) {
			// 			if (res.Result.StatusCode == 0) {
			// 				me.stepLayout = layoutData;
			// 				me.show();
			// 			}
			// 		});
			// 	}
			// });
		},

		showLayoutSetting: function() {
			var layoutColumns = this.__layoutColumns;
			var columns = [];
			_.each(layoutColumns, a => {
				a.data && a.title && columns.push({
					label: a.title,
					value: a.data,
					disableSet: false, //开关置灰
					fixed: false, //固定位置
					isHidden: a.isHidden, //开关值
					isDrag: true //是否可拖拽
				})
			})
			this.table.showSetting({
				isShowRecoverSys: true,
				hideResetAllFields: true,
				columns: columns,
				showSave: true,
				dSetTip: $t('卡片布局设置') + '(' + $t('卡片上最多可以展示8个字段') + ')',
				cleanFieldListConfig:(param, event) => {
					return this.model.deleteLayoutConfig(this.options.apiname, event);
				},
			}).on('setColumn', columns => {
				var layoutData = [];
				var tmp = [];
				_.each(columns, (d, k) => {
					var oitem = _.findWhere(layoutColumns, {data: d.value});
					oitem.isHidden = d.isHidden;
					tmp.push(oitem);

					if(d.isHidden) return;

					layoutData.push({
						api_name: d.value,
						label: d.label,
						render_type: oitem.render_type
					})
				})
				this.__layoutColumns = tmp;

				if (layoutData.length >= 9) {
					CRM.util.alert($t('paas.crm.list.stepview.max_limit_fields',{count: 8}));
				} else {
					this.model.saveLayoutConfig({
						describe_api_name: this.options.apiname,
						stage_view_infos: layoutData
					}).then(res => {
						if (res.Result.StatusCode == 0) {
							this.stepLayout = layoutData;
							this.show();
						}
					});
				}
			}).on('recoverField', () => {
				console.log()
				this.model.getLayoutConfig(this.options.apiname).then((res) => {
					if (res.Result.StatusCode == 0 && res.Value && res.Value.stage_view_infos) {
						let stepLayout = res.Value.stage_view_infos;
						let arr1 = [];
						_.each(stepLayout, a => {
							let tt = _.findWhere(layoutColumns, {data: a.api_name});
							if(tt) {
								tt.isHidden = false;
								arr1.push(tt);
							}
						})
						_.each(layoutColumns, a => {
							if(!_.findWhere(stepLayout, {api_name: a.data})) {
								a.isHidden = true;
								arr1.push(a)
							}
						})
						this.__layoutColumns = arr1;
						this.stepLayout = stepLayout;
						this.show();
					}
				});
			})
		},

		//个人布局提示语
		insertLayoutTip: function () {
			// let $warpEle = $('.setcolumn-wrap');
			// if ($warpEle.length && !$warpEle.find('.stageview-setcoumn-tip').length) {
			// 	$warpEle.find('.search').after('<div class="stageview-setcoumn-tip">' + $t('卡片上最多可以展示8个字段') + '</div>');
			// }
		},

		//阶段视图
		showStepView: function () {
			this.$batchTermFind('.term-column-btn').hide();
		},

		// 初始化阶段视图
		initStepView: function () {
			let me = this;
			if (!me.scrollBarView) {
				let wrapper = me.$('.step-wrapper');
				wrapper.html(Tpl);
				me.scrollBarView = new ScrollBar(wrapper);
			}
			me.curStage = [];
			me.show();
		},

		resize: function () {
			let me = this;
			if (me.$('.step-wrapper.scroll-content').length) {
				var height = me.$el.height();
				me.$('.nav-item-wrapper').height(height - 30);
				me.$('.new-oppo-listview').height(height - 100);
				me.scrollBarView && me.scrollBarView.updata();
			}
		},

		refresh: function() {
			this.show();
		},

		show: function () {
			let me = this;
			me.showLoading();

			me.QueryInfo = me.parseParam();

			//获取布局
			me.model.getLayoutConfig(me.options.apiname).then(function (res) {
				if (res.Result.StatusCode == 0 && res.Value && res.Value.stage_view_infos) {
					me.stepLayout = res.Value.stage_view_infos;
				}
				let search_query_info = CRM.util.parseJson(me.QueryInfo.search_query_info),
					sqi = search_query_info.filters;

				me.QueryInfo.search_query_info = JSON.stringify({
					offset: 0,
					limit: 20,
					filters: sqi.concat(me.stepConditions || [])
				});

				me.setStageList(me.processId);
			});
		},

		//获取流程对应商机阶段
		setStageList (id) {
			let me = this;
      this.PluginServiceRun('stage.stageView.render.before', {objApiName:me.options.apiname, queryInfo: me.QueryInfo, processId: id}).then(res => {
        this.pluginOppoTotalDom = res.banner
        if(this.pluginOppoTotalDom) {
          let $TotalTpl = $('.step-total-wrapper');
          $TotalTpl.append(me.pluginOppoTotalDom)
        } else {
          me.getOppoTotal(id);
        }
      //全部阶段
      if (id == 'all') {
        me.hideLoading();
        _.each(me.salesStages, function (s, index) {
          s = _.extend(s, {
            stageId: s.ItemCode,
            name: s.label,
            orderId: index
          });
        });
        me.renderNav(me.salesStages);
        me.salesStages.forEach(function (sitem) {
          if (!me[sitem.stageId + 'view']) {
            me[sitem.stageId + 'view'] = 'start';
            me.getStageOppo('', sitem, '0');
          }
        });
        return;
      } else {
        //根据流程id获取相应阶段
        me.model.getStageList(id).then(function (res) {
          me.hideLoading();
          if (res.Result.StatusCode == 0) {
            me.curStage = res.Value.stages;
            me.probabilityViewOnInstance = res.Value.probabilityViewOnInstance;
            me.renderNav(me.curStage);
            me.curStage.forEach(function (item) {
              if (!me[item.stageId + 'view']) {
                me[item.stageId + 'view'] = 'start';
                me.getStageOppo(id, item, '0');
              }
            });
          } else {
            CRM.util.alert(res.Result.FailureMessage);
          }
        });
      }
    })
		},

		//商机2.0金额计算
		getOppoTotal: function (id) {
			return this;
		},

		//渲染阶段视图整体框架
		renderNav: function (data) {
			let me = this,
				width = 240 * data.length + 'px',
				height = this.$el.height() * 1,
				$stepContainer = me.$('.step-container');

			//赢率label从描述获取
			let probabilityField=this.table.getDescribeByField('probability');
			$stepContainer.html(StepTpl({
				data: data,
				probabilityLabel:probabilityField&&probabilityField.label||$t('赢率'),
				probabilityViewOnInstance: me.probabilityViewOnInstance
			})).css('width', width);

			// let probablityLabelWidth = $stepContainer.find('.list-tit-probablity').width();
			// if (probablityLabelWidth) {
			// 	probablityLabelWidth = 25 + probablityLabelWidth + 'px';
			// 	$stepContainer.find('.list-tit .list-tit-text').css('paddingRight', probablityLabelWidth);
			// }

			me.resize();

			me.initSortTable();

		},

		//渲染每一列的数据卡片
		renderListView: function(value, stageId, offset, data) {
			let me = this,
				$Listview = me.$('.nav-item-wrapper[data-stage=' + stageId + ']'),
				$ListContent = $Listview.find('.list-content'),
				// isShowFieldname = value.listLayouts[0].is_show_fieldname,
				dataList = data;
			let fields = value.objectDescribe.fields;

			let originObjectDescribeExt = this.options.objectDescribeExt || {};
			let originExtFields = originObjectDescribeExt.fields || {};
			let originFields = this.options.fields || {};

			//布局只有“国家”字段，展示要国家省市区字段拼一起
			_.each(dataList, function (layout) {
				_.each(layout.list, function (item) {
          let mobileSummaryLayoutItem =  me.mobileSummaryLayout.find(mobileLayoutItem => {
            return mobileLayoutItem.api_name === item.apiname;
          })
          if(mobileSummaryLayoutItem !== undefined) {
						item.is_show_label = mobileSummaryLayoutItem && mobileSummaryLayoutItem.is_show_label;
					} else {
						item.is_show_label = true;
					}
          let itemDescribe = fields[item.apiname];
          let oldField = originFields[item.apiname];
					if (itemDescribe.type == 'country') {
						let areaValueList = [layout[item.apiname + '__r']];
						let areaNameList = [item.name];
						_.each(fields, function (field) {
							if (field.type == 'province') {
								areaValueList[1] = layout[field.api_name + '__r'];
								areaNameList[1] = field.label;
							} else if (field.type == 'city') {
								areaValueList[2] = layout[field.api_name + '__r'];
								areaNameList[2] = field.label;
							} else if (field.type == 'district') {
								areaValueList[3] = layout[field.api_name + '__r'];
								areaNameList[3] = field.label;
							}
						});
						item.name = areaNameList.join('/');
						item.value = layout[item.apiname] ? areaValueList.join('/') : '--';
					} else if (itemDescribe.type === 'select_one') {
						let tmp = itemDescribe.options.find(e => e.label === item.value);
						if (tmp) {
							item.value = `<span style="color: ${tmp.font_color}">${item.value}</span>`
						}
					} else if (itemDescribe.type === 'select_many') {
						if (item.value) {
							let values = item.value.split(',');
							item.value = '';
							values.forEach(e => {
								let tmp = itemDescribe.options.find(f => f.label === e);
								if (tmp) {
									item.value += `<span style="color: ${tmp.font_color}">${e}</span>,`
								} else {
									item.value += e + ','
								}
							})
							item.value = item.value.slice(0, -1);
						}
					} else if (itemDescribe.type === 'text' || itemDescribe.type === 'long_text') {
						const extField = originExtFields[itemDescribe.api_name];
						if (extField && extField.enable_multi_lang) {
							const rawData = layout._rawData || layout;
							const mlValue = rawData[itemDescribe.api_name + '__r'];
							if (mlValue && _.isString(mlValue)) {
								item.value = _.escape(mlValue).replace(/\x20/g, '&nbsp;');
							}
						}
					} else if(oldField && (oldField.font_color || oldField.is_show_thousands)) {
						if(oldField.is_show_thousands) {
							// 这里要用原值转，因为item.value是被处理过的（经过了一次format）
							const rawData = layout._rawData || layout;
							const oValue = rawData[itemDescribe.api_name];
							if (!isNaN(+oValue)) {
								item.value = CRM.util.formatNumberForRegion(oValue, {useGrouping: true});
							}
						}
						if(oldField.font_color) {
							item.value = `<span style="color:${oldField.font_color}">${item.value}</span>`;
						}
					}
					// 客户名称 880支持超出省略 底层返回没有title属性 这里自己加上
					if(item.apiname === "account_id" && item.value){
						const div = document.createElement('div');
						div.innerHTML = item.value;
						const a = div.querySelector('.j-show-lookup');
						if(a){
							const text = a.textContent;
							a.setAttribute('title', text);
						}
						item.value = div.innerHTML;
					}
				});
			});

			if (dataList.length >= 1) {
				$('.listview-el .empty', $Listview).hide();
				if (offset == 0) {
					$ListContent.html();
				}
				$ListContent.append(ListBody(_.extend({
					data: data,
					formatTime: CRM.util.formatTime,
					// isShowFieldname: isShowFieldname,
					apiName: me.options.apiname,
                    stageField: me.stageField.api_name
				}, data)));
			} else {
                $('.listview-el .empty', $Listview).show();
			}
			me.listViewComplete($Listview, value, stageId);
		},
        showExactNum(e){
            let me = this,
                sqi = JSON.parse(me.QueryInfo.search_query_info),
                field_name = me.stageField.api_name;
            let stageId = $(e.target).parents('.nav-item-wrapper').attr('data-stage');
            let stepFilters = [{
                "field_name": field_name,
                "field_values": [stageId],
                "operator": "EQ"
            }].concat(sqi.filters);

            let whatSearchParameter = {
                "whatDescribeApiName": 'StageInstanceObj',
                "filters": [{
                    "field_name": "source_workflow_id",
                    "field_values": [
                        me.processId
                    ],
                    "operator": "EQ",
                    "connector": "AND",
                    "fieldNum": 0,
                    "isObjectReference": false,
                    "isIndex": false
                }, {
                    "field_name": "state",
                    "field_values": [
                        "in_progress",
                        "pass"
                    ],
                    "operator": "IN",
                    "connector": "AND",
                    "fieldNum": 0,
                    "isObjectReference": false,
                    "isIndex": false
                }]
            }

            if (me.processId == 'all') {
                whatSearchParameter.filters.shift();
            }

            let queryInfo = _.extend({}, me.QueryInfo, {
            	find_explicit_total_num:true,
                search_query_info: JSON.stringify(_.extend(sqi, {
                    limit: "20",
                    filters: stepFilters,
                    whatSearchParameter
                }))
            });
            me.model.getDataList(queryInfo).then(function (res) {

                if (res.Result.StatusCode == 0) {
                    //重置卡片布局
                    $(e.target).parents('.nav-item-wrapper').find('.list-total').attr('data-total', res.Value.total).html(res.Value.total + $t("个"));
					$(e.target).parents('.nav-item-wrapper').find('.list-total').attr('title',res.Value.total + $t("个"));
                    $(e.target).parents('.list-exact').hide()
                } else {
                    CRM.util.alert(res.Result.FailureMessage);
                }
            });
		},
		//阶段视图渲染完成后
		listViewComplete: function($Listview, value, stageId) {
			//每一列数据总个数
      if(!this.pluginOppoStageDom) {
        if(value.total >= 1000){
          const msg = $t('约{{num}}条', {num: value.total + '+'});
          $Listview.find('.list-total').attr('title',msg);
          $Listview.find('.list-total').attr('data-total', value.total).html(msg);
                  $Listview.find('.list-exact').show()
        }else {
          $Listview.find('.list-total').attr('data-total', value.total).html(value.total + $t("个"));
                  $Listview.find('.list-exact').hide()
        }
      }
			//筛选为全部阶段时没有拖拽效果
			if (!this.processId || this.processId == 'all') {
				this.$('.oppo-item-wrapper').removeClass('j-drag-oppo-item');
			} else {
				this.$('.oppo-item-wrapper').addClass('j-drag-oppo-item');
			}

            this.$('.oppo-item-wrapper').mouseenter(function(e) {
                let $target = $(e.currentTarget);
                let $parent = $(e.currentTarget).closest('.nav-item-wrapper');
                if ($parent && $parent.length && $parent.hasClass('isTerminal')) {
                    $target.removeClass('j-drag-oppo-item');
                }
            });
            this.$('.oppo-item-wrapper').mouseleave(function(e) {
                let $target = $(e.currentTarget);
                let $parent = $(e.currentTarget).closest('.nav-item-wrapper');
                if ($parent && $parent.length && $parent.hasClass('isTerminal')) {
                    $target.addClass('j-drag-oppo-item');
                }
            });

            this[stageId + 'view'] = null;
            this.initStepScrollBar($(`.listview-el[data-stage=${stageId}]`, this.$el));
		},

		//准备参数，获取每个阶段下的全部数据
		getStageOppo: function (id, opt, offset) {
			let me = this;
      sqi = JSON.parse(me.QueryInfo.search_query_info),
      field_name = me.stageField.api_name;
        let stepFilters = [{
          "field_name": field_name,
          "field_values": [opt.stageId],
          "operator": "EQ"
        }].concat(sqi.filters);

        let whatSearchParameter = {
          "whatDescribeApiName": 'StageInstanceObj',
          "filters": [{
            "field_name": "source_workflow_id",
            "field_values": [
              me.processId
            ],
            "operator": "EQ",
            "connector": "AND",
            "fieldNum": 0,
            "isObjectReference": false,
            "isIndex": false
          }, {
            "field_name": "state",
            "field_values": [
              "in_progress",
              "pass"
            ],
            "operator": "IN",
            "connector": "AND",
            "fieldNum": 0,
            "isObjectReference": false,
            "isIndex": false
          }]
        }

        if (me.processId == 'all') {
          whatSearchParameter.filters.shift();
        }

        let queryInfo = _.extend({}, me.QueryInfo, {
          search_query_info: JSON.stringify(_.extend(sqi, {
            offset,
            limit: "20",
            filters: stepFilters,
            whatSearchParameter
          }))
        });
      $('.listview-el .loading', me.$('.nav-item-wrapper[data-stage=' + opt.stageId + ']')).show();
			let navItemDom = me.$('.nav-item-wrapper[data-stage=' + opt.stageId + '] .nav-item');
      this.PluginServiceRun('stage.stageView.card.render.before', {objApiName:me.options.apiname,
        stageId: opt.stageId, stageFieldApiName:field_name, queryInfo,stageName:opt.name,navItemDom}).then( ()=> {
        me[opt.stageId + '_ajax'] = me.model.getDataList(queryInfo).then(function (res) {
          $('.listview-el .loading', me.$('.nav-item-wrapper[data-stage=' + opt.stageId + ']')).hide();
          if(me.destroyed) return;
          if (res.Result.StatusCode == 0) {
            //重置卡片布局
            if (me.stepLayout && me.stepLayout.length) {
            me.mobileSummaryLayout = res.Value.listLayouts[0].components[0].include_fields;
              res.Value.listLayouts[0].components[0].include_fields = me.stepLayout;
            }

						CRM.util.getFormatDataByListLayout(res.Value.dataList, res.Value, function (data) {
							me.renderListView(res.Value, opt.stageId, offset, data);
							me[opt.stageId + '_ajax'] = null;
							me[opt.stageId + 'scroll'] = null;
						});
          } else {
            CRM.util.alert(res.Result.FailureMessage);
          }
        });
      })
		},
		// 初始化可拖拽商机阶段视图
		initSortTable: function () {
			let me = this;

			let container = me.$('.step-container')[0];
			let contentLists = container.getElementsByClassName("list-content");
			[].forEach.call(contentLists, function (el) {
				Sortable.create(el, {
					animation: 300,
					handle: '.j-drag-oppo-item',
					draggable: '.j-drag-oppo-item',
					group: 'list-content',
					scroll: true,
					ghostClass: 'oppo-item-ghost',
					scrollSensitivity: 200,
					onEnd: function (evt) {
						let $item = $(evt.item),
							curStageId = $item.attr('stage-id'),
							objectId = $item.attr('data-id'),
							$parent = $item.parent('.list-content'),
							stageId = $parent.attr('data-stage');
						if (curStageId !== stageId) {
							me.showLoading();
							me.model.getInstance({
								entityId: me.QueryInfo.object_describe_api_name,
								objectId,
							}).then(function (res) {
								if (res.Result.StatusCode == 0) {
									let stages = res.Value.stages;
									let toStage = _.find(stages, function (item) {
										return item.stageId == stageId;
									});
									let curStage = _.find(stages, function (item) {
										return item.isCurrent;
									});
									//当非商机2.0对象，该实例未在终结态上，
									//由于版本问题，需对比阶段是否存在、阶段顺序是否发生改变、阶段状态是否发生改变
									let isMove = false;
									if (curStage && curStage.isTerminal) {
										isMove = true;
									} else if (toStage) {
										let orderId = $parent.data('orderid');
										let status = $parent.data('status');
										if (toStage.orderId == orderId && toStage.extension && toStage.extension.status == status) {
											isMove = true;
										}
									}
									if (me.options.apiname == 'NewOpportunityObj' || isMove) {
										let workflowId = res.Value.id;
                    if(toStage?.moveInTip) {
										  me.moveToStage(workflowId, stageId, toStage.moveInTip);
                    }else{
                      CRM.util.remind($t("crm.opportunity.setting.saleaction.stagechange.tip"))
									    me.setStageList(me.processId);
                    }
									} else {
										CRM.util.alert($t('目标阶段不存在或配置了隐藏，不允许放到该阶段'));
										me.setStageList(me.processId);
									}
								} else {
									CRM.util.alert(res.Result.FailureMessage);
									me.setStageList(me.processId);
								}
							});
						}
					}
				});
				Sortable.utils.on(el, 'dragstart', function (e) {
					me.startOriginX = e.clientX;
                });
				Sortable.utils.on(el, 'dragenter', function (e) {
					let $target = $(e.target);
					if ($target.hasClass('oppo-item-wrapper') && $target.attr('draggable')) {
						me.endOriginX = e.clientX;
					}
                });
				Sortable.utils.on(el, 'dragover', function (e) {
					let $element = me.$('.step-wrapper.scroll-content');
					let maxWidth = parseInt($element[0].scrollWidth);
					let clientWidth = parseInt(e.target.clientWidth);
					let left = parseInt($element.offset().left);

                    // console.log('endOriginX:' + me.endOriginX + 'clientWidth+left:' + (clientWidth + left) + 'lastX:' + me.lastX);
					//向右滑动时
					if (me.endOriginX > me.startOriginX) {
                        if ((me.endOriginX >= clientWidth + left) && (clientWidth + left > me.lastX)) {
                            $element.scrollLeft($element[0].scrollLeft + 13);
						}
					} else {
						//向左滑动
						if ((me.endOriginX >= left) && ($element[0].scrollLeft > 0)) {
                            $element.scrollLeft($element[0].scrollLeft - 8);
						}
					}
                    me.lastX = clientWidth + left;
                });
			});
		},

		//初始化滚动条,翻页加载
		initStepScrollBar: function (item) {
			var me = this,
      $item = $(item),
      stageid = $item.attr('data-stage');
      if(!me[stageid+'scroll']){
        let scrollBar = me[stageid+'scroll'] =  new ScrollBar($item)
        scrollBar.$el.parent().css({
            zIndex: '50'
        })

        scrollBar.on('scroll', function (opts) {
            var	totalNums = $item.parents('.new-oppo-listview').siblings('.nav-item').find('.list-total').attr('data-total'),
                totalAmount = parseInt(totalNums),
                scroll = (opts.scroll + opts.visible) * 1 + 40,
                idx = parseInt($item.attr('data-idx')) + 1;

            if (me._scrollTimer) {
                clearTimeout(me._scrollTimer);
                me._scrollTimer = null;
            }
            me._scrollTimer = setTimeout(function () {
                var currentAmout = $item.find('li').length;
                if (scroll >= opts.size && currentAmout < totalAmount && !me[stageid + 'view']) {
                    var item = {
                        stageId: stageid,
                        orderId: idx
                    };
                    me[stageid + 'view'] = 'start';
                    me.getStageOppo(me.processId, item, currentAmout);
                }
            }, 100);
        });
			} else {
        me[stageid+'scroll'].updata();
			}
		},

		// 更换商机阶段
		moveToStage: function (workflowId, stageId, moveInTip='') {
			let me = this;

			let stage = _.find(me.curStage, function (item) {
				return item.stageId == stageId;
			});
			if (stage && stage.extension && stage.extension.terminal) {
				me.hideLoading();
				let confirm = CRM.util.confirm(moveInTip || $t('flow.crm.stepview.moveintip','确定跳往终结态？'), null, function () {
					confirm.hide();
					me.showLoading();
					me.moveAction(workflowId, stageId);
				});
				confirm.on('dialogCancel', function (evt) {
					confirm.hide();
					me.setStageList(me.processId);
				});
			} else {
				me.moveAction(workflowId, stageId);
			}
		},

		moveAction: function (workflowId, stageId) {
			var me = this;
			me.model.moveAjax(workflowId, stageId).then(function (res) {
				me.hideLoading();
				if (res.Result.StatusCode == 0) {
					if (res.Value.terminalTask) {
						me.afterAction({
							task: res.Value.terminalTask,
                            action: 'updateTerminalTask',
							title: $t('编辑')
						});
						return;
					} else if (res.Value.ruleMessage && res.Value.ruleMessage.conditions && res.Value.ruleMessage.conditions.length) {
                        me.showUnConditionDialog(res.Value.ruleMessage);
					} else {
						CRM.util.remind(1, $t("更换阶段成功"));
					}
				} else {
					CRM.util.alert(res.Result.FailureMessage);
				}
				me.setStageList(me.processId);
			})
		},

		afterAction: function (opts) {
            let me = this;
            require.async('paas-rocket/processor', function(Processor) {
                Processor.handleTask(opts).then(function() {
                    me.setStageList(me.processId);
                }, function() {
                    me.setStageList(me.processId);
				});
            })
		},

		excuteAfterAction: function (obj) {
			var me = this;
			me.model.excuteAfterAjax(obj).then(function (res) {
				if (res.Result.StatusCode == 0) {
					CRM.util.remind(1, $t("更换阶段成功"));
					obj.success && obj.success(res);
				} else {
					CRM.util.alert(res.Result.FailureMessage);
				}
			})
		},

        showUnConditionDialog: function(rule) {
            var me = this;
            require.async('paas-paasui/ui', function() {
				window.PaasUI.getComponent('FilterAnalyze').then((FilterAnalyze)=>{
					me.enterAnalyze = new FilterAnalyze({
						model: new Backbone.Model({
							originalConditions: rule,
							isShowMatchCondition: true
						})
					});
					CRM.util.alert(me.enterAnalyze.$el[0].outerHTML, '', {
						width: 480,
						title: $t('阶段进入条件')
					});
				})
            })
        },

		// 显示商机详情
		showOppoDetail: function (e) {
			e.stopPropagation();
			var me = this;
			var $target = $(e.target).parents('.j-show-opportunity');
			var id = $target.attr('data-id');
			var apiName = $target.attr('data-apiname');
			me._showDetail(id, apiName);
		},

		//显示客户详情
		showCustomerDetail: function (e) {
			e.stopPropagation();
			var me = this;
			var $target = $(e.target);
			var id = $target.attr('data-id');
			var apiName = $target.attr('data-apiname');
			me._showDetail(id, apiName);
		},

		_showDetail: function (id, apiname) {
			var me = this;

			if (me.detailFeatureDisabled) return;

			CRM.api.show_crm_detail({
                type:'udobj',
                data: {
                    crmId: id,
                    apiName: apiname
                },
                top: 56,
                showMask: false,
                callback: () => {
                    me.setStageList(me.processId);
                }
            });
		},

		showLoading: function () {
			this.$('.step-loading-wrapper').show();
		},

		hideLoading: function () {
			this.$('.step-loading-wrapper').hide();
		},

		destroy: function () {
			let me = this;
			if(me.destroyed) return;

			_.each(['processSelect', '_detail', 'Sortable', '_setLayout', 'scrollBarView', 'pluginService'], function (item) {
				if (me[item]) {
					me[item].destroy && me[item].destroy();
					me[item] = null;
				}
			});
			me.$batchTermFind('.term-column-btn').show();
			me.$batchTermFind('.step-filters-wrapper').remove();
			me.$layoutSet && me.$layoutSet.remove();
			me.$el.remove();
			me.destroyed = true;
		}
	});

	StepView.StepModel = StepModel;
	module.exports = StepView;
})
