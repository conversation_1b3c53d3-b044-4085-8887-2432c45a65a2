<ul>
## _.each(list, function(item) { ##
    <li data-id="{{item.objectId}}">
        <div class="left">
            <p class="f-18">{{FS.moment.unix(item.timeStamp / 1000).format('HH:mm')}}</p>
            <p class="c-gray">{{getYestodayText(item.timeStamp / 1000)}}</p>
        </div>
        <div class="right">
            <h3 class="f-18">{{item.messageTitle}}</h3>
            <p>
                ## if (item.objectId && item.entityId && item.name) { ##
                <p style="padding-bottom: 5px;">
                    ## if (item.entityName) { ##
                        <span class="c-gray">{{{{-item.entityName}}}}：</span>
                    ## } else { ##
                        <span class="c-gray">{{$t("数据名称：")}}</span>
                    ## } ##
                    <a href="javascript:;" class="show-detail" data-id="{{item.objectId}}" data-apiname="{{item.entityId}}">    {{{{-item.name}}}}
                    </a>
                </p>
                ## } ##
                <p class="c-gray">{{{{-item.messageContent}}}}</p>
            </p>
        </div>
    </li>
## }) ##
</ul>