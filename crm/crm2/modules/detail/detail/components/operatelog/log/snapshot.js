define(function(require, exports, module) {
	var Detail = require('./shotdetail');
	var util = FS.crmUtil;
	var getscopeHtml = require('../../usescope/index.js');
    var getrangeHtml = require('../../userange/index.js');
	var View = Backbone.View.extend({
		template: require('./view-html'),
		scopeTpl: require('./scope-html'),
		events: {
			'click .j-nav-item' : '_navHandle'
		},
		render: function() {
			var opts = this.options;
			if(opts.type === 'scope' || opts.type === 'range') {
                let fn = {
                    scope: getscopeHtml,
                    range: getrangeHtml
                };
                
				this.$el.html(this.scopeTpl({
					leftHtml: fn[opts.type](opts.data.old, opts.apiname, opts.fieldName),
					rightHtml: fn[opts.type](opts.data.value, opts.apiname, opts.fieldName)
				}));
				this.options.success();
				return;
			}
			this.navItems = this.parseNavItems();
			this.widgets = [];

			this.$el.html(this.template({
				navItems: this.navItems
			}))
			this.$contens = this.$('.j-content');
			this.$navs = this.$('.j-nav-item');

			this.$navs.eq(0).click();
		},

		parseNavItems: function() {
			var pageType = this.options.type === 'md' ? 'detail' : 'shot';
			var detailInfos = this.options.detailInfos;
			var dl = [];
			// 推拉单
			if (this.options.sourceId) {
				dl = _.map(this.options.sourceDetailInfos, function (a, index) {
					let detailInfo = detailInfos[index] || {};
					return {
						label: a.objectLabel,
						apiname: detailInfo.objectApiName || "",
						sourceApiName: a.objectApiName,
						type: 'renderMDDetail',
						pageType: pageType
					}
				})
			} else {
				dl = _.map(detailInfos, function (a) {
					return {
						label: a.objectLabel,
						apiname: a.objectApiName,
						type: 'renderMDDetail',
						pageType: pageType
					}
				})
			}
			

			if(pageType === 'shot') {
				dl.unshift({
					label: $t('详细信息'),
					type: 'renderDetail'
				})
			}
			return dl;
		},

		renderDetail: function($el) {
			var detail = new Detail({
				el: $el,
				logId: this.options.logId,
				apiname: this.options.apiname,
				sourceId: this.options.sourceId,
				sourceApiName: this.options.sourceApiName,
				objectId: this.options.objectId,
				setTitle: this.options.setTitle
			})

			detail.render();

			this.widgets.push(detail);
		},

		renderMDDetail: function($el, data) {
			var me = this;
			var opts = me.options;
			require.async('./mdlog', function(Log) {
				var log = new Log({
					el: $el,
					apiname: opts.apiname,
					sourceApiName: opts.sourceApiName,
					masterLogId: opts.masterLogId,
					operationalType: opts.operationalType,
					detailApiName: data.apiname,
					sourceDetailApiName: data.sourceApiName,
					pageType: data.pageType,
					objectId: opts.objectId,
					sourceId: opts.sourceId
				})
				log.render();
				me.widgets.push(log);
			})
		},

		_navHandle: function(e) {
			var $target = $(e.currentTarget);
			if($target.hasClass('.cur')) return;
			this._$target && (this._$target.removeClass('cur'), this.$contens.eq(this._$target.index()).hide());
			$target.addClass('cur');
			this._$target = $target;
			var index = $target.index()
			var data = this.navItems[index];
			var $content = this.$contens.eq(index).show();
			if(data.rendered) return;
			data.rendered = true;
			this[data.type]($content, data);
		},

		destroy: function() {
			_.each(this.widgets, function(a) {
				a.destroy && a.destroy();
			})
			this.widgets = null;
			this.stopListening(), this.undelegateEvents(), this.off();
		}
	})



	function showSnapshot(opts) {
		require.async('crm-widget/dialog/dialog', function(Dialog) {
			var dialog = new Dialog({
				classPrefix: 'crm-c-dialog crm-d-log-dialog ' + (opts.className || ''),
                title: _.escape(opts.title),
                showBtns: true,
				width: '860px',
				stopPropagation: true,
                height: (opts.type === 'scope' || opts.type === 'range') ? 'auto' : '676px',
                showScroll: false,
                btnName: {
                	cancel: $t('关闭')
                }
            });

            dialog.show();

            var view = new View(_.extend({
   				el: dialog.getContentWrap(),
   				success: function(obj) {
   					dialog.resizedialog();
   				},
   				setTitle: function(title) {
   					title && dialog.setTitle(title);
   				}
   			}, opts));

   			view.render();

			var _cancle = function() {
				view.destroy();
				dialog.destroy();
			}
			dialog.on('hide', _cancle);
			dialog.on('dialogCancel', _cancle);
			dialog.on('dialog_action', (_type, e) => {
				// 打开详情页
				if (_type == "show_detail") {
					var dataset = e.target.dataset;
					if (!dataset.id) {
						return;
					}
					CRM.api && CRM.api.show_crm_detail({
						type: 'udobj',
						data: {
							crmId: dataset.id,
							apiName: dataset.apiname
						}
					});
				}
			})
		})
	}

	return showSnapshot;
})
