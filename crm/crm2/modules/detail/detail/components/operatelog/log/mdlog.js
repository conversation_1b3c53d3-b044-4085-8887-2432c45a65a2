/**
 *@desc crm通用概要修改记录
 *author wangj
 *date 2016/07/12
 */

define(function(require, exports, module) {
    var util = FS.crmUtil;
    var Log = require('./log');
    var showGroupDetail = require('crm-modules/detail/detail/detail').components.showGroupDetail;

    return Log.extend({
        template: require('./mdlog-html'),
        contentTpl: require('./mdcontent-html'),
        events: _.extend({
            'click .j-shot-item' : '_showMDShotHandle',
            'click .j-load-more' : 'renderMore'
        }, Log.prototype.events),

        render: function() {
            util.showLoading(this.$el);
            var me = this;
            me.pageNumber = 1;
            me.options.pageSize = me.options.pageSize || 20;
            me.fetchFields(function(describe) {
                if(!describe) {
                    util.hideLoading(me.$el);
                    me.trigger('renderError');
                    return;
                }
                me.options.fields = describe.fields;
                // me.options.objectLabel = describe.display_name
                me.modifyLogDatas = [];
                require.async('crm-modules/action/field/field', function(field) {
                    me.format = field.format;
                    me.fetchModifyLog().then(_.bind(me.renderContent, me, true));
                })
            })
        },

        renderContent: function (isFirst, res) {
            if (isFirst) {
                this.$el.html(this.template());
                this.$mdlogload = this.$('.mdlog-load');
                this.$mdlogContent = this.$('.mdlog-content-wrap');
            }
            util.hideLoading(this.$el);
            var rv = res.Value;
            var pi = rv.pageInfo;
            var modifyLog = rv.modifyRecordList;
            if(!modifyLog) {
                this.trigger('renderError');
                return;
            }
            this.pageNumber++;
            if(isFirst && modifyLog.length === 0) {
                this.$el.html('<div class="crm-d-log-empty"><span></span><p>' + $t('暂无修改记录') + '</p></div>');
                return;
            }
            this.renderPage(modifyLog, isFirst);
            if(isFirst) {
                if(modifyLog && modifyLog.length==pi.pageSize ) {
                    this.$mdlogload.show();
                }
			} else if (modifyLog && modifyLog.length < pi.pageSize) {
                this.$mdlogload.addClass('load-end');
            }
        },

        renderMore: function() {
            if(this.fetchAjax) return;
            this.fetchModifyLog().then(_.bind(this.renderContent, this, false));
        },

        renderPage: function(modifyLog, isFirst) {
            this.$mdlogContent[isFirst ? 'html' : 'append'](this.contentTpl(this.parse(modifyLog)));
            this.trigger('renderComplete');
        },

        beforeParseObjectData: function(ob) {
            let util = CRM.util;
            if (this.options.detailApiName === "PricePolicyRuleObj") {
                _.each(ob.objectData, function(data) {
                    if (data['fieldApiName'] === 'rule_condition' && data.renderType === "long_text") {
                        let ov = data.oldValue['rule_condition'], nv = data.value['rule_condition'];

                        data.oldValue['rule_condition'] = ov ? util.parseCondition(ov) : ov;
                        data.value['rule_condition'] = nv ? util.parseCondition(nv) : nv;
                    }

                    if (data['fieldApiName'] === 'execution_result' && data.renderType === "long_text") {
                        let ov = data.oldValue['execution_result'], nv = data.value['execution_result'];

                        data.oldValue['execution_result'] = ov ? util.parseExecution(ov) : ov;
                        data.value['execution_result'] = nv ? util.parseExecution(nv) : nv;
                    }
                })
            } else if (this.options.detailApiName === 'RebatePolicyRuleObj') {
                // 返利政策-修改记录-从对象-解析
                ob.objectData = ob.objectData
                    .map((data) => {
                        const {oldValue, value} = data;
                        return {
                            ...data,
                            oldValue: CRM.api.rebatePolicyUtils.parseDetailFieldValue(oldValue),
                            value: CRM.api.rebatePolicyUtils.parseDetailFieldValue(value)
                        };
                    });
            }
        },

        parse: function(data) {
            var me = this;
            var mdlogList = [];
            _.each(data, function(a) {
                var ot = a.operationType;
                if(ot == '2') { //编辑
                    a.objectLabel = me.options.objectLabel;
                    me.beforeParseObjectData(a);
                    mdlogList.push(me._parseObjectData(a));
                } else {
                    mdlogList.push(me._parseObjectInfo(a));
                }
            })

            return {
                mdlogList
            }
        },

        fetchFields: function(callback) {
            var me = this;
            var apiname = me.options.sourceDetailApiName || me.options.detailApiName; // 优先取来源的布局
            me.fieldsAjax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/' + apiname +'/controller/DescribeLayout',
                data: {
                    apiname: apiname,
                    include_detail_describe: true,
                    include_ref_describe: false,
                    include_layout: false
                },
                success: function(res) {
                    if(res.Result.StatusCode === 0) {
                        callback(res.Value.objectDescribe);
                    } else {
                        callback(null);
                        util.alert(res.Result.FailureMessage || $t("暂时无法获取数据!"))
                    }
                },
                complete: function() {
                    me.fieldsAjax = null;
                }
            }, {
                errorAlertModel: 1
            })
        },

        fetchModifyLog: function(callback) {
            var me = this;
            me.fetchAjax = FS.crmUtil.FHHApi({
                url: '/EM1HNCRM/API/v1/object/modifyLog/service/getNewLogInfoListForWeb',
                data: {
                    apiName: me.options.apiname,
                    sourceApiName: me.options.sourceApiName,
                    objectId: me.options.objectId,
                    sourceId: me.options.sourceId,
                    pageSize: me.options.pageSize || 20,
                    pageNumber: me.pageNumber,
                    operationalType: me.options.operationalType,
                    masterLogId: me.options.masterLogId,
                    detailApiName: me.options.detailApiName,
                },
                complete: function() {
                    me.fetchAjax = null;
                }
            }, {
                errorAlertModel: 1
            })

            return me.fetchAjax;
        },

        _viewLogHandle: function(e) {
            var $target = $(e.currentTarget);
            var apiName = $target.data('apiname');
            var logId = $target.attr('data-logid');
            var dataId = $target.attr('data-dataid');
            var sourceId = $target.attr('data-sourcedataid');
            var sourceApiName = $target.attr('data-sourceapiname');
            if(apiName === 'SubProductCatalogObj'){
                util.getGroupInfo(dataId, function (data) {
                    if(data){
                        showGroupDetail(data);
                    }else{
                        util.alert($t('该分组已被删除'))
                    }
                });
                return
            }
            this.snapshot({
                logId: logId,
                apiname: this.options.detailApiName,
                sourceId: sourceId,
                sourceApiName: sourceApiName,
                title: $target.text(),
                className: 'crm-d-log-trans'
            });
        },

        _showMDShotHandle: function(e) {
            var $target = $(e.currentTarget);
            var obj = $target.data();
            this.snapshot({
                logId: obj.logid,
                apiname: obj.apiname,
                className: 'crm-d-log-trans',
                title: $target.text()
            });
        },

        destroy: function() {
            this.fetchAjax && this.fetchAjax.abort();
            this.fieldsAjax && this.fieldsAjax.abort();
            Log.prototype.destroy.call(this);
        }
    });
});
