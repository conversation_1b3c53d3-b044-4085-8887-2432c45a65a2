define(function(require, exports, module) {

  var Translate = Backbone.View.extend({
    initialize: function (opts) {
      this.setElement(opts.wrapper);
    },
    render: function() {
      var el = this.el;

      require.async('paas-vui/sdk', function (VuiSdk) {
      	VuiSdk.getWatermark().then(function (Obj) {
      		Obj.init(el)
      	})
      });
    }
  });

  module.exports = Translate;
});