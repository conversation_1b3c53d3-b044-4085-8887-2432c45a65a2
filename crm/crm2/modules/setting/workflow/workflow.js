/**
 * @description 工作流管理
 * <AUTHOR>
 */

define(function (require, exports, module) {

	var WorkFlow2 = Backbone.View.extend({

		initialize: function (opts) {
			var me = this;
			me.setElement(opts.wrapper);
			me.$el.addClass('crm-workProcess-table');
			const newChildElement = document.createElement('div');
			newChildElement.className = 'workProcess-manage';
      me.$el[0].appendChild(newChildElement)
		},
		render() {
			require.async(['paas-paasui/vui','paas-workprocess/sdk'], (vui,SDK) => {
				SDK.getModule('manageList').then(cmpt => {
				  new Vue({
						el: this.$el.children('.workProcess-manage')[0],
            render: h => h(cmpt.default)
					})
				})
			})
		},
	});

	module.exports = WorkFlow2;
});
