<div class="record-log">
    <div class="record-item-list r-list">
        ## if (!data.length) { ##
        <div class="tip">{{$t("暂无历史版本")}}</div>
        ## } ##
            ## _.each(data, function (obj) { ##
                <div class="o-item">
                    <div class="o-l">
                        <span class="l-tit"><span class="circle"></span><span>{{obj.date}}</span></span>
                        <span>{{obj.time}}</span>
                    </div>
                    <div class="o-r">
                        <div class="r-tit">
                            <div class="img-wrap" ##if(obj.empId > 0){##data-cardid="{{obj.empId}}"##}## >
                               ##if(obj.headImg){##
                                <img src="{{obj.headImg}}"/>
                                ##}##
                            </div>
                            <div class="o-name">{{{{-obj.name}}}}</div>
                        </div>
                        <!-- <span class="r-content">{{{{-obj.recordText}}}}</span> -->
                        <div class="revise-o-btn">
                            ## if(obj.ruleId){ ##
                                <a href="javascript:void(0);" class="none view-rule" data-ruleid="{{obj.ruleId}}">{{$t("查看流程触发条件")}}</a>
                            ## } ##
                            <a class="none view-detail" href="javascript:void(0);" data-id="{{obj.workflowId}}">{{$t("查看详情")}}</a>
                        </div>
                        <!--<a class="view-detail j-log-detail" href="javascript:void(0);" data-id="{{obj.workflowId}}">{{$t("查看详情")}}</a>-->
                        <!--<div class="j-detail-sec log-detail-con"></div>-->
                    </div>
                </div>
            ## }) ##
    </div>
    ## if(data.length){ ##
        <div class="pagination"></div>
    ## } ##
    <!-- <div class="record-log-pagination">
        <a href="javascript:void(0);" class="record-btn prev disabled">上一页</a>
        <a href="javascript:void(0);" class="record-btn next">下一页</a>
    </div> -->
</div>



