<div class="crm-d-layout">
    <div class="header d-top">
        <div class="crm-d-comp-tit">
            <span class="d-obj-icon"></span>
            <span class="obj-name">{{$t("工作流")}}</span>
            <div class="tit">
                <div title="{{{{-objname}}}}">{{{{-objname}}}}</div>
            </div>
        </div>
        <div class="operate">
            <div class="crm-d-btn-operate">
              ## if(controlStatus !== 'controlled' && showEdit){ ##
                <div class="crm-btn crm-btn-primary b-item b-edit" data-action="edit">{{$t("编辑")}}</div>
                ## } ##
                <div class="b-item b-list">
                    <span class="crm-btn show-more">...</span>
                    <!-- <span>{{$t("更多")}}</span> -->
                    <ul class="ops-list">
                        <li class="opts-list-item" data-action="toggle">{{status ? $t("停用") : $t("启用")}}</li>
                        ## if(showCopy && controlStatus !== 'controlled') {##
                        <li class="opts-list-item" data-action="copyadd">{{$t("复制并新建")}}</li>
                        ##}##
                        ## if(!status && controlStatus !== 'controlled') { ##
                            <li class="opts-list-item" data-action="delete">{{$t("删除")}}</li>
                        ## } ##
                    </ul>
                </div>
            </div>
        </div>
        <div class="d-g-btns">
            <span data-action="hide" class="h-btn" title="{{$t("关闭")}}"></span>
        </div>
    </div>
    <div class="workprocess-slide-navigation">
        <div class="nav-item-group">
            <span data-name="flow-detail" class="nav-item nav-selected">{{$t("详细信息")}}</span>
            <span data-name="flow-map" class="nav-item ">{{$t("流程配置")}}</span>
            <span data-name="flow-record" class="nav-item">{{$t("历史版本")}}</span>
        </div>
    </div>
    <div class="layout-scroll">
        <div class="d-content">
            <div class="d-container">
                <div class="crm-d-comp-board">
                    <div class="b-content">
                        <div class="flow-map" data-name="flow-map">
                            <article class="field-items b-g-clear svg"></article>
                        </div>
                        <div class="flow-detail" data-name="flow-detail">
                            <div class="sec-tit">
                                <h3><span class="icon icon-info"></span>{{$t("详细信息")}}</h3>
                            </div>
                            <div class="field-items b-g-clear">
                                <div class="crm-d-comp-infolist">
                                    ## _.each(list, function(item, index){ ##
                                    ## if(index % 2 == 0){ ##
                                    <div class="i-item b-g-clear">
                                        ## } ##

                                        <div class="{{index % 2 == 0 ? 'i-l' : 'i-r'}}">
                                            <div class="i-wrap">
                                                <span class="tit" title="{{item.name}}">{{item.name}}</span>
                                                <div class="con">{{{{-item.value}}}}</div>
                                            </div>
                                        </div>

                                        ## if(index % 2 == 1 || index == list.length - 1){ ##
                                    </div>
                                    ## } ##
                                    ## }) ##
                                    <div class="i-line"></div>
                                </div>
                            </div>
                            ## if(showRule){ ##
                                <div class="sec-tit">
                                    <h3><span class="icon icon-info"></span>{{$t("定时规则")}}</h3>
                                </div>
                                <div class="workprocess-triggerTime-list"></div>
                            ## } ##
                            <div class="sec-tit">
                                <h3><span class="icon icon-info"></span>{{$t("触发条件")}}</h3>
                            </div>
                            <div class="workprocess-trigger-list"></div>
                        </div>
                        <div class="flow-record " data-name="flow-record">
                            <div class="history-tit">
                                <h3>{{$t("历史版本")}}</h3>
                            </div>
                            <div class="record-box">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
