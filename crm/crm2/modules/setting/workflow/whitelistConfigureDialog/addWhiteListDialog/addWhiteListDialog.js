/**
 * @description 添加统计字段白名单
 * <AUTHOR>
 */

 define(function(require, exports, module) {
	var util = CRM.util,
        Dialog = require('crm-widget/dialog/dialog');
        Select = require('crm-widget/select/select'),
        Tpl = require('../template/tpl-html')

    module.exports = Dialog.extend({
    	attrs: {
            title: '',
            width: 500,
            showBtns: true,
            // showScroll:   true,
            btnName: {
                save: $t("保存"),
                cancel: $t("取消")
            },
            content:  '<div class="white-list-content"></div>',
        },

        events: {
            'click .b-g-btn-cancel':       '_closeHandle',   //取消按钮
			'click .dialog-btns .b-g-btn': 'onSave' //保存按钮
        },
    
        //初始化关联对象下拉框
        initObjectSelect: function() {
			var me = this
            const objectOptions = (this.entityIdList || []).map(item => {
                return {
                    value: item.objApiName,
                    name: item.displayName,
                }
            });
            me.objectSelect = new Select({
				$wrap:  $(this.element).find('.j-object-select'),
				multiple: 'single',  
                placeHolder: $t('请选择'), 
                options: objectOptions,
                defaultVal:[],
            });
            me.objectSelect.on('change', function(val,item) {
                me.getPara(val)
            });
		},

        //初始化统计字段下拉框
        initCountSelect:function(){
            var me = this
            me.countSelect = new Select({
				$wrap:  $(this.element).find('.j-count-select'),
				multiple: 'multiple', 
                onlyoneline: true,  
                options: [],
                defaultVal:''
            });
        },

        //从接口获取对象、统计字段数据
         getPara: function(entityId){
            var  me= this;
            util.FHHApi({
                url: '/EM1HPROCESS/ConfigAction/FieldWhiteFieldDescsForWhiteSelect',
                data: {entityId:entityId},
                success(res) {
                    if (res.Result.StatusCode == 0) {
                        const options = Object.values(res.Value).map(item => {
                            return {
                                name: item.label,
                                value: item.apiName,
                            }
                        })
                        me.countSelect.resetOptions(options)
                        const add = me.optionsData.find(item=>{
                            return item.entityId === entityId     
                        });
                        me.countSelect.setValue(add ? add.fieldApiNames : [])
                    }
                }
             })
        },
       
        show: function(data,optionsData) { 
            this.entityIdList = data;
            this.optionsData = optionsData || [];
            var me = this,
                result = Dialog.superclass.show.call(this);
                $(this.element).find('.white-list-content').html(Tpl());
            me.isLoading = false
            me.initObjectSelect();
            me.initCountSelect();
            return result;
        },
        
        //外层初始化table传进来
        initTable:function(callback){
            this._initTable = callback;
        },

        setDefaultValue(row){
            var me = this;
            me.objectSelect.setValue(row.entityId)
            me.objectSelect.disable()
            me.getPara(row.entityId)
        },
        //保存按钮
        onSave:function (entityId) {
			var me = this;
            // 判断当关联对象为空时，提示信息，不允许保存
            if(me.objectSelect.val && me.objectSelect.val.length == 0 ){
                FxUI.MessageBox.alert($t('关联对象不能为空！'),$t('提示'),{
                    confirmButtonText: $t('确定')
                })
                return false;
            }
            // 判断当统计字段为空时，提示信息，不允许保存
            if(me.objectSelect.val.length > 0 && me.countSelect.val && me.countSelect.val.length == 0 ){
                FxUI.MessageBox.alert($t('统计或引用字段不能为空！'),$t('提示'),{
                    confirmButtonText: $t('确定')
                })
                
                return false;
            }
            
            if(!me.isLoading){
                me.isLoading = true
                //增加loading
                $(me.element).find('.b-g-btn').prepend('<i class="el-icon-loading whitelist"></i>')
                util.FHHApi({
                    url: '/EM1HPROCESS/ConfigAction/FieldWhiteUpdate',
                    data: {entityId:me.objectSelect.val[0],fieldApiNames:me.countSelect.val},
                    success: function(res) {
                        if (res.Result.StatusCode == 0) {
                            me._initTable && me._initTable();
                            me.hide();
                        }else{
                            util.alert(res.Result.FailureMessage);
                        }
                    },
                    complete:function(){
                        me.isLoading = false;
                        $(me.element).find('.whitelist').remove();
                    }
                });
            }
        },

        _closeHandle: function() {
        	this.hide();
        },

        destroy:function(){
            const me = this
            me.objectSelect && me.objectSelect.destroy();
            me.countSelect && me.countSelect.destroy();
            return module.exports.superclass.destroy.call(this);
        }
    });
});