/**
 * @description 白名单配置
 * <AUTHOR>
 */

 define(function(require, exports, module) {
	var util = CRM.util,
        Table = require('crm-widget/table/table'),
        Dialog = require('crm-widget/dialog/dialog');
        AddWhiteListDialog = require('./addWhiteListDialog/addWhiteListDialog'),

    module.exports = Dialog.extend({
    	attrs: {
            title: $t("白名单配置"),
            width: 850,
            height:500,
            showBtns: true,
            showOkBtn: false,
            className:'white-list-dialog',
            btnName: {
                cancel: $t('关闭')
            }
        },

        events: {
            'click .j-addnew': 'onAdd',    //添加
            'click .b-g-btn-cancel': '_closeHandle', //关闭
        	
        },
        //增加初始化dialog  showOkBtn为false 
        initialize: function () {
            //调用父类方法中的初始化方法
            Dialog.prototype.initialize.call(this,{showOkBtn: false } );
        },

        //初始化表格
        _initTable: function() {
            var me = this;
            if (me.dt) {
                me.dt.destroy()
            }
            me.dt = new Table({
                $el: $(".dialog-con", me.element),
                className: 'crm-table white-list',
                title: $t("统计或引用字段白名单"),
                requestType: 'FHHApi',
                url: '/EM1HPROCESS/ConfigAction/FieldWhiteList',
                showSize: false,  //不显示设置图标
                showPage: true,  //分页
                pageType: 'pageNumber',
                noDataTip: $t('暂无数据'),
                height:200,
                scrollLoadY: true,
                //添加按钮
                operate: {
                    btns: [{
                        text: $t("添加"),
                        className: 'j-addnew',
                        order:1
                    }]
                },
                //表头
                columns: [{
                    data: 'displayName',
                    title: $t("对象"),
                    width: 200
                }, {
                    data: 'countNames',
                    title: $t("统计或引用字段"),
                    width: 300,
                    render: function(data, type, full) {
                        if (data instanceof Array) {
                            const _data = data.filter((item) => !!item).join(',') || '--'
                            var countNamesHtml = '<div title="' + _data + '" >' + _data + '</div>';
                            return countNamesHtml;
                        }
                    }
                },{
                    data: 'modifyTime',
                    title: $t("最后修改时间"),
                    width: 150,
                    dataType: 4, //转换时间戳
                },
                {
                    data: 'operate',
                    title: $t("操作"),
                    width: 50,
                    render: function(data, index, full) {
                        var editHtml = '<a href="javascript:;" data-operate="edit" >' + $t("编辑") + '</a>';
                        var delHtml = '<a href="javascript:;" data-operate="delete" >' + $t("删除") + '</a>';
						return editHtml + delHtml;
                    }
                }],
                
                //向表格中渲染从接口获取的数据
                formatData: function(data) {
                    let datas = data.data;
                    me.optionsData = datas;
                    _.each(datas, (item) => {
                        item.countNames = []
                        _.each(item.fieldApiNames, (item1,index) => {
                         item.countNames[index]=item.fieldDesc[item1] && item.fieldDesc[item1].label
                        });
                     });
                    return {
                        totalCount: data.totalCount,
                        data: datas
                    }
                    
                }
            });
            
            $('.dt-caption .dt-tit', me.dt.$el).append("<div style='color:#91959e;top:25px;font-size:10px' class='tit-title'></div>")
            $('.tit-title').html($t('仅配置的统计或引用字段，才能触发工作流'))
            me.dt.on('trclick', function(row, $tr, $target) {
                //编辑和删除的点击事件
                var operate = $target.data('operate');
                if (operate == 'edit') {
                    me.editHandle(row)
                } else if(operate == 'delete') {
                    me.deleteHandle(row)
                }
            }); 
        },
        // 获取全部对象
        getAllObject(){
            return new Promise((resolve) => {
                util.FHHApi({
                    url: '/EM1HPROCESS/MetadataAction/FindCustomObjs',
                    data: {
                        includeFieldsDesc: false,
                        packageName: 'CRM'
                    },
                    success: function(res) {
                        if (res.Result.StatusCode == 0) {
                            resolve(res.Value.customObjects)
                        }
                    }
                })
            })
        },
        async show(){
            result = Dialog.superclass.show.call(this);
            var me = this
            me.entityIdList = await me.getAllObject()
                me._initTable()
            return result;
        },
        
        //添加按钮
        onAdd(){
            if (!this.addWhiteListDialog) {
                this.addWhiteListDialog = new AddWhiteListDialog(
                    {
                        title:$t('添加统计或引用字段白名单')
                    }
                );
                this.addWhiteListDialog.initTable(this._initTable.bind(this))
            };
            this.addWhiteListDialog.show(this.entityIdList, this.optionsData);
        },
        //编辑
        editHandle(row){
            if (!this.editWhiteListDialog) {
                this.editWhiteListDialog = new AddWhiteListDialog(
                    {
                        title:$t('编辑统计或引用字段白名单')
                    }
                );
                this.editWhiteListDialog.initTable(this._initTable.bind(this));
            };
            this.editWhiteListDialog.show(this.entityIdList, this.optionsData);
            this.editWhiteListDialog.setDefaultValue(row);
            
        },

        //删除
        deleteHandle(row) {
			let me = this;
			FxUI.MessageBox.confirm($t("确定删除吗"), $t("提示"),{
                confirmButtonText: $t('确定'),
				cancelButtonText: $t('取消'),
            }).then(function () {
				util.FHHApi({
					url: '/EM1HPROCESS/ConfigAction/FieldWhiteDelete',
					data: { entityIds: new Array(row.entityId) },
					success(res) {
						if (res.Result.StatusCode == 0) {
							me.refreshTable();
						}
					}
				})
			});
		},

        //刷新表格
        refreshTable() {
			this._initTable()
		},
        _closeHandle: function() {
        	this.hide();
        },

        destroy:function(){
            const me = this;
            me.dt && me.dt.destroy();
            me.addWhiteListDialog && me.addWhiteListDialog.destroy();
            me.editWhiteListDialog && me.editWhiteListDialog.destroy();
            return module.exports.superclass.destroy.call(this);
        }
    });
});