.crm-s-workflow{
  .j-worklfow-export {
    background-color: #fff !important;
    color: #2a304d !important;
    border: 1px solid #c1c5ce !important;
  }
	.crm-ico-play{
		vertical-align: -2px;
        &::before{
            background:none !important;
            margin-right: 4px;
            width: 0px;
        }
	}
  .j-workflow-export {
    background-color: #fff !important;
    color: #2a304d !important;
    border: 1px solid #c1c5ce !important;
  }
    .fx-icon-others-video{
        &::before{
            color: #0C6CFF;
        }
    }
	.td-operate{
		a{
			margin-right: 5px;
		}
	}
  .crm-doclink {
    margin-left: 8px;
    margin-right: 16px;
    background: none;
    text-indent: 0px;
    vertical-align: -2px;
    &::before{
      color: #ccc;
    }
    &:hover{
      &::before {
        color:var(--color-primary05)
      }
    }
  }
  .click-disable {
		color: var(--color-neutrals07);
  }
  .definition-icon {
    height: 18px;
    color: #91959E;
    border: 1px solid #DEE1E8;
    display: inline-block;
    padding: 0px 4px;
    border-radius: 2px;
  }
  .definition-icon:nth-of-type(1){
    margin-left: 4px;
  }
  .apiName{
    &:hover{
        .fx-icon-fuzhi{
            display: inline-block;
        }
    }
    .fx-icon-fuzhi {
        display: none;
    }
  }
  .workflow-search-entity-class,.workflow-search-state-class{
    margin-right: 8px;
    width: 136px;
  }
  .workflow-search-button-class {
    color: #fff !important;
    border-radius: 0px 2px 2px 0px;
    background-color: var(--color-primary03) !important;
    padding-bottom: 8px;
  }
  .workflow-search-focus-button-class {
    color: #fff !important;
    border-radius: 0px 2px 2px 0px;
    background-color: var(--color-primary06) !important;
    padding-bottom: 8px;
  }
}

// 详情页
.crm-d-businessflow{
	.crm-d-layout{
        background-color: #f0f4f9;
        .operate {
            right: 50px!important;
            b-item {
                border-radius: 3px;
            }
        }
        .layout-scroll {
            top: 160px;
            background-color: #f0f4f9;
        }
		.d-content{
			padding: 0;
            .d-container {
                padding-left: 0;
                .crm-d-comp-board,.b-content,.flow-map{
                    height: 100%;
                }
                .flow-map {
                    background-color: var(--color-neutrals01);
                    padding-top: 8px;
                    h4 {
                        border-bottom: 1px solid transparent;
                    }
                    .field-items {
                        margin-top: -15px;
                    }
                    .svg{
                        margin-top: -9px;
                        padding: 0px;
                        overflow-x: scroll;
                        height: calc(100vh - 230px);
                      svg{
                        margin-right: 10px;
                      }
                    }
                }
                .flow-detail {
                    background-color: var(--color-neutrals01);
                    padding-bottom: 30px;
                    height: calc(100vh - 195px);
                    overflow:auto;
                    .workprocess-trigger-list,.workprocess-triggerTime-list{
                        margin-left: 20px;
                    }
                    .filter-analyze-box-filter{
                        .analyze-title{
                            margin-bottom: 8px;
                        }
                    }
                }
                .flow-record {
                    height: calc(100vh - 195px);
                    position: relative;
                    background-color: var(--color-neutrals01);
                    padding-bottom: 30px;
                    .history-tit{
                        position: relative;
                        padding-left: 16px;
                        h3{
                            display: inline-block;
                            line-height: 16px;
                            height: 16px;
                            margin-top: 16px;
                            padding-left: 8px;
                            border-left: 3px solid var(--color-primary06);
                            font-size: 14px;
                        }
                    }
                    .record-box{
                        padding: 0 24px;
                        position: absolute;
                        overflow: hidden;
                        top: 45px;
                        bottom: 0;
                        left: 0;
                        right: 0;
                    }

                }
            }
		}

	}
}
.alert-message {
    .filter-analyze-box-filter{
        padding: 0 0 0 10px;
    }
}
.rule-condition-dialog{
    .ruleContent >div:first-child:not(:last-child){
        margin-bottom: 16px;
    }
    .ruleContent{
        .filterAnalyzeContent{
            .filter-analyze-box-filter{
                .analyze-title{
                    margin-bottom: 8px;
                }
                .analyze-conditions .conditions{
                    margin-bottom: 0px!important;
                }
            }
        }
        h3{
            color: #181C25;
            line-height: 16px;
            height: 16px;
            margin-bottom: 12px;
            padding-left: 8px;
            border-left: 3px solid var(--color-primary06);
            font-size: 14px;
        }
        .title{
            margin-bottom:12px;
            color:#181C25;
        }
    }
}


