define(function (require, exports, module) {
  // 工作流定义详情
  var util = CRM.util,
    moment = util.moment,
    tpl = require("./template/page-html"),
    ScrollBar = require("base-modules/ui/scrollbar/scrollbar"),
    Slide = require("crm-modules/common/slide/slide");
    Table = require("crm-widget/table/table");

  var WorkProcess = Slide.extend({
    events: {
      "click [data-action]": "_actionHandle",
      "click .b-list": "toggleBtns",
      "mouseleave .b-list": "toggleBtns",
      // 'click .sec-tit': 'toggleContent'
      "click .nav-item": "toggleNavigation",
    },
    options: {
      showMask: false,
      width: 800,
      zIndex: 500,
      className: "crm-d-businessflow crm-d-detail crm-nd-detail workprocess-crm-detail",
      entry: "crm",
    },
    editFlowData: function (sourceWorkflowId) {
      var url = "/EM1HPROCESS/WorkflowAction/GetDefinitionDetail";
      var me = this;
      util.FHHApi(
        {
          url: url,
          data: {
            sourceWorkflowId: sourceWorkflowId,
          },
          success: function (res) {
            if (res.Result.StatusCode === 0) {
              Slide.prototype.show.apply(me);
              var data = res.Value.workflow;
              me.sourceWorkflowId = sourceWorkflowId;
              _.each(data.workflow.activities, function (item) {
                if (item.itemList && item.itemList.length) {
                  _.each(item.itemList, function (item1) {
                    if (item1.taskType == "updates") {
                      item1.updateFieldJson = JSON.parse(item1.updateFieldJson);
                    }
                  });
                }
              });
              me.data = data;
              me.render();
              me.initScroll();
            } else {
              util.alert(res.Result.FailureMessage);
            }
          },
        },
        {
          errorAlertModel: 1,
        }
      );
    },
    show: function (opt) {
      this.showCopy = opt.showCopy;
      this.showEdit = opt.showEdit;
      this.ruleList = {};
      if (this.page != 1) {
        this.page = 1;
        this.toPage && this.toPage.reset();
      }
      this.editFlowData(opt.sourceWorkflowId);
    },

    render: function () {
      var me = this;
      util.getUserGroups(function (groups) {
        me.groups = groups;
        util.getUserRoles(function (roles) {
          me.roles = roles;
          let name = me.data.nameTranslateInfo && me.data.nameTranslateInfo[Fx.userLanguage] || me.data.name;
          me.$el.html(
            tpl({
              objname: name,
              status: me.data.enable,
              list: me._getListData(),
              showCopy: me.showCopy,
              showEdit: me.showEdit,
              showRule:me.data.triggerTypes[0]===5 ? true : false,
              controlStatus: me.data.controlStatus,
            })
          );
          require.async("paas-workprocess/flowdetail", function (detailCavas) {
            me.cavas = new detailCavas({
              el: me.$el.find(".svg"),
              data: me.data,
              useToolbar: true,
            });
            me.cavas.show();
          });
          $(".flow-detail", me.$el).toggle(true);
          $(".flow-map", me.$el).toggle(false);
          $(".flow-record", me.$el).toggle(false);

          // 若是定时触发则展示定时规则
          if(me.data.triggerTypes[0] === 5 ){
            me.initTimeRule();
          }
          me.initTrigger();
        });
      });
    },
    // 历史版本表格
    renderHistoryTable() {
      let me = this;
      me.historyTable = new Table({
        className:"crm-table historyTable",
        $el: me.$el.find(".flow-record .record-box"),
        requestType: "FHHApi",
        url: "/EM1HFLOW/DefinitionConfig/GetDefinitionHistoryBySrcId",
        showPage: true,
        showMultiple: false,
        hideIconSet: true,
        search: {
          placeHolder: $t("flow.enter.version"),
          type: "workflowId",
          pos: "T",
        },
        paramFormat: function (param) {
          param = Object.assign(param, {
            type: "workflow",
            sourceWorkflowId: me.data.sourceWorkflowId,
          });
          return param;
        },
        formatData: function (data) {
          return {
            totalCount: data.total,
            data: data.data,
          };
        },
        columns: [
          {
            data: "workflowId",
            title: $t("版本号"),
            width: 200,
          },
          {
            data: "modifyTime",
            title: $t("修改时间"),
            width: 200,
            dataType: 4,
          },
          {
            data: "modifier",
            title: $t("修改人"),
            width: 160,
            render: function (data, type, full) {
              return FS.contacts.getEmployeeById(data,{includeStop:true,includeStopByRequest:true})
               ? FS.contacts.getEmployeeById(data,{includeStop:true,includeStopByRequest:true}).name : "--";
            },
          },
          {
            data: null,
            lastFixed: true,
            title: $t("操作"),
            width: 180,
            render: function (data, type, full, helper, index) {
              let btn = '<a class="view-detail" href="javascript:void(0)" data-id="' + full.workflowId +'">' + $t("流程图") + "</a>&nbsp;";
                btn += full.ruleId? '<a href="javascript:void(0)" class="view-rule" data-ruleid="' + full.ruleId + '">' + $t("触发规则") + "</a>" : "";
              return btn;
            },
          },
        ],
      });
      me.historyTable.on("trclick", (data, tr, target) => {
        if (target.hasClass("view-detail")) {
          me.rendLogDetail(target);
        } else if (target.hasClass("view-rule")) {
          me.showRule(target);
        }
      });
      me.historyTable._search.on('search', function () {
        window.Fx && window.Fx.log('paas-flow-searchversion','cl',{
          module:'workflow',
        })
      });
    },
    initTrigger: function () {
      var that = this;
      that.propertyView && that.propertyView.destroy();
      require.async("paas-paasui/ui", function () {
        window.PaasUI.getComponent("FilterAnalyze").then((FilterAnalyze) => {
          that.propertyView = new FilterAnalyze({
            model: new Backbone.Model({
              fromApp: "workprocess",
              fromModule: "filter",
              originalConditions: that.data.rule,
              apiName: that.data.entityId,
              forDetail: true,
              triggerNames: that.data.triggerNames,
            }),
          });
          that.$el.find(".workprocess-trigger-list").empty().append(that.propertyView.$el);
        });
      });
    },
    // 定时规则
    initTimeRule(){
      let me = this;
      me.timeRuleView && me.timeRuleView.destroy();
      require.async('paas-workprocess/sdk', function (sdk) {
        sdk.getModule('timeTrigger').then((timeTrigger) => {
          me.timeRuleView  = timeTrigger.default.render({
            el:me.$el.find(".workprocess-triggerTime-list")[0],
            fromApp: "workprocess",
            ruleContent: me.data.rule.quartzRule,
            apiName: me.data.entityId,
          });
        })
      });
    },
    showRule: function (e) {
      var me = this;
      var ruleId = e.data("ruleid");
      if (me.ruleList[ruleId]) {
        me.showUnConditionDialog(me.ruleList[ruleId], me.data.entityId);
        return false;
      }
      util.FHHApi(
        {
          url: "/EM1AFLOW/PaaS/GetHistoryRule",
          data: {
            flowType: "workflow",
            ruleId: ruleId,
          },
          success: function (res) {
            if (res.Value.rule) {
              me.ruleList[ruleId] = res.Value.rule;
              me.showUnConditionDialog(me.ruleList[ruleId], me.data.entityId);
            }
          },
        },
        {
          errorAlertModel: 1,
        }
      );
    },
    showUnConditionDialog: function (rule, apiName) {
      let that = this;
      let triggerNames = that.data.triggerNames;
      return FxUI.create({
        template: `<fx-dialog
						:visible.sync="dialogVisible"
            v-if="dialogVisible"
						:append-to-body="true"
						size="small"
						custom-class="rule-condition-dialog"
						:title="$t('流程触发规则')"
						@closed="handleClose">
              <div class="ruleContent">
                <div class="timeRuleContent" v-if="timeRule">
                  <h3>${$t("定时规则")}</h3>
                  <div class="content"></div>
                </div>
                <div class="filterAnalyzeContent" v-if="filterAnalyze">
                  <h3>${$t("触发条件")}</h3>
                  <div class="content"></div>
                </div>
              </div>
						</fx-dialog>`,
        data() {
          return {
            dialogVisible:true,
            timeRule:false,
            filterAnalyze:false,
          }
        },
        mounted() {
          if(rule.conditions && rule.conditions.length > 0){
            this.filterAnalyze = true;
            this.renderFilterContent();
          }
          if(rule.quartzRule && Object.keys(rule.quartzRule).length > 0){
            this.timeRule = true,
            this.renderTimeRuleContent();
          }
        },
        methods: {
          renderFilterContent(){
            let me = this;
            require.async("paas-paasui/ui", function () {
              window.PaasUI.getComponent("FilterAnalyze").then((FilterAnalyze) => {
                me.logAnalyze = new FilterAnalyze({
                  model: new Backbone.Model({
                    apiName: apiName,
                    fromApp: "workprocess",
                    fromModule: "filter",
                    originalConditions: rule,
                    forDetail: true,
                    triggerNames,
                  }),
                });
                $('.filterAnalyzeContent .content').append(me.logAnalyze.$el)
              });
            });
          },
          renderTimeRuleContent(){
            let me = this;
            require.async('paas-workprocess/sdk', function (sdk) {
              sdk.getModule('timeTrigger').then((timeTrigger) => {
                me.timeRuleView  = timeTrigger.default.render({
                  fromApp: "workprocess",
                  ruleContent: rule.quartzRule,
                  apiName:apiName,
                });
                $('.timeRuleContent .content').append(me.timeRuleView.$el)
              })
            });
          }
        },
      })
    },
    //获取列表数据
    _getListData: function () {
      var me = this;
      var data = this.data;

      function getTime(data) {
        return moment(data).format("YYYY-MM-DD HH:mm") || "--";
      }

      function getPerson(data) {
        var emp = data && util.getEmployeeById(data);
        return (emp ? emp.fullName : "--") || "--";
      }

      function getScope(data) {
        var scope = [];
        _.each(data.rangeCircleIds || [], function (id) {
          var cir = util.getCircleById(id);
          scope.push(cir ? cir.name : "--");
        });
        _.each(data.rangeEmployeeIds || [], function (id) {
          var emp = util.getEmployeeById(id);
          scope.push(emp ? emp.fullName : "--");
        });
        _.each(data.rangeGroupIds || [], function (id) {
          var group = _.findWhere(me.groups, {
            id: id,
          });
          scope.push(group ? group.name : "--");
        });
        _.each(data.rangeRoleIds || [], function (id) {
          var role = _.findWhere(me.roles, {
            id: id,
          });
          scope.push(role ? role.name : "--");
        });
        return scope;
      }

      var triggerNames;
      if (data.triggerNames instanceof Array) {
        triggerNames = data.triggerNames.join(",") || "--";
      } else {
        triggerNames = data.triggerNames || "--";
      }
      let name = me.data.nameTranslateInfo && me.data.nameTranslateInfo[Fx.userLanguage] || me.data.name;
      let description = me.data.descTranslateInfo && me.data.descTranslateInfo[Fx.userLanguage] || me.data.description;

      return [
        {
          name: $t("工作流名称"),
          value: name || CRM.config.TEXT_DEFAULT,
        },
        {
          name: $t("API名称"),
          value: data.sourceWorkflowId || CRM.config.TEXT_DEFAULT,
        },
        {
          name: $t("描述"),
          value: description || CRM.config.TEXT_DEFAULT,
        },
        {
          name: $t("状态"),
          value: data.enable ? $t("启用") : $t("停用"),
        },
        {
          name: $t("关联对象"),
          value: data.entityName || CRM.config.TEXT_DEFAULT,
        },
        {
          name: $t("触发动作"),
          value: triggerNames || CRM.config.TEXT_DEFAULT,
        },
        {
          name: $t("创建时间"),
          value: getTime(data.createTime) || CRM.config.TEXT_DEFAULT,
        },
        {
          name: $t("最后修改人"),
          value: getPerson(data.modifier) || CRM.config.TEXT_DEFAULT,
        },
        {
          name: $t("最后修改时间"),
          value: getTime(data.modifyTime) || CRM.config.TEXT_DEFAULT,
        },
      ];
    },
    //执行动作
    _actionHandle: function (e) {
      var me = this,
        param,
        data = me.data,
        $target = $(e.currentTarget),
        action = $target.data("action");
      if (action == "hide") {
        this.hide();
        return;
      } else if (action == "toggle") {
        action = data.enable ? "stop" : "start";
      }
      me.createAction(function (act) {
        switch (action) {
          case "edit":
          case "copyadd":
          case "delete":
            param = data.sourceWorkflowId;
            break;
          case "stop":
          case "start":
            param = data.sourceWorkflowId;
            break;
        }
        act[action] && act[action](param);
      });
    },
    _fetchLogDetail: function (cb) {
      var me = this;
      util.FHHApi(
        {
          url: "/EM1HPROCESS/WorkflowAction/GetDefinitionByWorkflowId",
          data: {
            workflowId: me.workflowId,
          },
          success: function (res) {
            if (res.Result.StatusCode === 0) {
              var data = res.Value.workflow;
              _.each(data.workflow.activities, function (item) {
                if (item.itemList && item.itemList.length) {
                  _.each(item.itemList, function (item1) {
                    if (item1.taskType == "updates") {
                      item1.updateFieldJson = JSON.parse(item1.updateFieldJson);
                    }
                  });
                }
              });
              cb && cb(data);
            } else {
              util.alert(res.Result.FailureMessage);
            }
          },
        },
        {
          errorAlertModel: 1,
        }
      );
    },
    rendLogDetail: function (e) {
      var me = this;
      me.workflowId = e.attr("data-id");
      me._fetchLogDetail(function (data) {
        require.async("paas-workprocess/flowdetail", function (detailCavas) {
          me.svgfull && me.svgfull.destroy();
          me.svgfull = new detailCavas({
            data: data,
            fullScreen: true,
            useToolbar: true,
          });
          me.svgfull.show();
        });
      });
    },
    //创建动作
    createAction: function (callback) {
      var me = this;
      require.async("crm-modules/action/workprocess/workprocess", function (Action) {
        if (!me.action) {
          me.action = new Action();
          me.action.on("refresh", function (type) {
            me.hide();
            me.trigger("refresh", type);
          });
          me.action.on("detailDestroy", function () {
            me.trigger("detailDestroy");
          });
        }
        callback && callback(me.action);
      });
    },

    //切换按钮列表
    toggleBtns: function (e) {
      if (e.type == "click") {
        $(e.currentTarget).toggleClass("active");
      } else {
        $(e.currentTarget).removeClass("active");
      }
    },

    toggleNavigation: function (e) {
      e.preventDefault();
      var $target = $(e.currentTarget);
      var $svg = $(".flow-map", this.$el);
      var $detail = $(".flow-detail", this.$el);
      var $record = $(".flow-record", this.$el);
      if ($target.hasClass("nav-selected")) {
        return;
      }
      $target.addClass("nav-selected").siblings().removeClass("nav-selected");
      var navType = $target.attr("data-name");
      $svg.toggle(navType === $svg.attr("data-name"));
      $detail.toggle(navType === $detail.attr("data-name"));
      if (navType === $record.attr("data-name")) {
          $record.toggle(true);
          this.renderHistoryTable();
      } else {
        $record.toggle(false);
      }
    },

    toggleContent: function (e) {
      var $target = $(e.currentTarget);
      var $icon = $("h4 span", $target);
      $icon.toggleClass("icon-arrow-b icon-arrow-r");
      if ($icon.hasClass("icon-arrow-r")) {
        $target.next().slideUp();
      } else {
        $target.next().slideDown();
      }
    },

    initScroll: function () {
      this.scroll && this.scroll.destroy && this.scroll.destroy();
      this.scroll = new ScrollBar($(".layout-scroll", this.$el));
    },

    //销毁
    destroy: function () {
      var me = this;
      me.action && me.action.destroy && me.action.destroy();
      me.scroll && me.scroll.destroy && me.scroll.destroy();
      me.record && me.record.destroy && me.record.destroy();
      me.historyTable && me.historyTable.destroy();
      me.record = me.action = me.scroll = null;
      Slide.prototype.destroy.call(me);
    },
  });

  module.exports = WorkProcess;
});
