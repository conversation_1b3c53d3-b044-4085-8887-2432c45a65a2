/*
 * @desc 流程操作
 * by wujing
 */
define(function(require, exports, module) {
    var util = CRM.util;
    var flowSetting = require('paas-workprocess/flowsetting');

    var WorkProcessAction = function(opts) {
      this.manageScope = opts.manageScope
    }

    _.extend(WorkProcessAction.prototype, {

        constructor: WorkProcessAction,
        //新建
        add: function() {
            var me = this;
            if (!me._onlyonce) {
                me._onlyonce = true;
                me._checkQuota(function(isAllow, data) {
                    me._onlyonce = false;
                    if (isAllow) {
                        require.async('paas-workprocess/sdk', function(SDK) {
                            me.flowdetail && me.flowdetail.destroy();
							me.flowdetail = new SDK({
								quota: data
							});
                            me.flowdetail.on('refresh', function(opts) {
                              if(me.manageScope.hasOwnProperty(opts.entityId)) {
                                me.trigger('refresh');
                              } else {
                                me.trigger('refreshManageScope');
                              }
                            });
                        });
                    } else {
                        // 可用配额不足，请联系纷享客服购买更高版本或增购资源包: web.paas.flow.workprocess_add_allow_able_label
                        // 说明：增购定时触发能力时，若工作流配额为0，需要一并增购: web.paas.flow.workprocess_add_allow_able_tip
                        let $message = `
                        <p>${$t('web.paas.flow.workprocess_add_allow_able_label')}</p>
                        <p style="color: #91959E;">${ $t('web.paas.flow.workprocess_add_allow_able_tip') }</p>`
                        FxUI.MessageBox.confirm($message
                        ,$t('提示'), {
                            confirmButtonText: $t('确定'),
                            showCancelButton:false,
                            dangerouslyUseHTMLString:true
                        })
                       FS.MEDIATOR.trigger('paas.workprocessoperate.load');
                    }
                });
            }
        },
        //复制并新建
        copyadd: function(id) {
            var that = this;
            this._checkQuota(function(isAllow, data) {
                if (!isAllow) {
                    let $message = `
                        <p>${$t('web.paas.flow.workprocess_add_allow_able_label')}</p>
                        <p style="color: #91959E;">${ $t('web.paas.flow.workprocess_add_allow_able_tip') }</p>`
                        FxUI.MessageBox.confirm($message
                        ,$t('提示'), {
                            confirmButtonText: $t('确定'),
                            showCancelButton:false,
                            dangerouslyUseHTMLString:true
                        })
                    FS.MEDIATOR.trigger('paas.workprocessoperate.load');
                    return;
                }
                if (!id) {
                    return;
                }
				var me = that;
                require.async('paas-workprocess/sdk', function(SDK) {
                    me.flowdetail && me.flowdetail.destroy();
                    me.flowdetail = new SDK({
						isCopy: true,
                        sourceWorkflowId: id
                    });
                    me.flowdetail.on('refresh', function() {
                        me.trigger('refresh');
                    });
                });
                // if (useNewFlowSdk === true) {
                //     require.async('paas-workprocess/sdk', function(SDK) {
                //         me.flowdetail && me.flowdetail.destroy();
                //         me.flowdetail = new SDK({
                //             sourceWorkflowId: id
                //         });
                //         me.flowdetail.on('refresh', function() {
                //             me.trigger('refresh');
                //         });
                //     });
                // } else {
                //     me.flowdetail = new flowSetting();
                //     me.flowdetail.show(id, true);
                //     me.flowdetail.on('refresh', function() {
                //         that.trigger('refresh');
                //     });
                // }

                that.trigger('detailDestroy');
            });
        },

        //编辑
        edit: function(id) {
            if (!id) {
                return;
            }
            var me = this;
            me.trigger('detailDestroy');
            require.async('paas-workprocess/sdk', function(SDK) {
                me.flowdetail && me.flowdetail.destroy();
                me.flowdetail = new SDK({
                    sourceWorkflowId: id
                });
                me.flowdetail.on('refresh', function() {
                    me.trigger('refresh');
                });
            });
            // if (useNewFlowSdk === true) {
            //     require.async('paas-workprocess/sdk', function(SDK) {
            //         me.flowdetail && me.flowdetail.destroy();
            //         me.flowdetail = new SDK({
            //             sourceWorkflowId: id
            //         });
            //         me.flowdetail.on('refresh', function() {
            //             me.trigger('refresh');
            //         });
            //     });
            // } else {
            //     me.flowdetail = new flowSetting();
            //     me.flowdetail.show(id);
            //     me.flowdetail.on('refresh', function() {
            //         me.trigger('refresh');
            //     });
            // }
        },
        /*
         * @desc 删除
         * @param {array} id 要删除的ID数组
         */
        delete: function(id) {
            var me = this;
            var confirm = util.confirm($t("确认删除该流程"), $t("删除流程"), function() {
                util.FHHApi({
                    url: '/EM1HPROCESS/WorkflowAction/DeleteDefinition',
                    data: {
                        sourceWorkflowId: id
                    },
                    success: function(data) {
                        if (data.Result.StatusCode == 0) {
                            if (data.Value.result) {
                                util.remind(1, $t("操作成功"));
                                me.trigger('refresh', 'delete');
                            } else {
                                util.alert($t("删除失败"), null, {
                                    title: $t("删除流程")
                                });
                            }
                            confirm.hide();
                            return;
                        }
                        confirm.hide();
                        util.alert(data.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1,
                    submitSelector: $('.b-g-btn', confirm.element)
                });
            });
            FS.MEDIATOR.trigger('paas.workprocessoperate.load');
            util.uploadLog('s-Workflow', 'List', {
                operationId: 'DeleteConfirm',
                eventType: 'cl'
            });
        },

        //启用
        start: function(id) {
            var me = this;
            util.FHHApi({
                url: '/EM1HPROCESS/WorkflowAction/EnableDefinition',
                data: {
                    sourceWorkflowId: id,
                    enabled: true
                },
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
                        util.remind(1, $t("操作成功"));
                        me.trigger('refresh', 'startusing');
                        return;
                    }
                    util.alert(data.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1
            });
            util.uploadLog('s-Workflow', 'List', {
                operationId: 'ActiveConfirm',
                eventType: 'cl'
            });
        },

        //停用
        stop: function(id) {
            let me = this;
            let confirm = util.confirm($t("确定要停用这个流程吗？"), $t("停用流程"), function() {
                util.FHHApi({
                    url: '/EM1HPROCESS/WorkflowAction/EnableDefinition',
                    data: {
                        sourceWorkflowId: id,
                        enabled: false
                    },
                    success: function(data) {
                        if (data.Result.StatusCode == 0) {
                            me.trigger('refresh', 'blockup');
                            util.remind(1, $t("操作成功"));
                            confirm.hide();
                            return;
                        }
                        confirm.hide();
                        util.alert(data.Result.FailureMessage);
                    }
                }, {
                    errorAlertModel: 1,
                    submitSelector: $('.b-g-btn', confirm.element)
                });
            });
            FS.MEDIATOR.trigger('paas.workprocessoperate.load');
            util.uploadLog('s-Workflow', 'List', {
                operationId: 'Deactivate',
                eventType: 'cl'
            });
        },

        //获取流程信息
        _getDefinition: function(id, callback) {
            util.FHHApi({
                url: '/EM1HPROCESS/WorkflowAction/GetDefinitionDetail',
                data: {
                    sourceWorkflowId: id
                },
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
                        callback && callback(data.Value.workflow);
                    }
                }
            });
        },

        _checkQuota: function(callback) {
            var me = this;
            util.FHHApi({
                url: '/EM1HPROCESS/WorkflowAction/GetLicenseInfo',
                success: function(res) {
                    if (res.Result.StatusCode == 0) {
                        callback && callback(res.Value.allowable, res.Value);
                        return;
                    }
                    callback && callback(false);
                }
            });
        },

        //销毁
        destroy: function() {
            var me = this;
            _.each(['selectTpl', 'editTpl', 'editflow'], function(item) {
                me[item] && me[item].destroy && me[item].destroy();
            });
            this.off();
        }

    }, Backbone.Events);

    module.exports = WorkProcessAction;
});
