/*
 * @Descripttion: 售中交易规则管理-交易相关的单据
 * @Author: chaoxin
 * @Date: 2022-12-12 18:13:05
 * @LastEditors: chaoxin
 * @LastEditTime: 2024-12-12 11:26:51
 */

define(function (require, exports, module) {
    const { Base } = require('../promotionrebate/promotionrebate');
    const config = require('./common/config');
    const { CONFIG_DATA, KEY_CONFIG } = config;

    module.exports = Base.extend({
        getConfigData() {
            return CONFIG_DATA;
        },

        getConfigKeyData() {
            return KEY_CONFIG;
        },

        async beforeSetConfig(key, value) {
            const param = await Base.prototype.beforeSetConfig.apply(this, arguments);
            if (key === 'sale_contract') {
                const editLayoutStatus = await CRM.util.getEditLayoutStatus('SalesOrderObj');
                if (!editLayoutStatus) throw new Error($t('请先开启{{apiName}}新建布局', { apiName: CRM.config.objDes['SalesOrderObj'.toLowerCase()].name }));
                param.confirmInfo = $t('确定要开启销售合同吗')
            } else if (key === 'manual_gift') {
                if (value == '1') {
                    if (CRM._cache.promotionStatus) {
                        throw new Error($t('已开启促销，无法开启临时赠品'));
                    } else {
                        param.confirmInfo = $t('确定要开启临时赠品吗')
                    }
                } else {
                    throw new Error($t('企业已开通临时赠品不允许关闭'));
                }
            } else if (key === 'new_invoice' && value === '1') {
                const oCurValue = this.getOKeyValues(key);
                if (oCurValue == '2') {
                    throw new Error($t('正在开启中，请耐心等待'));
                }
            } else if (["order_close_status", "delivery_status", "accounts_receivable_status"].includes(key)) {
                this.handleOrderCloseConfig(param, key, value);
            }
            return param;
        },

        //订单行关闭配置
        handleOrderCloseConfig(param, key, value) {
            const oriDataStr = this.getOKeyValues("order_close"),
                oriData = JSON.parse(oriDataStr);

            if (key == "order_close_status") {
                oriData.status = value;
            } else {
                oriData[key] = value;
            }
            if (oriData.status == "1") {
                oriData.delivery_status = "1"
            }
            param.data = {
                key: "order_close",
                value: JSON.stringify(oriData)
            }
        },

        transValue(key, value, toServer) {
            // 整单折扣与产品
            if (key === '16') {
                if (toServer) {
                    // '1' ==> '0,1,0' || '2' ==> '0,0,1'
                    const _value = Array(3).fill('0');
                    _value.splice(Number(value), 1, 1);
                    return _value.join(',');
                }
                // '0,1,0' ==> '1' || '0,0,1' ==> '1'
                return value.split(',').findIndex((v) => v === '1') + '';
            } else if (key === 'invoice_mode') {
                if (toServer) {
                    return value ? 'sales_order_product' : 'normal';
                }
                return value === 'sales_order_product';
            } else if (key === 'mobile_bottom_summary_setting') {
                const options = this.getConfigKeyDataByKey(key).options.map((opt) => opt.value);
                if (!value) return value;
                if (toServer) {
                    const _value = {};
                    options.forEach((opt, i) => {
                        _value[opt] = value[i] ? '1' : '0';
                    })
                    return JSON.stringify(_value);
                }
                const _value = JSON.parse(value);
                return options.map((opt) => _value[opt] === '1');
            } else if (key === 'input_display_fields') {
                return !value ? value : (
                    toServer ? JSON.stringify(value) : JSON.parse(value)
                )
            }
            return value;
        },

        // 开关切换钩子
        handleModuleItemChange(opts) {
            const { key, value, values } = opts;
            if (key === 'mobile_bottom_summary_setting') {
                return {
                    ...opts,
                    value: values,
                }
            }
            return opts;
        },

        // 自定义config请求
        setConfig(param) {
            const key = this.currentChangeKey;
            // 测试代码
            // else if (key === 'invoice_order_binding_status') {
            //     return new Promise((resolve) => {
            //         setTimeout(() => {
            //             resolve();
            //         }, 1000)
            //     })
            // }
            return Base.prototype.setConfig.apply(this, arguments);
        },

        afterSetConfig({update}) {
            const key = this.currentChangeKey;
            if (key === 'new_invoice') {
                // 开启模式二埋点
                CRM.util.sendLog('setting', 'invoice', {
                    operationId: 'openMode2'
                });
            } else if (key === 'invoice_mode') {
                // 开启模式三埋点
                CRM.util.sendLog('setting', 'invoice', {
                    operationId: 'openMode3'
                })
            } else if (key === 'invoice_order_binding_status') {
                // 开票与订单解绑
                CRM.util.sendLog('setting', 'invoice', {
                    operationId: 'invoice_order_binding_status'
                })
            } else if (key === 'invoice_support_negative_and_zero') {
                // 开票支持负数
                CRM.util.sendLog('setting', 'invoice', {
                    operationId: 'invoice_support_negative_and_zero'
                })
            } else if (key === 'invoice_lines_multi_source') {
                update(['invoice_lines_mapping_rule', 'invoice_lines_required']);
            } else if (key === 'is_open_additional_contract') {
                // 开关变更，更新相关开关
                update(['sale_contract_record_type_mapping']);
            }
        },

        getConfig(keys, isUpdate) {
            // 默认增量更新是标准config，如果涉及特殊开关(getChooseSPUConfig\getOrderCloseOptions)再单独处理
            if (isUpdate) {
                return this.getConfigValues(keys)
            }
            return Promise.all([
                this.getConfigValues(keys),
                this.getChooseSPUConfig(),
                this.getOrderCloseOptions()
            ]).then(([keyValues, chooseSPUValue, orderCloseOptions]) => {
                this.parseOrderCloseConfig(keyValues, orderCloseOptions);
                return keyValues.concat([{ key: 'choose_spu', value: chooseSPUValue }])
            })
        },

        //获取订单关闭配置
        getOrderCloseOptions() {
            const keys = ["accounts_receivable_status"];
            return new Promise((resolve, reject) => {
                CRM.util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/accounts_receivable/service/get_config',
                    data: { keys },
                    success: function (res) {
                        let list = [];
                        if (res?.Result?.StatusCode == 0) {
                            list = res.Value?.values || []
                        }
                        resolve(list);
                    }
                }, {
                    errorAlertModel: 1
                });
            });
        },

        //格式化订单关闭配置
        parseOrderCloseConfig(keyValues, orderCloseOptions = []) {
            const item = keyValues.find(item => item.key == "order_close");
            if (item) {
                const data = JSON.parse(item.value);
                Object.entries(data).forEach(([key, value]) => {
                    if (key !== "status") {
                        keyValues.push({
                            key: key,
                            value: value
                        })
                    } else {
                        keyValues.push({
                            key: "order_close_status",
                            value: value
                        })
                    }
                })
            }
            //根据orderCloseOptions设置订单关闭其他选项
            const accountsReceivable = (orderCloseOptions || []).find(o => o.key == "accounts_receivable_status")?.value == "2";
            if (accountsReceivable) {
                const businessBills = (CONFIG_DATA || []).find(c => c.moduleId === "businessBills");
                const salesOrderModule = businessBills?.moduleList?.find(m => m.moduleId === "SalesOrder");
                const orderCloseStatus = salesOrderModule?.moduleList?.find(s => s.key === "order_close_status");
                const orderClose = orderCloseStatus?.children?.find(c => c._id === "order_close");

                if (orderClose) {
                    orderClose.options = orderClose.options ||[];
                    const item = orderClose.options.find(o=>o.key=="accounts_receivable_status");
                    if(!item){
                        orderClose.options.push({
                            key: "accounts_receivable_status",
                            label: $t("crm.accounts_receivable_status")
                        })
                    }
                }
            }
        },

        // 移动端商品设置样式开关，依赖项
        getChooseSPUConfig() {
            return this.fetch({ url: '/EM1HNCRM/API/v1/object/spu_sku_choose/service/choose_spu' }).then((result) => result.Value.result === 'true');
        },

        // 开启入账到客户账户
        async setPaymentEnterAccountConfig() {
            const me = this;
            if (this.getKeyValues('is_customer_account_enable') !== '2') {
                throw new Error($t('该租户未启用客户账户模块请联系管理员开通'));
            }
            await me.confirm($t('启用回款入账将在对象上新增等字段并预设按钮确定要启用吗'));
            await me.fetch({
                url: '/EM1HNCRM/API/v1/object/fund_account/service/payment_enter_account_init'
            })
        },
        // 开启线上支付能力
        async setPaymentPayEnableConfig() {
            const me = this;
            // 查询是否绑定了支付宝或微信账号
            const onlineAccountList = await me.fetch({
                url: '/EM1HNCRM/API/v1/object/payment_pay/service/query_valid_isv'
            }).then((res) => res.Value.valid_isv_list);
            if (onlineAccountList && onlineAccountList.length) {
                await me.confirm($t('确定要开启显示支付能力吗'));
                await me.fetch({
                    url: '/EM1HNCRM/API/v1/object/payment_pay/service/enable_payment_pay'
                });
            } else {
                await me.confirm(
                    $t('企业钱包暂未绑定支付宝或微信账号请先绑定后在启用'),
                    {
                        btnLabel: {
                            confirm: $t("现在绑定"),
                            cancel: $t("知道了")
                        }
                    }
                );
                // 跳转至企业钱包页面
                window.location.hash = '#app/entwallet/wallet';
                throw new Error();
            }
        },

        async setClaimUponReceiptOfPayment() {
            await this.fetch({
                url: '/EM1HNCRM/API/v1/object/payment_claim/service/claim_upon_receipt_of_payment',
            });
        }
    });

    module.exports.config = config;
});
