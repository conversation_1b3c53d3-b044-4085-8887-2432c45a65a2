/**
 * wiki https://wiki.firstshare.cn/pages/viewpage.action?pageId=*********
 * date ********
 */
define(function(require, exports, module) {
    exports.presetObjectBlackList = {
        sourceObjectBlackList: [
            'ForecastRuleObj',
            'ForecastTaskObj',
            'OperationsStrategyObj',
            'SubAccountTreeRelationLogObj',
            'AccountTreeRelationObj',
            'BusinessRiskInformationObj',
            'EnterpriseRiskObj',
            'AccountRiskPortraitRecordObj',
            'LeadsFlowRecordObj',
            'LeadsTransferLogObj',
            'BehaviorRecordObj',
            'BehaviorIntegralDetailObj',
            'HighSeasObj',
            'LeadsPoolObj',
            'ActivityQuestionObj',
            'InteractionStrategyObj',
            'InteractionStrategyDetailObj',
            'ActivityMeetingSummaryObj',
            'TermBankDetailObj',
            'PartnerContactRelationshipObj',
            'PartnerDepartmentObj',
            'PartnerProvisionObj',
            'PartnerAgreementObj',
            'PartnerAgreementDetailObj',
            'AgreementStatusRecordObj',
    
            'GoalValueObj',
            'GoalRuleObj',
            'GoalRuleApplyCircleObj',
            'EmployeeLoginUsageObj',
            'EmployeeObjectUsageObj',
            'EnterpriseRelationObj',
            'ErDepartmentObj',
            'PublicEmployeeObj',
            'ErBindThirdAccountObj',
    
            'AccountsPayableNoteObj',
            'AccountsPayableDetailObj',
            'FMCGSerialNumberObj',
            'FMCGSerialNumberStatusObj',
            'InspectionRecordObj',
            'InspectionLogObj',
    
            "CheckinsObj",
            "ShelfReportAIDetailObj",
            "VisitRouteObj",
            "RouteCustomerObj",
            "AreaManageObj",
            "CoveredStoresObj",
            "UserVisitObj",
            "CheckinsImgObj",
            "CheckinsImgDetailObj",
            "CheckinsVerifyObj",
            "CheckinsVerifyDetailObj",
            "MileageStatisticsObj",
            "MileageStatisticsDetailObj",
            "StorePhotoWallInspectObj",
            "StorePhotoInspectDetailObj",
            "SuccessfulStoreRangeObj",
            "ProjectStandardsObj",
            "MustDistributeProductsObj",
            "DisplayTypeStandardsObj",
            "DisplayFormatDetailsObj",
            "SummaryDisplayAchievementObj",
            "DisplayDistrAchSummaryObj",
            "DisplayProjectAchievementObj",
            "DistributionProductsAchievedObj",
            "ErpOrganizationObj",
            "JournalObj",
            "BlogObj",
            "ScheduleObj",
            "AnnounceObj",
            "ServiceLogObj",
            "TelesalesRecordObj",
            "ApprovalFormObj",
            "LeaveApplicationObj",
            "LeaveApplicationItemObj",
            "OvertimeApplicationObj",
            "OvertimeApplicationItemObj",
            "TravelReimbursementObj",
            "TravelReimbursementItemObj",
            "TravelApplicationObj",
            "TravelApplicationItemObj",
            "ReimbursementObj",
            "ReimbursementItemObj",
            "ApprovalInstanceObj",
            "BpmInstance",
            "StageInstanceObj",
            "ApprovalTaskObj",
            "BpmTask",
            "StageTaskObj",
            "ApproverOpinionObj",
            "FlowTaskHandleTimeDetailObj",
            "InspectionDataAppealRecordObj"
        ],
        
        targetObjectBlackList: [
            'ForecastRuleObj',
            'ForecastTaskObj',
            'OperationsStrategyObj',
            'SubAccountTreeRelationLogObj',
            'AccountTreeRelationObj',
            'BusinessRiskInformationObj',
            'EnterpriseRiskObj',
            'AccountRiskPortraitRecordObj',
            'LeadsFlowRecordObj',
            'LeadsTransferLogObj',
            'BehaviorRecordObj',
            'BehaviorIntegralDetailObj',
            'HighSeasObj',
            'LeadsPoolObj',
            'ActivityQuestionObj',
            'InteractionStrategyObj',
            'InteractionStrategyDetailObj',
            'ActivityMeetingSummaryObj',
            'TermBankDetailObj',
            'PartnerContactRelationshipObj',
            'PartnerDepartmentObj',
            'PartnerProvisionObj',
            'PartnerAgreementObj',
            'PartnerAgreementDetailObj',
            'AgreementStatusRecordObj',
            'GoalValueObj',
            'GoalRuleObj',
            'GoalRuleApplyCircleObj',
            'EmployeeLoginUsageObj',
            'EmployeeObjectUsageObj',
            'EnterpriseRelationObj',
            'ErDepartmentObj',
            'PublicEmployeeObj',
            'ErBindThirdAccountObj',
    
            'AccountsPayableNoteObj',
            'AccountsPayableDetailObj',
            'SalesStatementsObj',
            'PointsRewardDetailObj',
            'FMCGSerialNumberObj',
            'FMCGSerialNumberStatusObj',
            'InspectionRecordObj',
            'InspectionLogObj',
    
            "CheckinsObj",
            "ShelfReportAIDetailObj",
            "VisitRouteObj",
            "RouteCustomerObj",
            "AreaManageObj",
            "CoveredStoresObj",
            "UserVisitObj",
            "CheckinsImgObj",
            "CheckinsImgDetailObj",
            "CheckinsVerifyObj",
            "CheckinsVerifyDetailObj",
            "MileageStatisticsObj",
            "MileageStatisticsDetailObj",
            "StorePhotoWallInspectObj",
            "StorePhotoInspectDetailObj",
            "SuccessfulStoreRangeObj",
            "ProjectStandardsObj",
            "MustDistributeProductsObj",
            "DisplayTypeStandardsObj",
            "DisplayFormatDetailsObj",
            "SummaryDisplayAchievementObj",
            "DisplayDistrAchSummaryObj",
            "DisplayProjectAchievementObj",
            "DistributionProductsAchievedObj",
            "ErpOrganizationObj",
            "JournalObj",
            "BlogObj",
            "ScheduleObj",
            "AnnounceObj",
            "ServiceLogObj",
            "TelesalesRecordObj",
            "ApprovalFormObj",
            "LeaveApplicationObj",
            "LeaveApplicationItemObj",
            "OvertimeApplicationObj",
            "OvertimeApplicationItemObj",
            "TravelReimbursementObj",
            "TravelReimbursementItemObj",
            "TravelApplicationObj",
            "TravelApplicationItemObj",
            "ReimbursementObj",
            "ReimbursementItemObj",
            "ApprovalInstanceObj",
            "BpmInstance",
            "StageInstanceObj",
            "ApprovalTaskObj",
            "BpmTask",
            "StageTaskObj",
            "ApproverOpinionObj",
            "FlowTaskHandleTimeDetailObj",
            "InspectionDataAppealRecordObj",
    
            /* 进销存&备件对象中不允许作为映射目标的对象 */
            'StockObj', // 库存
            'StockDetailsObj', // 出入库明细
            'InventoryFreezingObj', // 库存冻结
            'InventoryFreezingDetailObj', // 库存冻结明细
            'InventoryUnfreezingDetailObj', // 库存解冻明细
            'InventoryDetailsObj', // 库存明细
            'BatchStockObj', // 批次库存
            'EmployeeWarehouseDetailObj', // 个人库明细
            'EmployeeWarehouseInOutRecordObj', // 个人库出入库记录
            'IndividualStockTransactionsObj', // 个人库出入明细
            /* 进销存&备件对象中不允许作为映射目标的对象 */
            'SalesOrderProductObj', //订单产品
            'PersonnelObj',//人员
            'ProductObj',
            'SPUObj',
            'SpecificationValueObj',
            'AccountsReceivableNoteObj',
            'MatchNoteObj'
        ]
    };
    
    
    // 各业务对象的需去除字段 和 类型
    exports.presetFieldBlackList = {
        fields: {
            SalesOrderObj : {
                target: ['confirmed_delivery_date', 'confirmed_receive_date', 'delivery_comment','resource','promotion_id']
            },
            OpportunityObj: {
                source: [
                    'sales_process_name',
                    "sales_stg_changed_time",
                    "after_sale_stage_order",
                    "oppo_stage_id",
                    "biz_status",
                    "is_start_after_sale",
                    "last_followed_time",
                    "status",
                    "after_sale_stage_status",
                    "after_sale_stage_name",
                    "origin_source",
                    "is_bind_after_sale",
                    "leads_id",
                    "probability_amount",
                    "oppo_after_stage_id",
                    "before_sale_stage_order",
                    "before_sale_stage_name"
                ],
                target: [
                    'sales_stg_changed_time',
                    'probability',
                    "after_sale_stage_order",
                    "oppo_stage_id",
                    "sales_process_name",
                    "biz_status",
                    "is_start_after_sale",
                    "last_followed_time",
                    "status",
                    "after_sale_stage_status",
                    "after_sale_stage_name",
                    "origin_source",
                    "is_bind_after_sale",
                    "leads_id",
                    "probability_amount",
                    "oppo_after_stage_id",
                    "before_sale_stage_order",
                    "before_sale_stage_name"
                ]
            },
            ReturnedGoodsInvoiceObj: {
                source: ['ShouldReturnMoney'],
                target: ['ShouldReturnMoney']
            },
            ContactObj: {
                source: [
                    'mobile',
                    'tel',
                    'date_of_birth',
                    'year_of_birth',
                    'month_of_birth',
                    'day_of_birth'
                ],
                target: [
                    'mobile',
                    'tel',
                    'date_of_birth',
                    'year_of_birth',
                    'month_of_birth',
                    'day_of_birth'
                ]
            },
            LeadsObj: {
                target: [
                    "close_reason",
                    "completed_result",
                    "back_reason",
                    "returned_time",
                    "biz_status",
                    "life_status",
                    "owner_department",
                    "assigner_id",
                    "assigned_time",
                    "is_overtime",
                    "is_duplicated",
                    "is_relevance_wechat",
                    "out_resources",
                    "last_follower",
                    "last_follow_time",
                    "transform_time",
                    "transform_period",
                    "remaining_time",
                    "changed_to_mql_period"
                ]
            },
            RefundObj: {
                target: ['account_id']
            },
            VisitingObj: {
                target: ['account_id']
            },
            WechatEmployeeObj: {
                source: ['record_type', 'out_owner'],
                target: ['record_type', 'out_owner']
            },
            AccountObj: {
                source: [
                    "biz_status",
                    "deal_status",
                    "expire_time",
                    "last_followed_time",
                    "last_follower",
                    "last_deal_closed_time",
                    "returned_time",
                    "claimed_time",
                    "extend_days",
                    "remaining_time",
                    "owner_modified_time",
                    "completion_rate",
                    "completed_field_quantity",
                    "account_path",
                    "enable_risk_portrait",
                    "is_blacklist",
                    "blacklist_desc",
                    "credit_limit_upper",
                    "remind_days",
                    "risk_scores_model_name",
                    "self_service_scores_rule_name",
                    "formula_credit_limit",
                    "is_risk_monitor",
                    "third_party_risk_scores",
                    "credit_rule_name",
                    "credit_limit_lower",
                    "origin_source",
                    "industry_ext",
                    "is_remind_recycling",
                    "acc_expired_time",
                    "signing_status"
                ],
                target: [
                    "phone_number_attribution_city",
                    "phone_number_attribution_province",
                    "phone_number_attribution_district",
                    "phone_number_attribution_country",
                    "phone_number_attribution_address",
                    "phone_number_attribution_location",
                    "transfer_count",
                    "biz_reg_name",
                    "industry_ext",
                    "high_seas_name",
                    "high_seas_id",
                    "biz_status",
                    "deal_status",
                    "expire_time",
                    "last_followed_time",
                    "last_follower",
                    "last_deal_closed_time",
                    "returned_time",
                    "claimed_time",
                    "extend_days",
                    "remaining_time",
                    "owner_modified_time",
                    "completion_rate",
                    "completed_field_quantity",
                    "account_path",
                    "enable_risk_portrait",
                    "is_blacklist",
                    "blacklist_desc",
                    "credit_limit_upper",
                    "remind_days",
                    "risk_scores_model_name",
                    "self_service_scores_rule_name",
                    "formula_credit_limit",
                    "is_risk_monitor",
                    "third_party_risk_scores",
                    "credit_rule_name",
                    "credit_limit_lower",
                    "origin_source",
                    "is_remind_recycling",
                    "acc_expired_time",
                    "signing_status"
                ]
            },
            NewOpportunityObj: {
                source: [
                    "sales_process_id",
                    "sales_stage",
                    "leads_id",
                ],
                target: [
                    "sales_process_id",
                    "sales_stage",
                    "leads_id",
                ]
            },
            PartnerObj: {
                source: ['acc_expired_time', 'signing_status'],
                target: ['acc_expired_time', 'signing_status']
            },
            ProjectObj: {
                target: [
                    "planed_work_cost",
                    "actual_working_hours",
                    "biz_status",
                    "expense_claim_amount",
                    "remain_working_hours",
                    "actual_work_cost",
                    "percentage_complete"
                ]
            },
            ProjectStageObj: {
                target: [
                    "actual_working_hours",
                    "actual_work_cost",
                    "planed_work_cost"
                ]
            }
        },
        type: {
            ContractObj: {
                target: ['image', 'file_attachment']
            },
            RefundObj: {
                target: ['image', 'file_attachment']
            },
            InvoiceApplicationObj: {
                target: ['image']
            },
            VisitingObj: {
                target: ['image', 'file_attachment']
            },
            MarketingEventObj: {
                target: ['image', 'file_attachment']
            }
        }
    };
  
  
    exports.presetFieldCommonApiNameBlackList = [
        'id',
        'tenant_id',
        'lock_status',
        'extend_obj_data_id',
        'package',
        'object_describe_id',
        'object_describe_api_name',
        'version',
        'lock_user',
        'lock_rule',
        'life_status_before_invalid',
        'is_deleted',
        'data_auth_code',
        'change_type',
        'out_data_auth_code',
        'order_by',
        'data_auth_id',
        'out_data_auth_id',
        'origin_source',
        'sys_modified_time'
    ]
});