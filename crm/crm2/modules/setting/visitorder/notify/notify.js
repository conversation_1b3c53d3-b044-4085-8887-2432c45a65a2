define(function(require, exports, module) {

	var tpl 	= require('./template/tpl-html'),
		Select	= require('crm-widget/select/select'),
        util 	= require('crm-modules/common/util');

	var Notify = Backbone.View.extend({

		events: {
			'click .j-confirm': '_saveConfig',
			'click .j-cancel': '_cancelConfig',
			'click .j-set-notify': '_setNotifyPage',

			'click .j-confirm2': '_saveConfig2',
			'click .j-cancel2': '_cancelConfig2',
			'click .j-set-notify2': '_setNotifyPage2'
		},
		
		initialize: function(options) {
			this.setElement(options.wrapper);
			this.$$data = {};
    	},
		
    	render: function() {
			var me = this;
			me.$el.html(tpl());
			me.$$data.create = {}; // 【创建成功后发送通知】模块的数据
			me.$$data.confirm = {}; // 【确认后发送通知】模块的数据
			me.getRecordTypeDec();
			me.getRecordTypeDecOfConfirm();
		},
		
		/**
		 * 获取微信通知业务类型描述-订单创建模块
		 */
		getRecordTypeDec: function(cb) {
			var me = this;
            util.api({
                url: '/FHH/EM1HSailAdmin/sail-admin/config/listApplicableNoticeOrderRecordTypes',
                success: function(res) {
                    if (!res.Error) {
						var data = res.Value || []
						var options = [], defaultVal = [], defaultLable = [], map = {}
						data.forEach(function(a) {
							var obj = {name: a.label, value: a.apiName}
							if (a.chosen) {
								defaultVal.push(a.apiName) && defaultLable.push(a.label)
							}
							map[a.apiName] = a.label
							options.push(obj)
						})
						me.$$data.create = {
							notifyOpts: options,
							notifyVal: defaultVal,
							notifyValTemp: defaultVal,
							notifyOpsMap: map
						}
						// me.$el.html(tpl({label: defaultLable.join('、')}));
						defaultLable.length ? $('.notify-value', me.$el).html(defaultLable.join('、')) : $('.notify-value', me.$el).html('-')
                    }
                }
            }, {
				autoPrependPath: false
            });
		},

		/**
		 * 获取微信通知业务类型描述-订单确认模块
		 */
		getRecordTypeDecOfConfirm: function(cb) {
			var me = this;
            util.api({
				url: '/FHH/EM1HSailAdmin/sail-admin/config/listExamineApproveSalesOrderNoticeRecordTypes',
                success: function(res) {
                    if (!res.Error) {
                        var data = res.Value || []
						var options = [], defaultVal = [], defaultLable = [], map = {}
						data.forEach(function(a) {
							var obj = {name: a.label, value: a.apiName}
							if (a.chosen) {
								defaultVal.push(a.apiName) && defaultLable.push(a.label)
							}
							map[a.apiName] = a.label
							options.push(obj)
						})
						me.$$data.confirm = {
							notifyOpts: options,
							notifyVal: defaultVal,
							notifyValTemp: defaultVal,
							notifyOpsMap: map
						}
						// me.$el.html(tpl({label: defaultLable.join('、')}));
						defaultLable.length ? $('.notify-value2', me.$el).html(defaultLable.join('、')) : $('.notify-value2', me.$el).html('-')
                    }
                }
            }, {
				autoPrependPath: false
            });
		},

		/**
         * @desc 使用微信通知设置的下拉框
         */
        initSelect: function(options) {
			var me = this
			var createNotice = me.$$data.create;
			if (me.select) return
            me.select = new Select({
				$wrap: me.$('.notify-select'),
				multiple: 'multiple',
				allCheck: true,
                options: options,
				defaultValue: createNotice.notifyVal
            });
            me.select.on('change', function(val, data, a, b) {
				createNotice.notifyValTemp = val
            });
		},

        initSelect2: function(options) {
			var me = this
			var confirmNotice = me.$$data.confirm;
			if (me.select2) return
            me.select2 = new Select({
				$wrap: me.$('.notify-select2'),
				multiple: 'multiple',
				allCheck: true,
                options: options,
				defaultValue: confirmNotice.notifyVal
            });
            me.select2.on('change', function(val, data, a, b) {
				confirmNotice.notifyValTemp = val
            });
		},

		/**
         * @desc 设置操作
         */
		_setNotifyPage: function() {
			var creataNotice = this.$$data.create
			$('.notify-wrap', this.$el).addClass('column-item__active')
			$('.notify-wrap-text', this.$el).hide()
			$('.notify-wrap-set', this.$el).show()
			this.initSelect(creataNotice.notifyOpts)
			this.select && this.select.setValue(_.clone(creataNotice.notifyVal))
		},

		_setNotifyPage2: function() {
			var confirmNotice = this.$$data.confirm
			$('.notify-wrap', this.$el).addClass('column-item__active')
			$('.notify-wrap-text2', this.$el).hide()
			$('.notify-wrap-set2', this.$el).show()
			this.initSelect2(confirmNotice.notifyOpts)
			this.select2 && this.select2.setValue(_.clone(confirmNotice.notifyVal))
		},

		/**
         * @desc 取消操作
         */
		_cancelConfig: function() {
			// 查一下兄弟元素是否是隐藏状态，如果是隐藏则去掉聚焦的背景色
			if ($('.notify-wrap-set2', this.$el).css('display') === 'none') {
				$('.notify-wrap', this.$el).removeClass('column-item__active')
			}

			$('.notify-wrap-text', this.$el).show()
			$('.notify-wrap-set', this.$el).hide()
		},

		_cancelConfig2: function() {
			// 查一下兄弟元素是否是隐藏状态，如果是隐藏则去掉聚焦的背景色
			if ($('.notify-wrap-set', this.$el).css('display') === 'none') {
				$('.notify-wrap', this.$el).removeClass('column-item__active')
			}

			$('.notify-wrap-text2', this.$el).show()
			$('.notify-wrap-set2', this.$el).hide()
		},
		
		/**
         * @desc 保存操作
         */
        _saveConfig: function(e) {
			var me = this, $target = $(e.target);
			var createNotice = me.$$data.create;
            util.api({
                url: '/FHH/EM1HSailAdmin/sail-admin/config/createOrUpdateApplicableNoticeOrderRecordType',
                data: {
					orderRecordTypes: createNotice.notifyValTemp
				},
                success: function(data) {
                    if (!data.Error) {
						createNotice.notifyVal = _.clone(createNotice.notifyValTemp)
						var val = []
						createNotice.notifyVal.forEach(function(a) {
							val.push(createNotice.notifyOpsMap[a])
						})
						val.length ? $('.notify-value', me.$el).html(val.join('、')) : $('.notify-value', me.$el).html('-')
						me._cancelConfig();
                    } else {
                        util.alert(data.Error.Message);
                    }
                }
            },{
				submitSelector: $target,
				autoPrependPath: false,
                errorAlertModel: 1
            });            
		},

		_saveConfig2: function(e) {
			var me = this, $target = $(e.target);
			var confirmNotice = me.$$data.confirm;
            util.api({
				url: '/FHH/EM1HSailAdmin/sail-admin/config/createOrUpdateExamineApproveSalesOrderNoticeRecordType',
                data: {
					orderRecordTypes: confirmNotice.notifyValTemp
				},
                success: function(data) {
                    if (!data.Error) {
						confirmNotice.notifyVal = _.clone(confirmNotice.notifyValTemp)
						var val = []
						confirmNotice.notifyVal.forEach(function(a) {
							val.push(confirmNotice.notifyOpsMap[a])
						})
						val.length ? $('.notify-value2', me.$el).html(val.join('、')) : $('.notify-value2', me.$el).html('-')
						me._cancelConfig2();
                    } else {
                        util.alert(data.Error.Message);
                    }
                }
            },{
				submitSelector: $target,
				autoPrependPath: false,
                errorAlertModel: 1
            });            
		},
		destroy: function() {
			var me = this;
			this.select && this.select.destroy();
			this.select2 && this.select2.destroy();
			this.$$data = null;
        }
	});

	module.exports = Notify;
});
