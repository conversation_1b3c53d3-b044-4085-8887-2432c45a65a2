<div class="column-item notify-wrap">
	<div class="column-lb" title="{{ $t('微信通知设置') }}">{{ $t('微信通知设置') }}</div>
	<!-- 1 -->
	<div class="column-right">
		<div class="column-con notify-wrap-text">
			<div>{{ $t('订单创建成功发送') }}<span class="notify-value"></span></div>
			<div class="column-tip">{{ $t('crm.访销微信通知设置说明') }}</div>
		</div>
		<div class="column-con notify-wrap-set" style="display: none;">
			<p>{{ $t('订单创建成功发送') }}</p>
			<div class="select notify-select"></div>
			<!-- 指定哪些订单业务类型需要在创建访销订单后，微信通知CRM客户的联系人 -->
			<div class="column-tip">{{ $t('crm.访销微信通知设置说明') }}</div>
			<div class="btn-box">
				<button class="b-g-btn j-confirm">{{ $t('保存') }}</button>
				<button class="b-g-btn-cancel j-cancel">{{ $t('取消') }}</button>
			</div>
		</div>
		<span class="set-btn j-set-notify notify-wrap-text">{{ $t('设置') }}</span>
	</div>

	<!-- 2 -->
	<div class="column-right" style="margin-top: 30px;">
		<div class="column-con notify-wrap-text2">
			<div>{{ $t('订单确认发送') }}<span class="notify-value2"></span></div>
			<!-- 指定哪些订单业务类型需要在访销订单确认后，微信通知CRM客户的联系人 -->
			<div class="column-tip">{{ $t('crm.访销微信通知设置说明二') }}</div>
		</div>
		<div class="column-con notify-wrap-set2" style="display: none;">
			<p>{{ $t('订单确认发送') }}</p>
			<div class="select notify-select2"></div>
			<div class="column-tip">{{ $t('crm.访销微信通知设置说明二') }}</div>
			<div class="btn-box">
				<button class="b-g-btn j-confirm2">{{ $t('保存') }}</button>
				<button class="b-g-btn-cancel j-cancel2">{{ $t('取消') }}</button>
			</div>
		</div>
		<span class="set-btn j-set-notify2 notify-wrap-text2">{{ $t('设置') }}</span>
	</div>
</div>
