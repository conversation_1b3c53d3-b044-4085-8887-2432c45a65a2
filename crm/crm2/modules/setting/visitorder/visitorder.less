.crm-s-visitorder .crm-column {
	position: relative;
	font-size: 14px;
}
.crm-s-visitorder .crm-column:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
	right: 20px;
	border-bottom: 1px dashed #DDD;
}
.crm-s-visitorder .column-item {
	padding: 24px 0 24px 150px;
}
.crm-s-visitorder .column-item__active {
	background-color: #F6F9FD;
}
.crm-s-visitorder .column-lb {
	font-size: 14px;
	color: #666666;
	top: 24px;
	max-width: 125px;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}
.crm-s-visitorder .column-right {
	position: relative;
}
.crm-s-visitorder .column-con {
	margin-right: 80px;
	color: #333333;
}
.crm-s-visitorder .column-tip {
	color: #999999;
}
.crm-s-visitorder .set-btn {
	position: absolute;
    top: 0;
    right: 35px;
	font-size: 14px;
	color: #3487E2;
	cursor: pointer;
}
.crm-s-visitorder .select {
	width: 100%;
    position: relative;
}
.crm-s-visitorder .btn-box {
	margin-top: 20px;
}
.crm-s-visitorder .btn-box button {
	margin-right: 20px;
}
