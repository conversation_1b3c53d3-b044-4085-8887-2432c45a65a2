define(function(require, exports, module) {

	var tpl 		= require('./template/tpl-html'),
		Promotion 	= require('./promotion/promotion'),
		Notify 	= require('./notify/notify'),
        util 		= require('crm-modules/common/util');

	var VisitOrder = Backbone.View.extend({

		events: {},
		
		initialize: function(options) {
			this.$$components = {}
            this.setElement(options.wrapper);
    	},
		
    	render: function() {
			var me = this;
            me.getConfig(function(value) {
				if (!value) {
					me.$el.html('<div class="crm-tit"><h2><span class="tit-txt">' + $t("访销订单管理") + '</span></h2></div><div class="crm-module-con crm-scroll"><div class="crm-warn-bar"> ' + $t("该模块未开通如有需要请咨询纷享客服400-1122-778") + '</div></div>')
					return
				}
				me.$el.html(tpl());
				me.initPromotion();
				me.initNotify();
			});
			
		},

		/**
         * @desc 获取是否灰度
         */
		getConfig: function(cb) {
			var me = this;
            util.api({
                url: '/FHH/EM1HSailAdmin/sail-admin/config/isVisitOrderNeedSetting',
                success: function(data) {
                    if (!data.Error) {
                        cb && cb(data.Value)
                    }
                }
            }, {
				autoPrependPath: false
            });
		},

		initPromotion: function() {
			var me = this
			me.$$components.promotion = new Promotion({
				wrapper: $('.promotion', me.$el)
			})
			me.$$components.promotion.render()
		},

		initNotify: function() {
			var me = this
			me.$$components.notify = new Notify({
				wrapper: $('.notify', me.$el)
			})
			me.$$components.notify.render()
		},

		/**
         * 回收子组件
         * @return {Void}
         */
        destroyComponents: function () {
            var me = this, key;
            for (key in me.$$components) {
                if (me.$$components.hasOwnProperty(key)) {
                    me.$$components[key].destroy();
                    delete me.$$components[key];
                }
            }
            if (me.$$dt) {
                me.$$dt.destroy();
                delete me.$$dt;
            }
        },
        /**
         * 销毁组件
         * @return {Void}
         */
        destroy: function () {
            var me = this;
            me.$$state = {};
            me.destroyComponents();
            me.$el.html('');
        }
	});

	module.exports = VisitOrder;
});
