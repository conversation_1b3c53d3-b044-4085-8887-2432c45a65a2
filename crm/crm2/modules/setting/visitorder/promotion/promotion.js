define(function(require, exports, module) {

	var tpl 	= require('./template/tpl-html'),
		Select	= require('crm-widget/select/select'),
        util 	= require('crm-modules/common/util');

	var Promotion = Backbone.View.extend({

		events: {
			'click .b-g-btn': '_saveConfig',
			'click .b-g-btn-cancel': '_cancelConfig',
			'click .j-set-promotion': '_setPromotionPage'
		},
		
		initialize: function(options) {
			this.setElement(options.wrapper);
			this.$$data = {};
    	},
		
    	render: function() {
			var me = this;
			me.getRecordTypeDec(function(data) {
				var options = [], defaultVal = [], defaultLable = [], map = {}
				data.forEach(function(a) {
					var obj = {name: a.label, value: a.apiName}
					if (a.chosen) {
						defaultVal.push(a.apiName) && defaultLable.push(a.label)
					}
					map[a.apiName] = a.label
					options.push(obj)
				})
				me.$$data.promotionOpts = options
				me.$$data.promotionVal = defaultVal
				me.$$data.promotionValTemp = defaultVal
				me.$$data.promotionOpsMap = map
				me.$el.html(tpl({label: defaultLable.join('、')}));
				// me.initSelect(options)
			})
		},
		
		/**
		 * 获取促销的订单业务类型描述
		 */
		getRecordTypeDec: function(cb) {
			var me = this;
            util.api({
                url: '/FHH/EM1HSailAdmin/sail-admin/config/listApplicablePromotionOrderRecordTypes',
                success: function(data) {
                    if (!data.Error) {
                        cb && cb(data.Value)
                    }
                }
            }, {
				autoPrependPath: false
            });
		},

		/**
         * @desc 使用促销设置的下拉框
         */
        initSelect: function(options) {
			var me = this
			if (me.select) return
            me.select = new Select({
				$wrap: me.$('.promotion-select'),
				multiple: 'multiple',
				allCheck: true,
                options: options,
				defaultValue: me.$$data.promotionVal
            });
            me.select.on('change', function(val) {
				me.$$data.promotionValTemp = val
            });
		},

		/**
         * @desc 设置操作
         */
		_setPromotionPage: function() {
			$('.promotion-wrap', this.$el).addClass('column-item__active')
			$('.promotion-wrap-text', this.$el).hide()
			$('.promotion-wrap-set', this.$el).show()
			this.initSelect(this.$$data.promotionOpts)
			this.select && this.select.setValue(_.clone(this.$$data.promotionVal))
		},

		/**
         * @desc 取消操作
         */
		_cancelConfig: function() {
			$('.promotion-wrap', this.$el).removeClass('column-item__active')
			$('.promotion-wrap-text', this.$el).show()
			$('.promotion-wrap-set', this.$el).hide()
		},
		
		/**
         * @desc 保存操作
         */
        _saveConfig: function(e) {
			var me = this, $target = $(e.target);
			if (!me.$$data.promotionValTemp || !me.$$data.promotionValTemp.length) return
            util.api({
                url: '/FHH/EM1HSailAdmin/sail-admin/config/createOrUpdateApplicablePromotionOrderRecordType',
                data: {
					orderRecordTypes: me.$$data.promotionValTemp
				},
                success: function(data) {
                    if (!data.Error) {
						me.$$data.promotionVal = _.clone(me.$$data.promotionValTemp)
						var val = []
						me.$$data.promotionVal.forEach(function(a) {
							val.push(me.$$data.promotionOpsMap[a])
						})
						$('.promotion-value', me.$el).html(val.join('、'))
						me._cancelConfig();
                    } else {
                        util.alert(data.Error.Message);
                    }
                }
            },{
				submitSelector: $target,
				autoPrependPath: false,
                errorAlertModel: 1
            });            
		},
		destroy: function() {
			var me = this;
			this.select && this.select.destroy();
			this.$$data = null;
        }
	});

	module.exports = Promotion;
});
