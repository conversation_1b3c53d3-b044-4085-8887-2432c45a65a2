.crm-s-customerrule {
	.recover-box {
        .tb-fill-empty {
            display: none;
        }
        .item-tit {
            width: 280px;
        }
        .ipt-box {
            width: 125px;
        }
        .recover-circle-box {
            h3{
                font-size: 14px;
                color: #333;
                padding: 10px 0 25px;
            }
            .climits-title{
                font-size: 14px;
                color: #999;
                padding-bottom: 10px;
                border-bottom: 1px solid #eaeaea;
            }
            .climits-box{
                max-height: 250px;
                overflow: hidden;
                .box-left,.box-right{
                    max-height: 250px;
                    overflow-y: auto;
                    overflow-x: hidden;
                }
                .box-left{
                    height: 250px;
                    border-right: 1px solid #eee;
                }
                .box-right{
                    padding-left: 20px;
                    width: 200px;
                }
                .active{
                    background: #f4f7fd;
                }
                .climits-employee{
                    width: 140px;
                    height: 34px;
                    padding-left: 10px;
                    margin-right: 20px;
                    line-height: 34px;
                    border-bottom: 1px dashed #eee;
                    &:hover{
                        background: #f4f7fd;
                        cursor: pointer;
                    }
                }
                .climits-circle{
                    .circle-radio{
                        padding: 8px 0 2px;
                        max-width: 180px;
                        span[title] {
                            max-width: 155px;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }
                    }

                }
            }
        }
        .item-con-sec{
            white-space: normal;
            margin-bottom: 6px;
            &:last-child{
                margin: 0;
            }
            .sec-label{
                float: left;
                min-width: 60px;
            }
            .sec-con{
                margin-left: 60px;
				text-overflow: ellipsis;
				white-space:nowrap;
				overflow: hidden;
            }
        }
        .rule-subtit {
            width: 100%;
            .ml {
                margin-left: 404px;
            }
        }
        .rule-table-nav{
            padding: 30px 0;
            font-size: 18px;
            color: #aaa;
            .left{
                border-right: 2px solid #EEE;
            }
            .rule-nav-span{
                padding: 0 22px;
                &:hover{
                    cursor: pointer;
                    color: #333;
                }
            }
            .rule-nav-span.active{
                color: #333;
            }
        }
        .rule-remind{
            .item-tit{
                width: 280px;
                margin-right: 15px;
            }
            .item-remind-group{
                width: 565px;
            }
        }
        .rule-box {
            padding: 0 20px 40px;

            .crm-gray-9 {
                font-size: 12px;
            }
            .main .fixed-columns{
                border-left: 1px solid #eee;
            }
            .edite-remind, .edite-recover{
                margin-right: 12px;
            }
            .edite-remind, .del-recover{
                margin-right: 12px;
            }
            .table-action-btn{
                margin-right: 12px;
            }
        }
        .rule-recover {
            padding: 0;
            margin: 0 20px 40px;
            border: 1px solid #d6e2ed;
            border-width: 1px 1px 0;
            .main {
                min-height: auto;
            }
            .dt-no-data {
                border-bottom: 1px solid #d6e2ed;
            }
        }
        .rule-item {
            width: 100%;
            padding: 5px 0;
            line-height: 34px;
            position: relative;
            &:after {
                content: ' ';
                display: block;
                clear: both;
                height: 0;
                visiblity: hidden;
            }
            .checkbox-sec{
                float: left;
                width: 140px;
                text-align: center;
            }
            .item-tit {
                width: 280px;
                padding: 0;
                float: left;
                line-height: 20px;
                word-wrap: break-word;
                .names {
                    padding: 7px 0;
                    line-height: 20px;
                }
            }
            .item-remind-words{
                float: left;
                width: 430px;
            }
            .item-remind-group{
                float: left;
                width: 620px;
                min-height: 34px;
                .remind-item{
                    float: left;
                    position: relative;
                }
                .remind-add-box{
                    float: left;
                    .add-remind-item{
                        padding: 0;
                        margin: 0;
                        padding-left: 10px;
                    }
                }
                .ipt-box {
                    float: left;
                    width: 76px;
                    .num {
                        float: left;
                        width: 30px;
                        padding-right: 5px;
                        text-align: right;
                        overflow: hidden;
                    }
                    .b-g-ipt {
                        width: 20px;
                        padding: 5px 2px;
                        text-align: right;
                    }
                    .unit {
                        float: left;
                    }
                }
                .type-select-box{
                    width: 80px;
                }
                .remind-word-span{
                    padding-left: 5px;
                }
                .del-remind-item{
                    cursor: pointer;
                }
            }
            .ipt-box {
                float: left;
                width: 160px;
                .num {
                    float: left;
                    width: 90px;
                    padding-right: 10px;
                    text-align: right;
                    overflow: hidden;
                }
                .b-g-ipt {
                    width: 40px;
                    padding: 5px 2px;
                    text-align: right;
                }
                .unit {
                    float: left;
                }
            }
            .item-operate {
                float: left;
                color: @linkColor;
                span {
                    float: left;
                    margin-right: 15px;
                    cursor: pointer;
                    display: none;
                }
                .save-limit,
                .cancel-limit {
                    display: none;
                }
            }
            &:hover {
                .edite-recover, .del-recover,
                .edite-remind, .del-remind,
                .edite-limit, .del-limit, .preview-limit {
                    display: block;
                }
            }
            .operate-edite {
                .save-recover, .cancel-recover,
                .save-remind, .cancel-remind,
                .save-limit, .cancel-limit {
                    display: block;
                }
                .edite-recover, .del-recover,
                .edite-remind, .del-remind,
                .edite-limit, .del-limit, .preview-limit {
                    display: none!important;
                }
            }

            .type-select-box {
                float: left;
                width: 110px;
            }
            .hightsea-lb {
                width: 70px;
                padding: 0 10px;
                float: left;
                color: #999;
            }
            .hightsea-select-box {
                float: left;
                width: 120px;
                padding-right: 30px;
                overflow: hidden;
                .nowrap();
            }
        }
        .nonhigh-secrch-rule-deptment{
          margin: 12px 20px 10px 20px;
          border: 1px solid #FF8000;
          border-radius: 4px;
          .el-alert{
            background: #FFF7E6;
            padding: 12px 16px;
            .el-alert__content{
              width: 100%;
              padding-right: 0px;
              .rule-deptment-content{
                
              }
            }
          }
          .fx-icon-info::before {
            color: #737C8C;
          }
        }
    }
}
.change-deptment-rule-dialog{
  .el-radio-group.is-vertical .fx-radio{
    margin-bottom: 0px;
  }
  .change-deptment-rule-top{
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
    color: #181C25;
  }
  .change-deptment-rule-main-dept{
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    color: #91959E;
    margin-bottom: 8px;
    padding-left: 24px;
  }
  .change-deptment-rule-main-higher-dept{
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    color: #91959E;
    padding-left: 24px;
  }
  .change-deptment-rule-top-desc{
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    color: #91959E;
  }
}
