
.crm-s-customerrule {
	.bottom_0{
		margin-bottom:0!important;
	}
	.tab-con {
		position: absolute;
		left: 0;
		top: 48px;
		bottom: 0;
		right: 0;
		.crm-p20.crm-scroll {
			padding: 0;
    		height: 100%;
		}
	}
	.scroll-el {
		width: 100%;
		height: 100%;
	}
	.crm-tab .item {
		text-decoration: none;
	}
	.customer-rule-tab {
		white-space: nowrap;
		overflow-x: auto;

		.item {
			float: none;
			display: inline-block;
		}
	}
	.add-btn {
		display: inline-block;
		margin-top: 10px;
		cursor: pointer;
	}

	.line-mb-mid {
		margin: 10px 0;
		color:#999;
	}
	.behavior-weight{
		color:#333;
	}


	// 公海管理
	.highseas-box {
		position: absolute;
		left: 0;
		top: 1px;
		right: 0;
		bottom: 0;
	}

	// 客户规则设置
	.basic-box {
		&.mn-checkbox-box {
			p {
				margin: 0 0 20px;
			}
			span {
				display: inline-block;
				vertical-align: middle;
			}
			.check-lb {
				margin-left: 10px;
			}
			.crm-gray-9 {
				margin-top: -17px;
				padding-left: 25px;
				font-size: 12px;
			}
		}
		.setting-setrule{
			margin-top:20px;
		}
	}

	// 客户报备
	.check-box {
		.crm-warn-bar {
			margin: -19px -20px 20px;
		}
		.mn-radio-box {
			span {
				display: inline-block;
				vertical-align: middle;
			}
			.radio-lb {
				margin: 0 25px 0 10px;
			}
			.crm-gray-9 {
				margin-top: -2px;
				line-height: 18px;
				padding: 0 0 0 25px;
				font-size: 12px;
			}
		}
		.crm-column {
			padding-top: 15px;
		}
	}

	// 客户跟进成交规则
	.behavior-box, .makeDeal-box {
		width: 100%;
		box-sizing: border-box;
		.box-h3-title {
			line-height: 16px;
			font-size: 16px;
			color: #333;
            margin:0;
		}
		.crm-g-c-black {
			line-height: 16px;
			font-size: 13px;
			color: #333;
		}
		.behavior-btn-box {
			width: 100%;
			height: 60px;
			.setting-btn {
				float: left;
				margin: 5px 20px;
				display: none;

			}
			.add-btn {
				float: right;
				margin: 5px 20px;
				display: none;
			}
		}
		.nav {
			width: 210px;
			padding: 10px 5px 10px;;
			.title {
				color: #999;
				font-size: 13px;
			}
			.edit-btn {
				float: right;
			}
		}
		.crm-scroll {
			height: 210px;
			width: 220px;
			border: 1px solid #eee;
			#css3> .radius(5px);
			box-sizing: border-box;
		}
		.b-list {
			width: 100%;
			padding: 8px 0;
			.b-item {
				line-height: 28px;
				padding-left: 20px;
			}
		}

		.trade-behavior-wrap {
			margin-top: 20px;

			.item {
				padding: 0;
				margin-bottom: 15px;
			}
		}
		.customer-behavior-select {
			width: 220px;
			margin: 10px 0;
		}

		.empty {
			margin: auto;
			height: 20px;
			line-height: 20px;
			width: 100%;
			text-align: center;
			font-family: MicrosoftYaHei;
			font-size: 16px;
			font-weight: 400;
			font-style: normal;
			font-stretch: normal;
			letter-spacing: normal;
			color: #ccc;
			margin-top: 34%;
		}
		.trade-behavior-tabs{
			display: flex;
			.radio-item{
				margin-right: 30px;
			}
		}
		.trade-behavior-title{
			margin-top:20px;
		}
		.trade-behavior-tabs{
			margin-top:12px;
		}
		.trade-behavior-panel{
			margin-top: 20px;
			display: none;
			counter-reset: listcounter2;
			&.panel-on{
				display: block;
			}
			.line-mb-mid:before{
				content: counter(listcounter2)'、';
				counter-increment: listcounter2;
			}
		}
		.trade-behavior-inpanel{
			border: #e6e6e6 solid 1px;
			border-radius: 3px;
			padding: 12px 20px;
		}
		.trade-behavior-cbwrapper{
			padding-left:20px;
			margin-top: 20px;
			line-height: 26px;
		}
		.trade-behavior-checkbox{
			.mn-checkbox-item{
				margin-right: 10px;
			}
		}
		.trade-behavior-tip{
			color:#999;
		}
		.trade-behavior-btns{
			margin-top:50px;
		}
		.follow-behavior {
			.crm-intro-container {
				padding: 20px;
			}
			.behavior-table-box {
				padding: 0 20px;
				width: calc(~"100% - 40px");
			}
		}
	}

	// 客户成交规则
	.makeDeal-box{
		.crm-intro{
			counter-reset: listcounter;
			li:before{
				content: counter(listcounter)'、';
				counter-increment: listcounter;
			}
		}
		.trade-behavior-tabs{
			.radio-item{
				display: inline-flex;
				align-items: center;
			}
		}
		.radio-tag{
			visibility: hidden;
			height: 20px;
			padding: 0 3px;
			line-height: 20px;
			border-radius: 2px;
			color: var(--color-primary06,#ff8000);
			padding: 0 6px;
			background-color: var(--color-primary01,#fff7e6);
			display: inline-block;
			font-size: 12px;
		}
		.radio-lb{
			margin: 0 4px;
		}
		.radio-tag.tag-on{
			visibility: visible;
		}
	}

	// 地图模式设置
	.mapmodesetting-box {
		
		.update-tip{
			display:flex;
			overflow: hidden;
			margin-bottom: 20px;
			background-color: #ffab00;
			border: 1px solid #ffab00;
			#css3 .radius(3px);
			height: 40px;
			font-size: 12px;
			.icon{
				position:relative;
				display:inline-block;
				width: 40px;
				flex: 0 0 40px;
				vertical-align: top;
				margin-top: 13px;
				em{
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					text-align: center;
					color: #ffab00;
					font-size: 12px;
				}
			}
			.triangle{
				display: block;
				width: 2px;
				margin: auto;
				border-bottom: 16px solid var(--color-neutrals01);
				border-left: 8px solid transparent;
				border-right: 8px solid transparent;
			}
			.content{
				padding-bottom: 5px;
				width: 100%;
				background-color: #fff8ec;
				#css3 .radius(3px);
			}
			.title{
				color: var(--color-neutrals19);
				line-height: 40px;
				position: relative;
				padding: 0 24px 0 16px;
				&:before,
				&:after{
					position: absolute;
					right: 15px;
					content: '';
					display: inline-block;
					border: 8px solid transparent;
				}
				&:before{
					top: 6px;
					border-bottom-color: #aaa;
				}
				&:after{
					top: 9px;
					border-bottom-color: #fff8ec;
				}
			}
			.text{
				color: #94979c;
				padding: 0 24px 14px 16px;
			}
		}
		.update-tip-hover{
			height: auto;
			.title:before{
				border-bottom-color: transparent;
				border-top-color: #aaa;
				top: 15px;
			}
			.title:after{
				border-bottom-color: transparent;
				border-top-color: #fff8ec;
				top: 12px;
			}
		}
		width: 100%;
		box-sizing: border-box;
		.box-h3-title {
			line-height: 16px;
			font-size: 16px;
			color: #333;
            margin:0;
		}
		.crm-g-c-black {
			line-height: 16px;
			font-size: 13px;
			color: #333;
		}
		.map-mode-select {
			display: flex;
			align-items: initial;
			margin-top: 10px;
		}
		
		.mapmode-setting-wrap {
			margin-top: 20px;

			.item {
				padding: 0;
				margin-bottom: 15px;
			}
		}
		.mapmode-setting-select {
			width: 220px;
			margin: 10px 0;
		}
		
		.mapmode-setting-tabs{
			display: flex;
			.radio-item{
				margin-right: 30px;
			}
		}
		.mapmode-setting-title{
			margin-top:20px;
		}
		.mapmode-setting-tabs{
			margin-top:12px;
		}
		.mapmode-setting-panel{
			border: #e6e6e6 solid 1px;
			border-radius: 3px;
			margin-top: 20px;
			padding: 12px 20px;
			display: none;
			counter-reset: listcounter2;
			&.panel-on{
				display: block;
			}
			.line-mb-mid:before{
				content: counter(listcounter2)'、';
				counter-increment: listcounter2;
			}
		}
		.mapmode-setting-btns{
			margin-top:50px;
		}
		.content {
			.colorsconfig-wrapper {
				width: 100%;
				display: flex;
				flex-flow: row wrap;
			}
			.colors-item{
				flex: 0 0 25%;
				text-align: left;
				margin-top: 8px;
			}
			.el-input-group__append {
				background-color: var(--color-neutrals01) ;
				border: none ;
				padding: 0;
			}
			.el-color-picker__trigger{
				border: none;
				padding: 0;
			}
			.el-color-picker__color{
				border: none;
				.el-color-picker__color-inner {
					border-radius:0px 2px 2px 0px !important;
				}
			}
			.el-input__inner{
				color: var(--color-neutrals15) !important;
			}
			
			
		}
	}

	// TODO: 拆解出来
	.limit-box,
	.recover-box {
		.rule-subtit {
			width: 1000px;
			padding: 8px 0;
			overflow: hidden;
			color: #999;
			border-bottom: 1px solid #e6e6e6;
			margin-bottom: 10px;
			span {
				float: left;
			}
			.ml-c {
				margin-left: 25px;
                display: inline-block;
                width: 118px;
                text-overflow: ellipsis;
                overflow: hidden;
                white-space: nowrap;
			}
            .ml-con{
                margin-left: 25px;
            }
		}
		.rule-item {
			width: 100%;
			padding: 5px 0;
			line-height: 34px;
			position: relative;
			&:after {
				content: ' ';
				display: block;
				clear: both;
				height: 0;
				visiblity: hidden;
			}
			.checkbox-sec {
				float: left;
				width: 140px;
				// margin-right: 34px;
				text-align: center;
			}
			.item-tit {
				width: 280px;
				padding: 0;
				float: left;
				word-wrap: break-word;
				.names {
					padding: 7px 0;
					line-height: 20px;
				}
			}
			.item-remind-words {
				float: left;
				width: 430px;
			}
			.item-remind-group {
				float: left;
				width: 560px;
				min-height: 34px;
				.remind-item {
					float: left;
					position: relative;
				}
				.remind-add-box {
					float: left;
					.add-remind-item {
						padding: 0;
						margin: 0;
						padding-left: 10px;
					}
				}
				.ipt-box {
					float: left;
					margin-right: 5px;
					.num {
						float: left;
						width: 30px;
						padding-right: 5px;
						text-align: right;
						overflow: hidden;
					}
					.b-g-ipt {
						width: 20px;
						padding: 5px 2px;
						text-align: right;
					}
					.unit {
						float: left;
					}
				}
				.type-select-box {
					width: 80px;
				}
				.remind-word-span {
					padding-left: 5px;
				}
				.del-remind-item {
					cursor: pointer;
				}
			}
			.type-select-box {
				float: left;
				width: 110px;
			}
			.hightsea-lb {
				width: 70px;
				padding: 0 10px;
				float: left;
				color: #999;
			}
			.hightsea-select-box {
				float: left;
				width: 120px;
				padding-right: 30px;
				overflow: hidden;
				.nowrap();
			}
		}
	}

	.limit-circle-box,
	.recover-circle-box {
		h3 {
			font-size: 14px;
			padding: 0 0 20px;
		}
		.climits-title {
			font-size: 14px;
			color: #999;
			padding-bottom: 10px;
			border-bottom: 1px solid #eaeaea;
		}
		.climits-box {
			height: 250px;
			overflow: hidden;
			.box-left, .box-right {
				height: 250px;
				overflow-y: auto;
				overflow-x: hidden;
			}
			.box-left {
				height: 250px;
				border-right: 1px solid #eee;
			}
			.box-right {
				width: 200px;
			}
			.climits-employee {
				width: 130px;
				height: 40px;
				line-height: 40px;
				padding-left: 10px;
				padding-right: 10px;
				border-left: 3px solid transparent;
				.nowrap();
				&:hover {
					border-left-color: #3487e2;
					background-color: #f4f6f9;
					cursor: pointer;
				}
			}
			.climits-circle {
				.circle-radio {
					padding: 8px 0 2px;
					max-width: 180px;
					span[title] {
						max-width: 155px;
						white-space: nowrap;
						overflow: hidden;
						text-overflow: ellipsis;
					}
				}

			}
		}
	}

	&-dialog {
		.t-head {
			height: 28px;
			padding: 10px;
			border: 1px solid #eee;
			border-bottom: none;
			line-height: 28px;
		}

		.t-wrap {
			height: 440px;
			border: 1px solid #eee;
			border-top: none;
		}
		.item-tit {
			float: left;
			margin-right: 10px;
		}
		.item-con {
			float: left;
			width: 200px;
			height: 32px;
			background: url("@{imgUrl}/sec-ld.gif") 50% no-repeat;
		}

		.t-tip {
			display: none;
			position: fixed;
			z-index: 999;
			height: 20px;
			line-height: 16px;
			padding: 5px 20px;
			-webkit-border-radius: 3px;
			-moz-border-radius: 3px;
			border-radius: 3px;
			background-color: #212b36;
			font-size: 12px;
			color: var(--color-neutrals01);
			&:after {
				position: absolute;
				content: ' ';
				display: block;
				width: 0;
				height: 0;
				overflow: hidden;
				border: 10px solid transparent;
				border-bottom-color: #212b36;
				bottom: -15px;
				left: 50%;
				margin-left: -10px;
				-moz-transform: scaleY(-1);
				-webkit-transform: scaleY(-1);
				-o-transform: scaleY(-1);
				transform: scaleY(-1);
				filter: FlipV;
			}
		;
		}
		.td-employeename {
			.employeename-ico {
				float: left;
				display: inline-block;
				width: 14px;
				height: 14px;
				margin-right: 5px;
			}
			.employeename-title {
				overflow: hidden;
				text-overflow: ellipsis;
				width: 96px;
				white-space: nowrap;
				float: left;
				display: inline-block;
			}
		}
		.tip-btn {
			display: inline-block;
			&:before {
				content: ' ';
				display: inline-block;
				width: 17px;
				height: 17px;
				margin: 0 5px 3px 0;
				background: url(../images/icos-crm.png) no-repeat;
				background-position: -292px -19px;
				overflow: hidden;
				vertical-align: middle;
			}
		}
	}

	.behavior-table-box {
		height: calc(~"100% - 200px");
		overflow: hidden;
		float: left;
		width: 100%;

		.js-edit, .js-del {
			color: #407fff;
			font-size: 12px;
			margin-right: 8px;
		}

	}
	.crm-account-rule-setting{
		padding-bottom: 20px;
		.title {
			padding-left: 8px;
			border-left: 4px solid var(--color-primary06);
			height: 24px;
			line-height: 24px;
			font-size: 16px;
			border-radius: 1px;
			&.animation-highlight {
				animation: title_highlight linear 3s 1;
			}
		}
		.setting-content{
			border: 1px solid var(--color-neutrals05);
			border-radius: 4px;
			margin-top: 16px;
			padding: 16px 16px;
			&.animation-highlight {
				animation: border_highlight linear 3s 1;
			}
			.backstage-switch{
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 16px;
				.crm-module-title {
					display: flex;
					flex-direction: column;
					font-size: 16px;
					font-weight: 400;
					line-height: 24px;
					color: var(--Text-H1, #181C25);
				}
				.on-off {
					padding-right: 16px;
					display: flex;
					align-items: center;
				}
				label {
					height: 18px;
					line-height: 18px;
					font-size: 12px;
					color: #ff522a;
					margin-right: 10px;
				}
			}
			.open-address-tip{
				display: flex;
				padding: 8px;
				flex-direction: column;
				align-items: flex-start;
				gap: 4px;
				align-self: stretch;
				border-radius: 4px;
				background: var(--color-neutrals02);
				font-size: 12px;
				font-style: normal;
				font-weight: 400;
				line-height: 18px;
				color: var(--color-neutrals15);
			}
			.add-switch-box{
				margin-top: 16px;
			}
			.add-sync-tip, .position-sync-tip{
				font-size: 12px;
				font-style: normal;
				font-weight: 400;
				line-height: 18px;
				color: var(--Text-H3, #91959E);
			}
			.switch-content-box{
				padding: 5px 0;
				.check-lb{
					color: var(--color-neutrals19);
					font-size: 12px;
					font-weight: 400;
					line-height: 18px;
				}
				
			}
		}
	}
}
.lang-en .crm-s-customerrule{
	.customer-rule-tab {
		height:56px;
	}
	.tab-con {
		top:56px;
	}
} 

.crm-s-customer-follow {
	.dialog-scroll {
		padding: 4px 0 10px 24px;
	}

	.crm-c-dialog-content {
		.dialog-con {
			padding: 0;
		}
	}

	.follow-box {
		.follow-objTitle {
			display: none;
			margin: 14px 0 8px 0;
			font-size:12px;
			color:#3d3d3d;
		}
		.follow-objSelect {
			margin-bottom: 16px;

		}
		.checkboxCom-titleName {
			font-size: 12px;
			color: #3d3d3d;
			margin-right: 8px;
		}
		.checkboxComponent-clear, .checkboxComponent-selectAll {
			font-size:12px;
			color: #407fff;
			margin-bottom: 8px;
			cursor: pointer;
			-webkit-user-select: none;
			-moz-user-select: none;
			-ms-user-select: none;
			user-select: none;
		}
		.mn-checkbox-box {
			display: inline-block;
			width: 130px;
			margin-right: 24px;
			float: left;
			box-sizing: border-box;
			padding: 8px 0;

			.mn-checkbox-item {
				margin-right: 8px;
				float: left;
				margin-top: 2px;
			}
			.checkbox-lable {
				color: #3d3d3d;
				float: left;
				width: 105px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
		}
	}

	.checkboxComponent-main {
		overflow: hidden;
	}

}

.crm-highseas-table{
	.action-btn{
		margin-right: 10px;
	}
}
