/**
 *@desc  cpq 开关配置
 *<AUTHOR>
 *@date 2022/12/12
 */
define(function (require, exports, module) {
	const dataList = require('./data');

	var cpqconfigure = Backbone.View.extend({
		initialize: function (opts) {
			this.setElement(opts.wrapper);
			this.getLicense();
		},

		// 是否购买了cpq的licenses
		getLicense(){
			let _this = this;
			CRM.api.get_licenses({
				key: ['cpq_app', 'cpq_base_app'],
				cb: licenses => {
					if (!licenses.cpq_app && !licenses.cpq_base_app) _this.setVisible();
					_this.init();
				}
			});
		},

		setVisible(){
			dataList.forEach(d => d.visible = false);
		},

		init: function (param) {
			require.async('vcrm/sdk', (sdk) => {
				sdk.getComponent('backstage').then((Backstage) => {
					this.initView(Backstage.default, dataList);
				});
			});
		},

		initView(Backstage, data) {
			const me = this;
			new Vue({
				el: me.$el[0],

				template: `
					<Backstage :data="dataList" :methodList="methodList" @change="change" @click="click"></Backstage>
				`,

				components: {
					Backstage
				},

				data() {
					return {
						dataList: data,
						methodList: {
							// beforeGetConfig,
							afterGetConfig: this.afterGetConfig,
							beforeSetConfig: this.beforeSetConfig,
							afterSetConfig: this.afterSetConfig,
						}

					}
				},

				watch: {},

				computed: {},

				created() {
				},

				mounted() {
					this.initDataList();
				},

				methods: {
					click(val, e) {

					},

					// 请求完开关后，需要重置开关是否显示
					afterGetConfig(res) {
						let cpq = res[0].find(item => item.key === 'cpq');
						let isOpen = cpq.value === '1';
						if(isOpen){
							CRM.util.forEachTreeData(this.dataList[0].moduleList, item => {
								item.isShow = true;
								if(item.key === 'bom_temp_node'){
									item.isShow = true;
								}
								if (item.key === 'standard_cpq') {
									item.isShow = true;
								}
								if (item.key === 'generate_standard_bom_based_on_order') {
									item.isShow = this.getConfigByKey('standard_cpq').value;
								}
								if (item.key === 'multiplexed_bom_mode') {
									item.isShow = true;
								}
								if (item.key === 'bom_duplicate_check') {
									let v = this.getConfigByKey('generate_standard_bom_based_on_order').value;
									item.isShow = ['1','2'].includes(v);
								}
							})
						}
					},

					// 开之前有些开关需要校验，或者弹提示
					beforeSetConfig(key, value, switchConfig) {
						switch (key) {
							case 'bom_print_template_has_sub_node':
								switchConfig.confirmMessage = value ? $t("确认开启{{name}}吗", {
                                    name:  $t("报价单订单销售合同打印时只包含母件产品")
                                }) : $t('确认关闭只打印母件开关吗？');
								break;
							case 'bom_duplicate_check':
								switchConfig.confirmMessage = value ? CRM.util.getI18n(['确认开启','BOM', '查重', '校验', '开关']) + '?' :  CRM.util.getI18n(['确认关闭','BOM', '查重', '校验', '开关']) + '?'; // [ignore-i18n]
								break;
                            case 'not_show_bom':
                                switchConfig.confirmMessage = value ? $t("确认开启{{name}}吗", {
                                    name: $t('crm.setting.cpq.not_show_bom', null, '选产品组合不进入选配配置页')
                                }) : $t("确认关闭{{name}}吗", {
                                    name: $t('crm.setting.cpq.not_show_bom', null, '选产品组合不进入选配配置页')
                                });
                                break;
							default:
								break;
						}
                        
                        let res = null;
                        if (switchConfig.setUrl?.includes('save_module_status')) {
                            res = {
                                param: {
                                    moduleCode: key,
                                    openStatus: value ? '1' : '0',
                                    tenantId: CRM.enterpriseId
                                }
                            };
                            if(key === 'generate_standard_bom_based_on_order'){
                                res.param.openStatus = value;
                            }
                        }

						return res;
					},

					// 开关 change 之后
					afterSetConfig(key, value, switchConfig) {
						switch (key) {
							// 根据生成标准 bom 开关，设置 bom 查重开关是否显示；
							case 'generate_standard_bom_based_on_order':
								let c = this.getConfigByKey('bom_duplicate_check');
								if(c) c.isShow = value != '0';
								break;
							default:
								break;
						}
					},

					getConfigByKey(key) {
						return this._allConfig[key];
					},

					initDataList() {
						this._allConfig = {};
						this.dataList.forEach(item => {
							CRM.util.forEachTreeData(item.moduleList, mo => {
								this._allConfig[mo.key] = mo;
							})
						})
					},

					async change({type, key, value}) {

					},
				}
			})
		},

		destroy: function () {
		}
	});

	module.exports = cpqconfigure;
});
