/**
 * @desc 报价单管理
 */
define(function(require, exports, module) {
	var util = CRM.util;
  let isTPMVersion1 = FS.util.getUserAttribute('isTPMVersion1');

  if(!isTPMVersion1){  //TPM2
    return Backbone.View.extend({
      initialize: function(opts) {
        require.async('paas-tpm/sdk', ({ getLicenseView }) => {
          getLicenseView().then(LicenseView => {
            LicenseView.init(opts.wrapper[0]);
          })
        })
      }
    })
  }

	return Backbone.View.extend({

    template: require('./tpl-html'),

    events: {
      'click .crm-doclink': 'onClickToggleMarketing',
      'click .tpm-tab .item': 'onSwitchTab'
    },

		initialize: function(opts) {
      this.setElement(opts.wrapper);
      this.getLicense();
    },

    onClickToggleMarketing() {
      FS.tpl.navRouter.navigate('#crmmanage/=/module-tpm/image-show', { trigger: false });
      this.toggleMarketing();
    },

    onSwitchTab(event) {
      let target = event.target, tabIndex = +target.dataset.tab;
      console.log(tabIndex, !tabIndex - 0);
      this.$(target).addClass('cur').siblings('.cur').removeClass('cur');
      this.$('.tpm-tab-con').removeClass(`active-tab${!tabIndex - 0}`).addClass(`active-tab${tabIndex}`)
    },

    renderTpl(data) {
      let langres = FS.contacts.getCurrentEmployee().language;
      data ? '' : data = {}
      data['language'] = langres
      this.$el.html(this.template(data));
      this.$marketing = this.$('.tpm-marketing-img');
      if(this.license_status !== 'invalid'){  //已购买，处理营销活动图
        this.$marketing.hide().appendTo('body');
        this.toggleMarketing();
      }
    },

    //切换营销页
    toggleMarketing() {
      if(this.license_status === 'invalid') return;  //如果未购买直接返回
      const params = FS.util.getTplQueryParams();
      if(params.module === 'tpm'){
        params.image === 'show' ? this.$marketing.show() : this.$marketing.hide();
      }
    },

    renderSwitch(value) {
      if(this.$switch){
        this.$switch.value = value;
        return;
      }
      this.$switch = FxUI.create({
        wrapper: '#tpm-switch-box',
        template: `
          <div>
            <div class="tpm-switch-box">
              <div class="tpm-row">
                <span>{{$t('启用营销活动管理')}}</span>
                <fx-switch size="medium" :disabled="value" :value="value" inactive-color="#D7DBE2" @change="onChange"></fx-switch>
                <span v-if="isActiving">{{$t('正在初始化')}}...</span>
                <span v-if="isActiveError && !isActiving" style="color:#FF522A;">{{$t('开启失败，请重试')}}...</span>
              </div>
              <span class="tpm-tip">{{$t('营销活动管理开启后，不可关闭')}}</span>
            </div>
          </div>`,
        data() {
          return {
            isActiving: false,
            isActiveError: false,  //启用失败
            value,
            auditType: "0"
          }
        },
        methods: {
			getAuditType() {
				util.FHHApi({
					url: '/EM1HFMCGService/config/get',
					timeout: 15000,
					data: {key: 'TPM_AUDIT_MODE'},
					success: ({Result, Value}) => {
						if (Result.StatusCode === 0) {
							this.auditType = Value.value;
						}
					}
				}, {errorAlertModel: 1})
			},
			onSaveAuditType() {
				util.FHHApi({
					url: '/EM1HFMCGService/config/set',
					timeout: 15000,
					data: {key: "TPM_AUDIT_MODE", value: this.auditType},
					success: ({Result}) => {
						if (Result.StatusCode === 0) {
							this.$message({
								message: $t('保存成功'),
								type: 'success'
							});
						} else {
							this.$message.error($t('保存失败'));
						}
					}
				}, {errorAlertModel: 1})
			},
			onChangeAuditType(auditType) {
				this.$confirm([$t('抽检切换为全部检核'), $t('全部检核切换为抽检')][auditType - 0]).then(({value}) => {
					this.onSaveAuditType();
				}).catch(() => {
					this.auditType = !+auditType - 0 + "";
				})
			},
       onChange(val) {
        if(val){
          this.isActiving = true;
          util.FHHApi({
            url: '/EM1HFMCGService/license/active',
            timeout: 15000,
            data: { "appCode": "FMCG.TPM.2" },
            success:({ Result }) => {
              this.isActiving = false;
              if(Result.StatusCode !== 0){
                this.isActiveError = true;
                this.value = false;
              }else{
        this.getAuditType();
                this.renderBudgetSwitch(false, true);
              }
            }
          }, {  errorAlertModel: 1 })
        }
      }
		},
		  created() {
			  this.value && this.getAuditType();
		  }
      })
    },

    renderBudgetSwitch(value, isTPMActive) {
      console.log(this.$('.item[data-tab="1"]'));
      if(!isTPMActive || !value){  //如果没有开启TPM直接隐藏预算
        this.$('.item[data-tab="1"]').hide();
        return;
      }
      if(this.$budgetSwitch){
        this.$budgetSwitch.value = value;
        this.$budgetSwitch.isTPMActive = isTPMActive;
        return;
      }
      this.$budgetSwitch = FxUI.create({
        wrapper: '#tpm-budget-switch-box',
        template: `
          <div class="tpm-switch-box">
            <div class="tpm-row">
              <span class="switch-label">{{$t('开启【费用预算表】对象')}}</span>
              <fx-switch size="medium" :disabled="value" v-model="value" inactive-color="#D7DBE2" @change="onChange"></fx-switch>
              <span v-if="errorMsg" style="color:#FF522A;">{{errorMsg}}</span>
            </div>
            <p class="tpm-tip-default">{{$t('开启费用预算表对象，对营销活动费用进行合理管控，经销商业务员在申请活动时受相关费用预算表约束')}}</p>
            <span class="tpm-tip">{{$t('费用预算表对象开启后，不可关闭')}}</span>
            <p class="form-title">{{$t('规则设置')}}</p>
            <div class="budget-start-month">
              <div class="tpm-row">
                <span>{{$t('选择预算起始月')}}</span>
                <div class="tpm-row-content">
                  <fx-select v-model="month" :options="options"></fx-select>
                  <p class="tpm-tip-default">{{$t('说明：选择起始月，默认为起始月1日至尾月最后一日')}}</p>
                </div>
              </div>
            </div>
            <fx-button type="primary" :disabled="!value" @click="onSaveMonth">{{$t('保存')}}</fx-button>
          </div>`,
        data() {
          return {
            errorMsg: '',  //启用失败
            isTPMActive,
            value,
            month: 1,
            options: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(val => {
              return { label: $t(`${val}月`), value: val }
            })
          }
        },
        methods: {
          getMonth() {
			  util.FHHApi({
				  url: '/EM1HFMCGService/config/get',
				  timeout: 15000,
              data: { key: "TPM_BUDGET_START_MONTH" },
              success:({ Result, Value }) => {
                if(Result.StatusCode === 0){
                  this.month = +(Value.value || 1);
                }
              }
            }, {  errorAlertModel: 1 })
          },
          onSaveMonth() {
			  util.FHHApi({
				  url: '/EM1HFMCGService/config/set',
				  timeout: 15000,
              data: { key: "TPM_BUDGET_START_MONTH", value: this.month },
              success:({ Result, Value }) => {
                if(Result.StatusCode === 0){
                  this.$message({
                    message: $t('保存成功'),
                    type: 'success'
                  });
                }else{
                  this.$message.error($t('保存失败'));
                }
              }
            }, {  errorAlertModel: 1 })
          },
          onChange(val) {
            if(val){
              if(!this.isTPMActive){
                this.errorMsg = $t('预开启【费用预算表】，请先开启营销活动管理');
                this.value = false;
                return;
              }
              this.errorMsg = '';
				util.FHHApi({
					url: '/EM1HFMCGService/license/active',
					timeout: 15000,
                data: { "appCode": "FMCG.TPM_BUDGET.2" },
                success:({ Result }) => {
                  if(Result.StatusCode !== 0){
                    this.errorMsg = $t('开启失败，请重试');
                    this.value = false;
                  }
                }
              }, {  errorAlertModel: 1 })
            }
          }
        },
        created () {
          this.getMonth();
        }
      })
    },

    getLicense() {
		util.FHHApi({
        url: '/EM1HFMCGService/license/get',
        data: { "appCode": "FMCG.TPM.2" },
        success:({ Result, Value }) => {
          if(Result.StatusCode === 0){
            const { begin_time, end_time, license_status } = Value.externalData;
            this.license_status = license_status;
            if(license_status !== 'invalid'){  //已购买
              this.renderTpl({ license_status });
              this.$el.find('.tpm-VLD').text(`${$t('有效期')}：${FS.moment(begin_time).format('YYYY-MM-DD')} ${$t('至')} ${FS.moment(end_time).format('YYYY-MM-DD')}`);
              if(license_status !== 'expired') { //未过期
                this.renderSwitch(!_.isEmpty(Value.license));
                this.getBudgetLicense(!_.isEmpty(Value.license));
              }
              return;
            }
          }
          this.renderTpl();
        }
      })
    },

    getBudgetLicense(isTPMActive) {
      if(!isTPMActive)
        this.renderBudgetSwitch(false, false);  //如果TPM未开启则直接渲染
      else{
		  util.FHHApi({
          url: '/EM1HFMCGService/license/batch_validate',
          data: { "appCodeList": ["FMCG.TPM_BUDGET", "FMCG.TPM_BUDGET.2"]},
          success:({ Result, Value }) => {
            let { StatusCode } = Result, { data = [] } = Value;
            let isActive = StatusCode === 0 && data.some(item => item.activated);
            this.renderBudgetSwitch(isActive, isTPMActive);
            // let isActive = Result.StatusCode === 0 ? !_.isEmpty(Value.license) : false;
            // this.renderBudgetSwitch(!_.isEmpty(Value.license), isTPMActive);
          }
        })
      }
    },

    destroy() {
      console.log('销毁了');
      this.$marketing.remove();
      this.remove();
    },
	});
});
