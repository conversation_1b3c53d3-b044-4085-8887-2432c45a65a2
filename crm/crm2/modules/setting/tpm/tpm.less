.crm-s-tpm {
  .crm-tit{
    display: flex;
    align-items: center;
    .tpm-VLD{
      color: var(--color-neutrals11);
      font-size: 14px;
      margin-left: 12px;
    }
  }
}
.tpm-tab{
  padding-left: 22px;
}
.tpm-tab-con {
  .crm-p20{
    padding: 20px 30px;
  }
  .tpm-budget{
    display: none;
  }
  &.active-tab0{
    .tpm-container{
      display: block;
    }
    .tpm-budget{
      display: none;
    }
  }
  &.active-tab1{
    .tpm-container{
      display: none;
    }
    .tpm-budget{
      display: block;
    }
  }
  #tpm-budget-switch-box{
    .tpm-switch-box{
      padding-top: 0;
    }
  }
}

.tpm-expired-box{
  background: #F7F9FC;
  border-radius: 1px;
  padding: 22px 22px 32px 22px;
  margin-bottom: 32px;
  text-align: center;
  img{
    width: 62px;
    height: 62px;
    margin-bottom: 8px;
  }
  h3{
    margin-bottom: 6px;
  }
  p{
    color: var(--color-neutrals15);
    font-size: 12px;
    line-height: 18px;
    margin-bottom: 4px;
    &:last-of-type{
      margin-bottom: 0;
    }

	  span {
      color: var(--color-primary06);
      display: inline-block;
      margin: 0 4px;
    }
  }
}

.tpm-introduction{
  color: var(--color-neutrals14);
  h2, h3{
    font-size: 16px;
    line-height: 22px;
    color: var(--color-neutrals19);
    padding: 0;
  }
  h2{
    margin-bottom: 7px;
  }
  h3{
    line-height: 18px;
  }

  >img{
    width: 100%;
    max-width: 660px;
    margin-top: 12px;
  }

  .crm-intro{
    margin-top: 28px;
  }

  ul {
		counter-reset: number;
		li{
      line-height: 18px;
      color: var(--color-neutrals14);
      padding: 0;
      margin-bottom: 6px;
      &:before {
        font-size: 12px;
        padding-right: 6px;
      }

      &.level-1{
        color: var(--color-neutrals19);
          margin-top: 12px;
        &:first-of-type{
          margin-top: 10px;
        }
        &::before{
          content: counter(number)".";
          padding-right: 0;
          counter-increment: number;
        }
      }
      ul{
        margin-top: 6px;
        padding-left: 10px;
        counter-reset: number1;
        li::before{
          content: counter(number1)")";
          counter-increment: number1;
        }
      }
    }
  }
}

.tpm-switch-box{
  padding: 24px 0;
  color: var(--color-neutrals14);
  font-size: 12px;
  line-height: 18px;
  .tpm-row{
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    .el-switch{
      margin: 0 12px;
    }
    .switch-label{
      font-size: 16px;
    }
    span:first-child{
      color: var(--color-neutrals19);
    }
  }
  .tpm-tip{
    color: #ED7D31;
  }
  .tpm-tip-default{
    color: #9E9E9E;
    line-height: 20px;
    font-size: 14px;
  }
  .form-title{
    color: #4A4A4A;
    font-size: 14px;
    margin-top: 24px;
  }
  .budget-start-month{
    border: 1px solid #E6E6E6;
    margin-top: 8px;
    margin-bottom: 32px;
    padding: 20px 22px;

    .tpm-row{
      align-items: flex-start;
      >span{
        color: #4A4A4A;
        display: inline-block;
        line-height: 40px;
        font-size: 14px;
        margin-right: 12px;
	  }
      .tpm-tip-default{
        font-size: 12px;
        margin-top: 8px;
	  }
	}
  }
}

.tpm-audit-types {
	.title {
		font-size: 16px;
		margin-top: 8px;
		margin-bottom: 16px;
	}

	.el-radio-group {
		.audit-type {
			font-size: 12px;
			margin-bottom: 12px;

			.el-radio__label {
				font-size: 14px;
				color: var(--color-neutrals19);
			}

			.audit-type-tip {
				color: var(--color-neutrals14);
				padding-left: 30px;
			}
		}
	}
}

.tpm-marketing-img{
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--color-neutrals01);
  text-align: center;
  overflow: auto;
  z-index: 2;
  .shadow-box{
    width: 75%;
    margin: 0 auto;
    padding-top: 16px;
    box-shadow: 2px 0 20px rgba(33, 43, 54, 0.05), -2px 0px 20px rgba(33, 43, 54, 0.05);
    font-size: 0;
    p{
      color: var(--color-neutrals15);
      background: #FFF6ED;
		padding: 10px 24px;
      font-size: 12px;
      span{
        color: var(--color-primary06);
        display: inline-block;
        margin: 0 4px;
      }
    }
    img{
      width: 100%;
    }
  }

	&.fixed {
    position: fixed;
    padding-top: 56px;
  }
}
