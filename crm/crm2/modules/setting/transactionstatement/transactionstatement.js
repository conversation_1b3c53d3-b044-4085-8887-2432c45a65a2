/**
 * 对账单设置
 */
define(function(require, exports, module) {
	'use strict';
	const Tpl = require('./template/tpl-html');
	const OpenTransation = require('./components/open-transaction');
	const InitPlan = require('./components/init-plan');
	const EditTransaction = require('./components/edit-transaction');
	const SetFlow = require('./components/set-flow');
	const OpenSignature = require('./components/open-signature');
	const StepTitle = require('./components/step-title');
	const {
		STATUS,
		getReconciliationStatus,
	} = require('./global');

	const crmUtil = CRM.util;
	
	const View = Backbone.View.extend({
		initialize(opts) {
			this.status = 0;
			this.curStep = 0;

			this.setElement(opts.wrapper);
		},

		render() {
			const tpl = Tpl();
			this.$el.html(tpl);

			this.$stepWrapper = this.$('.transaction-list-wrapper');
			this.renderComponent();
		},

		renderStep() {
			const status = this.status;
			const curStep = this.curStep;
			const listWrapper =  this.$stepWrapper;

			const stepInstance = FxUI.create({
				name: 'TransactionSteps',
				wrapper: this.$stepWrapper[0],
				components: {
					OpenTransation,
					InitPlan,
					EditTransaction,
					SetFlow,
					OpenSignature,
					StepTitle,
				},
				template: `
					<open-transation
						:is-disable="false"
						:is-init="isInitTransaction"
						:cur-step="curStep"
						@init="initTransactionHandle"
					/>
				`,
				data() {
					return {
						curStep: curStep,
						status: status,
						planObj: Object.freeze({
							_id: '',
							name: '',
						}),
						tableData: [],
						initPlanTimer: null,
					};
				},

				computed: {
					isInitPlan() {
						return this.status === STATUS.INITIALIZED;
					},
					isInitTransaction() {
						return this.status >= STATUS.OPENED;
					},
					isInitializingPlan() {
						return this.status === STATUS.INITIALIZING;
					},
					isPlanDisable() {
						// 没有开启对账单或者对账方案接口还没返回
						return !this.isInitTransaction || !this.planObj._id;
					},
				},

				created() {
					if (this.isInitTransaction) {
						this.getPlan();
					}

					if (this.isInitializingPlan) {
						this.checkStatus();
					}
				},

				beforeDestroy() {
					this.clearInitPlanTimer();
				},

				methods: {
					clearInitPlanTimer() {
						if (this.initPlanTimer) {
							clearTimeout(this.initPlanTimer);
							this.initPlanTimer = null;
						}
					},

					initTransactionHandle(value) {
						this.status = value;

						if (this.isInitTransaction) {
							this.renderList();
						}
					},

					getCrmList() {
						return new Promise((resolve, _reject) => {
							seajs.use('crm-modules/page/list20/list20', List => {
								resolve(List);
							});
						});
					},

					async renderList() {
						const List = await this.getCrmList();
						const options = {        
							wrapper: listWrapper,
							jsPath: 'crm-modules/page/list/list',
							listOptions: {  
								apiname: 'ReconciliationPlanObj',
							}
						}
						const tb = new List(options);
						tb.render(['ReconciliationPlanObj']);
					},

					async initPlanHandle(value) {
						this.status = value;
						this.checkStatus();
					},

					/**
					 * 轮询初始化状态
					 * NOTE: 暂时用不上了，初始化直接成功返回4，失败会保持在2
					 */
					async checkStatus () {
						// 保险起见，每次先清除定时器
						this.clearInitPlanTimer();

						// 初始化中，轮询初始化状态
						if (this.isInitializingPlan) {
							this.initPlanTimer = setTimeout(async () => {
								const status = await getReconciliationStatus();
								this.status = status;
								this.checkStatus();
							}, 1000);
						}
					},

					getPlan() {
						const data = {
							search_query_info: '{\"limit\":1,\"offset\":0,\"filters\":[],\"orders\":[{\"fieldName\":\"last_modified_time\",\"isAsc\":false}]}',
						}
						crmUtil.FHHApi({
						    url: '/EM1HNCRM/API/v1/object/ReconciliationPlanObj/controller/List',
						    data,
						    success: res => {
						        if (res.Result.StatusCode === 0) {
									const plan = res.Value.dataList[0];
									const {
										_id,
										reconciliation_data_source = [],
										name,
										object_describe_api_name
									} = plan;
									const list = reconciliation_data_source;
									const planObj = { _id, name, api_name: object_describe_api_name };

									const tableData = list.map(({
										dest_object_display_name,
										dest_object_api_name,
									}) => ({
										name: dest_object_display_name,
										api_name: dest_object_api_name,
									}));
									const statementObj = {
										name: $t('交易对账单'),
										api_name: 'TransactionStatementObj',
									};

									// 交易对账单必须放在最前面
									tableData.unshift(statementObj);

									this.planObj = Object.freeze(planObj);
									this.tableData = Object.freeze(tableData);

									return;
						        }

						        crmUtil.error(res.Result.FailureMessage);
						    }
						});
					},
				}
			});
		},

		getCrmList() {
			return new Promise((resolve, _reject) => {
				seajs.use('crm-modules/page/list20/list20', List => {
					resolve(List);
				});
			});
		},

		async renderList() {
			const List = await this.getCrmList();
			const options = {        
				wrapper: this.$('.transaction-list-wrapper'),
				jsPath: 'crm-modules/page/list/list',
				listOptions: {  
					apiname: 'ReconciliationPlanObj',
				}
			}
			const tb = new List(options);
			tb.render(['ReconciliationPlanObj']);
		},

		async renderComponent() {
			try {
				await this.initStatus();
			} catch (error) {
				console.log(error);
			}
			if (this.status >= STATUS.OPENED){
				this.renderList();
			} else {
				this.renderStep();
			}
		},

		/**
		 * 初始化状态
		 * TODO: 是不是可以放到 TransactionSteps 中？跳动怎么解决？
		 */
		async initStatus() {
			try {
				const status = await getReconciliationStatus();
				this.status = status;
			} catch (error) {
				console.error(error);
			}
		},
	});

	module.exports = View;
});
