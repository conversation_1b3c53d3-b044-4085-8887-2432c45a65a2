define(function(require, exports, module) {
	'use strict';
	
	const crmUtil = CRM.util;
	const API_NAME = 'ReconciliationPlanObj';
	const STATUS_KEY = 'reconciliation_config_status';

	/**
	 * 0: 对账单未开启
	 * 1：对账单开启中
	 * 2：对账单已开启
	 * 3：对账方案初始化中
	 * 4：对账方案初始化完成
	 */
	const STATUS = {
		UN_OPEN: 0,
		OPENNING: 1,
		OPENED: 2,
		INITIALIZING: 3,
		INITIALIZED: 4,
	};

	const getReconciliationStatus = () => {
		return new Promise((resolve, reject) => {
			crmUtil.FHHApi({
				url: '/EM1HNCRM/API/v1/object/reconciliation/service/get_config',
				success: res => {
					if (res.Result.StatusCode === 0) {
						const status = res.Value[STATUS_KEY];
						resolve(+status);
						return;
					}

					crmUtil.error(res.Result.FailureMessage);
					reject(res);
				}
			});
		});
	};


	module.exports = {
		STATUS_KEY,
		STATUS,
		API_NAME,
		getReconciliationStatus,
	}
});
