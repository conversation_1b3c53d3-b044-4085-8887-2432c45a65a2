.transaction-content {
	padding: 20px;
	box-sizing: border-box;
}

.transaction-tips {
	list-style: decimal inside;
}

.transaction-list-wrapper {
	margin-top: 20px;
	margin-left: 20px;

	.el-step__title {
		display: none;
	}

	.el-step__head {
		padding-top: 7px;

		> .el-step__line {
			width: 1px;
			top: 35px;
			bottom: -3px;
		}

		> .el-step__icon {
			border-width: 1px;
		}
	}

	.el-step__description {
		padding: 12px;
		
		&:hover {
			background-color: #F7F8FA;
		}
	}
}

.transaction-step-item {
	color: #333;
	display: flex;
	align-items: center;
	&.is-collapse {
		> .transaction-step-content {
			max-height: 0;
		}
	}

	&.is-disable {
		> .transaction-step-title {
			cursor: default;
		}
	}

	> .transaction-step-title {
		line-height: 24px;
		font-size: 14px;
		cursor: pointer;
	}

	> .transaction-step-content {
		margin-left: 20px;
		max-height: 1000px;
		transition: all ease-in-out .4s;
		overflow: hidden;

		> .transaction-step-desc {
			margin: 10px 0;
			color: var(--color-neutrals15);
		}
	
		> .transaction-step-operations {
			display: flex;
			align-items: center;
	
			> .switch-label {
				margin-left: 5px;
			}
		}
	}

	.transaction-table-header {
		background-color: var(--color-neutrals03);
		color: var(--color-neutrals15);
	}
}

.transaction-table {
	display: inline-block;
	width: auto;

	.plan-setting-btn {
		color: var(--color-info06);
		cursor: pointer;
	}

	.el-link--primary {
		color: var(--color-info06);
	}

	.transaction-table-header {
		background-color: var(--color-neutrals03);

		th {
			background-color: transparent;
		}
	}
}
