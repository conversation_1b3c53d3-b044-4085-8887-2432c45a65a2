/**
 * 编辑对账单对象
 */
define(function(require, exports, module) {
	'use strict';

	const StepTitle = require('./step-title');
	const mixins = require('./mixins');
	
	const component = {
		name: 'EditTransaction',

		template: `
			<div
				class="transaction-step-item"
				:class="stepItemClass"
			>
				<step-title
					:title="$t('crm.manage.transaction.edit')"
					:show-tip="isDisable"
					:tip-content="$t('crm.manage.planobj.tip2')"
					@on-click="onTitleClick"
				/>
				<div class="transaction-step-content">
					<div class="transaction-step-desc">
						{{ $t('crm.manage.planobj.init_desc2') }}
					</div>
					<div class="transaction-table">
						<fx-table
							:data="tableData"
							border
							header-row-class-name="transaction-table-header"
						>
							<fx-table-column
								prop="name"
								:label="$t('对象名称')"
								width="300">
							</fx-table-column>
							<fx-table-column
								:label="$t('操作')"
								width="150">
								<template slot-scope="scope">
									<fx-link
										v-if="scope.$index === 0"
										type="primary"
										href="/XV/UI/manage#crmmanage/=/module-sysobject/api_name-TransactionStatementObj"
										target="_blank"
										:underline="false"
									>{{ $t('去配置') }}</fx-link>
									<span
										v-else
										class="plan-setting-btn"
										@click="onSettingBtnClick(scope.$index)"
									>{{ $t('crm.manage.planobj.edit_field') }}</span>
								</template>
							</fx-table-column>
						</fx-table>
					</div>
				</div>
			</div>
		`,
		
		components: {
			StepTitle,
		},

		mixins: [mixins],

		props: {
			tableData: {
				type: Array,
				default: () => ([]),
				required: true,
			},
			planObj: {
				type: Object,
				required: true,
				default: () => ({}),
			},
		},

		data() {
			return {
				isCollapse: this.curStep !== 2,
			}
		},

		methods: {
			onEditSuccess(data) {
				this.$emit('refresh');
			},

			onSettingBtnClick(index) {
				const id = this.planObj._id;
				const destObjectApiName = this.tableData[index].api_name;
				CRM.api.edit({
					id,
					apiname: 'ReconciliationPlanObj',
					destObjectApiName,
					isInitialized: true,
					success: this.onEditSuccess.bind(this),
				});
			}
		}
	};

	module.exports = component;
});
