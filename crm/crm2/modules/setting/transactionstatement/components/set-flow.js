/**
 * 配置对账流程、打印模板
 */
define(function(require, exports, module) {
	'use strict';
	const StepTitle = require('./step-title');
	const mixins = require('./mixins');
	
	const component = {
		name: 'SetFlow',

		template: `
			<div
				class="transaction-step-item"
				:class="stepItemClass"
			>
				<step-title
					:title="$t('crm.manage.transaction.flow_tpl')"
					:show-tip="isDisable"
					:tip-content="$t('crm.manage.transaction.flow_tip')"
					@on-click="onTitleClick"
				/>
				<div class="transaction-step-content">
					<div class="transaction-step-desc">
						{{ $t('crm.manage.transaction.flow_desc') }}
					</div>
					<div class="transaction-step-operations">
						<fx-button
							size="small"
							@click="onPrintBtnClick"
						>{{ $t('crm.manage.transaction.tpl_setting') }}</fx-button>
						<fx-button
							size="small"
							@click="onFlowBtnClick"
						>{{ $t('配置流程') }}</fx-button>
					</div>
				</div>
			</div>
		`,

		components: {
			StepTitle,
		},

		mixins: [mixins],

		data() {
			return {
				isCollapse: this.curStep !== 2,
			}
		},

		methods: {
			onFlowBtnClick() {
				// 打开【流程管理】-【业务流程】
				window.open('/XV/UI/manage#crmmanage/=/module-businessflow');
			},

			onPrintBtnClick() {
				// 打开【对象管理】-【对象模板管理】
				window.open('/XV/UI/manage#crmmanage/=/module-templatemanage');
			}
		}
	};

	module.exports = component;
});
