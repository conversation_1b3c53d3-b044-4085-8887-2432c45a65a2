define(function(require, exports, module) {
	'use strict';
	
	const mixins = {
		props: {
			curStep: {
				type: Number,
				required: true,
				default: 0,
			},
			isDisable: {
				type: Boolean,
				default: true,
			},
		},

		computed: {
			stepItemClass() {
				const className = [];
				if (this.isCollapse) {
					className.push('is-collapse');
				}
				if (this.isDisable) {
					className.push('is-disable');
				}

				return className.join(' ');
			}
		},

		methods: {
			onTitleClick() {
				if(this.isDisable) {
					return;
				}

				this.isCollapse = !this.isCollapse;
			}
		}
	};

	module.exports = mixins;
});
