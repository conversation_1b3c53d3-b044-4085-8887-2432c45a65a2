/**
 * 开启对账单
 */
define(function(require, exports, module) {
	'use strict';
	const crmUtil = CRM.util;
	const StepTitle = require('./step-title');
	const mixins = require('./mixins');
	const { STATUS_KEY } = require('../global');
	
	const component = {
		name: 'OpenTransation',

		template: `
			<div
				class="transaction-step-item"
			>
			    <div class="transaction-step-title">
					{{$t('crm.manage.transaction.open')}}
				</div>
				<div class="transaction-step-content">
					<div class="transaction-step-operations">
						<fx-switch
							v-model="isInit"
							size="mini"
							:disabled="isInit"
							:before-change="beforeChanage">
						</fx-switch>
						<span class="switch-label">{{ switchText }}</span>
					</div>
				</div>
			</div>
		`,

		components: {
			StepTitle,
		},

		mixins: [mixins],

		props: {
			isInit: {
				type: Boolean,
				default: false,
			},
		},

		data() {
			return {
				isCollapse: this.curStep !== 0,
			}
		},

		computed: {
			switchText() {
				return this.isInit ? $t('已启用') : $t('未启用');
			},
		},

		methods: {
			beforeChanage() {
				this.$confirm(
					$t('crm.manage.transaction.open_tip'),
					$t('提示'),
					{
						confirmButtonText: $t('确定'),
						cancelButtonText: $t('取消'),
						type:'warning'
					}
				).then(() => {
					this.initTransaction();
				});

				return false;
			},

			initTransaction() {
				crmUtil.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/reconciliation/service/enable',
                    success: res => {
                        if (res.Result.StatusCode === 0) {
							const status = res.Value[STATUS_KEY];
							this.$emit('init', +status);

							return;
                        }

                        crmUtil.error(res.Result.FailureMessage);
                    }
                });
			},
		}
	};

	module.exports = component;
});
