/**
 * 步骤条title
 */
define(function(require, exports, module) {
	'use strict';
	
	const component = {
		name: 'StepTitle',

		template: `
			<div
				class="transaction-step-title"
				@click="onClick"
			>
				{{ title }}
				<fx-tooltip
					v-if="showTip"
					effect="dark"
					:content="tipContent"
					placement="right"
				>
					<i class="el-icon-lock"></i>
				</fx-tooltip>
			</div>
		`,

		props: {
			showTip: {
				type: Boolean,
				default: false,
			},
			tipContent: {
				type: String,
				default: ''
			},
			title: {
				type: String,
				default: '',
				required: true,
			}
		},

		methods: {
			onClick() {
				this.$emit('on-click');
			}
		}
	};

	module.exports = component;
});
