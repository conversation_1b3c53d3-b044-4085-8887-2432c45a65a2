/**
 * 编辑对账方案并初始化
 */
define(function(require, exports, module) {
	'use strict';

	const crmUtil = CRM.util;
	const StepTitle = require('./step-title');
	const mixins = require('./mixins');
	const { API_NAME, STATUS_KEY } = require('../global');
	
	const component = {
		name: 'InitPlan',

		template: `
			<div
				class="transaction-step-item"
				:class="stepItemClass"
			>
				<step-title
					:title="$t('crm.manage.planobj.init_title')"
					:show-tip="isDisable"
					:tip-content="$t('crm.manage.planobj.tip1')"
					@on-click="onTitleClick"
				/>
				<div class="transaction-step-content">
					<div class="transaction-step-desc">
						{{ $t('crm.manage.planobj.init_desc1') }}
					</div>
					<div class="transaction-step-operations">
						<fx-button
							size="small"
							type="primary"
							:disabled="isInitializing"
							@click="openEditDesigner"
						>{{ $t('crm.manage.planobj.edit') }}</fx-button>
						<span
							v-if="isInit"
							style="color: #b4b6c0; margin-left: 10px;"
						>{{ $t('已完成初始化') }}</span>
						<fx-button
							v-else
							size="small"
							:loading="isInitializing"
							@click="onInitBtnClick"
						>{{ $t('初始化') }}</fx-button>
						<span style="font-size:10px;margin-left:10px" v-if="isInit">
							<fx-link :underline="false" type="standard" @click="onReInitBtnClick">{{ $t('crm.transactionstatement.reinit') }}</fx-link>
						</span>
					</div>
				</div>
			</div>
		`,

		components: {
			StepTitle,
		},

		mixins: [mixins],

		props: {
			isInit: {
				type: Boolean,
				default: false,
			},
			planObj: {
				type: Object,
				require: true,
			},
			isInitializing: {
				type: Boolean,
				default: false,
			}
		},

		data() {
			return {
				isCollapse: this.curStep !== 1,
			};
		},

		methods: {
			onEditSuccess() {
				this.$emit('refresh');
			},

			openEditDesigner() {
				const id = this.planObj._id;
				CRM.api.edit({
					id,
					isInitialized: this.isInit,
					apiname: API_NAME,
					success: this.onEditSuccess.bind(this),
				});
			},

			initPlan() {
				const reconciliation_plan_id = this.planObj._id;
				const data = {
					reconciliation_plan_id
				};
				crmUtil.FHHApi({
					url: '/EM1HNCRM/API/v1/object/reconciliation/service/init',
					data,
					success: res => {
						if (res.Result.StatusCode === 0) {
							this.$emit('init', +res.Value[STATUS_KEY]);

							const message = `${$t(
								'crm.transactionstatement.init.complate',
								null,
							)}`;

							this.$confirm(
								message,
								$t('提示'),
								{
									confirmButtonText: $t('知道了'),
									type:'info',
									showCancelButton: false,
								}
							)

							return;
						}

						crmUtil.error(res.Result.FailureMessage);
					}
				});
			},

			onReInitBtnClick() {
				crmUtil.FHHApi({
					url: '/EM1HNCRM/API/v1/object/transaction_statement/service/has_data',
					data: {},
					success: res => {
						if (res.Result.StatusCode === 0 && res.Value.hasData) {
							this.hasDataNotice();
						} else {
							this.reinitPlan();
						}
					}
				});
			},

			hasDataNotice() {
				const message = `${$t(
					'crm.transactionstatement.has.data',
					null,
				)}`;
				this.$confirm(
					message,
					$t('提示'),
					{
						confirmButtonText: $t('知道了'),
						type:'info',
						showCancelButton: false,
						dangerouslyUseHTMLString: true
					}
				)
			},

			reinitPlan() {
				const reconciliation_plan_id = this.planObj._id;
				const data = {
					reconciliation_plan_id
				};
				crmUtil.waiting();
				crmUtil.FHHApi({
					url: '/EM1HNCRM/API/v1/object/reconciliation/service/re_init',
					data,
					success: res => {
						if (res.Result.StatusCode === 0 ) {
							crmUtil.waiting(false);
							this.$emit('init', +res.Value[STATUS_KEY]);
							const message = `${$t(
								'crm.transactionstatement.has.reset',
								null,
							)}`;
							this.$confirm(
								message,
								$t('提示'),
								{
									confirmButtonText: $t('知道了'),
									type:'info',
									showCancelButton: false,
								}
							);
						} 
					}
				});
			},
			

			onInitBtnClick() {
				const message = `${$t(
					'crm.manage.planobj.init_tip',
					null,
					'初始化就是根据对账方家生成对象的对账单数据结构，包括对象、字段等。数据来源按从对象生成，确定现在要初始化吗？'
				)}`;
				this.$confirm(
					message,
					$t('提示'),
					{
						confirmButtonText: $t('开始初始化'),
						cancelButtonText: $t('取消'),
						type:'info'
					}
				).then(() => {
					this.initPlan();
				});
			}
		}
	};

	module.exports = component;
});
