/**
 * 开启电子签章
 */
define(function(require, exports, module) {
	'use strict';
	const StepTitle = require('./step-title');
	const mixins = require('./mixins');
	
	const component = {
		name: 'OpenSignature',

		template: `
			<div
				class="transaction-step-item"
				:class="stepItemClass"
			>
				<step-title
					title="集成电子签章(可选)"
					@on-click="onTitleClick"
				/>
				<div class="transaction-step-content">
					<div class="transaction-step-desc">
						当企业购买电子签章后，可启用电子签章插件，下游经销商就可以在线签署，直接具有法律效力。
					</div>
					<div class="transaction-step-operations">
						<fx-button
							size="small"
							@click="onOpenBtnClick"
						>启用插件</fx-button>
					</div>
				</div>
			</div>
		`,

		components: {
			StepTitle,
		},

		mixins: [mixins],

		data() {
			return {
				isCollapse: this.curStep !== 2,
			}
		},

		methods: {
			onOpenBtnClick() {
				// 打开【业务插件管理】-【电子签章】
				window.open('/XV/UI/manage#crmmanage/=/module-electronicsign');
			}
		}
	};

	module.exports = component;
});
