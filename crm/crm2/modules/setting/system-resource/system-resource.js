define(function (require, exports, module) {

	var Translate = Backbone.View.extend({
		initialize: function (opts) {
			this.setElement(opts.wrapper);
		},
		render: function () {
			var el = this.el;
			// require.async('paas-vui/sdk', function (VuiSdk) {
			// 	VuiSdk.getSystemResource().then(function (Obj) {
			// 		Obj.init(el)
			// 	})
			// });

			require.async('base-biz/sdk', function (SDK) {
				SDK.views.systemresource().then(function (obj) {
                        obj.init(el);
                    });
			});

		}
	});

	module.exports = Translate;
});
