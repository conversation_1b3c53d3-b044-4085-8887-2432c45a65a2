define(function (require, exports, module) {
    let Table = require('crm-modules/components/objecttable/objecttable');
    
    var Mod = Backbone.View.extend({
        initialize: function (opts) {
            this.apiname = 'WechatSessionAnalysisMasterObj';
        },
        render() {
            this.renderTable();
        },
        renderTable() {
            const me = this;
            let NTable = Table.extend({
                initialize(opts) {
                    this.setElement(opts.wrapper);
                    Table.prototype.initialize.apply(this, arguments);
                },
                getColumns() {
                    let columns = Table.prototype.getColumns.apply(this, arguments);
                    let operateCol = _.findWhere(columns, {dataType: 'operate'});
                    operateCol.width = 100;
                    return columns;
                },
                getCustomOperate(operate, data) {
                    _.each(operate, (item) => {
                        item.render_type = 'not_fold';
                        item.data = data;
                    })
                    return operate;
                },
                operateBtnClickHandle(e) {
                    let $tg = $(e.target);
                    var action = $tg.data('action');
                    let $tb = $tg.closest('.tb-cell');
                    let id = $tb.data('id');
                    if (action) {
                        action = '__' + action.split('_').join('') + 'Handle';
                        me[action] && me[action](e, id);
                    }
                },
                trclickHandle: function (data) {
                    me.showDetail(data, this.table.getCurData());
                },
            });
            this.list = new NTable({
                wrapper: this.options.wrapper,
                apiname: this.apiname,
                showOperate: true,
                tableOptions: {
                    searchTerm: null,
                    search: {
                        placeHolder: $t("crm.wechatsessionanalysismasterobj.searchPlaceHolder"),
                        type: 'Keyword',
                        highFieldName: 'name',
                        pos: 'T'
                    },
                    operate: {
                        pos: 'T',
                        btns: [{
                            action: "add",
                            attrs: "data-action=add",
                            className: "j-action",
                            text: $t("新建")
                        }]
                    },
                    refreshCallBack: () => {
                        this.refresh();
                    },
                }
            });
            this.list.render();
        },
        refresh: function () {
            this.list.refresh();
        },
        showDetail(data, idList) {
            const me = this;
            CRM.api.show_crm_detail({
                apiName: this.apiname,
                id: data._id,
                idList: _.pluck(idList, '_id'),
                showMask: false,
                top: 56,
                callback() {
                    me.refresh();
                }
            })
        },
        __addHandle() {
            const me = this;
            this.add = CRM.api.add({
                apiname: me.apiname,
                success() {
                    me.refresh();
                }
            })
        },
        __EditHandle(e, id) {
            let me = this;
            me.edit = CRM.api.edit({
                apiname: me.apiname,
                id: id,
                success() {
                    me.refresh();
                }
            })
        },
        __DeleteHandle(e, id) {
            let me = this;
            var confirm = FS.crmUtil.confirm($t("确定要删除？"), $t("删除"), function () {
                CRM.util.FHHApi(
                    {
                        url: "/EM1HNCRM/API/v1/object/WechatSessionAnalysisMasterObj/action/Delete",
                        data:{
                            "objectDataId": id,
                        },
                        success: function (res) {
                            confirm.hide();
                            if (res.Result.StatusCode === 0) {
                                CRM.util.remind($t("操作成功"));
                                me.refresh();
                                return;
                            }
                            CRM.util.alert(res.Result.FailureMessage);
                        },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
			}, {
				hideFn: function () {
					confirm = null;
				}
			});
        },
    })
    module.exports = Mod;
})