/**
 * 客服服务
 * <AUTHOR>
 */
define(function(require, exports, module) {
    
    var util = require('crm-modules/common/util'),
        tpl  = require('./template/tpl-html');
   
    var Service = Backbone.View.extend({
        
        initialize: function(opts) {
            this.$el.html(tpl({PhoneNum: '18211048931'}));
        },
        
        events: {
            'click .j-save': '_onSave'
        },
        
        _onSave: function() {
            util.remind($t("保存成功"));
        },
        
        //销毁
        destroy: function() {
            var me = this;
        }
    });

    module.exports = Service;
});