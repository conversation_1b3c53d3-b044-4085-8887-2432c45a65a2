/**
 * 邮箱模板
 * <AUTHOR>
 */
define(function (require, exports, module) {

    var util = FS.crmUtil,
        Tb = require('crm-widget/table/table'),
		Tpl = require('./template/tpl-html');

	var Model = Backbone.Model.extend({
		defaults:{
			objDescApiName: ''   //opts.apiname, 筛选apiName
		}
	});

    var Email = Backbone.View.extend({

        tagName: 'div',
        className:'',
        selectList: [],
				objSelectOptions: [],
        templateId: '',
        templateList: [],//模板列表
				keyword: '',//搜索关键字
        initialize: function (opts) {
        	var me = this;
			me.model = new Model();
			me.widgets = {};
			me.render();
			me.listenTo(me.model,'change:objDescApiName',me._refreshTB);
        },

		show: function() {
			this.widgets.tb &&　this.widgets.tb.setParam({},true);
		},

		render:function () {
			var me = this;
			me.$el.html(Tpl({}));
			me.getObjectList(function (data) {
				me.initTable(data);
				me.setTableData(me.keyword,me.model.get('objDescApiName'));
			});
		},

        events: {
            'click .j-add-tpl': '_onAdd'
        },

		/**
		 * @desc 获取支持打印模板的对象列表
		 */
		getObjectList: function(cb) {
			var me = this;
			util.FHHApi({
				url: '/EM1HCRMTemplate/emailTemplateAdminApi/findObjectListInfo',
				success: function(data) {
					if (data.Result.StatusCode == 0) {
						var arr = [{
							name:$t('全部'),
							value:''
						}
						];
						_.each(data.Value, function(k,v){
							arr.push({
								name:k,
								value:v
							})
						});
						me.objSelectOptions = arr;
						cb && cb(arr);
					}
				}
			})
		},

        createOperate: function (cb) {
            var me = this;
            require.async('crm-modules/components/templatemanage/templatemanage', function (Operate) {
                me.operate && me.operate.destroy && me.operate.destroy();
                me.operate = new Operate({
					type:'email'
				});
                me.operate.on('refresh', function () {
					// me.$el.find('.crm-loading').remove();
                    me.widgets.tb && me.widgets.tb.setParam({}, true);
                    me.setTableData(me.keyword,me.model.get('objDescApiName'),true);
                });
                cb && cb(me.operate);
            });
        },

        initTable: function (objSelectOptions) {
            var me = this;
            if (!me.widgets.tb) {
                me.widgets.tb = new Tb({
					$el: me.$(".right-content"),
					showMoreBtn: false,
					doStatic: true,
					operate: {
						pos: "T",
						btns: [
							{
								text: $t("新建"),
								className: "j-add-tpl",
							},
						],
					},
					showPage: false,
					search: {
						pos: 'T',
						placeHolder: $t("搜索模板名称"),
						type: 'Keyword',
						highFieldName: 'name'
					},
					columns: [
						{
							data: "name",
							title: $t("模板名称"),
							width: 240,
							orderValues: [1, 0],
							filterCompare: [22, 23],
							isOrderBy: true,
							isFilter: true,
							render: function (data, type, full) {
								return (
									"<div>" +
									(full.isDefault
										? '<span class="setStatus" >' +
										  $t("默认") +
										  "</span>"
										: "") +
									(full.type == 0
										? '<span class="setStatus" >' +
										  $t("预设") +
										  "</span>"
										: "") +
									"<span >" +
									data +
									"</span>" +
									"</div>"
								);
							},
						},
						{
							data: "objDescApiText",
							title: $t("所属对象"), //默认dataType 1 字符串类型，dataType 2 数字类型 如返回不是数字则变***
							orderValues: [1, 0],
							isOrderBy: true,
							render: function (data, type, full) {
								return data;
							},
						},
						{
							data: "templateId",
							title: $t("模板ID"),
							orderValues: [0, 1],
							width: 220,
							isOrderBy: true,
						},
						{
							data: "creatorId",
							title: $t("创建人"),
							orderValues: [1, 0],
							isOrderBy: true,
							isId: true,
							dataType: 8,
							referRule: "Employee",
						},
						{
							data: "createTime",
							title: $t("创建时间"),
							dataType: 4,
							orderValues: [1, 0],
							filterCompare: [1, 2, 17, 18, 19, 20, 21, 4, 6, 9, 10, 25,26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36],
							isOrderBy: true,
							isFilter: true,
							render: function (data, type, full) {
								return data;
							},
						},
						{
							data: "modifierId",
							title: $t("最后修改人"),
							orderValues: [1, 0],
							isOrderBy: true,
							isId: true,
							dataType: 8,
							referRule: "Employee",
						},
						{
							data: "modifyTime",
							title: $t("最后修改时间"),
							dataType: 4,
							orderValues: [1, 0],
							filterCompare: [1, 2, 17, 18, 19, 20, 21, 4, 6, 9, 10, 25,26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36],
							isOrderBy: true,
							isFilter: true,
							render: function (data, type, full) {
								return data;
							},
						},
						{
							data: null,
							title: $t("操作"),
							lastFixed: true,
							width: 170,
							render: function (data, type, full) {
								return (
									'<div class="operate"  templateId="' +
									full.templateId +
									'" apiname="' +
									full.objDescApiName +
									'" type="' +
									full.type +
									'" default="' +
									full.isDefault +
									'">' +
									'<a href="javascript:;" class="j-edit edit-icon" tplId="' +
									full.templateId +
									'">' +
									$t("编辑") +
									"</a>" +
									me._moreTpl(
										full.type,
										full.isDefault,
										full.templateId,
										full.name
									) +
									"</div>"
								);
							},
						},
					],
					formatData: function (data) {
						return {
							data: data.result && data.result.list,
							totalCount:
								data.result && (data.result.totalCount || 0),
						};
					},

					initComplete: function () {
						this.$el
							.find(".batch-term-operate .crm-btn-groups")
							.prepend(
								`<a class='el-icon-question vui-tooltip-question help-link' href="https://help.fxiaoke.com/dbde/ef25/c45b/5b4a"  target="_blank"></a>`
							);
					}
				});

                const assignObjDescApiName = me.getAssignObjDescApiName();
				me.model.set('objDescApiName', assignObjDescApiName || objSelectOptions[0].value);
                me.widgets.objSelect = me.widgets.tb.addSelect({
					$target:me.$('.dt-op-box'),
					pos:'before',
					label:$t('对象:'),
					options:objSelectOptions,
					defaultValue: assignObjDescApiName,
					zIndex:1000
				});
				me.widgets.tb.on('dt.search', function(keyword) {
					me.keyword = keyword;
					me.setTableData(keyword,me.model.get('objDescApiName'))
			  });
				me.widgets.objSelect.on('change', function (v, item) {
					if(item.value!=''){
						me.model.set('isAll',false);
					}else{
						me.model.set('isAll',true);
					}
					me.model.set('objDescApiName',item.value);
				});
                me.widgets.tb.on('trclick', function (data,$tr,$target) {
                	if($target.hasClass('j-edit')){
						me._onEdit($target);

					}else if($target.hasClass('j-init')){
						me._doInit($target);
					}else if($target.hasClass('j-default')){
						me._doDefault($target);
					}else if($target.hasClass('j-delete')){
						me._onDelDialog($target);
					}else{
						me._renderDetail(data);
					}
                });
            } else {
                me.widgets.tb.setParam({}, true)
								me.setTableData(me.keyword,me.model.get('objDescApiName'));
            }

        },
				setTableData(keyWord = '',objDescApiName = '',refresh = false){
					let me = this;
					if((keyWord || objDescApiName) && !refresh){
						let list = []
						_.each( me.templateList,(template) => {
								let objDescApiNamecopy  = template.objDescApiName || '', name = template.name || '';
								((name.toLowerCase()).indexOf((me.keyword || '').toLowerCase()) > -1 || me.keyword == '') && (objDescApiName == objDescApiNamecopy || objDescApiName == '') && list.push(template);
						})
						me.widgets.tb.doStaticData(list);
				}else{
					    me.widgets.tb.showLoading();
							util.FHHApi({
									url: '/EM1HCRMTemplate/emailTemplateAdminApi/page',
									data: {
											"objDescApiName": me.model.get('objDescApiName'),
											"pageNumber": 1,
											"pageSize": 1000000,
											"_isfilter": false
									},
									success: function(data) {
											if (data.Result.StatusCode == 0) {
													let list = data.Value.result.list
													me.templateList = list
													me.widgets.tb.doStaticData(list);
													me.selectList = [];
													me.templateId = list.length > 0 ? list[0].templateId : [];
													_.each(list, function (item) {
															// 排查excel
															if (![2,3].includes(item.isToWord)) {
																	me.selectList.push({
																			value: item.templateId,
																			name: item.name,
																			apiName:item.objDescApiName
																	});
															}
														
													});
											}else {
													data.Result.FailureMessage && CRM.util.alert(data.Result.FailureMessage);
											}
											me.widgets.tb.hideLoading();
									}
							})
					}
			},
        getAssignObjDescApiName() {
            return CRM.util.getUrlParam(location.search, 'assignObjDescApiName') || ''
        },
		_refreshTB: function () {
			var me = this;
			me.widgets.tb&& me.widgets.tb.setParam({
				"objDescApiName": me.model.get('objDescApiName'),
			},true);
			me.setTableData(me.keyword,me.model.get('objDescApiName'));
		},

        _renderDetail: function (data) {
            var me = this;
            require.async('crm-modules/detail/templatemanage/emaildetail/edetail', function (Detail) {
                if (!me.widgets.detail) {
                    me.widgets.detail = new Detail();
                    me.widgets.detail.on('refresh', function () {
                        me.widgets.tb && me.widgets.tb.setParam({}, true);
												me.setTableData(me.keyword,me.model.get('objDescApiName'));
                    })
                }
                me.widgets.detail.show({
                    type: data.type,
                    templateId: data.templateId,
                    Default: data.isDefault,
                    id: data.id,
                    name: data.name,
                    apiName: data.objDescApiName
                });
            });
			return false;

        },

        _onEdit: function ($target) {
            var me = this,
                tplId = $target.attr('tplId');
			// me.$el.find('.right').append('<div class="crm-loading" style="position:absolute;top:50%;z-index:100;"></div>');
			me.createOperate(function (operate) {
                operate.edit(tplId,$target.closest('.operate').attr('apiName'));
            });
            return false;
        },


        /**
         * @desc 根据列类型 匹配更多展示内容
         */
        _moreTpl: function (type, isDefault, templateId, _name) {
            let name = FS.util.encodeHtml(_name)
			return '<a class="j-delete del-icon"  templateId="' + templateId + '" tplName="' + name + '">'+$t("删除")+'</a>';
        },


        /**
         * @desc 新建
         */
        _onAdd: function (e) {
            var me = this,
						hasTemplate = (me.selectList.length >= 1) ? 'mn-selected' : '',
						noTemplate = (!hasTemplate) ? 'mn-selected' : '',
						objSelectOptions = _.clone(me.objSelectOptions);
						objSelectOptions.shift();
				require.async('./dialog/dialog',function (AddDialog) {
					me.widgets.createDialog = new AddDialog();
						me.widgets.createDialog.show({
							selectList:me.selectList,
							noTemplate:noTemplate,
						  hasTemplate:hasTemplate,
						  objSelectList: objSelectOptions,
							defaultObj:(me.model.get('objDescApiName')=='')?(objSelectOptions[0] && objSelectOptions[0].value):me.model.get('objDescApiName')
						});
					me.widgets.createDialog.on('suc',function () {
						me.widgets.tb && me.widgets.tb.setParam({},true);
						me.setTableData(me.keyword,me.model.get('objDescApiName'));
					});
				});
				FS.log && FS.log('s-paasobj_create_email_template', "cl", {
					module: "s-paasobj",
					subModule: "template"
				});
            e.preventDefault();
            e.stopPropagation();
            return false;
        },

        /**
         * @desc type 1 保存退出
         2 直接退出
         */

        _onClose: function (type) {
            var me = this;
            if (type == 2) {
                me.template && me.template.destroy();
                me.widgets.tb && me.widgets.tb.setParam({}, true)
								me.setTableData(me.keyword,me.model.get('objDescApiName'));
            }
            me.widgets.tb && me.widgets.tb.setParam({}, true)
						me.setTableData(me.keyword,me.model.get('objDescApiName'));
        },

        /**
         * @desc 初始化确认
         */
        _doInit: function ($target) {
            var me = this;
            var confirm = util.confirm($t('crm.确认初始化当前模板'),$t('提示'),function(){
                me.createOperate(function (operate) {
                    operate.init($target.closest('.operate').attr('apiName'));
                });
                confirm.hide();
            });
            return false;
        },

        /**
         * @desc 设默认
         */
        _doDefault: function ($target) {
            var me = this,
                templateId = $target.attr('templateId');
            me.createOperate(function (operate) {
                operate.setDefault(templateId,$target.closest('.operate').attr('apiName'));
            });
            return false;
        },


        /**
         * @desc 删除Dialog
         */
        _onDelDialog: function ($target) {
            var me = this,
                templateId = $target.attr('templateId'),
                tplName = $target.attr('tplName');

            me.createOperate(function (operate) {
                operate.delDialog(templateId, tplName,$t('该模板'));
            });
            return false;
        },


        //销毁
        destroy: function () {
            var me = this;
            _.each(me.widgets, function (widget) {
                widget && widget.destroy && widget.destroy();
            });
            me.widgets = null;
            me.$el.off().remove();
        }
    });

    module.exports = Email;
});
