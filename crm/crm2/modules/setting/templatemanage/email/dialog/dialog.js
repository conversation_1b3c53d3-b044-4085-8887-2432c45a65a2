define(function(require,exports,module){
	var Dialog = require('crm-widget/dialog/dialog'),
		Select = require('crm-widget/select/select'),
		util = require('crm-modules/common/util'),
		Tpl = require('./template/tpl-html');


	var AddDialog = Dialog.extend({


		templateId:'',

		options:{//通过show 方法传入的data参数
			selectList:[],//筛选下拉列表
			apiName:'',//创建对象api 默认模板下拉列表第一个
			noTemplate:'',
			hasTemplate:'',
			objSelectList:[],
			defaultObj:''
		},

		attrs:{
			title: $t("新建模板"),
			showBtns: true,
			showScroll:   false,
			content:  '<div class="content"></div>',
			className : 'crm-s-templatemanage print-dialog'
		},

		events:{
			'click .b-g-btn-cancel':       'hide',
			'click .dialog-btns .b-g-btn': 'onEnter'
		},

		show: function (data) {
			var me = this;
			AddDialog.superclass.show.call(this);
			$(this.element).find('.content').html(Tpl({
				noTemplate:data.noTemplate,
				hasTemplate:data.hasTemplate
			}));
			me.options.objSelectList = data.objSelectList;
			me.options.defaultObj = data.defaultObj;
			me.options.selectList = data.selectList;
			me.options.apiName = data.selectList&& data.selectList[0] && data.selectList[0].apiName;//新建模板，的默认模板为接口返回第一个模板
			var filterTplSelect = me.filterSelect(data.selectList,me.options.defaultObj);
			me.templateId =filterTplSelect&& filterTplSelect[0]&&filterTplSelect[0].value;
			me.initObjSelect(data.objSelectList,this.options.defaultObj);
			me.initTplSelect(_.filter(data.selectList,function(item){
				return item.apiName == me.options.defaultObj;
			}));

		},

		initObjSelect: function (selectList,defaultObj) {
			var me = this;
			me.objselect && me.objselect.destroy();
			me.objselect = new Select({
				$wrap: $(this.element).find('.j-obj-select'),
				width: 290,
				options: selectList || [],
				zIndex: 1000,
				defaultValue:defaultObj
			});
			me.objselect.on('change', function (v, item) {
				// me.templateId = item.value;
				me.options.defaultObj = item.value;
				me.objselect.setValue(item.value);//默认打印模板和关联对象一致
				if (me.select) {//修改tpl 的select
					var newSelect = me.filterSelect(me.options.selectList, item.value);
					if (newSelect.length == 0) {
						me.$('.mn-radio-item').eq(0).removeClass('mn-selected');
						me.$('.mn-radio-item').eq(1).addClass('mn-selected');
					}
					newSelect = newSelect.length? newSelect: [{name:'',value:''}];
					me.select && me.select.resetOptions(newSelect, true);
					me.select.setValue(newSelect.length ? newSelect[0].value:'', true);
				}
			});
		},


		filterSelect:function(selectList, key){
			return  _.filter(selectList, function (item) {
				return (item.apiName == key);
			});
		},

		initTplSelect: function (selectList) {
			var me = this;
			me.select && me.select.destroy();
			me.select = new Select({
				$wrap: $(this.element).find('.j-tpl-select'),
				width: 290,
				options: selectList || [],
				zIndex: 1000
			});
			me.select.on('change', function (v, item) {
				me.templateId = item.value;
				me.options.apiName = item.apiName;
				if(item.value!='' && (me.objselect.getValue()!=me.options.apiName)) {//确保tpl的对象和关联对象一致
					me.objselect.trigger('change',item.apiName, {
					value:item.apiName
				}, me.objselect)}
			});
		},

		//新建模板权限
		checkAddRight: function(apiName, callback) {
			util.FHHApi({
				url:'/EM1HCRMTemplate/emailTemplateAdminApi/isNotOverflow',
				data: {
					"objDescApiName": apiName
				},
				success: function(data) {
					if (data.Result.StatusCode == 0) {
						if (data.Value.code == 0) {
							callback && callback(data.Value.result,data.Value.msg);
						} else {
							util.alert(data.Value.msg);
						}
					}
				},
				error: function() {
					util.alert($t("网络错误请重试！"));
				}
			})
		},

		/**
		 * @desc type 1 保存退出
		 2 直接退出
		 */

		_onClose: function (type) {
			var me = this;
			if (type == 2) {
				me.template && me.template.destroy();
				me.trigger('suc');
			}
			me.trigger('suc');
		},

		onEnter:function () {
			var me = this;

			require.async('paas-template/sdk', function (Template) {
				if ($('.crm-s-templatemanage .mn-selected').attr('data-permissiontype') == 2) {//新建模板
					me.checkAddRight(me.options.defaultObj,function (addRight,msg) {
						if (!addRight) {
							util.alert(msg);
							me.hide();
							return;
						} else {
							me.template = new Template({
								objectName: me.options.defaultObj,
								templateType: 'email',
								onClose: function (type) {
									me._onClose(type);
									me.hide();
								}
							});
						}
					});

				} else {//从现有模板复
					me.checkAddRight(me.options.defaultObj,function (addRight,msg) {
						if (!addRight) {
							util.alert(msg);
							me.hide();
							return;
						} else {
							me.template = new Template({
								objectName: me.options.defaultObj,
								copyFrom:  me.templateId,
								templateType: 'email',
								onClose: function (type) {
									me._onClose(type);
									me.hide();
								}
							});
						}
					});

				}
			});
		},

		hide:function () {
			AddDialog.superclass.hide.call(this);
			this.destroy();
		},

		destroy:function () {
			_.each(['select','objselect'], function(obj){
				this[obj] && this[obj].destroy && this[obj].destroy();
				this[obj] = null;
			});
			$(this.element).off().remove();
			AddDialog.superclass.destroy.call(this);
		}
	});

	module.exports = AddDialog;
});
