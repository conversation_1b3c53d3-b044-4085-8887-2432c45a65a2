/**
 * @desc  模板管理
 * <AUTHOR>
 */
define(function (require, exports, module) {

    var util = require('crm-modules/common/util'),
        Print = require('./print/print'),
        Email = require('./email/email'),
        tpl = require('./template/tpl-html');

    var Model = Backbone.Model.extend({
		defaults:{
			printObjSelectList:[],//打印模板支持对象
			emailObjSelectList:[]//邮件模板支持对象
		}
	});

    var TemplateManage = Backbone.View.extend({

        apiName: 'SalesOrderObj',

        initialize: function (opts) {
            var me = this;
            me.setElement(opts.wrapper);
            me.model = new Model();
        },

        /**
         * @desc 渲染页面
         * 底层实例时调用的方法
         */
        render: function () {
            var me = this;
            me.pageType = 'print';

			me.$el.html(tpl({}));
			// me.switchPage();

			// me.$('.left-item').eq(0).trigger('click');
            me.triggerDefaultActiveTab()
        },
        
        /**
         * @desc 手动触发默认tab的点击
         */
        triggerDefaultActiveTab: function() {
            var me = this;
            
            const $tabList = Array.from(me.$('.crm-tab span'));
            let $firstTab = $tabList[0];

            const assignTabType = CRM.util.getUrlParam(location.search, 'assignTabType') || '';
            const $assignTab = assignTabType && $tabList.find($tab => $tab.dataset && $tab.dataset.type === assignTabType);

			$($assignTab || $firstTab).trigger('click');
        },

        /**
         * @desc 根据类型切换页面布局
         */
        switchPage: function () {
            var me = this,
                type = me.pageType.replace(/(\w)/, function ($$) {
                    return '_render' + $$.toUpperCase();
                });
                me[type]();
        },

        /**
         * @desc 渲染打印模板页面
         */
        _renderPrint: function (addRight) {
            var me = this;
            if (!me._print) {
                me._print = new Print({
                    el: me.$el.find('.print-box')
                });
            }
            me._print.show();
        },

        /**
         * @desc 渲染邮箱模板页面
         */
        _renderEmail: function (addRight) {
            var me = this;
            if (!me._email) {
                me._email = new Email({
                    el: me.$el.find('.email-box')
                });
            }
            me._email.show();
        },

        events: {
            'click .crm-tab span': '_onTab',
            'click .left': '_checkTplType'
        },

        /**
         * @desc lefttab切换
         * 记录打印模板类型
         */
        _checkTplType: function (e) {
            var me = this,
                $target = $(e.target),
                type = $(e.currentTarget).attr('type');
            if ($target.hasClass('tpl-checked')) {
                return;
            }
            $target.toggleClass('tpl-checked', !$target.hasClass('tpl-checked')).siblings().removeClass('tpl-checked');
            // me.apiName = $target.attr('data-type');
            me['_render'+type]();
        },

        /**
         * @desc tab切换
         * 记录类型
         */
        _onTab: function (e) {
            var me = this,
                $target = $(e.currentTarget),
                index = $target.index();

            $target.addClass('cur').siblings().removeClass('cur');
            me.$('.tab-con .view-item').eq(index).show().siblings().hide();

            me.pageType = $target.attr('data-type');
            me.switchPage();

        },

        // 底层调用的组件销毁
        destroy: function () {
            var me = this;
            _.each(['_print', '_email'], function (item) {
                me[item] && me[item].destroy();
            });
        }
    });

    module.exports = TemplateManage;
});
