/**
 * 打印模板
 * <AUTHOR>
 */
define(function (require, exports, module) {

    var util = FS.crmUtil,
        Tb = require('crm-widget/table/table'),
        VuiSdk = require('paas-vui/sdk'),
		Tpl = require('./template/tpl-html');

    var Model = Backbone.Model.extend({
		defaults:{
			objDescApiName: '',//opts.apiname
		}
	});

    var Print = Backbone.View.extend({

        tagName: 'div',
        className:'',
        selectList: [],//新建Dialog  模板下拉列表
        templateId: '',
		objSelectOptions:[],//对象下拉列表选项
        templateList: [],//模板列表
        keyword: '',//搜索关键字

        initialize: function (opts) {
            var me = this;
            me.model = new Model();
            me.widgets = {};
            me.render();
            me.listenTo(me.model,'change:objDescApiName',me._refreshTB);
        },

        show: function() {
            this.widgets.tb && this.widgets.tb.setParam({},true);
        },

        events: {
            'click .j-add-tpl': '_onAdd',
            'click .j-add-excel-tpl ': '_onAddExcel',
            'click .j-add-word-tpl': '_onAddWord',
        },

        render:function () {
        	var me = this;
			me.$el.html(Tpl({}));
            me.getObjectList(function (data) {
                //me.initTable(data);
                // me.$el.find(".j-add-word-tpl").css('display','none')//隐藏word按钮
                VuiSdk.getTemplate().then(Template => {
                    // if (Template.getGrayByMod) {
                    //     Template.getGrayByMod("gray_template_word").then(v => {
                    //         //v && me.$el.find(".j-add-word-tpl").css('display','block')
                    //         me.isGrayWord = v;
                    //     }).finally(() => {
                    //         me.initTable(data);
                    //     });
                    // } else {
                    //     me.initTable(data);
                    // }
                    me.initTable(data);
                    me.setTableData(me.keyword,me.model.get('objDescApiName'));
                });
			})
		},

		/**
		 * @desc 获取支持打印模板的对象列表
		 */
		getObjectList: function(cb) {
			var me = this;
			util.FHHApi({
				url: '/EM1HCRMTemplate/printTemplateAdminApi/findObjectListInfo',
				success: function(data) {
					if (data.Result.StatusCode == 0) {
						var arr = [{
							name: $t('全部'),
							value:''
						}];
						_.each(data.Value, function(k,v){
							arr.push({
								name:k,
								value:v
							})
						});
						cb && cb(arr);
					}else {
						data.Result.FailureMessage && CRM.util.alert(data.Result.FailureMessage);
					}
				}
			})
		},

        createOperate: function (cb) {
            var me = this;
            require.async('crm-modules/components/templatemanage/templatemanage', function (Operate) {
                me.operate && me.operate.destroy && me.operate.destroy();
                me.operate = new Operate({
					type:'print'
				});
                me.operate.on('refresh', function () {
					// me.$el.find('.crm-loading').remove();
                    me.setTableData(me.keyword,me.model.get('objDescApiName'),true);
                    me.widgets.tb && me.widgets.tb.setParam({}, true);
                });
                cb && cb(me.operate);
			});
        },

        initTable: function (objSelectOptions) {
            var me = this;
            me.objSelectOptions = objSelectOptions;
            var operateBtns = [{
                isFold: false,
                text: $t('新建Word模板'),
                className: 'j-add-word-tpl'
            },{
                isFold: false,
                text: $t('新建Excel模板'),
                className: 'j-add-excel-tpl'
            },{
                isFold: false,
                text: $t('新建模板'),
                className: 'j-add-tpl'
            }];
            if (!me.widgets.tb) {
                me.widgets.tb = new Tb({
                    $el: me.$('.right-content'),
                    showMoreBtn: false,
                    doStatic: true,
                    // showFilerBtn: true,
                    showPage: false,
                    search: {
                        pos: 'T',
                        placeHolder: $t("搜索模板名称"),
                        type: 'Keyword',
                        highFieldName: 'name'
                      },
                    operate: {
                        pos: 'T',
                        btns: operateBtns
                    },
                    columns: [
                        {
                            data: 'name',
                            title: $t('模板名称'),
                            width: 240,
							orderValues: [1, 0],
							isOrderBy: true,
                            isFilter: true,
                            filterCompare: [ 22, 23],
                            render: function (data, type, full) {
                                return '<div>' +
                                    (full.isDefault ? ('<span class="setStatus" >' + $t("默认") + '</span>') : '') +
                                    (full.type == 0 ? ('<span class="setStatus" >' + $t("预设") + '</span>') : '') +
                                    '<span >' + data + '</span>' +
                                    '</div>';
                            }
                        }, {
							data: 'objDescApiText',
							title: $t('所属对象'),//默认dataType 1 字符串类型，dataType 2 数字类型 如返回不是数字则变***
							orderValues: [1, 0],
							isOrderBy: true,
							render: function (data, type, full) {
								return data;
							}
						}, {
                            data: 'outPdf',
                            title: $t('打印输出格式'),// 0:pdf 1:word
                            orderValues: [0, 1],
                            isOrderBy: true,
                            render: function (data, type, full) {
                                let v = full.outPdf ? 0 : full.isToWord;
                                switch(v) {
                                    case 0:
                                        return '<span>pdf</span>';
                                    case 1:
                                        return `<span>word（${$t("仅支持web端")}）</span>`;
                                    case 2:
                                        return '<span>excel</span>';
                                    case 3:
                                            return `<span>word(${$t('上传离线word模板')})</span>`;
                                    default:
                                        return '<span>--</span>';
                                }
                            }
                        }, {
                        	data: 'isToWord',
                        	title: $t('source.file.format'), // 
                        	orderValues: [0, 1],
                        	isOrderBy: true,
                        	render: function (data, type, full) {
                        		switch (data) {
                        			case 0:
                        				return '<span>pdf</span>';
                        			case 1:
                        				return `<span>word（${$t("仅支持web端")}）</span>`;
                        			case 2:
                        				return '<span>excel</span>';
                        			case 3:
                        				return `<span>word(${$t('上传离线word模板')})</span>`;
                        			default:
                        				return '<span>--</span>';
                        		}
                        	}
                        }, {
                            data: 'templateId',
                            title: $t('模板ID'),
                            orderValues: [0, 1],
                            width: 220,
                            isOrderBy: true
                        }, {
                            data: 'creatorId',
                            title: $t('创建人'),//默认dataType 1 字符串类型，dataType 2 数字类型 如返回不是数字则变***
							orderValues: [1, 0],
							isOrderBy: true,
                            isId:true,
                            dataType:8,
                            referRule:'Employee'
                        }, {
                            data: 'createTime',
                            title: $t('创建时间'),
                            dataType: 4,
                            orderValues: [1, 0],
                            filterCompare:[1, 2, 17, 18, 19, 20, 21, 4, 6, 9, 10, 25,26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36],
							isOrderBy: true,
							isFilter: true,
                            render: function (data, type, full) {
                                return data;
                            }
                        }, {
                            data: 'modifierId',
                            title: $t('最后修改人'),
							orderValues: [1, 0],
							isOrderBy: true,
							isId:true,
							dataType:8,
							referRule:'Employee'
                        }, {
                            data: 'modifyTime',
                            title: $t('最后修改时间'),
                            dataType: 4,
                            filterCompare:[1, 2, 17, 18, 19, 20, 21, 4, 6, 9, 10, 25,26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36],
							orderValues: [1, 0],
							isOrderBy: true,
							isFilter: true,
                            render: function (data, type, full) {
                                return data;
                            }
                        }, {
                            data: null,
                            title: $t('操作'),
                            lastFixed: true,
                            width: 170,
                            render: function (data, type, full) {
                                return '<div class="operate"  templateId="' + full.templateId + '" apiname="' + full.objDescApiName + '" type="' + full.type + '" default="' + full.isDefault + '">' +
                                    '<a href="javascript:;" class="j-edit" tplId="' + full.templateId + '" tplIsToWord="' + full.isToWord + '">' + $t('编辑') + '</a>' + me._moreTpl(full.type, full.isDefault, full.templateId, full.name) +
                                    '</div>';
                            }
                        }],
                    formatData: function (data) {
                    	if(data.result){
							me.selectList = [];
							me.templateId = data.result.list && data.result.list[0] && data.result.list[0].templateId;
							_.each(data.result.list, function (item) {
                                // 排查excel
                                if (![2,3].includes(item.isToWord)) {
                                    me.selectList.push({
                                        value: item.templateId,
                                        name: item.name,
                                        apiName:item.objDescApiName
                                    });
                                }
								
							});
							return {
								data: data.result && data.result.list,
								totalCount: data.result && (data.result.totalCount || 0)
							}
						}

					},
                    initComplete: function () {
						this.$el
							.find(".batch-term-operate .crm-btn-groups")
							.prepend(
								`<a class='el-icon-question vui-tooltip-question help-link' href="https://help.fxiaoke.com/dbde/ef25/e552/cebd"  target="_blank"></a>`
							);
					},
				});
                const assignObjDescApiName = me.getAssignObjDescApiName();
                me.model.set('objDescApiName', assignObjDescApiName || me.objSelectOptions[0]?.value);
                me.widgets.tb.on('dt.search', function(keyword) {
                    me.keyword = keyword;
                    me.setTableData(keyword,me.model.get('objDescApiName'))
                });

                me.widgets.objSelect = me.widgets.tb.addSelect({
                    $target: me.$('.dt-op-box'),
                    pos:  'before',
                    label: $t('对象:'),
                    className: '',
                    options: me.objSelectOptions,
                    defaultValue: assignObjDescApiName || ""
                });

                me.widgets.objSelect.on('change', function (v, item) {
                    me.model.set('objDescApiName', item.value);
                });
                me.widgets.tb.on('trclick', function (data, $tr, $target) {
                    if ($target.hasClass('j-edit')) {
                        me._onEdit($target);
                    } else if($target.hasClass('j-init')) {
						me._doInit($target);

                    }else if($target.hasClass('j-delete')) {
						me._onDelDialog($target);

					}else if($target.hasClass('j-default')) {
						me._doDefault($target);
					}else{
						me._renderDetail(data);
					}
                });
            } else {
                me.setTableData(me.keyword,me.model.get('objDescApiName'));
                me.widgets.tb.setParam({}, true)
            }

        },

        setTableData(keyWord = '',objDescApiName = '', refresh = false){
            let me = this;
            if((keyWord || objDescApiName) && !refresh){
                let list = []
                _.each( me.templateList,(template) => {
                    let objDescApiNamecopy  = template.objDescApiName || '', name = template.name || '';
                    ((name.toLowerCase()).indexOf((me.keyword || '').toLowerCase()) > -1 || me.keyword == '') && (objDescApiName == objDescApiNamecopy || objDescApiName == '') && list.push(template);
                })
                me.widgets.tb.doStaticData(list);
            }else{
			    me.widgets.tb.showLoading();
                util.FHHApi({
                    url: '/EM1HCRMTemplate/printTemplateAdminApi/page',
                    data: {
                        "objDescApiName": me.model.get('objDescApiName'),
                        "pageNumber": 1,
                        "pageSize": 1000000,
                        "_isfilter": false
                    },
                    success: function(data) {
                        if (data.Result.StatusCode == 0) {
                            let list = data.Value.result.list
                            me.templateList = list
                            me.widgets.tb.doStaticData(list);
                            me.selectList = [];
							me.templateId = list.length > 0 ? list[0].templateId : [];
							_.each(list, function (item) {
                                // 排查excel
                                if (![2,3].includes(item.isToWord)) {
                                    me.selectList.push({
                                        value: item.templateId,
                                        name: item.name,
                                        apiName:item.objDescApiName
                                    });
                                }
								
							});
    
                        }else {
                            data.Result.FailureMessage && CRM.util.alert(data.Result.FailureMessage);
                        }
                        me.widgets.tb.hideLoading();
                    }
                })
            }
        },

        getAssignObjDescApiName() {
        	return CRM.util.getUrlParam(location.search, 'assignObjDescApiName') || ''
        },

        _refreshTB: function () {
        	var me = this;
			me.widgets.tb&&me.widgets.tb.setParam({
				"objDescApiName": me.model.get('objDescApiName')
			},true);
            me.setTableData(me.keyword,me.model.get('objDescApiName'));
		},

        _renderDetail: function (data) {
            var me = this;
            require.async('crm-modules/detail/templatemanage/printdetail/pdetail', function (Detail) {
                if (!me.widgets.detail) {
                    me.widgets.detail = new Detail();
                    me.widgets.detail.on('refresh', function () {
                        me.setTableData(me.keyword,me.model.get('objDescApiName'));
                        me.widgets.tb && me.widgets.tb.setParam({}, true);
                    })
                }
                me.widgets.detail.show({
                    type: data.type,
                    templateId: data.templateId,
                    Default: data.isDefault,
                    id: data.id,
                    name: data.name,
                    apiName: data.objDescApiName,
                    isToWord: data.isToWord
                });
            });
			return false;
        },

        _onEdit: function ($target) {
            var me = this,
                tplId = $target.attr('tplId'),
                tplIsToWord = $target.attr('tplIsToWord')-0;
			// me.$el.find('.right').append('<div class="crm-loading" style="position:absolute;top:50%;z-index:100;"></div>');
            me.createOperate(function (operate) {
                operate.edit(tplId,$target.closest('.operate').attr('apiName'), tplIsToWord);
            });
            return false;
        },


        /**
         * @desc 根据列类型 匹配更多展示内容
         */
        _moreTpl: function (type, isDefault, templateId, _name) {
            let name = FS.util.encodeHtml(_name)
            if (type == 0) {//预设
                if (isDefault) {//预设 默认
                    return '<a class="j-init" apiname="' + name + '">' + $t('初始化') + '</a>';
                }
                return '<a class="j-default"  templateId="' + templateId + '"  apiname="' + name + '">' + $t('设为默认') + '</a>' +
                    '<a class="j-init" apiname="' + name + '">' + $t('初始化') + '</a>';
            } else if (isDefault) {//默认
                return '<a class="j-delete"  templateId="' + templateId + '" tplName="' + name + '">' + $t('删除') + '</a>';
            } else {
                return '<a class="j-default" templateId="' + templateId + '"  apiname="' + name + '">' + $t('设为默认') + '</a>' +
                    '<a class="j-delete" templateId="' + templateId + '" tplName="' + name + '">' + $t('删除') + '</a>';
            }
        },


        /**
         * @desc 新建模板
         */
        _onAdd: function (e) {
			var me = this,
				hasTemplate = (me.selectList.length >= 1) ? 'mn-selected' : '',
				noTemplate = (!hasTemplate) ? 'mn-selected' : '',
				objSelectOptions =  _.clone(me.objSelectOptions);
				objSelectOptions.shift();
				require.async('./dialog/dialog',function (AddDialog) {
					me.createDialog = new AddDialog();
					me.createDialog.on('suc',function () {
                        me.setTableData(me.keyword,me.model.get('objDescApiName'));
						me.widgets.tb && me.widgets.tb.setParam({}, true)
					});
					me.createDialog.show({
						selectList:me.selectList,
						noTemplate:noTemplate,
						hasTemplate:hasTemplate,
						objSelectList: objSelectOptions,
						defaultObj:(me.model.get('objDescApiName')=='')?(objSelectOptions[0] && objSelectOptions[0].value):me.model.get('objDescApiName')
					});
				});
			FS.log && FS.log('s-paasobj_create_print_template', "cl", {
				module: "s-paasobj",
				subModule: "template"
			});
        },

        //调用打印模板
        printExcelAndWordTpl(type) {
            let api_name = this.model.get('objDescApiName');
            VuiSdk.getTemplate().then(Template => {
                this.excelTemplate = Template.createPrintTpl({objDescApiName: api_name, isToWord: type });
                this.excelTemplate.$on('save', () => {
                    this.widgets.tb && this.widgets.tb.setParam({}, true)
                });
            });

            
        },

        /**
         * 
         * @param 新建excel模板
         */
        _onAddExcel: function () {
            this.printExcelAndWordTpl(2)
			FS.log && FS.log('s-paasobj_create_excel_template', "cl", {
				module: "s-paasobj",
				subModule: "template"
			});
        },
                /**
         * 
         * @param 新建word模板
         */
        _onAddWord: function () {
            this.printExcelAndWordTpl(3);
            FS.log && FS.log('s-paasobj_create_word_template', "cl", {
				module: "s-paasobj",
				subModule: "template"
			});
        },

        /**
         * @desc type 1 保存退出
         2 直接退出
         */

        _onClose: function (type) {
            var me = this;
            if (type == 2) {
                me.template && me.template.destroy();
                me.setTableData(me.keyword,me.model.get('objDescApiName'));
                me.widgets.tb && me.widgets.tb.setParam({}, true)
            }
            me.setTableData(me.keyword,me.model.get('objDescApiName'));
            me.widgets.tb && me.widgets.tb.setParam({}, true)
        },

        /**
         * @desc 初始化确认
         */
        _doInit: function ($target) {
            var me = this;
            var confirm = util.confirm($t('crm.确认初始化当前模板'), $t('提示'),function(){
                me.createOperate(function (operate) {
                    operate.init($target.closest('.operate').attr('apiName'), $target.closest('.operate').attr('templateid'));
                });
                confirm.hide();
            });
            return false;
        },

        /**
         * @desc 设默认
         */
        _doDefault: function ($target) {
            var me = this,
                templateId = $target.attr('templateId');
            me.createOperate(function (operate) {
                operate.setDefault(templateId,$target.closest('.operate').attr('apiName'));
            });
            return false;
        },


        /**
         * @desc 删除Dialog
         */
        _onDelDialog: function ($target) {
            var me = this,
                templateId = $target.attr('templateId'),
                tplName = $target.attr('tplName');

            me.createOperate(function (operate) {
                operate.delDialog(templateId, tplName);
            });
            return false;
        },


        //销毁
        destroy: function () {
            var me = this;
            _.each(me.widgets, function (widget) {
                widget && widget.destroy && widget.destroy();
            });
            this.excelTemplate && this.excelTemplate.$destroy();
            this.wordTemplate && this.wordTemplate.$destroy();
            this.printHelp && this.printHelp.$destroy();
            me.widgets =null;
            me.$el.off().remove();
        }
    });

    module.exports = Print;
});
