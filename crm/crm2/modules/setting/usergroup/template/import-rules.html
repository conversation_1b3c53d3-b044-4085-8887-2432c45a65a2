<fx-dialog class="dialog-datapermissions-datashare-import_rules" :class="[!beforeImport?'after-import':'']"
	:visible.sync="show" size="small" max-height="400px" title="{{$t('<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>')}}">
	<div v-show="beforeImport">
		<div style="color: #545861;">{{$t("请添加您要导入的数据")}}</div>
		<div><a :href="downloadUrl">{{$t("下载数据模板")}}</a></div>
		<div ref="uploadBox" class="uploadBox">
			<div v-show="!hasFile">
				<i class="icon fx-icon-upload"></i>
				<span>{{$t("将文件拖到此处或")}}<a href="javascript:;" @click="onClickUpload">{{$t("点击上传")}}</a></span><br />
				<span class="note">%note%</span>
			</div>
			<div v-show="hasFile">
				<i class="icon el-icon-circle-check"></i>
				<span>{{$t("文件已添加")}}</span><br />
				<span class="note note2">%filename%</span>
				&nbsp;&nbsp;<a href="javascript:;" @click="onClickUpload">{{$t("重新上传")}}</a>
			</div>
		</div>
		<input type="file" ref="fileInput" accept=".xlsx,.xls" style="display: none" />
	</div>
	<div v-show="!beforeImport">
		<div v-show="percentage<100" style="text-align: center; padding: 16px 0">{{$t("正在收集数据")}}</div>
		<fx-progress v-show="percentage<100" :percentage="percentage" :stroke-width="10" color="#4D8CE6" :show-text="false"
			style="margin-bottom: 30px"></fx-progress>
		<div v-show="percentage>=100" style="padding: 26px 0;">%progressResult%</div>
	</div>
	<template slot="footer">
		<fx-button type="primary" :disabled="disabled" size="small" @click="onStartImport">{{$t("开始导入")}}</fx-button>
		<fx-button size="small" @click="onCancel">{{$t("取消")}}</fx-button>
	</template>
</fx-dialog>