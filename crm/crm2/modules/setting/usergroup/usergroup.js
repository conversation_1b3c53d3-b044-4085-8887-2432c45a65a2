/**
 * 用户组
 * 遵循seajs module规范
 */
define(function (require, exports, module) {
	var util = require("crm-modules/common/util"),
		DataTables = require("crm-widget/table/table"),
		SelectSearch = require("./selectsearch/selectsearch"),
		EditUserGroup = require("./editusergroup/editusergroup");

	var ExportList = require("./exportlist");
	var ImportList = require("./importlist");
	var tplImportRules = require("./template/import-rules-html");
	var H5Uploader = require("base-h5uploader"); // HTMl5上传

	module.exports = Backbone.View.extend({
		initialize: function (opts) {
			this.widgets = {};
			opts.wrapper.append(
				'<div class="table-wrap table-wrap1"></div><div class="table-wrap table-wrap2" style="display: none"><div class="table-box-header"><a href="javascript:;" class="btn-back-data-list">&lt; ' +
					$t("usergroup_back_to_list") +
					'</a><div class="operationType"><span>' +
					$t("操作类型") +
					': </span><div class="select"></div></div></div><div class="table-box"></div></div>'
			);
			this.tableWrapper1 = opts.wrapper.find(".table-wrap1");
			this.tableWrapper2 = opts.wrapper.find(".table-wrap2");
			this.tableWrapper2Table = opts.wrapper.find(".table-box");

			this.setElement(this.tableWrapper1);
			this.initTable();

			this.tableWrapper1
				.find(".btn-export-list")
				.on("click", this.showExportList.bind(this));

			this.tableWrapper2
				.find(".btn-back-data-list")
				.on("click", this.hideExportList.bind(this));

			this.getDownloadDataShareTemplate();

		},

		events: {
			"click .j-add": "onAdd",
			"click .j-stop": "onStop",
			"click .j-start": "onStar",
			"click .j-del": "onDel",
			"click .j-export": "onExport",
			"click .j-import": "onImport",
			"click .view-export-list": "showExportList",
			// "click .btn-export-list": "showExportList",
			"click .btn-back-data-list": "hideExportList",
		},

		initTable: function () {
			var me = this;

			me.dt = new DataTables({
				$el: me.$el,
				title: $t("crm.用户组"),
				url: "/EM1HCRMUdobj/groupApi/groupList",
				requestType: "FHHApi",
				trHandle: false,
				showMultiple: true,
				postData: {
					status: 0,
				},
				searchTerm: {
					pos: "C",
					type: "_status",
					showManage: false,
					showCustom: false,
					options: [
						{
							id: "9999",
							name: $t("全部"),
							type: "1",
							isNotRequest: true,
						},
						{
							id: 0,
							isdef: true,
							name: $t("启用中"),
							isNotRequest: true,
						},
						{
							id: 1,
							name: $t("已停用"),
							isNotRequest: true,
						},
					],
				},
				operate: {
					btns: [
						{ text: $t("新建用户组"), className: "j-add" },
						{ text: $t("导入"), className: "j-import" },
						{ text: $t("导出"), className: "j-export" },
						{ text: $t("查看")+$t("导入导出结果"), className: "view-export-list" },
					],
				},
				batchBtns: [
					{
						text: $t("停用"),
						className: "j-stop",
					},
					{
						text: $t("启用"),
						className: "j-start",
					},
					{
						text: $t("删除"),
						className: "j-del",
					},
				],
				columns: [
					{
						data: "name",
						title: $t("组名"),
						width: 240,
						isOrderBy: true,
						render: function (data, type, full) {
							return full.status === 1
								? '<span style="color:#ccc;">' +
										data +
										"</span>"
								: data || "--";
						},
					},
					{
						data: "groupUsers",
						title: $t("组成员"),
						width: 334,
						render: function (data, type, full) {
							var list = [];
							_.each(full.groupUsers || [], function (item) {
								var emp = util.getEmployeeById(item,true,true);
								// list.push(emp ? emp.name : "--");
								var name='--'
								if(emp){
									name = emp.name;
									if(emp.isStop){
										name += '('+$t('已停用')+')'
									}
								}
								list.push(name);
							});
							var value = list.join("、");
							return full.status === 1
								? '<span style="color:#ccc;" title="' +
										value +
										'">' +
										(value || "--") +
										"</span>"
								: '<span title="' +
										value +
										'">' +
										(value || "--") +
										"</span>";
						},
					},
					{
						data: "description",
						title: $t("备注"),
						isOrderBy: true,
						render: function (data, type, full) {
							return full.status === 1
								? '<span style="color:#ccc;">' +
										data +
										"</span>"
								: data || "--";
						},
					},
					{
						data: "status",
						title: $t("状态"),
						isOrderBy: true,
						render: function (data, type, full) {
							return data === 1
								? '<span style="color:#ccc;">' +
										$t("已停用") +
										"</span>"
								: $t("启用中");
						},
					},
					{
						data: null,
						title: $t("操作"),
						lastFixed: true,
						render: function (data, type, full) {
							return full.status === 1
								? '<div class="btns"><a class="j-del-btn">' +
										$t("删除") +
										'</a><a class="j-start-btn"> ' +
										$t("启用") +
										"</a></div>"
								: '<div class="btns"><a class="j-edit">' +
										$t("编辑") +
										'</a><a class="j-stop-btn">' +
										$t("停用") +
										"</a></div>";
						},
					},
				],
				search: {
					pos: "C",
					placeHolder: $t("搜索"),
					type: "keyword",
					filterColumns: [
						{
							title: $t("组名"),
							data: "name",
							isFilter: true,
							dataType: 1,
						},
						{
							title: $t("组成员"),
							data: "groupUsers",
							isFilter: true,
							dataType: 1,
						},
					],
				},
				sortField: 'orderKey', //排序字段的key
				sortType: 'isAsc', //排序字段值的key
				paramFormat: function (param) {
					let selectValue =
						me.dt?._search?.searchComp?.selectIntance?.getValue();
					if (selectValue)
						param.searchType = selectValue == "name" ? "0" : "1";
					// 处理排序参数
					if (param.isAsc) {
						if (param.isAsc == '1') param.isAsc = true
						if (param.isAsc == '2') param.isAsc = false
					}
					me.tableParam = param;
					return param;
				},
				// parseTableParam(obj){
				// 	return obj;
				// },
				formatData: function (data) {
					_.each(data.list, function (item) {
						item.groupUsers = data.groupUsers[item.id];
					});
					return {
						data: data.list,
						totalCount: data.page ? data.page.totalCount : 0,
					};
				},
			});

			me.dt.on("term.change", function (v) {
				me.dt.setParam(
					{
						status: v != "9999" ? v : null,
					},
					true,
					true
				);
			});

			me.dt.on("trclick", function (data, $tr, $target) {
				if ($target.hasClass("j-start-btn")) {
					me.onStar(data);
				} else if ($target.hasClass("j-stop-btn")) {
					me.onStop(data);
				} else if ($target.hasClass("j-edit")) {
					me.onEdit(data);
				} else if ($target.hasClass("j-del-btn")) {
					me.onDel(data);
				}
			});
		},

		//新建用户组
		onAdd: function () {
			var me = this;
			me.EditUserGroupWidget &&
				(me.EditUserGroupWidget.destroy(),
				(me.EditUserGroupWidget = null));
			me.EditUserGroupWidget = new EditUserGroup({
				title: $t("新建用户组"),
				type: "add",
			});
			me.EditUserGroupWidget.on("success", function () {
				me.refresh();
			});
			me.EditUserGroupWidget.show();
		},

		//编辑用户组
		onEdit: function (data) {
			var me = this;
			me.EditUserGroupWidget &&
				(me.EditUserGroupWidget.destroy(),
				(me.EditUserGroupWidget = null));
			me.EditUserGroupWidget = new EditUserGroup({
				title: $t("编辑用户组"),
				type: "edit",
				groupId: data.id,
			});
			me.EditUserGroupWidget.on("success", function () {
				me.refresh();
			});

			var obj = {member:[],stop:[]};
			(data.groupUsers||[]).forEach(item => {
				var emp = util.getEmployeeById(item,true,true);
				if(emp){
					if(emp.isStop){
						obj.stop.push(emp.id);
					}else{
						obj.member.push(emp.id);
					}
				}
			})
			me.EditUserGroupWidget.show({
				data: data,
				// groupUsers: data.groupUsers,
				groupUsers: obj,
			});
		},

		//(批量)停用数据组
		onStop: function (data) {
			var me = this;
			var selectArr = me.dt.getCheckedData();
			var req = [];

			_.each(selectArr, function (item, index) {
				req.push(item.id);
			});
			util.FHHApi({
				url: "/EM1HCRMUdobj/groupApi/updateGroupStatus",
				data: {
					groupIds: data.id ? data.id : req.join(","),
					stopStatus: true,
				},
				success: function (res) {
					if (res.Result.StatusCode == 0) {
						util.remind(1, $t("操作成功"));
						me.refresh();
					}
				},
			});
		},

		//(批量)启用数据组
		onStar: function (data) {
			var me = this;
			var selectArr = me.dt.getCheckedData();
			var req = [];

			_.each(selectArr, function (item, index) {
				req.push(item.id);
			});
			util.FHHApi({
				url: "/EM1HCRMUdobj/groupApi/updateGroupStatus",
				data: {
					groupIds: data.id ? data.id : req.join(","),
					stopStatus: false,
				},
				success: function (res) {
					if (res.Result.StatusCode == 0) {
						util.remind(1, $t("操作成功"));
						me.refresh();
					}
				},
			});
		},

		//(批量)删除数据组
		onDel: function (data) {
			var me = this;
			var selectArr = me.dt.getCheckedData();
			var req = [];

			_.each(selectArr, function (item, index) {
				req.push(item.id);
			});
			var confirm = util.confirm(
				$t("确定删除用户组") + "?",
				$t("删除"),
				function () {
					util.FHHApi({
						url: "/EM1HCRMUdobj/groupApi/deleteGroups",
						data: {
							groupIds: data.id ? data.id : req.join(","),
						},
						success: function (res) {
							if (res.Result.StatusCode == 0) {
								util.remind(1, $t("操作成功"));
								confirm.destroy();
								me.refresh();
							}
						},
					});
				}
			);
		},

		onImport: function () {
			var me = this;
			var filepath = "javascript:;";
			if (me.downloadDataShareTemplate) {
				var tpl = me.downloadDataShareTemplate["1"];
				filepath = FS.util.getFscLink(
					tpl.filePath.split(".")[0],
					tpl.fileName,
					true
				);
			}
			var vm = FxUI.create({
				template: tplImportRules()
					.replace("%filename%", "{{filename}}")
					.replace(
						"%note%",
						$t("导入数据上限为{{n}}条", {
							data: {
								n: 100,
							},
						})
					)
					.replace(
						"%progressResult%",
						$t("您可以在{{str}}中查看导出进度", {
							data: {
								str:
									'<a href="javascript:;" @click="viewResult">' +
									$t("导入导出结果") +
									"</a>",
							},
						})
					),
				data: function () {
					return {
						show: true,
						downloadUrl: filepath,
						hasFile: false,
						filename: "",
						filepath: "",
						beforeImport: true,
						percentage: 0,
						disabled: true,
					};
				},
				watch: {
					show: function (val) {
						if (!val) me.h5Uploader = null;
					},
				},
				methods: {
					initUpload: function () {
						if (me.h5Uploader) return;
						me.h5Uploader = new H5Uploader({
							multiple: false,
							accept: ".xlsx,.xls",
							autoPrependPath: false,
							fileInput: this.$refs.fileInput,
							dragDrop: this.$refs.uploadBox,
							url: FS.BASE_PATH + "/FSC/EM/File/UploadByStream",
							timeout: 180,
							onSelect: function (file) {
								me.h5Uploader.startUpload();
								vm.uploadLoading = FxUI.Loading.service({
									target: vm.$refs.uploadBox,
								});
							},
							onSuccess: function (file, responseText) {
								console.log("onSuccess...", file, responseText);
								vm.filename = file.name;
								vm.hasFile = true;
								vm.filepath =
									JSON.parse(responseText).TempFileName;
								vm.disabled = false;
							},
							onFailure: function (file) {
								console.log("onFailure...", file);
								FxUI.MessageBox.alert($t("上传文件失败"), {
									type: "error",
								});
							},
							onComplete: function () {
								console.log("onComplete...");
								vm.uploadLoading && vm.uploadLoading.close();
								me.h5Uploader.removeAllFile();
							},
						});
					},
					onDataSourceChange: function (val) {
						if (me.downloadDataShareTemplate) {
							var tpl = me.downloadDataShareTemplate[val];
							this.downloadUrl = FS.util.getFscLink(
								tpl.filePath.split(".")[0],
								tpl.fileName,
								true
							);
						} else {
							this.downloadUrl = "javascript:;";
						}
					},
					onClickUpload: function () {
						this.$refs.fileInput.click();
					},
					onStartImport: function () {
						if (!this.filename || !this.filepath) return;

						this.beforeImport = false;
						var timer = setInterval(function () {
							if (vm.percentage >= 100) {
								clearInterval(timer);
								return;
							}
							vm.percentage += 0.3;
							if (vm.percentage > 100) vm.percentage = 100;
						}, 150);
						FS.util.FHHApi({
							url: "/EM1HPAASBATCH/task/importGroupTask/create",
							data: {
								importGroupTaskConfig: {
									argType: 1,
									fileName: this.filename,
									file_path: this.filepath,
								},
							},
							success: function (res) {
								if (res.Result.StatusCode !== 0) {
									//导入失败
									// clearInterval(timer);
								}
							},
							fail: function () {},
							complete: function () {
								clearInterval(timer);
								timer = setInterval(function () {
									if (vm.percentage >= 100) {
										clearInterval(timer);
										return;
									}
									vm.percentage += 0.3;
									if (vm.percentage > 100)
										vm.percentage = 100;
								}, 10);
							},
						});
					},
					onCancel: function () {
						this.show = false;
						this.reset();
					},
					reset: function () {
						this.hasFile = false;
						this.filename = "";
						this.filepath = "";
						this.beforeImport = true;
						this.percentage = 0;
						this.disabled = true;
					},
					viewResult: function () {
						this.show = false;
						this.reset();
						this.$nextTick(() => {
							me.showExportList("import");
						});
					},
				},
				mounted: function () {
					this.initUpload();
				},
			});
		},

		onExport: function () {
			var me = this;
			util.FHHApi(
				{
					url: "/EM1HPAASBATCH/task/exportGroupTask/create",
					data: {
						exportGroupTaskConfig: {
							argType: 1,
							arg: me.tableParam || {},
						},
					},
					success: function (res) {
						// res.Value  //"2ac7e2681bc14cb8ac789e69f85f2770"
						// util.remind(1, $t("操作成功"));
						// location.hash = 'datamaintenancetools/initializationandbackup';

						if (res.Result.StatusCode == 0 && res.Value) {
							var vm = FxUI.create({
								template:
									'<fx-dialog  :visible.sync="show" size="small" max-height="400px" :append-to-body="true" title="' +
									$t("daochuyonghuzu") +
									'" >' +
									'<div v-if="!progressStop"><p style="text-align:center;padding: 20px 0 6px;">{{$t("正在收集数据")}}...</p><br><fx-progress :percentage="percentage" color="#4D8CE6" :stroke-width="10" :show-text="false" style="margin-bottom: 44px;"></fx-progress></div>' +
									'<div v-else><p style="padding: 20px 0 30px;">' +
									$t("您可以在{{str}}查看导出进度。", {
										data: {
											str:
												'<a href="javascript:;" @click="showExportList">' +
												$t("usergroup_exportlist") +
												"</a>",
										},
									}) +
									"</p></div></fx-dialog>",
								data: function () {
									return {
										show: false,
										percentage: 0,
										progressStop: true,
									};
								},
								watch: {
									show: function (val) {
										if (!val) {
											clearInterval(this.timer);
										}
									},
								},
								methods: {
									showExportList: function () {
										this.show = false;
										this.$nextTick(() => {
											me.showExportList("export");
										});
									},
								},
								mounted: function () {
									this.show = true;
								},
							});
						}
					},
					fail: function (res) {
						if (res.Result.FailureMessage) {
							util.alert(res.Result.FailureMessage);
							// FxUI.MessageBox.alert(res.Result.FailureMessage, {
							//   type: 'error'
							// })
						}
					},
				},
				{
					errorAlertModel: 1,
				}
			);
		},

		//显示已导出列表
		showExportList: function (type) {
			if (["export", "import"].indexOf(type) == -1) type = "import";

			var me = this;
			this.tableWrapper2.show();
			this.tableWrapper1.find('.dt-op-box').hide();

			if (this.widgets.exportList) {
				this.widgets.exportList.stopQueryStatus = true;
				this.widgets.exportList.destroy();
			}

			if (type == "export") {
				this.widgets.exportList = ExportList({
					$wrap: this.tableWrapper2Table,
				});
			} else {
				this.widgets.exportList = ImportList({
					$wrap: this.tableWrapper2Table,
				});
			}
			if (!this.widgets.operationType) {
				this.widgets.operationType = FxUI.create({
					wrapper: ".operationType > .select",
					replaceWrapper: true,
					template: `<fx-select v-model="val" :options="options" size="small" @change="onChange"></fx-select>`,
					data: function () {
						return {
							val: type == "export"?"export":"import",
							options: [
								{
									value: "import",
									label: $t("daoruyonghuzu"),
								},
								{
									value: "export",
									label: $t("daochuyonghuzu"),
								},
							],
						};
					},
					methods: {
						onChange: function (val) {
							if (me.widgets.exportList) {
								me.widgets.exportList.stopQueryStatus = true;
								me.widgets.exportList.destroy();
							}
							if (val == "export") {
								me.widgets.exportList = ExportList({
									$wrap: me.tableWrapper2Table,
								});
							} else {
								me.widgets.exportList = ImportList({
									$wrap: me.tableWrapper2Table,
								});
							}
						},
					},
				});
			} else {
				this.widgets.operationType.val = type;
			}
		},

		hideExportList: function () {
			this.tableWrapper2.hide();
			this.tableWrapper1.find('.dt-op-box').show();

			this.widgets.exportList.stopQueryStatus = true;
			this.widgets.exportList.destroy();
			this.widgets.exportList = null;
		},

		getDownloadDataShareTemplate() {
			var me = this;
			util.FHHApi(
				{
					url: "/EM1HPAASBATCH/task/importGroupTask/downloadGroupTemplate",
					data: { argType: 1 },
					success: function (res) {
						console.log("success...");
						if (res.Result.StatusCode == 0) {
							me.downloadDataShareTemplate = {};
							(res.Value.result || []).forEach((item) => {
								me.downloadDataShareTemplate[item.argType] =
									item;
							});
						}
					},
					fail: function (res) {
						console.log("fail...");
					},
				},
				{
					errorAlertModel: 0,
				}
			);
		},

		refresh: function () {
			FS.MEDIATOR.trigger("selector.usergroup.update");
			this.dt.setParam({}, true);
			// this.dt.resize();
		},

		destroy: function () {
			_.each(
				["dt", "selectSearch", "EditUserGroupWidget"],
				function (item) {
					this[item] && this[item].destroy();
					this[item] && (this[item] = null);
				},
				this
			);
		},
	});
});

