/**
 * Created by yez<PERSON> on 16/12/1.
 * 具有下拉框功能的搜索组件，使用方式可参照 crm/setting/usergroup, crm/setting/datapermissions
 */
define(function (require, exports, module) {
    
    /**
     * 下拉框
     * @example
     * ```html
     * <div id="select-wrapper" style="padding: 10px; width: 200px;"></div>
     * ```
     *
     * ```javascript
     * var Select = require('crm-modules/common/selectsearch/selectsearch');
     * var select = new Select({
     *     wrapper: $('#select-wrapper'),   // 容器
     *     options: [
     *         {
     *             value: 'key',
     *             name: 'name'
     *         },
     *         {
     *             value: 'key1',
     *             name: 'name1'
     *         }
     *     ],
     *     defaultValue: 'key'          // 默认值
     * });
     *
     * select.on('change', function(v, item){
     *     console.log(item);
     * });
     *
     * // 获取当前选中项的值
     * select.getValue();
     *
     * // 设置选中项的值
     * select.setValue('key1');
     *
     * // 添加一个选项
     * select.addOption({value: 'key2', name: 'name2'});
     *
     * // 根据value删除一个选项
     * select.removeOption('key');
     *
     * // 重置所有选项
     * select.resetOptions([{value: 'v', name: 'n'}])
     * ```
     */
    var util = require('base-modules/utils');
    module.exports = Backbone.View.extend({
        options: {
            wrapper: null,
            trigger: 'click',
            position: 'absolute',
            width: null,
            height: 240,
            zIndex: 100,
            spacing: 5,
            defaultValue: null,
            parentNode: $('body'),
            scrollNode:  $(window),

            disabled: false,
            // @example options: [{value: 'value', name: 'name'}]
            options: [],
            titleTemplate: '{{name}}',
            optionTemplate: '<li data-value="{{value}}" title="{{name}}">{{name}}</li>'
        },

        initialize: function () {
            var me = this,
                options = me.options;

            me.setElement([
                '<div class="f-g-select" style="width:110px;">',
                    '<div class="g-select-title-wrapper" style="border-right:0;border-top-right-radius: 0;border-bottom-right-radius: 0;">',
                        '<div class="select-title"></div><i></i>',
                    '</div>',
                 '</div>',    
                 '<div class="dt-ipt-wrap" style="flex:1;display: flex;border-left: 0;border-top-left-radius: 0;border-bottom-left-radius: 0;padding:2px 5px;">',
                 '<span class="line" style="display:block;margin: 3px 10px 3px 0;height: 16px;"></span>',
                 '<input placeholder='+$t("请输入")+' class="dt-ipt"></div><span class="dt-sc-ico j-sc-btn"></span>'
                ].join(''));
            me.$titleWrapper = me.$('.g-select-title-wrapper');
            me.$title = me.$titleWrapper.find('.select-title');
            options.wrapper.append(me.$el);
            me.uuid = _.uniqueId('select_');

            me.disabled = options.disabled || false;
            me.disabled && me.$el.addClass('g-select-disabled');

            if (options.trigger == 'mouseenter' || options.trigger == 'mouseover') {
                me.hideTimer = null;
                me.showTimer = null;
                me.$el.on('mouseenter', function(){

                    if(me.disabled)return;

                    me._hoverShow();
                });
                me.$el.on('mouseleave', function () {

                    if(me.disabled)return;

                    me._hoverHide();
                });
            }
            else {
                me.$el.filter('.f-g-select').on(options.trigger, function (evt) {

                    if(me.disabled)return;

                    if (me.status == 'show') {
                        me._hide();
                    }
                    else {
                        me._show();
                    }
                    util.stopPropagation(evt, me.uuid);
                });
                $('body').on('click.select'+me.uuid, function (e, data) {
                    var $target = $(e.target);
                    if (data && data.target == me.uuid) return;
                    if ($target.closest(me.$el.filter('.f-g-select')).length == 0 ) { 
                        me._hide();
                    }
                });
            }

            me.optionTemplate = _.template(options.optionTemplate);
            me.titleTemplate = _.template(options.titleTemplate);

            // 有默认值
            if (options.defaultValue !== null) {
                me.setValue(options.defaultValue);
            }
            else if (options.options.length) {
                me.setValue(options.options[0].value);
            }

            me.rendered = false;
        },

        _render: function () {
            var me = this, options = me.options;
            if (!me.rendered) {
                var html = [], flag = false;
                if (!me.$optionsWrapper) {
                    me.$optionsWrapper = $('<div class="' + options.prefix + 'options-wrapper" style="display:none;"></div>');
                    me.$options = $('<div style="display: inline-block;width: 130px;height: 32px;line-height:32px;color: #999;margin: 0 15px;border-bottom: 1px solid #eee;box-shadow: inset 0 1px 0 0 #eee;">'+ $t("选择搜索范围") +'</div><ul class="g-select-options"></ul>').appendTo(me.$optionsWrapper);

                    me._bindEvent();
                }

                _.each(me.options.options, function (item) {
                    html.push(me.optionTemplate(item));
                });

                me.$optionsWrapper.find('.g-select-options').html(html.join(''));
                me.$optionsWrapper.appendTo($(me.options.parentNode));
                me.$options.find('[data-value="' + me.value + '"]').addClass('state-selected');
                me.rendered = true;
            }
        },

        _bindEvent: function () {
            var me = this, options = me.options, t;
            me.$optionsWrapper.on('click', 'li', function (e) {
                var $this = $(this),
                    value = $this.attr('data-value');
                me.setValue(value);

                me._hide();
                e.stopPropagation();
            });
            
            $(window).on('resize.select'+me.uuid, function() {
                clearTimeout(t);
                t = setTimeout(function() {
                    if (!me.$optionsWrapper.is(':hidden')) {
                        me._setPosition();
                    }
                }, 100);
            })
            if (options.trigger == 'mouseenter' || options.trigger == 'mouseover') {
                me.$optionsWrapper.on('mouseenter', function(){
                    me._hoverShow();
                });
                me.$optionsWrapper.on('mouseleave', function () {
                    me._hoverHide();
                });
            }
        },

        _hoverShow: function () {
            var me = this;
            clearTimeout(me.hideTimer);
            me.showTimer = setTimeout(function () {
                me._show();
            }, 250);
        },
        _hoverHide: function () {

            var me = this;
            clearTimeout(me.showTimer);
            me.hideTimer = setTimeout(function () {
                me._hide();
            }, 250);
        },
        _show: function () {
            var me = this;
            me._render();
            me._setPosition();
            me.$titleWrapper.addClass('g-select-expand');
            me.$optionsWrapper.slideDown(200);
            me.status = 'show';
        },

        _hide: function () {
            var me = this;
            if (me.status == 'show') {
                me.$titleWrapper.removeClass('g-select-expand');
                me.$optionsWrapper.hide();
                me.status = 'hide';
            }
        },

        /**
         * 设置选中的值
         * @public
         * @param options
         * @param {boolean} slient 是否静默设置
         */
        setValue: function (value, slient) {
            var me = this, options = me.options, index = -1;
            if (value != me.value) {
                if (me.$options) {
                    me.$options.find('[data-value="' + value + '"]').addClass('state-selected').siblings().removeClass('state-selected');
                }

                for (var i = 0; i < options.options.length; i++) {
                    if (options.options[i].value == value) {
                        index = i;
                        break;
                    }
                }

                if (index > -1) {
                    var item = options.options[index];
                    /**
                     * 选中的值发生改变
                     * @event change
                     * @type {object}
                     */
                    me.$title.html(me.titleTemplate(item));
                    me.value = value;
                    !slient && me.trigger('change', item);
                }
            }
        },
        
        getReturnValue: function() {
            var me = this;
            return {
                selectValue: me.getValue(),
                searchValue: me.$el.find('.dt-ipt').val()
            };
        },

        reset: function() {
            this.$el.find('.dt-ipt').val('');
            this.setValue(this.options.options[0] && this.options.options[0].value, true);  
        },
        /**
         * 获取当前选中的值
         */
        getValue: function () {
            return this.value;
        },

        /**
         * 重新设置全部选项
         * @param options
         */
        resetOptions: function (options) {
            var me = this;
            me.rendered = false;
            me.options.options = options;
            if (me.status == 'show') {
                me._render();
            }
            for (var i = 0; i < options.length; i++) {
                if (options[i].value == me.value) {
                    me.$title.html(me.titleTemplate(options[i]));
                    break;
                }
            }
        },

        /**
         * 添加一个选项
         * @param {string} option.value 值
         * @param {string} option.name 显示的名
         */
        addOption: function (option) {
            var me = this;
            me.options.options.push(option);
            if (me.status == 'show') {
                me.$optionsWrapper.find('.g-select-options').append(me.optionTemplate(option));
            }
            else {
                me.rendered = false;
            }
        },

        /**
         * 删除一个选项
         * @param value 值
         */
        removeOption: function (value) {
            var me = this,
                options = me.options.options,
                index = -1;

            for (var i = 0; i < options.length; i++) {
                if (options[i].value == value) {
                    index = i;
                    break;
                }
            }
            if (index > -1) {
                if (value == me.value) {
                    if (options.length > 1) {
                        me.setValue(options[(index + 1) % options.length].value);
                    }
                    else {
                        me.setValue(null);
                    }
                }
                options.splice(index, 1);
                if (me.status == 'show') {
                    me.$options.find('[data-value="' + value + '"]').remove();
                }
                else {
                    me.rendered = false;
                }
            }
        },

        /*

         * 设置为disable状态
         * @param {boolean} value
         * */
        setDisable: function(value){
            value = value || false;
            this.disabled = value;
            value ? this.$el.addClass('g-select-disabled') : this.$el.removeClass('g-select-disabled');
        },

        _setPosition: function () {
            var me = this,
                options = me.options,
                position = options.position,
                spacing = options.spacing,
                $scroll = options.parentNode.closest('.ui-scrollbar'),
                $win = $scroll && $scroll[0] ? $scroll : options.scrollNode,
                winTop = $win.offset() ? $win.offset().top : 0,
                parentNodeOffset = $(options.parentNode).offset(),
                sourceEl = me.$el,
                offset = sourceEl.offset(),
                height = me.$optionsWrapper.height(),
                left, top;
            if (position == 'fixed') {
                left = offset.left - $win.scrollLeft() - parentNodeOffset.left;
                top = offset.top - $win.scrollTop() + sourceEl.height() + spacing - parentNodeOffset.left;

                if (top < 0) {
                    top = offset.top - $win.scrollTop() - height - spacing;
                }
            }
            else {
                position = 'absolute';

                top = offset.top + sourceEl.height() + spacing - parentNodeOffset.top;
                left = offset.left - parentNodeOffset.left;

                var _height = height > options.height ? options.height : height;
                if (top + parentNodeOffset.top - winTop - $win.height() > -10) {
                    if (parentNodeOffset.top - winTop < _height) {
                        if ($scroll && $scroll.length > 0) { // 上下全放不下
                            setTimeout(function() {
                                $('.scroll-content', $scroll.parent()).scrollTop(40);
                            }, 200);
                        }
                    } else {
                        top = offset.top - _height - spacing - parentNodeOffset.top;
                    }
                }
            }
            me.$optionsWrapper.css({
                position: position,
                top: top,
                left: left,
                zIndex: options.zIndex
            });
            me.$options.css({
                'max-height': options.height,
                //'width': options.width ? options.width : sourceEl.width()
            });
        },

        /**
         * 销毁
         */
        destroy: function () {
            /**
             * 销毁事件
             * @event destroy
             */
            var uuid = this.uuid;
            this.trigger('destroy');
            this.undelegateEvents();
            if (this.$optionsWrapper) {
                this.$optionsWrapper.remove();
            }
            $('body').off('.select'+uuid);
            $(window).off('resize.select'+uuid);
            this.remove();
        }
    });
});
