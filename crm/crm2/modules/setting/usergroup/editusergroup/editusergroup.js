/**
 * 新建用户组
 */
define(function (require, exports, module) {
    var util = require("crm-modules/common/util");
    var Dialog = require("crm-widget/dialog/dialog");
    var Selector = require("crm-widget/selector/selector");
    var mainHtml = require("../template/edit-usergroup-html");
    var EditUserGroup = Dialog.extend({
        attrs: {
            title: $t("新建用户组"),
            content: '<div class="crm-loading"></div>',
            showBtns: true,
            showScroll: false,
            data: null,
            className: "crm-s-usergroup",
        },

        events: {
            "click .name": "onRemoveErr",
            "click .b-g-btn": "onSubmit",
            "click .b-g-btn-cancel": "destroy",
        },

        //显示
        show: function (opts) {
            var result = EditUserGroup.superclass.show.call(this);
            this.set(opts);
            this.setContent(
                mainHtml({
                    data: this.get("data"),
                })
            );
            this.initSelector();
            this.initTranslateInput();
            return result;
        },
        //初始化多语翻译组件
        initTranslateInput: function () {
            var _this = this;
            if (_this.get("type") === "add") {
                _this.initNameInput();
            } else {
                util.FHHApi(
                    {
                        url: "/EM1HCRMUdobj/groupApi/queryGroupMulti",
                        data: {
                            groupId: _this.get("groupId"),
                        },
                        success: function (res) {
                            if (res.Result.StatusCode == 0) {
                                _this.set({ oldLanguages: res.Value });
                                _this.initNameInput(res.Value);
                            }
                        },
                    },
                    {
                        errorAlertModel: 1,
                    }
                );
            }
        },
        //初始化名称选择组件
        initNameInput: function (res = {}) {
            var _this = this;
            _this.translateInput = window.FxUI.create({
                wrapper: ".j-translate-input",
                template: `<fx-input-translate ref="translateInput" size="small" v-model="value" :translateDataDef="translateDataDef"
 clearable @change="onChange"></fx-input-translate>`,
                data: function () {
                    return {
                        value:
                            _this.get("data")?.defName ||
                            _this.get("data")?.name ||
                            "",
                        translateDataDef: res,
                    };
                },
                methods: {
                    onChange: function () {},
                },
                mounted: function () {},
            });
        },
        //初始化选人组件
        initSelector: function () {
            this.employeeWidget = new Selector({
                $wrap: this.$(".j-selector"),
                zIndex: this.get("zIndex") * 1 + 10,
                member: true,
                stop:true,
                single: false,
                label: $t("选择员工"),
                // defaultSelectedItems: {
                //     member: this.get("groupUsers") || [],
                // },
                defaultSelectedItems: this.get("groupUsers"),
                isFromManage: true, // 标记是否是管理后台
                enableScope: true, // 是否开启分管
            });
        },

        //表单验证
        onSubmit: function () {
            var me = this;
            var $remark = me.$(".remark");
            var $name = me.$(".j-translate-input");
            var remark = $remark.val() || "";
            var languages =
                me.translateInput.$refs.translateInput.getTranslateData();
            var reqs;
            var name = me.translateInput.value;
            if (!name) {
                util.showErrmsg($name, $t("请输入组名称"));
                return false;
            } else if (name.length > 20) {
                util.showErrmsg($name, $t("角色名称不能超过20个中文字符"));
                return false;
            } else if (remark.length > 2000) {
                util.showErrmsg($remark, $t("备注不能超过2000个中文字符"));
                return false;
            }
            var member = this.employeeWidget.getValue("member") || [];
            var stop = this.employeeWidget.getValue("stop") || [];
            member = member.concat(stop).join(',')
            if (me.get("type") === "add") {
                reqs = {
                    name: name,
                    languages: languages,
                    description: remark,
                    groupId: this.get("groupId"),
                    // userIds: (
                    //     this.employeeWidget.getValue("member") || []
                    // ).join(","),
                    userIds: member
                };
            } else {
                reqs = {
                    name: name,
                    defName: name,
                    languages: languages,
                    oldLanguages: this.get("oldLanguages") || {}, // 修改时，传旧的语言
                    description: remark,
                    groupId: this.get("groupId"),
                    // userIds: (
                    //     this.employeeWidget.getValue("member") || []
                    // ).join(","),
                    userIds: member
                };
            }

            this.submit(reqs);
        },

        onRemoveErr: function (e) {
            $(e.currentTarget).next().remove();
        },
        //保存设置
        submit: function (req) {
            var me = this;

            util.FHHApi(
                {
                    url:
                        me.get("type") === "add"
                            ? "/EM1HCRMUdobj/groupApi/createGroup"
                            : "/EM1HCRMUdobj/groupApi/updateGroupInfo",
                    data: req,
                    success: function (res) {
                        if (res.Result.StatusCode == 0) {
                            me.trigger("success");
                            util.remind(1, $t("操作成功！"));
                            me.hide();
                        } else {
                            util.alert(
                                res.Result.FailureMessage || $t("操作失败！")
                            );
                        }
                    },
                },
                {
                    submitSelector: me.$(".b-g-btn"),
                    errorAlertModel: 1,
                }
            );
        },

        hide: function () {
            this.destroy();
        },

        destroy: function () {
            this.employeeWidget && this.employeeWidget.destroy();
            return EditUserGroup.superclass.destroy.call(this);
        },
    });
    module.exports = EditUserGroup;
});