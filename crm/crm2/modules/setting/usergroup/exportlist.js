/**
 * 已导出列表
 */

define(function (require, exports, module) {
	var util = require("crm-modules/common/util");
	var Table = require("crm-widget/table/table");

	function func(defOpts) {
		var obj = {
			taskTable: null,
			initTasklistTable(param) {
				var me = this;
				this.type = "DataShareExport";
				var opts = this.getOptions();

				this.taskTable && this.taskTable.destroy();
				this.taskTable = new Table(opts);
				this.taskTable.stopQueryStatus = false;
				this.taskTable.$el.on("click", ".j-oprate", function (evt) {
					let $target = $(evt.target);
					let $tr = $target.closest("tr");
					let rowData = $.extend({}, me.taskTable.getRowData($tr));
					if (
						$target.hasClass("j-d-download") &&
						!$target.hasClass("disable")
					) {
						me.startDownload(rowData);
					}
				});
				this.taskTable.setParam(
					{
						taskType: 2,
						wheres: [],
					},
					true
				);
				this.taskTable.start();
			},
			getOptions() {
				var me = this;
				var columns = this.getColumns();
				var opts = {
					$el: defOpts.$wrap,
					url: "/EM1HPAASBATCH/task/findGroupTask",
					title: "",
					trHandle: true,
					openStart: true,
					alwaysShowTermBatch: true,
					showMultiple: false,
					requestType: "FHHApi",
					postData: {},
					autoHeight: false,
					caption: {},
					columns: columns,
					rowCallBack: function ($tr, rowData) {},
					initComplete: function ($el) {},
					getDataBack: function (data, $box) {
						return data;
					},
					formatData: function (data) {
						// {data: [], licenseInfo: "", pageNumber: 1, pageSize: 20, totalCount: 0}
						me.listData =
							(data &&
								data.data.map((item) => item.taskStatus)) ||
							[];
						if (
							data.data.filter(
								(item) =>
									item.taskStatus == "Running" ||
									item.taskStatus == "Ready"
							).length > 0
						) {
							me.queryStatus(data.pageNumber, data.pageSize);
						} else {
							me.timer && clearTimeout(me.timer);
						}
						return {
							totalCount: data.totalCount,
							data: data.data,
						};
					},
					getFullDataBack: function (res) {
						if (res.Error && res.Error.Code == "s211030015") {
							setTimeout(function () {
								util.alert(res.Error.Message, function () {
									location.reload();
								});
							}, 300);
						}
					},
				};
				return opts;
			},
			//轮询
			queryStatus: function (pageNumber, pageSize) {
				var me = this;
				clearTimeout(me.timer);
				if (me.taskTable.stopQueryStatus) return;

				var xhr = CRM.util.FHHApi({
					url: "/EM1HPAASBATCH/task/findGroupTask",
					data: {
						pageNumber: pageNumber || 1,
						pageSize: pageSize || 20,
						taskType: 2,
						wheres: [],
					},
					success: function (data) {
						if (data.Result.StatusCode == 0) {
							if (
								data.Value.data.filter(
									(item) =>
										item.taskStatus == "Running" ||
										item.taskStatus == "Ready"
								).length > 0
							) {
								let flagStatus = false;
								data.Value.data.forEach((status, i) => {
									if (
										status &&
										status.taskStatus !== me.listData[i]
									) {
										flagStatus = true;
										return;
									}
								});
								if (flagStatus) {
									me.timer = setTimeout(function () {
										clearTimeout(me.timer);
										me.taskTable.setParam(
											{
												wheres: [
													{
														connector: "OR",
														filters: [
															{
																field_name:
																	"operation_type",
																field_values:
																	me.type,
																operator: "LT",
																value_type: 0,
															},
														],
													},
												],
											},
											true
										);
									}, 5000);
								} else {
									me.timer = setTimeout(function () {
										clearTimeout(me.timer);
										me.queryStatus(pageNumber, pageSize);
									}, 3000);
								}
							} else {
								clearTimeout(me.timer);
								me.taskTable.setParam(
									{
										wheres: [
											{
												connector: "OR",
												filters: [
													{
														field_name:
															"operation_type",
														field_values: me.type,
														operator: "LT",
														value_type: 0,
													},
												],
											},
										],
									},
									true
								);
							}
						}
					},
				});
			},
			getColumns() {
				var columns = [
					{
						data: "operation",
						title: $t("操作类型"),
						render(oprationType) {
							if (oprationType == "GroupExport") {
								return $t("daochuyonghuzu");
							}
						},
					},
					{
						data: "taskStatus",
						title: $t("任务状态"),
						width: 200,
						// isFilter:true,
						render(taskStatus) {
							switch (taskStatus) {
								case "Ready":
									return $t("已准备");
								case "Running":
									return $t("执行中");
								case "Stopped":
									return $t("已停止");
								case "Finished":
									return $t("已完成");
							}
						},
					},
					{
						data: "fileName",
						title: $t("导出文件"),
						width: 200,
					},
					{
						data: "userName",
						width: 200,
						title: $t("创建人") || "--",
					},
					{
						data: "createTime",
						title: $t("创建时间"),
						render(createTime) {
							if (createTime) {
								return FS.moment(createTime).format(
									"YYYY-MM-DD HH:mm:ss"
								);
							} else {
								return "--";
							}
						},
					},
					{
						data: "taskId",
						width: 200,
						title: $t("任务ID"),
					},
					{
						data: "taskStatus",
						title: $t("操作"),
						lastFixed: true,
						render: function (taskStatus, column, data) {
							var s = "";
							if (taskStatus != "Finished" || !data.filePath) {
								s = "disable";
							}
							var str =
								'<span class="options-wrapper"><a href="javascript:;" class="j-oprate j-d-download ' +
								s +
								'" download="">' +
								$t("下载") +
								"</a></span>";
							return str;
						},
					},
				];
				return columns;
			},
			startDownload(data) {
				if (!data.filePath) return;
				var aDom = document.createElement("a"); //创建标签
				var evt = document.createEvent("HTMLEvents"); //创建事件
				evt.initEvent("click", false, false); //初始化事件，绑定点击事件，不冒泡，不阻止浏览器默认行为
				aDom.download = data.fileName;
				aDom.href = FS.util.getFscLink(
					data.filePath,
					data.fileName,
					true
				);
				aDom.dispatchEvent(evt); //触发事件
				aDom.click();
			},
		};
		obj.initTasklistTable();

		return obj.taskTable;
	}

	module.exports = func;
});
