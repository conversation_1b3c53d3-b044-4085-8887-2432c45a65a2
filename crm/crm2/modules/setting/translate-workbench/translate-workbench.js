define(function(require, exports, module) {

  // var VuiSdk = require('paas-vui/sdk').default;
  var Translate = Backbone.View.extend({
    initialize: function (opts) {
      this.setElement(opts.wrapper);
    },
    render: function() {
      var el = this.el;
      // VuiSdk.getTranslateWorkbench().then(function (TranslateWorkbench) {
      //   TranslateWorkbench.init(el)
      // })
      // require.async("paas-vui/sdk", function (VuiSdk) {
      //     VuiSdk.getTranslateWorkbench().then(function (TranslateWorkbench) {
      //         TranslateWorkbench.init(el);
      //     });
      // });
      
      // 翻译工作台项目整合处理
      require.async("base-biz/sdk", function (sdk) {
          sdk.views["translateworkbench"] &&
              sdk.views["translateworkbench"]().then(function (obj) {
                  obj.init(el);
              });
      });
    }
  });

  module.exports = Translate;
});