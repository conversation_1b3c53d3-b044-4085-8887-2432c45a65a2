/**
 * @Desc   前端默认预设对象数据
 * <AUTHOR>
 */
define(function (require, exports, module) {

    var columns = [{
        data:  'Name',
        title: $t("预设对象名称"),
        render: function(data, type, full) {
            return '<a href="javascript:;" class="j-name j-'+ full.ApiName.toLowerCase() +'">'+ data +'</a>'
        }
    },{
        data:  'ApiName',
        title: 'API Name'
    },{
        data:  'CreatedBy',
        title: $t("创建人")
    },{
        data:  'Desc',
        title: $t("描述")
    },{
        data:  'Status',
        title: $t("状态")
    }];

    var data = [{
        Name:    $t("crm.销售线索"),
        ApiName: 'LeadsObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 1,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: ********
    },{
        Name:    $t("crm.客户"),
        ApiName: 'AccountObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 2,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: ********
    },{
        Name:    $t("crm.联系人"),
        ApiName: 'ContactObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 3,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: ********
    },{
        Name:    $t("crm.商机"),
        ApiName: 'OpportunityObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 8,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: ********
    },{
        Name:    $t("crm.销售订单"),
        ApiName: 'SalesOrderObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 11,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200305
    },{
        Name:    $t("crm.销售订单产品"),
        ApiName: 'SalesOrderProductObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 28,
        layoutCode: 10200305
    },{
        Name:    $t("crm.退货单"),
        ApiName: 'ReturnedGoodsInvoiceObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 12,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200306
    },{
        Name:    $t("crm.退货单产品"),
        ApiName: 'ReturnedGoodsInvoiceProductObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 27,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200306
    },{
        Name:    $t("crm.拜访"),
        ApiName: 'VisitingObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 13,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200307,
        checkedProp: 'isNotYunZhiJia'
    },{
        Name:    $t("crm.回款"),
        ApiName: 'PaymentObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 5,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 102003081
    },{
        Name:    $t("crm.退款"),
        ApiName: 'RefundObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 6,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200309
    },{
        Name:    $t("crm.开票申请"),
        ApiName: 'InvoiceApplicationObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 9,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200310
    },{
        Name:    $t("crm.合同"),
        ApiName: 'ContractObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 16,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200311
    },{
        Name:    $t("crm.市场活动"),
        ApiName: 'MarketingEventObj',
        CreatedBy: $t("系统"),
        Desc:    '--',
        Status:  $t("启用"),
        objectType: 20,
        CreateTime: 0,
        IsActive: true,
        Define_type: 'sys',
        layoutCode: 10200313
    }];

    
    // 新版本权限
    //
    var verConfig = {
        'basic_edition':           [0,1,2,3,4,5,6,7,10,11,13],
        'wechat_standard_edition': [0,1,2,3,4,5,9,11],
        'kdweibo_edition':         [0,1,2,3,4,5,9,11],
        'kis_edition':             [0,1,2,4,5],
        'dealer_edition':          [1,2,4,5,6,7,10,11,12,13],
        'agent_edition':           [0,1,2,3,4,5,6,7,10,11,12,13],
        'promotion_sales_edition': [1,2,4,5,6,7,10,11,12,13],
        'wechat_standardpro_edition': [0,1,2,3,4,5,6,7,10,11,13],
        'standardpro_edition':     [0,1,2,3,4,5,6,7,10,11,12,13],
        'strengthen_edition':      [0,1,2,3,4,5,6,7,10,11,12,13],
        'enterprise_edition':      [0,1,2,3,4,5,6,7,10,11,12,13],
        'office_edition':          [0,1,2,3,4,5,6,7,10,11,12,13]
    };
    
    var objData = [];
    if (CRM.control.crmVersion) {
        _.each(verConfig[CRM.control.crmVersion], function(item) {
            objData.push(data[item]);
        });
    }
    
    module.exports = {
        data: objData,
        columns: columns
    };
});
