/**
 *  @desc  修改tags  地址类型
 */
define(function (require, exports, module) {
	var Tags = require('crm-modules/setting/common/tags/tags');

	module.exports = Tags.extend({
		initialize: function (opts) {
			var me = this;
			me.typeObj = {
				enumName: 'EnumCRMAddressType',
				name: 'address',
				type: 4,
				desc: $t("地址类型"),
				showRequire:false,
			};
			me._bindEvents();
			me.getConfig();
			me.render();
		},
	})

});
