/**
 * @file 后台管理预设对象迁移
 * @desc 客户的地址类型、唯一性规则，自定义对象调用我们的组件
 * <AUTHOR>
 */
define(function (require, exports, module) {

    var util = require('crm-modules/common/util'),
        newTb = require('crm-widget/table/table');
    var ObjectSDK = require('paas-object/sdk.js');
    var guideTpl = require('./template/guide-html');

    var MyObject = Backbone.View.extend({

        guide_key: 'guide_setting_myobject_payment',

        initialize: function (opts) {
            var params = util.getTplQueryParams() || {};

            // 获取版本模块配置
            CRM.api.get_licenses({
                objectApiName: params && params.api_name,
            });

            this.setElement(opts.wrapper);
            this.oObjectSDK = new ObjectSDK();

            this.keyword = ''; // 搜索关键词
            if (params.api_name) {
                this.jumpObjectDetail(params);
                return;
            }
			this.initDesc();
        },

        loadingTpl: _.template('<div class="myobject-loading"></div>'),

        events: {
            'click .j-designadd': 'onDesignAdd',  // 设计器新建
            'click .j-handadd': 'onHandAdd'     // 手动新建
        },

        initDesc: function () {
            this.$el.html('<div class="myobject-box"><div class="my-tb-wrap"></div><div class="my-tb-info"></div></div>');
            this.initTb();
        },

        /**
         * @desc 列表
         */
        initTb: function () {
            var me = this;
            me.tb = new newTb({
                $el: me.$('.my-tb-wrap'),
                tableName: 'inventory',
                url: '/EM1HCRMUdobj/objectDescribe/findByTenantId',
                postData: {
                    tenant_id: ''
                },
                requestType: 'FHHApi',
                showMultiple: false,
                trHandle: false,
                searchTip: $t("对象"),
                title: $t("预设对象管理") + '<a class="crm-doclink" href="https://help.fxiaoke.com/dbde/2049/5130/5a98" target="_blank"></a>',
                search: {
                    placeHolder:     $t("搜索预设对象"),
                    type:            'Keyword',
                    highFieldName:   'Name'
                },
                doStatic: true,
                openStart: true,
                showPage: false,
                columns: [{
                    data: 'Name',
                    title: $t("对象名称"),
                    width: 250,
                    render: function (data, type, full) {
                        var str = '<a href="javascript:;" class="j-detail" title="' + (_.escape(full.Name) || '--') + '">' + data + '</a>';
                        if (full && full.visibleScope == 'public') {
                            str += '<span class="el-tag fx-tag el-tag--link el-tag--small el-tag--light" style="line-height:19px;height:19px;margin-left:4px;border:0;">Public</span>';
                        }
                        return str;
                    }
                }, {
                    data: 'ApiName',
                    title: 'API Name'
                },  {
                    data: 'Desc',
                    title: $t("描述")
                },{
                    data: 'CreatedBy',
                    title: $t("创建人"),
                    render: function (data) {
                        var obj = FS.contacts.getEmployeeById(data) || {};
                        return obj.fullName || obj.name || '--';
                    }
                }, {
                    data: 'CreateTime',
                    title: $t("创建时间"),
                    dataType: 4
                }, {
                    data:     'LastModifiedBy',
                    title:    $t("最后修改人"),
                    render: function(data) {
                        var obj = FS.contacts.getEmployeeById(data) || {};
                        return obj.fullName || obj.name || '--';
                    }
                }, {
                    data:     'LastModifiedTime',
                    title:    $t("最后修改时间"),
                    dataType: 4
                }, {
                    data: 'Status',
                    title: $t("状态")
                }, {
                    data: 'Define_type',
                    title: $t("操作"),
                    lastFixed: true,
                    width: 170,
                    render: function () {
                        var data = arguments[2] || {}, obj = {
                            enableOrDisable: ['disable', 'enable'][!data.IsActive - 0],
                            label: [$t("禁用"), $t("启用")][!data.IsActive - 0]
                        }, html = '', objectConfigs = me.objectConfigs || {}, objectConfig = objectConfigs[data.ApiName] || {};
                        // 管控
                        if (objectConfig?.object?.controlLevel == 0) {
                        	html = '';
                        } else if (data.OriginalDescribeApiName) { // 是变更单对象
                            html +='<a href="javascript:;" class="j-object-btn j-extendchangeorder">' + $t('同步') + '</a>'
                        }
                        if (/__c$/.test(data.ApiName)) {
                            html += '<a href="javascript:;" class="j-object-btn j-' + obj.enableOrDisable + '">' + obj.label + '</a><a href="javascript:;" class="j-object-btn j-delete">' + $t("删除") + '</a>'
                        }
                        return html
                    }
                }],
                formatData: function (data) {
                    return null;
				},
				initComplete: function () {
					if (me.keyword) {
                        me.tb.$el.find('.dt-ipt').val(me.keyword);
                        me.tb.$el.find('.dt-sc-btn').trigger('click');
                    } else {
                       me.searchObjectList();
                    }
				}
            });

            me.tb.on('dt.search', function (keyword) {
                me.keyword = keyword;
                me.searchObjectList();
            });

            me.tb.on('trclick', function (data, $tr, $target) {
                if ($target.closest('.j-detail').length > 0) {
                    me.showDetail(data);
                } else if ($target.hasClass('j-delete')) {
                    me.onDelete(data);
                } else if ($target.hasClass('j-enable')) {
                    me.onEnableOrDisable(data, true);
                } else if ($target.hasClass('j-disable')) {
                    me.onEnableOrDisable(data, false);
                } else if ($target.hasClass('j-extendchangeorder')) {
                    me.onExtendChangeOrder(data);
                }
			});
			
            // 如果关键词存在，则预设搜索
            /* if (me.keyword) {
                me.tb.$el.find('.dt-ipt').val(me.keyword);
                me.tb.$el.find('.dt-sc-btn').trigger('click');
            } else {
                this.searchObjectList();
            } */
        },

        /**
         * @desc 刷新列表
         * flag 是否刷新到第一页
         */
        refresh: function (flag) {
            // var me = this;
            // this.tb.setParam({}, true, flag);
            CRM.control.refreshAside();
            this.searchObjectList();
        },

        /** 对象的 list page */
        searchObjectList: function () {
            var that = this;
            that.tb.showLoading();
            
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/describe/service/findDescribeManageList',
                data: {
                    "isIncludeFieldDescribe": false,
                    "isIncludeSystemObj": false,
                    "isIncludeUnActived": true,
                    "packageName": "CRM",
                    "sourceInfo": "object_management",
                    "describeDefineType": "package",
                    "includeControlLevel": true
                },
                success: function (res) {
                    var data = [];
                    var response = res.Value || {}, { objectDescribeList = [], manageGroup = {}, objectConfigs = {} } = response;
                    objectDescribeList = objectDescribeList.filter(obj => (manageGroup.all === false && manageGroup.apiNames && manageGroup.apiNames.includes(obj.api_name)) || manageGroup.all !== false);
                    that.objectConfigs = objectConfigs;
                    that.objectDescribeList = objectDescribeList;
                    _.each(objectDescribeList, function (objectDescribe) {
                        var obj = {
                            Name: objectDescribe.display_name,
                            ApiName: objectDescribe.api_name,
                            OriginalDescribeApiName: objectDescribe.original_describe_api_name,
                            visibleScope: objectDescribe.visible_scope,
                            CreatedBy: objectDescribe.created_by,
                            CreateTime: new Date(objectDescribe.create_time).getTime(),
                            Desc: objectDescribe.description,
                            Status: [$t("禁用"), $t("启用")][(objectDescribe.is_active || 0) - 0],
                            IsActive: objectDescribe.is_active,
                            Define_type: objectDescribe.define_type,
                            LastModifiedBy: objectDescribe.last_modified_by,
                            LastModifiedTime: objectDescribe.last_modified_time
                        };
                        if (that.keyword != '') {
                            ((objectDescribe.display_name || '').toLowerCase()).indexOf((that.keyword || '').toLowerCase()) > -1 && data.push(obj);
                        } else {
                            data.push(obj);
                        }
                    });

                    that.tb.hideLoading();
                    that.tb.doStaticData(data);
                    var tableFooter = $t('当前预置对象个数{{length}}', {
                        length: data.length
                    });
                    that.$('.my-tb-info').html(tableFooter);
                }
            })



        },

        // 跳转到对象详情页
        jumpObjectDetail: function (data) {
            this.showDetail({
                Name: data.display_name,
                ApiName: data.api_name,
                ChildType: data.child_type,
                SubChildType: data.sub_child_type
            });
        },

        /**
         * @desc detail page
         * TODO
         */
        showDetail: function (data) {
            var me = this;
            me.$el.append(this.loadingTpl());
            var detail = this.oObjectSDK.getDetail({
                display_name: data.Name,
                api_name: data.ApiName
            }, function (detail) {
                me.$el.find('.myobject-loading').remove();
                var detailView = detail.defaultView({
                    child_type: data.ChildType,
                    sub_child_type: data.SubChildType
                });
                detailView.on('go_back', function () {
                    me.initDesc();
                });
                detailView.render();
                me.$el.html(detailView.$el);
                detailView.resetTab(); // 重置tab导航菜单
            });
        },

        /**
         * @desc 删除对象
         * @param {{Object}} 编辑的数据
         * TODO
         */
        onDelete: function (data) {
            var that = this;
            that.oObjectSDK.deleteObject({
                api_name: data.ApiName,
                is_active: data.IsActive
            }, function (status) {
                if (status === 'success') {
                    that.refresh();
                }
            });
        },

        /**
         * @desc 启用/禁用对象
         * @param {{Object}} 编辑的数据
         * TODO
         */
        onEnableOrDisable: function (data, isEnable) {
            var that = this;
            that.oObjectSDK.enableOrDisableObject({
                api_name: data.ApiName,
                is_active: isEnable
            }, function (status) {
                if (status == 'success') {
                    that.refresh();
                }

            });
        },

        /**
         * @desc 变更单对象同步扩展
         * @param {{Object}} 编辑的数据
         * TODO
         */
        onExtendChangeOrder: function (data, isEnable) {
            var that = this,
            	originalDescribeApiName = data.OriginalDescribeApiName,
            	objectDescribeList = that.objectDescribeList || {};
            let originalDescribe = objectDescribeList.find(object => object.api_name == originalDescribeApiName) || {};
            that.oObjectSDK.extendChangeOrderObject({
                api_name: data.ApiName,
                display_name: data.Name,
                original_describe_api_name: originalDescribeApiName,
                original_describe_display_name: originalDescribe.display_name || ''
            }, function (status) {
                // if (status == 'success') {
                //     that.refresh();
                // }
            });
        },
        /**
         * @desc  手动新建
         * TODO
         */
        onHandAdd: function () {
            console.log('手动新建');
            var me = this;
            me.$el.append(this.loadingTpl());
            this.oObjectSDK.getLayout(function (Layout) {
                me.$el.find('.myobject-loading').remove();
                me.objectView = Layout.createObjectForBasic();

                // 高级模式保存成功
                me.objectView.on(me.oObjectSDK.EVENT_NAME.OBJECT_BASIC_SAVE, function (res) {
                    CRM.control.refreshAside();
                    me.showDetail({
                        Name: res.objectDescribe.display_name,
                        ApiName: res.objectDescribe.api_name
                    });
                });
            });
        },

        /**
         * @desc 设计器新建
         * TODO
         */
        onDesignAdd: function () {
            console.log('设计器新建');
            var me = this;
            me.$el.append(this.loadingTpl());
            this.oObjectSDK.getLayout(function (Layout) {
                me.$el.find('.myobject-loading').remove();
                me.objectView = Layout.createObject();
                // 保存并退出
                me.objectView.on(me.oObjectSDK.EVENT_NAME.LAYOUT_SAVE_QUIT, function (res) {
                    CRM.control.refreshAside();
                    me.showDetail({
                        Name: res.objectDescribe.display_name,
                        ApiName: res.objectDescribe.api_name
                    });
                });

                // 退出
                me.objectView.on(me.oObjectSDK.EVENT_NAME.LAYOUT_QUIT, function (res) {
                    if (res) {
                        CRM.control.refreshAside();
                        me.showDetail({
                            Name: res.objectDescribe.display_name,
                            ApiName: res.objectDescribe.api_name
                        });
                    }
                });
            });
		},
		
		/**
         * @desc 销售页面中的引用对象
         * TODO
         */
        destroy: function () {
            this.remove();
        }
    });

    module.exports = MyObject;
});
