// 异步任务监控
define(function (require, exports, module) {

	var TaskMonitoring = Backbone.View.extend({
		initialize: function (opts) {
			this.setElement(opts.wrapper);
		},

		render: function () {
			var el = this.el;
			require.async('paas-vui/sdk',  (VuiSdk) => {
				VuiSdk.getTaskmonitoring().then((TaskMonitoring) => {
					TaskMonitoring.init(el)
				})
			});
		}
	});

	module.exports = TaskMonitoring;
});