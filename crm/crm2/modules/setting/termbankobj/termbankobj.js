// 异步任务监控
define(function (require, exports, module) {
    var List = require('crm-modules/page/list/list');

    var termbankobj = Backbone.View.extend({
        initialize: function (opts) {
            this.setElement(opts.wrapper);
        },

        render: function () {
            var el = this.el;
            this.initDesc();
        },
        initDesc: function () {
            this.initTb();
        },
        initTb: function () {
            const me = this;
            let myObjectTable = List.extend({
                operateBtnClickHandle(e) {
                    var action = $(e.target).attr('data-action');
                    me[`${action}Handle`] && me[`${action}Handle`]();
                },
                // 修改展示样式
                getOptions: function () {
                    const me = this
                    let opts = List.prototype.getOptions.apply(this, arguments);
                    return _.extend(opts, {
                    });
                },
                parseData: function (obj) {
                    var res = List.prototype.parseData.apply(this, arguments);
                    _.each(res.data, function (item) {
                        _.each(item.operate, function (opt) {
                            if (opt.action === "ChangeStatus") {
                                opt.label = item.biz_status === "1" ? $t('停用') : $t('启用')
                            }
                        });
                    });
                    return res;
                },
                // 处理编辑按钮
                _operateHandle(opts, data) {
                    const me = this;
                    const { api_name } = opts;
                    const { _id, object_describe_api_name, name, biz_status } = data;
                    if (api_name == 'Edit_button_default') {
                        const edit_opts = {
                            apiname: object_describe_api_name,
                            id: _id,
                            displayName: name,
                            success() {
                                me.refresh();
                            }
                        }
                        this.edit = CRM.api.edit(edit_opts)
                    } else if (api_name == 'ChangeStatus_button_default') {
                        const tips = biz_status === "1" ? $t('termbankobj.list.confirm.stop') : $t('termbankobj.list.confirm.open');
                        const vm = new Vue();
                        vm.$confirm(tips).then(() => {
                            const url = '/EM1HNCRM/API/v1/object/TermBankObj/action/ChangeStatus'
                            CRM.util.FHHApi({
                                url,
                                data: {
                                    objectDataId: _id,
                                    args: {
                                        biz_status: biz_status === "1" ? '0' : '1' // 0-停用 1-启用
                                    }
                                },
                                success: function (res) {
                                    if (res.Result.StatusCode == 0) {
                                        CRM.util.remind(1, $t("操作成功！"));
                                    } else {
                                        CRM.util.alert(res.Result.FailureMessage);
                                    }
                                },
                                complete() {
                                    me.refresh();
                                }
                            });
                        })
                    } else {
                        List.prototype._operateHandle.apply(this, arguments)
                    }
                },
            });
            me.tb = new myObjectTable({
                wrapper: me.$el,
                apiname: 'TermBankObj'
            })
            me.tb.render();
        }
    });

    module.exports = termbankobj;
});