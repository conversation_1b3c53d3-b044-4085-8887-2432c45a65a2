define(function(require, exports, module) {
    const util = CRM.util;
    const template = require('./template/detail-html');
    const detailDataTpl = require('./template/detailData-html');
    const Slide = require('crm-modules/common/slide/slide');

    const Detail = Slide.extend({
        events: {
            'click .d-g-btns .h-btn': '_hide',
        },
        options: {
            showMask: false,
            width: 800,
            zIndex: 500,
            className: 'crm-d-detail crm-nd-detail workhistory-crm-detail loading',
            entry: 'crm'
        },

        show(workflowName, sourceWorkflowId,workflowId, workflowInstanceId) {
            let me = this;
            me.$el.html(template({
                objname: workflowName
            }));
            require.async('paas-paasui/ui', () => {
                me.fetchFlowData(sourceWorkflowId,workflowId).then(() => {
                    me.fetchAfterActionImplementation(workflowInstanceId).then(() => {
                        CRM.util.fetchCountryAreaOptions().then((data)=>{
                            me.areaOptions = data;
                            PaasUI.utils.fetchDescribesByFilter([me.data.entityId]).then(
                                (data)=>{
                                    me.objAndRefObjList = data[me.data.entityId];
                                    me.render();
                                }
                            );
                        })
                    })
                });
            });
        },

        fetchFlowData(sourceWorkflowId,workflowId) {
            let me = this;
            return new Promise((resolve) => {
                util.FHHApi({
                    url: '/EM1HPROCESS/WorkflowAction/GetDetailByWorkflowIdAndSourceWorkflowId',
                    data: {
                        workflowId,sourceWorkflowId
                    },
                    success(res) {
                        if (res.Result.StatusCode === 0) {
                            Slide.prototype.show.apply(me);
                            me.data = res.Value.workflow;
                            let activities = me.data.workflow.activities;
                            _.each(activities, (item) => {
                                if (item.itemList && item.itemList.length) {
                                    _.each(item.itemList, (item1, index) => {
                                        if (item1.taskType == 'updates') {
                                            item1.updateFieldJson = JSON.parse(item1.updateFieldJson);
                                        }
                                    });
                                }
                            });
                            resolve();
                        }
                    }
                }, {
                    errorAlertModel: 1
                });
            });
        },

        fetchAfterActionImplementation(workflowInstanceId) {
            let me = this;
            return new Promise((resolve) => {
                util.FHHApi({
                    url: '/EM1HPROCESS/InstanceAction/Detail',
                    data: {
                        workflowInstanceId
                    },
                    success(res) {
                        if (res.Result.StatusCode === 0) {
                            me.triggerType = res.Value.triggerType;
                            me.data.triggerType = res.Value.triggerType;
                            let executionList = res.Value.executionList;
                            let exclusiveGateways = res.Value.exclusiveGateways;
                            me.variable = res.Value.variable;
                            if (executionList && !_.isEmpty(executionList)) {
                                let activities = me.data.workflow.activities;
                                _.each(activities, (item) => {
                                    if (item.itemList && item.itemList.length) {
                                        let resultMap = executionList[item.id];
                                        if (resultMap) {
                                            _.each(item.itemList, (item1, index) => {
                                                let actionResult = resultMap[index];
                                                if (actionResult && actionResult.executionState) {
                                                    if (actionResult.executionState == 'success') {
                                                        actionResult.executionStateLabel = $t('成功');
                                                    } else if (actionResult.executionState == 'error') {
                                                        actionResult.executionStateLabel = $t('异常');
                                                        actionResult.isError = true;
                                                        item.hasActionResultError = true;
                                                    }
                                                    item1.actionResult = actionResult;
                                                }
                                            });
                                            if (!item.hasActionResultError) {
                                                let temp = _.every(item.itemList, (action) => {
                                                    return action.actionResult;
                                                });
                                                if (temp) {
                                                    item.hasActionResultError = false;
                                                }
                                            }
                                        }
                                    }
                                });
                            }
                            if (exclusiveGateways && !_.isEmpty(exclusiveGateways)){
                                let transitions = me.data.workflow.transitions;
                                let activities = me.data.workflow.activities;
                                activities.forEach( (item) => {

                                    let condition = exclusiveGateways[item.id];
                                    if(condition){
                                        let t = transitions.find(e => e.fromId === item.id && e.condition)
                                        t.conditionData = condition;
                                    }

                                })
                            }

                            resolve();
                        }
                    },
                    complete() {
                        resolve();
                    },
                }, {
                    errorAlertModel: 1
                });
            });
        },

        render() {
            let me = this;
            me.initTrigger();
            require.async('paas-workprocess/flowdetail', (detailCavas) => {
                me.cavas = new detailCavas({
                    el: me.$el.find('.svg'),
                    data: me.data,
                    useToolbar: true,
                    showDetailData: true
                });
                me.cavas.show();
                me.$el.removeClass('loading');
            });
        },

        initTrigger() {
            let me = this;
            me.propertyView && me.propertyView.destroy();
            if (me.data.rule && me.data.rule.conditions && me.data.rule.conditions.length) {
                window.PaasUI.getComponent('FilterAnalyze').then((FilterAnalyze)=>{
                    me.propertyView = new FilterAnalyze({
                        model: new Backbone.Model({
                            fromApp: 'workprocess',
                            fromModule: 'filter',
                            originalConditions: me.data.rule,
                            apiName: me.data.entityId,
                            forDetail: true,
                            triggerNames: me.data.triggerNames
                        })
                    });
                    me.$el.find('.workprocess-trigger-list').html(me.propertyView.$el);
                    if (me.variable && !_.isEmpty(me.variable)){
                        let values = [];
                        let keysMap = {
                            ...me.variable.before,
                            ...me.variable.after,
                        };
                        Object.keys(keysMap).forEach(e => {
                            let fieldLabel =  PaasUI.utils.replaceRichTextV2Async({
                                content: `\$\{${me.data.entityId}.${e}\}`,
                                useHtmlVarWrapper:false,
                                objAndRefObjList: me.objAndRefObjList
                            }).content;
                            let field =  PaasUI.utils.findFieldDescribeByVar({
                                variable: `\$\{${me.data.entityId}.${e}\}`,
                                objAndRefObjList: me.objAndRefObjList
                            });
                            let value = [];
                            value.push((()=>{
                                let v = {};
                                v.value = fieldLabel.replace(/\$\{/g,'').replace(/\}/g,'');
                                return v.title = v.value,v;
                            })())
                            if(me.triggerType === '2'){
                                value.push ((()=>{
                                    let v = {};
                                    v.value = PaasUI.utils.formatFieldValue({
                                        value: me.variable.before[e],
                                        fieldDescribe: field,
                                        areaOptions:me.areaOptions,
                                        notShowAttach: true
                                    })
                                    v.title = v.value && v.value.replace && v.value.replace(/<\/?[^>]*>/g, '');
                                    return v
                                })());
                            }
                            value.push ((()=>{
                                let v = {};
                                v.value = PaasUI.utils.formatFieldValue({
                                    value: me.variable.after[e],
                                    fieldDescribe: field,
                                    areaOptions:me.areaOptions,
                                    notShowAttach: true
                                })
                                v.title = v.value && v.value.replace && v.value.replace(/<\/?[^>]*>/g, '');
                                return v
                            })());
                            values.push(value)
                        })
                        me.$el.find('.workprocess-detail-data').html(detailDataTpl({
                            data: {
                                isUpdate:me.triggerType === '2',
                                values:values
                            }
                        }));
                    }
                })

            } else {
                me.$el.find('.flow-detail').hide();
            }
        },

        _hide() {
            this.hide();
        },

        destroy() {
            Slide.prototype.destroy.call(this);
        }
    });

    module.exports = Detail;

});
