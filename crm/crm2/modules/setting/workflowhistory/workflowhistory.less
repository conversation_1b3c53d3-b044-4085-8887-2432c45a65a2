.crm-s-workflowhistory {
	.mask{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: transparent;
		z-index: 999;
	}
	.workflow-history {
		padding: 0 16px 8px 16px;
		.workflow-history-filter {
			display: flex;
			flex-wrap: nowrap;
			.api-name-box, .data-id-box, .time, .trigger-person {
				display: inline-block;
				margin-right: 16px;
				margin-bottom: 8px;
			}
			.el-range-editor.el-input__inner{
				padding: 3px 10px;
			}
			.time, .trigger-person {
				display: flex;
				align-items: center;
				.title {
					&.required:before{
						content: '*';
						color: red;
						font-size: 14px;
						margin: 3px 4px 0 0;
					}
					color: var(--color-neutrals15);
					font-size: 14px;
				}
				.time-box {
					width: 290px;
				}
				.trigger-person-box {
					height: 30px;
					width: 120px;
					flex-shrink: 0;
					.selector-input-list-wrap {
						border-radius: 4px;
						min-height: 26px;
						.btn-trigger {
							height: 26px;
							line-height: 26px;
							color: var(--color-primary06);
						}
					}
				}
			}
			.history-search-btn {
				display: inline-block;
				font-size: 12px;
				color: var(--color-neutrals01);
				padding: 4px 16px;
				background-color: var(--color-primary06);
				border: 1px solid transparent;
				border-radius: 4px;
				cursor: pointer;
				margin-bottom: 8px;
				&.disabled{
					background-color: #C0C4CC;
					cursor: not-allowed;
				}
			}

		}
		.workflow-history-tip {
			font-size: 12px;
			color: var(--color-neutrals15);
			line-height: 40px;
			padding: 0 21px;
			background-color: #F4F6F9;
			.title {
				font-size: 16px;
				color: #333333;
			}
		}
	}
}
.workhistory-crm-detail {
	.crm-d-layout {
		.sec-tit {
			padding-left: 0;
			h4 {
				border: none;
			}
		}
	}
	.detail-table{
		width: 100%;
		text-align: center;
		table-layout: fixed;
		margin-top: 16px;
		border: 1px solid #E6E7EA;
		th{
			border-left: 1px solid #E6E7EA;
			padding: 11px 8px;
			background: var(--color-neutrals03);
			color: var(--color-neutrals15);
			width: 33.3%;
			text-align: left;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
		td{
			border: 1px solid #E6E7EA;
			border-right: 0;
			border-bottom: 0;
			padding: 11px 8px;
			color: var(--color-neutrals15);
			width: 33.3%;
			text-align: left;
			overflow: hidden;
			white-space: nowrap;
			text-overflow: ellipsis;
		}
	}
	.b-content {
		padding: 0 16px;
		height: calc(~"100% - 60px");
		overflow-y: auto;
	}
	.flow-map {
		margin-bottom: 40px;
		.svg {
			background-color: #eee;
			position: relative;
		}
	}

	.detail-loading {
		width: 140px;
		height: 70px;
		border: 1px solid #ddd;
		background: url(../images/table/loading.gif) 50% no-repeat var(--color-neutrals01);
		line-height: 60px;
		font-size: 16px;
		text-align: center;
		-webkit-border-radius: 5px;
		border-radius: 5px;
		position: absolute;
		left: 50%;
		top: 50%;
		margin: -15px 0 0 -70px;
		z-index: 10;
		display: none;
	}

	&.loading {
		.b-content {
			display: none;
		}
		.detail-loading {
			display: block;
		}
	}
}
