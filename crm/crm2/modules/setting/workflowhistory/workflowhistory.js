define(function(require, exports, module) {
    const util = CRM.util;
    const WorkflowHistory = Backbone.View.extend({
        events: {
            'click .history-search-btn': 'onSearch',
        },
        initialize(opts) {
            this.setElement(opts.wrapper);

            util.uploadLog('paasflow', 'workflow', {
                operationId: 'workflowhistory',
                eventType: 'pv'
            });
        },
        startInstanceId: '',
        endInstanceId: '',
        pageNumber: 1,
        render() {
            let me = this;
            require.async('paas-paasui/lib', function(lib) {
              lib.getModule('flowLog').then((LOG) => {
                me.log = LOG.default.render({
                  el: me.$el[0],
                })
              })
        })
        },
        destroy: function() {
            this.log.destroy();
        }

    });

    module.exports = WorkflowHistory;
});
