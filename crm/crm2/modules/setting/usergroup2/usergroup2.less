.crm-s-usergroup2 {
	.select-search {
        display:flex;
		width: 230px!important;
		.f-g-select {
			width: 80px!important;
		}
		.g-select-title-wrapper {
			height: 26px;
			line-height: 26px;
			i {
				top: 10px;
			}
		}
        .dt-ipt-wrap
        .select-title {
            line-height: 26px;
        }
        .type-select {
            width: 116px;
        }
        .dt-ipt-wrap {
            padding-left: 5px!important;
        }
        .dt-sc-ico {
            display: none!important;
        }
	}
    .state-item {
	    display: inline-block;
	    width: 46px;
	    height: 28px;
	    margin-right: 8px;
	    text-align: center;
	    cursor: pointer;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		
        &.cur {
            border-radius: 3px;
            background-color: #77b8ff;
            color: var(--color-neutrals01);
        }
    }
    .btns {
        a {
            margin-right: 5px;
            cursor:pointer;
        }
    }
    .crm-g-form {
        .fm-lb{
            width: 110px;
            text-align: right;
        }
        .fm-wrap{
            float: left;
            width: 310px;
            position: relative;
        }
        .fm-ipt{
            width: 290px;
        }
        textarea{
            height: 80px;
        }
    }
    .table-wrap{
        min-width: 800px;
    }
    .table-wrap1{
        position: relative;
        height: 100%;

        .btn-export-list{
            position: absolute;
            z-index: 1000;
            top: 62px;
            right: 54px;
        }   
        .crm-table-operate__more{
            z-index: 1100 !important;
        }
    }
    .table-wrap2{
        background: #fff;
        position: absolute;
        width: 100%;
        height:calc(100% - 44px);
        top: 44px;
        bottom: 0;
        z-index: 1005;
        padding-top: 52px;
        box-sizing: border-box;

        .table-box-header{
            position: absolute;
            top: 0;
            width: 100%;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
        }

        .table-box{
            height: 100%;
        }

        .crm-w-table .dt-term-batch{
            display: none;
        }

        .j-d-download.disable{
            color: #999;
        }
    }

    .table-wrap1-0{
        min-height: 260px;
        .crm-w-table .dt-caption{
            height: 44px;
        }
        .crm-w-table .dt-caption.no-tit{
            padding-left: 0;
        }
        .crm-w-table .dt-caption .dt-control-btns__new, .crm-w-table .dt-caption .dt-sc-box__new{
            margin-left: 0;
            margin-top: 10px;
        }
        .crm-w-table .dt-term-batch{
            display: none;
        }
    }
}
