/**
 * 用户组
 * 遵循seajs module规范
 */
define(function (require, exports, module) {
	var util = require("crm-modules/common/util"),
		DataTables = require("crm-widget/table/table"),
		SelectSearch = require("./selectsearch/selectsearch");

	module.exports = Backbone.View.extend({
		initialize: function (opts) {
			this.userid = opts.props.userId;
			this.widgets = {};
			opts.wrapper.append(
				'<div class="crm-s-usergroup2"><div class="table-wrap table-wrap1 table-wrap1-0"></div></div>'
			);
			this.tableWrapper1 = opts.wrapper.find(".table-wrap1");

			this.setElement(this.tableWrapper1);
			this.initTable();
		},

		events: {},

		initTable: function () {
			var me = this;

			me.dt = new DataTables({
				$el: me.$el,
				// title: $t("crm.用户组"),
				url: "/EM1HCRMUdobj/groupApi/queryUserGroup",
				requestType: "FHHApi",
				trHandle: false,
				showSize: false,
				// showMultiple: true,
				noSupportLock: true,
				postData: {
					status: null,
					id: this.userid,
				},
				columns: [
					{
						data: "name",
						title: $t("组名"),
						width: 240,
						fixed: false,
						isFilter: false,
						// isOrderBy: true,
						render: function (data, type, full) {
							return full.status === 1
								? '<span style="color:#ccc;">' +
										data +
										"</span>"
								: data || "--";
						},
					},
					{
						data: "description",
						title: $t("备注"),
						isFilter: false,
						// isOrderBy: true,
						render: function (data, type, full) {
							return full.status === 1
								? '<span style="color:#ccc;">' +
										data +
										"</span>"
								: data || "--";
						},
					},
					{
						data: "status",
						title: $t("状态"),
						width: 120,
						isFilter: false,
						// isOrderBy: true,
						render: function (data, type, full) {
							return data === 1
								? '<span style="color:#ccc;">' +
										$t("已停用") +
										"</span>"
								: $t("启用中");
						},
					},
				],
				search: {
					pos: "C",
					placeHolder: $t("搜索"),
					type: "searchKey",
					filterColumns: [
						{
							title: $t("组名"),
							data: "name",
							isFilter: true,
							dataType: 1,
						},
						// {
						// 	title: $t("组成员"),
						// 	data: "groupUsers",
						// 	isFilter: true,
						// 	dataType: 1,
						// },
					],
				},
				sortField: "orderKey", //排序字段的key
				sortType: "isAsc", //排序字段值的key
				paramFormat: function (param) {
					let selectValue =
						me.dt?._search?.searchComp?.selectIntance?.getValue();
					if (selectValue)
						param.searchType = selectValue == "name" ? "0" : "1";
					// 处理排序参数
					if (param.isAsc) {
						if (param.isAsc == "1") param.isAsc = true;
						if (param.isAsc == "2") param.isAsc = false;
					}
					me.tableParam = param;
					return param;
				},
				// parseTableParam(obj){
				// 	return obj;
				// },
				formatData: function (data) {
					// _.each(data.list, function (item) {
					// 	item.groupUsers = data.groupUsers[item.id];
					// });
					return {
						data: data.list,
						totalCount: data.page ? data.page.totalCount : 0,
					};
				},
			});

			me.dt.on("term.change", function (v) {
				me.dt.setParam(
					{
						status: v != "9999" ? v : null,
					},
					true,
					true
				);
			});
		},
		refresh: function () {
			FS.MEDIATOR.trigger("selector.usergroup.update");
			this.dt.setParam({}, true);
			// this.dt.resize();
		},

		destroy: function () {
			_.each(
				["dt", "selectSearch", "EditUserGroupWidget"],
				function (item) {
					this[item] && this[item].destroy();
					this[item] && (this[item] = null);
				},
				this
			);
		},
	});
});

/**
 

seajs.use('crm-modules/setting/usergroup2/usergroup2',function(b){
	var a =new b({
		wrapper:$('.aaaa')
		userid:'123456'
	})
})



 */
