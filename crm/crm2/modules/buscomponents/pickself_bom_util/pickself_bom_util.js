/**
 * @description: 根据bomcore数量，判断是否打开选版本
 * @author: lishsh9516
 * @Date: 2023-11-30 14:29:05
 * @LastEditors: lishsh9516
 * @LastEditTime: 2024-01-10 20:33:50
 */
define(function (require, exports, module) {
	var util = FS.crmUtil;
	return {

		// 可能切换了根结点的节目表，需要更新
		updatePricebookId: function (obj, currentRow) {
			let rootData = obj.newRootData;
			if (currentRow.pricebook_id && rootData && currentRow.pricebook_id !== rootData.pricebook_id) {
				currentRow.pricebook_id = rootData.pricebook_id;
				currentRow.pricebook_id__r = rootData.pricebook_id__r;
				currentRow._id = rootData._id;
				currentRow.name = rootData.name;
			}
		},
		// 更新产品的 属性 和 非标属性
		updateRootAttr(rootData, currentRow) {
			if (rootData?.attribute_json) {
				currentRow.selectedAttr = rootData.selectedAttr;
				currentRow.attribute_json = rootData.attribute_json;
				this.table.setAttrVal && this.table.setAttrVal(currentRow, currentRow.attribute_json);
			}
			if (rootData?.nonstandard_attribute_json) {
				currentRow.nsAttr = rootData.nsAttr;
				currentRow.nonstandard_attribute_json = rootData.nonstandard_attribute_json;
				this.table.setNsAttrValue && this.table.setNsAttrValue(currentRow, currentRow.nonstandard_attribute_json);
			}
		},
		//属性产品，补全默认选中属性值
		supplementAttrToData:function(data, key = 'attribute_values'){
			let attrObj={},
				attrG = {},
				attrTxt="";

			this.supplementNsAttrToData(data);

			if(data.selectedAttr || !data.attribute) return;
			data.__cacheAttribute = data.attribute;
			data.attribute.forEach(a => {
				let defAttr = a[key].find(i => i.is_default == '1');
				if (!defAttr) return;
				attrObj[a.id] = {
					name: a.name,
					value_ids: [{
						id: defAttr.id,
						name: defAttr.name
					}]
				};
				attrTxt+=a.name+":"+defAttr.name+";";
				attrG[a.id] = defAttr.id;
			});
			data.selectedAttr=attrObj;
			data.attribute_json = attrG;
			data.attribute = attrTxt.slice(0, attrTxt.length - 1);


			return data;
		},

		//补全非标属性
		supplementNsAttrToData:function(data){
			if(data?.nonstandardAttribute?.length && ! data.nsAttr){
				data.nsAttr = CRM.util.getDefaultNsAttr(data);
				CRM.util.parseNsAttrData(data);
			}
		},

		// ==========================报价器相关逻辑=============================================

		// 是否开启了报价器
		isOpenQuoter(){
			return CRM.util.getConfigStatusByKey('is_open_quoter') === '1';
		},

		// 报价器，包含默认选中场景，需要走取价服务，取默认属性对应的价格，缓存，计算父级价格
		async getRealPriceForQuote(res, rootData) {
			if(this.isOpenQuoter()){
				let data =  _.find(res.dataMapList, function (item) {
					return item.describeApiName === 'BOMObj'
				})?.dataList || [];
				let quoterData = data.filter(item => item.old_attribute && item.old_attribute.length || item.selected_by_quoter);
				this._hasQuoter = !!quoterData.length;
				if(this.isIncludeDefSelect() && CRM._cache.openAttribute){
					data = data.filter(item => item.old_attribute && item.old_attribute.length);
					if (!data || !data.length) return;
					let fullData = this.parseNormalDataForPriceService(data, rootData);
					if (!fullData.length) return;
					let newRst = await this._getRealPrice(fullData);
					this._addOldPrice(data, newRst);
				}
			}
		},

		// 必选或默认选中项是否计入默认产品包总金额；
		isIncludeDefSelect: function () {
			return CRM._cache.bom_price_calculation_configuration == '0';
		},

		parseNormalDataForPriceService(data, rootData) {
			let newData = _.isArray(data) ? data : [data];
			let res = [];
			let pb = this.isNeedSyncChildrenPriceBook() ? rootData.pricebook_id : '';
			newData.forEach(item => {
				let r = this._parseDataForPriceService(item, pb);
				if (r) res.push(r);
			});
			return res;
		},

		// 主子价目表是否需要同步；
		isNeedSyncChildrenPriceBook: function () {
			return CRM._cache.bom_adaptation_price_list_rules == '0';
		},

		// 给取价服务测试
		_parseDataForPriceService(item, rootPriceBookId) {
			if (!item.isGroup && (item.price_book_id || item.pricebook_id )) {
				let pbId = item.price_book_id || item.pricebook_id;
				let attrs = {};
				if(item.old_attribute){
					item.old_attribute.forEach(c => {
						let v = c.attribute_values.find(a => a.is_default === '1');
						attrs[c.id] = v.id;
					});
					delete item.old_attribute;
				}else if (item.selectedAttr) {
					for (let key in item.selectedAttr) {
						attrs[key] = item.selectedAttr[key].value_ids[0].id
					}
				}
				return {
					"productId": item.product_id,
					"priceBookId": rootPriceBookId || pbId || '',
					"attrMap": attrs,
					"rowId": item.rowId
				}
			}
		},

		_getRealPrice(fullData = []) {
			let { form_account_id, form_partner_id, } = this.options.fieldMapping;
			let masterData = this.options.master_data;
			const {details} = this.parseCommonParams();
			return new Promise(resolve => {
				if (!fullData.length) return resolve([]);
				util.showLoading_tip();
				CRM.util.replacePriceForPriceBook({
					accountId: masterData[form_account_id],
					partnerId: masterData[form_partner_id],
					fullProductList: fullData,
					object_data: masterData || {},
					details,
				}).then(res => {
					util.hideLoading_tip();
					if (res.applicablePriceSystem && res.newRst && res.newRst.length >= 1) {
						resolve(res.newRst)
					}
				}, err => {
					util.hideLoading_tip();
				});
			})
		},

		// 使用取价数据更改默认价格
		_addOldPrice(data = [], newRst = []) {
			newRst.forEach(realData => {
				let fd = data.find(d => d.rowId == realData.rowId);
				if (!fd) return;
				fd._oldAdjustPrice =  realData.selling_price;
			});
		},

		// 报价器计算;如果报价器改了子件的属性，需要重新计算子件父级价格和产品包价格
		calculatePriceForQuoter(children, rootValue){
			if(this.isOpenQuoter() && this.isIncludeDefSelect() && CRM._cache.openAttribute && this._hasQuoter){
				const {isSupportPeriod} = this.options;
				this.saveDefSelectRow(children);
				this.calDefSelectMoney(children);
				let {totalPrice, totalSingleSetPrice,} = util.calculateAllChildrenPrice(children, isSupportPeriod);
				let newestPrice = util.accAdd(util.accAdd(Number(rootValue), -this.defSelectMondy), totalPrice);
				return {
					newestPrice,
					totalSingleSetPrice
				}
			}
		},

		/**
		 * @desc 保存默认选中行数据，用于计算;
		 */
		saveDefSelectRow: function (data) {
			let _this = this;
			_this.defSelectRow = [];
			// 缓存原始金额
			util.forEachTreeData(data, item => {
				item.__adjust_price = item.hasOwnProperty('_oldAdjustPrice') ? item._oldAdjustPrice : item.adjust_price;
			});

			util.forEachTreeData(data, item => {
				// 缓存因为报价器而选中的数据
				if(!item.isGroup && item.selected_by_quoter){
					_this._cacheSelectedByQuote.push(item);
				}
				if(!item.group && item.children) item._defSelectMoney = util.calculateSelectMoney(item.children, '__adjust_price', true);
				let o = _this._getDefObj(item);
				if(o) _this.defSelectRow.push(o);
			});
		},

		// 是否是默认选中行
		isDefSelect(data = {}) {
			return data.is_required || data.selected_by_default;
		},

		_getDefObj(item){
			if ((this.isDefSelect(item)) && !item.isGroup) {
				return {
					adjust_price: item.adjust_price,
					__adjust_price: item.__adjust_price,
					product_id: item.product_id,
					product_id__r: item.product_id__r,
					parent_bom_id: item.parent_bom_id,
					amount: item.amount,
					_id: item._id,
					bom_id: item._id,
					rowId: item.rowId,
					pid: item.pid,
					current_root_new_path: item.current_root_new_path,
				};
			}
		},

		// 报价器设置命中的节点，子件全选，其父级也勾选上
		setCheckedForQuoter(data) {
			if(!this._hasQuoter) return;
			util.forEachTreeData(data, item => {
				if (item.selected_by_quoter) {
					item.isChecked = true;
					if (item.children && item.children.length) {
						util.forEachTreeData(item.children, c => {
							c.isChecked = true;
							if (!c.children) this._cacheSelectedByQuote.push(c)
						});
					}
					if(item.pid){
						this.setAllParentChecked(item.pid, data);
					}
				}
			});
		},

		// 设置所有父级节点选中
		setAllParentChecked(rowId, allData){
			function _fn(pid) {
				let f = util.getDataByKey(pid, allData);
				if (!f) return;
				f.isChecked = true;
				if (f.pid) _fn(f.pid);
			}
			_fn(rowId)
		},

		calDefSelectMoney(data){
			if (this.isIncludeDefSelect()) this.defSelectMondy = util.calculateSelectMoney(data, '__adjust_price', true);
		},

		// 更新缓存中根节点选中属性
		_extendRootData(rootData, newData) {
			if (!newData || !rootData) return;
			if (newData.attribute_json) rootData.attribute_json = newData.attribute_json;
			if (newData.nonstandard_attribute_json) rootData.nonstandard_attribute_json = newData.nonstandard_attribute_json;
		},

		//获取报价器参数，在bom进/不进配置页，调用treeV1接口时传参数
		getQuoterParam(){
			const pickSelfParam=this.table.__getQueryParam();
			if(pickSelfParam?.extraData?.is_from_quoter){
				return {
					extraData:pickSelfParam.extraData
				}
			}
			return {};
		},

		/**
		 * @desc 批量勾选bom时，需要查默认选中项
		 * @param ids
		 * @param bomList
		 */
		//不弹框选数据增加参数，lingj
		async fetchAllBomList(ids, bomList, cb) {
			let _this = this;
			// 子件不在母件价目表内，需要阻断
			const noFlowRootPricebookProds = {};
			let rootPDMap = {};
			if (CRM._cache.priceBookPriority && CRM.util.isGrayScale('CRM_BOM_FLOW_PRICEBOOK_EXTEND')) {
				rootPDMap = await this.getRootProductFromService(ids).then(({rst}) => rst);
			}
			const {master_data: masterData, isSupportPeriod} = _this.options;
			const {details} = this.parseCommonParams();
			// 获取bom默认选中项成功的回调
			const _successCallback = async function (item, res) {
				// 开了报价器
				await _this.getRealPriceForQuote(res, item);

				let bomData = util.flatBomData(res.dataMapList);
				let id = util.isBom(item).productId;
				let bomId = util.getBomId(res.dataMapList, id);
				if (!bomId) return Promise.reject(`【${_this.getProductName(item)}】：${$t('产品包为空')}`);
				let children = util.parseDataToBOM(bomData, bomId);
				if (children.length) {
					_this._cacheSelectedByQuote = [];
					_this.setCheckedForQuoter(children);
					util.setBomChecked(children);
					children = util.getOnlyCheckedData(children);
					_.each(children, function (c) {
						c.pid = item.rowId;
						// c.adjust_price = util.formatDecimalPlace(c.adjust_price, _this.options.__price_decimal_places);
						//属性产品，补全默认选中属性值
						if(CRM._cache.openAttribute){
							CRM.util.forEachTreeData([c], data => {
								if (!data.isGroup && data.attribute) {
									data=_this.supplementAttrToData(data);
								}
							})
						}
					});
					item.children = util.sortTreeData(children, 'order_field');
				} else {
					item.children = [];
				}

				// 判断子件不在母件价目表
				util.forEachTreeData(item.children, (c) => {
					const pdId = item.pricebook_id || rootPDMap[id]?.pricebook_id;
					const __isNotFlowRootPricebook = (
						CRM.util.isGrayScale('CRM_BOM_FLOW_PRICEBOOK_EXTEND') &&
						c.price_mode === '2' &&
						c.price_book_id !== pdId
					);
					if (__isNotFlowRootPricebook) {
						if (noFlowRootPricebookProds[bomId]) {
							noFlowRootPricebookProds[bomId].push(c);
						} else {
							noFlowRootPricebookProds[bomId] = [c];
						}
						noFlowRootPricebookProds[bomId].product_name = _this.getProductName(item);
					}
				})

				util.initBomData(children);

				// 默认选中项总金额
				let defSelectValue = tssp = _this.getPrice(item);
				if(!_this.isIncludeDefSelect()){
					let {totalPrice, totalSingleSetPrice,} = util.calculateAllChildrenPrice(children, isSupportPeriod);
					defSelectValue = util.accAdd(defSelectValue, totalPrice);
					tssp = util.accAdd(tssp, totalSingleSetPrice);
				}

				item.newestPrice = defSelectValue;
				item.totalSingleSetPrice = tssp;
				item.bom_id = bomId;
				// 开了报价器，如果改了属性需要重新计算对应价格
				let r1 = _this.calculatePriceForQuoter(item.children, defSelectValue);
				if(r1 && util.hasValue(r1.newestPrice)) item.newestPrice = r1.newestPrice;
				return item;
			};
			const quoterParam=this.getQuoterParam();
			const _params = {
				price_book_id: _this.currentPriceBook ? _this.currentPriceBook.id : '',
				account_id: masterData ? masterData.account_id : '',
				partner_id: masterData ? masterData.partner_id : '',
				mc_currency: masterData ? masterData.mc_currency : '',
				child_search_query_info: JSON.stringify({
					"limit": 2000,
					"offset" :0,
					"filters": [
						{ "field_name":"enabled_status", "field_values":true, "operator":"EQ" },
						{ "field_name":"selected_by_default", "field_values":true, "operator":"EQ" }
					]
				}),
				object_data: masterData,
				details,
				...quoterParam
			}
			// 批量获取bom默认选中数据
			//TODO: 勾选不进配置页，拉取默认子件lingj
			let _p = bomList.map(async (proData) => {
				let id = proData.product_id || proData._id;
				let res = await util.fetchBomAndRelatedBomData([id], {
					bom_core_id: proData.core_id,
					..._params
				});
				return await _successCallback(proData, res);
			});
			Promise.all(_p).then(bomProDataArr => {
				if (!_.isEmpty(noFlowRootPricebookProds)) {
					let tips = [];
					_.each(noFlowRootPricebookProds, (prods, bomId) => {
						const prodsName = prods.map((prod) => `【${_this.getProductName(prod)}】`).join('');
						tips.push(`${prods.product_name}:${prodsName}`);
					})
					util.alert(tips.join('<br/>') + $t('产品不在母件选择的价目表中，如需添加该产品，需将其维护至选择的价目表'))
					return;
				} else {
					cb && cb();
				}
			}).catch(err => {
				console.error(err);
				util.alert(err);
			});
		},

		parseCommonParams() {
			const {pluginContext, source_api_name, fieldMapping: oFieldMapping = {}} = this.options;
			const priceBookDesc = pluginContext?.dataGetter.getDescribe()?.fields?.price_book_id;
			const details = CRM.util.parsePriceBookDataRangeDetails(
				pluginContext?.dataGetter.getDetails(),
				priceBookDesc,
				source_api_name
			);
			const fieldMapping = Object.assign({}, {
				account_id: 'account_id',
				partner_id: 'partner_id',
				mc_currency: 'mc_currency',
				form_account_id: 'account_id',
				form_partner_id: 'partner_id',
				form_mc_currency: 'mc_currency',

			}, oFieldMapping);

			return {
				details,
				fieldMapping,
			}
		},

		getRootProductFromService(ids) {
			const {master_data, object_data} = this.options;
			const {details, fieldMapping} = this.parseCommonParams();
			return CRM.util.replacePriceForPriceBook({
				accountId: master_data[fieldMapping.account_id],
				partnerId: master_data[fieldMapping.partner_id],
				mcCurrency: master_data[fieldMapping.mc_currency],
				fullProductList: ids.map((id) => ({productId: id})),
				object_data,
				details,
			});
		},

		// 批量选择bom，校验子件非标属性是否为空
		checkNsAttr(bomList){
			if(!CRM._cache.openNsAttribute) return true;
			let msg = '';
			bomList.forEach(item => {
				util.forEachTreeData(item.children, item => {
					if (item.isGroup || !item.isChecked) return;
					if (item.nonstandardAttribute && item.nonstandardAttribute.length) {
						let s = CRM.util.checkNsAttrAllVal(item.nonstandard_attribute_json, item.nonstandardAttribute);
						if (!s.status) msg += util.getBomPath(item) +'【' + s.msg.join(',') + '】' + ': ' + $t('非标属性值不能为空') + '<br/>';
					}
				});
			});
			if(msg){
				util.alert(msg);
				return false;
			}
			return true;
		},

		// 校验 bom 版本是否都有值
		checkBOMVersion(bomList){
			let msg = '';
			bomList.forEach(item => {
				util.forEachTreeData(item.children, item => {
					if (item.isGroup || !item.isChecked) return;
					if (item.is_package && !item.related_core_id) {
						msg += '[' +util.getBomPath(item)  + ']: BOM' + $t('版本') + $t('不能为空') +'<br/>';
					}
				});
			});
			if(msg){
				util.alert(msg);
				return false;
			}
			return true;
		},

		getPrice(data) {
			if (CRM._cache.openPriceList && data.hasOwnProperty('pricebook_sellingprice')) {
				return data.pricebook_sellingprice
			}
			return data.price;
		},

		getProductName(currentRow) {
			let {display_name, product_id__r, name} = currentRow;
			const pName = CRM._cache.openPriceList ? display_name || product_id__r || name || '' :  display_name || name || product_id__r || '';
			return pName;
		},

		// 把从对象的数据，合并到当前选中项，一起校验；
		addMdData:function(ids){
			let mdPIds = _.map(this.options.mdData, item => item.product_id);
			let res = ids.concat(mdPIds);
			res = _.uniq(res);
			return _.filter(res, item => item);
		},

		// 回填数据前，自校验；校验约束关系规则
		validSelectValueBySelf:function(value){
			let _this = this;
			if(this.options.notShowBom) return true;
			value = _.isArray(value) ? value : [value];
			let ids = _.map(value, item => item.product_id || item._id);
			ids = this.addMdData(ids);
			return new Promise(resolve => {
				util.checkProductConstraint({
					productIds: ids
				}).then(res => {
					if(res.errorMessage && Object.keys(res.errorMessage).length){
						resolve(false);
						_this.alertMsg(res.errorMessage);
						return
					}
					resolve(true)
				})
			})
		},

		// 弹约束关系校验提示信息；
		alertMsg:function(msgs){
			let msg = '';
			_.each(msgs, (obj, key) => {
				if(obj.required.length){
					msg += $t('选择') + '[<span class="productName">' + key + '</span>]' + '，' + $t('必须选择') + '[<span class="productName">' +  obj.required.join() + '</span>]' + '<br/>';
				}
				if(obj.notRequired.length){
					msg += $t('选择') + '[<span class="productName">' + key + '</span>]' + '，' + $t('不允许选择') + '[<span class="productName">' +  obj.notRequired.join() + '</span>]' + '<br/>';
				}
			});
			util.alert(msg, null,{
				className: 'crm-c-dialog crm-c-dialog-alert crm-alert-constraint ',
				title: $t("约束条件") + $t("提示")
			});
		},



	}
})
