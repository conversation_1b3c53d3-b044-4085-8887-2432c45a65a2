/**
 * @Desc: 购物车
 * <AUTHOR>
 * @date 2025/05/15
 */
define(function(require, exports, module) {
    var ShoppingCart = function(opt) {
        // this.options = opt;
        return FxUI.create({
            wrapper: opt.wrapper,
            template: `
                <div style="display: flex;">
                
                <fx-dialog
                    class="cpq_style2_shoppingCart_dialog"
                    ref="shoppingCartDialog"
                    :title="$t('crm.sfa.shopping_cart.configuration_list', null, '选配清单') + '(' + dataList.length + ')'"
                    :visible="dialogVisible"
                    modal
                    append-to-body
                    sliderPanel
                    hasScroll
                    width="500px"
                    @close="closeDialog"
                >
                    <div class="shoppingCartList">
                        <div class="item" v-for="(item, i) in dataList" :key="item.__shoppingCartUuid">
                            <img v-if="getProductImg(item)" :src="getProductImg(item)" alt="">
                            <div class="content">
                                <div class="name">{{ _getProductName(item) }}<span class="del el-icon-delete" @click="deleteItem(item, i)"></span></div>
                                <div class="attr-wrap">
                                    <div class="attr" :class="{'showMore': item && item.__shoppingCartShow}">
                                        <label>{{$t('crm.sfa.shopping_cart.selected_configuration', null, '已选配置')}}</label>
                                        <p v-flex-overflow:[item].immediate="getClassName">{{ item && item.__shoppingCartText || '--' }}</p>
                                        <span @click="triggerShowMore(item, i)" v-if="item && item.__shoppingCartText && item.__shoppingCartShowMoreBtn && !item.__shoppingCartShow" class="more-btn">{{$t('查看更多')}}</span>
                                    </div>
                                    <div @click="triggerShowMore(item, i)" v-if="item && item.__shoppingCartShow" class="fold-btn">{{$t('收起')}}</div>
                                </div>
                                <div class="op">
                                    <template v-if="!hideMoney">
                                        {{$t('单价')}}<span class="price" v-html="formatPrice(item._selfPrice)"></span>
                                    </template>
                                    <fx-input-number v-if="!hideQuantity" v-model="item._selfQuantity" :min="0" size="micro"  @change="(val) => changeCount(val, item, i)"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <span slot="footer" class="dialog-footer">
                        <div class="cpq_style2_shoppingCart_total" v-if="!hideMoney">
                            ${$t('总金额')}<span class="money" v-loading="loading" v-html="formatPrice(totalMoney)"></span>
                        </div>
                        <fx-button @click="clearShoppingCart" :disabled="!dataList.length" size="medium">{{$t('清空')}}</fx-button>
                        <fx-button type="primary" :disabled="!dataList.length" @click="submitDialog" size="medium">{{$t('crm.sfa.shopping_cart.order_now', null, '立即下单')}}</fx-button>
                        <fx-button @click="closeDialog" size="medium">{{$t('关闭')}}</fx-button>
                    </span>
                </fx-dialog>
                <fx-button
                    size="medium"
                    @click="$emit('handleAddBtn')"
                >{{$t('crm.sfa.shopping_cart.add_to_cart', null, '加入购物车')}}
                </fx-button>
                <fx-button
                    class="confirm-shopping-cart-btn"
                    size="medium"
                    type="primary"
                    :disabled="!num"
                    @click="confirm"
                >
                    {{$t('crm.sfa.shopping_cart.checkout', null, '购物车下单')}}
                </fx-button>
                <fx-badge :value="num" class="item-badge">
                    <fx-button size="medium"
                    class="shopping-cart-num-btn"
                    :disabled="!num"
                    @click="openShoppingCart"
                    icon="fx-icon-obj-app416"
                    type="primary"></fx-button>
                </fx-badge>
                </div>
            `,
            directives: {
                'flex-overflow': {
                    bind(el, binding, vnode) {},
                    inserted(el, binding, vnode) {
                        let callback = binding.value;
                        let onResize = new ResizeObserver(_.debounce(function(entries) {
                            callback(el, entries, binding.arg);
                        }, 100));
                        onResize.observe(el);
                        el._onResize = onResize;
                    },
                    unbind(el, binding, vnode) {
                        if (!el._onResize) {
                            return;
                        }
                        el._onResize.disconnect();
                        // 解绑事件
                        delete el._onResize;
                    },
                }
            },
            data() {
                return {
                    loading: false,
                    hideMoney: opt.hideMoney,
                    hideQuantity: opt.hideQuantity,
                    dialogVisible: false,
                    dataList: [],
                    _mcCurrency: opt._mcCurrency
                };
            },
            computed: {
                totalMoney() {
                    return opt._calculateTotalPrice(this.dataList);
                },
                num() {
                    return this.dataList.length;
                }
            },
            methods: {
                getClassName(el, entries, item) {
                    if (!this.dialogVisible) return;
                    if (!item?.__shoppingCartText || item.__shoppingCartShowMoreBtn) return;
                    let target = entries[0].target;
                    let width = target.clientWidth;
                    const range = document.createRange();
                    range.selectNodeContents(target);
                    const rect = range.getBoundingClientRect();
                    const text_width = rect.width;
                    this.$set(item, '__shoppingCartShowMoreBtn', text_width > width);
                },
                getProductImg(item) {
                    let productInfo = item.product_id__ro || item;
                    return productInfo?.picture_path?.[0]?.signedUrl || '';
                },
                _getProductName(item) {
                    let isBom = CRM.util.isBom(item);
                    let name = opt.getProductName(item);
                    return name ? isBom.isPackage ? `${name}（${item.core_id__r}）` : name : '--';
                },
                openShoppingCart() {
                    this.dialogVisible = true;
                },
                async addToShoppingCart(data) {
                    // 校验重复
                    let cur_data = CRM.util.deepClone(data);
                    if (CRM.util.isBom(cur_data).isPackage && cur_data.children) {
                        CRM.util.updateRowIdAndPid(cur_data.children); // 重置chidren的rowId和pid
                    }
                    let _repeatCartData = !CRM.util.isBom(cur_data).isPackage && this.isRepeatProductByAttr(cur_data);
                    if (_repeatCartData?.length) {
                        if (_repeatCartData.length > 1) {
                            console.error($t('crm.sfa.shopping_cart.repeat_product', {
                                num: _repeatCartData.length
                            }, '找到 {{num}} 个重复产品'), _repeatCartData);
                            return;
                        }
                        // 如果相同，则合并购物车中的重复项数量_selfQuantity
                        _repeatCartData[0]._selfQuantity = Number(_repeatCartData[0]._selfQuantity) + Number(cur_data._selfQuantity);
                        // 重新取价
                        await this.changeCount(_repeatCartData[0]._selfQuantity, _repeatCartData[0]);
                        console.log($t('产品已存在购物车中'));
                        this.$message.success($t('添加成功！'));
                        return;
                    }
                    this.dataList.unshift({
                        ...cur_data,
                        __shoppingCartUuid: CRM.util.uuid(),
                        __shoppingCartShow: false,
                        __shoppingCartText: this.parseAttr(cur_data),
                        __shoppingCartShowMoreBtn: false
                    });
                    this.$message.success($t('添加成功！'));
                },
                getPriceBookId(data) {
                    return data.pricebook_id;
                },
                isRepeatProductByAttr(data) {
                    let repeat = false;
                    let _id = opt.getProductId(data);
                    let {selectedAttr, nsAttr} = data;
                    let cart_data = this.dataList.filter(item => {
                        if (CRM._cache.priceBookPriority) {
                            return opt.getProductId(item) === _id;
                        }
                        return opt.getProductId(item) === _id && this.getPriceBookId(item) === this.getPriceBookId(data);
                    });
                    if (cart_data.length) {
                        let res = cart_data.filter(item => {
                            let r1 = this.isEqualAttr(selectedAttr, item.selectedAttr, true);
                            let r2 = this.isEqualAttr(nsAttr, item.nsAttr, false);
                            return r1 && r2;
                        })
                        repeat = res;
                    }
                    return repeat;
                },
                isEqualAttr(new_attr, old_attr, isStandardAttribute) {
                    if (!new_attr && !old_attr) return true;
                    if (!new_attr || !old_attr) return false;
                    
                    const newKeys = Object.keys(new_attr);
                    const oldKeys = Object.keys(old_attr);
                    if (newKeys.length !== oldKeys.length) return false;
                    // 比较每个key对应的value_ids[0]是否相同
                    for (const key of newKeys) {
                        // 检查key是否存在于两个对象中
                        if (!old_attr.hasOwnProperty(key)) return false;
                        if (isStandardAttribute) {
                            // 检查value_ids是否存在且是数组
                            if (!Array.isArray(new_attr[key].value_ids) || !Array.isArray(old_attr[key].value_ids)) return false;
                            // 比较value_ids[0]的值
                            if (new_attr[key].value_ids[0].id !== old_attr[key].value_ids[0].id) return false;
                        } else {
                            // 检查value的值
                            if (new_attr[key].value !== old_attr[key].value) return false;
                        }
                    }
                    return true;
                },
                triggerShowMore(item, idx) {
                    item.__shoppingCartShow = !item.__shoppingCartShow;
                },
                parseAttr({selectedAttr = {}, nsAttr = {}}) {
                    let attr = [];
                    Object.keys(selectedAttr).forEach(key => {
                        attr.push(`${selectedAttr[key].name}: ${selectedAttr[key].value_ids[0].name}`);
                    });
                    Object.keys(nsAttr).forEach(key => {
                        attr.push(`${nsAttr[key].name}: ${nsAttr[key].value}`);
                    });
                    return attr.join(' | ');
                },
                formatPrice(price) {
                    return CRM.util.formatMoneyForCurrency(price, this._mcCurrency);
                },
                async changeCount(val, item, idx) {
                    if (val <= 0) {
                        this.deleteItem(item, idx).catch(action => {
                            this.$set(item, '_selfQuantity', 1);
                            this.changeCount(1, item, idx);
                        });
                        return;
                    }
                    this.loading = true;
                    // 取价，计算总价
                    if(opt.isOpenTieredPrice() && !CRM.util.isBom(item).isPackage){
                        let [newPrice, mcCurrency, result] = await opt.getRealPrice(item);
                        // this._mcCurrency = mcCurrency;
                        item._selfPrice = newPrice;
                    }
                    this.loading = false;
                    this.$emit('handleChangeCount', val, item, idx, this.dataList);
                },
                deleteItem(item) {
                    let index = this.dataList.findIndex(i => i.__shoppingCartUuid === item.__shoppingCartUuid);
                    if (index === -1) return;
                    return this.$confirm(
                        $t('crm.sfa.shopping_cart.confirm_delete', null, '确定删除该产品吗？'),
                        $t('提示'),
                        {
                            confirmButtonText: $t('确定'),
                            cancelButtonText: $t('取消'),
                            type:'info'
                        }
                    ).then(() => {
                        this.dataList.splice(index, 1);
                        this.$emit('handleDeleteAfter', item, index, this.dataList);
                    });
                },
                clearShoppingCart() {
                    this.$confirm(
                        $t('crm.sfa.shopping_cart.confirm_clear', null, '确定清空所有产品吗？'),
                        $t('提示'),
                        {
                            confirmButtonText: $t('确定'),
                            cancelButtonText: $t('取消'),
                            type:'info'
                        }
                    ).then(() => {
                        this.dataList = [];
                        this.$emit('handleClear');
                    });
                },
                confirm: _.debounce(function() {
                    this.$emit('submit', this.dataList);
                }, 300),
                submitDialog: _.debounce(function() {
                    this.$emit('submit', this.dataList);
                }, 300),
                closeDialog() {
                    this.dialogVisible = false;
                }
            }
        });
    };
    return ShoppingCart;
});
