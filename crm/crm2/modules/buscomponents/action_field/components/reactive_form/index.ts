/**
 * 根据列数据生成动态表格
 */
interface columnFace {
	api_name: string;
	type: string;
	label?: string;
	tip?: string;
	options?: Array<any>;
	defaultValue?: any;
	children?: {
		[value: string]: Array<columnFace>;
	};
	extraApis?: Array<string>;  //级联组件使用，扩展查看更多
	extraOpts?: {
		api_name: string;
		apiname: string;
		filters: Array<{
			field_name: string;
			field_values: Array<any>;
			operator: string;
		}>
	}
}

interface dataFace {
	[api_name: string]: any;
	rowId?: string;
}

import { FormulaDialog } from '../formula/index';
export class ReactiveForm {
	public widgets: {
		[rowId: string]: any;
	} = {};
	public formulaDialog:any = null;
	public $formContent: any;
	
	constructor(
		public $el: any,
		public columns: Array<columnFace>,
		public data: Array<dataFace> = [],
		public isSingle: boolean = false,
		public limitNum: number = 5,
		public style?: any,
		public size?: string,
		public isAndMode: boolean = false
	) {
		this.init();
	}

	init() {
		this.initDom();
		this.render();
		this.bindEvents();
	}
	initDom() {
		const me = this,
			flatModeClassName = this.isAndMode ? "" : "flat-mode",
			$btnDom = this.isSingle
				? ""
				: `<div class="crm-reactive-form-operation"><div class="form-operation-line"></div><span class="form-btn j-add" ><i class="el-icon-circle-plus"></i>${$t("添加")}</span></div>`;
		this.$el.append(`
		<div class="crm-reactive-form-wrapper ${flatModeClassName}">
			<div class="crm-reactive-form-title">
			${$t("且（AND）")}
			</div>
			<div class="crm-reactive-form">
            	<div class="crm-reactive-form-title"></div>
            	<div class="crm-reactive-form-content"></div>
            	${$btnDom}
        	</div>
		</div>`);

		this.$formContent = this.$el.find(".crm-reactive-form-content");
	}

	render() {
		const fragment = document.createDocumentFragment();
		if (this.data.length <= 0) {
			this.data.push(this.getDefaultValue());
		} 
		this.data.forEach((data) => {
			data.rowId = data.rowId || CRM.util.uniqueCode();
			const row = this.createFormGroup(data,false);
			fragment.appendChild(row[0]);
		});
		this.$formContent.append(fragment);
		this.checkAddBtn();
		this.checkRemoveBtn();
	}
	//绑定添加行和删除行事件
	bindEvents() {
		const me = this;
		this.$el.delegate(".j-add", "click", function () {
			me.addFormGroup();
		});
		this.$el.delegate(".j-remove", "click", function (e) {
			const rowId = $(e.target).parents(".form-row").attr("data-id");
			me.removeFormGroup(rowId);
			me.hideError();
		});
	}
	//获取一行默认值数据
	getDefaultValue() {
		let item: any = {};
		this.columns.forEach((col) => {
			item[col.api_name] = col.defaultValue || "";
			if (col.children) {
				let childColumns = col.children[col.defaultValue];
				childColumns?.forEach(c => {
					item[c.api_name] = c.defaultValue || "";
				})
			}
		});
		item.rowId = CRM.util.uniqueCode();
		return item;
	}
	//添加一行
	addFormGroup() {
		const item = this.getDefaultValue();
		this.data.push(item);
		this.createFormGroup(item);
		this.checkAddBtn();
		this.checkRemoveBtn();
	}
	//删除行
	removeFormGroup(rowId: string) {
		this.$el.find(`.form-row[data-id=${rowId}]`).remove();
		const index = this.data.findIndex((d) => d.rowId == rowId);
		if (index !== -1) this.data.splice(index, 1);
		this.checkAddBtn();
		this.checkRemoveBtn();
	}
	//控制删除按钮是否显示

	checkRemoveBtn() {
		this.$el.find('.row-btn').toggle(this.data.length > 1);
	}

	checkAddBtn() {
		this.$el.find('.crm-reactive-form-operation').toggle(this.data.length < this.limitNum);
	}

	//生成一行表单
	createFormGroup(data: dataFace, appendToDom = true) {
		const $row = $(`
			<div class="form-row" data-id=${data.rowId}>
				<div class="form-row-line"></div>
				<div class="form-row-box"></div>
			</div>
		`);
		const $box = $row.find('.form-row-box');
		this.generateComp($box, this.columns, data);
		$box.append(
			`<div class="row-btn j-remove"><i class="el-icon-delete"></i></div>`
		);
		if (appendToDom) {
			this.$formContent.append($row); // 将新行添加到 DOM 中
		}
		return $row; 
	}
	//生成行内组件方法
	generateComp($row: any, columns: Array<columnFace>, data: dataFace) {
		const rowId = data.rowId;
		columns.forEach((col) => {
			$row.append(
				`<div class="row-item row-item-${col.api_name}"></div>`
			);
			const wrapper = $row.find(`.row-item-${col.api_name}`);
			this.widgets[rowId] = this.widgets[rowId] || {};
			this.widgets[rowId][col.api_name] = this.renderComponent(wrapper,col,data);
			this.generateChildrenComp($row, col, data);
		});
	}

	renderComponent(wrapper, col, data) {
		const compType = `${col.type}CompRender`;
		if (typeof this[compType] === 'function') {
			return this[compType](wrapper, col, data);
		} else {
			console.log(`Component type ${col.type} not found`);
			return null;
		}
	}

	//生成级联子组件，并根据父元素值=>展示/隐藏
	generateChildrenComp($row: any, field: columnFace, data: dataFace) {
		if (!field.children || Object.keys(field.children).length === 0) return;
		Object.entries(field.children).forEach(([key, columns]) => {
			$row.append(`<div class="row-group row-group-${key}"></div>`);
			const $group = $row.find(`.row-group-${key}`);
			this.generateComp($group, columns, data);
			if (data[field.api_name] !== key) {
				$group.hide();
			}
		});
	}
	/*******************************************************
	 ************************* 组件 ************************
	 *******************************************************
	 */
	//单选组件
	selectCompRender(el, field, data) {
		const me = this;
		let selectOptions = field.options;
		if (field.api_name == "operator") {
			let operatorOpts = field.calculateOps?field.calculateOps(data):field.options;
			selectOptions = this.getOperateOptions(operatorOpts);
		}
		return FxUI.create({
			wrapper: el[0],
			template: ` <fx-select
                v-model="value"
				placeholder=${$t("请选择")}
                :el-style="selectStyle"
                :size="size"
                :options="options"
                @change="onChange" ></fx-select>`,
			data() {
				return {
					options: selectOptions,
					value: data[field.api_name] || field.defaultValue,
					selectStyle: {
						width: "150px",
					},
					size: "small",
				};
			},
			methods: {
				onChange(value) {
					me.formValueChange(value, field, data, selectOptions);
					me.displayChildComp(value, field, el);
				},
				getOptions() { },
				setOptions(options) { 
					this.options = options;
				},
				
			},
		});
	}
	//及联单选组件
	cascadeCompRender(el, field, data) {
		const me = this,
			compData = data[field.api_name] || {
				object_api_name: "",
				field_name: "",
			},
			formatCompData = me.formateValue(compData, field, false),
			fieldOptions = this.getFieldOptions(field);
		return FxUI.create({
			wrapper: el[0],
			template: ` <fx-cascader
                            v-model="value"   
							placeholder=${$t("请选择")}
                            :options="options"
                            :el-style="selectStyle"
                            :size="size"
							:disabled="disabled"
							:z-index="zIndex"
							popper-class="filter-cascader-popper"
							filterable
							:props="props"
                            @change="onChange" 
                        >
						<template slot="extra" slot-scope="{nodes}">
							<div class="el-cascader-extra" v-if="nodes.length && nodes[0].data.hasExtra"><a href="javascript:;" @click="onClick($event, nodes)">{{extra.text || $t("查看全部")}}</a></div>
						</template>
						</fx-cascader>`,
			data() {
				return {
					options: fieldOptions,
					value: [formatCompData.object_api_name, formatCompData.field_name],
					selectStyle: {
						width: field.width || "150px",
					},
					size: "small",
					disabled: false,
					extra: field.extraApis && field.extraApis.length >= 1,
					extraOpts: field.extraOpts,
					props: {},
					zIndex: Math.max(FxUI.Utils.getPopupZIndex(), CRM.util.getzIndex()) + 2
				};
			},
			methods: {
				onChange(value) {
					const parsedValue = me.formateValue(value, field);
					me.formValueChange(parsedValue, field, data);
					me.displayChildComp(value, field, el);
				},
				onClick(e, nodes) {
					if (field.extraClickFunc && _.isFunction(field.extraClickFunc)) {
						field.extraClickFunc(e, nodes, this);
						return;
					}
					me.onClick && me.onClick(e, nodes, this);
				},
				changeValue(val) {
					this.value = val;
					this.onChange(val);
				},
				updateOptions(path, item, options) {
					let op = options;

					_.each(path, (v, index) => {
						op = getList(op, v);
						if (index == path.length - 1) {
							op && op.unshift(item);
						}
					})
					function getList(op, v) {
						filterPlacer(op);
						let lv = op;
						let _lv = _.findWhere(op, { value: v });
						// 如果不存在children同时能找到节点_lv,则返回undefined，即无需往里添加节点
						_lv && (lv = _lv.children);
						return lv;
					}

					// 过滤掉占位的option
					function filterPlacer(op) {
						let pIdx = -1;
						_.each(op, (o, index) => {
							o.isPlaceholders && (pIdx = index);
						})
						pIdx > -1 && op.splice(pIdx, 1);
					}
				},


			},
		});
	}
	//数值类型Input组件
	numInputCompRender(el, field, data) {
		const me = this;
		return FxUI.create({
			wrapper: el[0],
			template: ` <fx-input 	
							v-model="input" 
							placeholder="${$t("请输入内容")}" 
							:type="type"
                            :el-style="selectStyle"
							@change="onChange"
 							:size="size"
							v-bind="props" 
 							>
							<span v-if='type=="percent"' class="el-input__icon" slot="suffix" style="padding-right:8px">%</span>
							</fx-input>`,
			data() {
				return {
					input: parseInt(data[field.api_name]) >= 0 ? data[field.api_name] : field.defaultValue,
					selectStyle: {
						width: field.width || "150px",
					},
					props: {
						'decimal-places': 4,
						maxlength: 14,
						...(field.props || {}),
					},
					size: "small",
					type:(field.extraOpts?.getInputType?.(field, data, me.columns)) ?? field.inputType ?? "number"
				};
			},
			
			methods: {
				onChange(value) {
					me.formValueChange(value, field, data);
					this.changeValueByType(this.type)
				},
				updateValue(newValue,data) {
					this.input = newValue;
				},
				setType(value){
					this.type = value;
					this.changeValueByType(value);
				},
				changeValueByType(valType){
					const val = data[field.api_name];
					if(val){
						me.updateData({
							[`${field.api_name}__s`] :valType === "percent" ? `${val}%` : val
						},data.rowId,false);
					}
					
				}
			},
		});
	}
	//固定文本
	textCompRender(el, field) {
		if (field.tip) {
			FxUI.create({
				wrapper: el[0],
				template: ` <div>
								<fx-tooltip class="item" effect="dark" :content="tipTxt" placement="top">
									<i class="el-icon-question"></i>
		  						</fx-tooltip>
								{{labelTxt}}
							</div>`,
				data() {
					return {
						tipTxt: field.tip,
						labelTxt: field.label
					};
				},
			});
		} else {
			el.append(field.label);
		}
	}
	// 高级公式组件
	formulaCompRender(el, field, data) {
		const me = this;
		return FxUI.create({
			wrapper: el[0],
			template: `<div @click="onClick">
                            <fx-input
                                width="200px"
                                :value="valueStr"
                                :placeholder="placeholder"
                                size="small"
                                readonly
                            />
                        </div>`,
			data() {
				return {
					valueStr: data?.[`${field.api_name}__s`]||"",
					placeholder: ""
				};
			},
			methods: {
				onClick() {
					// 实例化弹框
					if (me.formulaDialog) {
						me.formulaDialog.off("confirm"); 
						me.formulaDialog.destroy(); 
						me.formulaDialog = null;
					}
					me.formulaDialog = new FormulaDialog(
						el[0],
						field.extraOpts,
						{
							expression:data[field.api_name]||""
						},
						[]
					);

					// 监听弹框的 confirm 事件
					me.formulaDialog.on("confirm", ({ value, valueText }) => {
						const valType = me.getInputType(field, data, me.columns);
						valueText += (valType=="percent"?"%":"");
						this.valueStr = valueText; // 将选中的值设置为当前组件的值
						me.formValueChange({
							...value,
							valueText
						}, field, data);
					});
					me.formulaDialog.init();
				},
				updateValue(value) {
					this.valueStr = value;
				}
			},
		});

	}

	// 自定义组件
	customCompRender(el, field, data) {
		if (field.component && _.isFunction(field.component)) {
			return field.component(el[0], field, data);
		}
		return null;
	}
	//运算符选项
	getOperateOptions(options) {
		let optionsMap = [
			["EQUAL", $t("等于")],
			["ADD", $t("加")],
			["SUBTRACT", $t("减")],
			["MULTIPLY", $t("乘以")],
			["DIVIDE", $t("除以")],
		];
		if (options) {
			optionsMap = optionsMap.filter(o => options.includes(o[0]))
		}
		return optionsMap.map((o) => ({
			value: o[0],
			label: o[1],
		}));
	}
	/*******************************************************
	 ************************ 组件方法 ***********************
	 *******************************************************
	 */
	// 格式化级联组件的查看更多
	getFieldOptions(field) {
		let { extraApis = [], options = [] } = field;
		if (extraApis && extraApis.length >= 1) {
			options.forEach(o => {
				if (extraApis.includes(o.api_name)) {
					o.children = o.children && o.children.length >= 1 ? o.children : [{
						label: "",
						value: "",
						leaf: true,
						disabled: true,
						isPlaceholders: true, //后续判断是否为站位option，有值之后可以去掉这个option
					}];
					o.children[0].hasExtra = true; //是否展示查看更多
				}
			})
		}
		return options;
	}

	//级联组件查看更多
	onClick(e, nodes, context) {
		let node = nodes[0];
		let apiname = node.path[0];
		require.async('crm-modules/components/pickselfobject/pickselfobject', (Comp) => {
			let pickComp = new Comp();
			pickComp.on('select', function (list, remList) {
				let _item = {

					describe_api_name: apiname,
					hasExtra: true,
					label: list.name,
					leaf: true,
					type: "number",
					value: list._id,
				}
				let _val = _.clone(node.path);
				_val.splice(1, 1, _item.value);
				context.updateOptions(_val, _item, context.options);
				context.changeValue(_val);
			});
			const extraOpts = _.isFunction(context.extraOpts) ? context.extraOpts(apiname, node) : (context.extraOpts || {});
			pickComp.render({
				apiname: apiname,
				api_name: apiname,
				zIndex: Math.max(FxUI.Utils.getPopupZIndex(), CRM.util.getzIndex()) + 10,
				...extraOpts
			});
		})
	}

	//子组件展示控制
	displayChildComp(value, field, $el) {
		if (field.children) {
			field.options.forEach((o) => {
				const $groupDom = $el.siblings(`.row-group-${o.value}`);
				let checkVal = o.value;
				if (Array.isArray(o.value)) {
					checkVal = o.value[o.value.length - 1];
				}
				$groupDom.toggle(value === checkVal);
			});
		}
	}
	//格式化及联选择器数据
	formateValue(value, field, saveParse = true) {
		const aggrTypeMap = {
			AggregateRuleObj: 'aggregate',
			AggregateRebateObj: 'rebate_range',
			AggregateGroupObj: "group"
		};
		if (saveParse) {
			// 保存数据处理
			let data, childData;
			const [objApiName, fieldValue] = value;
			(field.options || []).some(o => {
				if (o.value === objApiName) {
					return (o.children || []).some(c => {
						if (c.value === fieldValue) {
							data = o;
							childData = c;
							return true; // 终止循环
						}
						return false;
					});
				}
				return false;
			});
			return {
				object_api_name: objApiName === 'AggregateRebateObj' ? 'AggregateRuleObj' : objApiName,
				object_api_name__s: data?.label,
				field_name: fieldValue,
				field_name__s: childData?.label,
				field_name_type: aggrTypeMap[objApiName] || "field",
			}
		}
		// 默认值数据处理
		return {
			...value,
			object_api_name: Object.entries(aggrTypeMap).find((a) => a[1] === value.field_name_type)?.[0] || value.object_api_name
		}
	}

	//组件值change
	formValueChange(value, field, data, fieldOptions = null) {
		CRM.util.hideErrmsg(this.$el);
		this.beforeFormChange(value, field, data, this.widgets);
		data[field.api_name] = value;

		let uItem = {};
		if (field.children) {
			const children = field.children[value] || [];
			const curFormCol = ["rowId", ...this.columns.map(c => c.api_name), ...children.map(c => c.api_name)];
			uItem = this.data.find((d) => d.rowId == data.rowId);
			Object.keys(uItem).forEach(key => {
				if (key.endsWith('__s')) {
					return;
				}
				if (!curFormCol.includes(key)) {
					delete uItem[key];
					if (uItem.hasOwnProperty(`${key}__s`)) {
						delete uItem[`${key}__s`];
					}
					this.updateWidgetComp(data.rowId,key,"");
				}
			});
		}

		uItem[field.api_name] = field.type === "formula" ? value.expression : value;

		//根据字段类型翻译__s
		if (field.type == "select") {
			let target = (fieldOptions || field.options || []).find(o => o.value == value);
			uItem[`${field.api_name}__s`] = target?.label || "";
		} else if (field.type == "formula") {
			uItem[`${field.api_name}__s`] = value.valueText;
		}
		
		this.updateData(uItem, data.rowId, field.children ? true : false);
		this.afterFormChange(value, field, data, this.widgets);
		this.hideError();
	}
	beforeFormChange(value, field, data,widgets) {

	}
	afterFormChange(value, field, data,widgets) {

	}
	//refresh:true-完全更新，false:覆盖更新
	updateData(value, rowId, refresh) {
		let idx = this.data.findIndex((d) => d.rowId == rowId);
		if (refresh) {
			this.data[idx] = value;
		} else {
			this.data[idx] = Object.assign(this.data[idx], value);
		}
	}

	updateWidgetComp(rowId,fieldApiName,value){
		if (!this.widgets || !this.widgets[rowId] || !this.widgets[rowId][fieldApiName]) {
			console.log(`Widget for rowId: ${rowId} and fieldApiName: ${fieldApiName} not found.`);
			return;
		}
		const widget = this.widgets[rowId][fieldApiName];
		if (typeof widget.updateValue === 'function') {
			widget.updateValue(value);
		}
	}

	getInputType(field,data,allColumns){
		if (!data.left) return "number";
		const leftColumn = allColumns.find(c => c.api_name === "left");
		const item = leftColumn?.options?.find(o => o.value === data.left);
		return item?.type === "percentile" ? "percent" : "number";
	}

	/*******************************************************
	 ************************ 表单方法 ***********************
	 *******************************************************
	 */
	hideError() {
		this.$el.find('.row-item').removeClass('crm-error-wrap')
		CRM.util.hideErrmsg(this.$el);
	}
	getField(apiname, columns) {
		let i = 0,
			field = null;
		while (!field && i < columns.length) {
			let col = columns[i];
			if (col.api_name == apiname) {
				field = col;
			} else if (col.children) {
				for (let key in col.children) {
					field = this.getField(apiname, col.children[key]);
					if (field) {
						return field;
					}
				}
			}
			i++;
		}
		return field;
	}
	collect() {
		const data = this.data.map((d) => {
			let item = {};
			Object.keys(d).forEach((key) => {
				if (key == "rowId") {
					item[key] = d[key];
					return;
				}
				let field = this.getField(key, this.columns);
				if (field && field.type !== "text") {
					item[key] = d[key];
					if (d.hasOwnProperty(`${key}__s`)) {
						item[`${key}__s`] = d[`${key}__s`] || "";
					}
				}
			});
			return item;
		});
		return data;
	}
	getValue() {
		const data = this.collect();
		if (!this.validFormValue(data)) {
			CRM.util.showErrmsg(this.$el, $t('请填写完整'));
		}
		return data;
	}

	//获取公式数据里指定类型值的id
	getFormulaIds(dataArr,type){
		if(!this.formulaDialog){
			return [];
		}
		return dataArr.reduce((accIds,item)=>{
			const itemIds = this.formulaDialog.getAggrIdsByType(item,type);
				(itemIds||[]).forEach(i=>{
					!accIds.includes(i) && accIds.push(i);
			})
			return accIds;
		},[])
	}
	validFormValue(data) {
		let count = 0,
			i = 0;
		data.forEach(item => {
			for (let key in item) {
				if (CRM.util.isEmptyValue(item[key])) {
					count += 1;
					this.$el.find(`.form-row[data-id=${item.rowId}]`).find(`.row-item-${key}`).addClass('crm-error-wrap')
				}
			}
		});

		return count <= 0;
	}

	destroy() {
		_.each(this.widgets, function (widget) {
			_.each(widget, (item) => {
				item?.off?.();
				item?.destroy?.();
				item = null;
			})
		});
		this.formulaDialog && this.formulaDialog.destroy();
	}
}
