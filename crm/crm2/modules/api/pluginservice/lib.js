/*
 * @Descripttion: 
 * @Author: LiAng
 * @Date: 2023-03-08 10:38:55
 * @LastEditors: LiAng
 * @LastEditTime: 2023-05-10 14:23:07
 */
define(function(require, exports, module) {
    return function(params) {
        let {instances, loggers, events} = params;

        return {
            /**
             * 通过appId获取pluginService实例
             *
             * @param {string} appId 
             * @returns 
             */
            getInstance(appId) {
                return instances[appId];
            },
    
            /**
             * 获取全部pluginService实例
             *
             * @returns 
             */
            getAllInstance() {
                return instances;
            },
    
            /**
             * 获取第一个pluginService实例
             *
             * @returns 
             */
            getFirstInstance() {
                return instances[Object.keys(instances)[0]];
            }
        }
    }
})