/**
 * @description aliyun上传文件模块
 * @return {Object}
 *
 */
 define(function (require, exports, module) {
    require('base-aliyun_oss');
    var OSS = window.OSS;
    var util = FS.util;
    var getParams = function (type, fileinfo, uploadAuthParams) {
      var data = {};
      switch (type) {
        case "upload"://第一次上传需要获取文件信息
          data = {
            "url": '/EM1HBFM/File/CreateFilePath', //获取文件
            "data": {
              "fileName": fileinfo.name,
              "fileSize": fileinfo.size,
              "appName": "paas"
            },
          }
          break;
        case "getUploadAuth":
          data = {
            "url": '/EM1HBFM/Ali/GetUploadAuthInfo', //获取上传授权信息
            "data": {
              "filePath": fileinfo._filePath,
              ...uploadAuthParams
            },
          }
          break;
        case "download":
          data = {
            "url": '/EM1HBFM/Ali/GetDownloadAuthInfo', //下载
            "data": {
              "filePath": fileinfo._filePath
            },
          }
          break;
        case "downloadWidthDuration":
          data = {
            "url": '/EM1HBFM/Ali/GetDownloadAuthInfoV2', //下载
            "data": {
              "filePath": fileinfo._filePath,
              "duration": fileinfo._duration
            },
          }
          break;
        case "abortUpload":
          data = {
            "url": '/EM1HBFM/Ali/AbortUploadAuthInfo', //取消
            "data": {
              "filePath": fileinfo._filePath
            },
          }
          break;
        default:
  
      };
      return data;
    };
    var getOssClientPromise = function (type, fileinfo, uploadAuthParams) {
      var data = getParams(type, fileinfo, uploadAuthParams);
  
      var clientPromise = $.Deferred();
      var successCb = function (d) {
        if (d.Result && d.Result.StatusCode == 0) {
          if (type == "upload") {//为了兼容后端的完美设计，初次上传前端需要发两次请求。有问题咨询rd@woshihui
            fileinfo._filePath = d.Value.filePath;
            var getUploadAuthParams = getParams("getUploadAuth", fileinfo, uploadAuthParams);
            util.FHHApi($.extend({}, {
              "type": 'post',
              "contentType": "application/json; charset=utf-8",
              "dataType": 'json',
              "success": function (authData) {
                if (authData.Result.StatusCode == 0) {
  
                  var options = {
                    region: authData.Value.region,
                    accessKeyId: authData.Value.accessKeyId,
                    accessKeySecret: authData.Value.accessKeySecret,
                    stsToken: authData.Value.stsToken,
                    bucket: authData.Value.bucket,
                    secure: location.protocol.toLowerCase() == "https:",
                  };
                  if (fileinfo.client) {
                    fileinfo.client.options = OSS.initOptions(options);
                  } else {
                    fileinfo.client = new OSS.Wrapper(options);
                  }
                  console.log(fileinfo.client);
                  fileinfo.client._fxdata = authData.Value;
                  fileinfo.client._fxdata.filePath = this.filePath;
                  fileinfo.client.createTime = (new Date()).getTime();
                  clientPromise.resolve(fileinfo.client);
                } else {
                  FxUI.Message({
                    isMiddler: true,
                    duration: 3000,
                    message: authData.Result.FailureMessage,
                    type: 'error'
                  })
                  clientPromise.reject(authData.Result.FailureMessage);
                }
              }.bind(d.Value),
              "error": function () {
                clientPromise.reject("error:" + data.url);
              }
            }, getUploadAuthParams));
          } else {
            var options = {
              region: d.Value.region,
              accessKeyId: d.Value.accessKeyId,
              accessKeySecret: d.Value.accessKeySecret,
              stsToken: d.Value.stsToken,
              bucket: d.Value.bucket,
              secure: location.protocol.toLowerCase() == "https:"
            };
            fileinfo.client = new OSS.Wrapper(options);
            fileinfo.client.createTime = (new Date()).getTime();
            fileinfo.client._fxdata = d.Value;
            fileinfo.client._fxdata.filePath = d.Value.filePath;
            clientPromise.resolve(fileinfo.client);
          }
        } else {
          //util.alert(d.Result.FailureMessage);
          clientPromise.reject(d);
        }
      }
      util.FHHApi($.extend({}, {
        "type": 'post',
        "contentType": "application/json; charset=utf-8",
        "dataType": 'json',
        "success": successCb,
        "error": function () {
          clientPromise.reject("error:" + data.url);
        }
      }, data), {
        errorAlertModel: 1
      });
      return clientPromise.promise();
    };
    var uploadFile = function (file, progressCb, successCb, failCb, uploadAuthParams) {
      file.checkpoint = file.checkpoint || null;
      file._isAborted = false;
      var clientPromise;
      if (!file._filePath || file._filePath == "") {
        clientPromise = getOssClientPromise("upload", file, uploadAuthParams);
      } else {//曾经获取过filePath
        clientPromise = getOssClientPromise("getUploadAuth", file, uploadAuthParams);
      }
      clientPromise.done(function (client) {
        file.client = client;
        //file._filePath = file._filePath ||client._fxdata.filePath ;//第一次获取,需要将_filePath存储起来
        file._objectKey = file._objectKey || client._fxdata.objectKey;//取第一次获取的_filePath
        file._uploader = client.multipartUpload(client._fxdata.objectKey, file, {
          partSize: (window.partSize || 1) * 1024 * 1024,//fixme for test
          parallel: window.parallel || 1,//fixme for test
          checkpoint: file.checkpoint,
          headers: {
            'x-oss-callback': client._fxdata.callback,
            'x-oss-callback-var': client._fxdata.callbackVar,
          },
          progress: function (percentage, cpt) {
            return function (done) {
              console.log("------file._isRemoved,file._isAborted", arguments, cpt, file._isRemoved, file._isAborted, percentage);
              file.checkpoint = cpt;
              if (!client.isexpiration) {//没有过期
                if (!file._isRemoved && !file._isAborted) {//没有删除 没有暂停
                  progressCb(percentage);
                  if (((new Date()).getTime() - client.createTime) > client._fxdata.expirationSeconds * 1000 * 0.6) {
                    client.isexpiration = true;
                    uploadFile(file, progressCb, successCb, failCb);
                  } else {
                    done();
                  }
                }
              }
            }
          }
        }).then(function (res) {
          console.log('upload success:', res);
          if (res.code == "NoSuchUpload") {
            file.checkpoint = null;
          }
          successCb(res);
        }).catch(function (res) {
          if ([200, 101, 100].indexOf(file._f_uploadStatus) >= 0) {
            return;
          }
          var err = {};
          if ((res.code == "EntityTooSmall" || res.code == "InvalidPart") && res.errinfo && res.errinfo.PartNumber) {
            file.checkpoint.doneParts = _.filter(file.checkpoint.doneParts, function (p) {
              return p.number != res.errinfo.PartNumber;
            });
            console.log('存在错误分片:', res);
            file.retrylog = file.retrylog || {};
            file.retrylog[res.errinfo.PartNumber] = file.retrylog[res.errinfo.PartNumber] || 0;
            if (file.retrylog[res.errinfo.PartNumber] < 3) {//单个分片只重试三次
              file.retrylog[res.errinfo.PartNumber]++;
              uploadFile(file, progressCb, successCb, failCb);
            } else {
              console.log('upload fail:', res);
  
              console.log("------------------------------", res);
              try {
                err.FailureMessage = $t("上传失败") + "：" + res.code + "-" + res.status;//OSSERR[res.code+res.status];
              } catch (e) {
  
              }
              failCb(err);
            }
          } else {
            console.log('upload fail:', res);
            console.log("------------------------------", res);
            try {
              err.FailureMessage = $t("上传失败") + "：" + res.code + "-" + res.status;//OSSERR[res.code+res.status];
            } catch (e) {
  
            }
            failCb(err);
          }
        });
      }).fail(function (res) {
        if ([200, 101, 100].indexOf(file._f_uploadStatus) >= 0) {
          return;
        }
        file._filePath = "";
        var err = {};
        try {
          err.FailureMessage = res.Result.FailureMessage;
        } catch (e) {
  
        }
        console.log('auth fail:', err, res);
        failCb(err);
      });
    };
    var downloadFile = function (filePath, filename, cb) {
      CRM.util.waiting(' ');
      getOssClientPromise("download", { _filePath: filePath }).done(function (client) {
        window.logger?.action({
          eventId: 'fx_filepreview_click_download',
          eventName: 'click',
          module: 'alioss',
          msg: filePath,
          str1: filename,
          str2: filePath
        });
        var result = client.signatureUrl(client._fxdata.objectKey, {
          response: {
            'content-disposition': 'attachment;filename="' + filename + '"'
          }
        });
        CRM.util.waiting(false);
        window.location.href = result;
      }).fail((res)=> {
        CRM.util.waiting(false);
        FxUI.Message({
          isMiddler: true,
          duration: 1500,
          message: res.Result.FailureMessage,
          type: 'error'
        })
      })
    };
    var removeUploadFile = function (file, cb) {
      if (file.checkpoint && file.checkpoint.uploadId) {
        file.client = null;
        var filePath = file._filePath, uploadId = file.checkpoint.uploadId;
        console.log("大文件取消file.client.abortMultipartUpload", filePath, uploadId);
  
        getOssClientPromise("abortUpload", { _filePath: filePath }).done(function (client) {
          var doAbortfail = false;
          var doAbort = function () {
            client.abortMultipartUpload(client._fxdata.objectKey, uploadId, {
              "Access-Control-Allow-Origin": "*"
            })
              .then(function (res) {
                if (cb) cb();
                file._isRemoved = true;
                console.log('取消成功 upload success:', res);
              }).catch(function (res) {
                doAbortfail = true;
                console.log('取消失败 abortUpload fail:', res);
              })
          }
          doAbort();
        });
      } else {//只有开始上传后才能取消
        setTimeout(function () {
          abortUploadFile(file, cb);
        }, 100);
      }
    };
    var abortUploadFile = function (file, cb) {
      file._isAborted = true;
    };
    var getDownLoadPath = function (filePath, filename, duration, sucCb, failCb) {
      getOssClientPromise("downloadWidthDuration", { 
        _filePath: filePath,
        _duration: duration
      }).done(function (client) {
        var result = client.signatureUrl(client._fxdata.objectKey, {
          response: {
            'content-disposition': 'attachment;filename="' + filename + '"'
          }
        });
        sucCb && sucCb(result);
      }).fail((res)=> {
        failCb && failCb();
      })
    };

    return function(param) {
        if(CRM.util.getUserAttribute('uploadByYun')) {
          switch(param.__name) {
            case 'alioss_upluoad':
                param.file.uploadObj = Fx.file.upload(param.file, {
                  bigFileSize: -1,
                  uploadAuthParams: param.uploadAuthParams || {},
                  success: (data) => {
                    param.successCb && param.successCb({data});
                  },
                  progress: (progress) => {
                    if(param.file.progress && param.file.progress > progress) {
                      //暂停之后继续上传，progress会瞬间变为0,体验不好，进度用上一次的
                      if(param.file.progress > 98) {
                        progress = 99;
                      } else {
                        progress = ++param.file.progress
                      }
                    } else {
                      param.file.progress = progress;
                    }
                    param.progressCb && param.progressCb(progress/100);
                  },
                  fail: (res) => {
                    res !== 0 && param.failCb && param.failCb();
                  }
                })
                break;
            case 'alioss_download':
                FS.file.downloadByYun({filePath: param.path, fileName: param.fileName});
                break;
            case 'alioss_getdownloadpath':
                FS.file.downloadByYun({
                  filePath: param.path,
                  success(url) {
                    param.successCb(url);
                  },
                  fail(res) {
                    param.failCb && param.failCb(res);
                  }
                });
                break;
            case 'alioss_abort':
                param.file.uploadObj.abort();
                break;
            case 'alioss_remove':
                delete param.file.progress;
                param.file.uploadObj.remove();
                delete param.file.progress;
                delete param.file.uploadObj;
                break;
          }
          return;
        }


        switch(param.__name) {
            case 'alioss_upluoad':
                uploadFile(param.file, param.progressCb, param.successCb, param.failCb, param.uploadAuthParams);
                break;
            case 'alioss_download':
                downloadFile(param.path, param.fileName, param.successCb);
                break;
            case 'alioss_getdownloadpath':
                getDownLoadPath(param.path, param.fileName, param.duration, param.successCb, param.failCb);
                break;
            case 'alioss_abort':
                abortUploadFile(param.file, param.successCb);
                break;
            case 'alioss_remove':
                removeUploadFile(param.file, param.successCb);
        }
    }
})
  