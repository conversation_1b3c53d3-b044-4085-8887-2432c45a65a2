<ul class="smartform-fieldList"> 
  ##if(obj.length){##
  	## _.each(obj, function(o){ ##
    ## var group_type = o.group_type; ##
    <li class="smartform-field ##if(o.is_required || o.is_show){##disabled##}## "
        data-apiname="{{o.api_name}}"
        data-type="{{o.type}}"
        ##if(group_type){##data-group_type={{group_type}} ##}##
        data-label="{{o.label}}"  
        ##if(o.is_preset_only || group_type === 'payment'){## data-ispresetonly="true" ##}## >
      {{{{-o.label}}}}
    </li>
  	## }) ##
  ##}else{##
		<div class="search-result">{{$t("未找到字段")}}</div>
	##}##
</ul>