define(function (require, exports, module) {
	var util = FS.crmUtil;
	var waiting = util.waiting;

	// 互联业务化逻辑代码，被分离到另一个项目去了，icmanage-modules/crmobj-actions/simply-icmanage
	// 需要在定义好 fns 之后异步加载上述模块，注意时间差和加载失败的情况
	let simplyIcmanage = null;
	const breakReturn = 'bret'; // 互联模块接手按钮 action 后，以此标识不再执行通用逻辑

	//////////////////// 历史队形功能同步(未标准化，标准化后删除) ////////////////////
	var NSButtons = {
		AsyncBulkChangeOwner: 'change_owner',
		AsyncBulkInvalid: 'abolish',
		AsyncBulkAddTeamMember: 'add_sales_team_advanced',
		AsyncBulkDelete: 'delete',
		AsyncBulkDeleteOrderPaymentObj: 'delete_async',
		AsyncBulkDeleteTeamMember: 'del_sales_team', //'del_sales_team_advanced',
		AsyncBulkLock: 'lock',
		AsyncBulkUnlock: 'unlock',
		AsyncBulkTransform: 'batchTransform',
		SendMail: 'mail',
		AsyncBulkSendMail: 'mail',
		Merge: 'merge_object', //联系人合并
		MergeUI:'merge_object',
		AsyncBulkRenewExpiration:'ren_expiration',
		ChangeSaleAction: 'change_sales_flow', //商机更换流程
		Export: 'export_selected',
		ExportFile: 'export_file_selected',
		SP_TEMP_SETTLE_TYPE: 'change_settle', // 客户账户修改结算方式
		AsyncBulkChangePartner: 'change_partner',
		AsyncBulkChangePartnerOwner: 'change_partner_owner',
		AsyncBulkDeletePartner: 'delete_partner',
		AccountObjAsyncBulkChoose: 'receive_accountobj',
		AccountObjAsyncBulkAllocate: 'assign_accountobj',
		AccountObjAsyncBulkMove: 'transfer_accountobj',
		AccountObjAsyncBulkReturn: 'return_accountobj',
		AccountObjAsyncBulkTakeBack: 'revoke_accountobj',
		AsyncBulkChoose: 'receive_leadsobj',
		AsyncBulkAllocate: 'assign_leadsobj',
		AsyncBulkMove: 'transfer_leadsobj',
		AsyncBulkReturn: 'return_leadsobj',
		AsyncBulkTakeBack: 'revoke_leadsobj',
		AsyncBulkCollect_To: 'collect_to_leadsobj',
		AsyncBulkMarkMQL: 'mark_mql_leadsobj',
		AsyncBulkFollowUp: 'follow_leadsobj',
		AsyncBulkClose: 'invalid_leadsobj',
		ProcessLeads_button_default: 'deal_leadsobj',  //列表行按钮 - 处理线索
		BulkDelete: 'delete', // 删除
		AddCampaignMembers: 'add_campaignmembers',
		AsyncBulkRemoveObject: 'remove_campaignmembers',
		AsyncBulkCancelEntry: 'cancelentry_button_default', // 批量取消入账
		AccountTransactionFlowObjCancelEntry_button_default: 'cancelentry_button_default', // 账户收支流水-取消入账
		PaymentObjCancelEntry_button_default: 'cancelentry_button_default', // 回款-取消入账
		FAccountAuthorizationObjEdit_button_default: 'edit', // 账户授权-编辑
		FAccountAuthorizationObjInit_button_default: 'account_authorization_init', // 账户授权-初始化

		Init_button_default:'init_button_default',//对账单初始化，重新初始化
		ConfigPrintTemplate_button_default:'configPrintTemplate_button_default', // 对账单配置打印模版
		ConfigBPM_button_default:'configBPM_button_default', // 对账单配置业务流程
		Edit_button_default: 'edit', // 对账单编辑
		CreatePartner_button_default: 'create_channel_merchant', // 创建渠道商家
		button_leishi_transorder__c: 'leishi_transorder', // 雷士照明转单demo
		button_jxs_transorder__c: 'jxs_transorder', // 经销商转单demo
		AccountAddrObjSetDefault_button_default: 'setdefault_address',
		AccountAddrObjSetMain_button_default: 'setmain_address',
		AccountFinInfoObjSetDefault_button_default: 'setdefault_financial',
		AsyncBulkAllocateMainData: 'distribute_org',  //分发到组织
		PayInstantly_button_default: 'PayInstantly_button_default', // 立即回款按钮
		ReturnedGoodsInvoiceObjRefund_button_default: 'Refund_button_default', // 退货单立即退款按钮
		BuyAgain_button_default: 'BuyAgain_button_default', //再次下单按钮
		ConfirmReceipt2_button_default: 'ConfirmReceipt2_button_default', //确认收货2按钮
		BulkHangTag: 'batch_tag',
		BalanceReduce_button_default: 'BalanceReduce_button_default',  //余额支付按钮
		button_e_call_out_1__c: 'call_out', // 外呼
		button_e_call_out__c: 'call_out', // 外呼
		button_e_work_phone_call_out__c: 'work_phone_call_out', // 工作手机外呼
		BudgetClosure_button_default: 'BudgetClosure',  //TPM 结案  （单条时 走的 button_apiname）
		BudgetClosure: 'BudgetClosure',  //TPM 结案（批量时 走的button_action）
		TPMActivityProofObjProofAudit_button_default: 'ProofAudit',   //TPM 活动检核按钮
		TPMActivityProofObjProofCheck_button_default: 'ProofCheck',   //TPM 查看活动检核按钮
		tpm_superior_audit__c: 'TPMSuperiorAudit',    //TPM 1+n穿透检核
		TPMActivityAgreementObjActivityProof_button_default: 'AgreementObjGotoActivityProof',    //从活动协议进行活动举证
		CloseActivityAgreement_button_default: 'onTPMTerminationOfAgreement',  //终止协议
		tpm_review_write_off__c: 'onTPMReviewWriteOff',  //TPM 复核核销按钮
		TaskPosterCheck_button_default: 'onTaskPosterCheck',  //查看任务海报按钮
		GoodsDelivery_button_default: 'onGoodsDeliveryButtonDefault',
		FaultKnowledgeRecommend_button_default: 'showKnowledgeRecommend',
		WechatGroupObjGroupInherit_button_default: 'inheritance_wechatObj', // 群主继承
		WechatGroupObjAsyncBulkGroupInherit: 'inheritance_wechatObj', // 群主继承
		WechatGroupObjGroupInherit: 'inheritance_wechatObj', // 群主继承
		WechatFriendsRecordObjFriendInherit_button_default: 'inheritance_wechatObj', // 好友继承
		WechatFriendsRecordObjAsyncBulkFriendInherit: 'inheritance_wechatObj', // 好友继承
		WechatFriendsRecordObjFriendInherit: 'inheritance_wechatObj', // 好友继承
		BankStatementObjIdentifyAccount_button_default: 'IdentifyAccount_button_default', // 银行流水--单条识别客户
		BankStatementObjAsyncBulkIdentifyAccount: 'BankStatementObjAsyncBulkIdentifyAccount', // 银行流水-批量识别客户
		SettlementObjAsyncBulkSettlementAR: 'SettlementObjAsyncBulkSettlementAR',
		SyncToPriceBook_button_default: 'SyncToPriceBook_button_default', // 同步价目表按钮
		AsyncBulkTransferSession: 'async_bulk_transfer_session', //批量转接会话
		TransferOrder_button_default: 'TransferOrder_button_default', // 一键转单
		VirtualStockIn_button_default: 'VirtualStockIn_button_default', // 虚拟入库
		// AsyncBulkEndSession: 'async_bulk_end_session' //批量结束会话
		Renew_button_default:'ren_ew',

	}

	var fns = {
		replyMail: function(params) {
			seajs.use(
				"crm-modules/page/mailobj/mailobj",
				(mailobj) => {
					seajs.use(
						"crm-modules/page/mailobj/utils",
						(utils) => {
								utils.replyMail({
									id: params.id,
									replyType: params.replyType
								});
						}
					);	
				}
			)
		},
		showChatHistory: function(params) {
			seajs.use(
				"crm-modules/page/customerservicesessionobj/utils/utils",
				(utils) => {
						utils.handleViewChatRecord({
								_id: params.id,
						});
				}
			);
		},
		work_phone_call_out: function(params) {
			return fns.call_out(params, 'workPhone');
		},
		call_out: function(params = {}, dialoutType) {
			const success = params.success || function () {};
			const error = params.error || function () {};
			
			require.async('vcrm/sdk', (SDK) => {
				SDK.getComponent('callOutPanel')
					.then(({default: CallOutPanel}) => {
						return CallOutPanel.$show({
							detailData: params.data,
							apiName: params.apiname || params.apiName,
							successTips: params.successTips,
							needSuccessTip: params.needSuccessTip,
							dialoutType
						})
					})
					.then(success, error)
			})
		},
		showKnowledgeRecommend(param) {
			const getKnowledgeDialog = () => new Promise((resolve) => {
				require.async('app-workorder/app.js', () => {
					require.async('app-workorder/assets/style/all.css');
					require.async('app-workorder/components/faulttree/knowledge-dialog/knowledge-dialog-vue', resolve)
				})
			})
			const getKnowledgeIdList = () => new Promise(resolve => {
				FS.util.FHHApi({
					url: '/EM1HESERVICE2/eservice/knowledge/queryFaultRecordKnowledgeList',
					data: {
						serviceFaultRecordId: param.data._id
					},
				}).then((res = {}) => {
					const { knowledgeIdList = [] } = res.Value.data || {}
					resolve(knowledgeIdList)
				})
			})
			Promise.all([getKnowledgeDialog(), getKnowledgeIdList()]).then(([KnowledgeDialog, knowledgeIdList]) => {
				KnowledgeDialog.$show({
					idList: knowledgeIdList
				})
			})
		},
		show_map: function (param) {
			waiting('');
			require.async('crm-modules/components/newmap/newmap', function (Map) {
				waiting(false);
				var mapView = new Map({
					type: 'full',
					infoWindow: {
						infoTpl: _.template('<div>{{{{-name}}}}</div>')
					}
				});
				mapView.render();
				mapView.once('map.complete', () => {
					mapView.addMarker([{
						customElements: '001',
						name: param.name,
						lon: param.lon,
						lat: param.lat
					}]);
				})
			});
		},

		print: function (param) {
			if (/runTimeIsElectron/i.test(navigator.userAgent) && window.sendIpcRendererMessage) {
				let params = `
							${encodeURIComponent(param['dataId'])}%26
							${encodeURIComponent(param['objectName'])}%26
							${util._escapeToUtf32(param['fileName'])}%26
							${encodeURIComponent(param['templateId'] || '')}
						`;
				window.sendIpcRendererMessage('desktop.crm.print', 'crm/print/=/' + params);
			}

			else {
				waiting();
				require.async('paas-template/sdk', function (Template) {
					waiting(false);
					Template.setzIndex && Template.setzIndex(CRM.util.getzIndex(1000));
					new Template.Preview(param);
				})
			}
		},

		show_guide: function (param) {
			var key = param.key || ('guide_page_' + param.apiname);
			if (CRM.getConfig(key)) return;
			waiting();
			require.async('base-modules/guide/guide', function (Action) {
				waiting(false);
				var widget = new Action(_.extend({
					type: param.show_type || 'dialog',
					key: key,
					title: param.displayName,
				}, param));
				widget.on('addobj', function () {
					param.success();
				});

				param.complete && param.complete(widget);
			})
		},

		enter_business_group: function (param) {
			if (FS.QX_MODULE.IS_NEW_WEB && window.QX) { //fix:新主站客群按钮点击后要隐藏详情页
				window.QX.openCrmChat({
					type: param.apiname,
					objectId: param.id,
					name: param.name,
					zIndex: param.zIndex
				}, param.success);
			} else {
				waiting();
				FS.MEDIATOR.trigger('qx.openCrmChat', {
					type: param.apiname,
					objectId: param.id,
					name: param.name,
					zIndex: param.zIndex
				});
				setTimeout(function () {
					waiting(false);
				}, 300);
			}
		},

		enter_connect_business_group: function (param) {
			waiting();
			var outerInfo = CRM.util.getConnectAppInfo() || {};
			var FS = window.FS;
			if(FS.util.getUserAttribute('paasxt_crosschat')) {
				if(top !== window && top.FS) {
					FS = top.FS;
				}
			}
			FS.MEDIATOR.trigger('qx.openCrossCrmChat', {
				type: param.apiname,
				objectId: param.id,
				name: param.name,
				zIndex: param.zIndex,
				upEa: outerInfo.upstreamEa,
				appId: outerInfo.appId,
				outTenantId: $.cookie('EROuterTenantId'),
				outUserId: $.cookie('EROuterUid')
			});
			setTimeout(function () {
				waiting(false);
			}, 300)
		},

		assign_casesobj: function () {
			CRM.control.navigate('#app/workorder/orderdispatch', {
				trigger: true
			});
		},

		ConfigureProduct: function (a, b) {
			this.configureproduct.apply(this, arguments);
		},

		configureproduct: function (a, b) {
			require.async('crm-modules/action/bom/bom', function (Action) {
				var obj = new Action();
				obj.renderBom({ productColumns: a.productColumns }, true);
			});
			// 点击列表页配置子产品埋点
			CRM.util.sendLog('BOMObj', 'list', {
				eventId: 'clickconfig'
			});
		},

		// 配置固定搭配
		ConfigureCollocation(params) {
			this.add.call(this, {
				...params,
				renderType: 'FixedCollocation',
			});
		},
		addFeed: function (obj, param) {
			var objData = obj.objData;
			if (!objData && obj.data && obj.data.relation_data) {
				objData = obj.data.relation_data;
			}
			// activity专项
			if (obj.__type__ === 'edit' && obj.apiname === 'ActiveRecordObj') {
				// 【销售记录】标准详情页编辑时控制title展示
				obj.isEdit = true;
				// 【销售记录】标准详情页编辑时控制保存接口
				obj.data.isEdit = true;
			}
			CRM.api.add_active_record(Object.assign({}, obj, {

				showDetail: true,
				apiName: obj.apiname,
				
				data: obj.data,
				objData: objData,
				oldDraftData: obj.oldDraftData,
				fullscreen: obj.fullscreen,
				methods: {
					success: function (activeRecordData) {
						let apiname =	activeRecordData.object_describe_api_name
						// activity专项改造，新增时统一详情页打开方式，show_crm_detail内有做处理
						if (obj.__type__ === 'add') {
							CRM.api.show_crm_detail({
								apiName: apiname,
								data: {
									crmId: activeRecordData._id,
								},
							});
						}
						// FS.MEDIATOR.trigger('fs.feed2019.slide', {//
						// 	url: 'FsFeed/getFeedByObjectId',
						// 	data: {
						// 		apiName: activeRecordData.object_describe_api_name,
						// 		dataId: activeRecordData._id
						// 	},
						// 	options: {
						// 		zIndex: CRM.util.getzIndex()
						// 	}
						// })
						FS && FS.MEDIATOR && FS.MEDIATOR.trigger && FS.MEDIATOR.trigger('crm.table.list.refresh', activeRecordData.object_describe_api_name);

						param && param.success && param.success('add', activeRecordData);
					}
				},
				entry: obj.entry||'simply.crm.api.add'
			}));
		},
		add: function (param) {
			// 打开独立站点详情落地页
			// if(window.auth && param.source !== 'custom_comp' && param.__type !== 'edit') {
			// 	Fx.getBizAction('paasbiz', 'openSitePage', {
			// 		pageType: 14,
			// 		objectApiName: param.apiname,
			// 	})	
			// 	return;
			// }

			// 没有传入recordTypeFilter, 且不是自定义菜单页
			if(!(param?.recordTypeFilter?.length > 0)  //没有传入recordTypeFilter
				&& !util.isCustommenuListPage() //不是自定义菜单页
				) { 
				//完整的 渠道订货预设对象, 下游何伟芳wiki中列的对象
				/* const isDistributionObj = ['AccountAddrObj', 'AvailableRangeObj', 'AccountObj', 'PriceBookAccountObj', 'ContactObj', 'RefundObj', 'InvoiceApplicationObj', 'SalesOrderObj', 'PaymentObj', 'PriceBookObj', 'PriceBookProductObj', 'WarehouseObj', 'StockObj', 'RequisitionNoteObj', 'StockCheckNoteObj', 'StockDetailsObj', 'BatchStockObj', 'DeliveryNoteObj', 'ReturnedGoodsInvoiceObj', 'GoodsReceivedNoteObj', 'OutboundDeliveryNoteObj'].includes(param.apiname); */
				
				//渠道订货预设对象, 原sfa处理过的对象
				const isDistributionObj = ['AccountAddrObj', 'AvailableRangeObj', 'AccountObj', 'RefundObj', 'InvoiceApplicationObj', 'SalesOrderObj', 'PaymentObj', 'PriceBookObj'].includes(param.apiname);

				if(isDistributionObj) {
					param.recordTypeFilter = ['distribution__c']; // 渠道订货预设对象默认过滤掉渠道业务类型
				}

			}
			
			waiting();
			function showErrorMsg(message, duration) {
				if(param.data?.customLayoutParam?.errorCallBack) {
					param.data.customLayoutParam.errorCallBack(message);
				} else {
					FxUI.Message({
						isMiddler: true,
						duration: duration || 1500,
						message: message,
						type: 'error'
					})
				}
			}
			function valid() {
				return new Promise(resolve => {
					if(_.contains(['list', 'custom_comp'], param.source) && param.record_type && param.apiname) {
						util.FHHApi({
							url: `/EM1HNCRM/API/v1/object/${param.apiname}/controller/ValidRecordType`,
							data: {
								describeApiName: param.apiname,
                    			is_only_active: true
							},
							success(res) {
								if(res.Result.StatusCode === 0) {
									if(!_.findWhere(res.Value.record_list, {api_name: param.record_type})) {
										showErrorMsg($t('crm.field.validrecordtype', null, $t('暂无权限')), 2000);
										resolve();
									} else {
										resolve(true);
									}
								} else {
									showErrorMsg(res.Result.FailureMessage);
									resolve();
								}
							},
							error() {
								showErrorMsg($t('暂时无法获取数据请稍后重试！'));
								resolve();
							}
						}, {
							errorAlertModel: 1
						})
					} else {
						resolve(true);
					}
				})
			}

			valid().then(isPass => {
				if(!isPass) {
					waiting(false);
					return;
				}

				CRM.util.getCrmAllConfig(function () {
					waiting(false);

					var type = param.__type || 'add';
					var path = util.getCrmFilePath(param.apiname, 'action') || 'crm-modules/action/myobject/myobject';
					var data = _.omit(param, ['success', 'error', 'destroy', '__type']);//？？？为什么？
					data.title = param.title;
					data.show_type = param.show_type || 'full';
					data._error = function () {
						param.error && param.error.apply(null, arguments);
					}
					data.__type__ = type;

					if (FS.util.getUserAttribute('feedObjs') && FS.util.getUserAttribute('feedObjs').indexOf(param.apiname) >= 0) {
						fns.addFeed(data, param);
					} else {
						waiting();
						require.async(path, function (Action) {
							waiting(false);
							var widget = new Action(data);
							widget.on('refresh', function (res) {
								param.success && param.success.apply(null, arguments);
							});
							widget[type] && widget[type](data);
						})
					}
				}, null, true).then(() => {}, (error) => {
					waiting(false);
					showErrorMsg($t('暂时无法获取数据请稍后重试！'));
				})
			})
		},

		/**
		 * 处理变更单编辑功能
		 * 变更单编辑是编辑的一种特殊形式,需要先获取原始数据再进行编辑
		 * @param {Object} param 编辑参数
		 * @param {string} param.apiname 对象API名称
		 * @param {string} param.id 数据ID
		 * @param {Object} param.allData 包含布局信息的数据对象
		 * @returns {boolean} 是否成功处理变更单编辑
		 */
		_change_order_edit(param) {
			try {
				// 从布局组件中查找变更单编辑按钮
				// const components = param.allData?.describe?.components;
				// const changeOrderEdit = _.find(components, c => {
				// 	return _.find(c?.buttons, b => b.action === 'Edit' && b.button_category === 'change_order');
				// });

				const changeOrderEdit = param.original_describe_api_name || param.allData?.describe?.original_describe_api_name;

				// 不是变更单编辑
				if(!changeOrderEdit) return;

				// 获取变更单原始数据
				const getOriginalData = new Promise((resolve, reject) => {
					util.FHHApi({
						url: `/EM1HNCRM/API/v1/object/${param.apiname}/action/ReChangeOrder`,
						data: {
							object_id: param.id || param.dataId,
						},
						success(res) {
							if(res.Result.StatusCode === 0) {
								resolve(res.Value);
							} else {
								reject(res.Result.FailureMessage);
							}
						},
						error() {
							reject($t('网络异常'));
						}
					}, {
						errorAlertModel: 1
					});
				});

				// 获取合并后的数据
				const getMergedData = new Promise((resolve, reject) => {
					util.FHHApi({
						url: `/EM1HNCRM/API/v1/object/${param.apiname}/controller/ChangeDataMerge`,
						data: {
							object_id: param.id || param.dataId,
						},
						success(res) {
							if(res.Result.StatusCode === 0) {
								resolve(res.Value);
							} else {
								reject(res.Result.FailureMessage);
							}
						},
						error() {
							reject($t('网络异常'));
						}
					}, {
						errorAlertModel: 1
					});
				});

				waiting(' ');

				// 同时获取原始数据和合并数据
				Promise.all([getOriginalData, getMergedData])
					.then(([originalRes, mergedRes]) => {
						waiting(false);
						
						// 更新编辑参数
						param.apiname = originalRes.originalApiName;
						param.id = param.dataId = originalRes.originalDataId;
						param.data = mergedRes.originalData;
						param.data.optionsCommonExtend = {
							versionChecked: false,
							noFetchMDData: true,
							action_type: 're_change_order',
							mdData: mergedRes.originalDetails,
							__changed_data_id__: mergedRes.originalData.__changed_data_id__
						}
						param.version = param.data.version;

						param.__type = 'edit';
						param.show_type = 'full';
						
						// 调用标准编辑功能
						fns.add(param);
					})
					.catch((error) => {
						waiting(false);
						FxUI.Message({
							isMiddler: true,
							duration: 1500,
							message: error,
							type: 'warning'
						});
					});
					
				window.logger?.action({
					eventId: 's-paasobj_action_change_order_edit',
					objectApiName: param.apiname,
					apiName: param.apiname
				});

				return true;

			} catch(e) {
				// 发生异常时静默失败,返回undefined
				return;
			}
		},

		edit: function (param) {
			if(fns._change_order_edit(param)) return;
			
			var isNeedFetchData = !param.data || _.isEmpty(param.data);
			param.__type = 'edit';
			param.show_type = 'full';
			if (isNeedFetchData) {
				waiting('  ');
				util.FHHApi({
					url: `/EM1HNCRM/API/v1/object/${param.apiname}/controller/WebDetail`,
					data: {
						management: false,
						objectDataId: param.id || param.dataId,
						objectDescribeApiName: param.apiname
					},
					success: function (res) {
						waiting(false);
						if (res.Result.StatusCode === 0) {
							if(param.extraFormData) {
								 param.data = _.extend({}, res.Value.data || {}, param.extraFormData);
								 param.data.optionsCommonExtend = {
									originalData: res.Value.data
								 }
							} else {
								param.data = res.Value.data || {};
							}
							param.data._id = param.id;
							fns.add(param);
						} else {
							FxUI.Message({
								isMiddler: true,
								duration: 1500,
								message: res.Result.FailureMessage,
								type: 'warning'
							})
						}
					},
					error: function () {
						waiting(false);
					}
				}, {
					errorAlertModel: 1
				})
			} else {
				fns.add(param)
			}
		},

		list: function (param) {

			//
			// 列表的路由特殊处理对象
			//
			var RouteSpecialObj = [
				// 预置对象
				'AccountObj',           // 客户
				'CrmRival',             // 竞争对手
				'GoalValueObj',         // 目标规则
				'HighSeasObj',          // 公海 c
				'LeadsPoolObj',         // 线索池
				'LeadsObj',         	// 销售线索
				'ContactObj',         	// 客户
				'OpportunityObj',       // 商机
				'NewOpportunityObj',    // 商机2.0
				'SalesOrderObj',        // 销售订单
				'PaymentObj',           // 回款

				'VisitingObj',          // 拜访，走老逻辑
				'InventoryObj',         // 盘点，走老逻辑

				'WarehouseObj',         // 仓库
				'StockObj',             // 库存
				'StockDetailsObj',      // 出入库明细
				'SPUObj',               // 商品
				'ProductObj',           // 产品
				'PriceBookObj',  // 价目表
				'PriceBookProductObj',  // 价目表明细
				'PromotionObj',         // 促销，有引导
				"QuoteLinesObj",         // 报价单明细
			];
			let page = _.contains(RouteSpecialObj, param.apiname) ? param.apiname.toLowerCase() : 'list';
			switch (param.apiname) {
				case 'LeadsPoolObj':
					param.apiname = 'LeadsObj';
					param.pageApiname = 'LeadsPoolObj';
					break;
				case 'HighSeasObj':
					param.apiname = 'AccountObj';
					param.pageApiname = 'HighSeasObj';
					break;
			}
			var jsPath = `crm-modules/page/${page}/${page}`;
			var objectApiName = param.pageApiname || param.apiname;

			this._getAllConfig(function () {
				// if (FS.util.getUserAttribute('isGrayConnectListLayout')) {
					require.async('paas-appcustomization/runsdk', function (appsutom) {
						if (!appsutom || !appsutom.runningobjectlist) {
							//throw new Error('未找到模块:' + 'paas-appcustomization/sdk');
							return;
						}
						appsutom.runningobjectlist().then(function (ObjectList) {
							var container = document.createElement('div');
							$(param.wrapper).html(container);
							let list = ObjectList.init(container, {
								jsPath: jsPath,
								source: 'list',
								apiname: objectApiName,
								apiName: objectApiName,
								param: [objectApiName]
							});
							list.destroy || (list.destroy = list.$destroy);
							param.success && param.success(list);
						});
					})
				// } else {
				// 	require.async(jsPath, function (List) {
				// 		var list = new List(param);
				// 		list.render([objectApiName]);
				// 		param.success && param.success(list);
				// 	});
				// }
			})
		},

		_getAllConfig(cb) {
			CRM.util.getCrmAllConfig(function () {
				cb && cb()
			}, null, true);
		},

		/**
		 *
		 * @param {Object} param
		 * {
		 * apiName {String} 必填
		 * id {String} 必填
		 * showMask {Boolean} 默认为true
		 * entry {String} 'crm' || 'qx'  //从企信打开详情会缩小企信框
		 * zIndex {Number}
		 * idList {Array}
		 * }
		 */
		show_detail: function (param) {
			console.log('该方法已作废，请使用CRM.api.show_crm_detail');

			var apiname = param.apiname;
			var myIndex = param?.zIndex || param?.data?.zIndex || CRM.util.getzIndex();
			if (FS.util.getUserAttribute('feedObjs') && FS.util.getUserAttribute('feedObjs').indexOf(apiname) >= 0) {
				FS.MEDIATOR.trigger('fs.feed2019.slide', {
					url: 'FsFeed/getFeedByObjectId',
					data: {
						apiName: apiname,
						dataId: param.id
					},
					options: {
						zIndex: myIndex,
						success: function () {
							// me.trigger('render.after')
						},
						beforeDestroy: function () {
							// me.trigger('hide')
						}
					}
				})
			} else {
				require.async('crm-components/showdetail/showdetail', function (Detail) {
					var detail = new Detail(_.extend({
						showMask: true,
					}, param));
					param.apiname && detail.setApiName(param.apiname);  //兼容老的其他业务调用 新调用请传apiName
					_.each(['refresh', 'changeRow', 'hide', 'render.after'], function (event) {
						detail.on(event, function (e) {
							var action = event.split('.').join() + 'Handle';
							param[action] && param[action].apply(detail, arguments);
							event == 'hide' && detail.destroy();
						});
					})
					detail.show(param.id, param.apiName, param);
					param.success && param.success(detail);
				});
			}
		},

		batch_add_paymentplanobj: function (param) {
			param.apiname = 'PaymentPlanObj';
			fns.add(param);
		},

		add_paymentobj: function (param) {
			_.extend(param, {
				apiname: 'PaymentObj',
				title: $t("回款"),
				showDetail: true,
			})
			fns.add(param);
		},

		recover: function (param) {
			var confirm = util.confirm($t("确定要恢复吗"), $t("恢复"), function () {
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/BulkRecover',
					data: {
						describe_api_name: param.apiname,
						idList: _.pluck(param.dataList, '_id')
					},
					success: function (res) {
						if (!confirm) return;
						confirm.hide();
						if (res.Result.StatusCode === 0) {
							util.remindSuccess();
							param.success();
							return;
						}
						util.remindFail(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1,
					submitSelector: confirm.$('.b-g-btn').text($t("恢复"))
				})
			}, {
				hideFn: function () {
					confirm = null;
				}
			})
		},

		//860新增变更单生效
		effective(param) {
			var confirm = util.confirm($t("detail.takeeffect.ask"), $t("detail.takeeffect"), function () {
				util.FHHApi({
					url: `/EM1HNCRM/API/v1/object/${param.apiname}/action/Effective`,
					data: {
						describe_api_name: param.apiname,
						object_id: param.dataList[0]._id
					},
					success: function (res) {
						if (!confirm) return;
						confirm.hide();
						if (res.Result.StatusCode === 0) {
							util.remindSuccess();
							param.success();
							return;
						}
						util.remindFail(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1,
					submitSelector: confirm.$('.b-g-btn').text($t("detail.takeeffect"))
				})
			}, {
				hideFn: function () {
					confirm = null;
				}
			});
		},

		delete: function (param) {
			var confirm = util.confirm($t("确定要删除？"), $t("删除"), function () {
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/BulkDelete',
					data: {
						describe_api_name: param.apiname,
						idList: _.pluck(param.dataList, '_id')
					},
					success: function (res) {
						if (!confirm) return;
						confirm.hide();
						if (res.Result.StatusCode === 0) {
							util.remindSuccess();
							param.success();
							return;
						}
						util.remindFail(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1,
					submitSelector: confirm.$('.b-g-btn').text($t("删除"))
				})
			}, {
				hideFn: function () {
					confirm = null;
				}
			})
		},

		delete_async: function (param) {
			var confirm = util.confirm($t("确定要删除？"), $t("删除"), function () {
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/' + param.async_button_apiname,
					data: {
						describe_api_name: param.apiname,
						idList: _.pluck(param.dataList, '_id')
					},
					success: function (res) {
						if (!confirm) return;
						confirm.hide();
						if (res.Result.StatusCode === 0) {
							util.remindSuccess();
							param.success();
							return;
						}
						util.remindFail(res.Result.FailureMessage);
					}
				}, {
					errorAlertModel: 1,
					submitSelector: confirm.$('.b-g-btn').text($t("删除"))
				})
			}, {
				hideFn: function () {
					confirm = null;
				}
			})
		},

		clone: function (param) {
			waiting();
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/Clone',
				data: {
					objectDataId: param.dataId
				},
				success: function (res) {
					waiting(false);
					if (res.Result.StatusCode === 0) {
						CRM.api.add({
							isCopy: true,
							apiname: param.apiname,
							dataId: param.dataId,
							_sourceId: param.dataId,
							data: res.Value.objectData,
							_staticData: res.Value.objectData,
							mdData: res.Value.details,
							showDetail: true,
							title: $t("复制"),
							showMask: true,
							show_type: 'full',
							pageApiname: param.pageApiname,
							_from: 'clone',
							success: function (type, data) {
								param.success && param.success(data);
							},
							error: function () {
								param.error && param.error.apply(null, arguments);
							}
						})
						return;
					}
					util.alert(res.Result.FailureMessage || $t("操作失败"));
				},
				error: function () {
					waiting(false)
				}
			}, {
				errorAlertModel: 1
			})
		},

		// 870 参考新建
		referenceCreate: function(param) {
			require.async('../convertrule/convertrule', (convertrule) => {
				convertrule.referenceCreate(param);
			});
		},

		// 表单二次拉单 910
		secondRefCreate: function(param) {
			require.async('../convertrule/convertrule', (convertrule) => {
				convertrule.secondRefCreate(param);
			});
		},

		// 870 转换规则
		convertRule: function(param) {
			require.async('../convertrule/convertrule', (convertrule) => {
				convertrule.convert(param);
			});
		},

		// 批量转换 910
		batchTransform: function(param) {
			require.async('../convertrule/convertrule', (convertrule) => {
				convertrule.batchTransform(param, true);
			});
		},

		convert: function (param) {
			waiting();
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/' + param.button_apiname,
				data: {
					objectDataId: param.dataId
				},
				success: function (res) {
					waiting(false);
					if (res.Result.StatusCode === 0) {
						var t_apiname = res.Value.targetDescribeApiName;
						var od = res.Value.objectData || {};
						od.is_user_define_work_flow = void 0;
						t_apiname && CRM.api.add({
							title: param.title,
							apiname: t_apiname,
							_sourceId: param.dataId,
							_sorrceApiName: param.apiname,
							data: od,
							_staticData: od,
							mdData: res.Value.details,
							showDetail: true,
							show_type: 'full',
							isConvert:true,
	                        convertParam: {
	                        	apiname: param.apiname,
	                        	buttonApiName: param.button_apiname
	                        },
	                        _from: 'mapping',
	                        noShowCreateContinue: true,
	                        success: function(data) {
	                        	param.success && param.success(data);
	                        }
	                    });
	                    return;
	                }
	                util.alert(res.Result.FailureMessage || $t("操作失败"));
	            },
	            error: function() {
	            	waiting(false)
	            }
	        }, {
	            errorAlertModel: 1
	        })
        },

        excute_after_action: function(param, callback) {
        	param.$btn || waiting($t('执行中') + '...');
			let needExtraArg = param.needExtraArg || false;
			let data = {
				objectDataId: param.dataId,
				args: param.args,
				skipPreFunction: param.skipPreFunction,
				skippedValidatorList: param.skippedValidatorList,
				queryParam: param._from === 'list' ? param.queryParam : void 0,
				trigger_info: param.trigger_info || {
					trigger_page: {
						list: 'List',
						relatedList: 'RelatedList'
					}[param._from] || 'Detail'
				}
			}
			if(needExtraArg) {
				data.arg = param.buttonParam || {};
			}
        	util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/' + (param.button_action || param.button_apiname),
				data,
				success: function (res) {
					param.$btn || waiting(false);
					if (res.Result.StatusCode === 0) {
						let vv = res.Value.validationMessage;
						let status = vv && fns._handValidationMessage(vv, () => {
							param.skipPreFunction = true;
							fns.excute_after_action(param, callback);
						});
						if(status) return;

						vv = res.Value.validationResult;
						vv && fns._handValidationResult(vv, () => {
							param.skippedValidatorList = vv.skippedValidatorList;
							fns.excute_after_action(param, callback);
						})

						if(vv && vv.block !== void 0) return;

						fns._uiSuccessHandle(param, res);
						callback && callback.type === 'FROMICM' && callback(param, res);
						return;
					}

					param.errorCallBack && param.errorCallBack(res);

					util.alert(res.Result.FailureMessage || $t("操作失败"));
				},
				error() {
					param.$btn || waiting(false);
					param.errorCallBack && param.errorCallBack({});
				}
			}, {
				submitSelector: param.$btn,
				errorAlertModel: 1
			})
		},

		_handValidationMessage: function(ov, callback) {
			let {nonBlockMessages, blockMessages} = ov;
			let type, messages;
            if(nonBlockMessages && nonBlockMessages.length) { //不阻断，还可以继续执行
				type = 'orange';
				messages = nonBlockMessages;
            } else if(blockMessages && blockMessages.length) {
				type = 'red';
				messages = blockMessages;
			}

			if(!messages) return;

            let strs = _.map(messages, (content) => `<div class="tip-item"><span class="tip-icon tip-icon-${type}">!</span><div class="tip-content">${_.escape(content)}</div></div>`);
			let html = `<div class="crm-api-ruletip">${strs.join('')}</div>`;

			if(type === 'orange') {
				let confirm = util.confirm(html, $t('提示'), () => {
					confirm.hide();
					callback();
				}, {
					btnLabel: {
                        confirm: $t('继续执行')
                    },
					zIndex: 10000
				})
			} else {
				util.alert(html);
			}

			return true;
        },

		_handValidationResult(ov, callback) {
			let {block, multiMessage} = ov;
			let strs = [];
			if(multiMessage) {
				_.each(multiMessage.blockMessages, content => {
					strs.push(`<div class="tip-item"><span class="tip-icon tip-icon-red">!</span><div class="tip-content">${_.escape(content)}</div></div>`);
				})
				_.each(multiMessage.nonBlockMessages, content => {
					strs.push(`<div class="tip-item"><span class="tip-icon tip-icon-orange">!</span><div class="tip-content">${_.escape(content)}</div></div>`);
				})
			}

			let html = `<div class="crm-api-ruletip">${strs.join('')}</div>`;

			if(!block) {
				let confirm = util.confirm(html, $t('提示'), () => {
					confirm.hide();
					callback();
				}, {
					btnLabel: {
                        confirm: $t('继续执行')
                    },
					zIndex: 10000
				})
			} else {
				util.alert(html);
			}

			return block;
		},

        _uiSuccessHandle: function(param, res) {
			var rv = res.Value.returnValue;
			var returnType = res.Value.returnType;
			if (returnType === 'UIEvent') {
				return param.success && param.success({ type: 'uievent', eventId: res.Value.returnValue });
			}

			if(returnType === 'one_flow') {
				if(res.Value.hasReturnValue) {
					require.async('paas-bpm/secondDev', (secondDev) => {
						secondDev.getComponent('Processor').then((Processor) => {
							let taskProcessor = new Processor()
							let result = taskProcessor.oneFlowProcess(rv, () => {
								//result.task.end
								util.remindSuccess();
							}, true)
						})
					})

					param.success && param.success();
				} else {
					rv && FxUI.Message({
						isMiddler: true,
						duration: 1500,
						message: rv,
						type: 'error'
					})
				}

				return;
			}

			if (param.button_type === 'redirect') {
				if (rv) {
					if (param.redirect_type === 'uipaas' || rv.action === 'WebAction') {
						if (rv.type === 'print') {
							fns.print(_.extend({
								dataId: param.data && param.data._id,
								objectName: param.data && param.data.object_describe_api_name,
								fileName: param.data && param.data.name,
								zIndex: 9999
							}, rv.data));
						} else {
							rv.callback = function (data) { param.success && param.success(data) }
							rv.data = _.extend(rv.data || {}, {
								extendData: param
							})

							if(rv.data.data) {
								rv.data.data.extendsLogInfo = {
									fromObjectApiName: param.apiname,
									fromType: param.button_apiname,
									fromDataId: param.dataId
								}
							}

							if(rv.data.KOtherUsefulDataForCreate) {
								rv.data.data = _.extend(rv.data.data || {}, {
									otherUsefulDataForCreate: rv.data.KOtherUsefulDataForCreate
								})
							}

							window.PAAS.apd.runUIAction(rv.action, rv.type, rv);
						}
						return;
					} else if (param.redirect_type === 'ai_agent') {
						window.PAAS.apd.runUIAction('AiAgent', 'Copilot', {
							objectApiName: param.apiname,
							objectDataId: param.dataId,
							title: param.title,
							...rv,
						});
						return;
					}
					rv = rv.indexOf('://') != -1 ? rv : 'http://' + rv;
					// 2kProtocol://1.0?apiname=&api=add
					// 二开协议
					// 二开模块应该自销毁
					//
					if (rv.split('://')[0] === '2kProtocol') {
						var params = CRM.util.getUrlParams(rv);
						if (params && params.api) {
							var url = FS.ROOT_PATH + '/2k/' + window.PAAS_CONFIG['2kSdk'];
							require.async(url, function (SDK) {
								var sdk = new SDK();
								sdk[params.api](_.extend(param, params, {
									_2kv: '1.0',
									ea: CRM.ea,
								}));
							});
						}
					} else {
						var $a = $('<a rel="noopener noreferrer" href="' + _.escape(rv) + '" target="_blank"></a>');
						$a[0].click();
						$a = null;
					}
				}
			} else {
				!param.no_alert_success_message && (res.Value && res.Value.hasReturnValue ? util.alert('<div style="white-space:pre-wrap">' + rv + '</div>') : util.remindSuccess());
			}
			param.success && param.success();
		},

		fetchFindButtonInfo: function (param) {
			return util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/button/service/findButtonInfo',
				data: {
					buttonApiName: param.button_apiname,
					describeApiName: param.apiname,
					handleButtonParam: true
				},
				success: function (res) {
					if (res.Result.StatusCode === 0) {
						let button = res.Value.button;
						if (button) {
							param.fields = button.param_form;
						}
					}
				},
				error: function () { }
			})
		},

		fetchFindDescribeByApiName: function (param) {
			return util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/describe/service/findDescribeByApiName',
				data: {
					describe_apiname: param.apiname,
					get_label_direct: true,
					include_layout: false,
					include_related_list: false
				},
				success: function (res) {
					param.allFieldsDes = res.Value && res.Value.objectDescribe && res.Value.objectDescribe.fields || {};
				},
				error: function () { }
			})
		},

		// 是否是TPM相关对象详情页的特殊按钮
		__isTPMDetailSpecialButton(param) {
			//TPM 活动举证上的检核、查看按钮(主要针对详情页)
			const TPMSpecialButtonApiName = [
				'CloseActivityAgreement_button_default',   // 终止协议按钮
				'tpm_superior_audit__c',  // 穿透检核
				'TaskPosterCheck_button_default',   // 海报预览
				'GoodsDelivery_button_default'   // 积分兑换记录中的发货按钮
			];
			if (
				(param.apiname == "TPMActivityProofObj" && ["ProofAudit", "ProofCheck"].includes(param.button_action)) ||
				(param.apiname == "TPMActivityAgreementObj" && param.button_action == "ActivityProof") ||
				TPMSpecialButtonApiName.includes(param.button_apiname)
			) {
				var nsb = NSButtons[param.apiname + param.button_apiname] || NSButtons[param.button_apiname];
				if (CRM.api[nsb]) {
					param.__name = nsb;
					CRM.api[nsb](param);
					return true;
				}
			}
			return false;
		},

		after_action: async function (param) {

			if(this.__isTPMDetailSpecialButton(param)) return;

			if(param.apiname === 'DistributorAuthorizationAgreementObj' && param.button_action === 'SyncToPriceBook') {
				let nsb = NSButtons[param.button_apiname];
				if(CRM.api[nsb]) {
					return CRM.api[nsb](param);
				}
			}

			// 新建渠道商
			if (param.button_apiname === 'CreatePartner_button_default') {
				var nsb = NSButtons[param.button_apiname];
				if (CRM.api[nsb]) {
					param.__name = nsb;
					CRM.api[nsb](param);
					return;
				}
			}

			// 外呼
			if (['button_e_call_out__c', 'button_e_work_phone_call_out__c'].includes(param.button_apiname)) {
				return CRM.api.call_out(
					param, 
					param.button_apiname === 'button_e_work_phone_call_out__c' ? 'workPhone' : ''
				)
			}
			// 故障知识推荐
			if (param.button_apiname === 'FaultKnowledgeRecommend_button_default') {
				return CRM.api.showKnowledgeRecommend(param)
			}
			// [入账]、[确认对账] 按钮特殊逻辑
			if (param.button_apiname === 'EnterAccount_button_default' || param.button_apiname === 'ConfirmReconciliation_button_default') {
				debugger
				// 详情页的预设按钮 和 列表页的批量操作按钮 无法填写字段（因为后台未返回需要填写的自定义字段描述），特由前端来特殊处理
				if (!param.fields) {
					waiting('  ');
					await Promise.all([
						this.fetchFindButtonInfo(param),
						this.fetchFindDescribeByApiName(param)
					]).then(() => {
						waiting(false);
						_.each(param.fields || [], item => {
							let apiname = item.api_name.split('form_')[1];
							let temp = _.extend({}, param.allFieldsDes[apiname], item);
							_.extend(item, temp);
						})

					})
				}
			}

			// [入账]按钮特殊逻辑
			if (param.button_apiname === 'EnterAccount_button_default') {
				// 处理入账账户字段特殊逻辑：添加默认值 & 新增过滤条件
				let fundAccountIdField = _.find(param.fields, item => {
					let apiname = item.api_name.split('form_')[1];
					return apiname === 'fund_account_id'
				})
				if (fundAccountIdField) {
					await CRM.util.fetchObjList('AuthorizationDetailObj', {
						search_query_info: JSON.stringify({
							limit: 50,
							offset: 0,
							filters: [{
								field_name: 'faccount_authorization_id.authorized_object_api_name',
								field_values: [param.apiname],
								operator: 'IN',
								is_master_field: true,
							}],
						})
					}).then((res) => {
						let defaultObj = null;
						let ids = _.map(res.dataList || [], a => {
							if (a.is_default_entryaccount) defaultObj = a;
							return a.authorize_account_id;
						})
						if (defaultObj) {
							_.extend(param.data, {
								fund_account_id: defaultObj.authorize_account_id,
								fund_account_id__r: defaultObj.authorize_account_id__r,
							})
						}
						let filters = fundAccountIdField.wheres[0].filters;
						let filterItem = {
							field_name: '_id',
							field_values: ids,
							operator: 'IN',
							value_type: 0,
						};
						let index = filters.findIndex(a => a.field_name === '_id');
						if (index === -1) {
							filters.push(filterItem);
						} else {
							filters[index] = filterItem;
						}
					});
				}
			}

			// 特殊处理互联按钮
			if (param.apiname === 'PublicEmployeeObj' || param.apiname === 'EnterpriseRelationObj' || param.apiname === 'ErDepartmentObj') {
				var nsb = param.apiname + param.button_apiname;
				// 参考 list_btn_operate 方法内 NSButtons 集合
				// CRM.api 由对象列表、详情等模块注册，如果不触发互联对象模块，就不会注册相应的 CRM.api
				if (CRM.api[nsb]) {
					param.__name = nsb;
					CRM.api[nsb](param);
					return;
				}
			}

			// [企微客户群&企微好友记录特殊处理]
			if (['GroupInherit',"FriendInherit"].includes(param.button_action)){
				var nsb = NSButtons[param.apiname + param.button_action]
				if (CRM.api[nsb]&& !param[param.apiname+'processed']) {
					param.__name = nsb;
					CRM.api[nsb](param);
					return;
				}
			}

			// [企微客户群成员特殊处理]
			if (['RelatedEmployee'].includes(param.button_action)){
				param.__after_action = require('../wechatgroupuserobj/wechatgroupuserobj').__after_action(fns);
			}

			if (simplyIcmanage && simplyIcmanage.breakCrmapi(param) === breakReturn) return; // 特殊处理详情页开通互联企业按钮

			//没有需要填写的参数直接执行后动作
			if (!param.fields || !param.fields.length) {
				if (param.__after_action) {
					param.__after_action(param);
				} else {
					if (simplyIcmanage && simplyIcmanage.after_action_callback(param) === breakReturn) return;
					// 互联对象业务化，相关按钮操作执行后需要刷新列表
					fns.excute_after_action(param);
				}
				return;
			}

			var layouts = [];
			var data = {};
			var fields = {};
			_.each(JSON.parse(JSON.stringify(param.fields)), function (a) {
				a.render_type = a.type;
				a.api_name = a.api_name.replace('form_', '');
				layouts.push(a);
				fields[a.api_name] = a;
				if (param.__isBatch) return;
				a.default_is_expression || (data[a.api_name] = a.default_value);
			});

			var paths = ['crm-modules/action/field/field'];
			if (fields.category && fields.category.describe_api_name === 'ServiceKnowledgeObj') {
				paths.push('crm-modules/action/serviceknowledgeobj/serviceknowledgeobj');
			}

			_.extend(data, param.data);

			param.parseFields && param.parseFields(fields);

			waiting(' ');
			require.async(paths, function (field, appField) {
				waiting(false);
				var actionForm = field.add(_.extend({
					Model: field.Model.extend({
						fetch: function () {
							this.parse()
						},
						change() {
							this.excuteCustomFieldRule();
						},
						excuteCustomFieldRule() {
							if(!param.customFieldRuler) return;
							let rule = param.customFieldRuler(this.get('data'));
							this.trigger('rule', rule, true);
						},
						parse: function () {
							this._filterOptions(fields);
							var newLayouts = [];
							var tt = {};
							_.each(layouts, function (a) {
								if (a.group_type === 'area') {
									_.each(a.fields, function (b) {
										tt[b] = true;
									})
								}
							})
							_.each(layouts, function (a, index) {
								if (a.group_type === 'area') {
									var tarr = [];
									_.each(a.fields, function (b) {
										fields[b] && tarr.push(fields[b]);
									})
									newLayouts.push({
										group_type: 'area',
										fields: a.fields,
										api_name: a.api_name,
										columns: 1,
										components: tarr
									})
								} else {
									tt[a.api_name] || newLayouts.push({
										label: '',
										api_name: 'basic',
										columns: 1,
										components: [a]
									})
								}
							})
							newLayouts = this._hackParseArea(newLayouts, fields);
							var fl = [];
							_.each(newLayouts, function (a) {
								[].push.apply(fl, a.components);
							})
							this.set({
								layout: [{
									label: '',
									columns: 1,
									components: fl
								}],
								fields: fields
							})

							this.excuteCustomFieldRule();
						},
						submit: function (data, btn) {
							data.object_describe_api_name = data.object_describe_id = void 0;
							var tmp = {};
							_.each(data, function (v, k) {
								tmp['form_' + k] = v;
							})
							_.each(this.get('param'), function (v, k) {
								tmp['form_' + k] = v;
							})

							//处理多语言
							let dd = this.get('data');
							_.each(fields, a => {
								if(a.enable_multi_lang) {
									tmp['form_' + a.api_name + '__lang'] = dd[a.api_name + '__lang'];
								}
							})

							data = tmp;
							var me = this;
							var _tmp = _.extend({}, param, {
								args: _.extend({}, data, this.get('param')),
								$btn: btn,
								success: function () {
									me.trigger('success');
									param.success && param.success(_tmp.args);
								},
								hide: function () {
									me.trigger('success');
								}
							})
							if (param.__after_action) {
								//me.trigger('success');
								param.__after_action(_tmp);
							} else {
								fns.excute_after_action(_tmp);
							}

						}
					}),
					View: param.view || (appField && appField.View) || null,
					className: appField && appField.className,
					size: 'md',
					data: data,
					apiname: '',
					record_type: 'default__c',
					title: _.escape(param.title),
					show_type: 'dialog',
					yqslCustom: param.apiname === "AccountObj" && param.button_apiname === "button_2Q2hk__c" && param.button_action === "BulkCustomButton", // 元气 批量修改配送商 客开
					btns: [{
						label: $t("确 定"),
						action: 'submit',
						isMain: true
					}, {
						label: $t("取 消"),
						action: 'cancel'
					}],
					__trigger_info: {
						trigger_page: 'Button'
					},
					action_from: [param.apiname, param._from || 'detail', param.button_apiname].join('_').toLowerCase()

				}, param.fieldParam));
				param.initComplete && param.initComplete(actionForm);
			});
		},

		duplicate: function (param) {
			waiting();
			require.async('crm-modules/components/duplicate/duplicate', function (Duplicate) {
				waiting(false);
				if (param.api === 'byTool') {
					Duplicate.byTool(param);
				} else {
					Duplicate.byAdd(param);
				}
			});
		},

		qxsendcrmobjectbytype_pick_data: function (param) {
			param.customerId = param.associatedObjectId;
			param.methods.select = function (res) {
				var objects = [];
				_.each(res.selected, function (dItem) {
					objects.push({
						apiName: res.apiname || (CRM.config.objDes[res.objectType] || {}).apiName || res.objectType,
						objectId: dItem._id
					});
				});
				param.methods.sendCrmObjects(objects);
			}
			CRM.api.pick_data(_.extend({}, param, {
				title: param.targetApiLabel ? $t("关联") + param.targetApiLabel : '',
				apiName: param.targetApiName,
				maxNum: param.maxCount,
				single: false,
				relationRequired: param.associatedObjectId ? {
					ObjectType: 2,
					DataIDs: [param.associatedObjectId],
					Names: [param.associatedObjectName]
				} : null,
				filters: (param.associatedApiName === 'AccountObj' && param.customerId) ? [
					{
						field_name: 'account_id',
						field_values: [param.customerId],
						operator: 'EQ'
					}
				] : [],
				object_data: {
					account_id: param.associatedObjectId,
					account_id__r: param.associatedObjectName
				}
			}))
		},

		qxsendcrmobject_pick_data: function (param) {
			CRM.api.relate_crm_object(_.extend({}, param, {
				showAsText: false,
				dataMaxCount: param.maxCount,
				selectedItems: param.customerId ? {
					2: {
						data: [{
							id: param.customerId,
							name: param.name
						}]
					}
				} : {},
			}));

			param.methods.hideLoading && param.methods.hideLoading();
			param.methods.success = function (data) {
				var objects = [];
				_.each(data.selectedItems, function (item) {
					_.each(item.data, function (item_item) {
						objects.push({
							apiName: item.objectType,
							objectId: item_item.data._id
						});
					});
				});
				param.methods.sendCrmObjects(objects);
			}
		},

		preview_image: function (param) {
			const imageList = param.list || param.datas;
			const previewImage = imageList?.[param.index];

			// 大附件预览
			if (previewImage) {
				const previewImageUrl = previewImage.bigUrl || previewImage.middleUrl;
				if (CRM.util.isBigFile(previewImageUrl)) {
					const checkLicense = function() {
						return new Promise((resolve) => {
							Fx.util.getFileUsedSpace({
								success(data) {
									resolve(data && data.open && data.available > 0)
								},
							})
						});
					};

					checkLicense().then(function(supportPreview) {
						if (supportPreview) {
							window.open(previewImageUrl, '_blank');
						} else {
							Fx.util.alert?.($t("fx_filepreview_bigfile_tips"));
						}
					});

					return;
				}
			}

			if (param.isNew) {
				if (param.objectPermissionApiName && !param.hideDownAllLink) {
					waiting();
					util.fetchObjectDownloadPermission(param.objectPermissionApiName).then(function (flag) {
						waiting(false);
						param.hideDownAllLink = !flag;
						fns.new_preview_image(param);
					})
				} else {
					fns.new_preview_image(param);
				}
				return;
			}
			waiting();
			require.async('base-imagepreview', function (ImagePreview) {
				waiting(false);
				if (!ImagePreview && !param.__noGet) {
					param.__noGet = true;
					fns.preview_image(param);
					return;
				}
				new ImagePreview({
					data: param.datas,
					activeIndex: param.index,
					mainType: 'crm',
					navType: 'crm',
					zIndex: param.zIndex || 5000,
					customOpts: {
						hideDownAllLink: param.hideDownAllLink || false,
						originHidden: param.originHidden,
						batchDownloadHandle: function (callback) {
							var me = this;
							util.api({
								url: '/FSC/ASYN/EM/File/batchDownload',
								data: {
									files: _.map(param.datas, function (item) {
										return {
											Name: item.filename || item.name,
											Path: item.path
										}
									})
								},
								success: function (res) {
									if (res.status === 0 && res.token) {
										callback && callback(FS.BASE_PATH + '/FSC/EM/File/BatchDownloadByStream?token=' + res.token);
									}
								}
							}, {
								autoPrependPath: false,
							})
						}
					}
				}).show();
			});
		},

		preview_video: function (param) {
			let type = 'video',
				ext = CRM.util.getFileExtText(param.fileName || '');
	

			const txCloundPlayUrl = param.filePath && param.filePath.slice(0, param.filePath.lastIndexOf("."))
			if(["998", "999", "1000"].includes(txCloundPlayUrl) || FS.crmUtil.getFileExtText(txCloundPlayUrl) === 'm3u8') {
				if(txCloundPlayUrl === "998") {
					FS.util.remind(3, $t('ava.video.transcoding.processing'));
					return
				} else if(txCloundPlayUrl === "999") {
					FS.util.remind(3, $t('ava.video.transcoding.timeout'));	
					return
				} else if(txCloundPlayUrl === "1000") {
					FS.util.remind(3, $t('ava.video.not_exist'));
					return
				} else if(FS.crmUtil.getFileExtText(txCloundPlayUrl) === 'm3u8'){
					require.async('vcrm/sdk', sdk => {
						sdk.getComponent('tencentMediaviewer').then(Comp => {
							Comp.$show({url: txCloundPlayUrl, mediaType: 'video', playInline: false}).then(res => {
								console.log(res);
							});
						});
					});
					return;
				}
			}
			if(FS.crmUtil.getFileExtText && !FS.crmUtil.getFileExtText(param.filePath)) {
				ext && (param.filePath = param.filePath + '.' + ext);
			}
			
			let path = FS.util.getUserAttribute('noSupportNewPreviewVideo')
				? Fx.util.getFscLinkByOpt({id: param.filePath, name: param.fileName, ext})
				: Fx.util.getFscStreamLink(param.filePath, {ext});

			if (/\.(?:ogg|mp3|wav|aac)$/i.test(param.fileName)) { //音频
				type = 'audio'
			}
			if (/\.(?:mp4|mkv|mov|m4v|webm)$/i.test(param.fileName)) { //视频
				type = 'video';
			}
			Cmpt.get('mediaviewer', { props: { url: path, mediaType: type, playInline: false }}, {appendToBody: true});
		},

		new_preview_image: function (param) {
			param.list || (param.list = param.datas);
			fns.__previewImage && (fns.__previewImage.destroy(), fns.__previewImage = null);
			// 全局滚动条去掉
			var prevOverflow = document.body.style.overflow;
			document.body.style.overflow = 'hidden';
			fns.__previewImage = FxUI.create({
				wrapper: $('body')[0],
				template: '<fx-image-viewer :z-index="zIndex" :with-watermark="true" :on-close="closeViewer" :initial-index="index" :url-list="list" :show-download="download" :batchDownloadHandle="batchDownloadHandle"></fx-image-viewer>',
				data: function () {
					return {
						list: _.map(param.list, function (a) {
							return {
								url: a.middleUrl || a.bigUrl,
								downloadUrl: a.originDownUrl
							}
						}),
						zIndex: +param.zIndex || 3000,
						show: true,
						index: param.index * 1,
						download: param.hideDownAllLink ? false : true,
						batchDownloadHandle: param.hideDownAllLink || _.find(param.list, a => /acModel=attachment$/.test(a.originDownUrl || '')) || param.list.length < 2 ? void 0 : function () {
							util.api({
								url: '/FSC/ASYN/EM/File/batchDownload',
								data: {
									files: _.map(param.list, function (item) {
										return {
											Name: item.filename || item.name,
											Path: item.path
										}
									})
								},
								success: function (res) {
									if (res.status === 0 && res.token) {
										var downUrl = FS.BASE_PATH + '/FSC/EM/File/BatchDownloadByStream?token=' + res.token;
										var $a = $('<a target="_blank"></a>');
										$a[0].href = downUrl;
										$a[0].click();
										$a = null;
									}
								}
							}, {
								autoPrependPath: false,
							})
						}
					}
				},

				methods: {
					closeViewer: function () {
						fns.__previewImage && (fns.__previewImage.destroy(), fns.__previewImage = null);
						document.body.style.overflow = prevOverflow;
					}
				}
			});
		},

		format_field_value: function (param) {
			function init() {
				return new Promise(function (resolve, reject) {
					require.async('crm-modules/action/field/field', function (Field) {
						resolve(function (opts) {
							return Field.format(opts.field, opts.val, opts.data) || $t('未填写');
						})
					});
				});
			}

			param.success && param.success(init);
		},

		//列表页单条操作按钮
		list_btn_operate: function (param) {
			// 未标准化的列表页单条操作按钮
			var nsb = NSButtons[param.apiname + param.button_apiname] || NSButtons[param.button_apiname];
			if (nsb && CRM.api[nsb]) {
				param.__name = nsb;
				CRM.api[nsb](param);
				return;
			}

			var name = {
				common: 'after_action',
				redirect: 'after_action',
				clone: 'clone',
				convert: 'convert',
				transform: 'convertRule'
			}[param.button_type];

			name && fns[name](param);
		},

		//列表页自定义的操作按钮(批量)
		list_batchbtn_operate: function (param) {
			if (_.contains(['thirdapp_image_preview__c', 'thirdapp_image_preview_row__c'], param.button_api_name)) { //快销批量预览图片
				waiting();
				require.async('vcrm/sdk', function (SDK) {
					waiting(false);
					param.describe.field_list = param.field_list;
					SDK.getKXImagePreviewModule(param.describe, param.dataList, param.button_api_name === 'thirdapp_image_preview_row__c' ? 'row' : void 0);
				})
				return;
			}

			if(param.button_api_name === 'tpm_review_write_off__c'){  // TPM 批量核销
				var nsb = NSButtons[param.button_api_name] || NSButtons[param.apiname + param.button_action] || NSButtons[param.button_action];
				if (nsb && CRM.api[nsb]) {
					param.__name = nsb;
					param.__is_batch = true;
					CRM.api[nsb](param);
					return;
				}
			}

			if (param.button_action === 'AsyncBulkPrint') {//批量打印
				waiting();
				require.async('paas-vui/sdk', function (SDK) {
					waiting(false);
					SDK.getTemplate().then(function (Template) {
						Template.batchPrint({ objDescApiName: param.apiname, dataIds: param.dataIds });
					})
				})
				return;
			}
			if (param.button_api_name === 'ErDataSync__c') {// 数据同步
				waiting();
				require.async('app-shuttles/app', function (mod) {
					waiting(false);
					mod.amodule('objexbtn', 'syncData', {
						object_describe_api_name: param.apiname,
						_id: param.dataIds,
					})
				})
				return;
			}

			var nsb = NSButtons[param.apiname + param.button_action] || NSButtons[param.button_action];
			if (nsb && CRM.api[nsb]) { //暂未标准化的批量按钮
				param.__name = nsb;
				CRM.api[nsb](param);
				return;
			}

			if (simplyIcmanage && simplyIcmanage.breakBatapi(param) === breakReturn) return;
			param.trigger_info || (param.trigger_info = {
				trigger_page: 'List'
			})
			if (param.button_type === 'redirect') { //列表页批量ui按钮同步执行
				fns.list_bulk_uiaction(param);
				return;
			}

			let secondConfirm = param.second_confirm_callback || function() {return Promise.resolve(true)};

			var innerFn = function (param, $btn) {
				secondConfirm(param).then(flag => {
					if(!flag) return;

					var data = {
						buttonApiName: param.button_api_name,
						dataIds: param.dataIds,
						args: param.args,
						trigger_info: param.trigger_info
					}
					if (param.async_button_apiname === 'AsyncBulkRecover') {
						data.describe_api_name = param.apiname;
						data.idList = param.dataIds;
						data.dataIds = void 0;
					}
					waiting($t('paas.crm.api.simply.operation_in_progress', null, '操作进行中') + '...');
					util.FHHApi({
						url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/' + param.async_button_apiname,
						data: data,
						success: function (res) {
							waiting(false);
							if (res.Result.StatusCode === 0) {
								param.jobId = res.Value.jobId;
								fns.list_batchbtn_operate_query(param);
							} else {
								util.alert(res.Result.FailureMessage || $t("操作失败"));
							}
						},
						error: function () {
							waiting(false)
						}
					}, {
						errorAlertModel: 1,
						submitSelector: $btn || param.$btn
					})
				})
			}
			if (param.param_form || param.button_api_name === 'EnterAccount_button_default' || param.button_api_name === 'ConfirmReconciliation_button_default') { //需要填写字段
				fns.after_action({
					fields: param.param_form,
					data: { object_describe_api_name: param.apiname },
					apiname: param.apiname,
					button_action: param.button_action,
					button_apiname: param.button_api_name,
					source: param.source,
					title: param.button_label,
					__after_action: function (a) {
						param.args = a.args;
						a.success && (param.success = a.success);
						innerFn(param, a.$btn);
					},
					__isBatch: true,
					success: param.success
				})
			} else {
				innerFn(param);
			}
		},

		list_bulk_uiaction: function(param, skipPreFunction) {
			var innerFn = function () {
				waiting('  ');
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/BulkUIAction',
					data: {
						buttonApiName: param.button_api_name,
						dataIds: param.dataIds,
						args: param.args,
						trigger_info: param.trigger_info,
						skipPreFunction
					},
					success: function (res) {
						waiting(false);
						if (res.Result.StatusCode === 0) {
							let vv = res.Value && res.Value.validationMessage;
							let status = vv && fns._handValidationMessage(vv, () => {
								fns.list_bulk_uiaction(param, true);
							});

							if(status) return;

							fns._uiSuccessHandle({ ...param, isBatchBtn: true }, res);
						} else {
							util.alert(res.Result.FailureMessage || $t("操作失败"));
						}
					},
					error: function () {
						waiting(false)
					}
				}, {
					errorAlertModel: 1,
					submitSelector: param.$btn
				})
			}

			if (param.param_form && param.param_form.length) {
				param.fields = param.param_form;
				param.__after_action = function (a) {
					innerFn(_.extend(param, a));
				}
				fns.after_action(param);
			} else {
				innerFn();
			}
		},

		//表单页面ui 按钮执行函数
		form_uiaction: function (param, skipPreFunction) {
			var innerFn = function () {
				waiting('  ');
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/UIAction',
					data: {
						objectDataId: param.data._id || '',
						buttonApiName: param.api_name,
						objectData: param.data,
						details: param.details || {},
						args: param.args,
						skipPreFunction,
						trigger_info: param.trigger_info
					},
					success: function (res) {
						waiting(false);
						if (res.Result.StatusCode === 0) {
							let vv = res.Value && res.Value.validationMessage;
							let status = vv && fns._handValidationMessage(vv, () => {
								fns.form_uiaction(param, true);
							});

							if(status) return;

							fns._uiSuccessHandle(param, res);
						} else {
							util.alert(res.Result.FailureMessage || $t("操作失败"));
						}
					},
					error: function () {
						waiting(false)
					}
				}, {
					errorAlertModel: 1,
					submitSelector: param.$btn
				})
			}

			if (param.param_form && param.param_form.length) {
				param.fields = param.param_form;
				param.__after_action = function (a) {
					innerFn(_.extend(param, a));
				}
				fns.after_action(param);
			} else {
				innerFn();
			}
		},

		/**
		 * 详情页UI按钮执行函数
		 * @param {*} param
		 * @returns
		 */
		detail_uiaction: function(param) {
			var BizButtons = {
				FollowUp_button_default: 'follow_leadsobj', // 跟进中
				Close_button_default: 'invalid_leadsobj',	 // 无效
				TransferUI_button_default:  'convert_leadsobj',  // 转换
				TransferPartner_button_default: 'convert_leadsobj', // 转换合作伙伴
				ChangePartner_button_default: 'change_partner', // 更换合作伙伴
				DeletePartner_button_default: 'delete_partner' // 移除合作伙伴
			};

			var fn = BizButtons[param.button_apiname];

			// 无效按钮处理参数
			if (fn === 'invalid_leadsobj') {
				// 无效原因
				if (!param.reasons && param.describe && param.describe.fields) {
					let reasons = param.describe.fields.close_reason && param.describe.fields.close_reason.is_active ? param.describe.fields.close_reason.options : [];

					let field = param.param_form.find(item => item.api_name === 'form_close_reason');

					if (field && (!field.options || !field.options.length)) {
						field.options = reasons;
					}

					// param.reasons = _.filter(reasons, function (item) {
					// 	return !item.not_usable;
					// });
				}
			}

			// 通用数据处理
			if (typeof param.dataList === 'undefined') {
				param.dataList = [param.data];
			}

			if (fn && CRM.api[fn]) {
				CRM.api[fn](param);

				return;
			}

			fns.after_action(param);
		},

		list_batchbtn_operate_query: function (param) {
			waiting($t('执行中') + '...');
			if (!param.__time) {
				param.__time = new Date().getTime();
			}
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/button/service/findButtonJobResult',
				data: {
					jobId: param.jobId,
					describeApiName: param.apiname
				},
				success: function (res) {
					var rv = res.Value;
					if (rv && rv.jobInfo) { //查询到结果
						waiting(false);
						var rj = rv.jobInfo;
						var failNum = rj.total - rj.successNum;
						if (failNum) {
							var tips = $t('本次操作覆盖{{total}}条{{displayName}}, 执行成功{{successNum}}条,失败{{failNum}}条,详细执行结果可', {
								total: rj.total,
								successNum: rj.successNum,
								failNum: failNum,
								displayName: rj.describeDisplayName
							}) + '<a class="d__view" href="javascript:;">' + $t('查看详情') + '</a>'
							var _alert = util.alert(tips);
							_alert.element.on('click', function (e) {
								if ($(e.target).hasClass('d__view')) {
									_alert.destroy();
									fns.show_batchoperate_detail(rj);
								}
							})
						} else {
							util.remindSuccess();
						}
						param.success && param.success();
					} else {
						if (new Date().getTime() - param.__time > (param.timeOut || 5000)) { //超过5s没结果
							waiting(false);
							var isContinue;
							var confirm = util.confirm($t('crm.批量操作提示'), $t('提示'), function () {
								isContinue = true;
								confirm.hide();
							}, {
								btnLabel: {
									confirm: $t('继续等待'),
									cancel: $t('我知道了')
								},
								hideFn: function () {
									isContinue ? (param.__time = null, param.stepTime = 3000, param.timeOut = 120000, fns.list_batchbtn_operate_query(param)) : param.success && param.success();
								}
							})
							// var msg = $t('crm.批量操作提示');
							// if(FxUI && FxUI.Message) {
							// 	FxUI.Message({
							//          isMiddler: true,
							//          duration: 3000,
							//          message: msg,
							//          type: 'warning'
							//        })
							// } else {
							// 	util.alert(msg);
							// }
							// param.success && param.success();
						} else {
							setTimeout(function () {
								fns.list_batchbtn_operate_query(param);
							}, param.stepTime || 1000)
						}
					}
				},
				error: function () {
					waiting(false);
				}
			}, {
				errorAlertModel: 1,
				submitSelector: param.$btn
			})
		},

		show_batchoperate_detail: function (param) {
			waiting();
			require.async('crm-modules/detail/batchoperate/batchoperate', function (Batchoperate) {
				waiting(false);
				var instance = new Batchoperate();
				instance.show(param);
				instance.on('hide', function () {
					instance.destroy();
				})
			})
		},

		/**
		 * 工商详情
		 * @param {Object} param
		 * {
		 * apiname {String} 对象apiname
		 * data {
		 * 	id {String} 工商id
		 *  name {String} 工商企业名称
		 *  datasource {String}(QiChaCha|TianYanCha|DengBaiShi) 工商来源（企查查|天眼查|邓白氏|不传则为纷享自己的工商库）
		 *  supplyTag {Number}(1|0) 是否补充查询（是|否|不传即为否）
		 *  zIndex {Number} 层级
		 *  isShowBtn {Boolean} 是否在详情页展示回填按钮
		 *  success {Function} 回填按钮的回填成功后的回调函数
		 * }
		 * }
		 * @returns
		 */
		business_detail: function (param) {
			if (!param.id) {
				return;
			}

			FS.BI_MODULE.MEDIATOR.trigger('bi.salesclues.recommend.boot', {
				apiName: param.apiname,
				data: {
					id: param.id,
					name: param.name || '',
					datasource: param.datasource,
					supplyTag: param.supplyTag,
					zIndex: param.zIndex,
					isDetail: true,
					btnName: param.isShowBtn ? $t("回填") : null,
					callback: function () {
						backfill();
					}
				}
			});

			function backfill(e) {
				var me = this;
				var successFn = param.success;
				var apiname = param.apiname;
				var url = '';
				if (param.mappingRuleApiname) {
					url = '/EM1HNCRM/API/v1/object/BizQueryObj/action/' + param.mappingRuleApiname;
				} else {
					const map = {
						'Default': '/EM1HNCRM/API/v1/object/BizQueryObj/action/button_fillICInfo__c',
						'PartnerObj': '/EM1HNCRM/API/v1/object/BizQueryObj/action/button_fillPartnerInfo__c',
						'SupplierObj': '/EM1HNCRM/API/v1/object/BizQueryObj/action/button_bizqueryobj2supplierobj__c',
						'LeadsObj': '/EM1HNCRM/API/v1/object/BizQueryObj/action/button_bizqueryobj2leadsobj__c',
						'EnterpriseInfoObj': '/EM1HNCRM/API/v1/object/BizQueryObj/action/button_bizqueryobj2enterpriseinfoobj__c',
						'AccountMainDataObj': '/EM1HNCRM/API/v1/object/BizQueryObj/action/button_bizqueryobj2accountmaindataobj__c',
						'CompetitorObj': '/EM1HNCRM/API/v1/object/BizQueryObj/action/button_bizqueryobj2competitorobj__c',
					}
					url = map[apiname] || map['Default'];
				}

				CRM.api.ajax = util.FHHApi({
					url: url,
					data: {
						objectDataId: param.id,
						industryDatasource: param.datasource,
						industrySupplyTag: param.supplyTag,
					},
					success: function (res) {
						if (res.Result.StatusCode === 0) {
							var data = res.Value.objectData;
							//客户名称默认不回填
							// data && (data.name = data.object_describe_api_name = void 0);
							successFn && successFn(data || res.Value);
							return;
						}
						util.alert(res.Result.FailureMessage || $t("操作失败"));
					}
				}, {
					errorAlertModel: 1
				});

				CRM.util.uploadLog('BizQueryObj', 'Detail', {
					operationId: 'WriteBack',
					eventType: 'cl',
				});

				// 区分对象的工商回填埋点
				CRM.util.uploadLog(param.apiname, 'newpage', {
					operationId: param.datasource ? param.datasource + 'WriteBack' : 'BusinessWriteBack',
					eventType: 'cl',
				});
			}
		},

		/**
		 * 工商详情
		 * @param {Object} param
		 * {
		 * apiname {String} 对象apiname
		 * data {
		 * 	id {String} 工商id
		 *  name {String} 工商企业名称
		 *  datasource {String}(QiChaCha|TianYanCha|DengBaiShi) 工商来源（企查查|天眼查|邓白氏|不传则为纷享自己的工商库）
		 *  supplyTag {Number}(1|0) 是否补充查询（是|否|不传即为否）
		 *  zIndex {Number} 层级
		 *  isShowBtn {Boolean} 是否在详情页展示回填按钮
		 *  callback {Function} 回填按钮的回调函数
		 * }
		 * }
		 * @returns
		 */
		business_detail2(param) {
			if (!param.id) {
				return;
			}

			FS.BI_MODULE.MEDIATOR.trigger('bi.salesclues.recommend.boot', {
				apiName: param.apiname,
				data: {
					id: param.id,
					name: param.name || '',
					datasource: param.datasource,
					supplyTag: param.supplyTag,
					zIndex: param.zIndex,
					isDetail: true,
					btnName: param.isShowBtn ? $t("回填") : null,
					callback: param.callback,
				}
			});
		},

		//异步获取布局规则数据
		layout_rule: function (param) {
			require.async('crm-modules/action/field/field', function (field) {
				var ruleData;
				try {
					if (param.rules && param.rules.length) {
						var rule = new field.Rule({
							rules: param.rules,
							layouts: param.layouts,
							fields: param.fields
						})
						ruleData = rule.getRuleWithEmptyData(param.data);
					}
				} catch (e) { }

				param.success(ruleData);
			})
		},

		remove_campaignmembers: function (param) {
			CRM.util.confirm($t("确定移除市场活动成员？"), null, function () {
				this.hide();
				waiting($t('paas.crm.api.simply.operation_in_progress', null, '操作进行中') + '...');
				innerFn(param);
			})
			var innerFn = function (param) {
				util.FHHApi({
					url: '/EM1HNCRM/API/v1/object/' + param.apiname + '/action/' + param.async_button_apiname,
					data: {
						buttonApiName: param.button_api_name,
						dataIds: param.dataIds,
						args: param.args
					},
					success: function (res) {
						waiting(false);
						if (res.Result.StatusCode === 0) {
							param.jobId = res.Value.jobId;
							fns.list_batchbtn_operate_query(param);
						} else {
							util.alert(res.Result.FailureMessage || $t("操作失败"));
						}
					},
					error: function () {
						waiting(false)
					}
				}, {
					errorAlertModel: 1,
					submitSelector: param.$btn
				})
			}
		},

		BatchManagePicture(param) {
			const { apiname } = param;

			waiting();

			require.async('crm-modules/components/batch_update_image/batch_update_image', (BatchUpdateImage) => {
				let widget = new BatchUpdateImage({ objectApiName: apiname });

				waiting(false);
				widget.on('refresh', function (res) {
					param.success && param.success.apply(null, arguments);
				});

				widget = null;
			});
		},

		//多账户扣减
		MultiAccountReduce(param) {
			let paramData = param.data;

			//先判断是否已开启支出业务类型
			util.FHHApi({
				url: '/EM1HNCRM/API/v1/object/AccountTransactionFlowObj/controller/ValidRecordType',
				data: {
					describeApiName: "AccountTransactionFlowObj",
					is_only_active: true
				},
				success: function (data) {
					if (data.Result.StatusCode == 0) {
						let isActive = false;
						let recordList = data.Value.record_list;
						_.each(recordList, (item) => {
							if (item.api_name === 'default__c' && item.is_active) {
								//已开启支出类型
								isActive = true;
							};
						});

						if (!isActive) {
							util.alert($t('该功能需要【支出】业务类型权限，请联系管理员！'));
							return;
						} else {
							//跳转到新建页
							CRM.api.add({
								apiname: 'AccountTransactionFlowObj',
								title: $t('批量新建') + ' ' + $t('账户支出流水'),
								show_type: 'full',
								__type: 'batchAdd',
								data: {
									record_type: 'default__c',
								},
								nonEditable: true,
								successCallback: function (type, data) {
									param.success && param.success(data);
								},
							})
						}
					};
				}
			}, {
				errorAlertModel: 1
			});
		},

		/**
		 * @desc 获取CRM模块
		 * @param param
		 * @param param.path 模块路径
		 * @param param.cb 回调
		 */
		get_module: function (param) {
			var path = param.path;
			var cb = param.cb;

			if (!path) {
				return;
			}

			try {
				require.async(path, function (module) {
					cb && cb(module);
				});
			}
			catch (e) {
				cb && cb(e);
			}
		},
		/**
		 * @desc 判断对象是不是启用流程布局
		 * @param param
		 * @param param.apiName 对象apiName
		 * @param param.success success回调
		 * @param param.error error回调
		 */
		isFlowLayout(param) {
			let me = this;
			let apiName = param.apiName;
			let success = param.success ;
			let error = param.error;
			let flag = false

			if(!apiName){
				error&&error()
			}
			function exec (grayConfig, apiName){
				let result = false;
				let currentEI = util.getCurrentEmployee().enterpriseID + '';
				let isMyObject = apiName.indexOf('__c') > -1;
				if(isMyObject){
					result = grayConfig.ei.includes('*') || grayConfig.ei.includes(currentEI);
				} else {
					if(grayConfig.ei.includes('*') || grayConfig.ei.includes(currentEI)){
						let solo = grayConfig.soloConfig[apiName];
						result = grayConfig.apiName.includes('*')
							|| grayConfig.apiName.includes(apiName)
							|| solo && solo.includes(currentEI);
					}
				}
				return result
			}
			if(me.__flowLayoutCache){
				try {
					success && success(exec(me.__flowLayoutCache, apiName))
				} catch (e) {
					error&&error(e)
				}
			} else {
				util.FHHApi({
					url: '/EM1AFLOW/Config/GetWebConfig',
					data: {
						key: 'new_flow_layout_gray_config'
					},
					success(data) {
						flag = true;
						try {
							let grayConfig = JSON.parse(data.Value) || {};
							me.__flowLayoutCache = grayConfig
							success && success(exec(grayConfig, apiName))
						} catch (e) {
							error&&error(e)
						}
					},
					complete() {
						if(!flag){
							error&&error()
						}
					}
				}, {
					errorAlertModel: 1
				});
			}
		}
	};

	(function (modsrc) {
		try {
			const isdev = window.PUBLISH_MODEL === 'development'; // 开发环境不依赖 md5
			const hasmd5 = require.resolve(modsrc).match(/\/simply-icmanage-[0-9a-f]{8,}\.js/);
			if (isdev || hasmd5) {
				// 开发环境，或者确定有 md5 的环境，才加载互联模块
				require.async(modsrc, (mod) => {
					mod && mod.simplySetup({ NSButtons, fns }); // 如果模块加载失败，后续不应该依赖 simplyIcmanage
					simplyIcmanage = mod || null; // 请看文件头部注释
				});
			}
		} catch (e) { }
	})('icmanage-modules/crmobj-actions/simply-icmanage');

	return function (param) {
		var name = param.__name;
		delete param.__name;
		return fns[name](param);
	}
})
