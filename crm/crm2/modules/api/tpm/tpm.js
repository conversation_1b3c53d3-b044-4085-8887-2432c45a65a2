/*
 * @Author: <PERSON>
 * @Date: 2022-12-01 11:20:16
 * @LastEditTime: 2024-10-25 18:31:36
 * @LastEditors: <PERSON> Jun
 * @Description: 处理结案
 */
define(function (require, exports, module) {
	const BTN_HANDLERS = {
		BudgetClosure: (param) => {
			let loading = FxUI.Loading.service({ fullscreen: true });
			CRM.util.FHHApi({
				url: "/EM1HNCRM/FMCG/API/TPM/BudgetClosure/Close",
				data: {
					describe_api_name: param.apiname,
					data_id: param.dataId,
					is_force_end: param.is_force_end || false,
				},
				success(res) {
					if (res.Value.need_confirm) {
						FxUI.MessageBox.confirm(res.Value.tips, undefined, {
							type: "warning",
							dangerouslyUseHTMLString: true,
							closeOnClickModal: false,
						}).then(() => {
							param.is_force_end = true;
							BTN_HANDLERS.BudgetClosure(param);
						});
					} else {
						FxUI.Message.success($t("执行成功"));
						if (param.success) {
							param.success();
						}
					}
				},
				complete() {
					loading.close();
				},
			});
		},

		//活动举证中检核按钮
		ProofAudit: (param, options = {}) => {
			const data = param.data;
			require.async("paas-tpm/sdk", ({ getTPMActivityAudit }) => {
				getTPMActivityAudit().then((Audit) => {
					Audit.init({
						activityId: data.activity_id,
						dealerId: data.dealer_id,
						storeId: data.store_id,
						proofId: data._id,
						viewMode:
							param.__name === "ProofAudit" ? "audit" : "view",
						...(options || {}),
					}).then(instance => {
						instance.$on("close", (is_updated) => {
							if (is_updated) {
								param.success && param.success();
							}
						});
					})

				});
			});
		},

		ProofCheck: (param) => {
			BTN_HANDLERS.ProofAudit(param);
		},

		TPMSuperiorAudit: (param) => {
			console.log(param);
			const data = param.data;
			param.data = {};
			let field_map = {
				object_Jk6CW__c: {    //蒙牛1端的穿透检核对象
					activityId: 'field_z1hrn__c',
					dealerId: 'dealer_id__c',
					storeId: 'store_id__c',
					field_Gvb1m__c: 'field_Gvb1m__c'
				},
				TPMActivityAgreementObj: {    // 蒙牛N端的穿透检核对象
					activityId: 'activity_id',
					dealerId: 'dealer_id__c',
					storeId:'store_id',
					field_Gvb1m__c: 'field_Gvb1m__c'
				}
			}[data.object_describe_api_name || 'object_Jk6CW__c'] || {}  // 对象的字段映射
			console.log('字段映射：', field_map);
			BTN_HANDLERS.ProofAudit(param, {
				viewMode: "view",
				activityId:  data[field_map.activityId || 'field_z1hrn__c'],
				dealerId: data[field_map.dealerId || 'dealer_id__c'],
				storeId: data[field_map.storeId || 'store_id__c'],
				is_superior: true,
				others_props: {
					object_api_name: data.object_describe_api_name,
					agreement_id: data._id,
					agreement_id__r: data.name,
					out_tenant_id: data.out_tenant_id,
					field_Gvb1m__c: data[field_map.field_Gvb1m__c || 'field_Gvb1m__c'],
				},
			});
		},

		//从协议对象进行活动举证
		AgreementObjGotoActivityProof(param) {
			console.log(param);
			let loading = FxUI.Loading.service({
				fullscreen: true,
				background: "rgba(0, 0, 0, 0)",
			});
			CRM.util.FHHApi({
				url: "/EM1HNCRM/API/v1/object/TPMActivityAgreementObj/action/ActivityProof",
				data: {
					objectDataId: param.dataId,
					trigger_info: {
						trigger_page: param._from == "list" ? "List" : "Detail",
					},
				},
				success({ Value }) {
					const { object_data } = Value
						? Value.preAddResult || {}
						: {};
					CRM.api.add({
						data: {
							...object_data,
							record_type:
								object_data.record_type || "default__c",
						},
						apiname: "TPMActivityProofObj",
						isSubmitAndCreate: false,
						success: function (data) {
							param.success && param.success(data);
						},
					});
				},
				complete() {
					loading.close();
				},
			});
		},

		//终止协议
		onTPMTerminationOfAgreement(param) {
			console.log("终止协议");
			let loading = FxUI.Loading.service({ fullscreen: true });
			CRM.util.FHHApi({
				url: "/EM1HNCRM/API/v1/object/TPMActivityAgreementObj/action/CloseActivityAgreement",
				data: {
					objectDataId: param.dataId,
					need_confirm: param.is_force_end || false,
				},
				success(res) {
					if (res.Value.isShowTips) {
						FxUI.MessageBox.confirm(res.Value.tips, undefined, {
							type: "warning",
							dangerouslyUseHTMLString: true,
							closeOnClickModal: false,
						}).then(() => {
							param.is_force_end = true;
							BTN_HANDLERS.onTPMTerminationOfAgreement(param);
						});
					} else {
						FxUI.Message.success($t("执行成功"));
						this.visible = false;
						if (param.success) {
							param.success();
						}
					}
				},
				complete() {
					loading.close();
				},
			});
		},

		// 复核核销
		onTPMReviewWriteOff(param) {
			console.log(param);
			require.async("paas-tpm/sdk", ({ getTPMStoreWriteoff }) => {
				getTPMStoreWriteoff().then((Audit) => {
					const writeoffData = param.__is_batch ? param.dataList : [param.data];
					Audit.init({ writeoffData, type: 'review', button_label: param.title || param.button_label }).then((instance) => {
						instance.$on("updateSuccess", () => {
							if (param.success) {
								param.success();
							}
						});
					});
				});
			});
		},

		// 预览任务海报
		onTaskPosterCheck(param) {
			require.async("paas-tpm/sdk", ({ getPosterDetailDialog }) => {
				getPosterDetailDialog().then((Poster) => {
					let PosterConstructor = Vue.extend(Poster);
					let instance = new PosterConstructor({
						propsData: {
							formData: param.data
						}
					}).$mount();
					document.body.appendChild(instance.$el);
					return instance;
				})
			})
		},
		// 发货
		onGoodsDeliveryButtonDefault(param) {
			console.log(param);

			const send = (data = {}) => {
				let loading = FxUI.Loading.service({ fullscreen: true });
				return new Promise((resolve) => {
					CRM.util.FHHApi({
						url: "/EM1HNCRM/API/v1/object/PointsExchangeRecordObj/action/GoodsDelivery",
						data: {
							objectDataId: param.dataId,
							trigger_info: {trigger_page: param._from == "list" ? "List" : "Detail"},
							...data
						},
						success(res) {
							FxUI.Message.success($t("执行成功"));
							if (param.success) {
								param.success();
							}
							resolve(res)
						},
						complete() {
							loading.close();
						},
					});
				})
			}

			const { order_type, pickup_method } = param.data;
			if(order_type == 'activity_physical_order' && pickup_method == 'option2'){  //发货单据类型 = 活动实物订单 ，且 领取方式 = 邮寄到家 时
				FxUI.create({
					template: `
						<fx-dialog title="${$t('app.checkin.orderDesigner.canvas.distribute.delivery')}" :visible.sync="visible" width='500px' :close-on-click-outside='false' :close-on-click-modal='false'>
							<fx-form :model="model" size='small' ref="form">
								<fx-input required label="${$t('eservice.repair.kuaidi.expressOrg')}" prop='delivery_company' v-model="model.delivery_company" />
								<fx-input required label="${$t('eservice.repair.kuaidi.expressOrderId')}" prop='express_delivery_number' v-model="model.express_delivery_number" />
							</fx-form>
							<div slot="footer" class="dialog-footer">
								<fx-button type="primary" @click="onSend" size="small">${$t('确定')}</fx-button>
								<fx-button @click="visible = false" size="small">${$t('取消')}</fx-button>
							</div>
						</fx-dialog>
					`,
					data() {
						return {
							visible: true,
							model: {
								delivery_company: "",
								express_delivery_number: "",
							}
						}
					},
					methods: {
						onSend() {
							this.$refs.form.validate().then(() => {
								send({ delivery_company: this.model.delivery_company, express_delivery_number: this.model.express_delivery_number }).then(() => {
									this.visible = false;
								})
							})
						}
					}
				})
			}else {
				send()
			}
		}
	};

	/**
	 * 来自详情页、列表页的结案按钮 （参数可能不一样）
	 * @param {Object}	param
	 * @param param.apiname      apiname
	 * @param param.dataId     对象数据
	 * @param param.success     成功回调
	 * @return {Function}
	 */
	return function (param) {
		let func = BTN_HANDLERS[param.__name];
		func && func(param);
	};
});
