.crm-a-productobj {
	.f-g-item.spu-wrapper {
		width: 100%;
		// padding-left: 68px;

		.crm-action-nfield {
			overflow: inherit;
			padding: 0;
		}

		.f-item-wrap {
			width: 100%;
			max-width: 100%;
			&.crm-a-product-category{
				width:0;
			}
		}

		.f-item {
			width: 100%;

			.f-comp-wrap[data-apiname='spu'] {
				width: 100%;

				.f-item {
					width: 50%;
				}
			}

			.f-comp-wrap[data-apiname='spu_id'] {
				width: 50%;
			}
		}
	}

	.f-g-item {
		&.f-g-item-specs,
		&.f-g-item-spuid {
			width: 100%;
		}

		&.f-g-item-spuid {
			.f-item-wrap {
				max-width: 480px;
			}
		}
	}

	.f-g-item-specs {
		.f-g-item-tit {
			display: none;
		}

		.f-item-wrap {
			width: 100%;
			max-width: 100%;

			.spec-sku-options {
				overflow: hidden;

				.options-item-wrapper {
					width: 50%;
					min-height: 34px;
					box-sizing: border-box;
					float: left;
					display: inline-block;
					padding-bottom: 8px;
					position: relative;

					&:nth-child(2n+1) {
						clear: left;
					}
					.lable {
						width: 128px;
						line-height: 28px;
						margin: 8px 8px 0 80px;
						font-size: 14px;
						position: absolute;
						left: 0;
					}
					.cpopt-wrapper {
						padding-left: 216px;
					}
				}
			}
		}
	}

	.f-g-item.f-g-item-issepc,.f-g-item.f-g-item-ismulti  {
		display: block !important;
		width: 100% !important;

		.f-g-item-tit,
		.f-item-wrap {
			display: inline-block;
			float: left;
		}
	}

	.error-tip {
		color: rgb(245, 113, 95);

		&::before {
			content: ' ';
			display: inline-block;
			width: 16px;
			height: 16px;
			background: url("@{imgUrl}/icos-crm.png") no-repeat;
			overflow: hidden;
			vertical-align: middle;
			margin-right: 4px;
			background-position: -271px -19px;
		}
	}

	.crm-a-product-category {
		width:0;
		.crm-a-category-hand {
			width: 100%;
			padding: 5px 10px;
			height: 34px;
			box-sizing: border-box;
			line-height: 22px;
		}
	}

	.f-g-item.multiunit {
		display: block;
		width: 100%;

		.f-item-wrap {
			width: 100%;
			max-width: 100%;
		}

		.add-unit {
			width: 100%;
			height: 22px;

			span {
				float: right;
				font-size: 14px;
				line-height: 22px;
				color: var(--color-info06);
				cursor: pointer;
			}
		}

		.multiunit-table-wrapper {
			// padding-left: 68px;
			.tr-disabled {
				background-color: rgba(238, 238, 238, 0.5);
				pointer-events: none;
			}
		}

		.check-wrapper {
			width: 100%;
			height: 100%;
			text-align: center;

			.check-radio-btn {
				width: 16px;
				height: 16px;
				display: block;
				content: '';
				border: 1px solid #ccc;
				border-radius: 50%;
				box-sizing: border-box;

				&.checked {
					border: 4px solid var(--color-info06);
				}

				&.disabled {
					background: #dee1e6;

					&.checked {
						background: var(--color-neutrals07);
						border-color: #dee1e6;
					}
				}
			}
		}

		.table-wrapper {
			.dt-main {
				border: 1px solid #dee1e6;

				table.tb {
					width: 100% !important;
				}

				.tb-del,
				.tb-enable {
					text-decoration: none;
					display: inline-block;
				}

				.tb-enable {
					margin-left: 10px;
				}
			}
		}
	}

	&.crm-action-nfield-mini {

		.f-g-item{
			&.spu-wrapper,&.multiunit .multiunit-table-wrapper {
				padding-left: 24px;
			}
			&.f-g-item__true_or_false,&.f-g-item__true_or_false,&.f-g-item-spuid{
				.f-g-item-tit{
					width: 7%;
				}
			}
		}


		.f-g-item-specs {
			.f-item-wrap {
				.spec-sku-options {
					.options-item-wrapper {
						.lable {
							font-size: 12px;
							width: 7%;
							min-width: 80px;
							margin-left: 40px;
						}

						.cpopt-wrapper {
							padding-left: 132px;
						}
					}
				}
			}
		}
		.crm-a-product-category {
			.crm-a-category-hand {
				height: 24px;
				line-height: 24px;
				padding:0 0 0 6px;

			}
		}
	}
}

.crm-c-dialog {
	.dialog-btns {
		.product-prompt {
			display: inline-block;
			float: left;
			margin-top: 6px;

			.mn-checkbox-item {
				width: 16px;
				height: 16px;
				display: inline-block;
				padding: 0;
				margin-right: 8px;
				background: url("@{imgUrl}/checkbox.png") no-repeat;
				background-position: -6% 50%;
				vertical-align: middle;

				&.mn-selected {
					background: url("@{imgUrl}/checkbox.png") no-repeat;
					background-position: 100% 0;
				}
			}

			.text {
				padding: 0;
				margin-left: 0;
				display: inline-block;
				vertical-align: middle;
			}
		}
	}

	.mn-radio-box {
		position: relative;
	}

	.mn-radio-wrapper {
		width: 48px;
		height: 24px;
		border-radius: 14px;
		background-color: #407fff;
		border: 2px solid #407fff;

		&.radio-false {
			background-color: #ccc;
			border: 2px solid #ccc;
		}

		.spu-radio-item {
			display: inline-block;
			width: 24px;
			height: 24px;
			border-radius: 50%;
			float: left;
			padding: 0;
			background: rgba(255,255, 255, 0);
			border: none;
			background-image: none !important;
			text-align: center;
			line-height: 24px;
			color: #ccc;
			cursor: pointer;

			&.mn-selected {
				background: rgba(255,255, 255, 1);
			}
		}
	}

	.radio-cover {
		position: absolute;
		width: 48px;
		height: 24px;
		border-radius: 14px;
		display: none;

		&.true {
			display: block;
			cursor: not-allowed;
		}
	}

	.spec-sku-options {
		.options-item-wrapper {
			overflow: hidden;
			width: 100%;
			padding-bottom: 4px;
		}

		.cpopt-item {
			display: inline-block;
			float: left;
			height: 24px;
			line-height: 24px;
			padding: 0 8px;
			background: #e1e9fa;
			margin-right: 4px;
			margin-top: 4px;
			color: var(--color-neutrals19);
			cursor: pointer;
			border-radius: 4px;

			&.active {
				background: #407fff;
				color: var(--color-neutrals01);
			}

			&.disabled,
			&.init-disabled,
			&.unactive {
				background: #7a96cc;
				color: var(--color-neutrals01);
				cursor: not-allowed;
			}
		}
	}
}

.lang-en {
	.crm-a-productobj {
		.crm-g-remind-ico {
			left: 160px;
		}
	}
}

.crm-a-fixedcollocation {
	.f-item-line-wrap[data-apiname="bom_section__c"] {
		width: 100%;

		.bom_table-wrapper {

			.bom_table-header {
				font-size: 14px;
				color: #407fff;
				text-align: right;
				line-height: 22px;
				margin-bottom: 8px;

				span {
					cursor: pointer;
				}
			}

			.bom_table-content {
				border: 1px solid #dee1e6;
				border-top: none;

				.crm-w-table .dt-term-batch {
					min-height: 0;
				}
				.js-btn {
					color: #407FFF;
				}
			}

		}
	}
}
