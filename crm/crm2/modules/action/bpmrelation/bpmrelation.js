/**
 * @desc  bmp任务依赖crm相关操作（新建，选对象，查看详情，批量编辑从对象）
 * <AUTHOR>
 * !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 * 慎重修改！！！！关联多个业务
 * 包括切不限于
 * 业务流新建从对象，新建关联对象，选择关联对象
 * 阶段推进器新建从对象，新建关联对象，选择关联对象
 * !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 */
 define(function(require, exports, module) {
    var masterdetail = require('./masterdetail/masterdetail');
    var isHanding, innerParam;
    var util = FS.crmUtil;
    var waiting = util.waiting;
    function showError(res) {
        util.alert(res.Result.FailureMessage || $t("获取数据失败"));
        triggerBPM();
    }
    let bizTypeMap = {
      bpm: 'workflow_bpm',
      rocket: 'stage'
    }


    function fetchDetail(callback,isMaster) {
        util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/' + innerParam.apiname + '/controller/WebDetail',
            data: {
                objectDataId: innerParam.dataId,
                objectDescribeApiName: innerParam.apiname
            },
            success: function(res) {
                if (res.Result.StatusCode === 0) {
					callback(res.Value.data,innerParam);
                    return;
                }
                showError(res);
                isHanding = null;
                waiting(false);
            }
        }, {
            errorAlertModel: 1
        })
    }

    function showMasterDetail() {
        waiting(' ');
        fetchDetail(function(data, item) {
            waiting(false);
            isHanding = null;
            masterdetail(innerParam, item);
        })
    }
    function getDraftId(bizType, bizId) {
      return new Promise((resolve) => {
        PaasUI.utils.api.findDraftId(bizType, bizId).then((res) => {
          if(res && res.draftId) {
            waiting(false);
            FxUI.MessageBox.confirm($t('当前任务已经保存过草稿，是否获取？'), $t('提示'), {confirmButtonText: $t('获取'),
            cancelButtonText:  $t('不获取'),
            distinguishCancelAndClose: true,
          }).then(() => {
            window.Fx && window.Fx.log && window.Fx.log('paas-flow-draft-action', 'cl', {
              module: bizTypeMap[bizType],
              extends: {
                action: 'getdraftdata',
              }
            })
              resolve({draftId: res.draftId, useDraft: true})
            }).catch((action) => {
              if(action === 'close') {
                isHanding = null;
                window.Fx && window.Fx.log && window.Fx.log('paas-flow-draft-action', 'cl', {
                  module: bizTypeMap[bizType],
                  extends: {
                    action: 'closedraftdialog',
                  }
                })
                resolve({action: 'breakOff'})
              } else {
                window.Fx && window.Fx.log && window.Fx.log('paas-flow-draft-action', 'cl', {
                  module: bizTypeMap[bizType],
                  extends: {
                    action: 'notgetdraft',
                  }
                })
                resolve({draftId: res.draftId, useDraft: false})
              }
            })
          } else {
            resolve()
          }
        });
      })
    }
    function batchUpdateMasterDetail() {
        waiting(' ');
        fetchDetail(function(data) {
            CRM.api.isFlowLayout({
                apiName:innerParam.apiname,
                success(result){
                    if(!result && innerParam.apiname.indexOf('__c') === -1){
                        var path = util.getCrmFilePath(innerParam.apiname, 'action');
                        require.async([path, 'crm-modules/action/field/field'], function(Comp, field) {
                            isHanding = null;
                            waiting(false);
                            var isComplete;
                            var btns = [{
                                action: 'cancel',
                                label: $t('取消')
                            }]
                            if (innerParam.showSaveBtn) {
                                btns.unshift({
                                    action: 'submit',
                                    label: innerParam.btn['update'] || $t('crm.保存'),
                                    isMain: true
                                })
                            }
                            if (innerParam.showCompleteTaskBtn) {
                                btns.unshift({
                                    action: 'saveAndCompleteTask',
                                    label: innerParam.btn['updateAndComplete'] || $t('保存并完成'),
                                    isMain: true
                                })
                            }
                            var MyModel = Comp.Model || field.Model;
                            if (innerParam.params && innerParam.params.bizInfo) {
                                MyModel = MyModel.extend({
                                    update: function(param) {
                                        param.bizInfo = innerParam.params.bizInfo;
                                        if (Comp.Model) {
                                            Comp.Model.prototype.update.apply(this, arguments);
                                        } else if (field.Model) {
                                            field.Model.prototype.update.apply(this, arguments);
                                        }
                                    }
                                })
                            }

                            var View = Comp.View || field.View;

                            var formCom = field.edit({
                                _from: 'bmp',
                                record_type: data.record_type,
                                show_type: 'full',
                                View: View.extend({
                                    _hideBpmMd: function() {
                                        var me = this;
                                        if(!me.__hideBpmMd) {
                                            me.__hideBpmMd = _.debounce(function() {
                                                util.waiting(false);
                                                var attr = {};
                                                var nn;
                                                _.each(me.get('detailObjectList') || [], function(obj) {
                                                    if(obj.related_list_name !== innerParam.listname) {
                                                        attr[obj.objectApiName] = {hidden: true};
                                                    } else {
                                                        nn = obj.objectApiName;
                                                    }
                                                })
                                                me.model._toggleMDStatus(attr);
                                                if(!nn) return;
                                                var $tmpEl = me.$('.md-content');
                                                if(!$tmpEl.length) {
                                                    $tmpEl = me.$('.crm-md20__wrapper');
                                                }
                                                $tmpEl[0] && $tmpEl[0].scrollIntoView();
                                            }, 50);
                                        }
                                        util.waiting(' ');
                                        me.__hideBpmMd();
                                    },
                                    mdrendercomplete: function() {
                                        View.prototype.mdrendercomplete.apply(this, arguments);
                                        if(!innerParam.listname) return;
                                        this._hideBpmMd();
                                    },
                                    __renderFreeApproval(){
                                        this.set("freeApprovalDef", undefined);
                                    }}),
                                Model: MyModel,
                                btns: btns,
                                activeNavName: innerParam.activeNavName,
                                data: data,
                                dataId: innerParam.dataId,
                                apiname: innerParam.apiname,
                                renderComplete: function(full) {
                                    setTimeout(function() {
                                        full && full.view && full.view.scrollToMD && full.view.scrollToMD();
                                    }, 200)
                                },
                                saveAndCompleteTask: function(e, full) {
                                    isComplete = true;
                                    full && full.view && full.view.submit(e);
                                },
                                submit: function(e, full) {
                                    isComplete = false;
                                    full && full.view && full.view.submit(e);
                                },
                                success: function(type, data) {
                                    innerParam.callback && innerParam.callback(_.extend(data, {
                                        isCompleteTask: isComplete,
                                        id: data._id,
                                        value: data.name || ''
                                    }));
                                }
                            });
                            if (formCom && formCom.on) {
                                formCom.on('destroy', function() {
                                    innerParam.destroy && innerParam.destroy();
                                });
                            }
                            triggerBPM();
                        })
                    } else {
                      waiting(false);
                      isHanding = null;
                      let biz = innerParam.params && innerParam.params.bizInfo && innerParam.params.bizInfo.biz || innerParam.formModel && bizTypeMap[innerParam.formModel] || ''
                      let bizId = innerParam.params && innerParam.params.bizInfo.bizId || innerParam.bizId || ''
                      getDraftId(biz, bizId).then((draft) => {
                        let draftId = draft && draft.draftId || ''
                        let useDraft = draft && draft.useDraft || false
                        if(draft && draft.action === 'breakOff') {
                          innerParam.callback && innerParam.callback({breakOff: true})
                          triggerBPM();
                          return
                        }
                        let isComplete;
                        let newDraft = true
                        data.bpmBizType = biz
                        data.bpmBizId = bizId
                        let crmParam = {
                            data: data,
                            id: innerParam.dataId,
                            dataId:innerParam.dataId,
                            apiname: innerParam.apiname,
                            version: data.version,
                            extendOptions: {
                                formFooterSlot:($wrapper, view)=>{
                                    if(view.get('detailObjectList') && view.get('detailObjectList').length){
                                        util.waiting(' ');
                                        let fun = ()=>{
                                            util.waiting(false);
                                            if(!innerParam.listname) return;
                                            let attr = {};
                                            (view.get('detailObjectList') || []).forEach((obj) => {
                                                if(obj.related_list_name !== innerParam.listname) {
                                                    attr[obj.objectApiName] = {hidden: true};
                                                }
                                            })
                                            view.model._toggleMDStatus(attr);
                                            let $tmpEl = view.$('.md-content');
                                            if(!$tmpEl.length) {
                                                $tmpEl = view.$('.crm-md20__wrapper');
                                            }
                                            $tmpEl[0] && $tmpEl[0].scrollIntoView();
                                        }
                                        view.listenTo(view.model, {mdrendercomplete: fun})
                                    }
                                    return {
                                        destroy:()=>{}
                                    }
                                },
                                formButtons: [{
                                    action: 'submit',
                                    isMain: true,
                                    label: innerParam.btn['update'] || $t('crm.保存'),
                                    callback: (view) => {
                                        isComplete = false;
                                        let fun = view.model.update;
                                        view.model.update = function(param, btn){
                                            if (innerParam.params && innerParam.params.bizInfo) {
                                                param.bizInfo = innerParam.params.bizInfo;
                                            }
                                            fun.call(this, param, btn)
                                        }
                                        view.submit(event);
                                    }
                                }, {
                                    action: 'saveAndCompleteTask',
                                    isMain: true,
                                    label: innerParam.btn['updateAndComplete'] || $t('保存并完成'),
                                    callback: (view) => {
                                        isComplete = true;
                                        let fun = view.model.update;
                                        view.model.update = function(param, btn){
                                            if (innerParam.params && innerParam.params.bizInfo) {
                                                param.bizInfo = innerParam.params.bizInfo;
                                            }
                                            fun.call(this, param, btn)
                                        }
                                        view.submit(event);
                                    }
                                },{
                                  action: 'updateDraft',
                                  isMain: false,
                                  label: $t('保存草稿'),
                                  callback: (view) => {
                                    isComplete = false;
                                    if(!useDraft && draftId) {
                                      view.saveDraft(event, true);
                                      if(newDraft) {
                                        PaasUI.utils.api.deleteDraft([draftId], innerParam.apiname)
                                        newDraft = false
                                      }
                                    } else {
                                      view.saveDraft(event, true);
                                    }
                                  }
                                }, {
                                    label: $t("取 消"),
                                    action: 'cancel'
                                }],
                            },

                            success:()=>{
                                innerParam.callback && innerParam.callback(_.extend(data, {
                                    isCompleteTask: isComplete,
                                    id: data._id,
                                    value: data.name || ''
                                }));
                            },
                            destroyCallback:()=>{
                                innerParam.destroy && innerParam.destroy();
                            }
                        }
                        if(draftId && useDraft) {
                          crmParam.draftId = draftId
                          CRM.api.use_draft(crmParam)
                        } else {
                          CRM.api.edit(crmParam)
                        }
                        triggerBPM();
                      })
                    }
                },
                error(){
                    waiting(false);
                }
            })

      },true)
    }
    function canAdd(item, cb) {
        util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/lookup/service/check',
            data: {
                dataId: innerParam.dataId,
                describeApiName: innerParam.apiname,
                lookupFieldName: item.field_api_name,
                targetDescribeApiName: item.ref_object_api_name
            },
            success: function(res) {
                var temp;
                if (res.Result.StatusCode === 0) {
                    temp = res.Value.canAdd;
                    if (!temp) {
                        util.alert(res.Value.helpText);
                        triggerBPM();
                    }
                    cb && cb(temp);
                    return;
                }
                showError(res);
                cb && cb();
            }
        }, {
            errorAlertModel: 1
        })
    }

    function _hackFieldData(sn, data, fieldData) {
        if (sn === 'OpportunityObjSalesOrderObj') {
            fieldData.order_amount = data.expected_deal_amount;
            fieldData.order_time = data.expected_deal_closed_date;
            return fieldData;
        }
        if (sn === 'SalesOrderObjOrderPaymentObj' || sn === 'SalesOrderObjPaymentObj') {
            fieldData.order_data_id = data._id;
            fieldData.order_id = data.name;
            fieldData.from_sales_order = true;
            fieldData.order_detail_data = data;
            return fieldData;
        }

        if (sn === 'SalesOrderObjInvoiceApplicationObj' || sn === 'SalesOrderObjRefundObj' || sn === 'SalesOrderObjContractObj') {
            fieldData.CustomerTradeID = data._id;
            fieldData.CustomerTradeID__r = data.name;
            return fieldData;
        }

        return fieldData;
    }

    function _fetchStaticDataAndWheres(data, item, callback) {
        var fieldData = {};
        var account_id = data.account_id || data.customer_id;
        var account_id__r = data.account_id__r || data.customer_id__r;
        if (innerParam.apiname === 'AccountObj') {
            account_id = data._id;
            account_id__r = data.displayName||data.name
        }
        if (account_id && !/__c$/.test(item.ref_object_api_name)) { //默认带入客户覆盖90%场景(仅限预置对象 自定义对象不带)
            _.extend(fieldData, {
                account_id: account_id,
                customer_id: account_id,
                account_id__r: account_id__r,
                customer_id__r: account_id__r,
                CustomerID: account_id,
                CustomerID__r: account_id__r
            })
        }

        util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/' + item.ref_object_api_name + '/controller/DescribeLayout',
            data: {
                include_detail_describe: true,
                include_layout: false,
                apiname: item.ref_object_api_name,
                // layout_type: 'add',
            },
            success: function(res) {
                if (res.Result.StatusCode !== 0) {
                    showError(res);
                    callback();
                    return;
                }
                var fields = res.Value.objectDescribe.fields;
                var temp = _.find(fields, function(a) { //先以自定义对象通用的方式找到回填的字段
                    return a.target_related_list_name === item.related_list_name && a.target_api_name === innerParam.apiname;
                })
                temp || (temp = _.find(fields, function(a) {
                    return a.target_api_name === innerParam.apiname;
                }))

                if (!temp) {
                    callback(fieldData);
                    return;
                }
                fieldData[temp.api_name] = data._id;
                fieldData[temp.api_name + '__r'] = data.displayName||data.name;

                _.each(temp.wheres, function(a) {
                    _.each(a.filters, function(b) {
                        var fv = b.field_values;

                        if (fv && b.value_type == 2 && fv[0] && data[b.field_name]) {
                            var key = fv[0].replace(/\$/g, '');
                            if (!key) return
                            fieldData[key] = data[b.field_name];
                            fieldData[key + '__r'] = data[b.field_name + '__r'];
                        }
                    })
                })

                callback(fieldData);
            }
        }, {
            errorAlertModel: 1
        })
    }

    function _createRelationData(param) {
      let hasUnDeleteDraft = false
      if(param.data) {
        param.data.bpmBizType = bizTypeMap[innerParam.formModel]
        param.data.bpmBizId = innerParam.bizId
      }
		    param.show_type = 'full';
        let path = util.getCrmFilePath(param.apiname, 'action');
        innerParam.btn = innerParam.btn || {}
        require.async(path, function(Action) {
            isHanding = null;
            waiting(false);
            if (!Action) return;
            let action = new Action();
            let isComplete = false;
            let btns = [{
                action: 'submit',
                label: innerParam.btn['update'] || $t('crm.保存'),
                isMain: true
            },{
                action: 'cancel',
                label: $t('取消')
            }]
            if(innerParam.batchAddRelatedObject){
                btns.splice(1, 0, {
                    action: 'saveAndCompleteTask',
                    label: innerParam.btn['updateAndComplete'] || $t('保存并完成'),
                    isMain: true
                })
            }
            let crmParam = JSON.parse(JSON.stringify(param))
            if(param.draftId && !param.useDraft) {
              delete crmParam.draftId
              hasUnDeleteDraft = true
              crmParam.data.optionsCommonExtend = {
                saveDraftCompleteCallback: () => {
                  if(hasUnDeleteDraft) {
                    PaasUI.utils.api.deleteDraft([param.draftId], param.apiname).then(() => {
                      hasUnDeleteDraft = false
                    })
                  }
                }
              }
            }
            let args = _.extend({
              _from: 'bpm',
              nonEditable: true,
              isSubmitAndCreate: !!innerParam.batchAddRelatedObject,
              btns: btns,
              showDetail: false,
              saveAndCompleteTask: function(e, full) {
                  isComplete = true;
                  if(full){
                      full._continue = false;
                      full.view && full.view.submit(e) ;
                  }
              },
              success: function(data, opt) {
                if(param.draftId && param.useDraft) {
                  // 使用了草稿
                  innerParam.callback && innerParam.callback([_.extend(opt, {
                    id: opt._id,
                    value: opt.name || '',
                    isComplete: isComplete
                  })]);
                } else {
                  // 没有使用草稿
                  innerParam.callback && innerParam.callback([_.extend(data, {
                    id: data._id,
                    value: data.name || '',
                    isComplete: isComplete
                  })]);
                }
              },
              destroyCallback: function(data) {
                innerParam && innerParam.error && innerParam.error()
              }
          }, crmParam)
          if(param.draftId && param.useDraft) {
            CRM.api.use_draft(args)
          } else {
            action.add && action.add(args);
          }
            triggerBPM();
        })
    }

    function setRelation(id, item, callback) {
        util.FHHApi({
            url: '/EM1HNCRM/API/v1/object/' + item.ref_object_api_name + '/action/BulkAssociate',
            data: {
                associate_obj_id: innerParam.dataId,
                associate_obj_api_name: innerParam.apiname,
                associated_obj_api_name: item.ref_object_api_name,
                associated_obj_related_list_name: item.related_list_name,
                associated_obj_ids: [id]
            },
            success: function(res) {
                if (res.Result.StatusCode === 0) {
                    callback && callback();
                }
            }
        }, {
            errorAlertModel: 1
        })
    }

    function addOrPickData() {
      let hasUnDeleteDraft =  false
      waiting(' ');
      getDraftId(bizTypeMap[innerParam.formModel], innerParam.bizId).then((draft) => {
        let draftId = draft && draft.draftId || ''
        let useDraft = draft && draft.useDraft || false
        if(draft && draft.action === 'breakOff') {
          innerParam.callback && innerParam.callback({breakOff: true})
          triggerBPM();
          return
        }
        fetchDetail(function(data, ac) {
          ac = ac || innerParam;
          var tmp = {
              apiname: ac.ref_object_api_name
          }
          tmp.draftId = draftId
          tmp.useDraft = useDraft
          if (ac.type === 'multi_table') {
              tmp.relationData = {
                  target_api_name: data.object_describe_api_name,
                  id: data._id,
                  value: data.name
              }
              tmp.data = {}
              _createRelationData(tmp);
              return;
          }
          canAdd(ac, function(isAdd) {
            if (!isAdd) { //不满足新建条件
                waiting(false);
                isHanding = null;
                return;
            };
            _fetchStaticDataAndWheres(data, ac, function(fieldData) {
                if (!fieldData) {
                    waiting(false);
                    isHanding = null;
                    return;
                }
                _hackFieldData(innerParam.apiname + ac.ref_object_api_name, data, fieldData);
                if (innerParam.onlyAdd || innerParam.batchAddRelatedObject || draftId && useDraft) {
                    tmp.data = fieldData;
                    _createRelationData(tmp);
                    return;
                }
                require.async('crm-modules/components/pickselfobject/pickselfobject', function(PickSelf) {
                    waiting(false);
                    isHanding = null;
                    let isMultiple = true;
                    if(innerParam.formModel === 'bpm'){
                        isMultiple = false
                    }
                    var _pickobject = new PickSelf({
                        isMultiple: isMultiple,
                    });
                    fieldData.bpmBizType = bizTypeMap[innerParam.formModel]
                    fieldData.bpmBizId = innerParam.bizId
                    if(tmp.draftId && !tmp.useDraft) {
                      hasUnDeleteDraft =  true
                      fieldData.optionsCommonExtend = {
                        saveDraftCompleteCallback: () => {
                          if(hasUnDeleteDraft) {
                            PaasUI.utils.api.deleteDraft([tmp.draftId], innerParam.apiname).then(() => {
                              hasUnDeleteDraft = false
                            })
                          }
                        }
                      }
                    }
                    _pickobject.render({
                        apiname: ac.ref_object_api_name,
                        relatedname: ac.related_list_name,
                        formFillData: fieldData || null,
                        beforeRequest: function(param) {
                            if(innerParam.formModel === 'rocket'){
                                param.associate_object_data_id = innerParam.dataId;
                                param.associate_object_describe_api_name = innerParam.apiname;
                                if(_.isBoolean(innerParam.include_associated) ) {
                                    param.include_associated = innerParam.include_associated;
                                    param.from_associate_range = innerParam.include_associated ? 'associated' : 'notAssociated'
                                } else {
                                    param.include_associated = undefined
                                    param.from_associate_range = 'all'
                                }
                            } else {
                                if(_.isBoolean(innerParam.include_associated) ) {
                                    param.associate_object_data_id = innerParam.dataId;
                                    param.associate_object_describe_api_name = innerParam.apiname;
                                    param.include_associated = _.isBoolean(innerParam.include_associated) ? innerParam.include_associated: true;
                                } else {
                                    param.associate_object_data_id = undefined;
                                    param.associate_object_describe_api_name = undefined;
                                    param.include_associated = undefined
                                }
                            }



                            // let json = JSON.parse(param.search_query_info);
                            // let queryList = [];
                            // if(json.filters.length){
                            //     json.filters.forEach(e => {
                            //         queryList[e.filterGroup || 0] = queryList[e.filterGroup || 0] || [];
                            //         queryList[e.filterGroup || 0].push(e);
                            //     })
                            // } else {
                            //     queryList[0] = [];
                            // }
                            //
                            //
                            // let orQueryList;
                            // if(innerParam.include_associated === 'all'){
                            //     orQueryList = util.deepClone(queryList);
                            // }
                            // queryList.forEach((e, index) => {
                            //     if(e === undefined) return;
                            //     if(_.isBoolean(innerParam.include_associated) && innerParam.include_associated ){
                            //         e.push({"field_name":"field_8Ay2F__c","field_values":[innerParam.dataId],"operator":"EQ",filterGroup:index+''});
                            //     } else if (_.isBoolean(innerParam.include_associated) && !innerParam.include_associated){
                            //         e.push({"field_name":"field_8Ay2F__c","field_values":[""],"operator":"IS",filterGroup:index+''});
                            //     } else {
                            //         e.push({"field_name":"field_8Ay2F__c","field_values":[innerParam.dataId],"operator":"EQ",filterGroup:index + ''});
                            //         orQueryList[index].push({"field_name":"field_8Ay2F__c","field_values":[""],"operator":"IS",filterGroup:index+ queryList.length + ''})
                            //     }
                            // })
                            // queryList = queryList.concat(orQueryList || []);
                            // let result = [];
                            // queryList.forEach(e => {
                            //     result = result.concat(e || []);
                            // })
                            // // //param.include_associated = _.isBoolean(innerParam.include_associated) ? innerParam.include_associated : undefined;
                            // json.filters = result
                            // param.search_query_info = JSON.stringify(json);
                            return param;
                        },
                        beforeAddSuccessRefresh: ac.relationType == 4 && function(id, ac, next) {
                            setRelation([id], next);
                        }
                    });
                    _pickobject.once('select', function(objs) {
                        if(!Array.isArray(objs)){
                            objs = [objs];
                        }
                        innerParam.callback && innerParam.callback(objs);
                    });
                    _pickobject.on('destroy', function() {
                        innerParam.destroy && innerParam.destroy();
                    });
                    // 阶段推进器要取不关联的数据，新建后直接调回调函数
                    if (innerParam.include_associated === false || innerParam.include_associated === 'all') {
                        _pickobject.once('add', function(obj) {
                            innerParam.callback && innerParam.callback([obj]);
                            _pickobject.destroy();
                        });
                    }

                    triggerBPM();
                });
            })
          })
        })
      })
    }

	function showLog(param) {
		waiting(' ');
        PAAS.fetch_describe(param.apiname).then((describe)=> {
            PAAS.get_component('ObjectDetailOperationLog')().then((Comp) => {
                isHanding = false;
                waiting(false);
                let dialog = FxUI.create({
                    template: `<fx-dialog
                          :visible.sync="dialogVisible"
                          :title="param.title || $t('节点编辑信息')"
                          max-height="500px"
                          :append-to-body="true"
                          @closed="handler"
                        >
                        <div ref="body">
                            <ObjectDetailOperationLog
                                  :apiName="apiname"
                                  :dataId="dataId"
                                  :bizIds="bizIds"
                                  :otherBizIds="otherBizIds"
                                  :compInfo="{fields:describe.objectDescribe.fields,data:{}}"
                                  :hooks="hooks"
                                  ></ObjectDetailOperationLog>
                        </div>
                        <div slot="footer" >
                            <fx-button @click="dialogVisible = false" size="small">{{$t('我知道了')}}</fx-button>
                        </div>
                      </fx-dialog>`,
                    components:{
                        ObjectDetailOperationLog: Comp.default
                    },
                    data() {
                        return {
                            param,
                            dialogVisible: true,
                            apiname: param.apiname,
                            dataId: param.dataId,
                            bizIds: param.bizIds || [],
                            otherBizIds: param.otherBizIds || [],
                            describe,
                            hooks: {
                                parseData(data) {
                                    const policyApis = CRM.api.getWorkflowSpecialApis();
                                    const policyFields = CRM.api.getWorkflowSpecialFields();
                                    if (param.apiname === 'RebatePolicyObj') {
                                        _.each(data, (item) => {
                                            _.each(item.objectData, (odItem) => {
                                                const parse = CRM.api.rebatePolicyUtils.parseMasterFieldValue;
                                                odItem.value = parse(odItem.value);
                                                odItem.oldValue = parse(odItem.oldValue);
                                            })
                                        })
                                    }else if(policyApis.includes(param.apiname)){
                                        data.forEach(item=>{
                                            item.objectData.forEach(odItem=>{
                                                const {fieldApiName,value,oldValue  } = odItem;
                                                if(policyFields.includes(fieldApiName)){
                                                    const info = {
                                                        describe_api_name:param.apiname,
                                                        api_name:fieldApiName
                                                    };
                                                    odItem.value = {
                                                        [fieldApiName]:CRM.api.parsePolicyDataToText(info,value[fieldApiName])  
                                                    };
                                                    odItem.oldValue = {
                                                        [fieldApiName]:CRM.api.parsePolicyDataToText(info,oldValue[fieldApiName])
                                                    };
                                                }
                                            })
                                        })
                                    }else if(param.apiname === 'PricePolicyObj'){
                                        data.forEach(item=>{
                                            item.objectData = item.objectData.filter(o=>o.fieldApiName !== "product_gift_data_json")
                                        })
                                    }
                                    return data;
                                }
                            }
                        }
                    },
                    methods: {
                        handler() {
                            param.callback && param.callback();
                            dialog.$destroy();
                            dialog = undefined;
                        }
                    }
                })
            })
        })
	}

    //待模块准备好后通知业务测
    function triggerBPM() {
        if (innerParam.formModel == 'rocket') {
            FS.MEDIATOR.trigger('crm.rocketrelation.load');
        } else if (innerParam.formModel == 'bpm') {
            FS.MEDIATOR.trigger('crm.bpmrelation.load');
        }
    }

    //opts: {apiname: '',dataId: '', listapiname: '', type: ''}
    return function(opts) {
        if (isHanding) return;
        isHanding = true;
        innerParam = opts;
        switch (opts.type) {
            case 'showMasterDetail':
                {
                    showMasterDetail(opts);
                    break;
                }
            case 'batchUpdateMasterDetail':
                {
                    batchUpdateMasterDetail(opts);
                    break;
                }
            case 'showLog':
                {
					showLog(opts)
                    break;
                }
            default:
                {
                    addOrPickData(opts);
                }
        }
    }
})
