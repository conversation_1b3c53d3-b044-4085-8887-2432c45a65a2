define(function (require, exports, module) {
	const PickData = require('crm-modules/api/pickdata/pickdata'),
		RebatePolicyObj = require('crm-modules/action/rebatepolicyobj/rebatepolicyobj'),
		Tools = require('../tools/tools');
	const { AplTable, advancedExpression } = RebatePolicyObj;

	const translate = function (obj) {
		if (Array.isArray(obj)) {
			obj.forEach(o => {
				translate(o);
			});
		} else {
			const keys = Object.keys(obj);
			keys.forEach(key => {
				const value = obj[key];
				if (key === 'label') {
					obj[key] = $t(value);
				} else {
					if (typeof value === 'object' && value !== null) {
						translate(value);
					}
				}
			});
		}
	};

	const cache = {};
	const booleanOptions = [
		{ label: $t('是'), value: true },
		{ label: $t('否'), value: false },
	];
	// translate(booleanOptions);

	const getExpression = function (metric_info, fieldsParams) {
		const component = getExpressionComponent();
		const varData = getExpressionVarData(metric_info, fieldsParams);

		return Promise.all([component, varData]);
	};

	const getExpressionComponent = function () {
		if (cache.comp) {
			return Promise.resolve(cache.comp);
		}
		return advancedExpression.getExpressionCompFile();
	};

	const getExpressionVarData = function (metric_info, fieldsParams) {
		if (cache.ExpressionVarData) {
			return Promise.resolve(cache.ExpressionVarData);
		}
		const { program_id, used_object_api_name } = fieldsParams;
		const api_name = 'TransactionEventObj';
		const transaction_event_obj = CRM.util.getFieldsByDescribe('TransactionEventObj');
		const filters = Tools.getMetricObjFilters(used_object_api_name, program_id);
		const param = {
			include_describe: false,
			search_query_info: JSON.stringify({ filters, limit: 2000, offset: 0 }),
		};
		const incentive_metric_obj = CRM.util.fetchObjRelatedList('IncentiveMetricObj', param);
		const promiseArr = [transaction_event_obj, incentive_metric_obj];
		if (!metric_info) {
			promiseArr.pop();
		}
		return Promise.all(promiseArr).then(res => {
			const [transaction, metric] = res;
			//todo 之后支持查看更多
			const metricObjList = metric ? formatMetricList(metric.dataList, metric_info) : [];
			const transactionFields = formatTransactionObj({
				resFields: transaction[api_name],
				metric_info,
				// 交易事件
				label: $t('crm_action_incentivepolicyruleobj_10'),
				api_name,
			});
			return transactionFields.concat(metricObjList);
		});
	};

	const formatMetricList = (list, metric_info) => {
		const children = list
			.filter(item => {
				const metric_info = JSON.parse(item.metric_info);
				return metric_info.type === 'number' || item._id === metric_info.metric_id;
			})
			.map(item => {
				const { name, _id } = item;
				return {
					label: name,
					value: `EXT#METRIC#${_id}`,
					is_agg: false,
				};
			});
		return {
			api_name: 'IncentiveMetricObj',
			// 激励指标
			menu_name: $t('crm_action_incentivepolicyruleobj_11'),
			children: children,
		};
	};

	const formatTransactionObj = ({ resFields, metric_info, label, api_name }) => {
		const fields = {};
		Object.keys(resFields).forEach(key => {
			if (resFields[key].define_type === 'system') {
				return;
			} else {
				fields[key] = resFields[key];
			}
		});
		const temp = advancedExpression.parseExpressionDataForSelect({
			api_name,
			label,
			fields,
		});
		const ret = [
			{
				api_name: temp.api_name,
				menu_name: temp.label,
				children: temp.children,
			},
		];
		// if (metric_info) {
		// 	const { metric_id, metric_name } = metric_info;
		// 	ret.push({
		// 		api_name: 'IncentiveMetricObj',
		// 		menu_name: $t('激励指标'),
		// 		children: [
		// 			{
		// 				label: metric_name,
		// 				value: `EXT#METRIC#${metric_id}`,
		// 				is_agg: false,
		// 			},
		// 		],
		// 	});
		// }
		return ret;
	};

	const validate = (rule, value, callback) => {
			if (rule.required && !value) {
				// 请输入{{label}}
				const value = $t('crm_action_incentivepolicyruleobj_12', { label: rule.label });
				callback(value);
			} else {
				callback();
			}
		},
		renderTable = function (options) {
			return new PickData(options);
		},
		renderAplTable = function (options) {
			const { el } = options;
			const name_space = ['incentive_rule_execute'];
			return new AplTable({ el, options: { name_space } }).table;
		},
		renderExpression = function ({ options, model, fieldsParams }) {
			const { el, submit } = options;
			const { value = '', action_type, metric_info = {} } = model;
			let metric_props;
			if (action_type === 'metric' && metric_info.metric_id) {
				metric_props = {
					metric_id: metric_info.metric_id,
					metric_name: metric_info.metric_name,
				};
			}
			CRM.util.showLoading_new();
			const return_type = [
				{
					// 数值
					label: 'crm_action_incentivepolicyruleobj_13',
					value: 'number',
				},
				{
					// 布尔
					label: 'crm_action_incentivepolicyruleobj_14',
					value: 'true_or_false',
				},
			];
			translate(return_type);
			getExpression(metric_props, fieldsParams)
				.then(res => {
					const [{ Expression, Cascader }, varData] = res;
					FxUI.create({
						wrapper: el,
						template: `
					<fx-dialog
						:title="$t('高级公式')"
						:visible="visible"
						:z-index="zIndex"
						width="640px"
						@close="destroy"
						max-height="60vh"
					>
						<expression
							ref="rExpression"
							objectApiName="TransactionEventObj"
							:value="value"
							:parseParam="parseParamFn"
							:translateFn="translateFn"
							:varData="varData"
							:visible="true"
							:returnTypeList="return_type"
							@change="handleChange"
						>
							<cascader
								slot="insertField"
								ref="rCascader"
								trigger="click"
								:data="varData"
								:is-show-value="true"
								cascaderClass="crm-a-rebatepolicyobj-cycle-expression-cascader"
								@change="handleAddField"
								placement="bottom-start"
							>
								<fx-button slot="reference">{{ $t("插入字段") }}</fx-button>
							</cascader>
						</expression>
						<div slot="footer" class="dialog-footer">
							<fx-button type="primary" @click="confirm" size="small">{{ $t('确 定') }}</fx-button>
							<fx-button @click="destroy" size="small">{{ $t('取 消') }}</fx-button>
						</div>
					</fx-dialog>`,
						components: {
							Expression,
							Cascader,
						},
						data() {
							return {
								visible: false,
								zIndex: CRM.util.getzIndex() + 10,
								value: {
									expression: value,
								},
								varData,
								return_type,
							};
						},
						mounted() {
							this.visible = true;
							CRM.util.hideLoading_new();
						},
						computed: {
							oriData() {
								const { varData: data } = this;
								const useMap = new Map();
								data.map(item => item.children)
									.flat()
									.forEach(item => {
										const { label, value } = item;
										useMap.set(`$${value}$`, label);
									});
								return useMap;
							},
						},
						methods: {
							destroy() {
								this.visible = false;
								this.$destroy();
							},
							handleChange(data) {
								const { expression } = data;
								this.value = {
									...data,
									value_content: this.translateFn(expression),
								};
							},
							confirm() {
								const { rExpression } = this?.$refs;
								rExpression.validate((valid, obj) => {
									if (valid) {
										submit && submit(this.value);
										this.destroy();
									} else {
										const msg = obj?.message?.value || '';
										msg && CRM.util.error(msg);
									}
								});
							},
							parseParamFn(params) {
								const expression = JSON.parse(params.json_data)?.expression || '';
								let arr = [];
								const resArr = [];
								const regIsField = /\$EXT\#METRIC\#([a-zA-Z0-9_.-]*)\$/g;
								while ((arr = regIsField.exec(expression)) !== null) {
									resArr.push(arr[0]);
								}
								const ext_fields = resArr.map(i => {
									return {
										fieldName: i.replace(/\$/g, ''),
										// todo 之后需要按照属性的类型进行处理
										type: 'number',
									};
								});
								return {
									...params,
									ext_fields,
								};
							},
							varTransformFn(val) {
								if (Array.isArray(val)) {
									return val.map(item => this.oriData.get(item)).join('.');
								} else {
									return this.oriData.get(val);
								}
							},
							translateFn(expression = '') {
								const regxTransField = /\$([#a-zA-Z0-9_.-]*)\$/g;
								return expression.replace(regxTransField, word => {
									return this.varTransformFn(word) || [];
								});
							},
							handleAddField(val) {
								if (val.value[0] === 'load_more') {
									// 查看更多
									this.loadMoreData(val);
								} else {
									this.$refs['rExpression'].handleAddField(val);
								}
							},
						},
					});
				})
				.catch(res => {
					CRM.util.hideLoading_new();
				});
		},
		renderPointTable = function (options, model, renderTableParams) {
			const program_id = renderTableParams?.program_id || '';
			const filters = [
				{
					field_name: 'is_qualifying',
					field_values: [model.action_type === 'tiered_points'],
					operator: 'EQ',
				},
			];
			if (program_id) {
				filters.push({
					field_name: 'program_id.name',
					field_values: [program_id],
					operator: 'EQ',
				});
			}
			const beforeRequest = function (rq) {
				const queryInfo = JSON.parse(rq.search_query_info);
				queryInfo.filters.push(...filters);
				rq.search_query_info = JSON.stringify(queryInfo);
				return rq;
			};
			const param = {
				...options,
				beforeRequest,
			};
			return renderTable(param);
		},
		renderMemberLevelTable = function (options, model, renderTableParams) {
			const program_id = renderTableParams?.program_id || '';
			const filters = [
				{
					field_name: 'program_id.name',
					field_values: [program_id],
					operator: 'EQ',
				},
			];
			const beforeRequest = function (rq) {
				if (program_id) {
					const queryInfo = JSON.parse(rq.search_query_info);
					queryInfo.filters.push(...filters);
					rq.search_query_info = JSON.stringify(queryInfo);
				}
				return rq;
			};
			const param = {
				...options,
				beforeRequest
			};
			return renderTable(param);
		},
		renderMetricTable = function (options, model, renderTableParams) {
			const program_id = renderTableParams?.program_id || '';
			const filters = [
				{
					field_name: 'metric_type',
					// 暂时不支持字段类型
					field_values: ['attribute'],
					operator: 'HASANYOF',
				},
			];
			program_id &&
				filters.push({
					field_name: 'program_id.name',
					field_values: [program_id],
					operator: 'EQ',
				});
			const beforeRequest = function (rq) {
				const queryInfo = JSON.parse(rq.search_query_info);
				queryInfo.filters.push(...filters);
				rq.search_query_info = JSON.stringify(queryInfo);
				return rq;
			};
			const formatDataAsync = function (data) {
				const parseData = data.data || data.dataList || [];
				parseData.forEach(item => {
					item.metric_info_origin = JSON.parse(item.metric_info);
					item.metric_info = CRM.util.parseMetricInfo(item.metric_info, item.metric_type);
				});
				return Promise.resolve(data);
			};
			const data = model?.metric_info?.metric_id;
			const param = {
				...options,
				data: data ? [{ _id: data, name: data }] : null,
				beforeRequest,
				extendParam: {
					formatDataAsync,
				},
			};
			return renderTable(param);
		},
		renderCouponTable = function (options, model, fieldsParams) {
			const { program_id } = fieldsParams;
			const filters = [
				{
					field_name: 'program_id.name',
					field_values: [program_id],
					operator: 'EQ',
				},
				{
					field_name: 'active_status',
					field_values: ['enable'],
					operator: 'EQ',
				},
			];
			const beforeRequest = function (rq) {
				const queryInfo = JSON.parse(rq.search_query_info);
				queryInfo.filters.push(...filters);
				rq.search_query_info = JSON.stringify(queryInfo);
				return rq;
			};
			const param = {
				...options,
				beforeRequest,
			};
			return renderTable(param);
		},
		renderPoolTable = function (options, model, fieldsParams) {
			const { point_category_name } = model;
			if (!point_category_name) {
				// 请先选择积分分类
				return CRM.util.alert($t('crm_action_incentivepolicyruleobj_15'));
			}
			const filters = [
				{
					field_name: 'point_type_id.name',
					field_values: [point_category_name],
					operator: 'EQ',
				},
			];
			const { org_id } = fieldsParams;
			if (org_id) {
				filters.push({
					field_name: 'org_id',
					field_values: [org_id],
					operator: 'EQ',
				});
			}
			const beforeRequest = function (rq) {
				const queryInfo = JSON.parse(rq.search_query_info);
				queryInfo.filters.push(...filters);
				rq.search_query_info = JSON.stringify(queryInfo);
				return rq;
			};
			const param = {
				...options,
				beforeRequest,
			};
			return renderTable(param);
		},
		shouldShowValueByData = function (model) {
			let res = false;
			const action_type_value_set = new Set(['consumer_points', 'tiered_points', 'level', 'metric']);
			if (action_type_value_set.has(model.action_type)) {
				res = true;
			}
			//todo 调整类型隐藏的时候不需要走这条逻辑
			const change_type_value_set = new Set(['flow_event']);
			if (change_type_value_set.has(model.change_type)) {
				res = false;
			}
			return res;
		},
		getValueTypeByModel = function ({ model }) {
			const { action_type, metric_info = {} } = model;
			if (action_type !== 'metric') {
				return 'input';
			}
			const type = metric_info?.info?.type || '';
			switch (type) {
				case 'true_or_false':
				case 'select_one':
				case 'select_many':
					return 'select';
				case 'date':
					return 'date';
				case 'number':
				default:
					return 'input';
			}
		},
		getOptionsByMetric = function (metric_type, metric_info, fieldsParams) {
			const { metricFieldsMap = new Map() } = fieldsParams;
			switch (metric_type) {
				case 'attribute':
					switch (metric_info.type) {
						case 'select_one':
						case 'select_many':
							return metricFieldsMap.get('attributeOptions')?.[metric_info.attribute_id] || [];
						case 'true_or_false':
							return booleanOptions;
					}
				case 'field':
					break;
				default:
					return [];
			}
		},
		getExtendedAttributeFieldDescribe = function (id) {
			const apiname = 'ExtendedAttributeObj';
			const search_query_info = {
				limit: 20,
				offset: 0,
				filters: [
					{
						field_name: '_id',
						field_values: [id],
						operator: 'EQ',
					},
				],
			};
			const param = {
				include_layout: false,
				include_describe: false,
				search_query_info: JSON.stringify(search_query_info),
			};
			return CRM.util.fetchObjRelatedList(apiname, param, false).then(res => {
				if (res && res.dataList && res.dataList.length > 0) {
					const attribute_data = res.dataList[0];
					const field_describe =
						typeof attribute_data.field_describe === 'string'
							? JSON.parse(attribute_data.field_describe)
							: attribute_data.field_describe;
					return field_describe;
				}
			});
		},
		needRequestFullAttributedata = function (type) {
			return ['number'].includes(type);
		},
		getDefaultPoolIdByOrgId = function (org_id) {
			CRM.util.showLoading_tip();
			const params = {
				org_id,
			};
			const request = new Promise((resolve, reject) => {
				CRM.util.FHHApi({
					// 接口文档 https://wiki.firstshare.cn/pages/viewpage.action?pageId=393645662
					url: '/EM1HNCRM/API/v1/object/loyalty/service/get_default_point_pool',
					data: params,
					success: res => {
						if (res.Result.StatusCode === 0) {
							const data = res?.Value?.data || false;
							data && resolve(data);
						}
					},
				});
			});
			const race = new Promise((resolve, reject) => {
				setTimeout(() => {
					resolve();
				}, 5000);
			});
			return Promise.race([request, race]).then(res => {
				CRM.util.hideLoading_tip();
				return res;
			});
		};

	/******************************** 分割线 ********************************/

	const incentiveActionFields = {
		name: {
			// 动作名称
			label: 'sfa_crm_action_incentivepolicyruleobj_label_action_name',
			type: 'input',
			show: true,
			required: true,
		},
		active_status: {
			// 启用状态
			label: 'sfa_crm_action_incentivepolicyruleobj_label_active_status',
			type: 'select',
			show: true,
			options: [
				// 启用
				{ label: 'sfa_crm_action_incentivepolicyruleobj_label_enable', value: 'enable' },
				// 禁用
				{ label: 'sfa_crm_action_incentivepolicyruleobj_label_disable', value: 'disable' },
			],
			required: true,
		},
		action_type: {
			// 动作类型
			label: 'sfa_crm_action_incentivepolicyruleobj_label_action_type',
			type: 'select',
			show: true,
			options: function ({ fieldsParams }) {
				const default_options = [
					// 调整消费积分
					{ label: 'crm_action_incentivepolicyruleobj_24', value: 'consumer_points' },
					// 调整定级积分
					{ label: 'crm_action_incentivepolicyruleobj_25', value: 'tiered_points' },
					// 调整会员等级
					{ label: 'crm_action_incentivepolicyruleobj_26', value: 'level' },
					// 调整会员等级名称
					{ label: 'crm_action_incentivepolicyruleobj_27', value: 'level_name' },
					// 设置激励指标
					{ label: 'crm_action_incentivepolicyruleobj_28', value: 'metric' },
					// APL函数
					{ label: 'sfa_crm_action_incentivepolicyruleobj_label_function_apl', value: 'apl' },
				];
				if (fieldsParams.haveCoupon || fieldsParams.isDisplay) {
					default_options.push({
						// 发放优惠券
						label: 'SFA.CRM.IncentivePolicyObj.field.issue_coupons.label',
						value: 'issued_coupon',
					});
				}
				translate(default_options);
				return default_options;
			},
			required: true,
			operate: {
				beforeSetValue: function (value, callback, { model = {}, fields = {}, fieldsParams = {} }) {
					const { point_category, pool } = fields;
					const { org_type = '', org_id = '' } = fieldsParams;
					// 调整动作类型后需要清空积分分类
					const multiField = point_category?.reference?.multiField || [];
					multiField.forEach(field => {
						callback(field, null);
					});
					if (value === 'apl' || value === 'metric') {
						callback('change_type', '');
					}
					callback('value_type', 'fixed');
					callback('point_category', '');
				},
			},
		},
		beneficiary: {
			// 受益人类型
			label: 'sfa_crm_action_incentivepolicyruleobj_label_beneficiary',
			type: 'select',
			show: true,
			options: function () {
				const default_options = [
					// 本人
					{
						label: 'crm_action_incentivepolicyruleobj_self',
						value: 'self',
					},
					// 直接上级
					{
						label: 'crm_action_incentivepolicyruleobj_supervisor',
						value: 'superior',
					},
				];
				translate(default_options);
				return default_options;
			},
			operate: {
				initModel: () => 'self',
			},
		},
		change_type: {
			// 调整类型
			label: 'crm_action_incentivepolicyruleobj_16',
			type: 'select',
			show: {
				action_type: ['consumer_points', 'tiered_points', 'level', 'level_name'],
			},
			options: function ({ model }) {
				const default_options = [
					// 上升
					{ label: 'crm_action_incentivepolicyruleobj_17', value: 'up' },
					// 下降
					{ label: 'crm_action_incentivepolicyruleobj_18', value: 'down' },
				];
				// 跟随事件
				const special_options = [{ label: 'crm_action_incentivepolicyruleobj_19', value: 'flow_event' }];
				// 固定值
				const action_type_level_options = [{ label: 'crm_action_incentivepolicyruleobj_20', value: 'fixed' }];
				let result = [];
				switch (model.action_type) {
					case 'level_name':
						result = [...special_options, ...action_type_level_options];
						break;
					default:
						result = [...default_options, ...special_options];
				}
				translate(result);
				return result;
			},
			required: true,
		},
		point_category: {
			// 积分分类
			label: 'crm_action_incentivepolicyruleobj_21',
			type: 'input',
			valueType: 'reference',
			show: function (model) {
				let isShow = false;
				if (model.action_type === 'tiered_points' || model.action_type === 'consumer_points') {
					isShow = true;
				}
				if (model.change_type === 'flow_event') {
					isShow = false;
				}
				return isShow;
			},
			readonly: true,
			required: true,
			reference: {
				apiName: 'LoyaltyPointTypeObj',
				renderTable: renderPointTable,
				multiField: ['point_category_name', 'point_category_id'],
				renderTableParams: null,
				convertToString: function (data) {
					if (!data) return '';
					return data.point_category_name;
				},
				format: function (data) {
					const {
						selected: { name, id },
					} = data;
					return { point_category_name: name, point_category_id: id };
				},
				setValue: function (data, callback) {
					const keys = Object.keys(data);
					keys.forEach(key => {
						callback(key, data[key]);
					});
					callback('point_category', data.point_category_name);
				},
			},
			operate: {
				beforeSetValue: function (value, callback, { model }) {
					const { point_category, action_type } = model;
					if (model.point_category !== value && action_type === 'consumer_points') {
						// 更改积分分类将清空积分池，是否继续？
						CRM.util.confirm($t('crm_action_incentivepolicyruleobj_22'), null, function () {
							callback('pool_id', '');
							callback('pool_name', '');
						});
					}
				},
			},
		},
		coupon_plan: {
			// 优惠券方案
			label: 'SFA.CRM.IncentivePolicyObj.field.coupon_program.label',
			type: 'input',
			valueType: 'reference',
			required: true,
			readonly: true,
			showLabel: 'coupon_plan_name',
			show: function (model, params) {
				const { action_type } = model;
				return action_type === 'issued_coupon';
			},
			reference: {
				apiName: 'CouponPlanObj',
				multiField: ['coupon_plan_name', 'coupon_plan_id'],
				renderTable: renderCouponTable,
				format: function (data) {
					const {
						selected: { name, id },
					} = data;
					return { coupon_plan_name: name, coupon_plan_id: id };
				},
				convertToString: function (data) {
					if (!data) return '';
					return data.coupon_plan_name;
				},
			},
			operate: {
				beforeSetValue: function (value, callback) {
					callback('coupon_plan_id', value.coupon_plan_id);
					callback('coupon_plan_name', value.coupon_plan_name);
				},
			},
		},
		pool: {
			// 积分池
			label: 'IncentivePolicyObj.field.default_pool_id.label',
			type: 'input',
			valueType: 'reference',
			// required: true,
			show: function (model, params) {
				const { action_type, change_type } = model;
				const { org_type } = params;
				return action_type === 'consumer_points' && org_type === 'org' && change_type !== 'flow_event';
			},
			showLabel: 'pool_name',
			reference: {
				apiName: 'LoyaltyPointPoolObj',
				multiField: ['pool_name', 'pool_id'],
				renderTable: renderPoolTable,
				format: function (data) {
					const {
						selected: { name, id },
					} = data;
					return { pool_name: name, pool_id: id };
				},
			},
			operate: {
				beforeSetValue: function (value, callback) {
					callback('pool_id', value.pool_id);
					callback('pool_name', value.pool_name);
				},
			},
		},
		metric_info: {
			// 激励指标
			label: 'crm_action_incentivepolicyruleobj_11',
			type: 'input',
			valueType: 'reference',
			show: {
				action_type: ['metric'],
			},
			readonly: true,
			required: true,
			reference: {
				apiName: 'IncentiveMetricObj',
				renderTable: renderMetricTable,
				format: function (data, params) {
					const {
						selected: { metric_info_origin, metric_type, name, id },
					} = data;
					const ret = {
						metric_id: id,
						metric_name: name,
						info: metric_info_origin,
						type: metric_type,
					};
					return ret;
				},
				convertToString: function (data) {
					if (!data) return '';
					return data?.metric_info?.metric_name;
				},
				setValue(data, callback, { model, fieldsParams, field }) {
					if (data.type === 'attribute' && needRequestFullAttributedata(data?.info?.type)) {
						getExtendedAttributeFieldDescribe(data.info.attribute_id).then(res => {
							const describe = res?.[data?.info?.attribute_field_api_name];
							data.component_props = {
								integerPlaces: describe.length || 12,
								decimalPlaces: describe.decimal_places || 2,
							};
							callback('metric_info', data, field);
						});
					} else {
						callback('metric_info', data, field);
					}
				},
			},
			operate: {
				beforeSetValue(value, callback) {
					callback('value', '');
					callback('value_label', '');
					callback('value_content', '');
				},
			},
		},
		member_level: {
			// 会员等级
			label: 'sfa_crm_action_incentivepolicyruleobj_label_membership_level',
			type: 'input',
			valueType: 'reference',
			show: function (model) {
				let isShow = false;
				if (model.action_type === 'level_name') {
					isShow = true;
				}
				if (model.change_type === 'flow_event') {
					isShow = false;
				}
				return isShow;
			},
			required: true,
			readonly: true,
			reference: {
				apiName: 'LoyaltyTierObj',
				renderTable: renderMemberLevelTable,
				multiField: ['member_level_name', 'member_level_id'],
				convertToString: function (data) {
					if (!data) return '';
					return data.member_level_name;
				},
				format: function (data) {
					const {
						selected: { name, id },
					} = data;
					return { member_level_name: name, member_level_id: id };
				},
				setValue: function (data, callback) {
					const keys = Object.keys(data);
					keys.forEach(key => {
						callback(key, data[key]);
					});
					callback('member_level', data.member_level_name);
				},
			},
		},
		apl_info: {
			// 自定义函数
			label: 'sfa_crm_action_incentivepolicyruleobj_label_custom_function',
			type: 'input',
			valueType: 'reference',
			show: {
				action_type: ['apl'],
			},
			required: true,
			readonly: true,
			reference: {
				type: 'customize',
				renderTable: renderAplTable,
				convertToString: function (data) {
					return data?.apl_info?.name || '';
				},
				format: function (data) {
					const { api_name, function_name, return_type, binding_object_api_name, name_space } = data[0];
					return {
						api_name,
						name: function_name,
						return_type,
						bind_object: binding_object_api_name,
						name_space,
					};
				},
			},
		},
		value_type: {
			// 结果值类型
			label: 'crm_action_incentivepolicyruleobj_23',
			type: 'select',
			show: shouldShowValueByData,
			options: function ({ model, field, fieldsParams }) {
				const origin = [
					// 固定值
					{ label: $t('crm_action_incentivepolicyruleobj_20'), value: 'fixed' },
					{ label: $t('高级公式'), value: 'expression' },
				];
				const { metric_info, action_type } = model;
				metric_info?.type === 'attribute' &&
					action_type === 'metric' &&
					metric_info?.info?.type !== 'number' &&
					origin.pop();
				// translate(origin);
				return origin;
			},
			operate: {
				beforeSetValue: function (value, callback) {
					// 修改结果值类型后需要清空结果
					callback('value', '');
					callback('value_label', '');
					callback('value_content', '');
				},
			},
			required: true,
		},
		value: {
			// 结果
			label: 'sfa_crm_action_incentivepolicyruleobj_label_result',
			type: getValueTypeByModel,
			show: shouldShowValueByData,
			valueType: function (model) {
				const { value_type, metric_info } = model;
				let type = 'text';
				if (value_type === 'expression') {
					type = 'function';
				} else {
					const metric_info_type = metric_info?.info?.type || '';
					if (metric_info_type === 'text') {
						type = 'text';
					} else {
						type = 'number';
					}
				}
				return type;
			},
			setComponentProps: function ({ model, config, fieldsParams }) {
				if (
					model.action_type === 'metric' &&
					config.props.type === 'number' &&
					model?.metric_info?.info?.type === 'number'
				) {
					const component_props = model?.metric_info?.component_props || {};
					config.props = {
						...config.props,
						...component_props,
					};
				}
			},
			showLabel: 'value_content',
			options: function ({ model, fieldsParams }) {
				const type = model?.metric_info?.info?.type || '';
				switch (type) {
					case 'true_or_false':
						return booleanOptions;
					case 'select_one':
					case 'select_many':
						const { metric_info: { type: metric_type = '', info = {} } = {} } = model || {};
						return getOptionsByMetric(metric_type, info, fieldsParams);
					default:
						return [];
				}
			},
			reference: {
				render: renderExpression,
				convertToString: function (data) {
					const { value } = data;
					return value;
				},
				format: function (data) {
					const { value, value_content } = data;
					return { value, value_content };
				},
				multiField: ['value', 'value_content'],
			},
			operate: {
				beforeSetValue: function (value, setValue, { model, fieldsParams }) {
					// 首先先区分高级公式和固定值 再区分不同类型
					const type = model?.metric_info?.info?.type || '';
					const value_type = model.value_type;
					if (value_type === 'expression') {
						setValue('value_content', value.value_content);
						setValue('value', value.expression);
						return true;
					}
					if (['select_one', 'select_many', 'true_or_false'].includes(type)) {
						const options = getOptionsByMetric(
							model.metric_info.type,
							model.metric_info.info,
							fieldsParams
						);
						const selected = options.find(option => option.value === value);
						setValue('value_content', selected?.label || '');
					}
				},
				beforeMount: function ({ model, setValue }) {
					const { metric_info } = model;
					if (metric_info?.type === 'attribute' && needRequestFullAttributedata(metric_info?.info?.type)) {
						getExtendedAttributeFieldDescribe(metric_info.info.attribute_id).then(res => {
							const describe = res?.[metric_info?.info?.attribute_field_api_name];
							const component_props = {
								integerPlaces: describe.length || 12,
								decimalPlaces: describe.decimal_places || 2,
							};
							metric_info.component_props = component_props;
							// setValue('metric_info', Object.assign({}, metric_info, { component_props }));
						});
					}
				},
				initModel: function ({ values, field, fieldsParams }) {
					const { action_type, metric_info = {}, value } = values;
					if (action_type === 'metric') {
						if (metric_info?.info?.type === 'true_or_false') {
							return value === 'true' || value === true;
						} else if (metric_info?.info?.type === 'date') {
							return values.value;
						}
					}
					return values.value;
				},
			},
			required: true,
		},
		quantity: {
			// 数量
			label: 'SFA.CRM.IncentivePolicyObj.field.quantity.label',
			type: 'input',
			valueType: 'number',
			show: {
				action_type: ['issued_coupon'],
			},
			decimalPlaces: 0,
			isPositiveNum: true,
			required: true,
		},
	};

	translate(incentiveActionFields);

	module.exports = {
		incentiveActionFields,
	};
});
