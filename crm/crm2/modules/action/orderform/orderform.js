/**
 * @fileOverview 订单新建编辑
 * <AUTHOR>
 */

define(function(require, exports, module) {
    var util = require('crm-modules/common/util'),
        Delivery = require('./delivery/delivery'),
        Reject = require('./reject/reject'),
        View = require('./view/view'),
        Model = require('./model/model'),
        Form = require('./form/form'),
        Common = require('crm-modules/action/common/common'),
        moment = CRM.util.moment;

	const DEALERVERSION = ['dealer_edition', 'advanced_dealer_edition'];

    var MyModel = Model.extend({
        parse: function () {
            var obj = Model.prototype.parse.apply(this, arguments);
            obj.data.order_time = obj.data.order_time || new Date(new Date().toDateString()).getTime(); // 下单日期为当天的0点
            return obj;
        }
    });

    var Order = Common.extend({

        objectType: 11,

        cacheConfing: function(config) {
            CRM._cache = _.extend(CRM._cache || {}, {
                // openPriceList: (_.findWhere(config, {key: '28'}) || {}).value === '1',
                // priceBookPriority: (_.findWhere(config, {key: 'enforce_priority'}) || {}).value === '1',
                // openAvailablerange: (_.findWhere(config, {key: 'available_range'}) || {}).value === '1',
				// ignore_price_book_valid_period: (_.findWhere(config, {key: 'ignore_price_book_valid_period'}) || {}).value === '1',
				// match_price_book_valid_field: (_.findWhere(config, {key: 'match_price_book_valid_field'}) || {}).value,
				isThirdOrder: (_.findWhere(config, {key: '49'}) || {}).value === '1',
                recent_order: (_.findWhere(config, {key: 'recent_order'}) || {}).value === '1',
            });
        },

		_getVersionInfo(){
			return new Promise((resolve) => {
				CRM.control._getVersionInfo(version  => {
					resolve(version);
				});
			});
		},

        //是否是第三方行业订单
        isThirdOrder: function(callback) {
            var me = this;
            // 改为获取全部配置
            // util.getConfigValues(['49', '46', '50', '28', 'enforce_priority', 'available_range', 'ignore_price_book_valid_period', 'match_price_book_valid_field', 'recent_order'])

			Promise.all([util.getCrmAllConfig(), me._getVersionInfo()])
			.then(function ([values, version]) {
				var configObj = {};
                _.each(values.crmAllConfig, function(a) {
                    configObj[a.key] = a.value;
                });
				configObj.crmVersion = version;
                me.cacheConfing(values.crmAllConfig);
                callback(configObj);
            }, function(msg){
                util.alert(msg);
            }).catch(err => {});
        },

        //跳转到行业订单，深研模块 对接人 池玲 林惠
        toThirdOrder: function(obj) {
            var me = this;
            require.async('crm-frurl/salesorderobj/salesorderobj', function(ThirdOrder) {
                me.thirdOrder && me.thirdOrder.destroy && me.thirdOrder.destroy();
                me.thirdOrder = new ThirdOrder({
                    data: obj
                })
            });
		},

        // 跳转到快消极简订单 对接人 hgl
        toSimpleOrder(obj){
            var me = this;
            require.async('crm-frurl/simplesalesorderobj/simplesalesorderobj', function(SimpleOrder) {
                me.simpleOrder && me.simpleOrder.destroy && me.simpleOrder.destroy();
                me.simpleOrder = new SimpleOrder({
                    data: obj
                })
            });
        },

		// 获取订单产品从对象的数据，用于找到是否参与促销
		getMDdata: function(obj, cb) {
			if (obj.config[46] === '1') {
				cb && cb();
				return;
			}
			if (obj.data.promotion_id) {
				util.alert($t("该订单已参加促销，不支持编辑，如有疑问请联系企业管理员！"));
				return;
			}
			util.waiting();
            var refname = 'SalesOrderProductObj';
            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/'+ refname +'/controller/RelatedList',
                data: {
                    associate_object_data_id: obj.dataId,
                    associate_object_describe_api_name: obj.apiname,
                    associated_object_describe_api_name: refname,
                    associated_object_field_related_list_name: 'order_id_list',
                    include_associated: true,
                    search_query_info: '{"limit":500,"offset":0}'
                },
                success: function(res) {
                    if (res.Result.StatusCode === 0) {
						var data = res.Value.dataList;
						var product = _.find(data, function(a) { return !!a.promotion_id });
						if (product) {
							util.alert($t("该订单已参加促销，不支持编辑，如有疑问请联系企业管理员！"));
							return;
						}
                        cb && cb();
                        return;
                    }
                    util.alert(res.Result.FailureMessage || $t("暂时无法获取订单产品数据")+'!'+$t("请稍后重试。"));
				},
				complete: function() {
					util.waiting(false);
				}
            }, {
                errorAlertModel: 1
            })
        },

        /**
         * @description 新建订单, 对外暴露的方法
         * @type {object} obj 新建订单传入对象
         */
        add: function(obj) {
            var me = this;
            obj = _.extend({
                apiname: 'SalesOrderObj',
                workFlowType: 2,
				className: 'crm-a-salesorderobj',
                success: function(data, model) {
                    me.trigger('refresh', 'add', data, data._id);
                    !data.__isContinue && obj.showDetail && me.showDetail({
                        id: data && data._id,
						apiname: 'SalesOrderObj',
						showMask: obj.showMask,
                    });

                    // 结算方式为预付
                    if (data.settle_type == '1' && model) {
                        // 新建回款
                        if (model.needCreatePayment) {
                            me.addPayment(data)
                        }
                        else if (model.needCreateOrderPayment) {
                            me.addOrderPayment(data);
                        }
                    }
                },
                error: function (data, obj) {
                    if (data && data.Result && data.Result.FailureCode == '48888888') {
                        util.alert(data.Result.FailureMessage, function () {
                            me.trigger('refresh');
                            obj.destroy && obj.destroy();
                        });
                    }
                }
            }, obj);
            this.setOrderTime(obj);
            this.isThirdOrder(function(config) {
				if(config[49] === '1') {
                    obj.type = 'add';
                    me.toThirdOrder(obj);
                }
				else if(DEALERVERSION.indexOf(config.crmVersion) > -1){
                    obj.type = 'add';
                    me.toSimpleOrder(_.extend({
						Model: MyModel
					}, obj));
                } 
				else {
                    require.async('crm-modules/action/field/field', function(field) {
                        field.add(_.extend({
                            View: View,
                            Model: MyModel
                        }, obj))
                    })
                }
            })
        },

        addPayment: function(data) {
            var me = this;
            require.async('crm-modules/action/paymentobj/paymentobj', function(Payment) {
               (me._addPayment || (me._addPayment = new Payment())).add({
                    nonEditable: true,
                    SettleType: '1',
                    data: {
                        CustomerID: data.account_id,
                        CustomerID__r: data.account_id__r,
                        CustomerTradeID: data._id,
                        CustomerTradeID__r: data.name || data.TradeCode,//兼容订单编号页面不可见的时候
                        PaymentMoney: data.order_amount,
                        PaymentType: '10000',
                        WaitPaymentMoney: data.order_amount,
                        TradeMoney: data.order_amount,
                        PaymentMoneyToConfirm: 0,
                        account_id: data.account_id,
                        account_id__r: data.account_id__r,
                        order_data_id: data._id,
                        order_id: data.name || data.TradeCode,
                        from_sales_order: true,
                        order_detail_data: data
                    }
               });
            })
        },

        addOrderPayment: function(data) {
            var me = this;
            require.async('crm-modules/action/orderpaymentobj/orderpaymentobj', function(OrderPayment) {
               (me._addOrderPayment || (me._addOrderPayment = new OrderPayment())).add({
                    nonEditable: true,
                    SettleType: '1',
                    data: {
                        account_id: data.account_id,
                        account_id__r: data.account_id__r,
                        order_id: data._id,
                        order_id__r: data.name
                    }
               });
            })
        },


        /**
         * @description 编辑订单
         * @param  {object} obj 编辑订单使用的数据对象，包括fieldData, tableData
         * productData 产品信息
         * productFieldList 产品的自定义字段
         */
        edit: function(obj) {
            var me = this;
            me.isThirdOrder(function(config) {
                if(config[49] === '1') {
					obj.type = 'edit';
					obj.config = config;
					me.getMDdata(obj, function() {
						me.toThirdOrder(_.extend({
							workFlowType: 2,
							apiname: 'SalesOrderObj',
							success: function(data) {
								me.trigger('refresh', 'edit', data);
							}
						}, obj))
					});
                }else if(DEALERVERSION.indexOf(config.crmVersion) > -1) {
                    obj.type = 'edit';
                    obj.config = config;
                    me.toSimpleOrder(_.extend({
                        workFlowType: 2,
                        apiname: 'SalesOrderObj',
                        className: 'crm-a-salesorderobj',
                        title: obj.title || $t("编辑销售订单"),
                        success: function(data) {
                            me.trigger('refresh', 'edit', data);
                        }
                    }, obj))
                } else {
                    require.async('crm-modules/action/field/field', function(field) {
                        field.edit(_.extend({
                            View: View,
                            Model: Model,
                            workFlowType: 2,
                            apiname: 'SalesOrderObj',
							className: 'crm-a-salesorderobj',
                            title: obj.title || $t("编辑销售订单"),
                            success: function(data) {
                                me.trigger('refresh', 'edit', data);
                            }
                        }, obj))
                    })
                }
            })
		},

        /**
         * @desc 删除订单
         * @param {{Array}} id集合
         */
        del: function(ids) {
            var me = this;

            me.crmObjectDel(ids, function() {
                me.trigger('refresh', 'del');
            }, $t("crm.销售订单"));
        },

        /**
         * @desc 作废订单
         * @param {{Array}} id集合
         */
        invalid: function(ids) {
            var me = this;

            me.crmObjectInvalid(ids, function() {
                me.trigger('refresh', 'invalid');
            }, $t("crm.销售订单"), true);
        },

        /**
         * @desc   确认
         * @param  确认id
         * @param  确认时间servertime
         */
        confirm: function(id, time) {
            var me = this;
            var confirm = util.confirm($t("确认销售订单信息无误"), $t("确认"), function() {
                util.FHHApi({
                    url: '/EM1HCRM/CustomerOrder/SetOrderStatus',
                    data: me._getStatusParam(id, 3, time),
                    success: function(data) {
                        confirm.hide();
                        if (data.Result.StatusCode == 0) {
                            util.remind($t("确认成功")+'!');
                            me.trigger('refresh', 'confirm');
                            return;
                        }
                        util.alert(data.Result.FailureMessage);
                    }
                }, {
                    submitSelector: $('.b-g-btn', confirm.element),
                    errorAlertModel: 1
                });
            });
        },

        /**
         * @desc   撤回
         * @param  撤回id
         * @param  撤回时间servertime
         */
        revoke: function(id, time) {
            var me = this,
                confirm = util.confirm($t("您确定撤回该销售订单"), $t("撤回"), function() {
                    util.FHHApi({
                        url: '/EM1HCRM/CustomerOrder/SetOrderStatus',
                        data: me._getStatusParam(id, 5, time),
                        success: function(data) {
                            confirm.hide();
                            if (data.Result.StatusCode == 0) {
                                util.remind($t("撤回成功")+'!');
                                me.trigger('refresh', 'revoke');
                                return;
                            }
                            util.alert(data.Result.FailureMessage);
                        }
                    }, {
                        submitSelector: $('.b-g-btn', confirm.element),
                        errorAlertModel: 1
                    });
                });
        },

        /**
         * @desc 确认收货
         */
        receipt: function(id) {
            var me = this;
            if (!id) {
                return;
            }
            util.FHHApi({
                url: '/EM1HCRM/CustomerOrder/ConfirmReceive',
                data: {
                    CustomerOrderIDs: [id]
                },
                success: function(data) {
                    if (data.Result.StatusCode == 0) {
                        var msg = data.Value.ErrorList.join('<br/>') + data.Value.FailedList.join('<br/>');
                        if(msg) {
                            util.alert(msg);
                            return;
                        }
                        util.remind($t("已确认收货")+'!');
                        me.trigger('refresh', 'receipt');
                        return;
                    }
                    util.alert(data.Result.FailureMessage);
                }
            }, {
                errorAlertModel: 1
            });
        },

        /**
         * @desc 确认发货
         */
        delivery: function(id) {
            var me = this,
                delivery = null;
            if (!id) {
                return;
            }
            delivery = new Delivery({
                dataId: id
            });
            delivery.on('suc', function() {
                me.trigger('refresh', 'delivery');
            });
            delivery.show();
        },

        /**
         * @desc   驳回
         * @param  驳回id
         * @param  驳回时间servertime
         */
        reject: function(id, time) {
            var me = this;
            if (!me.rejectDialog) {
                me.rejectDialog = new Reject();
                me.rejectDialog.on('suc', function() {
                    me.trigger('refresh', 'reject');
                });
            }

            me.rejectDialog.set({
                id: id,
                time: time
            });

            me.rejectDialog.show();
        },

        /**
         * 添加相关团队成员
         */
        addSalesTeamM: function(obj) {
        },

        /**
         * 收款
         * @param money-待回款的金额
         */
        pay: function(data) {
            var me = this;
            var order = data.data;

            require.async('crm-modules/action/paymentobj/paymentobj', function(Action) {
                me.gbAction = new Action();
                me.gbAction.add({
                    showDetail: false,
                    show_type: 'full',
                    isTriggerPay: true,
                    data: {
                        account_id: order.account_id._id,
                        account_id__r: order.account_id.name,
                        order_id: order._id,
                        order_id__r: order.name
                    },
                    nonEditable: true,
                    success: function(data) {
                        me.trigger('refresh', 'pay');
                    }
                });
            });
        },

        /**
         * @desc 获取设置状态参数
         */
        _getStatusParam: function(id, status, time, reason) {
            return {
                CustomerTradeID: id,
                Status: status,
                ServerTime: time || new Date().getTime(),
                RejectReason: reason || ''
            };
        },

        //创建发货单
        adddeliverynote: function(obj) {
            var me = this;
            //待确认的订单不能创建发货单
            // if(obj.life_status === 'under_review') {
            if(obj.order_status == 6) {
                util.alert($t("订单尚未确认不能创建发货单"));
                return;
            }
            //已发货和已收货时不能创建发货单
            if(obj.logistics_status == 3 || obj.logistics_status == 5) {
                util.alert($t("订单已发货请查看发货记录"));
                return;
            }
            require.async(util.getCrmFilePath('deliverynoteobj', 'action'), function(Action) {
                if(!me._adddeliverynoteAction) {
                    me._adddeliverynoteAction = new Action();
                }
                me._adddeliverynoteAction.add({
                    nonEditable: true,
                    data: {
                        sales_order_id: obj.order_id,
                        sales_order_id__r: obj.order_name,
                    },
                    showDetail: obj.showDetail === false ? false : true
                })
            })
        },

		/**
		 * @desc 复制时，下单日期改为当前日期
		 * @param obj
		 */
		setOrderTime:function(obj){
        	if(obj.isCopy){
        		if(obj.data.order_time){
        			obj.data.order_time = new Date().getTime();
				}
			}
			if(obj._from === 'draft' && obj.data) obj.data.order_time = Date.now();
		},

		/**
         * @description 销毁
         */
        destroy: function() {
            var me = this;
            _.each(['_edit', '_add', 'rejectDialog', 'salesTeam', 'gbAction', '_adddeliverynoteAction', 'thirdOrder', 'simpleOrder'], function(item) {
                me[item] && me[item].destroy();
                me[item] = null;
            });
            Common.prototype.destroy.call(me);
        }
    });

    Order.View  = View;
    Order.Model = Model;
    Order.Form = Form;
    module.exports = Order;

});
