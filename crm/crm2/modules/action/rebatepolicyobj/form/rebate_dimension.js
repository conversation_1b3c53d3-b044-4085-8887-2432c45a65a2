/*
 * @Descripttion: 
 * @Author: chaoxin
 * @Date: 2023-10-18 15:40:33
 * @LastEditors: chaoxin
 * @LastEditTime: 2024-02-29 20:13:00
 */

define(function (require, exports, module) {
    const Base = require('crm-modules/action/field/field').components.base;

    module.exports = Base.extend({
        initialize() {
            Base.prototype.initialize.apply(this, arguments);
            this.defaultValue = this.getData(this.apiname + '__ro')?.split('.') || [];
        },

        render() {
            const me = this;
            this.widget = FxUI.create({
                wrapper: this.$el[0],
                template: `
                    <fx-cascader
                        ref="cascader"
                        v-model="value"
                        :options="options"
                        :props="props"
                        :placeholder="placeholder"
                        :disabled="disabled"
                        size="small"
                    >
                `,
                data() {
                    return {
                        value: me.defaultValue,
                        options: [],
                        props: {
                            lazy: true,
                            lazyLoad: this.childOptionsLazyLoad.bind(this),
                            checkStrictly: true,
                        },
                        ruleList: [],
                        disabled: false,
                        placeholder: $t('请选择')
                    }
                },
                watch: {
                    value: {
                        handler(val, oldVal) {
                            this.handleChange(val, oldVal);
                        },
                        immediate: true
                    },
                },
                methods: {
                    childOptionsLazyLoad(node = {}, resolve) {
                        if (node.root) {
                            me.model.getRuleSourceObjectOptions()
                                .then((rst) => resolve(rst.map((item) => ({...item, disabled: true}))));
                            return;
                        }
                        if (node.data?.leaf || node.children?.length) {
                            resolve([])
                            return;
                        }
                        me.model.fetchSourceObjectDimensionFields(node.data?.value)
                            .then((rst) => resolve(rst.map((r) => ({...r, leaf: true}))));
                    },
                    handleChange(val, oldVal) {
                        if (this.ignoreChange) {
                            this.ignoreChange = false;
                            return;
                        }
                        const ruleObjComp = me.get('forms')?.ruleObj;
                        const callBack = (confirm) => {
                            if (confirm) {
                                // model 数据更新
                                me.setData(val.join('.'));
                                me.hideError();
                                if (ruleObjComp) ruleObjComp.clearRules();
                            } else {
                                this.value = oldVal;
                                this.ignoreChange = true;
                            }
                        }
                        if (!ruleObjComp || !ruleObjComp.getValue(false).length) {
                            callBack(true);
                            return;
                        }
                        CRM.util.confirm(
                            $t('crm.rebatepolicyobj.form.rebate_basis.switch_tip', null, '切换将清空规则列表，确定要切换吗？'),
                            '',
                            function() {
                                callBack(true);
                                this.hide();
                            },
                            {
                                cancelBack() {
                                    callBack(false);
                                    this.hide();
                                },
                                zIndex: 10001
                            }
                        );
                    }
                }
            })
        },

        getValue() {
            const val = this.widget?.value;
            if (this.isRequired() && !val?.length) {
                this.showError();
            } else {
                this.hideError();
                return val.join('.');
            }
        }
    })
});