/**
 *@desc 长文本
 *<AUTHOR>
 */
define(function(require, exports, module) {
    var Input = require('../input/input');

    return Input.extend(_.extend({
    	render: function() {
        	var me = this;
            var vv = me.getData() || '';
            me.$ipt = $('<input value=""/>');
            var autoFocus = me.fieldAttr.autoFocus;

            me.longTextComp = FxUI.create({
            	wrapper: me.$el[0],
                template: '<fx-input class="f-fxui-textarea" show-word-limit v-model="dValue" :disabled="dDisable" :maxlength="dMaxlength" @blur="blurHandle" @focus="focusHandle" @change="changeHandle" :clearable="true" :placeholder="dPlaceholder" type="textarea" :autosize="dSize"></fx-input>',
                data: function() {
                    return {
                    	dMaxlength: me.fieldAttr.max_length,
                        dPlaceholder: me.fieldAttr.placeholder || me.fieldAttr.empty_prompt || me.fieldAttr.help_text || $t('请输入'),
                        dDisable: me.fieldAttr.is_readonly,
                        dValue: '',
                        dSize: me.getSize()
                    }
                },
                mounted: function() {
                    this.$nextTick(() => {
                        this.dValue = vv;
                        me.$ipt = me.$('textarea');
                		me.$ipt.addClass('b-g-ipt xx-ipt');
                		autoFocus && me.$ipt.focus();
                        this.initMultilineActionbar();
                        me.renderAITool && me.renderAITool();
                    })
                },
                methods: {
                    blurHandle: function(e) {
                        me._onBlurHandle(e);
                        var val = me.getIptValue();
                        me.toggleLimitError(val);
                        me.trigger('blur');
                        me.model.trigger('resizedialog');
                    },
                    focusHandle: function(e) {
                    	me._onFocusHandle(e);
                        me.hideError();
                    },
                    changeHandle: function(e) {
                    	me._onInputHandle(e);
                    },
                    initMultilineActionbar() {
                        me.renderMultilineActionbar(me.$ipt, () => {
                            me.$('.el-input__count').css('right', '44px');
                            this.$watch('dDisable', (disable) => {
                                !disable && me.$('.el-input__count').css('right', '44px');
                            });
                        });
                    }
                }
            });
        },

        getSize() {
            return {minRows: this.model.get('viewType') === 'mini' ? 2 : 4, maxRows: 10}
        },

        setCompValue(value) {
            this.longTextComp && (this.longTextComp.dValue = value || '');
        },

        toggleLimitError(value) {
            let minLength = this.fieldAttr.min_length || 0;
            let length = value.length;
            if(length < minLength) {
                this.showError('', $t('请至少输入{{num}}个有效字符', {num: minLength}));
                return true;
            }
            if(length > this.fieldAttr.max_length) {
                this.showError('', $t('paas.crm.input.max', {num: this.fieldAttr.max_length}));
                return true;
            }
            
            this.hideError();
        },

        setValue: function(value, noTrigger) {
            this.longTextComp && (this.longTextComp.dValue = value || '');

            this.setData(value, '' , noTrigger);

            this.hideError();
        },

        getValue: function() {
            var val = this.getIptValue();
            if(this.toggleLimitError(val)) return;

            var validateErrMsg;
            if (validateErrMsg = this.validateMultilineActionbar()) {
                this.showError(null, validateErrMsg);
                return;
            }

            return Input.prototype.getValue.apply(this, arguments);
        },

        resize: function() {
            this.longTextComp && (this.longTextComp.dSize = this.getSize());
        },

        disable: function() {
            this.disableMultilineActionbar();
            this.longTextComp && (this.longTextComp.dDisable = true);
        },

        enable: function() {
            this.enableMultilineActionbar();
            this.longTextComp && (this.longTextComp.dDisable = false);
        },

        destroy: function() {
            this.longTextComp && this.longTextComp.destroy && this.longTextComp.destroy();
            this.longTextComp = null;
            this.destroyMultilineActionbar();
            Input.prototype.destroy.apply(this, arguments);
        }
    }, require('../multilang/multilang')));
})
