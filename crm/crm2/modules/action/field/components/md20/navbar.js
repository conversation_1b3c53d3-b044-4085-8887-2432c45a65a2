define(function (require, exports, module) {
	return Backbone.View.extend({
		render: function () {
			var me = this;
			var tabs = _.map(me.options.tabs, function (a) {
				return {
					value: a.value,
					label: a.label,
					errorStatus: false,
					activeClass: a.active ? 'active' : '',
					isHide: !!a.isHide
				}
			})
			_.findWhere(tabs, {activeClass: 'active'}) || (tabs[0].activeClass = 'active')
			me.navBar = FxUI.create({
				wrapper: me.$el[0],
				template: `
					<div 
						v-show="tabs.length > 1 && !!_.find(tabs, function(b) {return !b.isHide})" 
						class="crm-md20-navbar"
					>
						<div 
							v-show="!tab.isHide" 
							@click="clickHandle(tab)" 
							class="nav-item" 
							:class="tab.activeClass" 
							v-for="tab in tabs"
						>
							<span class="item-label">{{tab.label}}</span>
							<span v-show="tab.errorStatus" class="el-icon-warning"></span>
						</div>
						<div class="full-item">
							<span
								@click="handleViewShortKey"
								data-pos="top"
								data-title="${$t('查看{{title}}', {title: $t('快捷键')})}"
								class="crm-ui-title fx-icon-shortcuts"
							>
							</span>
							<span 
								@click="fullClickHandle" 
								data-pos="top"
								:data-title="[isFull ? $t('退出全屏') : $t('全屏显示')]"
								class="crm-ui-title"
								:class="[isFull ? 'fx-icon-fullscreen-exit' : 'fx-icon-fullscreen']"
							>
							</span>
						</div>
					</div>
				`,
				data: function () {
					return {
						tabs: tabs,
						isFull: false
					}
				},
				methods: {
					clickHandle: function (tab) {
						if (tab.activeClass) return;
						var tt = this.getCurActive();
						tt && (tt.activeClass = '');
						tab.activeClass = 'active';
						me.trigger('tabClick', tab.value);
					},
					fullClickHandle: function () {
						me.trigger('fullClick');
					},
					getCurActive() {
						return _.findWhere(this.tabs, {activeClass: 'active'});
					},
					handleViewShortKey() {
						let strs = '在填写数据的过程中，可以通过方向键快速切换。;提示：左右切换当前仅支持输入型字段，对于不支持的字段会跳过。;切换到上一行;切换到下一行，如果当前是最后一行则自动新增一行;切换到左边的单元格;切换到右边单元格';// [ignore-i18n]
						let tips = $t('crm.field.md.shortkey', null,  strs).split(';')
						let html = `<div>
										<p>${tips[0]}</p>
										<p style="margin-bottom:4px">${tips[1]}</p>
										<p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-top"></span>${tips[2]}</p>
										<p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-bottom"></span>${tips[3]}</p>
										<p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-back"></span>${tips[4]}</p>
										<p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-right"></span>${tips[5]}</p>
									</div>`
						CRM.util.alert(html, null, {title: $t('快捷键')});
					}
				}
			})
		},

		getCurActive: function() {
			return this.navBar.getCurActive() || {};
		},

		hideItem: function (value) {
			var tabs = this.navBar.tabs;
			_.isString(value) && (value = [value]);
			_.each(value, function (a) {
				var tt = _.findWhere(tabs, {value: a});
				tt && !tt.isHide && (tt.isHide = true, tt.activeClass = '');
			})
		},

		showItem: function (value) {
			var tabs = this.navBar.tabs;
			_.isString(value) && (value = [value]);
			_.each(value, function (a) {
				var tt = _.findWhere(tabs, {value: a});
				tt && tt.isHide && (tt.isHide = false);
			})
		},

		setActiveItem: function (value) {
			var tabs = this.navBar.tabs;
			var st = _.findWhere(tabs, {activeClass: 'active'});

			var vv;
			if (value) {
				var tt = _.findWhere(tabs, {value: value});
				if (tt) {
					st && (st.activeClass = '');
					tt.activeClass = 'active';
					vv = value;
				}
			} else {
				if (!st) {
					_.find(tabs, function (a) {
						if (!a.isHide) {
							a.activeClass = 'active';
							vv = a.value;
							return true;
						}
					})
				}
			}

			if (!vv && !_.findWhere(tabs, {isHide: false})) {
				vv = 'unkown'
			} else {
				let ttt = _.findWhere(tabs, {value: vv});
				if(ttt && ttt.isHide) vv = 'unkown';
			}

			vv && this.trigger('tabClick', vv);
		},

		setFullStatus: function (isFull) {
			this.navBar.isFull = !!isFull;
		},

		toggleErrorStatus: function (apiName, flag) {
			var tabs = this.navBar.tabs;
			_.isString(apiName) && (apiName = [apiName]);
			_.each(apiName, function (a) {
				var tt = _.findWhere(tabs, {value: a});
				tt && (tt.errorStatus = !!flag)
			})
		},

		destroy: function () {
			this.stopListening(), this.off();
			this.navBar && (this.navBar.destroy(), this.navBar = null);
			this.$el.remove();
			this.options = null;
		}
	})
})
