// [ignore-i18n-file]

/**
 *@desc 从对象通用布局
 */
 define((require, exports, module) => {
    const Table = require('../layout_v2/table');
    const TreeTable = require('../layout_v2/treetable');
    const ACTION = require('./action');
    return Backbone.View.extend({
        initialize() {
			this.listenTo(this.model, 'form.data.update.complete', () => this.vueInstance && this.vueInstance.updateSlot());
		},

        render() {
            let superRoot = this;
            let {detailObjects, $eventBus} = this.options;
            this.vueInstance = FxUI.create({
                wrapper: this.$el[0],
                components: {
                    'layout-tit': require('./tit')
                },
                template: `
                    <div class="crm-md20__wrapper" :class="[dFullStatus ? 'md20-layout__full' : '']">
                        <div v-if="dOriginTabs.length > 1" style="position:relative;padding-right:44px" class="md20-header">
                            <fx-tabs
                                :show-tab-content="false" 
                                v-model="dCurrentValue"
                                @tab-click="handleTabClick"
                            >
                                <fx-tab-pane
                                    v-for="item in dTabs"
                                    :key="item.objectApiName"
                                    :name="item.objectApiName"
                                >
                                    <div style="position:relative" slot="label"><span style="font-size:18px;">{{item.label}}</span><i v-show="cError(item.objectApiName)" style="position:absolute;right:-12px;top:4px" class="fx-icon-warning"></i></div>
                                </fx-tab-pane>
                            </fx-tabs>
                            <div v-if="dTabs.length" class="tab-item-btnwrap">
                                <span
                                    @click="handleViewShortKey"
                                    class="crm-ui-title fx-icon-shortcuts"
                                    data-pos="top"
                                    data-title="${$t('查看{{title}}', {title: $t('快捷键')})}"
                                >
                                </span>
                                <span
                                    @click="handleFull"
                                    class="crm-ui-title"
                                    :class="[dFullStatus ? 'fx-icon-fullscreen-exit' : 'fx-icon-fullscreen']"
                                    data-pos="top"
                                    :data-title="[dFullStatus ? $t('退出全屏') : $t('全屏显示')]"
                                >
                                </span>
                            </div>
                        </div>
                        <div class="tab-content">
                            <div v-show="dCurrentValue && (c.objectApiName === dCurrentValue || dOriginTabs.length === 1)" :key="c.objectApiName" v-for="c in dOriginTabs">
                                <template v-if="c.noLayoutSlot">
                                    <div class="md20-layoutitem__wrapper">
                                        <div :ref="'headerslot_' + c.objectApiName" class="slot-wrapper crm-clearfix clearfix"></div>
                                        <div 
                                            v-show="!item.isHide && !item.isRemove"
                                            :key="item.key"
                                            v-for="(item, index) in dLayoutItems[c.objectApiName]"
                                            class="j-layoutv2-wrapper"
                                        >
                                            <layout-tit
                                                :title="item.label"
                                                :singleButtons="_.filter(item.singleButtons, a => !a.isHide)"
                                                :batchButtons="_.filter(item.batchButtons, a => !a.isHide)"
                                                :total="dTotal[item.key]"
                                                :errorNum="dError[item.objectApiName][item.recordType]"
                                                :checkNum="dCheckedDatas[item.key]"
                                                :outNum="item.outNum || 4"
                                                :midSlot="item.midSlot"
                                                :fullStatus="cfullBtnsStatus"
                                                :hideDeleteAll="_.filter(dLayoutItems[c.objectApiName], a => !a.isRemove).length < 2"
                                                @btnClick="handleBtnClick($event, item)"
                                            />
                                            <div v-if="item.hasLeftSlot" class="layoutitem-slot">
                                                <div :ref="'leftslot_' + c.objectApiName + item.recordType" class="table-leftslot-wrapper"></div>
                                                <div :ref="item.key" :data-apiname="c.objectApiName" class="table-wrapper"></div>
                                            </div>
                                            <div v-else :ref="item.key" :data-apiname="c.objectApiName" class="table-wrapper"></div>
                                            <div :ref="'footerslot_' + item.key" class="slot-wrapper crm-clearfix clearfix"></div>
                                        </div>
                                        <div :ref="'footerslot_' + c.objectApiName" class="slot-wrapper crm-clearfix clearfix"></div>
                                    </div>
                                    <div class="md20-add__wrapper">
                                        <div class="rt-dropdown">
                                            <fx-dropdown v-if="cDownRecordTypes(c.objectApiName).length" trigger="click" @command="handleAddRecoryType($event, c)">
                                                <fx-button type="text">
                                                    <span style="color:#0c6cff" :ref="c.objectApiName">+${$t('添加业务类型')}</span>
                                                </fx-button>
                                                <fx-dropdown-menu slot="dropdown">
                                                    <fx-dropdown-item v-for="item in cDownRecordTypes(c.objectApiName)" :key="item.recordType" :command="item">{{item.label}}</fx-dropdown-item>
                                                </fx-dropdown-menu>
                                            </fx-dropdown>
                                        </div>
                                    </div>
                                </template>
                                <div v-else :ref="'layoutslot_' + c.objectApiName" class="md20-layoutitem__wrapper"></div>
                            </div>
                        </div>
                    </div>
                `,
                data() {
                    let dTabs = [],  dLayoutItems = {}, dError = {}, dTotal = {}, dCheckedDatas = {};
                    let more2 = detailObjects.length > 1;
                    _.each(detailObjects, (a)=> {
                        let {apiName} = a;
                        let showRTLabel = a.components.length > 1;
                        dTabs.push({
                            noLayoutSlot: !a.layoutSlot,
                            objectApiName: apiName, 
                            label: a.label
                        })
                        dError[apiName] = {};

                        dLayoutItems[apiName] = _.map(a.components, b => {
                            let key = apiName + b.recordType;
                            let outNum = 4;
                            let singleButtons = _.map(b.singleButtons, c => ({
                                action: c.action,
                                label: c.label,
                                isHide: false,
                                data: c.data
                            }))
                            if(detailObjects.length === 1) {
                                outNum++;
                            }
                            dTotal[key] = 0;
                            dCheckedDatas[key] = 0;
                            dError[apiName][b.recordType] = 0;
                            return {
                                key,
                                objectApiName: apiName,
                                recordType: b.recordType,
                                isHide: false,
                                isRemove: false,
                                hasLeftSlot: !!(b.fieldRenders && b.fieldRenders.tableLeftSlot),
                                label: more2 ? (showRTLabel ? b.label : '') : (
                                    showRTLabel ? a.label + '-' + b.label : a.label
                                ),
                                outNum,
                                singleButtons,
                                midSlot: b.fieldRenders && b.fieldRenders.titSlot,
                                batchButtons: _.map(b.batchButtons, c => ({
                                    action: c.action,
                                    label: c.label,
                                    isHide: false
                                }))
                            }
                        })
                    })
                    return {
                        dCurrentValue: dTabs[0].objectApiName,
                        dTabs,
                        dOriginTabs: dTabs.slice(0),
                        dLayoutItems,
                        dError,
                        dTotal,
                        dCheckedDatas,
                        dFullStatus: false
                    }
                },

                mounted() {
                    this.$nextTick(() => this._renderSome());
                },

                computed: {
                    cError() {
                        return function(name) {
                            return !!_.find(this.dError[name], item => item > 0);
                        }
                    },
                    cHideDeleteALl() {
                        let arr = _.filter(this.dLayoutItems, a => !a.isRemove);
                        return arr.length < 2;
                    },
                    cfullBtnsStatus() {
                        if(this.dTabs.length > 1) return 0;//不显示
                        return this.dFullStatus ? 2 : 1;
                    }
                },

                watch: {
                    dCurrentValue(objectApiName) {
                        this.$nextTick(() => _.each(this.tables[objectApiName], table => table.resizeHandle()));
                    }
                },
                
                methods: {
                    _renderSome() {
                        this.tables = {};
                        this.mySlots = [];
                        _.each(detailObjects, a => {
                            let objectApiName = a.apiName;
                            if(a.layoutSlot) {
                                this._renderLayoutSlot(a, objectApiName);
                                return;
                            }
                            this._renderSlot(a.headerSlot, 'headerslot_' + objectApiName, 'headerSlot');
                            this._renderSlot(a.footerSlot, 'footerslot_' + objectApiName, 'footerSlot');
                            let tt = {};
                            _.each(a.components, b => {
                                let recordType = b.recordType;
                                let key = objectApiName + recordType;
                                let MyTable = b.treeConfig ? TreeTable : Table;
                                tt[recordType] = new MyTable({
                                    el: this.$refs[key], 
                                    objectDescribe: a.objectDescribe,
                                    objectApiName: objectApiName,
                                    recordType: recordType,
                                    model: superRoot.model,
                                    layout: b.layout,
                                    isEdit: b.isEdit,
                                    datas: b.datas,
                                    fieldRenders: b.fieldRenders,
                                    treeConfig: b.treeConfig,
                                    supportBatchEdit: !!_.findWhere(b.batchButtons, {action: 'tableBatchEditHandle'}),
                                    insertMenuFn: operateBtns => {
                                        if(!_.findWhere(operateBtns, {action: 'insertHandle'}) || b.treeConfig) return;
                                        let tt = _.findWhere(this.dLayoutItems[objectApiName], {recordType: recordType});
                                        let btns = [];
                                        _.each(tt.singleButtons, btn => {
                                            if(btn.isHide) return;
                                            if(btn.action === ACTION.SINGLEADD || (btn.data && btn.data.lookup_field_name && !btn.callback)) {
                                                btns.push(_.extend({}, btn, {
                                                    callback($event, data) {
                                                        $eventBus.trigger(btn.action, objectApiName, recordType, _.extend({}, btn, {
                                                            insertedRowId: data.rowId,
                                                            $event
                                                        }));
                                                    }
                                                }))
                                            }
                                        });
                                        return btns;
                                    },
                                    $eventBus
                                })

                                b.fieldRenders && this._renderSlot(b.fieldRenders.tableLeftSlot, 'leftslot_' + objectApiName + recordType, 'tableLeftSlot', {objApiName: objectApiName, recordType});

                                this._bindEvent(a.apiName, b.recordType);
                            })
                            
                            this.tables[a.apiName] = tt;
                        })
                    },

                    updateSlot() {
                        _.each(this.mySlots, slot => slot.update && slot.update());
                    },

                    _bindEvent(objectApiName, recordType) {
                        $eventBus.on({
                            [ACTION.TOGGLE_COMMON_BUTTONS]: status => this.toggleTitButtons(status, objectApiName, recordType),
                            [ACTION.CHECKED]: checkedDatas=> this.dCheckedDatas[objectApiName + recordType] = checkedDatas.length,
                            [ACTION.CANCELSELECTED]: () => this.dCheckedDatas[objectApiName + recordType] = 0,
                            [ACTION.TOTALCHANGE]: total => this.dTotal[objectApiName + recordType] = total || 0,
                            [ACTION.REMOVERECORDTYPE]: () => this.removeLayoutItem(objectApiName, recordType),
                            [ACTION.UPDATE_ERRORNUM]: (num = 0) => this.dError[objectApiName][recordType] = num,
                            [ACTION.ASSERT_SINGLE_ADD_RIGHT]: opts => opts.callback(this.assertSingleAddRight(objectApiName, recordType))
                        }, objectApiName, recordType);
                    },

                    _renderSlot(slots, refkey, type, opts) {
                        _.each(slots, slot => {
                            try {
                                let slotInstance = slot(this.$refs[refkey][0], opts);
                                if(!slotInstance || !slotInstance.destroy) {
                                    if(Fx && Fx.env === 'test') {
                                        CRM.util.alert(`<div style="color:red;font-size:30px">
                                            请确保${type}插件里的slot 返回 destroy  
                                            比如 return {destroy() {...一定记得销毁你的内部组件}}
                                        </div>`)
                                    }
                                } else {
                                    this.mySlots.push(slotInstance);
                                }
                            } catch(msg) {
                                console.error(msg)
                            }   
                        })
                    },

                    _renderLayoutSlot(opts, refkey) {
                        let el = this.$refs['layoutslot_' + refkey];
                        if(!el) return;
                        let {layoutSlot, apiName, components} = opts;
                        let $wrapper = $(el);
                        
                        _.each(components, b => {
                            let recordType = b.recordType;
                            $eventBus.on({
                                [ACTION.TOGGLE_COMMON_FIELD_STATUS]: param => layoutItem.setFieldAttr(param),
                                [ACTION.TOGGLE_SINGLE_FIELD_STATUS]: param => layoutItem.setRowFieldAttr(param),
                                [ACTION.TOGGLE_SINGLE_BUTTONS]: (...args) => layoutItem.setTableBtns(...args),
                                [ACTION.UPDATE]: res => layoutItem.update(res, apiName, recordType),
                            }, apiName, recordType);
                        })

                        superRoot.model.on('getCheckedDatasHandle', cb => {
                            cb(layoutItem.getCheckedDatas())
                        })

                        opts.onCheckChange = () => {
                            _.each(this.mySlots, slot => slot.checkChange && slot.checkChange());
                        }

                        let _slotItem = layoutSlot($wrapper, opts);
                        let layoutItem = {
                            hide() {
                                $wrapper.hide();
                            },
                            show() {
                                $wrapper.show();
                            },
                            showError() {
                                if (this._fmerror) return;
                                this._fmerror = $('<span style="height:1px;" class="fm-error"></span>'); //class必须为fm-error方便上层验证错误 高度为1方便滚动到头部
                                $wrapper.prepend(this._fmerror);
                            },
                            hideError() {
                                if (this._fmerror) {
                                    this._fmerror.remove();
                                    this._fmerror = null;
                                }
                            },

                            getCheckedDatas() {return []},

                            //希望业务实现的方法
                            validate() {},
                            setFieldAttr() {},
                            setRowFieldAttr() {},
                            toggleTitButtons(){},
                            setTableBtns() {},
                            destroy() {},
                            ..._slotItem,

                            //获取数据先验证
                            getValue() {
                                this.validate() ? this.showError() : this.hideError();
                                return _slotItem.getValue();
                            },
                            update(datas, apiName, recordType) {
                                _slotItem.update && _slotItem.update(datas);
                                if(_slotItem.getValue) {//数据同步
                                    let dataList = _slotItem.getValue();
                                    let tt = {};
                                    _.each(dataList, a => {
                                        let ss = (tt[a.object_describe_api_name] || (tt[a.object_describe_api_name] = {}));
                                        (ss[a.record_type] || (ss[a.record_type] = [])).push(a);
                                    })
                                    _.each(tt, (item, objApiName) => {
                                        _.each(item, (dataList, recordType) => {
                                            superRoot.model.newUpdateMDData(dataList, objApiName, recordType);
                                        })
                                    })
                                } else {
                                    superRoot.model.updateMDData(datas, apiName, recordType);
                                }
                            }
                        }

                        if(!this._layoutSlotItems) {
                            this._layoutSlotItems = {};
                        }
                        this._layoutSlotItems[apiName] = layoutItem;
                    },

                    handleTabClick() {
                        superRoot.model.trigger('mdnav:change', this.dCurrentValue);
                    },

                    handleBtnClick(btn, item) {
                        let {objectApiName, recordType} = item;
                        if(btn.action === '__full') {
                            this.handleFull();
                            return;
                        }
                        if(btn.action === '__viewShortKey') {
                            this.handleViewShortKey();
                            return;
                        }
                        $eventBus.trigger(btn.action, objectApiName, recordType, btn);
                    },
                    
                    cDownRecordTypes(name) {
                        return _.filter(this.dLayoutItems[name], a => a.isRemove && !a.isHide);
                    },
                    handleFull() {//全屏
                        this.dFullStatus = !this.dFullStatus;
                        let $scroll;
                        superRoot.model.trigger('getScroll', scroll => {
                            if(scroll && scroll.length) {
                                $scroll = scroll;
                            }
                        })

                        if(this.isNormalFull === void 0) {
                            this.isNormalFull = superRoot.model.get('isNormalDialog') || superRoot.$el.closest('.crm-c-dialog').length;
                        }

                        this.fullParent || (this.fullParent = superRoot.$el.parent());

                        if(this.dFullStatus) {//全屏
                            $scroll && (this._scrollTop = $scroll.scrollTop());
                            if(!this.isNormalFull) {
                                let zIndex = superRoot.model.get('zIndex');
                                let nZIndex =  _.max([zIndex || 0, 1000]);
                                this.oldZIndex = zIndex;
                                superRoot.model.set('zIndex', nZIndex);
                
                                this.fulldialog = CRM.util.fullDialog({
                                    el: superRoot.$el,
                                    zIndex: nZIndex,
                                    css: {
                                        width: 'auto',
                                        padding: '0 8px',
                                        overflow: 'auto'
                                    }
                                })
                            }
                        } else {
                            this.$nextTick(() => {
                                if(!this.isNormalFull) {
                                    superRoot.$el.appendTo(this.fullParent);
                                    this.fulldialog.destroy();
                                    this.fulldialog = null;
                                    superRoot.model.set('zIndex', this.oldZIndex);
                                }
                                if ($scroll) {
                                    $scroll.scrollTop(this._scrollTop);
                                }
                            })
                        }
                    },

                    handleViewShortKey() {
                        let strs = '在填写数据的过程中，可以通过方向键快速切换。;提示：左右切换当前仅支持输入型字段，对于不支持的字段会跳过。;切换到上一行;切换到下一行，如果当前是最后一行则自动新增一行;切换到左边的单元格;切换到右边单元格';
                        let tips = $t('crm.field.md.shortkey', null,  strs).split(';')
                        let html = `<div>
                                        <p>${tips[0]}</p>
                                        <p style="margin-bottom:4px">${tips[1]}</p>
                                        <p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-top"></span>${tips[2]}</p>
                                        <p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-bottom"></span>${tips[3]}</p>
                                        <p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-back"></span>${tips[4]}</p>
                                        <p><span style="color:#ff8800;color:var(--color-primary06);margin-right:4px" class="el-icon-right"></span>${tips[5]}</p>
                                    </div>`
                        CRM.util.alert(html, null, {title: $t('快捷键')});
                    },
                
                    //添加一种业务类型
                    handleAddRecoryType(item) {
                        item.isRemove = false;
                    },

                    //控制tit的按钮
                    toggleTitButtons(status, objectApiName, recordType) {
                        if(_.isEmpty(status)) return;
                        let tt = _.findWhere(this.dLayoutItems[objectApiName], {recordType});
                        if(tt) {
                            _.each(tt.batchButtons, btn => {
                                if(status[btn.action] === void 0) return;
                                btn.isHide = status[btn.action] === false;
                            });
                            _.each(tt.singleButtons, btn => {
                                let vv;
                                if(btn.data && btn.data.lookup_field_name) {
                                    vv = status[btn.data.lookup_field_name]
                                } else {
                                    vv = status[btn.action]
                                }

                                if(vv === void 0) return;

                                btn.isHide = !vv;
                            });
                        }
                    },

                    //显示隐藏tab
                    toggleTabs(param) {
                        let tabs = [];
                        _.each(this.dOriginTabs, tab => {
                            let tt = param[tab.objectApiName];
                            if(tt) {
                                tt.hidden || tabs.push(tab);
                            } else {
                                tt = _.findWhere(this.dTabs, {objectApiName: tab.objectApiName});
                                tt && tabs.push(tt);
                            }
                        })
                        this.dTabs = tabs;
                        if(!_.findWhere(this.dTabs, {objectApiName: this.dCurrentValue})) {
                            this.dCurrentValue = tabs[0] ? tabs[0].objectApiName : '';
                            // superRoot.model.trigger('mdnav:change', this.dCurrentValue);
                        }
                    },

                    //隐藏显示指定的业务类型
                    toggleLayoutItem(param) {
                        _.each(param, (status, objectApiName) => {
                            _.each(status, (config, recordType) => {
                                let tt = _.findWhere(this.dLayoutItems[objectApiName], {recordType});
                                if(!tt) return
                                tt.isHide = config.hidden;
                            })
                        })
                    },

                    //移除某个业务类型，移除本身还保留元素，和隐藏的区别是，移除的类型被调整到了最后
                    removeLayoutItem(objectApiName, recordType) {
                        let tt = _.findWhere(this.dLayoutItems[objectApiName], {recordType});
                        if(tt) {
                            tt.isRemove = true;
                            this.dLayoutItems[objectApiName] = [..._.difference(this.dLayoutItems[objectApiName], tt), tt];
                        }
                    },

                    assertSingleAddRight(objectApiName, recordType) {
                        let tt = _.findWhere(this.dLayoutItems[objectApiName], {recordType});
                        return !!_.find(tt && tt.singleButtons, a => !a.isHide && a.action === ACTION.SINGLEADD);
                    },

                    toTab(objectApiName) {
                        if(!_.findWhere(this.dTabs, {objectApiName})) return;

                        this.dCurrentValue = objectApiName;
                        superRoot.model.trigger('mdnav:change', this.dCurrentValue);
                    },

                    getValue() {
                        let data = {};
                        _.each(this.dLayoutItems, (recordTypes, objectApiName) => {
                            let list = [];
                            _.each(recordTypes, b => {
                                let comp = this.tables[objectApiName] && this.tables[objectApiName][b.recordType];
                                if(comp) {
                                    [].push.apply(list, comp.getValue());
                                }
                                
                            })
                            data[objectApiName] = list;
                        })

                        _.each(this._layoutSlotItems, (layoutSlot, objectApiName) => {
                            data[objectApiName] = layoutSlot.getValue();
                        })
                        
                        return data;
                    },

                    //验证当前是否有错
                    valid() {
                        return !_.find(this.dError, item => _.find(item, v => !!v));
                    },

                    //得到当前是否有隐藏的表格，并且存在错误数据
                    getHideAndError() {
                        let result = [];
                        _.each(this.dError, (item, objectApiName) => {
                            _.each(item, (errorNum, recordType) => {
                                if(!errorNum) return;
                                let tt = _.findWhere(this.dLayoutItems[objectApiName], {recordType});
                                tt && tt.isHide && result.push({objectApiName, recordType});
                            })
                        })

                        return result;
                    }
                },

                beforeDestroy() {
                    _.each(this.tables, widgets => {
                        _.each(widgets, comp => comp.destroy());
                    });
                    _.each(this.mySlots, slot =>  slot.destroy && slot.destroy());
                    _.each(this._layoutSlotItems, slot =>  slot.destroy && slot.destroy());
                    this.tables = this.mySlots = detailObjects = $eventBus = null;
                }
            })
        },

        //隐藏或显示指定的从对象
		//param: {sdfsaf__cc: {hidden: true}, aa__c: {hidden: false}}
        toggleMDStatus(param) {
            this.vueInstance && this.vueInstance.toggleTabs(param);
        },

        //隐藏或显示指定的从对象业务类型
		//param: {sdfsaf__cc: {defalt__c: {hidden: true}, aa__c: {hidden:false}}}
		toggleRecordTypeStatus(param) {
            this.vueInstance && this.vueInstance.toggleLayoutItem(param);
		},

        //切换到指定的tab
        switchMDItem(apiName) {
            this.vueInstance && this.vueInstance.toTab(apiName);
		},

		_showMDHideError(apiName, recordType) {
            _.each(this.vueInstance.getHideAndError(), item => this.model.showMDHideError(item.objectApiName, item.recordType));
		},

        _showError() {
			if (this._fmerror) return;

            //class必须为fm-error方便上层验证错误 高度为1方便滚动到头部
			this.$el.prepend(this._fmerror = $('<span style="height:1px;" class="fm-error"></span>'));
		},

		_hideError() {
            this._fmerror && (this._fmerror.remove(), this._fmerror = null);
		},

        getValue() {
            let datas = this.vueInstance.getValue();
            this[this.vueInstance.valid() ? '_hideError' : '_showError']();
            this._showMDHideError();
            return datas;
        },

        destroy() {
            this.vueInstance && (this.vueInstance.destroy(), this.vueInstance = null);
            this.stopListening(), this.off();
            this.options = null;
        }
    });
});