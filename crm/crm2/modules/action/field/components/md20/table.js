define(function(require, exports, module) {
    let btnLabelwidthCache = [];
    function countBtnLabelWidth(btnLabel) {
        if(!btnLabel) return 0;
        let tmp = _.findWhere(btnLabelwidthCache, {btnLabel});
        if(tmp) {
            return tmp.width;
        }

        let $label = $(`<span style="position:absolute;visibility:hidden;">${btnLabel}</span>`);
        $(document.body).append($label);
        let width = $label.width();
        btnLabelwidthCache.push({btnLabel,width});
        $label.remove();
        return width;
    }
    const ACTION = require('./layout_v2/action')

    const card = require('./card/card');

    return Backbone.View.extend(_.extend({
        options: {
            objectDescribe: {},
            apiName: '',
            recordType: '',
            layout: [],
            layouts: [],
            operateButtons: null,
            datas: [],
            model: null,
            isOrderBy: true, //是否支持排序
            isDrag: true, //是否支持拖动
            isEdit: true, //是否可编辑
            eventKey: '',
            supportBatchEdit: true,
            fieldRenders: null
        },
        compPath: 'crm-modules/components/objecttable/objecttable',
        events: {
            'mousedown .tr-drag': '_startDragTrHandle',
            'focus .crm-md20-morebtn' : '_moreBtnFocusHandle',
            'blur .crm-md20-morebtn' : '_moreBtnBlurHandle'
        },

        ...card,

        _moreBtnFocusHandle(e) {
            let $target = $(e.target);
            let pos = $target.offset();
            let $ul = $target.find('ul');
            let $h = $ul.outerHeight();
            let top = pos.top + 22;
            if(top + $h > $('body').height()) {
                top = pos.top - $h - 4
            }
            $ul.css({
                top: top,
                right: $('body').width() - pos.left - 20
            })
            this.$el.css({
                zIndex: 100
            })
            $target.closest('.main').css({zIndex: 100});
        },
        _moreBtnBlurHandle(e) {
            this.$el.css({
                zIndex: ''
            })
            $(e.target).closest('.main').css({zIndex: 1});
        },

        initialize: function(opts) {
            var me = this;
            if(opts.fieldRenders && opts.fieldRenders.tableOptions && opts.fieldRenders.tableOptions.isDrag === false) {
                me.options.isDrag = false;
            }
            me.listenTo(me.model, 'action:' + opts.eventKey, me.actionHandle);
            me.listenTo(me.model, `getCheckedData_${opts.apiName}_${opts.recordType}`, callBack => callBack(me.getCheckedData()));

            opts.$eventBus && opts.$eventBus.on({
                [ACTION.GET_DATA_BY_OFFSETPOS]: opts => opts.callback(me.getDataByOffsetPos(opts.rowId, opts.offsetPos, opts.isTile))
            }, opts.apiName, opts.recordType);

            me.__operateBtns = {};

            me.render();
        },

        //高亮闪烁显示某行
        twinkleTrHandle(rowId) {
            if(this._twinkleing) return;
            this._twinkleing = true;
            let $strs = this.table.findTrs(this.getPosByRowId(rowId));
             $strs[0].scrollIntoView();
             $strs[1] &&  $strs[1].scrollIntoView();//取消固定列时可能没有
             $strs[2] &&  $strs[2].scrollIntoView();//取消固定列时可能没有
             let cls = 'crm-md20-tr-flash-highlight'
             $strs.addClass(cls);
             setTimeout(() => {
                $strs.removeClass(cls);
                this._twinkleing = false;
             }, 1600);
        },

        getDataByOffsetPos(rowId, offsetPos, isTile) {
            let pos = this.getPosByRowId(rowId);
            
            let datas = this.getTableData(), item;

            if(isTile) {
                if(offsetPos < 0) {//向上查找
                    while(pos + offsetPos >= 0) {
                        let citem = datas[pos + offsetPos]
                        if(citem && _.findWhere(this.__operateBtns[citem.rowId], {action: ACTION.SINGLE_EDIT})) {
                            item = citem;
                            break;
                        } else {
                            offsetPos--;
                        }
                    }
                } else {
                    while(pos + offsetPos < datas.length) {
                        let citem = datas[pos + offsetPos]
                        if(citem && _.findWhere(this.__operateBtns[citem.rowId], {action: ACTION.SINGLE_EDIT})) {
                            item = citem;
                            break;
                        } else {
                            offsetPos++;
                        }
                    }
                }
            } else {
                item = datas[pos + offsetPos];
            }

            if(isTile && !item) {
                return _.findWhere(datas, {rowId});
            }

            this.table.findTrs(pos).removeClass('tr-hover tr-edit');
            
            if(item) {
                this.table.findTrs(this.getPosByRowId(item.rowId)).addClass('tr-hover tr-edit');
                let $scroll = this.$('.main .main-scroll');
			    $scroll.scrollTop($scroll.scrollTop() + offsetPos * 34);
            }
            
            return item;
        },

        render: function() {
            var me = this;
            require.async(this.compPath, function (Table) {
                var MyTable = Table.extend(me.getExtendProtoAttr(Table.prototype));
                me.table = new MyTable(me.getTableInitOptions());
                me.table.render();
            })
        },

        //返回扩展属性
        getExtendProtoAttr: function(proto) {
            var me = this;
            var attr = {
                getOptions: function() {
                    var opts = proto.getOptions.call(this);
                    return me.parseTableOptions(opts);
                },
                fetchColunms: function(param, cb, context) {
                    this.fetchColunmsSuccess({
                        Value: {
                            objectDescribe: me.options.objectDescribe,
                            layout: me.options.layout
                        }
                    }, cb)
                }
            };

            _.each('checkboxclickHandle trclickHandle trErrorHandle staticParamChangeHandle tdWidthChangeHandle tableComplete batchCellChangeHandle cellChangeHandle'.split(' '), function(a) {
                attr[a] = function() {
                    if(me[a + 'Before'] && me[a + 'Before'].apply(me, arguments) === false) return;
                    me[a].apply(me, arguments)
                }
            })

            this.afterGetExtendProtoAttr && this.afterGetExtendProtoAttr(attr);

            return attr;
        },

        //获取表格初始化参数
        getTableInitOptions: function() {
            var opts = this.options;
            var attrs = {
                el: this.$el,
                apiname: opts.apiName,
                recordType: opts.recordType,
                showTitle: false,
                isEdit: opts.isEdit,
                supportFieldMapping: false
            }

            this.afterGetTableInitOptions && this.afterGetTableInitOptions(attrs);

            return attrs;
        },

        //返回当前表格列的排布顺序
        getColumnsByStartFieldName: function(startFieldName, num) {
            var fields = [];
            var flag;
            _.find(this.getAllColumns(), function(a) {
                if(!num) return true;
                if(a.data === startFieldName) {
                    flag = true;
                }
                if(flag && num) {
                    fields.push(a.data);
                    num--;
                }
            })

            return fields;
        },

        getColumnByFieldName: function(fieldName) {
            return _.findWhere(this.getAllColumns(), {data: fieldName});
        },

        //返回起始行开始的数据
        getDatasByStartRowId: function(startRowId, num) {
            var pos = this.getPosByRowId(startRowId);
            return this.getTableData().slice(pos, pos + num);
        },

        //定位到指定行的第一个可编辑的单元格
        focusTrFirstEditCell: function(rowId) {
            var trData = this.getDataByRowId(rowId);
            if(!trData) return;
            var table = this.table.table;
            var trHideFields = (trData.__rules || {}).hide_field || [];
            var pos = this.getPosByRowId(rowId);
            _.find(this.getAllColumns(), function(tc) {
                if(tc && tc.isEdit &&  _.contains(['text', 'long_text', 'email', 'url', 'number', 'currency', 'percentile', 'phone_number'], tc.type) && !_.contains(trHideFields, tc.data)) {
                    table.scrollToTd20(tc.data);
                    setTimeout(function() {
                        table.findTds([tc.data], table.findTrs(pos)).find('.td-cell-edit').trigger('click');
                    }, 50);
                    return true;
                }
            })
        },

        hasInsert(trData = {}) {
            let btns = this.__operateBtns && this.__operateBtns[trData.rowId];
            return btns && _.findWhere(btns, {action: 'insertHandle'});
        },

        //整理表格属性
        parseTableOptions: function(opts) {
            let me = this;
            let tableOptions = this.options.fieldRenders && this.options.fieldRenders.tableOptions;
            _.extend(opts, {
                columns: this.parseTableColumns(opts.columns),
                showHelpTip: true, //显示表头字段提示信息
                isMyObject: true, //按paas标准对象渲染 一般都为true,
                autoHeight: true, //高度随数据自从撑开
                maxHeight: $(window).height() - 275, //配合autoHeight 超出后显示滚动条
                showMultiple: true, //显示chekc框 没有理由修改
                showMoreBtn: false, //默认都是false 没有理由修改
                showFilerBtn: false, //默认都是false 没有理由修改
                doStatic: true, //表格为静态数据，不请求server 默认true 没有理由修改 
                showPage: false,//是否需要显示分页 默认都是false 没有理由修改 
                openStart: true, //默认都是true 没有理由修改
                zIndex: (this.model.get('zIndex') || 1000) + 1, //层级
                noCalculate: true, //默认都是true 没有理由修改
                //isOrderBy_allColumn: false, //所有字段支持排序 默认都是true 一般不用修改，除非业务方的表格不支持排序
                searchTerm: null, //默认都是true 没有理由修改
                showRequiredTip: true,//默认都是true 没有理由修改
                sizeType: 'md', //表格行高 没有理由修改
                noDataStyle: 'small', //表格无数据时的样式 没有理由修改
                beforeEditFn: _.bind(_.debounce(this.beforeEditCellHandle, 50), this), //单元格触发编辑之前的回调
                beforeDelCellFn: _.bind(this.beforeCleanCellHandle, this),//清除单元格之前的回调
                noSetBatchEdit: true,
                getFieldFilterOptions: (fieldName) => {
                    return this.model.getMDFieldOptions(this.options.apiName, this.options.recordType, fieldName)
                },
                getMasterData: () => {
                    return this.model.get('data');
                },
                fieldIsReadonly: (fieldName) => {
                    var cc = this.getColumnByFieldName(fieldName);
                    if(cc && !cc.isEdit) return true;
                    var tt = (this.model.getMDFieldAttrStatus(this.options.apiName, this.options.recordType) || {})[fieldName];
                    return tt && (tt.hidden || tt.readOnly || tt.forceReadOnly);
                },
                trigger_info: this.model.getTriggerInfo(),

                insertMuenFn(trData) {
                    if(!me.hasInsert(trData)) return;

                    let btns;
                    me.emit('getSingleButtonsHandle', {
                        callback(res) {
                            btns = _.map(res, a => {
                                return _.extend({}, a, {
                                    callback($event, data) {
                                        me.emit(a.action, _.extend({}, a.data, {
                                            insertedRowId: data.rowId
                                        }), $event);
                                    }
                                })
                            })
                        }
                    })
                    return btns;
                },

                forceTrWrap: this.options.fieldRenders && this.options.fieldRenders.cardConfig
            }, tableOptions && _.omit(tableOptions, ['renderComplete', 'isDrag']))
            this.afterParseTableOptions && this.afterParseTableOptions(opts);

            return opts;
        },

        //处理表格列
        parseTableColumns: function (columns) {
            this.__columns = columns;
            this._addCardColumn();
            this._addHandleColumn()._addWidthToColumn()._addBatchEditAttr()._addColumnRender()._parseColumn();
            delete this.__columns;
            // 硬代码方式走灰度 @780 todo
            // var selfInfo = FS.contacts.getCurrentEmployee();
            // if (selfInfo && ['74255','721788_sandbox','ylspjt','fktest1344','zjkxhy'].indexOf(selfInfo.enterpriseAccount) > -1) {
                if (this.model['mdparseTableColumns']) {
                    columns = this.model['mdparseTableColumns'](columns, this.options);
                }
            // }
            return columns;
        },

        _addCardColumn() {
            let me = this;
            let fr = this.options.fieldRenders;
            if(fr && fr.cardConfig) {
                this.$el.addClass('md20-table-card');
                let cr = this.parseCardConfig(fr.cardConfig, this.__columns, this.options.objectDescribe.fields);
                this.__columns.unshift({
                    data: '_card',
                    dataType: 'text',
                    width: 248,
                    title: fr.cardConfig.title,
                    isOrderBy: false,
                    is_fake: true
                });

                _.each(me.__columns, column => {
                    if(cr.depend_fields.indexOf(column.data) >= 0) {
                        column.isHidden = !!column.is_fake;
                    }
                });

                (fr.crm2kColumnRenders || (fr.crm2kColumnRenders = {}))._card = cr;
            }
        },

        //添加操作列
        _addHandleColumn: function() {
            var me = this;
            var btns = me.options.operateButtons || []

            me.__columns.push({
                data: '_handle',
                dataType: 'operate',
                width: _.max([104, (btns.length || 0.5) * 40 + 8 + (me.options.isDrag ? 16 : 0)]),
                title: $t("操作"),
                lastFixed: true,
                lastFixedIndex: 100,
                render: function (a, b, obj) {
                    return me.renderOperateColumn(a, b, obj, btns); 
                }
            })
            return me;
        },

        //给每列加上宽度
        _addWidthToColumn: function() {
            var twidth = _.extend({}, this.model.getMDTableWidth(this.options.apiName), this.options.fieldRenders && this.options.fieldRenders.columnWidthConfig);
            twidth && _.each(this.__columns, function (a) {
                var w = twidth[a.api_name];
                if (w) {
                    a.width = w;
                }
            })
            return this;
        },

        //给每列加上批量编辑按钮 todo 可以考虑提到model层处理。
        _addBatchEditAttr: function() {
            var filterBatchEditFields = (this.options.fieldRenders && this.options.fieldRenders.filterBatchEditFields) || [];
            _.each(this.__columns, (column) => {
                if(column.input_mode === 'scan_only') {
                    column.isEdit = false;
                    return;
                }
                if(column.end_time_field || column.start_time_field || _.contains(filterBatchEditFields, column.data)) {
                    column.noSupportBatchEdit = true;
                    return;
                }

                let wheres = column.wheres;
                if(wheres && wheres.length) {
                    if(column.where_type === 'function') {//apl函数筛选，不知道函数里是否用了当前数据的值，所以不支持批量编辑
                        column.noSupportBatchEdit = true;
                    } else {
                        column.noSupportBatchEdit = !!_.find(wheres, a => {
                            //value_type:8 五角关系
                            //value_type:2 三角四角关系，column.cascade_parent_api_name不为空，三角关系
                            let ss = _.find(a.filters, b => {
                                return b.value_type == 2 || b.value_type == 8 || b.value_type == 20;
                            })

                            return !!ss
                        })
                    }
                }
            })
            
            if(!this.options.supportBatchEdit) {
                _.each(this.__columns, function(column) {
                    column.noSupportBatchEdit = true;
                })
            }

            return this;
        },

        //每一列增加render
        _addColumnRender: function() {
            let fr = this.options.fieldRenders;
            if(!fr) return;

            var columnRenders = fr.columnRenders;
            columnRenders && _.each(this.__columns, function (a) {
                var render = columnRenders[a.data];
                if(render) {
                    _.isFunction(render) || (render = render.render);
                    a.__reRenderTextFn = function($td, formatVal, obj) {
                        $td.find('.crm_field_sss_ss').html(render(formatVal, obj, a));
                    }
                    a.render = function(formatVal, b, obj) {
                        return '<span class="crm_field_sss_ss">'  + render(formatVal, obj, a) + '</span>';
                    }
                }
            })

            var crm2kColumnRenders = fr.crm2kColumnRenders;
            crm2kColumnRenders && _.each(this.__columns, function (a) {
                if(a.render) return;

                var render = (crm2kColumnRenders[a.data] || {}).render;
                if(render) {
                    a.__reRenderTextFn = function($td, formatVal, obj) {
                        $td.find('.crm_field_sss_ss').html(render(formatVal, obj));
                    }
                    a.render = function(formatVal, b, obj) {
                        return '<span class="crm_field_sss_ss">'  + render(formatVal, obj) + '</span>';
                    }
                }
            })

            return this;
        },

        _parseColumn() {
            let {apiName, recordType} = this.options;
            let fieldMapping = this.model.get('mdFieldMapping');
            fieldMapping = fieldMapping && fieldMapping[apiName] && fieldMapping[apiName][recordType];
            let fields = (fieldMapping && fieldMapping.fields) || {};
            _.each(this.__columns, function (column) {
                column.is_fixed && (column.fixed = true);
                column.is_fake && (column.noSupportLock = true);
                column.isOrderBy = !column.is_fake && !column.lastFixed && !column.mask_field_encrypt;
                let tt = fields[column.data];
                tt && (column.label = column.title = tt.label);
            })
            return this;
        },

        renderOperateColumn: function(a, b, obj, btns) { //渲染操作列
            if(this.options.fieldRenders && this.options.fieldRenders.beforeOperate) {
                btns = this.options.fieldRenders.beforeOperate(btns, obj);
            }
            this.__operateBtns && (this.__operateBtns[obj.rowId] = btns);

            var mainStrs = this._renderOperateButtons(btns, obj);
            
            return `<div class="md20-tr-handle">${mainStrs.join('')}</div>`;
        },

        //刷新操作列按钮
        updateOperateBtns(rowId, obj) {
            var btns = this.options.operateButtons || [];
            var trData = this.getDataByRowId(rowId);
            var fieldRenders = this.options.fieldRenders;
            if(!fieldRenders || !fieldRenders.operateBtnsDepends || !fieldRenders.beforeOperate || !trData) return;
            if(!_.find(fieldRenders.operateBtnsDepends, k => _.has(obj, k))) return;

            
            btns = fieldRenders.beforeOperate(btns, trData);
            this.__operateBtns && (this.__operateBtns[rowId] = btns);

            var strs = this._renderOperateButtons(btns, trData);

            let $btns = $(strs.join(''));

            let $trHandle = this.findTrs([rowId]).find('.md20-tr-handle');
            $trHandle.html($btns);
            this._btnsStatus && _.each(this._btnsStatus[rowId], (status, action) => {
                $trHandle.find(`[data-action="${action}"]`)[status ? 'show' : 'hide']();
            })
        },

        _renderOperateButtons(btns, obj) {
            let widths = [], moreStrs = [], mainStrs = [];
    
            // 处理按钮
            _.each(btns, a => {
                let label = _.escape(a.label);
                if(a.isFold) {
                    moreStrs.push(`<li data-action="${a.action}">${label}</li>`);
                } else {
                    widths.push(countBtnLabelWidth(label));
                    mainStrs.push('<span class="hand-btn' + (a.className ? ' ' + a.className : '') + '" data-action="' + a.action + '">' + label + '</span>');
                }
            });

            // 添加更多按钮
            if(moreStrs.length) {
                widths.push(24);
                mainStrs.push(`<button class="fx-icon-more crm-md20-morebtn"><ul>${moreStrs.join('')}</ul></button>`);
            }

            // 添加拖拽按钮
            if(this.options.isDrag && (!this.isTree || !obj.parent_rowId)) {
                mainStrs.push('<span class="hand-btn tr-drag fx-icon-drag" data-action="dragHandle"></span>');
            }

            // 计算操作列宽度
            obj.__operateWidth = widths.reduce((pre, cur) => pre + cur, 0) + widths.length * 8 + (this.options.isDrag ? 32 : 10);
            
            this._updateOpereateWidth();

            return mainStrs;
        },

        _updateOpereateWidth() {
            if(!this._updateOpereateWidthHandle) {
                this._updateOpereateWidthHandle = _.debounce(() => {
                    let max = 104;
                    _.each(this.getTableData(), a => {
                        if(a.__operateWidth && a.__operateWidth > max) {
                            max = a.__operateWidth;
                        }
                    });
                    if(_.isNumber(max)) {
                        this.table.table && this.table.table.updateOpereateWidth(max);
                    }
                }, 50);
            }
            this._updateOpereateWidthHandle();
        },

        tableComplete: function() { //表格初始化完成
            this.updateByStaticData(this.options.datas);
            this._toggleHasDataClass();
            this.renderOther && this.renderOther();
            this.setFieldAttrHandle();
            this.model.trigger('mdTableRenderComplete', this.options.apiName, this.options.recordType);
            this.handleCompleteCallback();
        },

        _toggleHasDataClass() {
            let datas = this.getTableData() || [];
            this.$el.parent().toggleClass('md20-table-nodata', !datas.length);
            this.model.newUpdateMDData(datas, this.options.apiName, this.options.recordType);//同步数据
        },

        //选中不选中
        toggleCheckedHandle(rowIds, isChecked) {
            this.table && this.table.table && this.table.table.setCheckStatusByPos(_.map(rowIds, rowId => this.getPosByRowId(rowId)), !isChecked);
        },

        handleCompleteCallback() {
            let fr = this.options.fieldRenders;
            if(!fr.tableOptions || !fr.tableOptions.renderComplete) return;

            let Model = Backbone.Model.extend({
                check: (rowIds) => {
                    this.table && this.table.table && this.table.table.setCheckStatusByPos(_.map(rowIds, rowId => this.getPosByRowId(rowId)));
                },
                unCheck: (rowIds) => {
                    this.table && this.table.table && this.table.table.setCheckStatusByPos(_.map(rowIds, rowId => this.getPosByRowId(rowId)), true);
                },
                getCheckedData: () => {
                    return this.getCheckedData();
                },
                objectApiName: this.options.apiName,
                recordType: this.options.recordType
            })

            fr.tableOptions.renderComplete(this.frModel = new Model());
        },

        renderOther() {
            this.renderCount();
            let tableFooterSlot = this.options.fieldRenders.tableFooterSlot;
            if(tableFooterSlot) {
                let $footerSlot = $('<div class="footer-slot"></div>');
                this.$el.after($footerSlot);
                this.footerInstance = tableFooterSlot($footerSlot[0], this.options.datas || []);
            }
        },

        //统计字段
        renderCount() {
            let el = $('<div style="text-align:center;margin-top:8px;"></div>');
            this.$el.after(el);
            require.async('crm-modules/components/count/count', Count => {
                if(!this.table) return;
                

                this._countComp = new Count({
                    el
                })

                this.updateCount();
            })
        },

        updateCount() {
            let {apiName, recordType} = this.options;
            let fields = this.model.getMDSummaryInfo(apiName, recordType);
            if(!fields.length) return;

            this._countComp && this._countComp.render([{
                fields,
                dataList: this.getTableData(),
                title: $t('crm.count.alldata', null, '所有数据')
            }])
        },

        updateByStaticData: function(datas) {
            this.table.doStaticData(datas || []);
            this.toggleCheckBoxByDatas(datas);
            this.excuteaLayoutRuleByList(datas);
        },

        actionHandle: function(action, arg1, arg2) {
            this[action] && this[action](arg1, arg2);
            arg1 && arg1.$tr && arg1.$tr.find('.crm-md20-morebtn').blur();
        },

        srcollToHandle(rowId) {
            try {
                let datas = this.getTableData();
                let length = datas.length;
                if(length > 15 && datas[length - 1].rowId === rowId) {
                    this.table.table.scrollToTr20(length - 1);
                }
            } catch(e) {}
        },

        //设置列的隐藏 只读
        //fieldAttr: {sex: {hidden: false | true, readOnly: false | true}}
        setFieldAttrHandle: function(fieldAttr) {
            this.__setFieldAttr(this.getTableData(), fieldAttr);
        },

        //设置指定行的字段状态
        //fieldAttr: {rowId: {readOnly: {name: true, sex: false}}}
        setRowFieldAttrHandle: function(fieldAttr) {
            _.each(fieldAttr, (attr, rowId) => {
                var cellsStatus = {};
                _.each(attr.readOnly, (v, k) => {
                    (cellsStatus[k] || (cellsStatus[k] = {}))[v ? 'readonly' : 'notreadonly'] = true;
                })
                _.each(attr.hidden, (v, k) => {
                    (cellsStatus[k] || (cellsStatus[k] = {}))[v ? 'hide' : 'show'] = true;
                })
                
                if(attr.required) {
                    let trData = this.getDataByRowId(rowId);
                    _.each(attr.required, (v, k) => {
                        (cellsStatus[k] || (cellsStatus[k] = {}))[v ? 'require' : 'notrequire'] = true;
                        let __rules = trData.__srules;
                        if(!__rules) {
                            __rules = trData.__srules = [];
                        }
                        if(v) {
                            __rules.push(k);
                        } else {
                            trData.__srules = _.without(__rules, k);
                        }
                    })
                }
                
                this.setCellsStatus(cellsStatus, rowId);
            })
        },

        __setFieldAttr: function(data, fieldAttr) {
            fieldAttr || (fieldAttr = this.model.getMDFieldAttrStatus(this.options.apiName));
            _.isArray(data) || (data = [data]);
            if(!fieldAttr) return;

            _.each(data, (a, i) => {
                var cellsStatus = {};
                _.each(fieldAttr, (item, fieldName) => {
                    var column = this.getColumnByFieldName(fieldName);
                    if(!column) return;
                    let tt = (cellsStatus[fieldName] || (cellsStatus[fieldName] = {}));
                    if(item.forceReadOnly !== void 0) {
                        tt[item.forceReadOnly ? 'readonly' : 'notreadonly'] = true;
                    }
                    if(column.is_required) return;//必填不予响应

                    var rules = a.__rules;
                    if(item.required !== void 0) {
                        if(item.required) {
                            if(!column.is_readonly) {
                                tt['require'] = tt['show'] = true
                            }
                        } else {
                            tt['notrequire'] = true;
                        }
                    }

                    //if(tt.require) return;//必填不予响应

                    if(item.hidden !== void 0 && !tt.require) {
                        if(!(rules && rules.required_field && _.contains(rules.required_field, fieldName))) {
                            tt[item.hidden ? 'hide' : 'show'] = true;
                            // if(!(rules && _.contains(rules.hide_field) && _.contains(rules.required_field, fieldName))) {
                            //     tt.hide = true;
                            // }
                        }
                    }
                    if(item.readOnly !== void 0 && !column.is_readonly) { //只读的字段不响应
                        tt[item.readOnly ? 'readonly' : 'notreadonly'] = true;
                    }
                })

                let item = fieldAttr[a.rowId];
                if(item) {
                    _.each(item.required, (v, fieldName) => {
                        let column = this.getColumnByFieldName(fieldName);
                        if(!column) return;
                        if(column.is_readonly) return;//只读的字段不响应
                        if(column.is_required) return;//必填不予响应
                        let tt = (cellsStatus[fieldName] || (cellsStatus[fieldName] = {}));
                        delete tt['require'];
                        delete tt['notrequire'];

                        if(v) {
                            tt['require'] = tt['show'] = true;
                            delete tt['readonly'];
                            delete tt['hide'];
                        } else {
                            tt['notrequire'] = true;
                        }
                    })
                    let rules = a.__rules;
                    _.each(item.hidden, (v, fieldName) => {
                        let column = this.getColumnByFieldName(fieldName);
                        if(!column) return;
                        if(column.is_readonly) return;//只读的字段不响应
                        if(column.is_required) return;//必填不予响应
                        let tt = (cellsStatus[fieldName] || (cellsStatus[fieldName] = {}));
                        if(tt.require) return;//必填不予响应
                        if(!tt.notrequire && (rules && rules.required_field && _.contains(rules.required_field, fieldName))) return;

                        delete tt['hide'];
                        delete tt['show'];

                        tt[v ? 'hide' : 'show'] = true;
                    })
                    _.each(item.readOnly, (v, fieldName) => {
                        let column = this.getColumnByFieldName(fieldName);
                        if(!column) return;
                        if(column.is_readonly) return;//只读的字段不响应
                        if(column.is_required) return;//必填不予响应
                        let tt = (cellsStatus[fieldName] || (cellsStatus[fieldName] = {}));
                        delete tt['readonly'];
                        delete tt['notreadonly'];

                        tt[v ? 'readonly' : 'notreadonly'] = true;
                    })
                }

               _.isEmpty(cellsStatus) || this.setCellsStatus(cellsStatus, a.rowId);
            })

            let allColumns = this.getAllColumns();
            let recordType = this.options.recordType;
            let flag;
            _.each(fieldAttr, (item, fieldName) => {
                let column = _.find(allColumns, column => column.data === fieldName || (recordType + column.data) === fieldName)
                if(!column) return;
                if(item.customHidden === void 0) return;
 
                if(column.__customHidden === item.customHidden) return;

                flag = true;
                this.xflag = true;

                column.__customHidden = item.customHidden;

                this.table[item.customHidden ? 'hideColumn_new' : 'showColumn_new'](column.data);
            })

            flag && this.__resizeHandle();
        },

        __resizeHandle() {
            if(!this._timerResize) {
                this._timerResize = _.debounce(() => {
                    this.resizeHandle();
                }, 100)
            }
            this._timerResize();
        },

        setTableBtnsHandle: function(trBtns, tableBtns) {
            var $last = this.$el.find('.main .fix-end-b:first');
            _.each(trBtns, (item, rowId) => {
                var pos = this.getPosByRowId(rowId);
                var $tr = $last.find(`[data-index="${pos}"]`);
                _.each(item, (status, action) => {
                    if(!this._btnsStatus) {
                        this._btnsStatus = {};
                    }
                    if(!this._btnsStatus[rowId]) {
                        this._btnsStatus[rowId] = {};
                    }
                    this._btnsStatus[rowId][action] = status;
                    
                    $tr.find(`[data-action="${action}"]`)[status ? 'show' : 'hide']();
                });
            })
            
            //控制表头批量按钮
            var batchEdit = tableBtns && tableBtns.tableBatchEditHandle;
            if(batchEdit !== void 0) {
                this.$el[batchEdit ? 'removeClass' : 'addClass']('crm-field-md20-table__hideBatch');
            }
        },

        fullStatusChangelHandle: function() {
            this.resizeHandle();
        },

        resizeHandle: function() {
            if(!this.table) return;
            let tableOptions = this.options.fieldRenders && this.options.fieldRenders.tableOptions;
            this.table[tableOptions && tableOptions.height !== void 0 ? 'resize' : 'toggleFullHeight']();
        },

        cancelSelectedHandle: function() {
            this.table && this.table._clearChecked();
            this.emit('checkHandle');
        },

        //res: {add: [{...}], update: {rowId: {....}, del: [rowId, rowId...], insert:[{insertRowId: 'xxx', datas: [{.....}]}]}}
        mdupdateHandle: function(res) {
            if(!this.table) return;

            this._hackColumnDisplay();

            let fr = this.options.fieldRenders;
            _.each(fr && fr.beforeMDUpdate, fn => {
                _.isFunction(fn) && fn(res);
            })

            res.del && this.delRow(res.del);
            res.add && this.addRow(res.add);
            res.insert && this.insertRow(res.insert);
            res.update && this.updateRow(res.update);

            res.mdSort && this.sortByRowIds(res.mdSort);

            this.updateDataStatus(res);
            
            let datas = this.getTableData();
            if(!datas.length) {
                this.updateByStaticData();
            }

            setTimeout(() => {
                try {
                    this.footerInstance && this.footerInstance.update && this.footerInstance.update(datas);
                    this.frModel && this.frModel.trigger('update', datas);
                    this._setTrCss(res);
                    _.each(res.update, (obj, rowId) => {
                        this.updateOperateBtns(rowId, obj);
                    })
                } catch(e) {}
            })

            this._toggleHasDataClass();

            this.updateCount();
        },

        _hackColumnDisplay() {
            if(!this.xflag) return;

            this.xflag = false;

            let datas = this.getTableData();

            if(!datas.length) {
                this.__resizeHandle();
            }
        },

        sortByDatas(datas) {
            this.table.sortByDatas(datas);
            this.model && this.model.sortMDByDatas && this.model.sortMDByDatas(datas);//同步数据
        },

        sortByRowIds:_.debounce(function(rowIds) {
            let datas = this.getTableData();
            if(!rowIds || rowIds.length !== datas.length) return;
            if(rowIds.join('') === _.pluck(datas, 'rowId').join('')) return;

            this.sortByDatas(_.map(rowIds, rowId => _.findWhere(datas, {rowId: rowId})));
        }, 100),

        updateDataStatus(res) {
            let _datas = [];
            if(res.add) {
                _datas = _datas.concat(_.isArray(res.add) ? res.add : [res.add]);
            }
            _.each(res.insert, a => _.each(a.datas, b => _datas.push(b)));
            if(_datas.length) {
                this.excuteaLayoutRuleByList(_datas);
                this.__setFieldAttr(_datas);
                this.toggleCheckBoxByDatas(_datas);
            }
        },

        _setTrCss(res) {
            _.each(res.update, (a, rowId) => {
                a.__trCss && this.table.findTrs(this.getPosByRowId(rowId)).css(a.__trCss);
            })
            _.each(res.add, (a) => {
                a.__trCss && this.table.findTrs(this.getPosByRowId(a.rowId)).css(a.__trCss);
            })
            _.each(res.insert, (a) => {
                _.each(a.datas, b => {
                    b.__trCss && this.table.findTrs(this.getPosByRowId(b.rowId)).css(b.__trCss);
                })
            })
        },

        //添加
        addRow: function(data) {
            this.table && this.table.addRow(data);
        },

        //更新行数据
        updateRow: function(item) {
            var me = this;
            var list = this.getTableData();
            var opts = {};
            _.each(list, function(a, i) {
                var obj = item[a.rowId];
                if(obj) {
                    var tobj = me._diff(obj, a);
                    var keys = me._hackCRM2kKeys(_.keys(tobj), obj, a, _.keys(obj));
                    keys.length && (opts[i] = {cells: keys, data: obj});
                }
            })
            this.beforeUpdateRow && this.beforeUpdateRow(opts);
            _.isEmpty(opts) || this.table.setCellsVal(opts);
            this.excuteaLayoutRuleByList(item);
        },

        _hackCRM2kKeys(keys, data, trData, allKeys) {
            let fr = this.options.fieldRenders;
            if(fr && fr.crm2kColumnRenders) {
                let ak = [];
                _.each(allKeys, k => {
                    _.find(fr.crm2kColumnRenders, (a, b) => {
                        if(a.depend_fields && _.contains(a.depend_fields, k)) {
                            ak.push(b);
                            if(data[b] === void 0) {
                                data[b] = trData[b];
                            }
                        }
                    });
                })
                keys = _.union(keys, ak);
            }

            if(fr && fr.columnRenders) {
                let akk = [];
                _.each(allKeys, k => {
                    _.find(fr.columnRenders, (a, b) => {
                        if(a.depend_fields && _.contains(a.depend_fields, k)) {
                            akk.push(b);
                            if(data[b] === void 0) {
                                data[b] = trData[b];
                            }
                        }
                    });
                })
                keys = _.union(keys, akk);
            }

            return keys;
        },

        //仅更新前后有数据变化的
        _diff: function(d, l) {
            var model = this.model;
            var tobj = {};
            var columns = this.getAllColumns();
            var types = ['number', 'percentile', 'currency', 'text', 'formula'];
            _.each(d, function(v, k) {
                if(k === 'record_type' || k === 'object_describe_api_name' || k === 'rowId') return;
                var tt = _.findWhere(columns, {data: k});
                if(tt && tt.mask_field_encrypt) {
                    tobj[k] = v;
                    return;
                }
                if(model.dataIsNull(v) && model.dataIsNull(l[k])) return;
                if(!tt) {
                    l[k] = v;
                    return;
                }
                if(_.contains(types, tt.return_type || tt.render_type || tt.type) && (v === l[k])) return;

                tobj[k] = v;
            })

            return tobj;
        },

        //data: [{insertRow: {....}, datas: [{},....]}]
        //在指定的行下面插入数据
        insertRow: function(data) {
            var me = this;
            var list = me.getTableData();
            var table = me.table;
            _.each(data, function(a) {
                var pos;
                _.find(list, function(b, i) {
                    if(b.rowId === a.insertRow.rowId) {
                        pos = i;
                        return true;
                    }
                })
                table.insertRow(a.datas, {
                    pos: pos,
                    isBefore: a.isBefore
                })
                // me.excuteaLayoutRuleByList(a.datas);
                // me.__setFieldAttr(a.datas)
            })
        },

        //删除行
        delRow: function(rowIds) {
            if(!rowIds) return;
            var index = [];
            _.each(this.getTableData(), function(a, i) {
                _.contains(rowIds, a.rowId) && index.push(i);
            })
            index.length && this.table.delRow(index);
            this.trErrorHandle();
        },

        getPosByRowId: function(rowId) {
            var pos;
            _.find(this.getTableData(), function(a, index) {
                if(a.rowId === rowId) {
                    pos = index;
                    return true;
                }
            })
            return pos;
        },

        //设置单元格状态
        //  show: true
        //  hide: true
        //  require:
        //  notrequire:
        //  readonly:
        //  notreadonly
        //cellsStatus: {name: {show.....}, price: {show.....}}
        setCellsStatus: function(cellsStatus, rowId) {
            var table = this.table;
            var pos = this.getPosByRowId(rowId);
            _.each(cellsStatus, function(opts, fieldName) {
                table.setCellsStatus(fieldName, pos, opts);
            })
        },

        //获取当前表格数据
        getTableData: function() {
            var obj = this.table && this.table.getCurData();
            return obj ? obj.data : [];
        },

        getDataByRowId: function(rowId) {
            return _.findWhere(this.getTableData(), {rowId: rowId});
        },

        getAllColumns: function() {
            return this.table.getAllColumns();
        },

        //获取已选中的数据
        getCheckedData: function() {
            return this.table ? this.table.getCheckedData() : [];
        },

        //行点击事件
        trclickHandle: function(trData, $tr, $target) { //行点击
            var action = $target.attr('data-action');
            if(!action) return;
            if(this[action]) return this[action](trData, $tr, $target);

            this.emit(action, {data: trData, $target: $target, $tr: $tr});
        },

        //行错误回调 通知上层组件更新错误信息
        trErrorHandle: function(obj) {
            var rowNo = (obj || this.table.getEditValidInfo()).rowNo;
            this.emit('updateErrorHandle', rowNo);
        },

        findTrs(rowIds, isTb, isFirst) {
            return this.table.findTrs(_.map(rowIds, rowId => this.getPosByRowId(rowId)), isTb, isFirst);
        },

        cellChangeHandle(data, column, type, opts) {
            try {
                if(!_.contains(['image', 'file_attachment'], column.type)) return;
                let fieldName = column.data;
                this.emit('batchEditHandle', {noBatch: true, editField: fieldName , blurField: fieldName, modiFiedDatas: [{rowId: opts.cellData.rowId, [fieldName]: data}]});
            } catch(e) {}
        },

        //批量编辑
        batchCellChangeHandle: function(opts) {
            var tt = [];
            var isobj = !_.isArray(opts.val) && _.isObject(opts.val);
            var flag = _.contains([2, 3, 33], opts.column.dataType) && (opts.type == 1 || opts.type == 2);
            var fieldName = opts.column.api_name;
            _.each(opts.changeData, function(a) {
                var tm;
                if(isobj) {
                    tm = _.extend({rowId: a.rowId}, opts.val);
                } else {
                    tm = {};
                    tm.rowId = a.rowId;
                    tm[fieldName] = flag ? a[fieldName] : opts.val;
                }
                tt.push(tm);
            })

            var me = this;
            _.each(tt, function(a) {
                _.extend(a, _.pick(me.getDataByRowId(a.rowId), ['record_type', 'object_describe_api_name']));
            })

            if(opts.column.render_type === 'big_text') {
                _.each(tt, t => {
                    t[fieldName + '__o'] = (t[fieldName] || '').slice(0, 100);
                })
            }

            this.emit('batchEditHandle', {editField: _.isArray(opts.column) ? _.pluck(opts.column, 'data') : opts.column.data , blurField: opts.column.data, modiFiedDatas: tt, callback() {
                if(opts.isContinue) {
                    me.tableBatchEditHandle();
                }
            }});
        },

        tableBatchEditHandle: function() {
            var list = this.getTableData();
            if (!list.length) return;
            this.table.batchEditColumns(true, true, {
                ignoreEdit: true,
                upDown: true,
                showContinue: true
            })
        },

        //拦截底层编辑组件 启用本地编辑组件
        beforeEditCellHandle: function(opts, next) {
            let me = this, rowId = opts.data.rowId, fieldName = opts.column.data;
            if(opts.$target && opts.$target.closest('.td-status-readonly').length > 0) {
                me.emit('readOnlyCellEditHandle', {
                    rowId,
                    fieldName,
                    $target: opts.$target
                })
                return;
            };
            if(_.contains(['image', 'file_attachment', 'big_file_attachment'], opts.column.type)) { //图片附件签名 定位 不影响数据变化的还是走底层 后期统一迁移到fieldedittask
                next();
                return;
            }

            opts.data = function() { //data必须是方法的原因是因为 可能执行编辑的时候data已经被改变了，需要保证在执行编辑的时候获取到的是最新的数据
                return me.getDataByRowId(rowId);
            }

            let fr = (this.options.fieldRenders || {}).columnRenders;
            if(fr && fr[fieldName] && fr[fieldName].actionCallBacks && opts.e) {
                opts.actionCallBack = fr[fieldName].actionCallBacks[$(opts.e.target).attr('data-action')];
            }

            next(false);
            me.emit('cellEditHandle', opts);
        },

        //清除单元格值之前的回调
        beforeCleanCellHandle: function(param) {
            if(param.column.end_time_field) {
                param.changeData[param.column.end_time_field.api_name] = null;
            }
            if(param.column.start_time_field) {
                param.changeData[param.column.start_time_field.api_name] = null;
            }
            this.emit('cellCleanHandle', {data: param.cellData, cleanData: param.changeData, cleanFieldName: param.delColumn.data});
        },

        emit: function(action, opts) {
            this.model.trigger('action:' + this.options.eventKey, action, opts);
        },

        //表格行数据被选中/取消 
        checkboxclickHandle: function(isChecked, $checkItem, a, b, isAll, pos) {
            if(!this.__checkHandle) {
                this.__checkHandle = _.debounce(() => {
                    let cd = this.getCheckedData();
                    this.emit('checkHandle', cd);
                    this.frModel && this.frModel.trigger('check.change', cd || []);
                }, 100)
            }
            this.__checkHandle();

            this.afterCheckHandle && this.afterCheckHandle(isChecked, $checkItem, isAll, pos);
        },

        //根据数据属性设置checkbox的可见不可见
        toggleCheckBoxByDatas(datas) {
            datas = _.filter(datas, a => a.__hideCheckBox);
            datas.length && this.table.toggleCheckBoxByPos(_.map(datas, data => this.getPosByRowId(data.rowId)), false);
        },

        //表头排序变化
        staticParamChangeHandle: function(param) {
            this.__sort(param);
            this.emit('checkHandle', []);
        },

        //列宽变化 更新本地存储的列配置
        tdWidthChangeHandle: function(widths) {
            var w = {};
            _.each(widths, function(a) {
                w[a.field_name] = a.width;
            })
            this.model.upDataMDTdWidth(this.options.apiName, w);
        },

        //验证当前表格是否存在错误数据
        validate: function(list) {
            var me = this;;
            var errorTrs = {};
            _.each(list || this.getTableData(), function (data, pos) {
                if(data.isFake) return;//分组的数据不校验 相当于isFake 假数据
                var cellError = me.validateTrData(data, pos); 
                if (cellError) {
                    errorTrs[pos] = cellError;
                }
            })

            this.table.toggleValidError(errorTrs);
            this.emit('updateErrorHandle', _.keys(errorTrs).length);

            return _.isEmpty(errorTrs);
        },

        //验证一行数据数据是否正确
        validateTrData: function (data, pos) {
            var _cellStatus = data._cellStatus || {};
            var columns = this.getAllColumns();
            var errorCells = [];
            _.each(columns,  (field) => {
                var k = field.api_name;
                var vv = data[k];
                if(field.mask_field_encrypt && data[k + '__encrypt']) return;

                if (field.is_required || (_cellStatus[k] &&  _cellStatus[k].require)) { //验证必填
                    this.model.dataIsNull(vv) && errorCells.push(k);
                }
            })

            return errorCells.length && errorCells;
        },

        getValue: function() {
            var list = this.getTableData();
            if(this.options.fieldRenders && this.options.fieldRenders.parseData) {
                list = _.filter(this.options.fieldRenders.parseData(_.map(list, a => _.extend({}, a)).concat(this.__hideDatas || [])), a => !a.__isNeedHideFromUI);
            }
            this.validate(list);
            return list;
        },

        //历史遗留快捷键功能，方向键控制编辑
        keyDownHandle: function(opts) {
            if(!opts.keyCode) return;

            var me = this;
            var columns = this.table.getAllColumns();
            var column = _.findWhere(columns, {data: opts.fieldName});
            var columnIndex =  _.indexOf(columns, column);
            var pos = this.getPosByRowId(opts.rowId);
            var trData = this.getDataByRowId(opts.rowId);
            var trHideFields = (trData.__rules || {}).hide_field || [];
            var table = this.table.table;
            var list = this.getTableData();
            var totalNum = list.length;
            var columnNum = columns.length;

            var innerFn1 = function() {
                var isBreak;
                var tc = columns[columnIndex];
                if(tc && tc.isEdit &&  _.contains(['text', 'long_text', 'email', 'url', 'number', 'currency', 'percentile', 'phone_number'], tc.type) && !_.contains(trHideFields, tc.data)) {
                    table.scrollToTd20(tc.data);
                    setTimeout(function() {
                        table.findTds([tc.data], table.findTrs(pos)).find('.td-cell-edit').trigger('click');
                    }, 50);
                    
                    isBreak = true;
                }
                return isBreak;
            }
            var innerFn2 = function() {
                var isBreak;
                var td = list[pos] || {};
                if(!_.contains((td.__rules || {}).hide_field || [], column.data)) {
                    table.scrollToTr20(pos);
                    setTimeout(function() {
                        table.findTds([column.data], table.findTrs(pos)).find('.td-cell-edit').trigger('click');
                    }, 50);
                    isBreak = true;
                }
                return isBreak;
            }

            switch(opts.keyCode) {
                case 37: //←
                    while(columnIndex > 0) {
                        if(innerFn1(--columnIndex)) break;
                    }
                    break;
                case 38: //↑
                    while(pos > 0) {
                        if(innerFn2(--pos)) break;
                    }
                    break;
                case 39: //→
                    while(columnIndex < columnNum) {
                        if(innerFn1(++columnIndex)) break;
                    }
                    break;
                case 40: //↓
                    if(pos === totalNum - 1) {
                        this.emit('singleTrAddHandle', {
                            callback: function() {
                                innerFn2(++pos);
                            }
                        });
                    } else {
                        while(pos < totalNum) {
                            if(innerFn2(++pos)) break;
                        }
                    }
            }
        },

        noSupportPaste() {
        	let apiName = ['SalesOrderObj', 'QuoteObj', 'NewOpportunityObj', 'SaleContractObj'];
        	if(apiName.includes(this.model.get('apiname'))) return;
            if(/__c$/.test(this.model.get('apiname') || '__c')) return;
            return !/__c$/.test(this.options.apiName);
        },

        //高亮闪烁显示某行数据
        twinkleHighlightTr() {
            findTrs
        },

        destroy: function() {
            try {
                this.footerInstance && this.footerInstance.destroy && this.footerInstance.destroy();
                this.frModel && (this.frModel.destroy(), this.frModel = null);
                this._countComp && (this._countComp.destroy(), this._countComp = null);
            } catch(e) {};
            this.stopListening(), this.off();
            this.table && this.table.destroy();
            this.$el.remove();
            this.table = this.options = this.events = this.__operateBtns = null;
        }
    }, require('./rule'), require('./drag'), require('./sort')))
})
