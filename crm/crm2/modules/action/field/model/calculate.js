define(function(require, exports, module) {
    var util = FS.crmUtil;
    var msgs = $t('crm.计算错误提示', null, '取消后本次修改无效！是否继续重试？&本地网络连接问题，&UI事件未正确执行，是否重试？&数据未正确计算，是否重试？').split('&');
    return {
        //老的对象还在继续调用calculate,数据的唯一key是index，新的md20以rowId更新数据,需要进行存储转换
        __addRowIdToIndex(opts) {
            if(_.isEmpty(opts.detailDataMap)) return;

            let _indexToRowId = {}, flag;
            _.each(opts.detailDataMap, (item, objApiName) => {
                if(_.find(item, (obj, index) => {
                    _indexToRowId[objApiName + index] = obj.rowId;
                    return !obj.rowId;
                })) {
                    flag = true;
                }
            })

            if(!flag) {
                opts._indexToRowId = _indexToRowId;
            }
        },

        calculate: function(opts) {
            var me = this;
            var uiEvents=true&&(opts.fields.__uievents&&opts.fields.__uievents.length>=1);

            me.__addRowIdToIndex(opts);

            opts = me._analysisUIEvents(opts);

            if(!opts) {
                return {
                    abort: function() {}
                };
            };
            let dataArgs={
                masterObjectApiName: opts.apiname || this.get('apiname'),
                masterData: me._filterrCalculateMasterData(opts.data || this.get('data')),
				detailDataMap: me._filterCalculateMDData(opts.detailDataMap, opts.fields),
                calculateFieldApiNames: opts.fields,
                modifiedObjectApiName: opts.modifiedObjectApiName,
                modifiedDataIndexList: opts.modifiedDataIndexList,
                calculateAllFields: opts.calculateAllFields,//是否计算传过来的所有字段
                calculateFields: me._getCalculateFieldOrder(opts.fields),
                seriesId: me.requestId,
                maskFieldApiNames: me._getCalculateEncryptFields(opts.fields)
            };

            if(opts.parseCalculateParam) {
                dataArgs = opts.parseCalculateParam(dataArgs);
            }

            var post = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/calculate/service/batchCalculate',
                data:me.parseBeforeCalculate(dataArgs,opts) ,
				success: function (res, jqXHR) {
                    var result;
                    var isSuccess = res.Result.StatusCode === 0;
                    if(isSuccess) {
                        result = res.Value.calculateResult;
                        res._indexToRowId = opts._indexToRowId;
                        me._suppEncryptData(result);
                    } else {
                        opts.error && opts.error(res.Result.FailureMessage);
                    }
					opts.callback && opts.callback(result || {}, isSuccess, res, jqXHR);
                    if(!uiEvents){
                        opts.notifyFun&&opts.notifyFun(opts,result);
                        opts.endCb && opts.endCb(opts,result, void 0, !isSuccess);
                    }
                },
                complete: opts.complete,
                error: opts.fail
            }, {
                errorAlertModel: 1
            });

            return post;
        },

        //提交计算接口前可处理数据的hook函数
        parseBeforeCalculate(data,opts){
            return data;
        },

        mdCalculate: function(opts) {
            var me = this;
            return new Promise(function(resolve, reject) {
                if(_.keys(opts.fields).length === 1 && opts.fields.__uievents) { //仅有ui事件
                    resolve();
                    return;
                }
                var _timer = new Date().getTime();

                me._calculateMDAjax = util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/calculate/service/batchCalculate',
                    data: {
                        masterObjectApiName: opts.apiname || me.get('apiname'),
                        masterData: me._filterrCalculateMasterData(opts.data || me.get('data')),
						detailDataMap: me._filterCalculateMDData(opts.detailDataMap, opts.fields),
                        calculateFieldApiNames: opts.fields,
                        modifiedObjectApiName: opts.modifiedObjectApiName,
                        modifiedDataIndexList: opts.modifiedDataIndexList,
                        calculateFields: me._getCalculateFieldOrder(opts.fields),
                        seriesId: me.requestId
                    },
                    success: function(res) {
                        if(res.Result.StatusCode === 0) {
                            resolve(res.Value.calculateResult);
                        } else {
                            reject((new Date().getTime() - _timer < 20 ? msgs[1] : res.Result.FailureMessage) + msgs[3], res);
                        }
                    },
                    complete: function() {
                        me._calculateMDAjax = null;
                    },
                    error: function(e) {
                        reject(msgs[1] + msgs[3]);
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },


        excutemdUIEvent: function(opts) {
            var me = this;

            var uiEvents = this.get('uiEvents');
            var __uievents = opts.field.__uievents;
            var event;
            var apiname = opts.modifiedObjectApiName;
            var editing_object_data;
            if(opts.modifiedDataIndexList && opts.modifiedDataIndexList.length) { //从数据编辑 增加
                if(_.contains(__uievents, 'a')) { //添加行
                    event = uiEvents[apiname]['add_event'];
                } else { //编辑
                    event = uiEvents[apiname][__uievents[0]];
                    editing_object_data = opts.modifiedDataIndexList[0]
                }
            } else { //删除行数据
                event = uiEvents[apiname]['del_event'];
            }
            return new Promise(function(resolve, reject) {
                if(!event) {
                    resolve();
                    return;
                }

                util.waiting($t('UI事件执行中') + '...', true);
                var _timer = new Date().getTime();
                me._uiEventAjax = util.FHHApi({
                    url: '/EM1HNCRM/API/v1/object/'+ me.get('apiname') +'/action/TriggerEvent',
                    data: {
                        event_id: event._id,
                        layout_api_name: me.get('layout_apiname'),
                        object_data: opts.master_data,
                        detail_object_data: me.suppDDM(opts.detailDataMap),
                        editing_object_data: editing_object_data,
                        new_details: opts.modifiedDataIndexList,
                        trigger_field_api_name: [opts.blurKey],
                        trigger_info: me.getTriggerInfo(),
                        seriesId: me.requestId,
                        maskFieldApiNames: me._getCalculateEncryptFields()
                    },
                    success: function(res) {
                        if(res.Result.StatusCode === 0) {
                            me._setFieldAttribute(res.Value.fieldAttribute);
                            me._toggleFieldOptions(res.Value.optionAttribute);
                            me._toggleMDStatus(res.Value.objectAttribute);
                            me._toggleFieldRemind(res.remind, '', true);

                            resolve(res.Value.data);
                            me._updateMDByUIEventResult(res);
                        } else {
                            reject((new Date().getTime() - _timer < 20 ? msgs[1] : res.Result.FailureMessage) + msgs[2], res);
                        }
                    },
                    complete: function() {
                        me._uiEventAjax = null;
                        util.waiting(false, true);
                    },
                    error: function() {
                        me._uiEventAjax = null;
                        reject(msgs[1] + msgs[2]);
                        util.waiting(false, true);
                    }
                }, {
                    errorAlertModel: 1
                })
            })
        },

        excuteUIEvent: function(opts,result,noUpdate) {
            var me = this;
            var apiname = me.get('apiname');
            var masterData =noUpdate?opts.masterData: me.get('data');
            util.mixCalRes(opts, result);
			if(result && result[apiname]) masterData = _.extend(masterData, result[apiname][0]);

            util.waiting($t(this.getFieldAttr(opts.blurKey).is_ocr_recognition ? '图片上传识别中,请稍后' : 'UI事件执行中') + '...', true);
            var detail_object_data = me.suppDDM(opts.detailDataMap);
            me._uiEventAjax = util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/'+ me.get('apiname') +'/action/TriggerEvent',
                data: {
                    event_id: opts.event._id,
                    layout_api_name: me.get('layout_apiname'),
                    object_data: masterData,
                    detail_object_data: detail_object_data,
                    editing_object_data: opts.editing_object_data,
                    new_details: opts._copyIndex || opts.new_details || me._getUIEventNewDetails(opts),
                    trigger_field_api_name: [opts.blurKey || opts.mdBlurKey || ''],
                    trigger_info: me.getTriggerInfo(),
                    seriesId: me.requestId,
                    maskFieldApiNames: me._getCalculateEncryptFields()
                },
                success: function(res) {
                    util.waiting(false, true);
                    me._masterCalculating = null;
                    if(opts.checkResultCb && opts.checkResultCb(res, opts) === false) return;

                    if(res.Result.StatusCode === 0) {
                        try {
                            let copyUiRes=CRM.util.mapObj(value=>value,res.Value.data);
                            me._suppEncryptData({}, res.Value.data);
                            me.__addUIFlag(res.Value.data, res.Value, {detail_object_data: detail_object_data});
                            if(!noUpdate){
                                me._updateDataByUIEventData(res.Value, opts);
                            }
                            opts.notifyFun&&opts.notifyFun(opts,result,copyUiRes)
                            opts.endCb && opts.endCb(opts, result, copyUiRes);
                        } catch(e) {
                            console.error(e)
                        }

                    } else {
                        util.alert(res.Result.FailureMessage);
                        opts.notifyFun&&opts.notifyFun(opts,result,res, true);
                    }
                },
                complete: function() {
                    me._uiEventAjax = null;
                },
                error: function() {
                    me._uiEventAjax = null;
                    util.waiting(false, true);
                    util.alert($t('网络错误，请稍后再试'));
                    opts.notifyFun&&opts.notifyFun(opts,result,{
                        error:true,
                        errorFrom:"uiEvent"
                    }, true);
                }
            }, {
                errorAlertModel: 1
            })
        },

        //给UI事件返回的数据增加标记
        __addUIFlag(data, rv, opts) {
            if(!data) return;

            let md = (this.get('forms') || {}).md;
            if(md && md.isMd20) {
                _.each(data, item => _.each(item && item.a, a => a._fromUIEvent = true));
            }

            let ddm = opts && opts.detail_object_data || {};

            _.each(rv && rv.detailRowFieldAttribute, (obj, apiname) => {
                let tt = (rv.detailFieldAttribute || (rv.detailFieldAttribute = {}));
                _.each(ddm[apiname], (item, k) => {
                    if(!item) return;
                    if(k !== item.rowId && obj[k]) {
                        obj[item.rowId] = obj[k];
                        delete obj[k];
                    }
                })
                tt[apiname] = _.extend(tt[apiname] || {}, obj);
            })

            _.each(data, obj => {
                _.each(obj.a, item => {
                    let __add_mark__ = item.__add_mark__;
                    if(__add_mark__) {
                        let newRowId = this.getRowUniqueId();
                        _.each(rv && rv.detailButton, arr => {
                            _.each(arr, t => {
                                _.each(t.buttons, button => {
                                    _.find(button.rowIds || [], (rowId, index) => {
                                        if(rowId === __add_mark__) {
                                            button.rowIds[index] = newRowId;
                                            return true;
                                        }
                                    })
                                })
                            })
                        })

                        _.each(rv && rv.detailFieldAttribute, (obj, apiname) => {
                            if(obj[__add_mark__]) {
                                obj[newRowId] = obj[__add_mark__];
                                delete obj[__add_mark__];
                            }
                        })

                        item.rowId = item.__add_mark_rowid__ = newRowId;
                        
                        delete item.__add_mark__;
                    }
                })
            })
        },

        //将md数据补充完整
        suppDDM: function (ddm) {
            var detailObjectList = this.get('detailObjectList');
            var dobj = {};
            _.each(detailObjectList, function (obj) {
                dobj[obj.objectDescribe.api_name] = {};
            })
            return _.extend(dobj, ddm);
        },

        _getUIEventNewDetails: function(opts) {
            if(!opts.__uiAddEvent) return;
            return opts._copyIndex || opts.modifiedDataIndexList;
            // var arr = [];
            // if(opts.detailDataMap && opts.modifiedDataIndexList && opts.modifiedDataIndexList.length) {
            //     var list = opts.detailDataMap[opts.modifiedObjectApiName];
            //     if(list) {
            //         _.each(opts.modifiedDataIndexList, function(index) {
            //             arr.push(index * 1);
            //         })
            //     }
            // }

            // return arr.length ? arr : void 0;
        },

        _analysisUIEvents: function(opts) {
            var __uievents = opts.fields.__uievents;
            if(!__uievents) return opts; //不需要执行任何ui事件
            var me = this;
            var apiname = opts.modifiedObjectApiName;
            var callback = opts.callback
            opts = _.extend({}, opts, {
                callback: function(result, isSuccess, res) {
                    opts.complete && opts.complete();
                    let args = arguments;
                    //计算接口结束
                    me.ocrService(opts.blurKey, opts).then(res => {
                        if(res) {
                            args[0] = res.Value.calculateResult || {};
                            callback.apply(this, args);

                            me._updateDataByUIEventData(res.Value, opts);

                            let uiRes = res.Value.uiEventResult && res.Value.uiEventResult.data;
                            opts.notifyFun && opts.notifyFun(opts, args[0], uiRes);
                            opts.endCb && opts.endCb(opts, args[0], uiRes);
                            return;
                        };

                        callback.apply(this, args);

                        if(!opts.event){
                            opts.notifyFun&&opts.notifyFun(opts, result);
                            opts.endCb && opts.endCb(opts, result);
                        } else {
                            me.excuteUIEvent(opts, result);
                        }
                    })
                },

                fields: _.extend({}, opts.fields)
            })

            var uiEvents = this.get('uiEvents');

            if(!apiname || (apiname === this.get('apiname'))) { //主对象字段触发的计算
                if(opts.blurKey) {
                    opts.event = uiEvents[this.get('apiname')] && uiEvents[this.get('apiname')][opts.blurKey];
                }
            } else {
                if((opts.modifiedDataIndexList && opts.modifiedDataIndexList.length) || opts.type === 'copy') { //从数据编辑 增加
                    if(_.contains(__uievents, 'a')) { //添加行
                        opts.__uiAddEvent = true;
                        opts.event = uiEvents[apiname]['add_event'];
                    } else { //编辑
                        opts.event = uiEvents[apiname][__uievents[0]];
                        opts.editing_object_data = opts.modifiedDataIndexList[0]
                    }
                } else { //删除行数据
                    opts.event = uiEvents[apiname]['del_event'];
                }
            }

            if(_.keys(opts.fields).length < 2) { //只有ui事件，不用计算
                setTimeout(function() {
                   opts.callback({}, true, {});
                })
                return;
            }

            delete opts.fields.__uievents;//终端自己加的参数不用给server提交

            return opts;
        },

        _updateDataByUIEventData: function(res, opts) {
            var data = res.data;
            var forms = this.get('forms');
            var apiname = this.get('apiname');
            if(data) {
                if(data[apiname]) { //更新主对象数据
                    this.newBatchUpdate(data[apiname], true, true);
                    delete data[apiname];
                }

                var md = forms.md; //更新从对象数据
                md && md.updateByUIEventData && md.updateByUIEventData(data, opts);
            }

            var field = (this.get('fields') || {})[opts.blurKey];
            if(field && field.is_ocr_recognition) {//图片ocr识别
                this.trigger(opts.blurKey + ':ocrerror', (res.ocrErrorResultMap && res.ocrErrorResultMap[opts.blurKey]) || []);
            }

            this._toggleFieldRemind(res.remind, opts.blurKey);
            this._setFieldAttribute(res.fieldAttribute);
            this._toggleFieldOptions(res.optionAttribute);
            this._toggleMDStatus(res.objectAttribute);

            this._updateMDByUIEventResult(res);
        },

        _setFieldAttribute: function(attr) {
            if(!attr || _.isEmpty(attr)) return;
            this._hackAreaUIResult(attr);

            var obj = {
                show_field: [],
                hide_field: [],
                readonly_field: [],
				no_readonly_field: [],
				required_field: [],
				no_required_field: [],
                fromUIEvent: true
            }
			var un = void 0;
            _.each(attr, function(a, k) {
				a.hidden !== un && obj[a.hidden ? 'hide_field' : 'show_field'].push(k);
				a.readOnly !== un && obj[a.readOnly ? 'readonly_field' : 'no_readonly_field'].push(k);
				a.required !== un && obj[a.required ? 'required_field' : 'no_required_field'].push(k);
                a.forceReadOnly !== un && obj[a.forceReadOnly ? 'force_readonly_field' : 'no_force_readonly_field'].push(k);
            })

            var me = this;
            setTimeout(function() {
                me.trigger('rule', obj);
            }, 200);

            this.__cacheFormRuleData(attr, 'masterfields');
        },

        //处理字段错误提示信息res
        _toggleFieldRemind(remind, blurKey, isMd) {
            //   "remind" : {
            //     content : "",// 提醒内容， 如果是红字提醒，内容为json的字符串形式， 其它类型，为普通字符串
            //     type : 1//  提醒类型， 1: 红字提醒， 2: toast提醒， 3: 弹窗提醒
            // },
            var forms = this.get('forms');
            var comp = blurKey && forms[blurKey];
            if(!remind) {
                comp && comp.hideUIError();
                blurKey && this._setFieldRemind(null, blurKey);
                return;
            };
            if(!isMd && remind.type == '1') {//从对象暂时处理不了错误提示
                if(remind.contentMap !== void 0) {
                    _.each(remind.contentMap, (v, k) => {
                        var tc = forms[k];
                        tc && tc[v !== '' ? 'showUIError' : 'hideUIError'](v);
                        this._setFieldRemind(v, k);
                    })
                } else {
                    comp && comp[remind.content ? 'showUIError' : 'hideUIError'](remind.content);
                    this._setFieldRemind(remind.content, blurKey);
                }
            } else if(remind.type == '2') {
                util.remind(remind.content);
            } else if(remind.type == '3') {
                util.alert(remind.content, null, {stopPropagation: false});
            } 
        },

        //记住字段级提示
        _setFieldRemind: function(remind, key) {
            var tmp = this._fieldRemind || (this._fieldRemind = {});
            if(remind) {
                tmp[key] = remind;
            } else {
                delete tmp[key]
            }
        },

        _getFieldRemind: function() {
            return this._fieldRemind || {};
        },

        //过滤参与计算的数据，只给server提交必要的字段，减少数据量
        _filterrCalculateMasterData: function(data) {
            var fields = this._filterCalculateFields(this.get('fields'));
            return _.pick(data, fields);
        },

		_filterCalculateFields: function (fields, expFields) {
            var list = ['_id', 'record_type', 'life_status', 'object_describe_api_name'];
            _.each(fields, function(a) {
                if(a.calculate_relation || a.type === 'formula' || a.type === 'count' || a.default_value || a.quote_field) {
                    list.push(a.api_name);
                    
                    if(a.type === 'select_one' || a.type === 'select_many') {
                        list.push(a.api_name + '__o');
                    }
                }
                if(a.mask_field_encrypt) {
                    list.push(a.api_name + '__encrypt');
                }
            })

			return _.union(list, expFields || []);
        },

		_filterCalculateMDData: function (detailDataMap, calfields) {
            var me = this;
            var detailList = this.get('detailObjectList')
            var ddm = {};
            _.each(detailDataMap, function(list, apiname) {
                var tmp = _.find(detailList, function(a) {
                    return a.objectDescribe.api_name === apiname
                })

                var obj = {};
                if(tmp) {
					var fields = me._filterCalculateFields(tmp.objectDescribe.fields, calfields && calfields[apiname]);
                    _.each(list, function(item, k) {
                        var tobj = {};
                        _.each(fields, kk => {
                            tobj[kk] = item[kk];
                            // if(tobj[kk] = item[kk]) {
                            //     let v1 = item[kk + '__r'];
                            //     if(v1) {
                            //         tobj[kk + '__r'] = v1;
                            //     }

                            //     let v2 = item[kk + '__o'];
                            //     if(v2) {
                            //         tobj[kk + '__o'] = v2;
                            //     }
                            // }
                        })
                        obj[k] = tobj;
                    })
                }

				ddm[apiname] = obj;
            })

            return ddm;
        },

        _getCalculateFieldOrder: function(modifiedObjectApiName) {
            return CRM.util.getCalculateFieldOrder(this.get('calculateOrderMap'), modifiedObjectApiName);
        },

        analysisCalculte: function(key, cb, context, blurKey, notifyCb, endCb, xx, parseCalculateParam) {
            var me = this;
            var calFields = me.getCalculateFieldsByNames(key, xx);
            if(!calFields) { //没有需要计算的字段
                cb({});
                notifyCb&&notifyCb();
                endCb && endCb();
                return;
            };

            if(me._analysisAjax) {
                var trf = me._analysisAjax._crmCalFields;
                _.each(trf, function(arr, apiname) {
                    calFields[apiname] = _.union(arr, calFields[apiname] || []);
                })
                me._analysisAjax.abort && me._analysisAjax.abort();
            }

            util.waiting($t("数据计算中") + '...', true);
            var _num = 0;
            var _timer = setTimeout(function() { //无论怎样10秒之后框一定要消失
                //走到这个方法证明无法获取到server的返回，无法得到任何的回调
                me.__uploadApiErrorLog(null, me._analysisAjax);
                util.waiting(false, true);
            }, 10000);
            var ddm = me.mdData2Object();
            var _error = function(msg) {
                if(msg && msg.statusText === 'abort') {
                    clearTimeout(_timer);
                    me._analysisAjax = null;
                    return;
                }
                if(_num) {
                    clearTimeout(_timer);
                    util.waiting(false, true);
                    cb({}, {isBack: true});
                    me.__uploadApiErrorLog(msg, me._analysisAjax);
                    if(msg) {
                        util.alert(msg.statusText === 'error' ? $t('网络错误，请稍后再试') : msg);
                    }
                } else { //重试一次
                    _num++;
                    _calculate();
                }
                me._analysisAjax = null;
            }
            var _calculate = function() {
                me._analysisAjax = me.calculate({
                    fields: calFields,
                    detailDataMap: ddm,
                    callback: function(obj, isSuccess, res) {
                        if(!isSuccess) {
                            _error(res.Result.FailureMessage);
                            return;
                        }
                        util.waiting(false, true);
                        clearTimeout(_timer);
                        me._analysisAjax = null;
                        cb.apply(me, arguments);
                    },
                    notifyFun:function(opts,calRes,uiRes, error){
						notifyCb&&notifyCb(opts,calRes,uiRes, error)
                    },
                    endCb: endCb,
                    fail: _error,
                    blurKey: blurKey,
                    data: key.__data,
                    parseCalculateParam
                })

                me._analysisAjax._crmCalFields = calFields;
            }

            _calculate();
        },

        //上传错误计算日志
        __uploadApiErrorLog: function(e, opts) {
            util.uploadLog('field', 'newmd', {
                eventId: 'masterapierror',
                eventType: 'cl',
                eventData: {
                    apiname: this.get('apiname'),
                    traceId: opts && (opts._traceId || ''),
                    date: new Date().toLocaleString(),
                    failureMessage: e || $t('计算错误') // [ignore-i18n]
                }
            });
        },

        analysisCalculte20: require('./calpipe'),

        //获取所有参与计算的参数
        getCalculateFieldsByNames: function(keys, param) {
            var fields = this.get('fields');
            var calFields = {};
            _.isString(keys) && (keys = [keys]);
            _.each(keys, function(key) {
                var field = fields[key];
                if(!field || !field.calculate_relation) return;
                _.each(field.calculate_relation.calculate_fields, function(arr, apiname) {
                    calFields[apiname] = _.union(arr, calFields[apiname] || []);
                })
            })

            if(param && (param.extraFields || param.filterFields)) {
                let inner = (changeFields, fields, apiName) => {
                    _.each(changeFields, key => {
                        let tt = fields[key];
                        tt && tt.calculate_relation && _.each(tt.calculate_relation.calculate_fields, (arr, k) => {
                            if (k === '__uievents') return;
                            calFields[k] = _.union(arr, calFields[k] || []);
                        })
                        if(apiName) {
                            calFields[apiName] = _.union(changeFields, calFields[apiName] || []);
                        }
                    })
                }
                if(param.extraFields) {
                    if(_.isArray(param.extraFields)) {
                        inner(param.extraFields, fields);
                    } else {
                        _.each(param.extraFields, (a, b) => {
                            let tds = this.getDescribeByObjApiName(b);
                            tds && inner(a, tds.fields, b);
                        })
                    }
                }
    
                _.each(param.filterFields, (fields, apiName) => {
                    let tc = calFields[apiName];
                    if(tc) {
                        calFields[apiName] = _.filter(tc, a => {
                            let isC = /__c$/.test(a);
                            if(isC && _.contains(fields, '__c')) return;
                            if(!isC && _.contains(fields, '__s')) return;
                            return !_.contains(fields, a);
                        })
                    }
                })

                _.each(calFields, (v, k) => {
                    if(!v || !v.length) {
                        delete calFields[k];
                    }
                })
            }

            return _.isEmpty(calFields) ? null : calFields;
        },

        //提前计算一些值
        _preCalculate: function(obj, cb) {
            var me = this;
            var data = obj.data;
            var isCopy = me.get('isCopy');

            if(!isCopy && me._preCalculateDetails(obj, cb)) return;

            var calFields = me.getCalculateFieldsByNames(isCopy ? ['owner'] : _.keys(data));

            if(!calFields) { //没有任何需要提前计算的项
                cb && cb();
                return;
            }

            me.calculate({
                data: data,
                fields: calFields,
                callback: function(res) {
                    _.each(res, function(list) {
                        _.each(list, function(item) {
                            if(isCopy) {
                                obj.serverCalculateData = item;
                            } else {
                                _.each(item, function(v, k) {
                                    v !== null && (data[k] = v);
                                })
                            }
                            
                        })
                    })
                    cb && cb(obj);
                }
            })
        },

        //映射或转换时如果从对象的数据有不匹配的业务类型，需要重新计算一下
        _preCalculateDetails: function(obj, cb) {
            var me = this;
            var converParam = me.get('convertParam');
            var mdData = me.get('mdData');
            if(!mdData || !converParam) return;
            var to = _.find(obj.detailObjectList, function(item) {
                var arr = mdData[item.objectDescribe.api_name];
                if(!arr || !arr.length) return;
                var ll = item.layoutList;
                return !!_.find(arr, function(a) {
                    return !_.findWhere(ll, {
                        record_type: a.record_type
                    })
                })
            })
            if(!to) return;

            util.FHHApi({
                url: '/EM1HNCRM/API/v1/object/calculate/service/batchCalculateOfRecordType',
                data: {
					masterObjectApiName: me.get('apiname'),
                    recordType: me.get('record_type'),
                    masterData: obj.data,
                    detailDataMap: mdData,
                    originalObjectApiName: converParam.apiname,
					buttonApiName: converParam.buttonApiName,
                    ruleApiName: converParam.ruleApiName
                },
                success: function(res) {
                    if(res.Result.StatusCode === 0) {
                        obj.serverCalculateData = res.Value.objectData;
                        me.set('mdData', res.Value.detail);
                    }
                    cb(obj);
                }
            })

            return true;
        },
        //提前取引用字段的一些值
        //6.6.0版本之后，server会计算引用字段。
        _fillQuoteData: function() {},

        //控制md的隐藏显示
        _toggleMDStatus: function(attr) {
            if(!attr) return;
            this.__cacheFormRuleData(attr, 'mdstatus');
            var dl = this.get('detailObjectList');
            var _status = {};
            var cacheStatus = this.__mdUIStatus || (this.__mdUIStatus = {});
            _.each(attr, function(config, key) {
                var ti;
                _.find(dl, function(a, index) {
                    if(a.objectDescribe.api_name === key) {
                        ti = index;
                        return true;
                    }
                })

                cacheStatus[key] || (cacheStatus[key] = {});
                if(cacheStatus[key].hidden === config.hidden) return;

                if(!_.isUndefined(ti)) {
                    cacheStatus[key].hidden = config.hidden;
                    _status[key] = {hidden: config.hidden}
                }
            })

            if(_.isEmpty(_status)) return;

            var md = (this.get('forms') || {}).md;
            md && md.toggleMDStatus && md.toggleMDStatus(_status, cacheStatus);
        },

        _updateMDByUIEventResult: function(res) {
            res.detailRecordType && this.toggleRecordTypeStatus(res.detailRecordType);
            if(res.__detailRecordType) {
                let md = (this.get('forms') || {}).md;
                md && md.toggleRecordTypeStatus && md.toggleRecordTypeStatus(res.__detailRecordType);
            }
            res.detailFieldAttribute && this._toggleMDFieldStatus(res.detailFieldAttribute);
            res.detailButton && this._toggleMDButtons(res.detailButton);
            res.detailRowIdFieldAttribute && this._toggleMDFieldStatusByRowIdStatus(res.detailRowIdFieldAttribute);
        },

        //控制从对象业务类型隐藏显示
        toggleRecordTypeStatus: function(param) {
            if(!param) return;
            var md = (this.get('forms') || {}).md;
            this.set('mdRecordTypeStatus', param);
            md && md.toggleRecordTypeStatus && md.toggleRecordTypeStatus(param);

            this.__cacheFormRuleData(param, 'mdrecordtypestatus');
        },
        hideMDRecordTypeByLoadUIEvent: function() {
            var tt = this.get('mdRecordTypeStatus');
            if(tt) {
                this.set('mdRecordTypeStatus', null);
                this.toggleRecordTypeStatus(tt);
            }
        },
        //当从对象被隐藏时，有必填字段未填写，保存时弹框提示用户
        showMDHideError: function(apiName, rt) {
            var dl = _.findWhere(this.get('detailObjectList'), {objectApiName: apiName});
            if(!this.__showMDHideError) {
                this.__showMDHideError = _.debounce(() => {
                    if(this.__mdhideAlert) {
                        this.__mdhideAlert.element.find('.confirm-message').html(_.values(this.__mdHideErrors).join('</br>'));
                        this.__mdhideAlert.resetPosition();
                    } else {
                        this.__mdhideAlert =  FS.crmUtil.alert(_.values(this.__mdHideErrors).join('</br>'));
                        this.__mdhideAlert.on('hide', () => {
                            this.__mdhideAlert = null;
                            this.__mdHideErrors = null;
                        })
                    }
                }, 60)
            }
            var label = dl.objectDescribe.display_name + '_' + _.findWhere(dl.objectDescribe.fields.record_type.options, {value: rt}).label;
            (this.__mdHideErrors || (this.__mdHideErrors = {}))[apiName + rt] = $t('crm.md.recordtypehide.tip', {label: label});
            this.__showMDHideError();
        },
        //控制从对象字段隐藏 和 只读
        //fieldAttr: {object__cc: {name: {hidden: true, readOnly: true}}}
        _toggleMDFieldStatus: function(fieldAttr) {
            if(!fieldAttr || _.isEmpty(fieldAttr)) return;

            //把状态都缓存下来
            var tc = this.get('mdFieldAttrStatus');
            if(!tc) {
                this.set('mdFieldAttrStatus', tc = {});
            }
            _.each(fieldAttr, (attr, objectApiName) => {
                let dom = _.findWhere(this.get('detailObjectList'), {objectApiName});
                if(dom) {
                    let st = {}, et = [], fields = dom.objectDescribe.fields;
                    function inner(k) {
                        let field = fields[k];
                        if(!field) return;
                        //日期范围的结束时间字段，设置无效 需要删除
                        if(field.start_time_field) {
                            et.push(k)
                        } else if(field.end_time_field) {//日期范围的开始时间，要一起设置结束时间
                            st[field.end_time_field.api_name] = v;
                        }
                    }
                    function inner2(k, v, attr) {
                        let field = fields[k];
                        if(!field) return;
                        if(!_.contains(['country', 'province', 'city', 'district', 'town', 'village'], field.type)) return;

                        let ss = {};
                        _.each(v, (vv, t) => {
                            if(t === 'hidden') {
                                // if(vv) {
                                //     _.each(field.cus_children, c => {
                                //         ss[c] = {
                                //             hidden: vv
                                //         }
                                //     })
                                // } else {
                                //     _.each(field.cus_parents, c => {
                                //         ss[c] = {
                                //             hidden: vv
                                //         }
                                //     })
                                // }
                            }
                            if(t === 'readOnly') {
                                if(vv) {
                                    // _.each(field.cus_children, c => {
                                    //     ss[c] = {
                                    //         readOnly: vv
                                    //     }
                                    // })
                                } else {
                                    // _.each(field.cus_parents, c => {
                                    //     ss[c] = {
                                    //         readOnly: vv
                                    //     }
                                    // })
                                }
                            }
                            if(t === 'required') {
                                if(vv) {
                                    _.each(field.cus_parents, c => {
                                        ss[c] = {
                                            required: true
                                        }
                                    })
                                } else {
                                    // _.each(field.cus_children, c => {
                                    //     ss[c] = {
                                    //         required: false
                                    //     }
                                    // })
                                }
                            }
                        })

                        _.extend(attr, ss);
                    }

                    _.each(attr, (v, k) => {
                        if(_.isObject(v.hidden || v.readOnly || v.required)) {
                            let tt = {};
                            _.each(v, (item, type) => {
                                _.each(item, (vv, kk) => {
                                    inner(kk);
                                    inner2(kk, {[type]: vv}, tt);
                                })
                            })
                            _.each(tt, (a, ck) => {
                                _.each(a, (ss, sss) => {
                                    (v[sss] || (v[sss] = {}))[ck] = ss;
                                })
                            })
                        } else {
                            inner(k);
                            inner2(k, v, attr);
                        }
                    })
                    _.each(et, k => delete attr[k]);
                    _.extend(attr, st);
                }
                if(!tc[objectApiName]) {
                    tc[objectApiName] = attr;
                } else {
                    _.extend(tc[objectApiName], attr);
                }
            })
            
            _.each(fieldAttr, (attr, objApiName) => {
                var dl = _.findWhere(this.get('detailObjectList'), {objectApiName: objApiName});
                if(!dl) return
                _.each(dl.layoutList, (a) => {
                    this.trigger('action:' + objApiName + '_' + a.record_type, 'setFieldAttrHandle', attr)
                })
            })

            this.__cacheFormRuleData(fieldAttr, 'mdfields');
        },
        //控制从对象字段隐藏 和 只读
        //fieldAttr: {rowId: {readOnly: {name: true, sex: false}}}
        _toggleMDFieldStatusByRowIdStatus: function(fieldAttr) {
            var tc = {};
            _.each(fieldAttr, (attr, rowId) => {
                var item = this.getMDDataByRowId(rowId);
                if(!item) return;
                var tmp = tc[item.object_describe_api_name] || (tc[item.object_describe_api_name] = {});
                (tmp[item.record_type] || (tmp[item.record_type] = {}))[rowId] = attr;
            })
            _.each(tc, (a, objApiName) => {
                _.each(a, (b, recordType) => {
                    this.trigger('action:' + objApiName + '_' + recordType, 'setRowFieldAttrHandle', b);
                })
            })

            this.__cacheFormRuleData(fieldAttr, 'mdrowfields');
        },

        //根据rowId获取从对象数据
        getMDDataByRowId: function(rowId) {
            var item;
            _.find(this.get('allTablesData'), a => {
                return !!_.find(a, arr => {
                   item = _.findWhere(arr, {rowId: rowId});
                    return !!item;
                })
            })
            return item;
        },

        getMDFieldAttrStatus: function(apiName, rt) {
            var tc = this.get('mdFieldAttrStatus');
            return tc && tc[apiName];
        },

        //通过UI事件控制从对象的按钮显示状态
        _toggleMDButtons: function(buttons) {
            //把状态都缓存下来
            // var tc = this.get('mdButtonsStatus');
            // if(!tc) {
            //     this.set('mdButtonsStatus', tc = {});
            // }
            var tc = {};
            _.each(buttons, (btns, apiName) => {
                _.each(btns, btn => {
                    var tcc = tc[apiName + btn.record_type];
                    if(!tcc) {
                        tc[apiName + btn.record_type] = tcc = {};
                    }
                    var tbb = tc[apiName + btn.record_type + 'single'];
                    if(!tbb) {
                        tc[apiName + btn.record_type + 'single'] = tbb = {};
                    }
                    _.each(btn.buttons, tb => {
                        if(tb.action === 'Batch_Edit') {
                            tcc.tableBatchEditHandle = !tb.hidden;
                        } else if(tb.buttonType === 'batch' && tb.action === 'Delete') {
                            tcc.delSelectedHandle = !tb.hidden;
                        } else if(tb.buttonType === 'batch' && tb.action === 'Clone') {
                            tcc.copySelectedHandle = !tb.hidden;
                        } else if(tb.action === 'Single_Add') {
                            tcc.singleAddHandle = !tb.hidden;
                        } else if(tb.action === 'Import_Excel') {
                            tcc.importExcelHandle = !tb.hidden;
                        } else if(tb.lookupFieldName) {
                            tcc[tb.lookupFieldName] = !tb.hidden;
                        } else if(tb.rowIds) {
                            let ac = {'Delete': 'delRowHandle', Clone: 'copyRowHandle', 'Tile': 'tileHandle', 'Insert': 'insertHandle'}[tb.action] || tb.action;
                            tb.rowIds = _.map(tb.rowIds, rowId => {
                                rowId = this.__index2RowId(rowId, apiName);
                                (tbb[rowId] || (tbb[rowId] = {}))[ac] = !tb.hidden;
                                return rowId;
                            })
                        }
                    })
                    var eventKey = 'action:' + apiName + '_' + btn.record_type;
                    this.trigger(eventKey, 'toggleTitButtonsHandle', tcc);
                    this.trigger(eventKey, 'setTableBtnsHandle', tbb, tcc);
                })
            })
            
            this.__cacheFormRuleData(buttons, 'mdbuttons');
        },

        //兼容老的index充当rowId的情况
        __index2RowId: function(index, apiname) {
            var tt;
            if(!_.isNaN(index * 1)) {
                var ddm = this.mdData2Object();
                tt = ((ddm && ddm[apiname]) || {})[index];
            }
            return (tt && tt.rowId) || index;
        },

        //通过UI事件控制单选 多选的选项值 包含从对象
        //往描述增加filterOptions属性
        //{object__c: {field_y210t__c: {options1: {hidden: true|false}}}}
        _toggleFieldOptions: function(optionAttribute) {
            if(!optionAttribute) return;

            var masterApiName = this.get('apiname');
            var _inner = function(attr, fields, objApiName) {
                _.each(attr, (optionConfig, fieldName) => {
                    var tmp = fields[fieldName];
                    if(!tmp) return;
                    var filterOptions = tmp.filterOptions || [];
                    _.each(optionConfig, (item, value) => {
                        item.hidden ? filterOptions.push(value) : (filterOptions = _.without(filterOptions, value));
                    })
                    tmp.filterOptions = _.union(filterOptions);
                })
            }
            _.each(optionAttribute, (attr, objectApiName) => {
                if(objectApiName === masterApiName) { //控制主对象
                    _inner(attr, this.get('fields'), objectApiName);
                    _.each(attr, (v, k) => {
                        this.trigger('resetOptionsByFilterOptions.' + k);
                    })
                } else {
                    var dl = _.findWhere(this.get('detailObjectList'), {objectApiName: objectApiName});
                    dl && _inner(attr, dl.objectDescribe.fields, objectApiName);
                }
            })

            this.__cacheFormRuleData(optionAttribute, 'optionAttribute');
        },

        // 缓存表单数据 --------------- start ----------------------------------------------------//
        //缓存表单规则数据，方便从草稿箱进入时，对界面进行还原 
        //比如保存草稿之前，用户已经触发过ui事件，将一些字段设为隐藏或从对隐藏，如果不缓存数据，再次从草稿箱打开时，之前设置规则都会失效，体验不好。
        //type: optionAttribute(选项隐藏) | 
        __cacheFormRuleData: function(data, type) {
            if(!data) return;
            var cache = this.get('cacheFormRuleData');
            if(!cache) {
                this.set('cacheFormRuleData', cache = {});
            }
            (cache[type] || (cache[type] = [])).push(data);
        },
        updateFormRuleRowId(rowIdConfig) {
            if(_.isEmpty(rowIdConfig)) return;

            _.each(this.get('draftCacheFormRuleData'), (attr, type) => {
                if(type === 'mdrowfields') {
                    _.each(_.keys(attr), (rowId) => {
                        let newRowId = rowIdConfig[rowId];
                        if(newRowId) {
                            attr[newRowId] = attr[rowId];
                            delete attr[rowId];
                        }
                    })
                }
                if(type === 'mdrowfieldsuievent') {
                    _.each(rowIdConfig, (newRowId, oldRowId) => {
                        if(attr[oldRowId]) {
                            attr[newRowId] = attr[oldRowId];
                            delete attr[oldRowId];
                        }
                    })
                }
                if(type === 'mdbuttons') {
                    _.each(attr, arr => {
                        _.each(arr, item => {
                            item && _.each(item.buttons, btn => {
                                if(btn && btn.rowIds) {
                                    btn.rowIds = _.map(btn.rowIds, rowId => {
                                        return rowIdConfig[rowId] || rowId
                                    })
                                }
                            })
                        })
                    })
                }
            });
        },
        recoveryFormRule() {
            let data = this.get('draftCacheFormRuleData');
            _.each(data, (attr, type) => {
                if(type === 'optionAttribute') return this._toggleFieldOptions(attr);
                if(type === 'mdstatus') return this._toggleMDStatus(attr);
                if(type === 'mdrecordtypestatus') return this.toggleRecordTypeStatus(attr);
                if(type === 'mdfields') {
                    _.each(data.mdrowfieldsuievent, (config, rowId) => {
                        let item = this.getMDDataByRowId(rowId);
                        if(item) {
                            (attr[item.object_describe_api_name] || (attr[item.object_describe_api_name] = {}))[rowId] = config;
                        }
                    })
                    return this._toggleMDFieldStatus(attr);
                };
                if(type === 'masterfields') return this._setFieldAttribute(attr);
                if(type === 'mdrowfields') {
                    var nattr = {}
                    _.each(attr, (config, rowId) => {
                        nattr[rowId] = config;
                    })
                    this._toggleMDFieldStatusByRowIdStatus(nattr);
                }
                if(type === 'mdbuttons') {
                    this._toggleMDButtons(attr);
                } 
            });
        },
        getMergeCacheFormRuleData: function() {

            var cacheFormRuleData = {};
            try {
                _.each(this.get('cacheFormRuleData'), (arr, type) => {
                    if(type === 'mdbuttons') { //从对象按钮
                        let tmp = {};
                        _.each(arr, detailButton => {
                            detailButton = JSON.parse(JSON.stringify(detailButton));
                            _.each(detailButton, (arr, objApiName) => {
                                if(!tmp[objApiName]) tmp[objApiName] = {};
                                _.each(arr, a => {
                                    let tc = tmp[objApiName][a.record_type] || (tmp[objApiName][a.record_type] = []);
                                    Array.prototype.push.apply(tc, a.buttons);
                                })
                            })
                        })
                        let nDetailButton = {};
                        _.each(tmp, (item, objApiName) => {
                            let tc = nDetailButton[objApiName] || (nDetailButton[objApiName] = []);
                            _.each(item, (buttons, rt) => {
                                var b1 = [], b2 = [];
                                _.each(buttons, a => {
                                    if(a.rowIds) {
                                        _.find(b1, b => {
                                            if(b.action == a.action) {
                                                if(a.hidden) {
                                                    b.rowIds = _.union(b.rowIds, a.rowIds);
                                                } else {
                                                    b.rowIds = _.difference(b.rowIds, a.rowIds)
                                                }
                                                return true;
                                            }
                                        }) || (a.hidden && b1.push(a))
                                    } else {
                                        _.find(b2, b => {
                                            if(b.action == a.action && b.buttonType == a.buttonType && b.lookupFieldName == a.lookupFieldName) {
                                                b.hidden = a.hidden;
                                                return true
                                            }
                                        }) || (a.hidden && b2.push(a));
                                    }
                                })
                                tc.push({
                                    record_type: rt,
                                    buttons: b1.concat(b2)
                                })
                            })
                        })
                        cacheFormRuleData[type] = nDetailButton;
                    } else if(type === 'mdstatus') {
                        var mdStatus = {};
                        _.each(arr, a => { //{sdfsaf__cc: {defalt__c: {hidden: true}, aa__c: {hidden:false}}}
                            _.each(JSON.parse(JSON.stringify(a)), (attr, objApiName) => {
                                mdStatus[objApiName] = _.extend(mdStatus[objApiName] || {}, attr);
                            })  
                        })
                        cacheFormRuleData[type] = mdStatus;
                    } else if(type === 'masterfields') {
                        var masterfields = {};
                        _.each(arr, a => {
                            _.each(a, (config, fieldName) => {
                                masterfields[fieldName] = _.extend(masterfields[fieldName] || {}, config);
                            })
                        })
                        cacheFormRuleData[type] = masterfields;
                    } else if(type === 'mdrowfields') {
                        var mdrowfields = {};
                        _.each(arr, a => { // {row_id: {readOnly: {a: true, b:false},hidden: {a:true, b:false}}}
                            _.each(a, (config, rowId) => {
                                config = JSON.parse(JSON.stringify(config));
                                if(!mdrowfields[rowId]) {
                                    mdrowfields[rowId] = config
                                } else {
                                    _.each(config, (cc, k) => {
                                        mdrowfields[rowId][k] = _.extend({}, mdrowfields[rowId][k], cc);
                                    })
                                }
                            })
                        })
                        cacheFormRuleData[type] = mdrowfields;
                    } else if(type === 'mdfields') {
                        var mdfields = {};
                        var mdrowfieldsuievent = {};
                        _.each(arr, a => {
                            _.each(a, (aa, objApiName) => {// {row_id: {readOnly: {a: true, b:false},hidden: {a:true, b:false}, name: {readOnly: true}}}
                                _.each(aa, (config, rowId) => {
                                    if(_.isObject(config.hidden || config.readOnly || config.required)) {
                                        let ttf = (mdrowfieldsuievent[rowId] || (mdrowfieldsuievent[rowId] = {}));
                                        config.hidden && (ttf.hidden = _.extend(ttf.hidden || {}, config.hidden));
                                        config.readOnly && (ttf.readOnly = _.extend(ttf.readOnly || {}, config.readOnly));
                                        config.required && (ttf.required = _.extend(ttf.required || {}, config.required));
                                    } else {
                                        let tf = mdfields[objApiName] || (mdfields[objApiName] = {});
                                        let ttf = tf[rowId] || (tf[rowId] = {});
                                        ttf = _.extend(ttf, config);
                                    }
                                })
                            })
                        })

                        cacheFormRuleData[type] = mdfields;
                        cacheFormRuleData.mdrowfieldsuievent = mdrowfieldsuievent;

                    } else { //optionAttribute(控制选项？？？) mdrecordtypestatus(控制业务类型) mdbuttons(控制按钮)
                        var tmp = {};
                        _.each(arr, a => { //{sdfsaf__cc: {defalt__c: {hidden: true}, aa__c: {hidden:false}}}
                            _.each(JSON.parse(JSON.stringify(a)), (attr, objApiName) => {
                                if(!tmp[objApiName]) {
                                    tmp[objApiName] = attr;
                                } else {
                                    _.each(attr, (config, k) => {
                                        tmp[objApiName][k] = _.extend(tmp[objApiName][k] || {}, config);
                                    })
                                }
                            })  
                        })
                        cacheFormRuleData[type] = tmp;
                    }
                })
            } catch(e) {}
            
            return cacheFormRuleData;
        },

        // 缓存表单数据 --------------- end ----------------------------------------------------//

        getMDFieldOptions: function(objectApiName, rt, fieldName) {
            var dl = _.findWhere(this.get('detailObjectList'), {objectApiName: objectApiName});
            var field = dl && dl.objectDescribe.fields[fieldName];
            return field && field.filterOptions;
        }
    }
})
