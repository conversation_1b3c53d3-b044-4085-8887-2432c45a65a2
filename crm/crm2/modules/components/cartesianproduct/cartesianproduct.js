/**
 * @desc 根据笛卡尔集动态生成表格
 * <AUTHOR>
 * 组件分为三部分 1、select选框  2、select对应可选参数值  3、表格
 * @param 参数配置
 * params:{
	el:''                    //dom的wrapper
	selectOpt:[{             //规格
	  "name":'',             //select的选项名称（规格名称）
	  "value":'',            //select的选项值（规格id）
	  "options":[{           //select的选项值对应可选值（规格值）
		"name":'',
		"value":'',
		"status":0           //数值是否选中状态：0-未选中，1-已选中
	  }]
	}],
	columns:{                //表格的表头——url或者data
	  url:'',                //调用接口findHeader取表头数据
	  data:[]                //直接传表头数据
	},
	tableData:{              //表格的数据——url或者data
	  url:'',                //调用接口refObjEach取表格数据
	  data:[]                //直接传表格数据
	},
	od:{},                      //其他需传的接口数据originServerData
	opType:1                      //调用组件进行的operate类型：1-add新建数据，2-edit编辑数据，3-choice选择数据，4-价目表添加产品，5-选数据单选模式（新增）
	spuName:"产品A"                //商品名称
	className:"",                 //组件自定义类名
	specCompMaxHeight:200         //规格组件容器默认最大高度
	tableMaxHeight:500            //表格最大高度，如果有最大高度，就设置为可滚动表格，否则为不可滚动表格，通过外层容器滚动
	setSpecSkuShowMultiple:()=>{  //是否显示checkbox
		return false          
	}
	availableSpec:boolean          //是否根据可售产品过滤规格值，默认false
	getSkuDataAfter:(list)=>{      //处理接口获取的sku数据的勾子
		return list;
	}
	renderSpecBefore:(specArr)=>{  //规格规格值渲染前，可过滤数据
		return specArr;
	}
 * }
 */

define(function (require, exports, module) {
	var tpl = require('./tpl/tpl-html'),
		Model = require('./model'),
		optItemTpl = require('./tpl/optitem-html'),
		cpTableTpl = require('./tpl/cptable-html'),
		util = require('crm-modules/common/util'),
		Table = require('crm-widget/table/table'),
		ObjTable = require('crm-modules/components/objecttable/objecttable'),
		Sortable = require('base-sortable'),
		OptionsComp = require('./comps/optionscomp'),
		SelectComp = require('./comps/selectcomp'),
		SpecSingleComp = require('./comps/specsinglecomp');
	var mixin = require('crm-modules/buscomponents/pickself_bom_util/pickself_bom_util');

	var CartesianProduct = Backbone.View.extend({
		...mixin,
		getDataId(data) {
			return data.product_id || data._id;
		},
		renderBomCoreObj(id, from) {
			let me = this;
			require.async('crm-modules/buscomponents/pickselfobject_bomcore/pickselfobject_bomcore', async (bomCore) => {
				let config = {
					product_id: id,
					currentRow: me.currentRow,
					apiname: me.apiname,
					selectBomParams: {
						extendData: me.bomCheckedData[id]?.subBomData?.data,
						extendRootData: me.bomCheckedData[id]?.subBomData?.newRootData
					},
					bomCoreParams: {
						subBomData: me.bomCheckedData[id]?.subBomData,
						dataId: me.bomCheckedData[id]?._id,
					},
					from,
					...me.options,
					dialogEnter: me.bomCoreEnter.bind(me, id),
				};

				me.pickBomCore = await bomCore(config)

			});
		},
		bomCoreEnter(productId, checkedBomCoreList) {
			checkedBomCoreList.forEach(obj => {
				let { _id, name, core_version, category, subBomData } = obj;
				this.bomCheckedData[productId] = obj;
				if (subBomData) {
					this.updatePricebookId(subBomData, this.currentRow);
					this.updateRootAttr(subBomData.newRootData || subBomData.rootData, this.currentRow);
					this.supplementAttrToData(this.currentRow);
				}
				this._setCheckedRow(this.currentRow._id);
			})
		},
		// 配置完，设置行勾选;
		_setCheckedRow: function (id) {
			let checked = this.table.getRemberData() || [];
			let findPb = _.find(checked, item => item._id === id);
			if (!findPb) {
				this.table.setCheckedRow('_id', [{ '_id': id }]);
			};
		},
		// -----
		initialize: function () {
			var options = this.options;
			this.opType = options.opType; //1-add,2-edit,3-choice,5-规格单选筛

			this.singleSpec = options.singleSpec; //1-add,2-edit,3-choice
			this.isPb = options.objectData ? options.objectData.pricebook_open : false;
			this.apiname = this.isPb ? 'PriceBookProductObj' : 'ProductObj',
				this.canCreateSpec = options.canCreateSpec;  //是否有新建规格权限
			this.selects = [];
			this.stPrice = '';
			this.periodicFields = ["pricing_frequency", "settlement_frequency", "whole_period_sale", "pricing_cycle", "settlement_cycle", "settlement_mode"];
			this.cleanOptions = [];
			this.el.innerHTML = tpl({
				opType: this.opType,
				isSingle: this.singleSpec,
				className: options.specCompClassName || "",
				spuName: options.spuName || ""
			});

			this.setModel(this.options);
			this.collapseFlag = true;
			this.statusClass = {
				"0": '',
				"1": 'disabled',
				"2": 'active',
				"3": 'not-usable'
			};
			this.specCompMaxHeight = options.specCompMaxHeight || 188;

			this.tableMaxHeight = options.tableMaxHeight || null;
			if (this.opType >= 2) {
				this.showLoading();
			}
			this.render();
			this.bomCheckedData = (this.options?.remberData || []).reduce((res, item) => {
				let id = this.getDataId(item);
				item.core_id && (res[id] = {
					_id: item.core_id,
					category: item.node_bom_core_type,
					core_version: item.node_bom_core_version,
					subBomData: item.subBomData || null
				})
				return res;
			}, {});
			this.checkedData = {};// 勾选配置数据
			this.editAddSpec = false;
		},

		showLoading: function () {
			let me = this;
			me.$('.loading-wrapper').show();
			if (me.opType == 1) {
				let optionsAreaHeight = me.$el.find('.cp-options').height(),
					loadingPos = (optionsAreaHeight / 2) + 'px';
				me.$('.dketable-loading').css('top', loadingPos)
			} else if (me.opType == 2) {
				let offsetTop = me.$el.offset().top,
					tableOffsetTop = $('.cp-table').offset().top,
					loadingPos = (tableOffsetTop - offsetTop + 300 * 1) + 'px';
				me.$('.dketable-loading').css('top', loadingPos)
			}
		},

		hideLoading: function () {
			this.$('.loading-wrapper').hide();
		},

		render: function () {
			var me = this;
			this.model.getInitData().done(function () {
				const specData = me.model.get('optionsData'),
					param = {
						el: $('.cp-options', me.$el),
						data: specData || [],
						model: me.model,
						wrapper: '.cp-options',
						opType: me.opType,
						statusClass: me.statusClass,
						showLoading: me.showLoading
					};

				switch (me.opType) {
					case 2:  //编辑商品产品页
					case 3:  //订单选产品，多选规格值模式
					case 4:  //价目表添加产品
						me.specMultipleComp = new OptionsComp(param)
						me.specMultipleComp.render();
						break;
					case 5:  //订单选产品，单选规格值模式
						me.specSingleComp = new SpecSingleComp({
							...param,
							availableSpec: me.options?.availableSpec,
							renderSpecBefore: me.options?.renderSpecBefore
						})
						me.specSingleComp.render();
						break;
					default:
						me.cleanOptions = me.model.get('optionsData');
						me.selects[0] = new SelectComp({
							model: me.model,
							index: 0,
							wrapper: '.cp-selects',
							cleanOptions: me.cleanOptions
						})
						me.renderTable();
				}

				if (me.opType <= 2) {
					me.initSortList();
				} else {
					me.setExpandBtn();
				}
			});
		},

		initSortList: function () {
			let me = this;
			// Sortable: shared lists
			new Sortable(this.$('.cp-options').get(0), {
				group: 'cp-options-selects',
				animation: 150,
				onSort: evt => {
					me.onSort(evt);
				}
			});

			new Sortable(this.$('.cp-selects').get(0), {
				group: 'cp-options-selects',
				animation: 150,
				onSort: evt => {
					me.onSort(evt);
				}
			});
		},

		onSort: function (evt) {
			let opSelected = this.model.get('opSelected'),
				allSpec = this.$('.cp-options, .cp-selects').children('li'),
				specIds = allSpec.map(function () {
					return $(this).data('id');
				}).get();

			let sortedOpSelected = _.compact(specIds.map(id => {
				return _.findWhere(opSelected, { value: id });
			}));

			this.model.set('opSelected', sortedOpSelected);
			this.renderTable();

			// 更新label
			this.$('.cp-options, .cp-selects').children('.cpopt-wrapper').each(function (index, dom) {
				$('.j-label-count', dom).text(index + 1);
			});

		},

		events: {
			'click .j-cpopt-item': 'specValueChange',
			'click .j-all-spec': 'allSpecHandle',
			'click .j-add-spec': 'addSpecHandle',
			'click .j-option-all': 'allOptionHandle',
			'click .j-del-spec': 'delSpecHandle',
			'click .j-up-object': 'batchUpObj',
			'click .j-down-object': 'batchDownObj',
			'click .j-batch-del': 'batchRemoveObjHandle',
			'click .j-collapse': 'collapseSku',
			//'scroll': 'setScrollTable',
			'click .j-full-table': 'fullTableHandle',
			'click .j-replace-spec': 'replaceSpecHandle',
			'click .j-spec-toggle-btn': 'toggleSpecContainer'

		},
		fullTableHandle: function () {
			//全屏
			var me = this;
			var flag = false;
			me.$parent || (me.$parent = me.$el.parent());
			me.$('.j-full-table').text(me.fulldialog ? $t("全屏显示") : $t("crm.退出全屏"));
			if (me.fulldialog) {
				$('.j-table-wrap', me.$el).height("auto");
				me.$el.appendTo(me.$parent);
				me.fulldialog.destroy();
				me.fulldialog = null;
				if (me.$scroll) {
					$('.crm-a-spuobj.crm-scroll').scrollTop(me.$scroll);
				}
			} else {
				flag = true;
				me.$scroll = $('.crm-a-spuobj.crm-scroll').scrollTop();
				//$scroll && (me._scrollTop = $scroll.$el.scrollTop());
				let elHeight = $('.j-table-wrap', me.$el).height(),
					windowHeight = $(window).height();
				if (elHeight + 200 > windowHeight) {
					elHeight = windowHeight - 200;
				}
				$('.j-table-wrap', me.$el).height(`${elHeight}px`);
				me.fulldialog = util.fullDialog({
					el: me.$el,
					className: 'crm-action-field crm-full-cartesian',
					css: {
						width: 'auto',
						padding: '0 8px',
						overflow: 'auto'
					}
				});
			}
			me.table.toggleFullHeight(flag);
		},

		setModel: function (options) {
			var me = this;
			this.model = new Model(options);

			let needUpdateStatusForInit = false;

			me.model.on('init:complete',function () {
				needUpdateStatusForInit = true;
			});

			me.model.on('change:optionsData change:spuName', _.debounce(function () {
				me.renderTable();
			}, 500));

			me.model.on('change:tableData', _.debounce(function () {
				var tableData = me.model.get('tableData'),
					supplementData = me.model.get('supplementData'),
					totalData = [].concat(tableData);
				if (me.table) {
					if (!me.collapseFlag) { //折叠
						totalData = totalData.concat(supplementData);
					}
					if (me.tableMaxHeight) {
						const tr = $('.j-table-wrap .dt-main tr', me.$el).first().height() || 42, // 表头高度
							contentHeight = (totalData.length || 1) * tr,
							mainHeight = Math.min(contentHeight, me.tableMaxHeight) * 1 + 8;

						if (me.table.table && me.table.table?.options) {
							me.table.table.options.height = mainHeight;
						}
					}

					me.table.doStaticData(totalData);
					if (totalData.length >= 1 && me.opType <= 2) {
						me.renderRemoved();
					}

				} else {
					me.renderTable();
				}
				if(needUpdateStatusForInit){
					me.updateTableStatus();
					needUpdateStatusForInit=false;
				}
				
			}, 500));

			me.model.on('change:cleanOptions', _.debounce(function () {
				let selectedArr = [],
					options = me.model.get('optionsData'),
					cleanOptions = [];
				if (me.opType == 2) {
					// let existSpecs=$('.cp-options li').length;
					// for(let i=1;i<=existSpecs;i++){
					//     let id=$('.cp-options').children('li:nth-child('+i+')').attr('data-id')
					//     selectedArr.push(id);
					// }
					let existSpecs = me.$('.cp-options, .cp-selects').children('li.cpopt-edit[data-id]');
					existSpecs.each(function () {
						let id = $(this).attr('data-id');
						selectedArr.push(id);
					});

				}
				_.each(me.selects, function (s) {
					var value = s.getValue();
					selectedArr.push(value);
				})
				_.each(options, o => {
					if (!_.contains(selectedArr, o.value)) {
						cleanOptions.push(o)
					}
				})
				me.cleanOptions = cleanOptions;
				me.resetSelect();
				/*
				 *case1:当前规格未选规格值——不触发model数据变化
				 *case2:当前规格有选规格值——触发model数据变化
				 */
				if (selectedArr.length >= 1) {
					me.model.changeSpec(selectedArr, function () {
						me.model.trigger("change:optionsData");
					});
				}
			}))
		},

		addSpecHandle: function () {
			var me = this,
				length = this.selects.length,
				// 拖拽排序会导致$('.cp-options li').length不正确
				// index=me.opType==2?($('.cp-options li').length+length*1+1):length*1+1;
				index = me.opType == 2 ? (Object.keys(me.model.get('originSpecs')).length + length * 1 + 1) : length * 1 + 1;
			this.selects[length] = new SelectComp({
				model: me.model,
				index: length,
				labelIdx: index,
				wrapper: '.cp-selects',
				cleanOptions: me.cleanOptions,
				afterRenderSelect: function (options) {
					me.cleanOptions = options;
					let maxLen = me.model.get('maxSelects')
					//可选规格最多15个
					if (index == 15 || length >= (maxLen - 1)) {
						me.$('.add-spec').css('display', 'none');
					} else {
						me.$('.add-spec').css('display', 'inline-block');
					}
				}
			});
			this.$('.cpopt-wrapper').find('.cpopt-del').removeClass('disabled');
		},

		/*
		 *删除一个规格选项
		 */
		delSpecHandle: function (e) {
			var me = this,
				$item = $(e.target),
				$parent = $item.parents('.cpopt-wrapper'),
				cid = $parent.data('cid'),
				index = $parent.index(),
				allSpecDom = this.$('.cp-options, .cp-selects').children('li'),
				// 过滤出有选中项的规格和正在点击的规格
				filterSpecDom = allSpecDom.filter(function (index, dom) {
					return $('.cpopt-item[data-status="1"], .cpopt-item[data-status="2"]', this).length != 0 || $(this).is($parent);
				}),
				// 找出自身的正确index
				indexInAll = filterSpecDom.index($parent),
				opSelected = this.model.get('opSelected'),
				optionsData = this.model.get('optionsData'),
				// curSpecValue = this.selects[index].getValue(),
				curSpecValue = (_(this.selects).findWhere({ cid: cid }) || {}).getValue(),
				length = this.selects.length;

			/*
			 * step1: 当前select下规格值有选中，要处理table数据
			 * step2: 处理dom节点
			 */
			if (me.opType == 1) {
				if (length == 1) {
					return
				} else if (length == 2) {
					$parent.siblings('.cpopt-wrapper').find('.cpopt-del').addClass('disabled');
				}
			}

			if (curSpecValue && curSpecValue !== '') {
				_.find(opSelected, function (s, i) {
					if (s.value == curSpecValue) {
						// me.model.delSpec(i);
						me.model.delSpec(indexInAll);
						me.model.trigger("change:optionsData");
					}
				})
				//释放此select的option
				var item = _.find(optionsData, function (od) {
					return od.value == curSpecValue;
				});
				me.cleanOptions.push(item);
				me.resetSelect();
			}
			$parent.remove();
			// this.selects.splice(index, 1);
			this.selects = this.selects.filter(select => select.cid != cid);

			for (var i = 1; i < length; i++) {
				me.$('.cpopt-wrapper:nth-child(' + i + ')').find('.j-label-count').html(i)
			}
			this.$('.add-spec').css('display', 'inline-block');
		},


		//转义字符串
		_parseValue: function (data) {
			_.each(data, (d) => {
				d.name = CRM.util.enCodeValue(d.name) + ' ';
			})
			return data;
		},

		resetSelect: function () {
			var me = this;
			var optionsData = this.model.get('optionsData');
			_.each(me.selects, function (s, k) {
				let curOptions = [].concat(me.cleanOptions),
					value = s.getValue();
				if (value && value !== '') {
					let item = _.find(optionsData, function (od) {
						return od.value == value;
					})
					curOptions.splice(1, 0, item);
				}
				s.resetOptions(curOptions, true);
			})
		},

		//表格columns
		getColumn: function (selfColumns, fixName = false) {
			var column = this.model.get('optionsData'),
				columns = [],
				me = this;
			//
			if (this.opType <= 2) {
				let arr = [],
					selectColumn = this.model.get('opSelected');
				_.each(selectColumn, (s) => {
					let item = _.findWhere(column, { value: s.value });
					let newItem = _.extend({}, s, {
						options: item.options
					})
					arr.push(newItem);
				})
				column = arr;
			}
			column = _.map(column, function (a, index) {
				return {
					data: a.name,
					title: a.name,
					dataType: 'select_one',
					options: a.options,
					// isRequired: true,
					// 7.6.0规格列可删除不可编辑
					isEdit: me.opType <= 2 ? true : false,
					clearOnly: me.opType <= 2 ? true : false,
				}
			});
			// 商品多规格新建、编辑时，固定产品名称到第一列
			if (fixName) {
				_.find(selfColumns, col => {
					let name = col.api_name == 'name'
					if (name) {
						col.fixed = true;
						col.fixedIndex = 1;
					}
					return name;
				});
			}
			columns = column.concat(selfColumns);

			if (this.opType <= 3) {
				columns.push({
					data: null,
					width: me.opType <= 2 ? 48 : 124,
					title: $t('操作'),
					lastFixed: true,
					render: function (data, type, full) {
						let btns = '';
						if (me.opType === 3 && util.isBom(full).isPackage && !me.singleSpec) {
							btns += `<a data-id="${full._id}" class="tb-showBomCoreConfig" style="margin-right:10px;" class="tb-del-spec">${$t('配置')}</a>`;
						}
						var opText;
						if (full.data_right_flag == 'readonly') {
							opText = $t("只读")
						} else {
							opText = full.is_removed ? $t("恢复") : $t("删除");
						}
						btns += '<a data-id="' + full._id + '"  class="tb-del-spec" >' + opText + '</a>';

						return btns;
					}
				})
			}
			return this.formatColumns(columns);
		},

		formatColumns: function (columns) {
			return this.options.formatColumns ? this.options.formatColumns(columns, this.table?.get('apiname') || "") : columns;
		},

		//笛卡尔集表格
		renderTable: function (cb) {
			var me = this,
				od = me.model.get('od'),
				data = this.model.get('tableData'),
				remberData = this.model.get('remberData'),
				apiname = this.isPb ? 'PriceBookProductObj' : 'ProductObj',
				columns = [],
				selfColumns = [],
				defaultConfig = this.model.get('columnsObj'),
				opSelected = this.model.get('opSelected');
			me.collapseFlag = true;

			const defProStatus = _.findWhere(defaultConfig.data, {
				'api_name': 'product_status'
			}),
				defIsSaleable = _.findWhere(defaultConfig.data, {
					'api_name': 'is_saleable'
				}),
				defPricingMode = _.findWhere(defaultConfig.data, {
					'api_name': 'pricing_mode'
				});

			const defPeriodicFieldsMap = defPricingMode
				? Object.fromEntries(
					me.periodicFields
						.map(key => {
							const item = defaultConfig.data.find(field => field.apiname === key);
							return item ? [key, item.default_value] : null;
						})
						.filter(entry => entry !== null)
				)
				: {};

			_.each(data, function (i, idx) {
				if (me.opType <= 2 && !i.nameIsFrozen) {
					let specNames = [];
					_.each(opSelected, op => {
						let specValue = i[op.name];
						let { name } = _.findWhere(op.options, { value: specValue }) || {};
						!_.isUndefined(name) && specNames.push($.trim(name));
					});
					i.name = specNames.length ? `${me.model.get('spuName')}[${specNames.join('-')}]` : me.model.get('spuName');
				}
				i.product_status = i.product_status || (defProStatus && defProStatus.default_value);
				i.price = i.price || me.stPrice;
				i.is_removed = i.is_removed || false;
				i.is_saleable = (i.is_saleable !== undefined) ? i.is_saleable : (defIsSaleable && defIsSaleable.default_value);

				//周期性相关初始化数据
				if (defPricingMode && !i.pricing_mode) {
					i.pricing_mode = defPricingMode?.default_value || "one";
					
					const isOneMode = i.pricing_mode == "one";
					me.periodicFields.forEach(field => {
						i[field] = isOneMode?null : defPeriodicFieldsMap[field]
					})
				}
			});

			var tableConfig = {
				"record_type": od.record_type,
				"header": $t("产品明细"),
				"opType": me.opType
			};


			me.$('.cp-table').html(cpTableTpl(tableConfig));

			var options = {
				doStatic: true,
				showSize: false,
				showPage: false,
				showTerm: false,
				showFilerBtn: false,
				showMoreBtn: false,
				openStart: false,
				showRequiredTip: true,
				showBatchBtns: true,
				searchTerm: null,
				isMyObject: true,
				showMultiple: me.options.setSpecSkuShowMultiple ? me.options.setSpecSkuShowMultiple(me.singleSpec) : me.singleSpec ? false : true,
				sizeType: 'md',
				isShowAllChecked: !CRM._cache.cpqStatus, // 开cpq时，禁用全选
				beforeEditFn: function (opts, next) {
					return me.beforeEditTableCellHandle(opts, next);
				},
			};
			if (me.opType <= 2) {
				selfColumns = me.model.getSkuColumns();
				columns = me.getColumn(selfColumns, true);
				me.table = new Table(_.extend({
					$el: me.$('.j-table-wrap'),
					columns: columns,
					height: "auto",
					maxHeight: $(window).height() - 180
				}, options, {
					initComplete: function () {
						me.table && me.table.doStaticData(data);
						me.renderRemoved();
						me.updateTableStatus();
						if (data.length >= 1 && me.errorFlag) {
							me.hideErr();
						}
					}
				}));
			} else {
				var MyTable = ObjTable.extend({
					getOptions: function () {
						var op = _.extend({
							checked: {
								idKey: '_id',
								data: remberData
							},
							maxHeight: me.tableMaxHeight || "none",
							autoHeight: me.tableMaxHeight ? false : true,
							stopWheelEvent: me.tableMaxHeight ? false : true,
						}, options);
						return _.extend(ObjTable.prototype.getOptions.apply(this, arguments), op)
					},
					getColumns: function () {
						selfColumns = _.clone(this.get('columns')) || [];
						columns = me.getColumn(selfColumns);
						// columns = me.addPriceBookPriceColumn(columns, apiname);
						return columns;
					},
					initComplete: function () {
						let tData = me.model.get('tableData');
						me.table.doStaticData(tData);
						me.hideLoading();
					},
				});
				me.table = new MyTable({
					el: me.$('.j-table-wrap'),
					apiname: apiname,
					showTitle: false,
					operate: true,
					listType: 'selected'
				});

				me.table.render()

				var $countEl = me.$('.dke-table-layout').find('.j-h-count');
				$countEl.html(remberData.length);
			}

			me.table.on('trclick', function (row, $tr, $target) {
				var index = $tr.attr('data-index') * 1,
					value = row.object_describe_id;
				if ($target.hasClass('tb-del-spec')) {
					me.removeObjHandle(row, index, $target);
				}
				if ($target.hasClass('tb-showBomCoreConfig')) {
					let id = me.getDataId(row);
					me.currentRow = row;
					me.renderBomCoreObj(id, 'configBtn');
				}
			});
			me.table.on('checkbox.click', function (isChecked, target, checkedLen, isSingle, isAll, index) {
				var opType = me.opType,
					checkData = me.opType >= 3 ? me.table.getRemberData() : me.table.getCheckedData(),
					num = checkData ? checkData.length : 0,
					$btnEl = me.$('.dke-table-layout').find('.del-btn'),
					$countEl = me.$('.dke-table-layout').find('.j-h-count'),
					numText = opType >= 3 ? num : (num >= 1 ? $t("已选产品") + "(" + num + ")" : $t("未选择") + "(0)");

				num >= 1 ? $btnEl.show() : $btnEl.hide();
				$countEl.html(numText);

				let currentRow = me.currentRow = me.table.getCurData().data[index];
				if (isChecked && util.isBom(currentRow).isPackage && !me.singleSpec) {
					let id = me.getDataId(currentRow);
					me.renderBomCoreObj(id, 'checkbox');
				}
			});

			// 规格值变化修改产品名称
			me.table.on('cell.change', function (data, column, type, obj) {

				let specs = _(me.model.get('optionsData')).pluck('name');

				if (_.contains(specs, column.data)) {
					// 规格列不可编辑，只可清空，删除规格值重新组合name
					// 备份规格值id,'_内存': xxx, 取消选中某规格值更新列表中的数据用
					obj.cellData[`_${column.data}`] = obj.oldData[column.data];
					me.renderTable();
				}

				if (column.data === 'name' && obj.cellData.name !== null) {
					obj.cellData['nameIsFrozen'] = true; // 产品名称被修改且非空
				}

				if (column.data == "pricing_mode") {
					me.editPricingMode(data, obj)
				}
			});

			me.trigger('initTableComplete', me.table);
		},

		// 开了强制优先级，从产品添加时，添加一列价目表价格；
		// addPriceBookPriceColumn(columns, apiname) {
		// 	if (CRM.util.isGrayScale('CRM_SHOW_PRICEBOOK_PRICE') && CRM._cache.priceBookPriority && apiname === 'ProductObj') {
		// 		let index = util.findIndex(columns, item => item.api_name === 'price');
		// 		if (!util.hasValue(index)) index = util.findIndex(columns, item => item.api_name === 'name');
		// 		if (util.hasValue(index)) {
		// 			columns.splice(index + 1, 0, {
		// 				data: 'prick_book_price',
		// 				title: $t('价目表价格'),
		// 				render: function (data, type, full) {
		// 					if (full.extend_info && full.extend_info.realPriceInfo) {
		// 						return full.extend_info.realPriceInfo.pricebook_price;
		// 					}
		// 					return '';
		// 				}
		// 			})
		// 		}
		// 	}
		// 	return columns;
		// },

		//只读或删除项不可编辑
		beforeEditTableCellHandle: function (opts, next) {
			let $tr = opts.$tr,
				flag = $tr.hasClass('tr-readonly') || $tr.hasClass('tr-removed')
			if (flag) {
				$tr.removeClass('tr-edit');
				$tr.find('td').removeClass('td-edit');
				return false
			} else {
				next();
			}
		},

		/**
		 *  @desc 处理勾选配置数据
		 */
		parseData: function (data) {
			var res = [];
			_.each(data, function (item, key) {
				res = res.concat(item)
			});
			return res
		},

		//判断table是否需要滚动条
		setScrollTable: function () {
			if (this.opType >= 3) {
				var $table = $('body').find(".cp-table"),
					$tableMain = $table.find('.main'),
					winHeight = $(window).height(),
					tableTop = $table.offset().top,
					tableHeight = $table.find('.main-con').height(),
					countHeight = tableTop + tableHeight;
				if (countHeight >= winHeight) {
					var height = winHeight * 0.6 + 'px';
					$tableMain.css("height", height);
				} else {
					$tableMain.css("height", "auto");
				}
			}
		},

		//删除一条数据：作废or移除
		removeObjHandle: function (data, index) {
			var me = this;
			me.model.removeHandle(data, index, function () {
				var _checkedData = me.table.getCheckedData();
				if (_checkedData) {
					me.model.trigger("change:tableData");
				} else {
					//修改当前tr的dom
					var $curTrs = me.$('.tr[data-index=' + index + ']');
					var flag = $curTrs.hasClass('tr-removed') ? true : false;

					if (flag) {
						$curTrs.removeClass('tr-removed');
						$curTrs.find('.tb-del-spec').html($t("删除"));
						$curTrs.find('.checkbox-item').removeClass('checkbox-item-disabled');
					} else {
						$curTrs.addClass('tr-removed');
						$curTrs.find('.checkbox-item').addClass('checkbox-item-disabled');
						$curTrs.find('.tb-del-spec').html($t("恢复"));
					}

				}
				//重置checkbox和已选择
				me.resetBatchBtns();
			})
		},

		// 批量删除选中的数据
		batchRemoveObjHandle: function () {
			let checkedTable = this.table.getCheckedData();
			_.each(checkedTable, ct => {
				this.removeObjHandle(ct, ct.__tbIndex);
			});
		},

		/*
		 * table批量操作——产品批量上下架
		 * isUp：true－上架；false－下架
		 * status：1-上架；2-下架
		 */
		batchUpObj: function () {
			this.pdStatusHandle(true);
		},

		batchDownObj: function () {
			this.pdStatusHandle(false);
		},

		pdStatusHandle: function (isUp) {
			var me = this,
				checkedTable = me.table.getCheckedData(),
				keyArr = [];

			_.each(checkedTable, function (ct) {
				keyArr.push(ct.__tbIndex);
			})

			me.model.batchEdit(keyArr, isUp, function () {
				me.resetBatchBtns();
				me.model.trigger("change:tableData");
			})
		},

		resetBatchBtns: function () {
			var me = this,
				$btnEl = me.$('.dke-table-layout').find('.del-btn'),
				$countEl = me.$('.dke-table-layout').find('.j-h-count'),
				numText = $t("未选择") + "(0)";
			$btnEl.hide();
			$countEl.html(numText);
			me.table.clearRemberData();
		},

		//规格“全部操作”：批量选中该规格下全部规格值
		allOptionHandle: function (e) {
			var me = this,
				$item = $(e.target),
				$parent = $item.parents('.cpopt-wrapper'),
				index = $parent.index(),
				value = $item.hasClass('option-all-choosed') ? "0" : "2",
				specId = $parent.attr('data-id'),
				$optItems = $parent.find('.cpopt-area').children('.cpopt-item'),
				$allSpecBtn = this.$('.all-spec');

			me.model.batchChoose(specId, value, function () {
				me.model.trigger("change:optionsData");
			});

			if (value == '2') {
				$item.html($t("清空")).addClass('option-all-choosed');
				$optItems.attr('data-status', value).addClass("active");
				me.setTableChecked();
			} else {
				$item.html($t("全选")).removeClass('option-all-choosed');
				$optItems.attr('data-status', value).removeClass("active");
				var flag = me.model.get('allFlag')
				if (flag) {
					$allSpecBtn.removeClass('all-cancel');
				}
				me.model.trigger("change:tableData");
			}
		},

		getChangedData: function () {
			return this.checkedData
		},

		//添加规格or删除规格
		//status:0-未选中，1-历史数据已选中，2-触发选中，3-历史数据存在不可被选中
		specValueChange: function (e) {
			var me = this,
				$item = $(e.target),
				$parent = $item.parents('.cpopt-wrapper'),
				idx = $item.index(),
				// index = $parent.index(),
				cid = $parent.data('cid'),
				allSpecDom = this.$('.cp-options, .cp-selects').children('li'),
				// 过滤出有选中项的规格和正在点击的规格
				filterSpecDom = allSpecDom.filter(function (index, dom) {
					return $('.cpopt-item[data-status="1"], .cpopt-item[data-status="2"]', this).length != 0 || $(this).is($parent);
				}),
				// 找出自身的正确index
				indexInAll = filterSpecDom.index($parent),
				status = $item.attr('data-status'),
				// specId = me.selects[index].getValue(),
				specId = (_(me.selects).findWhere({ cid: cid }) || {}).getValue(),
				value = '';

			if (me.opType == 2 && (status == "1" || status == "3")) {
				return;
			}

			value = $item.attr('data-status') == '2' ? "0" : "2";
			/*
			 *specId——规格Id，idx——规格值是当前规格options数组的第idx项，
			 *value——添加or删除
			 *index——规格项是第几个规格－>表格排序
			 */
			if (me.opType == 2) {
				// let editOptions=$('.cp-options').find('li').length;
				// index=index*1+editOptions;
				//******************
				//当前规格项中第一个添加值->添加到历史产品上了
				let curSpec = me.model.get('opSelected').find(item => item.value == specId),
					specvalToOld = curSpec && curSpec.options[0];
				if (specvalToOld) {
					let $opts = $item.siblings(),
						$specvalToOld = $parent.find('.cpopt-item[title=' + specvalToOld.name + ']');
					//将添加到历史产品上的新规格值设为不可编辑
					if (curSpec.options.length == 1 && value == '2') {
						$specvalToOld.attr('data-status', '1');
						$specvalToOld.addClass('disabled').removeClass('active');
					} else if (curSpec.options.length == 2 && value !== '2') {
						//释放添加到历史产品上的新规格值设为可编辑
						$specvalToOld.attr('data-status', '2');
						$specvalToOld.addClass('active').removeClass('disabled');
					}
				}
				//******************
			}
			// me.model.setSpecValue(specId, idx, value, index, function() {
			me.model.setSpecValue(specId, idx, value, indexInAll, function () {
				$item.attr('data-status', value);
				value == '2' ? $item.addClass('active') : $item.removeClass('active');
			});
			me.showLoading();
			me.model.trigger("change:optionsData");

		},

		setTableChecked: function () {
			var $allSpecBtn = this.$('.all-spec'),
				allFlag = this.model.get('allFlag');
			//全部规格按钮取消or选中状态
			allFlag ? $allSpecBtn.removeClass('all-cancel') : $allSpecBtn.addClass('all-cancel');
			this.model.trigger("change:tableData");
		},

		//全选or取消全选
		allSpecHandle: function (e) {
			var me = this,
				$target = $(e.target),
				optionsData = this.model.get('optionsData'),
				tableData = this.model.get('cacheData');
			if ($target.hasClass('all-cancel')) {
				$target.removeClass('all-cancel');
				me.model.set('allFlag', true);
				me.model.set('opSelected', []);
				//规格值都取消选中状态
				var $items = $('.cp-options').find('.active');
				$items.attr('data-status', "0");
				$items.removeClass('active');
				_.each(optionsData, function (op) {
					_.each(op.options, function (item) {
						item.status = status;
					})
				})
				me.model.set('optionsData', optionsData);
				//规格清空改为全选
				$('.j-option-all').html($t("全选")).removeClass('option-all-choosed');
			} else {
				return false
			}
			me.table.doStaticData(tableData);
		},

		setDefaultPrice: function (data) {
			var me = this,
				tableData = this.model.get('tableData');

			me.stPrice = data;
			_.map(tableData, function (i) {
				i.price = i.price || me.stPrice;
			});
			me.model.trigger("change:tableData");
		},

		//格式化sku数据
		parseValue: function () {
			var data = this.table.getCurData(),
				opSelected = this.model.get('opSelected'),
				cacheData = this.model.get('cacheData'),
				od = this.model.get('od'),
				me = this,
				skuData = [];


			data = data.concat(cacheData);
			_.map(data, function (d) {
				// 删除规格值，提交时需要重新处理spec_and_value
				let spec_and_value = [];
				for (let i = 0; i < opSelected.length; i++) {
					let spec_value_id = d[opSelected[i].name];
					if (!_.isNull(spec_value_id) && !_.isUndefined(spec_value_id)) {
						let item = {
							"spec_id": opSelected[i].value,
							"spec_value_id": spec_value_id,
							"spec_value_name": (_.findWhere(opSelected[i].options, { value: spec_value_id }) || {})['name'],
							"order_field": i + ""
						};
						spec_and_value.push(item);
					}
				}

				if (d._id) {
					//编辑历史数据----status:1－编辑 2-新增  3-删除 4-已作废 6-有新增规格的历史数据, 5-规格值删除
					if (d.is_deleted) {
						d.status_flag = 4;
					} else if (d.status_flag) {
						d.status_flag = d.status_flag
					} else {
						d.status_flag = 1;
						if (d.spec_and_value.length !== opSelected.length) {
							d.status_flag = 6;
						} else if (d.spec_and_value.length !== spec_and_value.length) {
							d.status_flag = 5;
						}
					}
					// if(d.status_flag==6){
					// d.spec_and_value=spec_and_value;
					// }

					d.spec_and_value = spec_and_value;
					skuData.push(d);
				} else {
					//新增数据
					var newItem = _.extend({}, d, od);

					if (me.opType == 2) {
						newItem.status_flag = 2
					}

					_.extend(newItem, {
						"spec_and_value": spec_and_value
					});
					skuData.push(newItem);
				}
			});
			let skuKeys = Object.keys(skuData);
			if (skuKeys.length == 0) {
				me.showErr();
			} else {
				me.hideErr();
			}

			this.validateTableValue(skuData);
			this.validateTable();
			return skuData;
		},
		showErr: function (txt) {
			if (this.errorFlag) {
				return
			} else {
				this.$el.append('<div style="margin-left:68px;" class="fm-error crm-ico-error"></div>');
				this.$el.find('.fm-error').html(txt || $t("请至少选择一个产品"));
				this.errorFlag = true;
			}
		},
		hideErr: function (tex) {
			this.$el.find('.fm-error').remove();
			this.errorFlag = false;
		},

		validateTableValue: function (data) {
			let names = [];

			// 产品名称重复校验根据配置
			const nameCol = (this.table.getAllColumns() || []).find(c => c.api_name == "name"),
				//is_unique存在且为false时不需要校验，其他场景都需要校验
				checkNameDuplicate = nameCol?.is_unique !== false;

			for (let index = 0; index < data.length; index++) {
				const item = data[index];
				// 同一行产品所有规格值为空
				let specsInLine = item.spec_and_value.every(spec => (spec.spec_value_id == null || spec.spec_value_id == undefined));
				if (item.is_removed || item.status_flag == 3) { // 被删除的跳过
					continue;
				}
				if (specsInLine) {
					this.showErr(item.name + $t('产品至少有一个规格值'));
					return false;
				}

				// 规格组合重复或有包含关系
				let specsArray = _.pluck(item.spec_and_value, 'spec_value_id');
				let specNames = _.pluck(item.spec_and_value, 'spec_value_name').join('-');
				let contains = _.some(data.slice(0, index), (a, ind) => {
					let array = _.pluck(a.spec_and_value, 'spec_value_id');
					let interSize = _.size(_.intersection(array, specsArray)); // 交集大小

					if (a.is_removed || a.status_flag == 3) {
						return false;
					}

					if (interSize == array.length || interSize == specsArray.length) { // 包含或者相等
						if (interSize == array.length && interSize == specsArray.length) { // 相等
							this.showErr(item.name + $t('产品[{{specs}}]已存在]', { specs: specNames }));
						} else {
							this.showErr(item.name + $t('产品的规格值组合[{{specs}}]与{{prevName}}产品的规格值组合[{{prevSpecs}}]存在包含关系', {
								specs: specNames,
								prevName: a.name,
								prevSpecs: _.pluck(a.spec_and_value, 'spec_value_name').join('-')
							}));
						}
						return true;
					}
					return false;
				});

				if (contains) {
					return false;
				}

				if (item.name == undefined) {
					this.showErr($t(`产品名称字段值必填`));
					return false;
				}
				if (checkNameDuplicate) {
					// 产品名称重复
					if (names.indexOf(item.name) != -1) {
						this.showErr($t(`产品名称字段值[{{name}}]已存在`, { name: item.name }));
						return false;
					}
					names.push(item.name);
				}

			}

			return true;
		},

		// 默认表格校验，校验字段必填
		validateTable() {
			const data = this.table.getCurData().filter((item) => (!item.is_removed && item.status_flag != 3));
			const validate = this.table.validateTable(data);
			if (!validate) this.showErr($t('请输入必填项'));
		},

		/**
		 * @desc 获取当前勾选项
		 * @return {*}
		 */
		getValue: function () {

			var me = this;
			if (!me.table) {
				return ""
			}
			if (this.opType <= 2) {
				return me.parseValue();
			} else {
				var data = this.singleSpec ? me.table.getCurData().data : me.table.getRemberData();
				_.each(data, function (item, index) {
					var Id = item.product_id || item._id;
					if (me.checkedData.hasOwnProperty(Id)) {
						var selectSub = me.checkedData[Id];
						item.price = selectSub.totalMoney;
						item.rowId = util.uniqueCode();
						var allSub = [];
						_.each(selectSub.data, function (sub) {
							_.each(sub, function (s, index2) {
								s.rowId = util.uniqueCode();
								s.pid = item.rowId;
							});
							allSub = allSub.concat(sub)
						});
						if (allSub.length) item.children = allSub;
					}
					// bom版本选配信息
					if (me.bomCheckedData[Id]) {
						let { _id, name, core_version, category, subBomData } = me.bomCheckedData[Id];
						item.core_id = _id;
						item.core_id__r = name;;
						item.node_bom_core_type = category;
						item.node_bom_core_version = core_version;
						item.subBomData = subBomData;
					}
				});
				return data
			}
		},

		getCheckedFormatData: function (data) {
			return data && this.table && this.table.table.getCheckedFormatData(data);
		},

		//移除条目样式
		renderRemoved: function () {
			var me = this,
				curTableData = me.table.getCurData();
			me.__startTime = new Date().getTime();
			_.each(curTableData, function (td, idx) {
				var $trs = me.$('.tr[data-index=' + idx + ']');
				if (td.is_removed) {
					$trs.addClass('tr-removed');
					$trs.find('.checkbox-item').addClass('checkbox-item-disabled');
				} else if (td.data_right_flag == 'readonly') { //只读权限
					$trs.addClass('tr-readonly');
					$trs.find('.checkbox-item').addClass('checkbox-item-disabled');
					$trs.find('a').removeClass('j-tb-addimg').removeClass('j-edit-img');
				}
			})
			console.log('renderRemovedtable init耗时:' + (new Date().getTime() - me.__startTime));
			me.hideLoading();
		},

		collapseSku: function (e) {
			var me = this,
				$target = $(e.target).parent('.collapse'),
				tableData = this.model.get("tableData"),
				supplementData = this.model.get("supplementData"),
				totalArr = [];

			if (this.collapseFlag) { //当前折叠——>展开
				$target.removeClass('unflod');
				totalArr = totalArr.concat(tableData, supplementData);

			} else { //展开
				$target.addClass('unflod');
				totalArr = tableData;
			}
			me.table.doStaticData(totalArr);
			me.updateTableStatus();
			this.collapseFlag = !this.collapseFlag;
			setTimeout(function () {
				me.renderRemoved();
			}, 100)

		},

		replaceSpecHandle: function (e) {
			const me = this,
				$parent = $(e.target).parents('.cpopt-wrapper'),
				$wrapper = $(e.target).siblings('.cpopt-options'),
				idx = $parent.index(),
				optionsData = me.model.get('optionsData'),
				originSpecs = me.model.get('originSpecs'),
				options = optionsData[idx];
			let opSelected = me.model.get('opSelected');
			require.async('./replaceSpec', (ReplaceSpec) => {
				me.ReplaceSpecCom = null;
				me.ReplaceSpecCom = new ReplaceSpec({
					data: options,
					canCreateSpec: me.canCreateSpec,
					callback: (data, newSpecs) => {
						let oldOpt = {},
							newOpt = {},
							optionsArr = [];
						//如果什么都没有做
						if ((!newSpecs || newSpecs.length <= 0) && !data) {
							return
						}
						//如果有新增规格值
						if (newSpecs && newSpecs.length >= 1) {
							options.options = [].concat(options.options, newSpecs);
						}

						//修改optionsData
						_.each(options.options, (opt) => {
							if (data && opt.value == data.used) {
								let flag = _.findWhere(originSpecs[options.value], { value: opt.value });
								if (flag) {
									opt.status = '3';
								} else {
									opt.status = '0';
								}
								oldOpt = opt;
							} else if (data && opt.value == data.usable) {
								opt.status = '1';
								newOpt = opt;
							} else {
								optionsArr.push(opt)
							}
						})
						if (data) {
							optionsArr.push(oldOpt);
							optionsArr.splice(0, 0, newOpt);
						}
						optionsData[idx].options = optionsArr;
						$wrapper.html(optItemTpl({
							data: me._parseValue(optionsArr),
							opType: me.opType,
							statusClass: me.statusClass
						}));
						if (data) {
							$wrapper.find('.cpopt-item[data-key=0]').css('font-weight', 'bold');
						}
						me.model.set('optionsData', optionsData)

						//如果规格值变更,做相应改变
						let tableData = me.model.get('tableData');
						if (data) {
							//修改opSelected
							opSelected = _.map(opSelected, (os) => {
								if (os.value == options.value) {
									os.options = _.map(os.options, (oo) => {
										if (oo.value == data.used) {
											oo = newOpt
										}
										return oo;
									})
								}
								return os
							})
							me.model.set('opSelected', opSelected);

							//修改表格
							tableData = _.map(tableData, (td) => {
								if (td[options.name] == data.used) {
									_.each(td.spec_and_value, (s) => {
										if (s.spec_value_id == data.used) {
											s.spec_value_id = data.usable
											s.spec_value_name = newOpt.name,
												s.change_spec_vale = true;
										}
									})
									td[options.name] = data.usable;
									td.status_flag = 5;
								}
								return td;
							})
							me.model.set('tableData', tableData)
						}

						if (newSpecs && newSpecs.length >= 1) {
							me.model.trigger("change:optionsData");
						} else if (data) {
							me.table.doStaticData(tableData);
							me.updateTableStatus();
						}
						me.ReplaceSpecCom.destroyed()
					}
				});
				me.ReplaceSpecCom.show();
			});
		},
		setExpandBtn: function () {
			const $specComp = $('.cp-options-content', this.$el),
				specCompHeight = $specComp.height(),
				$toggleBtn = $(".spec-toggle-btn", this.$el);
			if (specCompHeight > this.specCompMaxHeight) {
				$toggleBtn.show();
				$specComp.css("max-height", `${this.specCompMaxHeight}px`);
			}
		},
		toggleSpecContainer: function () {
			const $specContent = $(".cp-options-content", this.$el),
				$toggleBtn = $(".spec-toggle-btn", this.$el),
				$text = $toggleBtn.find('span'),
				$icon = $toggleBtn.find('i');
			$specContent.toggleClass("expanded");
			$icon.removeClass("fx-icon-arrow-up fx-icon-arrow-down");

			// 切换按钮文本
			if ($specContent.hasClass("expanded")) {
				$specContent.css("max-height", "none");
				$text.text($t("收起更多"));
				$icon.addClass("fx-icon-arrow-up");
			} else {
				$specContent.css("max-height", `${this.specCompMaxHeight}px`);
				$text.text($t("crm.spec_expand"));
				$icon.addClass("fx-icon-arrow-down")
			}
		},

		//批量设置列数据
		batchSetColumnValue: function (field, value) {
			const columns = this.table.getAllColumns();

			if (columns.find(c => c.api_name === field)) {
				const tableData = this.table.getCurData();

				if (tableData) {
					const result = {};

					tableData.forEach((data, key) => {
						result[key] = { [field]: value };
					});

					this.table.setCellsValue(result);
				}
			}
		},

		//批量设置列状态
		batchSetColumnStatus: function (field, config, status) {
			const columns = this.table.getAllColumns();

			if (columns.find(c => c.api_name === field)) {
				const tableData = this.table.getCurData();

				if (tableData.length) {
					// 为每行数据的特定列设置状态
					for (let i = 0; i < tableData.length; i++) {
						this.table.setCellsStatus(field, i, {
							[config]: status
						});
					}
				}
			}
		},

		/************************ 周期性相关逻辑 ************************/

		//更新周期性相关字段单元格状态
		updateTableStatus(dataMap) {
			if(this.opType>2){
				return;
			}
			if(!dataMap){
				const tableData = this.table?.getCurData() || [];
				dataMap = Object.fromEntries(tableData.map((data,index)=>[index,data]));
			}
			Object.entries(dataMap).forEach(([key,data])=>{
				this.togglePeriodicFieldsStatus(data.pricing_mode, key);
			})
		},

		//编辑【定价模式】字段
		editPricingMode(data, obj) {
			const index = obj.$tr?.[0]?.getAttribute('data-index');
			if (index === null || index === undefined) {
				return;
			}
			// 清空周期性字段数据
			if (data === "one") {
				const cleanData = Object.fromEntries(this.periodicFields.map(field => [field, null]));
				this.table.setCellsValue({ [index]: cleanData }, true);
			}
			// 设置字段状态
			this.togglePeriodicFieldsStatus(data, index);
		},

		// 设置周期性相关字段状态
		togglePeriodicFieldsStatus(value, index) {
			const status = value === "one" ? "readonly" : "notreadonly";
			this.periodicFields.forEach(field => {
				this.table.setCellsStatus(field, index, { [status]: true });
			})
		},

		destroy: function () {
			this.$el.empty();
			this.undelegateEvents(), this.stopListening(), this.off();
			this.pickBomCore && this.pickBomCore?.destroy();
			this.model.off();
			this.model.destroy();
			this.model = this.table = this.events = null;
		}
	})

	module.exports = CartesianProduct;
})
