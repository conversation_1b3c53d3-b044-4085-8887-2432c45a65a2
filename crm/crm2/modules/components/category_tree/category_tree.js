/**
 * @desc: 分类组件;树型展示
 * @author: wang<PERSON><PERSON>
 * @date: 2020-03-08
 */
define(function (require, exports, module) {
	var util = CRM.util;

	module.exports = Backbone.View.extend({

		options: {
			$el: null,
            categoryModelType: void(0),
			defaultV: null, // 默认值
			showAllBtn: true, // 是否显示全部
            showFoldIcon: false, // 平铺样式搜索框左侧折叠按钮
			hasDraggable: true, // 是否支持可拖拽，默认支持，极简订单上不支持拖拽
			allowDraggable: true, // 允许拖拽宽度
		},

		events: {},

		initialize() {
			let _this = this;
			this.$el = this.options.$el;
			this.caregoryData = this.options.categoryData;
			if (!this.$el) return;
			this.setDefValue();
			this.getAllCategory(function () {
				_this.render()
			})
		},

		setDefValue() {
			this.checkedData = this.options.defaultV ?  this.options.defaultV : {
				Name: $t("全部分类"),
				CategoryCode: '',
				CategoryID: ''
			};
		},

		render() {
			var me = this;
			this.parseData(this.caregoryData);
            var originData = $.extend(true, {}, this.caregoryData);

			this.caregoryData = util.parseDataToTree(this.caregoryData);
			if(!this.caregoryData.length){
				me.$el.html('<span class="no-data">'+ $t('无数据') +'</span>');
				return;
			}

            // 平铺组件
            let categoryCompPath = `crm-modules/common/tree_search_slide/tree_search_slide`;

            if (this.options.categoryModelType) {
                if (this.options.categoryModelType === '1') {
                    categoryCompPath = `crm-modules/common/fx_tree_search/fx_tree_search`;
                }
            } else if (CRM._cache.category_model_type) {
                categoryCompPath = `crm-modules/common/fx_tree_search/fx_tree_search`;
            }
			require.async(categoryCompPath, function (Category) {
				me._cateGory = new Category({
					$el: me.$el,
					treeData: me.caregoryData,
                    originData,
					addClass: 'crm-category-tree',
					type: me.options.type,
					showAllBtn: me.options.showAllBtn,
                    showFoldIcon: me.options.showFoldIcon, // 树形分类没有实现折叠按钮
					btns: me.options.btns || [],
                    parseBtns: me.options.parseBtns,
					hasDraggable: me.options.hasDraggable,
					allowDraggable: me.options.allowDraggable,
					showCloseBtn: true,
					showContainerCloseIcon: true,
				});
                me.afterCategoryComponentRender(me._cateGory);
                me._cateGory.on('fold', function() {
                    me.trigger('fold');
                });
                me._cateGory.on('hide:sidePanel', function() {
                    me.trigger('hide:sidePanel');
                });
				me._cateGory.on('sel.suc', function (data) {
					data ? me.setValue(data) : me.setDefValue();
					me.trigger('sel.suc', me.checkedData)
				});
				me._cateGory.on('toggle', () => {
					me.trigger('toggle');
				});
				var checkData = me.checkedData;
				var defSelect = checkData.CategoryCode ? me._findDefault(checkData.CategoryCode): '';
				if(defSelect && defSelect._id){
					setTimeout(() => {
						// 防止因为同步事件，造成me._cateGory的sel.suc事件发出，而监听事件还没有监听到
						me._cateGory.expandSomeNode(defSelect._id);
					} ,10);
				}
                me.on('update:catcas', function(id) {
                    me._cateGory.trigger('update:catcas', id);
                });
			});
		},

		_findDefault(CategoryCode){
			let r;
			util.forEachTreeData(this.caregoryData, v => {
				if(v.category_code === CategoryCode){
					r = v
				}
			});
			return r;
		},

        afterCategoryComponentRender: $.noop,

		getAllCategory(callback) {
			var me = this;
			if (this.caregoryData) {
				callback && callback();
				return;
			}
			CRM.util.getCategoryDataList().then((list) => {
				me.caregoryData = list;
				callback && callback();
			});
		},

		parseData(data) {
			_.each(data, function (item) {
				item.rowId = item._id;
				item.id = item._id;
				item.label = item.name__r || item.name;
			})
		},

		setValue(data){
			if (_.isObject(data) && data.type === 'customBtn') {
				this.checkedData = {
					Name: data.label,
					CategoryCode: '',
					CategoryID: '',
					ButtonType: data.type,
					ButtonAction: data.action,
                    attribute_constraint_id: data.attribute_constraint_id
				};

				return;
			}

			this.checkedData = {
				CategoryCode: data.code,
				CategoryID: data._id,
				CategoryOrder: data.order_field,
				Name: data.name,
				ParentID: data.pid,
                attribute_constraint_id: data.attribute_constraint_id
			}
		},

		getValue() {
			return this.checkedData;
		},

		destroy(){
			this._cateGory && this._cateGory.destroy();
			this._cateGory = null;
			this.remove()
		}

	})

})
