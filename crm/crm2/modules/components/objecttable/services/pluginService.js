/**
 * plugin service
 */

define(function(require, exports, module) {
  const thenable = {
    then(cb) { cb(); }
  };

  const createSyntheticHooksPlugin = (options = {}) => {
    const {namespace = 'list'} = options;
    
    /**
     * parseHookName
     * @param {*} hookName 
     * @param {*} phases 
     * @returns 
     * @example
     * ```
     * beforeRender -> render.before
     * afterRender  -> render.after
     * ```
     */
    const parseHookName = (hookName, phases = ['before', 'after']) => {
      const phase = phases.find((p) => hookName.startsWith(p));
    
      if (!phase) return;
    
      const name = hookName.slice(phase.length);
      if (!name || !/^[A-Z]/.test(name)) return;
    
      return `${namespace}.${name[0].toLowerCase()}${name.slice(1)}.${phase}`;
    };
    
    /**
     * transformHooks
     * @param {Object} hooks 
     * @returns {Array<Object>} 
     */
    const transformHooks = (hooks) => {
      return Object.entries(hooks).map(([hookName, hook]) => {
        const eventName = parseHookName(hookName);
        if (hookName) {
          return {
            event: eventName,
            functional: function(plugin, context) {
              plugin = plugin ? _.pick(plugin, ['preData']) : plugin;
              return hook.call(hooks, plugin, context);
            }
          }
        }
      }).filter(Boolean);
    }
  
    return function hooksToPlugin(hooks) {
      return class SyntheticHooksPlugin {
        constructor() {
          this.hooks = transformHooks(hooks);
        }
    
        apply() {
          return this.hooks;
        }
      };
    };
  };

  // objecttable mixins
  const PluginService = {

    /**
     * hooksToPlugin
     * @returns Plugin
     */
    createHooksPlugin: createSyntheticHooksPlugin({
      namespace: 'list'
    }),

    /**
     * run [R]ender [B]efore [P]lugin Service
     */
    runRbpService() {
      return new Promise((resolve) => {
        const context = {
          recordType: this.get('recordType'),
        };

        this.runPshk('list.render.before', context).then((rst) => {
          if (rst && rst.Value) {
            rst = rst.Value;

            this.setPsrp('columnsExtendConfig', rst.columnsExtendConfig);
            this.setPsrp('filterExtendConfig', rst.filterExtendConfig);
            this.setPsrp('termExtendConfig', rst.termExtendConfig);
            this.setPsrp('imageExtendConfig', rst.imageExtendConfig);
            this.setPsrp('viewInfoExtendConfig', rst.viewInfoExtendConfig);
            this.setPsrp('actionExtendConfig', rst.actionExtendConfig);
            this.setPsrp('afterRequestDescribe', rst.afterRequestDescribe);
            this.setPsrp('formatListDataAsync', rst.formatListDataAsync);
            this.setPsrp('formatRequestParam', rst.formatRequestParam);
            this.setPsrp('allSummaryFields', rst.allSummaryFields);
            this.setPsrp('summaryFields', rst.summaryFields);
            this.setPsrp('selectedSummaryFields', rst.selectedSummaryFields);
            this.setPsrp('listRenderedCompleteCallback', rst.listRenderedCompleteCallback);
            this.setPsrp('fieldChangeCallback', rst.fieldChangeCallback);
            this.setPsrp('customFilterValue', rst.customFilterValue);
            this.setPsrp('forceTrWrap', rst.forceTrWrap);
            this.setPsrp('enableLiveFiltering', rst.enableLiveFiltering);
            this.setPsrp('pluginButtons', rst.buttons);
            this.setPsrp('operateBtns', rst.operateBtns);
            this.setPsrp('tableOptions', rst.tableOptions);
            this.setPsrp('treeConfig', rst.treeConfig);
            this.setPsrp('disableFeatures', rst.disableFeatures);
          }

          resolve();
        });
      })
    },

    /**
     * run [R]ender [A]fter [P]lugin Service
     */
    runRapService() {
      const me = this;

      // clean up
      me.clearPsrp('__batchtermSlot');
      return me.runPshk('list.render.after').then((rst) => {
        if (!rst || !rst.Value) {
          return;
        }

        const batchtermSlot = rst.Value.batchtermSlot;
        if (batchtermSlot) {
          const slot = batchtermSlot({
            wrapper: me.table.createBatchtermElement()
          });

          if (!slot || !slot.destroy) {
            throw new Error('The return value does not have a destroy method.');
          }

          me.setPsrp('__batchtermSlot', slot);
        }
      });
    },

    /**
     * run [I]mage [P]review [B]efore [P]lugin Service
     */
    runIpbpService(field, obj, callback) {
      this.runPshk('list.previewImage.before', {
        objectApiname: this.get('apiname'),
        fieldName: field.api_name,
        data: obj.data,
        trData: obj.trData,
        startIndex: obj.startIndex,
        field,
      }).then(rst => {
        callback(rst && rst.Value && rst.Value.stopPreView);
      });
    },

    /**
     * run [L]ist [Q]uery[P]aram[c]hange [P]lugin Service
     */
    runLqpcpService(params) {
      this.runPshk('list.queryParamChange.end', params);
    },

    pluginHeaderResponseFormatter(res) {
      const rv = res.Value;

      const afterRequestDescribe = this.getPsrp('afterRequestDescribe');
      if (afterRequestDescribe) {
        const updated = afterRequestDescribe(rv);
        if (updated) {
          Object.assign(rv, updated);
        }
      }

      const termExtendConfig = this.getPsrp('termExtendConfig');
      if (termExtendConfig && res && res.Value) {
        const {retain, del} = termExtendConfig;

        if (retain) {
          res.Value.templates = _.filter(res.Value.templates, a => 
            _.contains(retain, a.api_name) || _.contains(retain, a._id)
          );
        }

        if (del) {
          res.Value.templates = _.filter(res.Value.templates, a => 
            !_.contains(del, a.api_name) && !_.contains(del, a._id)
          );
        }

        if(termExtendConfig.default) {
          let tt = _.findWhere(res.Value.templates, {is_default: true});
          tt && (tt.is_default = false);
          tt = _.findWhere(res.Value.templates, {api_name: termExtendConfig.default});
          tt && (tt.is_default = true);
        }

        // 设置场景中配置的filters
        const readonlyFilters = termExtendConfig.readonlyFilters;
        if (readonlyFilters) {
          let memoizedReadonlyFilter;
          if (_.isFunction(readonlyFilters)) {
            memoizedReadonlyFilter = readonlyFilters;
          } else if (_.isBoolean(readonlyFilters)) {
            memoizedReadonlyFilter = () => readonlyFilters;
          } else if (_.isObject(readonlyFilters)) {
            memoizedReadonlyFilter = (a) => readonlyFilters[a.api_name] || readonlyFilters[a._id];
          }
          _.each(res.Value.templates, (a) => {
            a.readonlyFilters = memoizedReadonlyFilter(a);
            return a;
          });
        }
      }

      const filterExtendConfig = this.getPsrp('filterExtendConfig');
      const fields = rv.objectDescribe.fields;
      if (filterExtendConfig) {
        _.each(filterExtendConfig, (a, k) => {
          const field = fields[k];
          if (field && _.has(a, 'disabled')) {
            // 是否支持筛选
            field.is_index = !a.disabled;
          }
        });
      }
    },
  
    pluginAttrsFormatter(attrs) {
      const curTerm = attrs.defterm;

      // 所有页汇总统计
      const allSummaryFields = this.getPsrp('allSummaryFields');
      if (curTerm && allSummaryFields) {
        const fields = allSummaryFields[curTerm.id] || allSummaryFields[curTerm.apiname];

        // {type: 'sum', api_name: stirng, field_name: string}
        if (fields) {
          attrs.allSumField = fields.filter((field) => (
            field.api_name && field.type && field.field_name
          ));
        }
      }

      // 当前页汇总统计
      const summaryFields = this.getPsrp('summaryFields');
      if (curTerm && summaryFields) {
        const fields = summaryFields[curTerm.id] || summaryFields[curTerm.apiname];

        // {type: 'sum', field_name: string}
        if (fields) {
          attrs.sumField = fields.map((field) => field.field_name);
        }
      }

      // 选中数据汇总
      const selectedSummaryFields = this.getPsrp('selectedSummaryFields');
      if (curTerm && selectedSummaryFields) {
        const fields = selectedSummaryFields[curTerm.id] || selectedSummaryFields[curTerm.apiname];

        // {type: 'sum', api_name: stirng, field_name: string}
        if (fields) {
          attrs.selectedSumField = fields.map((field) => field.field_name);
        }
      }

      // plugin buttons
      this.processPslbs(attrs);

      // disableFeatures
      this.processDisableFeatures(attrs);

      return attrs;
    },

    pluginOptionsFormatter(options) {
      // tableOptions
      this.processPstos(options);

      // filterExtendConfig
      const filterExtendConfig = this.getPsrp('filterExtendConfig');
      if (filterExtendConfig) {
        _.each(filterExtendConfig, (config, fieldName) => {
        let tt = _.findWhere(this.options.filterColumns, {data: fieldName});
          if (tt) {
            const selectAttrs = ['selectType'];
            const departmentAttrs = ['groupIncludeChildrenStatus'];
            _.extend(tt, _.pick(config,
              ['components', 'onlyOr', 'defaultCompare', ...selectAttrs, ...departmentAttrs]
            ));
          }
        });

        const filterDatas = options.filterDatas;
        if (filterDatas && filterDatas.filters) {
          filterDatas.filters = filterDatas.filters.filter((item) => {
            const config = filterExtendConfig[item.FieldName];
            if (config && _.has(config, 'disabled')) {
              return !config.disabled;
            }
            return true;
          });
        }
      }

      // columnsExtendConfig
      const columnsExtendConfig = this.getPsrp('columnsExtendConfig');
      if (columnsExtendConfig) {
        let cfc =  columnsExtendConfig.filterColumns;
        let cfr =  columnsExtendConfig.render;
        let cfa = columnsExtendConfig.attrs;

        if(cfc) {
          options.columns = _.filter(options.columns, column => !_.contains(cfc, column.data));
          options.addColumns = _.filter(options.addColumns, column => !_.contains(cfc, column.data));
        }

        if (cfr || cfa) {
          _.each(options.columns, (column) => {
            if (cfr) {
              const render = cfr[column.data] || cfr[column.returnType || column.dataType];
              render && (column.render = render);
            }

            if (cfa) {
              const attrs = cfa[column.data] || cfa[column.returnType || column.dataType];
              attrs && _.extend(column, _.pick(attrs, ['showLookupText', 'isEdit', 'noSupportBatchEdit', 'disabledDel']));
            }
          });
        }
      }

      // imageExtendConfig
      const imageExtendConfig = this.getPsrp('imageExtendConfig');
      if (imageExtendConfig) {
        _.each(imageExtendConfig, (config, fieldName) => {
          let tt = _.findWhere(options.columns, {data: fieldName});
          tt && _.extend(tt, _.pick(config, ['previewWidth', 'previewHeight']));
        })
      }

      if (this.getPsrp('forceTrWrap')) {
        options.forceTrWrap = true;
      }

      if (this.getPsrp('enableLiveFiltering')) {
        options.enableLiveFiltering = true;
      }

      const treeConfig = this.getPsrp('treeConfig');
      if (treeConfig) {
        options.treeConfig = treeConfig;
      }

      const pluginFormat = this.getPsrp('formatListDataAsync');
      if (pluginFormat) {
        const originalFormat = options.formatDataAsync;
        options.formatDataAsync = async (data) => {
          return pluginFormat(
            originalFormat ? await originalFormat(data) : data
          );
        };
      }

      return options;
    },

    pluginFilterColumnsFormatter(filterColumns) {
      const filterExtendConfig = this.getPsrp('filterExtendConfig');

      if(filterExtendConfig) {
        _.each(filterColumns, (column) => {
          const a = filterExtendConfig[column.data] || filterExtendConfig[column.returnType || column.dataType];
  
          if (!a) {
            return;
          }

          if (a.filterOptions && column.options) {
            column.options = a.filterOptions(column.options);
          }

          _.extend(column, _.pick(a, ['noSupportSearch']));
        });
      }
    },

    pluginOperateButtonsFormatter(btns, data) {
      const pluginButtons = this.getPsrp('operateBtns');
  
      if (!pluginButtons) return btns;

      if (!_.isArray(btns)) btns = [];

      let adds = [], dels = [], lastRetains;
      _.each(pluginButtons, callback => {
        const btnConfig = callback(data);

        if (!btnConfig) return;

        // add
        if (btnConfig.add) {
          btnConfig.add.forEach(btn => {
            if (!_.findWhere(adds, {action: btn.action})) {
              adds.push({...btn, isPluginButton: true});
            }
          });
        }
        
        // reset
        _.each(btnConfig.reset, (rbtn, action) => {
          const btn = _.findWhere(btns, {action});
          if (btn) {
            _.extend(btn, rbtn, {isPluginButton: !!rbtn.callback});
          }
        });

        // del
        if (btnConfig.del) {
          dels = _.union(dels, btnConfig.del);
        }

        // retain 以最后一次为准
        if (btnConfig.retain) {
          lastRetains = btnConfig.retain;
        }
      });

      if (lastRetains) {
        return _.filter(btns, btn => _.contains(lastRetains, btn.action));
      }

      btns = _.filter(btns, btn => !_.contains(dels, btn.action));
      adds = _.filter(adds, btn => !_.findWhere(btns, {action: btn.action}));

      return btns.concat(adds);
    },

    /**
     * process [P]lugin [s]ervice [t]able [o]option[s]
     */
    processPstos(options) {
      // tableOptions
      const tableOptions = this.getPsrp('tableOptions');

      if (!tableOptions) return;

      Object.assign(options, tableOptions);
        
      // beforeEditFn 过滤掉dom相关属性
      if (tableOptions.beforeEditFn) {
        options.beforeEditFn = _.debounce(({column, data}, next) => {
          tableOptions.beforeEditFn({column, data}, next);
        }, 50);
      }
    },

    /**
     * process [P]lugin [s]ervice [l]ayout [b]utton[s]
     */
    processPslbs(attrs) {
      const buttons = this.getPsrp('pluginButtons');

      if (!attrs || !buttons) {
        return;
      }

      let describeButtons = attrs.layoutButtons || [];
      _.each(buttons.add, (btn) => {
        // {action: string; label: string; callBack: fn}

        // 有的话不追加
        if (_.findWhere(describeButtons, {action: btn.action})) {
          return;
        }

        const pluginButton = {
          isPluginButton: true,
          action_type: 'default',
          label: btn.label,
          action: btn.action,
          api_name: btn.action,
          is_exposed: btn.exposed,
          callback: btn.callBack || btn.callback,
        };

        const placement = btn.placement;
        if (placement && placement.base) {
          const baseline = describeButtons.findIndex((db) => {
            return db.api_name === placement.base || db.action === placement.base;
          });
          if (baseline >= 0) {
            describeButtons.splice(
              placement.relative === 'next' ? baseline + 1 : baseline,
              0,
              pluginButton
            );
          }
        } else {
          describeButtons.push(pluginButton);
        }
      });

      // [action]
      if (buttons.del) {
        describeButtons = _.reject(describeButtons, (btn) => {
          return buttons.del.includes(btn.api_name) || buttons.del.includes(btn.action); 
        });
      }

      // [{action: string; label: string;}]
      _.each(buttons.reset, (rb) => {
        const dd = _.find(describeButtons, (btn) => {
          return rb.action === btn.api_name || rb.action === btn.action;
        });

        if (dd) {
          rb.label && (dd.label = rb.label);
          rb.callback && (_.extend(dd, {callback: rb.callback, action: `plugin_${dd.action}`, isPluginButton: true}));
        }
      });

      // update layoutButtons
      attrs.layoutButtons = describeButtons;
    },

    /**
     * process disableFeatures
     */
    processDisableFeatures(attrs) {
      const disableFeatures = this.getPsrp('disableFeatures');
      if (!disableFeatures) {
        return;
      }

      attrs.disabledFeatureControls = {
        ...(attrs.disabledFeatureControls || {}),
        ...disableFeatures,
      };
    },

    /**
     * get [P]lugin [s]ervice [i]ns[t]ance
     */
    getPsit() {
      if (this.isCrossFiltersMode?.()) {
        return null;
      }

      return this.__pluginService;
    },

    /**
     * run [P]lugin [s]ervice [h]oo[k]
     */
    runPshk(name, options) {
      const me = this;
      const pluginService  = me.getPsit();

      if (!pluginService) return thenable;

      return pluginService.run(name, me.gpsrc(options));
    },

    /**
     * get [P]lugin [s]ervice [runtime] [p]aram
     */
    getPsrp(name) {
      const pluginService = this.getPsit();
      if (!pluginService || !name) {
        return;
      }
      return pluginService[name];
    },

    /**
     * set [P]lugin [s]ervice [runtime] [p]aram
     */
    setPsrp(name, value) {
      const pluginService = this.getPsit();
      if (!pluginService) {
        return;
      }

      pluginService[name] = value;
    },

    /**
     * clear [P]lugin [s]ervice [runtime] [p]aram
     */
    clearPsrp(name) {
      const me = this;
      const disposable = me.getPsrp(name);

      if (disposable) {
        try {
          if (disposable.dispose) {
            disposable.dispose();
          } else if (disposable.destroy) {
            disposable.destroy();
          }
        } catch (error) {
          console['error'](error);
        }
      }
    },

    /**
     * [g]enerate [p]lugin [s]erivce [c]ontext
     */
    gpsrc(options) {
      const me = this;
      const accessor = {
        getDescribe: () => {
          return me.get('objectDescribe');
        },
        getDescribeExt: () => {
          return me.get('objectDescribeExt');
        },
        findIndex: (name, value) => {
          const dataList = me.getCurData().data;
          if (!dataList) {
            return -1;
          }
          return dataList.findIndex((item) => item[name] === value);
        },
      };

      const updater = {
        check: (rowIds) => {
          me.table?.setCheckStatusByPos(
            rowIds.map((rowId) => accessor.findIndex('rowId', rowId))
          );
        },
        unCheck: (rowIds) => {
          me.table?.setCheckStatusByPos(
            rowIds.map((rowId) => accessor.findIndex('rowId', rowId)),
            true
          );
        },
        updateData: (rowId, data) => {
          const index = accessor.findIndex('rowId', rowId);
          me.setCellsValue({[index]: data});
        },
      };

      const service = {
        filter: (value) => {
          me.setPsrp('customFilterValue', value);
          me.table.setParam({}, true, true);
        },
        refresh() {
          me.refresh();
          me.refreshCurView();
        },
      };

      return {
        ...(options || {}),
        bizApi: service,
        dataGetter: accessor,
        dataUpdater: updater,
        filter: (value) => {
          service.filter(value);
        }
      };
    },

    /**
     * attachHooksToPlugin
     * 提供了使用插件执行hooks的能力
     * @returns
     * ```
     * 1.定义hooks
     * hooks = {
     *   beforeRender(params, context) {
     *     return {
     *       // 和插件list.render.before一致
     *     };   
     *   }
     * }
     * 
     * 2.hooks会被转成标准的Plugin插件
     * class SyntheticHooksPlugin {
     *   apply() {
     *     return [{
     *       'list.render.before': function(params, context) {
     *          return {
     *            // list.render.before钩子返回值
     *          };
     *        }
     *     }]
     *   }
     * }
     * ```
     * 3.注册SyntheticHooksPlugin
     * 4.走列表标准的插件执行逻辑
     */
    attachHooksToPlugin(hooks) {
      if (!hooks || _.isEmpty(hooks)) {
        return;
      }

      const psit = this.getPsit();
      if (!psit) {
        return;
      }

      try {
        const HooksPlugin = this.createHooksPlugin(hooks);
        psit.register(new HooksPlugin().apply(), {
          pluginApiName: "ListHooksPlugin",
          params: {}
        });
      } catch (error) {
        console['error'](error);
      }
    },

    preparePluginService() {
      const {hooks, pluginService} = this.options;

      // 如果已有插件服务，直接返回现有服务
      if (pluginService) {
        return pluginService;
      }

      // 前置条件检查：必须有hooks且Plug可用
      if (_.isEmpty(hooks) || !window.Plug) {
        return null;
      }

      try {
        // 创建新的插件服务实例
        const hooksPluginService = new window.Plug({
          appId: `hooks-${CRM.util.getUUIdAsMiniProgram()}`,
        });
        
        if (hooksPluginService) {
          this.set('pluginService', hooksPluginService);
          return hooksPluginService;
        }
      } catch (error) {
        console.error(error);
      }

      return null;
    },

    initPluginService() {
      const hooks = this.options.hooks;
      const hostService = this.preparePluginService();

      if (!hostService) return thenable;

      this.__pluginService = hostService;

      this.attachHooksToPlugin(hooks);

      return this.runRbpService();
    },

    disposePluginService() {
      this.clearPsrp('__batchtermSlot');
      this.runPshk('list.destroy.before', {
        apiName: this.get('apiname')
      });

      this.__pluginService = null;

      console.log('Dispose List PluginService');
    },
  };

  module.exports = PluginService;
});