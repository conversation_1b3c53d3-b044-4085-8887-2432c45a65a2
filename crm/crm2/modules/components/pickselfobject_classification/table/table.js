/*
 * @Descripttion:
 * @Author: LiAng
 * @Date: 2021-01-18 12:06:59
 * @LastEditors: chaoxin
 * @LastEditTime: 2025-04-17 15:00:12
 */
define(function (require, exports, module) {
	var Table = require('crm-modules/components/pickselfobject/pickselfobject').table;
	const shopcategory = require('crm-modules/components/pickselfobject_spusku/shopcategory');
    const {createPromotionActivityDetail, createPromotionActivityList} = require('./PromotionActivity');
    var createFloatCategory = Backbone.View.extend({
        options: {
            btns: [],
            parseBtns: void(0),
            categoryData: [],
            clickItemCb: $.noop,
            defaultCategoryId: '',
            defaultShowPanel: false,
            fixCategoryBtnVisibility: true, // '固定分类'是否显示
            fClassName: '',
            fBtnElement: '', // 可以替换全部分类按钮，
            fBtnName: ''
        },
        initialize: function(opt) {
            this.$el.css('overflow', 'visible')
            this.render();
        },
        events: {
            'mouseenter .category-float': 'showCategoryPanel',
            'mouseleave .category-float': 'showCategoryPanel',
            'click .fix-button-inner': 'fixButtonHandle'
        },
        template: _.template(`
                        <div class="category-float {{ fClassName }}">
                            ## if (fBtnElement) { ##
                                {{ fBtnElement }}
                            ## } else { ##
                                <div class="crm-btn category-float-tag">
                                    <span class="category-float-tag-icon"></span>
                                    <span>{{ (fBtnName=='' || fBtnName == void(0))? $t("全部分类"):fBtnName }}</span>
                                </div>
                            ## } ##
                            <div class="category-float-panel">
                                <div class="category-float-panel-inner"></div>
                                <div class="fix-button" style="display: {{ (fixCategoryBtnVisibility ? 'block':'none') }}">
                                    <div class="fix-button-inner">
                                        <span class="fix-button-icon"></span>
                                        <span>${$t("固定分类")}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `),
        render: function() {
            this.$el.html(this.template(this.options));
            this.togglePanel(!!this.options.defaultShowPanel);
            this.createFxIcon();
            this.loadCategory();
        },
        createFxIcon() {
            if (this.$('.category-float-tag-icon').length) {
                this.tagIconVm = CRM.util.createFxIcon({
                    el: this.$('.category-float-tag-icon').get(0),
                    className: 'fx-icon-list-2',
                    rClassName: 'category-float-tag-icon',
                    title: this.options.fBtnName || $t('全部分类'),
                    style: 'font-size:14px;vertical-align:middle;margin-right:5px;'
                }, { replaceWrapper: true }); // 替换挂载点
            }

            this.foldIconVm = CRM.util.createFxIcon({
                el: this.$('.fix-button-icon').get(0),
                className: 'el-icon-s-fold',
                rClassName: 'fix-button-icon',
                title: $t('固定分类'),
                style: 'font-size:14px;vertical-align:middle;margin-right:5px;'
            }, { replaceWrapper: true });
        },
        fixButtonHandle(e) {
            this.togglePanel(!!this.options.defaultShowPanel);
            this.$el.hide();
            this.trigger('hide');
        },
        togglePanel(flag) {
            this.$('.category-float-panel').toggle(flag);
        },
        showCategoryPanel(e) {
            let flag = e.type !== 'mouseleave';
            let target = this.$('.category-float-panel');

            if (flag) { // mouseenter
                target.toggle(true);
            } else {
                setTimeout(() => {
                    target.toggle(false);
                }, 300);
            }
            return false;
        },
        loadCategory() {
            let me = this;
            require.async('crm-modules/components/category_tree/category_tree', function (Category) {
                Category = Category.extend({
                    afterCategoryComponentRender(Cate) {
                        Cate.expandSomeNode(me.options.defaultCategoryId, true);
                    }
                });
                me._categoryTree = new Category({
                    $el: me.$('.category-float-panel-inner'),
                    btns: me.options.btns,
                    parseBtns: me.options.parseBtns,
                    categoryData: me.options.categoryData,
                    categoryModelType: '2',
                    showFoldIcon: false,
                    allowDraggable: false
                });

                me._categoryTree.on('sel.suc', function (data) {
                    me.options.clickItemCb(data);
                });
                // 隐藏panel, 侧滑上阻止了事件冒泡在mouseleave sidePanel时,需要隐藏panel
                me._categoryTree.on('hide:sidePanel', function (data) {
                    me.togglePanel(false);
                });
                me._categoryTree.on('toggle', () => {
                    me.trigger('toggle');
                });
            });
        },
        destroy: function() {
            this.tagIconVm?.destroy();
            this.foldIconVm?.destroy();
            this.undelegateEvents();
            this.off();
			this.remove();
        }
    });

    const CLOSE_OLD_CATEGORY = CRM._cache.close_old_category;
    // 促销活动按钮
	module.exports = Table.extend({

		refreshTable(){
			this.table.setParam({}, true, true);
		},

        render() {
            Table.prototype.render.apply(this, arguments);
            this.isInitCategory = false;
        },

        async beforeRequestFHH(opt) {
            await this._renderOptimizedCategory();
            return opt;
        },

        /**
         * 分类有默认值或者有属性时，需要走分类优化逻辑
         * 未优化，table与分类同时渲染
         * 优化后，先渲染分类后渲染table
         */
        shouldOptimizeRenderCategoryLogic() {
            return this.options.hasDefaultCategory || this._isAttrTable();
        },

        async _renderOptimizedCategory() {
            try {
                // 首次请求初始化侧边分类、分类属性组件
                if (!this.isInitCategory && this.shouldOptimizeRenderCategoryLogic()) {
                    await this._renderCustomCategory();
                    await this._initAttributeFilter();
                    this.isInitCategory = true;
                }
            } catch (error) {
                console.error(error);
            }
        },

        _renderCategory() {
            if (!this.shouldOptimizeRenderCategoryLogic()) {
                this._renderCustomCategory();
            }
        },
		/**
		 * @desc 特殊逻辑需要增加产品分类
		 */
		async _renderCustomCategory() {
			this._startTime_category = new Date().getTime();
			if(this.options.renderCategoryExtend){
				this.options.renderCategoryExtend({
					$el: this.$el.parent().find('.category-tree-box'),
					api: {
						refreshTable: this.refreshTable.bind(this)
					}
				});
				this._renderCopyCheckbox();
				return
			}
            if (this.get('forceHideCategory')) {
				this._renderCopyCheckbox();
				return;
            }
            this.beforeRenderCategory();
			var me = this;
            /**
            * options.openFloatCategory: Boolean | Object, 见getOpenFloatCategoryValue方法
            * options.categoryRequestExtra : Object
            */
            var openFloatCategory = this.getOpenFloatCategoryValue();
            var categoryRequestExtra = this.get('categoryRequestExtra');

            // 提前隐藏左侧
            if (openFloatCategory.floatFirst) {
               me.floatCategoryCollapseLeft(true);
            }

            await me.getAllCategory(me.getCategoryRequestExtra(categoryRequestExtra))
            if (!openFloatCategory.floatFirst) {
                await me._createCommonCategory();
            } else {
                me._createFloatCategory(openFloatCategory.floatCategoryEl);
            }

            // 产品分类筛选
            this._initCategoryCascader(me.__initCategoryCasId || me._realCategoryId || '');

            me._renderCopyCheckbox();
		},

        /**
         * 产品分类接口入参
         * @param {object} data 
         * @returns 
         */
        getCategoryRequestExtra(data) {
            if (_.isObject(data)) {
                return data;
            }

            // 产品分类支持按照可售范围过滤
            if (CRM.util.isGrayScale('CRM_Category_Request_Extra')) {
                const {master_data = {}} = this.options || this.get('pluginContext') || {};
                return {
                    object_data: master_data
                }
            }
        },

        // 添加隐藏左侧分类的class
        floatCategoryCollapseLeft(flag, noTrigger) {
            let handle = flag ? 'addClass' : 'removeClass';
			if(!this.$el) return;
            (this.$el.parent())[handle]('pickself-box-collapse');
            (this.$el.siblings(':not(".t-wrap, .promotion-activity-list__wrap")'))[handle]('floatcategory-need-collapse');

            !noTrigger && this.trigger('toggle.category');
        },

        /**
         * 促销活动按钮点击回调
         * 1.区域显示隐藏
         * 2.请求参数 object_data 修改
         * @returns {boolean} endflag 是否结束执行
         */
        execClickPromotionActivityItem() {
            if (!this.promotionActivityList) return false;
            const object_data = this.get('object_data');
            const captionWrapper = this.getCaptionWrapper();
            const isPromotionAction = (action) => action === 'CUSTOM_ACTION_PROMOTION_ACTIVITY';
            // 促销活动列表与普通列表之间切换，兼容滚动条，需要手动 resize 下
            if ([this._beforeBtnAction, this._btnAction].filter(isPromotionAction).length === 1) {
                this.trigger('toggle.promotion')
            }
            if (isPromotionAction(this._btnAction)) {
                // 添加筛选参数
                object_data.tab_price_policy_id = this._pricePolicyId;
                if (this._pricePolicyId) {
                    this.$el.show();
                    this.promotionActivityList$el.hide();
                    this.promotionActivityDetail.toggleShowStatus(true);
                    this.resize();
                } else {
                    this.$el.hide();
                    this.promotionActivityList$el.show();
                    this.promotionActivityDetail.toggleShowStatus(false);
                    captionWrapper.css({
                        display: 'none',
                        height: 0
                    });
                }
            } else {
                // 移除筛选参数
                delete object_data.tab_price_policy_id;
                this.$el.show();
                this.promotionActivityList$el.hide();
                this.promotionActivityDetail.toggleShowStatus(false);
                this.resize();
                captionWrapper.css({
                    display: captionWrapper.hasClass('dt-caption__flex') ? 'flex' : 'block',
                    height: '56px'
                });
            }

            // 进入促销活动列表，不进行筛选
            return isPromotionAction(this._btnAction) && !this._pricePolicyId;
        },

        isDingQiaoEA() {
            try {
                var ea = CRM.ea;
                var isCross = window.PAAS ? window.PAAS.app.isCross() : false;
                if(isCross) {
                    ea = window.PAAS.app.getInfo()?.upstreamEa || ea;
                }
                return ['90438', '782969_sandbox', 'tdtech123'].includes(ea);
            } catch {
                return false;
            }
        },

        async clickCategoryItemCb(data) {
            this._beforeBtnAction = this._btnAction || '';
            this._btnType = data.ButtonType || '';
            this._btnAction = data.ButtonAction || '';
            this._cateGoryId = data.CategoryCode || '';
            this._realCategoryId = data.CategoryID || '';
            this._pricePolicyId = data.PolicyId || ''
            // 促销活动
            const endflag = this.execClickPromotionActivityItem();
            if (endflag) return;
            this.setValueToObjectData(this._btnAction);
            let notGetTableData = await this.handleOption(this._btnAction);

            if (this._isAttrTable()) {
                let conditions = await this.categoryAttribute.setCategory(data.CategoryID, data);
                this._attrConditions = conditions;
            }

            // TODO 鼎桥分类需求影响了标准逻辑(订货通下游分类筛选异常)，这里先给鼎桥单独逻辑判断，待鼎桥问题修复后，去除逻辑
            if (this.isDingQiaoEA()) {
                notGetTableData ? this.table.doStaticData([]) : this.table.start();
            } else {
                notGetTableData ? this.table.doStaticData([]) : this.table.setParam({}, true, true);
            }
            this.refreshCurView?.({
                from: 'categoryChange',
                fromArgs: _.extend({}, data)
            });

            if (data.ButtonType == 'customBtn') {
                this.categoryCascader && this.categoryCascader.destroy();
                this.categoryCascader = null;
            } else if (this.categoryCascader) {
                this.categoryCascader.update(data.CategoryID);
            } else {
                this._initCategoryCascader(data.CategoryID);
            }
        },

        getAllCategory(data) {
            const me = this;
            return new Promise((resolve) => {
                // 自定义获取接口数据 hook
                if (me.options.getCategoryData) {
                    Promise.resolve(me.options.getCategoryData()).then((categoryData) => {
                        me.categoryData = categoryData;
                        resolve();
                    });
                    return;
                }

                if (me.categoryData) {
                    resolve();
                    return;
                }

				this._startTime_category_server = new Date().getTime();

				CRM.util.getCategoryDataList(data).then((list) => {
					CRM.util.sendLog('crm', 'pickself', {
						eventId: 'crm_getCategory_server',
						operationId: 'crm_getCategory_server',
						eventData:{
							time: new Date().getTime() - me._startTime_category_server
						}
					});

                    me.categoryData = me.__parseCategoryData(list);
                    if (me.options.parseCategoryData) {
                        me.categoryData = me.options.parseCategoryData(me.categoryData);
                    }
                    resolve();
                })
            });
		},
		// 对好丽友企业特殊处理 add by hgl
		__parseCategoryData(categoryData){
			// 去除水产品
			let newCatoryData = categoryData;
			let isHly = this.get('isHly');
			let hlyFilterWater = this.get('hlyFilterWater');
			let hlyWaterCategory = this.get('hlyWaterCategory');
			if(isHly){
				if(hlyFilterWater){
					newCatoryData = categoryData.filter(v => v.code == hlyWaterCategory);
					let _v = (newCatoryData[0] && newCatoryData[0]);
					this._cateGoryId = (_v && _v.category_code) || '';
					this._realCategoryId = this.__initCategoryCasId = (_v && _v._id) || '';
					this._showAllCatoryBtn = false;
				}else {
					newCatoryData = categoryData.filter(v => v.code != hlyWaterCategory);
				}
			}
			return newCatoryData;
		},

        // 获取悬浮分类需要的默认参数
        getOpenFloatCategoryValue: function() {
            let openFloatCategory = this.get('openFloatCategory') || {};
            if (_.isBoolean(openFloatCategory)) {
                openFloatCategory = {
                    showFoldIcon: openFloatCategory
                }
            }
            return _.extend({
                showFoldIcon: false,// 左侧分类显示折叠图标(点击开启悬浮分类)默认显示
                floatFirst: false, // 优先显示浮动分类
                floatCategoryEl: '', // 悬浮分类挂载点selector | jQuery
                fixCategoryBtnVisibility: true, // 悬浮分类中'固定分类'默认显示，隐藏无法切换到左侧分类
                defaultShowPanel: false, // 悬浮分类下拉panel默认隐藏 | 显示
                fClassName: '', // 悬浮分类className,
                fBtnElement: '', // 悬浮分类panel附着元素的html,默认“全部分类”按钮
                fBtnName: '', //  悬浮分类按钮名称
            }, openFloatCategory);
        },

        // 分类筛选
        _initCategoryCascader(id = '') {
            var me = this;
            // 内部嵌套一层是为了兼容"包含本单已选产品"按钮，产品分类隐藏后还需要空的容器占位
            $('.dt-out-filter', this.$el).after('<div class="category-cascader-filter"><div class="category-cas-wrapper-outer"></div></div>');

            require.async('crm-modules/components/category_cascader/category_cascader', function(CategoryCascader) {
                let options = {
                    $el: me.$('.category-cas-wrapper-outer'),
                    defaultSelected: id,
                    catgoryData: me.categoryData,
                    showAllBtn: me._showAllCatoryBtn !== undefined ? me._showAllCatoryBtn : true
                };
                if (me.options.parseCategoryCascaderInitOptions) {
                    options = me.options.parseCategoryCascaderInitOptions(options);
                }
                me.categoryCascader = new CategoryCascader(options);

                me.categoryCascader.on('update:catcas', function(id) {
                    me._cateGory && me._cateGory.trigger('update:catcas', id);
                    me._floatCategory?._categoryTree?.trigger('update:catcas', id);
                });
            });
        },

        // 创建左侧分类
        _createCommonCategory() {
			var me = this, isHly = me.get('isHly');
            var openFloatCategory = this.getOpenFloatCategoryValue();

            return new Promise((resolve) => {
                require.async('crm-modules/components/category_tree/category_tree', function (Category) {
                    Category = Category.extend({
                        // 设置默认值，不需要走isHly逻辑
                        afterCategoryComponentRender(Cate) {
                            Cate.expandSomeNode(me._realCategoryId, true);
                        }
                    });
                    if(!me.$el) return;
                    let options = _.extend({
                        $el: me.$el.parent().find('.category-tree-box'),
                        btns: me.parseBtns(),
                        parseBtns: _.bind(me.parseCategoryBtns, me),
                        categoryData: me.categoryData,
                        categoryModelType: me.get('categoryModelType'),
                        showFoldIcon: openFloatCategory.showFoldIcon
                    }, isHly ? {
                        defaultV: me._cateGoryId ? {
                            Name: '',
                            CategoryCode: me._cateGoryId,
                            CategoryID: ''
                        } : null,
                        showAllBtn: me._showAllCatoryBtn !== undefined ? me._showAllCatoryBtn : true
                    } : {});
                    if (me.options.parseCategoryTreeInitOptions) {
                        options = me.options.parseCategoryTreeInitOptions(options);
                    }
                    if (options.defaultValue) {
                        const defaultCategory = options.defaultValue;
                        me._cateGoryId = defaultCategory.code;
                        me._realCategoryId = defaultCategory._id;
                        me._categoryData = defaultCategory;
                    }
                    me._cateGory = new Category(options);
                    me._cateGory.on('sel.suc', async function (data) {
                        if (me.options.onCategoryClickItemCb) {
                            await Promise.resolve(me.options.onCategoryClickItemCb(data));
                        }
                        await me.clickCategoryItemCb(data);
                        // 同步数据
                        me._floatCategory?._categoryTree?._cateGory?.expandSomeNode((data.CategoryID || ''), true);
                    });
                    me._cateGory.on('fold', function() {
                        // 隐藏左侧, 展示表头分类
                        me.floatCategoryCollapseLeft(true);
                        // 展示表头分类
                        if (!me._floatCategory) {
                            me._createFloatCategory(openFloatCategory.floatCategoryEl);
                        } else {
                            me._floatCategory.$el.show();
                        }
                    });
                    me._cateGory.on('toggle', () => {
                        me.trigger('toggle.category');
                    });

                    CRM.util.sendLog('crm', 'pickself', {
                        eventId: 'crm_category_tree',
                        operationId: 'crm_category_tree',
                        eventData:{
                            time: new Date().getTime() - me._startTime_category
                        }
                    });
                    resolve();
                });
            })

        },

        // 创建悬浮分类
        _createFloatCategory(el) {
            const me = this;
            let openFloatCategory = me.getOpenFloatCategoryValue();

            if (el) {
                el = (el instanceof window.jQuery) ? el.get(0) : el;
            } else {
                el = $('<div class="category-float-wrap"></div>').insertAfter(this.$('.dt-tit')).get(0);
            }
            me._floatCategory = new createFloatCategory({
                el,
				btns: me.parseBtns(),
				parseBtns: _.bind(me.parseCategoryBtns, me),
                categoryData: me.categoryData,
                defaultCategoryId: me._realCategoryId,
                defaultShowPanel: openFloatCategory.defaultShowPanel,
                fixCategoryBtnVisibility: openFloatCategory.fixCategoryBtnVisibility,
                fClassName: openFloatCategory.fClassName,
                fBtnElement: openFloatCategory.fBtnElement,
                fBtnName: openFloatCategory.fBtnName,
                clickItemCb: async function(data) {
                    if (me.options.onCategoryClickItemCb) {
                        await Promise.resolve(me.options.onCategoryClickItemCb(data));
                    }
                    await me.clickCategoryItemCb(data);
                    me._cateGory?._cateGory?.expandSomeNode((data.CategoryID || ''), true);
                }
            });
            me._floatCategory.on('hide', function() {
                // 展示左侧分类
                me.floatCategoryCollapseLeft(false);
                if (!me._cateGory) {
                    me._createCommonCategory();
                }
            })

            me._floatCategory.on('toogle', () => {
                me.trigger('toggle.category');
            });

			CRM.util.sendLog('crm', 'pickself', {
				eventId: 'crm_floatCategory',
				operationId: 'crm_floatCategory',
				eventData:{
					time: new Date().getTime() - me._startTime_category
				}
			});
        },

        // 创建促销活动
        beforeRenderCategory() {
            if (!CRM._cache.advancedPricing) return;

            // table 表头下追加元素，促销活动详情
            this.$('.dt-caption', this.$el).after('<div class="promotion-activity-detail__wrap"></div>');
            const {master_data = {}} = this.options || this.get('pluginContext') || {};
            const {account_id, object_describe_api_name} = master_data;
            this.promotionActivityList$el = this.$el.siblings('.promotion-activity-list__wrap');
            this.promotionActivityList = createPromotionActivityList({
                wrapper:  this.promotionActivityList$el[0],
                params: {
                    masterObjectApiName: object_describe_api_name,
                    accountId: account_id,
                }
            })
            this.promotionActivityList$el.hide();
            this.promotionActivityList.$on('openDetail', (data) => {
                this.promotionActivityDetail?.updatePolicyDetail(data);
                this.clickCategoryItemCb({
                    ButtonAction: 'CUSTOM_ACTION_PROMOTION_ACTIVITY',
                    PolicyId: data.id,
                })
            })
            this.promotionActivityDetail = createPromotionActivityDetail({
                wrapper: this.$el.find('.promotion-activity-detail__wrap')[0],
                data: {
                    showBackButton: true,
                }
            })
            this.promotionActivityDetail.toggleShowStatus(false);
            this.promotionActivityDetail.$on('back', () => {
                this.clickCategoryItemCb({
                    ButtonAction: 'CUSTOM_ACTION_PROMOTION_ACTIVITY'
                })
            })
        },

        // 处理产品分类自定义按钮
        parseCategoryBtns(btns = []) {
            let parseCategoryBtns = this.get('parseCategoryBtns');

            if (_.isFunction(parseCategoryBtns)) {
                btns = parseCategoryBtns(btns);
            } else if (_.isArray(parseCategoryBtns)) {
                btns = parseCategoryBtns;
            }

            return btns;
        },

		_isAttrTable(){
			let tableType=this.options&&this.options.tableType;
			return CRM._cache.openAttribute&&tableType=="AttrTable"
		},

		parseOptionsBySelf:function (opts) {
			var apiname = this.get('apiname');
			// 开了可售范围不能新建产品。因为要走范围过滤；
			if(apiname === 'ProductObj' && CRM._cache.openAvailablerange) opts.operate.btns = [];
			// console.log(this.options.master_data.object_describe_api_name);
		},

        /*
        * 初始化属性筛选组件
        */
        _initAttributeFilter() {
            const me = this;
            //属性价目表，根据分类 -> 属性值 筛选
			if(!this._isAttrTable()) return;
            $('.dt-caption',this.$el).after('<div class="attribute-filter" style="padding:10px 16px; background:#fff"></div>');
            $('.attribute-filter').append('<div></div>');
            return new Promise((resolve, reject) => {

                require.async('vcrm/sdk',function(sdk){
                    sdk.getComponent('categoryAttrSetting').then(async (Comp) => {
                        let vm = new Vue({
                            el: $('.attribute-filter').children()[0],
                            render: (h) => h(Comp.default, {
                                props: {}
                            }),
                        })
                        me.categoryAttribute = vm.$children[0];
                        let conditions = await me.categoryAttribute.setCategory(me._realCategoryId, me._categoryData);
                        me._attrConditions = conditions;
                        me.categoryAttribute.$on('change', (conditions) => {
                            me._attrConditions = conditions;
                            me.table.setParam({}, true, true);
                        })
                        me.categoryAttribute.$on('attribute.render.after', me.attributeRenderAfter.bind(me))
                        resolve();
                    })
                });
            })
        },
		attributeRenderAfter(){
			this.table.updateLoadingPosition();
		},

		getFilterAttrValue(arr, attrObj) {
			const id = attrObj.attributeValue,
				type = attrObj.type;
			switch (type) {
				case "1":
					arr = [id];
					break;
				case "2":
					arr.push(id);
					break;
				case "3":
					arr = arr.filter(a => a !== id);
			}
			return arr;
		},

        getColumns: function () {
			var me = this;
			var columns = Table.prototype.getColumns.apply(me, arguments);
			var category = _.findWhere(columns, {
				data: CLOSE_OLD_CATEGORY ? 'product_category_id' : 'category',
			});
			if (category) {
				category.isFilter = false; // 前端逻辑写死
			}
			return columns;
		},

		parseParamCustom(rq, action){
			let isHly = this.get('isHly');
			let hlyFilterWater = this.get('hlyFilterWater');
			let hlyWaterCategory = this.get('hlyWaterCategory');
            let search_query_info = JSON.parse(rq.search_query_info);
			if(isHly && !hlyFilterWater){
				search_query_info.filters.push({
					field_name: 'category',
					field_values: [hlyWaterCategory],
					operator: 'N'
				});
			}

            // 收藏
            if (action === 'collection') {
                let collectionFilter = this.get('collectionFilter');
				collectionFilter && search_query_info.filters.push(collectionFilter);
            }

            rq.search_query_info = JSON.stringify(search_query_info);

			let ret = Table.prototype.parseParamCustom.call(this, rq, action);

			return ret;
		},

        changeOptions:function (opts) {
            var item = _.findWhere(opts.filterColumns, {
				data: CLOSE_OLD_CATEGORY ? 'product_category_id' : 'category',
			});
			if (item) {
				item.isFilter = item.is_active = false; // 隐藏分类字段筛选
			}
		},

		parseBtns() {
			let btns = this.isPromotion(this.get('apiname')) ? [
				{
					label: $t('促销'),
					action: 'promotion'
				}
			] : [];

			if(this.options.source_api_name === "SalesOrderProductObj" || /__c/.test(this.options.source_api_name)){
				let ob = [];

                // 选价目表明细，没有最近订购、促销产品
                // 基于商品选产品，规格请求价目表明细，没有最近订购、促销产品
                if(
                    this.options.apiname === 'ProductObj' ||
                    (this.options.apiname === 'SPUObj' && (!CRM._cache.openPriceList || CRM._cache.priceBookPriority))) 
                {
                    if(CRM._cache.advancedPricing) ob.push({
                        label: $t('促销产品'),
                        action: 'promotionProduct'
                    });

                    if (CRM._cache.advancedPricing) ob.push({
                        label: $t('crm.salesorderobj.pickobj_promotion_activity.name', null, '促销活动'),
                        action: 'CUSTOM_ACTION_PROMOTION_ACTIVITY'
                    });
    
                    if(CRM._cache.recent_order) ob.push({
                        label: $t('最近订购'),
                        action: 'recentOrder'
                    });
                }

                // 订货通下的产品/商品特殊逻辑
                if (['ProductObj', 'SPUObj'].includes(this.options.apiname) && window.Fx && Fx.IS_DHT_CONNECTAPP) {
                    ob.push({
                        label: $t('收藏'),
					    action: 'collection'
                    });
                }

				btns = btns.concat(ob);
			}
			return btns;
		},

		// 添加订购、促销条件；
		setValueToObjectData(action){
			let object_data = this.get('object_data');
			object_data.tab_recently_ordered = object_data.tab_promotion = false;
			if(action === 'recentOrder'){
			 	object_data.tab_recently_ordered = true;
			 	// if(rq.master_data) rq.master_data.tab_recently_ordered = true;
			}else if(action === 'promotionProduct'){
				object_data.tab_promotion = true;
				// if(rq.master_data) rq.master_data.tab_promotion = true;
			}
		},

        // 设置table的option
        async handleOption(action) {
            // 收藏
            if (action === 'collection') {
                let data = await this.getCollectList();

                this.table && this.set('collectionFilter', {
					field_name: '_id',
					field_values: data,
					operator: 'IN'
				});

                return !data.length;
            }
        },

        getCollectList() {
            return new Promise((resolve, reject) => {
                require.async('vcrm/sdk', (Sdk) => {
                    let params = {
                        subType: this.options.apiname,
                        pageNumber: 1,
                        pageSize: 500
                    };

                    Sdk.widgetService.getService('collectionService').then(({collectionService}) => {
                        collectionService.getCollectionIds(params, true).then((ids) => {
                            resolve(Object.keys(ids));
                        });
                    });
                })
            })
        },

        destroy: function() {
            this.categoryAttribute?.$destroy();
            this.categoryAttribute = null;
            this._cateGory && this._cateGory.destroy();
            this._floatCategory?.destroy();
            this.promotionActivityTable?.destroy?.();
            Table.prototype.destroy.apply(this, arguments);
        },

		...shopcategory.tableMethods,
	})
})
