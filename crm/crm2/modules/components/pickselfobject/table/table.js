define(function (require, exports, module) {
	var Table = require('crm-modules/components/objecttable/objecttable');
	var Addor = require('../add/add');
	var util = require('crm-modules/common/util');

	var disabledFillPreRemberData = FS.util.getUserAttribute('paasListRetainRemberDataDisabled');

	var AccountHyphen = '#%_'; //按照客户地址选择客户，提供的连接符hack

	var __getValue = function(data, flag){
		var me = this;
		var fields = me.get('fields');
		var objectDescribe = me.get('objectDescribe');
		var showAccountByAddr = me.get('showAccountByAddr');
		data = data || [];
		if (data.length && flag) {
			var list = this.table.getCheckedFormatData(data);
			var temp = {};

			_.each(list, function (a) {
				var to = {};
				_.each(a, function (b, k) {
					if(to.hasOwnProperty(k + '__tpd') || k.includes('__tpd')) return;
					if (me.isObjectReference(k)) {
						to[k + '__tpd'] = a[k + '__r'];
					} else {
						to[k + '__tpd'] = b;
					}
				})
				temp[a._id] = to;
			})
			_.each(data, function (a) {
				if(showAccountByAddr){ // 如果按照客户地址显示客户，那么_id要特殊hack下
					a._id = a._id.split(AccountHyphen)[0]
				}
				a._fields = fields;
				a._is_open_display_name = (objectDescribe && objectDescribe.is_open_display_name) || false;
				_.extend(a, temp[a._id]);
			})
		}

		return this.get('isMultiple') ? data[0] && data : data[0];
	}

	module.exports = Table.extend({

		events: {
			'click .j-add': '_addHandle',
			'mouseenter .j-show-policy':'_showPricePolicy',
			'mouseleave .j-show-policy':'_hidePricePolicy',
		},

		// 统一的埋点方法
		loggerMark: function(pointName) {
			this.trigger('logger:mark', pointName);
		},

		options: {
			noOperate: true,
			totalNumLimit: 1000,
			listType: 'selected',
      pricebookChecked: CRM._cache.priceBookSelectProduct,
			hideCategory: false,
		},

		instantiatePluginService(apiname) {
			if (!apiname) return Promise.resolve();

			return new Promise(resolve => {
				try {
						const customPlugins = CRM.util.fetchLayoutPlugins(apiname, {
							type: 'selected_list_plugin',
						});

						CRM.api.pluginService.initByInterface({
							pluginOpts: {
								appId: `CRM-${CRM.util.getUUIdAsMiniProgram()}`,
								api: {
                	hostAgent: 'pickSelfObject',
								}
							},
							formatPluginList(pluginList) {
								return [].concat(pluginList).concat([
									{
										"pluginApiName": 'PWCPlugin',
										"resource": function(){
											return window.PAAS.plugin?.libs.get('PWCPlugin');
										},
										"params": {
											_host: 'selectedList',
										}
									}
								]);
							},
							describe: {},
							apiName: apiname,
							actionCode: 'SelectedList',
							agentType: 'web',
							noErrorMessage: true
					}).then((pluginService) => {
						this.__pluginService = pluginService;
						if(pluginService) {
							customPlugins.then(plugins => {
								pluginService.run('pwc.init.before', {
									plugins
								}).then(resolve, resolve);
							});
						}else {
							resolve();
						}
					}).catch(resolve);
				} catch(e) {
					resolve();
				}
			});
		},

		render: function() {
			// 记录表格初始化开始时间
			this.loggerMark('table_init_start');

			this.instantiatePluginService(this.get('apiname')).then(() => {
				this.set('pluginService', this.__pluginService);
				this.super.render.call(this);

				// 记录表格初始化结束时间
				this.loggerMark('table_init_end');
			});
		},

		//
		// 换新的表格
		//
		_renderTable: function () {
			var me = this;
			this.cloneFilterIds = _.extend([], this.options.filterIds);
			this._totalNumLimit = this.options.totalNumLimit;
			this.getTable(function (Table) {
				me.table && me.table.destroy();
				var options = me.getOptions();
				//大对象不支持模糊搜索，故屏蔽
				if(me.get('isBigObject')) {
					options.search = null;
				}
				me.fillLastRemberData(options);
				me.changeOptions(options);
				me.super.pluginOptionsFormatter?.call(me, options);
				me.options.formatTableOption && me.options.formatTableOption(options);
				me.table = new Table(_.extend({
					className: 'crm-table crm-table-noborder crm-table-open pickself_table',
					isMyObject: true,  //默认都是自定义对象
					noAllowedWrap: true
				}, options));
				me.bindTableEvents(me.table);
				me.copyDtEvents(me.table);
				me._proxyTableFn(me.table);
				me.tableComplete();
				me.trigger('complete', me);
			});
		},

		changeOptions:$.noop,

		//
		// 场景放到头部
		//
		getTerm: function () {
			var term = _.clone(this.get('term'));
			if (term) {
				term.pos = 'C';
			}
			return term;
		},

		_hackName(name) {
			if(name && !name.render) {
				let apiname = this.get('apiname');
				name.render = function(data, type, full, helper, index) {
					if(data === null || data === '' || data === void 0) return '--';
					let id = full.__crmAccountAddrId || full._id;
					return `<a href="javascript:;" class="j-show-lookup" data-id="${id}" data-apiname="${apiname}">${data}</a>${CRM.util.getDetailTabUrl(apiname, id)}`
				}
			}
		},

		getColumns: function () {
			var me = this;
			var columns = _.clone(this.get('columns')) || [];
			var name = _.findWhere(columns, {data: 'name'});

			me._hackName(name);//主属性可点击跳转详情


			if (name && CRM.util.isCrmPreObject(this.get('apiname'))) {
				name.fixed = true;
				name.fixedIndex = 1;
			}

			if (this.get('apiname') === 'AccountObj') {   // 客户的所属公海特殊处理
				var highSeaPool = _.findWhere(columns, {data: 'high_seas_id'});
				if (highSeaPool) {
					highSeaPool.showLookupText = true;
				}
			}

			columns.push({
                id: 'operate_column',
                data: null,
                width: 110,
                title: $t("操作"),
                lastFixed: true,
                render: function (data, type, full, helper, index) {
                    let showAccountByAddr = me.get('showAccountByAddr');
                    let id = full._id;
                    if(showAccountByAddr){
                        id = id.split(AccountHyphen)[0]
                    }
                    let btn = '';
                    if (!me.get('notShowDetail')) {
                        btn += '<a data-id="' + id + '" tb-action-type="showdetail" href="javascript:;" class="j-detail" >' + $t("查看") + '</a>';
                    }
                    if (me.options.otherBtns) {
                        return btn + me.options.otherBtns(full, index);
                    }
                    return btn;
                }
            });

			columns = this.formatColumns(columns, this.get('apiname'));
			// columns = this.addPriceBookPriceColumn(columns);
			return columns;
		},

		formatColumns(columns, apiname) {
			// 选产品显示促销icon&价格政策促销品显示促销icon
			if (this.isPromotion(apiname) || this.isPricePolicy(apiname)) {
				_.each(columns, item => {
					if (item.api_name === 'name') {
						item.render = function(data, type, full, helper, index) {
							if ((full.promotion && full.promotion.length) || full.have_promotion || full.hasPricePolicy) {
								return `
									<span class="crm_promotion_icon circle j-show-policy">${$t('促')}</span>
									<span>${data}</span>
								`;
							}
							return data;
						}
					}
				})
			}
			// 产品分类字段展示全路径
			if (apiname === 'ProductCategoryObj') {
				columns.forEach((column) => {
					if (column.api_name === 'name') {
						column.render = function(data, type, full, helper, index, item) {
							return `<span class="main755-name">${full?.product_category_name_path || data}</span>`;
						};
					}
				})
			}

			if (this.options.formatColumns) {
				columns = this.options.formatColumns(columns, apiname);
			}

			return columns;
		},

		shouldFillLastRememberedData() {
			if (this.get('showAccountByAddr')) {
				return false;
			}

			return !disabledFillPreRemberData && !!this.get('isMultiple');
		},

		// 列表重新渲染时，带入之前选中的数据
		fillLastRemberData(options) {
			if (!this.shouldFillLastRememberedData() || !options || options.single) {
				return;
			}
			
			const preRemberData = this.getRemberData();
			if (!_.isEmpty(preRemberData)) {
				options.checked = this.formatChecked(preRemberData);
			}
		},

		// 开了强制优先级，从产品添加时，添加一列价目表价格；
		// addPriceBookPriceColumn(columns) {
		// 	if (!CRM.util.isGrayScale('CRM_VIRTUAL_EXTENSION') && CRM.util.isGrayScale('CRM_SHOW_PRICEBOOK_PRICE') && CRM._cache.priceBookPriority && this.get('target_related_list_name') === 'salesorderproduct_product_list' && this.get('apiname') === 'ProductObj') {
		// 		let index = util.findIndex(columns, item => item.api_name === 'price');
		// 		if (!util.hasValue(index)) index = util.findIndex(columns, item => item.api_name === 'name');
		// 		if (util.hasValue(index)) {
		// 			columns.splice(index + 1, 0, {
		// 				data: 'prick_book_price',
		// 				title: $t('价目表价格'),
		// 				render: function (data, type, full) {
		// 					if (full.extend_info && full.extend_info.realPriceInfo) {
		// 						return full.extend_info.realPriceInfo.pricebook_price;
		// 					}
		// 					return '';
		// 				}
		// 			})
		// 		}
		// 	}
		// 	return columns;
		// },

		isPromotion(apiname) {
			let apiName = ['ProductObj', 'SPUObj', 'PriceBookProductObj'];

			return CRM._cache.promotionStatusByRecordType && _.contains(apiName, apiname);
		},

		isPricePolicy(apiname) {
			let apiName = ['ProductObj', 'SPUObj', 'PriceBookProductObj'];

			return CRM._cache.advancedPricing && _.contains(apiName, apiname);
		},

		setParam: function () {
			this.table?.setParam({}, true);
		},

		//获取不支持过滤的字段
		//filters条件传入的和wheres传入的都不支持过滤
		getNoAllowFilterFields: function () {
			var wheres = this.get('wheres');

			var filters = this.get('filters');
			var _noAllowFilterFields = {};
			_.each(filters, function (a) {
				_noAllowFilterFields[a.field_name] = 1;
			})
			_.each(wheres, function (a) {
				_.each(a.filters, function (b) {
					_noAllowFilterFields[a.field_name] = 1;
				})
			})
			return _.isEmpty(_noAllowFilterFields) ? false : _noAllowFilterFields;
		},

		// 支持设置多选
		formatChecked: function (checked) {
			var showAccountByAddr = this.get('showAccountByAddr');
			var data = [];
			if (_.isArray(checked)) {
				data = checked;
			} else {
				checked && data.push({_id: checked});
			}
			if(showAccountByAddr){
				// 特殊处理下address
				data = data.map(v => {
					return _.extend(v, {
						_id: v._id + AccountHyphen + v.address
					});
				});
				console.log(_.pluck(data, '_id'));
			}
			return {
				idKey: '_id',
				data: data,
				maxNum: this.get('customMaxNum')
			}
		},

		getOptions: function () {
			var me = this;
			var options = Table.prototype.getOptions.apply(this, arguments);
			var apiname = me.get('apiname');
			var showAccountByAddr = me.get('showAccountByAddr');
			var _noAllowFilterFields = me.getNoAllowFilterFields();
			_noAllowFilterFields && _.each(options.columns, function (a) {
				a.isFilter = a.isFilter && !_noAllowFilterFields[a.data];
			})

			// hack：新建按钮控制
			var isHidden = (_.contains([
				'PriceBookObj',
				'WarehouseObj',
				'StockObj',
				'PurchaseOrderProductObj'
			], apiname) || me.options.hideAdd) ? true : !me.hasRight('Add');
			_.extend(options, {
				termBatchPos: 'C',
				url: me.options.url ? me.options.url : (options.listUrl || (showAccountByAddr ? '/EM1ANCRM/API/v1/object/account_addr/service/find_account_addr_list' : '/EM1HNCRM/API/v1/object/' + apiname + '/controller/RelatedList')),
				title: this.get('displayName'),
				scrollLoad: true,
				search: this.get('search') || {
					showAllField: this.get('supportFullFieldSearch'),
					showFilterField: true,
					placeHolder: '',
					type: 'Keyword'
				},
				checked: me.formatChecked(me.get('dataId')),
				disabledcfg: me.get('disabledcfg') || null,
				operate: {
					btns: [{
						className: 'b-g-btn-small j-add',
						text: $t("新建"),
						isHidden: isHidden,
						isFold: false
					}]
				},
				openStart: this.options.openStart,
				single: !me.get('isMultiple'),
				separateCheckCount: me.shouldFillLastRememberedData(),
				arrowResetSingle: true,
				lineCheck: !me.get('nosupportLineCheck'),
				showSize: true,
				showFilerBtn: !me.options.__yqslCustom,
				showMoreBtn: true,
				showPage: true, //价目表 仓库不需要分页
				allColumns: [],
				showMask: true,
				showWaterMask: true,
				cacheSearchField: true,
				showTagBtn: this.get('supportTag'),
				showOutFilter: true,
				height: 'auto',
				isListLayout: true,
				visiblePageNums: me.getVisiblePageNums(),
				initComplete: function () {
					// 记录表格数据加载完成时间
					me.loggerMark('table_data_end');

					me.trigger('render');
					me.completeHandle();
					// 包含本价目表已选产品
					if (me.isShowPriceBookCheckbox()) {
							me.set('pricebookChecked', CRM._cache.priceBookSelectProduct);
							me._renderPriceCheckbox();
					}
					me.trigger('initComplete');

					// 记录表格插件初始化完成时间
					me.loggerMark('table_plugin_end');
				},
				showMultiple: _.isUndefined(me.options.showMultiple) ? options.showMultiple : me.options.showMultiple,
				otherBtns: (this.options.formatOtherBtns ? this.options.formatOtherBtns(options.otherBtns) : options.otherBtns) || [],
				formatDataAsync: this.options.formatDataAsync,
				beforeRequestFHH: me.beforeRequestFHH.bind(me),
			}, showAccountByAddr ? {
				pageType: 'pageTime',
			} : {})
			if (!_.isUndefined(me.get('zIndex'))) {
				options.zIndex = me.get('zIndex');
			}
			if (options.searchTerm && apiname === 'ProductObj') {
				_.extend(options.searchTerm, {
					//订单选产品，未开启价目表时也显示高级筛选
					showManage: false
				});
			}
			// 选价目表，屏蔽场景；默认全部场景；
			if(!CRM.util.isGrayScale('CRM_SELECT_DATA_SHOW_SCENE')){
				if (apiname === 'PriceBookObj') {
					options.searchTerm = null
				}
			}
			me.parseOptionsBySelf(options);

			me.__reqUrl = options.url;

			return options;
		},

		getVisiblePageNums: function() {
			return 5;
		},

		async beforeRequestFHH() {},
		parseOptionsBySelf: $.noop,

		parseParam: function (param) {
			var me = this;
			me.initFilterIds();
			var apiname = me.get('apiname');
			var wheres = this.get('wheres');
			var filters = this.get('filters');
			var selfFilters = this.get('selfFilters');
			var filterIds = this.get('filterIds');
			var obj = Table.prototype.parseParam.call(this, param);
			var questParam = me.get('questParam');
			var extraData = me.get('extraData');
			var aoo = JSON.parse(obj.search_query_info);
			var filterId = me.getFilterId();
			me._limit = aoo.limit * 1;

			function addFilter(target = [], filters = []) {
				filters.forEach(filter => {
					let exist = false;

					target.some(item => {
						if (item.field_name === filter.field_name) {
							item.field_values = filter.field_values;
							return exist = true;
						}
					})

					if (!exist) {
						target.push(filter);
					}
				})

				return target;
			}

			//全字段搜索
			let all_field = _.findWhere(aoo.filters, {field_name: '#$%all_fields#$%'});
			if(all_field) {
				all_field.field_name = 'plain_content';
				all_field.operator = 'MATCH';
				all_field.value_type = 22;
			}

			if (filters) {
				// aoo.filters = aoo.filters.concat(filters);
				addFilter(aoo.filters, filters);
			}
			if (selfFilters) {
				// aoo.filters = aoo.filters.concat(selfFilters);
				addFilter(aoo.filters, selfFilters);
			}
			if (filterIds && filterIds.length && filterIds[0]) {
				addFilter(aoo.filters, [{
					field_name: filterId,
					field_values: filterIds,
					operator: 'NIN'
				}]);
			}

			// 处理分类筛选条件
			const parseCategoryFilter = () => {
				let result = [];

				if (me._cateGoryId) {
					// 灰度企业使用分类id筛选，如果id不存在回退到用code
					let objs =  ['ProductObj', 'SPUObj'];
					const CLOSE_OLD_CATEGORY = me._realCategoryId && CRM._cache.close_old_category && _.contains(objs, apiname);

					// 商城分类
					const isShopCategory = me.get('categoryRequestExtra')?.filterCategory === 'Shop';
					if (isShopCategory) {
						result = [{
							// 价目表明细 shop_category 商品产品 shop_category_id
							field_name: _.contains(objs, apiname) ? 'shop_category_id' : 'shop_category',
							field_values: [me._realCategoryId],
							operator: 'HASANYOF'
						}]
					} else {
						result = [{
							field_name: CLOSE_OLD_CATEGORY ? 'product_category_id_search' : 'category',
							field_values: CLOSE_OLD_CATEGORY ? [me._realCategoryId] : [me._cateGoryId],
							operator: 'EQ'
						}];
					}
				}

				// 插件扩展，自定义分类字段筛选产品
				if (this.options?.parseRelatedCategoryFilterParam) {
					try {
						result = this.options.parseRelatedCategoryFilterParam(result, {
							categoryCode: me._cateGoryId,
							categoryId: me._realCategoryId,
							allCategoryData: me.categoryData || []
						});
					} catch (error) {
						console.error(error);
					}
				}

				if (result && result.length) {
					addFilter(aoo.filters, result);
				}
			}
			parseCategoryFilter();

			// 万马特殊需求 搜索时传递开始于检索
			if (FS.util.getUserAttribute('wanmaadd')) {
				var mainSearch = _.findWhere(aoo.filters, {field_name: 'name'});

				mainSearch = !mainSearch ? _.findWhere(aoo.filters, {field_name: 'product_id'}) : mainSearch;

				if (mainSearch) {
					mainSearch.operator = 'STARTWITH';
				}
			}
			if (wheres && wheres.length) {
				aoo.wheres = wheres;
			}

			obj.search_query_info = JSON.stringify(aoo);

			//
			// 产品和价目表 不要场景
			//
			if(!CRM.util.isGrayScale('CRM_SELECT_DATA_SHOW_SCENE')){
				if (apiname === 'PriceBookProductObj') {
					obj.search_template_id = '';
				}
			}

			if (questParam) {
				questParam.search_query_info = obj.search_query_info;
				questParam.search_template_id = obj.search_template_id;
				questParam.ignore_scene_record_type = obj.ignore_scene_record_type;
				questParam.include_describe = false;
				questParam.search_template_type = obj.search_template_type;
				questParam.trigger_info = this.get('trigger_info');
				return this.options.beforeRequest ? this.options.beforeRequest(questParam) : questParam;
			}

			var rq = {
				associated_object_describe_api_name: apiname, //"ContactObj",
				associated_object_field_related_list_name: me.get('relatedname'), //"account_contact_list",
				search_query_info: obj.search_query_info,
				search_template_id: obj.search_template_id,
				search_template_type: obj.search_template_type,
				object_data: this.get('object_data'),
				ignore_scene_record_type: obj.ignore_scene_record_type,
				trigger_info: this.get('trigger_info'),
				include_describe: false,
                extraData
			}

			rq = this.parseParamCustom(rq, this._btnAction);

			if (!_.isUndefined(obj.ignore_scene_filter)) { // 自定义对象忽略条件
				rq.ignore_scene_filter = obj.ignore_scene_filter;
			}

			if (this.options.beforeRequest) {
				rq = this.options.beforeRequest(rq);
			}

			rq = this.parseParamFinally(rq, this._btnAction);

			return rq;
		},

		parseParamFinally:function(rq){ return rq; },

		parseParamCustom: function(rq, action) {
			// 促销
			if (this.isPromotion(this.get('apiname'))) {
				rq.object_data && (rq.object_data.is_promotion_list = action === 'promotion');
			}
			var showAccountByAddr = this.get('showAccountByAddr');
			if(showAccountByAddr){
				rq.searchQueryInfo = rq.search_query_info;
				rq.searchTemplateId = rq.search_template_id;
				rq.includeLayout = true;
			}
			return rq;
		},

		getFilterId:function(){
			return '_id'
		},

		// 加唯一标记，支持选重复数据用
		addIdKey(list){
			if(!list) return;
			list.forEach(item => {
				item._idKey = item._id;
			})
		},

		parseData: function (data) {
			var ret = this.__parseData(data);
			this.options.formatData && this.options.formatData(data);
			var obj = Table.prototype.parseData.call(this, ret);
			obj.totalCount = this._tatal = this._getTotal(obj.totalCount);
			if(data && data.relatedObjectDataSpecified) {
				this.relatedObjectDataSpecified = data.relatedObjectDataSpecified;
			}
			this.addIdKey(data.dataList);
			return obj;
		},
		__parseData: function (data) {
			var me = this;
			var showAccountByAddr = me.get('showAccountByAddr');
			if(!showAccountByAddr){
				return data;
			}
			data.dataList = data.dataList.map((v) => {
				return _.extend({}, v.master, {
					address: v.address,
					location: v.location,
          			location_id: v._id,
					__crmAccountAddrId: v.master._id,
					_id: v.master._id + AccountHyphen + v.address
				});
			});
			// console.log(_.pluck(data.dataList, '_id'))
			return data;
		},

		isObjectReference: function (apiName) {
			var data = _.findWhere(this.table.options.columns, {
				api_name: apiName
			}) || {};

			return data.type === 'object_reference';
		},
		__getValue: __getValue,

		getValue: function (flag) {
			var me = this;
			var tableData = this.table.getCurData();

			if (!this.shouldFillLastRememberedData() && (!tableData || !tableData.length)) {
				return;
			}

			var data = flag ? this.getRemberData() : this.getCheckedData();

			return __getValue.call(this, data, flag);
		},

		_addHandle: function () {
			var me = this;
			var relationData;
			var questParam = this.get('questParam');
			if (questParam) {
				relationData = {
					id: questParam.associate_object_data_id,
					target_api_name: questParam.associate_object_describe_api_name,
					value: questParam.name
				}
				if (questParam.associated_object_field_related_list_name) {
					relationData.target_related_list_name = questParam.associated_object_field_related_list_name;
				}
			}

			var apiName = me.get('apiname');
			var targetObject = this.getTarget(apiName);
			var objdata = me._getAddData();
			var formFillData = me.get('formFillData');

			var tdata = formFillData || objdata;
			if(!tdata.record_type && me.relatedObjectDataSpecified) {
				tdata.record_type = me.relatedObjectDataSpecified.record_type;
			}

			var sss = CRM.util.getUserAttribute('crmLookupAddEditable');

			if (!targetObject) {
                let lookupAddFieldReadonly = CRM.util.isGrayScale('LOOKUP_ADD_FIELD_READONLY');
				var path = FS.crmUtil.getCrmFilePath(apiName, 'action');
				path && require.async(path, function (Myobject) {
					if (!me.selfObject) {
						me.selfObject = new Myobject();
						me.selfObject.on('refresh', function (action, data) {
							// 销售订单action传的是新建的数据
							if (action === 'add') {
								me.trigger('add', data);
								if (!me.options) return;
								if (me.options.beforeAddSuccessRefresh) {
									me.options.beforeAddSuccessRefresh(data._id, function () {
										me.refresh();
									})
								} else {
									me.refresh();
								}
							} else if (apiName === 'SalesOrderObj' && _.isObject(action)) {
								me.trigger('add', action);
								if (!me.options) return;
								if (me.options.beforeAddSuccessRefresh) {
									me.options.beforeAddSuccessRefresh(data._id, function () {
										me.refresh();
									})
								} else {
									me.refresh();
								}
							} else {
								me.refresh();
							}
						})
					}
					me.selfObject.add(_.extend({
						show_type: 'full',
						nonEditable: sss ? lookupAddFieldReadonly && lookupAddFieldReadonly.includes(apiName) ? false : true : false,
						apiname: apiName,
						title: $t("新建") + me.get('displayName'),
						relationData: formFillData ? null : relationData,
						data: tdata,
						isSubmitAndCreate: false //选数据列表屏蔽保存并新建按钮，原因是bpm业务无法在此功能实现闭环，产品建议先屏蔽。
					}, me.get('addOption')));
				})
			} else {
				if (!this.addor) {
					this.addor = new Addor({
						nonEditable: !!sss,
						relationData: formFillData ? null : relationData,
						data: tdata
					});
					this.listenTo(this.addor, 'success', function (obj) {
						if (me.options.beforeAddSuccessRefresh) {
							me.options.beforeAddSuccessRefresh(obj._id || obj.DataID, function () {
								me.refresh();
							})
						} else {
							me.refresh();
						}
					});
				}
				this.addor['add_' + targetObject]();
			}
		},

		_getAddData: function () {
			// var objdata = _.extend({}, this.options.object_data); 绝对不能带入全量数据，会污染新建对象的整体数据。可以根据自己的特殊业务追加。
			var data = this.options && this.options.object_data || {};
			var objdata = {};
			if (data.account_id && !/__c$/.test(this.get('apiname'))) { //默认带入客户覆盖90%场景(仅限预置对象 自定义对象不带)
				objdata.account_id = data.account_id;
				objdata.account_id__r = data.account_id__r;
			}
			var temp = {};
			_.each(data, function (v, k) {
				var vv = data[k + '__r'];
				if (vv) {
					temp['$' + k + '$'] = [v, vv];
				}
			})
			var wheres = this.get('wheres');
			_.each(wheres, function (a) {
				_.each(a.filters, function (b) {
					var arr = temp[b.field_values[0]];
					arr && b.operator === 'EQ' && (objdata[b.field_name] = arr[0], objdata[b.field_name + '__r'] = arr[1])
				})
			})
			return objdata;
		},

		getTarget: function (targetApiName) {
			var obj = FS.crmUtil.isCrmPreObject(targetApiName);
			return obj && obj.objectType;
		},

		completeHandle: function () {
			var apiname = this.get('apiname');

			// 价目表 仓库不需要分页
			//
			if (apiname === 'PriceBookObj' || apiname === 'WarehouseObj') {
				this.$('.dt-page .page-box').hide();
				this.$('.dt-page .dt-size').hide();
			}
			//订单产品隐藏所有的筛选。骆晶晶 要求
			// if (apiname === 'SalesOrderProductObj') {
			// 	this.$('.batch-term').hide();
			// 	return;
			// }

			// 产品 价目表产品显示分类
			const hideCategory = this.get('hideCategory');
			const showCategoryObj = ['ProductObj', 'SPUObj', 'PriceBookProductObj', 'StockObj', 'ServiceFaultCategoryObj', 'ServiceProjectObj'];
			if (!hideCategory && showCategoryObj.includes(apiname)) {
				this._renderCategory();
			}
			if(CRM.util.isGrayScale('CRM_SELECT_DATA_SHOW_SCENE')){
				// 这些对象不显示场景
				if ( apiname === 'StockObj' || apiname === 'ServiceFaultCategoryObj' || apiname === 'ServiceProjectObj') {
					$('.dt-tit', this.$el).removeClass('line');
					$('.term-item', this.$el).hide();
				}else{
					this._renderCopyCheckbox();
				}
			}else{
				if (apiname === 'ProductObj' || apiname === 'PriceBookProductObj' || apiname === 'StockObj' || apiname === 'ServiceFaultCategoryObj' || apiname === 'ServiceProjectObj') {
					$('.dt-tit', this.$el).removeClass('line');
					$('.term-item', this.$el).hide();
				}else{
					this._renderCopyCheckbox();
				}
			}

			this.renderCacheView();
		},

		parseTerm: function () {
			var apiname = this.get('apiname');
			var term = Table.prototype.parseTerm.apply(this, arguments);
			// 选产品，因为不显示和切换场景，所以默认场景设为全部
			if(!CRM.util.isGrayScale('CRM_SELECT_DATA_SHOW_SCENE')){
				if (apiname === 'ProductObj') {
					_.each(term.options, function (item) {
						item.isdef = item.apiname && item.apiname.indexOf('All') != -1; // 默认为全部场景
					});
				}
			}
			return term;
		},

		// 是否勾选包含本单已选产品
		isCheckedIncludeSelected(){
			return CRM._cache.isIncludeSelectedForMd !== '' ? CRM._cache.isIncludeSelectedForMd : CRM._cache.tenant_whether_filter_order_select_product;
		},
        // 是否显示包含本价目表已选产品复选框；
        isShowPriceBookCheckbox() {
            return this.options.showPriceBookSelectProduct;
        },
		initFilterIds:function(){
			if(this.getIsSupportCopy()){
				this.options.filterIds = this.isCheckedIncludeSelected() ? [] : this.cloneFilterIds;
			}
            if(this.isShowPriceBookCheckbox()){
				this.options.filterIds = this.get('pricebookChecked') ? [] : this.cloneFilterIds;
			}
		},

		// 是否显示包含重复产品复选框；
		// 条件：只有这三个从对象支持 and 添加产品或者价目表明细 and 没开属性价目表 and 没开促销
		getIsSupportCopy:function(){
			let supportCopyApiName = ["QuoteLinesObj", "SalesOrderProductObj", "NewOpportunityLinesObj", 'SaleContractLineObj'];
			let r = this.options.isSupportCopy && supportCopyApiName.includes(this.options.source_api_name);
			if(!r) return;
			let b = (this.options.btnInfo || !CRM._cache.openPriceList) && ["price_book_product_id", "product_id", "sale_contract_line_id"].includes(this.options.fieldName || this.options.btnInfo && this.options.btnInfo.fieldname);
			return b && !CRM._cache.openAttribute && !(CRM._cache.promotionStatusByRecordType && CRM._cache.promotionStatus);
		},

		// 渲染允许添加重复产品复选框；
		_renderCopyCheckbox:function(){
			let _this = this;
			if(_this.getIsSupportCopy()){
				let defValue = CRM._cache.tenant_whether_filter_order_select_product;
				CRM.util.getIncludeSelected({
					key:'whether_filter_order_select_product'
				}).then(function (res) {
					if(_this.$el.find('.pickself-copy-checkbox').length) return;
					let categoryDom = _this.$el.find('.category-cascader-filter');
					if(categoryDom.length){
						categoryDom.append('<div class="pickself-copy-checkbox pickself-copy-checkbox-absolute"></div>')
					}else{
						_this.$el.find('.dt-caption').after('<div class="pickself-copy-checkbox pickself-copy-checkbox-right"></div>');
					}
					FxUI.create({
						wrapper: '.pickself-copy-checkbox',
						template: `<fx-checkbox 
									v-model="checked"
									@change="change"
								>${$t('包含本单已选产品')}</fx-checkbox>`,
						data() {
							return {
								checked: res.value ? res.value == '1' : defValue == '1',
							}
						},
						methods: {
							change:function (val) {
								CRM.util.setIncludeSelected({
									key:'whether_filter_order_select_product',
									value:val ? '1' : '0'
								}).then(function (res) {
									_this.refresh();
									_this.trigger('includeSelectedChange', val, _this);
								});
							}
						}
					})
				});
			}
		},
        // 【包含本价目表已选产品】复选框；
        _renderPriceCheckbox: async function() {
            let _this = this;
            if(_this.$el.find('.pickself-include-checkbox').length) return;
            _this.$el.find('.dt-term-batch').find('.dt-control-btns').before('<div class="pickself-include-checkbox pickself-include-checkbox-right"></div>');
            FxUI.create({
                wrapper: '.pickself-include-checkbox',
                template: `<fx-checkbox 
                            v-model="checked"
                            @change="change"
                            disabled
                        >${$t('crm.PriceBookObj.priceBookSelectProduct')}</fx-checkbox>`,
                data() {
                    return {
                        checked: _this.get('pricebookChecked')
                    }
                },
                methods: {
                    change(val) {
                        _this.set('pricebookChecked', val);
                        _this.refresh();
                    }
                }
            });
        },

		/**
		 * @desc 特殊逻辑需要增加产品分类
		 */
		_renderCategory: function () {
			var me = this;

			// 记录分类渲染开始时间
			this.loggerMark('category_render_start');

            var defaultCategory = this.get('defaultCategory');
            var filters = this.get('filters');

			$('.last-target-item', me.$el).after('<div class="item"><span class="line"></span><span class="item-tit">' + $t("分类") + '</span><div class="item-con  category-wrap"></div></div>');

			require.async('crm-modules/components/category/category', function (Category) {

				if (me.get('apiname') === 'ServiceProjectObj' ) {
					var MyModel = Category.Model.extend({
						getAllCategory: function (callback) {
							var me = this;
							if (this.get('ProductCategoryList')) {
								callback && callback();
							}
							util.FHHApi({
								url: '/EM1HNCRM/API/v1/object/service_project_category/service/list',
								success: function (res) {
									if (res.Result.StatusCode == 0) {
										me.set('ProductCategoryList', res.Value.result);
										callback && callback();
									}
								}
							})
						},
					})

					Category = Category.extend({
						setModel: function(obj) {
							this.model = new MyModel(obj);
							this.model.set('categoryList', []);
							//先将全部分类保存为面包屑第一项
							this.addCategoryList(this.model.get('category'));
						},
					})
				}

				me._cateGory = new Category({
					hasBtn: true,
					target: $('.category-wrap', me.$el),
					zIndex: 10000
				});
                if (defaultCategory != void(0)) {
                    me._cateGory.setValue(defaultCategory);
                }
				me._cateGory.on('sel.suc', function (data) {
					me.categoryChangeHook(data);
					me._cateGoryData = data;
					me._cateGoryId = data.CategoryCode || '';
                    if (!me._cateGoryId && me.get('clearCategoryInFilters')) {
                        let fl =  _.filter(filters, filter => filter.field_name !== 'category' && filter.field_name !== 'product_category_id_search');
                        me.set('filters', fl);
                    }
					me.table?.setParam({}, true, true)
				});
				me._renderCopyCheckbox();

				// 记录分类渲染结束时间
				me.loggerMark('category_render_end');
			});
		},

		categoryChangeHook(){

		},

		renderListCompleteHandle: function(opts) {
            this.trigger('datarendercomplete', opts);
			Table.prototype.renderListCompleteHandle.apply(this, arguments);
            let checkedData = this.getCheckedData() || [],
				arr = checkedData && checkedData.filter(d => d.attribute);
            this.options.renderListCompleteHandle_after && this.options.renderListCompleteHandle_after(arr);
		},

		showAboutNum: function() {
			return true;
		},

		getAboutNumUrl: function() {
			return this.__reqUrl || '/EM1HNCRM/API/v1/object/'+ this.get('apiname') +'/controller/RelatedList';
		},

		refresh: function() {
            if (!this.table) return;
            !this.options.noClear && this.table.clearRemberData();
            this.table?.setParam({}, true);
        },

		otherbtnChangeHandle: function ($target) {
			this.options.otherbtnChangeHandle && this.options.otherbtnChangeHandle($target.data('type'), {
				setParam: this.table.setParam.bind(this.table),
				getParam: this.__getQueryParam.bind(this)
			});
		},

		renderCacheView: function() {
			var otherBtns = this.table.options.otherBtns;

			if (otherBtns && otherBtns.length) {
				var btn = _.findWhere(otherBtns, {isDefault: true});
				var $div = $('<div></div>');

				_.each((btn.attrs || '').split(' '), item => {
					let [key, value] = item.split('=');

					key && value && $div.attr(key, value.replace(/"/g, ''));
				})

				this.otherbtnChangeHandle($div);
			}
		},

		// 业务类型变化
		recordTypeChangeChangeHandle: function () {
			this.queryParamChangeHandle();
		},

		//请求参数变化 刷新阶段 地图 分屏视图的数据
		queryParamChangeHandle: function (...args) {
			this.refreshCurView({
				from: 'queryParamChange',
				fromArgs: args
			});
		},

		// 切换分页
		pageNumberChangeHandle: function (...args) {
			this.refreshCurView({
				from: 'pageNumberChange',
				fromArgs: args
			});
		},

		// 切换每页条数
		pageSizeChangeHandle: function (...args) {
			this.refreshCurView({
				from: 'pageSizeChange',
				fromArgs: args
			});
		},

		// 行高改变
		pageLineHeightChangeHandle: function (...args) {
			this.refreshCurView({
				from: 'pageLineHeightChange',
				fromArgs: args
			});
		},

		refreshCurView: function ({from, fromArgs}) {
			this.options.refreshCurView && this.options.refreshCurView({
				setParam: this.table.setParam.bind(this.table),
				getParam: this.__getQueryParam.bind(this),
				from,
				fromArgs
			});
		},
        searchChangeHandle: function(keyword, value) {
            this.options.searchChangeHandle && this.options.searchChangeHandle.apply(this, arguments);
        },
		// 行点击
		trclickHandle: function (rowData, $tr, $target, $trs) {
			this.options.trclickHandle && this.options.trclickHandle.apply(this, arguments);
		},
        cellChangeHandle: function(value, field, type, options) {
            this.options.cellChangeHandle && this.options.cellChangeHandle.apply(this, arguments)
        },
		//hover促显示价格政策信息
		_showPricePolicy:async function(e){
			e.stopPropagation&&e.stopPropagation();
			let $item=$(e.target),
				listApiName=this.options&&this.options.apiname,
				masterData=this.options&&this.options.master_data,
				domInfo={
					$el:this.$el,
					$item:$item,
					zIndex:FxUI.Utils.getPopupZIndex()*1+100
				};
			this.isMouseOnElement = true;
			if(CRM._cache.advancedPricing&&masterData){
				let dataId=$item.parent().attr('data-id');
				//从价目表明细添加，data-id是价目表明细id，要重新获取产品id
				if(listApiName == 'PriceBookProductObj'){
					const tableData=this.table.getCurData(),
						targetItem = tableData.find(d=>d._id==dataId);
						dataId=targetItem?.product_id||dataId;
				}
				const data=await CRM.util.showPolicyInfo({
					"masterApiName":masterData.object_describe_api_name,
					"accountId":masterData.account_id,
					"dataId":dataId,
					"limit":1
				},listApiName,domInfo,true);
				if(this.isMouseOnElement){
					CRM.util.showPolicyInfoDom(data.policyInfo, domInfo);
				}
			}
		},

		_hidePricePolicy:function(e){
			this.isMouseOnElement = false;
			CRM.util.hidePolicyInfo(this.$el);
		},
		

		destroy: function () {
			this.selfObject && this.selfObject.destroy();
			this.selfObject = null;
			try {
				this._cateGory && (this._cateGory.destroy(), this._cateGory = null);
			} catch(e) {}
			this.super.destroy.apply(this, arguments);
		}
	})
})


