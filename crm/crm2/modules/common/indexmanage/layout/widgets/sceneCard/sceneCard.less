
.crm-sceneCard-dialog{
	.crm-sceneCard-box{
		.crm-sceneCard-icon{
			vertical-align: text-top;
			color: #ff8989;
		}
		.crm-sceneCard-name{
			color: #212b36;
			font-size: 12px;
		}
	}
	.crm-sceneCard-objSelect,.crm-sceneCard-sceneSelect{
		//width:416px;
		margin-top:8px;

	}
	.crm-sceneCard-first{
		margin-bottom:16px;
	}
}

.crm-sceneCard-main {
	box-sizing: border-box;
	width: 100%;
	overflow: auto;
	min-height: 130px;
	background: rgba(64, 127, 255, 0.05);
	border-radius: 2px;
	
	&.margin_bottom_16{
		margin-bottom:16px;
	}
	.hide{
		display: none;
	}

	//.dt-page {
	//	display:none!important;
	//}

	.dt-term-batch{
		display:none;
	}

	.tb-b, .j-tb-header{
		border-right: 1px solid #e5e9f2;
	}


	.addSceneBtn{
		position: relative;
		display:block;
		width:96px;
		height:40px;
		border-radius:4px;
		background-color:#407fff;
		line-height:40px;
		text-align:center;
		font-size:16px;
		color:var(--color-neutrals01);
		cursor:pointer;
		margin: 40px auto 0;
		z-index: 10;
	}
	.crm-w-table .dt-page{
		background-color:var(--color-neutrals01);
	}

}



