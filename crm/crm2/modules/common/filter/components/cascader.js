define(function(require, exports, module) {
	var Common = require('./common');

    module.exports = Common.extend({
        render() {
            var me = this;
            var _props = me.options.props;
            me._widgets.cascader = FxUI.create({
                wrapper: $('.input-box', me.$el)[0],
                template: `<fx-cascader
                            v-model="value"
                            :options="options"
                            :props="props"
                            :zIndex="zIndex"
                            :disabled="disabled"
                            filterable
                            :placeholder="$t('请选择')"
                            clearable
                            popper-class="filter-cascader-popper"
                            @change="changeHandle"
                            >
                            <template slot="extra" slot-scope="{nodes}">
                                <div class="el-cascader-extra" v-if="nodes[0].data.hasExtra"><a href="javascript:;" @click="onClick($event, nodes)">{{extra.text || $t("查看全部")}}</a></div>
                            </template>
                            </fx-cascader>`,
                data() {
                    return {
                        props: _.extend({
                            expandTrigger: 'hover',
                            // lazy: true,
                            // lazyLoad(node, resolve) {
                            //     const { level } = node;
                            //     let id = 0;
                            //     if (level != 2) {
                            //         // resolve([]);
                            //         return;
                            //     }
                            //     setTimeout(() => {
                            //     const nodes = Array.from({ length: level + 1 })
                            //         .map(item => ({
                            //         value: ++id,
                            //         label: `选项${id}`,
                            //         leaf: level >= 2
                            //         }));
                            //         // 通过调用resolve将子节点数据返回，通知组件数据加载完成
                            //         resolve(nodes);
                            //     }, 1000);
                            // },
                        }, _props),
                        value: [],
                        valuePath: [],
                        options: me.options.options,
                        extra: me.options.extra,
                        zIndex: Math.max(FxUI.Utils.getPopupZIndex(), CRM.util.getzIndex()) + 2,
                        disabled: false,
                    }
                },
                methods: {
                    updateOptions(path, item, options) {
                        let op = options;
                        
                        _.each(path, (v, index) => {
                            op = getList(op, v);
                            if (index == path.length - 1) {
                                op && op.unshift(item);
                            }
                        })
                        function getList(op, v) {
                            filterPlacer(op);
                            let lv = op;
                            let _lv = _.findWhere(op, {value: v});
                            // 如果不存在children同时能找到节点_lv,则返回undefined，即无需往里添加节点
                            _lv && (lv = _lv.children);  
                            return lv;
                        }

                        // 过滤掉占位的option
                        function filterPlacer(op) {
                            let pIdx = -1;
                            _.each(op, (o, index) => {
                                o.isPlaceholders && (pIdx = index);
                            })
                            pIdx > -1 && op.splice(pIdx, 1);
                        }
                    },
                    changeValue(val) {
                        this.value = val;
                        this.changeHandle(val);
                    },
                    changeHandle(val) {
                        this.valuePath = val;
                        _.isArray(val) && me.trigger('change', this.getItem(this.options, val));
                    },
                    getItem(list, val) {
                        let lv = list;
                        _.each(val, function(v, index){
                            let _lv = _.findWhere(lv, {value: v});
                            
                            if (val.length - 1 == index) return lv = _lv;
                            if (_lv && _lv.children){
                                lv = _lv.children;
                                return;
                            }
                            lv = _lv;
                        })

                        return lv || {};
                    },
                    onClick(e, nodes) {
                        this.extra && this.extra.onClick && this.extra.onClick(e, nodes, this);
                    },
               }

            })
        },
        getValue() {
            return this._widgets.cascader.value;
        },
        setValue(val) {
            this._widgets.cascader.changeValue(val);
        },
        setDisable(status) {
            this._widgets.cascader.disabled = status;
        }
    })
});