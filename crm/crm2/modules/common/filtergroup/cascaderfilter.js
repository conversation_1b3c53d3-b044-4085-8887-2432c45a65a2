define(function(require, exports, module) {
    var fieldFilter = require('./fieldfilter'),
        helper = require('crm-modules/common/filtergroup/helper'),
		util = require('crm-modules/common/util');
    
    return fieldFilter.extend({
        options: {
            $wrapper: '',
			title: $t("默认"),
			level: 3,
			max: 0, //最大限制
			maxErrTip: $t("达到最大量"), //超过最大量的文字提示
            width: 800, //宽度
            objects: [ {
				api_name: '',
				label: '',
				fields: {},
				getDataUrl: '',
				postData: '',
				parseData: null, //function(){},
				filterType: [],
				filterApiname: [],
			}],
			showObjectsCascader: false, // 如 objects 数据仅为一组时，也展示完整的级联关系
			props: null,  //cascade使用
			extra: null,  //cascade使用
			// oOptions: null,
			optionType: 'cascader',
			origin: null,
			isInitFilter: true,
			isSupportCascadingCheck: true, //支持级联单选校验
			filterType: [
				'formula',
				'group',
				'image',
				'file_attachment',
				'master_detail',
				'auto_number',
				'signature',
				'quote',
				'embedded_object_list',
				'multi_level_select_one',
				'tree_path',
				'employee_many',
				'department_many',
				'html_rich_text',
				'object_reference_many'
			], //过滤的字段类型
			filterApiname: [], //过滤的字段apiname
			isRelate: false,
			disabled: false, // 是否禁用
            uncheckFirstLevel: true, 
			defaultValue: null, //默认值
			parseCompare: function (compare, field) {  // 处理比较符的函数
				return compare;
			},
			parseFields: function (fields) {   // 处理字段
				return fields
			},
			// formatGetItem(){ },
			helper: helper,
			getData: null,
			setData: null,
		},

		initialize() {
			this.objects = {};
			fieldFilter.prototype.initialize.apply(this, arguments);
		},

		render(objects, cb) {
			var me = this;
			objects = objects || me.options.objects;
			var len = objects.length;
			var count = 0; 
			_.each(objects, function(object) {
				me.fetchFields(object).then((fields) => {
					object.fields = me.parseFields(fields, object);
					object.allowFields = me._filterFields(fields, object);
					me.objects[object.api_name] = object.fields;
				}).finally(() => {
					count ++;
					if (count === len) {
						if (cb) {
							cb();
						} else {
							me._setOptions(function() {
								me.initFilter();
							});
						}
					}
				})
			})
		},

		parseProps(props) {
			var me = this;
			props = _.extend({}, props);
			if (props.lazy && !props.lazyLoad) {
				const lazyLoad = function(options) {
					return function(node, resolve) {
						const { level, data, path } = node;
						if (data.leaf) {
							resolve([]);
							return;
						}
						if (data.type === 'object_reference') {
							let opts = me.getSelectedFields(path, options.options);
							let _opf = _.findWhere(opts, {type: 'object_reference'}) || {};
							// let	_field = _opf.ofield ? _opf.ofield : _opf;
							let _field = me.getOField(_opf);

							if (_field) {
								CRM.util.getFieldsByDescribe(_field.target_api_name).then((res) => {						
									let _fields = res[_field.target_api_name];//CRM.util.deepClone(CRM.get('fields.' + _field.target_api_name));
									_field.target_fields = _fields;
									_fields = me.parseFields(_fields);
									let _allowFields = me._filterFields(_fields, {
										filterType: ['object_reference'],
                                        filterApiname: ['owner_department'] // server底层不支持使用lookup对象的【负责人所在主属部门】字段进行过滤
									}); 
						
									let nodes = me._getFieldOptions(_allowFields);
									_opf.children = nodes;
									_field.target_fieldslist = nodes;
									me.objects[_field.target_api_name] = _fields;
									//对组装后的nodes可以再自定义处理
									me.options.parseNodes&&me.options.parseNodes(nodes);
									resolve(nodes);
								})
							}
							return;
						}
						resolve([]);
					}
				}
				props.lazyLoad = lazyLoad(me.options);
			}
			this.trigger('change.props', props);
			return props;
		},

		parseFields(fields, object = {}) {
			var me = this;
			var _fields;
			_fields = object.parseData ? object.parseData(fields) : fields;
			_fields = me.options.parseFields(me.options.helper.formatFields(_fields));
			return _fields
		},

		fetchFields(object) {
			var me = this;
			return new Promise((resolve, reject) => {
				if (object.fields && !_.isEmpty(object.fields)) {
					resolve(object.fields);
					return;
				}
				if (object.getDataUrl) {
					CRM.util.FHHApi({
						url: object.getDataUrl,
						data: object.postData,
						success(res) {
							if (res.Result.StatusCode == 0) {
								resolve(res.Value);
								return;
							}
							reject(res.Result);
						},
						error(err) {
							reject(err);
						},
					}, {
						errorAlertModel: 1
					})
					return;
				}
				//获取类型为列表
				if(object.hasExtra){
					resolve(object.fields);
					return;
				}
				util.getFieldsByDescribe(object.api_name).then((res) => {
					var fields = res[object.api_name];//util.deepClone(CRM.get('fields.' + object.api_name));
					resolve(fields);
				})
			})
		},

		_filterFields: function (list, object = {}) {
			// 默认过滤的字段类型
			var defaultFilterFieldType = [
				'rich_text', // 协同富文本
			];
			var fields = [],
				opts = this.options,
				mustFilter = this.options.helper.getFieldMustFilter(),
				groupField = this._getGroupFields(object.fields);
			const whiteList = opts?.fieldWhiteList||[];
			_.each(list, function (field) {
				// 白名单优先级最高
				if(whiteList.includes(field.api_name)){
					fields.push(field);
					return;
				}

				// 默认过滤的字段类型
				if (_.contains(defaultFilterFieldType, field.type)) {
					return;
				}
				//过滤组字段
				if (groupField[field.api_name]) {
					return;
				}
				//过滤类型
				var filterType = object.filterType ? _.compact(_.uniq(opts.filterType.concat(object.filterType))) : opts.filterType;
				if (_.contains(filterType, field.type)) {
					return;
				}
				//过滤apiname
				var filterApiname = object.filterApiname ? _.compact(_.uniq(opts.filterApiname.concat(object.filterApiname))) : opts.filterApiname;
				if (_.contains(filterApiname, field.api_name)) {
					return;
				}
				if (_.contains(mustFilter, field.api_name)) {
					return;
				}
				//过滤禁用字段
				if (field.is_active === false || !field.is_index || !!field.is_abstract) {
					return;
                }
                // 自定义过滤函数, 可以根据field的key自行过滤
                if (_.isFunction(opts.filterExp) && opts.filterExp(field)) {
                    return;
                }
				fields.push(field);
			});
			return fields;
		},

		_setOptions(cb) {
			var me = this;
			me.options.options = me._getObjectOptions(me.options.objects);
			me.options.props = me.parseProps(me.options.props);
			let getList = [];
			if (me.options.defaultValue && me.options.defaultValue.length) {
				_.each(me.options.defaultValue, function(item){
					if (me.options.props && me.options.props.lazy) {
						let fields = me.getSelectedFields(item[0]);
						let _field = {};
						_.each(fields, (f) => {
							if (f.type == 'object_reference') {
								// _field = f.ofield ? f.ofield : f;
								_field = me.getOField(f);
							}
						})
                        
                        let _fields = (_field.target_fields && util.deepClone(_field.target_fields)) || (CRM.get('fields.' + _field.target_api_name) && util.deepClone(CRM.get('fields.' + _field.target_api_name)));
						_field.target_api_name && getList.push({
                            field: _field,
							api_name: _field.target_api_name,
                            fields: _fields,
                            filterType: ['object_reference']
						})
					}
				})
			}
			
			if (getList.length) {
				me.render(getList, function() {
                    // 编辑回填object_reference上的字段时， 此时已拿到object_reference对象的allowFields
                    _.each(getList, function(ref, index) {
                        !ref.field.target_fields && (ref.field.target_fields = ref.fields);
                        !ref.field.target_fieldslist && (ref.field.target_fieldslist = me._getFieldOptions(ref.allowFields));
                    });
                    // 需要更新object_reference类型_op的children， 否则没有缓存回填失败
                    me.options.options = me._getObjectOptions(me.options.objects);

                    cb();
                });
			} else {
				cb && cb();
			}
		},

		_getObjectOptions(objects) {
			var me = this, props = this.options.props;
			if(!objects.length) return [];
			let result = [];
			// 如果只有一个对象，则该对象的字段直接拍成一级
			if(objects.length < 2 && !this.options.showObjectsCascader) {
				result = me._getFieldOptions(objects[0].allowFields, objects[0].hasExtra);
			} else {
				result = _.map(objects, function(obj){
					return {
						label: obj.label,
						value: obj.api_name,
						type: 'object',
						disabled: _.isObject(props) && props.checkStrictly && me.options.uncheckFirstLevel, // 第一级对象禁止选中
						children: me._getFieldOptions(obj.allowFields, obj.hasExtra, obj.extraParams),
					}
				});
			}
			this.trigger('change.objects', result);
			return result;
		},

		_getFieldOptions: function (fields, hasExtra, extraParam) {
			var me = this;
			let _options = _.map(fields, function (field) {
				let _op = {
					label: field.label,
					value: field.api_name,
					type: field.type,
					leaf: true,
					// ofield: field,
					describe_api_name: field.describe_api_name,
					child: {
						type: 'selectone',
						options: me._getCompareOptions(field)
					}
				}
				if (_op.type === 'object_reference') {
                    _op.leaf = false;
					field.target_fieldslist && (_op.children = field.target_fieldslist);
				}
				return _op;
			});
			if(hasExtra){
				_options=_options.length>=1?_options:[{
					label:$t("暂无数据"),
					value:"",
					leaf: true,
					disabled: true,
					isPlaceholders: true, //后续判断是否为站位option，有值之后可以去掉这个option
				}];
				_options[0].hasExtra = true; //是否展示查看更多
				_options[0].extraParams = extraParam; //查看更多参数
			}
			//hasExtra && (_options[0].hasExtra = true);
			return _options;
		},
		getData() {
			var me = this;
			let data = me.filter&&me.filter.getData()||[];
			let filter = [];

			if (me.options.getData && _.isFunction(me.options.getData)) {
				return me.options.getData(data, me);
			}

			for (let i = 0; i < data.length; i++) {
				let iobj = {};
				let item = data[i];
				if (_.every(item, (v) => { return v === '' || !v.length })) continue;
				let names = item[0];
				let fields = this.getSelectedFields(names);
				let cfield = fields[fields.length - 1] || {};
				// cfield = cfield.ofield ? cfield.ofield : cfield;
				cfield = me.getOField(cfield);
				if (fields.length) {
					iobj.field_name = names.join('.');
					iobj.field_name__s = me.stringifyFieldsInfo(fields);
					iobj.operator = me._parseOperator(item[1]);
					iobj.operator__s = me.options.helper.getNameByValue(iobj.operator, cfield.type);
					iobj.field_values = me._parseFieldValue(cfield, iobj.operator, item[2]);
					iobj.field_values__s = me.options.helper.formatFieldsValue(cfield, iobj.field_values, iobj.operator, item[2]);
					iobj.type = cfield.type;
					//object_reference类型添加字段，避免特殊符号在拼接字符串再解析导致数据错误
					if (cfield.type === 'object_reference' && item && Array.isArray(item[2])) {
						iobj.field_values__array = item[2].map(i=>i.name);
					}
					if (me.options.formatGetItem) {
						iobj = me.options.formatGetItem(iobj, fields, me);
					}
				}

				filter.push(iobj);
			}
			return filter;
		},

		setData: function (data) {
			var me = this;
			if (!data) {
				return;
			}

			if (me.options.setData && _.isFunction(me.options.setData)) {
				return me.options.setData(data, me);
			}

			data = _.map(data, (item) => {
				let names = item[0];
				let fields = this.getSelectedFields(names);
				let cfield = fields[fields.length - 1] || {};
				var compareConfig = me.options.helper.getCompare(cfield.type);
				compareConfig = me.options.parseCompare(compareConfig, cfield);
				var compare = _.findWhere(compareConfig, {
					value1: item[1]
				});
				var result = [item[0], compare ? compare.value : ''];
				var operator = compare ? compare.value1 : '';
				var field_value = item[2];

				let value = me.setFieldValue(cfield, operator, field_value);

				result.push(value);
				return result;
			})

			this.filter.setData(data);
		},

		getSelectedFields(names, options) {
			let me = this;
			let opts = options || me.options.options;
			let fields = [];
			_.each(names, (val) => {
				if (!opts) return;
				let _field = _.findWhere(opts, {value: val});
				if (!_field) return;
				opts = _field.children;
				fields.push(_field);
			})
			return fields;
		},

		getOField(field) {
			const me = this;
			return (field.describe_api_name ? (me.objects[field.describe_api_name] && me.objects[field.describe_api_name][field.value]) : field) || field;
		},

		stringifyFieldsInfo(fields, key, saperator) {
			saperator = saperator || '.';
			key = key || 'label';
			let arr = _.pluck(fields, key);
			let str = arr.join(saperator);
			return str;
		},

    })
})
