.fx-tree-search {
	display: flex;
	flex-direction: column;
	box-sizing: border-box;
	height: 100%;
	padding: 8px 12px 8px 12px;

	.bk-color:hover,
	.bk-color.is-current {
		background: var(--color-primary01);
		border-radius: 4px;
		//color: var(--color-primary06);
		border-color:var(--color-primary06);
	}

	.fx-tree-search-tree {
		flex: 1;
		overflow: auto;
        .el-tree {
            overflow-x: visible;
        }

		//min-width: max-content;

		.el-tree-node__content{
			border:1px solid var(--color-neutrals01);
			height: auto;
			// padding: 3px 0;
			white-space: normal;
			line-height: 18px;
		}

		.el-tree-node__expand-icon{
			// padding: 0 6px;
		}

		.el-tree-node__content:hover {
			background: var(--color-primary01);
			border-radius: 4px;
			//color: var(--color-primary06);
			border-color:var(--color-primary06);
		}

		.current-tree-node {
			background: var(--color-primary01);
			border-radius: 4px;
			color: var(--color-primary06);
		}

		.is-current {
			> .el-tree-node__content {
				background: var(--color-primary01);
				border-radius: 4px;
				//color: var(--color-primary06);
				border-color:var(--color-primary06);

			}
		}

		.no-data{
			display: inline-block;
			width: 100%;
			color: #999;
			text-align: center;
		}
	}

	.fx-tree-search-all,
	.fx-tree-search-custom-btn .fx-tree-search-custom-btn-item {
		box-sizing: border-box;
		padding: 5px 8px;
		font-size: 12px;
		color: var(--color-neutrals19);
		line-height: 22px;
		cursor: pointer;
	}

	.fx-tree-search-box {
		width: 100%;
		padding: 0;
		box-sizing: border-box;
		margin-bottom: 8px;

	}
}
.fx-tree-search-box-wrapper {
    display: flex;
    .fx-tree-fold-icon {
        margin-bottom: 8px;
        & > .create-icon-wrap {
            height: 100%;
            padding-right: 8px;
            display: flex;
            align-items: center;
            font-size: 18px;
            &:hover {
                cursor: pointer;
                color: #f97f0e;
            }
        }
    }
}
